import{a as c}from"./bootstrap-5OPUVRWy.js";import{x as i,j as p,a as f,b as a,q as _,f as d,t as o,r as n,o as u}from"../jse/index-index-DyHD_jbN.js";const x={name:"EllipsisText",props:{text:{type:String,required:!0},maxWidth:{type:String,default:"200px"}},setup(l){const e=n(!1),t=n(null),s=()=>{t.value&&(e.value=t.value.scrollWidth>t.value.clientWidth)};return u(()=>{s()}),{isOverflow:e,textElement:t}}},m={class:"truncate-text"},v={key:1,class:"truncate-text"};function h(l,e,t,s,k,y){const r=i("a-tooltip");return s.isOverflow?(a(),p(r,{key:0,title:t.text},{default:_(()=>[d("span",m,o(t.text),1)]),_:1},8,["title"])):(a(),f("span",v,o(t.text),1))}const E=c(x,[["render",h],["__scopeId","data-v-700afdef"]]);export{E as default};
