
> @vben/web-antd@5.4.6 build E:\work\git\gisbase-datamanage-fed\apps\web-antd
> pnpm vite build --mode production

[36mvite v6.3.4 [32mbuilding for production...[36m[39m
transforming...

 WARN  [33m[plugin vite:esbuild] src/views/dataManage/entity/components/EntityUpdateModal.vue?vue&type=script&setup=true&lang.ts: [33mDuplicate key "closeOnClickModal" in object literal[33m
53 |  const [Modal, modalApi] = useVbenModal({
54 |    closeOnClickModal:false,
55 |    closeOnClickModal:false,
   |    ^
56 |    onCancel() {
57 |      modalApi.close();
[39m


 WARN  [33msrc/utils/app.js (199:0): "verifyFuncAuth" is not exported by "src/api/core/map.js", imported by "src/utils/app.js".[39m


 WARN  [33msrc/utils/app.js (371:0): "getFieldList" is not exported by "src/api/core/map.js", imported by "src/utils/app.js".[39m


 WARN  [33msrc/utils/cesium/layers/POIViewer/index.js (132:0): "getPoiList" is not exported by "src/api/index.ts", imported by "src/utils/cesium/layers/POIViewer/index.js".[39m


 WARN  [33msrc/utils/cesium/layers/POIViewer/index.js (142:0): "getUserVisibleTypeList" is not exported by "src/api/index.ts", imported by "src/utils/cesium/layers/POIViewer/index.js".[39m


 WARN  [33msrc/utils/cesium/layers/POIViewer/index.js (168:0): "getLayerTag" is not exported by "src/api/index.ts", imported by "src/utils/cesium/layers/POIViewer/index.js".[39m


 WARN  [33msrc/utils/cesium/layers/POIViewer/index.js (241:0): "deleteLayerTag" is not exported by "src/api/index.ts", imported by "src/utils/cesium/layers/POIViewer/index.js".[39m


 WARN  [33msrc/utils/cesium/layers/POIViewer/index.js (317:0): "editLayerTag" is not exported by "src/api/index.ts", imported by "src/utils/cesium/layers/POIViewer/index.js".[39m


 WARN  [33msrc/utils/cesium/layers/POIViewer/index.js (322:0): "createLayerTag" is not exported by "src/api/index.ts", imported by "src/utils/cesium/layers/POIViewer/index.js".[39m

[32m✓[39m 7121 modules transformed.

 WARN  [33msrc/utils/cesium/layers/index.js (576:0): Use of eval in "src/utils/cesium/layers/index.js" is strongly discouraged as it poses security risks and may cause issues with minification.[39m

rendering chunks...

 WARN  [33m[esbuild css minify]
▲ [WARNING] Comments in CSS use "/* ... */" instead of "//" [js-comment-in-css]

    <stdin>:1:305:
      1 │ ...ss[data-v-87b81a34]{color:#fff;//padding:5px}.folderProgressArea...
        ╵                                   ~~

[39m


 WARN  [33m[esbuild css minify]
▲ [WARNING] Comments in CSS use "/* ... */" instead of "//" [js-comment-in-css]

    <stdin>:1:67:
      1 │ ...round-color:#00a2ff;color:#fff;//padding:5px;width:0}.folderProg...
        ╵                                   ~~

[39m


 WARN  [33m[esbuild css minify]
▲ [WARNING] Comments in CSS use "/* ... */" instead of "//" [js-comment-in-css]

    <stdin>:1:31:
      1 │ .fileProgress[data-v-2874f8c2]{//padding:5px;width:0}.folderProgres...
        ╵                                ~~

[39m


 WARN  [33m[plugin vite:reporter] 
(!) E:/work/git/gisbase-datamanage-fed/apps/web-antd/src/store/index.ts is dynamically imported by E:/work/git/gisbase-datamanage-fed/apps/web-antd/src/bootstrap.ts but also statically imported by E:/work/git/gisbase-datamanage-fed/apps/web-antd/src/api/request.ts, E:/work/git/gisbase-datamanage-fed/apps/web-antd/src/layouts/basic.vue?vue&type=script&setup=true&lang.ts, E:/work/git/gisbase-datamanage-fed/apps/web-antd/src/router/guard.ts, E:/work/git/gisbase-datamanage-fed/apps/web-antd/src/views/_core/authentication/login.vue?vue&type=script&setup=true&lang.ts, dynamic import will not move module into another chunk.
[39m

✨configuration file is build successfully!
[2mdist/[22m[32m_app.config.js                                                              [39m[1m[2m     0.23 kB[22m[1m[22m
[2mdist/[22m[32mindex.html                                                                  [39m[1m[2m     2.87 kB[22m[1m[22m
[2mdist/[22m[32mpng/grid-COVuxytV.png                                                       [39m[1m[2m     4.19 kB[22m[1m[22m
[2mdist/[22m[32mpng/icon_head_default-CEmUpiwH.png                                          [39m[1m[2m     4.76 kB[22m[1m[22m

[2mdist/[22m[32mjpg/login_bg-Bug-Qese.jpg                                                   [39m[1m[2m   135.53 kB[22m[1m[22m
 WARN  [33m
(!) Some chunks are larger than 2000 kB after minification. Consider:
[2mdist/[22m[35mcss/index-DWa1U8Kh.css                                                      [39m[1m[2m     0.06 kB[22m[1m[22m
[2mdist/[22m[35mcss/EntityLayerData-DsMiYZwv.css                                            [39m[1m[2m     0.11 kB[22m[1m[22m
[2mdist/[22m[35mcss/index-DzmaY38O.css                                                      [39m[1m[2m     0.15 kB[22m[1m[22m
[2mdist/[22m[35mcss/EllipsisText-mK6kptjZ.css                                               [39m[1m[2m     0.18 kB[22m[1m[22m
[2mdist/[22m[35mcss/EntityCreateModal-C2ongmUz.css                                          [39m[1m[2m     0.20 kB[22m[1m[22m
[2mdist/[22m[35mcss/EntityUpdateModal-BSGeHHxD.css                                          [39m[1m[2m     0.23 kB[22m[1m[22m
- Using dynamic import() to code-split the application
[2mdist/[22m[35mcss/index-CSYWBIXV.css                                                      [39m[1m[2m     0.24 kB[22m[1m[22m
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.[39m
[2mdist/[22m[35mcss/SceneCreateModal-CC79zc6e.css                                           [39m[1m[2m     0.26 kB[22m[1m[22m

[2mdist/[22m[35mcss/SceneUploadModal-ry2GqM1s.css                                           [39m[1m[2m     0.29 kB[22m[1m[22m
[2mdist/[22m[35mcss/SceneUpdateModal-CgFFc9nL.css                                           [39m[1m[2m     0.31 kB[22m[1m[22m
[2mdist/[22m[35mcss/EntityUploadModal-nIZxgH-k.css                                          [39m[1m[2m     0.46 kB[22m[1m[22m
[2mdist/[22m[35mcss/authentication-EC56YaF4.css                                             [39m[1m[2m     0.49 kB[22m[1m[22m
[2mdist/[22m[35mcss/loading-BxEkFk4N.css                                                    [39m[1m[2m     0.56 kB[22m[1m[22m
[2mdist/[22m[35mcss/SpaceQuery-DYLXNFPn.css                                                 [39m[1m[2m     0.71 kB[22m[1m[22m
[2mdist/[22m[35mcss/LayerPreview-CGTzIrqa.css                                               [39m[1m[2m     0.76 kB[22m[1m[22m
[2mdist/[22m[35mcss/Base-ScrT1TyK.css                                                       [39m[1m[2m     0.95 kB[22m[1m[22m
[2mdist/[22m[35mcss/index-BoBxju6P.css                                                      [39m[1m[2m     0.99 kB[22m[1m[22m
[2mdist/[22m[35mcss/MapViewer-CwP4Zifw.css                                                  [39m[1m[2m     1.31 kB[22m[1m[22m
[2mdist/[22m[35mcss/EntityDetialModal-BvK6Ja18.css                                          [39m[1m[2m     1.39 kB[22m[1m[22m
[2mdist/[22m[35mcss/theme-toggle-EeIgddXJ.css                                               [39m[1m[2m     1.52 kB[22m[1m[22m
[2mdist/[22m[35mcss/SceneDetialModal-BvtnFyN6.css                                           [39m[1m[2m     1.53 kB[22m[1m[22m
[2mdist/[22m[35mcss/Dashboard-DE1QLNiZ.css                                                  [39m[1m[2m    10.80 kB[22m[1m[22m
[2mdist/[22m[35mcss/layout-BsmOB0aT.css                                                     [39m[1m[2m    17.72 kB[22m[1m[22m
[2mdist/[22m[35mcss/index-DFY6Dly2.css                                                      [39m[1m[2m    37.39 kB[22m[1m[22m
[2mdist/[22m[35mcss/bootstrap-BMsz0smL.css                                                  [39m[1m[2m    87.52 kB[22m[1m[22m
[2mdist/[22m[35mcss/vxe-table--8AfTs7T.css                                                  [39m[1m[2m   273.47 kB[22m[1m[22m
[2mdist/[22m[36mjs/login-B7KvnCKq.js                                                        [39m[1m[2m     0.11 kB[22m[1m[22m
[2mdist/[22m[36mjs/rotate-cw-lLmdvVrn.js                                                    [39m[1m[2m     0.20 kB[22m[1m[22m
[2mdist/[22m[36mjs/alert-DJKWbMfG.js                                                        [39m[1m[2m     0.22 kB[22m[1m[22m
[2mdist/[22m[36mjs/analytics-visits-source-8u-Mzqjj.js                                      [39m[1m[2m     0.22 kB[22m[1m[22m
[2mdist/[22m[36mjs/toast-CQjPPeQ1.js                                                        [39m[1m[2m     0.24 kB[22m[1m[22m
[2mdist/[22m[36mjs/demos-Bb4SQ2Py.js                                                        [39m[1m[2m     0.26 kB[22m[1m[22m
[2mdist/[22m[36mjs/page-B2keVEix.js                                                         [39m[1m[2m     0.26 kB[22m[1m[22m
[2mdist/[22m[36mjs/page-BkKdz23R.js                                                         [39m[1m[2m     0.26 kB[22m[1m[22m
[2mdist/[22m[36mjs/demos-DL4kfOp3.js                                                        [39m[1m[2m     0.27 kB[22m[1m[22m
[2mdist/[22m[36mjs/coming-soon-DaCvZppm.js                                                  [39m[1m[2m     0.33 kB[22m[1m[22m
[2mdist/[22m[36mjs/forbidden-Cbt0OPzR.js                                                    [39m[1m[2m     0.34 kB[22m[1m[22m
[2mdist/[22m[36mjs/not-found-DKOLrUfZ.js                                                    [39m[1m[2m     0.34 kB[22m[1m[22m
[2mdist/[22m[36mjs/internal-error-CoB1OTVV.js                                               [39m[1m[2m     0.35 kB[22m[1m[22m
[2mdist/[22m[36mjs/offline-giewLbGQ.js                                                      [39m[1m[2m     0.35 kB[22m[1m[22m
[2mdist/[22m[36mjs/util-BadkgFi3.js                                                         [39m[1m[2m     0.38 kB[22m[1m[22m
[2mdist/[22m[36mjs/common-rVEJhhhE.js                                                       [39m[1m[2m     0.41 kB[22m[1m[22m
[2mdist/[22m[36mjs/form-DdFfsSWf.js                                                         [39m[1m[2m     0.42 kB[22m[1m[22m
[2mdist/[22m[36mjs/common-BArc3qur.js                                                       [39m[1m[2m     0.43 kB[22m[1m[22m
[2mdist/[22m[36mjs/auth-CpEXFi-5.js                                                         [39m[1m[2m     0.57 kB[22m[1m[22m
[2mdist/[22m[36mjs/analytics-visits-BYZdEk2H.js                                             [39m[1m[2m     0.70 kB[22m[1m[22m
[2mdist/[22m[36mjs/EllipsisText-CcdNr0cD.js                                                 [39m[1m[2m     0.74 kB[22m[1m[22m
[2mdist/[22m[36mjs/index-dCUV1C4I.js                                                        [39m[1m[2m     0.75 kB[22m[1m[22m
[2mdist/[22m[36mjs/analytics-visits-sales-Cl-MJ7pq.js                                       [39m[1m[2m     0.77 kB[22m[1m[22m
[2mdist/[22m[36mjs/scene.api-DEW02Ykq.js                                                    [39m[1m[2m     0.84 kB[22m[1m[22m
[2mdist/[22m[36mjs/analytics-visits-data-CNxhdy3m.js                                        [39m[1m[2m     0.89 kB[22m[1m[22m
[2mdist/[22m[36mjs/analytics-visits-source.vue_vue_type_script_setup_true_lang-CDxpLUXm.js  [39m[1m[2m     0.90 kB[22m[1m[22m
[2mdist/[22m[36mjs/entity.api-CPgpBrqe.js                                                   [39m[1m[2m     0.90 kB[22m[1m[22m
[2mdist/[22m[36mjs/en-BCQOzw6J.js                                                           [39m[1m[2m     0.91 kB[22m[1m[22m
[2mdist/[22m[36mjs/analytics-trends-DNTvLjc_.js                                             [39m[1m[2m     1.07 kB[22m[1m[22m
[2mdist/[22m[36mjs/index-BtUs3TrT.js                                                        [39m[1m[2m     1.11 kB[22m[1m[22m
[2mdist/[22m[36mjs/loading-DzjUKA94.js                                                      [39m[1m[2m     1.16 kB[22m[1m[22m
[2mdist/[22m[36mjs/EntityLayerData-B2GLrKMm.js                                              [39m[1m[2m     1.50 kB[22m[1m[22m
[2mdist/[22m[36mjs/zh-cn-SItMEtG3.js                                                        [39m[1m[2m     1.80 kB[22m[1m[22m
[2mdist/[22m[36mjs/page.vue_vue_type_script_setup_true_lang-D2AkmoUI.js                     [39m[1m[2m     1.82 kB[22m[1m[22m
[2mdist/[22m[36mjs/LayerPreview-B-iME7wH.js                                                 [39m[1m[2m     1.94 kB[22m[1m[22m
[2mdist/[22m[36mjs/index-D28drw2N.js                                                        [39m[1m[2m     2.07 kB[22m[1m[22m
[2mdist/[22m[36mjs/forget-password-DFI-_8Vr.js                                              [39m[1m[2m     2.17 kB[22m[1m[22m
[2mdist/[22m[36mjs/ui-CIT4X-s6.js                                                           [39m[1m[2m     2.32 kB[22m[1m[22m
[2mdist/[22m[36mjs/icon.vue_vue_type_script_setup_true_lang-CgLK7NiC.js                     [39m[1m[2m     2.49 kB[22m[1m[22m
[2mdist/[22m[36mjs/ui-ZcNFyFV6.js                                                           [39m[1m[2m     2.52 kB[22m[1m[22m
[2mdist/[22m[36mjs/code-login-BdOZqK18.js                                                   [39m[1m[2m     2.55 kB[22m[1m[22m
[2mdist/[22m[36mjs/authentication-DXutKJsd.js                                               [39m[1m[2m     2.97 kB[22m[1m[22m
[2mdist/[22m[36mjs/entity.data-u4HDUExc.js                                                  [39m[1m[2m     3.13 kB[22m[1m[22m
[2mdist/[22m[36mjs/authentication-BR5K4oDt.js                                               [39m[1m[2m     3.15 kB[22m[1m[22m
[2mdist/[22m[36mjs/fallback.vue_vue_type_script_setup_true_lang-D4Fn8j9V.js                 [39m[1m[2m     3.17 kB[22m[1m[22m
[2mdist/[22m[36mjs/register-DNPU5_Oy.js                                                     [39m[1m[2m     3.25 kB[22m[1m[22m
[2mdist/[22m[36mjs/SceneUpdateModal-Bymu8ibZ.js                                             [39m[1m[2m     3.38 kB[22m[1m[22m
[2mdist/[22m[36mjs/index-N9Ppvh4q.js                                                        [39m[1m[2m     3.75 kB[22m[1m[22m
[2mdist/[22m[36mjs/SceneUploadModal-pbSscZPJ.js                                             [39m[1m[2m     3.91 kB[22m[1m[22m
[2mdist/[22m[36mjs/EntityUploadModal-DySgipYG.js                                            [39m[1m[2m     3.96 kB[22m[1m[22m
[2mdist/[22m[36mjs/EntityUpdateModal-D_UmvE2A.js                                            [39m[1m[2m     4.21 kB[22m[1m[22m
[2mdist/[22m[36mjs/index-D25aAg6D.js                                                        [39m[1m[2m     4.25 kB[22m[1m[22m
[2mdist/[22m[36mjs/scene.data-BMXeOdST.js                                                   [39m[1m[2m     4.30 kB[22m[1m[22m
[2mdist/[22m[36mjs/index-DDlnE-1O.js                                                        [39m[1m[2m     4.32 kB[22m[1m[22m
[2mdist/[22m[36mjs/preferences-CciNWw5O.js                                                  [39m[1m[2m     4.57 kB[22m[1m[22m
[2mdist/[22m[36mjs/nprogress-C0AwdyHR.js                                                    [39m[1m[2m     4.59 kB[22m[1m[22m
[2mdist/[22m[36mjs/preferences-6HYBOYEe.js                                                  [39m[1m[2m     4.66 kB[22m[1m[22m
[2mdist/[22m[36mjs/basic-BiSeoNYY.js                                                        [39m[1m[2m     5.59 kB[22m[1m[22m
[2mdist/[22m[36mjs/SceneCreateModal-QtF1Ihbe.js                                             [39m[1m[2m     5.95 kB[22m[1m[22m
[2mdist/[22m[36mjs/EntityCreateModal-9qv7l48P.js                                            [39m[1m[2m     5.98 kB[22m[1m[22m
[2mdist/[22m[36mjs/EntityDetialModal-CdtxQuDW.js                                            [39m[1m[2m     6.61 kB[22m[1m[22m
[2mdist/[22m[36mjs/index-BTh7nQXa.js                                                        [39m[1m[2m     6.95 kB[22m[1m[22m
[2mdist/[22m[36mjs/icon-500-W1O2h24g.js                                                     [39m[1m[2m     7.08 kB[22m[1m[22m
[2mdist/[22m[36mjs/SceneDetialModal-CMSsmrBj.js                                             [39m[1m[2m     7.10 kB[22m[1m[22m
[2mdist/[22m[36mjs/icon-offline-DBCYebSH.js                                                 [39m[1m[2m     8.09 kB[22m[1m[22m
[2mdist/[22m[36mjs/index-BxyXkQEt.js                                                        [39m[1m[2m     8.82 kB[22m[1m[22m
[2mdist/[22m[36mjs/fileUpload-DI0dJ9zY.js                                                   [39m[1m[2m     9.02 kB[22m[1m[22m
[2mdist/[22m[36mjs/icon-403-Cz_oemPw.js                                                     [39m[1m[2m     9.39 kB[22m[1m[22m
[2mdist/[22m[36mjs/index-CHmwozon.js                                                        [39m[1m[2m    11.22 kB[22m[1m[22m
[2mdist/[22m[36mjs/theme-toggle.vue_vue_type_script_setup_true_lang-pVzKzmJu.js             [39m[1m[2m    11.33 kB[22m[1m[22m
[2mdist/[22m[36mjs/icon-coming-soon-BqaHHAOR.js                                             [39m[1m[2m    12.89 kB[22m[1m[22m
[2mdist/[22m[36mjs/use-modal-uChFuhJy.js                                                    [39m[1m[2m    14.50 kB[22m[1m[22m
[2mdist/[22m[36mjs/icon-404-j0CmHeao.js                                                     [39m[1m[2m    24.34 kB[22m[1m[22m
[2mdist/[22m[36mjs/MapViewer-CBAv8z2T.js                                                    [39m[1m[2m    26.20 kB[22m[1m[22m
[2mdist/[22m[36mjs/qrcode-login-D0VG7YAn.js                                                 [39m[1m[2m    27.32 kB[22m[1m[22m
[2mdist/[22m[36mjs/SpaceQuery-BLePLjaE.js                                                   [39m[1m[2m    29.98 kB[22m[1m[22m
[2mdist/[22m[36mjs/index.esm-DCgfxJEb.js                                                    [39m[1m[2m    30.16 kB[22m[1m[22m
[2mdist/[22m[36mjs/Dashboard-SMthZzgV.js                                                    [39m[1m[2m    34.41 kB[22m[1m[22m
[2mdist/[22m[36mjs/sortable.complete.esm-Crr9kxfy.js                                        [39m[1m[2m    45.80 kB[22m[1m[22m
[2mdist/[22m[36mjs/index-D4Q7xmlJ.js                                                        [39m[1m[2m    75.73 kB[22m[1m[22m
[2mdist/[22m[36mjs/authentication-Cmx7T77G.js                                               [39m[1m[2m   118.42 kB[22m[1m[22m
[2mdist/[22m[36mjs/index-BYvs451p.js                                                        [39m[1m[2m   144.29 kB[22m[1m[22m
[2mdist/[22m[36mjs/Base-xeJpkIWP.js                                                         [39m[1m[2m   147.86 kB[22m[1m[22m
[2mdist/[22m[36mjs/layout.vue_vue_type_script_setup_true_lang-CXvwR802.js                   [39m[1m[2m   198.58 kB[22m[1m[22m
[2mdist/[22m[36mjs/vxe-table-CZ9gPHn5.js                                                    [39m[1m[2m   601.71 kB[22m[1m[22m
[2mdist/[22m[36mjs/use-echarts-CfT8MSbz.js                                                  [39m[1m[2m   625.60 kB[22m[1m[22m
[2mdist/[22m[36mjs/bootstrap-5OPUVRWy.js                                                    [39m[1m[2m 1,851.35 kB[22m[1m[22m
[2mdist/[22m[36mjse/index-index-DyHD_jbN.js                                                 [39m[1m[33m26,012.29 kB[39m[22m
[32m✓ built in 4m 12s[39m
ZIP file created: E:\work\git\gisbase-datamanage-fed\apps\web-antd\dist.zip (12506180 total bytes)
Folder has been zipped to: E:\work\git\gisbase-datamanage-fed\apps\web-antd\dist.zip
