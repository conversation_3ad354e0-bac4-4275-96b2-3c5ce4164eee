import { Store } from "@vben-core/shared/store";
import { bindMethods, isFunction } from "@vben-core/shared/utils";
export class DrawerApi {
  api;
  // private prevState!: DrawerState;
  state;
  // 共享数据
  sharedData = {
    payload: {}
  };
  store;
  constructor(options = {}) {
    const {
      connectedComponent: _,
      onBeforeClose,
      onCancel,
      onConfirm,
      onOpenChange,
      ...storeState
    } = options;
    const defaultState = {
      class: "",
      closable: true,
      closeOnClickModal: true,
      closeOnPressEscape: true,
      confirmLoading: false,
      contentClass: "",
      footer: true,
      header: true,
      isOpen: false,
      loading: false,
      modal: true,
      openAutoFocus: false,
      showCancelButton: true,
      showConfirmButton: true,
      title: ""
    };
    this.store = new Store(
      {
        ...defaultState,
        ...storeState
      },
      {
        onUpdate: () => {
          const state = this.store.state;
          if (state?.isOpen === this.state?.isOpen) {
            this.state = state;
          } else {
            this.state = state;
            this.api.onOpenChange?.(!!state?.isOpen);
          }
        }
      }
    );
    this.state = this.store.state;
    this.api = {
      onBeforeClose,
      onCancel,
      onConfirm,
      onOpenChange
    };
    bindMethods(this);
  }
  // 如果需要多次更新状态，可以使用 batch 方法
  batchStore(cb) {
    this.store.batch(cb);
  }
  /**
   * 关闭弹窗
   */
  close() {
    const allowClose = this.api.onBeforeClose?.() ?? true;
    if (allowClose) {
      this.store.setState((prev) => ({ ...prev, isOpen: false }));
    }
  }
  getData() {
    return this.sharedData?.payload ?? {};
  }
  /**
   * 取消操作
   */
  onCancel() {
    if (this.api.onCancel) {
      this.api.onCancel?.();
    } else {
      this.close();
    }
  }
  /**
   * 确认操作
   */
  onConfirm() {
    this.api.onConfirm?.();
  }
  open() {
    this.store.setState((prev) => ({ ...prev, isOpen: true }));
  }
  setData(payload) {
    this.sharedData.payload = payload;
  }
  setState(stateOrFn) {
    if (isFunction(stateOrFn)) {
      this.store.setState(stateOrFn);
    } else {
      this.store.setState((prev) => ({ ...prev, ...stateOrFn }));
    }
  }
}
