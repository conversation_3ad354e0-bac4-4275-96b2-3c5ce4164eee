import{d as i,j as l,b as o,k as t,h as u,J as k,q as p,g as v,O as f,P as g,I as c,c as m,a as h,s as d,e as _,v as b,t as C,Q as y,R as z,S as $,T as B,p as w,U as M}from"../jse/index-index-DyHD_jbN.js";import{c as j,p as P,z as I,H as S,K as A}from"./bootstrap-5OPUVRWy.js";const V=j("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),F=P("inline-flex items-center justify-center font-normal text-foreground select-none shrink-0 bg-secondary overflow-hidden",{variants:{shape:{circle:"rounded-full",square:"rounded-md"},size:{base:"h-16 w-16 text-2xl",lg:"h-32 w-32 text-5xl",sm:"h-10 w-10 text-xs"}}}),H=i({__name:"Avatar",props:{class:{},shape:{default:"circle"},size:{default:"sm"}},setup(r){const s=r;return(a,n)=>(o(),l(t(I),{class:u(t(k)(t(F)({size:a.size,shape:a.shape}),s.class))},{default:p(()=>[v(a.$slots,"default")]),_:3},8,["class"]))}}),N=i({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{}},setup(r){const s=r;return(a,n)=>(o(),l(t(S),f(g(s)),{default:p(()=>[v(a.$slots,"default")]),_:3},16))}}),R=i({__name:"AvatarImage",props:{src:{},referrerPolicy:{},asChild:{type:Boolean},as:{}},setup(r){const s=r;return(a,n)=>(o(),l(t(A),c(s,{class:"h-full w-full object-cover"}),null,16))}}),K=i({inheritAttrs:!1,__name:"avatar",props:{alt:{default:"avatar"},class:{},dot:{type:Boolean,default:!1},dotClass:{default:"bg-green-500"},asChild:{type:Boolean},as:{default:"button"},delayMs:{},src:{},referrerPolicy:{}},setup(r){const s=r,a=m(()=>s.alt.slice(-2).toUpperCase());return(n,e)=>(o(),h("div",{class:u([s.class,"relative flex flex-shrink-0 items-center"])},[d(t(H),{class:u([s.class,"size-full"])},{default:p(()=>[d(t(R),{alt:n.alt,src:n.src},null,8,["alt","src"]),d(t(N),null,{default:p(()=>[b(C(a.value),1)]),_:1})]),_:1},8,["class"]),n.dot?(o(),h("span",{key:0,class:u([n.dotClass,"border-background absolute bottom-0 right-0 size-3 rounded-full border-2"])},null,2)):_("",!0)],2))}}),U=["src"],O=i({__name:"icon",props:{fallback:{type:Boolean},icon:{type:[Function,String]}},setup(r){const s=r,a=m(()=>y(s.icon)&&z(s.icon)),n=m(()=>{const{icon:e}=s;return!y(e)&&($(e)||B(e))});return(e,q)=>n.value?(o(),l(w(e.icon),f(c({key:0},e.$attrs)),null,16)):a.value?(o(),h("img",c({key:1,src:e.icon},e.$attrs),null,16,U)):e.icon?(o(),l(t(M),c({key:2},e.$attrs,{icon:e.icon}),null,16,["icon"])):e.fallback?(o(),l(t(V),f(c({key:3},e.$attrs)),null,16)):_("",!0)}});export{V as M,K as _,O as a};
