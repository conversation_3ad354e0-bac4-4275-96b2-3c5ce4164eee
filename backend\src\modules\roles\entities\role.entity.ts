import { Entity, Column, Index, ManyToMany, JoinTable } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '@/common/entities/base.entity';
import { User } from '@/modules/users/entities/user.entity';
import { Function } from '@/modules/functions/entities/function.entity';

@Entity('roles')
@Index(['code'])
@Index(['status'])
export class Role extends BaseEntity {
  @ApiProperty({ description: '角色名称' })
  @Column({
    type: 'varchar',
    length: 100,
    comment: '角色名称',
  })
  name: string;

  @ApiProperty({ description: '角色编码' })
  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: '角色编码',
  })
  code: string;

  @ApiProperty({ description: '角色描述' })
  @Column({
    type: 'text',
    nullable: true,
    comment: '角色描述',
  })
  description?: string;

  @ApiProperty({ description: '状态：1启用，0禁用' })
  @Column({
    type: 'tinyint',
    default: 1,
    comment: '状态：1启用，0禁用',
  })
  status: number;

  // 关联关系
  @ManyToMany(() => User, (user) => user.roles)
  users?: User[];

  @ManyToMany(() => Function, (func) => func.roles)
  @JoinTable({
    name: 'role_functions',
    joinColumn: { name: 'role_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'function_id', referencedColumnName: 'id' },
  })
  functions?: Function[];
}
