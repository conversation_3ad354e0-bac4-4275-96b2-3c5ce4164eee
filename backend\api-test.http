### 系统管理API测试文件
### 使用VS Code的REST Client插件运行这些请求

@baseUrl = http://localhost:3000/api
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### 1. 管理员登录
POST {{baseUrl}}/system/login
Content-Type: application/json

{
  "account": "admin",
  "password": "admin123"
}

### 2. 手机号登录
POST {{baseUrl}}/system/login
Content-Type: application/json

{
  "account": "***********",
  "password": "8888a8888#@"
}

### 3. 用户名登录
POST {{baseUrl}}/system/login
Content-Type: application/json

{
  "account": "testuser",
  "password": "8888a8888#@"
}

### 4. 带captcha参数的登录（验证兼容性）
POST {{baseUrl}}/system/login
Content-Type: application/json

{
  "account": "admin",
  "password": "admin123",
  "captcha": true
}

### 3. 获取用户列表
GET {{baseUrl}}/users
Authorization: Bearer {{token}}

### 4. 获取组织树形结构
GET {{baseUrl}}/organizations/tree
Authorization: Bearer {{token}}

### 5. 创建组织
POST {{baseUrl}}/organizations
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "测试部门",
  "code": "TEST_DEPT",
  "parentId": 0,
  "type": "department",
  "sortOrder": 1,
  "status": 1,
  "description": "测试用部门"
}

### 6. 获取角色列表
GET {{baseUrl}}/roles
Authorization: Bearer {{token}}

### 7. 创建用户
POST {{baseUrl}}/users
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "username": "testuser",
  "password": "test123",
  "realName": "测试用户",
  "phone": "***********",
  "email": "<EMAIL>",
  "status": 1
}

### 8. 健康检查
GET {{baseUrl}}/health

### 9. 应用信息
GET {{baseUrl}}/

### 测试错误处理
POST {{baseUrl}}/system/login
Content-Type: application/json

{
  "account": "wronguser",
  "password": "wrongpass"
}
