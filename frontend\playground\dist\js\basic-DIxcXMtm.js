import{u as b}from"./vxe-table-a0ubJ4nQ.js";import{_ as h}from"./doc-button.vue_vue_type_script_setup_true_lang-BzJdr9vE.js";import{M as k}from"./table-data-D75cgFtg.js";import{B as s,by as x}from"./bootstrap-DShsrVit.js";import{_ as y}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as C,af as v,ag as B,ah as a,a3 as t,n as r,an as n,ao as d}from"../jse/index-index-BMh_AyeW.js";import"./empty-icon-Crs2MoG4.js";import"./loading-Cqdke3S1.js";import"./form-DnT3S1ma.js";const D=C({__name:"basic",setup(O){const c={columns:[{title:"序号",type:"seq",width:50},{field:"name",title:"Name"},{field:"age",sortable:!0,title:"Age"},{field:"nickname",title:"Nickname"},{field:"role",title:"Role"},{field:"address",showOverflow:!0,title:"Address"}],data:k,pagerConfig:{enabled:!1},sortConfig:{multiple:!0}},m={cellClick:({row:o})=>{x.info(`cell-click: ${o.name}`)}},[f,i]=b({gridEvents:m,gridOptions:c}),l=i.useStore(o=>{var e;return(e=o.gridOptions)==null?void 0:e.border}),p=i.useStore(o=>{var e;return(e=o.gridOptions)==null?void 0:e.stripe});function u(){i.setGridOptions({border:!l.value})}function g(){i.setGridOptions({stripe:!p.value})}function _(){i.setLoading(!0),setTimeout(()=>{i.setLoading(!1)},2e3)}return(o,e)=>(v(),B(t(y),{description:"表格组件常用于快速开发数据展示与交互界面，示例数据为静态数据。该组件是对vxe-table进行简单的二次封装，大部分属性与方法与vxe-table保持一致。",title:"表格基础示例"},{extra:a(()=>[r(h,{path:"/components/common-ui/vben-vxe-table"})]),default:a(()=>[r(t(f),{"table-title":"基础列表","table-title-help":"提示"},{"toolbar-tools":a(()=>[r(t(s),{class:"mr-2",type:"primary",onClick:u},{default:a(()=>[n(d(t(l)?"隐藏":"显示")+"边框 ",1)]),_:1}),r(t(s),{class:"mr-2",type:"primary",onClick:_},{default:a(()=>e[0]||(e[0]=[n(" 显示loading ")])),_:1}),r(t(s),{type:"primary",onClick:g},{default:a(()=>[n(d(t(p)?"隐藏":"显示")+"斑马纹 ",1)]),_:1})]),_:1})]),_:1}))}});export{D as default};
