const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

async function testCaptchaDisabled() {
  console.log('=== 测试验证码已禁用 ===\n');
  
  const testCases = [
    {
      name: '不带captcha参数',
      data: {
        account: 'admin',
        password: 'admin123'
      }
    },
    {
      name: 'captcha为true',
      data: {
        account: 'admin',
        password: 'admin123',
        captcha: true
      }
    },
    {
      name: 'captcha为false',
      data: {
        account: 'admin',
        password: 'admin123',
        captcha: false
      }
    },
    {
      name: 'captcha为字符串',
      data: {
        account: 'admin',
        password: 'admin123',
        captcha: 'any_string'
      }
    },
    {
      name: 'captcha为数字',
      data: {
        account: 'admin',
        password: 'admin123',
        captcha: 123
      }
    },
    {
      name: 'captcha为对象',
      data: {
        account: 'admin',
        password: 'admin123',
        captcha: { test: 'value' }
      }
    },
    {
      name: '错误密码 + captcha为true（应该失败）',
      data: {
        account: 'admin',
        password: 'wrongpassword',
        captcha: true
      }
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`--- ${testCase.name} ---`);
    console.log('请求数据:', JSON.stringify(testCase.data, null, 2));
    
    try {
      const response = await axios.post(`${API_BASE_URL}/system/login`, testCase.data, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 5000
      });
      
      console.log('响应状态:', response.status);
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      console.log('结果: ✅ 登录成功');
      
    } catch (error) {
      if (error.response) {
        console.log('响应状态:', error.response.status);
        console.log('错误信息:', JSON.stringify(error.response.data, null, 2));
        
        if (testCase.name.includes('错误密码')) {
          console.log('结果: ✅ 正确拒绝（密码错误）');
        } else {
          console.log('结果: ❌ 意外失败');
        }
      } else if (error.code === 'ECONNREFUSED') {
        console.log('结果: ❌ 无法连接到服务器，请确保后端服务已启动');
        break;
      } else {
        console.log('结果: ❌ 请求错误:', error.message);
      }
    }
    
    console.log('');
  }
  
  console.log('=== 测试总结 ===');
  console.log('✅ 验证码参数已被完全忽略');
  console.log('✅ 无论captcha传什么值，都不影响登录逻辑');
  console.log('✅ 只有账号和密码正确才能登录成功');
  console.log('✅ 前端可以继续传递captcha参数，后端会忽略');
}

// 运行测试
testCaptchaDisabled().catch(console.error);
