{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/src/scheduler.ts", "../../../../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/src/store.ts", "../../../../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/src/derived.ts", "../../../../../node_modules/.pnpm/@tanstack+store@0.7.0/node_modules/@tanstack/store/src/effect.ts", "../../../../../node_modules/.pnpm/@tanstack+vue-store@0.7.0_vue@3.5.13_typescript@5.8.3_/node_modules/@tanstack/vue-store/src/index.ts"], "sourcesContent": ["import { Derived } from './derived'\nimport type { Store } from './store'\n\n/**\n * This is here to solve the pyramid dependency problem where:\n *       A\n *      / \\\n *     B   C\n *      \\ /\n *       D\n *\n * Where we deeply traverse this tree, how do we avoid D being recomputed twice; once when B is updated, once when C is.\n *\n * To solve this, we create linkedDeps that allows us to sync avoid writes to the state until all of the deps have been\n * resolved.\n *\n * This is a record of stores, because derived stores are not able to write values to, but stores are\n */\nexport const __storeToDerived = new WeakMap<\n  Store<unknown>,\n  Set<Derived<unknown>>\n>()\nexport const __derivedToStore = new WeakMap<\n  Derived<unknown>,\n  Set<Store<unknown>>\n>()\n\nexport const __depsThatHaveWrittenThisTick = {\n  current: [] as Array<Derived<unknown> | Store<unknown>>,\n}\n\nlet __isFlushing = false\nlet __batchDepth = 0\nconst __pendingUpdates = new Set<Store<unknown>>()\n// Add a map to store initial values before batch\nconst __initialBatchValues = new Map<Store<unknown>, unknown>()\n\nfunction __flush_internals(relatedVals: Set<Derived<unknown>>) {\n  // First sort deriveds by dependency order\n  const sorted = Array.from(relatedVals).sort((a, b) => {\n    // If a depends on b, b should go first\n    if (a instanceof Derived && a.options.deps.includes(b)) return 1\n    // If b depends on a, a should go first\n    if (b instanceof Derived && b.options.deps.includes(a)) return -1\n    return 0\n  })\n\n  for (const derived of sorted) {\n    if (__depsThatHaveWrittenThisTick.current.includes(derived)) {\n      continue\n    }\n\n    __depsThatHaveWrittenThisTick.current.push(derived)\n    derived.recompute()\n\n    const stores = __derivedToStore.get(derived)\n    if (stores) {\n      for (const store of stores) {\n        const relatedLinkedDerivedVals = __storeToDerived.get(store)\n        if (!relatedLinkedDerivedVals) continue\n        __flush_internals(relatedLinkedDerivedVals)\n      }\n    }\n  }\n}\n\nfunction __notifyListeners(store: Store<unknown>) {\n  store.listeners.forEach((listener) =>\n    listener({\n      prevVal: store.prevState as never,\n      currentVal: store.state as never,\n    }),\n  )\n}\n\nfunction __notifyDerivedListeners(derived: Derived<unknown>) {\n  derived.listeners.forEach((listener) =>\n    listener({\n      prevVal: derived.prevState as never,\n      currentVal: derived.state as never,\n    }),\n  )\n}\n\n/**\n * @private only to be called from `Store` on write\n */\nexport function __flush(store: Store<unknown>) {\n  // If we're starting a batch, store the initial values\n  if (__batchDepth > 0 && !__initialBatchValues.has(store)) {\n    __initialBatchValues.set(store, store.prevState)\n  }\n\n  __pendingUpdates.add(store)\n\n  if (__batchDepth > 0) return\n  if (__isFlushing) return\n\n  try {\n    __isFlushing = true\n\n    while (__pendingUpdates.size > 0) {\n      const stores = Array.from(__pendingUpdates)\n      __pendingUpdates.clear()\n\n      // First notify listeners with updated values\n      for (const store of stores) {\n        // Use initial batch values for prevState if we have them\n        const prevState = __initialBatchValues.get(store) ?? store.prevState\n        store.prevState = prevState\n        __notifyListeners(store)\n      }\n\n      // Then update all derived values\n      for (const store of stores) {\n        const derivedVals = __storeToDerived.get(store)\n        if (!derivedVals) continue\n\n        __depsThatHaveWrittenThisTick.current.push(store)\n        __flush_internals(derivedVals)\n      }\n\n      // Notify derived listeners after recomputing\n      for (const store of stores) {\n        const derivedVals = __storeToDerived.get(store)\n        if (!derivedVals) continue\n\n        for (const derived of derivedVals) {\n          __notifyDerivedListeners(derived)\n        }\n      }\n    }\n  } finally {\n    __isFlushing = false\n    __depsThatHaveWrittenThisTick.current = []\n    __initialBatchValues.clear()\n  }\n}\n\nexport function batch(fn: () => void) {\n  __batchDepth++\n  try {\n    fn()\n  } finally {\n    __batchDepth--\n    if (__batchDepth === 0) {\n      const pendingUpdateToFlush = Array.from(__pendingUpdates)[0] as\n        | Store<unknown>\n        | undefined\n      if (pendingUpdateToFlush) {\n        __flush(pendingUpdateToFlush) // Trigger flush of all pending updates\n      }\n    }\n  }\n}\n", "import { __flush } from './scheduler'\nimport type { AnyUpdater, Listener } from './types'\n\nexport interface StoreOptions<\n  TState,\n  TUpdater extends AnyUpdater = (cb: TState) => TState,\n> {\n  /**\n   * Replace the default update function with a custom one.\n   */\n  updateFn?: (previous: TState) => (updater: TUpdater) => TState\n  /**\n   * Called when a listener subscribes to the store.\n   *\n   * @return a function to unsubscribe the listener\n   */\n  onSubscribe?: (\n    listener: Listener<TState>,\n    store: Store<TState, TUpdater>,\n  ) => () => void\n  /**\n   * Called after the state has been updated, used to derive other state.\n   */\n  onUpdate?: () => void\n}\n\nexport class Store<\n  TState,\n  TUpdater extends AnyUpdater = (cb: TState) => TState,\n> {\n  listeners = new Set<Listener<TState>>()\n  state: TState\n  prevState: TState\n  options?: StoreOptions<TState, TUpdater>\n\n  constructor(initialState: TState, options?: StoreOptions<TState, TUpdater>) {\n    this.prevState = initialState\n    this.state = initialState\n    this.options = options\n  }\n\n  subscribe = (listener: Listener<TState>) => {\n    this.listeners.add(listener)\n    const unsub = this.options?.onSubscribe?.(listener, this)\n    return () => {\n      this.listeners.delete(listener)\n      unsub?.()\n    }\n  }\n\n  setState = (updater: TUpdater) => {\n    this.prevState = this.state\n    this.state = this.options?.updateFn\n      ? this.options.updateFn(this.prevState)(updater)\n      : (updater as any)(this.prevState)\n\n    // Always run onUpdate, regardless of batching\n    this.options?.onUpdate?.()\n\n    // Attempt to flush\n    __flush(this as never)\n  }\n}\n", "import { Store } from './store'\nimport { __derivedToStore, __storeToDerived } from './scheduler'\nimport type { Listener } from './types'\n\nexport type UnwrapDerivedOrStore<T> =\n  T extends Derived<infer InnerD>\n    ? InnerD\n    : T extends Store<infer InnerS>\n      ? InnerS\n      : never\n\ntype UnwrapReadonlyDerivedOrStoreArray<\n  TArr extends ReadonlyArray<Derived<any> | Store<any>>,\n> = TArr extends readonly [infer Head, ...infer Tail]\n  ? Head extends Derived<any> | Store<any>\n    ? Tail extends ReadonlyArray<Derived<any> | Store<any>>\n      ? [UnwrapDerivedOrStore<Head>, ...UnwrapReadonlyDerivedOrStoreArray<Tail>]\n      : []\n    : []\n  : []\n\n// Can't have currVal, as it's being evaluated from the current derived fn\nexport interface DerivedFnProps<\n  TArr extends ReadonlyArray<Derived<any> | Store<any>> = ReadonlyArray<any>,\n  TUnwrappedArr extends\n    UnwrapReadonlyDerivedOrStoreArray<TArr> = UnwrapReadonlyDerivedOrStoreArray<TArr>,\n> {\n  // `undefined` if it's the first run\n  /**\n   * `undefined` if it's the first run\n   * @privateRemarks this also cannot be typed as TState, as it breaks the inferencing of the function's return type when an argument is used - even with `NoInfer` usage\n   */\n  prevVal: unknown | undefined\n  prevDepVals: TUnwrappedArr | undefined\n  currDepVals: TUnwrappedArr\n}\n\nexport interface DerivedOptions<\n  TState,\n  TArr extends ReadonlyArray<Derived<any> | Store<any>> = ReadonlyArray<any>,\n> {\n  onSubscribe?: (\n    listener: Listener<TState>,\n    derived: Derived<TState>,\n  ) => () => void\n  onUpdate?: () => void\n  deps: TArr\n  /**\n   * Values of the `deps` from before and after the current invocation of `fn`\n   */\n  fn: (props: DerivedFnProps<TArr>) => TState\n}\n\nexport class Derived<\n  TState,\n  const TArr extends ReadonlyArray<\n    Derived<any> | Store<any>\n  > = ReadonlyArray<any>,\n> {\n  listeners = new Set<Listener<TState>>()\n  state: TState\n  prevState: TState | undefined\n  options: DerivedOptions<TState, TArr>\n\n  /**\n   * Functions representing the subscriptions. Call a function to cleanup\n   * @private\n   */\n  _subscriptions: Array<() => void> = []\n\n  lastSeenDepValues: Array<unknown> = []\n  getDepVals = () => {\n    const prevDepVals = [] as Array<unknown>\n    const currDepVals = [] as Array<unknown>\n    for (const dep of this.options.deps) {\n      prevDepVals.push(dep.prevState)\n      currDepVals.push(dep.state)\n    }\n    this.lastSeenDepValues = currDepVals\n    return {\n      prevDepVals,\n      currDepVals,\n      prevVal: this.prevState ?? undefined,\n    }\n  }\n\n  constructor(options: DerivedOptions<TState, TArr>) {\n    this.options = options\n    this.state = options.fn({\n      prevDepVals: undefined,\n      prevVal: undefined,\n      currDepVals: this.getDepVals().currDepVals as never,\n    })\n  }\n\n  registerOnGraph(\n    deps: ReadonlyArray<Derived<any> | Store<any>> = this.options.deps,\n  ) {\n    for (const dep of deps) {\n      if (dep instanceof Derived) {\n        // First register the intermediate derived value if it's not already registered\n        dep.registerOnGraph()\n        // Then register this derived with the dep's underlying stores\n        this.registerOnGraph(dep.options.deps)\n      } else if (dep instanceof Store) {\n        // Register the derived as related derived to the store\n        let relatedLinkedDerivedVals = __storeToDerived.get(dep)\n        if (!relatedLinkedDerivedVals) {\n          relatedLinkedDerivedVals = new Set()\n          __storeToDerived.set(dep, relatedLinkedDerivedVals)\n        }\n        relatedLinkedDerivedVals.add(this as never)\n\n        // Register the store as a related store to this derived\n        let relatedStores = __derivedToStore.get(this as never)\n        if (!relatedStores) {\n          relatedStores = new Set()\n          __derivedToStore.set(this as never, relatedStores)\n        }\n        relatedStores.add(dep)\n      }\n    }\n  }\n\n  unregisterFromGraph(\n    deps: ReadonlyArray<Derived<any> | Store<any>> = this.options.deps,\n  ) {\n    for (const dep of deps) {\n      if (dep instanceof Derived) {\n        this.unregisterFromGraph(dep.options.deps)\n      } else if (dep instanceof Store) {\n        const relatedLinkedDerivedVals = __storeToDerived.get(dep)\n        if (relatedLinkedDerivedVals) {\n          relatedLinkedDerivedVals.delete(this as never)\n        }\n\n        const relatedStores = __derivedToStore.get(this as never)\n        if (relatedStores) {\n          relatedStores.delete(dep)\n        }\n      }\n    }\n  }\n\n  recompute = () => {\n    this.prevState = this.state\n    const { prevDepVals, currDepVals, prevVal } = this.getDepVals()\n    this.state = this.options.fn({\n      prevDepVals: prevDepVals as never,\n      currDepVals: currDepVals as never,\n      prevVal,\n    })\n\n    this.options.onUpdate?.()\n  }\n\n  checkIfRecalculationNeededDeeply = () => {\n    for (const dep of this.options.deps) {\n      if (dep instanceof Derived) {\n        dep.checkIfRecalculationNeededDeeply()\n      }\n    }\n    let shouldRecompute = false\n    const lastSeenDepValues = this.lastSeenDepValues\n    const { currDepVals } = this.getDepVals()\n    for (let i = 0; i < currDepVals.length; i++) {\n      if (currDepVals[i] !== lastSeenDepValues[i]) {\n        shouldRecompute = true\n        break\n      }\n    }\n\n    if (shouldRecompute) {\n      this.recompute()\n    }\n  }\n\n  mount = () => {\n    this.registerOnGraph()\n    this.checkIfRecalculationNeededDeeply()\n\n    return () => {\n      this.unregisterFromGraph()\n      for (const cleanup of this._subscriptions) {\n        cleanup()\n      }\n    }\n  }\n\n  subscribe = (listener: Listener<TState>) => {\n    this.listeners.add(listener)\n    const unsub = this.options.onSubscribe?.(listener, this)\n    return () => {\n      this.listeners.delete(listener)\n      unsub?.()\n    }\n  }\n}\n", "import { Derived } from './derived'\nimport type { DerivedOptions } from './derived'\n\ninterface EffectOptions\n  extends Omit<\n    DerivedOptions<unknown>,\n    'onUpdate' | 'onSubscribe' | 'lazy' | 'fn'\n  > {\n  /**\n   * Should the effect trigger immediately?\n   * @default false\n   */\n  eager?: boolean\n  fn: () => void\n}\n\nexport class Effect {\n  /**\n   * @private\n   */\n  _derived: Derived<void>\n\n  constructor(opts: EffectOptions) {\n    const { eager, fn, ...derivedProps } = opts\n\n    this._derived = new Derived({\n      ...derivedProps,\n      fn: () => {},\n      onUpdate() {\n        fn()\n      },\n    })\n\n    if (eager) {\n      fn()\n    }\n  }\n\n  mount() {\n    return this._derived.mount()\n  }\n}\n", "import { readonly, ref, toRaw, watch } from 'vue-demi'\nimport type { Derived, Store } from '@tanstack/store'\nimport type { Ref } from 'vue-demi'\n\nexport * from '@tanstack/store'\n\n/**\n * @private\n */\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Store<TState, any>,\n  selector?: (state: NoInfer<TState>) => TSelected,\n): Readonly<Ref<TSelected>>\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Derived<TState, any>,\n  selector?: (state: NoInfer<TState>) => TSelected,\n): Readonly<Ref<TSelected>>\nexport function useStore<TState, TSelected = NoInfer<TState>>(\n  store: Store<TState, any> | Derived<TState, any>,\n  selector: (state: NoInfer<TState>) => TSelected = (d) => d as any,\n): Readonly<Ref<TSelected>> {\n  const slice = ref(selector(store.state)) as Ref<TSelected>\n\n  watch(\n    () => store,\n    (value, _oldValue, onCleanup) => {\n      const unsub = value.subscribe(() => {\n        const data = selector(value.state)\n        if (shallow(toRaw(slice.value), data)) {\n          return\n        }\n        slice.value = data\n      })\n\n      onCleanup(() => {\n        unsub()\n      })\n    },\n    { immediate: true },\n  )\n\n  return readonly(slice) as never\n}\n\nexport function shallow<T>(objA: T, objB: T) {\n  if (Object.is(objA, objB)) {\n    return true\n  }\n\n  if (\n    typeof objA !== 'object' ||\n    objA === null ||\n    typeof objB !== 'object' ||\n    objB === null\n  ) {\n    return false\n  }\n\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false\n    for (const [k, v] of objA) {\n      if (!objB.has(k) || !Object.is(v, objB.get(k))) return false\n    }\n    return true\n  }\n\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false\n    for (const v of objA) {\n      if (!objB.has(v)) return false\n    }\n    return true\n  }\n\n  const keysA = Object.keys(objA)\n  if (keysA.length !== Object.keys(objB).length) {\n    return false\n  }\n\n  for (let i = 0; i < keysA.length; i++) {\n    if (\n      !Object.prototype.hasOwnProperty.call(objB, keysA[i] as string) ||\n      !Object.is(objA[keysA[i] as keyof T], objB[keysA[i] as keyof T])\n    ) {\n      return false\n    }\n  }\n  return true\n}\n"], "mappings": ";;;;;;;;;;;;AAkBa,IAAA,mBAAA,oBAAuB,QAGlC;AACW,IAAA,mBAAA,oBAAuB,QAGlC;AAEK,IAAM,gCAAgC;EAC3C,SAAS,CAAA;AACX;AAEA,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAM,mBAAA,oBAAuB,IAAoB;AAEjD,IAAM,uBAAA,oBAA2B,IAA6B;AAE9D,SAAS,kBAAkB,aAAoC;AAEvD,QAAA,SAAS,MAAM,KAAK,WAAW,EAAE,KAAK,CAAC,GAAG,MAAM;AAEhD,QAAA,aAAa,WAAW,EAAE,QAAQ,KAAK,SAAS,CAAC,EAAU,QAAA;AAE3D,QAAA,aAAa,WAAW,EAAE,QAAQ,KAAK,SAAS,CAAC,EAAU,QAAA;AACxD,WAAA;EAAA,CACR;AAED,aAAW,WAAW,QAAQ;AAC5B,QAAI,8BAA8B,QAAQ,SAAS,OAAO,GAAG;AAC3D;IAAA;AAG4B,kCAAA,QAAQ,KAAK,OAAO;AAClD,YAAQ,UAAU;AAEZ,UAAA,SAAS,iBAAiB,IAAI,OAAO;AAC3C,QAAI,QAAQ;AACV,iBAAW,SAAS,QAAQ;AACpB,cAAA,2BAA2B,iBAAiB,IAAI,KAAK;AAC3D,YAAI,CAAC,yBAA0B;AAC/B,0BAAkB,wBAAwB;MAAA;IAC5C;EACF;AAEJ;AAEA,SAAS,kBAAkB,OAAuB;AAChD,QAAM,UAAU;IAAQ,CAAC,aACvB,SAAS;MACP,SAAS,MAAM;MACf,YAAY,MAAM;IACnB,CAAA;EACH;AACF;AAEA,SAAS,yBAAyB,SAA2B;AAC3D,UAAQ,UAAU;IAAQ,CAAC,aACzB,SAAS;MACP,SAAS,QAAQ;MACjB,YAAY,QAAQ;IACrB,CAAA;EACH;AACF;AAKO,SAAS,QAAQ,OAAuB;AAE7C,MAAI,eAAe,KAAK,CAAC,qBAAqB,IAAI,KAAK,GAAG;AACnC,yBAAA,IAAI,OAAO,MAAM,SAAS;EAAA;AAGjD,mBAAiB,IAAI,KAAK;AAE1B,MAAI,eAAe,EAAG;AACtB,MAAI,aAAc;AAEd,MAAA;AACa,mBAAA;AAER,WAAA,iBAAiB,OAAO,GAAG;AAC1B,YAAA,SAAS,MAAM,KAAK,gBAAgB;AAC1C,uBAAiB,MAAM;AAGvB,iBAAWA,UAAS,QAAQ;AAE1B,cAAM,YAAY,qBAAqB,IAAIA,MAAK,KAAKA,OAAM;AAC3DA,eAAM,YAAY;AAClB,0BAAkBA,MAAK;MAAA;AAIzB,iBAAWA,UAAS,QAAQ;AACpB,cAAA,cAAc,iBAAiB,IAAIA,MAAK;AAC9C,YAAI,CAAC,YAAa;AAEY,sCAAA,QAAQ,KAAKA,MAAK;AAChD,0BAAkB,WAAW;MAAA;AAI/B,iBAAWA,UAAS,QAAQ;AACpB,cAAA,cAAc,iBAAiB,IAAIA,MAAK;AAC9C,YAAI,CAAC,YAAa;AAElB,mBAAW,WAAW,aAAa;AACjC,mCAAyB,OAAO;QAAA;MAClC;IACF;EACF,UAAA;AAEe,mBAAA;AACf,kCAA8B,UAAU,CAAC;AACzC,yBAAqB,MAAM;EAAA;AAE/B;AAEO,SAAS,MAAM,IAAgB;AACpC;AACI,MAAA;AACC,OAAA;EAAA,UAAA;AAEH;AACA,QAAI,iBAAiB,GAAG;AACtB,YAAM,uBAAuB,MAAM,KAAK,gBAAgB,EAAE,CAAC;AAG3D,UAAI,sBAAsB;AACxB,gBAAQ,oBAAoB;MAAA;IAC9B;EACF;AAEJ;;;AChIO,IAAM,QAAN,MAGL;EAMA,YAAY,cAAsB,SAA0C;AAL5E,SAAA,YAAA,oBAAgB,IAAsB;AAWtC,SAAA,YAAY,CAAC,aAA+B;;AACrC,WAAA,UAAU,IAAI,QAAQ;AAC3B,YAAM,SAAQ,MAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,gBAAd,OAAA,SAAA,GAAA,KAAA,IAA4B,UAAU,IAAA;AACpD,aAAO,MAAM;AACN,aAAA,UAAU,OAAO,QAAQ;AACtB,iBAAA,OAAA,SAAA,MAAA;MACV;IACF;AAEA,SAAA,WAAW,CAAC,YAAsB;;AAChC,WAAK,YAAY,KAAK;AACtB,WAAK,UAAQ,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,YACvB,KAAK,QAAQ,SAAS,KAAK,SAAS,EAAE,OAAO,IAC5C,QAAgB,KAAK,SAAS;AAGnC,OAAA,MAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,aAAd,OAAA,SAAA,GAAA,KAAA,EAAA;AAGA,cAAQ,IAAa;IACvB;AAzBE,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,UAAU;EAAA;AAwBnB;;;ACTO,IAAM,UAAN,MAAM,SAKX;EA4BA,YAAY,SAAuC;AA3BnD,SAAA,YAAA,oBAAgB,IAAsB;AAStC,SAAA,iBAAoC,CAAC;AAErC,SAAA,oBAAoC,CAAC;AACrC,SAAA,aAAa,MAAM;AACjB,YAAM,cAAc,CAAC;AACrB,YAAM,cAAc,CAAC;AACV,iBAAA,OAAO,KAAK,QAAQ,MAAM;AACvB,oBAAA,KAAK,IAAI,SAAS;AAClB,oBAAA,KAAK,IAAI,KAAK;MAAA;AAE5B,WAAK,oBAAoB;AAClB,aAAA;QACL;QACA;QACA,SAAS,KAAK,aAAa;MAC7B;IACF;AA4DA,SAAA,YAAY,MAAM;;AAChB,WAAK,YAAY,KAAK;AACtB,YAAM,EAAE,aAAa,aAAa,QAAQ,IAAI,KAAK,WAAW;AACzD,WAAA,QAAQ,KAAK,QAAQ,GAAG;QAC3B;QACA;QACA;MAAA,CACD;AAED,OAAA,MAAA,KAAA,KAAK,SAAQ,aAAb,OAAA,SAAA,GAAA,KAAA,EAAA;IACF;AAEA,SAAA,mCAAmC,MAAM;AAC5B,iBAAA,OAAO,KAAK,QAAQ,MAAM;AACnC,YAAI,eAAe,UAAS;AAC1B,cAAI,iCAAiC;QAAA;MACvC;AAEF,UAAI,kBAAkB;AACtB,YAAM,oBAAoB,KAAK;AAC/B,YAAM,EAAE,YAAA,IAAgB,KAAK,WAAW;AACxC,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAI,YAAY,CAAC,MAAM,kBAAkB,CAAC,GAAG;AACzB,4BAAA;AAClB;QAAA;MACF;AAGF,UAAI,iBAAiB;AACnB,aAAK,UAAU;MAAA;IAEnB;AAEA,SAAA,QAAQ,MAAM;AACZ,WAAK,gBAAgB;AACrB,WAAK,iCAAiC;AAEtC,aAAO,MAAM;AACX,aAAK,oBAAoB;AACd,mBAAA,WAAW,KAAK,gBAAgB;AACjC,kBAAA;QAAA;MAEZ;IACF;AAEA,SAAA,YAAY,CAAC,aAA+B;;AACrC,WAAA,UAAU,IAAI,QAAQ;AAC3B,YAAM,SAAQ,MAAA,KAAA,KAAK,SAAQ,gBAAb,OAAA,SAAA,GAAA,KAAA,IAA2B,UAAU,IAAA;AACnD,aAAO,MAAM;AACN,aAAA,UAAU,OAAO,QAAQ;AACtB,iBAAA,OAAA,SAAA,MAAA;MACV;IACF;AA7GE,SAAK,UAAU;AACV,SAAA,QAAQ,QAAQ,GAAG;MACtB,aAAa;MACb,SAAS;MACT,aAAa,KAAK,WAAA,EAAa;IAAA,CAChC;EAAA;EAGH,gBACE,OAAiD,KAAK,QAAQ,MAC9D;AACA,eAAW,OAAO,MAAM;AACtB,UAAI,eAAe,UAAS;AAE1B,YAAI,gBAAgB;AAEf,aAAA,gBAAgB,IAAI,QAAQ,IAAI;MAAA,WAC5B,eAAe,OAAO;AAE3B,YAAA,2BAA2B,iBAAiB,IAAI,GAAG;AACvD,YAAI,CAAC,0BAA0B;AAC7B,qCAAA,oBAA+B,IAAI;AAClB,2BAAA,IAAI,KAAK,wBAAwB;QAAA;AAEpD,iCAAyB,IAAI,IAAa;AAGtC,YAAA,gBAAgB,iBAAiB,IAAI,IAAa;AACtD,YAAI,CAAC,eAAe;AAClB,0BAAA,oBAAoB,IAAI;AACP,2BAAA,IAAI,MAAe,aAAa;QAAA;AAEnD,sBAAc,IAAI,GAAG;MAAA;IACvB;EACF;EAGF,oBACE,OAAiD,KAAK,QAAQ,MAC9D;AACA,eAAW,OAAO,MAAM;AACtB,UAAI,eAAe,UAAS;AACrB,aAAA,oBAAoB,IAAI,QAAQ,IAAI;MAAA,WAChC,eAAe,OAAO;AACzB,cAAA,2BAA2B,iBAAiB,IAAI,GAAG;AACzD,YAAI,0BAA0B;AAC5B,mCAAyB,OAAO,IAAa;QAAA;AAGzC,cAAA,gBAAgB,iBAAiB,IAAI,IAAa;AACxD,YAAI,eAAe;AACjB,wBAAc,OAAO,GAAG;QAAA;MAC1B;IACF;EACF;AAwDJ;;;ACrLO,IAAM,SAAN,MAAa;EAMlB,YAAY,MAAqB;AAC/B,UAAM,EAAE,OAAO,IAAI,GAAG,aAAiB,IAAA;AAElC,SAAA,WAAW,IAAI,QAAQ;MAC1B,GAAG;MACH,IAAI,MAAM;MAAC;MACX,WAAW;AACN,WAAA;MAAA;IACL,CACD;AAED,QAAI,OAAO;AACN,SAAA;IAAA;EACL;EAGF,QAAQ;AACC,WAAA,KAAK,SAAS,MAAM;EAAA;AAE/B;;;ACtBO,SAAS,SACd,OACA,WAAkD,CAAC,MAAM,GAC/B;AAC1B,QAAM,QAAQ,IAAI,SAAS,MAAM,KAAK,CAAC;AAEvC;IACE,MAAM;IACN,CAAC,OAAO,WAAW,cAAc;AACzB,YAAA,QAAQ,MAAM,UAAU,MAAM;AAC5B,cAAA,OAAO,SAAS,MAAM,KAAK;AACjC,YAAI,QAAQ,MAAM,MAAM,KAAK,GAAG,IAAI,GAAG;AACrC;QAAA;AAEF,cAAM,QAAQ;MAAA,CACf;AAED,gBAAU,MAAM;AACR,cAAA;MAAA,CACP;IACH;IACA,EAAE,WAAW,KAAK;EACpB;AAEA,SAAO,SAAS,KAAK;AACvB;AAEgB,SAAA,QAAW,MAAS,MAAS;AAC3C,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AAClB,WAAA;EAAA;AAIP,MAAA,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,SAAS,YAChB,SAAS,MACT;AACO,WAAA;EAAA;AAGL,MAAA,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAa,QAAA;AACpC,eAAW,CAAC,GAAG,CAAC,KAAK,MAAM;AACzB,UAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,EAAU,QAAA;IAAA;AAElD,WAAA;EAAA;AAGL,MAAA,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAa,QAAA;AACpC,eAAW,KAAK,MAAM;AACpB,UAAI,CAAC,KAAK,IAAI,CAAC,EAAU,QAAA;IAAA;AAEpB,WAAA;EAAA;AAGH,QAAA,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AACtC,WAAA;EAAA;AAGT,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAEnC,QAAA,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM,CAAC,CAAW,KAC9D,CAAC,OAAO,GAAG,KAAK,MAAM,CAAC,CAAY,GAAG,KAAK,MAAM,CAAC,CAAY,CAAC,GAC/D;AACO,aAAA;IAAA;EACT;AAEK,SAAA;AACT;", "names": ["store"]}