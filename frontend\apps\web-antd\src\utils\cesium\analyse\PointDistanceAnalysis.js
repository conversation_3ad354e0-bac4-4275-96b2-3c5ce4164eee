import * as Cesium from 'cesium';
import { executeTask } from './GPExecute';
import GPConfig from '../../../config/GPConfig';
import {
  arcgisToGeoJSON,
  geojsonToArcGIS,
} from '@esri/arcgis-to-geojson-utils';

export default class PointDistanceAnalysis {
  constructor(params) {
    this.viewer = params.that._viewer;
    this.gp = params.gp || GPConfig.pointdistance;
    this.complete = params.complete;
  }
  async execute(options, positions) {
    this.remove();
    positions = positions.map((p) => [p.lng, p.lat]);
    const params = {
      ...options,
      filterFeature: JSON.stringify({
        objectIdFieldName: 'objectid',
        globalIdFieldName: '',
        geometryType: 'esriGeometryPolygon',
        spatialReference: {
          wkid: 4326,
          latestWkid: 4326,
        },
        fields: [],
        features: [
          {
            geometry: {
              rings: [positions],
            },
          },
        ],
      }),
      'env:outSR': 4326,
      'env:processSR': 4326,
      f: 'json',
    };
    //try{
    const res = await executeTask(this.gp, params);
    const result = res.results[0].value;
    console.log('...........result', result);
    // return result
    if (this.complete) this.complete(result);
    //}
    //catch(err){

    //	console.log('sfsdfsdfsdf',err)
    //}
  }
  remove() {
    let viewer = this.viewer;
    if (this.dataSource) {
      viewer.dataSources.remove(this.dataSource);
    }
  }
}
