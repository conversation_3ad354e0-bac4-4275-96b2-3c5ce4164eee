{"version": 3, "sources": ["../../../../../node_modules/.pnpm/yocto-queue@1.2.1/node_modules/yocto-queue/index.js", "../../../../../node_modules/.pnpm/p-limit@6.2.0/node_modules/p-limit/index.js"], "sourcesContent": ["/*\nHow it works:\n`this.#head` is an instance of `Node` which keeps track of its current value and nests another instance of `Node` that keeps the value that comes after it. When a value is provided to `.enqueue()`, the code needs to iterate through `this.#head`, going deeper and deeper to find the last value. However, iterating through every single item is slow. This problem is solved by saving a reference to the last value as `this.#tail` so that it can reference it to add a new value.\n*/\n\nclass Node {\n\tvalue;\n\tnext;\n\n\tconstructor(value) {\n\t\tthis.value = value;\n\t}\n}\n\nexport default class Queue {\n\t#head;\n\t#tail;\n\t#size;\n\n\tconstructor() {\n\t\tthis.clear();\n\t}\n\n\tenqueue(value) {\n\t\tconst node = new Node(value);\n\n\t\tif (this.#head) {\n\t\t\tthis.#tail.next = node;\n\t\t\tthis.#tail = node;\n\t\t} else {\n\t\t\tthis.#head = node;\n\t\t\tthis.#tail = node;\n\t\t}\n\n\t\tthis.#size++;\n\t}\n\n\tdequeue() {\n\t\tconst current = this.#head;\n\t\tif (!current) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.#head = this.#head.next;\n\t\tthis.#size--;\n\t\treturn current.value;\n\t}\n\n\tpeek() {\n\t\tif (!this.#head) {\n\t\t\treturn;\n\t\t}\n\n\t\treturn this.#head.value;\n\n\t\t// TODO: Node.js 18.\n\t\t// return this.#head?.value;\n\t}\n\n\tclear() {\n\t\tthis.#head = undefined;\n\t\tthis.#tail = undefined;\n\t\tthis.#size = 0;\n\t}\n\n\tget size() {\n\t\treturn this.#size;\n\t}\n\n\t* [Symbol.iterator]() {\n\t\tlet current = this.#head;\n\n\t\twhile (current) {\n\t\t\tyield current.value;\n\t\t\tcurrent = current.next;\n\t\t}\n\t}\n\n\t* drain() {\n\t\twhile (this.#head) {\n\t\t\tyield this.dequeue();\n\t\t}\n\t}\n}\n", "import Queue from 'yocto-queue';\n\nexport default function pLimit(concurrency) {\n\tvalidateConcurrency(concurrency);\n\n\tconst queue = new Queue();\n\tlet activeCount = 0;\n\n\tconst resumeNext = () => {\n\t\tif (activeCount < concurrency && queue.size > 0) {\n\t\t\tqueue.dequeue()();\n\t\t\t// Since `pendingCount` has been decreased by one, increase `activeCount` by one.\n\t\t\tactiveCount++;\n\t\t}\n\t};\n\n\tconst next = () => {\n\t\tactiveCount--;\n\n\t\tresumeNext();\n\t};\n\n\tconst run = async (function_, resolve, arguments_) => {\n\t\tconst result = (async () => function_(...arguments_))();\n\n\t\tresolve(result);\n\n\t\ttry {\n\t\t\tawait result;\n\t\t} catch {}\n\n\t\tnext();\n\t};\n\n\tconst enqueue = (function_, resolve, arguments_) => {\n\t\t// Queue `internalResolve` instead of the `run` function\n\t\t// to preserve asynchronous context.\n\t\tnew Promise(internalResolve => {\n\t\t\tqueue.enqueue(internalResolve);\n\t\t}).then(\n\t\t\trun.bind(undefined, function_, resolve, arguments_),\n\t\t);\n\n\t\t(async () => {\n\t\t\t// This function needs to wait until the next microtask before comparing\n\t\t\t// `activeCount` to `concurrency`, because `activeCount` is updated asynchronously\n\t\t\t// after the `internalResolve` function is dequeued and called. The comparison in the if-statement\n\t\t\t// needs to happen asynchronously as well to get an up-to-date value for `activeCount`.\n\t\t\tawait Promise.resolve();\n\n\t\t\tif (activeCount < concurrency) {\n\t\t\t\tresumeNext();\n\t\t\t}\n\t\t})();\n\t};\n\n\tconst generator = (function_, ...arguments_) => new Promise(resolve => {\n\t\tenqueue(function_, resolve, arguments_);\n\t});\n\n\tObject.defineProperties(generator, {\n\t\tactiveCount: {\n\t\t\tget: () => activeCount,\n\t\t},\n\t\tpendingCount: {\n\t\t\tget: () => queue.size,\n\t\t},\n\t\tclearQueue: {\n\t\t\tvalue() {\n\t\t\t\tqueue.clear();\n\t\t\t},\n\t\t},\n\t\tconcurrency: {\n\t\t\tget: () => concurrency,\n\n\t\t\tset(newConcurrency) {\n\t\t\t\tvalidateConcurrency(newConcurrency);\n\t\t\t\tconcurrency = newConcurrency;\n\n\t\t\t\tqueueMicrotask(() => {\n\t\t\t\t\t// eslint-disable-next-line no-unmodified-loop-condition\n\t\t\t\t\twhile (activeCount < concurrency && queue.size > 0) {\n\t\t\t\t\t\tresumeNext();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t},\n\t});\n\n\treturn generator;\n}\n\nexport function limitFunction(function_, option) {\n\tconst {concurrency} = option;\n\tconst limit = pLimit(concurrency);\n\n\treturn (...arguments_) => limit(() => function_(...arguments_));\n}\n\nfunction validateConcurrency(concurrency) {\n\tif (!((Number.isInteger(concurrency) || concurrency === Number.POSITIVE_INFINITY) && concurrency > 0)) {\n\t\tthrow new TypeError('Expected `concurrency` to be a number from 1 and up');\n\t}\n}\n"], "mappings": ";;;;;;;;;AAKA,IAAM,OAAN,MAAW;AAAA,EAIV,YAAY,OAAO;AAHnB;AACA;AAGC,SAAK,QAAQ;AAAA,EACd;AACD;AAZA;AAcA,IAAqB,QAArB,MAA2B;AAAA,EAK1B,cAAc;AAJd;AACA;AACA;AAGC,SAAK,MAAM;AAAA,EACZ;AAAA,EAEA,QAAQ,OAAO;AACd,UAAM,OAAO,IAAI,KAAK,KAAK;AAE3B,QAAI,mBAAK,QAAO;AACf,yBAAK,OAAM,OAAO;AAClB,yBAAK,OAAQ;AAAA,IACd,OAAO;AACN,yBAAK,OAAQ;AACb,yBAAK,OAAQ;AAAA,IACd;AAEA,2BAAK,OAAL;AAAA,EACD;AAAA,EAEA,UAAU;AACT,UAAM,UAAU,mBAAK;AACrB,QAAI,CAAC,SAAS;AACb;AAAA,IACD;AAEA,uBAAK,OAAQ,mBAAK,OAAM;AACxB,2BAAK,OAAL;AACA,WAAO,QAAQ;AAAA,EAChB;AAAA,EAEA,OAAO;AACN,QAAI,CAAC,mBAAK,QAAO;AAChB;AAAA,IACD;AAEA,WAAO,mBAAK,OAAM;AAAA,EAInB;AAAA,EAEA,QAAQ;AACP,uBAAK,OAAQ;AACb,uBAAK,OAAQ;AACb,uBAAK,OAAQ;AAAA,EACd;AAAA,EAEA,IAAI,OAAO;AACV,WAAO,mBAAK;AAAA,EACb;AAAA,EAEA,EAAG,OAAO,QAAQ,IAAI;AACrB,QAAI,UAAU,mBAAK;AAEnB,WAAO,SAAS;AACf,YAAM,QAAQ;AACd,gBAAU,QAAQ;AAAA,IACnB;AAAA,EACD;AAAA,EAEA,CAAE,QAAQ;AACT,WAAO,mBAAK,QAAO;AAClB,YAAM,KAAK,QAAQ;AAAA,IACpB;AAAA,EACD;AACD;AApEC;AACA;AACA;;;ACfc,SAAR,OAAwB,aAAa;AAC3C,sBAAoB,WAAW;AAE/B,QAAM,QAAQ,IAAI,MAAM;AACxB,MAAI,cAAc;AAElB,QAAM,aAAa,MAAM;AACxB,QAAI,cAAc,eAAe,MAAM,OAAO,GAAG;AAChD,YAAM,QAAQ,EAAE;AAEhB;AAAA,IACD;AAAA,EACD;AAEA,QAAM,OAAO,MAAM;AAClB;AAEA,eAAW;AAAA,EACZ;AAEA,QAAM,MAAM,OAAO,WAAW,SAAS,eAAe;AACrD,UAAM,UAAU,YAAY,UAAU,GAAG,UAAU,GAAG;AAEtD,YAAQ,MAAM;AAEd,QAAI;AACH,YAAM;AAAA,IACP,QAAQ;AAAA,IAAC;AAET,SAAK;AAAA,EACN;AAEA,QAAM,UAAU,CAAC,WAAW,SAAS,eAAe;AAGnD,QAAI,QAAQ,qBAAmB;AAC9B,YAAM,QAAQ,eAAe;AAAA,IAC9B,CAAC,EAAE;AAAA,MACF,IAAI,KAAK,QAAW,WAAW,SAAS,UAAU;AAAA,IACnD;AAEA,KAAC,YAAY;AAKZ,YAAM,QAAQ,QAAQ;AAEtB,UAAI,cAAc,aAAa;AAC9B,mBAAW;AAAA,MACZ;AAAA,IACD,GAAG;AAAA,EACJ;AAEA,QAAM,YAAY,CAAC,cAAc,eAAe,IAAI,QAAQ,aAAW;AACtE,YAAQ,WAAW,SAAS,UAAU;AAAA,EACvC,CAAC;AAED,SAAO,iBAAiB,WAAW;AAAA,IAClC,aAAa;AAAA,MACZ,KAAK,MAAM;AAAA,IACZ;AAAA,IACA,cAAc;AAAA,MACb,KAAK,MAAM,MAAM;AAAA,IAClB;AAAA,IACA,YAAY;AAAA,MACX,QAAQ;AACP,cAAM,MAAM;AAAA,MACb;AAAA,IACD;AAAA,IACA,aAAa;AAAA,MACZ,KAAK,MAAM;AAAA,MAEX,IAAI,gBAAgB;AACnB,4BAAoB,cAAc;AAClC,sBAAc;AAEd,uBAAe,MAAM;AAEpB,iBAAO,cAAc,eAAe,MAAM,OAAO,GAAG;AACnD,uBAAW;AAAA,UACZ;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EACD,CAAC;AAED,SAAO;AACR;AAEO,SAAS,cAAc,WAAW,QAAQ;AAChD,QAAM,EAAC,YAAW,IAAI;AACtB,QAAM,QAAQ,OAAO,WAAW;AAEhC,SAAO,IAAI,eAAe,MAAM,MAAM,UAAU,GAAG,UAAU,CAAC;AAC/D;AAEA,SAAS,oBAAoB,aAAa;AACzC,MAAI,GAAG,OAAO,UAAU,WAAW,KAAK,gBAAgB,OAAO,sBAAsB,cAAc,IAAI;AACtG,UAAM,IAAI,UAAU,qDAAqD;AAAA,EAC1E;AACD;", "names": []}