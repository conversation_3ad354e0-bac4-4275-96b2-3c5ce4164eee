import { Entity, Column, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '@/common/entities/base.entity';

@Entity('sys_config')
@Index(['configKey'])
@Index(['configType'])
export class SysConfig extends BaseEntity {
  @ApiProperty({ description: '参数名称' })
  @Column({
    name: 'config_name',
    type: 'varchar',
    length: 100,
    comment: '参数名称',
  })
  configName: string;

  @ApiProperty({ description: '参数键名' })
  @Column({
    name: 'config_key',
    type: 'varchar',
    length: 100,
    unique: true,
    comment: '参数键名',
  })
  configKey: string;

  @ApiProperty({ description: '参数键值' })
  @Column({
    name: 'config_value',
    type: 'text',
    nullable: true,
    comment: '参数键值',
  })
  configValue?: string;

  @ApiProperty({ description: '系统内置：Y是，N否' })
  @Column({
    name: 'config_type',
    type: 'enum',
    enum: ['Y', 'N'],
    default: 'N',
    comment: '系统内置：Y是，N否',
  })
  configType: string;

  @ApiProperty({ description: '备注' })
  @Column({
    type: 'text',
    nullable: true,
    comment: '备注',
  })
  remark?: string;
}
