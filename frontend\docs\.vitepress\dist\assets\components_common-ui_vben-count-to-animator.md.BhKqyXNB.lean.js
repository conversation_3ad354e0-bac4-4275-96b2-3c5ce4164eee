import{_ as i}from"./chunks/theme.TDvSnEYR.js";import{f as s,j as t,y as a,u as e,k as h,aP as d,l,m as k,z as n,I as r,ay as o}from"./chunks/framework.C8U7mBUf.js";const E=s({__name:"index",setup:s=>(s,h)=>(t(),a(e(i),{duration:3e3,"end-val":3e4,"start-val":1}))}),p=s({__name:"index",setup:s=>(s,h)=>(t(),a(e(i),{duration:3e3,"end-val":2e6,"start-val":1,prefix:"$",separator:"/"}))}),c=JSON.parse('{"title":"Vben CountToAnimator 数字动画","description":"","frontmatter":{"outline":"deep"},"headers":[],"relativePath":"components/common-ui/vben-count-to-animator.md","filePath":"components/common-ui/vben-count-to-animator.md"}'),g={name:"components/common-ui/vben-count-to-animator.md"},y=Object.assign(g,{setup:i=>(i,s)=>{const a=o("DemoPreview"),e=o("NolebaseGitContributors"),c=o("NolebaseGitChangelog");return t(),h("div",null,[s[2]||(s[2]=d('<h1 id="vben-counttoanimator-数字动画" tabindex="-1">Vben CountToAnimator 数字动画 <a class="header-anchor" href="#vben-counttoanimator-数字动画" aria-label="Permalink to &quot;Vben CountToAnimator 数字动画&quot;">​</a></h1><p>框架提供的数字动画组件，支持数字动画效果。</p><blockquote><p>如果文档内没有参数说明，可以尝试在在线示例内寻找</p></blockquote><div class="info custom-block"><p class="custom-block-title">写在前面</p><p>如果你觉得现有组件的封装不够理想，或者不完全符合你的需求，大可以直接使用原生组件，亦或亲手封装一个适合的组件。框架提供的组件并非束缚，使用与否，完全取决于你的需求与自由。</p></div><h2 id="基础用法" tabindex="-1">基础用法 <a class="header-anchor" href="#基础用法" aria-label="Permalink to &quot;基础用法&quot;">​</a></h2><p>通过 <code>start-val</code> 和 <code>end-val</code>设置数字动画的开始值和结束值， 持续时间<code>3000</code>ms。</p>',6)),l(a,{files:"%5B%22index.vue%22%5D"},{"index.vue":k((()=>s[0]||(s[0]=[n("div",{class:"language-vue vp-adaptive-theme"},[n("button",{title:"Copy Code",class:"copy"}),n("span",{class:"lang"},"vue"),n("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[n("code",null,[n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),n("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),n("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),n("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { VbenCountToAnimator } "),n("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '@vben/common-ui'"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),n("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),n("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),n("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenCountToAnimator"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," :"),n("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"duration"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),n("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"3000"),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," :"),n("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"end-val"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),n("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"30000"),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," :"),n("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"start-val"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),n("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"1"),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," />")]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),n("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:k((()=>[l(E)])),_:1}),s[3]||(s[3]=n("h2",{id:"自定义前缀及分隔符",tabindex:"-1"},[r("自定义前缀及分隔符 "),n("a",{class:"header-anchor",href:"#自定义前缀及分隔符","aria-label":'Permalink to "自定义前缀及分隔符"'},"​")],-1)),s[4]||(s[4]=n("p",null,[r("通过 "),n("code",null,"prefix"),r(" 和 "),n("code",null,"separator"),r(" 设置数字动画的前缀和分隔符。")],-1)),l(a,{files:"%5B%22index.vue%22%5D"},{"index.vue":k((()=>s[1]||(s[1]=[n("div",{class:"language-vue vp-adaptive-theme"},[n("button",{title:"Copy Code",class:"copy"}),n("span",{class:"lang"},"vue"),n("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[n("code",null,[n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),n("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),n("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),n("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { VbenCountToAnimator } "),n("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '@vben/common-ui'"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),n("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),n("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),n("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenCountToAnimator")]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    :"),n("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"duration"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),n("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"3000"),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"')]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    :"),n("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"end-val"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),n("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"2000000"),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"')]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    :"),n("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"start-val"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),n("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"1"),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"')]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"    prefix"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"$"')]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"    separator"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),n("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"/"')]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  />")]),r("\n"),n("span",{class:"line"},[n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),n("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),n("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:k((()=>[l(p)])),_:1}),s[5]||(s[5]=d('<h3 id="props" tabindex="-1">Props <a class="header-anchor" href="#props" aria-label="Permalink to &quot;Props&quot;">​</a></h3><table tabindex="0"><thead><tr><th>属性名</th><th>描述</th><th>类型</th><th>默认值</th></tr></thead><tbody><tr><td>startVal</td><td>起始值</td><td><code>number</code></td><td><code>0</code></td></tr><tr><td>endVal</td><td>结束值</td><td><code>number</code></td><td><code>2021</code></td></tr><tr><td>duration</td><td>动画持续时间</td><td><code>number</code></td><td><code>1500</code></td></tr><tr><td>autoplay</td><td>自动执行</td><td><code>boolean</code></td><td><code>true</code></td></tr><tr><td>prefix</td><td>前缀</td><td><code>string</code></td><td>-</td></tr><tr><td>suffix</td><td>后缀</td><td><code>string</code></td><td>-</td></tr><tr><td>separator</td><td>分隔符</td><td><code>string</code></td><td><code>,</code></td></tr><tr><td>color</td><td>字体颜色</td><td><code>string</code></td><td>-</td></tr><tr><td>useEasing</td><td>是否开启动画</td><td><code>boolean</code></td><td><code>true</code></td></tr><tr><td>transition</td><td>动画效果</td><td><code>string</code></td><td><code>linear</code></td></tr><tr><td>decimals</td><td>保留小数点位数</td><td><code>number</code></td><td><code>0</code></td></tr></tbody></table><h3 id="methods" tabindex="-1">Methods <a class="header-anchor" href="#methods" aria-label="Permalink to &quot;Methods&quot;">​</a></h3><p>以下事件，只有在 <code>useVbenModal({onCancel:()=&gt;{}})</code> 中传入才会生效。</p><table tabindex="0"><thead><tr><th>事件名</th><th>描述</th><th>类型</th></tr></thead><tbody><tr><td>start</td><td>开始执行动画</td><td><code>()=&gt;void</code></td></tr><tr><td>reset</td><td>重置</td><td><code>()=&gt;void</code></td></tr></tbody></table>',5)),l(e),l(c)])}});export{c as __pageData,y as default};
