import{by as n}from"./bootstrap-DShsrVit.js";import{a4 as s,O as r,af as c,ag as i,ah as l,a3 as f,ap as p,ao as m}from"../jse/index-index-BMh_AyeW.js";import{u as d}from"./use-modal-B0smF4x0.js";const _={class:"flex-col-center"},v=s({__name:"shared-data-demo",setup(u){const a=r(),[t,e]=d({onCancel(){e.close()},onConfirm(){n.info("onConfirm")},onOpenChange(o){o&&(a.value=e.getData())}});return(o,h)=>(c(),i(f(t),{title:"数据共享示例"},{default:l(()=>[p("div",_,"外部传递数据： "+m(a.value),1)]),_:1}))}});export{v as _};
