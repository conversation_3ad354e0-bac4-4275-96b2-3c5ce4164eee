<script>
export default {
  props: {
    p: {
      type: String,
      default: '{}',
    },
  },
  data() {
    return {
      ps: [],
    };
  },
  mounted() {
    console.log(this.p);
    const prop = JSON.parse(this.p);
    const data = [];
    for (const p in prop) {
      data.push([p, prop[p]]);
    }
    console.log(this.p, prop, data);
    this.ps = data;
    // 属性筛选及别名
  },
};
</script>

<template>
  <div class="properties-box">
    <div v-for="(item, key) in ps" :key="key" class="property">
      <div class="field">{{ item[0] }}</div>
      <div class="value">{{ item[1] }}</div>
    </div>
  </div>
</template>

<style scoped lang="less">
.properties-box {
  display: flex;
  flex-direction: column;
  max-height: 300px;
  width: 100%;
  overflow-y: auto;
  .property {
    display: flex;
    flex-direction: row;
    align-items: start;
    .field {
      width: 40%;
      line-height: 30px;
      padding: 0 10px;
      color: #fff;
      word-break: break-all;
    }
    .value {
      flex-grow: 1;
      line-height: 30px;
      padding: 0 10px;
      color: #fff;
      word-break: break-all;
    }
    &:nth-child(even) {
      background-color: rgba(40, 44, 52, 0.5);
    }
  }
}
</style>
