import {Texture,Color, ShadowMap} from 'cesium'
import Shaders from './Shaders'
class VideoShed {
 	// 定义变量
 	/** 纹理类型：VIDEO、IMAGE、COLOR */
 	#textureType;
 	/** 纹理 */
 	#texture;
 	/** 观测点位置 */
 	#viewPosition;
 	/** 最远观测点位置（如果设置了观测距离，这个属性可以不设置） */
 	#viewPositionEnd;
	#alpha;
	#visibleAreaColor;
	#invisibleAreaColor;
	#activeVideoListener;
	#postProcessStage;
	#videoShedShader;
	#video;
	#shadowMap;
 	// ...

 	// 构造函数
 	constructor(viewer, options) {
 		super(viewer);

 		// 纹理类型
 		this.#textureType = options.textureType;
 		// 纹理地址（纹理为视频或图片的时候需要设置此项）
 		this.#textureUrl = options.textureUrl;
 		// 观测点位置
 		this.#viewPosition = options.viewPosition;
 		// 最远观测点位置（如果设置了观测距离，这个属性可以不设置）
 		this.#viewPositionEnd = options.viewPositionEnd;

 		this.#alpha= options.alpha || 1;
 		this.#visibleAreaColor = options.visibleAreaColor || Color.GREEN;
 		this.#invisibleAreaColor = options.invisibleAreaColor || Color.RED;
 		this.#videoShedShader = Shaders.getVideoShedShader({
			get:true
		});
		this.#shadowMap = new ShadowMap({
			
		});

 		switch (this.#textureType) {
 			default:
 			case VideoShed.TEXTURE_TYPE.VIDEO:
 				this.activeVideo();
 				break;
 			case VideoShed.TEXTURE_TYPE.IMAGE:
 				this.activePicture();
 				break;
 		}
 		this.#refresh()
 		this.viewer.scene.primitives.add(this);
 	}
	static TEXTURE_TYPE = {
		VIDEO:0,
		IMAGE:1,
		COLOR:2
	}
 	/**
 	 * 投放视频。
 	 *
 	 * <AUTHOR>
 	 * @date 2020/09/19
 	 * @param {String} textureUrl 视频地址。
 	 */
 	activeVideo(textureUrl = undefined) {
		this.deActiveVideo()
 		if (!textureUrl) {
 			textureUrl = this.#textureUrl;
 		} else {
 			this.#textureUrl = textureUrl;
 		}
 		const video = this.#createVideoElement(textureUrl);
 		const that = this;
 		if (video /*&& !video.paused*/ ) {
 			this.#activeVideoListener || (this.#activeVideoListener = function() {
 				that.#texture && that.#texture.destroy();
 				that.#texture = new Texture({
 					context: that.viewer.scene.context,
 					source: video,
 					1,
 					height: 1,
 					pixelFormat: PixelFormat.RGBA,
 					pixelDatatype: PixelDatatype.UNSIGNED_BYTE
 				});
 			});
 			that.viewer.clock.onTick.addEventListener(this.#activeVideoListener);
 		}
 	}
	#createVideoElement(url){
		//this.#video && document.body.removeChild(this.#video);
		this.#video = document.createElement('video',{
			autoplay:true,
			loop:true,
			crossorigin:true,
			controls:true,
			style:"display: none"
		})
		this.#video.innerHTML(`<source
		        src="${url}"
		        type="video/mp4"
		      />`)
		/* <video autoplay loop crossorigin controls style="display: none">
		      <source
		        src="https://cesium.com/public/SandcastleSampleData/big-buck-bunny_trailer.mp4"
		        type="video/mp4"
		      />
		    </video> */
		document.body.appendChild(this.#video);
		return this.#video;
	}
	/* addVideo() {
	      let videoElement = this.$refs.video;
	      this.viewer.showRenderLoopErrors = false;
	      this.viewer.shouldAnimate = true;
	      this.viewer.entities.add({
	        polygon: {
	          hierarchy: Cesium.Cartesian3.fromDegreesArrayHeights([
	            113.2321,
	            23.1172,
	            5,
	            113.2315,
	            23.1172,
	            5,
	            113.2315,
	            23.1178,
	            5,
	            113.2321,
	            23.1178,
	            5,
	          ]),
	          material: videoElement, // 将材质设置为video元素
	          clampToGround: true,
	        },
	      });
	    }, */
	deActiveVideo(){
		if(this.#video){
			document.body.removeChild(this.#video);
			this.#video=null;
		}
		if(this.#activeVideoListener){
			this.viewer.clock.onTick.removeEventListener(this.#activeVideoListener);
			this.#activeVideoListener = null;
		}
	}
 	/**
 	 * 投放图片。
 	 *
 	 * <AUTHOR>
 	 * @date 2020/09/19
 	 * @param {String} textureUrl 图片地址。
 	 */
 	activePicture(textureUrl = undefined) {
 		this.deActiveVideo();
 		if (!textureUrl) {
 			textureUrl = this.#textureUrl;
 		} else {
 			this.#textureUrl = textureUrl;
 		}
 		const that = this;
 		const img = new Image;
 		img.onload = function() {
 			that.#textureType = VideoShed.TEXTURE_TYPE.IMAGE;
 			that.#texture = new Texture({
 				context: that.viewer.scene.context,
 				source: img
 			});
 		};
 		img.onerror = function() {
 			console.log('图片加载失败:' + textureUrl)
 		};
 		img.src = textureUrl;
 	}
	#refresh(){
		if(this.#postProcessStage){
			this.viewer.scene.postProcessStages.remove(this.#postProcessStage)
		}
		this.#addPostProcessStage();
	}
 	/**
 	 * 创建后期程序。
 	 *
 	 * <AUTHOR>
 	 * @date 2020/09/19
 	 * @ignore
 	 */
 	#addPostProcessStage() {
 		const that = this;
 		const bias = that.#shadowMap._isPointLight ? that.#shadowMap._pointBias : that.#shadowMap._primitiveBias;
 		const postStage = new PostProcessStage({
 			fragmentShader: this.#videoShedShader,
 			uniforms: {
 				helsing_textureType: function() {
 					return that.#textureType;
 				},
 				helsing_texture: function() {
 					return that.#texture;
 				},
 				helsing_alpha: function() {
 					return that.#alpha;
 				},
 				helsing_visibleAreaColor: function() {
 					return that.#visibleAreaColor;
 				},
 				helsing_invisibleAreaColor: function() {
 					return that.#invisibleAreaColor;
 				},
 				shadowMap_texture: function() {
 					return that.#shadowMap._shadowMapTexture;
 				},
 				shadowMap_matrix: function() {
 					return that.#shadowMap._shadowMapMatrix;
 				},
 				shadowMap_lightPositionEC: function() {
 					return that.#shadowMap._lightPositionEC;
 				},
 				shadowMap_texelSizeDepthBiasAndNormalShadingSmooth: function() {
 					const t = new Cartesian2;
 					t.x = 1 / that.#shadowMap._textureSize.x;
 					t.y = 1 / that.#shadowMap._textureSize.y;
 					return Cartesian4.fromElements(t.x, t.y, bias.depthBias, bias.normalShadingSmooth,
 						that.#combinedUniforms1);
 				},
 				shadowMap_normalOffsetScaleDistanceMaxDistanceAndDarkness: function() {
 					return Cartesian4.fromElements(bias.normalOffsetScale, that.#shadowMap._distance,
 						that.#shadowMap.maximumDistance, that.#shadowMap._darkness, that
 						.#combinedUniforms2);
 				},
 			}
 		});
 		this.#postProcessStage = this.viewer.scene.postProcessStages.add(postStage);
 	}
 }

