import * as Cesium from 'cesium';
export default class Cesium3DTilesMaximumTotalMemoryUsageControl {
  private _scene: Cesium.Scene;
  private _current3DTilesTotalMemoryUsage: number;

  /**
   * Cesium3DTiles的最大使用显存（单位MB）
   *
   * @type {Number}
   *
   */
  public maximumTotalMemoryUsage: number;

  /**
   * 创建Cesium3DTiles显存管理器<br>
   *
   * <pre>
   * 该显存管理器可管理整个场景中，Cesium3DTiles使用的总显存数量。
   * 合理的使用该管理器可以有效的减少甚至避免 WebGL 由于爆显存而导致的报错。
   *
   * 值得注意的是：
   *    这里设置的最大使用显存并不是一个强制性的值，它仅仅起到一个阈值的作用。
   *    也就是当场景中 3DTiles 使用的显存超过该值时，才会触发缓存回收机制。
   *    而缓存回收后，使用的总显存并不一定会降到该值以下。
   *    比如，在当前相机视角下，3DTiles 需要 300MB 才能达到对应的显示精度，
   *    而最大使用显存设置到 200MB 时，最终使用的显存将以 Cesium 需要的显存为准，也就是 300MB。
   * <pre>
   *
   * @constructor
   * @alias Cesium3DTilesMaximumTotalMemoryUsageControl
   *
   * @param {Cesium.Scene} scene Cesium的Scene对象
   * @param {Number} maximumTotalMemoryUsage Cesium3DTiles的最大使用显存（单位MB）
   *
   */
  constructor(scene: Cesium.Scene, maximumTotalMemoryUsage: number) {
    this._scene = scene;
    this.maximumTotalMemoryUsage = maximumTotalMemoryUsage;
    this._current3DTilesTotalMemoryUsage = 0;

    // tileLoad时获取新的内存占用
    (this._scene.primitives as any)._primitives.forEach((primitive: any) => {
      if (primitive instanceof Cesium.Cesium3DTileset) {
        primitive.tileLoad.addEventListener(this._preRender);
      }
    });
  }

  /**
   * 当前场景中Cesium3DTiles使用的显存总数(单位MB)
   *
   * @type {Number}
   * @readonly
   */
  get current3DTilesTotalMemoryUsage() {
    return this._current3DTilesTotalMemoryUsage;
  }

  private _preRender = () => {
    // 计算当前使用的显存总数
    this._current3DTilesTotalMemoryUsage = 0;
    (this._scene.primitives as any)._primitives.forEach((primitive: any) => {
      if (primitive instanceof Cesium.Cesium3DTileset) {
        this._current3DTilesTotalMemoryUsage +=
          primitive.totalMemoryUsageInBytes / 1024 / 1024;
      }
    });

    // 如果显存总数超过设置的阈值
    if (this._current3DTilesTotalMemoryUsage > this.maximumTotalMemoryUsage) {
      // 手动释放上一帧未选择的所有瓦片
      (this._scene.primitives as any)._primitives.forEach((primitive: any) => {
        if (primitive instanceof Cesium.Cesium3DTileset) {
          primitive.trimLoadedTiles();
        }
      });
    }
  };

  /**
   * 销毁释放内存
   */
  destroy() {
    (this._scene.primitives as any)._primitives.forEach((primitive: any) => {
      if (primitive instanceof Cesium.Cesium3DTileset) {
        primitive.tileLoad.removeEventListener(this._preRender);
      }
    });
    Cesium.destroyObject(this);
  }
}

/*
https://www.liaomz.top/2022/12/05/kai-yuan-yi-ge-cesium3dtiles-de-xian-cun-guan-li-gong-ju/ 
// 创建显存管理工具
const memoryUsageControl = new Cesium3DTilesMaximumTotalMemoryUsageControl(scene, 1024);

// 更新最大显存
memoryUsageControl.maximumTotalMemoryUsage = 2048;

// 获取当前使用的总显存
console.log(memoryUsageControl.current3DTilesTotalMemoryUsage);

// 销毁显存管理工具
memoryUsageControl.destroy(); */
