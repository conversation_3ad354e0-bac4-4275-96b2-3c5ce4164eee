#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/sass@1.87.0/node_modules/sass/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/sass@1.87.0/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/sass@1.87.0/node_modules/sass/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/sass@1.87.0/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../sass/sass.js" "$@"
else
  exec node  "$basedir/../sass/sass.js" "$@"
fi
