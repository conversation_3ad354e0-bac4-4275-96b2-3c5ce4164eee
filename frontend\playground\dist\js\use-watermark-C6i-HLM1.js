var w=Object.defineProperty;var y=Object.getOwnPropertySymbols;var g=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var p=(e,t,a)=>t in e?w(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,u=(e,t)=>{for(var a in t||(t={}))g.call(t,a)&&p(e,a,t[a]);if(y)for(var a of y(t))k.call(t,a)&&p(e,a,t[a]);return e};var l=(e,t,a)=>new Promise((r,n)=>{var c=o=>{try{m(a.next(o))}catch(f){n(f)}},d=o=>{try{m(a.throw(o))}catch(f){n(f)}},m=o=>o.done?r(o.value):Promise.resolve(o.value).then(c,d);m((a=a.apply(e,t)).next())});import{O as v,a6 as _,q as h,$ as W}from"../jse/index-index-BMh_AyeW.js";const i=v(),s=v({advancedStyle:{colorStops:[{color:"gray",offset:0},{color:"gray",offset:1}],type:"linear"},content:"",contentType:"multi-line-text",globalAlpha:.25,gridLayoutOptions:{cols:2,gap:[20,20],matrix:[[1,0],[0,1]],rows:2},height:200,layout:"grid",rotate:30,width:160});function T(){function e(r){return l(this,null,function*(){var c;const{Watermark:n}=yield W(()=>l(this,null,function*(){const{Watermark:d}=yield import("./index.esm-C1Abm_da.js");return{Watermark:d}}),[]);s.value=u(u({},s.value),r),i.value=new n(s.value),yield(c=i.value)==null?void 0:c.create()})}function t(r){return l(this,null,function*(){var n;i.value?(yield h(),yield(n=i.value)==null?void 0:n.changeOptions(u(u({},s.value),r))):yield e(r)})}function a(){var r;(r=i.value)==null||r.destroy()}return _(()=>{a()}),{destroyWatermark:a,updateWatermark:t,watermark:i}}export{T as u};
