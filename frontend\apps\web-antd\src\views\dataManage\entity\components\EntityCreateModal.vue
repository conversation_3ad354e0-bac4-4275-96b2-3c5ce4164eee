<script lang="ts" setup>
import { computed, ref, unref, onUnmounted, onMounted, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';

import {
  dataTypeOptions,
  getFormSchemaByCategory,
  getUploadFormSchemaByCategory
} from '../entity.data';
import type { FileInfo } from "cspell/dist/esm/util/fileHelper";
import { save, edit, updateFileState, getEpsgCodes } from "#/views/dataManage/entity/entity.api";
import { message } from "ant-design-vue";
import { showLoading, showSuccess } from "#/utils/toast.js";
import { stopUpload, uploadFolder } from "#/utils/folderUpload.js";
import  {uploadFile} from '#/utils/fileUpload.js'
import { showConform } from "#/utils/alert";
import {RegionUtils} from "#/utils/regionUtils";
import { fileUploader } from '#/utils/upload';
import { getUploadUrls } from '#/utils/upload-urls';
import { isTaskUploading, addUploadingTask, removeUploadingTask } from '#/utils/upload-state';
import { uploadTaskManager } from '#/utils/uploadTaskManager';
import emitter from '#/utils/mitt';
// 声明Emits
const emit = defineEmits(['success']);
const props = defineProps(['category','row']);
let id = null;

const clearData = () => {
  currentStep.value = 1;
  startUpload.value = false;
  firstFormApi.setState({
    commonConfig: { disabled: false },
    submitButtonOptions: { disabled: false },
    resetButtonOptions: { disabled: false }
  });
  secondFormApi.setState({
    commonConfig: { disabled: false },
    submitButtonOptions: { disabled: false },
    resetButtonOptions: { disabled: false }
  });
}

const [Modal, modalApi] = useVbenModal({
  closeOnClickModal:false,
  onCancel() {
    modalApi.close();
  },
  onClosed() {
    clearData();
  },
  onConfirm() {},
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      firstFormApi.resetForm();
      const data = modalApi.getData<Record<string, any>>();
      const state = modalApi.useStore();
      console.log(data);
      isUpdate.value = state.value && state.value.update;
      isUpload.value = state.value && state.value.upload;
      category = state.value && state.value.category;
      dataType = state.value && state.value.row && state.value.row.dataType;

      let values = {}
      if (isUpdate.value) {
        values = {...state.value.row};
      }
      let dataTypes = dataTypeOptions[category];
      if(dataTypes && dataTypes.length === 1) {
        values.dataType = dataTypes[0].value;
      }
      id = values.id;
      values.update = isUpdate.value;
      values.upload = isUpload.value;
      firstFormApi.setValues(values);

        // 页面打开时获取坐标系数据
        fetchEpsgCodes();
    }
  },
});

const state = modalApi.useStore();

const formModel = ref({
  id: '', // 确保初始化
  datasetName: '',  // 其他字段
});
let isUpdate = ref(false);
let isUpload = ref(false);
let category = props.category;
let dataType = '';
const [FirstForm, firstFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'text-right pr-4',
    labelWidth: 100,
  },
  // 提交函数
  handleReset,
  handleSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: getFormSchemaByCategory(category),
  wrapperClass: 'grid-cols-1',
  submitButtonOptions:{
    content: '下一步',
    style: {
        backgroundColor: 'hsl(var(--primary))',
    },
  }

});

const [SecondForm, secondFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelWidth: 80,
  },
  // 提交函数
  handleSubmit:handleNext,
  handleReset,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: getUploadFormSchemaByCategory(category),
  wrapperClass: 'grid-cols-1',
  resetButtonOptions: { show: false },
  submitButtonOptions:{
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  }
});

let fileInput : any= null;
const fileList = ref([]);
const uploadType = ref('0');
const startUpload = ref(false);
const currentStep = ref(1); // 当前步骤
const epsgList = ref([]); // 添加epsgList
let firstResult = '';

const headers = {
    authorization: 'authorization-text',
  };

// 设置标题
const title = computed(() => (unref(isUpdate) ? '编辑' : '新增'));

async function handleReset(values) {
  let id = values.id;
  await firstFormApi.resetForm();
  await firstFormApi.setFieldValue("id",id);
  await firstFormApi.setFieldValue("dataType",dataType);
}

// 获取坐标系列表
const fetchEpsgCodes = async () => {
  try {
    const res = await getEpsgCodes({});
    if (res && res.records) {
      epsgList.value = res.records.map(item => ({
        label: `${item.name} (${item.code})`,
        value: item.code
      }));
    }
  } catch (error) {
    console.error('获取坐标系列表失败:', error);
  }
};


// 监听epsgList的变化，更新表单的options
watch(epsgList, (newValue) => {
  secondFormApi.updateSchema([
    {
      fieldName: 'epsgCode',
      componentProps: {
        options: newValue,
        placeholder: '请选择坐标系'
      }
    }
  ]);
});

async function handleNext(values) {
  if (startUpload.value) return;
  startUpload.value = true;

  try {
    // 检查任务是否正在上传
    if (isTaskUploading(firstResult)) {
      message.warning('该任务已经在上传中，请稍后再试');
      startUpload.value = false;
      return;
    }

    // 添加到正在上传的任务集合中
    addUploadingTask(firstResult);

    showLoading('操作处理中...');
    secondFormApi.setState({
      commonConfig: { disabled: true },
      submitButtonOptions: { disabled: true },
      resetButtonOptions: { disabled: true }
    });

    if(fileInput.files == null || fileInput.files.length == 0) {
      onSuccess(firstResult);
      return;
    }

    const urls = getUploadUrls(dataType, values.fileFormat);
    // 使用 formModel 获取数据集名称
    const taskName = formModel.value.datasetName;

    fileUploader.uploadFiles(
      fileInput.files,
      { urls, taskName },
      {
        onSuccess: async (uploadId) => {
          try {
            showLoading('文件上传成功，解析文件中...');
            await updateFileState({
              id: firstResult,
              fileId: uploadId,
              dataType: dataType,
              epsgCode: values.epsgCode
            });
            onSuccess(firstResult);
          } catch (e) {
            onError(firstResult);
          }
        },
        onError: () => {
          onError(firstResult);
        },
      }
    );
    modalApi.close();
  } catch (error) {
    onError(firstResult);
  }
}

// 表单提交事件
async function handleSubmit(values) {
  try {
    // 更新 formModel
    formModel.value = { ...values };

    values.category = category;
    dataType = values.dataType;
    firstFormApi.setState({
      commonConfig: { disabled: true },
      submitButtonOptions: { disabled: true },
      resetButtonOptions: { disabled: true }
    });
    showLoading('操作处理中...');
    try {
      let regionCode = values.regionCode;
      let regionProvinceCode = regionCode[0];
      let regionCityCode = regionCode[1];
      let regionCountyCode = regionCode[2];

      // 从表单中获取省市区名称
      let region = RegionUtils.getRegionNameByCode(regionCountyCode);
      let regionProvinceName = region.province;
      let regionCityName = region.city;
      let regionCountyName = region.area;

      // 将省市区名称添加到values中
      values.regionProvinceCode = regionProvinceCode;
      values.regionProvinceName = regionProvinceName;
      values.regionCityCode = regionCityCode;
      values.regionCityName = regionCityName;
      values.regionCountyCode = regionCountyCode;
      values.regionCountyName = regionCountyName;

      let result = isUpdate.value ? await edit(values,id) : await save(values);
      delete values.regionCode;
      showSuccess("创建数据集完成，请上传数据!");
      onFirstSuccess(result);
    } catch (e) {
      onFirstError();
    }
  } finally {
  }
}

const onSuccess = (taskId) =>{
  // 从正在上传的任务集合中移除
  if (taskId) {
    removeUploadingTask(taskId);
  }

  // 关闭弹窗
  startUpload.value = false;
  secondFormApi.setState({
    commonConfig: { disabled: false },
    submitButtonOptions: { disabled: false },
    resetButtonOptions: { disabled: false }
  });
  modalApi.close();
  showSuccess('操作成功！');
  emitter.emit('uploadSuccess');
}

const onError = (taskId) =>{
  // 从正在上传的任务集合中移除
  if (taskId) {
    removeUploadingTask(taskId);
  }

  // 关闭弹窗
  startUpload.value = false;
  secondFormApi.setState({
    commonConfig: { disabled: false },
    submitButtonOptions: { disabled: false },
    resetButtonOptions: { disabled: false }
  });
  modalApi.close();
  emitter.emit('uploadSuccess');
}

const onFirstError = () =>{
  // 关闭弹窗
  firstFormApi.setState({
    commonConfig: { disabled: false },
    submitButtonOptions: { disabled: false },
    resetButtonOptions: { disabled: false }
  });
}

const onFirstSuccess = async (result) => {
  firstResult = result;
  currentStep.value = 2;

  firstFormApi.setState({
    commonConfig: { disabled: false },
    submitButtonOptions: { disabled: false },
    resetButtonOptions: { disabled: false }
  });
  emitter.emit('uploadSuccess');
  await secondFormApi.setFieldValue("dataType",dataType);
}

const handleChange = (info: FileInfo) => {
  if (info.file.status !== 'uploading') {
    console.log(info.file, info.fileList);
  }
  if (info.file.status === 'done') {
  } else if (info.file.status === 'error') {
  }
};

const handleRemove = (file) => {
  const index = fileList.value.indexOf(file);
  const newFileList = fileList.value.slice();
  newFileList.splice(index, 1);
  fileList.value = newFileList;
};

const beforeUpload = (file) => {
  fileList.value = [...fileList.value, file];
  return false;
};

const handleUploadTypeChange = (data) =>{
  uploadType.value = data.target.value;
}

const handleFolderSelect = (data) =>{
  console.log(data);
  fileInput = data.target;
  // uploadFolder(fileInput);
  const fileName = document.getElementById('fileName');
  fileName.textContent = fileInput.files.length > 0 ? '已选'+fileInput.files.length+'个文件' : '没有选择文件';

  secondFormApi.setFieldValue("fileUpload",fileInput.files);
}


const triggerFileInput = () => {
  if(startUpload.value) {
    return;
  }
  document.getElementById('fileInput').click();
}

// 添加组件卸载时的清理函数
onUnmounted(() => {
  // 可以选择在组件卸载时清除已完成的任务
  uploadTaskManager.clearCompletedTasks();
});

</script>
<template>
  <Modal
    v-bind="$attrs"
    :footer="false"
    :title="title"
    class="w-[800px]"
    :destroy-on-close="true"
    :maskClosable="false"
  >
    <div style="padding-left: 20px;padding-right: 20px;padding-top: 20px">
      <FirstForm
        v-if="currentStep===1">
      </FirstForm>
      <SecondForm v-if="currentStep===2">
        <!--      <template #uploadType="slotProps">-->
        <!--        <a-radio-group v-show="!isUpdate" v-model:value="uploadType" @change="handleUploadTypeChange":disabled="true">-->
        <!--          <a-radio value="0">目录</a-radio>-->
        <!--          <a-radio value="1">文件</a-radio>-->
        <!--        </a-radio-group>-->
        <!--      </template>-->
        <template #fileUpload="slotProps">
          <input
            class="hidden"
            v-show="!isUpdate"  id="fileInput" type="file"  :directory="uploadType ==='0'"  :webkitdirectory="uploadType ==='0'" @change="handleFolderSelect"  />

          <!-- 自定义按钮 -->
          <label for="fileInput" v-if="startUpload" onclick="triggerFileInput"  class="custom-label bg-gray-200 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400">
            选择文件
          </label>

          <label for="fileInput" v-else   onclick="triggerFileInput"  class="custom-label  bg-primary-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400">
            选择文件
          </label>

          <!-- 显示选择的文件名称 -->
          <span id="fileName" class="ml-4">没有选择文件</span>
        </template>
      </SecondForm>
      <view v-if="startUpload" style="width:900px">
        <div id="folderProgressArea" class="folderProgressArea">
          文件夹总进度：<div id="folderProgress" class="folderProgress ">0%</div>
        </div>
        <div style="display: flex;flex-direction:column;justify-content: flex-start;align-items: flex-start">
          <div style="margin-bottom: 5px">当前文件进度：</div>
          <div id="fileProgress" class="fileProgress bg-primary text-white "></div>
        </div>
      </view>
    </div>


  </Modal>
</template>
<style scoped lang="less">
@import '#/styles/dark-antd.less';

.fileProgress {
  //padding: 5px;
  width:0px;
}
.folderProgressArea {
  width:100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-bottom: 5px;
  visibility:hidden;
}
</style>
