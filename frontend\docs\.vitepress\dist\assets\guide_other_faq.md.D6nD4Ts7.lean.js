import{ao as s,k as i,z as a,I as t,aP as n,l as e,ay as h,j as l}from"./chunks/framework.C8U7mBUf.js";const p=JSON.parse('{"title":"常见问题 #","description":"","frontmatter":{},"headers":[],"relativePath":"guide/other/faq.md","filePath":"guide/other/faq.md"}');const k=s({name:"guide/other/faq.md"},[["render",function(s,p,k,d,r,o){const g=h("NolebaseGitContributors"),E=h("NolebaseGitChangelog");return l(),i("div",null,[p[0]||(p[0]=a("h1",{faq:"",id:"常见问题",tabindex:"-1"},[t("常见问题 # "),a("a",{class:"header-anchor",href:"#常见问题","aria-label":'Permalink to "常见问题 #{faq}"'},"​")],-1)),p[1]||(p[1]=n('<div class="tip custom-block"><p class="custom-block-title">列举了一些常见的问题</p><p>有问题可以先来这里寻找，如果没有可以在 <a href="https://github.com/vbenjs/vue-vben-admin/issues" target="_blank" rel="noreferrer">GitHub Issue</a> 搜索或者提交你的问题, 如果是讨论性的问题可以在 <a href="https://github.com/vbenjs/vue-vben-admin/discussions" target="_blank" rel="noreferrer">GitHub Discussions</a></p></div><h2 id="说明" tabindex="-1">说明 <a class="header-anchor" href="#说明" aria-label="Permalink to &quot;说明&quot;">​</a></h2><p>遇到问题,可以先从以下几个方面查找</p><ol><li>对应模块的 GitHub 仓库 <a href="https://github.com/vbenjs/vue-vben-admin/issues" target="_blank" rel="noreferrer">issue</a> 搜索</li><li>从<a href="https://www.google.com" target="_blank" rel="noreferrer">google</a>搜索问题</li><li>从<a href="https://www.baidu.com" target="_blank" rel="noreferrer">百度</a>搜索问题</li><li>在下面列表找不到问题可以到 issue 提问 <a href="https://github.com/vbenjs/vue-vben-admin/issues" target="_blank" rel="noreferrer">issues</a></li><li>如果不是问题类型的，需要讨论的，请到 <a href="https://github.com/vbenjs/vue-vben-admin/discussions" target="_blank" rel="noreferrer">discussions</a> 讨论</li></ol><h2 id="依赖问题" tabindex="-1">依赖问题 <a class="header-anchor" href="#依赖问题" aria-label="Permalink to &quot;依赖问题&quot;">​</a></h2><p>在 <code>Monorepo</code> 项目下，需要养成每次 <code>git pull</code>代码都要执行<code>pnpm install</code>的习惯，因为经常会有新的依赖包加入，项目在<code>.husky/git-merge</code>已经配置了自动执行<code>pnpm install</code>，但是有时候会出现问题，如果没有自动执行，建议手动执行一次。</p><h2 id="关于缓存更新问题" tabindex="-1">关于缓存更新问题 <a class="header-anchor" href="#关于缓存更新问题" aria-label="Permalink to &quot;关于缓存更新问题&quot;">​</a></h2><p>项目配置默认是缓存在 <code>localStorage</code> 内，所以版本更新后可能有些配置没改变。</p><p>解决方式是每次更新代码的时候修改 <code>package.json</code> 内的 <code>version</code> 版本号. 因为 localStorage 的 key 是根据版本号来的。所以更新后版本不同前面的配置会失效。重新登录即可</p><h2 id="关于修改配置文件的问题" tabindex="-1">关于修改配置文件的问题 <a class="header-anchor" href="#关于修改配置文件的问题" aria-label="Permalink to &quot;关于修改配置文件的问题&quot;">​</a></h2><p>当修改 <code>.env</code> 等环境文件以及 <code>vite.config.ts</code> 文件时，vite 会自动重启服务。</p><p>自动重启有几率出现问题，请重新运行项目即可解决.</p><h2 id="本地运行报错" tabindex="-1">本地运行报错 <a class="header-anchor" href="#本地运行报错" aria-label="Permalink to &quot;本地运行报错&quot;">​</a></h2><p>由于 vite 在本地没有转换代码，且代码中用到了可选链等比较新的语法。所以本地开发需要使用版本较高的浏览器(<code>Chrome 90+</code>)进行开发</p><h2 id="页面切换后页面空白" tabindex="-1">页面切换后页面空白 <a class="header-anchor" href="#页面切换后页面空白" aria-label="Permalink to &quot;页面切换后页面空白&quot;">​</a></h2><p>这是由于开启了路由切换动画,且对应的页面组件存在多个根节点导致的，在页面最外层添加<code>&lt;div&gt;&lt;/div&gt;</code>即可</p><p><strong>错误示例</strong></p><div class="language-vue vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">vue</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  &lt;!-- 注释也算一个节点 --&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;text h1&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;text h2&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><p><strong>正确示例</strong></p><div class="language-vue vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">vue</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">div</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;text h1&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;text h2&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">div</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><div class="tip custom-block"><p class="custom-block-title">提示</p><ul><li>如果想使用多个根标签，可以禁用路由切换动画</li><li>template 下面的根注释节点也算一个节点</li></ul></div><h2 id="我的代码本地开发可以-打包就不行了" tabindex="-1">我的代码本地开发可以，打包就不行了 <a class="header-anchor" href="#我的代码本地开发可以-打包就不行了" aria-label="Permalink to &quot;我的代码本地开发可以，打包就不行了&quot;">​</a></h2><p>目前发现这个原因可能有以下，可以从以下原因来排查，如果还有别的可能，欢迎补充</p><ul><li>使用了 ctx 这个变量，ctx 本身未暴露出在实例类型内，Vue官方也是说了不要用这个属性。这个属性只是用于内部使用。</li></ul><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { getCurrentInstance } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;vue&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">getCurrentInstance</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">().ctx.xxxx;</span></span></code></pre></div><h2 id="依赖安装问题" tabindex="-1">依赖安装问题 <a class="header-anchor" href="#依赖安装问题" aria-label="Permalink to &quot;依赖安装问题&quot;">​</a></h2><ul><li>如果依赖安装不了或者启动报错可以尝试执行<code>pnpm run reinstall</code>。</li><li>如果依赖安装不了或者报错，可以尝试切换手机热点来进行依赖安装。</li><li>如果还是不行，可以自行配置国内镜像安装。</li><li>也可以在项目根目录创建 <code>.npmrc</code> 文件，内容如下</li></ul><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># .npmrc</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">registry</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> =</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> https://registry.npmmirror.com/</span></span></code></pre></div><h2 id="打包文件过大" tabindex="-1">打包文件过大 <a class="header-anchor" href="#打包文件过大" aria-label="Permalink to &quot;打包文件过大&quot;">​</a></h2><ul><li><p>首先，完整版由于引用了比较多的库文件，所以打包会比较大。可以使用精简版来进行开发</p></li><li><p>其次建议开启 gzip，使用之后体积会只有原先 1/3 左右。gzip 可以由服务器直接开启。如果是这样，前端不需要构建 <code>.gz</code> 格式的文件，如果前端构建了 <code>.gz</code> 文件，以 nginx 为例，nginx 需要开启 <code>gzip_static: on</code> 这个选项。</p></li><li><p>开启 gzip 的同时还可以同时开启 <code>brotli</code>，比 gzip 更好的压缩。两者可以共存</p></li></ul><p><strong>注意</strong></p><ul><li><p>gzip_static: 这个模块需要 nginx 另外安装，默认的 nginx 没有安装这个模块。</p></li><li><p>开启 <code>brotli</code> 也需要 nginx 另外安装模块</p></li></ul><h2 id="运行错误" tabindex="-1">运行错误 <a class="header-anchor" href="#运行错误" aria-label="Permalink to &quot;运行错误&quot;">​</a></h2><p>如果出现类似以下错误，请检查项目全路径（包含所有父级路径）不能出现中文、日文、韩文。否则将会出现路径访问 404 导致以下问题</p><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">[vite] Failed to resolve </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">module</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> import</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;ant-design-vue/dist/antd.css-vben-adminode_modulesant-design-vuedistantd.css&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">. (</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">imported</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> by</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /@/</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">setup</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">/</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">ant</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">-</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">design</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">-</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">vue</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">/</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">info</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">ts</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span></span></code></pre></div><h2 id="控制台路由警告问题" tabindex="-1">控制台路由警告问题 <a class="header-anchor" href="#控制台路由警告问题" aria-label="Permalink to &quot;控制台路由警告问题&quot;">​</a></h2><p>如果看到控制台有如下警告，且页面<strong>能正常打开</strong> 可以忽略该警告。</p><p>后续 <code>vue-router</code> 可能会提供配置项来关闭警告</p><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">[Vue Router warn]: No match found for location </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">with</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> path </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;xxxx&quot;</span></span></code></pre></div><h2 id="启动报错" tabindex="-1">启动报错 <a class="header-anchor" href="#启动报错" aria-label="Permalink to &quot;启动报错&quot;">​</a></h2><p>当出现以下错误信息时，请检查你的 nodejs 版本号是否符合要求</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">TypeError:</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> str.matchAll</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> is</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> not</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> a</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> function</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">at</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Object.extractor</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> (vue-vben-admin-main</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\n</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ode_modules@purge-icons</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\c</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ore</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\d</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ist</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\i</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ndex.js:146:27)</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">at</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Extract</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> (vue-vben-admin-main</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\n</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ode_modules@purge-icons</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\c</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ore</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\d</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ist</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\i</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ndex.js:173:54)</span></span></code></pre></div><h2 id="nginx-部署" tabindex="-1">nginx 部署 <a class="header-anchor" href="#nginx-部署" aria-label="Permalink to &quot;nginx 部署&quot;">​</a></h2><p>部署到 <code>nginx</code>后，可能会出现以下错误：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">Failed</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> to</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> load</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> module</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> script:</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Expected</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> a</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> JavaScript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> module</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> script</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> but</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> the</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> server</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> responded</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> with</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> a</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> MIME</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> type</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> of</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;application/octet-stream&quot;.</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Strict</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> MIME</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> type</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> checking</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> is</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> enforced</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> for</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> module</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> scripts</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> per</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> HTML</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> spec.</span></span></code></pre></div><p>解决方式一：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">http</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    #如果有此项配置需要注释掉</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    #include       mime.types;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    types</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">      application/javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> js</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> mjs</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p>解决方式二：</p><p>进入 <code>nginx</code> 下的<code>mime.types</code>文件, 将<code>application/javascript js;</code> 修改为 <code>application/javascript js mjs;</code></p>',49)),e(g),e(E)])}]]);export{p as __pageData,k as default};
