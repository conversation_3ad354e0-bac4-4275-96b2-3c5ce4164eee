import axios from 'axios';
import { capp } from '../../app.js';
import Box from '../../../components/js/box.js';

// let loadingInstance = {
//     open() {
//         store.dispatch('app/setRequestLoading', true);
//     },
//     close() {
//         store.dispatch('app/setRequestLoading', false);
//     }
// }

/**
 * @description 处理code异常
 * @param {*} code
 * @param {*} msg
 */
const handleCode = (code, msg) => {
  switch (code) {
    case 400:
      capp.message(msg);
      break;
    case 401:
      capp.message('登录失效,请重新登录').then(() => {
        capp.router.replace('/login');
      });
      break;
    // case 403:
    //   break
    case 500:
      capp.message(msg || '服务端错误');
      break;
    default:
      capp.message(msg || `后端接口${code}异常`);
      break;
  }
};

/**
 * @description axios初始化
 */
const instance = axios.create({
  timeout: 60000,
  headers: {
    //'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/json;charset=ANSI',
  },
});

/**
 * @description axios请求拦截器
 */
instance.interceptors.request.use(
  (config) => {
    //if (
    //config.data &&
    //config.headers['Content-Type'] ===
    //'application/x-www-form-urlencoded;charset=UTF-8'
    // )
    //     config.data = qs.stringify(config.data)
    // if (debounce.some((item) => config.url.includes(item))) {
    //     //这里写加载动画
    // }
    // if (loadingInstance) loadingInstance.open()
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

let ErrorCodeEnum = {
  // 40001: '用户名或者密码错误!',
  // 40002: '该用户不唯一,请联系管理员!',
  // 40003: '该账号已被禁用,请联系管理员！',
  40004: '无效的令牌，请重新登陆！',
  40005: '登陆过期，请重新登陆！',
  40006: '登陆过期，请重新登陆！', //令牌获取失败
  40007: '用户未登陆，请前往登陆！',
  40008: '登陆过期，请重新登陆！', //令牌检查错误
  40009: '用户密码已更新，请重新登录',
};

/**
 * @description axios响应拦截器
 */
instance.interceptors.response.use(
  (response) => {
    // if (loadingInstance) loadingInstance.close()
    console.log(response);
    const { data, config } = response;
    // 是否操作正常
    if (!data.error) {
      return data;
    } else if (data.error) {
      capp.message(data.error.message);
    }
    return Promise.reject('请求异常');
  },
  (error) => {
    console.log(error);
    // if (loadingInstance) loadingInstance.close()
    let { response, message: msg = '' } = error;
    if (response && response.data) {
      const { status, data } = response;
      //handleCode(status, data.msg || msg)
      return Promise.reject(error);
    } else {
      if (msg === 'Network Error') {
        msg = '后端接口连接异常';
      } else if (msg.includes('timeout')) {
        msg = '后端接口请求超时';
      } else if (msg.includes('Request failed with status code')) {
        const code = msg.substr(msg.length - 3);
        msg = '后端接口' + code + '异常';
      }
      capp.message(msg || `后端接口未知异常`);
      return Promise.reject(error);
    }
  },
);

export default instance;
