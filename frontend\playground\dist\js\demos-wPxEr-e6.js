const e="Demos",n={frontendPermissions:"Frontend Permissions",backendPermissions:"Backend Permissions",pageAccess:"Page Access",buttonControl:"Button Control",menuVisible403:"Menu Visible(403)",superVisible:"Visible to Super",adminVisible:"Visible to Admin",userVisible:"Visible to User"},t={title:"Nested Menu",menu1:"Menu 1",menu2:"Menu 2",menu2_1:"Menu 2-1",menu3:"Menu 3",menu3_1:"Menu 3-1",menu3_2:"Menu 3-2",menu3_2_1:"Menu 3-2-1"},i={title:"External Pages",embedded:"Embedded",externalLink:"External Link"},a={title:"Menu Badge",dot:"Dot Badge",text:"Text Badge",color:"Badge Color"},l={title:"Active Menu Icon",children:"Children Active Icon"},o={title:"Fallback Page"},s={title:"Features",hideChildrenInMenu:"Hide Menu Children",loginExpired:"Login Expired",icons:"Icons",watermark:"Watermark",tabs:"Tabs",tabDetail:"Tab Detail Page",fullScreen:"FullScreen",clipboard:"Clipboard",menuWithQuery:"Menu With Query",openInNewWindow:"Open in New Window",fileDownload:"File Download"},r={navigation:"Breadcrumb Navigation",lateral:"Lateral Mode",lateralDetail:"Lateral Mode Detail",level:"Level Mode",levelDetail:"Level Mode Detail"},d={title:"Project",about:"About",document:"Document",antdv:"Ant Design Vue Version","naive-ui":"Naive UI Version","element-plus":"Element Plus Version"},u={title:e,access:n,nested:t,outside:i,badge:a,activeIcon:l,fallback:o,features:s,breadcrumb:r,vben:d};export{n as access,l as activeIcon,a as badge,r as breadcrumb,u as default,o as fallback,s as features,t as nested,i as outside,e as title,d as vben};
