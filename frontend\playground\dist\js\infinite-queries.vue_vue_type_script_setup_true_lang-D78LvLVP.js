var B=Object.defineProperty,O=Object.defineProperties;var Q=Object.getOwnPropertyDescriptors;var b=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var k=(e,t,s)=>t in e?B(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,m=(e,t)=>{for(var s in t||(t={}))C.call(t,s)&&k(e,s,t[s]);if(b)for(var s of b(t))I.call(t,s)&&k(e,s,t[s]);return e},_=(e,t)=>O(e,Q(t));var v=(e,t,s)=>new Promise((f,h)=>{var P=r=>{try{u(s.next(r))}catch(c){h(c)}},l=r=>{try{u(s.throw(r))}catch(c){h(c)}},u=r=>r.done?f(r.value):Promise.resolve(r.value).then(P,l);u((s=s.apply(e,t)).next())});import{Q as q,u as j}from"./useBaseQuery-6AyvSVSW.js";import{ba as N,bb as D,bc as L,B as V}from"./bootstrap-DShsrVit.js";import{a4 as $,af as i,am as n,a3 as a,ao as F,al as R,F as w,as as E,n as K,ah as S}from"../jse/index-index-BMh_AyeW.js";var T=class extends q{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions(_(m({},e),{behavior:N()}),t)}getOptimisticResult(e){return e.behavior=N(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch(_(m({},e),{meta:{fetchMore:{direction:"forward"}}}))}fetchPreviousPage(e){return this.fetch(_(m({},e),{meta:{fetchMore:{direction:"backward"}}}))}createResult(e,t){var p,x;const{state:s}=e,f=super.createResult(e,t),{isFetching:h,isRefetching:P,isError:l,isRefetchError:u}=f,r=(x=(p=s.fetchMeta)==null?void 0:p.fetchMore)==null?void 0:x.direction,c=l&&r==="forward",o=h&&r==="forward",d=l&&r==="backward",g=h&&r==="backward";return _(m({},f),{fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:D(t,s.data),hasPreviousPage:L(t,s.data),isFetchNextPageError:c,isFetchingNextPage:o,isFetchPreviousPageError:d,isFetchingPreviousPage:g,isRefetchError:u&&!c&&!d,isRefetching:P&&!o&&!g})}};function z(e,t){return j(T,e)}const A={key:0},G={key:1},H={key:2},J={key:0},U={key:0},W={key:1},X={key:2},M=10,se=$({__name:"infinite-queries",setup(e){const t=d=>v(this,[d],function*({pageParam:o=0}){return(yield fetch(`https://dummyjson.com/products?limit=${M}&skip=${o*M}`)).json()}),{data:s,error:f,fetchNextPage:h,hasNextPage:P,isError:l,isFetching:u,isFetchingNextPage:r,isPending:c}=z({getNextPageParam:(o,d)=>{const g=d.length+1;if(o.skip+o.limit!==o.total)return g},initialPageParam:0,queryFn:t,queryKey:["products"]});return(o,d)=>(i(),n("div",null,[a(c)?(i(),n("span",A,"加载...")):a(l)?(i(),n("span",G,"出错了: "+F(a(f)),1)):a(s)?(i(),n("div",H,[a(u)&&!a(r)?(i(),n("span",J,"Fetching...")):R("",!0),(i(!0),n(w,null,E(a(s).pages,(g,y)=>(i(),n("ul",{key:y},[(i(!0),n(w,null,E(g.products,p=>(i(),n("li",{key:p.id},F(p.title),1))),128))]))),128)),K(a(V),{disabled:!a(P)||a(r),onClick:d[0]||(d[0]=()=>a(h)())},{default:S(()=>[a(r)?(i(),n("span",U,"加载中...")):a(P)?(i(),n("span",W,"加载更多")):(i(),n("span",X,"没有更多了"))]),_:1},8,["disabled"])])):R("",!0)]))}});export{se as _};
