import inputEvent from './common/InputEvent'
import './common/Date.js'
import './common/String.js'
export {inputEvent};


export function HSLToHex(h, s, l) {
  s /= 100;
  l /= 100;

  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs(((h / 60) % 2) - 1));
  const m = l - c / 2;

  let r = 0, g = 0, b = 0;

  if (h >= 0 && h < 60) {
    r = c; g = x; b = 0;
  } else if (h >= 60 && h < 120) {
    r = x; g = c; b = 0;
  } else if (h >= 120 && h < 180) {
    r = 0; g = c; b = x;
  } else if (h >= 180 && h < 240) {
    r = 0; g = x; b = c;
  } else if (h >= 240 && h < 300) {
    r = x; g = 0; b = c;
  } else {
    r = c; g = 0; b = x;
  }

  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);

  // Convert RGB to Hex
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
}

export function stringToHSL(str) {
  // 解析字符串 "212 100% 45%" 为 HSL 参数
  const hsl = str.split(' ');  // 按空格拆分
  const h1 = parseInt(hsl[0]); // 色相
  const s1 = parseInt(hsl[1]); // 饱和度
  const l1 = parseInt(hsl[2]); // 亮度

  return { h1, s1, l1 };
}


/**
 * 保存到本地文件
 * @param {Blob | String} blob
 * @param {String?} filename
 * @param {String?} type
 */
export function saveAs(blob, filename, type = '') {
	if (blob === null) {
		//console.log(blob)
		return;
	}
	let fileTypes = {
		'.md': 'text/markdown',
		'.json': 'application/json',
		'.txt': 'text/plain;charset=utf-8'
	}
	if (!(blob instanceof Blob)) {
		blob = new Blob([blob], {
			type: fileTypes[type || '.txt'] || fileTypes['.txt']
		})
	}
	//console.log(blob)
	if (window.navigator.msSaveOrOpenBlob) {
		navigator.msSaveBlob(blob, filename);
	} else {
		const target = document.createElement("a");
		const body = document.querySelector("body");
		target.href = window.URL.createObjectURL(blob);
		target.download = filename;
		target.style.display = "none";
		body.appendChild(target);
		target.click();
		body.removeChild(target);
		window.URL.revokeObjectURL(target.href);
	}
}
export function saveAsByUrl(url, filename) {
	const elink = document.createElement('a');
	elink.download = filename;
	elink.style.display = 'none';
	elink.href = url;
	document.body.appendChild(elink);
	elink.click();
	window.URL.revokeObjectURL(elink.href); // 释放URL 对象
	document.body.removeChild(elink)
}
/**
 * 对象转化为formdata
 * @param {Object} object
 */
export function getFormData(object) {
	const formData = new FormData();
	Object.keys(object).forEach((key) => {
	const value = object[key];
	if (Array.isArray(value)) {
		value.forEach((subValue, i) => formData.append(key + `[${i}]`, subValue));
	} else {
		formData.append(key, object[key]);
	}
	});
	return formData;
}
// Converts image to canvas; returns new canvas element
function convertImageToCanvas(image) {
	var canvas = document.createElement("canvas");
	canvas.width = image.width;
	canvas.height = image.height;
	canvas.getContext("2d").drawImage(image, 0, 0);

	return canvas;
}
// Converts canvas to an image
/**
 * 转换画布为图片
 * @param {Canvas} canvas
 * @param {String} type 图片类型
 * @returns {Image}
 */
function convertCanvasToImage(canvas, type = 'image/png') {
	var image = new Image();
	image.src = canvas.toDataURL(type);
	return image;
}
/**
 * 截取画布的一部分
 * @param {Canvas} canvas
 * @param {Object} param1
 * @returns {Promise}
 */
export function clipCanvas(canvas, {
	x,
	y,
	width,
	height
}) {
	return new Promise(function(resolve, reject) {
		canvas.toBlob((blob) => {
			var a = new FileReader();
			a.onload = function(e) {
				var img = new Image();
				img.onload = function() {
					var canvas1 = document.createElement("canvas");
					canvas1.width = width;
					canvas1.height = height;
					canvas1.getContext("2d").drawImage(img, -x, -y);
					resolve(canvas1)
				}
				img.src = e.target.result;
			}
			a.readAsDataURL(blob);
		})
	});
}
/**
 * 下载Base64到本地
 1. trident内核都不可以（IE，猎豹）
 2. chrome, opera 等使用blink内核 可以（但是chrome好像还是webkit内核）
 3. firefox（Gecko） 可以
 4. 三星可以（多余后缀）
 5. 小米自带的手机浏览器可以（但filename乱码）
 6. qq， uc（PC都可以）
 * */
function downloadFile(content, fileName) { //下载base64图片
	const base64ToBlob = function(code) {
		let parts = code.split(';base64,');
		let contentType = parts[0].split(':')[1];
		let raw = window.atob(parts[1]);
		let rawLength = raw.length;
		let uInt8Array = new Uint8Array(rawLength);
		for (let i = 0; i < rawLength; ++i) {
			uInt8Array[i] = raw.charCodeAt(i);
		}
		return new Blob([uInt8Array], {
			type: contentType
		});
	};
	let aLink = document.createElement('a');
	let blob = base64ToBlob(content); //new Blob([content]);
	let evt = document.createEvent("HTMLEvents");
	evt.initEvent("click", true, true); //initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
	aLink.download = fileName;
	aLink.href = URL.createObjectURL(blob);
	// aLink.click();
	aLink.dispatchEvent(new MouseEvent("click", {
		bubbles: true,
		cancelable: true,
		view: window
	})); //
}
/**
 * 复制进剪贴板
 * @param str
 */
export function copyText(message) {
	let input = document.createElement("textarea");
	input.value = message;
	document.body.appendChild(input);
	input.select();
	input.setSelectionRange(0, input.value.length);
	document.execCommand('Copy');
	document.body.removeChild(input);
	console.log("复制成功") //"复制成功"
}
// Check Functions
export function checkHex(hex) {
  const hexRegex = /^[#]*([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/i
  if (hexRegex.test(hex)) {
    return true;
  }
}

export function checkRgb(rgb) {
  const rgbRegex = /([R][G][B][A]?[(]\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\s*,\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\s*,\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])(\s*,\s*((0\.[0-9]{1})|(1\.0)|(1)))?[)])/i
  if (rgbRegex.test(rgb)) {
    return true
  }
}
// Parse Function
export function modifyHex(hex) {
  if (hex.length == 4) {
    hex = hex.replace('#', '');
  }
  if (hex.length == 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }
  return hex;
}

// Converting Functions
export function hexToRgb(hex) {
  let x = [];
  hex = hex.replace('#', '')
  if (hex.length != 6) {
    hex = modifyHex(hex)
  }
  x.push(parseInt(hex.slice(0, 2), 16))
  x.push(parseInt(hex.slice(2, 4), 16))
  x.push(parseInt(hex.slice(4, 6), 16))
  return "rgb(" + x.toString() + ")"
}

export function rgbToHex(rgb) {
  let y = rgb.match(/\d+/g).map(function(x) {
    return parseInt(x).toString(16).padStart(2, '0')
  });
  return y.join('').toUpperCase()
}

/**
 * 颜色混合
 * @param {*} c1 颜色1
 * @param {*} c2 颜色2
 * @param {*} ratio 比例
 * @example colourBlend('#ff0000', '#3333ff', 0.5) // "#991a80"
 */
function colourBlend(c1, c2, ratio) {
	ratio = Math.max(Math.min(Number(ratio), 1), 0)
	let r1 = parseInt(c1.substring(1, 3), 16)
	let g1 = parseInt(c1.substring(3, 5), 16)
	let b1 = parseInt(c1.substring(5, 7), 16)
	let r2 = parseInt(c2.substring(1, 3), 16)
	let g2 = parseInt(c2.substring(3, 5), 16)
	let b2 = parseInt(c2.substring(5, 7), 16)
	let r = Math.round(r1 * (1 - ratio) + r2 * ratio)
	let g = Math.round(g1 * (1 - ratio) + g2 * ratio)
	let b = Math.round(b1 * (1 - ratio) + b2 * ratio)
	r = ('0' + (r || 0).toString(16)).slice(-2)
	g = ('0' + (g || 0).toString(16)).slice(-2)
	b = ('0' + (b || 0).toString(16)).slice(-2)
	return '#' + r + g + b
}
/**
 * 画布上拾取颜色
 * @param {Object} canvas
 * @param {Number} x
 * @param {Number} y
 */
function pickColor(canvas, x, y) {
	var ctx = canvas.getContext('2d');
	var pixel = ctx.getImageData(x, y, 1, 1);
	var data = pixel.data;
	// 获取rgba值
	//var rgba = 'rgba(' + data[0] + ',' + data[1] + ',' + data[2] + ',' + (data[3] / 255) + ')';
	return {
		red: data[0],
		green: data[1],
		blue: data[2],
		alpha: data[3] / 255
	}
}

function color2rgba(color) {
	if (!color) return '';
	return 'rgba(' + color.red + ',' + color.green + ',' + color.blue + ',' + color.alpha + ')';
}
/**
 * 计算反色,
 * @param {*} a 色值
 * @param {*} ilighten 减弱对比度(-1 ~ -15)
 * @returns
 * 示例: oppositeColor("#000000", -4); 返回: #bbbbbb
 */
export function oppositeColor(a, ilighten) {
	a = a.replace('#', '');
	//var max16 = 15;
	var max16 = Math.floor(15 + (ilighten || 0));
	if (max16 < 0 || max16 > 15) max16 = 15;

	var c16,
		c10,
		b = [];

	for (var i = 0; i < a.length; i++) {
		c16 = parseInt(a.charAt(i), 16); // to 16进制
		c10 = parseInt(max16 - c16, 10); // 10进制计算
		if (c10 < 0) c10 = Math.abs(c10);
		b.push(c10.toString(16)); // to 16进制
	}
	return '#' + b.join('');
}
/**
 * 合并对象
 * @param {Object} obj1
 * @param {Object} obj2
 * @summary 从右向左合并对象
 */
/* export function deepMerge(obj1, obj2) {
    let _this = this
    for (let key in obj2) {
      obj1[key] = obj1[key] && obj1[key].toString() === "[object Object]" ? _this.deepMerge(obj1[key], obj2[key]) : obj1[key] = obj2[key]
    }
    return obj1
} */
/**
 * 深度合并对象
 * @param  {...any} obj
 * @summary 从右向左合并对象
 */
export function deepMerge(...obj) {


	if (!obj.length) return null;
	if (obj.length == 1) return obj[0];
	let obj1 = {};
	for (let i = 0; i < obj.length; i++) {
		let obj2 = obj[i];
		loop(obj1, obj2);
	}

	function loop(obj1, obj2) {
		for (let key in obj2) {
			obj1[key] = obj1[key] && obj1[key].toString() === "[object Object]" ? loop(obj1[key], obj2[
				key]) : obj1[key] = obj2[key];
		}
		return obj1;
	}
	return obj1;
};
/**
 * 判断是否为质数
 * @param {Number} n
 * @returns {Boolean}
 * @example console.log(mathIsPrime(7)) //true
 */
function isPrime(n) {
	if (n === 2 || n === 3) {
		return true
	}
	if (isNaN(n) || n <= 1 || n % 1 != 0 || n % 2 == 0 || n % 3 == 0) {
		return false;
	}
	for (let x = 6; x <= Math.sqrt(n) + 1; x += 6) {
		if (n % (x - 1) == 0 || n % (x + 1) == 0) {
			return false
		}
	}
	return true
}
/**
 * 列表转树形数据
 * @param {Array} list
 * @param {Object} param1
 * @returns {Array}
 */
export function list2Tree(list, options) {
	options = {
		keyName: 'key',
		idName: 'id',
		fatherName: 'fid',
		titleName: 'title',
		childName: 'children',
		build: null,
		...options
	}
	options.build = options.build || ((row) => {
		return {
			title: row[options.titleName],
			key: row[options.keyName],
			id: row[options.idName],
      isLeaf:true,
			data: row
		}
	});
	let tree = [];
	if (list instanceof Array) {
		tree = list.filter(a => !a[options.fatherName]).map(a => {
			return options.build(a)
		})
	}
	let loop = (tree) => {
		tree.forEach(node => {
			let children = list.filter(a => a[options.fatherName] == node.id).map(a => {
				return options.build(a)
			});
			if (children.length > 0) {
				node[options.childName] = children;
				loop(children);
			}
		})
	}
	loop(tree);
	return tree;
}
export function reBuildTree(tree, build) {
	if (!(tree instanceof Array)) return null;
	if (typeof build !== 'function') return tree;
	let loop = (tree) => {
		return tree.map(a => {
			return build(a, loop);
		});
	}
	return loop(tree);
}
/**
 * 获取光标位置
 */
export function getCursorPosition(el) {
	let element = null;
	if (typeof el === 'string')
		element = document.getElementById(el); // 获取到指定标签
	else
		element = el;
	let startPos = element.selectionStart; // 获取光标开始的位置
	let endPos = element.selectionEnd; // 获取光标结束的位置
	if (startPos === undefined || endPos === undefined) return; // 如果没有光标位置 不操作
	return {
		start: startPos,
		end: endPos
	}
}
export function setCursorPosition(el, start, end) {
	let element = null;
	if (typeof el === 'string')
		element = document.getElementById(el); // 获取到指定标签
	else
		element = el;
	element.focus();
	//console.log(start,end)
	element.selectionStart = start;
	element.selectionEnd = end; // 设置光标开始的位置

}

export function getKeysFromTreeNodes(nodes, includeChildNodes = true) {
	let keys = [];
	if (!(nodes instanceof Array)) nodes = [nodes];

	let loop = (nodes) => {
		nodes.forEach(node => {
			keys.push(node.key);
			if (node.children) {
				loop(node.children);
			}
		})
	}
	loop(nodes);
	return keys;
}

export function loadScript(src, attrs) {
	return new Promise((resolve, reject) => {
		try {
			let scriptEle = document.createElement('script')
			scriptEle.type = 'text/javascript'
			scriptEle.src = src
			for (let key in attrs) {
				scriptEle.setAttribute(key, attrs[key])
			}
			scriptEle.addEventListener('load', function(e) {
				console.log(scriptEle, e)
				resolve('成功')
			})
			document.body.appendChild(scriptEle)
		} catch (err) {
			reject(err)
		}
	})
}
function toAbsURL(url){
    var a = document.createElement('a');
    a.href = url;
    return a.href;
};
//创建XMLHttpRequest 对象
//参数：无
//返回值：XMLHttpRequest 对象
function createXHR () {
    var XHR = [  //兼容不同浏览器和版本得创建函数数组
        function () { return new XMLHttpRequest () },
        function () { return new ActiveXObject ("Msxml2.XMLHTTP") },
        function () { return new ActiveXObject ("Msxml3.XMLHTTP") },
        function () { return new ActiveXObject ("Microsoft.XMLHTTP") }
    ];
    var xhr = null;
    //尝试调用函数，如果成功则返回XMLHttpRequest对象，否则继续尝试
    for (var i = 0; i < XHR.length; i ++) {
        try {
            xhr = XHR[i]();
        } catch(e) {
            continue  //如果发生异常，则继续下一个函数调用
        }
        break;  //如果成功，则中止循环
    }
    return xhr;  //返回对象实例
}
let loadFileAsTextCache=null;
export function syncLoadFileAsText(path){
	var xhr = createXHR();  //实例化XMLHttpRequest 对象
	xhr.open ("GET", path, false);  //建立连接
	xhr.send(null);  //发送请求
	return xhr.responseText;  //接收数据
}
export function loadFileAsText(path) {
	return new Promise((resolve, reject) => {
		loadFileAsTextCache = loadFileAsTextCache || {};
		if(loadFileAsTextCache[path+'']!=undefined){
			return resolve(loadFileAsTextCache[path+''])
		}
		const xhr = createXHR();
		// if(path.startsWith('/')){
		// 	path = toAbsURL(path);
		// }
		xhr.open('get', path);
		xhr.onreadystatechange = function() {
			if (xhr.readyState === 4) {
				if (xhr.status >= 200 && xhr.status < 300 || xhr.status === 304) {
					loadFileAsTextCache[path+'']=xhr.responseText;
					resolve(xhr.responseText)
				}
			}
		}
		xhr.send(null);
	})
}
let loadFunctionCache=null;
export async function loadFunction(path){
	loadFunctionCache = loadFunctionCache || {};
	if(loadFunctionCache[path+'']!=undefined){
		return loadFunctionCache[path+''];
	}
	let res = await loadFileAsText(path)
	res = eval(`(${res})`)({
		loadFileAsText,
		loadFunction
	});
	res = await toAsync(res);
	loadFunctionCache[path+'']=res;
	return res;
}
/**
 * 统一为异步
 * @param {object | Promise} res
 */
export  function toAsync(res){
	if(res instanceof Promise)
		return res;
	return new Promise((resolve,reject)=>{
		resolve(res);
	})
}

export function deepEquals(obj1, obj) {
	var p;
	if (obj1 === obj) {
		return true;
	}
	// some checks for native types first
	// function and sring
	if (typeof(obj1) === "function" || typeof(obj1) === "string" || obj1 instanceof String) {
		return obj1.toString() === obj.toString();
	}
	// number
	if (obj1 instanceof Number || typeof(obj1) === "number") {
		if (obj instanceof Number || typeof(obj) === "number") {
			return obj1.valueOf() === obj.valueOf();
		}
		return false;
	}
	// equalsObject(null,null) and equalsObject(undefined,undefined) do not inherit from the
	// Object.prototype so we can return false when they are passed as obj
	if (typeof(obj1) !== typeof(obj) || obj === null || typeof(obj) === "undefined") {
		return false;
	}

	function sort(o) {
		var result = {};
		if (typeof o !== "object") {
			return o;
		}
		Object.keys(o).sort().forEach(function(key) {
			result[key] = sort(o[key]);
		});
		return result;
	}
	if (typeof(obj1) === "object") {
		if (Array.isArray(obj1)) { // check on arrays
			return JSON.stringify(obj1) === JSON.stringify(obj);
		} else {
			// anyway objects
			for (p in obj1) {
				if (typeof(obj1[p]) !== typeof(obj[p])) {
					return false;
				}
				if ((obj1[p] === null) !== (obj[p] === null)) {
					return false;
				}
				switch (typeof(obj1[p])) {
					case 'undefined':
						if (typeof(obj[p]) !== 'undefined') {
							return false;
						}
						break;
					case 'object':
						if (obj1[p] !== null && obj[p] !== null &&
							(obj1[p].constructor.toString() !== obj[p].constructor.toString() ||
								!equalsObject(obj1[p], obj[p]))) {
							return false;
						}
						break;
					case 'function':
						if (obj1[p].toString() !== obj[p].toString()) {
							return false;
						}
						break;
					default:
						if (obj1[p] !== obj[p]) {
							return false;
						}
				}
			};
		}
	}
	// at least check them with JSON
	return JSON.stringify(sort(obj1)) === JSON.stringify(sort(obj));
}

/**
 * 删除URL中指定search参数,会将参数值一起删除
 * @param {string} url 地址字符串
 * @param {array} aParam 要删除的参数key数组，如['name','age']
 * @return {string} 返回新URL字符串
 */
export function ridUrlParam(url, params) {
	for (var index = 0; index < params.length; index++) {
		var item = params[index];
		var fromIndex = url.indexOf(item + "="); //必须加=号，避免参数值中包含item字符串
		if (fromIndex !== -1) {
			// 通过url特殊符号，计算出=号后面的的字符数，用于生成replace正则
			var startIndex = url.indexOf("=", fromIndex);
			var endIndex = url.indexOf("&", fromIndex);
			var hashIndex = url.indexOf("#", fromIndex);

			var reg = "";
			if (endIndex !== -1) {
				// 后面还有search参数的情况
				var num = endIndex - startIndex;
				reg = new RegExp(item + "=.{" + num + "}");
				url = url.replace(reg, "");
			} else if (hashIndex !== -1) {
				// 有hash参数的情况
				var num = hashIndex - startIndex - 1;
				reg = new RegExp("&?" + item + "=.{" + num + "}");
				url = url.replace(reg, "");
			} else {
				// search参数在最后或只有一个参数的情况
				reg = new RegExp("&?" + item + "=.+");
				url = url.replace(reg, "");
			}
		}
	}
	var noSearchParam = url.indexOf("=");
	if (noSearchParam === -1) {
		url = url.replace(/\?/, ""); // 如果已经没有参数，删除？号
	}
	return url;
}
export function toKeyValues(obj) {
	let keyValues = [];
	if (obj) {
		for (let p in obj) {
			keyValues.push({
				key: p,
				value: obj[p]
			})
		}
	}
	return keyValues;
}
/**
 * Base64编解码
 */
function Base64() {

	// private property
	_keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

	/**
	 * Base64编码
	 * @param {String} input
	 * @returns {String}
	 * @example  this.base64 = new Base64(); console.log(this.base64.encode("123"))
	 */
	this.encode = function(input) {
		var output = "";
		var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
		var i = 0;
		input = _utf8_encode(input);
		while (i < input.length) {
			chr1 = input.charCodeAt(i++);
			chr2 = input.charCodeAt(i++);
			chr3 = input.charCodeAt(i++);
			enc1 = chr1 >> 2;
			enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
			enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
			enc4 = chr3 & 63;
			if (isNaN(chr2)) {
				enc3 = enc4 = 64;
			} else if (isNaN(chr3)) {
				enc4 = 64;
			}
			output = output +
				_keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
				_keyStr.charAt(enc3) + _keyStr.charAt(enc4);
		}
		return output;
	}

	/**
	 * Base64解码
	 * @param {String} input
	 * @returns {String}
	 * @example  this.base64 = new Base64(); console.log(this.base64.decode(this.base64.encode("123")))
	 */
	this.decode = function(input) {
		var output = "";
		var chr1, chr2, chr3;
		var enc1, enc2, enc3, enc4;
		var i = 0;
		input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
		while (i < input.length) {
			enc1 = _keyStr.indexOf(input.charAt(i++));
			enc2 = _keyStr.indexOf(input.charAt(i++));
			enc3 = _keyStr.indexOf(input.charAt(i++));
			enc4 = _keyStr.indexOf(input.charAt(i++));
			chr1 = (enc1 << 2) | (enc2 >> 4);
			chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
			chr3 = ((enc3 & 3) << 6) | enc4;
			output = output + String.fromCharCode(chr1);
			if (enc3 != 64) {
				output = output + String.fromCharCode(chr2);
			}
			if (enc4 != 64) {
				output = output + String.fromCharCode(chr3);
			}
		}
		output = _utf8_decode(output);
		return output;
	}
	this.
	// private method for UTF-8 encoding
	_utf8_encode = function(string) {
		string = string.replace(/\r\n/g, "\n");
		var utftext = "";
		for (var n = 0; n < string.length; n++) {
			var c = string.charCodeAt(n);
			if (c < 128) {
				utftext += String.fromCharCode(c);
			} else if ((c > 127) && (c < 2048)) {
				utftext += String.fromCharCode((c >> 6) | 192);
				utftext += String.fromCharCode((c & 63) | 128);
			} else {
				utftext += String.fromCharCode((c >> 12) | 224);
				utftext += String.fromCharCode(((c >> 6) & 63) | 128);
				utftext += String.fromCharCode((c & 63) | 128);
			}

		}
		return utftext;
	}

	// private method for UTF-8 decoding
	_utf8_decode = function(utftext) {
		var string = "";
		var i = 0;
		var c = c1 = c2 = 0;
		while (i < utftext.length) {
			c = utftext.charCodeAt(i);
			if (c < 128) {
				string += String.fromCharCode(c);
				i++;
			} else if ((c > 191) && (c < 224)) {
				c2 = utftext.charCodeAt(i + 1);
				string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
				i += 2;
			} else {
				c2 = utftext.charCodeAt(i + 1);
				c3 = utftext.charCodeAt(i + 2);
				string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
				i += 3;
			}
		}
		return string;
	}
}


var utils = {
	/**
	 * 日期格式化
	 *
	 * @param {Date} date 指定日期
	 * @param {String} format
	 * @returns {String}
	 * @summary 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
	 * 年(y)可以用 1-4个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
	 * @example (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02
	 * 08:09:04.423 (new Date()).Format("yyyy-M-d h:m:s.S") ==> 2006-7-2 8:9:4.18
	 */
	formatDate: function(date, format) {
		var o = {
			'M+': date.getMonth() + 1, //month
			'd+': date.getDate(), //day
			'h+': date.getHours(), //hour
			'm+': date.getMinutes(), //minute
			's+': date.getSeconds(), //second
			'q+': Math.floor((date.getMonth() + 3) / 3), //quarter
			'S': date.getMilliseconds() //millisecond
		};
		if (/(y+)/.test(format)) {
			format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
		}
		for (var k in o) {
			if (new RegExp('(' + k + ')').test(format)) {
				format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[
					k]).length));
			}
		}
		return format;
	},

	// 获取过去的n天
	getBeforeDay: function(date, days) {
		var date = date || new Date();
		return new Date(Date.parse(date.toString()) - 86400000 * days);
	},

	// 查询字符串
	getQueryString: function(name) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
		var r = window.location.search.substr(1).match(reg); // 获取url中"?"符后的字符串并正则匹配
		var context = "";
		if (r != null) {
			context = r[2];
		}
		reg = null;
		r = null;
		return (context == null || context == "" || context == "undefined") ? "" : context;
	},

	// 删除空白字符串
	delBlankSpace: function(str) {
		var str = str.replace(/<\/?[^>]*>/gim, ""); // 去掉所有的html标记
		var result = str.replace(/(^\s+)|(\s+$)/g, ""); // 去掉前后空格
		return result.replace(/\s/g, ""); // 去除文章中间空格
	},

	// 判断参数非空
	validateBlank: function(tmp) {
		if (!tmp && typeof(tmp) != "undefined" && tmp != 0) {
			// null
			return;
		} else if (typeof(tmp) == "undefined") {
			// undefined
			return;
		} else if (Array.isArray(tmp) && tmp.length === 0) {
			// 空数组
			return;
		} else if ($.trim(tmp) == "") {
			// 空串
			return;
		} else if (Object.prototype.isPrototypeOf(tmp) && Object.keys(tmp).length === 0) {
			// 空对象
			return;
		} else {
			return tmp;
		}
	},

	// 检测段落里空格和换行,转换成html输出
	blankRegExp: function(str) {
		if (typeof str != "string")
			return "";

		return this.htmlEncode(str).replace(/\r{0,}\n/g, '<br/>');
	},

	// 转义html为安全文本
	htmlEncode: function(str) {
		//多个replace会有bug
		//return str.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/\"/g, "&quot;").replace(/\'/g, "&#39;").replace(/ /g, "&nbsp;");
		var html_encodes = {
			'&': '&amp;',
			'<': '&lt;',
			'>': '&gt;',
			'"': '&quot;',
			"'": "&#39;",
			' ': '&nbsp;'
		};
		return str.replace(/(&|<|>|\"|\'| )/g, function(str, item) {
			return html_encodes[item];
		});
	},
	//正则解码
	htmlDecode: function(str) {
		var html_decodes = {
			'&amp;': '&',
			'&lt;': '<',
			'&gt;': '>',
			'&quot;': '"',
			"&#39;": "'",
			'&nbsp;': ' '
		};
		return str.replace(/(&amp;|&lt;|&gt;|&quot;|&#39;|&nbsp;)/g, function(str, item) {
			return html_decodes[item];
		});
	},
	/*用浏览器内部转换器实现html转码*/
	HTMLEncode: function(html) {
		//1.首先动态创建一个容器标签元素，如DIV
		var temp = document.createElement("div");
		//2.然后将要转换的字符串设置为这个元素的innerText(ie支持)或者textContent(火狐，google支持)
		(temp.textContent != undefined) ? (temp.textContent = html) : (temp.innerText = html);
		//3.最后返回这个元素的innerHTML，即得到经过HTML编码转换的字符串了
		var output = temp.innerHTML;
		temp = null;
		return output;
	},

	/*用浏览器内部转换器实现html解码*/
	HTMLDecode: function(input) {
		var converter = document.createElement("DIV");
		converter.innerHTML = input;
		var output = converter.innerText;
		converter = null;
		return output;
	},

	// 裁剪文字，显示...
	cutText: function(str, maxLength, showEllipsis) {
		if (str.length > maxLength) {
			str = str.substr(0, maxLength);
			if (showEllipsis) {
				str += "...";
			}
		}
		return str;
	},

	// 判断微信内置浏览器
	isWeixin: function() {
		var ua = navigator.userAgent.toLowerCase();
		return (ua.match(/MicroMessenger/i) == "micromessenger");
	},


	// 获取页面停留时间,依赖jquery.cookie.js
	countStayTime: function() {
		var second = 0;
		window.setInterval(function() {
			second++;
		}, 1000);
		var tjArr = localStorage.getItem("jsArr") ? localStorage.getItem("jsArr") : '[{}]';
		$.cookie('tjRefer', getReferrer(), {
			expires: 1,
			path: '/'
		});

		window.onbeforeunload = function() {
			if ($.cookie('tjRefer') == '') {
				var tjT = eval('(' + localStorage.getItem("jsArr") + ')');
				if (tjT) {
					tjT[tjT.length - 1].time += second;
					var jsArr = JSON.stringify(tjT);
					localStorage.setItem("jsArr", jsArr);
				}
			} else {
				var tjArr = localStorage.getItem("jsArr") ? localStorage.getItem("jsArr") : '[{}]';
				var dataArr = {
					'url': location.href,
					'time': second,
					'refer': getReferrer() || document.referrer,
					'timeIn': Date.parse(new Date()),
					'timeOut': Date.parse(new Date()) + (second * 1000)
				};
				tjArr = eval('(' + tjArr + ')');
				tjArr = JSON.stringify(dataArr);
				localStorage.setItem("jsArr", tjArr);
			}
			var standTime = (JSON.parse(localStorage.getItem('jsArr'))).time;
			return standTime; //单位：s
		};

	},

	// 获取前一个页面的url
	getReferrer: function() {
		var referrer = '';
		try {
			referrer = window.top.document.referrer;
		} catch (e) {
			if (window.parent) {
				try {
					referrer = window.parent.document.referrer;
				} catch (e2) {
					referrer = '';
				}
			}
		}
		if (referrer === '') {
			referrer = document.referrer;
		}
		return referrer;
	},

	//屏蔽alert 弹框
	noAlert: function() {
		//if(true) return
		window.alert = function(str) {
			return;
		};
	},

};

/**
 * 深拷贝数据
 * @param {*} data
 */
 export function deepCopy(data) {
	//string,number,bool,null,undefined,symbol
	//object,array,date
	if (data && typeof data === "object") {
	  //针对函数的拷贝
	  if (typeof data === "function") {
		let tempFunc = data.bind(null);
		tempFunc.prototype = deepCopy(data.prototype);
		return tempFunc;
	  }

	  switch (Object.prototype.toString.call(data)) {
		case "[object String]":
		  return data.toString();
		case "[object Number]":
		  return Number(data.toString());
		case "[object Boolean]":
		  return new Boolean(data.toString());
		case "[object Date]":
		  return new Date(data.getTime());
		case "[object Array]":
		  var arr = [];
		  for (let i = 0; i < data.length; i++) {
			arr[i] = deepCopy(data[i]);
		  }
		  return arr;

		//js自带对象或用户自定义类实例
		case "[object Object]":
		  var obj = {};
		  for (let key in data) {
			//会遍历原型链上的属性方法，可以用hasOwnProperty来控制 （obj.hasOwnProperty(prop)
			obj[key] = deepCopy(data[key]);
		  }
		  return obj;
	  }
	} else {
	  //string,number,bool,null,undefined,symbol
	  return data;
	}
  }



//event事件
var EventUtil = {

	addHandler: function(element, type, handler) { //添加事件
		if (element.addEventListener) {
			element.addEventListener(type, handler, false); //使用DOM2级方法添加事件
		} else if (element.attachEvent) { //使用IE方法添加事件
			element.attachEvent("on" + type, handler);
		} else {
			element["on" + type] = handler; //使用DOM0级方法添加事件
		}
	},

	removeHandler: function(element, type, handler) { //取消事件
		if (element.removeEventListener) {
			element.removeEventListener(type, handler, false);
		} else if (element.detachEvent) {
			element.detachEvent("on" + type, handler);
		} else {
			element["on" + type] = null;
		}
	},

	getEvent: function(event) { //使用这个方法跨浏览器取得event对象
		return event ? event : window.event;
	},

	getTarget: function(event) { //返回事件的实际目标
		return event.target || event.srcElement;
	},

	preventDefault: function(event) { //阻止事件的默认行为
		if (event.preventDefault) {
			event.preventDefault();
		} else {
			event.returnValue = false;
		}
	},

	stopPropagation: function(event) { //立即停止事件在DOM中的传播
		//避免触发注册在document.body上面的事件处理程序
		if (event.stopPropagation) {
			event.stopPropagation();
		} else {
			event.cancelBubble = true;
		}
	},

	getRelatedTarget: function(event) { //获取mouseover和mouseout相关元素
		if (event.relatedTarget) {
			return event.relatedTarget;
		} else if (event.toElement) { //兼容IE8-
			return event.toElement;
		} else if (event.formElement) {
			return event.formElement;
		} else {
			return null;
		}
	},

	getButton: function(event) { //获取mousedown或mouseup按下或释放的按钮是鼠标中的哪一个
		if (document.implementation.hasFeature("MouseEvents", "2.0")) {
			return event.button;
		} else {
			switch (event.button) { //将IE模型下的button属性映射为DOM模型下的button属性
				case 0:
				case 1:
				case 3:
				case 5:
				case 7:
					return 0; //按下的是鼠标主按钮（一般是左键）
				case 2:
				case 6:
					return 2; //按下的是中间的鼠标按钮
				case 4:
					return 1; //鼠标次按钮（一般是右键）
			}
		}
	},

	getWheelDelta: function(event) { //获取表示鼠标滚轮滚动方向的数值
		if (event.wheelDelta) {
			return event.wheelDelta;
		} else {
			return -event.detail * 40;
		}
	},

	getCharCode: function(event) { //以跨浏览器取得相同的字符编码，需在keypress事件中使用
		if (typeof event.charCode == "number") {
			return event.charCode;
		} else {
			return event.keyCode;
		}
	},


};
export default {
	saveAs
}
