export default class LabelCanvas {
  constructor(options) {
    this.viewer = options.viewer;

    this.viewer.scene.postRender.addEventListener(this.postRender, this);
  }
  postRender() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const image = new Image();
    image.style.width = '400px';
    const name = '韭年';
    image.onload = () => {
      canvas.width = image.width;
      canvas.height = image.height;
      ctx.drawImage(image, 0, 0, image.width, image.height);
      ctx.font = 'bold 20px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';
      ctx.fillStyle = '#FFF';
      ctx.fillText(name, 220, 70);
      this.imageUrl = canvas.toDataURL('image/png');
    };
    image.src = Iurl;

    //当前锚点的笛卡尔坐标
    var nowposition = this.point.position.getValue(Cesium.JulianDate.now());
    //笛卡尔坐标转屏幕坐标
    var screen = Cesium.SceneTransforms.wgs84ToWindowCoordinates(
      this.viewer.scene,
      nowposition,
    );
    if (!Cesium.defined(screen)) return undefined;

    var tx = this.viewer.container.getClientRects()[0].x;
    var ry = this.viewer.container.getClientRects()[0].y;
    this.canvas.style.left = tx + (screen.x + 0) + 'px';
    this.canvas.style.top = ry + (screen.y - 50) + 'px'; //50为偏移设置
    if (this.data.maxDistance) {
      var nowi = this.point.position.getValue(Cesium.JulianDate.now());
      var position1 = Cesium.Cartesian3.distance(
        nowi,
        this.viewer.scene.camera.position,
      );
      //最大可视距离
      if (position1 > this.data.maxDistance || !this.show) {
        this.setVisible(false);
      } else {
        this.setVisible(true);
      }
    }
  }
  //bol为布尔值
  setVisible(bol) {
    if (this.point) {
      this.point.show = bol;
    }
    if (this.canvas) {
      this.canvas.style.display = bol ? 'block' : 'none';
    }
    this.viewer.scene.requestRender();
  }
  destroy() {
    this.viewer.scene.postRender.removeEventListener(this.postRender);
  }
}
/* 
var obj ={
	viewer:viewer,
	position: [longitude, latitude, 10],
	width: 192, //Label的宽度
	height: 32, //Label的高度
	text: '带锚点的Label', //文字内容
	lineWidth: 1, //线宽
	textColor: '#FF0000', //文字颜色
	textbackground:"#D1D1D1",
	fontSize: 16, //字号
	maxDistance: 1000000 ,//最大可视距离
	anchorstyle:{
		color:"#000000",
		coloralpha:0.5,
		pixelSize:5,
		outlineColor:"#FF0000",
		outlineColoralpha:0.5,
		outlineWidth:2,
		show:true
	}
}
var Label = new Labelcanvas(obj);
 */
