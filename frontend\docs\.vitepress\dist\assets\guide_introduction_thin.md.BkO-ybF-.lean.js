import{ao as s,k as i,aP as a,l as e,ay as n,j as t}from"./chunks/framework.C8U7mBUf.js";const l=JSON.parse('{"title":"精简版本","description":"","frontmatter":{"outline":"deep"},"headers":[],"relativePath":"guide/introduction/thin.md","filePath":"guide/introduction/thin.md"}');const h=s({name:"guide/introduction/thin.md"},[["render",function(s,l,h,p,d,o){const k=n("NolebaseGitContributors"),r=n("NolebaseGitChangelog");return t(),i("div",null,[l[0]||(l[0]=a('<h1 id="精简版本" tabindex="-1">精简版本 <a class="header-anchor" href="#精简版本" aria-label="Permalink to &quot;精简版本&quot;">​</a></h1><p>从 <code>5.0</code> 版本开始，我们不再提供精简的仓库或者分支。我们的目标是提供一个更加一致的开发体验，同时减少维护成本。在这里，我们将如何介绍自己的项目，如何去精简以及移除不需要的功能。</p><h2 id="应用精简" tabindex="-1">应用精简 <a class="header-anchor" href="#应用精简" aria-label="Permalink to &quot;应用精简&quot;">​</a></h2><p>首先，确认你需要的 <code>UI</code> 组件库版本，然后删除对应的应用，比如你选择使用 <code>Ant Design Vue</code>，那么你可以删除其他应用， 只需要删除下面两个文件夹即可：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">apps/web-ele</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">apps/web-naive</span></span></code></pre></div><div class="tip custom-block"><p class="custom-block-title">TIP</p><p>如果项目没有内置你需要的 <code>UI</code> 组件库应用，你可以直接全部删除其他应用。然后自行新建应用即可。</p></div><h2 id="演示代码精简" tabindex="-1">演示代码精简 <a class="header-anchor" href="#演示代码精简" aria-label="Permalink to &quot;演示代码精简&quot;">​</a></h2><p>如果你不需要演示代码，你可以直接删除的<code>playground</code>文件夹。</p><h2 id="文档精简" tabindex="-1">文档精简 <a class="header-anchor" href="#文档精简" aria-label="Permalink to &quot;文档精简&quot;">​</a></h2><p>如果你不需要文档，你可以直接删除<code>docs</code>文件夹。</p><h2 id="mock-服务精简" tabindex="-1">Mock 服务精简 <a class="header-anchor" href="#mock-服务精简" aria-label="Permalink to &quot;Mock 服务精简&quot;">​</a></h2><p>如果你不需要<code>Mock</code>服务，你可以直接删除<code>apps/backend-mock</code>文件夹。同时在你的应用下<code>.env.development</code>文件中删除<code>VITE_NITRO_MOCK</code>变量。</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 是否开启 Nitro Mock服务，true 为开启，false 为关闭</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_NITRO_MOCK</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">false</span></span></code></pre></div><h2 id="安装依赖" tabindex="-1">安装依赖 <a class="header-anchor" href="#安装依赖" aria-label="Permalink to &quot;安装依赖&quot;">​</a></h2><p>到这里，你已经完成了精简操作，接下来你可以安装依赖，并启动你的项目：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 根目录下执行</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> install</span></span></code></pre></div><h2 id="命令调整" tabindex="-1">命令调整 <a class="header-anchor" href="#命令调整" aria-label="Permalink to &quot;命令调整&quot;">​</a></h2><p>在精简后，你可能需要根据你的项目调整命令，在根目录下的<code>package.json</code>文件中，你可以调整<code>scripts</code>字段，移除你不需要的命令。</p><div class="language-json vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;scripts&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:antd&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/web-antd&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:docs&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/docs&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:ele&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/web-ele&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:naive&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/web-naive&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:play&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/playground&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:antd&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/web-antd run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:docs&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/docs run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:ele&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/web-ele run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:play&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/playground run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:naive&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/web-naive run dev&quot;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><h2 id="其他" tabindex="-1">其他 <a class="header-anchor" href="#其他" aria-label="Permalink to &quot;其他&quot;">​</a></h2><p>如果你想更进一步精简，你可以删除参考一下文件或者文件夹的作用，判断自己是否需要，不需要删除即可：</p><ul><li><code>.changeset</code> 文件夹用于管理版本变更</li><li><code>.github</code> 文件夹用于存放 GitHub 的配置文件</li><li><code>.vscode</code> 文件夹用于存放 VSCode 的配置文件，如果你使用其他编辑器，可以删除</li><li><code>./scripts/deploy</code> 文件夹用于存放部署脚本，如果你不需要docker部署，可以删除</li></ul><h2 id="应用精简-1" tabindex="-1">应用精简 <a class="header-anchor" href="#应用精简-1" aria-label="Permalink to &quot;应用精简&quot;">​</a></h2><p>当你确定了某个应用，你还可以进一步精简：</p><h3 id="删除不需要的路由及页面" tabindex="-1">删除不需要的路由及页面 <a class="header-anchor" href="#删除不需要的路由及页面" aria-label="Permalink to &quot;删除不需要的路由及页面&quot;">​</a></h3><ul><li><p>在应用的 <code>src/router/routes</code> 文件中，你可以删除不需要的路由。其中 <code>core</code> 文件夹内，如果只需要登录和忘记密码，你可以删除其他路由，如忘记密码、注册等。路由删除后，你可以删除对应的页面文件，在 <code>src/views/_core</code> 文件夹中。</p></li><li><p>在应用的 <code>src/router/routes</code> 文件中，你可以按需求删除不需要的路由，如<code>demos</code>、<code>vben</code> 目录等。路由删除后，你可以删除对应的页面文件，在 <code>src/views</code> 文件夹中。</p></li></ul><h3 id="删除不需要的组件" tabindex="-1">删除不需要的组件 <a class="header-anchor" href="#删除不需要的组件" aria-label="Permalink to &quot;删除不需要的组件&quot;">​</a></h3><ul><li>在应用的 <code>packages/effects/common-ui/src/ui</code> 文件夹中，你可以删除不需要的组件，如<code>about</code>、<code>dashboard</code> 目录等。删除之前请先确保你的路由中没有引用到这些组件。</li></ul>',28)),e(k),e(r)])}]]);export{l as __pageData,h as default};
