import * as Cesium from 'cesium';
import * as echarts from 'echarts';
import Base from '../core/Base';
import Graphics from '../core/Graphics';
import core from '../core.js';
import timeSlice from '../../common/timeSlice';
import LabelDiv from '../components/LabelDiv';
import { utils, writeFileXLSX } from 'xlsx';
/**
 * 坡度分析
 * @param {*} params
 */
function SlopeAnalysis(params) {
  if (params && params.positions) {
    this._base = new Base(params.that._viewer);
    this._graphics = new Graphics(params.that._viewer);
    var $this = this;
    var positions = params.positions,
      that = params.that,
      points = [],
      echartContainer = null,
      position1 = this._base.transformWGS84ToCartesian(positions[0]),
      position2 = this._base.transformWGS84ToCartesian(positions[1]);
    points = this._graphics.createPointsGraphics({
      point: true,
      positions: [position1, position2],
    });
    //显示结果
    function showResult(startPoint, endPoint) {
      console.log('showResult', startPoint, endPoint);
      //起止点相关信息
      var scartographic = Cesium.Cartographic.fromCartesian(startPoint);
      console.log(scartographic);
      var samplePoint = [scartographic];
      var heights = [];
      var pointSum = params.pointSum || 10; //取样点个数
      var tempCartesians = new Cesium.Cartesian3();
      var slopePercent = [0];
      var disL = [0];
      var angle = 0;
      for (var i = 1; i <= pointSum; i++) {
        console.log(i);
        Cesium.Cartesian3.lerp(
          startPoint,
          endPoint,
          i / pointSum,
          tempCartesians,
        );
        tempCartesians = that._viewer.scene.clampToHeight(tempCartesians, []);
        var tempCartographic =
          Cesium.Cartographic.fromCartesian(tempCartesians);
        var surfaceHeight = tempCartographic.height; // that._viewer.scene.globe.getHeight(tempCartographic);
        //tempCartographic.height = surfaceHeight;
        heights.push(surfaceHeight);
        samplePoint.push(tempCartographic);
        var lastCarto = samplePoint[i - 1];
        var dis = Cesium.Cartesian3.distance(
          Cesium.Cartographic.toCartesian(lastCarto),
          Cesium.Cartographic.toCartesian(tempCartographic),
        );
        disL.push(disL[i - 1] + dis);
        angle = Math.asin((tempCartographic.height - lastCarto.height) / dis);
        slopePercent.push(Math.tan(angle) * 100);
      }
      slopePercent = slopePercent.slice(1);
      disL = disL.slice(1);
      slopePercent = slopePercent.map((a) => parseFloat(a.toFixed(2)));
      disL = disL.map((a) => Math.floor(a));
      heights = heights.map((a) => Math.floor(a));
      echartContainer = document.createElement('div');
      echartContainer.className = 'echart-viewer';
      /* that._viewer.container.appendChild(echartContainer, 'dark', {
                        renderer: 'canvas',
                        width: 640,
                        height: 480
                    }); */
      $this._label = new LabelDiv({
        viewer: that._viewer,
        el: echartContainer,
        position: startPoint,
        toolbox: {
          size: 20,
          fill: '#fff',
          buttons: {
            close: {
              click: () => {
                params.close && params.close();
                $this.remove();
              },
            },
          },
        },
      });
      //echartContainer.style.position = "absolute";
      //echartContainer.style.right = '140px';
      //echartContainer.style.top = '100px';
      echartContainer.style.height = '300px';
      echartContainer.style.width = '640px';
      echartContainer.style.overflow = 'hidden';
      echartContainer.style.zIndex = '1';
      echartContainer.style.background = 'rgba(5, 17, 41, 0.8)';
      echartContainer.style['pointer-events'] = 'all';
      //echartContainer.style.opacity = 0.9;
      var myChart = echarts.init(echartContainer);
      var option = {
        title: {
          text: '剖面示意图',
          left: 'center',
          subtext: '',
          textStyle: {
            color: 'white',
            fontSize: 15,
          },
        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: [''],
        },
        //右上角工具条
        toolbox: {
          show: true,
          iconStyle: {
            borderColor: '#fff', // 图标默认颜色
          },
          emphasis: {
            iconStyle: {
              borderColor: '#fff', // 图标hover颜色
            },
          },
          orient: 'vertical',
          itemSize: 16, // 设置图标大小
          feature: {
            dataView: {
              show: true,
              readOnly: false,
              title: '数据视图',
              lang: ['数据视图', '关闭', '刷新'],
              backgroundColor: 'rgba(5, 17, 41, 0.8)',
              textareaColor: 'transparent',
              textColor: '#fff',
              textareaBorderColor: '#fff',
              buttonColor: 'rgba(0, 0, 0, 0.85)',
              buttonTextColor: '#fff',
            },
            /* magicType: { show: true, type: ['line', 'bar'],title:{
									line:'切换为折线图',
									bar:'切换为柱状图'
								} }, */
            //restore: { show: true,title:'刷新' },
            saveAsImage: {
              show: true,
              backgroundColor: '#020527',
              title: '保存为图片',
            },
            myExportExcel: {
              show: true,
              title: '保存为Excel',
              icon: 'path://M563.2 1006.933333s-3.413333 0 0 0l-549.546667-102.4c-6.826667-3.413333-13.653333-10.24-13.653333-17.066666V170.666667c0-6.826667 6.826667-13.653333 13.653333-17.066667l546.133334-136.533333c3.413333 0 10.24 0 13.653333 3.413333 3.413333 3.413333 6.826667 6.826667 6.826667 13.653333v955.733334c0 3.413333-3.413333 10.24-6.826667 13.653333-3.413333 3.413333-6.826667 3.413333-10.24 3.413333zM34.133333 873.813333l512 95.573334V54.613333L34.133333 184.32v689.493333zM1006.933333 938.666667h-443.733333c-10.24 0-17.066667-6.826667-17.066667-17.066667s6.826667-17.066667 17.066667-17.066667H989.866667v-785.066666H580.266667c-10.24 0-17.066667-6.826667-17.066667-17.066667s6.826667-17.066667 17.066667-17.066667h426.666666c10.24 0 17.066667 6.826667 17.066667 17.066667v819.2c0 10.24-6.826667 17.066667-17.066667 17.066667zM392.533333 699.733333c-6.826667 0-10.24-3.413333-13.653333-6.826666l-204.8-341.333334c-6.826667-10.24-3.413333-20.48 3.413333-23.893333s17.066667-3.413333 23.893334 6.826667l204.8 341.333333c3.413333 6.826667 3.413333 17.066667-6.826667 23.893333h-6.826667zM187.733333 699.733333c-3.413333 0-6.826667 0-10.24-3.413333-6.826667-3.413333-10.24-13.653333-3.413333-23.893333l204.8-341.333334c3.413333-6.826667 13.653333-10.24 23.893333-6.826666 6.826667 3.413333 10.24 13.653333 6.826667 23.893333l-204.8 341.333333c-6.826667 6.826667-10.24 10.24-17.066667 10.24zM733.866667 938.666667c-10.24 0-17.066667-6.826667-17.066667-17.066667V119.466667c0-10.24 6.826667-17.066667 17.066667-17.066667s17.066667 6.826667 17.066666 17.066667V921.6c0 10.24-6.826667 17.066667-17.066666 17.066667zM989.866667 802.133333H563.2c-10.24 0-17.066667-6.826667-17.066667-17.066666s6.826667-17.066667 17.066667-17.066667H989.866667c10.24 0 17.066667 6.826667 17.066666 17.066667s-6.826667 17.066667-17.066666 17.066666zM1006.933333 665.6h-443.733333c-10.24 0-17.066667-6.826667-17.066667-17.066667s6.826667-17.066667 17.066667-17.066666h443.733333c10.24 0 17.066667 6.826667 17.066667 17.066666s-6.826667 17.066667-17.066667 17.066667zM1006.933333 529.066667H580.266667c-10.24 0-17.066667-6.826667-17.066667-17.066667s6.826667-17.066667 17.066667-17.066667h426.666666c10.24 0 17.066667 6.826667 17.066667 17.066667s-6.826667 17.066667-17.066667 17.066667zM1000.106667 392.533333H573.44c-10.24 0-17.066667-6.826667-17.066667-17.066666s6.826667-17.066667 17.066667-17.066667h426.666667c10.24 0 17.066667 6.826667 17.066666 17.066667s-10.24 17.066667-17.066666 17.066666zM1006.933333 256h-443.733333c-10.24 0-17.066667-6.826667-17.066667-17.066667s6.826667-17.066667 17.066667-17.066666h443.733333c10.24 0 17.066667 6.826667 17.066667 17.066666s-6.826667 17.066667-17.066667 17.066667z',
              onclick: () => {
                let data = disL.map((a, index) => {
                  return {
                    distance: a,
                    height: heights[index] || 0,
                  };
                });
                const ws = utils.json_to_sheet(data);
                const wb = utils.book_new();
                utils.book_append_sheet(wb, ws, 'Data');
                /* fix headers */
                utils.sheet_add_aoa(ws, [['长度(米)', '高度(米)']], {
                  origin: 'A1',
                });
                writeFileXLSX(wb, 'slope.xlsx');
              },
            },
          },
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            name: '长度(米)',
            boundaryGap: false,
            data: disL,
            axisLabel: {
              textStyle: {
                color: 'white',
              },
            },
            axisLine: {
              lineStyle: {
                color: 'white',
              },
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '高度(米)',
            axisLabel: {
              formatter: function (data) {
                return data.toFixed(0) + '米';
              },
              // formatter: '{value} 米',
              textStyle: {
                color: 'white',
              },
            },
            axisLine: {
              lineStyle: {
                color: 'white',
              },
            },
          },
        ],
        series: [
          {
            name: '高度',
            type: 'line',
            areaStyle: {},
            smooth: true,
            data: heights,
          },
        ],
      };

      // 为echarts对象加载数据
      myChart.setOption(option);
      return myChart;
    }

    //showResult(points[0], points[1])
    showResult(position1, position2);
  }
  SlopeAnalysis.prototype.remove = function () {
    points.forEach((a) => {
      if (a.owner) {
        a.owner.entities.remove(a);
      } else if (a.entityCollection && a.entityCollection.owner) {
        a.entityCollection.owner.entities.remove(a);
      }
      //that._analysisLayer.entities.remove(a);
    });
    //if(echartContainer){
    //	that._viewer.container.removeChild(echartContainer);
    //}
    if ($this._label) {
      $this._label.destroy();
      $this._label = null;
    }
    points = [];
    echartContainer = null;
  };
}
export default SlopeAnalysis;
