function N(r,e){e===void 0&&(e={});var t=e.insertAt;if(typeof document!="undefined"){var i=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",t==="top"&&i.firstChild?i.insertBefore(n,i.firstChild):i.appendChild(n),n.styleSheet?n.styleSheet.cssText=r:n.appendChild(document.createTextNode(r))}}var F="@keyframes watermark{0%{background-position:0 0}25%{background-position:100% 100%}50%{background-position:0 0}75%{background-position:100% -100%}to{background-position:0 0}}";N(F);var R=function(r,e){return R=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])},R(r,e)};function X(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");R(r,e);function t(){this.constructor=r}r.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}var _=function(){return _=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++){t=arguments[i];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},_.apply(this,arguments)};function S(r,e,t,i){function n(a){return a instanceof t?a:new t(function(d){d(a)})}return new(t||(t=Promise))(function(a,d){function l(u){try{o(i.next(u))}catch(c){d(c)}}function s(u){try{o(i.throw(u))}catch(c){d(c)}}function o(u){u.done?a(u.value):n(u.value).then(l,s)}o((i=i.apply(r,e||[])).next())})}function b(r,e){var t={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},i,n,a,d;return d={next:l(0),throw:l(1),return:l(2)},typeof Symbol=="function"&&(d[Symbol.iterator]=function(){return this}),d;function l(o){return function(u){return s([o,u])}}function s(o){if(i)throw new TypeError("Generator is already executing.");for(;d&&(d=0,o[0]&&(t=0)),t;)try{if(i=1,n&&(a=o[0]&2?n.return:o[0]?n.throw||((a=n.return)&&a.call(n),0):n.next)&&!(a=a.call(n,o[1])).done)return a;switch(n=0,a&&(o=[o[0]&2,a.value]),o[0]){case 0:case 1:a=o;break;case 4:return t.label++,{value:o[1],done:!1};case 5:t.label++,n=o[1],o=[0];continue;case 7:o=t.ops.pop(),t.trys.pop();continue;default:if(a=t.trys,!(a=a.length>0&&a[a.length-1])&&(o[0]===6||o[0]===2)){t=0;continue}if(o[0]===3&&(!a||o[1]>a[0]&&o[1]<a[3])){t.label=o[1];break}if(o[0]===6&&t.label<a[1]){t.label=a[1],a=o;break}if(a&&t.label<a[2]){t.label=a[2],t.ops.push(o);break}a[2]&&t.ops.pop(),t.trys.pop();continue}o=e.call(r,t)}catch(u){o=[6,u],n=0}finally{i=a=0}if(o[0]&5)throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}}var q=function(r){return r.toDataURL("image/png",1)},V=function(r){return typeof r=="function"},x=function(r){return r===void 0},Y=function(r){return typeof r=="string"},M=function(r,e,t){e===void 0&&(e={}),t===void 0&&(t="http://www.w3.org/2000/svg");var i=document.createElementNS(t,r);for(var n in e)i.setAttribute(n,e[n]);return i},K=function(r,e,t){for(var i=[],n="",a="",d=0,l=e.length;d<l;d++){if(a=e.charAt(d),a===`
`){i.push(n),n="";continue}n+=a,r.measureText(n).width>t&&(i.push(n.substring(0,n.length-1)),n="",d--)}return i.push(n),i},U=function(r,e){return S(void 0,void 0,void 0,function(){var t,i,n,a,d,l,s,o,u;return b(this,function(c){switch(c.label){case 0:return t=M("svg",{xmlns:"http://www.w3.org/2000/svg"}),i=document.createElement("div"),i.setAttribute("xmlns","http://www.w3.org/1999/xhtml"),i.style.cssText=`
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font: `.concat(r.font,`;
  color: `).concat(e.fontColor,`;
`),i.innerHTML="<div class='rich-text-content'>".concat(e.content,"</div>"),document.body.appendChild(i),[4,$(i)];case 1:return c.sent(),n=(u=i.querySelector(".rich-text-content"))===null||u===void 0?void 0:u.getBoundingClientRect(),a=n==null?void 0:n.width,d=n==null?void 0:n.height,document.body.removeChild(i),l=e.richTextWidth||a||e.width,s=e.richTextHeight||d||e.height,t.setAttribute("width",l.toString()),t.setAttribute("height",s.toString()),o=M("foreignObject",{width:l.toString(),height:s.toString()}),o.appendChild(i),t.appendChild(o),[2,{element:t,width:l,height:s}]}})})};function $(r){return S(this,void 0,void 0,function(){var e,t,i,n,a;return b(this,function(d){switch(d.label){case 0:e=r.querySelectorAll("img"),t=function(l){var s,o,u,c,f;return b(this,function(h){switch(h.label){case 0:if(s=l.getAttribute("src"),!s)return[3,6];h.label=1;case 1:return h.trys.push([1,5,,6]),[4,fetch(s)];case 2:return o=h.sent(),[4,o.blob()];case 3:return u=h.sent(),[4,new Promise(function(m,p){var y=new FileReader;y.onloadend=function(){return m(y.result)},y.onerror=p,y.readAsDataURL(u)})];case 4:return c=h.sent(),Y(c)&&l.setAttribute("src",c),[3,6];case 5:return f=h.sent(),console.error("Error converting ".concat(s," to base64:"),f),[3,6];case 6:return[2]}})},i=0,n=Array.from(e),d.label=1;case 1:return i<n.length?(a=n[i],[5,t(a)]):[3,4];case 2:d.sent(),d.label=3;case 3:return i++,[3,1];case 4:return[2]}})})}var J=function(r){var e=r.outerHTML.replace(/<(img|br|input|hr|embed)(.*?)>/g,"<$1$2/>").replace(/\n/g,"").replace(/\t/g,"").replace(/#/g,"%23");return"data:image/svg+xml;charset=utf-8,".concat(e)},v=function(r,e){return x(r)?e:r},B=function(r,e,t){e===void 0&&(e=void 0),t===void 0&&(t=void 0);var i=new Image;return i.setAttribute("crossOrigin","Anonymous"),!x(e)&&(i.width=e),!x(t)&&(i.height=t),i.src=r,new Promise(function(n){i.onload=function(){n(i)}})},Q=function(r,e,t){return Array.from({length:r},function(){return new Array(e).fill(t)})},G={width:300,height:300,rotate:45,layout:"default",auxiliaryLine:!1,translatePlacement:"middle",contentType:"text",content:"hello watermark-js-plus",textType:"fill",imageWidth:0,imageHeight:0,lineHeight:30,zIndex:2147483647,backgroundPosition:"0 0",backgroundRepeat:"repeat",fontSize:"20px",fontFamily:"sans-serif",fontStyle:"",fontVariant:"",fontColor:"#000",fontWeight:"normal",filter:"none",letterSpacing:"0px",wordSpacing:"0px",globalAlpha:.5,mode:"default",mutationObserve:!0,monitorProtection:!1,movable:!1,parent:"body",onSuccess:function(){},onBeforeDestroy:function(){},onDestroyed:function(){},onObserveError:function(){}},Z=function(r,e,t){var i=r.getContext("2d");if(i===null)throw new Error("get context error");i.font="".concat(e.fontStyle," ").concat(e.fontVariant," ").concat(e.fontWeight," ").concat(e.fontSize," ").concat(e.fontFamily),i.filter=e.filter,i.letterSpacing=e.letterSpacing,i.wordSpacing=e.wordSpacing,e!=null&&e.rotate&&(e.rotate=(360-e.rotate%360)*(Math.PI/180)),x(t.textRowMaxWidth)&&(e.textRowMaxWidth=e.width);var n={image:{rect:{width:e.imageWidth,height:e.imageHeight},position:{x:0,y:0}},textLine:{data:[],yOffsetValue:0},advancedStyleParams:{linear:{x0:0,x1:0},radial:{x0:0,y0:0,r0:0,x1:0,y1:0,r1:0},conic:{x:0,y:0,startAngle:0},pattern:{}}};switch(e.contentType){case"text":n.textLine.data=[e.content];break;case"multi-line-text":n.textLine.data=K(i,e.content,e.textRowMaxWidth);break}var a=e.width/2,d=e.height/2,l="middle",s="center";switch(!x(t==null?void 0:t.translateX)&&!x(t==null?void 0:t.translateY)?(a=t==null?void 0:t.translateX,d=t==null?void 0:t.translateY,l="top",s="left"):(n.advancedStyleParams.linear.x0=-e.width/2,n.advancedStyleParams.linear.x1=e.width/2,n.advancedStyleParams.radial.r0=0,n.advancedStyleParams.radial.r1=e.width/2),t.translatePlacement){case"top":a=e.width/2,d=0,l="top",n.advancedStyleParams.linear.x0=-e.width/2,n.advancedStyleParams.linear.x1=e.width/2,n.advancedStyleParams.radial.y0=n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.radial.y1=n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.conic.y=n.textLine.data.length*e.lineHeight/2;break;case"top-start":a=0,d=0,l="top",s="start",n.advancedStyleParams.linear.x0=0,n.advancedStyleParams.linear.x1=e.width,n.advancedStyleParams.radial.x0=e.width/2,n.advancedStyleParams.radial.y0=n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.radial.x1=e.width/2,n.advancedStyleParams.radial.y1=n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.conic.x=e.width/2,n.advancedStyleParams.conic.y=n.textLine.data.length*e.lineHeight/2;break;case"top-end":a=e.width,d=0,l="top",s="end",n.advancedStyleParams.linear.x0=0,n.advancedStyleParams.linear.x1=-e.width,n.advancedStyleParams.radial.x0=-e.width/2,n.advancedStyleParams.radial.y0=n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.radial.x1=-e.width/2,n.advancedStyleParams.radial.y1=n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.conic.x=-e.width/2,n.advancedStyleParams.conic.y=n.textLine.data.length*e.lineHeight/2;break;case"bottom":a=e.width/2,d=e.height,l="bottom",n.advancedStyleParams.linear.x0=-e.width/2,n.advancedStyleParams.linear.x1=e.width/2,n.advancedStyleParams.radial.y0=-n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.radial.y1=-n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.conic.x=0,n.advancedStyleParams.conic.y=-n.textLine.data.length*e.lineHeight/2;break;case"bottom-start":a=0,d=e.height,l="bottom",s="start",n.advancedStyleParams.linear.x0=0,n.advancedStyleParams.linear.x1=e.width,n.advancedStyleParams.radial.x0=e.width/2,n.advancedStyleParams.radial.y0=-n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.radial.x1=e.width/2,n.advancedStyleParams.radial.y1=-n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.conic.x=e.width/2,n.advancedStyleParams.conic.y=-n.textLine.data.length*e.lineHeight/2;break;case"bottom-end":a=e.width,d=e.height,l="bottom",s="end",n.advancedStyleParams.linear.x0=0,n.advancedStyleParams.linear.x1=-e.width,n.advancedStyleParams.radial.x0=-e.width/2,n.advancedStyleParams.radial.y0=-n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.radial.x1=-e.width/2,n.advancedStyleParams.radial.y1=-n.textLine.data.length*e.lineHeight/2,n.advancedStyleParams.conic.x=-e.width/2,n.advancedStyleParams.conic.y=-n.textLine.data.length*e.lineHeight/2;break;case"left":a=0,d=e.height/2,s="start",n.advancedStyleParams.linear.x0=0,n.advancedStyleParams.linear.x1=e.width,n.advancedStyleParams.radial.x0=e.width/2,n.advancedStyleParams.radial.x1=e.width/2,n.advancedStyleParams.conic.x=e.width/2,n.advancedStyleParams.conic.y=0;break;case"right":a=e.width,d=e.height/2,s="end",n.advancedStyleParams.linear.x0=0,n.advancedStyleParams.linear.x1=-e.width,n.advancedStyleParams.radial.x0=-e.width/2,n.advancedStyleParams.radial.x1=-e.width/2,n.advancedStyleParams.conic.x=-e.width/2,n.advancedStyleParams.conic.y=0;break}if(e.translateX=a,e.translateY=d,x(t==null?void 0:t.textBaseline)&&(e.textBaseline=l),x(t==null?void 0:t.textAlign)&&(e.textAlign=s),["text","multi-line-text"].includes(e.contentType))switch(e.textBaseline){case"middle":n.textLine.yOffsetValue=(n.textLine.data.length-1)*e.lineHeight/2;break;case"bottom":case"alphabetic":case"ideographic":n.textLine.yOffsetValue=(n.textLine.data.length-1)*e.lineHeight+(e.lineHeight-parseInt(e.fontSize))/2;break;case"top":case"hanging":n.textLine.yOffsetValue=-e.lineHeight/2+parseInt(e.fontSize)/2;break}return n},z=function(r){typeof window!="undefined"&&r&&(Object.defineProperty(window,"MutationObserver",{writable:!1,configurable:!1}),Object.defineProperty(window,"requestAnimationFrame",{writable:!1,configurable:!1}))},C=function(){function r(e,t){this.props=e,this.options=t,this.canvas=r.createCanvas(this.options.width,this.options.height),this.recommendOptions=Z(this.canvas,this.options,this.props)}return r.createCanvas=function(e,t){var i,n=window.devicePixelRatio||1,a=document.createElement("canvas");return a.width=e*n,a.height=t*n,a.style.width="".concat(e,"px"),a.style.height="".concat(t,"px"),(i=a.getContext("2d"))===null||i===void 0||i.setTransform(n,0,0,n,0,0),a},r.clearCanvas=function(e){var t=e.getContext("2d");if(t===null)throw new Error("get context error");t.restore(),t.resetTransform(),t.clearRect(0,0,e.width,e.height);var i=window.devicePixelRatio||1;t.setTransform(i,0,0,i,0,0)},r.prototype.getCanvas=function(){return this.canvas},r.prototype.clear=function(){r.clearCanvas(this.canvas)},r.prototype.draw=function(){var e=this,t=this.canvas.getContext("2d");if(t===null)throw new Error("get context error");return this.options.auxiliaryLine&&(t.beginPath(),t.rect(0,0,this.options.width,this.options.height),t.lineWidth=1,t.strokeStyle="#000",t.stroke(),t.closePath(),t.beginPath(),t.rect(this.options.translateX,this.options.translateY,1,1),t.lineWidth=1,t.strokeStyle="#f00",t.stroke(),t.closePath()),this.setStyle(t),t.save(),t.translate(this.options.translateX,this.options.translateY),t.rotate(this.options.rotate),new Promise(function(i){switch(e.options.contentType){case"text":e.drawText(t,i);break;case"image":e.drawImage(t,i);break;case"multi-line-text":e.drawMultiLineText(t,i);break;case"rich-text":e.drawRichText(t,i);break}})},r.prototype.setStyle=function(e){var t,i="fillStyle";this.options.textType==="stroke"&&(i="strokeStyle");var n=this.options.fontColor;if(!((t=this.options)===null||t===void 0)&&t.advancedStyle)switch(this.options.advancedStyle.type){case"linear":n=this.createLinearGradient(e);break;case"radial":n=this.createRadialGradient(e);break;case"conic":n=this.createConicGradient(e);break;case"pattern":n=this.createPattern(e);break}e[i]&&n&&(e[i]=n),this.options.textAlign&&(e.textAlign=this.options.textAlign),this.options.textBaseline&&(e.textBaseline=this.options.textBaseline),e.globalAlpha=this.options.globalAlpha,this.options.shadowStyle&&(e.shadowBlur=v(this.options.shadowStyle.shadowBlur,0),e.shadowColor=v(this.options.shadowStyle.shadowColor,"#00000000"),e.shadowOffsetX=v(this.options.shadowStyle.shadowOffsetX,0),e.shadowOffsetY=v(this.options.shadowStyle.shadowOffsetY,0)),V(this.options.extraDrawFunc)&&this.options.extraDrawFunc(e)},r.prototype.createLinearGradient=function(e){var t,i,n,a,d,l,s,o,u,c,f,h,m,p,y,w=e.createLinearGradient(v((n=(i=(t=this.options.advancedStyle)===null||t===void 0?void 0:t.params)===null||i===void 0?void 0:i.linear)===null||n===void 0?void 0:n.x0,this.recommendOptions.advancedStyleParams.linear.x0),v((l=(d=(a=this.options.advancedStyle)===null||a===void 0?void 0:a.params)===null||d===void 0?void 0:d.linear)===null||l===void 0?void 0:l.y0,0),v((u=(o=(s=this.options.advancedStyle)===null||s===void 0?void 0:s.params)===null||o===void 0?void 0:o.linear)===null||u===void 0?void 0:u.x1,this.recommendOptions.advancedStyleParams.linear.x1),v((h=(f=(c=this.options.advancedStyle)===null||c===void 0?void 0:c.params)===null||f===void 0?void 0:f.linear)===null||h===void 0?void 0:h.y1,0));return(y=(p=(m=this.options)===null||m===void 0?void 0:m.advancedStyle)===null||p===void 0?void 0:p.colorStops)===null||y===void 0||y.forEach(function(g){w.addColorStop(g.offset,g.color)}),w},r.prototype.createConicGradient=function(e){var t,i,n,a,d,l,s,o,u,c,f,h,m,p,y,w=e.createConicGradient(v((a=(n=(i=(t=this.options)===null||t===void 0?void 0:t.advancedStyle)===null||i===void 0?void 0:i.params)===null||n===void 0?void 0:n.conic)===null||a===void 0?void 0:a.startAngle,0),v((o=(s=(l=(d=this.options)===null||d===void 0?void 0:d.advancedStyle)===null||l===void 0?void 0:l.params)===null||s===void 0?void 0:s.conic)===null||o===void 0?void 0:o.x,this.recommendOptions.advancedStyleParams.conic.x),v((h=(f=(c=(u=this.options)===null||u===void 0?void 0:u.advancedStyle)===null||c===void 0?void 0:c.params)===null||f===void 0?void 0:f.conic)===null||h===void 0?void 0:h.y,this.recommendOptions.advancedStyleParams.conic.y));return(y=(p=(m=this.options)===null||m===void 0?void 0:m.advancedStyle)===null||p===void 0?void 0:p.colorStops)===null||y===void 0||y.forEach(function(g){w.addColorStop(g.offset,g.color)}),w},r.prototype.createRadialGradient=function(e){var t,i,n,a,d,l,s,o,u,c,f,h,m,p,y,w,g,k,P,O,E,L,T,A,D,W,H,I=e.createRadialGradient(v((a=(n=(i=(t=this.options)===null||t===void 0?void 0:t.advancedStyle)===null||i===void 0?void 0:i.params)===null||n===void 0?void 0:n.radial)===null||a===void 0?void 0:a.x0,this.recommendOptions.advancedStyleParams.radial.x0),v((o=(s=(l=(d=this.options)===null||d===void 0?void 0:d.advancedStyle)===null||l===void 0?void 0:l.params)===null||s===void 0?void 0:s.radial)===null||o===void 0?void 0:o.y0,this.recommendOptions.advancedStyleParams.radial.y0),v((h=(f=(c=(u=this.options)===null||u===void 0?void 0:u.advancedStyle)===null||c===void 0?void 0:c.params)===null||f===void 0?void 0:f.radial)===null||h===void 0?void 0:h.r0,this.recommendOptions.advancedStyleParams.radial.r0),v((w=(y=(p=(m=this.options)===null||m===void 0?void 0:m.advancedStyle)===null||p===void 0?void 0:p.params)===null||y===void 0?void 0:y.radial)===null||w===void 0?void 0:w.x1,this.recommendOptions.advancedStyleParams.radial.x1),v((O=(P=(k=(g=this.options)===null||g===void 0?void 0:g.advancedStyle)===null||k===void 0?void 0:k.params)===null||P===void 0?void 0:P.radial)===null||O===void 0?void 0:O.y1,this.recommendOptions.advancedStyleParams.radial.y1),v((A=(T=(L=(E=this.options)===null||E===void 0?void 0:E.advancedStyle)===null||L===void 0?void 0:L.params)===null||T===void 0?void 0:T.radial)===null||A===void 0?void 0:A.r1,this.recommendOptions.advancedStyleParams.radial.r1));return(H=(W=(D=this.options)===null||D===void 0?void 0:D.advancedStyle)===null||W===void 0?void 0:W.colorStops)===null||H===void 0||H.forEach(function(j){I.addColorStop(j.offset,j.color)}),I},r.prototype.createPattern=function(e){var t,i,n,a,d,l,s,o;return e.createPattern((a=(n=(i=(t=this.options)===null||t===void 0?void 0:t.advancedStyle)===null||i===void 0?void 0:i.params)===null||n===void 0?void 0:n.pattern)===null||a===void 0?void 0:a.image,((o=(s=(l=(d=this.options)===null||d===void 0?void 0:d.advancedStyle)===null||l===void 0?void 0:l.params)===null||s===void 0?void 0:s.pattern)===null||o===void 0?void 0:o.repetition)||"")},r.prototype.setText=function(e,t){var i="fillText";this.options.textType==="stroke"&&(i="strokeText"),e[i]&&e[i](t.text,t.x,t.y,t.maxWidth)},r.prototype.drawText=function(e,t){this.setText(e,{text:this.options.content,x:0,y:0-this.recommendOptions.textLine.yOffsetValue,maxWidth:this.options.textRowMaxWidth||this.options.width}),t(e.canvas)},r.prototype.drawImage=function(e,t){var i=this;B(this.options.image).then(function(n){var a=i.getImageRect(n),d=a.width,l=a.height,s=i.getDrawImagePosition(d,l);e.drawImage(n,s.x,s.y,d,l),t(e.canvas)})},r.prototype.drawMultiLineText=function(e,t){var i=this,n=this.recommendOptions.textLine.data,a=this.recommendOptions.textLine.yOffsetValue;n.forEach(function(d,l){i.setText(e,{text:d,x:0,y:i.options.lineHeight*l-a,maxWidth:i.options.textRowMaxWidth||i.options.width})}),t(e.canvas)},r.prototype.drawRichText=function(e,t){return S(this,void 0,void 0,function(){var i,n=this;return b(this,function(a){switch(a.label){case 0:return[4,U(e,this.options)];case 1:return i=a.sent(),B(J(i.element),i.width,i.height).then(function(d){var l=n.getDrawImagePosition(d.width,d.height);e.drawImage(d,l.x,l.y,d.width,d.height),t(e.canvas)}),[2]}})})},r.prototype.getImageRect=function(e){var t={width:this.options.imageWidth||0,height:this.options.imageHeight||0};switch(!0){case(t.width!==0&&t.height===0):t.height=t.width*e.height/e.width;break;case(t.width===0&&t.height!==0):t.width=t.height*e.width/e.height;break;case(t.width===0&&t.height===0):t.width=e.width,t.height=e.height;break}return t},r.prototype.getDrawImagePosition=function(e,t){var i,n,a={x:-e/2,y:-t/2};switch(this.options.translatePlacement){case"top":a.x=-e/2,a.y=0;break;case"top-start":a.x=0,a.y=0;break;case"top-end":a.x=-e,a.y=0;break;case"bottom":a.x=-e/2,a.y=-t;break;case"bottom-start":a.x=0,a.y=-t;break;case"bottom-end":a.x=-e,a.y=-t;break;case"left":a.x=0,a.y=-t/2;break;case"right":a.x=-e,a.y=-t/2;break}return!x((i=this.props)===null||i===void 0?void 0:i.translateX)&&(a.x=0),!x((n=this.props)===null||n===void 0?void 0:n.translateY)&&(a.y=0),a},r}(),ee=function(){function r(e,t){var i,n,a,d;this.options=e,this.partialWidth=this.options.width,this.partialHeight=this.options.height,this.rows=((i=this.options.gridLayoutOptions)===null||i===void 0?void 0:i.rows)||1,this.cols=((n=this.options.gridLayoutOptions)===null||n===void 0?void 0:n.cols)||1,this.matrix=((a=this.options.gridLayoutOptions)===null||a===void 0?void 0:a.matrix)||Q(this.rows,this.cols,1),this.gap=((d=this.options.gridLayoutOptions)===null||d===void 0?void 0:d.gap)||[0,0],this.partialCanvas=t}return r.prototype.draw=function(){var e,t,i,n,a,d,l,s,o=C.createCanvas(((e=this.options.gridLayoutOptions)===null||e===void 0?void 0:e.width)||this.partialWidth*this.cols+this.gap[0]*this.cols,((t=this.options.gridLayoutOptions)===null||t===void 0?void 0:t.height)||this.partialHeight*this.rows+this.gap[1]*this.rows),u=o.getContext("2d");!((i=this.options.gridLayoutOptions)===null||i===void 0)&&i.backgroundImage&&(u==null||u.drawImage((n=this.options.gridLayoutOptions)===null||n===void 0?void 0:n.backgroundImage,0,0,(a=this.options.gridLayoutOptions)===null||a===void 0?void 0:a.width,(d=this.options.gridLayoutOptions)===null||d===void 0?void 0:d.height));for(var c=0;c<this.rows;c++)for(var f=0;f<this.cols;f++)!((s=(l=this.matrix)===null||l===void 0?void 0:l[c])===null||s===void 0)&&s[f]&&(u==null||u.drawImage(this.partialCanvas,this.partialWidth*f+this.gap[0]*f,this.partialHeight*c+this.gap[1]*c,this.partialWidth,this.partialHeight));return o},r}(),te=function(r,e){switch(r.layout){case"grid":return new ee(r,e).draw();default:return e}},ne=function(r){var e,t,i;switch(r.layout){case"grid":{var n=((e=r.gridLayoutOptions)===null||e===void 0?void 0:e.cols)||1,a=((t=r.gridLayoutOptions)===null||t===void 0?void 0:t.rows)||1,d=((i=r.gridLayoutOptions)===null||i===void 0?void 0:i.gap)||[0,0];return[r.width*n+d[0]*n,r.height*a+d[1]*a]}default:return[r.width,r.height]}},ie=function(){function r(e){e===void 0&&(e={}),this.parentElement=document.body,this.isCreating=!1,this.props=e,this.options=_(_({},G),e),this.changeParentElement(this.options.parent),this.watermarkCanvas=new C(this.props,this.options),z(this.options.monitorProtection)}return r.prototype.changeOptions=function(){return S(this,arguments,void 0,function(e,t,i){return e===void 0&&(e={}),t===void 0&&(t="overwrite"),i===void 0&&(i=!0),b(this,function(n){switch(n.label){case 0:return this.initConfigData(e,t),z(this.options.monitorProtection),i?(this.remove(),[4,this.create()]):[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}})})},r.prototype.create=function(){return S(this,void 0,void 0,function(){var e,t,i,n,a,d,l,s,o,u,c,f;return b(this,function(h){switch(h.label){case 0:return this.isCreating?[2]:(this.isCreating=!0,this.validateUnique()?this.validateContent()?(e=x(this.watermarkDom),[4,(d=this.watermarkCanvas)===null||d===void 0?void 0:d.draw()]):(this.isCreating=!1,[2]):(this.isCreating=!1,[2]));case 1:if(h.sent(),this.layoutCanvas=te(this.options,(l=this.watermarkCanvas)===null||l===void 0?void 0:l.getCanvas()),t=q(this.layoutCanvas),(s=this.watermarkCanvas)===null||s===void 0||s.clear(),this.watermarkDom=document.createElement("div"),i=document.createElement("div"),this.watermarkDom.__WATERMARK__="watermark",this.watermarkDom.__WATERMARK__INSTANCE__=this,n=this.checkParentElementType(),this.watermarkDom.style.cssText=`
      z-index:`.concat(this.options.zIndex,`!important;display:block!important;visibility:visible!important;transform:none!important;scale:none!important;
      `).concat(n==="custom"?"top:0!important;bottom:0!important;left:0!important;right:0!important;height:100%!important;pointer-events:none!important;position:absolute!important;":"position:relative!important;",`
    `),a=ne(this.options),i.style.cssText=`
      display:block!important;visibility:visible!important;pointer-events:none;top:0;bottom:0;left:0;right:0;transform:none!important;scale:none!important;
      position:`.concat(n==="root"?"fixed":"absolute",`!important;-webkit-print-color-adjust:exact!important;width:100%!important;height:100%!important;
      z-index:`).concat(this.options.zIndex,"!important;background-image:url(").concat(t,")!important;background-repeat:").concat(this.options.backgroundRepeat,`!important;
      background-size:`).concat(a[0],"px ").concat(a[1],"px!important;background-position:").concat(this.options.backgroundPosition,`;
      `).concat(this.options.movable?"animation: 200s ease 0s infinite normal none running watermark !important;":"",`
    `),this.watermarkDom.appendChild(i),this.parentElement.appendChild(this.watermarkDom),this.options.mutationObserve)try{this.bindMutationObserve()}catch(m){(u=(o=this.options).onObserveError)===null||u===void 0||u.call(o)}return e&&((f=(c=this.options).onSuccess)===null||f===void 0||f.call(c)),this.isCreating=!1,[2]}})})},r.prototype.destroy=function(){this.remove(),this.watermarkDom=void 0},r.prototype.check=function(){return S(this,void 0,void 0,function(){return b(this,function(e){return[2,this.parentElement.contains(this.watermarkDom)]})})},r.prototype.remove=function(){var e,t,i,n,a,d,l,s;(t=(e=this.options).onBeforeDestroy)===null||t===void 0||t.call(e),(i=this.observer)===null||i===void 0||i.disconnect(),(n=this.parentObserve)===null||n===void 0||n.disconnect(),this.unbindCheckWatermarkElementEvent(),(d=(a=this.watermarkDom)===null||a===void 0?void 0:a.parentNode)===null||d===void 0||d.removeChild(this.watermarkDom),(s=(l=this.options).onDestroyed)===null||s===void 0||s.call(l)},r.prototype.initConfigData=function(e,t){var i=this;t===void 0&&(t="overwrite"),t==="append"?Object.keys(e).forEach(function(n){i.props&&(i.props[n]=e[n])}):this.props=e,this.options=_(_({},G),this.props),this.changeParentElement(this.options.parent),this.watermarkCanvas=new C(this.props,this.options)},r.prototype.changeParentElement=function(e){if(typeof e=="string"){var t=document.querySelector(e);t&&(this.parentElement=t)}else this.parentElement=e;this.parentElement||console.error("[WatermarkJsPlus]: please pass a valid parent element.")},r.prototype.validateUnique=function(){var e=!0;return Array.from(this.parentElement.childNodes).forEach(function(t){e&&Object.hasOwnProperty.call(t,"__WATERMARK__")&&(e=!1)}),e},r.prototype.validateContent=function(){switch(this.options.contentType){case"image":return Object.hasOwnProperty.call(this.options,"image");case"multi-line-text":case"rich-text":case"text":return this.options.content.length>0}},r.prototype.checkParentElementType=function(){return["html","body"].includes(this.parentElement.tagName.toLocaleLowerCase())?"root":"custom"},r.prototype.checkWatermarkElement=function(){return S(this,void 0,void 0,function(){return b(this,function(e){switch(e.label){case 0:return this.parentElement.contains(this.watermarkDom)?[3,2]:(this.remove(),[4,this.create()]);case 1:e.sent(),e.label=2;case 2:return this.bindCheckWatermarkElementEvent(),[2]}})})},r.prototype.bindMutationObserve=function(){var e=this;this.watermarkDom&&(this.bindCheckWatermarkElementEvent(),this.observer=new MutationObserver(function(t){return S(e,void 0,void 0,function(){return b(this,function(i){switch(i.label){case 0:return t.length>0?(this.remove(),[4,this.create()]):[3,2];case 1:i.sent(),i.label=2;case 2:return[2]}})})}),this.observer.observe(this.watermarkDom,{attributes:!0,childList:!0,subtree:!0,characterData:!0}),this.parentObserve=new MutationObserver(function(t){return S(e,void 0,void 0,function(){var i,n,a,d;return b(this,function(l){switch(l.label){case 0:i=0,n=t,l.label=1;case 1:return i<n.length?(a=n[i],(a==null?void 0:a.target)===this.watermarkDom||((d=a==null?void 0:a.removedNodes)===null||d===void 0?void 0:d[0])===this.watermarkDom||a.type==="childList"&&a.target===this.parentElement&&a.target.lastChild!==this.watermarkDom?(this.remove(),[4,this.create()]):[3,3]):[3,4];case 2:l.sent(),l.label=3;case 3:return i++,[3,1];case 4:return[2]}})})}),this.parentObserve.observe(this.parentElement,{attributes:!0,childList:!0,subtree:!0,characterData:!0}))},r.prototype.bindCheckWatermarkElementEvent=function(){this.unbindCheckWatermarkElementEvent(),this.checkWatermarkElementRequestID=requestAnimationFrame(this.checkWatermarkElement.bind(this))},r.prototype.unbindCheckWatermarkElementEvent=function(){x(this.checkWatermarkElementRequestID)||cancelAnimationFrame(this.checkWatermarkElementRequestID)},r}();(function(r){X(e,r);function e(t){t===void 0&&(t={});var i={globalAlpha:.005,mode:"blind"};return r.call(this,_(_({},t),i))||this}return e.decode=function(t){var i=t.url,n=i===void 0?"":i,a=t.fillColor,d=a===void 0?"#000":a,l=t.compositeOperation,s=l===void 0?"color-burn":l,o=t.mode,u=o===void 0?"canvas":o,c=t.compositeTimes,f=c===void 0?3:c,h=t.onSuccess;if(n&&u==="canvas"){var m=new Image;m.src=n,m.addEventListener("load",function(){var p=m.width,y=m.height,w=C.createCanvas(p,y),g=w.getContext("2d");if(!g)throw new Error("get context error");g.drawImage(m,0,0,p,y),g.globalCompositeOperation=s,g.fillStyle=d;for(var k=0;k<f;k++)g.fillRect(0,0,p,y);var P=q(w);V(h)&&(h==null||h(P))})}},e})(ie);export{ie as Watermark};
