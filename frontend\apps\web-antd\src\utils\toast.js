import { message } from "ant-design-vue";

export function showLoading(content) {
  return message.loading({content:content,key:'global'});
}


export function showSuccess(content) {
  return message.success({content:content,key:'global'});
}

export function showError(content) {
  return message.error({content:content,key:'global'});
}

export function showWarn(content) {
  return message.warn({content:content,key:'global'});
}
