const categoryOptions = [
  { label: '城市级', value: '城市级' },
  { label: '地形级', value: '地形级' },
  { label: '部件级', value: '部件级' },
];

const datasetTypeOptions = [
  { label: 'OSGB', value: 'osgb',fileType:'folder' },
  { label: '3DTiles', value: 'tiles',fileType:'folder'},
  { label: 'DEM', value: 'dem' ,fileType:'folder'},
  { label: 'DOM', value: 'dom' ,fileType:'folder'},
  { label: 'OBJ', value: 'obj' ,fileType:'folder'},
];

export const fileFormatOptions = {
 osgb: [{ label: 'OSGB', value: 'osgb'}],
 tiles : [{ label: 'tiles', value: 'tiles'}],
 dem : [{ label: 'tif', value: 'tif'},{ label: 'img', value: 'img'}],
 dom:[{ label: 'tif', value: 'tif'},{ label: 'img', value: 'img'}],
 vector:[{ label: 'shp', value: 'shp'},{ label: 'gdb', value: 'gdb'}],
  obj: [{ label: 'OBJ', value: 'obj'}],
};

const adminRegionOptions = [
  { label: '武夷山市', value: '350782000000' },
  { label: '崇安街道', value: '350782001000' },
  { label: '新丰街道', value: '350782002000' },
  { label: '武夷街道', value: '350782003000' },
  { label: '星村镇', value: '350782100000' },
  { label: '兴田镇', value: '350782101000' },
  { label: '五夫镇', value: '350782102000' },
  { label: '上梅乡', value: '350782200000' },
  { label: '吴屯乡', value: '350782201000' },
  { label: '岚谷乡', value: '350782202000' },
  { label: '洋庄乡', value: '350782203000' },
];

export function getRegionNameByCode(row) {
  let code = row.regionCode;
  for(let i=0; i<adminRegionOptions.length; i++) {
    if(adminRegionOptions[i].value == code) {
      return adminRegionOptions[i].label;
    }
  }
  return '';
}

export function getUploadStatusLabel(row) {
  let uploadStatus = row.uploadStatus;
  for(let i=0; i<statusOptions.length; i++) {
    if(statusOptions[i].value === uploadStatus) {
      return statusOptions[i].label;
    }
  }
  return '';
}

const createTimeOptions = [
];

let nowYear = new Date().getFullYear();
for(let i=nowYear; i>2000; i--) {
  createTimeOptions.push( { label: ''+i, value: ''+i })
}

const statusOptions = [
  { label: '待上传', value: 0 },
  { label: '已上传', value: 1 },
];

export const columns = [
  {
    title: '数据集名称',
    dataIndex: 'layerName',
    field: 'layerName',
    width: 180,
  },
  {
    title: '类别',
    dataIndex: 'dataType',
    field: 'dataType',
    width: 120,
  },

  // {
  //   title: '数据量(M)',
  //   dataIndex: 'dataSize',
  //   field: 'dataSize',
  //   width: 100,
  // },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 160,
  },
];

export const layerQueryColumns = [
  { align: 'left', title: 'Name', type: 'checkbox', width: 290 },
  // {
  //   title: '图层名称',
  //   dataIndex: 'layerName',
  //   field: 'layerName',
  //   width: 180,
  // },
  // {
  //   title: '数据集名称',
  //   dataIndex: 'collectionName',
  //   field: 'collectionName',
  //   width: 180,
  // },
];

export const searchFormSchema = [
  {
    label: '数据集名称',
    fieldName: 'name',
    component: 'Input',
  },
  // {
  //   label: '类别',
  //   fieldName: 'category',
  //   component: 'Select',
  //   componentProps: {
  //     options: categoryOptions,
  //   },
  // },
  {
    label: '数据类型',
    fieldName: 'dataType',
    component: 'Select',
    componentProps: {
      options: datasetTypeOptions,
    },
  },
  {
    label: '行政区划',
    fieldName: 'regionCode',
    component: 'Select',
    componentProps: {
      options: adminRegionOptions,
    },
  },
  {
    label: '数据提供者',
    fieldName: 'provider',
    component: 'Input',
  },
  {
    label: '建设年份',
    fieldName: 'dataYear',
    component: 'Select',
    componentProps: {
      options: createTimeOptions,
    },
  },
  {
    label: '状态',
    fieldName: 'uploadStatus',
    component: 'Select',
    componentProps: {
      options: statusOptions,
    },

  },
];

export const formSchema = [
  {
    fieldName: 'id',
    label: '主键',
    component: 'Input',
    dependencies: {
      show:false,
      triggerFields: ['upload'],
    },
  },
  {
    fieldName: 'name',
    label: '数据集名称',
    rules: 'required',
    component: 'Input',
    componentProps: {
      placeholder: '请输入数据集名称',
    },
    dependencies: {
      show(values) {
        return !values.upload;
      },
      triggerFields: ['upload'],
    },
  },
  {
    label: '数据类型',
    fieldName: 'dataType',
    component: 'Select',
    componentProps: {
      options: datasetTypeOptions,
    },
    rules: 'selectRequired',
  },
  // {
  //   label: '类别',
  //   fieldName: 'category',
  //   component: 'Select',
  //   componentProps: {
  //     options: categoryOptions,
  //   },
  //   rules: 'selectRequired',
  // },
  {
    label: '建设年份',
    fieldName: 'dataYear',
    component: 'Select',
    componentProps: {
      options: createTimeOptions,
    },
    rules: 'selectRequired',
  },
  {
    label: '行政区划',
    fieldName: 'regionCode',
    component: 'Select',
    componentProps: {
      options: adminRegionOptions,
    },
    rules: 'selectRequired',
  },
  {
    label: '数据提供者',
    fieldName: 'provider',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '文件格式',
    fieldName: 'fileFormat',
    component: 'Select',
    componentProps: {
      options: fileFormatOptions.osgb,
      placeholder: '请选择',
    },
    dependencies: {
      show(values) {
        return !(values.id != null && values.id.length > 0);
      },
      componentProps(values) {
        if(values.dataType == null || values.dataType.length == 0) {
          return {
            options: fileFormatOptions.osgb,
            placeholder: '请选择',
          };
        }
        values.fileFormat = '';
        return {
          options: fileFormatOptions[values.dataType],
          placeholder: '请选择',
        };
      },
      triggerFields: ['dataType'],
    },
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: [
        {
          label: '目录',
          value: '1',
        },
        {
          label: '文件',
          value: '2',
        },
      ],
    },
    fieldName: 'uploadType',
    label: '',
  },
  {
    fieldName: 'fileUpload',
    label: '',
    component: 'Upload',

  },
];
