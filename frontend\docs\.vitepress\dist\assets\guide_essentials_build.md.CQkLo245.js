import{_ as s}from"./chunks/report.Dsxn5c6D.js";import{ao as i,k as a,aP as n,l as h,ay as l,j as p}from"./chunks/framework.C8U7mBUf.js";const t=JSON.parse('{"title":"构建与部署","description":"","frontmatter":{},"headers":[],"relativePath":"guide/essentials/build.md","filePath":"guide/essentials/build.md"}');const e=i({name:"guide/essentials/build.md"},[["render",function(i,t,e,k,d,r){const g=l("NolebaseGitContributors"),o=l("NolebaseGitChangelog");return p(),a("div",null,[t[0]||(t[0]=n('<h1 id="构建与部署" tabindex="-1">构建与部署 <a class="header-anchor" href="#构建与部署" aria-label="Permalink to &quot;构建与部署&quot;">​</a></h1><div class="tip custom-block"><p class="custom-block-title">前言</p><p>由于是展示项目，所以打包后相对较大，如果项目中没有用到的插件，可以删除对应的文件或者路由，不引用即可，没有引用就不会打包。</p></div><h2 id="构建" tabindex="-1">构建 <a class="header-anchor" href="#构建" aria-label="Permalink to &quot;构建&quot;">​</a></h2><p>项目开发完成之后，执行以下命令进行构建：</p><p><strong>注意：</strong> 请在项目根目录下执行以下命令</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> build</span></span></code></pre></div><p>构建打包成功之后，会在根目录生成对应的应用下的 <code>dist</code> 文件夹，里面就是构建打包好的文件，例如: <code>apps/web-antd/dist/</code></p><h2 id="预览" tabindex="-1">预览 <a class="header-anchor" href="#预览" aria-label="Permalink to &quot;预览&quot;">​</a></h2><p>发布之前可以在本地进行预览，有多种方式，这里介绍两种：</p><ul><li>使用项目自定的命令进行预览(推荐)</li></ul><p><strong>注意：</strong> 请在项目根目录下执行以下命令</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> preview</span></span></code></pre></div><p>等待构建成功后，访问 <code>http://localhost:4173</code> 即可查看效果。</p><ul><li>本地服务器预览</li></ul><p>可以在电脑全局安装 <code>serve</code> 服务，如 <code>live-server</code>,</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">npm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> i</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> -g</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> live-server</span></span></code></pre></div><p>然后在 <code>dist</code> 目录下执行 <code>live-server</code> 命令，即可在本地查看效果。</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">cd</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> apps/web-antd/dist</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 本地预览，默认端口8080</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">live-server</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 指定端口</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">live-server</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> --port</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 9000</span></span></code></pre></div><h2 id="压缩" tabindex="-1">压缩 <a class="header-anchor" href="#压缩" aria-label="Permalink to &quot;压缩&quot;">​</a></h2><h3 id="开启-gzip-压缩" tabindex="-1">开启 <code>gzip</code> 压缩 <a class="header-anchor" href="#开启-gzip-压缩" aria-label="Permalink to &quot;开启 `gzip` 压缩&quot;">​</a></h3><p>需要在打包的时候更改<code>.env.production</code>配置:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_COMPRESS</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">gzip</span></span></code></pre></div><h3 id="开启-brotli-压缩" tabindex="-1">开启 <code>brotli</code> 压缩 <a class="header-anchor" href="#开启-brotli-压缩" aria-label="Permalink to &quot;开启 `brotli` 压缩&quot;">​</a></h3><p>需要在打包的时候更改<code>.env.production</code>配置:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_COMPRESS</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">brotli</span></span></code></pre></div><h3 id="同时开启-gzip-和-brotli-压缩" tabindex="-1">同时开启 <code>gzip</code> 和 <code>brotli</code> 压缩 <a class="header-anchor" href="#同时开启-gzip-和-brotli-压缩" aria-label="Permalink to &quot;同时开启 `gzip` 和 `brotli` 压缩&quot;">​</a></h3><p>需要在打包的时候更改<code>.env.production</code>配置:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_COMPRESS</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">gzip,brotli</span></span></code></pre></div><div class="tip custom-block"><p class="custom-block-title">提示</p><p><code>gzip</code> 和 <code>brotli</code> 都需要安装特定模块才能使用。</p></div><details class="details custom-block"><summary>gzip 与 brotli 在 nginx 内的配置</summary><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">http</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # 开启gzip</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> on</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # 开启gzip_static</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # gzip_static 开启后可能会报错，需要安装相应的模块, 具体安装方式可以自行查询</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # 只有这个开启，vue文件打包的.gz文件才会有效果，否则不需要开启gzip进行打包</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_static</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> on</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_proxied</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> any</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_min_length</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> 1k</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_buffers</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 4</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> 16k</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  #如果nginx中使用了多层代理 必须设置这个才可以开启gzip。</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_http_version</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 1.0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_comp_level</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_types</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/plain</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/x-javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/css</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/xml</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/x-httpd-php</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> image/jpeg</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> image/gif</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> image/png</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_vary</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> off</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_disable</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;MSIE [1-6]\\.&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # 开启 brotli压缩</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # 需要安装对应的nginx模块,具体安装方式可以自行查询</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # 可以与gzip共存不会冲突</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  brotli</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> on</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  brotli_comp_level</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 6</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  brotli_buffers</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 16</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> 8k</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  brotli_min_length</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 20</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  brotli_types</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/plain</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/css</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/json</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/x-javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/xml</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/xml</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/xml+rss</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> image/svg+xml</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div></details><h2 id="构建分析" tabindex="-1">构建分析 <a class="header-anchor" href="#构建分析" aria-label="Permalink to &quot;构建分析&quot;">​</a></h2><p>如果你的构建文件很大，可以通过项目内置 <a href="https://github.com/doesdev/rollup-plugin-analyzer" target="_blank" rel="noreferrer">rollup-plugin-analyzer</a> 插件进行代码体积分析，从而优化你的代码。只需要在<code>根目录</code>下执行以下命令：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> run</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> build:analyze</span></span></code></pre></div><p>运行之后，在自动打开的页面可以看到具体的体积分布，以分析哪些依赖有问题。</p><p><img src="'+s+'" alt="Build analysis report"></p><h2 id="部署" tabindex="-1">部署 <a class="header-anchor" href="#部署" aria-label="Permalink to &quot;部署&quot;">​</a></h2><p>简单的部署只需要将最终生成的静态文件，dist 文件夹的静态文件发布到你的 cdn 或者静态服务器即可，需要注意的是其中的 index.html 通常会是你后台服务的入口页面，在确定了 js 和 css 的静态之后可能需要改变页面的引入路径。</p><p>例如上传到 nginx 服务器，可以将 dist 文件夹下的文件上传到服务器的 <code>/srv/www/project/index.html</code> 目录下，然后访问配置好的域名即可。</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># nginx配置</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">location</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # 不缓存html，防止程序更新后缓存继续生效</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  if</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> ($request_filename </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">~*</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> .</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">*</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\.</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">:htm</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">|</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">html</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">$</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    add_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Cache-Control</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;private, no-store, no-cache, must-revalidate, proxy-revalidate&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    access_log</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> on</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # 这里是vue打包文件dist内的文件的存放路径</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  root</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">   /srv/www/project/</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  index</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">  index.html</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> index.htm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p>部署时可能会发现资源路径不对，只需要修改<code>.env.production</code>文件即可。</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 根据自己路径来配置更改</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 注意需要以 / 开头和结尾</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_BASE</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_BASE</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/xxx/</span></span></code></pre></div><h3 id="前端路由与服务端的结合" tabindex="-1">前端路由与服务端的结合 <a class="header-anchor" href="#前端路由与服务端的结合" aria-label="Permalink to &quot;前端路由与服务端的结合&quot;">​</a></h3><p>项目前端路由使用的是 vue-router，所以你可以选择两种方式：history 和 hash。</p><ul><li><code>hash</code> 默认会在 url 后面拼接<code>#</code></li><li><code>history</code> 则不会，不过 <code>history</code> 需要服务器配合</li></ul><p>可在 <code>.env.production</code> 内进行 mode 修改</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_ROUTER_HISTORY</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">hash</span></span></code></pre></div><h3 id="history-路由模式下服务端配置" tabindex="-1">history 路由模式下服务端配置 <a class="header-anchor" href="#history-路由模式下服务端配置" aria-label="Permalink to &quot;history 路由模式下服务端配置&quot;">​</a></h3><p>开启 <code>history</code> 模式需要服务器配置，更多的服务器配置详情可以看 <a href="https://router.vuejs.org/guide/essentials/history-mode.html#html5-mode" target="_blank" rel="noreferrer">history-mode</a></p><p>这里以 <code>nginx</code> 配置为例：</p><h4 id="部署到根目录" tabindex="-1">部署到根目录 <a class="header-anchor" href="#部署到根目录" aria-label="Permalink to &quot;部署到根目录&quot;">​</a></h4><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">server</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  listen</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 80</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  location</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    # 用于配合 History 使用</span></span>\n<span class="line highlighted"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    try_files</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> $uri $uri</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /index.html</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><h4 id="部署到非根目录" tabindex="-1">部署到非根目录 <a class="header-anchor" href="#部署到非根目录" aria-label="Permalink to &quot;部署到非根目录&quot;">​</a></h4><ul><li>首先需要在打包的时候更改<code>.env.production</code>配置:</li></ul><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">VITE_BASE</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> =</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /sub/</span></span></code></pre></div><ul><li>然后在 nginx 配置文件中配置</li></ul><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">server</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    listen</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">       80</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    server_name</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">  localhost</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    location</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /sub/</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">      # 这里是vue打包文件dist内的文件的存放路径</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      alias</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">   /srv/www/project/</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">      index</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> index.html</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> index.htm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line highlighted"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">      try_files</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> $uri $uri</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /sub/index.html</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><h2 id="跨域处理" tabindex="-1">跨域处理 <a class="header-anchor" href="#跨域处理" aria-label="Permalink to &quot;跨域处理&quot;">​</a></h2><p>使用 nginx 处理项目部署后的跨域问题</p><ol><li>配置前端项目接口地址，在项目目录下的``.env.production`文件中配置：</li></ol><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_GLOB_API_URL</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/api</span></span></code></pre></div><ol start="2"><li>在 nginx 配置请求转发到后台</li></ol><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">server</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  listen</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">       8080</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  server_name</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">  localhost</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # 接口代理，用于解决跨域问题</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  location</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /api</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    proxy_set_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Host</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> $host;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    proxy_set_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> X-Real-IP</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> $remote_addr;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    proxy_set_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> X-Forwarded-For</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> $proxy_add_x_forwarded_for;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    # 后台接口地址</span></span>\n<span class="line highlighted"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    proxy_pass</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> http://***********:8080/api</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line highlighted"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    rewrite</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;^/api/(.*)$&quot;</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">$1</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> break</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    proxy_redirect</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> default</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    add_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Access-Control-Allow-Origin</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> *</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    add_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Access-Control-Allow-Headers</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> X-Requested-With</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    add_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Access-Control-Allow-Methods</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> GET,POST,OPTIONS</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div>',62)),h(g),h(o)])}]]);export{t as __pageData,e as default};
