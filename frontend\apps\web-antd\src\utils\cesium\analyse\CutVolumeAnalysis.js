import * as Cesium from 'cesium';

import timeSlice from '../../common/timeSlice';
import LabelDiv from '../components/LabelDiv';
import core from '../core.js';
/**
 * 方量分析
 * @param {*} params
 */
function CutVolumeAnalysis(params) {
  if (params && params.positions && params.that) {
    var $this = this;
    var _debugShowSubTriangles = true;
    var baseHeight = params.height || 20;
    var onProgress = params.progress;
    const size = params.size || 1;
    var particleSize =
      (Math.sqrt(core.getPositionsArea(params.positions)) / 640 / 4) * size;
    var positions = core.transformWGS84ArrayToCartesianArray(
      params.positions,
      100,
    );
    var stop = false;
    var that = params.that;
    var polygons = [];
    console.log(baseHeight);
    computeCutVolume();
  }

  /**
   * 计算多边形的重心点
   * @param {*} positions
   */
  function computeCentroidOfPolygon(positions) {
    const x = [];
    const y = [];

    for (var i = 0; i < positions.length; i++) {
      const cartographic = Cesium.Cartographic.fromCartesian(positions[i]);

      x.push(cartographic.longitude);
      y.push(cartographic.latitude);
    }

    let x0 = 0;
    let x1 = 0;
    let y0 = 0;
    let y1 = 0;
    let signedArea = 0;
    let a = 0;
    let centroidx = 0;
    let centroidy = 0;

    for (i = 0; i < positions.length; i++) {
      x0 = x[i];
      y0 = y[i];

      if (i == positions.length - 1) {
        x1 = x[0];
        y1 = y[0];
      } else {
        x1 = x[i + 1];
        y1 = y[i + 1];
      }

      a = x0 * y1 - x1 * y0;
      signedArea += a;
      centroidx += (x0 + x1) * a;
      centroidy += (y0 + y1) * a;
    }

    signedArea *= 0.5;
    centroidx /= 6 * signedArea;
    centroidy /= 6 * signedArea;

    return new Cesium.Cartographic(centroidx, centroidy);
  }

  /**
   * 计算三角形的面积
   * @param {*} pos1
   * @param {*} pos2
   * @param {*} pos3
   */
  function computeAreaOfTriangle(pos1, pos2, pos3) {
    const a = Cesium.Cartesian3.distance(pos1, pos2);
    const b = Cesium.Cartesian3.distance(pos2, pos3);
    const c = Cesium.Cartesian3.distance(pos3, pos1);

    const S = (a + b + c) / 2;

    return Math.sqrt(S * (S - a) * (S - b) * (S - c));
  }
  /**
   * 计算方量
   */
  async function computeCutVolume() {
    stop = false;
    if (onProgress) onProgress(0);
    /* var tileAvailability = that._viewer.terrainProvider.availability;
		if (!tileAvailability) {
			alert("未获取到地形")
			return false;
		} */
    // var maxLevel = 0;
    let minHeight = 15_000;
    const heights = {};
    let heightCount = 0;
    let key = '';
    // 计算差值点
    for (let i = 0; i < positions.length; i++) {
      key = `${positions[i].x}/${positions[i].y}/${positions[i].z}`;

      if (!heights[key]) {
        heights[key] = that._viewer.scene.clampToHeight(positions[i], []);
      }
      positions[i] = heights[key].clone();
      // positions[i] = that._viewer.scene.clampToHeight(positions[i], []);
      var cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
      const height = cartographic.height; // that._viewer.scene.globe.getHeight(cartographic);

      if (minHeight > height) minHeight = height;

      // var level = tileAvailability.computeMaximumLevelAtPosition(cartographic);

      // if (maxLevel < level)
      //	maxLevel = level;
    }
    /* $this._label = new LabelDiv({
			viewer:that._viewer,
			text:'挖方量: m3\n填方量: m3',
			position:positions[0].clone(),
		}) */
    let granularity = Math.PI / 2 ** 11;
    granularity = granularity / (64 / particleSize);
    console.log(granularity);
    const polygonGeometry = new Cesium.PolygonGeometry.fromPositions({
      positions,
      vertexFormat: Cesium.PerInstanceColorAppearance.FLAT_VERTEX_FORMAT,
      granularity,
    });

    // polygon subdivision

    const geom = new Cesium.PolygonGeometry.createGeometry(polygonGeometry);

    let totalCutVolume = 0;
    let totalFillVolume = 0;
    let maxHeight = 0;

    let i0, i1, i2;
    let height1, height2, height3;
    let p1, p2, p3;
    let bottomP1, bottomP2, bottomP3;
    let scratchCartesian = new Cesium.Cartesian3();
    var cartographic;
    let bottomArea;
    let subTrianglePositions;

    function* fnc_() {
      let i = 0;
      for (i = 0; i < geom.indices.length; i += 3) {
        if (stop) break;
        if (onProgress) onProgress(i / geom.indices.length);

        i0 = geom.indices[i];
        i1 = geom.indices[i + 1];
        i2 = geom.indices[i + 2];

        subTrianglePositions = geom.attributes.position.values;

        scratchCartesian.x = subTrianglePositions[i0 * 3];
        scratchCartesian.y = subTrianglePositions[i0 * 3 + 1];
        scratchCartesian.z = subTrianglePositions[i0 * 3 + 2];

        key = `${scratchCartesian.x}/${scratchCartesian.y}/${scratchCartesian.z}`;

        if (!heights[key]) {
          heights[key] = that._viewer.scene.clampToHeight(scratchCartesian, []);
          heightCount++;
        }
        scratchCartesian = heights[key].clone();
        // scratchCartesian= that._viewer.scene.clampToHeight(scratchCartesian, []);
        cartographic = Cesium.Cartographic.fromCartesian(scratchCartesian);

        height1 = cartographic.height; // that._viewer.scene.globe.getHeight(cartographic);

        p1 = Cesium.Cartesian3.fromRadians(
          cartographic.longitude,
          cartographic.latitude,
          height1,
        );
        bottomP1 = Cesium.Cartesian3.fromRadians(
          cartographic.longitude,
          cartographic.latitude,
          0,
        );

        if (maxHeight < height1) maxHeight = height1;

        scratchCartesian.x = subTrianglePositions[i1 * 3];
        scratchCartesian.y = subTrianglePositions[i1 * 3 + 1];
        scratchCartesian.z = subTrianglePositions[i1 * 3 + 2];

        key = `${scratchCartesian.x}/${scratchCartesian.y}/${scratchCartesian.z}`;

        if (!heights[key]) {
          heights[key] = that._viewer.scene.clampToHeight(scratchCartesian, []);
          heightCount++;
        }
        scratchCartesian = heights[key].clone();
        // scratchCartesian= that._viewer.scene.clampToHeight(scratchCartesian, []);
        cartographic = Cesium.Cartographic.fromCartesian(scratchCartesian);

        height2 = cartographic.height; // that._viewer.scene.globe.getHeight(cartographic);

        p2 = Cesium.Cartesian3.fromRadians(
          cartographic.longitude,
          cartographic.latitude,
          height2,
        );
        bottomP2 = Cesium.Cartesian3.fromRadians(
          cartographic.longitude,
          cartographic.latitude,
          0,
        );

        if (maxHeight < height2) maxHeight = height2;

        scratchCartesian.x = subTrianglePositions[i2 * 3];
        scratchCartesian.y = subTrianglePositions[i2 * 3 + 1];
        scratchCartesian.z = subTrianglePositions[i2 * 3 + 2];

        key = `${scratchCartesian.x}/${scratchCartesian.y}/${scratchCartesian.z}`;

        if (!heights[key]) {
          heights[key] = that._viewer.scene.clampToHeight(scratchCartesian, []);
          heightCount++;
        }
        scratchCartesian = heights[key].clone();
        // scratchCartesian= that._viewer.scene.clampToHeight(scratchCartesian, []);
        cartographic = Cesium.Cartographic.fromCartesian(scratchCartesian);

        height3 = cartographic.height; // that._viewer.scene.globe.getHeight(cartographic);

        p3 = Cesium.Cartesian3.fromRadians(
          cartographic.longitude,
          cartographic.latitude,
          height3,
        );
        bottomP3 = Cesium.Cartesian3.fromRadians(
          cartographic.longitude,
          cartographic.latitude,
          0,
        );

        if (maxHeight < height3) maxHeight = height3;

        bottomArea = computeAreaOfTriangle(bottomP1, bottomP2, bottomP3);

        const volume =
          (bottomArea *
            (height1 -
              baseHeight +
              height2 -
              baseHeight +
              height3 -
              baseHeight)) /
          3;
        if (volume > 0) totalCutVolume = totalCutVolume + volume;
        else {
          totalFillVolume = totalFillVolume + volume;
        }
        if (_debugShowSubTriangles) {
          // console.log(baseHeight,totalCutVolume,totalFillVolume)
          const positionsarr = [];

          positionsarr.push(p1, p2, p3);

          const drawingPolygon = {
            polygon: {
              hierarchy: {
                positions: positionsarr,
              },
              // extrudedHeight: 0,
              // perPositionHeight: true,
              heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
              material: (volume > 0
                ? Cesium.Color.RED
                : Cesium.Color.GREEN
              ).withAlpha(0.5),
              outline: true,
              closeTop: true,
              closeBottom: true,
              outlineColor: Cesium.Color.WHITE,
              outlineWidth: 2,
            },
          };

          polygons.push(that._analysisLayer.entities.add(drawingPolygon));
        }
        yield i;
      }

      return i;
    }
    const num = await timeSlice(fnc_)();
    if (stop) return;
    // console.log(heightCount,heights,subTrianglePositions.length);
    // var centroid = computeCentroidOfPolygon(positions);
    let centroid = Cesium.BoundingSphere.fromPoints(positions).center;
    centroid = that._viewer.scene.clampToHeight(centroid, []);
    cartographic = Cesium.Cartographic.fromCartesian(centroid);
    /* $this._volumeLabel = that._analysisLayer.entities.add({
			position: Cesium.Cartesian3.fromRadians(centroid.longitude, centroid.latitude, maxHeight),
			label: {
				text: '挖方量: ' + totalCutVolume.toFixed(2) + 'm3\n填方量: ' + Math.abs(totalFillVolume).toFixed(2) + 'm3',
				showBackground: true,
				font: '14px monospace',
				fillColor: Cesium.Color.YELLOW,
				pixelOffset: {
					x: 0,
					y: -20
				},
				verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
				horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
				disableDepthTestDistance: Number.POSITIVE_INFINITY,
			}
		}); */
    $this._label = new LabelDiv({
      viewer: that._viewer,
      // text:'挖方量: ' + totalCutVolume.toFixed(2) + 'm3\n填方量: ' + Math.abs(totalFillVolume).toFixed(2) + 'm3',
      html: `<span>挖方量: ${totalCutVolume.toFixed(2)}m</span><span style='vertical-align: super;font-size:50%;'>3</span><br>
				<span>填方量:${Math.abs(totalFillVolume).toFixed(2)}</span>m<span style='vertical-align: super;font-size:50%;'>3</span>`,
      style: 'width:240px;',
      position: Cesium.Cartesian3.fromRadians(
        cartographic.longitude,
        cartographic.latitude,
        cartographic.height,
      ),
      toolbox: {
        size: 20,
        fill: '#fff',
        buttons: {
          copy: {
            click: () => {
              return `挖方量: ${totalCutVolume.toFixed(
                2,
              )}m3\n填方量: ${Math.abs(totalFillVolume).toFixed(2)}m3`;
            },
          },
          close: {
            click: () => {
              params.close && params.close();
              $this.remove();
            },
          },
        },
      },
    });
    if (onProgress) onProgress(1);
    return maxHeight;
  }
  CutVolumeAnalysis.prototype.remove = function () {
    stop = true;
    polygons.forEach((a) => {
      if (a.owner) {
        a.owner.entities.remove(a);
      } else if (a.entityCollection && a.entityCollection.owner) {
        a.entityCollection.owner.entities.remove(a);
      }
      // that._analysisLayer.entities.remove(a);
    });
    if ($this._volumeLabel) {
      that._analysisLayer.entities.remove($this._volumeLabel);
    }
    if ($this._label) {
      $this._label.destroy();
    }
    polygons = [];
    // $this._volumeLabel=null;
    $this._label = null;
    if (onProgress) onProgress(1);
  };
}
export default CutVolumeAnalysis;
