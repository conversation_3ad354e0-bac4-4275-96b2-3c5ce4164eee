@echo off
chcp 65001 >nul
echo ================================================
echo           系统管理数据库初始化工具
echo ================================================
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 💡 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

REM 检查mysql2包是否安装
if not exist "..\node_modules\mysql2" (
    echo 📦 正在安装mysql2依赖包...
    cd ..
    npm install mysql2
    cd data
    if %errorlevel% neq 0 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
)

REM 检查数据库初始化脚本是否存在
if not exist "database-init.sql" (
    echo ❌ 错误: 未找到database-init.sql文件
    pause
    exit /b 1
)

REM 提示用户配置数据库连接
echo 🔧 请确保已正确配置数据库连接信息：
echo    方式1: 设置环境变量 DB_HOST, DB_PORT, DB_USERNAME, DB_PASSWORD
echo    方式2: 修改 init-database.js 中的默认配置
echo.

set /p confirm="是否继续执行数据库初始化？(y/N): "
if /i not "%confirm%"=="y" (
    echo 取消初始化
    pause
    exit /b 0
)

echo.
echo 🚀 开始执行数据库初始化...
echo.

REM 运行初始化脚本
node init-database.js

echo.
echo 按任意键退出...
pause >nul
