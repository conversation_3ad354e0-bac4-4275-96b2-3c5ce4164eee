const bcrypt = require('bcryptjs');

async function testLoginLogic() {
  console.log('=== 测试登录逻辑 ===\n');
  
  // 测试数据
  const testCases = [
    {
      name: 'admin用户',
      plainPassword: 'admin123',
      hashedPassword: '$2b$10$MvaLUvYvSfRqMKVd.Tz0c.2j3U0LG2OnN0xrXIboiE/6bbZSU7BcG'
    },
    {
      name: '测试用户',
      plainPassword: '8888a8888#@',
      hashedPassword: '$2b$10$4HoGJ.gGo7ffRDcu4E5DDub0qN5IvXiuuwUMMSVF2YiDeOtSgwVnm'
    },
    {
      name: '错误密码测试',
      plainPassword: 'wrongpassword',
      hashedPassword: '$2b$10$4HoGJ.gGo7ffRDcu4E5DDub0qN5IvXiuuwUMMSVF2YiDeOtSgwVnm'
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`--- ${testCase.name} ---`);
    console.log(`明文密码: ${testCase.plainPassword}`);
    console.log(`加密密码: ${testCase.hashedPassword}`);
    
    try {
      const isValid = await bcrypt.compare(testCase.plainPassword, testCase.hashedPassword);
      console.log(`验证结果: ${isValid ? '✅ 成功' : '❌ 失败'}`);
    } catch (error) {
      console.log(`验证错误: ${error.message}`);
    }
    
    console.log('');
  }
  
  // 模拟前端登录请求
  console.log('=== 模拟前端登录请求 ===\n');
  
  const loginRequests = [
    {
      account: 'admin',
      password: 'admin123'
    },
    {
      account: '***********',
      password: '8888a8888#@'
    },
    {
      account: 'admin',
      password: 'wrongpassword'
    }
  ];
  
  // 模拟数据库中的用户数据
  const mockUsers = [
    {
      id: 1,
      username: 'admin',
      phone: null,
      password: '$2b$10$MvaLUvYvSfRqMKVd.Tz0c.2j3U0LG2OnN0xrXIboiE/6bbZSU7BcG',
      realName: '系统管理员',
      status: 1
    },
    {
      id: 2,
      username: 'testuser',
      phone: '***********',
      password: '$2b$10$4HoGJ.gGo7ffRDcu4E5DDub0qN5IvXiuuwUMMSVF2YiDeOtSgwVnm',
      realName: '何',
      status: 1
    }
  ];
  
  for (const request of loginRequests) {
    console.log(`--- 登录请求: ${request.account} ---`);
    console.log(`请求密码: ${request.password}`);
    
    // 模拟用户查找逻辑
    let user = mockUsers.find(u => u.username === request.account);
    if (!user) {
      user = mockUsers.find(u => u.phone === request.account);
    }
    
    if (!user) {
      console.log('结果: ❌ 用户不存在');
    } else {
      console.log(`找到用户: ${user.realName} (${user.username})`);
      
      if (user.status !== 1) {
        console.log('结果: ❌ 用户已被禁用');
      } else {
        const isPasswordValid = await bcrypt.compare(request.password, user.password);
        console.log(`密码验证: ${isPasswordValid ? '✅ 成功' : '❌ 失败'}`);
        
        if (isPasswordValid) {
          console.log('结果: ✅ 登录成功');
        } else {
          console.log('结果: ❌ 账号或密码错误');
        }
      }
    }
    
    console.log('');
  }
}

// 运行测试
testLoginLogic().catch(console.error);
