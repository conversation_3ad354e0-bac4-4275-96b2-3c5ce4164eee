import { requestClient } from '#/api/request';

enum Api {
  deleteOne = '/sys/tableWhiteList/delete',
  edit = '/geo-entity/update',
  list = '/geo-entity/page',
  logs = '/data/logs',
  metadata = '/data/metadata',
  preview = '/data/preview',
  save = '/geo-entity/add',
}

/**
 * 保存
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const save = (params) => {
  let nParams = {...params};
  delete nParams.regionCode;
  return requestClient.post(Api.save, { ...nParams });
};

/**
 * 修改
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const edit = (params,id) => {
  let nParams = {...params};
  delete nParams.regionCode;
  return requestClient.put(`/geo-entity/update/${id}`, { ...nParams });
};

/**
 * 更新文件状态
 * @param params
 */
export const updateFileState = (params) => {
  return requestClient.put(`/geo-entity/update-file`, {
    id: params.id,
    fileId: params.fileId,
    dataType: params.dataType,
    epsgCode: params.epsgCode
  });
};

/**
 * 列表接口
 * @param params
 */
export const list = (params) => {
  return requestClient.post(Api.list, {
    ...params,
  });
};

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (dataType,id, handleSuccess) => {
  return requestClient.delete(`/geo-entity/${id}`, {  }).then(() => {
    handleSuccess();
  });
};

export const getMetadata = (dataType,id) => {
  return requestClient.get(`/geo-entity/detail/${id}`, {
  });
};

//获取图层列表
export const getLayers = (dataType,id) => {
  return requestClient.get(`/geo-entity/layers/${id}`, {
  });
};

//获取图层元数据
export const getLayerDatas = (dataType,id) => {
  return requestClient.get(`/geo-entity/layer/metadata/${id}`, {
  });
};


//获取图层表数据
export const getLayerTableDatas = (id,params) => {
  return requestClient.post(`geo-entity/layer/property-data/page/${id}`, {
    ...params,
  });
};

//获取矢量预览图片：用于dataType为vector数据的预览
export const getPreviewVectorDatas = (layerId) => {
  return requestClient.get(`/layer-data/preview/vector/${layerId}`, {
  });
};

//获取坐标系列表
export const getEpsgCodes = (params) => {
  return requestClient.post('/common/get-epsg-code', {
    condition: params.condition || '',
    page: {
      current: params.current || 1,
      size: params.size || 20000,
      searchCount: true
    },
  });
};
