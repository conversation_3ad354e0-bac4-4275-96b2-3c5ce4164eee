var T=(c,l,a)=>new Promise((i,s)=>{var d=e=>{try{u(a.next(e))}catch(m){s(m)}},r=e=>{try{u(a.throw(e))}catch(m){s(m)}},u=e=>e.done?i(e.value):Promise.resolve(e.value).then(d,r);u((a=a.apply(c,l)).next())});import{d as _,y as B,c as k,a as $,b as S,s as p,f as P,q as g,g as w,v as h,t as b,k as o,$ as n,h as V,r as C,j as y}from"../jse/index-index-DyHD_jbN.js";import{b as F,u as x,T as N,f as v,j as A}from"./bootstrap-5OPUVRWy.js";const L=_({name:"ForgetPassword",__name:"forget-password",props:{formSchema:{},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(c,{expose:l,emit:a}){const i=c,s=a,[d,r]=F(B({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:k(()=>i.formSchema),showDefaultActions:!1})),u=x();function e(){return T(this,null,function*(){const{valid:t}=yield r.validate(),f=yield r.getValues();t&&s("submit",f)})}function m(){u.push(i.loginPath)}return l({getFormApi:()=>r}),(t,f)=>(S(),$("div",null,[p(N,null,{desc:g(()=>[w(t.$slots,"subTitle",{},()=>[h(b(t.subTitle||o(n)("authentication.forgetPasswordSubtitle")),1)])]),default:g(()=>[w(t.$slots,"title",{},()=>[h(b(t.title||o(n)("authentication.forgetPassword"))+" 🤦🏻‍♂️ ",1)])]),_:3}),p(o(d)),P("div",null,[p(o(v),{class:V([{"cursor-wait":t.loading},"mt-2 w-full"]),"aria-label":"submit",onClick:e},{default:g(()=>[w(t.$slots,"submitButtonText",{},()=>[h(b(t.submitButtonText||o(n)("authentication.sendResetLink")),1)])]),_:3},8,["class"]),p(o(v),{class:"mt-4 w-full",variant:"outline",onClick:f[0]||(f[0]=R=>m())},{default:g(()=>[h(b(o(n)("common.back")),1)]),_:1})])]))}}),E=_({name:"ForgetPassword",__name:"forget-password",setup(c){const l=C(!1),a=k(()=>[{component:"VbenInput",componentProps:{placeholder:"<EMAIL>"},fieldName:"email",label:n("authentication.email"),rules:A().min(1,{message:n("authentication.emailTip")}).email(n("authentication.emailValidErrorTip"))}]);function i(s){console.log("reset email:",s)}return(s,d)=>(S(),y(o(L),{"form-schema":a.value,loading:l.value,onSubmit:i},null,8,["form-schema","loading"]))}});export{E as default};
