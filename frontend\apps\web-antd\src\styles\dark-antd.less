// Ant Design 深色模式样式覆盖

// Card 样式
  :deep(.ant-card) {
    background-color: hsl(var(--background));
    border-color: hsl(var(--border));
  }

  :deep(.ant-card-head) {
    background-color: hsl(var(--background));
    border-bottom-color: hsl(var(--border));
  }

  :deep(.ant-card-head-title) {
    color: hsl(var(--foreground));
  }

  // Table 样式
  :deep(.ant-table) {
    background-color: hsl(var(--background));
  }

  :deep(.ant-table-thead > tr > th) {
    background-color: hsl(var(--muted));
    color: hsl(var(--foreground));
    border-bottom-color: hsl(var(--border));
  }

  :deep(.ant-table-tbody > tr > td) {
    border-bottom-color: hsl(var(--border));
    color: hsl(var(--foreground));
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background-color: hsl(var(--accent));
  }

  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: hsl(var(--accent));
  }

  // Radio 样式
  :deep(.ant-radio-wrapper) {
    color: hsl(var(--foreground));
  }

  :deep(.ant-radio-wrapper:hover .ant-radio-inner) {
    border-color: hsl(var(--primary));
  }

  :deep(.ant-radio-inner) {
    background-color: hsl(var(--background));
    border-color: hsl(var(--border));
  }

  :deep(.ant-radio-checked .ant-radio-inner) {
    border-color: hsl(var(--primary));
    background-color: hsl(var(--background));
  }

  :deep(.ant-radio-checked .ant-radio-inner::after) {
    background-color: hsl(var(--primary));
  }

  :deep(.ant-radio-disabled .ant-radio-inner) {
    background-color: hsl(var(--muted));
    border-color: hsl(var(--border));
  }

  :deep(.ant-radio-disabled + span) {
    color: hsl(var(--muted-foreground));
  }

  :deep(.ant-radio-disabled.ant-radio-checked .ant-radio-inner::after) {
    background-color: hsl(var(--muted-foreground));
  }

  // Button 样式
  :deep(.upload-button) {
    background-color: hsl(var(--primary)) !important;
    border-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
  }

  :deep(.upload-button:hover) {
    background-color: hsl(var(--primary) / 0.9) !important;
    border-color: hsl(var(--primary) / 0.9) !important;
  }

  :deep(.upload-button:disabled) {
    background-color: hsl(var(--muted)) !important;
    border-color: hsl(var(--muted)) !important;
    color: hsl(var(--muted-foreground)) !important;
    opacity: 0.5;
  }


  // Tab 组件样式
  :deep([role="tab"]) {
    position: relative;
    margin-right: 32px;
    padding: 8px 0;
    background: transparent;
    border: none;
    box-shadow: none;
    color: hsl(var(--muted-foreground));
  }

  :deep([role="tab"]:hover),
  :deep([role="tab"][data-state="active"]) {
    color: hsl(var(--primary));
  }

  :deep([role="tab"][data-state="active"])::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: hsl(var(--primary));
  }

  :deep([role="tablist"]) {
    background: transparent;
    margin-bottom: 16px;
  }

  :deep([role="tabpanel"]) {
    background: transparent;
    color: hsl(var(--foreground));
  }

  // Modal 组件样式
  :deep(.ant-modal) {
    background-color: hsl(var(--background));
  }

  :deep(.ant-modal-content) {
    background-color: hsl(var(--background));
  }

  :deep(.ant-modal-header) {
    background-color: hsl(var(--background));
    border-bottom-color: hsl(var(--border));
  }

  :deep(.ant-modal-title) {
    color: hsl(var(--foreground));
  }

  :deep(.ant-modal-footer) {
    border-top-color: hsl(var(--border));
  }


  /* 输入框样式 */
  :deep(.ant-input),
  :deep(.ant-input-number),
  :deep(.ant-input-affix-wrapper),
  :deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector),
  :deep(.ant-picker) {
    background-color: hsl(var(--background));
    border-color: hsl(var(--border));
    color: hsl(var(--foreground));

    &:hover {
      border-color: hsl(var(--primary)) !important;
    }

    &:focus,
    &-focused {
      border-color: hsl(var(--primary)) !important;
      box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2) !important;
    }
  }

  /* 输入框激活状态 */
  :deep(.ant-input-affix-wrapper-focused),
  :deep(.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector),
  :deep(.ant-picker-focused) {
    border-color: hsl(var(--primary)) !important;
    box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2) !important;
  }

  /* 输入框内的图标颜色 */
  :deep(.ant-input-prefix),
  :deep(.ant-input-suffix),
  :deep(.ant-select-arrow),
  :deep(.ant-picker-suffix) {
    color: hsl(var(--muted-foreground));
  }

  /* 下拉菜单样式 */
  :deep(.ant-select-dropdown),
  :deep(.ant-picker-dropdown) {
    background-color: hsl(var(--background));
    border: 1px solid hsl(var(--border));
    box-shadow: 0 2px 8px hsl(var(--overlay));

    .ant-select-item {
      color: hsl(var(--foreground));

      &:hover {
        background-color: hsl(var(--accent));
      }

      &-selected {
        background-color: hsl(var(--primary) / 0.1);
        color: hsl(var(--primary));
      }
    }
  }

  /* 选择框选中项样式 */
  :deep(.ant-select-selection-item) {
    color: hsl(var(--foreground));
  }

  /* 禁用状态样式 */
  :deep(.ant-input-disabled),
  :deep(.ant-input-number-disabled),
  :deep(.ant-select-disabled .ant-select-selector),
  :deep(.ant-picker-disabled) {
    background-color: hsl(var(--muted)) !important;
    border-color: hsl(var(--border)) !important;
    color: hsl(var(--muted-foreground)) !important;
    cursor: not-allowed;

    .ant-select-selection-item {
      color: hsl(var(--muted-foreground)) !important;
    }
  }

  :deep(.ant-select-disabled .ant-select-selection-item) {
    color: hsl(var(--foreground)) !important;
    -webkit-text-fill-color: hsl(var(--foreground)) !important;
  }

  /* a-tree 夜间模式样式 */
  :deep(.ant-tree) {
    background: transparent;
    color: hsl(var(--foreground));

    /* 树节点样式 */
    .ant-tree-treenode {
      color: hsl(var(--foreground));

      &:hover {
        background: hsl(var(--accent));
      }

      /* 选中状态 */
      &.ant-tree-treenode-selected {
        background: hsl(var(--accent));
      }

      /* 展开/折叠图标 */
      .ant-tree-switcher {
        color: hsl(var(--muted-foreground));

        &:hover {
          color: hsl(var(--foreground));
        }
      }

      /* 复选框样式 */
      .ant-tree-checkbox {
        .ant-tree-checkbox-inner {
          background-color: hsl(var(--card));
          border-color: hsl(var(--border));
        }

        &:hover .ant-tree-checkbox-inner {
          border-color: hsl(var(--primary));
        }

        &.ant-tree-checkbox-checked .ant-tree-checkbox-inner {
          background-color: hsl(var(--primary));
          border-color: hsl(var(--primary));
        }

        &.ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner {
          background-color: hsl(var(--card));
          border-color: hsl(var(--primary));

          &::after {
            background-color: hsl(var(--primary));
          }
        }
      }

      /* 节点标题 */
      .ant-tree-node-content-wrapper {
        color: hsl(var(--foreground));

        &:hover {
          background: hsl(var(--accent));
        }

        &.ant-tree-node-selected {
          background: hsl(var(--accent));
        }
      }

      /* 搜索高亮 */
      .ant-tree-node-content-wrapper .ant-tree-title {
        span {
          &[style*="color: red"] {
            color: hsl(var(--destructive)) !important;
          }
        }
      }
    }

    /* 连接线 */
    .ant-tree-show-line .ant-tree-switcher {
      background: transparent;
    }

    /* 拖拽样式 */
    .ant-tree-drop-indicator {
      background-color: hsl(var(--primary));
    }

    .ant-tree-drop-target-active {
      background: hsl(var(--accent));
    }

  }


  :deep(.ant-btn) {
    &.ant-btn-primary {
      background: hsl(var(--primary));
      border-color: hsl(var(--primary));
      color: hsl(var(--primary-foreground));

      &:hover {
        background: hsl(var(--primary)) / 0.9;
        border-color: hsl(var(--primary)) / 0.9;
      }
    }

    &.ant-btn-default {
      background: hsl(var(--card));
      border-color: hsl(var(--border));
      color: hsl(var(--foreground));

      &:hover {
        background: hsl(var(--accent));
        border-color: hsl(var(--primary));
        color: hsl(var(--accent-foreground));
      }
    }
  }

  /* 面包屑夜间模式样式 */
  :deep(.ant-breadcrumb) {
    color: hsl(var(--foreground));

    .ant-breadcrumb-link {
      color: hsl(var(--foreground));

      a {
        color: hsl(var(--foreground));

        &:hover {
          color: hsl(var(--primary));
        }
      }
    }

    .ant-breadcrumb-separator {
      color: hsl(var(--muted-foreground));
    }
  }
