import { Controller, Post, Body, UseGuards, Get, Put } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LoginDto, LoginResponseDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { User } from '@/modules/users/entities/user.entity';
import { ResponseDto } from '@/common/dto/response.dto';

@ApiTags('认证')
@Controller('system')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({ status: 200, description: '登录成功', type: LoginResponseDto })
  async login(@Body() loginDto: LoginDto): Promise<ResponseDto<LoginResponseDto>> {
    const result = await this.authService.login(loginDto);
    return ResponseDto.success(result, '登录成功');
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '用户退出' })
  @ApiResponse({ status: 200, description: '退出成功' })
  async logout(@CurrentUser() user: User): Promise<ResponseDto<any>> {
    const result = await this.authService.logout(user);
    return ResponseDto.success(result, '退出成功');
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取用户信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getProfile(@CurrentUser() user: User): Promise<ResponseDto<any>> {
    const result = await this.authService.getProfile(user.id);
    return ResponseDto.success(result, '获取成功');
  }

  @Put('password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '修改密码' })
  @ApiResponse({ status: 200, description: '修改成功' })
  async changePassword(
    @CurrentUser() user: User,
    @Body() changePasswordDto: ChangePasswordDto,
  ): Promise<ResponseDto<any>> {
    const result = await this.authService.changePassword(user.id, changePasswordDto);
    return ResponseDto.success(result, '密码修改成功');
  }
}
