var y=Object.defineProperty;var s=Object.getOwnPropertySymbols;var g=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var o=(e,t,a)=>t in e?y(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,n=(e,t)=>{for(var a in t||(t={}))g.call(t,a)&&o(e,a,t[a]);if(s)for(var a of s(t))p.call(t,a)&&o(e,a,t[a]);return e};import{r}from"./bootstrap-5OPUVRWy.js";const u=e=>r.post("/geo-entity/add",n({},e)),l=(e,t)=>r.put(`/geo-entity/update/${t}`,n({},e)),c=e=>r.put("/geo-entity/update-file",n({},e)),$=e=>r.post("/geo-entity/page",n({},e)),T=(e,t,a)=>r.delete(`/geo-entity/${t}`,{}).then(()=>{a()}),m=(e,t)=>r.get(`/geo-entity/detail/${t}`,{}),L=(e,t)=>r.get(`/geo-entity/layers/${t}`,{}),b=(e,t)=>r.get(`/geo-entity/layer/metadata/${t}`,{}),f=(e,t)=>r.post(`geo-entity/layer/property-data/page/${e}`,n({},t));export{L as a,b,f as c,T as d,l as e,m as g,$ as l,u as s,c as u};
