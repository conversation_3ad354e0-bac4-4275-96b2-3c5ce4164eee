const n=[{label:"Vector",value:"vector",fileType:"folder"}],d=[{label:"武夷山市",value:"350782000000"},{label:"崇安街道",value:"350782001000"},{label:"新丰街道",value:"350782002000"},{label:"武夷街道",value:"350782003000"},{label:"星村镇",value:"350782100000"},{label:"兴田镇",value:"350782101000"},{label:"五夫镇",value:"350782102000"},{label:"上梅乡",value:"350782200000"},{label:"吴屯乡",value:"350782201000"},{label:"岚谷乡",value:"350782202000"},{label:"洋庄乡",value:"350782203000"}],t=[{label:"待上传",value:0},{label:"已上传",value:1},{label:"解析中",value:2},{label:"解析失败",value:3}];function r(e){let o=e.uploadStatus;for(let l=0;l<t.length;l++)if(t[l].value===o)return t[l].label;return""}const a=[];let i=new Date().getFullYear();for(let e=i;e>2e3;e--)a.push({label:""+e,value:""+e});const s=[{title:"数据集名称",dataIndex:"name",field:"name",width:300},{title:"数据类型",dataIndex:"dataType",field:"dataType",width:100},{title:"建设年份",dataIndex:"dataYear",field:"dataYear",width:100},{title:"行政区划",dataIndex:"regionName",field:"regionName",width:160},{title:"面积(平方米)",dataIndex:"area",field:"area",width:180,slots:{default:"area"}},{title:"长度(米)",dataIndex:"length",field:"length",width:120,slots:{default:"length"}},{title:"图层个数",dataIndex:"layerCount",field:"layerCount",width:100},{title:"数据量",dataIndex:"dataSize",field:"dataSize",width:120},{title:"数据提供者",dataIndex:"provider",field:"provider",width:180},{title:"状态",dataIndex:"uploadStatus",field:"uploadStatus",slots:{default:"upload-status"},width:100},{title:"创建人",dataIndex:"userNameCreated",field:"userNameCreated",width:150},{title:"创建时间",dataIndex:"timeCreated",field:"timeCreated",width:180},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",width:210}],u=[{label:"数据集名称",fieldName:"name",component:"Input"},{label:"行政区划",fieldName:"regionCode",component:"Select",componentProps:{options:d}},{label:"数据提供者",fieldName:"provider",component:"Input"},{label:"建设年份",fieldName:"dataYear",component:"Select",componentProps:{options:a}},{label:"状态",fieldName:"uploadStatus",component:"Select",componentProps:{options:t}}],p=[{fieldName:"id",label:"主键",component:"Input",dependencies:{show:!1,triggerFields:["upload"]}},{fieldName:"name",label:"数据集名称",rules:"required",component:"Input",componentProps:{placeholder:"请输入数据集名称"},dependencies:{show(e){return!e.upload},triggerFields:["upload"]}},{label:"数据类型",fieldName:"dataType",component:"Select",componentProps:{options:n},rules:"selectRequired",dependencies:{disabled(e){return e.id!=null&&e.id.length>0},triggerFields:["id"]}},{label:"建设年份",fieldName:"dataYear",component:"Select",componentProps:{options:a},rules:"selectRequired"},{label:"行政区划",fieldName:"regionCode",component:"Select",componentProps:{options:d},rules:"selectRequired"},{label:"数据提供者",fieldName:"provider",component:"Input",rules:"required"}];export{s as c,p as f,r as g,u as s};
