import {
  <PERSON><PERSON>n,
  VxeColumn,
  column_default
} from "./chunk-TBV226UF.js";
import "./chunk-UVVQ2N3A.js";
import "./chunk-D5IZJMP2.js";
import "./chunk-35UPDOAM.js";
import "./chunk-NGQJRTLF.js";
import "./chunk-DULHHPCE.js";
import "./chunk-3X4K7UGJ.js";
import "./chunk-5XE5RK6E.js";
import "./chunk-OWQEP5NU.js";
import "./chunk-YHD4RJOZ.js";

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/vxe-column/index.js
var vxe_column_default = column_default;
export {
  Column,
  VxeColumn,
  vxe_column_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-column_index__js.js.map
