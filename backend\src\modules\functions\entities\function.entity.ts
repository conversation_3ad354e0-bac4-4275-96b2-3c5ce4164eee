import { Entity, Column, Index, ManyToOne, OneToMany, ManyToMany, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '@/common/entities/base.entity';
import { Application } from '@/modules/applications/entities/application.entity';
import { Role } from '@/modules/roles/entities/role.entity';

@Entity('functions')
@Index(['applicationId'])
@Index(['parentId'])
@Index(['type'])
@Index(['status'])
@Index(['sortOrder'])
export class Function extends BaseEntity {
  @ApiProperty({ description: '应用ID' })
  @Column({
    name: 'application_id',
    type: 'bigint',
    comment: '应用ID',
  })
  applicationId: number;

  @ApiProperty({ description: '上级功能ID，0为根节点' })
  @Column({
    name: 'parent_id',
    type: 'bigint',
    default: 0,
    comment: '上级功能ID，0为根节点',
  })
  parentId: number;

  @ApiProperty({ description: '功能名称' })
  @Column({
    type: 'varchar',
    length: 100,
    comment: '功能名称',
  })
  name: string;

  @ApiProperty({ description: '功能类型：directory目录，menu菜单，button按钮' })
  @Column({
    type: 'enum',
    enum: ['directory', 'menu', 'button'],
    default: 'menu',
    comment: '功能类型：directory目录，menu菜单，button按钮',
  })
  type: string;

  @ApiProperty({ description: '功能路径' })
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '功能路径',
  })
  path?: string;

  @ApiProperty({ description: '功能图标' })
  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '功能图标',
  })
  icon?: string;

  @ApiProperty({ description: '关联资源' })
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: '关联资源',
  })
  resource?: string;

  @ApiProperty({ description: '排序' })
  @Column({
    name: 'sort_order',
    type: 'int',
    default: 0,
    comment: '排序',
  })
  sortOrder: number;

  @ApiProperty({ description: '状态：1启用，0停用' })
  @Column({
    type: 'tinyint',
    default: 1,
    comment: '状态：1启用，0停用',
  })
  status: number;

  // 关联关系
  @ManyToOne(() => Application, (app) => app.functions, { eager: true })
  @JoinColumn({ name: 'application_id' })
  application?: Application;

  @ManyToOne(() => Function, (func) => func.children)
  @JoinColumn({ name: 'parent_id' })
  parent?: Function;

  @OneToMany(() => Function, (func) => func.parent)
  children?: Function[];

  @ManyToMany(() => Role, (role) => role.functions)
  roles?: Role[];
}
