var gi=(t,i,a)=>new Promise((s,e)=>{var r=o=>{try{n(a.next(o))}catch(l){e(l)}},h=o=>{try{n(a.throw(o))}catch(l){e(l)}},n=o=>o.done?s(o.value):Promise.resolve(o.value).then(r,h);n((a=a.apply(t,i)).next())});class la{constructor(){this.__handlers={}}emit(i,a){if(this.__handlers[i]instanceof Array)for(var s=this.__handlers[i],e=0;e<s.length;e++)s[e].handler(a),s[e].once&&(s.splice(e,1),e--)}on(i,a){typeof this.__handlers[i]=="undefined"&&(this.__handlers[i]=[]),this.__handlers[i].push({handler:a})}once(i,a){typeof this.__handlers[i]=="undefined"&&(this.__handlers[i]=[]),this.__handlers[i].push({handler:a,once:!0})}off(i,a){if(this.__handlers[i]instanceof Array){for(var s=this.__handlers[i],e=0;e<s.length&&s[e].handler!=a;e++);s.splice(e,1)}}clear(i){i?this.__handlers[i]instanceof Array&&(this.__handlers[i]=[]):this.__handlers={}}}let mi=!1;class ca extends la{constructor(){super(),this.keyCodes=[],document.addEventListener("keydown",i=>{this.keyCodes.includes(i.keyCode)&&this.emit("keydown."+i.keyCode)}),window.addEventListener("resize",i=>{this.emit("resize",i);var a=document.fullScreen||document.mozFullScreen||document.webkitIsFullScreen;mi!=a&&this.emit("fullScreenChange",mi)})}}new ca;Date.prototype.timeDiff=function(t){let i;return i=t-this,i};Date.prototype.dayDiff=function(t){return Math.ceil(Math.abs(this.getTime()-t.getTime())/864e5)};const di={Jan:{en:"January",cn:"一"},Feb:{en:"February",cn:"二"},Mar:{en:"March",cn:"三"},April:{en:"April",cn:"四"},May:{en:"May",cn:"五"},Jun:{en:"June",cn:"六"},Jul:{en:"July",cn:"七"},Aug:{en:"August",cn:"八"},Sep:{en:"September",cn:"九"},Oct:{en:"October",cn:"十"},Nov:{en:"November",cn:"十一"},Dec:{en:"December",cn:"十二"}},Mi={Mon:{en:"Monday",cn:"一"},Tue:{en:"Tuesday",cn:"二"},Wed:{en:"Wednesday",cn:"三"},Thu:{en:"Thursday",cn:"四"},Fri:{en:"Friday",cn:"五"},Sat:{en:"Saturday",cn:"六"},Sun:{en:"Sunday",cn:"日"}};Date.prototype.format2=function(t){var i={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()};/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length)));for(var a in i)new RegExp("("+a+")").test(t)&&(t=t.replace(RegExp.$1,RegExp.$1.length==1?i[a]:("00"+i[a]).substr((""+i[a]).length)));return t};Date.prototype.format=function(t){date=this,t=t||"Y-m-d";let i=new Date(date),[a,s,e,r,h,n,o]=[i.getFullYear()+"",i.getMonth()+1+"",i.getDate()+"",i.getDay()+"",i.getHours()+"",i.getMinutes()+"",i.getSeconds()+""],[l,_]=[i.toUTCString().substr(8,3),i.toUTCString().substr(0,3)],f=r==="0"?"7":r;return t.replace(/Y/g,a).replace(/y/g,a.slice(-2)).replace(/m/g,("0"+s).slice(-2)).replace(/n/g,s).replace(/M/g,l).replace(/F/g,di[l].en).replace(/\_F/g,di[l].cn).replace(/j/g,e).replace(/d/g,("0"+e).slice(-2)).replace(/D/g,_).replace(/l/g,Mi[_].en).replace(/_l/g,Mi[_].cn).replace(/w/g,r).replace(/N/g,f).replace(/H/g,("0"+h).slice(-2)).replace(/G/g,h).replace(/i/g,("0"+n).slice(-2)).replace(/s/g,("0"+o).slice(-2))};Date.prototype.addSeconds=function(t){const i=new Date(this);return i.setSeconds(i.getSeconds()+t),i};Date.prototype.addMinutes=function(t){const i=new Date(this);return i.setMinutes(i.getMinutes()+t),i};String.format=function(t,...i){t=t||"",i=i||[],i.length==1&&i[0]instanceof Array&&(i=i[0]);for(var a=0;a<i.length;a++){var s=new RegExp("\\{"+a+"\\}","gm");t=t.replace(s,i[a])}return t};String.prototype.format=function(...t){return String.format(this,t)};String.prototype.trim=function(){return this.replace(/(^\s*)|(\s*$)/g,"")};String.prototype.ltrim=function(){return this.replace(/(^\s*)/g,"")};String.prototype.rtrim=function(){return this.replace(/(\s*$)/g,"")};String.prototype.toDate=function(){return new Date(this)};String.prototype.capitalize=()=>(void 0).charAt(0).toUpperCase()+(void 0).slice(1);String.prototype.trim=function(t,i){return t?i=="left"?this.replace(new RegExp("^\\"+t+"+","g"),""):i=="right"?this.replace(new RegExp("\\"+t+"+$","g"),""):this.replace(new RegExp("^\\"+t+"+|\\"+t+"+$","g"),""):this.replace(/^\s+|\s+$/g,"")};Array.union=function(t,i,a){return t.concat(i.filter(s=>a?!t.map(e=>e[a]).includes(s[a]):!t.includes(a)))};Array.intersection=function(t,i,a){return t.filter(s=>a?i.map(e=>e[a]).includes(s[a]):i.includes(s))};Array.except=function(t,i,a){return[...t,...i].filter(s=>![t,i].every(e=>a?e.map(r=>r[a]).includes(s[a]):e.includes(s)))};Array.chunk=function(t,i){return[...Array(Math.ceil(t.length/i)).keys()].reduce((a,s,e)=>(a.push(t.slice(e*i,(e+1)*i)),a),[])};Array.range=function(t,i){return Array.from({length:i-t+1},(a,s)=>s+t)};Array.shuffle=function(){return this.sort(()=>.5-Math.random())};Date.prototype.timeDiff=function(t){let i;return i=t-this,i};Date.prototype.dayDiff=function(t){return Math.ceil(Math.abs(this.getTime()-t.getTime())/864e5)};Date.prototype.format=function(t){date=this,t=t||"Y-m-d";let i=new Date(date),[a,s,e,r,h,n,o]=[i.getFullYear()+"",i.getMonth()+1+"",i.getDate()+"",i.getDay()+"",i.getHours()+"",i.getMinutes()+"",i.getSeconds()+""],[l,_]=[i.toUTCString().substr(8,3),i.toUTCString().substr(0,3)],f=r==="0"?"7":r;return t.replace(/Y/g,a).replace(/y/g,a.slice(-2)).replace(/m/g,("0"+s).slice(-2)).replace(/n/g,s).replace(/M/g,l).replace(/F/g,months[l].en).replace(/\_F/g,months[l].cn).replace(/j/g,e).replace(/d/g,("0"+e).slice(-2)).replace(/D/g,_).replace(/l/g,weeks[_].en).replace(/_l/g,weeks[_].cn).replace(/w/g,r).replace(/N/g,f).replace(/H/g,("0"+h).slice(-2)).replace(/G/g,h).replace(/i/g,("0"+n).slice(-2)).replace(/s/g,("0"+o).slice(-2))};function fa(t){t("EPSG:4326","+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees"),t("EPSG:4269","+title=NAD83 (long/lat) +proj=longlat +a=6378137.0 +b=6356752.31414036 +ellps=GRS80 +datum=NAD83 +units=degrees"),t("EPSG:3857","+title=WGS 84 / Pseudo-Mercator +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs");for(var i=1;i<=60;++i)t("EPSG:"+(32600+i),"+proj=utm +zone="+i+" +datum=WGS84 +units=m"),t("EPSG:"+(32700+i),"+proj=utm +zone="+i+" +south +datum=WGS84 +units=m");t.WGS84=t["EPSG:4326"],t["EPSG:3785"]=t["EPSG:3857"],t.GOOGLE=t["EPSG:3857"],t["EPSG:900913"]=t["EPSG:3857"],t["EPSG:102113"]=t["EPSG:3857"]}var it=1,at=2,ct=3,_a=4,Kt=5,vi=6378137,ua=6356752314e-3,yi=.0066943799901413165,vt=484813681109536e-20,m=Math.PI/2,ga=.16666666666666666,ma=.04722222222222222,da=.022156084656084655,v=1e-10,R=.017453292519943295,W=57.29577951308232,b=Math.PI/4,wt=Math.PI*2,I=3.14159265359,F={};F.greenwich=0;F.lisbon=-9.131906111111;F.paris=2.337229166667;F.bogota=-74.080916666667;F.madrid=-3.687938888889;F.rome=12.452333333333;F.bern=7.439583333333;F.jakarta=106.807719444444;F.ferro=-17.666666666667;F.brussels=4.367975;F.stockholm=18.058277777778;F.athens=23.7163375;F.oslo=10.722916666667;const Ma={mm:{to_meter:.001},cm:{to_meter:.01},ft:{to_meter:.3048},"us-ft":{to_meter:1200/3937},fath:{to_meter:1.8288},kmi:{to_meter:1852},"us-ch":{to_meter:20.1168402336805},"us-mi":{to_meter:1609.34721869444},km:{to_meter:1e3},"ind-ft":{to_meter:.30479841},"ind-yd":{to_meter:.91439523},mi:{to_meter:1609.344},yd:{to_meter:.9144},ch:{to_meter:20.1168},link:{to_meter:.201168},dm:{to_meter:.1},in:{to_meter:.0254},"ind-ch":{to_meter:20.11669506},"us-in":{to_meter:.025400050800101},"us-yd":{to_meter:.914401828803658}};var Si=/[\s_\-\/\(\)]/g;function X(t,i){if(t[i])return t[i];for(var a=Object.keys(t),s=i.toLowerCase().replace(Si,""),e=-1,r,h;++e<a.length;)if(r=a[e],h=r.toLowerCase().replace(Si,""),h===s)return t[r]}function Xt(t){var i={},a=t.split("+").map(function(n){return n.trim()}).filter(function(n){return n}).reduce(function(n,o){var l=o.split("=");return l.push(!0),n[l[0].toLowerCase()]=l[1],n},{}),s,e,r,h={proj:"projName",datum:"datumCode",rf:function(n){i.rf=parseFloat(n)},lat_0:function(n){i.lat0=n*R},lat_1:function(n){i.lat1=n*R},lat_2:function(n){i.lat2=n*R},lat_ts:function(n){i.lat_ts=n*R},lon_0:function(n){i.long0=n*R},lon_1:function(n){i.long1=n*R},lon_2:function(n){i.long2=n*R},alpha:function(n){i.alpha=parseFloat(n)*R},gamma:function(n){i.rectified_grid_angle=parseFloat(n)*R},lonc:function(n){i.longc=n*R},x_0:function(n){i.x0=parseFloat(n)},y_0:function(n){i.y0=parseFloat(n)},k_0:function(n){i.k0=parseFloat(n)},k:function(n){i.k0=parseFloat(n)},a:function(n){i.a=parseFloat(n)},b:function(n){i.b=parseFloat(n)},r:function(n){i.a=i.b=parseFloat(n)},r_a:function(){i.R_A=!0},zone:function(n){i.zone=parseInt(n,10)},south:function(){i.utmSouth=!0},towgs84:function(n){i.datum_params=n.split(",").map(function(o){return parseFloat(o)})},to_meter:function(n){i.to_meter=parseFloat(n)},units:function(n){i.units=n;var o=X(Ma,n);o&&(i.to_meter=o.to_meter)},from_greenwich:function(n){i.from_greenwich=n*R},pm:function(n){var o=X(F,n);i.from_greenwich=(o||parseFloat(n))*R},nadgrids:function(n){n==="@null"?i.datumCode="none":i.nadgrids=n},axis:function(n){var o="ewnsud";n.length===3&&o.indexOf(n.substr(0,1))!==-1&&o.indexOf(n.substr(1,1))!==-1&&o.indexOf(n.substr(2,1))!==-1&&(i.axis=n)},approx:function(){i.approx=!0}};for(s in a)e=a[s],s in h?(r=h[s],typeof r=="function"?r(e):i[r]=e):i[s]=e;return typeof i.datumCode=="string"&&i.datumCode!=="WGS84"&&(i.datumCode=i.datumCode.toLowerCase()),i}class qi{static getId(i){const a=i.find(s=>Array.isArray(s)&&s[0]==="ID");return a&&a.length>=3?{authority:a[1],code:parseInt(a[2],10)}:null}static convertUnit(i,a="unit"){if(!i||i.length<3)return{type:a,name:"unknown",conversion_factor:null};const s=i[1],e=parseFloat(i[2])||null,r=i.find(n=>Array.isArray(n)&&n[0]==="ID"),h=r?{authority:r[1],code:parseInt(r[2],10)}:null;return{type:a,name:s,conversion_factor:e,id:h}}static convertAxis(i){const a=i[1]||"Unknown";let s;const e=a.match(/^\((.)\)$/);if(e){const l=e[1].toUpperCase();if(l==="E")s="east";else if(l==="N")s="north";else if(l==="U")s="up";else throw new Error(`Unknown axis abbreviation: ${l}`)}else s=i[2]?i[2].toLowerCase():"unknown";const r=i.find(l=>Array.isArray(l)&&l[0]==="ORDER"),h=r?parseInt(r[1],10):null,n=i.find(l=>Array.isArray(l)&&(l[0]==="LENGTHUNIT"||l[0]==="ANGLEUNIT"||l[0]==="SCALEUNIT")),o=this.convertUnit(n);return{name:a,direction:s,unit:o,order:h}}static extractAxes(i){return i.filter(a=>Array.isArray(a)&&a[0]==="AXIS").map(a=>this.convertAxis(a)).sort((a,s)=>(a.order||0)-(s.order||0))}static convert(i,a={}){switch(i[0]){case"PROJCRS":a.type="ProjectedCRS",a.name=i[1],a.base_crs=i.find(c=>Array.isArray(c)&&c[0]==="BASEGEOGCRS")?this.convert(i.find(c=>Array.isArray(c)&&c[0]==="BASEGEOGCRS")):null,a.conversion=i.find(c=>Array.isArray(c)&&c[0]==="CONVERSION")?this.convert(i.find(c=>Array.isArray(c)&&c[0]==="CONVERSION")):null;const s=i.find(c=>Array.isArray(c)&&c[0]==="CS");s&&(a.coordinate_system={type:s[1],axis:this.extractAxes(i)});const e=i.find(c=>Array.isArray(c)&&c[0]==="LENGTHUNIT");if(e){const c=this.convertUnit(e);a.coordinate_system.unit=c}a.id=this.getId(i);break;case"BASEGEOGCRS":case"GEOGCRS":a.type="GeographicCRS",a.name=i[1];const r=i.find(c=>Array.isArray(c)&&(c[0]==="DATUM"||c[0]==="ENSEMBLE"));if(r){const c=this.convert(r);r[0]==="ENSEMBLE"?a.datum_ensemble=c:a.datum=c;const u=i.find(g=>Array.isArray(g)&&g[0]==="PRIMEM");u&&u[1]!=="Greenwich"&&(c.prime_meridian={name:u[1],longitude:parseFloat(u[2])})}a.coordinate_system={type:"ellipsoidal",axis:this.extractAxes(i)},a.id=this.getId(i);break;case"DATUM":a.type="GeodeticReferenceFrame",a.name=i[1],a.ellipsoid=i.find(c=>Array.isArray(c)&&c[0]==="ELLIPSOID")?this.convert(i.find(c=>Array.isArray(c)&&c[0]==="ELLIPSOID")):null;break;case"ENSEMBLE":a.type="DatumEnsemble",a.name=i[1],a.members=i.filter(c=>Array.isArray(c)&&c[0]==="MEMBER").map(c=>({type:"DatumEnsembleMember",name:c[1],id:this.getId(c)}));const h=i.find(c=>Array.isArray(c)&&c[0]==="ENSEMBLEACCURACY");h&&(a.accuracy=parseFloat(h[1]));const n=i.find(c=>Array.isArray(c)&&c[0]==="ELLIPSOID");n&&(a.ellipsoid=this.convert(n)),a.id=this.getId(i);break;case"ELLIPSOID":a.type="Ellipsoid",a.name=i[1],a.semi_major_axis=parseFloat(i[2]),a.inverse_flattening=parseFloat(i[3]),i.find(c=>Array.isArray(c)&&c[0]==="LENGTHUNIT")&&this.convert(i.find(c=>Array.isArray(c)&&c[0]==="LENGTHUNIT"),a);break;case"CONVERSION":a.type="Conversion",a.name=i[1],a.method=i.find(c=>Array.isArray(c)&&c[0]==="METHOD")?this.convert(i.find(c=>Array.isArray(c)&&c[0]==="METHOD")):null,a.parameters=i.filter(c=>Array.isArray(c)&&c[0]==="PARAMETER").map(c=>this.convert(c));break;case"METHOD":a.type="Method",a.name=i[1],a.id=this.getId(i);break;case"PARAMETER":a.type="Parameter",a.name=i[1],a.value=parseFloat(i[2]),a.unit=this.convertUnit(i.find(c=>Array.isArray(c)&&(c[0]==="LENGTHUNIT"||c[0]==="ANGLEUNIT"||c[0]==="SCALEUNIT"))),a.id=this.getId(i);break;case"BOUNDCRS":a.type="BoundCRS";const o=i.find(c=>Array.isArray(c)&&c[0]==="SOURCECRS");if(o){const c=o.find(u=>Array.isArray(u));a.source_crs=c?this.convert(c):null}const l=i.find(c=>Array.isArray(c)&&c[0]==="TARGETCRS");if(l){const c=l.find(u=>Array.isArray(u));a.target_crs=c?this.convert(c):null}const _=i.find(c=>Array.isArray(c)&&c[0]==="ABRIDGEDTRANSFORMATION");_?a.transformation=this.convert(_):a.transformation=null;break;case"ABRIDGEDTRANSFORMATION":if(a.type="Transformation",a.name=i[1],a.method=i.find(c=>Array.isArray(c)&&c[0]==="METHOD")?this.convert(i.find(c=>Array.isArray(c)&&c[0]==="METHOD")):null,a.parameters=i.filter(c=>Array.isArray(c)&&(c[0]==="PARAMETER"||c[0]==="PARAMETERFILE")).map(c=>{if(c[0]==="PARAMETER")return this.convert(c);if(c[0]==="PARAMETERFILE")return{name:c[1],value:c[2],id:{authority:"EPSG",code:8656}}}),a.parameters.length===7){const c=a.parameters[6];c.name==="Scale difference"&&(c.value=Math.round((c.value-1)*1e12)/1e6)}a.id=this.getId(i);break;case"AXIS":a.coordinate_system||(a.coordinate_system={type:"unspecified",axis:[]}),a.coordinate_system.axis.push(this.convertAxis(i));break;case"LENGTHUNIT":const f=this.convertUnit(i,"LinearUnit");a.coordinate_system&&a.coordinate_system.axis&&a.coordinate_system.axis.forEach(c=>{c.unit||(c.unit=f)}),f.conversion_factor&&f.conversion_factor!==1&&a.semi_major_axis&&(a.semi_major_axis={value:a.semi_major_axis,unit:f});break;default:a.keyword=i[0];break}return a}}class va extends qi{static convert(i,a={}){return super.convert(i,a),a.coordinate_system&&a.coordinate_system.subtype==="Cartesian"&&delete a.coordinate_system,a.usage&&delete a.usage,a}}class ya extends qi{static convert(i,a={}){super.convert(i,a);const s=i.find(r=>Array.isArray(r)&&r[0]==="CS");s&&(a.coordinate_system={subtype:s[1],axis:this.extractAxes(i)});const e=i.find(r=>Array.isArray(r)&&r[0]==="USAGE");if(e){const r=e.find(o=>Array.isArray(o)&&o[0]==="SCOPE"),h=e.find(o=>Array.isArray(o)&&o[0]==="AREA"),n=e.find(o=>Array.isArray(o)&&o[0]==="BBOX");a.usage={},r&&(a.usage.scope=r[1]),h&&(a.usage.area=h[1]),n&&(a.usage.bbox=n.slice(1))}return a}}function Sa(t){return t.find(i=>Array.isArray(i)&&i[0]==="USAGE")?"2019":(t.find(i=>Array.isArray(i)&&i[0]==="CS")||t[0]==="BOUNDCRS"||t[0]==="PROJCRS"||t[0]==="GEOGCRS","2015")}function Ea(t){return(Sa(t)==="2019"?ya:va).convert(t)}function Ga(t){const i=t.toUpperCase();return i.includes("PROJCRS")||i.includes("GEOGCRS")||i.includes("BOUNDCRS")||i.includes("VERTCRS")||i.includes("LENGTHUNIT")||i.includes("ANGLEUNIT")||i.includes("SCALEUNIT")?"WKT2":(i.includes("PROJCS")||i.includes("GEOGCS")||i.includes("LOCAL_CS")||i.includes("VERT_CS")||i.includes("UNIT"),"WKT1")}var Pt=1,Fi=2,ji=3,Ft=4,Ui=5,ei=-1,wa=/\s/,Pa=/[A-Za-z]/,xa=/[A-Za-z84_]/,Wt=/[,\]]/,$i=/[\d\.E\-\+]/;function Q(t){if(typeof t!="string")throw new Error("not a string");this.text=t.trim(),this.level=0,this.place=0,this.root=null,this.stack=[],this.currentObject=null,this.state=Pt}Q.prototype.readCharicter=function(){var t=this.text[this.place++];if(this.state!==Ft)for(;wa.test(t);){if(this.place>=this.text.length)return;t=this.text[this.place++]}switch(this.state){case Pt:return this.neutral(t);case Fi:return this.keyword(t);case Ft:return this.quoted(t);case Ui:return this.afterquote(t);case ji:return this.number(t);case ei:return}};Q.prototype.afterquote=function(t){if(t==='"'){this.word+='"',this.state=Ft;return}if(Wt.test(t)){this.word=this.word.trim(),this.afterItem(t);return}throw new Error(`havn't handled "`+t+'" in afterquote yet, index '+this.place)};Q.prototype.afterItem=function(t){if(t===","){this.word!==null&&this.currentObject.push(this.word),this.word=null,this.state=Pt;return}if(t==="]"){this.level--,this.word!==null&&(this.currentObject.push(this.word),this.word=null),this.state=Pt,this.currentObject=this.stack.pop(),this.currentObject||(this.state=ei);return}};Q.prototype.number=function(t){if($i.test(t)){this.word+=t;return}if(Wt.test(t)){this.word=parseFloat(this.word),this.afterItem(t);return}throw new Error(`havn't handled "`+t+'" in number yet, index '+this.place)};Q.prototype.quoted=function(t){if(t==='"'){this.state=Ui;return}this.word+=t};Q.prototype.keyword=function(t){if(xa.test(t)){this.word+=t;return}if(t==="["){var i=[];i.push(this.word),this.level++,this.root===null?this.root=i:this.currentObject.push(i),this.stack.push(this.currentObject),this.currentObject=i,this.state=Pt;return}if(Wt.test(t)){this.afterItem(t);return}throw new Error(`havn't handled "`+t+'" in keyword yet, index '+this.place)};Q.prototype.neutral=function(t){if(Pa.test(t)){this.word=t,this.state=Fi;return}if(t==='"'){this.word="",this.state=Ft;return}if($i.test(t)){this.word=t,this.state=ji;return}if(Wt.test(t)){this.afterItem(t);return}throw new Error(`havn't handled "`+t+'" in neutral yet, index '+this.place)};Q.prototype.output=function(){for(;this.place<this.text.length;)this.readCharicter();if(this.state===ei)return this.root;throw new Error('unable to parse string "'+this.text+'". State is '+this.state)};function ba(t){var i=new Q(t);return i.output()}function Ht(t,i,a){Array.isArray(i)&&(a.unshift(i),i=null);var s=i?{}:t,e=a.reduce(function(r,h){return nt(h,r),r},s);i&&(t[i]=e)}function nt(t,i){if(!Array.isArray(t)){i[t]=!0;return}var a=t.shift();if(a==="PARAMETER"&&(a=t.shift()),t.length===1){if(Array.isArray(t[0])){i[a]={},nt(t[0],i[a]);return}i[a]=t[0];return}if(!t.length){i[a]=!0;return}if(a==="TOWGS84"){i[a]=t;return}if(a==="AXIS"){a in i||(i[a]=[]),i[a].push(t);return}Array.isArray(a)||(i[a]={});var s;switch(a){case"UNIT":case"PRIMEM":case"VERT_DATUM":i[a]={name:t[0].toLowerCase(),convert:t[1]},t.length===3&&nt(t[2],i[a]);return;case"SPHEROID":case"ELLIPSOID":i[a]={name:t[0],a:t[1],rf:t[2]},t.length===4&&nt(t[3],i[a]);return;case"EDATUM":case"ENGINEERINGDATUM":case"LOCAL_DATUM":case"DATUM":case"VERT_CS":case"VERTCRS":case"VERTICALCRS":t[0]=["name",t[0]],Ht(i,a,t);return;case"COMPD_CS":case"COMPOUNDCRS":case"FITTED_CS":case"PROJECTEDCRS":case"PROJCRS":case"GEOGCS":case"GEOCCS":case"PROJCS":case"LOCAL_CS":case"GEODCRS":case"GEODETICCRS":case"GEODETICDATUM":case"ENGCRS":case"ENGINEERINGCRS":t[0]=["name",t[0]],Ht(i,a,t),i[a].type=a;return;default:for(s=-1;++s<t.length;)if(!Array.isArray(t[s]))return nt(t,i[a]);return Ht(i,a,t)}}var pa=.017453292519943295;function $(t){return t*pa}function Bi(t){const i=(t.projName||"").toLowerCase().replace(/_/g," ");!t.long0&&t.longc&&(i==="albers conic equal area"||i==="lambert azimuthal equal area")&&(t.long0=t.longc),!t.lat_ts&&t.lat1&&(i==="stereographic south pole"||i==="polar stereographic (variant b)")?(t.lat0=$(t.lat1>0?90:-90),t.lat_ts=t.lat1,delete t.lat1):!t.lat_ts&&t.lat0&&(i==="polar stereographic"||i==="polar stereographic (variant a)")&&(t.lat_ts=t.lat0,t.lat0=$(t.lat0>0?90:-90),delete t.lat1)}function Ei(t){let i={units:null,to_meter:void 0};return typeof t=="string"?(i.units=t.toLowerCase(),i.units==="metre"&&(i.units="meter"),i.units==="meter"&&(i.to_meter=1)):t&&t.name&&(i.units=t.name.toLowerCase(),i.units==="metre"&&(i.units="meter"),i.to_meter=t.conversion_factor),i}function Gi(t){return typeof t=="object"?t.value*t.unit.conversion_factor:t}function wi(t,i){t.ellipsoid.radius?(i.a=t.ellipsoid.radius,i.rf=0):(i.a=Gi(t.ellipsoid.semi_major_axis),t.ellipsoid.inverse_flattening!==void 0?i.rf=t.ellipsoid.inverse_flattening:t.ellipsoid.semi_major_axis!==void 0&&t.ellipsoid.semi_minor_axis!==void 0&&(i.rf=i.a/(i.a-Gi(t.ellipsoid.semi_minor_axis))))}function jt(t,i={}){return!t||typeof t!="object"?t:t.type==="BoundCRS"?(jt(t.source_crs,i),t.transformation&&(t.transformation.method&&t.transformation.method.name==="NTv2"?i.nadgrids=t.transformation.parameters[0].value:i.datum_params=t.transformation.parameters.map(a=>a.value)),i):(Object.keys(t).forEach(a=>{const s=t[a];if(s!==null)switch(a){case"name":if(i.srsCode)break;i.name=s,i.srsCode=s;break;case"type":s==="GeographicCRS"?i.projName="longlat":s==="ProjectedCRS"&&t.conversion&&t.conversion.method&&(i.projName=t.conversion.method.name);break;case"datum":case"datum_ensemble":s.ellipsoid&&(i.ellps=s.ellipsoid.name,wi(s,i)),s.prime_meridian&&(i.from_greenwich=s.prime_meridian.longitude*Math.PI/180);break;case"ellipsoid":i.ellps=s.name,wi(s,i);break;case"prime_meridian":i.long0=(s.longitude||0)*Math.PI/180;break;case"coordinate_system":if(s.axis){if(i.axis=s.axis.map(e=>{const r=e.direction;if(r==="east")return"e";if(r==="north")return"n";if(r==="west")return"w";if(r==="south")return"s";throw new Error(`Unknown axis direction: ${r}`)}).join("")+"u",s.unit){const{units:e,to_meter:r}=Ei(s.unit);i.units=e,i.to_meter=r}else if(s.axis[0]&&s.axis[0].unit){const{units:e,to_meter:r}=Ei(s.axis[0].unit);i.units=e,i.to_meter=r}}break;case"id":s.authority&&s.code&&(i.title=s.authority+":"+s.code);break;case"conversion":s.method&&s.method.name&&(i.projName=s.method.name),s.parameters&&s.parameters.forEach(e=>{const r=e.name.toLowerCase().replace(/\s+/g,"_"),h=e.value;e.unit&&e.unit.conversion_factor?i[r]=h*e.unit.conversion_factor:e.unit==="degree"?i[r]=h*Math.PI/180:i[r]=h});break;case"unit":s.name&&(i.units=s.name.toLowerCase(),i.units==="metre"&&(i.units="meter")),s.conversion_factor&&(i.to_meter=s.conversion_factor);break;case"base_crs":jt(s,i),i.datumCode=s.id?s.id.authority+"_"+s.id.code:s.name;break}}),i.latitude_of_false_origin!==void 0&&(i.lat0=i.latitude_of_false_origin),i.longitude_of_false_origin!==void 0&&(i.long0=i.longitude_of_false_origin),i.latitude_of_standard_parallel!==void 0&&(i.lat0=i.latitude_of_standard_parallel,i.lat1=i.latitude_of_standard_parallel),i.latitude_of_1st_standard_parallel!==void 0&&(i.lat1=i.latitude_of_1st_standard_parallel),i.latitude_of_2nd_standard_parallel!==void 0&&(i.lat2=i.latitude_of_2nd_standard_parallel),i.latitude_of_projection_centre!==void 0&&(i.lat0=i.latitude_of_projection_centre),i.longitude_of_projection_centre!==void 0&&(i.longc=i.longitude_of_projection_centre),i.easting_at_false_origin!==void 0&&(i.x0=i.easting_at_false_origin),i.northing_at_false_origin!==void 0&&(i.y0=i.northing_at_false_origin),i.latitude_of_natural_origin!==void 0&&(i.lat0=i.latitude_of_natural_origin),i.longitude_of_natural_origin!==void 0&&(i.long0=i.longitude_of_natural_origin),i.longitude_of_origin!==void 0&&(i.long0=i.longitude_of_origin),i.false_easting!==void 0&&(i.x0=i.false_easting),i.easting_at_projection_centre&&(i.x0=i.easting_at_projection_centre),i.false_northing!==void 0&&(i.y0=i.false_northing),i.northing_at_projection_centre&&(i.y0=i.northing_at_projection_centre),i.standard_parallel_1!==void 0&&(i.lat1=i.standard_parallel_1),i.standard_parallel_2!==void 0&&(i.lat2=i.standard_parallel_2),i.scale_factor_at_natural_origin!==void 0&&(i.k0=i.scale_factor_at_natural_origin),i.scale_factor_at_projection_centre!==void 0&&(i.k0=i.scale_factor_at_projection_centre),i.scale_factor_on_pseudo_standard_parallel!==void 0&&(i.k0=i.scale_factor_on_pseudo_standard_parallel),i.azimuth!==void 0&&(i.alpha=i.azimuth),i.azimuth_at_projection_centre!==void 0&&(i.alpha=i.azimuth_at_projection_centre),i.angle_from_rectified_to_skew_grid&&(i.rectified_grid_angle=i.angle_from_rectified_to_skew_grid),Bi(i),i)}var Ca=["PROJECTEDCRS","PROJCRS","GEOGCS","GEOCCS","PROJCS","LOCAL_CS","GEODCRS","GEODETICCRS","GEODETICDATUM","ENGCRS","ENGINEERINGCRS"];function Aa(t,i){var a=i[0],s=i[1];!(a in t)&&s in t&&(t[a]=t[s],i.length===3&&(t[a]=i[2](t[a])))}function zi(t){for(var i=Object.keys(t),a=0,s=i.length;a<s;++a){var e=i[a];Ca.indexOf(e)!==-1&&Na(t[e]),typeof t[e]=="object"&&zi(t[e])}}function Na(t){if(t.AUTHORITY){var i=Object.keys(t.AUTHORITY)[0];i&&i in t.AUTHORITY&&(t.title=i+":"+t.AUTHORITY[i])}if(t.type==="GEOGCS"?t.projName="longlat":t.type==="LOCAL_CS"?(t.projName="identity",t.local=!0):typeof t.PROJECTION=="object"?t.projName=Object.keys(t.PROJECTION)[0]:t.projName=t.PROJECTION,t.AXIS){for(var a="",s=0,e=t.AXIS.length;s<e;++s){var r=[t.AXIS[s][0].toLowerCase(),t.AXIS[s][1].toLowerCase()];r[0].indexOf("north")!==-1||(r[0]==="y"||r[0]==="lat")&&r[1]==="north"?a+="n":r[0].indexOf("south")!==-1||(r[0]==="y"||r[0]==="lat")&&r[1]==="south"?a+="s":r[0].indexOf("east")!==-1||(r[0]==="x"||r[0]==="lon")&&r[1]==="east"?a+="e":(r[0].indexOf("west")!==-1||(r[0]==="x"||r[0]==="lon")&&r[1]==="west")&&(a+="w")}a.length===2&&(a+="u"),a.length===3&&(t.axis=a)}t.UNIT&&(t.units=t.UNIT.name.toLowerCase(),t.units==="metre"&&(t.units="meter"),t.UNIT.convert&&(t.type==="GEOGCS"?t.DATUM&&t.DATUM.SPHEROID&&(t.to_meter=t.UNIT.convert*t.DATUM.SPHEROID.a):t.to_meter=t.UNIT.convert));var h=t.GEOGCS;t.type==="GEOGCS"&&(h=t),h&&(h.DATUM?t.datumCode=h.DATUM.name.toLowerCase():t.datumCode=h.name.toLowerCase(),t.datumCode.slice(0,2)==="d_"&&(t.datumCode=t.datumCode.slice(2)),t.datumCode==="new_zealand_1949"&&(t.datumCode="nzgd49"),(t.datumCode==="wgs_1984"||t.datumCode==="world_geodetic_system_1984")&&(t.PROJECTION==="Mercator_Auxiliary_Sphere"&&(t.sphere=!0),t.datumCode="wgs84"),t.datumCode==="belge_1972"&&(t.datumCode="rnb72"),h.DATUM&&h.DATUM.SPHEROID&&(t.ellps=h.DATUM.SPHEROID.name.replace("_19","").replace(/[Cc]larke\_18/,"clrk"),t.ellps.toLowerCase().slice(0,13)==="international"&&(t.ellps="intl"),t.a=h.DATUM.SPHEROID.a,t.rf=parseFloat(h.DATUM.SPHEROID.rf,10)),h.DATUM&&h.DATUM.TOWGS84&&(t.datum_params=h.DATUM.TOWGS84),~t.datumCode.indexOf("osgb_1936")&&(t.datumCode="osgb36"),~t.datumCode.indexOf("osni_1952")&&(t.datumCode="osni52"),(~t.datumCode.indexOf("tm65")||~t.datumCode.indexOf("geodetic_datum_of_1965"))&&(t.datumCode="ire65"),t.datumCode==="ch1903+"&&(t.datumCode="ch1903"),~t.datumCode.indexOf("israel")&&(t.datumCode="isr93")),t.b&&!isFinite(t.b)&&(t.b=t.a),t.rectified_grid_angle&&(t.rectified_grid_angle=$(t.rectified_grid_angle));function n(_){var f=t.to_meter||1;return _*f}var o=function(_){return Aa(t,_)},l=[["standard_parallel_1","Standard_Parallel_1"],["standard_parallel_1","Latitude of 1st standard parallel"],["standard_parallel_2","Standard_Parallel_2"],["standard_parallel_2","Latitude of 2nd standard parallel"],["false_easting","False_Easting"],["false_easting","False easting"],["false-easting","Easting at false origin"],["false_northing","False_Northing"],["false_northing","False northing"],["false_northing","Northing at false origin"],["central_meridian","Central_Meridian"],["central_meridian","Longitude of natural origin"],["central_meridian","Longitude of false origin"],["latitude_of_origin","Latitude_Of_Origin"],["latitude_of_origin","Central_Parallel"],["latitude_of_origin","Latitude of natural origin"],["latitude_of_origin","Latitude of false origin"],["scale_factor","Scale_Factor"],["k0","scale_factor"],["latitude_of_center","Latitude_Of_Center"],["latitude_of_center","Latitude_of_center"],["lat0","latitude_of_center",$],["longitude_of_center","Longitude_Of_Center"],["longitude_of_center","Longitude_of_center"],["longc","longitude_of_center",$],["x0","false_easting",n],["y0","false_northing",n],["long0","central_meridian",$],["lat0","latitude_of_origin",$],["lat0","standard_parallel_1",$],["lat1","standard_parallel_1",$],["lat2","standard_parallel_2",$],["azimuth","Azimuth"],["alpha","azimuth",$],["srsCode","name"]];l.forEach(o),Bi(t)}function Yt(t){if(typeof t=="object")return jt(t);const i=Ga(t);var a=ba(t);if(i==="WKT2"){const r=Ea(a);return jt(r)}var s=a[0],e={};return nt(a,e),zi(e),e[s]}function L(t){var i=this;if(arguments.length===2){var a=arguments[1];typeof a=="string"?a.charAt(0)==="+"?L[t]=Xt(arguments[1]):L[t]=Yt(arguments[1]):L[t]=a}else if(arguments.length===1){if(Array.isArray(t))return t.map(function(s){return Array.isArray(s)?L.apply(i,s):L(s)});if(typeof t=="string"){if(t in L)return L[t]}else"EPSG"in t?L["EPSG:"+t.EPSG]=t:"ESRI"in t?L["ESRI:"+t.ESRI]=t:"IAU2000"in t?L["IAU2000:"+t.IAU2000]=t:console.log(t);return}}fa(L);function Ra(t){return typeof t=="string"}function Ia(t){return t in L}function Ta(t){return t.indexOf("+")!==0&&t.indexOf("[")!==-1||typeof t=="object"&&!("srsCode"in t)}var Oa=["3857","900913","3785","102113"];function La(t){var i=X(t,"authority");if(i){var a=X(i,"epsg");return a&&Oa.indexOf(a)>-1}}function ka(t){var i=X(t,"extension");if(i)return X(i,"proj4")}function Da(t){return t[0]==="+"}function qa(t){if(Ra(t)){if(Ia(t))return L[t];if(Ta(t)){var i=Yt(t);if(La(i))return L["EPSG:3857"];var a=ka(i);return a?Xt(a):i}if(Da(t))return Xt(t)}else return"projName"in t?t:Yt(t)}function Pi(t,i){t=t||{};var a,s;if(!i)return t;for(s in i)a=i[s],a!==void 0&&(t[s]=a);return t}function H(t,i,a){var s=t*i;return a/Math.sqrt(1-s*s)}function pt(t){return t<0?-1:1}function y(t){return Math.abs(t)<=I?t:t-pt(t)*wt}function B(t,i,a){var s=t*a,e=.5*t;return s=Math.pow((1-s)/(1+s),e),Math.tan(.5*(m-i))/s}function xt(t,i){for(var a=.5*t,s,e,r=m-2*Math.atan(i),h=0;h<=15;h++)if(s=t*Math.sin(r),e=m-2*Math.atan(i*Math.pow((1-s)/(1+s),a))-r,r+=e,Math.abs(e)<=1e-10)return r;return-9999}function Fa(){var t=this.b/this.a;this.es=1-t*t,"x0"in this||(this.x0=0),"y0"in this||(this.y0=0),this.e=Math.sqrt(this.es),this.lat_ts?this.sphere?this.k0=Math.cos(this.lat_ts):this.k0=H(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)):this.k0||(this.k?this.k0=this.k:this.k0=1)}function ja(t){var i=t.x,a=t.y;if(a*W>90&&a*W<-90&&i*W>180&&i*W<-180)return null;var s,e;if(Math.abs(Math.abs(a)-m)<=v)return null;if(this.sphere)s=this.x0+this.a*this.k0*y(i-this.long0),e=this.y0+this.a*this.k0*Math.log(Math.tan(b+.5*a));else{var r=Math.sin(a),h=B(this.e,a,r);s=this.x0+this.a*this.k0*y(i-this.long0),e=this.y0-this.a*this.k0*Math.log(h)}return t.x=s,t.y=e,t}function Ua(t){var i=t.x-this.x0,a=t.y-this.y0,s,e;if(this.sphere)e=m-2*Math.atan(Math.exp(-a/(this.a*this.k0)));else{var r=Math.exp(-a/(this.a*this.k0));if(e=xt(this.e,r),e===-9999)return null}return s=y(this.long0+i/(this.a*this.k0)),t.x=s,t.y=e,t}var $a=["Mercator","Popular Visualisation Pseudo Mercator","Mercator_1SP","Mercator_Auxiliary_Sphere","Mercator_Variant_A","merc"];const Ba={init:Fa,forward:ja,inverse:Ua,names:$a};function za(){}function xi(t){return t}var Wa=["longlat","identity"];const Ha={init:za,forward:xi,inverse:xi,names:Wa};var Qa=[Ba,Ha],tt={},ht=[];function Wi(t,i){var a=ht.length;return t.names?(ht[a]=t,t.names.forEach(function(s){tt[s.toLowerCase()]=a}),this):(console.log(i),!0)}function Hi(t){return t.replace(/[-\(\)\s]+/g," ").trim().replace(/ /g,"_")}function Ja(t){if(!t)return!1;var i=t.toLowerCase();if(typeof tt[i]!="undefined"&&ht[tt[i]]||(i=Hi(i),i in tt&&ht[tt[i]]))return ht[tt[i]]}function Va(){Qa.forEach(Wi)}const Ka={start:Va,add:Wi,get:Ja};var Qi={MERIT:{a:6378137,rf:298.257,ellipseName:"MERIT 1983"},SGS85:{a:6378136,rf:298.257,ellipseName:"Soviet Geodetic System 85"},GRS80:{a:6378137,rf:298.*********,ellipseName:"GRS 1980(IUGG, 1980)"},IAU76:{a:6378140,rf:298.257,ellipseName:"IAU 1976"},airy:{a:6377563396e-3,b:635625691e-2,ellipseName:"Airy 1830"},APL4:{a:6378137,rf:298.25,ellipseName:"Appl. Physics. 1965"},NWL9D:{a:6378145,rf:298.25,ellipseName:"Naval Weapons Lab., 1965"},mod_airy:{a:6377340189e-3,b:6356034446e-3,ellipseName:"Modified Airy"},andrae:{a:637710443e-2,rf:300,ellipseName:"Andrae 1876 (Den., Iclnd.)"},aust_SA:{a:6378160,rf:298.25,ellipseName:"Australian Natl & S. Amer. 1969"},GRS67:{a:6378160,rf:298.*********,ellipseName:"GRS 67(IUGG 1967)"},bessel:{a:6377397155e-3,rf:299.1528128,ellipseName:"Bessel 1841"},bess_nam:{a:6377483865e-3,rf:299.1528128,ellipseName:"Bessel 1841 (Namibia)"},clrk66:{a:63782064e-1,b:63565838e-1,ellipseName:"Clarke 1866"},clrk80:{a:6378249145e-3,rf:293.4663,ellipseName:"Clarke 1880 mod."},clrk80ign:{a:63782492e-1,b:6356515,rf:293.4660213,ellipseName:"Clarke 1880 (IGN)"},clrk58:{a:6378293645208759e-9,rf:294.2606763692654,ellipseName:"Clarke 1858"},CPM:{a:63757387e-1,rf:334.29,ellipseName:"Comm. des Poids et Mesures 1799"},delmbr:{a:6376428,rf:311.5,ellipseName:"Delambre 1810 (Belgium)"},engelis:{a:637813605e-2,rf:298.2566,ellipseName:"Engelis 1985"},evrst30:{a:6377276345e-3,rf:300.8017,ellipseName:"Everest 1830"},evrst48:{a:6377304063e-3,rf:300.8017,ellipseName:"Everest 1948"},evrst56:{a:6377301243e-3,rf:300.8017,ellipseName:"Everest 1956"},evrst69:{a:6377295664e-3,rf:300.8017,ellipseName:"Everest 1969"},evrstSS:{a:6377298556e-3,rf:300.8017,ellipseName:"Everest (Sabah & Sarawak)"},fschr60:{a:6378166,rf:298.3,ellipseName:"Fischer (Mercury Datum) 1960"},fschr60m:{a:6378155,rf:298.3,ellipseName:"Fischer 1960"},fschr68:{a:6378150,rf:298.3,ellipseName:"Fischer 1968"},helmert:{a:6378200,rf:298.3,ellipseName:"Helmert 1906"},hough:{a:6378270,rf:297,ellipseName:"Hough"},intl:{a:6378388,rf:297,ellipseName:"International 1909 (Hayford)"},kaula:{a:6378163,rf:298.24,ellipseName:"Kaula 1961"},lerch:{a:6378139,rf:298.257,ellipseName:"Lerch 1979"},mprts:{a:6397300,rf:191,ellipseName:"Maupertius 1738"},new_intl:{a:63781575e-1,b:63567722e-1,ellipseName:"New International 1967"},plessis:{a:6376523,rf:6355863,ellipseName:"Plessis 1817 (France)"},krass:{a:6378245,rf:298.3,ellipseName:"Krassovsky, 1942"},SEasia:{a:6378155,b:63567733205e-4,ellipseName:"Southeast Asia"},walbeck:{a:6376896,b:63558348467e-4,ellipseName:"Walbeck"},WGS60:{a:6378165,rf:298.3,ellipseName:"WGS 60"},WGS66:{a:6378145,rf:298.25,ellipseName:"WGS 66"},WGS7:{a:6378135,rf:298.26,ellipseName:"WGS 72"},WGS84:{a:6378137,rf:298.257223563,ellipseName:"WGS 84"},sphere:{a:6370997,b:6370997,ellipseName:"Normal Sphere (r=6370997)"}};const Xa=Qi.WGS84;function Ya(t,i,a,s){var e=t*t,r=i*i,h=(e-r)/e,n=0;s?(t*=1-h*(ga+h*(ma+h*da)),e=t*t,h=0):n=Math.sqrt(h);var o=(e-r)/r;return{es:h,e:n,ep2:o}}function Za(t,i,a,s,e){if(!t){var r=X(Qi,s);r||(r=Xa),t=r.a,i=r.b,a=r.rf}return a&&!i&&(i=(1-1/a)*t),(a===0||Math.abs(t-i)<v)&&(e=!0,i=t),{a:t,b:i,rf:a,sphere:e}}var Lt={wgs84:{towgs84:"0,0,0",ellipse:"WGS84",datumName:"WGS84"},ch1903:{towgs84:"674.374,15.056,405.346",ellipse:"bessel",datumName:"swiss"},ggrs87:{towgs84:"-199.87,74.79,246.62",ellipse:"GRS80",datumName:"Greek_Geodetic_Reference_System_1987"},nad83:{towgs84:"0,0,0",ellipse:"GRS80",datumName:"North_American_Datum_1983"},nad27:{nadgrids:"@conus,@alaska,@ntv2_0.gsb,@ntv1_can.dat",ellipse:"clrk66",datumName:"North_American_Datum_1927"},potsdam:{towgs84:"598.1,73.7,418.2,0.202,0.045,-2.455,6.7",ellipse:"bessel",datumName:"Potsdam Rauenberg 1950 DHDN"},carthage:{towgs84:"-263.0,6.0,431.0",ellipse:"clark80",datumName:"Carthage 1934 Tunisia"},hermannskogel:{towgs84:"577.326,90.129,463.919,5.137,1.474,5.297,2.4232",ellipse:"bessel",datumName:"Hermannskogel"},mgi:{towgs84:"577.326,90.129,463.919,5.137,1.474,5.297,2.4232",ellipse:"bessel",datumName:"Militar-Geographische Institut"},osni52:{towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"airy",datumName:"Irish National"},ire65:{towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"mod_airy",datumName:"Ireland 1965"},rassadiran:{towgs84:"-133.63,-157.5,-158.62",ellipse:"intl",datumName:"Rassadiran"},nzgd49:{towgs84:"59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993",ellipse:"intl",datumName:"New Zealand Geodetic Datum 1949"},osgb36:{towgs84:"446.448,-125.157,542.060,0.1502,0.2470,0.8421,-20.4894",ellipse:"airy",datumName:"Ordnance Survey of Great Britain 1936"},s_jtsk:{towgs84:"589,76,480",ellipse:"bessel",datumName:"S-JTSK (Ferro)"},beduaram:{towgs84:"-106,-87,188",ellipse:"clrk80",datumName:"Beduaram"},gunung_segara:{towgs84:"-403,684,41",ellipse:"bessel",datumName:"Gunung Segara Jakarta"},rnb72:{towgs84:"106.869,-52.2978,103.724,-0.33657,0.456955,-1.84218,1",ellipse:"intl",datumName:"Reseau National Belge 1972"},EPSG_5451:{towgs84:"6.41,-49.05,-11.28,1.5657,0.5242,6.9718,-5.7649"},IGNF_LURESG:{towgs84:"-192.986,13.673,-39.309,-0.4099,-2.9332,2.6881,0.43"},EPSG_4614:{towgs84:"-119.4248,-303.65872,-11.00061,1.164298,0.174458,1.096259,3.657065"},EPSG_4615:{towgs84:"-494.088,-312.129,279.877,-1.423,-1.013,1.59,-0.748"},ESRI_37241:{towgs84:"-76.822,257.457,-12.817,2.136,-0.033,-2.392,-0.031"},ESRI_37249:{towgs84:"-440.296,58.548,296.265,1.128,10.202,4.559,-0.438"},ESRI_37245:{towgs84:"-511.151,-181.269,139.609,1.05,2.703,1.798,3.071"},EPSG_4178:{towgs84:"24.9,-126.4,-93.2,-0.063,-0.247,-0.041,1.01"},EPSG_4622:{towgs84:"-472.29,-5.63,-304.12,0.4362,-0.8374,0.2563,1.8984"},EPSG_4625:{towgs84:"126.93,547.94,130.41,-2.7867,5.1612,-0.8584,13.8227"},EPSG_5252:{towgs84:"0.023,0.036,-0.068,0.00176,0.00912,-0.01136,0.00439"},EPSG_4314:{towgs84:"597.1,71.4,412.1,0.894,0.068,-1.563,7.58"},EPSG_4282:{towgs84:"-178.3,-316.7,-131.5,5.278,6.077,10.979,19.166"},EPSG_4231:{towgs84:"-83.11,-97.38,-117.22,0.0276,-0.2167,0.2147,0.1218"},EPSG_4274:{towgs84:"-230.994,102.591,25.199,0.633,-0.239,0.9,1.95"},EPSG_4134:{towgs84:"-180.624,-225.516,173.919,-0.81,-1.898,8.336,16.71006"},EPSG_4254:{towgs84:"18.38,192.45,96.82,0.056,-0.142,-0.2,-0.0013"},EPSG_4159:{towgs84:"-194.513,-63.978,-25.759,-3.4027,3.756,-3.352,-0.9175"},EPSG_4687:{towgs84:"0.072,-0.507,-0.245,0.0183,-0.0003,0.007,-0.0093"},EPSG_4227:{towgs84:"-83.58,-397.54,458.78,-17.595,-2.847,4.256,3.225"},EPSG_4746:{towgs84:"599.4,72.4,419.2,-0.062,-0.022,-2.723,6.46"},EPSG_4745:{towgs84:"612.4,77,440.2,-0.054,0.057,-2.797,2.55"},EPSG_6311:{towgs84:"8.846,-4.394,-1.122,-0.00237,-0.146528,0.130428,0.783926"},EPSG_4289:{towgs84:"565.7381,50.4018,465.2904,-1.91514,1.60363,-9.09546,4.07244"},EPSG_4230:{towgs84:"-68.863,-134.888,-111.49,-0.53,-0.14,0.57,-3.4"},EPSG_4154:{towgs84:"-123.02,-158.95,-168.47"},EPSG_4156:{towgs84:"570.8,85.7,462.8,4.998,1.587,5.261,3.56"},EPSG_4299:{towgs84:"482.5,-130.6,564.6,-1.042,-0.214,-0.631,8.15"},EPSG_4179:{towgs84:"33.4,-146.6,-76.3,-0.359,-0.053,0.844,-0.84"},EPSG_4313:{towgs84:"-106.8686,52.2978,-103.7239,0.3366,-0.457,1.8422,-1.2747"},EPSG_4194:{towgs84:"163.511,127.533,-159.789"},EPSG_4195:{towgs84:"105,326,-102.5"},EPSG_4196:{towgs84:"-45,417,-3.5"},EPSG_4611:{towgs84:"-162.619,-276.959,-161.764,0.067753,-2.243649,-1.158827,-1.094246"},EPSG_4633:{towgs84:"137.092,131.66,91.475,-1.9436,-11.5993,-4.3321,-7.4824"},EPSG_4641:{towgs84:"-408.809,366.856,-412.987,1.8842,-0.5308,2.1655,-121.0993"},EPSG_4643:{towgs84:"-480.26,-438.32,-643.429,16.3119,20.1721,-4.0349,-111.7002"},EPSG_4300:{towgs84:"482.5,-130.6,564.6,-1.042,-0.214,-0.631,8.15"},EPSG_4188:{towgs84:"482.5,-130.6,564.6,-1.042,-0.214,-0.631,8.15"},EPSG_4660:{towgs84:"982.6087,552.753,-540.873,32.39344,-153.25684,-96.2266,16.805"},EPSG_4662:{towgs84:"97.295,-263.247,310.882,-1.5999,0.8386,3.1409,13.3259"},EPSG_3906:{towgs84:"577.88891,165.22205,391.18289,4.9145,-0.94729,-13.05098,7.78664"},EPSG_4307:{towgs84:"-209.3622,-87.8162,404.6198,0.0046,3.4784,0.5805,-1.4547"},EPSG_6892:{towgs84:"-76.269,-16.683,68.562,-6.275,10.536,-4.286,-13.686"},EPSG_4690:{towgs84:"221.597,152.441,176.523,2.403,1.3893,0.884,11.4648"},EPSG_4691:{towgs84:"218.769,150.75,176.75,3.5231,2.0037,1.288,10.9817"},EPSG_4629:{towgs84:"72.51,345.411,79.241,-1.5862,-0.8826,-0.5495,1.3653"},EPSG_4630:{towgs84:"165.804,216.213,180.26,-0.6251,-0.4515,-0.0721,7.4111"},EPSG_4692:{towgs84:"217.109,86.452,23.711,0.0183,-0.0003,0.007,-0.0093"},EPSG_9333:{towgs84:"0,0,0,-8.393,0.749,-10.276,0"},EPSG_9059:{towgs84:"0,0,0"},EPSG_4312:{towgs84:"601.705,84.263,485.227,4.7354,1.3145,5.393,-2.3887"},EPSG_4123:{towgs84:"-96.062,-82.428,-121.753,4.801,0.345,-1.376,1.496"},EPSG_4309:{towgs84:"-124.45,183.74,44.64,-0.4384,0.5446,-0.9706,-2.1365"},ESRI_104106:{towgs84:"-283.088,-70.693,117.445,-1.157,0.059,-0.652,-4.058"},EPSG_4281:{towgs84:"-219.247,-73.802,269.529"},EPSG_4322:{towgs84:"0,0,4.5"},EPSG_4324:{towgs84:"0,0,1.9"},EPSG_4284:{towgs84:"43.822,-108.842,-119.585,1.455,-0.761,0.737,0.549"},EPSG_4277:{towgs84:"446.448,-125.157,542.06,0.15,0.247,0.842,-20.489"},EPSG_4207:{towgs84:"-282.1,-72.2,120,-1.529,0.145,-0.89,-4.46"},EPSG_4688:{towgs84:"347.175,1077.618,2623.677,33.9058,-70.6776,9.4013,186.0647"},EPSG_4689:{towgs84:"410.793,54.542,80.501,-2.5596,-2.3517,-0.6594,17.3218"},EPSG_4720:{towgs84:"0,0,4.5"},EPSG_4273:{towgs84:"278.3,93,474.5,7.889,0.05,-6.61,6.21"},EPSG_4240:{towgs84:"204.64,834.74,293.8"},EPSG_4817:{towgs84:"278.3,93,474.5,7.889,0.05,-6.61,6.21"},ESRI_104131:{towgs84:"426.62,142.62,460.09,4.98,4.49,-12.42,-17.1"},EPSG_4265:{towgs84:"-104.1,-49.1,-9.9,0.971,-2.917,0.714,-11.68"},EPSG_4263:{towgs84:"-111.92,-87.85,114.5,1.875,0.202,0.219,0.032"},EPSG_4298:{towgs84:"-689.5937,623.84046,-65.93566,-0.02331,1.17094,-0.80054,5.88536"},EPSG_4270:{towgs84:"-253.4392,-148.452,386.5267,0.15605,0.43,-0.1013,-0.0424"},EPSG_4229:{towgs84:"-121.8,98.1,-10.7"},EPSG_4220:{towgs84:"-55.5,-348,-229.2"},EPSG_4214:{towgs84:"12.646,-155.176,-80.863"},EPSG_4232:{towgs84:"-345,3,223"},EPSG_4238:{towgs84:"-1.977,-13.06,-9.993,0.364,0.254,0.689,-1.037"},EPSG_4168:{towgs84:"-170,33,326"},EPSG_4131:{towgs84:"199,931,318.9"},EPSG_4152:{towgs84:"-0.9102,2.0141,0.5602,0.029039,0.010065,0.010101,0"},EPSG_5228:{towgs84:"572.213,85.334,461.94,4.9732,1.529,5.2484,3.5378"},EPSG_8351:{towgs84:"485.021,169.465,483.839,7.786342,4.397554,4.102655,0"},EPSG_4683:{towgs84:"-127.62,-67.24,-47.04,-3.068,4.903,1.578,-1.06"},EPSG_4133:{towgs84:"0,0,0"},EPSG_7373:{towgs84:"0.819,-0.5762,-1.6446,-0.00378,-0.03317,0.00318,0.0693"},EPSG_9075:{towgs84:"-0.9102,2.0141,0.5602,0.029039,0.010065,0.010101,0"},EPSG_9072:{towgs84:"-0.9102,2.0141,0.5602,0.029039,0.010065,0.010101,0"},EPSG_9294:{towgs84:"1.16835,-1.42001,-2.24431,-0.00822,-0.05508,0.01818,0.23388"},EPSG_4212:{towgs84:"-267.434,173.496,181.814,-13.4704,8.7154,7.3926,14.7492"},EPSG_4191:{towgs84:"-44.183,-0.58,-38.489,2.3867,2.7072,-3.5196,-8.2703"},EPSG_4237:{towgs84:"52.684,-71.194,-13.975,-0.312,-0.1063,-0.3729,1.0191"},EPSG_4740:{towgs84:"-1.08,-0.27,-0.9"},EPSG_4124:{towgs84:"419.3836,99.3335,591.3451,0.850389,1.817277,-7.862238,-0.99496"},EPSG_5681:{towgs84:"584.9636,107.7175,413.8067,1.1155,0.2824,-3.1384,7.9922"},EPSG_4141:{towgs84:"23.772,17.49,17.859,-0.3132,-1.85274,1.67299,-5.4262"},EPSG_4204:{towgs84:"-85.645,-273.077,-79.708,2.289,-1.421,2.532,3.194"},EPSG_4319:{towgs84:"226.702,-193.337,-35.371,-2.229,-4.391,9.238,0.9798"},EPSG_4200:{towgs84:"24.82,-131.21,-82.66"},EPSG_4130:{towgs84:"0,0,0"},EPSG_4127:{towgs84:"-82.875,-57.097,-156.768,-2.158,1.524,-0.982,-0.359"},EPSG_4149:{towgs84:"674.374,15.056,405.346"},EPSG_4617:{towgs84:"-0.991,1.9072,0.5129,1.25033e-7,4.6785e-8,5.6529e-8,0"},EPSG_4663:{towgs84:"-210.502,-66.902,-48.476,2.094,-15.067,-5.817,0.485"},EPSG_4664:{towgs84:"-211.939,137.626,58.3,-0.089,0.251,0.079,0.384"},EPSG_4665:{towgs84:"-105.854,165.589,-38.312,-0.003,-0.026,0.024,-0.048"},EPSG_4666:{towgs84:"631.392,-66.551,481.442,1.09,-4.445,-4.487,-4.43"},EPSG_4756:{towgs84:"-192.873,-39.382,-111.202,-0.00205,-0.0005,0.00335,0.0188"},EPSG_4723:{towgs84:"-179.483,-69.379,-27.584,-7.862,8.163,6.042,-13.925"},EPSG_4726:{towgs84:"8.853,-52.644,180.304,-0.393,-2.323,2.96,-24.081"},EPSG_4267:{towgs84:"-8.0,160.0,176.0"},EPSG_5365:{towgs84:"-0.16959,0.35312,0.51846,0.03385,-0.16325,0.03446,0.03693"},EPSG_4218:{towgs84:"304.5,306.5,-318.1"},EPSG_4242:{towgs84:"-33.722,153.789,94.959,-8.581,-4.478,4.54,8.95"},EPSG_4216:{towgs84:"-292.295,248.758,429.447,4.9971,2.99,6.6906,1.0289"},ESRI_104105:{towgs84:"631.392,-66.551,481.442,1.09,-4.445,-4.487,-4.43"},ESRI_104129:{towgs84:"0,0,0"},EPSG_4673:{towgs84:"174.05,-25.49,112.57"},EPSG_4202:{towgs84:"-124,-60,154"},EPSG_4203:{towgs84:"-117.763,-51.51,139.061,0.292,0.443,0.277,-0.191"},EPSG_3819:{towgs84:"595.48,121.69,515.35,4.115,-2.9383,0.853,-3.408"},EPSG_8694:{towgs84:"-93.799,-132.737,-219.073,-1.844,0.648,-6.37,-0.169"},EPSG_4145:{towgs84:"275.57,676.78,229.6"},EPSG_4283:{towgs84:"61.55,-10.87,-40.19,39.4924,32.7221,32.8979,-9.994"},EPSG_4317:{towgs84:"2.3287,-147.0425,-92.0802,-0.3092483,0.32482185,0.49729934,5.68906266"},EPSG_4272:{towgs84:"59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993"},EPSG_4248:{towgs84:"-307.7,265.3,-363.5"},EPSG_5561:{towgs84:"24,-121,-76"},EPSG_5233:{towgs84:"-0.293,766.95,87.713,0.195704,1.695068,3.473016,-0.039338"},ESRI_104130:{towgs84:"-86,-98,-119"},ESRI_104102:{towgs84:"682,-203,480"},ESRI_37207:{towgs84:"7,-10,-26"},EPSG_4675:{towgs84:"59.935,118.4,-10.871"},ESRI_104109:{towgs84:"-89.121,-348.182,260.871"},ESRI_104112:{towgs84:"-185.583,-230.096,281.361"},ESRI_104113:{towgs84:"25.1,-275.6,222.6"},IGNF_WGS72G:{towgs84:"0,12,6"},IGNF_NTFG:{towgs84:"-168,-60,320"},IGNF_EFATE57G:{towgs84:"-127,-769,472"},IGNF_PGP50G:{towgs84:"324.8,153.6,172.1"},IGNF_REUN47G:{towgs84:"94,-948,-1262"},IGNF_CSG67G:{towgs84:"-186,230,110"},IGNF_GUAD48G:{towgs84:"-467,-16,-300"},IGNF_TAHI51G:{towgs84:"162,117,154"},IGNF_TAHAAG:{towgs84:"65,342,77"},IGNF_NUKU72G:{towgs84:"84,274,65"},IGNF_PETRELS72G:{towgs84:"365,194,166"},IGNF_WALL78G:{towgs84:"253,-133,-127"},IGNF_MAYO50G:{towgs84:"-382,-59,-262"},IGNF_TANNAG:{towgs84:"-139,-967,436"},IGNF_IGN72G:{towgs84:"-13,-348,292"},IGNF_ATIGG:{towgs84:"1118,23,66"},IGNF_FANGA84G:{towgs84:"150.57,158.33,118.32"},IGNF_RUSAT84G:{towgs84:"202.13,174.6,-15.74"},IGNF_KAUE70G:{towgs84:"126.74,300.1,-75.49"},IGNF_MOP90G:{towgs84:"-10.8,-1.8,12.77"},IGNF_MHPF67G:{towgs84:"338.08,212.58,-296.17"},IGNF_TAHI79G:{towgs84:"160.61,116.05,153.69"},IGNF_ANAA92G:{towgs84:"1.5,3.84,4.81"},IGNF_MARQUI72G:{towgs84:"330.91,-13.92,58.56"},IGNF_APAT86G:{towgs84:"143.6,197.82,74.05"},IGNF_TUBU69G:{towgs84:"237.17,171.61,-77.84"},IGNF_STPM50G:{towgs84:"11.363,424.148,373.13"},EPSG_4150:{towgs84:"674.374,15.056,405.346"},EPSG_4754:{towgs84:"-208.4058,-109.8777,-2.5764"},ESRI_104101:{towgs84:"374,150,588"},EPSG_4693:{towgs84:"0,-0.15,0.68"},EPSG_6207:{towgs84:"293.17,726.18,245.36"},EPSG_4153:{towgs84:"-133.63,-157.5,-158.62"},EPSG_4132:{towgs84:"-241.54,-163.64,396.06"},EPSG_4221:{towgs84:"-154.5,150.7,100.4"},EPSG_4266:{towgs84:"-80.7,-132.5,41.1"},EPSG_4193:{towgs84:"-70.9,-151.8,-41.4"},EPSG_5340:{towgs84:"-0.41,0.46,-0.35"},EPSG_4246:{towgs84:"-294.7,-200.1,525.5"},EPSG_4318:{towgs84:"-3.2,-5.7,2.8"},EPSG_4121:{towgs84:"-199.87,74.79,246.62"},EPSG_4223:{towgs84:"-260.1,5.5,432.2"},EPSG_4158:{towgs84:"-0.465,372.095,171.736"},EPSG_4285:{towgs84:"-128.16,-282.42,21.93"},EPSG_4613:{towgs84:"-404.78,685.68,45.47"},EPSG_4607:{towgs84:"195.671,332.517,274.607"},EPSG_4475:{towgs84:"-381.788,-57.501,-256.673"},EPSG_4208:{towgs84:"-157.84,308.54,-146.6"},EPSG_4743:{towgs84:"70.995,-335.916,262.898"},EPSG_4710:{towgs84:"-323.65,551.39,-491.22"},EPSG_7881:{towgs84:"-0.077,0.079,0.086"},EPSG_4682:{towgs84:"283.729,735.942,261.143"},EPSG_4739:{towgs84:"-156,-271,-189"},EPSG_4679:{towgs84:"-80.01,253.26,291.19"},EPSG_4750:{towgs84:"-56.263,16.136,-22.856"},EPSG_4644:{towgs84:"-10.18,-350.43,291.37"},EPSG_4695:{towgs84:"-103.746,-9.614,-255.95"},EPSG_4292:{towgs84:"-355,21,72"},EPSG_4302:{towgs84:"-61.702,284.488,472.052"},EPSG_4143:{towgs84:"-124.76,53,466.79"},EPSG_4606:{towgs84:"-153,153,307"},EPSG_4699:{towgs84:"-770.1,158.4,-498.2"},EPSG_4247:{towgs84:"-273.5,110.6,-357.9"},EPSG_4160:{towgs84:"8.88,184.86,106.69"},EPSG_4161:{towgs84:"-233.43,6.65,173.64"},EPSG_9251:{towgs84:"-9.5,122.9,138.2"},EPSG_9253:{towgs84:"-78.1,101.6,133.3"},EPSG_4297:{towgs84:"-198.383,-240.517,-107.909"},EPSG_4269:{towgs84:"0,0,0"},EPSG_4301:{towgs84:"-147,506,687"},EPSG_4618:{towgs84:"-59,-11,-52"},EPSG_4612:{towgs84:"0,0,0"},EPSG_4678:{towgs84:"44.585,-131.212,-39.544"},EPSG_4250:{towgs84:"-130,29,364"},EPSG_4144:{towgs84:"214,804,268"},EPSG_4147:{towgs84:"-17.51,-108.32,-62.39"},EPSG_4259:{towgs84:"-254.1,-5.36,-100.29"},EPSG_4164:{towgs84:"-76,-138,67"},EPSG_4211:{towgs84:"-378.873,676.002,-46.255"},EPSG_4182:{towgs84:"-422.651,-172.995,84.02"},EPSG_4224:{towgs84:"-143.87,243.37,-33.52"},EPSG_4225:{towgs84:"-205.57,168.77,-4.12"},EPSG_5527:{towgs84:"-67.35,3.88,-38.22"},EPSG_4752:{towgs84:"98,390,-22"},EPSG_4310:{towgs84:"-30,190,89"},EPSG_9248:{towgs84:"-192.26,65.72,132.08"},EPSG_4680:{towgs84:"124.5,-63.5,-281"},EPSG_4701:{towgs84:"-79.9,-158,-168.9"},EPSG_4706:{towgs84:"-146.21,112.63,4.05"},EPSG_4805:{towgs84:"682,-203,480"},EPSG_4201:{towgs84:"-165,-11,206"},EPSG_4210:{towgs84:"-157,-2,-299"},EPSG_4183:{towgs84:"-104,167,-38"},EPSG_4139:{towgs84:"11,72,-101"},EPSG_4668:{towgs84:"-86,-98,-119"},EPSG_4717:{towgs84:"-2,151,181"},EPSG_4732:{towgs84:"102,52,-38"},EPSG_4280:{towgs84:"-377,681,-50"},EPSG_4209:{towgs84:"-138,-105,-289"},EPSG_4261:{towgs84:"31,146,47"},EPSG_4658:{towgs84:"-73,46,-86"},EPSG_4721:{towgs84:"265.025,384.929,-194.046"},EPSG_4222:{towgs84:"-136,-108,-292"},EPSG_4601:{towgs84:"-255,-15,71"},EPSG_4602:{towgs84:"725,685,536"},EPSG_4603:{towgs84:"72,213.7,93"},EPSG_4605:{towgs84:"9,183,236"},EPSG_4621:{towgs84:"137,248,-430"},EPSG_4657:{towgs84:"-28,199,5"},EPSG_4316:{towgs84:"103.25,-100.4,-307.19"},EPSG_4642:{towgs84:"-13,-348,292"},EPSG_4698:{towgs84:"145,-187,103"},EPSG_4192:{towgs84:"-206.1,-174.7,-87.7"},EPSG_4311:{towgs84:"-265,120,-358"},EPSG_4135:{towgs84:"58,-283,-182"},ESRI_104138:{towgs84:"198,-226,-347"},EPSG_4245:{towgs84:"-11,851,5"},EPSG_4142:{towgs84:"-125,53,467"},EPSG_4213:{towgs84:"-106,-87,188"},EPSG_4253:{towgs84:"-133,-77,-51"},EPSG_4129:{towgs84:"-132,-110,-335"},EPSG_4713:{towgs84:"-77,-128,142"},EPSG_4239:{towgs84:"217,823,299"},EPSG_4146:{towgs84:"295,736,257"},EPSG_4155:{towgs84:"-83,37,124"},EPSG_4165:{towgs84:"-173,253,27"},EPSG_4672:{towgs84:"175,-38,113"},EPSG_4236:{towgs84:"-637,-549,-203"},EPSG_4251:{towgs84:"-90,40,88"},EPSG_4271:{towgs84:"-2,374,172"},EPSG_4175:{towgs84:"-88,4,101"},EPSG_4716:{towgs84:"298,-304,-375"},EPSG_4315:{towgs84:"-23,259,-9"},EPSG_4744:{towgs84:"-242.2,-144.9,370.3"},EPSG_4244:{towgs84:"-97,787,86"},EPSG_4293:{towgs84:"616,97,-251"},EPSG_4714:{towgs84:"-127,-769,472"},EPSG_4736:{towgs84:"260,12,-147"},EPSG_6883:{towgs84:"-235,-110,393"},EPSG_6894:{towgs84:"-63,176,185"},EPSG_4205:{towgs84:"-43,-163,45"},EPSG_4256:{towgs84:"41,-220,-134"},EPSG_4262:{towgs84:"639,405,60"},EPSG_4604:{towgs84:"174,359,365"},EPSG_4169:{towgs84:"-115,118,426"},EPSG_4620:{towgs84:"-106,-129,165"},EPSG_4184:{towgs84:"-203,141,53"},EPSG_4616:{towgs84:"-289,-124,60"},EPSG_9403:{towgs84:"-307,-92,127"},EPSG_4684:{towgs84:"-133,-321,50"},EPSG_4708:{towgs84:"-491,-22,435"},EPSG_4707:{towgs84:"114,-116,-333"},EPSG_4709:{towgs84:"145,75,-272"},EPSG_4712:{towgs84:"-205,107,53"},EPSG_4711:{towgs84:"124,-234,-25"},EPSG_4718:{towgs84:"230,-199,-752"},EPSG_4719:{towgs84:"211,147,111"},EPSG_4724:{towgs84:"208,-435,-229"},EPSG_4725:{towgs84:"189,-79,-202"},EPSG_4735:{towgs84:"647,1777,-1124"},EPSG_4722:{towgs84:"-794,119,-298"},EPSG_4728:{towgs84:"-307,-92,127"},EPSG_4734:{towgs84:"-632,438,-609"},EPSG_4727:{towgs84:"912,-58,1227"},EPSG_4729:{towgs84:"185,165,42"},EPSG_4730:{towgs84:"170,42,84"},EPSG_4733:{towgs84:"276,-57,149"},ESRI_37218:{towgs84:"230,-199,-752"},ESRI_37240:{towgs84:"-7,215,225"},ESRI_37221:{towgs84:"252,-209,-751"},ESRI_4305:{towgs84:"-123,-206,219"},ESRI_104139:{towgs84:"-73,-247,227"},EPSG_4748:{towgs84:"51,391,-36"},EPSG_4219:{towgs84:"-384,664,-48"},EPSG_4255:{towgs84:"-333,-222,114"},EPSG_4257:{towgs84:"-587.8,519.75,145.76"},EPSG_4646:{towgs84:"-963,510,-359"},EPSG_6881:{towgs84:"-24,-203,268"},EPSG_6882:{towgs84:"-183,-15,273"},EPSG_4715:{towgs84:"-104,-129,239"},IGNF_RGF93GDD:{towgs84:"0,0,0"},IGNF_RGM04GDD:{towgs84:"0,0,0"},IGNF_RGSPM06GDD:{towgs84:"0,0,0"},IGNF_RGTAAF07GDD:{towgs84:"0,0,0"},IGNF_RGFG95GDD:{towgs84:"0,0,0"},IGNF_RGNCG:{towgs84:"0,0,0"},IGNF_RGPFGDD:{towgs84:"0,0,0"},IGNF_ETRS89G:{towgs84:"0,0,0"},IGNF_RGR92GDD:{towgs84:"0,0,0"},EPSG_4173:{towgs84:"0,0,0"},EPSG_4180:{towgs84:"0,0,0"},EPSG_4619:{towgs84:"0,0,0"},EPSG_4667:{towgs84:"0,0,0"},EPSG_4075:{towgs84:"0,0,0"},EPSG_6706:{towgs84:"0,0,0"},EPSG_7798:{towgs84:"0,0,0"},EPSG_4661:{towgs84:"0,0,0"},EPSG_4669:{towgs84:"0,0,0"},EPSG_8685:{towgs84:"0,0,0"},EPSG_4151:{towgs84:"0,0,0"},EPSG_9702:{towgs84:"0,0,0"},EPSG_4758:{towgs84:"0,0,0"},EPSG_4761:{towgs84:"0,0,0"},EPSG_4765:{towgs84:"0,0,0"},EPSG_8997:{towgs84:"0,0,0"},EPSG_4023:{towgs84:"0,0,0"},EPSG_4670:{towgs84:"0,0,0"},EPSG_4694:{towgs84:"0,0,0"},EPSG_4148:{towgs84:"0,0,0"},EPSG_4163:{towgs84:"0,0,0"},EPSG_4167:{towgs84:"0,0,0"},EPSG_4189:{towgs84:"0,0,0"},EPSG_4190:{towgs84:"0,0,0"},EPSG_4176:{towgs84:"0,0,0"},EPSG_4659:{towgs84:"0,0,0"},EPSG_3824:{towgs84:"0,0,0"},EPSG_3889:{towgs84:"0,0,0"},EPSG_4046:{towgs84:"0,0,0"},EPSG_4081:{towgs84:"0,0,0"},EPSG_4558:{towgs84:"0,0,0"},EPSG_4483:{towgs84:"0,0,0"},EPSG_5013:{towgs84:"0,0,0"},EPSG_5264:{towgs84:"0,0,0"},EPSG_5324:{towgs84:"0,0,0"},EPSG_5354:{towgs84:"0,0,0"},EPSG_5371:{towgs84:"0,0,0"},EPSG_5373:{towgs84:"0,0,0"},EPSG_5381:{towgs84:"0,0,0"},EPSG_5393:{towgs84:"0,0,0"},EPSG_5489:{towgs84:"0,0,0"},EPSG_5593:{towgs84:"0,0,0"},EPSG_6135:{towgs84:"0,0,0"},EPSG_6365:{towgs84:"0,0,0"},EPSG_5246:{towgs84:"0,0,0"},EPSG_7886:{towgs84:"0,0,0"},EPSG_8431:{towgs84:"0,0,0"},EPSG_8427:{towgs84:"0,0,0"},EPSG_8699:{towgs84:"0,0,0"},EPSG_8818:{towgs84:"0,0,0"},EPSG_4757:{towgs84:"0,0,0"},EPSG_9140:{towgs84:"0,0,0"},EPSG_8086:{towgs84:"0,0,0"},EPSG_4686:{towgs84:"0,0,0"},EPSG_4737:{towgs84:"0,0,0"},EPSG_4702:{towgs84:"0,0,0"},EPSG_4747:{towgs84:"0,0,0"},EPSG_4749:{towgs84:"0,0,0"},EPSG_4674:{towgs84:"0,0,0"},EPSG_4755:{towgs84:"0,0,0"},EPSG_4759:{towgs84:"0,0,0"},EPSG_4762:{towgs84:"0,0,0"},EPSG_4763:{towgs84:"0,0,0"},EPSG_4764:{towgs84:"0,0,0"},EPSG_4166:{towgs84:"0,0,0"},EPSG_4170:{towgs84:"0,0,0"},EPSG_5546:{towgs84:"0,0,0"},EPSG_7844:{towgs84:"0,0,0"},EPSG_4818:{towgs84:"589,76,480"}};for(var ts in Lt){var Qt=Lt[ts];Qt.datumName&&(Lt[Qt.datumName]=Qt)}function is(t,i,a,s,e,r,h){var n={};return t===void 0||t==="none"?n.datum_type=Kt:n.datum_type=_a,i&&(n.datum_params=i.map(parseFloat),(n.datum_params[0]!==0||n.datum_params[1]!==0||n.datum_params[2]!==0)&&(n.datum_type=it),n.datum_params.length>3&&(n.datum_params[3]!==0||n.datum_params[4]!==0||n.datum_params[5]!==0||n.datum_params[6]!==0)&&(n.datum_type=at,n.datum_params[3]*=vt,n.datum_params[4]*=vt,n.datum_params[5]*=vt,n.datum_params[6]=n.datum_params[6]/1e6+1)),h&&(n.datum_type=ct,n.grids=h),n.a=a,n.b=s,n.es=e,n.ep2=r,n}var ri={};function as(t,i,a){return i instanceof ArrayBuffer?ss(t,i,a):{ready:es(t,i)}}function ss(t,i,a){var s=!0;a!==void 0&&a.includeErrorFields===!1&&(s=!1);var e=new DataView(i),r=hs(e),h=os(e,r),n=ls(e,h,r,s),o={header:h,subgrids:n};return ri[t]=o,o}function es(t,i){return gi(this,null,function*(){for(var a=[],s=yield i.getImageCount(),e=s-1;e>=0;e--){var r=yield i.getImage(e),h=yield r.readRasters(),n=h,o=[r.getWidth(),r.getHeight()],l=r.getBoundingBox().map(bi),_=[r.fileDirectory.ModelPixelScale[0],r.fileDirectory.ModelPixelScale[1]].map(bi),f=l[0]+(o[0]-1)*_[0],c=l[3]-(o[1]-1)*_[1],u=n[0],g=n[1],d=[];for(let E=o[1]-1;E>=0;E--)for(let G=o[0]-1;G>=0;G--){var M=E*o[0]+G;d.push([-V(g[M]),V(u[M])])}a.push({del:_,lim:o,ll:[-f,c],cvs:d})}var S={header:{nSubgrids:s},subgrids:a};return ri[t]=S,S})}function rs(t){if(t===void 0)return null;var i=t.split(",");return i.map(ns)}function ns(t){if(t.length===0)return null;var i=t[0]==="@";return i&&(t=t.slice(1)),t==="null"?{name:"null",mandatory:!i,grid:null,isNull:!0}:{name:t,mandatory:!i,grid:ri[t]||null,isNull:!1}}function bi(t){return t*Math.PI/180}function V(t){return t/3600*Math.PI/180}function hs(t){var i=t.getInt32(8,!1);return i===11?!1:(i=t.getInt32(8,!0),i!==11&&console.warn("Failed to detect nadgrid endian-ness, defaulting to little-endian"),!0)}function os(t,i){return{nFields:t.getInt32(8,i),nSubgridFields:t.getInt32(24,i),nSubgrids:t.getInt32(40,i),shiftType:Zt(t,56,64).trim(),fromSemiMajorAxis:t.getFloat64(120,i),fromSemiMinorAxis:t.getFloat64(136,i),toSemiMajorAxis:t.getFloat64(152,i),toSemiMinorAxis:t.getFloat64(168,i)}}function Zt(t,i,a){return String.fromCharCode.apply(null,new Uint8Array(t.buffer.slice(i,a)))}function ls(t,i,a,s){for(var e=176,r=[],h=0;h<i.nSubgrids;h++){var n=fs(t,e,a),o=_s(t,e,n,a,s),l=Math.round(1+(n.upperLongitude-n.lowerLongitude)/n.longitudeInterval),_=Math.round(1+(n.upperLatitude-n.lowerLatitude)/n.latitudeInterval);r.push({ll:[V(n.lowerLongitude),V(n.lowerLatitude)],del:[V(n.longitudeInterval),V(n.latitudeInterval)],lim:[l,_],count:n.gridNodeCount,cvs:cs(o)});var f=16;s===!1&&(f=8),e+=176+n.gridNodeCount*f}return r}function cs(t){return t.map(function(i){return[V(i.longitudeShift),V(i.latitudeShift)]})}function fs(t,i,a){return{name:Zt(t,i+8,i+16).trim(),parent:Zt(t,i+24,i+24+8).trim(),lowerLatitude:t.getFloat64(i+72,a),upperLatitude:t.getFloat64(i+88,a),lowerLongitude:t.getFloat64(i+104,a),upperLongitude:t.getFloat64(i+120,a),latitudeInterval:t.getFloat64(i+136,a),longitudeInterval:t.getFloat64(i+152,a),gridNodeCount:t.getInt32(i+168,a)}}function _s(t,i,a,s,e){var r=i+176,h=16;e===!1&&(h=8);for(var n=[],o=0;o<a.gridNodeCount;o++){var l={latitudeShift:t.getFloat32(r+o*h,s),longitudeShift:t.getFloat32(r+o*h+4,s)};e!==!1&&(l.latitudeAccuracy=t.getFloat32(r+o*h+8,s),l.longitudeAccuracy=t.getFloat32(r+o*h+12,s)),n.push(l)}return n}function z(t,i){if(!(this instanceof z))return new z(t);this.forward=null,this.inverse=null,this.name,this.title,i=i||function(l){if(l)throw l};var a=qa(t);if(typeof a!="object"){i("Could not parse to valid json: "+t);return}var s=z.projections.get(a.projName);if(!s){i("Could not get projection name from: "+t);return}if(a.datumCode&&a.datumCode!=="none"){var e=X(Lt,a.datumCode);e&&(a.datum_params=a.datum_params||(e.towgs84?e.towgs84.split(","):null),a.ellps=e.ellipse,a.datumName=e.datumName?e.datumName:a.datumCode)}a.k0=a.k0||1,a.axis=a.axis||"enu",a.ellps=a.ellps||"wgs84",a.lat1=a.lat1||a.lat0;var r=Za(a.a,a.b,a.rf,a.ellps,a.sphere),h=Ya(r.a,r.b,r.rf,a.R_A),n=rs(a.nadgrids),o=a.datum||is(a.datumCode,a.datum_params,r.a,r.b,h.es,h.ep2,n);Pi(this,a),Pi(this,s),this.a=r.a,this.b=r.b,this.rf=r.rf,this.sphere=r.sphere,this.es=h.es,this.e=h.e,this.ep2=h.ep2,this.datum=o,"init"in this&&typeof this.init=="function"&&this.init(),i(null,this)}z.projections=Ka;z.projections.start();function us(t,i){return t.datum_type!==i.datum_type||t.a!==i.a||Math.abs(t.es-i.es)>5e-11?!1:t.datum_type===it?t.datum_params[0]===i.datum_params[0]&&t.datum_params[1]===i.datum_params[1]&&t.datum_params[2]===i.datum_params[2]:t.datum_type===at?t.datum_params[0]===i.datum_params[0]&&t.datum_params[1]===i.datum_params[1]&&t.datum_params[2]===i.datum_params[2]&&t.datum_params[3]===i.datum_params[3]&&t.datum_params[4]===i.datum_params[4]&&t.datum_params[5]===i.datum_params[5]&&t.datum_params[6]===i.datum_params[6]:!0}function Ji(t,i,a){var s=t.x,e=t.y,r=t.z?t.z:0,h,n,o,l;if(e<-m&&e>-1.001*m)e=-m;else if(e>m&&e<1.001*m)e=m;else{if(e<-m)return{x:-1/0,y:-1/0,z:t.z};if(e>m)return{x:1/0,y:1/0,z:t.z}}return s>Math.PI&&(s-=2*Math.PI),n=Math.sin(e),l=Math.cos(e),o=n*n,h=a/Math.sqrt(1-i*o),{x:(h+r)*l*Math.cos(s),y:(h+r)*l*Math.sin(s),z:(h*(1-i)+r)*n}}function Vi(t,i,a,s){var e=1e-12,r=e*e,h=30,n,o,l,_,f,c,u,g,d,M,S,E,G,P=t.x,w=t.y,x=t.z?t.z:0,C,T,N;if(n=Math.sqrt(P*P+w*w),o=Math.sqrt(P*P+w*w+x*x),n/a<e){if(C=0,o/a<e)return T=m,N=-s,{x:t.x,y:t.y,z:t.z}}else C=Math.atan2(w,P);l=x/o,_=n/o,f=1/Math.sqrt(1-i*(2-i)*_*_),g=_*(1-i)*f,d=l*f,G=0;do G++,u=a/Math.sqrt(1-i*d*d),N=n*g+x*d-u*(1-i*d*d),c=i*u/(u+N),f=1/Math.sqrt(1-c*(2-c)*_*_),M=_*(1-c)*f,S=l*f,E=S*g-M*d,g=M,d=S;while(E*E>r&&G<h);return T=Math.atan(S/Math.abs(M)),{x:C,y:T,z:N}}function gs(t,i,a){if(i===it)return{x:t.x+a[0],y:t.y+a[1],z:t.z+a[2]};if(i===at){var s=a[0],e=a[1],r=a[2],h=a[3],n=a[4],o=a[5],l=a[6];return{x:l*(t.x-o*t.y+n*t.z)+s,y:l*(o*t.x+t.y-h*t.z)+e,z:l*(-n*t.x+h*t.y+t.z)+r}}}function ms(t,i,a){if(i===it)return{x:t.x-a[0],y:t.y-a[1],z:t.z-a[2]};if(i===at){var s=a[0],e=a[1],r=a[2],h=a[3],n=a[4],o=a[5],l=a[6],_=(t.x-s)/l,f=(t.y-e)/l,c=(t.z-r)/l;return{x:_+o*f-n*c,y:-o*_+f+h*c,z:n*_-h*f+c}}}function It(t){return t===it||t===at}function ds(t,i,a){if(us(t,i)||t.datum_type===Kt||i.datum_type===Kt)return a;var s=t.a,e=t.es;if(t.datum_type===ct){var r=pi(t,!1,a);if(r!==0)return;s=vi,e=yi}var h=i.a,n=i.b,o=i.es;if(i.datum_type===ct&&(h=vi,n=ua,o=yi),e===o&&s===h&&!It(t.datum_type)&&!It(i.datum_type))return a;if(a=Ji(a,e,s),It(t.datum_type)&&(a=gs(a,t.datum_type,t.datum_params)),It(i.datum_type)&&(a=ms(a,i.datum_type,i.datum_params)),a=Vi(a,o,h,n),i.datum_type===ct){var l=pi(i,!0,a);if(l!==0)return}return a}function pi(t,i,a){if(t.grids===null||t.grids.length===0)return console.log("Grid shift grids not found"),-1;var s={x:-a.x,y:a.y},e={x:Number.NaN,y:Number.NaN},r=[];t:for(var h=0;h<t.grids.length;h++){var n=t.grids[h];if(r.push(n.name),n.isNull){e=s;break}if(n.grid===null){if(n.mandatory)return console.log("Unable to find mandatory grid '"+n.name+"'"),-1;continue}for(var o=n.grid.subgrids,l=0,_=o.length;l<_;l++){var f=o[l],c=(Math.abs(f.del[1])+Math.abs(f.del[0]))/1e4,u=f.ll[0]-c,g=f.ll[1]-c,d=f.ll[0]+(f.lim[0]-1)*f.del[0]+c,M=f.ll[1]+(f.lim[1]-1)*f.del[1]+c;if(!(g>s.y||u>s.x||M<s.y||d<s.x)&&(e=Ms(s,i,f),!isNaN(e.x)))break t}}return isNaN(e.x)?(console.log("Failed to find a grid shift table for location '"+-s.x*W+" "+s.y*W+" tried: '"+r+"'"),-1):(a.x=-e.x,a.y=e.y,0)}function Ms(t,i,a){var s={x:Number.NaN,y:Number.NaN};if(isNaN(t.x))return s;var e={x:t.x,y:t.y};e.x-=a.ll[0],e.y-=a.ll[1],e.x=y(e.x-Math.PI)+Math.PI;var r=Ci(e,a);if(i){if(isNaN(r.x))return s;r.x=e.x-r.x,r.y=e.y-r.y;var h=9,n=1e-12,o,l;do{if(l=Ci(r,a),isNaN(l.x)){console.log("Inverse grid shift iteration failed, presumably at grid edge.  Using first approximation.");break}o={x:e.x-(l.x+r.x),y:e.y-(l.y+r.y)},r.x+=o.x,r.y+=o.y}while(h--&&Math.abs(o.x)>n&&Math.abs(o.y)>n);if(h<0)return console.log("Inverse grid shift iterator failed to converge."),s;s.x=y(r.x+a.ll[0]),s.y=r.y+a.ll[1]}else isNaN(r.x)||(s.x=t.x+r.x,s.y=t.y+r.y);return s}function Ci(t,i){var a={x:t.x/i.del[0],y:t.y/i.del[1]},s={x:Math.floor(a.x),y:Math.floor(a.y)},e={x:a.x-1*s.x,y:a.y-1*s.y},r={x:Number.NaN,y:Number.NaN},h;if(s.x<0||s.x>=i.lim[0]||s.y<0||s.y>=i.lim[1])return r;h=s.y*i.lim[0]+s.x;var n={x:i.cvs[h][0],y:i.cvs[h][1]};h++;var o={x:i.cvs[h][0],y:i.cvs[h][1]};h+=i.lim[0];var l={x:i.cvs[h][0],y:i.cvs[h][1]};h--;var _={x:i.cvs[h][0],y:i.cvs[h][1]},f=e.x*e.y,c=e.x*(1-e.y),u=(1-e.x)*(1-e.y),g=(1-e.x)*e.y;return r.x=u*n.x+c*o.x+g*_.x+f*l.x,r.y=u*n.y+c*o.y+g*_.y+f*l.y,r}function Ai(t,i,a){var s=a.x,e=a.y,r=a.z||0,h,n,o,l={};for(o=0;o<3;o++)if(!(i&&o===2&&a.z===void 0))switch(o===0?(h=s,"ew".indexOf(t.axis[o])!==-1?n="x":n="y"):o===1?(h=e,"ns".indexOf(t.axis[o])!==-1?n="y":n="x"):(h=r,n="z"),t.axis[o]){case"e":l[n]=h;break;case"w":l[n]=-h;break;case"n":l[n]=h;break;case"s":l[n]=-h;break;case"u":a[n]!==void 0&&(l.z=h);break;case"d":a[n]!==void 0&&(l.z=-h);break;default:return null}return l}function Ki(t){var i={x:t[0],y:t[1]};return t.length>2&&(i.z=t[2]),t.length>3&&(i.m=t[3]),i}function vs(t){Ni(t.x),Ni(t.y)}function Ni(t){if(typeof Number.isFinite=="function"){if(Number.isFinite(t))return;throw new TypeError("coordinates must be finite numbers")}if(typeof t!="number"||t!==t||!isFinite(t))throw new TypeError("coordinates must be finite numbers")}function ys(t,i){return(t.datum.datum_type===it||t.datum.datum_type===at||t.datum.datum_type===ct)&&i.datumCode!=="WGS84"||(i.datum.datum_type===it||i.datum.datum_type===at||i.datum.datum_type===ct)&&t.datumCode!=="WGS84"}function Ut(t,i,a,s){var e;Array.isArray(a)?a=Ki(a):a={x:a.x,y:a.y,z:a.z,m:a.m};var r=a.z!==void 0;if(vs(a),t.datum&&i.datum&&ys(t,i)&&(e=new z("WGS84"),a=Ut(t,e,a,s),t=e),s&&t.axis!=="enu"&&(a=Ai(t,!1,a)),t.projName==="longlat")a={x:a.x*R,y:a.y*R,z:a.z||0};else if(t.to_meter&&(a={x:a.x*t.to_meter,y:a.y*t.to_meter,z:a.z||0}),a=t.inverse(a),!a)return;if(t.from_greenwich&&(a.x+=t.from_greenwich),a=ds(t.datum,i.datum,a),!!a)return a=a,i.from_greenwich&&(a={x:a.x-i.from_greenwich,y:a.y,z:a.z||0}),i.projName==="longlat"?a={x:a.x*W,y:a.y*W,z:a.z||0}:(a=i.forward(a),i.to_meter&&(a={x:a.x/i.to_meter,y:a.y/i.to_meter,z:a.z||0})),s&&i.axis!=="enu"?Ai(i,!0,a):(a&&!r&&delete a.z,a)}var Ri=z("WGS84");function Jt(t,i,a,s){var e,r,h;return Array.isArray(a)?(e=Ut(t,i,a,s)||{x:NaN,y:NaN},a.length>2?typeof t.name!="undefined"&&t.name==="geocent"||typeof i.name!="undefined"&&i.name==="geocent"?typeof e.z=="number"?[e.x,e.y,e.z].concat(a.slice(3)):[e.x,e.y,a[2]].concat(a.slice(3)):[e.x,e.y].concat(a.slice(2)):[e.x,e.y]):(r=Ut(t,i,a,s),h=Object.keys(a),h.length===2||h.forEach(function(n){if(typeof t.name!="undefined"&&t.name==="geocent"||typeof i.name!="undefined"&&i.name==="geocent"){if(n==="x"||n==="y"||n==="z")return}else if(n==="x"||n==="y")return;r[n]=a[n]}),r)}function Tt(t){return t instanceof z?t:typeof t=="object"&&"oProj"in t?t.oProj:z(t)}function Ss(t,i,a){var s,e,r=!1,h;return typeof i=="undefined"?(e=Tt(t),s=Ri,r=!0):(typeof i.x!="undefined"||Array.isArray(i))&&(a=i,e=Tt(t),s=Ri,r=!0),s||(s=Tt(t)),e||(e=Tt(i)),a?Jt(s,e,a):(h={forward:function(n,o){return Jt(s,e,n,o)},inverse:function(n,o){return Jt(e,s,n,o)}},r&&(h.oProj=e),h)}var Ii=6,Xi="AJSAJS",Yi="AFAFAF",ot=65,D=73,U=79,mt=86,dt=90;const Es={forward:Zi,inverse:Gs,toPoint:ta};function Zi(t,i){return i=i||5,xs(ws({lat:t[1],lon:t[0]}),i)}function Gs(t){var i=ni(aa(t.toUpperCase()));return i.lat&&i.lon?[i.lon,i.lat,i.lon,i.lat]:[i.left,i.bottom,i.right,i.top]}function ta(t){var i=ni(aa(t.toUpperCase()));return i.lat&&i.lon?[i.lon,i.lat]:[(i.left+i.right)/2,(i.top+i.bottom)/2]}function Vt(t){return t*(Math.PI/180)}function Ti(t){return 180*(t/Math.PI)}function ws(t){var i=t.lat,a=t.lon,s=6378137,e=.00669438,r=.9996,h,n,o,l,_,f,c,u=Vt(i),g=Vt(a),d,M;M=Math.floor((a+180)/6)+1,a===180&&(M=60),i>=56&&i<64&&a>=3&&a<12&&(M=32),i>=72&&i<84&&(a>=0&&a<9?M=31:a>=9&&a<21?M=33:a>=21&&a<33?M=35:a>=33&&a<42&&(M=37)),h=(M-1)*6-180+3,d=Vt(h),n=e/(1-e),o=s/Math.sqrt(1-e*Math.sin(u)*Math.sin(u)),l=Math.tan(u)*Math.tan(u),_=n*Math.cos(u)*Math.cos(u),f=Math.cos(u)*(g-d),c=s*((1-e/4-3*e*e/64-5*e*e*e/256)*u-(3*e/8+3*e*e/32+45*e*e*e/1024)*Math.sin(2*u)+(15*e*e/256+45*e*e*e/1024)*Math.sin(4*u)-35*e*e*e/3072*Math.sin(6*u));var S=r*o*(f+(1-l+_)*f*f*f/6+(5-18*l+l*l+72*_-58*n)*f*f*f*f*f/120)+5e5,E=r*(c+o*Math.tan(u)*(f*f/2+(5-l+9*_+4*_*_)*f*f*f*f/24+(61-58*l+l*l+600*_-330*n)*f*f*f*f*f*f/720));return i<0&&(E+=1e7),{northing:Math.round(E),easting:Math.round(S),zoneNumber:M,zoneLetter:Ps(i)}}function ni(t){var i=t.northing,a=t.easting,s=t.zoneLetter,e=t.zoneNumber;if(e<0||e>60)return null;var r=.9996,h=6378137,n=.00669438,o,l=(1-Math.sqrt(1-n))/(1+Math.sqrt(1-n)),_,f,c,u,g,d,M,S,E,G=a-5e5,P=i;s<"N"&&(P-=1e7),M=(e-1)*6-180+3,o=n/(1-n),d=P/r,S=d/(h*(1-n/4-3*n*n/64-5*n*n*n/256)),E=S+(3*l/2-27*l*l*l/32)*Math.sin(2*S)+(21*l*l/16-55*l*l*l*l/32)*Math.sin(4*S)+151*l*l*l/96*Math.sin(6*S),_=h/Math.sqrt(1-n*Math.sin(E)*Math.sin(E)),f=Math.tan(E)*Math.tan(E),c=o*Math.cos(E)*Math.cos(E),u=h*(1-n)/Math.pow(1-n*Math.sin(E)*Math.sin(E),1.5),g=G/(_*r);var w=E-_*Math.tan(E)/u*(g*g/2-(5+3*f+10*c-4*c*c-9*o)*g*g*g*g/24+(61+90*f+298*c+45*f*f-252*o-3*c*c)*g*g*g*g*g*g/720);w=Ti(w);var x=(g-(1+2*f+c)*g*g*g/6+(5-2*c+28*f-3*c*c+8*o+24*f*f)*g*g*g*g*g/120)/Math.cos(E);x=M+Ti(x);var C;if(t.accuracy){var T=ni({northing:t.northing+t.accuracy,easting:t.easting+t.accuracy,zoneLetter:t.zoneLetter,zoneNumber:t.zoneNumber});C={top:T.lat,right:T.lon,bottom:w,left:x}}else C={lat:w,lon:x};return C}function Ps(t){var i="Z";return 84>=t&&t>=72?i="X":72>t&&t>=64?i="W":64>t&&t>=56?i="V":56>t&&t>=48?i="U":48>t&&t>=40?i="T":40>t&&t>=32?i="S":32>t&&t>=24?i="R":24>t&&t>=16?i="Q":16>t&&t>=8?i="P":8>t&&t>=0?i="N":0>t&&t>=-8?i="M":-8>t&&t>=-16?i="L":-16>t&&t>=-24?i="K":-24>t&&t>=-32?i="J":-32>t&&t>=-40?i="H":-40>t&&t>=-48?i="G":-48>t&&t>=-56?i="F":-56>t&&t>=-64?i="E":-64>t&&t>=-72?i="D":-72>t&&t>=-80&&(i="C"),i}function xs(t,i){var a="00000"+t.easting,s="00000"+t.northing;return t.zoneNumber+t.zoneLetter+bs(t.easting,t.northing,t.zoneNumber)+a.substr(a.length-5,i)+s.substr(s.length-5,i)}function bs(t,i,a){var s=ia(a),e=Math.floor(t/1e5),r=Math.floor(i/1e5)%20;return ps(e,r,s)}function ia(t){var i=t%Ii;return i===0&&(i=Ii),i}function ps(t,i,a){var s=a-1,e=Xi.charCodeAt(s),r=Yi.charCodeAt(s),h=e+t-1,n=r+i,o=!1;h>dt&&(h=h-dt+ot-1,o=!0),(h===D||e<D&&h>D||(h>D||e<D)&&o)&&h++,(h===U||e<U&&h>U||(h>U||e<U)&&o)&&(h++,h===D&&h++),h>dt&&(h=h-dt+ot-1),n>mt?(n=n-mt+ot-1,o=!0):o=!1,(n===D||r<D&&n>D||(n>D||r<D)&&o)&&n++,(n===U||r<U&&n>U||(n>U||r<U)&&o)&&(n++,n===D&&n++),n>mt&&(n=n-mt+ot-1);var l=String.fromCharCode(h)+String.fromCharCode(n);return l}function aa(t){if(t&&t.length===0)throw"MGRSPoint coverting from nothing";for(var i=t.length,a=null,s="",e,r=0;!/[A-Z]/.test(e=t.charAt(r));){if(r>=2)throw"MGRSPoint bad conversion from: "+t;s+=e,r++}var h=parseInt(s,10);if(r===0||r+3>i)throw"MGRSPoint bad conversion from: "+t;var n=t.charAt(r++);if(n<="A"||n==="B"||n==="Y"||n>="Z"||n==="I"||n==="O")throw"MGRSPoint zone letter "+n+" not handled: "+t;a=t.substring(r,r+=2);for(var o=ia(h),l=Cs(a.charAt(0),o),_=As(a.charAt(1),o);_<Ns(n);)_+=2e6;var f=i-r;if(f%2!==0)throw`MGRSPoint has to have an even number 
of digits after the zone letter and two 100km letters - front 
half for easting meters, second half for 
northing meters`+t;var c=f/2,u=0,g=0,d,M,S,E,G;return c>0&&(d=1e5/Math.pow(10,c),M=t.substring(r,r+c),u=parseFloat(M)*d,S=t.substring(r+c),g=parseFloat(S)*d),E=u+l,G=g+_,{easting:E,northing:G,zoneLetter:n,zoneNumber:h,accuracy:d}}function Cs(t,i){for(var a=Xi.charCodeAt(i-1),s=1e5,e=!1;a!==t.charCodeAt(0);){if(a++,a===D&&a++,a===U&&a++,a>dt){if(e)throw"Bad character: "+t;a=ot,e=!0}s+=1e5}return s}function As(t,i){if(t>"V")throw"MGRSPoint given invalid Northing "+t;for(var a=Yi.charCodeAt(i-1),s=0,e=!1;a!==t.charCodeAt(0);){if(a++,a===D&&a++,a===U&&a++,a>mt){if(e)throw"Bad character: "+t;a=ot,e=!0}s+=1e5}return s}function Ns(t){var i;switch(t){case"C":i=11e5;break;case"D":i=2e6;break;case"E":i=28e5;break;case"F":i=37e5;break;case"G":i=46e5;break;case"H":i=55e5;break;case"J":i=64e5;break;case"K":i=73e5;break;case"L":i=82e5;break;case"M":i=91e5;break;case"N":i=0;break;case"P":i=8e5;break;case"Q":i=17e5;break;case"R":i=26e5;break;case"S":i=35e5;break;case"T":i=44e5;break;case"U":i=53e5;break;case"V":i=62e5;break;case"W":i=7e6;break;case"X":i=79e5;break;default:i=-1}if(i>=0)return i;throw"Invalid zone letter: "+t}function _t(t,i,a){if(!(this instanceof _t))return new _t(t,i,a);if(Array.isArray(t))this.x=t[0],this.y=t[1],this.z=t[2]||0;else if(typeof t=="object")this.x=t.x,this.y=t.y,this.z=t.z||0;else if(typeof t=="string"&&typeof i=="undefined"){var s=t.split(",");this.x=parseFloat(s[0]),this.y=parseFloat(s[1]),this.z=parseFloat(s[2])||0}else this.x=t,this.y=i,this.z=a||0;console.warn("proj4.Point will be removed in version 3, use proj4.toPoint")}_t.fromMGRS=function(t){return new _t(ta(t))};_t.prototype.toMGRS=function(t){return Zi([this.x,this.y],t)};var Rs=1,Is=.25,Oi=.046875,Li=.01953125,ki=.01068115234375,Ts=.75,Os=.46875,Ls=.013020833333333334,ks=.007120768229166667,Ds=.3645833333333333,qs=.005696614583333333,Fs=.3076171875;function hi(t){var i=[];i[0]=Rs-t*(Is+t*(Oi+t*(Li+t*ki))),i[1]=t*(Ts-t*(Oi+t*(Li+t*ki)));var a=t*t;return i[2]=a*(Os-t*(Ls+t*ks)),a*=t,i[3]=a*(Ds-t*qs),i[4]=a*t*Fs,i}function ut(t,i,a,s){return a*=i,i*=i,s[0]*t-a*(s[1]+i*(s[2]+i*(s[3]+i*s[4])))}var js=20;function oi(t,i,a){for(var s=1/(1-i),e=t,r=js;r;--r){var h=Math.sin(e),n=1-i*h*h;if(n=(ut(e,h,Math.cos(e),a)-t)*(n*Math.sqrt(n))*s,e-=n,Math.abs(n)<v)return e}return e}function Us(){this.x0=this.x0!==void 0?this.x0:0,this.y0=this.y0!==void 0?this.y0:0,this.long0=this.long0!==void 0?this.long0:0,this.lat0=this.lat0!==void 0?this.lat0:0,this.es&&(this.en=hi(this.es),this.ml0=ut(this.lat0,Math.sin(this.lat0),Math.cos(this.lat0),this.en))}function $s(t){var i=t.x,a=t.y,s=y(i-this.long0),e,r,h,n=Math.sin(a),o=Math.cos(a);if(this.es){var _=o*s,f=Math.pow(_,2),c=this.ep2*Math.pow(o,2),u=Math.pow(c,2),g=Math.abs(o)>v?Math.tan(a):0,d=Math.pow(g,2),M=Math.pow(d,2);e=1-this.es*Math.pow(n,2),_=_/Math.sqrt(e);var S=ut(a,n,o,this.en);r=this.a*(this.k0*_*(1+f/6*(1-d+c+f/20*(5-18*d+M+14*c-58*d*c+f/42*(61+179*M-M*d-479*d)))))+this.x0,h=this.a*(this.k0*(S-this.ml0+n*s*_/2*(1+f/12*(5-d+9*c+4*u+f/30*(61+M-58*d+270*c-330*d*c+f/56*(1385+543*M-M*d-3111*d))))))+this.y0}else{var l=o*Math.sin(s);if(Math.abs(Math.abs(l)-1)<v)return 93;if(r=.5*this.a*this.k0*Math.log((1+l)/(1-l))+this.x0,h=o*Math.cos(s)/Math.sqrt(1-Math.pow(l,2)),l=Math.abs(h),l>=1){if(l-1>v)return 93;h=0}else h=Math.acos(h);a<0&&(h=-h),h=this.a*this.k0*(h-this.lat0)+this.y0}return t.x=r,t.y=h,t}function Bs(t){var i,a,s,e,r=(t.x-this.x0)*(1/this.a),h=(t.y-this.y0)*(1/this.a);if(this.es)if(i=this.ml0+h/this.k0,a=oi(i,this.es,this.en),Math.abs(a)<m){var f=Math.sin(a),c=Math.cos(a),u=Math.abs(c)>v?Math.tan(a):0,g=this.ep2*Math.pow(c,2),d=Math.pow(g,2),M=Math.pow(u,2),S=Math.pow(M,2);i=1-this.es*Math.pow(f,2);var E=r*Math.sqrt(i)/this.k0,G=Math.pow(E,2);i=i*u,s=a-i*G/(1-this.es)*.5*(1-G/12*(5+3*M-9*g*M+g-4*d-G/30*(61+90*M-252*g*M+45*S+46*g-G/56*(1385+3633*M+4095*S+1574*S*M)))),e=y(this.long0+E*(1-G/6*(1+2*M+g-G/20*(5+28*M+24*S+8*g*M+6*g-G/42*(61+662*M+1320*S+720*S*M))))/c)}else s=m*pt(h),e=0;else{var n=Math.exp(r/this.k0),o=.5*(n-1/n),l=this.lat0+h/this.k0,_=Math.cos(l);i=Math.sqrt((1-Math.pow(_,2))/(1+Math.pow(o,2))),s=Math.asin(i),h<0&&(s=-s),o===0&&_===0?e=0:e=y(Math.atan2(o,_)+this.long0)}return t.x=e,t.y=s,t}var zs=["Fast_Transverse_Mercator","Fast Transverse Mercator"];const kt={init:Us,forward:$s,inverse:Bs,names:zs};function sa(t){var i=Math.exp(t);return i=(i-1/i)/2,i}function q(t,i){t=Math.abs(t),i=Math.abs(i);var a=Math.max(t,i),s=Math.min(t,i)/(a||1);return a*Math.sqrt(1+Math.pow(s,2))}function Ws(t){var i=1+t,a=i-1;return a===0?t:t*Math.log(i)/a}function Hs(t){var i=Math.abs(t);return i=Ws(i*(1+i/(q(1,i)+1))),t<0?-i:i}function li(t,i){for(var a=2*Math.cos(2*i),s=t.length-1,e=t[s],r=0,h;--s>=0;)h=-r+a*e+t[s],r=e,e=h;return i+h*Math.sin(2*i)}function Qs(t,i){for(var a=2*Math.cos(i),s=t.length-1,e=t[s],r=0,h;--s>=0;)h=-r+a*e+t[s],r=e,e=h;return Math.sin(i)*h}function Js(t){var i=Math.exp(t);return i=(i+1/i)/2,i}function ea(t,i,a){for(var s=Math.sin(i),e=Math.cos(i),r=sa(a),h=Js(a),n=2*e*h,o=-2*s*r,l=t.length-1,_=t[l],f=0,c=0,u=0,g,d;--l>=0;)g=c,d=f,c=_,f=u,_=-g+n*c-o*f+t[l],u=-d+o*c+n*f;return n=s*h,o=e*r,[n*_-o*u,n*u+o*_]}function Vs(){if(!this.approx&&(isNaN(this.es)||this.es<=0))throw new Error('Incorrect elliptical usage. Try using the +approx option in the proj string, or PROJECTION["Fast_Transverse_Mercator"] in the WKT.');this.approx&&(kt.init.apply(this),this.forward=kt.forward,this.inverse=kt.inverse),this.x0=this.x0!==void 0?this.x0:0,this.y0=this.y0!==void 0?this.y0:0,this.long0=this.long0!==void 0?this.long0:0,this.lat0=this.lat0!==void 0?this.lat0:0,this.cgb=[],this.cbg=[],this.utg=[],this.gtu=[];var t=this.es/(1+Math.sqrt(1-this.es)),i=t/(2-t),a=i;this.cgb[0]=i*(2+i*(-2/3+i*(-2+i*(116/45+i*(26/45+i*(-2854/675)))))),this.cbg[0]=i*(-2+i*(2/3+i*(4/3+i*(-82/45+i*(32/45+i*(4642/4725)))))),a=a*i,this.cgb[1]=a*(7/3+i*(-8/5+i*(-227/45+i*(2704/315+i*(2323/945))))),this.cbg[1]=a*(5/3+i*(-16/15+i*(-13/9+i*(904/315+i*(-1522/945))))),a=a*i,this.cgb[2]=a*(56/15+i*(-136/35+i*(-1262/105+i*(73814/2835)))),this.cbg[2]=a*(-26/15+i*(34/21+i*(8/5+i*(-12686/2835)))),a=a*i,this.cgb[3]=a*(4279/630+i*(-332/35+i*(-399572/14175))),this.cbg[3]=a*(1237/630+i*(-12/5+i*(-24832/14175))),a=a*i,this.cgb[4]=a*(4174/315+i*(-144838/6237)),this.cbg[4]=a*(-734/315+i*(109598/31185)),a=a*i,this.cgb[5]=a*(601676/22275),this.cbg[5]=a*(444337/155925),a=Math.pow(i,2),this.Qn=this.k0/(1+i)*(1+a*(1/4+a*(1/64+a/256))),this.utg[0]=i*(-.5+i*(2/3+i*(-37/96+i*(1/360+i*(81/512+i*(-96199/604800)))))),this.gtu[0]=i*(.5+i*(-2/3+i*(5/16+i*(41/180+i*(-127/288+i*(7891/37800)))))),this.utg[1]=a*(-1/48+i*(-1/15+i*(437/1440+i*(-46/105+i*(1118711/3870720))))),this.gtu[1]=a*(13/48+i*(-3/5+i*(557/1440+i*(281/630+i*(-1983433/1935360))))),a=a*i,this.utg[2]=a*(-17/480+i*(37/840+i*(209/4480+i*(-5569/90720)))),this.gtu[2]=a*(61/240+i*(-103/140+i*(15061/26880+i*(167603/181440)))),a=a*i,this.utg[3]=a*(-4397/161280+i*(11/504+i*(830251/7257600))),this.gtu[3]=a*(49561/161280+i*(-179/168+i*(6601661/7257600))),a=a*i,this.utg[4]=a*(-4583/161280+i*(108847/3991680)),this.gtu[4]=a*(34729/80640+i*(-3418889/1995840)),a=a*i,this.utg[5]=a*(-20648693/638668800),this.gtu[5]=a*(212378941/319334400);var s=li(this.cbg,this.lat0);this.Zb=-this.Qn*(s+Qs(this.gtu,2*s))}function Ks(t){var i=y(t.x-this.long0),a=t.y;a=li(this.cbg,a);var s=Math.sin(a),e=Math.cos(a),r=Math.sin(i),h=Math.cos(i);a=Math.atan2(s,h*e),i=Math.atan2(r*e,q(s,e*h)),i=Hs(Math.tan(i));var n=ea(this.gtu,2*a,2*i);a=a+n[0],i=i+n[1];var o,l;return Math.abs(i)<=2.623395162778?(o=this.a*(this.Qn*i)+this.x0,l=this.a*(this.Qn*a+this.Zb)+this.y0):(o=1/0,l=1/0),t.x=o,t.y=l,t}function Xs(t){var i=(t.x-this.x0)*(1/this.a),a=(t.y-this.y0)*(1/this.a);a=(a-this.Zb)/this.Qn,i=i/this.Qn;var s,e;if(Math.abs(i)<=2.623395162778){var r=ea(this.utg,2*a,2*i);a=a+r[0],i=i+r[1],i=Math.atan(sa(i));var h=Math.sin(a),n=Math.cos(a),o=Math.sin(i),l=Math.cos(i);a=Math.atan2(h*l,q(o,l*n)),i=Math.atan2(o,l*n),s=y(i+this.long0),e=li(this.cgb,a)}else s=1/0,e=1/0;return t.x=s,t.y=e,t}var Ys=["Extended_Transverse_Mercator","Extended Transverse Mercator","etmerc","Transverse_Mercator","Transverse Mercator","Gauss Kruger","Gauss_Kruger","tmerc"];const Dt={init:Vs,forward:Ks,inverse:Xs,names:Ys};function Zs(t,i){if(t===void 0){if(t=Math.floor((y(i)+Math.PI)*30/Math.PI)+1,t<0)return 0;if(t>60)return 60}return t}var te="etmerc";function ie(){var t=Zs(this.zone,this.long0);if(t===void 0)throw new Error("unknown utm zone");this.lat0=0,this.long0=(6*Math.abs(t)-183)*R,this.x0=5e5,this.y0=this.utmSouth?1e7:0,this.k0=.9996,Dt.init.apply(this),this.forward=Dt.forward,this.inverse=Dt.inverse}var ae=["Universal Transverse Mercator System","utm"];const se={init:ie,names:ae,dependsOn:te};function ci(t,i){return Math.pow((1-t)/(1+t),i)}var ee=20;function re(){var t=Math.sin(this.lat0),i=Math.cos(this.lat0);i*=i,this.rc=Math.sqrt(1-this.es)/(1-this.es*t*t),this.C=Math.sqrt(1+this.es*i*i/(1-this.es)),this.phic0=Math.asin(t/this.C),this.ratexp=.5*this.C*this.e,this.K=Math.tan(.5*this.phic0+b)/(Math.pow(Math.tan(.5*this.lat0+b),this.C)*ci(this.e*t,this.ratexp))}function ne(t){var i=t.x,a=t.y;return t.y=2*Math.atan(this.K*Math.pow(Math.tan(.5*a+b),this.C)*ci(this.e*Math.sin(a),this.ratexp))-m,t.x=this.C*i,t}function he(t){for(var i=1e-14,a=t.x/this.C,s=t.y,e=Math.pow(Math.tan(.5*s+b)/this.K,1/this.C),r=ee;r>0&&(s=2*Math.atan(e*ci(this.e*Math.sin(t.y),-.5*this.e))-m,!(Math.abs(s-t.y)<i));--r)t.y=s;return r?(t.x=a,t.y=s,t):null}const fi={init:re,forward:ne,inverse:he};function oe(){fi.init.apply(this),this.rc&&(this.sinc0=Math.sin(this.phic0),this.cosc0=Math.cos(this.phic0),this.R2=2*this.rc,this.title||(this.title="Oblique Stereographic Alternative"))}function le(t){var i,a,s,e;return t.x=y(t.x-this.long0),fi.forward.apply(this,[t]),i=Math.sin(t.y),a=Math.cos(t.y),s=Math.cos(t.x),e=this.k0*this.R2/(1+this.sinc0*i+this.cosc0*a*s),t.x=e*a*Math.sin(t.x),t.y=e*(this.cosc0*i-this.sinc0*a*s),t.x=this.a*t.x+this.x0,t.y=this.a*t.y+this.y0,t}function ce(t){var i,a,s,e,r;if(t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,r=q(t.x,t.y)){var h=2*Math.atan2(r,this.R2);i=Math.sin(h),a=Math.cos(h),e=Math.asin(a*this.sinc0+t.y*i*this.cosc0/r),s=Math.atan2(t.x*i,r*this.cosc0*a-t.y*this.sinc0*i)}else e=this.phic0,s=0;return t.x=s,t.y=e,fi.inverse.apply(this,[t]),t.x=y(t.x+this.long0),t}var fe=["Stereographic_North_Pole","Oblique_Stereographic","sterea","Oblique Stereographic Alternative","Double_Stereographic"];const _e={init:oe,forward:le,inverse:ce,names:fe};function _i(t,i,a){return i*=a,Math.tan(.5*(m+t))*Math.pow((1-i)/(1+i),.5*a)}function ue(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.coslat0=Math.cos(this.lat0),this.sinlat0=Math.sin(this.lat0),this.sphere?this.k0===1&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=v&&(this.k0=.5*(1+pt(this.lat0)*Math.sin(this.lat_ts))):(Math.abs(this.coslat0)<=v&&(this.lat0>0?this.con=1:this.con=-1),this.cons=Math.sqrt(Math.pow(1+this.e,1+this.e)*Math.pow(1-this.e,1-this.e)),this.k0===1&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=v&&Math.abs(Math.cos(this.lat_ts))>v&&(this.k0=.5*this.cons*H(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts))/B(this.e,this.con*this.lat_ts,this.con*Math.sin(this.lat_ts))),this.ms1=H(this.e,this.sinlat0,this.coslat0),this.X0=2*Math.atan(_i(this.lat0,this.sinlat0,this.e))-m,this.cosX0=Math.cos(this.X0),this.sinX0=Math.sin(this.X0))}function ge(t){var i=t.x,a=t.y,s=Math.sin(a),e=Math.cos(a),r,h,n,o,l,_,f=y(i-this.long0);return Math.abs(Math.abs(i-this.long0)-Math.PI)<=v&&Math.abs(a+this.lat0)<=v?(t.x=NaN,t.y=NaN,t):this.sphere?(r=2*this.k0/(1+this.sinlat0*s+this.coslat0*e*Math.cos(f)),t.x=this.a*r*e*Math.sin(f)+this.x0,t.y=this.a*r*(this.coslat0*s-this.sinlat0*e*Math.cos(f))+this.y0,t):(h=2*Math.atan(_i(a,s,this.e))-m,o=Math.cos(h),n=Math.sin(h),Math.abs(this.coslat0)<=v?(l=B(this.e,a*this.con,this.con*s),_=2*this.a*this.k0*l/this.cons,t.x=this.x0+_*Math.sin(i-this.long0),t.y=this.y0-this.con*_*Math.cos(i-this.long0),t):(Math.abs(this.sinlat0)<v?(r=2*this.a*this.k0/(1+o*Math.cos(f)),t.y=r*n):(r=2*this.a*this.k0*this.ms1/(this.cosX0*(1+this.sinX0*n+this.cosX0*o*Math.cos(f))),t.y=r*(this.cosX0*n-this.sinX0*o*Math.cos(f))+this.y0),t.x=r*o*Math.sin(f)+this.x0,t))}function me(t){t.x-=this.x0,t.y-=this.y0;var i,a,s,e,r,h=Math.sqrt(t.x*t.x+t.y*t.y);if(this.sphere){var n=2*Math.atan(h/(2*this.a*this.k0));return i=this.long0,a=this.lat0,h<=v?(t.x=i,t.y=a,t):(a=Math.asin(Math.cos(n)*this.sinlat0+t.y*Math.sin(n)*this.coslat0/h),Math.abs(this.coslat0)<v?this.lat0>0?i=y(this.long0+Math.atan2(t.x,-1*t.y)):i=y(this.long0+Math.atan2(t.x,t.y)):i=y(this.long0+Math.atan2(t.x*Math.sin(n),h*this.coslat0*Math.cos(n)-t.y*this.sinlat0*Math.sin(n))),t.x=i,t.y=a,t)}else if(Math.abs(this.coslat0)<=v){if(h<=v)return a=this.lat0,i=this.long0,t.x=i,t.y=a,t;t.x*=this.con,t.y*=this.con,s=h*this.cons/(2*this.a*this.k0),a=this.con*xt(this.e,s),i=this.con*y(this.con*this.long0+Math.atan2(t.x,-1*t.y))}else e=2*Math.atan(h*this.cosX0/(2*this.a*this.k0*this.ms1)),i=this.long0,h<=v?r=this.X0:(r=Math.asin(Math.cos(e)*this.sinX0+t.y*Math.sin(e)*this.cosX0/h),i=y(this.long0+Math.atan2(t.x*Math.sin(e),h*this.cosX0*Math.cos(e)-t.y*this.sinX0*Math.sin(e)))),a=-1*xt(this.e,Math.tan(.5*(m+r)));return t.x=i,t.y=a,t}var de=["stere","Stereographic_South_Pole","Polar_Stereographic_variant_A","Polar_Stereographic_variant_B","Polar_Stereographic"];const Me={init:ue,forward:ge,inverse:me,names:de,ssfn_:_i};function ve(){var t=this.lat0;this.lambda0=this.long0;var i=Math.sin(t),a=this.a,s=this.rf,e=1/s,r=2*e-Math.pow(e,2),h=this.e=Math.sqrt(r);this.R=this.k0*a*Math.sqrt(1-r)/(1-r*Math.pow(i,2)),this.alpha=Math.sqrt(1+r/(1-r)*Math.pow(Math.cos(t),4)),this.b0=Math.asin(i/this.alpha);var n=Math.log(Math.tan(Math.PI/4+this.b0/2)),o=Math.log(Math.tan(Math.PI/4+t/2)),l=Math.log((1+h*i)/(1-h*i));this.K=n-this.alpha*o+this.alpha*h/2*l}function ye(t){var i=Math.log(Math.tan(Math.PI/4-t.y/2)),a=this.e/2*Math.log((1+this.e*Math.sin(t.y))/(1-this.e*Math.sin(t.y))),s=-this.alpha*(i+a)+this.K,e=2*(Math.atan(Math.exp(s))-Math.PI/4),r=this.alpha*(t.x-this.lambda0),h=Math.atan(Math.sin(r)/(Math.sin(this.b0)*Math.tan(e)+Math.cos(this.b0)*Math.cos(r))),n=Math.asin(Math.cos(this.b0)*Math.sin(e)-Math.sin(this.b0)*Math.cos(e)*Math.cos(r));return t.y=this.R/2*Math.log((1+Math.sin(n))/(1-Math.sin(n)))+this.y0,t.x=this.R*h+this.x0,t}function Se(t){for(var i=t.x-this.x0,a=t.y-this.y0,s=i/this.R,e=2*(Math.atan(Math.exp(a/this.R))-Math.PI/4),r=Math.asin(Math.cos(this.b0)*Math.sin(e)+Math.sin(this.b0)*Math.cos(e)*Math.cos(s)),h=Math.atan(Math.sin(s)/(Math.cos(this.b0)*Math.cos(s)-Math.sin(this.b0)*Math.tan(e))),n=this.lambda0+h/this.alpha,o=0,l=r,_=-1e3,f=0;Math.abs(l-_)>1e-7;){if(++f>20)return;o=1/this.alpha*(Math.log(Math.tan(Math.PI/4+r/2))-this.K)+this.e*Math.log(Math.tan(Math.PI/4+Math.asin(this.e*Math.sin(l))/2)),_=l,l=2*Math.atan(Math.exp(o))-Math.PI/2}return t.x=n,t.y=l,t}var Ee=["somerc"];const Ge={init:ve,forward:ye,inverse:Se,names:Ee};var rt=1e-7;function we(t){var i=["Hotine_Oblique_Mercator","Hotine_Oblique_Mercator_variant_A","Hotine_Oblique_Mercator_Azimuth_Natural_Origin"],a=typeof t.projName=="object"?Object.keys(t.projName)[0]:t.projName;return"no_uoff"in t||"no_off"in t||i.indexOf(a)!==-1||i.indexOf(Hi(a))!==-1}function Pe(){var t,i,a,s,e,r,h,n,o,l,_=0,f,c=0,u=0,g=0,d=0,M=0,S=0;this.no_off=we(this),this.no_rot="no_rot"in this;var E=!1;"alpha"in this&&(E=!0);var G=!1;if("rectified_grid_angle"in this&&(G=!0),E&&(S=this.alpha),G&&(_=this.rectified_grid_angle),E||G)c=this.longc;else if(u=this.long1,d=this.lat1,g=this.long2,M=this.lat2,Math.abs(d-M)<=rt||(t=Math.abs(d))<=rt||Math.abs(t-m)<=rt||Math.abs(Math.abs(this.lat0)-m)<=rt||Math.abs(Math.abs(M)-m)<=rt)throw new Error;var P=1-this.es;i=Math.sqrt(P),Math.abs(this.lat0)>v?(n=Math.sin(this.lat0),a=Math.cos(this.lat0),t=1-this.es*n*n,this.B=a*a,this.B=Math.sqrt(1+this.es*this.B*this.B/P),this.A=this.B*this.k0*i/t,s=this.B*i/(a*Math.sqrt(t)),e=s*s-1,e<=0?e=0:(e=Math.sqrt(e),this.lat0<0&&(e=-e)),this.E=e+=s,this.E*=Math.pow(B(this.e,this.lat0,n),this.B)):(this.B=1/i,this.A=this.k0,this.E=s=e=1),E||G?(E?(f=Math.asin(Math.sin(S)/s),G||(_=S)):(f=_,S=Math.asin(s*Math.sin(f))),this.lam0=c-Math.asin(.5*(e-1/e)*Math.tan(f))/this.B):(r=Math.pow(B(this.e,d,Math.sin(d)),this.B),h=Math.pow(B(this.e,M,Math.sin(M)),this.B),e=this.E/r,o=(h-r)/(h+r),l=this.E*this.E,l=(l-h*r)/(l+h*r),t=u-g,t<-Math.PI?g-=wt:t>Math.PI&&(g+=wt),this.lam0=y(.5*(u+g)-Math.atan(l*Math.tan(.5*this.B*(u-g))/o)/this.B),f=Math.atan(2*Math.sin(this.B*y(u-this.lam0))/(e-1/e)),_=S=Math.asin(s*Math.sin(f))),this.singam=Math.sin(f),this.cosgam=Math.cos(f),this.sinrot=Math.sin(_),this.cosrot=Math.cos(_),this.rB=1/this.B,this.ArB=this.A*this.rB,this.BrA=1/this.ArB,this.no_off?this.u_0=0:(this.u_0=Math.abs(this.ArB*Math.atan(Math.sqrt(s*s-1)/Math.cos(S))),this.lat0<0&&(this.u_0=-this.u_0)),e=.5*f,this.v_pole_n=this.ArB*Math.log(Math.tan(b-e)),this.v_pole_s=this.ArB*Math.log(Math.tan(b+e))}function xe(t){var i={},a,s,e,r,h,n,o,l;if(t.x=t.x-this.lam0,Math.abs(Math.abs(t.y)-m)>v){if(h=this.E/Math.pow(B(this.e,t.y,Math.sin(t.y)),this.B),n=1/h,a=.5*(h-n),s=.5*(h+n),r=Math.sin(this.B*t.x),e=(a*this.singam-r*this.cosgam)/s,Math.abs(Math.abs(e)-1)<v)throw new Error;l=.5*this.ArB*Math.log((1-e)/(1+e)),n=Math.cos(this.B*t.x),Math.abs(n)<rt?o=this.A*t.x:o=this.ArB*Math.atan2(a*this.cosgam+r*this.singam,n)}else l=t.y>0?this.v_pole_n:this.v_pole_s,o=this.ArB*t.y;return this.no_rot?(i.x=o,i.y=l):(o-=this.u_0,i.x=l*this.cosrot+o*this.sinrot,i.y=o*this.cosrot-l*this.sinrot),i.x=this.a*i.x+this.x0,i.y=this.a*i.y+this.y0,i}function be(t){var i,a,s,e,r,h,n,o={};if(t.x=(t.x-this.x0)*(1/this.a),t.y=(t.y-this.y0)*(1/this.a),this.no_rot?(a=t.y,i=t.x):(a=t.x*this.cosrot-t.y*this.sinrot,i=t.y*this.cosrot+t.x*this.sinrot+this.u_0),s=Math.exp(-this.BrA*a),e=.5*(s-1/s),r=.5*(s+1/s),h=Math.sin(this.BrA*i),n=(h*this.cosgam+e*this.singam)/r,Math.abs(Math.abs(n)-1)<v)o.x=0,o.y=n<0?-m:m;else{if(o.y=this.E/Math.sqrt((1+n)/(1-n)),o.y=xt(this.e,Math.pow(o.y,1/this.B)),o.y===1/0)throw new Error;o.x=-this.rB*Math.atan2(e*this.cosgam-h*this.singam,Math.cos(this.BrA*i))}return o.x+=this.lam0,o}var pe=["Hotine_Oblique_Mercator","Hotine Oblique Mercator","Hotine_Oblique_Mercator_variant_A","Hotine_Oblique_Mercator_Variant_B","Hotine_Oblique_Mercator_Azimuth_Natural_Origin","Hotine_Oblique_Mercator_Two_Point_Natural_Origin","Hotine_Oblique_Mercator_Azimuth_Center","Oblique_Mercator","omerc"];const Ce={init:Pe,forward:xe,inverse:be,names:pe};function Ae(){if(this.lat2||(this.lat2=this.lat1),this.k0||(this.k0=1),this.x0=this.x0||0,this.y0=this.y0||0,!(Math.abs(this.lat1+this.lat2)<v)){var t=this.b/this.a;this.e=Math.sqrt(1-t*t);var i=Math.sin(this.lat1),a=Math.cos(this.lat1),s=H(this.e,i,a),e=B(this.e,this.lat1,i),r=Math.sin(this.lat2),h=Math.cos(this.lat2),n=H(this.e,r,h),o=B(this.e,this.lat2,r),l=Math.abs(Math.abs(this.lat0)-m)<v?0:B(this.e,this.lat0,Math.sin(this.lat0));Math.abs(this.lat1-this.lat2)>v?this.ns=Math.log(s/n)/Math.log(e/o):this.ns=i,isNaN(this.ns)&&(this.ns=i),this.f0=s/(this.ns*Math.pow(e,this.ns)),this.rh=this.a*this.f0*Math.pow(l,this.ns),this.title||(this.title="Lambert Conformal Conic")}}function Ne(t){var i=t.x,a=t.y;Math.abs(2*Math.abs(a)-Math.PI)<=v&&(a=pt(a)*(m-2*v));var s=Math.abs(Math.abs(a)-m),e,r;if(s>v)e=B(this.e,a,Math.sin(a)),r=this.a*this.f0*Math.pow(e,this.ns);else{if(s=a*this.ns,s<=0)return null;r=0}var h=this.ns*y(i-this.long0);return t.x=this.k0*(r*Math.sin(h))+this.x0,t.y=this.k0*(this.rh-r*Math.cos(h))+this.y0,t}function Re(t){var i,a,s,e,r,h=(t.x-this.x0)/this.k0,n=this.rh-(t.y-this.y0)/this.k0;this.ns>0?(i=Math.sqrt(h*h+n*n),a=1):(i=-Math.sqrt(h*h+n*n),a=-1);var o=0;if(i!==0&&(o=Math.atan2(a*h,a*n)),i!==0||this.ns>0){if(a=1/this.ns,s=Math.pow(i/(this.a*this.f0),a),e=xt(this.e,s),e===-9999)return null}else e=-m;return r=y(o/this.ns+this.long0),t.x=r,t.y=e,t}var Ie=["Lambert Tangential Conformal Conic Projection","Lambert_Conformal_Conic","Lambert_Conformal_Conic_1SP","Lambert_Conformal_Conic_2SP","lcc","Lambert Conic Conformal (1SP)","Lambert Conic Conformal (2SP)"];const Te={init:Ae,forward:Ne,inverse:Re,names:Ie};function Oe(){this.a=6377397155e-3,this.es=.006674372230614,this.e=Math.sqrt(this.es),this.lat0||(this.lat0=.863937979737193),this.long0||(this.long0=.7417649320975901-.308341501185665),this.k0||(this.k0=.9999),this.s45=.785398163397448,this.s90=2*this.s45,this.fi0=this.lat0,this.e2=this.es,this.e=Math.sqrt(this.e2),this.alfa=Math.sqrt(1+this.e2*Math.pow(Math.cos(this.fi0),4)/(1-this.e2)),this.uq=1.04216856380474,this.u0=Math.asin(Math.sin(this.fi0)/this.alfa),this.g=Math.pow((1+this.e*Math.sin(this.fi0))/(1-this.e*Math.sin(this.fi0)),this.alfa*this.e/2),this.k=Math.tan(this.u0/2+this.s45)/Math.pow(Math.tan(this.fi0/2+this.s45),this.alfa)*this.g,this.k1=this.k0,this.n0=this.a*Math.sqrt(1-this.e2)/(1-this.e2*Math.pow(Math.sin(this.fi0),2)),this.s0=1.37008346281555,this.n=Math.sin(this.s0),this.ro0=this.k1*this.n0/Math.tan(this.s0),this.ad=this.s90-this.uq}function Le(t){var i,a,s,e,r,h,n,o=t.x,l=t.y,_=y(o-this.long0);return i=Math.pow((1+this.e*Math.sin(l))/(1-this.e*Math.sin(l)),this.alfa*this.e/2),a=2*(Math.atan(this.k*Math.pow(Math.tan(l/2+this.s45),this.alfa)/i)-this.s45),s=-_*this.alfa,e=Math.asin(Math.cos(this.ad)*Math.sin(a)+Math.sin(this.ad)*Math.cos(a)*Math.cos(s)),r=Math.asin(Math.cos(a)*Math.sin(s)/Math.cos(e)),h=this.n*r,n=this.ro0*Math.pow(Math.tan(this.s0/2+this.s45),this.n)/Math.pow(Math.tan(e/2+this.s45),this.n),t.y=n*Math.cos(h)/1,t.x=n*Math.sin(h)/1,this.czech||(t.y*=-1,t.x*=-1),t}function ke(t){var i,a,s,e,r,h,n,o,l=t.x;t.x=t.y,t.y=l,this.czech||(t.y*=-1,t.x*=-1),h=Math.sqrt(t.x*t.x+t.y*t.y),r=Math.atan2(t.y,t.x),e=r/Math.sin(this.s0),s=2*(Math.atan(Math.pow(this.ro0/h,1/this.n)*Math.tan(this.s0/2+this.s45))-this.s45),i=Math.asin(Math.cos(this.ad)*Math.sin(s)-Math.sin(this.ad)*Math.cos(s)*Math.cos(e)),a=Math.asin(Math.cos(s)*Math.sin(e)/Math.cos(i)),t.x=this.long0-a/this.alfa,n=i,o=0;var _=0;do t.y=2*(Math.atan(Math.pow(this.k,-1/this.alfa)*Math.pow(Math.tan(i/2+this.s45),1/this.alfa)*Math.pow((1+this.e*Math.sin(n))/(1-this.e*Math.sin(n)),this.e/2))-this.s45),Math.abs(n-t.y)<1e-10&&(o=1),n=t.y,_+=1;while(o===0&&_<15);return _>=15?null:t}var De=["Krovak","krovak"];const qe={init:Oe,forward:Le,inverse:ke,names:De};function k(t,i,a,s,e){return t*e-i*Math.sin(2*e)+a*Math.sin(4*e)-s*Math.sin(6*e)}function Ct(t){return 1-.25*t*(1+t/16*(3+1.25*t))}function At(t){return .375*t*(1+.25*t*(1+.46875*t))}function Nt(t){return .05859375*t*t*(1+.75*t)}function Rt(t){return t*t*t*(35/3072)}function ui(t,i,a){var s=i*a;return t/Math.sqrt(1-s*s)}function Z(t){return Math.abs(t)<m?t:t-pt(t)*Math.PI}function $t(t,i,a,s,e){var r,h;r=t/i;for(var n=0;n<15;n++)if(h=(t-(i*r-a*Math.sin(2*r)+s*Math.sin(4*r)-e*Math.sin(6*r)))/(i-2*a*Math.cos(2*r)+4*s*Math.cos(4*r)-6*e*Math.cos(6*r)),r+=h,Math.abs(h)<=1e-10)return r;return NaN}function Fe(){this.sphere||(this.e0=Ct(this.es),this.e1=At(this.es),this.e2=Nt(this.es),this.e3=Rt(this.es),this.ml0=this.a*k(this.e0,this.e1,this.e2,this.e3,this.lat0))}function je(t){var i,a,s=t.x,e=t.y;if(s=y(s-this.long0),this.sphere)i=this.a*Math.asin(Math.cos(e)*Math.sin(s)),a=this.a*(Math.atan2(Math.tan(e),Math.cos(s))-this.lat0);else{var r=Math.sin(e),h=Math.cos(e),n=ui(this.a,this.e,r),o=Math.tan(e)*Math.tan(e),l=s*Math.cos(e),_=l*l,f=this.es*h*h/(1-this.es),c=this.a*k(this.e0,this.e1,this.e2,this.e3,e);i=n*l*(1-_*o*(1/6-(8-o+8*f)*_/120)),a=c-this.ml0+n*r/h*_*(.5+(5-o+6*f)*_/24)}return t.x=i+this.x0,t.y=a+this.y0,t}function Ue(t){t.x-=this.x0,t.y-=this.y0;var i=t.x/this.a,a=t.y/this.a,s,e;if(this.sphere){var r=a+this.lat0;s=Math.asin(Math.sin(r)*Math.cos(i)),e=Math.atan2(Math.tan(i),Math.cos(r))}else{var h=this.ml0/this.a+a,n=$t(h,this.e0,this.e1,this.e2,this.e3);if(Math.abs(Math.abs(n)-m)<=v)return t.x=this.long0,t.y=m,a<0&&(t.y*=-1),t;var o=ui(this.a,this.e,Math.sin(n)),l=o*o*o/this.a/this.a*(1-this.es),_=Math.pow(Math.tan(n),2),f=i*this.a/o,c=f*f;s=n-o*Math.tan(n)/l*f*f*(.5-(1+3*_)*f*f/24),e=f*(1-c*(_/3+(1+3*_)*_*c/15))/Math.cos(n)}return t.x=y(e+this.long0),t.y=Z(s),t}var $e=["Cassini","Cassini_Soldner","cass"];const Be={init:Fe,forward:je,inverse:Ue,names:$e};function K(t,i){var a;return t>1e-7?(a=t*i,(1-t*t)*(i/(1-a*a)-.5/t*Math.log((1-a)/(1+a)))):2*i}var ti=1,ii=2,ai=3,qt=4;function ze(){var t=Math.abs(this.lat0);if(Math.abs(t-m)<v?this.mode=this.lat0<0?ti:ii:Math.abs(t)<v?this.mode=ai:this.mode=qt,this.es>0){var i;switch(this.qp=K(this.e,1),this.mmf=.5/(1-this.es),this.apa=Ze(this.es),this.mode){case ii:this.dd=1;break;case ti:this.dd=1;break;case ai:this.rq=Math.sqrt(.5*this.qp),this.dd=1/this.rq,this.xmf=1,this.ymf=.5*this.qp;break;case qt:this.rq=Math.sqrt(.5*this.qp),i=Math.sin(this.lat0),this.sinb1=K(this.e,i)/this.qp,this.cosb1=Math.sqrt(1-this.sinb1*this.sinb1),this.dd=Math.cos(this.lat0)/(Math.sqrt(1-this.es*i*i)*this.rq*this.cosb1),this.ymf=(this.xmf=this.rq)/this.dd,this.xmf*=this.dd;break}}else this.mode===qt&&(this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0))}function We(t){var i,a,s,e,r,h,n,o,l,_,f=t.x,c=t.y;if(f=y(f-this.long0),this.sphere){if(r=Math.sin(c),_=Math.cos(c),s=Math.cos(f),this.mode===this.OBLIQ||this.mode===this.EQUIT){if(a=this.mode===this.EQUIT?1+_*s:1+this.sinph0*r+this.cosph0*_*s,a<=v)return null;a=Math.sqrt(2/a),i=a*_*Math.sin(f),a*=this.mode===this.EQUIT?r:this.cosph0*r-this.sinph0*_*s}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(s=-s),Math.abs(c+this.lat0)<v)return null;a=b-c*.5,a=2*(this.mode===this.S_POLE?Math.cos(a):Math.sin(a)),i=a*Math.sin(f),a*=s}}else{switch(n=0,o=0,l=0,s=Math.cos(f),e=Math.sin(f),r=Math.sin(c),h=K(this.e,r),(this.mode===this.OBLIQ||this.mode===this.EQUIT)&&(n=h/this.qp,o=Math.sqrt(1-n*n)),this.mode){case this.OBLIQ:l=1+this.sinb1*n+this.cosb1*o*s;break;case this.EQUIT:l=1+o*s;break;case this.N_POLE:l=m+c,h=this.qp-h;break;case this.S_POLE:l=c-m,h=this.qp+h;break}if(Math.abs(l)<v)return null;switch(this.mode){case this.OBLIQ:case this.EQUIT:l=Math.sqrt(2/l),this.mode===this.OBLIQ?a=this.ymf*l*(this.cosb1*n-this.sinb1*o*s):a=(l=Math.sqrt(2/(1+o*s)))*n*this.ymf,i=this.xmf*l*o*e;break;case this.N_POLE:case this.S_POLE:h>=0?(i=(l=Math.sqrt(h))*e,a=s*(this.mode===this.S_POLE?l:-l)):i=a=0;break}}return t.x=this.a*i+this.x0,t.y=this.a*a+this.y0,t}function He(t){t.x-=this.x0,t.y-=this.y0;var i=t.x/this.a,a=t.y/this.a,s,e,r,h,n,o,l;if(this.sphere){var _=0,f,c=0;if(f=Math.sqrt(i*i+a*a),e=f*.5,e>1)return null;switch(e=2*Math.asin(e),(this.mode===this.OBLIQ||this.mode===this.EQUIT)&&(c=Math.sin(e),_=Math.cos(e)),this.mode){case this.EQUIT:e=Math.abs(f)<=v?0:Math.asin(a*c/f),i*=c,a=_*f;break;case this.OBLIQ:e=Math.abs(f)<=v?this.lat0:Math.asin(_*this.sinph0+a*c*this.cosph0/f),i*=c*this.cosph0,a=(_-Math.sin(e)*this.sinph0)*f;break;case this.N_POLE:a=-a,e=m-e;break;case this.S_POLE:e-=m;break}s=a===0&&(this.mode===this.EQUIT||this.mode===this.OBLIQ)?0:Math.atan2(i,a)}else{if(l=0,this.mode===this.OBLIQ||this.mode===this.EQUIT){if(i/=this.dd,a*=this.dd,o=Math.sqrt(i*i+a*a),o<v)return t.x=this.long0,t.y=this.lat0,t;h=2*Math.asin(.5*o/this.rq),r=Math.cos(h),i*=h=Math.sin(h),this.mode===this.OBLIQ?(l=r*this.sinb1+a*h*this.cosb1/o,n=this.qp*l,a=o*this.cosb1*r-a*this.sinb1*h):(l=a*h/o,n=this.qp*l,a=o*r)}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(a=-a),n=i*i+a*a,!n)return t.x=this.long0,t.y=this.lat0,t;l=1-n/this.qp,this.mode===this.S_POLE&&(l=-l)}s=Math.atan2(i,a),e=tr(Math.asin(l),this.apa)}return t.x=y(this.long0+s),t.y=e,t}var Qe=.3333333333333333,Je=.17222222222222222,Ve=.10257936507936508,Ke=.06388888888888888,Xe=.0664021164021164,Ye=.016415012942191543;function Ze(t){var i,a=[];return a[0]=t*Qe,i=t*t,a[0]+=i*Je,a[1]=i*Ke,i*=t,a[0]+=i*Ve,a[1]+=i*Xe,a[2]=i*Ye,a}function tr(t,i){var a=t+t;return t+i[0]*Math.sin(a)+i[1]*Math.sin(a+a)+i[2]*Math.sin(a+a+a)}var ir=["Lambert Azimuthal Equal Area","Lambert_Azimuthal_Equal_Area","laea"];const ar={init:ze,forward:We,inverse:He,names:ir,S_POLE:ti,N_POLE:ii,EQUIT:ai,OBLIQ:qt};function Y(t){return Math.abs(t)>1&&(t=t>1?1:-1),Math.asin(t)}function sr(){Math.abs(this.lat1+this.lat2)<v||(this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e3=Math.sqrt(this.es),this.sin_po=Math.sin(this.lat1),this.cos_po=Math.cos(this.lat1),this.t1=this.sin_po,this.con=this.sin_po,this.ms1=H(this.e3,this.sin_po,this.cos_po),this.qs1=K(this.e3,this.sin_po),this.sin_po=Math.sin(this.lat2),this.cos_po=Math.cos(this.lat2),this.t2=this.sin_po,this.ms2=H(this.e3,this.sin_po,this.cos_po),this.qs2=K(this.e3,this.sin_po),this.sin_po=Math.sin(this.lat0),this.cos_po=Math.cos(this.lat0),this.t3=this.sin_po,this.qs0=K(this.e3,this.sin_po),Math.abs(this.lat1-this.lat2)>v?this.ns0=(this.ms1*this.ms1-this.ms2*this.ms2)/(this.qs2-this.qs1):this.ns0=this.con,this.c=this.ms1*this.ms1+this.ns0*this.qs1,this.rh=this.a*Math.sqrt(this.c-this.ns0*this.qs0)/this.ns0)}function er(t){var i=t.x,a=t.y;this.sin_phi=Math.sin(a),this.cos_phi=Math.cos(a);var s=K(this.e3,this.sin_phi),e=this.a*Math.sqrt(this.c-this.ns0*s)/this.ns0,r=this.ns0*y(i-this.long0),h=e*Math.sin(r)+this.x0,n=this.rh-e*Math.cos(r)+this.y0;return t.x=h,t.y=n,t}function rr(t){var i,a,s,e,r,h;return t.x-=this.x0,t.y=this.rh-t.y+this.y0,this.ns0>=0?(i=Math.sqrt(t.x*t.x+t.y*t.y),s=1):(i=-Math.sqrt(t.x*t.x+t.y*t.y),s=-1),e=0,i!==0&&(e=Math.atan2(s*t.x,s*t.y)),s=i*this.ns0/this.a,this.sphere?h=Math.asin((this.c-s*s)/(2*this.ns0)):(a=(this.c-s*s)/this.ns0,h=this.phi1z(this.e3,a)),r=y(e/this.ns0+this.long0),t.x=r,t.y=h,t}function nr(t,i){var a,s,e,r,h,n=Y(.5*i);if(t<v)return n;for(var o=t*t,l=1;l<=25;l++)if(a=Math.sin(n),s=Math.cos(n),e=t*a,r=1-e*e,h=.5*r*r/s*(i/(1-o)-a/r+.5/t*Math.log((1-e)/(1+e))),n=n+h,Math.abs(h)<=1e-7)return n;return null}var hr=["Albers_Conic_Equal_Area","Albers_Equal_Area","Albers","aea"];const or={init:sr,forward:er,inverse:rr,names:hr,phi1z:nr};function lr(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0),this.infinity_dist=1e3*this.a,this.rc=1}function cr(t){var i,a,s,e,r,h,n,o,l=t.x,_=t.y;return s=y(l-this.long0),i=Math.sin(_),a=Math.cos(_),e=Math.cos(s),h=this.sin_p14*i+this.cos_p14*a*e,r=1,h>0||Math.abs(h)<=v?(n=this.x0+this.a*r*a*Math.sin(s)/h,o=this.y0+this.a*r*(this.cos_p14*i-this.sin_p14*a*e)/h):(n=this.x0+this.infinity_dist*a*Math.sin(s),o=this.y0+this.infinity_dist*(this.cos_p14*i-this.sin_p14*a*e)),t.x=n,t.y=o,t}function fr(t){var i,a,s,e,r,h;return t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,(i=Math.sqrt(t.x*t.x+t.y*t.y))?(e=Math.atan2(i,this.rc),a=Math.sin(e),s=Math.cos(e),h=Y(s*this.sin_p14+t.y*a*this.cos_p14/i),r=Math.atan2(t.x*a,i*this.cos_p14*s-t.y*this.sin_p14*a),r=y(this.long0+r)):(h=this.phic0,r=0),t.x=r,t.y=h,t}var _r=["gnom"];const ur={init:lr,forward:cr,inverse:fr,names:_r};function gr(t,i){var a=1-(1-t*t)/(2*t)*Math.log((1-t)/(1+t));if(Math.abs(Math.abs(i)-a)<1e-6)return i<0?-1*m:m;for(var s=Math.asin(.5*i),e,r,h,n,o=0;o<30;o++)if(r=Math.sin(s),h=Math.cos(s),n=t*r,e=Math.pow(1-n*n,2)/(2*h)*(i/(1-t*t)-r/(1-n*n)+.5/t*Math.log((1-n)/(1+n))),s+=e,Math.abs(e)<=1e-10)return s;return NaN}function mr(){this.sphere||(this.k0=H(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)))}function dr(t){var i=t.x,a=t.y,s,e,r=y(i-this.long0);if(this.sphere)s=this.x0+this.a*r*Math.cos(this.lat_ts),e=this.y0+this.a*Math.sin(a)/Math.cos(this.lat_ts);else{var h=K(this.e,Math.sin(a));s=this.x0+this.a*this.k0*r,e=this.y0+this.a*h*.5/this.k0}return t.x=s,t.y=e,t}function Mr(t){t.x-=this.x0,t.y-=this.y0;var i,a;return this.sphere?(i=y(this.long0+t.x/this.a/Math.cos(this.lat_ts)),a=Math.asin(t.y/this.a*Math.cos(this.lat_ts))):(a=gr(this.e,2*t.y*this.k0/this.a),i=y(this.long0+t.x/(this.a*this.k0))),t.x=i,t.y=a,t}var vr=["cea"];const yr={init:mr,forward:dr,inverse:Mr,names:vr};function Sr(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Equidistant Cylindrical (Plate Carre)",this.rc=Math.cos(this.lat_ts)}function Er(t){var i=t.x,a=t.y,s=y(i-this.long0),e=Z(a-this.lat0);return t.x=this.x0+this.a*s*this.rc,t.y=this.y0+this.a*e,t}function Gr(t){var i=t.x,a=t.y;return t.x=y(this.long0+(i-this.x0)/(this.a*this.rc)),t.y=Z(this.lat0+(a-this.y0)/this.a),t}var wr=["Equirectangular","Equidistant_Cylindrical","Equidistant_Cylindrical_Spherical","eqc"];const Pr={init:Sr,forward:Er,inverse:Gr,names:wr};var Di=20;function xr(){this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=Ct(this.es),this.e1=At(this.es),this.e2=Nt(this.es),this.e3=Rt(this.es),this.ml0=this.a*k(this.e0,this.e1,this.e2,this.e3,this.lat0)}function br(t){var i=t.x,a=t.y,s,e,r,h=y(i-this.long0);if(r=h*Math.sin(a),this.sphere)Math.abs(a)<=v?(s=this.a*h,e=-1*this.a*this.lat0):(s=this.a*Math.sin(r)/Math.tan(a),e=this.a*(Z(a-this.lat0)+(1-Math.cos(r))/Math.tan(a)));else if(Math.abs(a)<=v)s=this.a*h,e=-1*this.ml0;else{var n=ui(this.a,this.e,Math.sin(a))/Math.tan(a);s=n*Math.sin(r),e=this.a*k(this.e0,this.e1,this.e2,this.e3,a)-this.ml0+n*(1-Math.cos(r))}return t.x=s+this.x0,t.y=e+this.y0,t}function pr(t){var i,a,s,e,r,h,n,o,l;if(s=t.x-this.x0,e=t.y-this.y0,this.sphere)if(Math.abs(e+this.a*this.lat0)<=v)i=y(s/this.a+this.long0),a=0;else{h=this.lat0+e/this.a,n=s*s/this.a/this.a+h*h,o=h;var _;for(r=Di;r;--r)if(_=Math.tan(o),l=-1*(h*(o*_+1)-o-.5*(o*o+n)*_)/((o-h)/_-1),o+=l,Math.abs(l)<=v){a=o;break}i=y(this.long0+Math.asin(s*Math.tan(o)/this.a)/Math.sin(a))}else if(Math.abs(e+this.ml0)<=v)a=0,i=y(this.long0+s/this.a);else{h=(this.ml0+e)/this.a,n=s*s/this.a/this.a+h*h,o=h;var f,c,u,g,d;for(r=Di;r;--r)if(d=this.e*Math.sin(o),f=Math.sqrt(1-d*d)*Math.tan(o),c=this.a*k(this.e0,this.e1,this.e2,this.e3,o),u=this.e0-2*this.e1*Math.cos(2*o)+4*this.e2*Math.cos(4*o)-6*this.e3*Math.cos(6*o),g=c/this.a,l=(h*(f*g+1)-g-.5*f*(g*g+n))/(this.es*Math.sin(2*o)*(g*g+n-2*h*g)/(4*f)+(h-g)*(f*u-2/Math.sin(2*o))-u),o-=l,Math.abs(l)<=v){a=o;break}f=Math.sqrt(1-this.es*Math.pow(Math.sin(a),2))*Math.tan(a),i=y(this.long0+Math.asin(s*f/this.a)/Math.sin(a))}return t.x=i,t.y=a,t}var Cr=["Polyconic","American_Polyconic","poly"];const Ar={init:xr,forward:br,inverse:pr,names:Cr};function Nr(){this.A=[],this.A[1]=.6399175073,this.A[2]=-.1358797613,this.A[3]=.063294409,this.A[4]=-.02526853,this.A[5]=.0117879,this.A[6]=-.0055161,this.A[7]=.0026906,this.A[8]=-.001333,this.A[9]=67e-5,this.A[10]=-34e-5,this.B_re=[],this.B_im=[],this.B_re[1]=.7557853228,this.B_im[1]=0,this.B_re[2]=.249204646,this.B_im[2]=.003371507,this.B_re[3]=-.001541739,this.B_im[3]=.04105856,this.B_re[4]=-.10162907,this.B_im[4]=.01727609,this.B_re[5]=-.26623489,this.B_im[5]=-.36249218,this.B_re[6]=-.6870983,this.B_im[6]=-1.1651967,this.C_re=[],this.C_im=[],this.C_re[1]=1.3231270439,this.C_im[1]=0,this.C_re[2]=-.577245789,this.C_im[2]=-.007809598,this.C_re[3]=.508307513,this.C_im[3]=-.112208952,this.C_re[4]=-.15094762,this.C_im[4]=.18200602,this.C_re[5]=1.01418179,this.C_im[5]=1.64497696,this.C_re[6]=1.9660549,this.C_im[6]=2.5127645,this.D=[],this.D[1]=1.5627014243,this.D[2]=.5185406398,this.D[3]=-.03333098,this.D[4]=-.1052906,this.D[5]=-.0368594,this.D[6]=.007317,this.D[7]=.0122,this.D[8]=.00394,this.D[9]=-.0013}function Rr(t){var i,a=t.x,s=t.y,e=s-this.lat0,r=a-this.long0,h=e/vt*1e-5,n=r,o=1,l=0;for(i=1;i<=10;i++)o=o*h,l=l+this.A[i]*o;var _=l,f=n,c=1,u=0,g,d,M=0,S=0;for(i=1;i<=6;i++)g=c*_-u*f,d=u*_+c*f,c=g,u=d,M=M+this.B_re[i]*c-this.B_im[i]*u,S=S+this.B_im[i]*c+this.B_re[i]*u;return t.x=S*this.a+this.x0,t.y=M*this.a+this.y0,t}function Ir(t){var i,a=t.x,s=t.y,e=a-this.x0,r=s-this.y0,h=r/this.a,n=e/this.a,o=1,l=0,_,f,c=0,u=0;for(i=1;i<=6;i++)_=o*h-l*n,f=l*h+o*n,o=_,l=f,c=c+this.C_re[i]*o-this.C_im[i]*l,u=u+this.C_im[i]*o+this.C_re[i]*l;for(var g=0;g<this.iterations;g++){var d=c,M=u,S,E,G=h,P=n;for(i=2;i<=6;i++)S=d*c-M*u,E=M*c+d*u,d=S,M=E,G=G+(i-1)*(this.B_re[i]*d-this.B_im[i]*M),P=P+(i-1)*(this.B_im[i]*d+this.B_re[i]*M);d=1,M=0;var w=this.B_re[1],x=this.B_im[1];for(i=2;i<=6;i++)S=d*c-M*u,E=M*c+d*u,d=S,M=E,w=w+i*(this.B_re[i]*d-this.B_im[i]*M),x=x+i*(this.B_im[i]*d+this.B_re[i]*M);var C=w*w+x*x;c=(G*w+P*x)/C,u=(P*w-G*x)/C}var T=c,N=u,J=1,j=0;for(i=1;i<=9;i++)J=J*T,j=j+this.D[i]*J;var st=this.lat0+j*vt*1e5,et=this.long0+N;return t.x=et,t.y=st,t}var Tr=["New_Zealand_Map_Grid","nzmg"];const Or={init:Nr,forward:Rr,inverse:Ir,names:Tr};function Lr(){}function kr(t){var i=t.x,a=t.y,s=y(i-this.long0),e=this.x0+this.a*s,r=this.y0+this.a*Math.log(Math.tan(Math.PI/4+a/2.5))*1.25;return t.x=e,t.y=r,t}function Dr(t){t.x-=this.x0,t.y-=this.y0;var i=y(this.long0+t.x/this.a),a=2.5*(Math.atan(Math.exp(.8*t.y/this.a))-Math.PI/4);return t.x=i,t.y=a,t}var qr=["Miller_Cylindrical","mill"];const Fr={init:Lr,forward:kr,inverse:Dr,names:qr};var jr=20;function Ur(){this.sphere?(this.n=1,this.m=0,this.es=0,this.C_y=Math.sqrt((this.m+1)/this.n),this.C_x=this.C_y/(this.m+1)):this.en=hi(this.es)}function $r(t){var i,a,s=t.x,e=t.y;if(s=y(s-this.long0),this.sphere){if(!this.m)e=this.n!==1?Math.asin(this.n*Math.sin(e)):e;else for(var r=this.n*Math.sin(e),h=jr;h;--h){var n=(this.m*e+Math.sin(e)-r)/(this.m+Math.cos(e));if(e-=n,Math.abs(n)<v)break}i=this.a*this.C_x*s*(this.m+Math.cos(e)),a=this.a*this.C_y*e}else{var o=Math.sin(e),l=Math.cos(e);a=this.a*ut(e,o,l,this.en),i=this.a*s*l/Math.sqrt(1-this.es*o*o)}return t.x=i,t.y=a,t}function Br(t){var i,a,s,e;return t.x-=this.x0,s=t.x/this.a,t.y-=this.y0,i=t.y/this.a,this.sphere?(i/=this.C_y,s=s/(this.C_x*(this.m+Math.cos(i))),this.m?i=Y((this.m*i+Math.sin(i))/this.n):this.n!==1&&(i=Y(Math.sin(i)/this.n)),s=y(s+this.long0),i=Z(i)):(i=oi(t.y/this.a,this.es,this.en),e=Math.abs(i),e<m?(e=Math.sin(i),a=this.long0+t.x*Math.sqrt(1-this.es*e*e)/(this.a*Math.cos(i)),s=y(a)):e-v<m&&(s=this.long0)),t.x=s,t.y=i,t}var zr=["Sinusoidal","sinu"];const Wr={init:Ur,forward:$r,inverse:Br,names:zr};function Hr(){}function Qr(t){for(var i=t.x,a=t.y,s=y(i-this.long0),e=a,r=Math.PI*Math.sin(a);;){var h=-(e+Math.sin(e)-r)/(1+Math.cos(e));if(e+=h,Math.abs(h)<v)break}e/=2,Math.PI/2-Math.abs(a)<v&&(s=0);var n=.900316316158*this.a*s*Math.cos(e)+this.x0,o=1.4142135623731*this.a*Math.sin(e)+this.y0;return t.x=n,t.y=o,t}function Jr(t){var i,a;t.x-=this.x0,t.y-=this.y0,a=t.y/(1.4142135623731*this.a),Math.abs(a)>.999999999999&&(a=.999999999999),i=Math.asin(a);var s=y(this.long0+t.x/(.900316316158*this.a*Math.cos(i)));s<-Math.PI&&(s=-Math.PI),s>Math.PI&&(s=Math.PI),a=(2*i+Math.sin(2*i))/Math.PI,Math.abs(a)>1&&(a=1);var e=Math.asin(a);return t.x=s,t.y=e,t}var Vr=["Mollweide","moll"];const Kr={init:Hr,forward:Qr,inverse:Jr,names:Vr};function Xr(){Math.abs(this.lat1+this.lat2)<v||(this.lat2=this.lat2||this.lat1,this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=Ct(this.es),this.e1=At(this.es),this.e2=Nt(this.es),this.e3=Rt(this.es),this.sin_phi=Math.sin(this.lat1),this.cos_phi=Math.cos(this.lat1),this.ms1=H(this.e,this.sin_phi,this.cos_phi),this.ml1=k(this.e0,this.e1,this.e2,this.e3,this.lat1),Math.abs(this.lat1-this.lat2)<v?this.ns=this.sin_phi:(this.sin_phi=Math.sin(this.lat2),this.cos_phi=Math.cos(this.lat2),this.ms2=H(this.e,this.sin_phi,this.cos_phi),this.ml2=k(this.e0,this.e1,this.e2,this.e3,this.lat2),this.ns=(this.ms1-this.ms2)/(this.ml2-this.ml1)),this.g=this.ml1+this.ms1/this.ns,this.ml0=k(this.e0,this.e1,this.e2,this.e3,this.lat0),this.rh=this.a*(this.g-this.ml0))}function Yr(t){var i=t.x,a=t.y,s;if(this.sphere)s=this.a*(this.g-a);else{var e=k(this.e0,this.e1,this.e2,this.e3,a);s=this.a*(this.g-e)}var r=this.ns*y(i-this.long0),h=this.x0+s*Math.sin(r),n=this.y0+this.rh-s*Math.cos(r);return t.x=h,t.y=n,t}function Zr(t){t.x-=this.x0,t.y=this.rh-t.y+this.y0;var i,a,s,e;this.ns>=0?(a=Math.sqrt(t.x*t.x+t.y*t.y),i=1):(a=-Math.sqrt(t.x*t.x+t.y*t.y),i=-1);var r=0;if(a!==0&&(r=Math.atan2(i*t.x,i*t.y)),this.sphere)return e=y(this.long0+r/this.ns),s=Z(this.g-a/this.a),t.x=e,t.y=s,t;var h=this.g-a/this.a;return s=$t(h,this.e0,this.e1,this.e2,this.e3),e=y(this.long0+r/this.ns),t.x=e,t.y=s,t}var t0=["Equidistant_Conic","eqdc"];const i0={init:Xr,forward:Yr,inverse:Zr,names:t0};function a0(){this.R=this.a}function s0(t){var i=t.x,a=t.y,s=y(i-this.long0),e,r;Math.abs(a)<=v&&(e=this.x0+this.R*s,r=this.y0);var h=Y(2*Math.abs(a/Math.PI));(Math.abs(s)<=v||Math.abs(Math.abs(a)-m)<=v)&&(e=this.x0,a>=0?r=this.y0+Math.PI*this.R*Math.tan(.5*h):r=this.y0+Math.PI*this.R*-Math.tan(.5*h));var n=.5*Math.abs(Math.PI/s-s/Math.PI),o=n*n,l=Math.sin(h),_=Math.cos(h),f=_/(l+_-1),c=f*f,u=f*(2/l-1),g=u*u,d=Math.PI*this.R*(n*(f-g)+Math.sqrt(o*(f-g)*(f-g)-(g+o)*(c-g)))/(g+o);s<0&&(d=-d),e=this.x0+d;var M=o+f;return d=Math.PI*this.R*(u*M-n*Math.sqrt((g+o)*(o+1)-M*M))/(g+o),a>=0?r=this.y0+d:r=this.y0-d,t.x=e,t.y=r,t}function e0(t){var i,a,s,e,r,h,n,o,l,_,f,c,u;return t.x-=this.x0,t.y-=this.y0,f=Math.PI*this.R,s=t.x/f,e=t.y/f,r=s*s+e*e,h=-Math.abs(e)*(1+r),n=h-2*e*e+s*s,o=-2*h+1+2*e*e+r*r,u=e*e/o+(2*n*n*n/o/o/o-9*h*n/o/o)/27,l=(h-n*n/3/o)/o,_=2*Math.sqrt(-l/3),f=3*u/l/_,Math.abs(f)>1&&(f>=0?f=1:f=-1),c=Math.acos(f)/3,t.y>=0?a=(-_*Math.cos(c+Math.PI/3)-n/3/o)*Math.PI:a=-(-_*Math.cos(c+Math.PI/3)-n/3/o)*Math.PI,Math.abs(s)<v?i=this.long0:i=y(this.long0+Math.PI*(r-1+Math.sqrt(1+2*(s*s-e*e)+r*r))/2/s),t.x=i,t.y=a,t}var r0=["Van_der_Grinten_I","VanDerGrinten","Van_der_Grinten","vandg"];const n0={init:a0,forward:s0,inverse:e0,names:r0};function h0(t,i,a,s,e,r){const h=s-i,n=Math.atan((1-r)*Math.tan(t)),o=Math.atan((1-r)*Math.tan(a)),l=Math.sin(n),_=Math.cos(n),f=Math.sin(o),c=Math.cos(o);let u=h,g,d=100,M,S,E,G,P,w,x,C,T,N,J,j,st,et;do{if(M=Math.sin(u),S=Math.cos(u),E=Math.sqrt(c*M*(c*M)+(_*f-l*c*S)*(_*f-l*c*S)),E===0)return{azi1:0,s12:0};G=l*f+_*c*S,P=Math.atan2(E,G),w=_*c*M/E,x=1-w*w,C=x!==0?G-2*l*f/x:0,T=r/16*x*(4+r*(4-3*x)),g=u,u=h+(1-T)*r*w*(P+T*E*(C+T*G*(-1+2*C*C)))}while(Math.abs(u-g)>1e-12&&--d>0);return d===0?{azi1:NaN,s12:NaN}:(N=x*(e*e-e*(1-r)*(e*(1-r)))/(e*(1-r)*(e*(1-r))),J=1+N/16384*(4096+N*(-768+N*(320-175*N))),j=N/1024*(256+N*(-128+N*(74-47*N))),st=j*E*(C+j/4*(G*(-1+2*C*C)-j/6*C*(-3+4*E*E)*(-3+4*C*C))),et=e*(1-r)*J*(P-st),{azi1:Math.atan2(c*M,_*f-l*c*S),s12:et})}function o0(t,i,a,s,e,r){const h=Math.atan((1-r)*Math.tan(t)),n=Math.sin(h),o=Math.cos(h),l=Math.sin(a),_=Math.cos(a),f=Math.atan2(n,o*_),c=o*l,u=1-c*c,g=u*(e*e-e*(1-r)*(e*(1-r)))/(e*(1-r)*(e*(1-r))),d=1+g/16384*(4096+g*(-768+g*(320-175*g))),M=g/1024*(256+g*(-128+g*(74-47*g)));let S=s/(e*(1-r)*d),E,G=100,P,w,x,C;do P=Math.cos(2*f+S),w=Math.sin(S),x=Math.cos(S),C=M*w*(P+M/4*(x*(-1+2*P*P)-M/6*P*(-3+4*w*w)*(-3+4*P*P))),E=S,S=s/(e*(1-r)*d)+C;while(Math.abs(S-E)>1e-12&&--G>0);if(G===0)return{lat2:NaN,lon2:NaN};const T=n*w-o*x*_,N=Math.atan2(n*x+o*w*_,(1-r)*Math.sqrt(c*c+T*T)),J=Math.atan2(w*l,o*x-n*w*_),j=r/16*u*(4+r*(4-3*u)),st=J-(1-j)*r*c*(S+j*w*(P+j*x*(-1+2*P*P))),et=i+st;return{lat2:N,lon2:et}}function l0(){this.sin_p12=Math.sin(this.lat0),this.cos_p12=Math.cos(this.lat0),this.f=this.es/(1+Math.sqrt(1-this.es))}function c0(t){var i=t.x,a=t.y,s=Math.sin(t.y),e=Math.cos(t.y),r=y(i-this.long0),h,n,o,l,_,f,c,u,g,d,M;return this.sphere?Math.abs(this.sin_p12-1)<=v?(t.x=this.x0+this.a*(m-a)*Math.sin(r),t.y=this.y0-this.a*(m-a)*Math.cos(r),t):Math.abs(this.sin_p12+1)<=v?(t.x=this.x0+this.a*(m+a)*Math.sin(r),t.y=this.y0+this.a*(m+a)*Math.cos(r),t):(g=this.sin_p12*s+this.cos_p12*e*Math.cos(r),c=Math.acos(g),u=c?c/Math.sin(c):1,t.x=this.x0+this.a*u*e*Math.sin(r),t.y=this.y0+this.a*u*(this.cos_p12*s-this.sin_p12*e*Math.cos(r)),t):(h=Ct(this.es),n=At(this.es),o=Nt(this.es),l=Rt(this.es),Math.abs(this.sin_p12-1)<=v?(_=this.a*k(h,n,o,l,m),f=this.a*k(h,n,o,l,a),t.x=this.x0+(_-f)*Math.sin(r),t.y=this.y0-(_-f)*Math.cos(r),t):Math.abs(this.sin_p12+1)<=v?(_=this.a*k(h,n,o,l,m),f=this.a*k(h,n,o,l,a),t.x=this.x0+(_+f)*Math.sin(r),t.y=this.y0+(_+f)*Math.cos(r),t):Math.abs(i)<v&&Math.abs(a-this.lat0)<v?(t.x=t.y=0,t):(d=h0(this.lat0,this.long0,a,i,this.a,this.f),M=d.azi1,t.x=d.s12*Math.sin(M),t.y=d.s12*Math.cos(M),t))}function f0(t){t.x-=this.x0,t.y-=this.y0;var i,a,s,e,r,h,n,o,l,_,f,c,u,g,d,M;return this.sphere?(i=Math.sqrt(t.x*t.x+t.y*t.y),i>2*m*this.a?void 0:(a=i/this.a,s=Math.sin(a),e=Math.cos(a),r=this.long0,Math.abs(i)<=v?h=this.lat0:(h=Y(e*this.sin_p12+t.y*s*this.cos_p12/i),n=Math.abs(this.lat0)-m,Math.abs(n)<=v?this.lat0>=0?r=y(this.long0+Math.atan2(t.x,-t.y)):r=y(this.long0-Math.atan2(-t.x,t.y)):r=y(this.long0+Math.atan2(t.x*s,i*this.cos_p12*e-t.y*this.sin_p12*s))),t.x=r,t.y=h,t)):(o=Ct(this.es),l=At(this.es),_=Nt(this.es),f=Rt(this.es),Math.abs(this.sin_p12-1)<=v?(c=this.a*k(o,l,_,f,m),i=Math.sqrt(t.x*t.x+t.y*t.y),u=c-i,h=$t(u/this.a,o,l,_,f),r=y(this.long0+Math.atan2(t.x,-1*t.y)),t.x=r,t.y=h,t):Math.abs(this.sin_p12+1)<=v?(c=this.a*k(o,l,_,f,m),i=Math.sqrt(t.x*t.x+t.y*t.y),u=i-c,h=$t(u/this.a,o,l,_,f),r=y(this.long0+Math.atan2(t.x,t.y)),t.x=r,t.y=h,t):(g=Math.atan2(t.x,t.y),d=Math.sqrt(t.x*t.x+t.y*t.y),M=o0(this.lat0,this.long0,g,d,this.a,this.f),t.x=M.lon2,t.y=M.lat2,t))}var _0=["Azimuthal_Equidistant","aeqd"];const u0={init:l0,forward:c0,inverse:f0,names:_0};function g0(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0)}function m0(t){var i,a,s,e,r,h,n,o,l=t.x,_=t.y;return s=y(l-this.long0),i=Math.sin(_),a=Math.cos(_),e=Math.cos(s),h=this.sin_p14*i+this.cos_p14*a*e,r=1,(h>0||Math.abs(h)<=v)&&(n=this.a*r*a*Math.sin(s),o=this.y0+this.a*r*(this.cos_p14*i-this.sin_p14*a*e)),t.x=n,t.y=o,t}function d0(t){var i,a,s,e,r,h,n;return t.x-=this.x0,t.y-=this.y0,i=Math.sqrt(t.x*t.x+t.y*t.y),a=Y(i/this.a),s=Math.sin(a),e=Math.cos(a),h=this.long0,Math.abs(i)<=v?(n=this.lat0,t.x=h,t.y=n,t):(n=Y(e*this.sin_p14+t.y*s*this.cos_p14/i),r=Math.abs(this.lat0)-m,Math.abs(r)<=v?(this.lat0>=0?h=y(this.long0+Math.atan2(t.x,-t.y)):h=y(this.long0-Math.atan2(-t.x,t.y)),t.x=h,t.y=n,t):(h=y(this.long0+Math.atan2(t.x*s,i*this.cos_p14*e-t.y*this.sin_p14*s)),t.x=h,t.y=n,t))}var M0=["ortho"];const v0={init:g0,forward:m0,inverse:d0,names:M0};var A={FRONT:1,RIGHT:2,BACK:3,LEFT:4,TOP:5,BOTTOM:6},p={AREA_0:1,AREA_1:2,AREA_2:3,AREA_3:4};function y0(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Quadrilateralized Spherical Cube",this.lat0>=m-b/2?this.face=A.TOP:this.lat0<=-(m-b/2)?this.face=A.BOTTOM:Math.abs(this.long0)<=b?this.face=A.FRONT:Math.abs(this.long0)<=m+b?this.face=this.long0>0?A.RIGHT:A.LEFT:this.face=A.BACK,this.es!==0&&(this.one_minus_f=1-(this.a-this.b)/this.a,this.one_minus_f_squared=this.one_minus_f*this.one_minus_f)}function S0(t){var i={x:0,y:0},a,s,e,r,h,n,o={value:0};if(t.x-=this.long0,this.es!==0?a=Math.atan(this.one_minus_f_squared*Math.tan(t.y)):a=t.y,s=t.x,this.face===A.TOP)r=m-a,s>=b&&s<=m+b?(o.value=p.AREA_0,e=s-m):s>m+b||s<=-(m+b)?(o.value=p.AREA_1,e=s>0?s-I:s+I):s>-(m+b)&&s<=-b?(o.value=p.AREA_2,e=s+m):(o.value=p.AREA_3,e=s);else if(this.face===A.BOTTOM)r=m+a,s>=b&&s<=m+b?(o.value=p.AREA_0,e=-s+m):s<b&&s>=-b?(o.value=p.AREA_1,e=-s):s<-b&&s>=-(m+b)?(o.value=p.AREA_2,e=-s-m):(o.value=p.AREA_3,e=s>0?-s+I:-s-I);else{var l,_,f,c,u,g,d;this.face===A.RIGHT?s=ft(s,+m):this.face===A.BACK?s=ft(s,3.14159265359):this.face===A.LEFT&&(s=ft(s,-m)),c=Math.sin(a),u=Math.cos(a),g=Math.sin(s),d=Math.cos(s),l=u*d,_=u*g,f=c,this.face===A.FRONT?(r=Math.acos(l),e=Ot(r,f,_,o)):this.face===A.RIGHT?(r=Math.acos(_),e=Ot(r,f,-l,o)):this.face===A.BACK?(r=Math.acos(-l),e=Ot(r,f,-_,o)):this.face===A.LEFT?(r=Math.acos(-_),e=Ot(r,f,l,o)):(r=e=0,o.value=p.AREA_0)}return n=Math.atan(12/I*(e+Math.acos(Math.sin(e)*Math.cos(b))-m)),h=Math.sqrt((1-Math.cos(r))/(Math.cos(n)*Math.cos(n))/(1-Math.cos(Math.atan(1/Math.cos(e))))),o.value===p.AREA_1?n+=m:o.value===p.AREA_2?n+=I:o.value===p.AREA_3&&(n+=1.5*I),i.x=h*Math.cos(n),i.y=h*Math.sin(n),i.x=i.x*this.a+this.x0,i.y=i.y*this.a+this.y0,t.x=i.x,t.y=i.y,t}function E0(t){var i={lam:0,phi:0},a,s,e,r,h,n,o,l,_,f={value:0};if(t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,s=Math.atan(Math.sqrt(t.x*t.x+t.y*t.y)),a=Math.atan2(t.y,t.x),t.x>=0&&t.x>=Math.abs(t.y)?f.value=p.AREA_0:t.y>=0&&t.y>=Math.abs(t.x)?(f.value=p.AREA_1,a-=m):t.x<0&&-t.x>=Math.abs(t.y)?(f.value=p.AREA_2,a=a<0?a+I:a-I):(f.value=p.AREA_3,a+=m),_=I/12*Math.tan(a),h=Math.sin(_)/(Math.cos(_)-1/Math.sqrt(2)),n=Math.atan(h),e=Math.cos(a),r=Math.tan(s),o=1-e*e*r*r*(1-Math.cos(Math.atan(1/Math.cos(n)))),o<-1?o=-1:o>1&&(o=1),this.face===A.TOP)l=Math.acos(o),i.phi=m-l,f.value===p.AREA_0?i.lam=n+m:f.value===p.AREA_1?i.lam=n<0?n+I:n-I:f.value===p.AREA_2?i.lam=n-m:i.lam=n;else if(this.face===A.BOTTOM)l=Math.acos(o),i.phi=l-m,f.value===p.AREA_0?i.lam=-n+m:f.value===p.AREA_1?i.lam=-n:f.value===p.AREA_2?i.lam=-n-m:i.lam=n<0?-n-I:-n+I;else{var c,u,g;c=o,_=c*c,_>=1?g=0:g=Math.sqrt(1-_)*Math.sin(n),_+=g*g,_>=1?u=0:u=Math.sqrt(1-_),f.value===p.AREA_1?(_=u,u=-g,g=_):f.value===p.AREA_2?(u=-u,g=-g):f.value===p.AREA_3&&(_=u,u=g,g=-_),this.face===A.RIGHT?(_=c,c=-u,u=_):this.face===A.BACK?(c=-c,u=-u):this.face===A.LEFT&&(_=c,c=u,u=-_),i.phi=Math.acos(-g)-m,i.lam=Math.atan2(u,c),this.face===A.RIGHT?i.lam=ft(i.lam,-m):this.face===A.BACK?i.lam=ft(i.lam,-3.14159265359):this.face===A.LEFT&&(i.lam=ft(i.lam,+m))}if(this.es!==0){var d,M,S;d=i.phi<0?1:0,M=Math.tan(i.phi),S=this.b/Math.sqrt(M*M+this.one_minus_f_squared),i.phi=Math.atan(Math.sqrt(this.a*this.a-S*S)/(this.one_minus_f*S)),d&&(i.phi=-i.phi)}return i.lam+=this.long0,t.x=i.lam,t.y=i.phi,t}function Ot(t,i,a,s){var e;return t<v?(s.value=p.AREA_0,e=0):(e=Math.atan2(i,a),Math.abs(e)<=b?s.value=p.AREA_0:e>b&&e<=m+b?(s.value=p.AREA_1,e-=m):e>m+b||e<=-(m+b)?(s.value=p.AREA_2,e=e>=0?e-I:e+I):(s.value=p.AREA_3,e+=m)),e}function ft(t,i){var a=t+i;return a<-3.14159265359?a+=wt:a>3.14159265359&&(a-=wt),a}var G0=["Quadrilateralized Spherical Cube","Quadrilateralized_Spherical_Cube","qsc"];const w0={init:y0,forward:S0,inverse:E0,names:G0};var si=[[1,22199e-21,-715515e-10,31103e-10],[.9986,-482243e-9,-24897e-9,-13309e-10],[.9954,-83103e-8,-448605e-10,-986701e-12],[.99,-.00135364,-59661e-9,36777e-10],[.9822,-.00167442,-449547e-11,-572411e-11],[.973,-.00214868,-903571e-10,18736e-12],[.96,-.00305085,-900761e-10,164917e-11],[.9427,-.00382792,-653386e-10,-26154e-10],[.9216,-.00467746,-10457e-8,481243e-11],[.8962,-.00536223,-323831e-10,-543432e-11],[.8679,-.00609363,-113898e-9,332484e-11],[.835,-.00698325,-640253e-10,934959e-12],[.7986,-.00755338,-500009e-10,935324e-12],[.7597,-.00798324,-35971e-9,-227626e-11],[.7186,-.00851367,-701149e-10,-86303e-10],[.6732,-.00986209,-199569e-9,191974e-10],[.6213,-.010418,883923e-10,624051e-11],[.5722,-.00906601,182e-6,624051e-11],[.5322,-.00677797,275608e-9,624051e-11]],Mt=[[-520417e-23,.0124,121431e-23,-845284e-16],[.062,.0124,-126793e-14,422642e-15],[.124,.0124,507171e-14,-160604e-14],[.186,.0123999,-190189e-13,600152e-14],[.248,.0124002,710039e-13,-224e-10],[.31,.0123992,-264997e-12,835986e-13],[.372,.0124029,988983e-12,-311994e-12],[.434,.0123893,-369093e-11,-435621e-12],[.4958,.0123198,-102252e-10,-345523e-12],[.5571,.0121916,-154081e-10,-582288e-12],[.6176,.0119938,-241424e-10,-525327e-12],[.6769,.011713,-320223e-10,-516405e-12],[.7346,.0113541,-397684e-10,-609052e-12],[.7903,.0109107,-489042e-10,-104739e-11],[.8435,.0103431,-64615e-9,-140374e-14],[.8936,.00969686,-64636e-9,-8547e-9],[.9394,.00840947,-192841e-9,-42106e-10],[.9761,.00616527,-256e-6,-42106e-10],[1,.00328947,-319159e-9,-42106e-10]],ra=.8487,na=1.3523,ha=W/5,P0=1/ha,lt=18,Bt=function(t,i){return t[0]+i*(t[1]+i*(t[2]+i*t[3]))},x0=function(t,i){return t[1]+i*(2*t[2]+i*3*t[3])};function b0(t,i,a,s){for(var e=i;s;--s){var r=t(e);if(e-=r,Math.abs(r)<a)break}return e}function p0(){this.x0=this.x0||0,this.y0=this.y0||0,this.long0=this.long0||0,this.es=0,this.title=this.title||"Robinson"}function C0(t){var i=y(t.x-this.long0),a=Math.abs(t.y),s=Math.floor(a*ha);s<0?s=0:s>=lt&&(s=lt-1),a=W*(a-P0*s);var e={x:Bt(si[s],a)*i,y:Bt(Mt[s],a)};return t.y<0&&(e.y=-e.y),e.x=e.x*this.a*ra+this.x0,e.y=e.y*this.a*na+this.y0,e}function A0(t){var i={x:(t.x-this.x0)/(this.a*ra),y:Math.abs(t.y-this.y0)/(this.a*na)};if(i.y>=1)i.x/=si[lt][0],i.y=t.y<0?-m:m;else{var a=Math.floor(i.y*lt);for(a<0?a=0:a>=lt&&(a=lt-1);;)if(Mt[a][0]>i.y)--a;else if(Mt[a+1][0]<=i.y)++a;else break;var s=Mt[a],e=5*(i.y-s[0])/(Mt[a+1][0]-s[0]);e=b0(function(r){return(Bt(s,r)-i.y)/x0(s,r)},e,v,100),i.x/=Bt(si[a],e),i.y=(5*a+e)*R,t.y<0&&(i.y=-i.y)}return i.x=y(i.x+this.long0),i}var N0=["Robinson","robin"];const R0={init:p0,forward:C0,inverse:A0,names:N0};function I0(){this.name="geocent"}function T0(t){var i=Ji(t,this.es,this.a);return i}function O0(t){var i=Vi(t,this.es,this.a,this.b);return i}var L0=["Geocentric","geocentric","geocent","Geocent"];const k0={init:I0,forward:T0,inverse:O0,names:L0};var O={N_POLE:0,S_POLE:1,EQUIT:2,OBLIQ:3},gt={h:{def:1e5,num:!0},azi:{def:0,num:!0,degrees:!0},tilt:{def:0,num:!0,degrees:!0},long0:{def:0,num:!0},lat0:{def:0,num:!0}};function D0(){if(Object.keys(gt).forEach(function(a){if(typeof this[a]=="undefined")this[a]=gt[a].def;else{if(gt[a].num&&isNaN(this[a]))throw new Error("Invalid parameter value, must be numeric "+a+" = "+this[a]);gt[a].num&&(this[a]=parseFloat(this[a]))}gt[a].degrees&&(this[a]=this[a]*R)}.bind(this)),Math.abs(Math.abs(this.lat0)-m)<v?this.mode=this.lat0<0?O.S_POLE:O.N_POLE:Math.abs(this.lat0)<v?this.mode=O.EQUIT:(this.mode=O.OBLIQ,this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0)),this.pn1=this.h/this.a,this.pn1<=0||this.pn1>1e10)throw new Error("Invalid height");this.p=1+this.pn1,this.rp=1/this.p,this.h1=1/this.pn1,this.pfact=(this.p+1)*this.h1,this.es=0;var t=this.tilt,i=this.azi;this.cg=Math.cos(i),this.sg=Math.sin(i),this.cw=Math.cos(t),this.sw=Math.sin(t)}function q0(t){t.x-=this.long0;var i=Math.sin(t.y),a=Math.cos(t.y),s=Math.cos(t.x),e,r;switch(this.mode){case O.OBLIQ:r=this.sinph0*i+this.cosph0*a*s;break;case O.EQUIT:r=a*s;break;case O.S_POLE:r=-i;break;case O.N_POLE:r=i;break}switch(r=this.pn1/(this.p-r),e=r*a*Math.sin(t.x),this.mode){case O.OBLIQ:r*=this.cosph0*i-this.sinph0*a*s;break;case O.EQUIT:r*=i;break;case O.N_POLE:r*=-(a*s);break;case O.S_POLE:r*=a*s;break}var h,n;return h=r*this.cg+e*this.sg,n=1/(h*this.sw*this.h1+this.cw),e=(e*this.cg-r*this.sg)*this.cw*n,r=h*n,t.x=e*this.a,t.y=r*this.a,t}function F0(t){t.x/=this.a,t.y/=this.a;var i={x:t.x,y:t.y},a,s,e;e=1/(this.pn1-t.y*this.sw),a=this.pn1*t.x*e,s=this.pn1*t.y*this.cw*e,t.x=a*this.cg+s*this.sg,t.y=s*this.cg-a*this.sg;var r=q(t.x,t.y);if(Math.abs(r)<v)i.x=0,i.y=t.y;else{var h,n;switch(n=1-r*r*this.pfact,n=(this.p-Math.sqrt(n))/(this.pn1/r+r/this.pn1),h=Math.sqrt(1-n*n),this.mode){case O.OBLIQ:i.y=Math.asin(h*this.sinph0+t.y*n*this.cosph0/r),t.y=(h-this.sinph0*Math.sin(i.y))*r,t.x*=n*this.cosph0;break;case O.EQUIT:i.y=Math.asin(t.y*n/r),t.y=h*r,t.x*=n;break;case O.N_POLE:i.y=Math.asin(h),t.y=-t.y;break;case O.S_POLE:i.y=-Math.asin(h);break}i.x=Math.atan2(t.x,t.y)}return t.x=i.x+this.long0,t.y=i.y,t}var j0=["Tilted_Perspective","tpers"];const U0={init:D0,forward:q0,inverse:F0,names:j0};function $0(){if(this.flip_axis=this.sweep==="x"?1:0,this.h=Number(this.h),this.radius_g_1=this.h/this.a,this.radius_g_1<=0||this.radius_g_1>1e10)throw new Error;if(this.radius_g=1+this.radius_g_1,this.C=this.radius_g*this.radius_g-1,this.es!==0){var t=1-this.es,i=1/t;this.radius_p=Math.sqrt(t),this.radius_p2=t,this.radius_p_inv2=i,this.shape="ellipse"}else this.radius_p=1,this.radius_p2=1,this.radius_p_inv2=1,this.shape="sphere";this.title||(this.title="Geostationary Satellite View")}function B0(t){var i=t.x,a=t.y,s,e,r,h;if(i=i-this.long0,this.shape==="ellipse"){a=Math.atan(this.radius_p2*Math.tan(a));var n=this.radius_p/q(this.radius_p*Math.cos(a),Math.sin(a));if(e=n*Math.cos(i)*Math.cos(a),r=n*Math.sin(i)*Math.cos(a),h=n*Math.sin(a),(this.radius_g-e)*e-r*r-h*h*this.radius_p_inv2<0)return t.x=Number.NaN,t.y=Number.NaN,t;s=this.radius_g-e,this.flip_axis?(t.x=this.radius_g_1*Math.atan(r/q(h,s)),t.y=this.radius_g_1*Math.atan(h/s)):(t.x=this.radius_g_1*Math.atan(r/s),t.y=this.radius_g_1*Math.atan(h/q(r,s)))}else this.shape==="sphere"&&(s=Math.cos(a),e=Math.cos(i)*s,r=Math.sin(i)*s,h=Math.sin(a),s=this.radius_g-e,this.flip_axis?(t.x=this.radius_g_1*Math.atan(r/q(h,s)),t.y=this.radius_g_1*Math.atan(h/s)):(t.x=this.radius_g_1*Math.atan(r/s),t.y=this.radius_g_1*Math.atan(h/q(r,s))));return t.x=t.x*this.a,t.y=t.y*this.a,t}function z0(t){var i=-1,a=0,s=0,e,r,h,n;if(t.x=t.x/this.a,t.y=t.y/this.a,this.shape==="ellipse"){this.flip_axis?(s=Math.tan(t.y/this.radius_g_1),a=Math.tan(t.x/this.radius_g_1)*q(1,s)):(a=Math.tan(t.x/this.radius_g_1),s=Math.tan(t.y/this.radius_g_1)*q(1,a));var o=s/this.radius_p;if(e=a*a+o*o+i*i,r=2*this.radius_g*i,h=r*r-4*e*this.C,h<0)return t.x=Number.NaN,t.y=Number.NaN,t;n=(-r-Math.sqrt(h))/(2*e),i=this.radius_g+n*i,a*=n,s*=n,t.x=Math.atan2(a,i),t.y=Math.atan(s*Math.cos(t.x)/i),t.y=Math.atan(this.radius_p_inv2*Math.tan(t.y))}else if(this.shape==="sphere"){if(this.flip_axis?(s=Math.tan(t.y/this.radius_g_1),a=Math.tan(t.x/this.radius_g_1)*Math.sqrt(1+s*s)):(a=Math.tan(t.x/this.radius_g_1),s=Math.tan(t.y/this.radius_g_1)*Math.sqrt(1+a*a)),e=a*a+s*s+i*i,r=2*this.radius_g*i,h=r*r-4*e*this.C,h<0)return t.x=Number.NaN,t.y=Number.NaN,t;n=(-r-Math.sqrt(h))/(2*e),i=this.radius_g+n*i,a*=n,s*=n,t.x=Math.atan2(a,i),t.y=Math.atan(s*Math.cos(t.x)/i)}return t.x=t.x+this.long0,t}var W0=["Geostationary Satellite View","Geostationary_Satellite","geos"];const H0={init:$0,forward:B0,inverse:z0,names:W0};var yt=1.340264,St=-.081106,Et=893e-6,Gt=.003796,zt=Math.sqrt(3)/2;function Q0(){this.es=0,this.long0=this.long0!==void 0?this.long0:0}function J0(t){var i=y(t.x-this.long0),a=t.y,s=Math.asin(zt*Math.sin(a)),e=s*s,r=e*e*e;return t.x=i*Math.cos(s)/(zt*(yt+3*St*e+r*(7*Et+9*Gt*e))),t.y=s*(yt+St*e+r*(Et+Gt*e)),t.x=this.a*t.x+this.x0,t.y=this.a*t.y+this.y0,t}function V0(t){t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a;var i=1e-9,a=12,s=t.y,e,r,h,n,o,l;for(l=0;l<a&&(e=s*s,r=e*e*e,h=s*(yt+St*e+r*(Et+Gt*e))-t.y,n=yt+3*St*e+r*(7*Et+9*Gt*e),s-=o=h/n,!(Math.abs(o)<i));++l);return e=s*s,r=e*e*e,t.x=zt*t.x*(yt+3*St*e+r*(7*Et+9*Gt*e))/Math.cos(s),t.y=Math.asin(Math.sin(s)/zt),t.x=y(t.x+this.long0),t}var K0=["eqearth","Equal Earth","Equal_Earth"];const X0={init:Q0,forward:J0,inverse:V0,names:K0};var bt=1e-10;function Y0(){var t;if(this.phi1=this.lat1,Math.abs(this.phi1)<bt)throw new Error;this.es?(this.en=hi(this.es),this.m1=ut(this.phi1,this.am1=Math.sin(this.phi1),t=Math.cos(this.phi1),this.en),this.am1=t/(Math.sqrt(1-this.es*this.am1*this.am1)*this.am1),this.inverse=tn,this.forward=Z0):(Math.abs(this.phi1)+bt>=m?this.cphi1=0:this.cphi1=1/Math.tan(this.phi1),this.inverse=sn,this.forward=an)}function Z0(t){var i=y(t.x-(this.long0||0)),a=t.y,s,e,r;return s=this.am1+this.m1-ut(a,e=Math.sin(a),r=Math.cos(a),this.en),e=r*i/(s*Math.sqrt(1-this.es*e*e)),t.x=s*Math.sin(e),t.y=this.am1-s*Math.cos(e),t.x=this.a*t.x+(this.x0||0),t.y=this.a*t.y+(this.y0||0),t}function tn(t){t.x=(t.x-(this.x0||0))/this.a,t.y=(t.y-(this.y0||0))/this.a;var i,a,s,e;if(a=q(t.x,t.y=this.am1-t.y),e=oi(this.am1+this.m1-a,this.es,this.en),(i=Math.abs(e))<m)i=Math.sin(e),s=a*Math.atan2(t.x,t.y)*Math.sqrt(1-this.es*i*i)/Math.cos(e);else if(Math.abs(i-m)<=bt)s=0;else throw new Error;return t.x=y(s+(this.long0||0)),t.y=Z(e),t}function an(t){var i=y(t.x-(this.long0||0)),a=t.y,s,e;return e=this.cphi1+this.phi1-a,Math.abs(e)>bt?(t.x=e*Math.sin(s=i*Math.cos(a)/e),t.y=this.cphi1-e*Math.cos(s)):t.x=t.y=0,t.x=this.a*t.x+(this.x0||0),t.y=this.a*t.y+(this.y0||0),t}function sn(t){t.x=(t.x-(this.x0||0))/this.a,t.y=(t.y-(this.y0||0))/this.a;var i,a,s=q(t.x,t.y=this.cphi1-t.y);if(a=this.cphi1+this.phi1-s,Math.abs(a)>m)throw new Error;return Math.abs(Math.abs(a)-m)<=bt?i=0:i=s*Math.atan2(t.x,t.y)/Math.cos(a),t.x=y(i+(this.long0||0)),t.y=Z(a),t}var en=["bonne","Bonne (Werner lat_1=90)"];const rn={init:Y0,names:en};function nn(t){t.Proj.projections.add(kt),t.Proj.projections.add(Dt),t.Proj.projections.add(se),t.Proj.projections.add(_e),t.Proj.projections.add(Me),t.Proj.projections.add(Ge),t.Proj.projections.add(Ce),t.Proj.projections.add(Te),t.Proj.projections.add(qe),t.Proj.projections.add(Be),t.Proj.projections.add(ar),t.Proj.projections.add(or),t.Proj.projections.add(ur),t.Proj.projections.add(yr),t.Proj.projections.add(Pr),t.Proj.projections.add(Ar),t.Proj.projections.add(Or),t.Proj.projections.add(Fr),t.Proj.projections.add(Wr),t.Proj.projections.add(Kr),t.Proj.projections.add(i0),t.Proj.projections.add(n0),t.Proj.projections.add(u0),t.Proj.projections.add(v0),t.Proj.projections.add(w0),t.Proj.projections.add(R0),t.Proj.projections.add(k0),t.Proj.projections.add(U0),t.Proj.projections.add(H0),t.Proj.projections.add(X0),t.Proj.projections.add(rn)}const oa=Object.assign(Ss,{defaultDatum:"WGS84",Proj:z,WGS84:new z("WGS84"),Point:_t,toPoint:Ki,defs:L,nadgrid:as,transform:Ut,mgrs:Es,version:"__VERSION__"});nn(oa);oa.defs([["EPSG:4326","+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees"],["CRS:84","+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees"],["EPSG:4269","+title=NAD83 (long/lat) +proj=longlat +a=6378137.0 +b=6356752.31414036 +ellps=GRS80 +datum=NAD83 +units=degrees"],["EPSG:102100",'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["EPSG","102100"]]'],["EPSG:3395","+proj=merc +lon_0=0 +k=1 +x_0=0 +y_0=0 +ellps=WGS84 +datum=WGS84 +units=m +no_defs"],["EPSG:4610","+proj=longlat +a=6378140 +b=6356755.288157528 +no_defs"],["EPSG:4490","+proj=longlat +ellps=GRS80 +no_defs"],["EPSG:4491","+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=13500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4492","+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=14500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4493","+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=15500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4494","+proj=tmerc +lat_0=0 +lon_0=93 +k=1 +x_0=16500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4495","+proj=tmerc +lat_0=0 +lon_0=99 +k=1 +x_0=17500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4496","+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=18500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4497","+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=19500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4498","+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=20500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4499","+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=21500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4500","+proj=tmerc +lat_0=0 +lon_0=129 +k=1 +x_0=22500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4501","+proj=tmerc +lat_0=0 +lon_0=135 +k=1 +x_0=23500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4502","+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4503","+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4504","+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4505","+proj=tmerc +lat_0=0 +lon_0=93 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4506","+proj=tmerc +lat_0=0 +lon_0=99 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4507","+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4508","+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4509","+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4510","+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4511","+proj=tmerc +lat_0=0 +lon_0=129 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4512","+proj=tmerc +lat_0=0 +lon_0=135 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4513","+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=25500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4514","+proj=tmerc +lat_0=0 +lon_0=78 +k=1 +x_0=26500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4515","+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=27500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4516","+proj=tmerc +lat_0=0 +lon_0=84 +k=1 +x_0=28500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4517","+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=29500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4518","+proj=tmerc +lat_0=0 +lon_0=90 +k=1 +x_0=30500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4519","+proj=tmerc +lat_0=0 +lon_0=93 +k=1 +x_0=31500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4520","+proj=tmerc +lat_0=0 +lon_0=96 +k=1 +x_0=32500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4521","+proj=tmerc +lat_0=0 +lon_0=99 +k=1 +x_0=33500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4522","+proj=tmerc +lat_0=0 +lon_0=102 +k=1 +x_0=34500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4523","+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=35500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4524","+proj=tmerc +lat_0=0 +lon_0=108 +k=1 +x_0=36500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4525","+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=37500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4526","+proj=tmerc +lat_0=0 +lon_0=114 +k=1 +x_0=38500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4527","+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=39500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4528","+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=40500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4529","+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=41500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4530","+proj=tmerc +lat_0=0 +lon_0=126 +k=1 +x_0=42500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4531","+proj=tmerc +lat_0=0 +lon_0=129 +k=1 +x_0=43500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4532","+proj=tmerc +lat_0=0 +lon_0=132 +k=1 +x_0=44500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4533","+proj=tmerc +lat_0=0 +lon_0=135 +k=1 +x_0=45500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4534","+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4535","+proj=tmerc +lat_0=0 +lon_0=78 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4536","+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4537","+proj=tmerc +lat_0=0 +lon_0=84 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4538","+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4539","+proj=tmerc +lat_0=0 +lon_0=90 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4540","+proj=tmerc +lat_0=0 +lon_0=93 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4541","+proj=tmerc +lat_0=0 +lon_0=96 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4542","+proj=tmerc +lat_0=0 +lon_0=99 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4543","+proj=tmerc +lat_0=0 +lon_0=102 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4544","+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4545","+proj=tmerc +lat_0=0 +lon_0=108 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4546","+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4547","+proj=tmerc +lat_0=0 +lon_0=114 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4548","+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4549","+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4550","+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4551","+proj=tmerc +lat_0=0 +lon_0=126 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4552","+proj=tmerc +lat_0=0 +lon_0=129 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4553","+proj=tmerc +lat_0=0 +lon_0=132 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"],["EPSG:4554","+proj=tmerc +lat_0=0 +lon_0=135 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"]]);const cn={pointStyle:{color:Cesium.Color.RED,pixelSize:10,outlineColor:Cesium.Color.BLACK,outlineWidth:0,show:!0,clampToGround:!0},markerStyle:{image:"",scale:1,verticalOrigin:Cesium.VerticalOrigin.BOTTOM,horizontalOrigin:Cesium.HorizontalOrigin.CENTER,eyeOffset:new Cesium.Cartesian3(0,.05,0),clampToGround:!0,distanceDisplayCondition:new Cesium.DistanceDisplayCondition(10,1e5)},rectangleStyle:{clampToGround:!0,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:20,verticalOrigin:Cesium.VerticalOrigin.TOP,horizontalOrigin:Cesium.HorizontalOrigin.LEFT},circleStyle:{clampToGround:!0,heightReference:Cesium.HeightReference.RELATIVE_TO_GROUND,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:20,verticalOrigin:Cesium.VerticalOrigin.TOP,horizontalOrigin:Cesium.HorizontalOrigin.LEFT,pixelOffset:new Cesium.Cartesian2(0,-50)},polygonStyle:{clampToGround:!0,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:20,verticalOrigin:Cesium.VerticalOrigin.TOP,horizontalOrigin:Cesium.HorizontalOrigin.LEFT,pixelOffset:new Cesium.Cartesian2(0,-50)},polylineStyle:{clampToGround:!0,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:20,verticalOrigin:Cesium.VerticalOrigin.Button,horizontalOrigin:Cesium.HorizontalOrigin.LEFT,pixelOffset:new Cesium.Cartesian2(0,-50)},textStyle:{text:"名称",disableDepthTestDistance:Number.POSITIVE_INFINITY,pixelOffset:new Cesium.Cartesian2(0,-50),eyeOffset:new Cesium.Cartesian3(0,.01,0),distanceDisplayCondition:new Cesium.DistanceDisplayCondition(10,1e5)},modelStyle:{uri:"/assets/models/Cesium_Man.glb",scale:1e3}};function hn(t){t&&(this._viewer=t,this._installBaiduImageryProvider(),this._installGooGleImageryProvider(),this._installAmapImageryProvider(),this._installTencentImageryProvider(),this._installTdtImageryProvider())}hn.prototype={setView:function(t){if(this._viewer&&t&&t.position){if(t.distance){var i=new Cesium.Cartesian3(0,t.distance,opt.distance);t.position=Cesium.Cartesian3.add(t.position,i,new Cesium.Cartesian3)}this._viewer.scene.camera.setView({destination:t.position,orientation:t.orientation||{heading:Cesium.Math.toRadians(90),pitch:Cesium.Math.toRadians(90),roll:Cesium.Math.toRadians(0)}})}},flyTo:function(t){if(this._viewer&&t&&t.position){if(t.distance){var i=new Cesium.Cartesian3(0,t.distance,t.distance);t.position=Cesium.Cartesian3.add(t.position,i,new Cesium.Cartesian3)}this._viewer.scene.camera.flyTo({destination:t.position,orientation:t.orientation||{heading:Cesium.Math.toRadians(90),pitch:Cesium.Math.toRadians(90),roll:Cesium.Math.toRadians(0)},easingFunction:t.easingFunction||Cesium.EasingFunction.LINEAR_NONE,duration:t.duration||3,complete:t.callback})}},transformCartesianToWGS84:function(t){if(this._viewer&&t){var i=Cesium.Ellipsoid.WGS84,a=i.cartesianToCartographic(t);return{lng:Cesium.Math.toDegrees(a.longitude),lat:Cesium.Math.toDegrees(a.latitude),alt:a.height}}},transformWGS84ArrayToCartesianArray:function(t,i){if(this._viewer&&t){var a=this;return t?t.map(function(s){return a.transformWGS84ToCartesian(s,i)}):[]}},transformWGS84ToCartesian:function(t,i){if(this._viewer)return t?Cesium.Cartesian3.fromDegrees(t.lng||t.lon,t.lat,t.alt=i||t.alt,Cesium.Ellipsoid.WGS84):Cesium.Cartesian3.ZERO},transformCartesianArrayToWGS84Array:function(t){if(this._viewer){var i=this;return t?t.map(function(a){return i.transformCartesianToWGS84(a)}):[]}},setCameraEotateHeading(t){if(t){let i=this._viewer,a=Cesium.Cartesian3.fromDegrees(t.lng,t.lat,t.height),s=Cesium.Math.toRadians(-30),e=360/30,r=5e3,h=Cesium.JulianDate.fromDate(new Date);i.clock.startTime=h.clone(),i.clock.currentTime=h.clone(),i.clock.clockRange=Cesium.ClockRange.CLAMPED,i.clock.clockStep=Cesium.ClockStep.SYSTEM_CLOCK;let n=i.camera.heading,o=function(){let _=Cesium.JulianDate.secondsDifference(i.clock.currentTime,i.clock.startTime),f=Cesium.Math.toRadians(_*e)+n;i.scene.camera.setView({destination:a,orientation:{heading:f,pitch:s}}),i.scene.camera.moveBackward(r),Cesium.JulianDate.compare(i.clock.currentTime,i.clock.stopTime)>=0&&i.clock.onTick.removeEventListener(o)};i.clock.onTick.addEventListener(o)}},transformWGS84ToCartographic:function(t){return t?Cesium.Cartographic.fromDegrees(t.lng||t.lon,t.lat,t.alt):Cesium.Cartographic.ZERO},getCatesian3FromPX:function(t){let i=this._viewer;if(i&&t){var a=i.scene.drillPick(t),s=null,e=!1,r=!1;for(let o in a){let l=a[o];if((l&&l.primitive instanceof Cesium.Cesium3DTileFeature||l&&l.primitive instanceof Cesium.Cesium3DTileset||l&&l.primitive instanceof Cesium.Model)&&(e=!0),e&&(i.scene.pick(t),s=i.scene.pickPosition(t),s)){let _=Cesium.Cartographic.fromCartesian(s);_.height<0&&(_.height=0);let f=Cesium.Math.toDegrees(_.longitude),c=Cesium.Math.toDegrees(_.latitude),u=_.height;s=this.transformWGS84ToCartesian({lng:f,lat:c,alt:u})}}let n=i.terrainProvider instanceof Cesium.EllipsoidTerrainProvider;if(!e&&!n){var h=i.scene.camera.getPickRay(t);if(!h)return null;s=i.scene.globe.pick(h,i.scene),r=!0}if(!e&&!r&&n&&(s=i.scene.camera.pickEllipsoid(t,i.scene.globe.ellipsoid)),s){let o=this.transformCartesianToWGS84(s);return o.alt<0&&(s=this.transformWGS84ToCartesian(o,.1)),s}return!1}},getCameraPosition:function(){if(this._viewer){var t=this._viewer.scene.camera.pickEllipsoid(new Cesium.Cartesian2(this._viewer.canvas.clientWidth/2,this._viewer.canvas.clientHeight/2));if(t){var i=Cesium.Ellipsoid.WGS84.cartesianToCartographic(t),a=i.longitude*180/Math.PI,s=i.latitude*180/Math.PI,e=this._viewer.camera._direction,r=Cesium.Math.toDegrees(e.x),h=Cesium.Math.toDegrees(e.y),n=Cesium.Math.toDegrees(e.z),o=this._viewer.camera.positionCartographic.height,l=Cesium.Math.toDegrees(this._viewer.camera.heading),_=Cesium.Math.toDegrees(this._viewer.camera.pitch),f=Cesium.Math.toDegrees(this._viewer.camera.roll),c=this._viewer.camera.computeViewRectangle(),u=c.west/Math.PI*180,g=c.north/Math.PI*180,d=c.east/Math.PI*180,M=c.south/Math.PI*180,S=(u+d)/2,E=(g+M)/2;return{lon:a,lat:s,height:o,heading:l,pitch:_,roll:f,position:this._viewer.camera.position,center:{x:S,y:E},direction:new Cesium.Cartesian3(r,h,n)}}}},updateCameraState:function(t){this._viewer.scene._screenSpaceCameraController.enableRotate=t,this._viewer.scene._screenSpaceCameraController.enableTranslate=t,this._viewer.scene._screenSpaceCameraController.enableZoom=t,this._viewer.scene._screenSpaceCameraController.enableTilt=t,this._viewer.scene._screenSpaceCameraController.enableLook=t},bindHandelEvent:function(t,i,a){if(this._viewer){var s=new Cesium.ScreenSpaceEventHandler(this._viewer.canvas);s.setInputAction(function(e){t&&t(e,s)},Cesium.ScreenSpaceEventType.LEFT_CLICK),s.setInputAction(function(e){i&&i(e,s)},Cesium.ScreenSpaceEventType.MOUSE_MOVE),s.setInputAction(function(e){a&&a(e,s)},Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)}},getHandelPosition:function(t){if(this._viewer){var i=new Cesium.ScreenSpaceEventHandler(this._viewer.scene.canvas),a=this;i.setInputAction(function(s){var e=a._viewer.scene.camera.pickEllipsoid(s.endPosition,a._viewer.scene.globe.ellipsoid);typeof t=="function"&&t(a.transformCartesianToWGS84(e),i)},Cesium.ScreenSpaceEventType.MOUSE_MOVE)}},saveSceneImages:function(){if(this._viewer){let r=function(h){for(var n=h.split(","),o=n[0].match(/:(.*?);/)[1],l=atob(n[1]),_=l.length,f=new Uint8Array(_);_--;)f[_]=l.charCodeAt(_);return new Blob([f],{type:o})};var t=this._viewer.scene.canvas,i=t.toDataURL("image/png").replace("image/png","image/octet-stream"),a=document.createElement("a");i.substr(22,i.length);var s=r(i),e=URL.createObjectURL(s);a.download="scene.png",a.href=e,a.click()}},_installAmapImageryProvider:function(){},_installTdtImageryProvider:function(){},_installTencentImageryProvider:function(){},_installGooGleImageryProvider:function(){},_installBaiduImageryProvider:function(){var t="http://api{s}.map.bdimg.com/customimage/tile?&x={x}&y={y}&z={z}&scale=1&customid={style}";function i(a){t=a.temp_url||t,this._url=t,this._tileWidth=256,this._tileHeight=256,this._maximumLevel=18,this._minimumLevel=1,this._tilingScheme=new Cesium.WebMercatorTilingScheme({rectangleSouthwestInMeters:new Cesium.Cartesian2(-33554054,-33746824),rectangleNortheastInMeters:new Cesium.Cartesian2(33554054,33746824)}),this._rectangle=this._tilingScheme.rectangle,this._credit=void 0,this._style=a.style||"normal"}Object.defineProperties(i.prototype,{url:{get:function(){return this._url}},token:{get:function(){return this._token}},tileWidth:{get:function(){if(!this.ready)throw new Cesium.DeveloperError("tileWidth must not be called before the imagery provider is ready.");return this._tileWidth}},tileHeight:{get:function(){if(!this.ready)throw new Cesium.DeveloperError("tileHeight must not be called before the imagery provider is ready.");return this._tileHeight}},maximumLevel:{get:function(){if(!this.ready)throw new Cesium.DeveloperError("tileHeight must not be called before the imagery provider is ready.");return this._tileHeight}},minimumLevel:{get:function(){if(!this.ready)throw new Cesium.DeveloperError("minimumLevel must not be called before the imagery provider is ready.");return 0}},tilingScheme:{get:function(){if(!this.ready)throw new Cesium.DeveloperError("tilingScheme must not be called before the imagery provider is ready.");return this._tilingScheme}},rectangle:{get:function(){if(!this.ready)throw new Cesium.DeveloperError("rectangle must not be called before the imagery provider is ready.");return this._rectangle}},ready:{get:function(){return!!this._url}},credit:{get:function(){return this._credit}}}),i.prototype.getTileCredits=function(a,s,e){},i.prototype.requestImage=function(a,s,e){if(!this.ready)throw new Cesium.DeveloperError("requestImage must not be called before the imagery provider is ready.");var r=this._tilingScheme.getNumberOfXTilesAtLevel(e),h=this._tilingScheme.getNumberOfYTilesAtLevel(e),n=this._url.replace("{x}",a-r/2).replace("{y}",h/2-s-1).replace("{z}",e).replace("{s}",1).replace("{style}",this._style);return Cesium.ImageryProvider.loadImage(this,n)}}};export{hn as B,cn as S,oa as p};
