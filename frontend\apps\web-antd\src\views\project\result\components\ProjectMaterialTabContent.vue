<template>
  <div class="upload-section-row">
    <a-breadcrumb style="margin-bottom: 10px;">
      <a-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.id">
        <a @click="handleBreadcrumbClick(item, index)">{{ item.name }}</a>
      </a-breadcrumb-item>
    </a-breadcrumb>
    <div class="upload-actions">
      <VbenButton type="primary" @click="openUploadModal">上传</VbenButton>
      <a-button danger ghost @click="handleBatchDelete" style="margin-left: 10px;height: 37.5px;">批量删除</a-button>
    </div>
  </div>



  <Grid>
    <template #appendixNameSlot="{ row }">
      <div style="display: flex; align-items: center;">
        <FolderOutlined v-if="row.appendixType ===1" style="margin-right: 5px;margin-left: 5px;" @click="row.appendixType === 1 && handleFolderClick(row)" />
        <FileOutlined v-else style="margin-right: 5px;margin-left: 5px;" />
        <span :style="{ cursor: row.appendixType === 1 ? 'pointer' : 'default' }"
              :class="row.appendixType === 1 ? 'folder-name' : ''"
              @click="row.appendixType === 1 && handleFolderClick(row)">{{ row.appendixName }}</span>
      </div>
    </template>
    <template #action="{ row }">
      <div class="actionBar">
        <a class="actionButton" @click.stop="() => handleDownload(row)">下载</a>
        <a class="actionButton" @click.stop="() => handleDeleteFile(row)">删除</a>
        <a class="actionButton" v-if="row.appendixType ===2 && row.previewable" @click.stop="() => handlePreview(row)">预览</a>
      </div>
    </template>
  </Grid>

  <UploadModal
    :projectId="row.id"
    :appendixCategory="getAppendixCategory()"
    :parentAppendixId="currentParentAppendixId"
    @success="onUploadSuccess"
  />
</template>

<script lang="ts" setup>
import { ref, watch, defineProps, defineEmits } from 'vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { listMaterial, previewMaterial } from '#/views/project/project.api';
import { useVbenModal, VbenButton } from '@vben/common-ui';
import ProjectUploadModal from './ProjectUploadModal.vue';
import { showConform } from '#/utils/alert.js';
import { deleteBatchMaterial, downloadMaterial } from '#/views/project/project.api';
import { FolderOutlined, FileOutlined } from '@ant-design/icons-vue';
import { Breadcrumb as ABreadcrumb, BreadcrumbItem as ABreadcrumbItem } from 'ant-design-vue'; // 引入面包屑组件

const props = defineProps({
  activeTab: { type: String, required: true },
  row: { type: Object, default: () => ({}) },
  initialState: { type: Object, default: () => ({
    currentParentAppendixId: 0,
    breadcrumbs: [{ id: 0, name: '根目录' }]
  }) }
});

const emit = defineEmits(['reloadGrid', 'stateChange']);

const currentParentAppendixId = ref(props.initialState.currentParentAppendixId);
const breadcrumbs = ref(props.initialState.breadcrumbs);

const columns = [
  {
    type: 'checkbox',
    width: 50,
  },
  {
    title: '文件名',
    field: 'appendixName',
    width: 250,
    slots: { default: 'appendixNameSlot' }, // 添加 slot
  },
  {
    title: '文件格式',
    field: 'appendixFormat',
  },
  {
    title: '文件大小',
    field: 'appendixSize',
    formatter: ({ cellValue }) => formatFileSize(cellValue),
  },
  {
    title: '上传日期',
    field: 'timeCreated',
  },
  {
    title: '上传者',
    field: 'userNameCreated',
  },
  {
    title: '操作',
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
  },
];

const onUploadSuccess = () => {
  let timer = setTimeout(() => {
    gridApi.reload();
    clearTimeout(timer);
  }, 1000);
};

const getAppendixCategory = () => {
    let appendixCategory = "";
    if (props.activeTab === 'acceptance') {
      appendixCategory = "yscl"; // 对应后端YS
    } else if (props.activeTab === 'result') {
      appendixCategory = "cgzl"; // 对应后端CG
    } else if (props.activeTab === 'process') {
      appendixCategory = "zygczl"; // 对应后端ZYGC
    }
    return appendixCategory;
}

const gridOptions: VxeGridProps = {
  columns,
  height: 500,
  rowConfig: {
    keyField: 'id',
    isHover: true,
  },
  checkboxConfig: {
    checkField: 'checked',
    trigger: 'row',
    highlight: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        let appendixCategory = getAppendixCategory();

        const condition = {
          projectResultId: props.row && props.row.id,
          appendixCategory: appendixCategory,
          parentAppendixId: currentParentAppendixId.value,
        };

        const data = await listMaterial({
          page: {
            current: page.currentPage,
            size: page.pageSize,
            searchCount: true,
          },
          condition: condition,
        });
        return {
          total: data.total,
          items: data.records,
        };
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

// 监听状态变化并触发事件
watch([currentParentAppendixId, breadcrumbs], () => {
  emit('stateChange', {
    currentParentAppendixId: currentParentAppendixId.value,
    breadcrumbs: breadcrumbs.value
  });
}, { deep: true });

// 监听项目变化
watch(() => props.row, () => {
  gridApi.reload();
}, { deep: true });

const [UploadModal, uploadModalApi] = useVbenModal({
  connectedComponent: ProjectUploadModal,
});

const handlePreview = (row) => {
  console.log('Preview:', row);
  const previewUrl = previewMaterial(props.row.id, getAppendixCategory(), row.appendixId);
  window.open(previewUrl, '_blank');
};
const handleDownload = (row) => {
  const downloadUrl = downloadMaterial(props.row.id, getAppendixCategory(), row.appendixId);
  window.open(downloadUrl, '_blank');
};

const handleDeleteFile = (row) => {
  showConform('提示', '确定要删除？', async () => {
    await deleteBatchMaterial(props.row.id, getAppendixCategory(), [row.appendixId]);
    gridApi.reload();
  });
};

const handleBatchDelete = () => {
  const checkedRecords = gridApi.grid?.getCheckboxRecords() || [];
  if (checkedRecords.length === 0) {
    showConform('提示', '请选择要删除的文件！');
    return;
  }

  showConform('提示', `确定要删除选中的 ${checkedRecords.length} 个文件吗？`, async () => {
    try {
      const appendixIds = checkedRecords.map(record => record.appendixId);
      await deleteBatchMaterial(props.row.id, getAppendixCategory(), appendixIds);
      gridApi.reload();
    } catch (error) {
      console.error('批量删除失败:', error);
      showConform('错误', '批量删除过程中出现错误，请重试！');
    }
  });
};

const openUploadModal = () => {
  uploadModalApi.open();
};

const handleFolderClick = (row) => {
  if (row.appendixType === 1) { // 确保是文件夹
    currentParentAppendixId.value = row.appendixId;
    breadcrumbs.value.push({ id: row.appendixId, name: row.appendixName });
    gridApi.reload();
  }
};

const handleBreadcrumbClick = (item, index) => {
  currentParentAppendixId.value = item.id;
  breadcrumbs.value = breadcrumbs.value.slice(0, index + 1);
  gridApi.reload();
};

const formatFileSize = (size) => {
  if(!size) {
    return '';
  }
  if (size === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(size) / Math.log(k));
  return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>
<style scoped lang="less">
@import '#/styles/dark-antd.less';

.upload-section-row{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 15px;
}
.folder-name {
  color: hsl(var(--primary));
  font-weight: bold;
}

.actionBar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
}

.actionButton {
  padding: 6px;
  color:hsl(var(--primary));
  cursor: pointer;
}

.actionButton:hover {
  color:hsl(var(--primary));
}
</style>
