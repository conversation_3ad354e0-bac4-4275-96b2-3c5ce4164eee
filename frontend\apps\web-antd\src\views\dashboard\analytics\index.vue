<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import Bar from '#/components/chart/Bar.vue';
import Pie from '#/components/chart/Pie.vue';
import Ring from '#/components/chart/Ring.vue';
import {
  getBarDatas,
  getInfo,
  getPieDatas,
  getRingDatas
} from "#/views/dashboard/analytics/index.api";
import SCharts from "#/components/chart/SCharts.vue";
import { useUserStore } from '@vben/stores';
import iconHeadDefault from '#/assets/images/icon_head_default.png'

const infoData = ref([]);
const barData = ref( {
  xAxisData: [],
    seriesData: [

  ],
});
const pieOption = ref({
});
const yearData = ref([
]);
const senceData = ref([]);
const ticketData = ref([]);
const username = ref('xxx');

const columns = [
  { title: '日期', dataIndex: 'date', key: 'date' },
  { title: '类别', dataIndex: 'category', key: 'category' },
  { title: '数据集名称', dataIndex: 'name', key: 'name' },
];

function formatDataSize(mb) {
  const tbThreshold = 1024 * 1024; // 1 TB = 1024 GB = 1024 * 1024 MB
  const gbThreshold = 1024;        // 1 GB = 1024 MB

  if (mb >= tbThreshold) {
    // 超过 1 TB，显示 TB
    return `${(mb / tbThreshold).toFixed(2)} TB`;
  } else if (mb >= gbThreshold) {
    // 超过 1 GB 但不超过 1 TB，显示 GB
    return `${(mb / gbThreshold).toFixed(2)} GB`;
  } else {
    // 不超过 1 GB，显示 MB
    return `${mb.toFixed(2)} MB`;
  }
}

const getSingleCount = (item,sizeDatas) =>{
  let count = 0;
  sizeDatas && sizeDatas.forEach((sizeItem) => {
    if(item.fileFormat.length == 0 ||
      item.fileFormat.includes(sizeItem.dataType)){
      if(sizeItem.dataSize) {
        count += sizeItem.dataSize;
      }
    }
  });

  //格式化
  item.value = formatDataSize(count);
}

const getSizeInfo = (sizeDatas) =>{
  let datas = [
    { label: '数据总量', value: '120G', color: '#00BFFF',fileFormat:[]},
    { label: '影像数据', value: '120G', color: '#32CD32',fileFormat:['dom','dem','dsm'] },
    { label: '三维数据', value: '120G', color: '#FF4500',fileFormat:['osgb','tiles','obj','point-cloud'] },
    { label: '专题数据', value: '120G', color: '#f89d76',fileFormat:['vector'] }
  ];

  datas.forEach((item) => {
     getSingleCount(item,sizeDatas);
  });

  infoData.value = datas;
}

// 动态获取当前时间
const getCurrentDate = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  return `${year}年${month}月${day}日`;
};

// 根据当前时间返回问候语
const getGreeting = () => {
  const now = new Date();
  const hour = now.getHours();
  if (hour < 12) {
    return '早上好';
  } else if (hour < 18) {
    return '下午好';
  } else {
    return '晚上好';
  }
};

const userStore = useUserStore();
const resetBarDatas = (barDatas) => {
  let datas = {};

  barDatas && barDatas.forEach(item=>{
    if(datas[item.dataType] == null){
      datas[item.dataType] = {months:[],datas:[]};
    }

    datas[item.dataType].months.push(item.month);
    datas[item.dataType].datas.push(item.dataSize ? item.dataSize : 0);
  });

  // 获取第一个属性对象
  let entrys =  Object.entries(datas);
  const [firstKey, firstValue] =entrys[0];
  let seriesData = [];
  entrys && entrys.forEach((item) => {
    seriesData.push({ name: item[0], data: item[1].datas, color: '#00BFFF' });
  });

  let newBarData = {
    xAxisData: firstValue.months,
    seriesData:seriesData,
  }

  barData.value = newBarData;

}



const resetPieDatas = (barDatas) => {
  let datas = {};

  barDatas && barDatas.forEach(item=>{
    if(datas[item.category] == null){
      datas[item.category] = {regions:[],datas:[]};
    }

    datas[item.category].regions.push(item.regionName);
    datas[item.category].datas.push(item.dataSize ? item.dataSize : 0);
  });

  // 获取第一个属性对象
  let entrys =  Object.entries(datas);
  const [firstKey, firstValue] =entrys[0];
  let seriesData = [];
  let regions = firstValue.regions;
  entrys && entrys.forEach((item) => {
    seriesData.push({
      name: item[0],
      type: 'bar',
      stack: 'total',
      label: {
        show: false
      },
      emphasis: {
        focus: 'series'
      },
      data: item[1].datas
    },);
  });

  let newBarData = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // Use axis to trigger tooltip
        type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
      }
    },
    legend: {},
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data:regions
    },
    series: seriesData
  };

  pieOption.value = newBarData;

}


const resetRingDatas = (barDatas) => {
  let datas = {};

  barDatas && barDatas.forEach(item=>{
    if(datas[item.dataYear] == null){
      datas[item.dataYear] = {dataTypes:[],dataSize:0};
    }

    datas[item.dataYear].dataTypes.push(item.dataType);
    datas[item.dataYear].dataSize = datas[item.dataYear].dataSize + (item.dataSize ? item.dataSize : 0);
  });

  // 获取第一个属性对象
  let entrys =  Object.entries(datas);
  const [firstKey, firstValue] =entrys[0];
  let seriesData = [];
  entrys && entrys.forEach((item) => {
    seriesData.push({ name: item[0], value: item[1].dataSize});
  });

  yearData.value = seriesData;

}




const loadDatas = async () =>{
  let sizeDatas = await  getInfo();
  getSizeInfo(sizeDatas);

  let barDatas = await getBarDatas();
  resetBarDatas(barDatas);


  let pieDatas = await getPieDatas();
  resetPieDatas(pieDatas);


  let ringDatas = await getRingDatas();
  resetRingDatas(ringDatas);
}

onMounted(() => {
  loadDatas();
});
</script>

<template>
  <div class="dark-mode-antd">
    <a-card style="margin: 20px 20px 0px 20px">
      <view class="pageHead">
        <!-- 动态显示问候语和日期 -->
        <view class="imageLabelArea">
          <a-image class="imageLabel"  :preview="false" :src="iconHeadDefault" width="80px" height="80px" />
          <view class="headLeftText">
            <h2  style="margin-bottom: 10px">{{ getGreeting() }}, {{ userStore.userInfo?.realName }}</h2>
            <p>{{ getCurrentDate() }}</p>
          </view>
        </view>

        <view class="staticsArea">
          <view v-for="(item, index) in infoData" :key="index" class="headLeftText">
            <view>{{ item.label }}</view>
            <h2  style="margin-top: 15px">{{ item.value }}</h2>
          </view>
        </view>
      </view>
    </a-card >

    <a-row class="chartArea">
      <a-col :span="8">
        <!-- 柱状图 -->
        <a-card size="small" title="数据量统计" class="chartCard">
          <Bar :chart-data="barData" height="300px" width="350px" />
        </a-card>
      </a-col>
      <a-col :span="8">
        <!-- 饼图 -->
        <a-card size="small" title="数据范围统计" class="chartCard">
          <SCharts :option="pieOption" height="300px" width="350px" />
        </a-card>
      </a-col>
      <a-col :span="8">
        <!-- 年度统计 -->
        <a-card size="small" title="数据建设年份统计" class="chartCard">
          <Ring :chart-data="yearData" height="300px" width="350px" />
        </a-card>
      </a-col>
    </a-row>
<!--    <div class="listArea">-->
<!--      <a-card-->
<!--        style="width: 600px; margin-top: 20px; margin-right: 80px"-->
<!--        title="最新场景数据"-->
<!--      >-->
<!--        <a-table-->
<!--          :columns="columns"-->
<!--          :data-source="senceData"-->
<!--          :pagination="false"-->
<!--        />-->
<!--      </a-card>-->
<!--      <a-card style="width: 600px; margin-top: 20px" title="最新实体数据">-->
<!--        <a-table-->
<!--          :columns="columns"-->
<!--          :data-source="ticketData"-->
<!--          :pagination="false"-->
<!--        />-->
<!--      </a-card>-->
<!--    </div>-->
  </div>
</template>

<style lang="less" scoped>
@import '#/styles/dark-antd.less';

.pageHead {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin: 20px;
}

.chartCard {
  margin-right: 5px;
}

.imageLabelArea {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin: 20px 20px 20px;
}

.staticsArea {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin: 20px 20px 30px;
}

.imageLabel {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.headLeftText {
  font-size: 16px;
  font-weight: bold;
  text-align: left;
  margin-left: 20px;
  margin-right: 20px;
  color: hsl(var(--foreground));
}

.headLeftText h2 {
  color: hsl(var(--foreground));
}

.headLeftText p {
  color: hsl(var(--muted-foreground));
}

.headRight {
  width: 70%;
}

.statistifTitleText {
  font-size: 24px;
  font-weight: bold;
  color: hsl(var(--foreground));
}

.statistifText {
  width: 200px;
  text-align: center;
  margin: 10px;
}

.chartArea {
  margin: 30px 20px 30px;
}

.listArea {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  width: 100%;
  margin: 50px 20px 30px;
}
</style>
