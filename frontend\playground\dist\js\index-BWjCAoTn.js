import{V as b,_ as d,a as D,b as I}from"./bootstrap-DShsrVit.js";import{_ as A}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as h,h as f,af as l,ag as y,ah as _,ap as e,a3 as i,ao as r,an as U,am as c,as as p,n as u,F as m}from"../jse/index-index-BMh_AyeW.js";var q={authorEmail:"<EMAIL>",authorName:"vben",authorUrl:"https://github.com/anncwb",buildTime:"2025-05-27 18:05:43",dependencies:{"@faker-js/faker":"^9.2.0",jsonwebtoken:"^9.0.2",nitropack:"^2.10.4","@vben/access":"5.4.6","@vben/common-ui":"5.4.6","@vben/constants":"5.4.6","@vben/hooks":"5.4.6","@vben/icons":"5.4.6","@vben/layouts":"5.4.6","@vben/locales":"5.4.6","@vben/plugins":"5.4.6","@vben/preferences":"5.4.6","@vben/request":"5.4.6","@vben/stores":"5.4.6","@vben/styles":"5.4.6","@vben/types":"5.4.6","@vben/utils":"5.4.6","@vueuse/core":"^11.2.0","ant-design-vue":"^4.2.5",dayjs:"^1.11.13",pinia:"2.2.2",vue:"^3.5.12","vue-router":"^4.4.5","@ant-design/icons-vue":"^7.0.1",axios:"^1.7.7",cesium:"1.95.0",less:"^4.2.0",mitt:"^3.0.1",mockjs:"^1.1.0",proj4:"^2.14.0",qs:"^6.13.0",shpjs:"^4.0.4","vite-plugin-cesium":"^1.2.23","vite-plugin-static-copy":"^2.2.0","vue3-colorpicker":"^2.3.0","lodash-es":"^4.17.21","p-limit":"^6.1.0",nanoid:"^5.0.8","@iconify/vue":"^4.1.2","@vben-core/shadcn-ui":"5.4.6","lucide-vue-next":"^0.456.0","medium-zoom":"^1.1.0","radix-vue":"^1.9.9","vitepress-plugin-group-icons":"^1.3.0","@commitlint/cli":"^19.5.0","@commitlint/config-conventional":"^19.5.0","@vben/node-utils":"5.4.6","commitlint-plugin-function-rules":"^4.0.1","cz-git":"^1.11.0",czg:"^1.11.0","eslint-config-turbo":"^2.2.3","eslint-plugin-command":"^0.2.6","eslint-plugin-import-x":"^4.4.0",prettier:"^3.3.3","prettier-plugin-tailwindcss":"^0.6.8","@stylistic/stylelint-plugin":"^3.1.1","stylelint-config-recess-order":"^5.1.1","stylelint-scss":"^6.8.1","@changesets/git":"^3.0.1","@manypkg/get-packages":"^2.2.2",chalk:"^5.3.0",consola:"^3.2.3",execa:"^9.5.1","find-up":"^7.0.0",ora:"^8.1.1","pkg-types":"^1.2.1",rimraf:"^6.0.1","@iconify/json":"^2.2.270","@iconify/tailwind":"^1.1.3","@tailwindcss/nesting":"0.0.0-insiders.565cd3e","@tailwindcss/typography":"^0.5.15",autoprefixer:"^10.4.20",cssnano:"^7.0.6",postcss:"^8.4.47","postcss-antd-fixes":"^0.2.0","postcss-import":"^16.1.0","postcss-preset-env":"^10.0.9",tailwindcss:"^3.4.14","tailwindcss-animate":"^1.0.7",vite:"^5.4.10","@intlify/unplugin-vue-i18n":"^5.2.0","@jspm/generator":"^2.4.1",archiver:"^7.0.1",cheerio:"1.0.0","get-port":"^7.1.0","html-minifier-terser":"^7.2.0","resolve.exports":"^2.0.2","vite-plugin-pwa":"^0.20.5","vite-plugin-vue-devtools":"^7.6.3","@ctrl/tinycolor":"^4.1.0","@tanstack/vue-store":"^0.5.6","@types/lodash.get":"^4.4.9","@vue/shared":"^3.5.12",clsx:"^2.1.1",defu:"^6.1.4","lodash.clonedeep":"^4.5.0","lodash.get":"^4.4.2",nprogress:"^0.2.0","tailwind-merge":"^2.5.4","theme-colors":"^0.1.0","@vben-core/shared":"5.4.6",sortablejs:"^1.15.3","@vben-core/typings":"5.4.6","@vben-core/composables":"5.4.6","@vee-validate/zod":"^4.14.7","vee-validate":"^4.14.7",zod:"^3.23.8","zod-defaults":"^0.1.3","@vben-core/icons":"5.4.6","class-variance-authority":"^0.7.0","@vben-core/form-ui":"5.2.1","@vben-core/popup-ui":"5.2.1","@vueuse/integrations":"^11.2.0",qrcode:"^1.5.4","watermark-js-plus":"^1.5.7","@vben-core/layout-ui":"5.4.6","@vben-core/menu-ui":"5.4.6","@vben-core/tabs-ui":"5.4.6",echarts:"^5.5.1","vxe-pc-ui":"^4.2.49","vxe-table":"4.10.0","@intlify/core-base":"^10.0.4","vue-i18n":"^10.0.4","@vben-core/preferences":"5.4.6","pinia-plugin-persistedstate":"^4.1.3","@vben-core/design":"5.4.6","@tanstack/vue-query":"^5.59.20","@clack/prompts":"^0.7.0",cac:"^6.7.14","circular-dependency-scanner":"^2.3.0",depcheck:"^1.4.7",publint:"^0.2.12"},devDependencies:{"@types/jsonwebtoken":"^9.0.7",h3:"^1.13.0","@nolebase/vitepress-plugin-git-changelog":"^2.8.1","@vben/vite-config":"5.4.6","@vite-pwa/vitepress":"^0.5.3",vitepress:"^1.5.0",vue:"^3.5.12","@eslint/js":"^9.14.0","@types/eslint":"^9.6.1","@typescript-eslint/eslint-plugin":"^8.13.0","@typescript-eslint/parser":"^8.13.0",eslint:"^9.14.0","eslint-plugin-eslint-comments":"^3.2.0","eslint-plugin-jsdoc":"^50.4.3","eslint-plugin-jsonc":"^2.17.0","eslint-plugin-n":"^17.13.1","eslint-plugin-no-only-tests":"^3.3.0","eslint-plugin-perfectionist":"^3.9.1","eslint-plugin-prettier":"^5.2.1","eslint-plugin-regexp":"^2.6.0","eslint-plugin-unicorn":"^56.0.0","eslint-plugin-unused-imports":"^4.1.4","eslint-plugin-vitest":"^0.5.4","eslint-plugin-vue":"^9.30.0",globals:"^15.12.0","jsonc-eslint-parser":"^2.4.0","vue-eslint-parser":"^9.4.3",postcss:"^8.4.47","postcss-html":"^1.7.0","postcss-scss":"^4.0.9",prettier:"^3.3.3",stylelint:"^16.10.0","stylelint-config-recommended":"^14.0.1","stylelint-config-recommended-scss":"^14.1.0","stylelint-config-recommended-vue":"^1.5.0","stylelint-config-standard":"^36.0.1","stylelint-order":"^6.0.4","stylelint-prettier":"^5.0.2","@types/postcss-import":"^14.0.3","@pnpm/workspace.read-manifest":"^2.2.1","@types/archiver":"^6.0.3","@types/html-minifier-terser":"^7.0.2","@vben/node-utils":"5.4.6","@vitejs/plugin-vue":"^5.1.4","@vitejs/plugin-vue-jsx":"^4.0.1",dayjs:"^1.11.13",dotenv:"^16.4.5",rollup:"^4.25.0","rollup-plugin-visualizer":"^5.12.0",sass:"1.80.6",vite:"^5.4.10","vite-plugin-compression":"^0.5.1","vite-plugin-dts":"4.2.1","vite-plugin-html":"^3.2.2","vite-plugin-lazy-import":"^1.0.7","vite-plugin-cesium":"^1.2.23","vite-plugin-static-copy":"^2.2.0","@types/lodash.clonedeep":"^4.5.9","@types/nprogress":"^0.2.3","@types/sortablejs":"^1.15.8","@types/qrcode":"^1.5.5","axios-mock-adapter":"^2.1.0"},homepage:"https://vben.pro",license:"MIT",version:"5.4.6"};const L={class:"text-foreground mt-3 text-sm leading-6"},$=["href"],C={class:"card-box p-5"},R={class:"mt-4"},S={class:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4"},M={class:"text-foreground text-sm font-medium leading-6"},O={class:"text-foreground mt-1 text-sm leading-6 sm:mt-2"},F={class:"card-box mt-6 p-5"},G={class:"mt-4"},H={class:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4"},P={class:"text-foreground text-sm"},W={class:"text-foreground/80 mt-1 text-sm sm:mt-2"},J={class:"card-box mt-6 p-5"},K={class:"mt-4"},Q={class:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4"},X={class:"text-foreground text-sm"},Y={class:"text-foreground/80 mt-1 text-sm sm:mt-2"},Z=h({name:"AboutUI",__name:"about",props:{description:{default:"是一个现代化开箱即用的中后台解决方案，采用最新的技术栈，包括 Vue 3.0、Vite、TailwindCSS 和 TypeScript 等前沿技术，代码规范严谨，提供丰富的配置选项，旨在为中大型项目的开发提供现成的开箱即用解决方案及丰富的示例，同时，它也是学习和深入前端技术的一个极佳示例。"},name:{default:"Vben Admin"},title:{default:"关于项目"}},setup(x){const o=(t,n)=>f("a",{href:t,target:"_blank",class:"vben-link"},{default:()=>n}),{authorEmail:a,authorName:k,authorUrl:j,buildTime:w,dependencies:g={},devDependencies:v={},homepage:V,license:E,version:N}=q,B=[{content:N,title:"版本号"},{content:E,title:"开源许可协议"},{content:w,title:"最后构建时间"},{content:o(V,"点击查看"),title:"主页"},{content:o(D,"点击查看"),title:"文档地址"},{content:o(I,"点击查看"),title:"预览地址"},{content:o(b,"点击查看"),title:"Github"},{content:f("div",[o(j,`${k}  `),o(`mailto:${a}`,a)]),title:"作者"}],T=Object.keys(g).map(t=>({content:g[t],title:t})),z=Object.keys(v).map(t=>({content:v[t],title:t}));return(t,n)=>(l(),y(i(A),{title:t.title},{description:_(()=>[e("p",L,[e("a",{href:i(b),class:"vben-link",target:"_blank"},r(t.name),9,$),U(" "+r(t.description),1)])]),default:_(()=>[e("div",C,[n[0]||(n[0]=e("div",null,[e("h5",{class:"text-foreground text-lg"},"基本信息")],-1)),e("div",R,[e("dl",S,[(l(),c(m,null,p(B,s=>e("div",{key:s.title,class:"border-border border-t px-4 py-6 sm:col-span-1 sm:px-0"},[e("dt",M,r(s.title),1),e("dd",O,[u(i(d),{content:s.content},null,8,["content"])])])),64))])])]),e("div",F,[n[1]||(n[1]=e("div",null,[e("h5",{class:"text-foreground text-lg"},"生产环境依赖")],-1)),e("div",G,[e("dl",H,[(l(!0),c(m,null,p(i(T),s=>(l(),c("div",{key:s.title,class:"border-border border-t px-4 py-3 sm:col-span-1 sm:px-0"},[e("dt",P,r(s.title),1),e("dd",W,[u(i(d),{content:s.content},null,8,["content"])])]))),128))])])]),e("div",J,[n[2]||(n[2]=e("div",null,[e("h5",{class:"text-foreground text-lg"},"开发环境依赖")],-1)),e("div",K,[e("dl",Q,[(l(!0),c(m,null,p(i(z),s=>(l(),c("div",{key:s.title,class:"border-border border-t px-4 py-3 sm:col-span-1 sm:px-0"},[e("dt",X,r(s.title),1),e("dd",Y,[u(i(d),{content:s.content},null,8,["content"])])]))),128))])])])]),_:1},8,["title"]))}}),ne=h({name:"About",__name:"index",setup(x){return(o,a)=>(l(),y(i(Z)))}});export{ne as default};
