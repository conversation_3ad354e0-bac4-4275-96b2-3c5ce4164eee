<script>
import { toKeyValues } from '../../../utils.js';

const defaultBtns = {
  close: {
    path: 'M576 512l277.333333 277.333333-64 64-277.333333-277.333333L234.666667 853.333333 170.666667 789.333333l277.333333-277.333333L170.666667 234.666667 234.666667 170.666667l277.333333 277.333333L789.333333 170.666667 853.333333 234.666667 576 512z',
    title: '关闭',
  },
  copy: {
    path: 'M670.037333 283.818667H206.677333c-28.330667 0-51.2 22.869333-51.2 51.2V836.266667c0 28.330667 22.869333 51.2 51.2 51.2h463.530667c28.330667 0 51.2-22.869333 51.2-51.2V335.018667a51.370667 51.370667 0 0 0-51.370667-51.2z m-17.066666 535.381333H223.744V352.085333h429.397333V819.2zM794.965333 119.466667H342.186667c-18.773333 0-34.133333 15.36-34.133334 34.133333s15.36 34.133333 34.133334 34.133333h452.778666c12.458667 0 22.528 10.069333 22.528 22.528v509.269334c0 18.773333 15.36 34.133333 34.133334 34.133333s34.133333-15.36 34.133333-34.133333V210.261333C885.589333 160.085333 844.970667 119.466667 794.965333 119.466667z',
    title: '复制',
    clickEnd: (text) => {
      if (!text) return;
      const input = document.createElement('input');
      document.body.append(input);
      input.setAttribute('value', text);
      input.select();
      document.execCommand('copy'); // 执行浏览器复制命令
      if (document.execCommand('copy')) {
        document.execCommand('copy');
        console.log(text);
        alert(`${text}\n已复制好，可贴粘。`);
      }
      input.remove();
    },
  },
};

export default {
  props: {
    style: {
      type: String,
      default: '',
    },
    text: {
      type: String,
      default: '',
    },
    html: {
      type: String,
      default: '',
    },
    toolbox: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      show: true,
      restore: true,
    };
  },
  computed: {
    mHtml() {
      let html = this.html;
      if (this.text) {
        const el = document.createElement('div');
        el.innerText = this.text;
        html = el.innerHTML;
      }
      return html;
    },
    mToolbox() {
      if (!this.toolbox || !this.toolbox.buttons) return null;
      const m = {
        size: this.toolbox.size || 32,
        fill: this.toolbox.fill || '#99999',
        minimize: this.toolbox.minimize || true,
        buttons: [],
      };
      const btns = toKeyValues(this.toolbox.buttons);
      if (btns.length === 0) return null;
      btns.forEach((btn) => {
        const option = {
          ...defaultBtns[btn.key],
          ...btn.value,
        };
        if (option.clickEnd) {
          const click = option.click;
          if (click) {
            option.click = () => {
              option.clickEnd(click());
            };
          }
        }
        m.buttons.push(option);
      });
      return m;
    },
  },
  methods: {
    minimize() {
      this.restore = false;
    },
  },
};
</script>

<template>
  <div v-if="show" class="divlabel-container">
    <template v-if="restore">
      <div v-if="!!mToolbox" class="divlabel-container-toolbox">
        <div
          v-if="mToolbox.minimize"
          class="divlabel-container-toolbox-button"
          title="最小化"
          @click.stop="restore = false"
        >
          <svg
            :style="{ zoom: mToolbox.size / 32 }"
            height="32px"
            version="1.1"
            viewBox="0 0 1024 1024"
            width="32px"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              :fill="mToolbox.fill"
              d="M449.92 543.36C449.92 520.96 431.36 512 410.24 512L97.92 512c-17.92 0-32 14.08-32 32 0 17.92 14.08 32 32 32l242.56 0-267.52 267.52c-12.16 12.16-12.16 31.36 0 43.52 12.16 12.16 31.36 12.16 43.52 0l269.44-269.44 0 246.4c0 17.92 14.08 32 32 32 17.92 0 32-14.08 32-32l0-320M865.92 384 622.72 384l267.52-267.52c12.16-12.16 12.16-31.36 0-43.52-12.16-12.16-31.36-12.16-43.52 0L577.92 342.4 577.92 96C577.92 78.08 563.2 64 545.92 64c-17.92 0-32 14.08-32 32l0 320c0 0.64 0.64 1.28 0.64 1.92 0 8.32 2.56 16.64 8.96 22.4C531.2 448.64 542.72 451.2 552.96 448l312.32 0c17.92 0 32-14.08 32-32C897.92 398.08 883.2 384 865.92 384z"
            />
          </svg>
        </div>
        <div
          v-for="btn in mToolbox.buttons || []"
          :title="btn.title"
          class="divlabel-container-toolbox-button"
          @click.stop="btn.click && btn.click()"
        >
          <svg
            :style="{ zoom: (btn.size || mToolbox.size) / 32 }"
            height="32px"
            version="1.1"
            viewBox="0 0 1024 1024"
            width="32px"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path :d="btn.path" :fill="btn.fill || mToolbox.fill" />
          </svg>
        </div>
      </div>
      <div :style="style" class="divlabel-container-text" v-html="mHtml"></div>
    </template>
    <div
      v-else
      class="divlabel-container-minimize"
      @click.stop="restore = true"
    >
      <svg
        height="20px"
        version="1.1"
        viewBox="0 0 1024 1024"
        width="20px"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M444.3 539.9L202 782.2 199.8 563c0-16.5-13.5-30-30-30s-30 13.5-30 30l2.2 285.1c0 8.8 3.8 16.7 9.8 22.2 5.5 6 13.4 9.8 22.2 9.8h295.6c16.5 0 30-13.5 30-30s-13.5-30-30-30H248.9l237.8-237.8c11.7-11.7 11.7-30.8 0-42.4-11.6-11.6-30.7-11.6-42.4 0zM578.1 488l242.3-242.3 2.2 219.2c0 16.5 13.5 30 30 30s30-13.5 30-30l-2.2-285.1c0-8.8-3.8-16.7-9.8-22.2-5.5-6-13.4-9.8-22.2-9.8H552.8c-16.5 0-30 13.5-30 30s13.5 30 30 30h220.7L535.7 445.6c-11.7 11.7-11.7 30.8 0 42.4 11.7 11.7 30.8 11.7 42.4 0z"
          fill="#ffffff"
        />
      </svg>
    </div>
    <div v-show="restore" class="divlabel-container-content"></div>
  </div>
</template>

<style lang="less" scoped>
.divlabel-container {
  position: absolute;
  left: 0;
  bottom: 0;
  width: fit-content;
  height: fit-content;
  display: flex;
  flex-direction: column;
  pointer-events: none;
  .divlabel-container-text {
    min-width: 200px;
    color: #fff;
    font-size: 14px;
    line-height: 20px;
    padding: 8px;
    display: block;
    user-select: none;
    background: rgba(5, 17, 41, 0.8);
    overflow: visible;
  }
  .divlabel-container-toolbox {
    padding: 8px;
    background: #051129;
    display: flex;
    flex-direction: row;
    justify-content: right;
    line-height: 0;
    &-button {
      cursor: pointer;
      pointer-events: all;
    }
  }
  .divlabel-container-toolbox-button {
    margin-left: 8px;
  }
  &-toolbox:empty,
  &-text:empty {
    display: none;
  }
  .divlabel-container-minimize {
    width: 32px;
    height: 32px;
    padding: 6px;
    background: rgba(5, 17, 41, 0.2);
    pointer-events: all;
    border-radius: 50%;
    &:hover {
      background: rgba(5, 17, 41, 0.8);
    }
  }
}
</style>
