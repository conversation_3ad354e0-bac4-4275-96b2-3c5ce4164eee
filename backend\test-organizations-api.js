const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

async function testOrganizationsAPI() {
  console.log('=== 测试组织管理API ===\n');
  
  let token = '';
  
  // 1. 先登录获取token
  try {
    console.log('--- 1. 登录获取token ---');
    const loginResponse = await axios.post(`${API_BASE_URL}/system/login`, {
      account: 'admin',
      password: 'admin123',
      captcha: true
    });
    
    token = loginResponse.data.data.token;
    console.log('✅ 登录成功，获取到token');
  } catch (error) {
    console.error('❌ 登录失败:', error.response?.data || error.message);
    return;
  }
  
  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
  
  // 2. 测试获取组织列表
  try {
    console.log('\n--- 2. 获取组织列表 ---');
    const response = await axios.get(`${API_BASE_URL}/organizations`, { headers });
    
    console.log('✅ 获取组织列表成功');
    console.log('响应状态:', response.status);
    console.log('组织数量:', response.data.data?.length || 0);
    
    if (response.data.data && response.data.data.length > 0) {
      console.log('前3个组织:');
      response.data.data.slice(0, 3).forEach((org, index) => {
        console.log(`  ${index + 1}. ${org.name} (${org.code}) - 状态: ${org.status}`);
      });
    }
    
  } catch (error) {
    console.error('❌ 获取组织列表失败:');
    console.error('状态码:', error.response?.status);
    console.error('错误信息:', error.response?.data || error.message);
  }
  
  // 3. 测试获取组织树形结构
  try {
    console.log('\n--- 3. 获取组织树形结构 ---');
    const response = await axios.get(`${API_BASE_URL}/organizations/tree`, { headers });
    
    console.log('✅ 获取组织树形结构成功');
    console.log('响应状态:', response.status);
    console.log('根节点数量:', response.data.data?.length || 0);
    
  } catch (error) {
    console.error('❌ 获取组织树形结构失败:');
    console.error('状态码:', error.response?.status);
    console.error('错误信息:', error.response?.data || error.message);
  }
  
  // 4. 测试创建组织
  try {
    console.log('\n--- 4. 创建测试组织 ---');
    const createData = {
      name: '测试部门',
      code: 'TEST_DEPT_' + Date.now(),
      parentId: 0,
      type: 'department',
      sortOrder: 1,
      status: 1,
      description: '这是一个测试部门'
    };
    
    const response = await axios.post(`${API_BASE_URL}/organizations`, createData, { headers });
    
    console.log('✅ 创建组织成功');
    console.log('响应状态:', response.status);
    console.log('新组织ID:', response.data.data?.id);
    console.log('新组织名称:', response.data.data?.name);
    
    // 保存新创建的组织ID，用于后续测试
    const newOrgId = response.data.data?.id;
    
    // 5. 测试更新组织
    if (newOrgId) {
      try {
        console.log('\n--- 5. 更新测试组织 ---');
        const updateData = {
          name: '更新后的测试部门',
          description: '这是更新后的测试部门描述'
        };
        
        const updateResponse = await axios.put(`${API_BASE_URL}/organizations/${newOrgId}`, updateData, { headers });
        
        console.log('✅ 更新组织成功');
        console.log('响应状态:', updateResponse.status);
        console.log('更新后名称:', updateResponse.data.data?.name);
        
      } catch (error) {
        console.error('❌ 更新组织失败:');
        console.error('状态码:', error.response?.status);
        console.error('错误信息:', error.response?.data || error.message);
      }
      
      // 6. 测试删除组织
      try {
        console.log('\n--- 6. 删除测试组织 ---');
        const deleteResponse = await axios.delete(`${API_BASE_URL}/organizations/${newOrgId}`, { headers });
        
        console.log('✅ 删除组织成功');
        console.log('响应状态:', deleteResponse.status);
        
      } catch (error) {
        console.error('❌ 删除组织失败:');
        console.error('状态码:', error.response?.status);
        console.error('错误信息:', error.response?.data || error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ 创建组织失败:');
    console.error('状态码:', error.response?.status);
    console.error('错误信息:', error.response?.data || error.message);
  }
  
  console.log('\n=== 组织管理API测试完成 ===');
}

// 运行测试
testOrganizationsAPI().catch(console.error);
