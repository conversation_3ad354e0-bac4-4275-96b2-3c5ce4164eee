const e="Welcome Back",o="Plug-and-play Admin system",n="Efficient, versatile frontend template",t="Login Successful",s="Welcome Back",c="Enter your account details to manage your projects",i="Quick Select Account",r="Username",a="Password",l="Please enter username",u="Password is incorrect",d="Please enter password",g="Please complete the verification first",m="Remember Me",p="Create an Account",y="Create Account",P="Already have an account?",h="Don't have an account?",f="Sign Up",T="Make managing your applications simple and fun",b="Confirm Password",w="The passwords do not match",S="I agree to",A="Privacy-policy",L="Terms",v="Please agree to the Privacy Policy and Terms",R="Login instead",k="Use 8 or more characters with a mix of letters, numbers & symbols",C="Forget Password?",E="Enter your email and we'll send you instructions to reset your password",q="Please enter email",M="The email format you entered is incorrect",U="Send Reset Link",x="Email",B="Scan the QR code with your phone to login",D="Click 'Confirm' after scanning to complete login",Q="QR Code Login",j="Enter your phone number to start managing your project",I="Security code",W="Security code is required",F="Mobile",G="Mobile Login",H="Please enter mobile number",O="The phone number format is incorrect",V="Get Security code",Y="Resend in {0}s",z="Or continue with",J="Please Log In Again",K="Your login session has expired. Please log in again to continue.",N={center:"Align Center",alignLeft:"Align Left",alignRight:"Align Right"},X={welcomeBack:e,pageTitle:o,pageDesc:n,loginSuccess:t,loginSuccessDesc:s,loginSubtitle:c,selectAccount:i,username:r,password:a,usernameTip:l,passwordErrorTip:u,passwordTip:d,verifyRequiredTip:g,rememberMe:m,createAnAccount:p,createAccount:y,alreadyHaveAccount:P,accountTip:h,signUp:f,signUpSubtitle:T,confirmPassword:b,confirmPasswordTip:w,agree:S,privacyPolicy:A,terms:L,agreeTip:v,goToLogin:R,passwordStrength:k,forgetPassword:C,forgetPasswordSubtitle:E,emailTip:q,emailValidErrorTip:M,sendResetLink:U,email:x,qrcodeSubtitle:B,qrcodePrompt:D,qrcodeLogin:Q,codeSubtitle:j,code:I,codeTip:W,mobile:F,mobileLogin:G,mobileTip:H,mobileErrortip:O,sendCode:V,sendText:Y,thirdPartyLogin:z,loginAgainTitle:J,loginAgainSubTitle:K,layout:N};export{h as accountTip,S as agree,v as agreeTip,P as alreadyHaveAccount,I as code,j as codeSubtitle,W as codeTip,b as confirmPassword,w as confirmPasswordTip,y as createAccount,p as createAnAccount,X as default,x as email,q as emailTip,M as emailValidErrorTip,C as forgetPassword,E as forgetPasswordSubtitle,R as goToLogin,N as layout,K as loginAgainSubTitle,J as loginAgainTitle,c as loginSubtitle,t as loginSuccess,s as loginSuccessDesc,F as mobile,O as mobileErrortip,G as mobileLogin,H as mobileTip,n as pageDesc,o as pageTitle,a as password,u as passwordErrorTip,k as passwordStrength,d as passwordTip,A as privacyPolicy,Q as qrcodeLogin,D as qrcodePrompt,B as qrcodeSubtitle,m as rememberMe,i as selectAccount,V as sendCode,U as sendResetLink,Y as sendText,f as signUp,T as signUpSubtitle,L as terms,z as thirdPartyLogin,r as username,l as usernameTip,g as verifyRequiredTip,e as welcomeBack};
