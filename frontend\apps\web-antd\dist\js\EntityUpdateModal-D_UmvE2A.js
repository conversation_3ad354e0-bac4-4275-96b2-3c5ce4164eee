var j=Object.defineProperty;var I=Object.getOwnPropertySymbols;var q=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable;var T=(r,a,e)=>a in r?j(r,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[a]=e,P=(r,a)=>{for(var e in a||(a={}))q.call(a,e)&&T(r,e,a[e]);if(I)for(var e of I(a))z.call(a,e)&&T(r,e,a[e]);return r};var w=(r,a,e)=>new Promise((y,g)=>{var v=o=>{try{m(e.next(o))}catch(i){g(i)}},b=o=>{try{m(e.throw(o))}catch(i){g(i)}},m=o=>o.done?y(o.value):Promise.resolve(o.value).then(v,b);m((e=e.apply(r,a)).next())});import{v as S,a as H}from"./bootstrap-5OPUVRWy.js";import{u as L}from"./form-DdFfsSWf.js";import{f as R}from"./entity.data-u4HDUExc.js";import{e as $,s as G,u as J}from"./entity.api-CPgpBrqe.js";import{s as K,a as Q}from"./toast-CQjPPeQ1.js";import{u as W,a as X}from"./fileUpload-DI0dJ9zY.js";import{d as Y,r as f,c as Z,k as h,j as ee,b as F,q as V,s as te,H as U,a as B,f as c,v as ae,I as le}from"../jse/index-index-DyHD_jbN.js";import{u as oe}from"./use-modal-uChFuhJy.js";import"./loading-DzjUKA94.js";const se=["directory","webkitdirectory"],ie={key:0,for:"fileInput",onclick:"triggerFileInput",class:"custom-label bg-gray-200 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400"},re={key:1,for:"fileInput",onclick:"triggerFileInput",class:"custom-label bg-primary-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400"},ne={style:{width:"900px"}},de=Y({__name:"EntityUpdateModal",emits:["register","success"],setup(r,{emit:a}){let e,y;const g=a;let v=null;const b=()=>{u.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}})},[m,o]=oe({closeOnClickModal:!1,closeOnClickModal:!1,onCancel(){o.close()},onClosed(){b()},onConfirm(){},onOpenChange(t){if(t){u.resetForm();const s=o.getData(),l=o.useStore();console.log(s),i.value=l.value&&l.value.update,C.value=l.value&&l.value.upload,e=l.value&&l.value.category,y=l.value&&l.value.row&&l.value.row.dataType;let d={};i.value&&(d=P({},l.value.row)),v=d.id,d.update=i.value,d.upload=C.value,u.setValues(d)}}});f({id:"",datasetName:""});let i=f(!1),C=f(!1);const[N,u]=L({commonConfig:{componentProps:{class:"w-full"}},handleReset:O,handleSubmit:A,layout:"horizontal",schema:R,wrapperClass:"grid-cols-1"});let n=null;f([]);const x=f("0"),_=f(!1),M=Z(()=>h(i)?"编辑":"新增");function O(t){return w(this,null,function*(){let s=t.id;yield u.resetForm(),yield u.setFieldValue("id",s),yield u.setFieldValue("dataType",y)})}function A(t){return w(this,null,function*(){try{t.category=e,_.value=!0,K("操作处理中..."),t.dataType="vector";let s=i.value?yield $(t,v):yield G(t);if(!i.value){if(n.files==null||n.files.length==0){p&&p();return}let l=D=>w(null,null,function*(){yield J({id:s,fileId:D,dataType:t.dataType}),p&&p()}),d=t.dataType,k=t.fileFormat;x.value==="0"?yield W(n,l,d,k):yield X(n,l,d,k);return}p&&p()}finally{}})}const p=()=>{o.close(),Q("操作成功！"),g("success")},E=t=>{console.log(t),n=t.target;const s=document.getElementById("fileName");s.textContent=n.files.length>0?"已选"+n.files.length+"个文件":"没有选择文件",u.setFieldValue("fileUpload",n.files)};return(t,s)=>(F(),ee(h(m),le(t.$attrs,{footer:!1,title:M.value,"destroy-on-close":"",width:"900px",maskClosable:!1}),{default:V(()=>[te(h(N),null,{fileUpload:V(l=>[U(c("input",{class:"hidden",id:"fileInput",type:"file",directory:x.value==="0",webkitdirectory:x.value==="0",onChange:E},null,40,se),[[S,!h(i)]]),_.value?(F(),B("label",ie," 选择文件 ")):(F(),B("label",re," 选择文件 ")),s[0]||(s[0]=c("span",{id:"fileName",class:"ml-4"},"没有选择文件",-1))]),_:1}),U(c("view",ne,s[1]||(s[1]=[c("div",{id:"folderProgressArea",class:"folderProgressArea"},[ae(" 文件夹总进度："),c("div",{id:"folderProgress",class:"folderProgress"},"0%")],-1),c("div",{style:{display:"flex","flex-direction":"column","justify-content":"flex-start","align-items":"flex-start"}},[c("div",{style:{"margin-bottom":"5px"}},"当前文件进度："),c("div",{id:"fileProgress",class:"fileProgress bg-primary text-white"})],-1)]),512),[[S,_.value]])]),_:1},16,["title"]))}}),be=H(de,[["__scopeId","data-v-53efdf1f"]]);export{be as default};
