var Re=Object.defineProperty,Xe=Object.defineProperties;var Ye=Object.getOwnPropertyDescriptors;var J=Object.getOwnPropertySymbols;var fe=Object.prototype.hasOwnProperty,me=Object.prototype.propertyIsEnumerable;var ne=(s,e,o)=>e in s?Re(s,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):s[e]=o,v=(s,e)=>{for(var o in e||(e={}))fe.call(e,o)&&ne(s,o,e[o]);if(J)for(var o of J(e))me.call(e,o)&&ne(s,o,e[o]);return s},z=(s,e)=>Xe(s,Ye(e));var F=(s,e)=>{var o={};for(var n in s)fe.call(s,n)&&e.indexOf(n)<0&&(o[n]=s[n]);if(s!=null&&J)for(var n of J(s))e.indexOf(n)<0&&me.call(s,n)&&(o[n]=s[n]);return o};var R=(s,e,o)=>ne(s,typeof e!="symbol"?e+"":e,o);var ae=(s,e,o)=>new Promise((n,l)=>{var i=c=>{try{C(o.next(c))}catch(r){l(r)}},f=c=>{try{C(o.throw(c))}catch(r){l(r)}},C=c=>c.done?n(c.value):Promise.resolve(c.value).then(i,f);C((o=o.apply(s,e)).next())});import{g as we,x as xe,bA as je,bC as Ne,bD as He,bB as Ke,bE as Ue,bF as We,A as Be,bG as qe,bH as Je,bI as Ge,bJ as Ze,bK as Qe,bL as et,bM as tt,bN as ot,aR as st,d as he,bO as nt,bP as at}from"./bootstrap-DShsrVit.js";import{a4 as O,af as u,ag as m,ah as y,ae as b,ai as lt,aj as rt,a3 as t,U as ke,am as re,J as j,O as $,n as I,al as _,aZ as S,aX as k,ac as ie,W as Oe,a5 as it,av as ct,au as dt,bK as le,aB as ut,az as Me,V as ge,q as De,an as X,ao as Y,ap as pt,aq as ye,b5 as ft,i as mt,h as ve}from"../jse/index-index-BMh_AyeW.js";import{X as ht}from"./x-B-ntYT_e.js";import{V as gt}from"./loading-Cqdke3S1.js";const yt=we("ExpandIcon",[["path",{d:"m21 21-6-6m6 6v-4.8m0 4.8h-4.8",key:"1c15vz"}],["path",{d:"M3 16.2V21m0 0h4.8M3 21l6-6",key:"1fsnz2"}],["path",{d:"M21 7.8V3m0 0h-4.8M21 3l-6 6",key:"hawz9i"}],["path",{d:"M3 7.8V3m0 0h4.8M3 3l6 6",key:"u9ee12"}]]);const vt=we("ShrinkIcon",[["path",{d:"m15 15 6 6m-6-6v4.8m0-4.8h4.8",key:"17vawe"}],["path",{d:"M9 19.8V15m0 0H4.2M9 15l-6 6",key:"chjx8e"}],["path",{d:"M15 4.2V9m0 0h4.8M15 9l6-6",key:"lav6yq"}],["path",{d:"M9 4.2V9m0 0H4.2M9 9 3 3",key:"1pxi2q"}]]),Ct=O({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(s,{emit:e}){const l=xe(s,e);return(i,f)=>(u(),m(t(je),lt(rt(t(l))),{default:y(()=>[b(i.$slots,"default")]),_:3},16))}}),bt=["data-dismissable-modal"],_t=O({__name:"DialogOverlay",setup(s){Ne();const e=ke("DISMISSABLE_MODAL_ID");return(o,n)=>(u(),re("div",{"data-dismissable-modal":t(e),class:"bg-overlay fixed inset-0 z-[1000]"},null,8,bt))}}),wt=O({__name:"DialogContent",props:{class:{},closeClass:{},modal:{type:Boolean},open:{type:Boolean},showClose:{type:Boolean,default:!0},forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["close","closed","opened","escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(s,{expose:e,emit:o}){const n=s,l=o,i=j(()=>{const x=n,{class:r,modal:p,open:h,showClose:d}=x;return F(x,["class","modal","open","showClose"])}),f=xe(i,l),C=$(null);function c(){n.open?l("opened"):l("closed")}return e({getContentRef:()=>C.value}),(r,p)=>(u(),m(t(We),null,{default:y(()=>[I(He,{name:"fade"},{default:y(()=>[r.open&&r.modal?(u(),m(_t,{key:0,onClick:p[0]||(p[0]=()=>l("close"))})):_("",!0)]),_:1}),I(t(Ue),ie({ref_key:"contentRef",ref:C,onAnimationend:c},t(f),{class:t(k)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-top-[48%] fixed z-[1000] w-full p-6 shadow-lg outline-none sm:rounded-xl",n.class)}),{default:y(()=>[b(r.$slots,"default"),r.showClose?(u(),m(t(Ke),{key:0,class:S(t(k)("data-[state=open]:bg-accent data-[state=open]:text-muted-foreground hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-3 top-3 h-6 w-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none",n.closeClass)),onClick:p[1]||(p[1]=()=>l("close"))},{default:y(()=>[I(t(ht),{class:"h-4 w-4"})]),_:1},8,["class"])):_("",!0)]),_:3},16,["class"])]),_:3}))}}),Ce=O({__name:"DialogDescription",props:{class:{},asChild:{type:Boolean},as:{}},setup(s){const e=s,o=j(()=>{const f=e,{class:l}=f;return F(f,["class"])}),n=Be(o);return(l,i)=>(u(),m(t(qe),ie(t(n),{class:t(k)("text-muted-foreground text-sm",e.class)}),{default:y(()=>[b(l.$slots,"default")]),_:3},16,["class"]))}}),xt=O({__name:"DialogFooter",props:{class:{}},setup(s){const e=s;return(o,n)=>(u(),re("div",{class:S(t(k)("flex flex-col-reverse sm:flex-row sm:justify-end sm:gap-x-2",e.class))},[b(o.$slots,"default")],2))}}),Bt=O({__name:"DialogHeader",props:{class:{}},setup(s){const e=s;return(o,n)=>(u(),re("div",{class:S(t(k)("flex flex-col gap-y-1.5 text-center sm:text-left",e.class))},[b(o.$slots,"default")],2))}}),be=O({__name:"DialogTitle",props:{class:{},asChild:{type:Boolean},as:{}},setup(s){const e=s,o=j(()=>{const f=e,{class:l}=f;return F(f,["class"])}),n=Be(o);return(l,i)=>(u(),m(t(Je),ie(t(n),{class:t(k)("text-lg font-semibold leading-none tracking-tight",e.class)}),{default:y(()=>[b(l.$slots,"default")]),_:3},16,["class"]))}});function kt(s,e,o){const n=Oe({offsetX:0,offsetY:0}),l=$(!1),i=r=>{const p=r.clientX,h=r.clientY;if(!s.value)return;const d=s.value.getBoundingClientRect(),{offsetX:w,offsetY:x}=n,D=d.left,N=d.top,H=d.width,G=d.height,K=document.documentElement,Z=K.clientWidth,Q=K.clientHeight,E=-D+w,ee=-N+x,te=Z-D-H+w,oe=Q-N-G+x,L=P=>{let V=w+P.clientX-p,M=x+P.clientY-h;V=Math.min(Math.max(V,E),te),M=Math.min(Math.max(M,ee),oe),n.offsetX=V,n.offsetY=M,s.value&&(s.value.style.transform=`translate(${V}px, ${M}px)`,l.value=!0)},U=()=>{l.value=!1,document.removeEventListener("mousemove",L),document.removeEventListener("mouseup",U)};document.addEventListener("mousemove",L),document.addEventListener("mouseup",U)},f=()=>{const r=le(e);r&&s.value&&r.addEventListener("mousedown",i)},C=()=>{const r=le(e);r&&s.value&&r.removeEventListener("mousedown",i)},c=()=>{n.offsetX=0,n.offsetY=0;const r=le(s);r&&(r.style.transform="none")};return it(()=>{ct(()=>{o.value?f():C()})}),dt(()=>{C()}),{dragging:l,resetPosition:c,transform:n}}const Ot=O({__name:"modal",props:{modalApi:{default:void 0},bordered:{type:Boolean},cancelText:{},centered:{type:Boolean},class:{},closable:{type:Boolean},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},draggable:{type:Boolean},footer:{type:Boolean},footerClass:{},fullscreen:{type:Boolean},fullscreenButton:{type:Boolean},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},title:{},titleTooltip:{}},setup(s){var q,pe;const e=s,o=Ge.getComponents(),n=$(),l=$(),i=$(),f=$(),C=$(),c=ut();Me("DISMISSABLE_MODAL_ID",c);const{$t:r}=Ze(),{isMobile:p}=Qe(),h=(pe=(q=e.modalApi)==null?void 0:q.useStore)==null?void 0:pe.call(q),{bordered:d,cancelText:w,centered:x,class:D,closable:N,closeOnClickModal:H,closeOnPressEscape:G,confirmLoading:K,confirmText:Z,contentClass:Q,description:E,draggable:ee,footer:te,footerClass:oe,fullscreen:L,fullscreenButton:U,header:P,headerClass:V,loading:M,modal:Ae,openAutoFocus:$e,showCancelButton:Se,showConfirmButton:Ee,title:W,titleTooltip:ce}=et(e,h),se=j(()=>L.value&&P.value||p.value),de=j(()=>ee.value&&!se.value&&P.value),{dragging:Le,transform:Pe}=kt(i,f,de);ge(()=>{var a;return(a=h==null?void 0:h.value)==null?void 0:a.isOpen},a=>ae(this,null,function*(){if(a){if(yield De(),!n.value)return;const g=n.value.getContentRef();i.value=g.$el;const{offsetX:T,offsetY:A}=Pe;i.value.style.transform=`translate(${T}px, ${A}px)`}})),ge(()=>M.value,a=>{a&&l.value&&l.value.scrollTo({top:0})});function Ve(){var a;(a=e.modalApi)==null||a.setState(g=>z(v({},g),{fullscreen:!L.value}))}function Te(a){H.value||(a.preventDefault(),a.stopPropagation())}function Ie(a){G.value||a.preventDefault()}function ze(a){$e.value||a==null||a.preventDefault()}function Fe(a){const g=a.target,T=g==null?void 0:g.dataset.dismissableModal;(!H.value||T!==c)&&(a.preventDefault(),a.stopPropagation())}function ue(a){a.preventDefault(),a.stopPropagation()}return(a,g)=>{var T;return u(),m(t(Ct),{modal:!1,open:(T=t(h))==null?void 0:T.isOpen,"onUpdate:open":g[4]||(g[4]=()=>{var A;return(A=a.modalApi)==null?void 0:A.close()})},{default:y(()=>{var A;return[I(t(wt),{ref_key:"contentRef",ref:n,class:S(t(k)("left-0 right-0 top-[10vh] mx-auto flex max-h-[80%] w-[520px] flex-col p-0 sm:rounded-2xl",t(D),{"border-border border":t(d),"shadow-3xl":!t(d),"left-0 top-0 size-full max-h-full !translate-x-0 !translate-y-0":se.value,"top-1/2 !-translate-y-1/2":t(x)&&!se.value,"duration-300":!t(Le)})),modal:t(Ae),open:(A=t(h))==null?void 0:A.isOpen,"show-close":t(N),"close-class":"top-3",onCloseAutoFocus:ue,onClosed:g[2]||(g[2]=()=>{var B;return(B=a.modalApi)==null?void 0:B.onClosed()}),onEscapeKeyDown:Ie,onFocusOutside:ue,onInteractOutside:Te,onOpenAutoFocus:ze,onOpened:g[3]||(g[3]=()=>{var B;return(B=a.modalApi)==null?void 0:B.onOpened()}),onPointerDownOutside:Fe},{default:y(()=>[I(t(Bt),{ref_key:"headerRef",ref:f,class:S(t(k)("px-5 py-4",{"border-b":t(d),hidden:!t(P),"cursor-move select-none":de.value},t(V)))},{default:y(()=>[t(W)?(u(),m(t(be),{key:0,class:"text-left"},{default:y(()=>[b(a.$slots,"title",{},()=>[X(Y(t(W))+" ",1),t(ce)?b(a.$slots,"titleTooltip",{key:0},()=>[I(t(tt),{"trigger-class":"pb-1"},{default:y(()=>[X(Y(t(ce)),1)]),_:1})]):_("",!0)])]),_:3})):_("",!0),t(E)?(u(),m(t(Ce),{key:1},{default:y(()=>[b(a.$slots,"description",{},()=>[X(Y(t(E)),1)])]),_:3})):_("",!0),!t(W)||!t(E)?(u(),m(t(ot),{key:2},{default:y(()=>[t(W)?_("",!0):(u(),m(t(be),{key:0})),t(E)?_("",!0):(u(),m(t(Ce),{key:1}))]),_:1})):_("",!0)]),_:3},8,["class"]),pt("div",{ref_key:"wrapperRef",ref:l,class:S(t(k)("relative min-h-40 flex-1 overflow-y-auto p-3",t(Q),{"overflow-hidden":t(M)}))},[t(M)?(u(),m(t(gt),{key:0,class:"size-full h-auto min-h-full",spinning:""})):_("",!0),b(a.$slots,"default")],2),t(U)?(u(),m(t(st),{key:0,class:"hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-10 top-3 hidden size-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none sm:block",onClick:Ve},{default:y(()=>[t(L)?(u(),m(t(vt),{key:0,class:"size-3.5"})):(u(),m(t(yt),{key:1,class:"size-3.5"}))]),_:1})):_("",!0),t(te)?(u(),m(t(xt),{key:1,ref_key:"footerRef",ref:C,class:S(t(k)("flex-row items-center justify-end p-2",{"border-t":t(d)},t(oe)))},{default:y(()=>[b(a.$slots,"prepend-footer"),b(a.$slots,"footer",{},()=>[t(Se)?(u(),m(ye(t(o).DefaultButton||t(he)),{key:0,variant:"ghost",onClick:g[0]||(g[0]=()=>{var B;return(B=a.modalApi)==null?void 0:B.onCancel()})},{default:y(()=>[b(a.$slots,"cancelText",{},()=>[X(Y(t(w)||t(r)("cancel")),1)])]),_:3})):_("",!0),t(Ee)?(u(),m(ye(t(o).PrimaryButton||t(he)),{key:1,loading:t(K),onClick:g[1]||(g[1]=()=>{var B;return(B=a.modalApi)==null?void 0:B.onConfirm()})},{default:y(()=>[b(a.$slots,"confirmText",{},()=>[X(Y(t(Z)||t(r)("confirm")),1)])]),_:3},8,["loading"])):_("",!0)]),b(a.$slots,"append-footer")]),_:3},8,["class"])):_("",!0)]),_:3},8,["class","modal","open","show-close"])]}),_:3},8,["open"])}}});class Mt{constructor(e={}){R(this,"api");R(this,"state");R(this,"sharedData",{payload:{}});R(this,"store");const h=e,{connectedComponent:o,onBeforeClose:n,onCancel:l,onClosed:i,onConfirm:f,onOpenChange:C,onOpened:c}=h,r=F(h,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),p={bordered:!0,centered:!1,class:"",closeOnClickModal:!0,closeOnPressEscape:!0,confirmLoading:!1,contentClass:"",draggable:!1,footer:!0,footerClass:"",fullscreen:!1,fullscreenButton:!0,header:!0,headerClass:"",isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,showCancelButton:!0,showConfirmButton:!0,title:""};this.store=new nt(v(v({},p),r),{onUpdate:()=>{var w,x,D;const d=this.store.state;(d==null?void 0:d.isOpen)===((w=this.state)==null?void 0:w.isOpen)?this.state=d:(this.state=d,(D=(x=this.api).onOpenChange)==null||D.call(x,!!(d!=null&&d.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:n,onCancel:l,onClosed:i,onConfirm:f,onOpenChange:C,onOpened:c},ft(this)}batchStore(e){this.store.batch(e)}close(){var o,n,l;((l=(n=(o=this.api).onBeforeClose)==null?void 0:n.call(o))!=null?l:!0)&&this.store.setState(i=>z(v({},i),{isOpen:!1}))}getData(){var e,o;return(o=(e=this.sharedData)==null?void 0:e.payload)!=null?o:{}}onCancel(){var e,o;this.api.onCancel?(o=(e=this.api).onCancel)==null||o.call(e):this.close()}onClosed(){var e,o;this.state.isOpen||(o=(e=this.api).onClosed)==null||o.call(e)}onConfirm(){var e,o;(o=(e=this.api).onConfirm)==null||o.call(e)}onOpened(){var e,o;this.state.isOpen&&((o=(e=this.api).onOpened)==null||o.call(e))}open(){this.store.setState(e=>z(v({},e),{isOpen:!0}))}setData(e){this.sharedData.payload=e}setState(e){mt(e)?this.store.setState(e):this.store.setState(o=>v(v({},o),e))}}const _e=Symbol("VBEN_MODAL_INJECT");function Pt(s={}){var C;const{connectedComponent:e}=s;if(e){const c=Oe({});return[O((p,{attrs:h,slots:d})=>(Me(_e,{extendApi(w){Object.setPrototypeOf(c,w)},options:s}),Dt(c,v(v(v({},p),h),d)),()=>ve(e,v(v({},p),h),d)),{inheritAttrs:!1,name:"VbenParentModal"}),c]}const o=ke(_e,{}),n=v(v({},o.options),s);n.onOpenChange=c=>{var r,p,h;(r=s.onOpenChange)==null||r.call(s,c),(h=(p=o.options)==null?void 0:p.onOpenChange)==null||h.call(p,c)};const l=new Mt(n),i=l;i.useStore=c=>at(l.store,c);const f=O((c,{attrs:r,slots:p})=>()=>ve(Ot,z(v(v({},c),r),{modalApi:i}),p),{inheritAttrs:!1,name:"VbenModal"});return(C=o.extendApi)==null||C.call(o,i),[f,i]}function Dt(s,e){return ae(this,null,function*(){var l;if(!e||Object.keys(e).length===0)return;yield De();const o=(l=s==null?void 0:s.store)==null?void 0:l.state;if(!o)return;const n=new Set(Object.keys(o));for(const i of Object.keys(e))n.has(i)&&!["class"].includes(i)&&console.warn(`[Vben Modal]: When 'connectedComponent' exists, do not set props or slots '${i}', which will increase complexity. If you need to modify the props of Modal, please use useVbenModal or api.`)})}export{Pt as u};
