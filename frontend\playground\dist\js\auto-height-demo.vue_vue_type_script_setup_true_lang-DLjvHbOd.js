import{by as m,B as p}from"./bootstrap-DShsrVit.js";import{a4 as d,O as c,af as o,ag as g,ah as r,a3 as i,n as _,an as v,am as f,as as C,ao as h,F as k}from"../jse/index-index-BMh_AyeW.js";import{u as x}from"./use-modal-B0smF4x0.js";const V=d({__name:"auto-height-demo",setup(y){const s=c([]),[u,n]=x({onCancel(){n.close()},onConfirm(){m.info("onConfirm")},onOpenChange(t){t&&l(10)}});function l(t){n.setState({loading:!0}),setTimeout(()=>{s.value=Array.from({length:t},(e,a)=>a+1),n.setState({loading:!1})},2e3)}return(t,e)=>(o(),g(i(u),{title:"自动计算高度"},{"prepend-footer":r(()=>[_(i(p),{type:"link",onClick:e[0]||(e[0]=a=>l(6))},{default:r(()=>e[1]||(e[1]=[v("点击更新数据")])),_:1})]),default:r(()=>[(o(!0),f(k,null,C(s.value,a=>(o(),f("div",{key:a,class:"even:bg-heavy bg-muted flex-center h-[220px] w-full"},h(a),1))),128))]),_:1}))}});export{V as _};
