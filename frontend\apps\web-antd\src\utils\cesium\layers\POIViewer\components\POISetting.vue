<script>
import { Button as AButton, Select as ASelect } from 'ant-design-vue';
import { ColorPicker } from 'vue3-colorpicker';

import Box from '../../../../../components/js/box.js';
import core from '../../../../../utils/cesium/core';
import { poiViewer } from '../index.js';

import 'vue3-colorpicker/style.css';

let targetEntity = null;

export default {
  components: {
    AButton,
    ColorPicker,
    ASelect,
  },
  data() {
    return {
      imgsgroup: [],
      billboard: {
        name: '',
        tagtype: '其它',
        content: '',
        fontsize: 14,
        fontcolor: 'rgb(255,255,255)',
        resId: '',
      },
      typegroup: [],
      isAdd: true,
    };
  },
  mounted() {
    targetEntity = poiViewer.editEntity;
    this.imgsgroup = poiViewer.poiImages;
    this.typegroup = poiViewer.poiTypes.map((a) => {
      return {
        value: a,
        label: a,
      };
    });
    this.billboard.tagtype =
      (this.typegroup.length && this.typegroup[0].value) || '其它';
    const json = core.entity2Json(targetEntity);
    if (json.label) {
      const font = json.label.font.split(/\s+/);
      this.billboard.name = json.label.text;
      this.billboard.fontsize = Number.parseInt(font[0]);
      this.billboard.fontcolor = json.label.fillColor;
    }
    const item = targetEntity.refData;
    if (item) {
      this.billboard.id = item.id;
      this.billboard.content = item.remark;
      this.billboard.resId = item.resId;
      this.billboard.tagtype = item.tagType;
      this.isAdd = false;
    } else {
      const img = this.imgsgroup[0];
      console.log(targetEntity, img);
      this.billboard.resId = img.id;
      targetEntity.billboard._image._value = img.resUrl;
      this.billboard.content = '';
      this.isAdd = true;
    }
  },
  methods: {
    getParams() {
      const loca =
        targetEntity.position._value || targetEntity.position.getValue();

      // viewer.entities.remove(this.entity)
      let position = null;
      if (loca) {
        position = core.transformCartesianToWGS84(loca);
        console.log('position', position);
      }
      // 新增兴趣点
      const poiparams = {
        latitude: position.lat || 0,
        longitude: position.lng || 0,
        name: this.billboard.name || '',
        nameColor: this.billboard.fontcolor || '',
        nameSize: this.billboard.fontsize || 14,
        remark: this.billboard.content || '',
        resId: this.billboard.resId || this.inimageUrl.id,
        tagType: this.billboard.tagtype || '其它',
      };
      if (!this.isAdd) {
        poiparams.id = this.billboard.id;
      }
      return poiparams;
    },
    // 生成实体
    async review() {
      const poiparams = this.getParams();
      await poiViewer.review(poiparams, targetEntity);
    },
    // 保存至后台
    async save() {
      if (!this.billboard.name) {
        Box.info(' ', '名称不能为空');
        return;
      }
      const poiparams = this.getParams();
      await poiViewer.save(poiparams);
    },
    // 获取标注点图片
    getimg(e) {
      console.log('e', e.target);
      if (this.imgsgroup) {
        this.imgsgroup.forEach((item) => {
          if (item.id == e.target.id) {
            // console.log("this.entity.billboard", this.entity.billboard)
            targetEntity.billboard._image._value = item.resUrl;
            this.billboard.resId = e.target.id;
          }
        });
      } else {
        alert('当前无可用图标！');
      }
    },
    handleChange(e) {
      this.billboard.tagtype = e;
    },
    onSearch(value) {
      const foundIt = this.typegroup.some((option) => {
        return option.value.toString() === value.toString();
      });
      if (!foundIt && !!value) {
        this.deleteSearchAdd(value);
        const params = {
          label: value,
          value,
          searchAdd: true,
        };
        this.typegroup.push(params);
      } else {
        this.handleChange(value);
      }
    },
    deleteSearchAdd(value = '') {
      const indexes = [];
      this.typegroup.forEach((option, index) => {
        if (
          option.searchAdd &&
          (option.value ?? '').toString() !== value.toString()
        ) {
          indexes.push(index);
        }
      });
      // 翻转删除数组中的项
      for (const index of indexes.reverse()) {
        this.typegroup.splice(index, 1);
      }
    },
    async movePOI() {
      const item = await poiViewer.moveStart(this.billboard.id);
      targetEntity.position._value = core.transformWGS84ToCartesian({
        lng: item.longitude,
        lat: item.latitude,
        alt: 0,
      });
    },
    async deletePOI() {
      await poiViewer.remove(this.billboard.id);
      this.$parent.$parent.close();
    },
  },
};
</script>

<template>
  <div class="shape-setting-container">
    <div class="marker">
      <div class="form-item">
        <div class="form-item-label" style="display: inline-flex">名称</div>
        <div
          class="el-form-item-content"
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 210px;
          "
        >
          <input v-model="billboard.name" style="width: 80px" />
          <input
            v-model="billboard.fontsize"
            min="7"
            style="width: 50px"
            type="number"
          />
          <ColorPicker
            v-model:pure-color="billboard.fontcolor"
            format="rgb"
            shape="circle"
            style="z-index: 1"
          />
        </div>
      </div>
      <div class="form-item" style="height: 35px">
        <div class="form-item-label">类型</div>
        <div class="el-form-item-content">
          <ASelect
            v-model:value="billboard.tagtype"
            show-search
            style="width: 200px; color: #fff; background: transparent"
            @change="handleChange"
            @search="onSearch"
          >
            <a-select-option
              v-for="item in typegroup"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </ASelect>
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-label">图标</div>
        <div class="el-form-item-content" @click="getimg">
          <div class="markerIconBox">
            <img
              v-for="item in imgsgroup"
              :id="item.id"
              :key="item.id"
              :alt="item.name"
              :class="{ selected: item.id == billboard.resId }"
              :src="item.resUrl"
              class="img"
              style="margin: 3px 5px"
            />
          </div>
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-label">备注</div>
        <div class="el-form-item-content">
          <textarea v-model="billboard.content"></textarea>
        </div>
      </div>
      <div v-if="!isAdd" style="margin: 5px 0 5px 25px">
        <AButton
          block
          style="
            display: block;
            width: 83%;
            margin: 0 auto;
            color: #fff;
            background: transparent;
          "
          type="primary"
          @click.stop="movePOI"
        >
          移动位置
        </AButton>
      </div>
    </div>
    <div class="footer">
      <AButton type="default" @click="save">保存</AButton>
      <AButton type="primary" @click="review">预览</AButton>
      <AButton v-if="!isAdd" type="primary" @click="deletePOI">删除</AButton>
    </div>
  </div>
</template>

<style lang="less" scoped>
.shape-setting-container {
  width: 100%;

  .marker {
    padding: 10px;

    input {
      height: 26px;
      margin: 7px;
      line-height: 22px;
      font-size: 14px;
      background: rgba(40, 44, 52, 0.5);
      border: 1px solid rgba(40, 44, 52, 0.8);
      color: #fff;
    }

    textarea {
      background: rgba(40, 44, 52, 0.5);
      border: 1px solid rgba(40, 44, 52, 0.8);
      color: #fff;
      margin-left: 7px;
      height: 100%;
      width: 210px;
    }

    .form-item {
      margin-bottom: 5px;
    }

    .form-item-label {
      text-align: right;
      vertical-align: middle;
      float: left;
      font-size: 14px;
      color: #fff;
      line-height: 1.5715;
      padding: 0 12px 0 0;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
    }

    .form-item-content {
      line-height: 22px;
      position: relative;
      font-size: 14px;
      height: 50px;
      width: 210px;
    }
  }

  .footer {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    place-items: 10px;
    width: 100%;
    padding: 10px;
    border-top: 1px solid rgba(40, 44, 52, 0.8);

    .ant-btn {
      margin: 0 10px;
      color: rgba(0, 0, 0, 0.85);
      border-color: #d9d9d9;
      background: rgba(255, 255, 255, 0.3);
    }

    .ant-btn:hover,
    .ant-btn:focus,
    .ant-btn:active {
      color: rgba(0, 0, 0, 1);
    }

    .ant-btn-primary:hover,
    .ant-btn-primary:focus,
    .ant-btn-primary:active {
      color: #fff;
      border-color: #1890ff;
      background: #1890ff;
    }
  }

  .img {
    height: 30px;
    width: 30px;
    margin-right: 10px;
    margin-bottom: 5px;
  }

  .markerIconBox {
    width: 210px;
    margin-left: 7px;
    line-height: 35px;
    font-size: 14px;
    background: rgba(40, 44, 52, 0.5);
    border: 1px solid rgba(40, 44, 52, 0.8);
    color: #fff;
    max-height: 80px;
    overflow-y: scroll;
    .selected {
      border: 1px solid red;
    }
  }

  .markerIconBox::-webkit-scrollbar {
    width: 7px;
  }

  .markerIconBox::-webkit-scrollbar-thumb {
    background-color: rgba(30, 128, 255, 0.8);
    border-radius: 10%;
  }

  .colorui {
    margin: 5px 5px 10px 48px !important;
    line-height: 22px;
    z-index: 100000 !important;
  }

  :deep(.vc-color-wrap) {
    margin-top: 6px;
  }

  :deep(.ant-select-selection-item) {
    background: transparent;
    border: 0px solid #f0f0f0;
  }

  :deep(.shape-setting-container) {
    color: #fff;
    background: transparent;
    height: 26px;
    line-height: 26px;
    border: 1px solid #282c34;
    margin: 7px;
  }
}
</style>
