<template>
  <Modal
    title="上传文件"
    class="w-[600px]"
    :canFullscreen="false"
    :width="800"
    :footer="false"
    @ok="handleConfirm"
  >
    <div class="p-4">
      <a-radio-group v-model:value="uploadType">
        <a-radio :value="0">上传文件夹</a-radio>
        <a-radio :value="1">上传文件</a-radio>
      </a-radio-group>

      <div
        class="mt-4 p-6 border-2 border-dashed rounded-md text-center cursor-pointer upload-area"
        @dragover.prevent="handleDragOver"
        @dragleave.prevent="handleDragLeave"
        @drop.prevent="handleDrop"
        @click="triggerFileInput"
      >
        <p class="text-lg upload-text">拖拽文件到此处或点击选择</p>
        <p class="text-sm upload-text-secondary">(支持文件或文件夹上传)</p>
        <input
          class="hidden"
          id="fileInput"
          type="file"
          :directory="uploadType === 0"
          :webkitdirectory="uploadType === 0"
          @change="handleFileSelect"
          ref="fileInputRef"
        />
      </div>
      <div class="mt-2 text-gray-700" v-if="fileName">已选择：{{ fileName }}</div>
      <a-button
        type="primary"
        @click="handleUpload"
        :disabled="!fileInput.length || startUpload"
        class="mt-4 upload-button"
      >开始上传</a-button>
    </div>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { fileUploader } from '#/utils/upload';

const emit = defineEmits(['success']);

const props = defineProps({
  projectId: { type: String, required: true },
  appendixCategory: { type: String, required: true },
  parentAppendixId: { type: String, required: true },
  baseUrl: {
    type: String,
    default: '/project-result-appendix'
  }
});

const uploadType = ref(0);
const fileInputRef = ref<HTMLInputElement | null>(null);
const fileInput = ref<File[]>([]);
const fileName = ref('');
const startUpload = ref(false);

const [Modal, uploadModalApi] = useVbenModal({
  closeOnClickModal: false,
  onCancel() {
    clearData();
    uploadModalApi.close();
  },
  onBeforeClose() {
    clearData();
    return true;
  },
});

const handleUploadTypeChange = (e: number) => {
  uploadType.value = e;
  fileInput.value = [];
  fileName.value = '';
};

const triggerFileInput = () => {
  if (startUpload.value) return;
  fileInputRef.value?.click();
};

const handleFileSelect = (event: Event) => {
  if (startUpload.value) return;
  const target = event.target as HTMLInputElement;

  if (target.files) {
    if (target.files.length === 1) {
      const file = target.files[0];
      if (file) {
        fileInput.value = [file];
      }
    } else {
      fileInput.value = Array.from(target.files);
    }
    fileName.value = fileInput.value.length + ' 个文件';
  }
};

const handleDragOver = (event: DragEvent) => {
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy';
  }
};

const handleDragLeave = () => {
  // Optional: Add visual feedback when drag leaves
};

const handleDrop = async (event: DragEvent) => {
  if (startUpload.value) return;
  event.preventDefault();

  if (event.dataTransfer) {
    const files = await fileUploader.handleDroppedFiles(event.dataTransfer);
    fileInput.value = files;
    fileName.value = files.length + ' 个文件';
  }
};

const handleUpload = async () => {
  if (startUpload.value) return;
  startUpload.value = true;

  try {
    // 构建实际的上传链接
    const urls = {
      initUpload: `${props.baseUrl}/init-upload/${props.projectId}/${props.appendixCategory}/${props.parentAppendixId}`,
      uploadChunk: `${props.baseUrl}/upload-chunk/${props.projectId}/${props.appendixCategory}`,
      uploadProgress: `${props.baseUrl}/upload-progress`
    };

    fileUploader.uploadFiles(
      fileInput.value,
      { urls },
      {
        onSuccess: () => {
          emit('success');
        },
        onError: () => {
          startUpload.value = false;
        },
      }
    );

    uploadModalApi.close();
  } catch (error) {
    startUpload.value = false;
  }
};

const clearData = () => {
  fileInput.value = [];
  fileName.value = '';
  startUpload.value = false;
  uploadType.value = 0;
};

const handleConfirm = () => {
  // This function is called when the modal's confirm button is clicked
  // Since we have a dedicated upload button, this might not be needed
  // or can be used to trigger handleUpload if desired.
};
</script>

<style scoped>
@import '#/styles/dark-antd.less';
.fileProgress {
  color: white;
}

.folderProgressArea {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-bottom: 5px;
}

.upload-area {
  border-color: hsl(var(--border));
  background-color: hsl(var(--background));
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: hsl(var(--primary));
  background-color: hsl(var(--background));
}

.upload-text {
  color: hsl(var(--foreground));
}

.upload-text-secondary {
  color: hsl(var(--muted-foreground));
}

:deep(.upload-button) {
  background-color: hsl(var(--primary)) !important;
  border-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

:deep(.upload-button:hover) {
  background-color: hsl(var(--primary) / 0.9) !important;
  border-color: hsl(var(--primary) / 0.9) !important;
}

:deep(.upload-button:disabled) {
  background-color: hsl(var(--muted)) !important;
  border-color: hsl(var(--muted)) !important;
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.5;
}
</style>
