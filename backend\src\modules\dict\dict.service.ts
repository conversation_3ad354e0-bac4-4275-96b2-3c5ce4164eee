import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DictType } from './entities/dict-type.entity';
import { DictData } from './entities/dict-data.entity';

@Injectable()
export class DictService {
  constructor(
    @InjectRepository(DictType)
    private dictTypeRepository: Repository<DictType>,
    @InjectRepository(DictData)
    private dictDataRepository: Repository<DictData>,
  ) {}
}
