var Et=Object.defineProperty,Ut=Object.defineProperties;var Dt=Object.getOwnPropertyDescriptors;var J=Object.getOwnPropertySymbols;var dt=Object.prototype.hasOwnProperty,ft=Object.prototype.propertyIsEnumerable;var pt=i=>{throw TypeError(i)};var lt=(i,t,e)=>t in i?Et(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e,I=(i,t)=>{for(var e in t||(t={}))dt.call(t,e)&&lt(i,e,t[e]);if(J)for(var e of J(t))ft.call(t,e)&&lt(i,e,t[e]);return i},bt=(i,t)=>Ut(i,Dt(t));var mt=(i,t)=>{var e={};for(var n in i)dt.call(i,n)&&t.indexOf(n)<0&&(e[n]=i[n]);if(i!=null&&J)for(var n of J(i))t.indexOf(n)<0&&ft.call(i,n)&&(e[n]=i[n]);return e};var Y=(i,t,e)=>t.has(i)||pt("Cannot "+e);var s=(i,t,e)=>(Y(i,t,"read from private field"),e?e.call(i):t.get(i)),p=(i,t,e)=>t.has(i)?pt("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(i):t.set(i,e),u=(i,t,e,n)=>(Y(i,t,"write to private field"),n?n.call(i,e):t.set(i,e),e),f=(i,t,e)=>(Y(i,t,"access private method"),e);import{be as Ft,bf as yt,bg as C,bh as $,bi as Z,bj as Tt,bk as gt,bl as Rt,bm as xt,bn as jt,bo as Lt,bp as vt,bq as Mt,br as kt,bs as Pt,bt as Ot,bu as Ct}from"./bootstrap-DShsrVit.js";import{X as _t,U as At,J as zt,aV as Vt,V as X,_ as Wt,Z as Bt}from"../jse/index-index-BMh_AyeW.js";var y,r,N,m,D,A,Q,O,H,z,V,F,T,E,W,h,B,q,tt,et,st,it,nt,rt,Qt,It,Yt=(It=class extends Ft{constructor(t,e){super();p(this,h);p(this,y);p(this,r);p(this,N);p(this,m);p(this,D);p(this,A);p(this,Q);p(this,O);p(this,H);p(this,z);p(this,V);p(this,F);p(this,T);p(this,E);p(this,W,new Set);this.options=e,u(this,y,t),u(this,O,null),u(this,Q,yt()),this.options.experimental_prefetchInRender||s(this,Q).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(s(this,r).addObserver(this),St(s(this,r),this.options)?f(this,h,B).call(this):this.updateResult(),f(this,h,st).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return at(s(this,r),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return at(s(this,r),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,f(this,h,it).call(this),f(this,h,nt).call(this),s(this,r).removeObserver(this)}setOptions(t,e){const n=this.options,c=s(this,r);if(this.options=s(this,y).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof C(this.options.enabled,s(this,r))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");f(this,h,rt).call(this),s(this,r).setOptions(this.options),n._defaulted&&!$(this.options,n)&&s(this,y).getQueryCache().notify({type:"observerOptionsUpdated",query:s(this,r),observer:this});const o=this.hasListeners();o&&wt(s(this,r),c,this.options,n)&&f(this,h,B).call(this),this.updateResult(e),o&&(s(this,r)!==c||C(this.options.enabled,s(this,r))!==C(n.enabled,s(this,r))||Z(this.options.staleTime,s(this,r))!==Z(n.staleTime,s(this,r)))&&f(this,h,q).call(this);const l=f(this,h,tt).call(this);o&&(s(this,r)!==c||C(this.options.enabled,s(this,r))!==C(n.enabled,s(this,r))||l!==s(this,E))&&f(this,h,et).call(this,l)}getOptimisticResult(t){const e=s(this,y).getQueryCache().build(s(this,y),t),n=this.createResult(e,t);return Ht(this,n)&&(u(this,m,n),u(this,A,this.options),u(this,D,s(this,r).state)),n}getCurrentResult(){return s(this,m)}trackResult(t,e){const n={};return Object.keys(t).forEach(c=>{Object.defineProperty(n,c,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(c),e==null||e(c),t[c])})}),n}trackProp(t){s(this,W).add(t)}getCurrentQuery(){return s(this,r)}refetch(e={}){var t=mt(e,[]);return this.fetch(I({},t))}fetchOptimistic(t){const e=s(this,y).defaultQueryOptions(t),n=s(this,y).getQueryCache().build(s(this,y),e);return n.fetch().then(()=>this.createResult(n,e))}fetch(t){var e;return f(this,h,B).call(this,bt(I({},t),{cancelRefetch:(e=t.cancelRefetch)!=null?e:!0})).then(()=>(this.updateResult(),s(this,m)))}createResult(t,e){var ct;const n=s(this,r),c=this.options,o=s(this,m),l=s(this,D),g=s(this,A),j=t!==n?t.state:s(this,N),{state:w}=t;let d=I({},w),U=!1,a;if(e._optimisticResults){const b=this.hasListeners(),P=!b&&St(t,e),_=b&&wt(t,n,e,c);(P||_)&&(d=I(I({},d),Lt(w.data,t.options))),e._optimisticResults==="isRestoring"&&(d.fetchStatus="idle")}let{error:S,errorUpdatedAt:L,status:R}=d;if(e.select&&d.data!==void 0)if(o&&d.data===(l==null?void 0:l.data)&&e.select===s(this,H))a=s(this,z);else try{u(this,H,e.select),a=e.select(d.data),a=vt(o==null?void 0:o.data,a,e),u(this,z,a),u(this,O,null)}catch(b){u(this,O,b)}else a=d.data;if(e.placeholderData!==void 0&&a===void 0&&R==="pending"){let b;if(o!=null&&o.isPlaceholderData&&e.placeholderData===(g==null?void 0:g.placeholderData))b=o.data;else if(b=typeof e.placeholderData=="function"?e.placeholderData((ct=s(this,V))==null?void 0:ct.state.data,s(this,V)):e.placeholderData,e.select&&b!==void 0)try{b=e.select(b),u(this,O,null)}catch(P){u(this,O,P)}b!==void 0&&(R="success",a=vt(o==null?void 0:o.data,b,e),U=!0)}s(this,O)&&(S=s(this,O),a=s(this,z),L=Date.now(),R="error");const M=d.fetchStatus==="fetching",k=R==="pending",G=R==="error",ot=k&&M,ut=a!==void 0,v={status:R,fetchStatus:d.fetchStatus,isPending:k,isSuccess:R==="success",isError:G,isInitialLoading:ot,isLoading:ot,data:a,dataUpdatedAt:d.dataUpdatedAt,error:S,errorUpdatedAt:L,failureCount:d.fetchFailureCount,failureReason:d.fetchFailureReason,errorUpdateCount:d.errorUpdateCount,isFetched:d.dataUpdateCount>0||d.errorUpdateCount>0,isFetchedAfterMount:d.dataUpdateCount>j.dataUpdateCount||d.errorUpdateCount>j.errorUpdateCount,isFetching:M,isRefetching:M&&!k,isLoadingError:G&&!ut,isPaused:d.fetchStatus==="paused",isPlaceholderData:U,isRefetchError:G&&ut,isStale:ht(t,e),refetch:this.refetch,promise:s(this,Q)};if(this.options.experimental_prefetchInRender){const b=K=>{v.status==="error"?K.reject(v.error):v.data!==void 0&&K.resolve(v.data)},P=()=>{const K=u(this,Q,v.promise=yt());b(K)},_=s(this,Q);switch(_.status){case"pending":t.queryHash===n.queryHash&&b(_);break;case"fulfilled":(v.status==="error"||v.data!==_.value)&&P();break;case"rejected":(v.status!=="error"||v.error!==_.reason)&&P();break}}return v}updateResult(t){const e=s(this,m),n=this.createResult(s(this,r),this.options);if(u(this,D,s(this,r).state),u(this,A,this.options),s(this,D).data!==void 0&&u(this,V,s(this,r)),$(n,e))return;u(this,m,n);const c={},o=()=>{if(!e)return!0;const{notifyOnChangeProps:l}=this.options,g=typeof l=="function"?l():l;if(g==="all"||!g&&!s(this,W).size)return!0;const x=new Set(g!=null?g:s(this,W));return this.options.throwOnError&&x.add("error"),Object.keys(s(this,m)).some(j=>{const w=j;return s(this,m)[w]!==e[w]&&x.has(w)})};(t==null?void 0:t.listeners)!==!1&&o()&&(c.listeners=!0),f(this,h,Qt).call(this,I(I({},c),t))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&f(this,h,st).call(this)}},y=new WeakMap,r=new WeakMap,N=new WeakMap,m=new WeakMap,D=new WeakMap,A=new WeakMap,Q=new WeakMap,O=new WeakMap,H=new WeakMap,z=new WeakMap,V=new WeakMap,F=new WeakMap,T=new WeakMap,E=new WeakMap,W=new WeakMap,h=new WeakSet,B=function(t){f(this,h,rt).call(this);let e=s(this,r).fetch(this.options,t);return t!=null&&t.throwOnError||(e=e.catch(Tt)),e},q=function(){f(this,h,it).call(this);const t=Z(this.options.staleTime,s(this,r));if(gt||s(this,m).isStale||!Rt(t))return;const n=xt(s(this,m).dataUpdatedAt,t)+1;u(this,F,setTimeout(()=>{s(this,m).isStale||this.updateResult()},n))},tt=function(){var t;return(t=typeof this.options.refetchInterval=="function"?this.options.refetchInterval(s(this,r)):this.options.refetchInterval)!=null?t:!1},et=function(t){f(this,h,nt).call(this),u(this,E,t),!(gt||C(this.options.enabled,s(this,r))===!1||!Rt(s(this,E))||s(this,E)===0)&&u(this,T,setInterval(()=>{(this.options.refetchIntervalInBackground||jt.isFocused())&&f(this,h,B).call(this)},s(this,E)))},st=function(){f(this,h,q).call(this),f(this,h,et).call(this,f(this,h,tt).call(this))},it=function(){s(this,F)&&(clearTimeout(s(this,F)),u(this,F,void 0))},nt=function(){s(this,T)&&(clearInterval(s(this,T)),u(this,T,void 0))},rt=function(){const t=s(this,y).getQueryCache().build(s(this,y),this.options);if(t===s(this,r))return;const e=s(this,r);u(this,r,t),u(this,N,t.state),this.hasListeners()&&(e==null||e.removeObserver(this),t.addObserver(this))},Qt=function(t){Mt.batch(()=>{t.listeners&&this.listeners.forEach(e=>{e(s(this,m))}),s(this,y).getQueryCache().notify({query:s(this,r),type:"observerResultsUpdated"})})},It);function Nt(i,t){return C(t.enabled,i)!==!1&&i.state.data===void 0&&!(i.state.status==="error"&&t.retryOnMount===!1)}function St(i,t){return Nt(i,t)||i.state.data!==void 0&&at(i,t,t.refetchOnMount)}function at(i,t,e){if(C(t.enabled,i)!==!1){const n=typeof e=="function"?e(i):e;return n==="always"||n!==!1&&ht(i,t)}return!1}function wt(i,t,e,n){return(i!==t||C(n.enabled,i)===!1)&&(!e.suspense||i.state.status!=="error")&&ht(i,e)}function ht(i,t){return C(t.enabled,i)!==!1&&i.isStaleByTime(Z(t.staleTime,i))}function Ht(i,t){return!$(i.getCurrentResult(),t)}function Kt(i=""){if(!_t())throw new Error("vue-query hooks can only be used inside setup() function or functions that support injection context.");const t=kt(i),e=At(t);if(!e)throw new Error("No 'queryClient' found in Vue context, use 'VueQueryPlugin' to properly initialize the library.");return e}function $t(i,t,e){const n=Kt(),c=zt(()=>{const a=Pt(t);typeof a.enabled=="function"&&(a.enabled=a.enabled());const S=n.defaultQueryOptions(a);return S._optimisticResults=n.isRestoring.value?"isRestoring":"optimistic",S}),o=new i(n,c.value),l=Vt(o.getCurrentResult());let g=()=>{};X(n.isRestoring,a=>{a||(g(),g=o.subscribe(S=>{Ot(l,S)}))},{immediate:!0});const x=()=>{o.setOptions(c.value),Ot(l,o.getCurrentResult())};X(c,x),Bt(()=>{g()});const j=(...a)=>(x(),l.refetch(...a)),w=()=>new Promise((a,S)=>{let L=()=>{};const R=()=>{if(c.value.enabled!==!1){o.setOptions(c.value);const M=o.getOptimisticResult(c.value);M.isStale?(L(),o.fetchOptimistic(c.value).then(a,k=>{Ct(c.value.throwOnError,[k,o.getCurrentQuery()])?S(k):a(o.getCurrentResult())})):(L(),a(M))}};R(),L=X(c,R)});X(()=>l.error,a=>{if(l.isError&&!l.isFetching&&Ct(c.value.throwOnError,[a,o.getCurrentQuery()]))throw a});const U=Wt(l);for(const a in l)typeof l[a]=="function"&&(U[a]=l[a]);return U.suspense=w,U.refetch=j,U}export{Yt as Q,$t as u};
