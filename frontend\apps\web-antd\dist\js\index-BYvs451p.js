var go=Object.defineProperty,vo=Object.defineProperties;var mo=Object.getOwnPropertyDescriptors;var Jn=Object.getOwnPropertySymbols;var yo=Object.prototype.hasOwnProperty,bo=Object.prototype.propertyIsEnumerable;var Zn=(e,t,r)=>t in e?go(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,V=(e,t)=>{for(var r in t||(t={}))yo.call(t,r)&&Zn(e,r,t[r]);if(Jn)for(var r of Jn(t))bo.call(t,r)&&Zn(e,r,t[r]);return e},at=(e,t)=>vo(e,mo(t));import{ap as un,aq as Co,ar as wo,as as da,at as xo,au as So,av as _o,aw as ha,ax as ko,ay as Eo,az as Qn,aA as Ao,aB as Oo,aC as Ro,aD as Mo,aE as To,aF as ei,aG as Po,aH as Ho,aI as Io,aJ as Lo,aK as M,aL as Ze,v as Kr,a as Do,aM as $o,aN as Bo,B as No,ac as Vo,q as jo}from"./bootstrap-5OPUVRWy.js";import{G as Fo,b as pe,u as pa,z as ti,B as Rt}from"./index-D4Q7xmlJ.js";import{d as Ce,r as Z,y as ge,w as pt,c as ie,af as St,ag as zo,i as Wo,ah as ue,ai as Uo,aj as Be,ak as cn,a2 as ga,s as ne,o as Go,a as I,b as A,f as b,n as oe,h as ce,F as Ue,D as gt,e as $,H as Pe,v as We,t as nr,al as _t,am as kt,x as z,j as te,q as $e,p as ri,I as ni,g as qr,an as Ko,a9 as qo,ao as Xo,ap as Yo,C as ee}from"../jse/index-index-DyHD_jbN.js";function Jo(e,t,r){if(!un(r))return!1;var n=typeof t;return(n=="number"?Co(r)&&wo(t,r.length):n=="string"&&t in r)?da(r[t],e):!1}function Zo(e){return xo(function(t,r){var n=-1,i=r.length,a=i>1?r[i-1]:void 0,l=i>2?r[2]:void 0;for(a=e.length>3&&typeof a=="function"?(i--,a):void 0,l&&Jo(r[0],r[1],l)&&(a=i<3?void 0:a,i=1),t=Object(t);++n<i;){var o=r[n];o&&e(t,o,n,a)}return t})}function Xr(e,t,r){(r!==void 0&&!da(e[t],r)||r===void 0&&!(t in e))&&So(e,t,r)}function Yr(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}function Qo(e){return _o(e,ha(e))}function es(e,t,r,n,i,a,l){var o=Yr(e,r),s=Yr(t,r),c=l.get(s);if(c){Xr(e,r,c);return}var u=a?a(o,s,r+"",e,t,l):void 0,f=u===void 0;if(f){var d=Qn(s),h=!d&&ko(s),m=!d&&!h&&Eo(s);u=s,d||h||m?Qn(o)?u=o:Ao(o)?u=Oo(o):h?(f=!1,u=Ro(s,!0)):m?(f=!1,u=Mo(s,!0)):u=[]:To(s)||ei(s)?(u=o,ei(o)?u=Qo(o):(!un(o)||Po(o))&&(u=Ho(s))):f=!1}f&&(l.set(s,u),i(u,s,n,a,l),l.delete(s)),Xr(e,r,u)}function va(e,t,r,n,i){e!==t&&Io(t,function(a,l){if(i||(i=new Lo),un(a))es(e,t,l,r,va,n,i);else{var o=n?n(Yr(e,l),a,l+"",e,t,i):void 0;o===void 0&&(o=a),Xr(e,l,o)}},ha)}var mr=Zo(function(e,t,r){va(e,t,r)});class fn{constructor(){this.__handlers={}}emit(t,r){if(this.__handlers[t]instanceof Array)for(var n=this.__handlers[t],i=0;i<n.length;i++)n[i].handler(r),n[i].once&&(n.splice(i,1),i--)}on(t,r){typeof this.__handlers[t]=="undefined"&&(this.__handlers[t]=[]),this.__handlers[t].push({handler:r})}once(t,r){typeof this.__handlers[t]=="undefined"&&(this.__handlers[t]=[]),this.__handlers[t].push({handler:r,once:!0})}off(t,r){if(this.__handlers[t]instanceof Array){for(var n=this.__handlers[t],i=0;i<n.length&&n[i].handler!=r;i++);n.splice(i,1)}}clear(t){t?this.__handlers[t]instanceof Array&&(this.__handlers[t]=[]):this.__handlers={}}}let Dt=new fn;class ts extends fn{constructor(t,r){super(),this.viewer=t||Fo.viewer,this.entities=[],this.handler=null,this.tipEntity=null,this.tipPosition=null,this.isDrawing=!1,this.dragHandler=null,this.options=V({selectable:!1,draggable:!1,removeable:!1,highlightable:!1},r),this.is2DMode=this.viewer.scene.mode===Cesium.SceneMode.SCENE2D,this.viewer.scene.morphComplete.addEventListener(()=>{this.is2DMode=this.viewer.scene.mode===Cesium.SceneMode.SCENE2D,this.entities.forEach(n=>{n.heightReference&&(n.heightReference=this.is2DMode?Cesium.HeightReference.NONE:Cesium.HeightReference.RELATIVE_TO_GROUND)})}),this.options.draggable&&this.dragEntity()}drawPoint(t,r){let n={pixelSize:4,color:Cesium.Color.RED,outlineColor:Cesium.Color.WHITE,outlineWidth:2,heightReference:this.is2DMode?Cesium.HeightReference.NONE:Cesium.HeightReference.RELATIVE_TO_GROUND},i=this.viewer.entities.add({name:"point",entityType:"point",position:t,point:V(V({},n),r)});return this.entities.push(i),i}drawLine(t,r,n){let i={show:!0,positions:t,material:Cesium.Color.CHARTREUSE,width:2,clampToGround:!1,heightReference:this.is2DMode?Cesium.HeightReference.NONE:Cesium.HeightReference.RELATIVE_TO_GROUND},a=this.viewer.entities.add({position:r,name:"polyline",polyline:V(V({},i),n)});return this.entities.push(a),a}drawMarker(t,r){let n={show:!0,scale:1,heightReference:this.is2DMode?Cesium.HeightReference.NONE:Cesium.HeightReference.RELATIVE_TO_GROUND,verticalOrigin:Cesium.VerticalOrigin.BOTTOM},i=this.viewer.entities.add({name:"billboard",show:!0,position:t,billboard:V(V({},n),r)});return this.entities.push(i),i}drawCircle(t,r){let n={semiMinorAxis:10,semiMajorAxis:10,material:Cesium.Color.BLUE.withAlpha(.5),heightReference:this.is2DMode?Cesium.HeightReference.NONE:Cesium.HeightReference.RELATIVE_TO_GROUND,height:this.is2DMode?0:void 0,outline:!0,outlineColor:Cesium.Color.WHITE},i=this.viewer.entities.add({name:"ellipse",position:t,ellipse:V(V({},n),r)});return this.entities.push(i),i}drawRectangle(t,r,n){let i={coordinates:t,material:Cesium.Color.GREEN.withAlpha(.5),heightReference:this.is2DMode?Cesium.HeightReference.NONE:Cesium.HeightReference.RELATIVE_TO_GROUND,height:this.is2DMode?0:n.height,outline:!0,outlineColor:Cesium.Color.WHITE},a=this.viewer.entities.add({position:r,name:"rectangle",rectangle:V(V({},i),n)});return this.entities.push(a),a}drawPolygon(t,r,n){let i={hierarchy:t,material:Cesium.Color.GREEN.withAlpha(.5),heightReference:this.is2DMode?Cesium.HeightReference.NONE:Cesium.HeightReference.RELATIVE_TO_GROUND,height:this.is2DMode?0:void 0,extrudedHeight:this.is2DMode?void 0:n.extrudedHeight,classificationType:this.is2DMode?void 0:Cesium.ClassificationType.BOTH},a=this.viewer.entities.add({position:r,name:"polygon",polygon:V(V({},i),n)});return this.entities.push(a),a}drawModel(t,r){let n=new Cesium.HeadingPitchRoll(Cesium.Math.toRadians(0),0,0),i=r.orientation||Cesium.Transforms.headingPitchRollQuaternion(t,n),a={scale:1,show:!0},l=this.viewer.entities.add({name:"model",position:t,orientation:i,model:V(V({},a),r)});return this.entities.push(l),l}drawText(t,r){let n={text:"",font:"12px sans-serif",fillColor:Cesium.Color.WHITE,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.TOP,horizontalOrigin:Cesium.HorizontalOrigin.LEFT,pixelOffset:new Cesium.Cartesian2(20,-20)},i=this.viewer.entities.add({name:"label",position:t,label:V(V({},n),r)});return this.entities.push(i),i}drawEntity(t){return t&&(this.entities.push(t),this.viewer.entities.add(t),this.emit("add",t),t)}select(t){this.entities.indexOf(t)>=0&&(t.color=Cesium.Color.RED)}remove(t){if(t)for(t instanceof Array||(t=[t]);t.length;){let r=t.pop(),n=this.entities.indexOf(r);n>=0&&(this.viewer.entities.remove(r),this.entities.splice(n,1))}}clear(){this.entities.forEach(t=>{this.viewer.entities.remove(t)}),this.entities=[],this.emit("clear")}show(){this.entities.forEach(t=>{t.show=!0})}hide(){this.entities.forEach(t=>{t.show=!1})}drawStart(t,r,n){this.drawEnd();var i=[],a=[],l=null,o=0,s=0;this.isDrawing=!0,Dt.emit("draw",{from:"Drawer"}),this.tipEntity=this.drawText(new Cesium.CallbackProperty(()=>this.tipPosition,!1),{text:new Cesium.CallbackProperty(function(){return i.length==0?"点击开始,右击取消":i.length<=s?"点击继续,右击取消":"点击继续,右击结束"},!1),font:"12px sans-serif",fillColor:Cesium.Color.WHITE,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.TOP,horizontalOrigin:Cesium.HorizontalOrigin.LEFT,pixelOffset:new Cesium.Cartesian2(5,5),showBackground:!0,backgroundColor:new Cesium.Color(.165,.165,.165,.8),backgroundPadding:new Cesium.Cartesian2(6,6),disableDepthTestDistance:Number.POSITIVE_INFINITY}),this.viewer._element.style.cursor="crosshair",this.viewer.enableCursorStyle=!0,this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.handler.setInputAction(c=>{let u=pe.getCatesian3FromPX(this.viewer,c.position);if(!u)return!1;i.length==0?(i.push(u.clone()),i.push(u),a.push(u.clone()),a.push(u),console.log(u),t=="point"?(l=this.drawPoint(new Cesium.CallbackProperty(function(){return i[0]},!1),r),o=1):t=="polyline"?(l=this.drawLine(new Cesium.CallbackProperty(function(){return i},!1),new Cesium.CallbackProperty(function(){return pe.getPolylineCenter(i)},!1),r),s=2):t=="polygon"?(l=this.drawPolygon(new Cesium.CallbackProperty(function(){return new Cesium.PolygonHierarchy(i)},!1),new Cesium.CallbackProperty(function(){return pe.getPolygonCenter(i)},!1),r),s=3):t=="marker"?(l=this.drawMarker(new Cesium.CallbackProperty(function(){return i[0]},!1),r),o=1):t=="model"?(l=this.drawModel(i[0],r),o=1):t=="circle"?(l=this.drawCircle(new Cesium.CallbackProperty(()=>i[0].clone(),!1),V(V({semiMinorAxis:new Cesium.CallbackProperty(()=>{let f=Cesium.Cartesian3.distance(a[0],a[1]);return(Math.max(.01,f)/1).toFixed(2)*1},!1),semiMajorAxis:new Cesium.CallbackProperty(()=>{let f=Cesium.Cartesian3.distance(a[0],a[1]);return(Math.max(.01,f)/1).toFixed(2)*1},!1)},r.clampToGround?{}:{height:new Cesium.CallbackProperty(()=>this.viewer.scene.globe.ellipsoid.cartesianToCartographic(i[0]).height,!1)}),r)),o=2,s=2):t=="rectangle"?(l=this.drawRectangle(new Cesium.CallbackProperty(function(){return Cesium.Rectangle.fromCartesianArray(i)},!1),new Cesium.CallbackProperty(function(){return pe.getPolygonCenter(i)},!1),r),o=2,s=2):t=="text"?(l=this.drawText(new Cesium.CallbackProperty(function(){return i[0]},!1),r),o=1):this.drawEnd(),o==1&&this.drawEnd(l,n)):(i.push(u),a.push(u.clone()),o&&i.length>=o&&this.drawEnd(l,n))},Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.setInputAction(c=>{let u=pe.getCatesian3FromPX(this.viewer,c.endPosition);if(!u)return!1;i.length>0&&(i.pop(),i.push(u),a.pop(),a.push(u.clone())),this.tipPosition=u},Cesium.ScreenSpaceEventType.MOUSE_MOVE),this.handler.setInputAction(c=>{i.length==0?this.drawEnd():i.length<=s?(this.remove(l),this.drawEnd()):(i.pop(),this.drawEnd(l,n))},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}drawEnd(t,r){this.handler&&!this.handler.isDestroyed()&&(this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK),this.handler.destroy()),this.remove(this.tipEntity),this.isDrawing=!1,this.viewer._element.style.cursor="default",this.viewer.enableCursorStyle=!1,console.log(t),t&&this.emit("add",t),t&&typeof r=="function"&&r(t),Dt.emit("drawEnd",{from:"Drawer"})}toJson(){let t=[];return this.entities.forEach(r=>{t.push(pe.entity2Json(r))}),t}doubleClick(t){new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas).setInputAction(n=>{let i=this.viewer.scene.drillPick(n.position);if(!i||i.length==0)return;let a=i[0].id;a&&(!a||!this.entities.includes(a)||t&&t(a))},Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)}dragEntity(t){var r=null,n=this.viewer,i,a;(!this.dragHandler||this.dragHandler.isDestroyed())&&(this.dragHandler=new Cesium.ScreenSpaceEventHandler(n.scene.canvas)),i=this.dragHandler,i.setInputAction(l=>{if(this.isDrawing||!this.options.draggable)return;let o=n.scene.drillPick(l.position);!o||o.length==0||(r=o[0].primitive,console.log(r,r._boundingSpheresKeys),console.log(r instanceof Cesium.GroundPrimitive),r&&(r=r.id||r instanceof Cesium.GroundPrimitive&&r._boundingSpheresKeys[0],!(!r||!this.entities.includes(r))&&(this.select(r),pe.getCatesian3FromPX(this.viewer,l.position),n.scene.screenSpaceCameraController.enableRotate=!1,n.scene.screenSpaceCameraController.enableInputs=!1,i.setInputAction(function(){n.scene.screenSpaceCameraController.enableInputs=!0,n.scene.screenSpaceCameraController.enableRotate=!0,t&&(t(r,a),r=null),i.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP),i.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE)},Cesium.ScreenSpaceEventType.LEFT_UP),i.setInputAction(s=>{if(r!=null){let c=pe.getCatesian3FromPX(this.viewer,s.startPosition),u=pe.getCatesian3FromPX(this.viewer,s.endPosition);if(!c||!u)return;let f=u.x-c.x,d=u.y-c.y,h=u.z-c.z;if(r.position&&(r.position=new Cesium.CallbackProperty(function(){return u},!1)),r.polyline){let m=r.polyline.positions.getValue();for(let g=0;g<m.length;g++)m[g].x=m[g].x+f,m[g].y=m[g].y+d,m[g].z=m[g].z+h;r.polyline.positions=new Cesium.CallbackProperty(function(){return m},!1)}if(r.polygon){let m=r.polygon.hierarchy.getValue(),g=m.positions,v=m.holes;for(let p=0;p<g.length;p++)g[p].x=g[p].x+f,g[p].y=g[p].y+d,g[p].z=g[p].z+h;for(let p=0;p<v.length;p++)v[p].x=v[p].x+f,v[p].y=v[p].y+d,v[p].z=v[p].z+h;r.polygon.hierarchy=new Cesium.CallbackProperty(function(){return new Cesium.PolygonHierarchy(g,v)},!1)}if(r.rectangle){let m=r.rectangle.coordinates.getValue(),g=r.rectangle.height.getValue(),v=new Cesium.Cartesian3.fromRadiansArrayHeights([m.west,m.north,g,m.east,m.south,g]);for(let p=0;p<v.length;p++)v[p].x=v[p].x+f,v[p].y=v[p].y+d,v[p].z=v[p].z+h;r.rectangle.coordinates=new Cesium.CallbackProperty(function(){return Cesium.Rectangle.fromCartesianArray(v)},!1)}if(r.ellipse){let m=u,v=Cesium.Cartographic.fromCartesian(m).height;r.ellipse.height=new Cesium.CallbackProperty(function(){return v},!1)}}},Cesium.ScreenSpaceEventType.MOUSE_MOVE))))},Cesium.ScreenSpaceEventType.LEFT_DOWN)}}function ir(e){"@babel/helpers - typeof";return ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ir(e)}var rs=/^\s+/,ns=/\s+$/;function w(e,t){if(e=e||"",t=t||{},e instanceof w)return e;if(!(this instanceof w))return new w(e,t);var r=is(e);this._originalInput=e,this._r=r.r,this._g=r.g,this._b=r.b,this._a=r.a,this._roundA=Math.round(100*this._a)/100,this._format=t.format||r.format,this._gradientType=t.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=r.ok}w.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(t.r*299+t.g*587+t.b*114)/1e3},getLuminance:function(){var t=this.toRgb(),r,n,i,a,l,o;return r=t.r/255,n=t.g/255,i=t.b/255,r<=.03928?a=r/12.92:a=Math.pow((r+.055)/1.055,2.4),n<=.03928?l=n/12.92:l=Math.pow((n+.055)/1.055,2.4),i<=.03928?o=i/12.92:o=Math.pow((i+.055)/1.055,2.4),.2126*a+.7152*l+.0722*o},setAlpha:function(t){return this._a=ma(t),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var t=ai(this._r,this._g,this._b);return{h:t.h*360,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=ai(this._r,this._g,this._b),r=Math.round(t.h*360),n=Math.round(t.s*100),i=Math.round(t.v*100);return this._a==1?"hsv("+r+", "+n+"%, "+i+"%)":"hsva("+r+", "+n+"%, "+i+"%, "+this._roundA+")"},toHsl:function(){var t=ii(this._r,this._g,this._b);return{h:t.h*360,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=ii(this._r,this._g,this._b),r=Math.round(t.h*360),n=Math.round(t.s*100),i=Math.round(t.l*100);return this._a==1?"hsl("+r+", "+n+"%, "+i+"%)":"hsla("+r+", "+n+"%, "+i+"%, "+this._roundA+")"},toHex:function(t){return oi(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return ls(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return this._a==1?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(U(this._r,255)*100)+"%",g:Math.round(U(this._g,255)*100)+"%",b:Math.round(U(this._b,255)*100)+"%",a:this._a}},toPercentageRgbString:function(){return this._a==1?"rgb("+Math.round(U(this._r,255)*100)+"%, "+Math.round(U(this._g,255)*100)+"%, "+Math.round(U(this._b,255)*100)+"%)":"rgba("+Math.round(U(this._r,255)*100)+"%, "+Math.round(U(this._g,255)*100)+"%, "+Math.round(U(this._b,255)*100)+"%, "+this._roundA+")"},toName:function(){return this._a===0?"transparent":this._a<1?!1:Cs[oi(this._r,this._g,this._b,!0)]||!1},toFilter:function(t){var r="#"+si(this._r,this._g,this._b,this._a),n=r,i=this._gradientType?"GradientType = 1, ":"";if(t){var a=w(t);n="#"+si(a._r,a._g,a._b,a._a)}return"progid:DXImageTransform.Microsoft.gradient("+i+"startColorstr="+r+",endColorstr="+n+")"},toString:function(t){var r=!!t;t=t||this._format;var n=!1,i=this._a<1&&this._a>=0,a=!r&&i&&(t==="hex"||t==="hex6"||t==="hex3"||t==="hex4"||t==="hex8"||t==="name");return a?t==="name"&&this._a===0?this.toName():this.toRgbString():(t==="rgb"&&(n=this.toRgbString()),t==="prgb"&&(n=this.toPercentageRgbString()),(t==="hex"||t==="hex6")&&(n=this.toHexString()),t==="hex3"&&(n=this.toHexString(!0)),t==="hex4"&&(n=this.toHex8String(!0)),t==="hex8"&&(n=this.toHex8String()),t==="name"&&(n=this.toName()),t==="hsl"&&(n=this.toHslString()),t==="hsv"&&(n=this.toHsvString()),n||this.toHexString())},clone:function(){return w(this.toString())},_applyModification:function(t,r){var n=t.apply(null,[this].concat([].slice.call(r)));return this._r=n._r,this._g=n._g,this._b=n._b,this.setAlpha(n._a),this},lighten:function(){return this._applyModification(ds,arguments)},brighten:function(){return this._applyModification(hs,arguments)},darken:function(){return this._applyModification(ps,arguments)},desaturate:function(){return this._applyModification(us,arguments)},saturate:function(){return this._applyModification(cs,arguments)},greyscale:function(){return this._applyModification(fs,arguments)},spin:function(){return this._applyModification(gs,arguments)},_applyCombination:function(t,r){return t.apply(null,[this].concat([].slice.call(r)))},analogous:function(){return this._applyCombination(ys,arguments)},complement:function(){return this._applyCombination(vs,arguments)},monochromatic:function(){return this._applyCombination(bs,arguments)},splitcomplement:function(){return this._applyCombination(ms,arguments)},triad:function(){return this._applyCombination(li,[3])},tetrad:function(){return this._applyCombination(li,[4])}};w.fromRatio=function(e,t){if(ir(e)=="object"){var r={};for(var n in e)e.hasOwnProperty(n)&&(n==="a"?r[n]=e[n]:r[n]=It(e[n]));e=r}return w(e,t)};function is(e){var t={r:0,g:0,b:0},r=1,n=null,i=null,a=null,l=!1,o=!1;return typeof e=="string"&&(e=_s(e)),ir(e)=="object"&&(Le(e.r)&&Le(e.g)&&Le(e.b)?(t=as(e.r,e.g,e.b),l=!0,o=String(e.r).substr(-1)==="%"?"prgb":"rgb"):Le(e.h)&&Le(e.s)&&Le(e.v)?(n=It(e.s),i=It(e.v),t=ss(e.h,n,i),l=!0,o="hsv"):Le(e.h)&&Le(e.s)&&Le(e.l)&&(n=It(e.s),a=It(e.l),t=os(e.h,n,a),l=!0,o="hsl"),e.hasOwnProperty("a")&&(r=e.a)),r=ma(r),{ok:l,format:e.format||o,r:Math.min(255,Math.max(t.r,0)),g:Math.min(255,Math.max(t.g,0)),b:Math.min(255,Math.max(t.b,0)),a:r}}function as(e,t,r){return{r:U(e,255)*255,g:U(t,255)*255,b:U(r,255)*255}}function ii(e,t,r){e=U(e,255),t=U(t,255),r=U(r,255);var n=Math.max(e,t,r),i=Math.min(e,t,r),a,l,o=(n+i)/2;if(n==i)a=l=0;else{var s=n-i;switch(l=o>.5?s/(2-n-i):s/(n+i),n){case e:a=(t-r)/s+(t<r?6:0);break;case t:a=(r-e)/s+2;break;case r:a=(e-t)/s+4;break}a/=6}return{h:a,s:l,l:o}}function os(e,t,r){var n,i,a;e=U(e,360),t=U(t,100),r=U(r,100);function l(c,u,f){return f<0&&(f+=1),f>1&&(f-=1),f<1/6?c+(u-c)*6*f:f<1/2?u:f<2/3?c+(u-c)*(2/3-f)*6:c}if(t===0)n=i=a=r;else{var o=r<.5?r*(1+t):r+t-r*t,s=2*r-o;n=l(s,o,e+1/3),i=l(s,o,e),a=l(s,o,e-1/3)}return{r:n*255,g:i*255,b:a*255}}function ai(e,t,r){e=U(e,255),t=U(t,255),r=U(r,255);var n=Math.max(e,t,r),i=Math.min(e,t,r),a,l,o=n,s=n-i;if(l=n===0?0:s/n,n==i)a=0;else{switch(n){case e:a=(t-r)/s+(t<r?6:0);break;case t:a=(r-e)/s+2;break;case r:a=(e-t)/s+4;break}a/=6}return{h:a,s:l,v:o}}function ss(e,t,r){e=U(e,360)*6,t=U(t,100),r=U(r,100);var n=Math.floor(e),i=e-n,a=r*(1-t),l=r*(1-i*t),o=r*(1-(1-i)*t),s=n%6,c=[r,l,a,a,o,r][s],u=[o,r,r,l,a,a][s],f=[a,a,o,r,r,l][s];return{r:c*255,g:u*255,b:f*255}}function oi(e,t,r,n){var i=[Te(Math.round(e).toString(16)),Te(Math.round(t).toString(16)),Te(Math.round(r).toString(16))];return n&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function ls(e,t,r,n,i){var a=[Te(Math.round(e).toString(16)),Te(Math.round(t).toString(16)),Te(Math.round(r).toString(16)),Te(ya(n))];return i&&a[0].charAt(0)==a[0].charAt(1)&&a[1].charAt(0)==a[1].charAt(1)&&a[2].charAt(0)==a[2].charAt(1)&&a[3].charAt(0)==a[3].charAt(1)?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0)+a[3].charAt(0):a.join("")}function si(e,t,r,n){var i=[Te(ya(n)),Te(Math.round(e).toString(16)),Te(Math.round(t).toString(16)),Te(Math.round(r).toString(16))];return i.join("")}w.equals=function(e,t){return!e||!t?!1:w(e).toRgbString()==w(t).toRgbString()};w.random=function(){return w.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})};function us(e,t){t=t===0?0:t||10;var r=w(e).toHsl();return r.s-=t/100,r.s=yr(r.s),w(r)}function cs(e,t){t=t===0?0:t||10;var r=w(e).toHsl();return r.s+=t/100,r.s=yr(r.s),w(r)}function fs(e){return w(e).desaturate(100)}function ds(e,t){t=t===0?0:t||10;var r=w(e).toHsl();return r.l+=t/100,r.l=yr(r.l),w(r)}function hs(e,t){t=t===0?0:t||10;var r=w(e).toRgb();return r.r=Math.max(0,Math.min(255,r.r-Math.round(255*-(t/100)))),r.g=Math.max(0,Math.min(255,r.g-Math.round(255*-(t/100)))),r.b=Math.max(0,Math.min(255,r.b-Math.round(255*-(t/100)))),w(r)}function ps(e,t){t=t===0?0:t||10;var r=w(e).toHsl();return r.l-=t/100,r.l=yr(r.l),w(r)}function gs(e,t){var r=w(e).toHsl(),n=(r.h+t)%360;return r.h=n<0?360+n:n,w(r)}function vs(e){var t=w(e).toHsl();return t.h=(t.h+180)%360,w(t)}function li(e,t){if(isNaN(t)||t<=0)throw new Error("Argument to polyad must be a positive number");for(var r=w(e).toHsl(),n=[w(e)],i=360/t,a=1;a<t;a++)n.push(w({h:(r.h+a*i)%360,s:r.s,l:r.l}));return n}function ms(e){var t=w(e).toHsl(),r=t.h;return[w(e),w({h:(r+72)%360,s:t.s,l:t.l}),w({h:(r+216)%360,s:t.s,l:t.l})]}function ys(e,t,r){t=t||6,r=r||30;var n=w(e).toHsl(),i=360/r,a=[w(e)];for(n.h=(n.h-(i*t>>1)+720)%360;--t;)n.h=(n.h+i)%360,a.push(w(n));return a}function bs(e,t){t=t||6;for(var r=w(e).toHsv(),n=r.h,i=r.s,a=r.v,l=[],o=1/t;t--;)l.push(w({h:n,s:i,v:a})),a=(a+o)%1;return l}w.mix=function(e,t,r){r=r===0?0:r||50;var n=w(e).toRgb(),i=w(t).toRgb(),a=r/100,l={r:(i.r-n.r)*a+n.r,g:(i.g-n.g)*a+n.g,b:(i.b-n.b)*a+n.b,a:(i.a-n.a)*a+n.a};return w(l)};w.readability=function(e,t){var r=w(e),n=w(t);return(Math.max(r.getLuminance(),n.getLuminance())+.05)/(Math.min(r.getLuminance(),n.getLuminance())+.05)};w.isReadable=function(e,t,r){var n=w.readability(e,t),i,a;switch(a=!1,i=ks(r),i.level+i.size){case"AAsmall":case"AAAlarge":a=n>=4.5;break;case"AAlarge":a=n>=3;break;case"AAAsmall":a=n>=7;break}return a};w.mostReadable=function(e,t,r){var n=null,i=0,a,l,o,s;r=r||{},l=r.includeFallbackColors,o=r.level,s=r.size;for(var c=0;c<t.length;c++)a=w.readability(e,t[c]),a>i&&(i=a,n=w(t[c]));return w.isReadable(e,n,{level:o,size:s})||!l?n:(r.includeFallbackColors=!1,w.mostReadable(e,["#fff","#000"],r))};var Jr=w.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},Cs=w.hexNames=ws(Jr);function ws(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(t[e[r]]=r);return t}function ma(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function U(e,t){xs(e)&&(e="100%");var r=Ss(e);return e=Math.min(t,Math.max(0,parseFloat(e))),r&&(e=parseInt(e*t,10)/100),Math.abs(e-t)<1e-6?1:e%t/parseFloat(t)}function yr(e){return Math.min(1,Math.max(0,e))}function ye(e){return parseInt(e,16)}function xs(e){return typeof e=="string"&&e.indexOf(".")!=-1&&parseFloat(e)===1}function Ss(e){return typeof e=="string"&&e.indexOf("%")!=-1}function Te(e){return e.length==1?"0"+e:""+e}function It(e){return e<=1&&(e=e*100+"%"),e}function ya(e){return Math.round(parseFloat(e)*255).toString(16)}function ui(e){return ye(e)/255}var Me=function(){var e="[-\\+]?\\d+%?",t="[-\\+]?\\d*\\.\\d+%?",r="(?:"+t+")|(?:"+e+")",n="[\\s|\\(]+("+r+")[,|\\s]+("+r+")[,|\\s]+("+r+")\\s*\\)?",i="[\\s|\\(]+("+r+")[,|\\s]+("+r+")[,|\\s]+("+r+")[,|\\s]+("+r+")\\s*\\)?";return{CSS_UNIT:new RegExp(r),rgb:new RegExp("rgb"+n),rgba:new RegExp("rgba"+i),hsl:new RegExp("hsl"+n),hsla:new RegExp("hsla"+i),hsv:new RegExp("hsv"+n),hsva:new RegExp("hsva"+i),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/}}();function Le(e){return!!Me.CSS_UNIT.exec(e)}function _s(e){e=e.replace(rs,"").replace(ns,"").toLowerCase();var t=!1;if(Jr[e])e=Jr[e],t=!0;else if(e=="transparent")return{r:0,g:0,b:0,a:0,format:"name"};var r;return(r=Me.rgb.exec(e))?{r:r[1],g:r[2],b:r[3]}:(r=Me.rgba.exec(e))?{r:r[1],g:r[2],b:r[3],a:r[4]}:(r=Me.hsl.exec(e))?{h:r[1],s:r[2],l:r[3]}:(r=Me.hsla.exec(e))?{h:r[1],s:r[2],l:r[3],a:r[4]}:(r=Me.hsv.exec(e))?{h:r[1],s:r[2],v:r[3]}:(r=Me.hsva.exec(e))?{h:r[1],s:r[2],v:r[3],a:r[4]}:(r=Me.hex8.exec(e))?{r:ye(r[1]),g:ye(r[2]),b:ye(r[3]),a:ui(r[4]),format:t?"name":"hex8"}:(r=Me.hex6.exec(e))?{r:ye(r[1]),g:ye(r[2]),b:ye(r[3]),format:t?"name":"hex"}:(r=Me.hex4.exec(e))?{r:ye(r[1]+""+r[1]),g:ye(r[2]+""+r[2]),b:ye(r[3]+""+r[3]),a:ui(r[4]+""+r[4]),format:t?"name":"hex8"}:(r=Me.hex3.exec(e))?{r:ye(r[1]+""+r[1]),g:ye(r[2]+""+r[2]),b:ye(r[3]+""+r[3]),format:t?"name":"hex"}:!1}function ks(e){var t,r;return e=e||{level:"AA",size:"small"},t=(e.level||"AA").toUpperCase(),r=(e.size||"small").toLowerCase(),t!=="AA"&&t!=="AAA"&&(t="AA"),r!=="small"&&r!=="large"&&(r="small"),{level:t,size:r}}var Yt={},ci;function Es(){if(ci)return Yt;ci=1;var e=e||{};e.stringify=function(){var t={"visit_linear-gradient":function(r){return t.visit_gradient(r)},"visit_repeating-linear-gradient":function(r){return t.visit_gradient(r)},"visit_radial-gradient":function(r){return t.visit_gradient(r)},"visit_repeating-radial-gradient":function(r){return t.visit_gradient(r)},visit_gradient:function(r){var n=t.visit(r.orientation);return n&&(n+=", "),r.type+"("+n+t.visit(r.colorStops)+")"},visit_shape:function(r){var n=r.value,i=t.visit(r.at),a=t.visit(r.style);return a&&(n+=" "+a),i&&(n+=" at "+i),n},"visit_default-radial":function(r){var n="",i=t.visit(r.at);return i&&(n+=i),n},"visit_extent-keyword":function(r){var n=r.value,i=t.visit(r.at);return i&&(n+=" at "+i),n},"visit_position-keyword":function(r){return r.value},visit_position:function(r){return t.visit(r.value.x)+" "+t.visit(r.value.y)},"visit_%":function(r){return r.value+"%"},visit_em:function(r){return r.value+"em"},visit_px:function(r){return r.value+"px"},visit_calc:function(r){return"calc("+r.value+")"},visit_literal:function(r){return t.visit_color(r.value,r)},visit_hex:function(r){return t.visit_color("#"+r.value,r)},visit_rgb:function(r){return t.visit_color("rgb("+r.value.join(", ")+")",r)},visit_rgba:function(r){return t.visit_color("rgba("+r.value.join(", ")+")",r)},visit_hsl:function(r){return t.visit_color("hsl("+r.value[0]+", "+r.value[1]+"%, "+r.value[2]+"%)",r)},visit_hsla:function(r){return t.visit_color("hsla("+r.value[0]+", "+r.value[1]+"%, "+r.value[2]+"%, "+r.value[3]+")",r)},visit_var:function(r){return t.visit_color("var("+r.value+")",r)},visit_color:function(r,n){var i=r,a=t.visit(n.length);return a&&(i+=" "+a),i},visit_angular:function(r){return r.value+"deg"},visit_directional:function(r){return"to "+r.value},visit_array:function(r){var n="",i=r.length;return r.forEach(function(a,l){n+=t.visit(a),l<i-1&&(n+=", ")}),n},visit_object:function(r){return r.width&&r.height?t.visit(r.width)+" "+t.visit(r.height):""},visit:function(r){if(!r)return"";if(r instanceof Array)return t.visit_array(r);if(typeof r=="object"&&!r.type)return t.visit_object(r);if(r.type){var n=t["visit_"+r.type];if(n)return n(r);throw Error("Missing visitor visit_"+r.type)}else throw Error("Invalid node.")}};return function(r){return t.visit(r)}}();var e=e||{};return e.parse=function(){var t={linearGradient:/^(\-(webkit|o|ms|moz)\-)?(linear\-gradient)/i,repeatingLinearGradient:/^(\-(webkit|o|ms|moz)\-)?(repeating\-linear\-gradient)/i,radialGradient:/^(\-(webkit|o|ms|moz)\-)?(radial\-gradient)/i,repeatingRadialGradient:/^(\-(webkit|o|ms|moz)\-)?(repeating\-radial\-gradient)/i,sideOrCorner:/^to (left (top|bottom)|right (top|bottom)|top (left|right)|bottom (left|right)|left|right|top|bottom)/i,extentKeywords:/^(closest\-side|closest\-corner|farthest\-side|farthest\-corner|contain|cover)/,positionKeywords:/^(left|center|right|top|bottom)/i,pixelValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))px/,percentageValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))\%/,emValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))em/,angleValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))deg/,radianValue:/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))rad/,startCall:/^\(/,endCall:/^\)/,comma:/^,/,hexColor:/^\#([0-9a-fA-F]+)/,literalColor:/^([a-zA-Z]+)/,rgbColor:/^rgb/i,rgbaColor:/^rgba/i,varColor:/^var/i,calcValue:/^calc/i,variableName:/^(--[a-zA-Z0-9-,\s\#]+)/,number:/^(([0-9]*\.[0-9]+)|([0-9]+\.?))/,hslColor:/^hsl/i,hslaColor:/^hsla/i},r="";function n(_){var O=new Error(r+": "+_);throw O.source=r,O}function i(){var _=a();return r.length>0&&n("Invalid input not EOF"),_}function a(){return S(l)}function l(){return o("linear-gradient",t.linearGradient,c)||o("repeating-linear-gradient",t.repeatingLinearGradient,c)||o("radial-gradient",t.radialGradient,d)||o("repeating-radial-gradient",t.repeatingRadialGradient,d)}function o(_,O,P){return s(O,function(G){var de=P();return de&&(F(t.comma)||n("Missing comma before color stops")),{type:_,orientation:de,colorStops:S(E)}})}function s(_,O){var P=F(_);if(P){F(t.startCall)||n("Missing (");var G=O(P);return F(t.endCall)||n("Missing )"),G}}function c(){var _=u();if(_)return _;var O=Y("position-keyword",t.positionKeywords,1);return O?{type:"directional",value:O.value}:f()}function u(){return Y("directional",t.sideOrCorner,1)}function f(){return Y("angular",t.angleValue,1)||Y("angular",t.radianValue,1)}function d(){var _,O=h(),P;return O&&(_=[],_.push(O),P=r,F(t.comma)&&(O=h(),O?_.push(O):r=P)),_}function h(){var _=m()||g();if(_)_.at=p();else{var O=v();if(O){_=O;var P=p();P&&(_.at=P)}else{var G=p();if(G)_={type:"default-radial",at:G};else{var de=C();de&&(_={type:"default-radial",at:de})}}}return _}function m(){var _=Y("shape",/^(circle)/i,0);return _&&(_.style=xe()||v()),_}function g(){var _=Y("shape",/^(ellipse)/i,0);return _&&(_.style=C()||X()||v()),_}function v(){return Y("extent-keyword",t.extentKeywords,1)}function p(){if(Y("position",/^at/,0)){var _=C();return _||n("Missing positioning value"),_}}function C(){var _=x();if(_.x||_.y)return{type:"position",value:_}}function x(){return{x:X(),y:X()}}function S(_){var O=_(),P=[];if(O)for(P.push(O);F(t.comma);)O=_(),O?P.push(O):n("One extra comma");return P}function E(){var _=k();return _||n("Expected color definition"),_.length=X(),_}function k(){return D()||H()||R()||N()||L()||y()||T()}function T(){return Y("literal",t.literalColor,0)}function D(){return Y("hex",t.hexColor,1)}function L(){return s(t.rgbColor,function(){return{type:"rgb",value:S(B)}})}function N(){return s(t.rgbaColor,function(){return{type:"rgba",value:S(B)}})}function y(){return s(t.varColor,function(){return{type:"var",value:j()}})}function R(){return s(t.hslColor,function(){var _=F(t.percentageValue);_&&n("HSL hue value must be a number in degrees (0-360) or normalized (-360 to 360), not a percentage");var O=B();F(t.comma);var P=F(t.percentageValue),G=P?P[1]:null;F(t.comma),P=F(t.percentageValue);var de=P?P[1]:null;return(!G||!de)&&n("Expected percentage value for saturation and lightness in HSL"),{type:"hsl",value:[O,G,de]}})}function H(){return s(t.hslaColor,function(){var _=B();F(t.comma);var O=F(t.percentageValue),P=O?O[1]:null;F(t.comma),O=F(t.percentageValue);var G=O?O[1]:null;F(t.comma);var de=B();return(!P||!G)&&n("Expected percentage value for saturation and lightness in HSLA"),{type:"hsla",value:[_,P,G,de]}})}function j(){return F(t.variableName)[1]}function B(){return F(t.number)[1]}function X(){return Y("%",t.percentageValue,1)||se()||ze()||xe()}function se(){return Y("position-keyword",t.positionKeywords,1)}function ze(){return s(t.calcValue,function(){for(var _=1,O=0;_>0&&O<r.length;){var P=r.charAt(O);P==="("?_++:P===")"&&_--,O++}_>0&&n("Missing closing parenthesis in calc() expression");var G=r.substring(0,O-1);return Re(O-1),{type:"calc",value:G}})}function xe(){return Y("px",t.pixelValue,1)||Y("em",t.emValue,1)}function Y(_,O,P){var G=F(O);if(G)return{type:_,value:G[P]}}function F(_){var O,P;return P=/^[\n\r\t\s]+/.exec(r),P&&Re(P[0].length),O=_.exec(r),O&&Re(O[0].length),O}function Re(_){r=r.substr(_)}return function(_){return r=_.toString().trim(),r.endsWith(";")&&(r=r.slice(0,-1)),i()}}(),Yt.parse=e.parse,Yt.stringify=e.stringify,Yt}var fi=Es(),ve="top",Ee="bottom",Ae="right",me="left",dn="auto",Ut=[ve,Ee,Ae,me],vt="start",jt="end",As="clippingParents",ba="viewport",Mt="popper",Os="reference",di=Ut.reduce(function(e,t){return e.concat([t+"-"+vt,t+"-"+jt])},[]),Ca=[].concat(Ut,[dn]).reduce(function(e,t){return e.concat([t,t+"-"+vt,t+"-"+jt])},[]),Rs="beforeRead",Ms="read",Ts="afterRead",Ps="beforeMain",Hs="main",Is="afterMain",Ls="beforeWrite",Ds="write",$s="afterWrite",Bs=[Rs,Ms,Ts,Ps,Hs,Is,Ls,Ds,$s];function Ie(e){return e?(e.nodeName||"").toLowerCase():null}function be(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function tt(e){var t=be(e).Element;return e instanceof t||e instanceof Element}function _e(e){var t=be(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function hn(e){if(typeof ShadowRoot=="undefined")return!1;var t=be(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function Ns(e){var t=e.state;Object.keys(t.elements).forEach(function(r){var n=t.styles[r]||{},i=t.attributes[r]||{},a=t.elements[r];!_e(a)||!Ie(a)||(Object.assign(a.style,n),Object.keys(i).forEach(function(l){var o=i[l];o===!1?a.removeAttribute(l):a.setAttribute(l,o===!0?"":o)}))})}function Vs(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow),function(){Object.keys(t.elements).forEach(function(n){var i=t.elements[n],a=t.attributes[n]||{},l=Object.keys(t.styles.hasOwnProperty(n)?t.styles[n]:r[n]),o=l.reduce(function(s,c){return s[c]="",s},{});!_e(i)||!Ie(i)||(Object.assign(i.style,o),Object.keys(a).forEach(function(s){i.removeAttribute(s)}))})}}const js={name:"applyStyles",enabled:!0,phase:"write",fn:Ns,effect:Vs,requires:["computeStyles"]};function He(e){return e.split("-")[0]}var et=Math.max,ar=Math.min,mt=Math.round;function Zr(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function wa(){return!/^((?!chrome|android).)*safari/i.test(Zr())}function yt(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!1);var n=e.getBoundingClientRect(),i=1,a=1;t&&_e(e)&&(i=e.offsetWidth>0&&mt(n.width)/e.offsetWidth||1,a=e.offsetHeight>0&&mt(n.height)/e.offsetHeight||1);var l=tt(e)?be(e):window,o=l.visualViewport,s=!wa()&&r,c=(n.left+(s&&o?o.offsetLeft:0))/i,u=(n.top+(s&&o?o.offsetTop:0))/a,f=n.width/i,d=n.height/a;return{width:f,height:d,top:u,right:c+f,bottom:u+d,left:c,x:c,y:u}}function pn(e){var t=yt(e),r=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-r)<=1&&(r=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:r,height:n}}function xa(e,t){var r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&hn(r)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function Ve(e){return be(e).getComputedStyle(e)}function Fs(e){return["table","td","th"].indexOf(Ie(e))>=0}function Xe(e){return((tt(e)?e.ownerDocument:e.document)||window.document).documentElement}function br(e){return Ie(e)==="html"?e:e.assignedSlot||e.parentNode||(hn(e)?e.host:null)||Xe(e)}function hi(e){return!_e(e)||Ve(e).position==="fixed"?null:e.offsetParent}function zs(e){var t=/firefox/i.test(Zr()),r=/Trident/i.test(Zr());if(r&&_e(e)){var n=Ve(e);if(n.position==="fixed")return null}var i=br(e);for(hn(i)&&(i=i.host);_e(i)&&["html","body"].indexOf(Ie(i))<0;){var a=Ve(i);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||t&&a.willChange==="filter"||t&&a.filter&&a.filter!=="none")return i;i=i.parentNode}return null}function Gt(e){for(var t=be(e),r=hi(e);r&&Fs(r)&&Ve(r).position==="static";)r=hi(r);return r&&(Ie(r)==="html"||Ie(r)==="body"&&Ve(r).position==="static")?t:r||zs(e)||t}function gn(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function $t(e,t,r){return et(e,ar(t,r))}function Ws(e,t,r){var n=$t(e,t,r);return n>r?r:n}function Sa(){return{top:0,right:0,bottom:0,left:0}}function _a(e){return Object.assign({},Sa(),e)}function ka(e,t){return t.reduce(function(r,n){return r[n]=e,r},{})}var Us=function(t,r){return t=typeof t=="function"?t(Object.assign({},r.rects,{placement:r.placement})):t,_a(typeof t!="number"?t:ka(t,Ut))};function Gs(e){var t,r=e.state,n=e.name,i=e.options,a=r.elements.arrow,l=r.modifiersData.popperOffsets,o=He(r.placement),s=gn(o),c=[me,Ae].indexOf(o)>=0,u=c?"height":"width";if(!(!a||!l)){var f=Us(i.padding,r),d=pn(a),h=s==="y"?ve:me,m=s==="y"?Ee:Ae,g=r.rects.reference[u]+r.rects.reference[s]-l[s]-r.rects.popper[u],v=l[s]-r.rects.reference[s],p=Gt(a),C=p?s==="y"?p.clientHeight||0:p.clientWidth||0:0,x=g/2-v/2,S=f[h],E=C-d[u]-f[m],k=C/2-d[u]/2+x,T=$t(S,k,E),D=s;r.modifiersData[n]=(t={},t[D]=T,t.centerOffset=T-k,t)}}function Ks(e){var t=e.state,r=e.options,n=r.element,i=n===void 0?"[data-popper-arrow]":n;i!=null&&(typeof i=="string"&&(i=t.elements.popper.querySelector(i),!i)||xa(t.elements.popper,i)&&(t.elements.arrow=i))}const qs={name:"arrow",enabled:!0,phase:"main",fn:Gs,effect:Ks,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function bt(e){return e.split("-")[1]}var Xs={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ys(e,t){var r=e.x,n=e.y,i=t.devicePixelRatio||1;return{x:mt(r*i)/i||0,y:mt(n*i)/i||0}}function pi(e){var t,r=e.popper,n=e.popperRect,i=e.placement,a=e.variation,l=e.offsets,o=e.position,s=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,f=e.isFixed,d=l.x,h=d===void 0?0:d,m=l.y,g=m===void 0?0:m,v=typeof u=="function"?u({x:h,y:g}):{x:h,y:g};h=v.x,g=v.y;var p=l.hasOwnProperty("x"),C=l.hasOwnProperty("y"),x=me,S=ve,E=window;if(c){var k=Gt(r),T="clientHeight",D="clientWidth";if(k===be(r)&&(k=Xe(r),Ve(k).position!=="static"&&o==="absolute"&&(T="scrollHeight",D="scrollWidth")),k=k,i===ve||(i===me||i===Ae)&&a===jt){S=Ee;var L=f&&k===E&&E.visualViewport?E.visualViewport.height:k[T];g-=L-n.height,g*=s?1:-1}if(i===me||(i===ve||i===Ee)&&a===jt){x=Ae;var N=f&&k===E&&E.visualViewport?E.visualViewport.width:k[D];h-=N-n.width,h*=s?1:-1}}var y=Object.assign({position:o},c&&Xs),R=u===!0?Ys({x:h,y:g},be(r)):{x:h,y:g};if(h=R.x,g=R.y,s){var H;return Object.assign({},y,(H={},H[S]=C?"0":"",H[x]=p?"0":"",H.transform=(E.devicePixelRatio||1)<=1?"translate("+h+"px, "+g+"px)":"translate3d("+h+"px, "+g+"px, 0)",H))}return Object.assign({},y,(t={},t[S]=C?g+"px":"",t[x]=p?h+"px":"",t.transform="",t))}function Js(e){var t=e.state,r=e.options,n=r.gpuAcceleration,i=n===void 0?!0:n,a=r.adaptive,l=a===void 0?!0:a,o=r.roundOffsets,s=o===void 0?!0:o,c={placement:He(t.placement),variation:bt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,pi(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:l,roundOffsets:s})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,pi(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const Zs={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Js,data:{}};var Jt={passive:!0};function Qs(e){var t=e.state,r=e.instance,n=e.options,i=n.scroll,a=i===void 0?!0:i,l=n.resize,o=l===void 0?!0:l,s=be(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&c.forEach(function(u){u.addEventListener("scroll",r.update,Jt)}),o&&s.addEventListener("resize",r.update,Jt),function(){a&&c.forEach(function(u){u.removeEventListener("scroll",r.update,Jt)}),o&&s.removeEventListener("resize",r.update,Jt)}}const el={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Qs,data:{}};var tl={left:"right",right:"left",bottom:"top",top:"bottom"};function tr(e){return e.replace(/left|right|bottom|top/g,function(t){return tl[t]})}var rl={start:"end",end:"start"};function gi(e){return e.replace(/start|end/g,function(t){return rl[t]})}function vn(e){var t=be(e),r=t.pageXOffset,n=t.pageYOffset;return{scrollLeft:r,scrollTop:n}}function mn(e){return yt(Xe(e)).left+vn(e).scrollLeft}function nl(e,t){var r=be(e),n=Xe(e),i=r.visualViewport,a=n.clientWidth,l=n.clientHeight,o=0,s=0;if(i){a=i.width,l=i.height;var c=wa();(c||!c&&t==="fixed")&&(o=i.offsetLeft,s=i.offsetTop)}return{width:a,height:l,x:o+mn(e),y:s}}function il(e){var t,r=Xe(e),n=vn(e),i=(t=e.ownerDocument)==null?void 0:t.body,a=et(r.scrollWidth,r.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),l=et(r.scrollHeight,r.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),o=-n.scrollLeft+mn(e),s=-n.scrollTop;return Ve(i||r).direction==="rtl"&&(o+=et(r.clientWidth,i?i.clientWidth:0)-a),{width:a,height:l,x:o,y:s}}function yn(e){var t=Ve(e),r=t.overflow,n=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(r+i+n)}function Ea(e){return["html","body","#document"].indexOf(Ie(e))>=0?e.ownerDocument.body:_e(e)&&yn(e)?e:Ea(br(e))}function Bt(e,t){var r;t===void 0&&(t=[]);var n=Ea(e),i=n===((r=e.ownerDocument)==null?void 0:r.body),a=be(n),l=i?[a].concat(a.visualViewport||[],yn(n)?n:[]):n,o=t.concat(l);return i?o:o.concat(Bt(br(l)))}function Qr(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function al(e,t){var r=yt(e,!1,t==="fixed");return r.top=r.top+e.clientTop,r.left=r.left+e.clientLeft,r.bottom=r.top+e.clientHeight,r.right=r.left+e.clientWidth,r.width=e.clientWidth,r.height=e.clientHeight,r.x=r.left,r.y=r.top,r}function vi(e,t,r){return t===ba?Qr(nl(e,r)):tt(t)?al(t,r):Qr(il(Xe(e)))}function ol(e){var t=Bt(br(e)),r=["absolute","fixed"].indexOf(Ve(e).position)>=0,n=r&&_e(e)?Gt(e):e;return tt(n)?t.filter(function(i){return tt(i)&&xa(i,n)&&Ie(i)!=="body"}):[]}function sl(e,t,r,n){var i=t==="clippingParents"?ol(e):[].concat(t),a=[].concat(i,[r]),l=a[0],o=a.reduce(function(s,c){var u=vi(e,c,n);return s.top=et(u.top,s.top),s.right=ar(u.right,s.right),s.bottom=ar(u.bottom,s.bottom),s.left=et(u.left,s.left),s},vi(e,l,n));return o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}function Aa(e){var t=e.reference,r=e.element,n=e.placement,i=n?He(n):null,a=n?bt(n):null,l=t.x+t.width/2-r.width/2,o=t.y+t.height/2-r.height/2,s;switch(i){case ve:s={x:l,y:t.y-r.height};break;case Ee:s={x:l,y:t.y+t.height};break;case Ae:s={x:t.x+t.width,y:o};break;case me:s={x:t.x-r.width,y:o};break;default:s={x:t.x,y:t.y}}var c=i?gn(i):null;if(c!=null){var u=c==="y"?"height":"width";switch(a){case vt:s[c]=s[c]-(t[u]/2-r[u]/2);break;case jt:s[c]=s[c]+(t[u]/2-r[u]/2);break}}return s}function Ft(e,t){t===void 0&&(t={});var r=t,n=r.placement,i=n===void 0?e.placement:n,a=r.strategy,l=a===void 0?e.strategy:a,o=r.boundary,s=o===void 0?As:o,c=r.rootBoundary,u=c===void 0?ba:c,f=r.elementContext,d=f===void 0?Mt:f,h=r.altBoundary,m=h===void 0?!1:h,g=r.padding,v=g===void 0?0:g,p=_a(typeof v!="number"?v:ka(v,Ut)),C=d===Mt?Os:Mt,x=e.rects.popper,S=e.elements[m?C:d],E=sl(tt(S)?S:S.contextElement||Xe(e.elements.popper),s,u,l),k=yt(e.elements.reference),T=Aa({reference:k,element:x,placement:i}),D=Qr(Object.assign({},x,T)),L=d===Mt?D:k,N={top:E.top-L.top+p.top,bottom:L.bottom-E.bottom+p.bottom,left:E.left-L.left+p.left,right:L.right-E.right+p.right},y=e.modifiersData.offset;if(d===Mt&&y){var R=y[i];Object.keys(N).forEach(function(H){var j=[Ae,Ee].indexOf(H)>=0?1:-1,B=[ve,Ee].indexOf(H)>=0?"y":"x";N[H]+=R[B]*j})}return N}function ll(e,t){t===void 0&&(t={});var r=t,n=r.placement,i=r.boundary,a=r.rootBoundary,l=r.padding,o=r.flipVariations,s=r.allowedAutoPlacements,c=s===void 0?Ca:s,u=bt(n),f=u?o?di:di.filter(function(m){return bt(m)===u}):Ut,d=f.filter(function(m){return c.indexOf(m)>=0});d.length===0&&(d=f);var h=d.reduce(function(m,g){return m[g]=Ft(e,{placement:g,boundary:i,rootBoundary:a,padding:l})[He(g)],m},{});return Object.keys(h).sort(function(m,g){return h[m]-h[g]})}function ul(e){if(He(e)===dn)return[];var t=tr(e);return[gi(e),t,gi(t)]}function cl(e){var t=e.state,r=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var i=r.mainAxis,a=i===void 0?!0:i,l=r.altAxis,o=l===void 0?!0:l,s=r.fallbackPlacements,c=r.padding,u=r.boundary,f=r.rootBoundary,d=r.altBoundary,h=r.flipVariations,m=h===void 0?!0:h,g=r.allowedAutoPlacements,v=t.options.placement,p=He(v),C=p===v,x=s||(C||!m?[tr(v)]:ul(v)),S=[v].concat(x).reduce(function(O,P){return O.concat(He(P)===dn?ll(t,{placement:P,boundary:u,rootBoundary:f,padding:c,flipVariations:m,allowedAutoPlacements:g}):P)},[]),E=t.rects.reference,k=t.rects.popper,T=new Map,D=!0,L=S[0],N=0;N<S.length;N++){var y=S[N],R=He(y),H=bt(y)===vt,j=[ve,Ee].indexOf(R)>=0,B=j?"width":"height",X=Ft(t,{placement:y,boundary:u,rootBoundary:f,altBoundary:d,padding:c}),se=j?H?Ae:me:H?Ee:ve;E[B]>k[B]&&(se=tr(se));var ze=tr(se),xe=[];if(a&&xe.push(X[R]<=0),o&&xe.push(X[se]<=0,X[ze]<=0),xe.every(function(O){return O})){L=y,D=!1;break}T.set(y,xe)}if(D)for(var Y=m?3:1,F=function(P){var G=S.find(function(de){var Ye=T.get(de);if(Ye)return Ye.slice(0,P).every(function(kr){return kr})});if(G)return L=G,"break"},Re=Y;Re>0;Re--){var _=F(Re);if(_==="break")break}t.placement!==L&&(t.modifiersData[n]._skip=!0,t.placement=L,t.reset=!0)}}const fl={name:"flip",enabled:!0,phase:"main",fn:cl,requiresIfExists:["offset"],data:{_skip:!1}};function mi(e,t,r){return r===void 0&&(r={x:0,y:0}),{top:e.top-t.height-r.y,right:e.right-t.width+r.x,bottom:e.bottom-t.height+r.y,left:e.left-t.width-r.x}}function yi(e){return[ve,Ae,Ee,me].some(function(t){return e[t]>=0})}function dl(e){var t=e.state,r=e.name,n=t.rects.reference,i=t.rects.popper,a=t.modifiersData.preventOverflow,l=Ft(t,{elementContext:"reference"}),o=Ft(t,{altBoundary:!0}),s=mi(l,n),c=mi(o,i,a),u=yi(s),f=yi(c);t.modifiersData[r]={referenceClippingOffsets:s,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":f})}const hl={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:dl};function pl(e,t,r){var n=He(e),i=[me,ve].indexOf(n)>=0?-1:1,a=typeof r=="function"?r(Object.assign({},t,{placement:e})):r,l=a[0],o=a[1];return l=l||0,o=(o||0)*i,[me,Ae].indexOf(n)>=0?{x:o,y:l}:{x:l,y:o}}function gl(e){var t=e.state,r=e.options,n=e.name,i=r.offset,a=i===void 0?[0,0]:i,l=Ca.reduce(function(u,f){return u[f]=pl(f,t.rects,a),u},{}),o=l[t.placement],s=o.x,c=o.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=s,t.modifiersData.popperOffsets.y+=c),t.modifiersData[n]=l}const vl={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:gl};function ml(e){var t=e.state,r=e.name;t.modifiersData[r]=Aa({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}const yl={name:"popperOffsets",enabled:!0,phase:"read",fn:ml,data:{}};function bl(e){return e==="x"?"y":"x"}function Cl(e){var t=e.state,r=e.options,n=e.name,i=r.mainAxis,a=i===void 0?!0:i,l=r.altAxis,o=l===void 0?!1:l,s=r.boundary,c=r.rootBoundary,u=r.altBoundary,f=r.padding,d=r.tether,h=d===void 0?!0:d,m=r.tetherOffset,g=m===void 0?0:m,v=Ft(t,{boundary:s,rootBoundary:c,padding:f,altBoundary:u}),p=He(t.placement),C=bt(t.placement),x=!C,S=gn(p),E=bl(S),k=t.modifiersData.popperOffsets,T=t.rects.reference,D=t.rects.popper,L=typeof g=="function"?g(Object.assign({},t.rects,{placement:t.placement})):g,N=typeof L=="number"?{mainAxis:L,altAxis:L}:Object.assign({mainAxis:0,altAxis:0},L),y=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,R={x:0,y:0};if(k){if(a){var H,j=S==="y"?ve:me,B=S==="y"?Ee:Ae,X=S==="y"?"height":"width",se=k[S],ze=se+v[j],xe=se-v[B],Y=h?-D[X]/2:0,F=C===vt?T[X]:D[X],Re=C===vt?-D[X]:-T[X],_=t.elements.arrow,O=h&&_?pn(_):{width:0,height:0},P=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:Sa(),G=P[j],de=P[B],Ye=$t(0,T[X],O[X]),kr=x?T[X]/2-Y-Ye-G-N.mainAxis:F-Ye-G-N.mainAxis,lo=x?-T[X]/2+Y+Ye+de+N.mainAxis:Re+Ye+de+N.mainAxis,Er=t.elements.arrow&&Gt(t.elements.arrow),uo=Er?S==="y"?Er.clientTop||0:Er.clientLeft||0:0,Fn=(H=y==null?void 0:y[S])!=null?H:0,co=se+kr-Fn-uo,fo=se+lo-Fn,zn=$t(h?ar(ze,co):ze,se,h?et(xe,fo):xe);k[S]=zn,R[S]=zn-se}if(o){var Wn,ho=S==="x"?ve:me,po=S==="x"?Ee:Ae,Je=k[E],Xt=E==="y"?"height":"width",Un=Je+v[ho],Gn=Je-v[po],Ar=[ve,me].indexOf(p)!==-1,Kn=(Wn=y==null?void 0:y[E])!=null?Wn:0,qn=Ar?Un:Je-T[Xt]-D[Xt]-Kn+N.altAxis,Xn=Ar?Je+T[Xt]+D[Xt]-Kn-N.altAxis:Gn,Yn=h&&Ar?Ws(qn,Je,Xn):$t(h?qn:Un,Je,h?Xn:Gn);k[E]=Yn,R[E]=Yn-Je}t.modifiersData[n]=R}}const wl={name:"preventOverflow",enabled:!0,phase:"main",fn:Cl,requiresIfExists:["offset"]};function xl(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Sl(e){return e===be(e)||!_e(e)?vn(e):xl(e)}function _l(e){var t=e.getBoundingClientRect(),r=mt(t.width)/e.offsetWidth||1,n=mt(t.height)/e.offsetHeight||1;return r!==1||n!==1}function kl(e,t,r){r===void 0&&(r=!1);var n=_e(t),i=_e(t)&&_l(t),a=Xe(t),l=yt(e,i,r),o={scrollLeft:0,scrollTop:0},s={x:0,y:0};return(n||!n&&!r)&&((Ie(t)!=="body"||yn(a))&&(o=Sl(t)),_e(t)?(s=yt(t,!0),s.x+=t.clientLeft,s.y+=t.clientTop):a&&(s.x=mn(a))),{x:l.left+o.scrollLeft-s.x,y:l.top+o.scrollTop-s.y,width:l.width,height:l.height}}function El(e){var t=new Map,r=new Set,n=[];e.forEach(function(a){t.set(a.name,a)});function i(a){r.add(a.name);var l=[].concat(a.requires||[],a.requiresIfExists||[]);l.forEach(function(o){if(!r.has(o)){var s=t.get(o);s&&i(s)}}),n.push(a)}return e.forEach(function(a){r.has(a.name)||i(a)}),n}function Al(e){var t=El(e);return Bs.reduce(function(r,n){return r.concat(t.filter(function(i){return i.phase===n}))},[])}function Ol(e){var t;return function(){return t||(t=new Promise(function(r){Promise.resolve().then(function(){t=void 0,r(e())})})),t}}function Rl(e){var t=e.reduce(function(r,n){var i=r[n.name];return r[n.name]=i?Object.assign({},i,n,{options:Object.assign({},i.options,n.options),data:Object.assign({},i.data,n.data)}):n,r},{});return Object.keys(t).map(function(r){return t[r]})}var bi={placement:"bottom",modifiers:[],strategy:"absolute"};function Ci(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return!t.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function Ml(e){e===void 0&&(e={});var t=e,r=t.defaultModifiers,n=r===void 0?[]:r,i=t.defaultOptions,a=i===void 0?bi:i;return function(o,s,c){c===void 0&&(c=a);var u={placement:"bottom",orderedModifiers:[],options:Object.assign({},bi,a),modifiersData:{},elements:{reference:o,popper:s},attributes:{},styles:{}},f=[],d=!1,h={state:u,setOptions:function(p){var C=typeof p=="function"?p(u.options):p;g(),u.options=Object.assign({},a,u.options,C),u.scrollParents={reference:tt(o)?Bt(o):o.contextElement?Bt(o.contextElement):[],popper:Bt(s)};var x=Al(Rl([].concat(n,u.options.modifiers)));return u.orderedModifiers=x.filter(function(S){return S.enabled}),m(),h.update()},forceUpdate:function(){if(!d){var p=u.elements,C=p.reference,x=p.popper;if(Ci(C,x)){u.rects={reference:kl(C,Gt(x),u.options.strategy==="fixed"),popper:pn(x)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach(function(N){return u.modifiersData[N.name]=Object.assign({},N.data)});for(var S=0;S<u.orderedModifiers.length;S++){if(u.reset===!0){u.reset=!1,S=-1;continue}var E=u.orderedModifiers[S],k=E.fn,T=E.options,D=T===void 0?{}:T,L=E.name;typeof k=="function"&&(u=k({state:u,options:D,name:L,instance:h})||u)}}}},update:Ol(function(){return new Promise(function(v){h.forceUpdate(),v(u)})}),destroy:function(){g(),d=!0}};if(!Ci(o,s))return h;h.setOptions(c).then(function(v){!d&&c.onFirstUpdate&&c.onFirstUpdate(v)});function m(){u.orderedModifiers.forEach(function(v){var p=v.name,C=v.options,x=C===void 0?{}:C,S=v.effect;if(typeof S=="function"){var E=S({state:u,name:p,instance:h,options:x}),k=function(){};f.push(E||k)}})}function g(){f.forEach(function(v){return v()}),f=[]}return h}}var Tl=[el,yl,Zs,js,vl,fl,wl,qs,hl],Pl=Ml({defaultModifiers:Tl}),wi=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{};function bn(e){var t={exports:{}};return e(t,t.exports),t.exports}var Zt=function(e){return e&&e.Math==Math&&e},re=Zt(typeof globalThis=="object"&&globalThis)||Zt(typeof window=="object"&&window)||Zt(typeof self=="object"&&self)||Zt(typeof wi=="object"&&wi)||function(){return this}()||Function("return this")(),W=function(e){try{return!!e()}catch(t){return!0}},Se=!W(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7}),xi={}.propertyIsEnumerable,Si=Object.getOwnPropertyDescriptor,Hl={f:Si&&!xi.call({1:2},1)?function(e){var t=Si(this,e);return!!t&&t.enumerable}:xi},Cr=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},Il={}.toString,Ne=function(e){return Il.call(e).slice(8,-1)},Ll="".split,wr=W(function(){return!Object("z").propertyIsEnumerable(0)})?function(e){return Ne(e)=="String"?Ll.call(e,""):Object(e)}:Object,Ge=function(e){if(e==null)throw TypeError("Can't call method on "+e);return e},Et=function(e){return wr(Ge(e))},ae=function(e){return typeof e=="object"?e!==null:typeof e=="function"},Cn=function(e,t){if(!ae(e))return e;var r,n;if(t&&typeof(r=e.toString)=="function"&&!ae(n=r.call(e))||typeof(r=e.valueOf)=="function"&&!ae(n=r.call(e))||!t&&typeof(r=e.toString)=="function"&&!ae(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")},Dl={}.hasOwnProperty,Q=function(e,t){return Dl.call(e,t)},en=re.document,$l=ae(en)&&ae(en.createElement),Oa=function(e){return $l?en.createElement(e):{}},Ra=!Se&&!W(function(){return Object.defineProperty(Oa("div"),"a",{get:function(){return 7}}).a!=7}),_i=Object.getOwnPropertyDescriptor,wn={f:Se?_i:function(e,t){if(e=Et(e),t=Cn(t,!0),Ra)try{return _i(e,t)}catch(r){}if(Q(e,t))return Cr(!Hl.f.call(e,t),e[t])}},he=function(e){if(!ae(e))throw TypeError(String(e)+" is not an object");return e},ki=Object.defineProperty,je={f:Se?ki:function(e,t,r){if(he(e),t=Cn(t,!0),he(r),Ra)try{return ki(e,t,r)}catch(n){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},ke=Se?function(e,t,r){return je.f(e,t,Cr(1,r))}:function(e,t,r){return e[t]=r,e},xn=function(e,t){try{ke(re,e,t)}catch(r){re[e]=t}return t},rt=re["__core-js_shared__"]||xn("__core-js_shared__",{}),Bl=Function.toString;typeof rt.inspectSource!="function"&&(rt.inspectSource=function(e){return Bl.call(e)});var or,Nt,sr,Ma=rt.inspectSource,Ei=re.WeakMap,Nl=typeof Ei=="function"&&/native code/.test(Ma(Ei)),Ta=bn(function(e){(e.exports=function(t,r){return rt[t]||(rt[t]=r!==void 0?r:{})})("versions",[]).push({version:"3.8.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})}),Vl=0,jl=Math.random(),Sn=function(e){return"Symbol("+String(e===void 0?"":e)+")_"+(++Vl+jl).toString(36)},Ai=Ta("keys"),_n=function(e){return Ai[e]||(Ai[e]=Sn(e))},xr={},Fl=re.WeakMap;if(Nl){var ot=rt.state||(rt.state=new Fl),zl=ot.get,Wl=ot.has,Ul=ot.set;or=function(e,t){return t.facade=e,Ul.call(ot,e,t),t},Nt=function(e){return zl.call(ot,e)||{}},sr=function(e){return Wl.call(ot,e)}}else{var Tt=_n("state");xr[Tt]=!0,or=function(e,t){return t.facade=e,ke(e,Tt,t),t},Nt=function(e){return Q(e,Tt)?e[Tt]:{}},sr=function(e){return Q(e,Tt)}}var Ke={set:or,get:Nt,has:sr,enforce:function(e){return sr(e)?Nt(e):or(e,{})},getterFor:function(e){return function(t){var r;if(!ae(t)||(r=Nt(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return r}}},qe=bn(function(e){var t=Ke.get,r=Ke.enforce,n=String(String).split("String");(e.exports=function(i,a,l,o){var s,c=!!o&&!!o.unsafe,u=!!o&&!!o.enumerable,f=!!o&&!!o.noTargetGet;typeof l=="function"&&(typeof a!="string"||Q(l,"name")||ke(l,"name",a),(s=r(l)).source||(s.source=n.join(typeof a=="string"?a:""))),i!==re?(c?!f&&i[a]&&(u=!0):delete i[a],u?i[a]=l:ke(i,a,l)):u?i[a]=l:xn(a,l)})(Function.prototype,"toString",function(){return typeof this=="function"&&t(this).source||Ma(this)})}),Or=re,Oi=function(e){return typeof e=="function"?e:void 0},Sr=function(e,t){return arguments.length<2?Oi(Or[e])||Oi(re[e]):Or[e]&&Or[e][t]||re[e]&&re[e][t]},Gl=Math.ceil,Kl=Math.floor,At=function(e){return isNaN(e=+e)?0:(e>0?Kl:Gl)(e)},ql=Math.min,we=function(e){return e>0?ql(At(e),9007199254740991):0},Xl=Math.max,Yl=Math.min,lr=function(e,t){var r=At(e);return r<0?Xl(r+t,0):Yl(r,t)},Jl=function(e){return function(t,r,n){var i,a=Et(t),l=we(a.length),o=lr(n,l);if(e&&r!=r){for(;l>o;)if((i=a[o++])!=i)return!0}else for(;l>o;o++)if((e||o in a)&&a[o]===r)return e||o||0;return!e&&-1}},Pa={indexOf:Jl(!1)},Zl=Pa.indexOf,Ha=function(e,t){var r,n=Et(e),i=0,a=[];for(r in n)!Q(xr,r)&&Q(n,r)&&a.push(r);for(;t.length>i;)Q(n,r=t[i++])&&(~Zl(a,r)||a.push(r));return a},ur=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ql=ur.concat("length","prototype"),eu={f:Object.getOwnPropertyNames||function(e){return Ha(e,Ql)}},tu={f:Object.getOwnPropertySymbols},ru=Sr("Reflect","ownKeys")||function(e){var t=eu.f(he(e)),r=tu.f;return r?t.concat(r(e)):t},nu=function(e,t){for(var r=ru(t),n=je.f,i=wn.f,a=0;a<r.length;a++){var l=r[a];Q(e,l)||n(e,l,i(t,l))}},iu=/#|\.prototype\./,Kt=function(e,t){var r=ou[au(e)];return r==lu||r!=su&&(typeof t=="function"?W(t):!!t)},au=Kt.normalize=function(e){return String(e).replace(iu,".").toLowerCase()},ou=Kt.data={},su=Kt.NATIVE="N",lu=Kt.POLYFILL="P",tn=Kt,uu=wn.f,fe=function(e,t){var r,n,i,a,l,o=e.target,s=e.global,c=e.stat;if(r=s?re:c?re[o]||xn(o,{}):(re[o]||{}).prototype)for(n in t){if(a=t[n],i=e.noTargetGet?(l=uu(r,n))&&l.value:r[n],!tn(s?n:o+(c?".":"#")+n,e.forced)&&i!==void 0){if(typeof a==typeof i)continue;nu(a,i)}(e.sham||i&&i.sham)&&ke(a,"sham",!0),qe(r,n,a,e)}},kn=function(e,t){var r=[][e];return!!r&&W(function(){r.call(null,t||function(){throw 1},1)})},cu=Object.defineProperty,Rr={},Ri=function(e){throw e},Ot=function(e,t){if(Q(Rr,e))return Rr[e];t||(t={});var r=[][e],n=!!Q(t,"ACCESSORS")&&t.ACCESSORS,i=Q(t,0)?t[0]:Ri,a=Q(t,1)?t[1]:void 0;return Rr[e]=!!r&&!W(function(){if(n&&!Se)return!0;var l={length:-1};n?cu(l,1,{enumerable:!0,get:Ri}):l[1]=1,r.call(l,i,a)})},fu=Pa.indexOf,Ia=[].indexOf,Mi=!!Ia&&1/[1].indexOf(1,-0)<0,du=kn("indexOf"),hu=Ot("indexOf",{ACCESSORS:!0,1:0});function nt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function pu(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function it(e,t,r){return r&&pu(e,r),e}fe({target:"Array",proto:!0,forced:Mi||!du||!hu},{indexOf:function(e){return Mi?Ia.apply(this,arguments)||0:fu(this,e,arguments.length>1?arguments[1]:void 0)}});(function(){function e(){nt(this,e)}return it(e,null,[{key:"isInBrowser",value:function(){return typeof window!="undefined"}},{key:"isServer",value:function(){return typeof window=="undefined"}},{key:"getUA",value:function(){return e.isInBrowser()?window.navigator.userAgent.toLowerCase():""}},{key:"isMobile",value:function(){return/Mobile|mini|Fennec|Android|iP(ad|od|hone)/.test(navigator.appVersion)}},{key:"isOpera",value:function(){return navigator.userAgent.indexOf("Opera")!==-1}},{key:"isIE",value:function(){var t=e.getUA();return t!==""&&t.indexOf("msie")>0}},{key:"isIE9",value:function(){var t=e.getUA();return t!==""&&t.indexOf("msie 9.0")>0}},{key:"isEdge",value:function(){var t=e.getUA();return t!==""&&t.indexOf("edge/")>0}},{key:"isChrome",value:function(){var t=e.getUA();return t!==""&&/chrome\/\d+/.test(t)&&!e.isEdge()}},{key:"isPhantomJS",value:function(){var t=e.getUA();return t!==""&&/phantomjs/.test(t)}},{key:"isFirefox",value:function(){var t=e.getUA();return t!==""&&/firefox/.test(t)}}]),e})();var gu=[].join,vu=wr!=Object,mu=kn("join",",");fe({target:"Array",proto:!0,forced:vu||!mu},{join:function(e){return gu.call(Et(this),e===void 0?",":e)}});var st,cr,Fe=function(e){return Object(Ge(e))},Ct=Array.isArray||function(e){return Ne(e)=="Array"},La=!!Object.getOwnPropertySymbols&&!W(function(){return!String(Symbol())}),yu=La&&!Symbol.sham&&typeof Symbol.iterator=="symbol",Qt=Ta("wks"),Vt=re.Symbol,bu=yu?Vt:Vt&&Vt.withoutSetter||Sn,q=function(e){return Q(Qt,e)||(La&&Q(Vt,e)?Qt[e]=Vt[e]:Qt[e]=bu("Symbol."+e)),Qt[e]},Cu=q("species"),_r=function(e,t){var r;return Ct(e)&&(typeof(r=e.constructor)!="function"||r!==Array&&!Ct(r.prototype)?ae(r)&&(r=r[Cu])===null&&(r=void 0):r=void 0),new(r===void 0?Array:r)(t===0?0:t)},wt=function(e,t,r){var n=Cn(t);n in e?je.f(e,n,Cr(0,r)):e[n]=r},Mr=Sr("navigator","userAgent")||"",Ti=re.process,Pi=Ti&&Ti.versions,Hi=Pi&&Pi.v8;Hi?cr=(st=Hi.split("."))[0]+st[1]:Mr&&(!(st=Mr.match(/Edge\/(\d+)/))||st[1]>=74)&&(st=Mr.match(/Chrome\/(\d+)/))&&(cr=st[1]);var fr=cr&&+cr,wu=q("species"),En=function(e){return fr>=51||!W(function(){var t=[];return(t.constructor={})[wu]=function(){return{foo:1}},t[e](Boolean).foo!==1})},xu=En("splice"),Su=Ot("splice",{ACCESSORS:!0,0:0,1:2}),_u=Math.max,ku=Math.min;fe({target:"Array",proto:!0,forced:!xu||!Su},{splice:function(e,t){var r,n,i,a,l,o,s=Fe(this),c=we(s.length),u=lr(e,c),f=arguments.length;if(f===0?r=n=0:f===1?(r=0,n=c-u):(r=f-2,n=ku(_u(At(t),0),c-u)),c+r-n>9007199254740991)throw TypeError("Maximum allowed length exceeded");for(i=_r(s,n),a=0;a<n;a++)(l=u+a)in s&&wt(i,a,s[l]);if(i.length=n,r<n){for(a=u;a<c-n;a++)o=a+r,(l=a+n)in s?s[o]=s[l]:delete s[o];for(a=c;a>c-n+r;a--)delete s[a-1]}else if(r>n)for(a=c-n;a>u;a--)o=a+r-1,(l=a+n-1)in s?s[o]=s[l]:delete s[o];for(a=0;a<r;a++)s[a+u]=arguments[a+2];return s.length=c-n+r,i}});var Da={};Da[q("toStringTag")]="z";var An=String(Da)==="[object z]",Eu=q("toStringTag"),Au=Ne(function(){return arguments}())=="Arguments",$a=An?Ne:function(e){var t,r,n;return e===void 0?"Undefined":e===null?"Null":typeof(r=function(i,a){try{return i[a]}catch(l){}}(t=Object(e),Eu))=="string"?r:Au?Ne(t):(n=Ne(t))=="Object"&&typeof t.callee=="function"?"Arguments":n},Ou=An?{}.toString:function(){return"[object "+$a(this)+"]"};An||qe(Object.prototype,"toString",Ou,{unsafe:!0});var Ba=function(){var e=he(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t};function Ii(e,t){return RegExp(e,t)}var Tr,Pr,Li={UNSUPPORTED_Y:W(function(){var e=Ii("a","y");return e.lastIndex=2,e.exec("abcd")!=null}),BROKEN_CARET:W(function(){var e=Ii("^r","gy");return e.lastIndex=2,e.exec("str")!=null})},dr=RegExp.prototype.exec,Ru=String.prototype.replace,Na=dr,Hr=(Tr=/a/,Pr=/b*/g,dr.call(Tr,"a"),dr.call(Pr,"a"),Tr.lastIndex!==0||Pr.lastIndex!==0),Di=Li.UNSUPPORTED_Y||Li.BROKEN_CARET,Ir=/()??/.exec("")[1]!==void 0;(Hr||Ir||Di)&&(Na=function(e){var t,r,n,i,a=this,l=Di&&a.sticky,o=Ba.call(a),s=a.source,c=0,u=e;return l&&((o=o.replace("y","")).indexOf("g")===-1&&(o+="g"),u=String(e).slice(a.lastIndex),a.lastIndex>0&&(!a.multiline||a.multiline&&e[a.lastIndex-1]!==`
`)&&(s="(?: "+s+")",u=" "+u,c++),r=new RegExp("^(?:"+s+")",o)),Ir&&(r=new RegExp("^"+s+"$(?!\\s)",o)),Hr&&(t=a.lastIndex),n=dr.call(l?r:a,u),l?n?(n.input=n.input.slice(c),n[0]=n[0].slice(c),n.index=a.lastIndex,a.lastIndex+=n[0].length):a.lastIndex=0:Hr&&n&&(a.lastIndex=a.global?n.index+n[0].length:t),Ir&&n&&n.length>1&&Ru.call(n[0],r,function(){for(i=1;i<arguments.length-2;i++)arguments[i]===void 0&&(n[i]=void 0)}),n});var zt=Na;fe({target:"RegExp",proto:!0,forced:/./.exec!==zt},{exec:zt});var Va=RegExp.prototype,ja=Va.toString,Mu=W(function(){return ja.call({source:"a",flags:"b"})!="/a/b"}),Tu=ja.name!="toString";(Mu||Tu)&&qe(RegExp.prototype,"toString",function(){var e=he(this),t=String(e.source),r=e.flags;return"/"+t+"/"+String(r===void 0&&e instanceof RegExp&&!("flags"in Va)?Ba.call(e):r)},{unsafe:!0});var Pu=q("species"),Hu=!W(function(){var e=/./;return e.exec=function(){var t=[];return t.groups={a:"7"},t},"".replace(e,"$<a>")!=="7"}),$i="a".replace(/./,"$0")==="$0",Bi=q("replace"),Ni=!!/./[Bi]&&/./[Bi]("a","$0")==="",Iu=!W(function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var r="ab".split(e);return r.length!==2||r[0]!=="a"||r[1]!=="b"}),Fa=function(e,t,r,n){var i=q(e),a=!W(function(){var f={};return f[i]=function(){return 7},""[e](f)!=7}),l=a&&!W(function(){var f=!1,d=/a/;return e==="split"&&((d={}).constructor={},d.constructor[Pu]=function(){return d},d.flags="",d[i]=/./[i]),d.exec=function(){return f=!0,null},d[i](""),!f});if(!a||!l||e==="replace"&&(!Hu||!$i||Ni)||e==="split"&&!Iu){var o=/./[i],s=r(i,""[e],function(f,d,h,m,g){return d.exec===zt?a&&!g?{done:!0,value:o.call(d,h,m)}:{done:!0,value:f.call(h,d,m)}:{done:!1}},{REPLACE_KEEPS_$0:$i,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:Ni}),c=s[0],u=s[1];qe(String.prototype,e,c),qe(RegExp.prototype,i,function(f,d){return u.call(f,this,d)})}n&&ke(RegExp.prototype[i],"sham",!0)},Lu=q("match"),za=function(e){var t;return ae(e)&&((t=e[Lu])!==void 0?!!t:Ne(e)=="RegExp")},On=function(e){if(typeof e!="function")throw TypeError(String(e)+" is not a function");return e},Du=q("species"),$u=function(e){return function(t,r){var n,i,a=String(Ge(t)),l=At(r),o=a.length;return l<0||l>=o?e?"":void 0:(n=a.charCodeAt(l))<55296||n>56319||l+1===o||(i=a.charCodeAt(l+1))<56320||i>57343?e?a.charAt(l):n:e?a.slice(l,l+2):i-56320+(n-55296<<10)+65536}},Wa={charAt:$u(!0)},Bu=Wa.charAt,Ua=function(e,t,r){return t+(r?Bu(e,t).length:1)},rn=function(e,t){var r=e.exec;if(typeof r=="function"){var n=r.call(e,t);if(typeof n!="object")throw TypeError("RegExp exec method returned something other than an Object or null");return n}if(Ne(e)!=="RegExp")throw TypeError("RegExp#exec called on incompatible receiver");return zt.call(e,t)},Nu=[].push,Vu=Math.min,lt=!W(function(){return!RegExp(4294967295,"y")});Fa("split",2,function(e,t,r){var n;return n="abbc".split(/(b)*/)[1]=="c"||"test".split(/(?:)/,-1).length!=4||"ab".split(/(?:ab)*/).length!=2||".".split(/(.?)(.?)/).length!=4||".".split(/()()/).length>1||"".split(/.?/).length?function(i,a){var l=String(Ge(this)),o=a===void 0?4294967295:a>>>0;if(o===0)return[];if(i===void 0)return[l];if(!za(i))return t.call(l,i,o);for(var s,c,u,f=[],d=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(i.sticky?"y":""),h=0,m=new RegExp(i.source,d+"g");(s=zt.call(m,l))&&!((c=m.lastIndex)>h&&(f.push(l.slice(h,s.index)),s.length>1&&s.index<l.length&&Nu.apply(f,s.slice(1)),u=s[0].length,h=c,f.length>=o));)m.lastIndex===s.index&&m.lastIndex++;return h===l.length?!u&&m.test("")||f.push(""):f.push(l.slice(h)),f.length>o?f.slice(0,o):f}:"0".split(void 0,0).length?function(i,a){return i===void 0&&a===0?[]:t.call(this,i,a)}:t,[function(i,a){var l=Ge(this),o=i==null?void 0:i[e];return o!==void 0?o.call(i,l,a):n.call(String(l),i,a)},function(i,a){var l=r(n,i,this,a,n!==t);if(l.done)return l.value;var o=he(i),s=String(this),c=function(S,E){var k,T=he(S).constructor;return T===void 0||(k=he(T)[Du])==null?E:On(k)}(o,RegExp),u=o.unicode,f=(o.ignoreCase?"i":"")+(o.multiline?"m":"")+(o.unicode?"u":"")+(lt?"y":"g"),d=new c(lt?o:"^(?:"+o.source+")",f),h=a===void 0?4294967295:a>>>0;if(h===0)return[];if(s.length===0)return rn(d,s)===null?[s]:[];for(var m=0,g=0,v=[];g<s.length;){d.lastIndex=lt?g:0;var p,C=rn(d,lt?s:s.slice(g));if(C===null||(p=Vu(we(d.lastIndex+(lt?0:g)),s.length))===m)g=Ua(s,g,u);else{if(v.push(s.slice(m,g)),v.length===h)return v;for(var x=1;x<=C.length-1;x++)if(v.push(C[x]),v.length===h)return v;g=m=p}}return v.push(s.slice(m)),v}]},!lt);var nn=`	
\v\f\r                　\u2028\u2029\uFEFF`,hr="["+nn+"]",ju=RegExp("^"+hr+hr+"*"),Fu=RegExp(hr+hr+"*$"),zu=function(e){return function(t){var r=String(Ge(t));return 1&e&&(r=r.replace(ju,"")),2&e&&(r=r.replace(Fu,"")),r}},Wu={trim:zu(3)},Uu=Wu.trim;fe({target:"String",proto:!0,forced:function(e){return W(function(){return!!nn[e]()||"​᠎"[e]()!="​᠎"||nn[e].name!==e})}("trim")},{trim:function(){return Uu(this)}});var Gu=En("slice"),Ku=Ot("slice",{ACCESSORS:!0,0:0,1:2}),qu=q("species"),Xu=[].slice,Yu=Math.max;fe({target:"Array",proto:!0,forced:!Gu||!Ku},{slice:function(e,t){var r,n,i,a=Et(this),l=we(a.length),o=lr(e,l),s=lr(t===void 0?l:t,l);if(Ct(a)&&(typeof(r=a.constructor)!="function"||r!==Array&&!Ct(r.prototype)?ae(r)&&(r=r[qu])===null&&(r=void 0):r=void 0,r===Array||r===void 0))return Xu.call(a,o,s);for(n=new(r===void 0?Array:r)(Yu(s-o,0)),i=0;o<s;o++,i++)o in a&&wt(n,i,a[o]);return n.length=i,n}});var Rn=Object.keys||function(e){return Ha(e,ur)},Ju=W(function(){Rn(1)});fe({target:"Object",stat:!0,forced:Ju},{keys:function(e){return Rn(Fe(e))}});var Lr,Zu=function(e){if(za(e))throw TypeError("The method doesn't accept regular expressions");return e},Qu=q("match"),ec=wn.f,Vi="".startsWith,tc=Math.min,Ga=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[Qu]=!1,"/./"[e](t)}catch(n){}}return!1}("startsWith"),rc=!(Ga||(Lr=ec(String.prototype,"startsWith"),!Lr||Lr.writable));function Ka(e){return(Ka=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}fe({target:"String",proto:!0,forced:!rc&&!Ga},{startsWith:function(e){var t=String(Ge(this));Zu(e);var r=we(tc(arguments.length>1?arguments[1]:void 0,t.length)),n=String(e);return Vi?Vi.call(t,n,r):t.slice(r,r+n.length)===n}});var ut=function(e){return typeof e=="string"},ct=function(e){return e!==null&&Ka(e)==="object"},Wt=function(){function e(){nt(this,e)}return it(e,null,[{key:"isWindow",value:function(t){return t===window}},{key:"addEventListener",value:function(t,r,n){var i=arguments.length>3&&arguments[3]!==void 0&&arguments[3];t&&r&&n&&t.addEventListener(r,n,i)}},{key:"removeEventListener",value:function(t,r,n){var i=arguments.length>3&&arguments[3]!==void 0&&arguments[3];t&&r&&n&&t.removeEventListener(r,n,i)}},{key:"triggerDragEvent",value:function(t,r){var n=!1,i=function(l){var o;(o=r.drag)===null||o===void 0||o.call(r,l)},a=function l(o){var s;e.removeEventListener(document,"mousemove",i),e.removeEventListener(document,"mouseup",l),document.onselectstart=null,document.ondragstart=null,n=!1,(s=r.end)===null||s===void 0||s.call(r,o)};e.addEventListener(t,"mousedown",function(l){var o;n||(document.onselectstart=function(){return!1},document.ondragstart=function(){return!1},e.addEventListener(document,"mousemove",i),e.addEventListener(document,"mouseup",a),n=!0,(o=r.start)===null||o===void 0||o.call(r,l))})}},{key:"getBoundingClientRect",value:function(t){return t&&ct(t)&&t.nodeType===1?t.getBoundingClientRect():null}},{key:"hasClass",value:function(t,r){return!!(t&&ct(t)&&ut(r)&&t.nodeType===1)&&t.classList.contains(r.trim())}},{key:"addClass",value:function(t,r){if(t&&ct(t)&&ut(r)&&t.nodeType===1&&(r=r.trim(),!e.hasClass(t,r))){var n=t.className;t.className=n?n+" "+r:r}}},{key:"removeClass",value:function(t,r){if(t&&ct(t)&&ut(r)&&t.nodeType===1&&typeof t.className=="string"){r=r.trim();for(var n=t.className.trim().split(" "),i=n.length-1;i>=0;i--)n[i]=n[i].trim(),n[i]&&n[i]!==r||n.splice(i,1);t.className=n.join(" ")}}},{key:"toggleClass",value:function(t,r,n){t&&ct(t)&&ut(r)&&t.nodeType===1&&t.classList.toggle(r,n)}},{key:"replaceClass",value:function(t,r,n){t&&ct(t)&&ut(r)&&ut(n)&&t.nodeType===1&&(r=r.trim(),n=n.trim(),e.removeClass(t,r),e.addClass(t,n))}},{key:"getScrollTop",value:function(t){var r="scrollTop"in t?t.scrollTop:t.pageYOffset;return Math.max(r,0)}},{key:"setScrollTop",value:function(t,r){"scrollTop"in t?t.scrollTop=r:t.scrollTo(t.scrollX,r)}},{key:"getRootScrollTop",value:function(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0}},{key:"setRootScrollTop",value:function(t){e.setScrollTop(window,t),e.setScrollTop(document.body,t)}},{key:"getElementTop",value:function(t,r){if(e.isWindow(t))return 0;var n=r?e.getScrollTop(r):e.getRootScrollTop();return t.getBoundingClientRect().top+n}},{key:"getVisibleHeight",value:function(t){return e.isWindow(t)?t.innerHeight:t.getBoundingClientRect().height}},{key:"isHidden",value:function(t){if(!t)return!1;var r=window.getComputedStyle(t),n=r.display==="none",i=t.offsetParent===null&&r.position!=="fixed";return n||i}},{key:"triggerEvent",value:function(t,r){if("createEvent"in document){var n=document.createEvent("HTMLEvents");n.initEvent(r,!1,!0),t.dispatchEvent(n)}}},{key:"calcAngle",value:function(t,r){var n=t.getBoundingClientRect(),i=n.left+n.width/2,a=n.top+n.height/2,l=Math.abs(i-r.clientX),o=Math.abs(a-r.clientY),s=o/Math.sqrt(Math.pow(l,2)+Math.pow(o,2)),c=Math.acos(s),u=Math.floor(180/(Math.PI/c));return r.clientX>i&&r.clientY>a&&(u=180-u),r.clientX==i&&r.clientY>a&&(u=180),r.clientX>i&&r.clientY==a&&(u=90),r.clientX<i&&r.clientY>a&&(u=180+u),r.clientX<i&&r.clientY==a&&(u=270),r.clientX<i&&r.clientY<a&&(u=360-u),u}},{key:"querySelector",value:function(t,r){return r?r.querySelector(t):document.querySelector(t)}},{key:"createElement",value:function(t){for(var r=document.createElement(t),n=arguments.length,i=new Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];for(var l=0;l<i.length;l++)i[l]&&r.classList.add(i[l]);return r}},{key:"appendChild",value:function(t){for(var r=0;r<(arguments.length<=1?0:arguments.length-1);r++)t.appendChild(r+1<1||arguments.length<=r+1?void 0:arguments[r+1])}},{key:"getWindow",value:function(t){if(t.toString()!=="[object Window]"){var r=t.ownerDocument;return r&&r.defaultView||window}return t}},{key:"isElement",value:function(t){return t instanceof this.getWindow(t).Element||t instanceof Element}},{key:"isHTMLElement",value:function(t){return t instanceof this.getWindow(t).HTMLElement||t instanceof HTMLElement}},{key:"isShadowRoot",value:function(t){return typeof ShadowRoot!="undefined"&&(t instanceof this.getWindow(t).ShadowRoot||t instanceof ShadowRoot)}},{key:"getWindowScroll",value:function(t){var r=this.getWindow(t);return{scrollLeft:r.pageXOffset||0,scrollTop:r.pageYOffset||0}}}]),e}(),nc=Math.floor,ic="".replace,ac=/\$([$&'`]|\d\d?|<[^>]*>)/g,oc=/\$([$&'`]|\d\d?)/g,sc=function(e,t,r,n,i,a){var l=r+e.length,o=n.length,s=oc;return i!==void 0&&(i=Fe(i),s=ac),ic.call(a,s,function(c,u){var f;switch(u.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,r);case"'":return t.slice(l);case"<":f=i[u.slice(1,-1)];break;default:var d=+u;if(d===0)return c;if(d>o){var h=nc(d/10);return h===0?c:h<=o?n[h-1]===void 0?u.charAt(1):n[h-1]+u.charAt(1):c}f=n[d-1]}return f===void 0?"":f})},lc=Math.max,uc=Math.min;Fa("replace",2,function(e,t,r,n){var i=n.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,a=n.REPLACE_KEEPS_$0,l=i?"$":"$0";return[function(o,s){var c=Ge(this),u=o==null?void 0:o[e];return u!==void 0?u.call(o,c,s):t.call(String(c),o,s)},function(o,s){if(!i&&a||typeof s=="string"&&s.indexOf(l)===-1){var c=r(t,o,this,s);if(c.done)return c.value}var u=he(o),f=String(this),d=typeof s=="function";d||(s=String(s));var h=u.global;if(h){var m=u.unicode;u.lastIndex=0}for(var g=[];;){var v=rn(u,f);if(v===null||(g.push(v),!h))break;String(v[0])===""&&(u.lastIndex=Ua(f,we(u.lastIndex),m))}for(var p,C="",x=0,S=0;S<g.length;S++){v=g[S];for(var E=String(v[0]),k=lc(uc(At(v.index),f.length),0),T=[],D=1;D<v.length;D++)T.push((p=v[D])===void 0?p:String(p));var L=v.groups;if(d){var N=[E].concat(T,k,f);L!==void 0&&N.push(L);var y=String(s.apply(void 0,N))}else y=sc(E,f,k,T,L,s);k>=x&&(C+=f.slice(x,k)+y,x=k+E.length)}return C+f.slice(x)}]});(function(){function e(){nt(this,e)}return it(e,null,[{key:"camelize",value:function(t){return t.replace(/-(\w)/g,function(r,n){return n?n.toUpperCase():""})}},{key:"capitalize",value:function(t){return t.charAt(0).toUpperCase()+t.slice(1)}}]),e})();(function(){function e(){nt(this,e)}return it(e,null,[{key:"_clone",value:function(){}}]),e})();var qa=q("isConcatSpreadable"),cc=fr>=51||!W(function(){var e=[];return e[qa]=!1,e.concat()[0]!==e}),fc=En("concat"),dc=function(e){if(!ae(e))return!1;var t=e[qa];return t!==void 0?!!t:Ct(e)};fe({target:"Array",proto:!0,forced:!cc||!fc},{concat:function(e){var t,r,n,i,a,l=Fe(this),o=_r(l,0),s=0;for(t=-1,n=arguments.length;t<n;t++)if(dc(a=t===-1?l:arguments[t])){if(s+(i=we(a.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(r=0;r<i;r++,s++)r in a&&wt(o,s,a[r])}else{if(s>=9007199254740991)throw TypeError("Maximum allowed index exceeded");wt(o,s++,a)}return o.length=s,o}});var Dr,qt=function(e,t,r){if(On(e),t===void 0)return e;switch(r){case 0:return function(){return e.call(t)};case 1:return function(n){return e.call(t,n)};case 2:return function(n,i){return e.call(t,n,i)};case 3:return function(n,i,a){return e.call(t,n,i,a)}}return function(){return e.apply(t,arguments)}},ji=[].push,Fi=function(e){var t=e==1,r=e==2,n=e==3,i=e==4,a=e==6,l=e==7,o=e==5||a;return function(s,c,u,f){for(var d,h,m=Fe(s),g=wr(m),v=qt(c,u,3),p=we(g.length),C=0,x=f||_r,S=t?x(s,p):r||l?x(s,0):void 0;p>C;C++)if((o||C in g)&&(h=v(d=g[C],C,m),e))if(t)S[C]=h;else if(h)switch(e){case 3:return!0;case 5:return d;case 6:return C;case 2:ji.call(S,d)}else switch(e){case 4:return!1;case 7:ji.call(S,d)}return a?-1:n||i?i:S}},Xa={find:Fi(5),findIndex:Fi(6)},hc=Se?Object.defineProperties:function(e,t){he(e);for(var r,n=Rn(t),i=n.length,a=0;i>a;)je.f(e,r=n[a++],t[r]);return e},pc=Sr("document","documentElement"),Ya=_n("IE_PROTO"),$r=function(){},zi=function(e){return"<script>"+e+"<\/script>"},rr=function(){try{Dr=document.domain&&new ActiveXObject("htmlfile")}catch(n){}var e,t;rr=Dr?function(n){n.write(zi("")),n.close();var i=n.parentWindow.Object;return n=null,i}(Dr):((t=Oa("iframe")).style.display="none",pc.appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write(zi("document.F=Object")),e.close(),e.F);for(var r=ur.length;r--;)delete rr.prototype[ur[r]];return rr()};xr[Ya]=!0;var Mn=Object.create||function(e,t){var r;return e!==null?($r.prototype=he(e),r=new $r,$r.prototype=null,r[Ya]=e):r=rr(),t===void 0?r:hc(r,t)},an=q("unscopables"),on=Array.prototype;on[an]==null&&je.f(on,an,{configurable:!0,value:Mn(null)});var ht=function(e){on[an][e]=!0},gc=Xa.find,Wi=!0,vc=Ot("find");"find"in[]&&Array(1).find(function(){Wi=!1}),fe({target:"Array",proto:!0,forced:Wi||!vc},{find:function(e){return gc(this,e,arguments.length>1?arguments[1]:void 0)}}),ht("find");var mc=Xa.findIndex,Ui=!0,yc=Ot("findIndex");"findIndex"in[]&&Array(1).findIndex(function(){Ui=!1}),fe({target:"Array",proto:!0,forced:Ui||!yc},{findIndex:function(e){return mc(this,e,arguments.length>1?arguments[1]:void 0)}}),ht("findIndex");var Ja=function(e,t,r,n,i,a,l,o){for(var s,c=i,u=0,f=!!l&&qt(l,o,3);u<n;){if(u in r){if(s=f?f(r[u],u,t):r[u],a>0&&Ct(s))c=Ja(e,t,s,we(s.length),c,a-1)-1;else{if(c>=9007199254740991)throw TypeError("Exceed the acceptable array length");e[c]=s}c++}u++}return c},bc=Ja;fe({target:"Array",proto:!0},{flat:function(){var e=arguments.length?arguments[0]:void 0,t=Fe(this),r=we(t.length),n=_r(t,0);return n.length=bc(n,t,t,r,0,e===void 0?1:At(e)),n}});var sn=function(e){var t=e.return;if(t!==void 0)return he(t.call(e)).value},Cc=function(e,t,r,n){try{return n?t(he(r)[0],r[1]):t(r)}catch(i){throw sn(e),i}},xt={},wc=q("iterator"),xc=Array.prototype,Za=function(e){return e!==void 0&&(xt.Array===e||xc[wc]===e)},Sc=q("iterator"),Qa=function(e){if(e!=null)return e[Sc]||e["@@iterator"]||xt[$a(e)]},eo=q("iterator"),to=!1;try{var _c=0,Gi={next:function(){return{done:!!_c++}},return:function(){to=!0}};Gi[eo]=function(){return this},Array.from(Gi,function(){throw 2})}catch(e){}var ro=function(e,t){if(!to)return!1;var r=!1;try{var n={};n[eo]=function(){return{next:function(){return{done:r=!0}}}},e(n)}catch(i){}return r},kc=!ro(function(e){Array.from(e)});fe({target:"Array",stat:!0,forced:kc},{from:function(e){var t,r,n,i,a,l,o=Fe(e),s=typeof this=="function"?this:Array,c=arguments.length,u=c>1?arguments[1]:void 0,f=u!==void 0,d=Qa(o),h=0;if(f&&(u=qt(u,c>2?arguments[2]:void 0,2)),d==null||s==Array&&Za(d))for(r=new s(t=we(o.length));t>h;h++)l=f?u(o[h],h):o[h],wt(r,h,l);else for(a=(i=d.call(o)).next,r=new s;!(n=a.call(i)).done;h++)l=f?Cc(i,u,[n.value,h],!0):n.value,wt(r,h,l);return r.length=h,r}});var Ec=function(e){return function(t,r,n,i){On(r);var a=Fe(t),l=wr(a),o=we(a.length),s=e?o-1:0,c=e?-1:1;if(n<2)for(;;){if(s in l){i=l[s],s+=c;break}if(s+=c,e?s<0:o<=s)throw TypeError("Reduce of empty array with no initial value")}for(;e?s>=0:o>s;s+=c)s in l&&(i=r(i,l[s],s,a));return i}},Ac={left:Ec(!1)},Oc=Ne(re.process)=="process",Rc=Ac.left,Mc=kn("reduce"),Tc=Ot("reduce",{1:0});fe({target:"Array",proto:!0,forced:!Mc||!Tc||!Oc&&fr>79&&fr<83},{reduce:function(e){return Rc(this,e,arguments.length,arguments.length>1?arguments[1]:void 0)}}),ht("flat");var Qe,Ki,qi,Pc=!W(function(){return Object.isExtensible(Object.preventExtensions({}))}),no=bn(function(e){var t=je.f,r=Sn("meta"),n=0,i=Object.isExtensible||function(){return!0},a=function(o){t(o,r,{value:{objectID:"O"+ ++n,weakData:{}}})},l=e.exports={REQUIRED:!1,fastKey:function(o,s){if(!ae(o))return typeof o=="symbol"?o:(typeof o=="string"?"S":"P")+o;if(!Q(o,r)){if(!i(o))return"F";if(!s)return"E";a(o)}return o[r].objectID},getWeakData:function(o,s){if(!Q(o,r)){if(!i(o))return!0;if(!s)return!1;a(o)}return o[r].weakData},onFreeze:function(o){return Pc&&l.REQUIRED&&i(o)&&!Q(o,r)&&a(o),o}};xr[r]=!0}),Pt=function(e,t){this.stopped=e,this.result=t},Xi=function(e,t,r){var n,i,a,l,o,s,c,u=r&&r.that,f=!(!r||!r.AS_ENTRIES),d=!(!r||!r.IS_ITERATOR),h=!(!r||!r.INTERRUPTED),m=qt(t,u,1+f+h),g=function(p){return n&&sn(n),new Pt(!0,p)},v=function(p){return f?(he(p),h?m(p[0],p[1],g):m(p[0],p[1])):h?m(p,g):m(p)};if(d)n=e;else{if(typeof(i=Qa(e))!="function")throw TypeError("Target is not iterable");if(Za(i)){for(a=0,l=we(e.length);l>a;a++)if((o=v(e[a]))&&o instanceof Pt)return o;return new Pt(!1)}n=i.call(e)}for(s=n.next;!(c=s.call(n)).done;){try{o=v(c.value)}catch(p){throw sn(n),p}if(typeof o=="object"&&o&&o instanceof Pt)return o}return new Pt(!1)},Yi=function(e,t,r){if(!(e instanceof t))throw TypeError("Incorrect "+(r?r+" ":"")+"invocation");return e},Hc=je.f,Ji=q("toStringTag"),ln=function(e,t,r){e&&!Q(e=r?e:e.prototype,Ji)&&Hc(e,Ji,{configurable:!0,value:t})},pr=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(r,[]),t=r instanceof Array}catch(n){}return function(n,i){return he(n),function(a){if(!ae(a)&&a!==null)throw TypeError("Can't set "+String(a)+" as a prototype")}(i),t?e.call(n,i):n.__proto__=i,n}}():void 0),Zi=function(e,t,r){for(var n in t)qe(e,n,t[n],r);return e},Ic=!W(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}),Qi=_n("IE_PROTO"),Lc=Object.prototype,gr=Ic?Object.getPrototypeOf:function(e){return e=Fe(e),Q(e,Qi)?e[Qi]:typeof e.constructor=="function"&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?Lc:null},Br=q("iterator"),io=!1;[].keys&&("next"in(qi=[].keys())?(Ki=gr(gr(qi)))!==Object.prototype&&(Qe=Ki):io=!0),(Qe==null||W(function(){var e={};return Qe[Br].call(e)!==e}))&&(Qe={}),Q(Qe,Br)||ke(Qe,Br,function(){return this});var Tn={IteratorPrototype:Qe,BUGGY_SAFARI_ITERATORS:io},Dc=Tn.IteratorPrototype,$c=function(){return this},Nr=Tn.IteratorPrototype,er=Tn.BUGGY_SAFARI_ITERATORS,Ht=q("iterator"),Bc=function(){return this},Pn=function(e,t,r,n,i,a,l){(function(p,C,x){var S=C+" Iterator";p.prototype=Mn(Dc,{next:Cr(1,x)}),ln(p,S,!1),xt[S]=$c})(r,t,n);var o,s,c,u=function(p){if(p===i&&g)return g;if(!er&&p in h)return h[p];switch(p){case"keys":case"values":case"entries":return function(){return new r(this,p)}}return function(){return new r(this)}},f=t+" Iterator",d=!1,h=e.prototype,m=h[Ht]||h["@@iterator"]||i&&h[i],g=!er&&m||u(i),v=t=="Array"&&h.entries||m;if(v&&(o=gr(v.call(new e)),Nr!==Object.prototype&&o.next&&(gr(o)!==Nr&&(pr?pr(o,Nr):typeof o[Ht]!="function"&&ke(o,Ht,Bc)),ln(o,f,!0))),i=="values"&&m&&m.name!=="values"&&(d=!0,g=function(){return m.call(this)}),h[Ht]!==g&&ke(h,Ht,g),xt[t]=g,i)if(s={values:u("values"),keys:a?g:u("keys"),entries:u("entries")},l)for(c in s)(er||d||!(c in h))&&qe(h,c,s[c]);else fe({target:t,proto:!0,forced:er||d},s);return s},ea=q("species"),Nc=je.f,ta=no.fastKey,ra=Ke.set,Vr=Ke.getterFor;(function(e,t,r){var n=e.indexOf("Map")!==-1,i=e.indexOf("Weak")!==-1,a=n?"set":"add",l=re[e],o=l&&l.prototype,s=l,c={},u=function(v){var p=o[v];qe(o,v,v=="add"?function(C){return p.call(this,C===0?0:C),this}:v=="delete"?function(C){return!(i&&!ae(C))&&p.call(this,C===0?0:C)}:v=="get"?function(C){return i&&!ae(C)?void 0:p.call(this,C===0?0:C)}:v=="has"?function(C){return!(i&&!ae(C))&&p.call(this,C===0?0:C)}:function(C,x){return p.call(this,C===0?0:C,x),this})};if(tn(e,typeof l!="function"||!(i||o.forEach&&!W(function(){new l().entries().next()}))))s=r.getConstructor(t,e,n,a),no.REQUIRED=!0;else if(tn(e,!0)){var f=new s,d=f[a](i?{}:-0,1)!=f,h=W(function(){f.has(1)}),m=ro(function(v){new l(v)}),g=!i&&W(function(){for(var v=new l,p=5;p--;)v[a](p,p);return!v.has(-0)});m||((s=t(function(v,p){Yi(v,s,e);var C=function(x,S,E){var k,T;return pr&&typeof(k=S.constructor)=="function"&&k!==E&&ae(T=k.prototype)&&T!==E.prototype&&pr(x,T),x}(new l,v,s);return p!=null&&Xi(p,C[a],{that:C,AS_ENTRIES:n}),C})).prototype=o,o.constructor=s),(h||g)&&(u("delete"),u("has"),n&&u("get")),(g||d)&&u(a),i&&o.clear&&delete o.clear}c[e]=s,fe({global:!0,forced:s!=l},c),ln(s,e),i||r.setStrong(s,e,n)})("Set",function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}},{getConstructor:function(e,t,r,n){var i=e(function(s,c){Yi(s,i,t),ra(s,{type:t,index:Mn(null),first:void 0,last:void 0,size:0}),Se||(s.size=0),c!=null&&Xi(c,s[n],{that:s,AS_ENTRIES:r})}),a=Vr(t),l=function(s,c,u){var f,d,h=a(s),m=o(s,c);return m?m.value=u:(h.last=m={index:d=ta(c,!0),key:c,value:u,previous:f=h.last,next:void 0,removed:!1},h.first||(h.first=m),f&&(f.next=m),Se?h.size++:s.size++,d!=="F"&&(h.index[d]=m)),s},o=function(s,c){var u,f=a(s),d=ta(c);if(d!=="F")return f.index[d];for(u=f.first;u;u=u.next)if(u.key==c)return u};return Zi(i.prototype,{clear:function(){for(var s=a(this),c=s.index,u=s.first;u;)u.removed=!0,u.previous&&(u.previous=u.previous.next=void 0),delete c[u.index],u=u.next;s.first=s.last=void 0,Se?s.size=0:this.size=0},delete:function(s){var c=this,u=a(c),f=o(c,s);if(f){var d=f.next,h=f.previous;delete u.index[f.index],f.removed=!0,h&&(h.next=d),d&&(d.previous=h),u.first==f&&(u.first=d),u.last==f&&(u.last=h),Se?u.size--:c.size--}return!!f},forEach:function(s){for(var c,u=a(this),f=qt(s,arguments.length>1?arguments[1]:void 0,3);c=c?c.next:u.first;)for(f(c.value,c.key,this);c&&c.removed;)c=c.previous},has:function(s){return!!o(this,s)}}),Zi(i.prototype,r?{get:function(s){var c=o(this,s);return c&&c.value},set:function(s,c){return l(this,s===0?0:s,c)}}:{add:function(s){return l(this,s=s===0?0:s,s)}}),Se&&Nc(i.prototype,"size",{get:function(){return a(this).size}}),i},setStrong:function(e,t,r){var n=t+" Iterator",i=Vr(t),a=Vr(n);Pn(e,t,function(l,o){ra(this,{type:n,target:l,state:i(l),kind:o,last:void 0})},function(){for(var l=a(this),o=l.kind,s=l.last;s&&s.removed;)s=s.previous;return l.target&&(l.last=s=s?s.next:l.state.first)?o=="keys"?{value:s.key,done:!1}:o=="values"?{value:s.value,done:!1}:{value:[s.key,s.value],done:!1}:(l.target=void 0,{value:void 0,done:!0})},r?"entries":"values",!r,!0),function(l){var o=Sr(l),s=je.f;Se&&o&&!o[ea]&&s(o,ea,{configurable:!0,get:function(){return this}})}(t)}});var Vc=Wa.charAt,jc=Ke.set,Fc=Ke.getterFor("String Iterator");Pn(String,"String",function(e){jc(this,{type:"String Iterator",string:String(e),index:0})},function(){var e,t=Fc(this),r=t.string,n=t.index;return n>=r.length?{value:void 0,done:!0}:(e=Vc(r,n),t.index+=e.length,{value:e,done:!1})});var na={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},zc=Ke.set,Wc=Ke.getterFor("Array Iterator"),Lt=Pn(Array,"Array",function(e,t){zc(this,{type:"Array Iterator",target:Et(e),index:0,kind:t})},function(){var e=Wc(this),t=e.target,r=e.kind,n=e.index++;return!t||n>=t.length?(e.target=void 0,{value:void 0,done:!0}):r=="keys"?{value:n,done:!1}:r=="values"?{value:t[n],done:!1}:{value:[n,t[n]],done:!1}},"values");xt.Arguments=xt.Array,ht("keys"),ht("values"),ht("entries");var jr=q("iterator"),ia=q("toStringTag"),Fr=Lt.values;for(var zr in na){var aa=re[zr],De=aa&&aa.prototype;if(De){if(De[jr]!==Fr)try{ke(De,jr,Fr)}catch(e){De[jr]=Fr}if(De[ia]||ke(De,ia,zr),na[zr]){for(var ft in Lt)if(De[ft]!==Lt[ft])try{ke(De,ft,Lt[ft])}catch(e){De[ft]=Lt[ft]}}}}(function(){function e(){nt(this,e)}return it(e,null,[{key:"deduplicate",value:function(t){return Array.from(new Set(t))}},{key:"flat",value:function(t){return t.reduce(function(r,n){var i=Array.isArray(n)?e.flat(n):n;return r.concat(i)},[])}},{key:"find",value:function(t,r){return t.find(r)}},{key:"findIndex",value:function(t,r){return t.findIndex(r)}}]),e})();(function(){function e(){nt(this,e)}return it(e,null,[{key:"today",value:function(){return new Date}}]),e})();(function(){function e(){nt(this,e)}return it(e,null,[{key:"range",value:function(t,r,n){return Math.min(Math.max(t,r),n)}},{key:"clamp",value:function(t,r,n){return r<n?t<r?r:t>n?n:t:t<n?n:t>r?r:t}}]),e})();var Uc=Object.defineProperty,Gc=(e,t,r)=>t in e?Uc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,le=(e,t,r)=>(Gc(e,typeof t!="symbol"?t+"":t,r),r);const J=e=>Math.round(e*100)/100;class K{constructor(t){le(this,"instance"),le(this,"alphaValue",0),le(this,"redValue",0),le(this,"greenValue",0),le(this,"blueValue",0),le(this,"hueValue",0),le(this,"saturationValue",0),le(this,"brightnessValue",0),le(this,"hslSaturationValue",0),le(this,"lightnessValue",0),le(this,"initAlpha",()=>{const r=this.instance.getAlpha();this.alphaValue=Math.min(1,r)*100}),le(this,"initLightness",()=>{const{s:r,l:n}=this.instance.toHsl();this.hslSaturationValue=J(r),this.lightnessValue=J(n)}),le(this,"initRgb",()=>{const{r,g:n,b:i}=this.instance.toRgb();this.redValue=J(r),this.greenValue=J(n),this.blueValue=J(i)}),le(this,"initHsb",()=>{const{h:r,s:n,v:i}=this.instance.toHsv();this.hueValue=Math.min(360,Math.ceil(r)),this.saturationValue=J(n),this.brightnessValue=J(i)}),le(this,"toHexString",()=>this.instance.toHexString()),le(this,"toRgbString",()=>this.instance.toRgbString()),this.instance=w(t),this.initRgb(),this.initHsb(),this.initLightness(),this.initAlpha()}toString(t){return this.instance.toString(t)}get hex(){return this.instance.toHex()}set hex(t){this.instance=w(t),this.initHsb(),this.initRgb(),this.initAlpha(),this.initLightness()}set hue(t){this.saturation===0&&this.brightness===0&&(this.saturationValue=1,this.brightnessValue=1),this.instance=w({h:J(t),s:this.saturation,v:this.brightness,a:this.alphaValue/100}),this.initRgb(),this.initLightness(),this.hueValue=J(t)}get hue(){return this.hueValue}set saturation(t){this.instance=w({h:this.hue,s:J(t),v:this.brightness,a:this.alphaValue/100}),this.initRgb(),this.initLightness(),this.saturationValue=J(t)}get saturation(){return this.saturationValue}set brightness(t){this.instance=w({h:this.hue,s:this.saturation,v:J(t),a:this.alphaValue/100}),this.initRgb(),this.initLightness(),this.brightnessValue=J(t)}get brightness(){return this.brightnessValue}set lightness(t){this.instance=w({h:this.hue,s:this.hslSaturationValue,l:J(t),a:this.alphaValue/100}),this.initRgb(),this.initHsb(),this.lightnessValue=J(t)}get lightness(){return this.lightnessValue}set red(t){const r=this.instance.toRgb();this.instance=w(at(V({},r),{r:J(t),a:this.alphaValue/100})),this.initHsb(),this.initLightness(),this.redValue=J(t)}get red(){return this.redValue}set green(t){const r=this.instance.toRgb();this.instance=w(at(V({},r),{g:J(t),a:this.alphaValue/100})),this.initHsb(),this.initLightness(),this.greenValue=J(t)}get green(){return this.greenValue}set blue(t){const r=this.instance.toRgb();this.instance=w(at(V({},r),{b:J(t),a:this.alphaValue/100})),this.initHsb(),this.initLightness(),this.blueValue=J(t)}get blue(){return this.blueValue}set alpha(t){this.instance.setAlpha(t/100),this.alphaValue=t}get alpha(){return this.alphaValue}get RGB(){return[this.red,this.green,this.blue,parseFloat((this.alpha/100).toFixed(2))]}get HSB(){return[this.hue,this.saturation,this.brightness,parseFloat((this.alpha/100).toFixed(2))]}get HSL(){return[this.hue,this.hslSaturationValue,this.lightness,parseFloat((this.alpha/100).toFixed(2))]}}function oa(e,t,r,n){return`rgba(${[e,t,r,n/100].join(",")})`}const Wr=(e,t,r)=>t<r?e<t?t:e>r?r:e:e<r?r:e>t?t:e,Hn="color-history",In=8,Oe=(e,t)=>{const r=e.__vccOpts||e;for(const[n,i]of t)r[n]=i;return r},Kc=Ce({name:"Alpha",props:{color:M.instanceOf(K),size:M.oneOf(["small","default"]).def("default")},emits:["change"],setup(e,{emit:t}){const r=Z(null),n=Z(null);let i=e.color||new K;const a=ge({red:i.red,green:i.green,blue:i.blue,alpha:i.alpha});pt(()=>e.color,f=>{f&&(i=f,mr(a,{red:f.red,green:f.green,blue:f.blue,alpha:f.alpha}))},{deep:!0});const l=ie(()=>{const f=oa(a.red,a.green,a.blue,0),d=oa(a.red,a.green,a.blue,100);return{background:`linear-gradient(to right, ${f} , ${d})`}}),o=()=>{if(r.value&&n.value){const f=a.alpha/100,d=r.value.getBoundingClientRect(),h=n.value.offsetWidth;return Math.round(f*(d.width-h)+h/2)}return 0},s=ie(()=>({left:o()+"px",top:0})),c=f=>{f.target!==r.value&&u(f)},u=f=>{if(f.stopPropagation(),r.value&&n.value){const d=r.value.getBoundingClientRect(),h=n.value.offsetWidth;let m=f.clientX-d.left;m=Math.max(h/2,m),m=Math.min(m,d.width-h/2);const g=Math.round((m-h/2)/(d.width-h)*100);i.alpha=g,a.alpha=g,t("change",g)}};return St(()=>{const f={drag:d=>{u(d)},end:d=>{u(d)}};r.value&&n.value&&Wt.triggerDragEvent(r.value,f)}),{barElement:r,cursorElement:n,getCursorStyle:s,getBackgroundStyle:l,onClickSider:c}}}),qc=e=>(_t("data-v-18925ba6"),e=e(),kt(),e),Xc=qc(()=>b("div",{class:"vc-alpha-slider__bar-handle"},null,-1)),Yc=[Xc];function Jc(e,t,r,n,i,a){return A(),I("div",{class:ce(["vc-alpha-slider","transparent",{"small-slider":e.size==="small"}])},[b("div",{ref:"barElement",class:"vc-alpha-slider__bar",style:oe(e.getBackgroundStyle),onClick:t[0]||(t[0]=(...l)=>e.onClickSider&&e.onClickSider(...l))},[b("div",{class:ce(["vc-alpha-slider__bar-pointer",{"small-bar":e.size==="small"}]),ref:"cursorElement",style:oe(e.getCursorStyle)},Yc,6)],4)],2)}const Ln=Oe(Kc,[["render",Jc],["__scopeId","data-v-18925ba6"]]),Zc=[["#fcc02e","#f67c01","#e64a19","#d81b43","#8e24aa","#512da7","#1f87e8","#008781","#05a045"],["#fed835","#fb8c00","#f5511e","#eb1d4e","#9c28b1","#5d35b0","#2097f3","#029688","#4cb050"],["#ffeb3c","#ffa727","#fe5722","#eb4165","#aa47bc","#673bb7","#42a5f6","#26a59a","#83c683"],["#fff176","#ffb74e","#ff8a66","#f1627e","#b968c7","#7986cc","#64b5f6","#80cbc4","#a5d6a7"],["#fff59c","#ffcc80","#ffab91","#fb879e","#cf93d9","#9ea8db","#90caf8","#b2dfdc","#c8e6ca"],["transparent","#ffffff","#dedede","#a9a9a9","#4b4b4b","#353535","#212121","#000000","advance"]],Qc=Ce({name:"Palette",emits:["change"],setup(e,{emit:t}){return{palettes:Zc,computedBgStyle:r=>r==="transparent"?r:r==="advance"?{}:{background:w(r).toRgbString()},onColorChange:r=>{t("change",r)}}}}),ef={class:"vc-compact"},tf=["onClick"];function rf(e,t,r,n,i,a){return A(),I("div",ef,[(A(!0),I(Ue,null,gt(e.palettes,(l,o)=>(A(),I("div",{key:o,class:"vc-compact__row"},[(A(!0),I(Ue,null,gt(l,(s,c)=>(A(),I("div",{key:c,class:"vc-compact__color-cube--wrap",onClick:u=>e.onColorChange(s)},[b("div",{class:ce(["vc-compact__color_cube",{advance:s==="advance",transparent:s==="transparent"}]),style:oe(e.computedBgStyle(s))},null,6)],8,tf))),128))]))),128))])}const ao=Oe(Qc,[["render",rf],["__scopeId","data-v-b969fd48"]]),nf=Ce({name:"Board",props:{color:M.instanceOf(K),round:M.bool.def(!1),hide:M.bool.def(!0)},emits:["change"],setup(e,{emit:t}){var r,n,i;const a=zo(),l={h:((r=e.color)==null?void 0:r.hue)||0,s:1,v:1},o=new K(l).toHexString(),s=ge({hueColor:o,saturation:((n=e.color)==null?void 0:n.saturation)||0,brightness:((i=e.color)==null?void 0:i.brightness)||0}),c=Z(0),u=Z(0),f=Z(),d=ie(()=>({top:c.value+"px",left:u.value+"px"})),h=()=>{if(a){const x=a.vnode.el;u.value=s.saturation*(x==null?void 0:x.clientWidth),c.value=(1-s.brightness)*(x==null?void 0:x.clientHeight)}};let m=!1;const g=x=>{m=!0,C(x)},v=x=>{m&&C(x)},p=()=>{m=!1},C=x=>{if(a){const S=a.vnode.el,E=S==null?void 0:S.getBoundingClientRect();let k=x.clientX-E.left,T=x.clientY-E.top;k=Wr(k,0,E.width),T=Wr(T,0,E.height);const D=k/E.width,L=Wr(-(T/E.height)+1,0,1);u.value=k,c.value=T,s.saturation=D,s.brightness=L,t("change",D,L)}};return St(()=>{a&&a.vnode.el&&f.value&&Wo(()=>{h()})}),ue(()=>e.color,x=>{mr(s,{hueColor:new K({h:x.hue,s:1,v:1}).toHexString(),saturation:x.saturation,brightness:x.brightness}),h()},{deep:!0}),{state:s,cursorElement:f,getCursorStyle:d,onClickBoard:g,onDrag:v,onDragEnd:p}}}),Dn=e=>(_t("data-v-7f0cdcdf"),e=e(),kt(),e),af=Dn(()=>b("div",{class:"vc-saturation__white"},null,-1)),of=Dn(()=>b("div",{class:"vc-saturation__black"},null,-1)),sf=Dn(()=>b("div",null,null,-1)),lf=[sf];function uf(e,t,r,n,i,a){return A(),I("div",{ref:"boardElement",class:ce(["vc-saturation",{"vc-saturation__chrome":e.round,"vc-saturation__hidden":e.hide}]),style:oe({backgroundColor:e.state.hueColor}),onMousedown:t[0]||(t[0]=(...l)=>e.onClickBoard&&e.onClickBoard(...l)),onMousemove:t[1]||(t[1]=(...l)=>e.onDrag&&e.onDrag(...l)),onMouseup:t[2]||(t[2]=(...l)=>e.onDragEnd&&e.onDragEnd(...l))},[af,of,b("div",{class:"vc-saturation__cursor",ref:"cursorElement",style:oe(e.getCursorStyle)},lf,4)],38)}const $n=Oe(nf,[["render",uf],["__scopeId","data-v-7f0cdcdf"]]),cf=Ce({name:"Hue",props:{color:M.instanceOf(K),size:M.oneOf(["small","default"]).def("default")},emits:["change"],setup(e,{emit:t}){const r=Z(null),n=Z(null);let i=e.color||new K;const a=ge({hue:i.hue||0});pt(()=>e.color,u=>{u&&(i=u,mr(a,{hue:i.hue}))},{deep:!0});const l=()=>{if(r.value&&n.value){const u=r.value.getBoundingClientRect(),f=n.value.offsetWidth;return a.hue===360?u.width-f/2:a.hue%360*(u.width-f)/360+f/2}return 0},o=ie(()=>({left:l()+"px",top:0})),s=u=>{u.target!==r.value&&c(u)},c=u=>{if(u.stopPropagation(),r.value&&n.value){const f=r.value.getBoundingClientRect(),d=n.value.offsetWidth;let h=u.clientX-f.left;h=Math.min(h,f.width-d/2),h=Math.max(d/2,h);const m=Math.round((h-d/2)/(f.width-d)*360);i.hue=m,a.hue=m,t("change",m)}};return St(()=>{const u={drag:f=>{c(f)},end:f=>{c(f)}};r.value&&n.value&&Wt.triggerDragEvent(r.value,u)}),{barElement:r,cursorElement:n,getCursorStyle:o,onClickSider:s}}}),ff=e=>(_t("data-v-e1a08576"),e=e(),kt(),e),df=ff(()=>b("div",{class:"vc-hue-slider__bar-handle"},null,-1)),hf=[df];function pf(e,t,r,n,i,a){return A(),I("div",{class:ce(["vc-hue-slider",{"small-slider":e.size==="small"}])},[b("div",{ref:"barElement",class:"vc-hue-slider__bar",onClick:t[0]||(t[0]=(...l)=>e.onClickSider&&e.onClickSider(...l))},[b("div",{class:ce(["vc-hue-slider__bar-pointer",{"small-bar":e.size==="small"}]),ref:"cursorElement",style:oe(e.getCursorStyle)},hf,6)],512)],2)}const Bn=Oe(cf,[["render",pf],["__scopeId","data-v-e1a08576"]]),gf=Ce({name:"Lightness",props:{color:M.instanceOf(K),size:M.oneOf(["small","default"]).def("default")},emits:["change"],setup(e,{emit:t}){const r=Z(null),n=Z(null);let i=e.color||new K;const[a,l,o]=i.HSL,s=ge({hue:a,saturation:l,lightness:o});pt(()=>e.color,m=>{if(m){i=m;const[g,v,p]=i.HSL;mr(s,{hue:g,saturation:v,lightness:p})}},{deep:!0});const c=ie(()=>{const m=w({h:s.hue,s:s.saturation,l:.8}).toPercentageRgbString(),g=w({h:s.hue,s:s.saturation,l:.6}).toPercentageRgbString(),v=w({h:s.hue,s:s.saturation,l:.4}).toPercentageRgbString(),p=w({h:s.hue,s:s.saturation,l:.2}).toPercentageRgbString();return{background:[`linear-gradient(to right, rgb(255, 255, 255), ${m}, ${g}, ${v}, ${p}, rgb(0, 0, 0))`,`-webkit-linear-gradient(left, rgb(255, 255, 255), ${m}, ${g}, ${v}, ${p}, rgb(0, 0, 0))`,`-moz-linear-gradient(left, rgb(255, 255, 255), ${m}, ${g}, ${v}, ${p}, rgb(0, 0, 0))`,`-ms-linear-gradient(left, rgb(255, 255, 255), ${m}, ${g}, ${v}, ${p}, rgb(0, 0, 0))`]}}),u=()=>{if(r.value&&n.value){const m=s.lightness,g=r.value.getBoundingClientRect(),v=n.value.offsetWidth;return(1-m)*(g.width-v)+v/2}return 0},f=ie(()=>({left:u()+"px",top:0})),d=m=>{m.target!==r.value&&h(m)},h=m=>{if(m.stopPropagation(),r.value&&n.value){const g=r.value.getBoundingClientRect(),v=n.value.offsetWidth;let p=m.clientX-g.left;p=Math.max(v/2,p),p=Math.min(p,g.width-v/2);const C=1-(p-v/2)/(g.width-v);i.lightness=C,t("change",C)}};return St(()=>{const m={drag:g=>{h(g)},end:g=>{h(g)}};r.value&&n.value&&Wt.triggerDragEvent(r.value,m)}),{barElement:r,cursorElement:n,getCursorStyle:f,getBackgroundStyle:c,onClickSider:d}}}),vf=e=>(_t("data-v-94a50a9e"),e=e(),kt(),e),mf=vf(()=>b("div",{class:"vc-lightness-slider__bar-handle"},null,-1)),yf=[mf];function bf(e,t,r,n,i,a){return A(),I("div",{class:ce(["vc-lightness-slider",{"small-slider":e.size==="small"}])},[b("div",{ref:"barElement",class:"vc-lightness-slider__bar",style:oe(e.getBackgroundStyle),onClick:t[0]||(t[0]=(...l)=>e.onClickSider&&e.onClickSider(...l))},[b("div",{class:ce(["vc-lightness-slider__bar-pointer",{"small-bar":e.size==="small"}]),ref:"cursorElement",style:oe(e.getCursorStyle)},yf,6)],4)],2)}const oo=Oe(gf,[["render",bf],["__scopeId","data-v-94a50a9e"]]),Cf=Ce({name:"History",props:{colors:M.arrayOf(String).def(()=>[]),round:M.bool.def(!1)},emits:["change"],setup(e,{emit:t}){return{onColorSelect:r=>{t("change",r)}}}}),wf={key:0,class:"vc-colorPicker__record"},xf={class:"color-list"},Sf=["onClick"];function _f(e,t,r,n,i,a){return e.colors&&e.colors.length>0?(A(),I("div",wf,[b("div",xf,[(A(!0),I(Ue,null,gt(e.colors,(l,o)=>(A(),I("div",{key:o,class:ce(["color-item","transparent",{"color-item__round":e.round}]),onClick:s=>e.onColorSelect(l)},[b("div",{class:"color-item__display",style:oe({backgroundColor:l})},null,4)],10,Sf))),128))])])):$("",!0)}const Nn=Oe(Cf,[["render",_f],["__scopeId","data-v-0f657238"]]),kf=Ce({name:"Display",props:{color:M.instanceOf(K),disableAlpha:M.bool.def(!1)},emits:["update:color","change"],setup(e,{emit:t}){var r,n,i,a;const{copy:l,copied:o,isSupported:s}=Uo(),c=Z("hex"),u=ge({color:e.color,hex:(r=e.color)==null?void 0:r.hex,alpha:Math.round(((n=e.color)==null?void 0:n.alpha)||100),rgba:(i=e.color)==null?void 0:i.RGB,previewBgColor:(a=e.color)==null?void 0:a.toRgbString()}),f=ie(()=>({background:u.previewBgColor})),d=()=>{c.value=c.value==="rgba"?"hex":"rgba"},h=Be(p=>{if(!p.target.value)return;let C=parseInt(p.target.value.replace("%",""));C>100&&(p.target.value="100",C=100),C<0&&(p.target.value="0",C=0),isNaN(C)&&(p.target.value="100",C=100),!isNaN(C)&&u.color&&(u.color.alpha=C),t("change",u.color)},300),m=Be((p,C)=>{if(u.color){if(c.value==="hex"){const x=p.target.value.replace("#","");w(x).isValid()?[3,4].includes(x.length)&&(u.color.hex=x):u.color.hex="000000",t("change",u.color)}else if(c.value==="rgba"&&C===3&&p.target.value.toString()==="0."&&u.rgba){u.rgba[C]=p.target.value;const[x,S,E,k]=u.rgba;u.color.hex=w({r:x,g:S,b:E}).toHex(),u.color.alpha=Math.round(k*100),t("change",u.color)}}},100),g=Be((p,C)=>{if(p.target.value){if(c.value==="hex"){const x=p.target.value.replace("#","");w(x).isValid()&&u.color&&[6,8].includes(x.length)&&(u.color.hex=x)}else if(C!==void 0&&u.rgba&&u.color){if(p.target.value<0&&(p.target.value=0),C===3&&((p.target.value>1||isNaN(p.target.value))&&(p.target.value=1),p.target.value.toString()==="0."))return;C<3&&p.target.value>255&&(p.target.value=255),u.rgba[C]=p.target.value;const[x,S,E,k]=u.rgba;u.color.hex=w({r:x,g:S,b:E}).toHex(),u.color.alpha=Math.round(k*100)}t("change",u.color)}},300),v=()=>{if(s&&u.color){const p=c.value==="hex"?u.color.toString(u.color.alpha===100?"hex6":"hex8"):u.color.toRgbString();l(p||"")}};return ue(()=>e.color,p=>{p&&(u.color=p,u.alpha=Math.round(u.color.alpha),u.hex=u.color.hex,u.rgba=u.color.RGB)},{deep:!0}),ue(()=>u.color,()=>{u.color&&(u.previewBgColor=u.color.toRgbString())},{deep:!0}),{state:u,getBgColorStyle:f,inputType:c,copied:o,onInputTypeChange:d,onAlphaBlur:h,onInputChange:g,onBlurChange:m,onCopyColorStr:v}}}),Ef={class:"vc-display"},Af={class:"vc-current-color vc-transparent"},Of={key:0,class:"copy-text"},Rf={key:0,style:{display:"flex",flex:"1",gap:"4px",height:"100%"}},Mf={class:"vc-color-input"},Tf={key:0,class:"vc-alpha-input"},Pf=["value"],Hf={key:1,style:{display:"flex",flex:"1",gap:"4px",height:"100%"}},If=["value","onInput","onBlur"];function Lf(e,t,r,n,i,a){return A(),I("div",Ef,[b("div",Af,[b("div",{class:"color-cube",style:oe(e.getBgColorStyle),onClick:t[0]||(t[0]=(...l)=>e.onCopyColorStr&&e.onCopyColorStr(...l))},[e.copied?(A(),I("span",Of,"Copied!")):$("",!0)],4)]),e.inputType==="hex"?(A(),I("div",Rf,[b("div",Mf,[Pe(b("input",{"onUpdate:modelValue":t[1]||(t[1]=l=>e.state.hex=l),maxlength:"8",onInput:t[2]||(t[2]=(...l)=>e.onInputChange&&e.onInputChange(...l)),onBlur:t[3]||(t[3]=(...l)=>e.onBlurChange&&e.onBlurChange(...l))},null,544),[[Ze,e.state.hex]])]),e.disableAlpha?$("",!0):(A(),I("div",Tf,[b("input",{class:"vc-alpha-input__inner",value:e.state.alpha,onInput:t[4]||(t[4]=(...l)=>e.onAlphaBlur&&e.onAlphaBlur(...l))},null,40,Pf),We("% ")]))])):e.state.rgba?(A(),I("div",Hf,[(A(!0),I(Ue,null,gt(e.state.rgba,(l,o)=>(A(),I("div",{class:"vc-color-input",key:o},[b("input",{value:l,onInput:s=>e.onInputChange(s,o),onBlur:s=>e.onBlurChange(s,o)},null,40,If)]))),128))])):$("",!0),b("div",{class:"vc-input-toggle",onClick:t[5]||(t[5]=(...l)=>e.onInputTypeChange&&e.onInputTypeChange(...l))},nr(e.inputType),1)])}const Vn=Oe(kf,[["render",Lf],["__scopeId","data-v-7334ac20"]]),Df=Ce({name:"FkColorPicker",components:{Display:Vn,Alpha:Ln,Palette:ao,Board:$n,Hue:Bn,Lightness:oo,History:Nn},props:{color:M.instanceOf(K),disableHistory:M.bool.def(!1),roundHistory:M.bool.def(!1),disableAlpha:M.bool.def(!1)},emits:["update:color","change","advanceChange"],setup(e,{emit:t}){const r=e.color||new K,n=ge({color:r,hex:r.toHexString(),rgb:r.toRgbString()}),i=Z(!1),a=ie(()=>({background:n.rgb})),l=()=>{i.value=!1,t("advanceChange",!1)},o=cn(Hn,[],{}),s=Be(()=>{if(e.disableHistory)return;const g=n.color.toRgbString();if(o.value=o.value.filter(v=>!w.equals(v,g)),!o.value.includes(g)){for(;o.value.length>In;)o.value.pop();o.value.unshift(g)}},500),c=g=>{g==="advance"?(i.value=!0,t("advanceChange",!0)):(n.color.hex=g,t("advanceChange",!1))},u=g=>{n.color.alpha=g},f=g=>{n.color.hue=g},d=(g,v)=>{n.color.saturation=g,n.color.brightness=v},h=g=>{n.color.lightness=g},m=g=>{const v=g.target.value.replace("#","");w(v).isValid()&&(n.color.hex=v)};return ue(()=>e.color,g=>{g&&(n.color=g)},{deep:!0}),ue(()=>n.color,()=>{n.hex=n.color.hex,n.rgb=n.color.toRgbString(),s(),t("update:color",n.color),t("change",n.color)},{deep:!0}),{state:n,advancePanelShow:i,onBack:l,onCompactChange:c,onAlphaChange:u,onHueChange:f,onBoardChange:d,onLightChange:h,onInputChange:m,previewStyle:a,historyColors:o}}}),$f=e=>(_t("data-v-48e3c224"),e=e(),kt(),e),Bf={class:"vc-fk-colorPicker"},Nf={class:"vc-fk-colorPicker__inner"},Vf={class:"vc-fk-colorPicker__header"},jf=$f(()=>b("div",{class:"back"},null,-1)),Ff=[jf];function zf(e,t,r,n,i,a){const l=z("Palette"),o=z("Board"),s=z("Hue"),c=z("Lightness"),u=z("Alpha"),f=z("Display"),d=z("History");return A(),I("div",Bf,[b("div",Nf,[b("div",Vf,[e.advancePanelShow?(A(),I("span",{key:0,style:{cursor:"pointer"},onClick:t[0]||(t[0]=(...h)=>e.onBack&&e.onBack(...h))},Ff)):$("",!0)]),e.advancePanelShow?$("",!0):(A(),te(l,{key:0,onChange:e.onCompactChange},null,8,["onChange"])),e.advancePanelShow?(A(),te(o,{key:1,color:e.state.color,onChange:e.onBoardChange},null,8,["color","onChange"])):$("",!0),e.advancePanelShow?(A(),te(s,{key:2,color:e.state.color,onChange:e.onHueChange},null,8,["color","onChange"])):$("",!0),e.advancePanelShow?$("",!0):(A(),te(c,{key:3,color:e.state.color,onChange:e.onLightChange},null,8,["color","onChange"])),e.disableAlpha?$("",!0):(A(),te(u,{key:4,color:e.state.color,onChange:e.onAlphaChange},null,8,["color","onChange"])),ne(f,{color:e.state.color,"disable-alpha":e.disableAlpha},null,8,["color","disable-alpha"]),e.disableHistory?$("",!0):(A(),te(d,{key:5,round:e.roundHistory,colors:e.historyColors,onChange:e.onCompactChange},null,8,["round","colors","onChange"]))])])}const sa=Oe(Df,[["render",zf],["__scopeId","data-v-48e3c224"]]),Wf=Ce({name:"ChromeColorPicker",components:{Display:Vn,Alpha:Ln,Board:$n,Hue:Bn,History:Nn},props:{color:M.instanceOf(K),disableHistory:M.bool.def(!1),roundHistory:M.bool.def(!1),disableAlpha:M.bool.def(!1)},emits:["update:color","change"],setup(e,{emit:t}){const r=e.color||new K,n=ge({color:r,hex:r.toHexString(),rgb:r.toRgbString()}),i=ie(()=>({background:n.rgb})),a=cn(Hn,[],{}),l=Be(()=>{if(e.disableHistory)return;const d=n.color.toRgbString();if(a.value=a.value.filter(h=>!w.equals(h,d)),!a.value.includes(d)){for(;a.value.length>In;)a.value.pop();a.value.unshift(d)}},500),o=d=>{n.color.alpha=d},s=d=>{n.color.hue=d},c=d=>{d.hex!==void 0&&(n.color.hex=d.hex),d.alpha!==void 0&&(n.color.alpha=d.alpha)},u=(d,h)=>{n.color.saturation=d,n.color.brightness=h},f=d=>{d!=="advance"&&(n.color.hex=d)};return ue(()=>e.color,d=>{d&&(n.color=d)},{deep:!0}),ue(()=>n.color,()=>{n.hex=n.color.hex,n.rgb=n.color.toRgbString(),l(),t("update:color",n.color),t("change",n.color)},{deep:!0}),{state:n,previewStyle:i,historyColors:a,onAlphaChange:o,onHueChange:s,onBoardChange:u,onInputChange:c,onCompactChange:f}}}),Uf={class:"vc-chrome-colorPicker"},Gf={class:"vc-chrome-colorPicker-body"},Kf={class:"chrome-controls"},qf={class:"chrome-sliders"};function Xf(e,t,r,n,i,a){const l=z("Board"),o=z("Hue"),s=z("Alpha"),c=z("Display"),u=z("History");return A(),I("div",Uf,[ne(l,{round:!0,hide:!1,color:e.state.color,onChange:e.onBoardChange},null,8,["color","onChange"]),b("div",Gf,[b("div",Kf,[b("div",qf,[ne(o,{size:"small",color:e.state.color,onChange:e.onHueChange},null,8,["color","onChange"]),e.disableAlpha?$("",!0):(A(),te(s,{key:0,size:"small",color:e.state.color,onChange:e.onAlphaChange},null,8,["color","onChange"]))])]),ne(c,{color:e.state.color,"disable-alpha":e.disableAlpha},null,8,["color","disable-alpha"]),e.disableHistory?$("",!0):(A(),te(u,{key:0,round:e.roundHistory,colors:e.historyColors,onChange:e.onCompactChange},null,8,["round","colors","onChange"]))])])}const la=Oe(Wf,[["render",Xf],["__scopeId","data-v-2611d66c"]]),jn="Vue3ColorPickerProvider",Yf=(e,t)=>{const r=e.getBoundingClientRect(),n=r.left+r.width/2,i=r.top+r.height/2,a=Math.abs(n-t.clientX),l=Math.abs(i-t.clientY),o=Math.sqrt(Math.pow(a,2)+Math.pow(l,2)),s=l/o,c=Math.acos(s);let u=Math.floor(180/(Math.PI/c));return t.clientX>n&&t.clientY>i&&(u=180-u),t.clientX==n&&t.clientY>i&&(u=180),t.clientX>n&&t.clientY==i&&(u=90),t.clientX<n&&t.clientY>i&&(u=180+u),t.clientX<n&&t.clientY==i&&(u=270),t.clientX<n&&t.clientY<i&&(u=360-u),u};let Ur=!1;const Jf=(e,t)=>{const r=function(i){var a;(a=t.drag)==null||a.call(t,i)},n=function(i){var a;document.removeEventListener("mousemove",r,!1),document.removeEventListener("mouseup",n,!1),document.onselectstart=null,document.ondragstart=null,Ur=!1,(a=t.end)==null||a.call(t,i)};e&&e.addEventListener("mousedown",i=>{var a;Ur||(document.onselectstart=()=>!1,document.ondragstart=()=>!1,document.addEventListener("mousemove",r,!1),document.addEventListener("mouseup",n,!1),Ur=!0,(a=t.start)==null||a.call(t,i))})},Zf={angle:{type:Number,default:0},size:{type:Number,default:16,validator:e=>e>=16},borderWidth:{type:Number,default:1,validator:e=>e>=1},borderColor:{type:String,default:"#666"}},Qf=Ce({name:"Angle",props:Zf,emits:["update:angle","change"],setup(e,{emit:t}){const r=Z(null),n=Z(0);pt(()=>e.angle,o=>{n.value=o});const i=()=>{let o=Number(n.value);isNaN(o)||(o=o>360||o<0?e.angle:o,n.value=o===360?0:o,t("update:angle",n.value),t("change",n.value))},a=ie(()=>({width:e.size+"px",height:e.size+"px",borderWidth:e.borderWidth+"px",borderColor:e.borderColor,transform:`rotate(${n.value}deg)`})),l=o=>{r.value&&(n.value=Yf(r.value,o)%360,i())};return Go(()=>{const o={drag:s=>{l(s)},end:s=>{l(s)}};r.value&&Jf(r.value,o)}),()=>ne("div",{class:"bee-angle"},[ne("div",{class:"bee-angle__round",ref:r,style:a.value},null)])}}),ed=Ce({name:"GradientColorPicker",components:{Angle:Qf,Display:Vn,Alpha:Ln,Palette:ao,Board:$n,Hue:Bn,Lightness:oo,History:Nn},props:{startColor:M.instanceOf(K).isRequired,endColor:M.instanceOf(K).isRequired,startColorStop:M.number.def(0),endColorStop:M.number.def(100),angle:M.number.def(0),type:M.oneOf(["linear","radial"]).def("linear"),disableHistory:M.bool.def(!1),roundHistory:M.bool.def(!1),disableAlpha:M.bool.def(!1),pickerType:M.oneOf(["fk","chrome"]).def("fk")},emits:["update:startColor","update:endColor","update:angle","update:startColorStop","update:endColorStop","startColorChange","endColorChange","advanceChange","angleChange","startColorStopChange","endColorStopChange","typeChange"],setup(e,{emit:t}){const r=ge({startActive:!0,startColor:e.startColor,endColor:e.endColor,startColorStop:e.startColorStop,endColorStop:e.endColorStop,angle:e.angle,type:e.type,startColorRgba:e.startColor.toRgbString(),endColorRgba:e.endColor.toRgbString()}),n=ga(jn),i=Z(e.pickerType==="chrome"),a=Z(),l=Z(),o=Z();pt(()=>[e.startColor,e.endColor,e.angle],y=>{r.startColor=y[0],r.endColor=y[1],r.angle=y[2]}),pt(()=>e.type,y=>{r.type=y});const s=ie({get:()=>r.startActive?r.startColor:r.endColor,set:y=>{if(r.startActive){r.startColor=y;return}r.endColor=y}}),c=ie(()=>{if(o.value&&a.value){const y=r.startColorStop/100,R=o.value.getBoundingClientRect(),H=a.value.offsetWidth;return Math.round(y*(R.width-H)+H/2)}return 0}),u=ie(()=>{if(o.value&&l.value){const y=r.endColorStop/100,R=o.value.getBoundingClientRect(),H=l.value.offsetWidth;return Math.round(y*(R.width-H)+H/2)}return 0}),f=ie(()=>{let y=`background: linear-gradient(${r.angle}deg, ${r.startColorRgba} ${r.startColorStop}%, ${r.endColorRgba} ${r.endColorStop}%)`;return r.type==="radial"&&(y=`background: radial-gradient(circle, ${r.startColorRgba} ${r.startColorStop}%, ${r.endColorRgba} ${r.endColorStop}%)`),y}),d=y=>{var R;if(r.startActive=!0,o.value&&a.value){const H=(R=o.value)==null?void 0:R.getBoundingClientRect();let j=y.clientX-H.left;j=Math.max(a.value.offsetWidth/2,j),j=Math.min(j,H.width-a.value.offsetWidth/2),r.startColorStop=Math.round((j-a.value.offsetWidth/2)/(H.width-a.value.offsetWidth)*100),t("update:startColorStop",r.startColorStop),t("startColorStopChange",r.startColorStop)}},h=y=>{var R;if(r.startActive=!1,o.value&&l.value){const H=(R=o.value)==null?void 0:R.getBoundingClientRect();let j=y.clientX-H.left;j=Math.max(l.value.offsetWidth/2,j),j=Math.min(j,H.width-l.value.offsetWidth/2),r.endColorStop=Math.round((j-l.value.offsetWidth/2)/(H.width-l.value.offsetWidth)*100),t("update:endColorStop",r.endColorStop),t("endColorStopChange",r.endColorStop)}},m=y=>{const R=y.target,H=parseInt(R.value.replace("°",""));isNaN(H)||(r.angle=H%360),t("update:angle",r.angle),t("angleChange",r.angle)},g=y=>{r.angle=y,t("update:angle",r.angle),t("angleChange",r.angle)},v=y=>{y==="advance"?(i.value=!0,t("advanceChange",!0)):(s.value.hex=y,t("advanceChange",!1)),k()},p=y=>{s.value.alpha=y,k()},C=y=>{s.value.hue=y,k()},x=(y,R)=>{s.value.saturation=y,s.value.brightness=R,k()},S=y=>{s.value.lightness=y,k()},E=()=>{k()},k=()=>{r.startActive?(t("update:startColor",r.startColor),t("startColorChange",r.startColor)):(t("update:endColor",r.endColor),t("endColorChange",r.endColor))},T=()=>{i.value=!1,t("advanceChange",!1)},D=()=>{r.type=r.type==="linear"?"radial":"linear",t("typeChange",r.type)},L=cn(Hn,[],{}),N=Be(()=>{if(e.disableHistory)return;const y=s.value.toRgbString();if(L.value=L.value.filter(R=>!w.equals(R,y)),!L.value.includes(y)){for(;L.value.length>In;)L.value.pop();L.value.unshift(y)}},500);return St(()=>{l.value&&a.value&&(Wt.triggerDragEvent(l.value,{drag:y=>{h(y)},end:y=>{h(y)}}),Wt.triggerDragEvent(a.value,{drag:y=>{d(y)},end:y=>{d(y)}}))}),ue(()=>r.startColor,y=>{r.startColorRgba=y.toRgbString()},{deep:!0}),ue(()=>r.endColor,y=>{r.endColorRgba=y.toRgbString()},{deep:!0}),ue(()=>s.value,()=>{N()},{deep:!0}),{startGradientRef:a,stopGradientRef:l,colorRangeRef:o,state:r,currentColor:s,getStartColorLeft:c,getEndColorLeft:u,gradientBg:f,advancePanelShow:i,onDegreeBlur:m,onCompactChange:v,onAlphaChange:p,onHueChange:C,onBoardChange:x,onLightChange:S,historyColors:L,onBack:T,onDegreeChange:g,onDisplayChange:E,onTypeChange:D,lang:n==null?void 0:n.lang}}}),so=e=>(_t("data-v-c4d6d6ea"),e=e(),kt(),e),td={class:"vc-gradient-picker"},rd={class:"vc-gradient-picker__header"},nd={class:"vc-gradient__types"},id={class:"vc-gradient-wrap__types"},ad={class:"vc-picker-degree-input vc-degree-input"},od={class:"vc-degree-input__control"},sd=["value"],ld={class:"vc-degree-input__panel"},ud={class:"vc-degree-input__disk"},cd={class:"vc-gradient-picker__body"},fd={class:"vc-color-range",ref:"colorRangeRef"},dd={class:"vc-color-range__container"},hd={class:"vc-gradient__stop__container"},pd=["title"],gd=so(()=>b("span",{class:"vc-gradient__stop--inner"},null,-1)),vd=[gd],md=["title"],yd=so(()=>b("span",{class:"vc-gradient__stop--inner"},null,-1)),bd=[yd];function Cd(e,t,r,n,i,a){var l,o;const s=z("Angle"),c=z("Board"),u=z("Hue"),f=z("Palette"),d=z("Lightness"),h=z("Alpha"),m=z("Display"),g=z("History");return A(),I("div",td,[b("div",rd,[b("div",null,[Pe(b("div",{class:"back",style:{cursor:"pointer"},onClick:t[0]||(t[0]=(...v)=>e.onBack&&e.onBack(...v))},null,512),[[Kr,e.pickerType==="fk"&&e.advancePanelShow]])]),b("div",nd,[b("div",id,[(A(),I(Ue,null,gt(["linear","radial"],v=>b("div",{class:ce(["vc-gradient__type",{active:e.state.type===v}]),key:v,onClick:t[1]||(t[1]=(...p)=>e.onTypeChange&&e.onTypeChange(...p))},nr(e.lang?e.lang[v]:v),3)),64))]),Pe(b("div",ad,[b("div",od,[b("input",{value:e.state.angle,onBlur:t[2]||(t[2]=(...v)=>e.onDegreeBlur&&e.onDegreeBlur(...v))},null,40,sd),We("deg ")]),b("div",ld,[b("div",ud,[ne(s,{angle:e.state.angle,"onUpdate:angle":t[3]||(t[3]=v=>e.state.angle=v),size:40,onChange:e.onDegreeChange},null,8,["angle","onChange"])])])],512),[[Kr,e.state.type==="linear"]])])]),b("div",cd,[b("div",fd,[b("div",dd,[b("div",{class:"vc-background",style:oe(e.gradientBg)},null,4),b("div",hd,[b("div",{class:ce(["vc-gradient__stop",{"vc-gradient__stop--current":e.state.startActive}]),ref:"startGradientRef",title:(l=e.lang)==null?void 0:l.start,style:oe({left:e.getStartColorLeft+"px",backgroundColor:e.state.startColorRgba})},vd,14,pd),b("div",{class:ce(["vc-gradient__stop",{"vc-gradient__stop--current":!e.state.startActive}]),ref:"stopGradientRef",title:(o=e.lang)==null?void 0:o.end,style:oe({left:e.getEndColorLeft+"px",backgroundColor:e.state.endColorRgba})},bd,14,md)])])],512)]),e.advancePanelShow?(A(),te(c,{key:0,color:e.currentColor,onChange:e.onBoardChange},null,8,["color","onChange"])):$("",!0),e.advancePanelShow?(A(),te(u,{key:1,color:e.currentColor,onChange:e.onHueChange},null,8,["color","onChange"])):$("",!0),e.advancePanelShow?$("",!0):(A(),te(f,{key:2,onChange:e.onCompactChange},null,8,["onChange"])),e.advancePanelShow?$("",!0):(A(),te(d,{key:3,color:e.currentColor,onChange:e.onLightChange},null,8,["color","onChange"])),e.disableAlpha?$("",!0):(A(),te(h,{key:4,color:e.currentColor,onChange:e.onAlphaChange},null,8,["color","onChange"])),ne(m,{color:e.currentColor,"disable-alpha":e.disableAlpha,onChange:e.onDisplayChange},null,8,["color","disable-alpha","onChange"]),e.disableHistory?$("",!0):(A(),te(g,{key:5,round:e.roundHistory,colors:e.historyColors,onChange:e.onCompactChange},null,8,["round","colors","onChange"]))])}const ua=Oe(ed,[["render",Cd],["__scopeId","data-v-c4d6d6ea"]]),wd=Ce({name:"WrapContainer",props:{theme:M.oneOf(["white","black"]).def("white"),showTab:M.bool.def(!1),activeKey:M.oneOf(["pure","gradient"]).def("pure")},emits:["update:activeKey","change"],setup(e,{emit:t}){const r=ge({activeKey:e.activeKey}),n=ga(jn),i=a=>{r.activeKey=a,t("update:activeKey",a),t("change",a)};return ue(()=>e.activeKey,a=>{r.activeKey=a}),{state:r,onActiveKeyChange:i,lang:n==null?void 0:n.lang}}}),xd={class:"vc-colorpicker--container"},Sd={key:0,class:"vc-colorpicker--tabs"},_d={class:"vc-colorpicker--tabs__inner"},kd={class:"vc-btn__content"},Ed={class:"vc-btn__content"};function Ad(e,t,r,n,i,a){var l,o;return A(),I("div",{class:ce(["vc-colorpicker",e.theme])},[b("div",xd,[e.showTab?(A(),I("div",Sd,[b("div",_d,[b("div",{class:ce(["vc-colorpicker--tabs__btn",{"vc-btn-active":e.state.activeKey==="pure"}]),onClick:t[0]||(t[0]=s=>e.onActiveKeyChange("pure"))},[b("button",null,[b("div",kd,nr((l=e.lang)==null?void 0:l.pure),1)])],2),b("div",{class:ce(["vc-colorpicker--tabs__btn",{"vc-btn-active":e.state.activeKey==="gradient"}]),onClick:t[1]||(t[1]=s=>e.onActiveKeyChange("gradient"))},[b("button",null,[b("div",Ed,nr((o=e.lang)==null?void 0:o.gradient),1)])],2),b("div",{class:"vc-colorpicker--tabs__bg",style:oe({width:"50%",left:`calc(${e.state.activeKey==="gradient"?50:0}%)`})},null,4)])])):$("",!0),qr(e.$slots,"default",{},void 0,!0)])],2)}const Od=Oe(wd,[["render",Ad],["__scopeId","data-v-0492277d"]]),Rd={start:"Start",end:"End",pure:"Pure",gradient:"Gradient",linear:"linear",radial:"radial"},Md={start:"开始",end:"结束",pure:"纯色",gradient:"渐变",linear:"线性",radial:"径向"},Td={En:Rd,"ZH-cn":Md},Pd={isWidget:M.bool.def(!1),pickerType:M.oneOf(["fk","chrome"]).def("fk"),shape:M.oneOf(["circle","square"]).def("square"),pureColor:{type:[String,Object],default:"#000000"},gradientColor:M.string.def("linear-gradient(90deg, rgba(255, 255, 255, 1) 0%, rgba(0, 0, 0, 1) 100%)"),format:{type:String,default:"rgb"},disableAlpha:M.bool.def(!1),disableHistory:M.bool.def(!1),roundHistory:M.bool.def(!1),useType:M.oneOf(["pure","gradient","both"]).def("pure"),activeKey:M.oneOf(["pure","gradient"]).def("pure"),lang:{type:String,default:"ZH-cn"},zIndex:M.number.def(9999),pickerContainer:{type:[String,HTMLElement],default:"body"},debounce:M.number.def(100),theme:M.oneOf(["white","black"]).def("white"),blurClose:M.bool.def(!1),defaultPopup:M.bool.def(!1)},Hd=Ce({name:"ColorPicker",components:{FkColorPicker:sa,ChromeColorPicker:la,GradientColorPicker:ua,WrapContainer:Od},inheritAttrs:!1,props:Pd,emits:["update:pureColor","pureColorChange","update:gradientColor","gradientColorChange","update:activeKey","activeKeyChange"],setup(e,{emit:t}){qo(jn,{lang:ie(()=>Td[e.lang||"ZH-cn"])});const r=!!Xo().extra,n=ge({pureColor:e.pureColor||"",activeKey:e.useType==="gradient"?"gradient":e.activeKey,isAdvanceMode:!1}),i=new K("#000"),a=new K("#000"),l=new K(n.pureColor),o=ge({startColor:i,endColor:a,startColorStop:0,endColorStop:100,angle:0,type:"linear",gradientColor:e.gradientColor}),s=Z(l),c=Z(e.defaultPopup),u=Z(null),f=Z(null);let d=null;const h=ie(()=>({background:n.activeKey!=="gradient"?w(n.pureColor).toRgbString():o.gradientColor})),m=ie(()=>n.activeKey==="gradient"?ua.name:e.pickerType==="fk"?sa.name:la.name),g=y=>{n.isAdvanceMode=y},v=ie(()=>{const y={disableAlpha:e.disableAlpha,disableHistory:e.disableHistory,roundHistory:e.roundHistory,pickerType:e.pickerType};return n.activeKey==="gradient"?at(V({},y),{startColor:o.startColor,endColor:o.endColor,angle:o.angle,type:o.type,startColorStop:o.startColorStop,endColorStop:o.endColorStop,onStartColorChange:R=>{o.startColor=R,E()},onEndColorChange:R=>{o.endColor=R,E()},onStartColorStopChange:R=>{o.startColorStop=R,E()},onEndColorStopChange:R=>{o.endColorStop=R,E()},onAngleChange:R=>{o.angle=R,E()},onTypeChange:R=>{o.type=R,E()},onAdvanceChange:g}):at(V({},y),{disableAlpha:e.disableAlpha,disableHistory:e.disableHistory,roundHistory:e.roundHistory,color:s.value,onChange:D,onAdvanceChange:g})}),p=()=>{c.value=!0,d?d.update():T()},C=()=>{c.value=!1},x=Be(()=>{!e.isWidget&&e.blurClose&&C()},100);Yo(f,()=>{C()});const S=()=>{var y,R,H,j;try{const[B]=fi.parse(o.gradientColor);if(B&&B.type.includes("gradient")&&B.colorStops.length>=2){const X=B.colorStops[0],se=B.colorStops[1];o.startColorStop=Number((y=X.length)==null?void 0:y.value)||0,o.endColorStop=Number((R=se.length)==null?void 0:R.value)||0,B.type==="linear-gradient"&&((H=B.orientation)==null?void 0:H.type)==="angular"&&(o.angle=Number((j=B.orientation)==null?void 0:j.value)||0),o.type=B.type.split("-")[0];const[ze,xe,Y,F]=X.value,[Re,_,O,P]=se.value;o.startColor=new K({r:Number(ze),g:Number(xe),b:Number(Y),a:Number(F)}),o.endColor=new K({r:Number(Re),g:Number(_),b:Number(O),a:Number(P)})}}catch(B){console.log(`[Parse Color]: ${B}`)}},E=Be(()=>{const y=k();try{o.gradientColor=fi.stringify(y),t("update:gradientColor",o.gradientColor),t("gradientColorChange",o.gradientColor)}catch(R){console.log(R)}},e.debounce),k=()=>{const y=[],R=o.startColor.RGB.map(B=>B.toString()),H=o.endColor.RGB.map(B=>B.toString()),j=[{type:"rgba",value:[R[0],R[1],R[2],R[3]],length:{value:o.startColorStop+"",type:"%"}},{type:"rgba",value:[H[0],H[1],H[2],H[3]],length:{value:o.endColorStop+"",type:"%"}}];return o.type==="linear"?y.push({type:"linear-gradient",orientation:{type:"angular",value:o.angle+""},colorStops:j}):o.type==="radial"&&y.push({type:"radial-gradient",orientation:[{type:"shape",value:"circle"}],colorStops:j}),y},T=()=>{u.value&&f.value&&(d=Pl(u.value,f.value,{placement:"auto",modifiers:[{name:"offset",options:{offset:[0,8]}},{name:"flip",options:{allowedAutoPlacements:["top","bottom","left","right"],rootBoundary:"viewport"}}]}))},D=y=>{s.value=y,n.pureColor=y.toString(e.format),L()},L=Be(()=>{t("update:pureColor",n.pureColor),t("pureColorChange",n.pureColor)},e.debounce),N=y=>{n.activeKey=y,t("update:activeKey",y),t("activeKeyChange",y)};return St(()=>{S(),d||T()}),ue(()=>e.gradientColor,y=>{y!=o.gradientColor&&(o.gradientColor=y)}),ue(()=>o.gradientColor,()=>{S()}),ue(()=>e.activeKey,y=>{n.activeKey=y}),ue(()=>e.useType,y=>{n.activeKey!=="gradient"&&y==="gradient"?n.activeKey="gradient":n.activeKey="pure"}),ue(()=>e.pureColor,y=>{w.equals(y,n.pureColor)||(n.pureColor=y,s.value=new K(y))},{deep:!0}),{colorCubeRef:u,pickerRef:f,showPicker:c,colorInstance:s,getBgColorStyle:h,getComponentName:m,getBindArgs:v,state:n,hasExtra:r,onColorChange:D,onShowPicker:p,onActiveKeyChange:N,onAutoClose:x}}}),Id={key:0,class:"vc-color-extra"},Ld={key:0,class:"vc-color-extra"};function Dd(e,t,r,n,i,a){const l=z("WrapContainer");return A(),I(Ue,null,[e.isWidget?(A(),te(l,{key:0,"active-key":e.state.activeKey,"onUpdate:activeKey":t[0]||(t[0]=o=>e.state.activeKey=o),"show-tab":e.useType==="both",style:oe({zIndex:e.zIndex}),theme:e.theme,onChange:e.onActiveKeyChange},{default:$e(()=>[(A(),te(ri(e.getComponentName),ni({key:e.getComponentName},e.getBindArgs),null,16)),e.hasExtra?(A(),I("div",Id,[qr(e.$slots,"extra",{},void 0,!0)])):$("",!0)]),_:3},8,["active-key","show-tab","style","theme","onChange"])):$("",!0),e.isWidget?$("",!0):(A(),I(Ue,{key:1},[b("div",{class:ce(["vc-color-wrap transparent",{round:e.shape==="circle"}]),ref:"colorCubeRef"},[b("div",{class:"current-color",style:oe(e.getBgColorStyle),onClick:t[1]||(t[1]=(...o)=>e.onShowPicker&&e.onShowPicker(...o))},null,4)],2),(A(),te(Ko,{to:e.pickerContainer},[Pe(b("div",{ref:"pickerRef",style:oe({zIndex:e.zIndex}),onMouseleave:t[3]||(t[3]=(...o)=>e.onAutoClose&&e.onAutoClose(...o))},[e.showPicker?(A(),te(l,{key:0,"show-tab":e.useType==="both"&&!e.state.isAdvanceMode,theme:e.theme,"active-key":e.state.activeKey,"onUpdate:activeKey":t[2]||(t[2]=o=>e.state.activeKey=o),onChange:e.onActiveKeyChange},{default:$e(()=>[(A(),te(ri(e.getComponentName),ni({key:e.getComponentName},e.getBindArgs),null,16)),e.hasExtra?(A(),I("div",Ld,[qr(e.$slots,"extra",{},void 0,!0)])):$("",!0)]),_:3},8,["show-tab","theme","active-key","onChange"])):$("",!0)],36),[[Kr,e.showPicker]])],8,["to"]))],64))],64)}const $d=Oe(Hd,[["render",Dd],["__scopeId","data-v-354ca836"]]);let Bd={open:(e,t,r)=>{r=V({multiple:!1,maxSize:0,accept:""},r);const n=document.querySelector(`#${t}InputDom`);if(["file","folder"].indexOf(t)<0)return!1;n&&document.body.removeChild(n);const i=document.createElement("input");i.setAttribute("type","file"),r.multiple&&i.setAttribute("multiple","multiple"),r.accept&&i.setAttribute("accept",r.accept),i.setAttribute("style","width:0;height:0;display:none;"),i.id=t+"InputDom",t==="folder"&&i.setAttribute("webkitdirectory",""),document.body.appendChild(i),i.onchange=function(a){if(document.body.removeChild(i),r.maxSize){let l=!1;if(a.target.files.forEach(o=>{l=l||o.size>r.maxSize}),l){e(null,!0);return}}e(a.target.files)},i.click()},openFile(e,t){this.open(e,"file",t)},openFolder(e){this.open(e,"folder",options)}};const Gr={pointStyle:{color:Cesium.Color.RED,pixelSize:10,outlineColor:Cesium.Color.BLACK,outlineWidth:0,show:!0,disableDepthTestDistance:5e6},rectangleStyle:{material:Cesium.Color.GREEN.withAlpha(.5),disableDepthTestDistance:5e6},circleStyle:{material:Cesium.Color.GREEN.withAlpha(.5),disableDepthTestDistance:5e6,clampToGround:!0},polygonStyle:{disableDepthTestDistance:5e6},polylineStyle:{disableDepthTestDistance:5e6,material:Cesium.Color.GREEN,depthFailMaterial:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.GREEN})},textStyle:{text:"我在这儿",disableDepthTestDistance:5e6,distanceDisplayCondition:new Cesium.DistanceDisplayCondition(null,5e6)},modelStyle:{uri:"/assets/models/Cesium_Man.glb",scale:1e3}};let dt=null;const Nd={components:{AButton:No,ColorPicker:$d,ASelect:Bo,ASelectOption:$o},props:{entity:{type:Object,default:null}},data(){return{type:"",fontSize:"",fontFamily:"",data:{},label:{},properties:[]}},mounted(){dt=vr.selectEntity,this.cleanSelect(),this.type=dt.name;const e=pe.entity2Json(dt);this.data=e[this.type]||{},this.label=e.label||{fillColor:"rgba(255,255,255,1)",font:"14px sans-serif",text:""};const t=this.label.font.indexOf(" ");this.fontSize=Number.parseInt(this.label.font.slice(0,Math.max(0,t))||14),this.fontFamily=this.label.font.slice(t+1);const r=e.properties||{},n=[];for(const i in r)n.push([i,r[i]]);this.properties=n},methods:{cleanSelect(){ti.global.GISLayers.isSelected(dt)&&(ti.global.GISLayers.select=null)},save(){this.cleanSelect();const e={};this.type!="label"&&(e[this.type]=this.data),this.label.font=`${this.fontSize}px ${this.fontFamily}`,e.label=this.label;const t={};this.properties.forEach((r,n)=>{t[r[0]]=r[1]}),e.properties=t,vr.save(e),this.$parent.$parent.close()},exportFile(){this.save();const e=JSON.stringify(pe.entity2Json(dt));console.log(dt,e),pa.saveAs(e,`${this.type}.json`,".json"),this.$parent.$parent.close()}}},Vd={class:"shape-setting-container"},jd={class:"form-item"},Fd={class:"el-form-item-content"},zd={class:"form-item"},Wd={class:"el-form-item-content"},Ud={key:0,class:"form-item"},Gd={class:"el-form-item-content"},Kd={key:1,class:"form-item"},qd={class:"el-form-item-content"},Xd={key:2,class:"form-item"},Yd={class:"el-form-item-content"},Jd={key:3,class:"form-item"},Zd={key:4,class:"form-item"},Qd={class:"form-item",style:{"align-items":"start"}},eh={ref:"properties",class:"el-form-item-content"},th=["onUpdate:modelValue"],rh=["onUpdate:modelValue"],nh=["onClick"],ih={class:"footer"};function ah(e,t,r,n,i,a){const l=z("ColorPicker"),o=z("ASelectOption"),s=z("ASelect"),c=z("AButton");return A(),I("div",Vd,[b("div",null,[b("div",jd,[t[9]||(t[9]=b("div",{class:"form-item-label",style:{display:"inline-flex"}},"文本",-1)),b("div",Fd,[Pe(b("input",{"onUpdate:modelValue":t[0]||(t[0]=u=>i.label.text=u),style:{width:"140px"}},null,512),[[Ze,i.label.text]]),ne(l,{"pure-color":i.label.fillColor,"onUpdate:pureColor":t[1]||(t[1]=u=>i.label.fillColor=u),format:"rgb",shape:"square"},null,8,["pure-color"])])]),b("div",zd,[t[14]||(t[14]=b("div",{class:"form-item-label",style:{display:"inline-flex"}},"字体",-1)),b("div",Wd,[Pe(b("input",{"onUpdate:modelValue":t[2]||(t[2]=u=>i.fontSize=u),min:"8",step:"1",style:{width:"60px"},type:"number"},null,512),[[Ze,i.fontSize]]),ne(s,{value:i.fontFamily,"onUpdate:value":t[3]||(t[3]=u=>i.fontFamily=u),style:{width:"130px"}},{default:$e(()=>[ne(o,{value:"sans-serif"},{default:$e(()=>t[10]||(t[10]=[We("sans-serif")])),_:1}),ne(o,{value:"宋体"},{default:$e(()=>t[11]||(t[11]=[We("宋体")])),_:1}),ne(o,{value:"楷体"},{default:$e(()=>t[12]||(t[12]=[We("楷体")])),_:1}),ne(o,{value:"黑体"},{default:$e(()=>t[13]||(t[13]=[We("黑体")])),_:1})]),_:1},8,["value"])])]),i.type=="polyline"?(A(),I("div",Ud,[t[15]||(t[15]=b("div",{class:"form-item-label"},"边框",-1)),b("div",Gd,[Pe(b("input",{"onUpdate:modelValue":t[4]||(t[4]=u=>i.data.width=u),min:"1",step:"1",style:{width:"60px"},type:"number"},null,512),[[Ze,i.data.width]]),ne(l,{"pure-color":i.data.material,"onUpdate:pureColor":t[5]||(t[5]=u=>i.data.material=u),format:"rgb",shape:"square"},null,8,["pure-color"])])])):$("",!0),["polygon","rectangle","ellipse"].includes(i.type)?(A(),I("div",Kd,[t[16]||(t[16]=b("div",{class:"form-item-label"},"填充",-1)),b("div",qd,[ne(l,{"pure-color":i.data.material,"onUpdate:pureColor":t[6]||(t[6]=u=>i.data.material=u),format:"rgb",shape:"square"},null,8,["pure-color"])])])):$("",!0),["billboard","model"].includes(i.type)?(A(),I("div",Xd,[t[17]||(t[17]=b("div",{class:"form-item-label"},"缩放",-1)),b("div",Yd,[Pe(b("input",{"onUpdate:modelValue":t[7]||(t[7]=u=>i.data.scale=u),min:"0.01",step:"0.01",style:{width:"60px"},type:"number"},null,512),[[Ze,i.data.scale]])])])):$("",!0),i.type=="billboard"?(A(),I("div",Jd,t[18]||(t[18]=[b("div",{class:"form-item-label"},"图标",-1),b("div",{class:"el-form-item-content"},[b("div",{class:"markerIconBox"})],-1)]))):$("",!0),i.type=="model"?(A(),I("div",Zd,t[19]||(t[19]=[b("div",{class:"form-item-label"},"模型",-1),b("div",{class:"el-form-item-content"},[b("div",{class:"markerIconBox"})],-1)]))):$("",!0),b("div",Qd,[t[21]||(t[21]=b("div",{class:"form-item-label"},"属性",-1)),b("div",eh,[t[20]||(t[20]=b("div",null,[b("input",{style:{width:"70px","background-color":"cornflowerblue","border-color":"aliceblue"},type:"text",value:"属性名称"}),b("input",{style:{width:"90px","background-color":"cornflowerblue","border-color":"aliceblue"},type:"text",value:"属性值"})],-1)),(A(!0),I(Ue,null,gt(i.properties,(u,f)=>(A(),I("div",{key:f},[Pe(b("input",{"onUpdate:modelValue":d=>u[0]=d,style:{width:"70px"},type:"text"},null,8,th),[[Ze,u[0]]]),Pe(b("input",{"onUpdate:modelValue":d=>u[1]=d,style:{width:"90px"},type:"text"},null,8,rh),[[Ze,u[1]]]),b("div",{class:"properties-btn",onClick:d=>i.properties.splice(f,1)}," - ",8,nh)]))),128)),b("div",{class:"properties-btn",onClick:t[8]||(t[8]=u=>i.properties.push([,]))},"+")],512)])]),b("div",ih,[ne(c,{type:"default",onClick:a.exportFile},{default:$e(()=>t[22]||(t[22]=[We("导出")])),_:1},8,["onClick"]),ne(c,{type:"primary",onClick:a.save},{default:$e(()=>t[23]||(t[23]=[We("保存")])),_:1},8,["onClick"])])])}const oh=Do(Nd,[["render",ah]]);function sh(e){return new Promise((t,r)=>{var n=new FileReader;n.onload=function(i){t(i.target.result)},n.onprogress=function(i){},n.onerror=function(i){r(i.error)},n.readAsText(e)})}const ca={point:"点",polyline:"线",polygon:"多边形",rectangle:"矩形",ellipse:"圆"},fa="layer/shapes";class lh extends fn{constructor(t){super(),this.viewer=t.viewer,this.drawer=t.drawer||new ts(this.viewer),this.shapes=[],this.treeData=ge([{title:"我的绘图",key:fa,children:[]}]),this.checkedKeys=ge([]),this.selectedKeys=ge([]),this.drawer.doubleClick(r=>{console.log(".......entity",r)}),t.el&&this.mount(t.el),this.on("change",r=>{let n=r.map(a=>({title:a.label&&a.label.text._value||ca[a.name],key:a.id,type:(a.polyline?"polyline":"")||(a.polygon?"polygon":"")||(a.rectangle?"rectangle":"")||(a.ellipse?"ellipse":"")||a.name||"未知类型",show:a.show}));console.log(n);let i=Array.from(new Set(n.map(a=>a.type)));this.treeData[0].children=i.map(a=>({title:ca[a],key:a,children:n.filter(l=>l.type==a)})),this.checkedKeys.splice(0,this.checkedKeys.length),console.log(this.checkedKeys),n.filter(a=>a.show).map(a=>a.key).forEach(a=>{this.checkedKeys.push(a)}),console.log(this.checkedKeys)}),this.init(t)}init(t){t.data&&this.loadByJson(t.data),Dt.on("clearAll",r=>{this.removeAll(!0)}),Dt.on("captureEnd",r=>{this.drawer.drawEnd()})}mount(t){if(typeof t=="string"&&(t=document.getElementById(t)),!(t instanceof HTMLElement))return console.log("POIViewer绑定节点无效");let r=ee(Vo,{selectable:!0,blockNode:!0,checkable:!0,checkedKeys:this.checkedKeys,selectedKeys:this.selectedKeys,onCheck:n=>this.show(n),onSelect:n=>{this.showAndZoom(n[0])},treeData:this.treeData,style:{margin:"0px",marginTop:0}},{title:({title:n,key:i,type:a})=>i==fa?ee("div",{style:"display:flex;align-items: center;justify-content: space-between;user-select: none;"},[ee("span",null,n),ee("span",null,[ee("span",{style:"cursor: pointer;padding:0 4px;",title:"导出JSON",onClick:()=>this.exportJson()},[ee("span",{class:"iconfont icon-daochu"})]),ee("span",{style:"cursor: pointer;padding:0 4px;",title:"清空",onClick:()=>this.removeAll()},[ee("span",{class:"iconfont icon-act_qingkong"})]),ee("span",{style:"cursor: pointer;padding:0 4px;margin-right:-4px;",title:"导入本地文件",onClick:()=>this.loadFile()},[ee("span",{class:"iconfont icon-jia"})])])]):a?ee("div",{style:"display:flex;align-items: center;justify-content: space-between;user-select: none;"},[ee("span",null,n),ee("span",null,[ee("span",{style:"cursor: pointer;padding:0 4px;margin-right:-4px;",title:"编辑",onClick:()=>this.showEditor(i)},[ee("span",{class:"iconfont icon-bianji"})]),ee("span",{style:"cursor: pointer;padding:0 4px;margin-right:-4px;",title:"删除",onClick:()=>this.remove(i)},[ee("span",{class:"iconfont icon-shanchu"})])])]):ee("span",null,n)});jo(r,t)}getById(t){return this.shapes.find(r=>r.id==t)}getIndexById(t){return this.shapes.findIndex(r=>r.id==t)}add(t,r){Dt.emit("captureEnd",{target:"shapeViewer"}),this.drawer.drawStart(t,V(V({},Gr[t+"Style"]),r),n=>{this.shapes.push(n),this.emit("change",this.shapes)})}remove(t){let r=this.getIndexById(t);r!=-1&&Rt.confirm("删除","确定要删除吗?",()=>{this.selectEntity===this.shapes[r]&&(this.editor&&Rt.close(this.editor),this.selectEntity=null,this.editor=null),this.drawer.remove(this.shapes[r]),this.shapes.splice(r,1),this.emit("change",this.shapes)})}showEditor(t){let r=this.getById(t);r&&(this.selectEntity=r,this.editor=Rt.open({title:"属性",beforeClose:()=>{this.editor=null,this.selectEntity=null}},[ee(oh,{entity:r})],this.editor))}save(t){this.selectEntity&&(pe.json2Entity(t,this.selectEntity,Gr),console.log(t,this.selectEntity),this.emit("change",this.shapes))}loadByJson(t){if(t instanceof Array)return t.forEach(n=>{this.loadByJson(n)});let r=pe.json2Entity(t,null,Gr);r&&(this.drawer.drawEntity(r),this.shapes.push(r),this.emit("change",this.shapes))}loadFile(){Bd.openFile(t=>{for(let r=0;r<t.length;r++){let n=t[r];n.name.toLocaleLowerCase().indexOf(".json")>0&&sh(n).then(i=>{console.log(i);let a=JSON.parse(i);this.loadByJson(a)})}},{multiple:!1,accept:".json"})}exportJson(){if(this.shapes.length){let t=[];this.shapes.forEach(r=>{t.push(pe.entity2Json(r))}),pa.saveAs(JSON.stringify(t),"export.json",".json")}else Rt.info("提示","绘图数据为空，无需导出")}removeAll(t=!1){let r=()=>{this.shapes.forEach(n=>{this.drawer.remove(n)}),this.shapes=[],this.emit("change",this.shapes)};t?r():Rt.confirm("清空","确定要清空吗?",r)}show(t){this.shapes.forEach(r=>{r.show=t.includes(r.id)}),this.emit("change",this.shapes)}hide(t){if(t instanceof Array)return t.forEach(r=>{this.hide(r)});if(typeof t=="string"){let r=t,n=this.getById(r);return!n||!n.show?void 0:(n.show=!1,this.emit("change",this.shapes),n)}}showAndZoom(t){let r=this.getById(t);r&&(r.show=!0,this.emit("change",this.shapes),this.viewer.flyTo(r))}}let vr=null;function hh(e){return vr=new lh(e),vr}export{ts as D,Dt as a,fn as e,hh as i,vr as s};
