import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SysConfigService } from './sys-config.service';
import { SysConfigController } from './sys-config.controller';
import { SysConfig } from './entities/sys-config.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SysConfig])],
  controllers: [SysConfigController],
  providers: [SysConfigService],
  exports: [SysConfigService],
})
export class SysConfigModule {}
