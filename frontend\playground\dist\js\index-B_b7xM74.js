import{w as Qt,z as en,D as tn,E as yt,G as nn,H as an,J as on,K as St,M as Ht,O as rn,P as ln,Q as sn,R as dn,U as A,W as z,X as W,Y as cn,Z as at,a0 as un,a1 as Ae,a2 as re,a3 as d,a4 as j,a5 as fn,a6 as pn,a7 as vn,a8 as bn,a9 as ne,aa as gn,ab as Re,ac as xt,ad as Ee,ae as hn,af as Ct,ag as lt,ah as st,ai as dt,aj as ct,ak as zt,al as Le,am as Nt,an as ut,ao as ze,ap as mn,aq as $n,ar as yn,as as Sn,at as xn,au as Ne,av as ue,aw as Cn,ax as wt,ay as wn,az as Ke,aA as Tn,aB as Ie,aC as _n,aD as Pn,aE as Bn,aF as Ze,aG as Je}from"./bootstrap-DShsrVit.js";import{a4 as N,O as Q,V as ce,J as D,n as s,F as Rn,a7 as q,au as ft,av as We,a5 as Ge,az as In,U as An,bb as Tt}from"../jse/index-index-BMh_AyeW.js";function En(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}var Mn=800,Ln=16,On=Date.now;function Dn(e){var t=0,n=0;return function(){var a=On(),i=Ln-(a-n);if(n=a,i>0){if(++t>=Mn)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}function Hn(e){return function(){return e}}var je=function(){try{var e=Qt(Object,"defineProperty");return e({},"",{}),e}catch(t){}}(),zn=je?function(e,t){return je(e,"toString",{configurable:!0,enumerable:!1,value:Hn(t),writable:!0})}:en,Nn=Dn(zn);function kn(e,t,n){t=="__proto__"&&je?je(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}var Wn=Object.prototype,Gn=Wn.hasOwnProperty;function jn(e,t,n){var a=e[t];(!(Gn.call(e,t)&&tn(a,n))||n===void 0&&!(t in e))&&kn(e,t,n)}var _t=Math.max;function Kn(e,t,n){return t=_t(t===void 0?e.length-1:t,0),function(){for(var a=arguments,i=-1,r=_t(a.length-t,0),o=Array(r);++i<r;)o[i]=a[t+i];i=-1;for(var c=Array(t+1);++i<t;)c[i]=a[i];return c[t]=n(o),En(e,this,c)}}var Pt=yt?yt.isConcatSpreadable:void 0;function Xn(e){return nn(e)||an(e)||!!(Pt&&e&&e[Pt])}function Fn(e,t,n,a,i){var r=-1,o=e.length;for(n||(n=Xn),i||(i=[]);++r<o;){var c=e[r];n(c)?on(i,c):i[i.length]=c}return i}function Vn(e){var t=e==null?0:e.length;return t?Fn(e):[]}function qn(e){return Nn(Kn(e,void 0,Vn),e+"")}function Yn(e,t,n,a){if(!St(e))return e;t=Ht(t,e);for(var i=-1,r=t.length,o=r-1,c=e;c!=null&&++i<r;){var g=rn(t[i]),f=n;if(g==="__proto__"||g==="constructor"||g==="prototype")return e;if(i!=o){var b=c[g];f=void 0,f===void 0&&(f=St(b)?b:ln(t[i+1])?[]:{})}jn(c,g,f),c=c[g]}return e}function Un(e,t,n){for(var a=-1,i=t.length,r={};++a<i;){var o=t[a],c=sn(e,o);n(c,o)&&Yn(r,Ht(o,e),c)}return r}function Zn(e,t){return Un(e,t,function(n,a){return dn(e,a)})}var kt=qn(function(e,t){return e==null?{}:Zn(e,t)});const we={adjustX:1,adjustY:1},Te=[0,0],Jn={topLeft:{points:["bl","tl"],overflow:we,offset:[0,-4],targetOffset:Te},topCenter:{points:["bc","tc"],overflow:we,offset:[0,-4],targetOffset:Te},topRight:{points:["br","tr"],overflow:we,offset:[0,-4],targetOffset:Te},bottomLeft:{points:["tl","bl"],overflow:we,offset:[0,4],targetOffset:Te},bottomCenter:{points:["tc","bc"],overflow:we,offset:[0,4],targetOffset:Te},bottomRight:{points:["tr","br"],overflow:we,offset:[0,4],targetOffset:Te}};var Qn=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)t.indexOf(a[i])<0&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(n[a[i]]=e[a[i]]);return n};const ea=N({compatConfig:{MODE:3},props:{minOverlayWidthMatchTrigger:{type:Boolean,default:void 0},arrow:{type:Boolean,default:!1},prefixCls:A.string.def("rc-dropdown"),transitionName:String,overlayClassName:A.string.def(""),openClassName:String,animation:A.any,align:A.object,overlayStyle:{type:Object,default:void 0},placement:A.string.def("bottomLeft"),overlay:A.any,trigger:A.oneOfType([A.string,A.arrayOf(A.string)]).def("hover"),alignPoint:{type:Boolean,default:void 0},showAction:A.array,hideAction:A.array,getPopupContainer:Function,visible:{type:Boolean,default:void 0},defaultVisible:{type:Boolean,default:!1},mouseEnterDelay:A.number.def(.15),mouseLeaveDelay:A.number.def(.1)},emits:["visibleChange","overlayClick"],setup(e,t){let{slots:n,emit:a,expose:i}=t;const r=Q(!!e.visible);ce(()=>e.visible,l=>{l!==void 0&&(r.value=l)});const o=Q();i({triggerRef:o});const c=l=>{e.visible===void 0&&(r.value=!1),a("overlayClick",l)},g=l=>{e.visible===void 0&&(r.value=l),a("visibleChange",l)},f=()=>{var l;const u=(l=n.overlay)===null||l===void 0?void 0:l.call(n),m={prefixCls:`${e.prefixCls}-menu`,onClick:c};return s(Rn,{key:un},[e.arrow&&s("div",{class:`${e.prefixCls}-arrow`},null),at(u,m,!1)])},b=D(()=>{const{minOverlayWidthMatchTrigger:l=!e.alignPoint}=e;return l}),y=()=>{var l;const u=(l=n.default)===null||l===void 0?void 0:l.call(n);return r.value&&u?at(u[0],{class:e.openClassName||`${e.prefixCls}-open`},!1):u},h=D(()=>!e.hideAction&&e.trigger.indexOf("contextmenu")!==-1?["click"]:e.hideAction);return()=>{const{prefixCls:l,arrow:u,showAction:m,overlayStyle:w,trigger:x,placement:R,align:L,getPopupContainer:O,transitionName:v,animation:S,overlayClassName:p}=e,T=Qn(e,["prefixCls","arrow","showAction","overlayStyle","trigger","placement","align","getPopupContainer","transitionName","animation","overlayClassName"]);return s(cn,z(z({},T),{},{prefixCls:l,ref:o,popupClassName:W(p,{[`${l}-show-arrow`]:u}),popupStyle:w,builtinPlacements:Jn,action:x,showAction:m,hideAction:h.value||[],popupPlacement:R,popupAlign:L,popupTransitionName:v,popupAnimation:S,popupVisible:r.value,stretch:b.value?"minWidth":"",onPopupVisibleChange:g,getPopupContainer:O}),{popup:f,default:y})}}});function ta(e){const t=q(),n=q(!1);function a(){for(var i=arguments.length,r=new Array(i),o=0;o<i;o++)r[o]=arguments[o];n.value||(Ae.cancel(t.value),t.value=Ae(()=>{e(...r)}))}return ft(()=>{n.value=!0,Ae.cancel(t.value)}),a}function na(e){const t=q([]),n=q(typeof e=="function"?e():e),a=ta(()=>{let r=n.value;t.value.forEach(o=>{r=o(r)}),t.value=[],n.value=r});function i(r){t.value.push(r),a()}return[n,i]}const aa=N({compatConfig:{MODE:3},name:"TabNode",props:{id:{type:String},prefixCls:{type:String},tab:{type:Object},active:{type:Boolean},closable:{type:Boolean},editable:{type:Object},onClick:{type:Function},onResize:{type:Function},renderWrapper:{type:Function},removeAriaLabel:{type:String},onFocus:{type:Function}},emits:["click","resize","remove","focus"],setup(e,t){let{expose:n,attrs:a}=t;const i=Q();function r(g){var f;!((f=e.tab)===null||f===void 0)&&f.disabled||e.onClick(g)}n({domRef:i});function o(g){var f;g.preventDefault(),g.stopPropagation(),e.editable.onEdit("remove",{key:(f=e.tab)===null||f===void 0?void 0:f.key,event:g})}const c=D(()=>{var g;return e.editable&&e.closable!==!1&&!(!((g=e.tab)===null||g===void 0)&&g.disabled)});return()=>{var g;const{prefixCls:f,id:b,active:y,tab:{key:h,tab:l,disabled:u,closeIcon:m},renderWrapper:w,removeAriaLabel:x,editable:R,onFocus:L}=e,O=`${f}-tab`,v=s("div",{key:h,ref:i,class:W(O,{[`${O}-with-remove`]:c.value,[`${O}-active`]:y,[`${O}-disabled`]:u}),style:a.style,onClick:r},[s("div",{role:"tab","aria-selected":y,id:b&&`${b}-tab-${h}`,class:`${O}-btn`,"aria-controls":b&&`${b}-panel-${h}`,"aria-disabled":u,tabindex:u?null:0,onClick:S=>{S.stopPropagation(),r(S)},onKeydown:S=>{[re.SPACE,re.ENTER].includes(S.which)&&(S.preventDefault(),r(S))},onFocus:L},[typeof l=="function"?l():l]),c.value&&s("button",{type:"button","aria-label":x||"remove",tabindex:0,class:`${O}-remove`,onClick:S=>{S.stopPropagation(),o(S)}},[(m==null?void 0:m())||((g=R.removeIcon)===null||g===void 0?void 0:g.call(R))||"×"])]);return w?w(v):v}}}),Bt={width:0,height:0,left:0,top:0};function ia(e,t){const n=Q(new Map);return We(()=>{var a,i;const r=new Map,o=e.value,c=t.value.get((a=o[0])===null||a===void 0?void 0:a.key)||Bt,g=c.left+c.width;for(let f=0;f<o.length;f+=1){const{key:b}=o[f];let y=t.value.get(b);y||(y=t.value.get((i=o[f-1])===null||i===void 0?void 0:i.key)||Bt);const h=r.get(b)||d({},y);h.right=g-h.left-h.width,r.set(b,h)}n.value=new Map(r)}),n}const Wt=N({compatConfig:{MODE:3},name:"AddButton",inheritAttrs:!1,props:{prefixCls:String,editable:{type:Object},locale:{type:Object,default:void 0}},setup(e,t){let{expose:n,attrs:a}=t;const i=Q();return n({domRef:i}),()=>{const{prefixCls:r,editable:o,locale:c}=e;return!o||o.showAdd===!1?null:s("button",{ref:i,type:"button",class:`${r}-nav-add`,style:a.style,"aria-label":(c==null?void 0:c.addAriaLabel)||"Add tab",onClick:g=>{o.onEdit("add",{event:g})}},[o.addIcon?o.addIcon():"+"])}}}),oa={prefixCls:{type:String},id:{type:String},tabs:{type:Object},rtl:{type:Boolean},tabBarGutter:{type:Number},activeKey:{type:[String,Number]},mobile:{type:Boolean},moreIcon:A.any,moreTransitionName:{type:String},editable:{type:Object},locale:{type:Object,default:void 0},removeAriaLabel:String,onTabClick:{type:Function},popupClassName:String,getPopupContainer:ne()},ra=N({compatConfig:{MODE:3},name:"OperationNode",inheritAttrs:!1,props:oa,emits:["tabClick"],slots:Object,setup(e,t){let{attrs:n,slots:a}=t;const[i,r]=j(!1),[o,c]=j(null),g=l=>{const u=e.tabs.filter(x=>!x.disabled);let m=u.findIndex(x=>x.key===o.value)||0;const w=u.length;for(let x=0;x<w;x+=1){m=(m+l+w)%w;const R=u[m];if(!R.disabled){c(R.key);return}}},f=l=>{const{which:u}=l;if(!i.value){[re.DOWN,re.SPACE,re.ENTER].includes(u)&&(r(!0),l.preventDefault());return}switch(u){case re.UP:g(-1),l.preventDefault();break;case re.DOWN:g(1),l.preventDefault();break;case re.ESC:r(!1);break;case re.SPACE:case re.ENTER:o.value!==null&&e.onTabClick(o.value,l);break}},b=D(()=>`${e.id}-more-popup`),y=D(()=>o.value!==null?`${b.value}-${o.value}`:null),h=(l,u)=>{l.preventDefault(),l.stopPropagation(),e.editable.onEdit("remove",{key:u,event:l})};return Ge(()=>{ce(o,()=>{const l=document.getElementById(y.value);l&&l.scrollIntoView&&l.scrollIntoView(!1)},{flush:"post",immediate:!0})}),ce(i,()=>{i.value||c(null)}),fn({}),()=>{var l;const{prefixCls:u,id:m,tabs:w,locale:x,mobile:R,moreIcon:L=((l=a.moreIcon)===null||l===void 0?void 0:l.call(a))||s(pn,null,null),moreTransitionName:O,editable:v,tabBarGutter:S,rtl:p,onTabClick:T,popupClassName:E}=e;if(!w.length)return null;const I=`${u}-dropdown`,K=x==null?void 0:x.dropdownAriaLabel,ee={[p?"marginRight":"marginLeft"]:S};w.length||(ee.visibility="hidden",ee.order=1);const ae=W({[`${I}-rtl`]:p,[`${E}`]:!0}),le=R?null:s(ea,{prefixCls:I,trigger:["hover"],visible:i.value,transitionName:O,onVisibleChange:r,overlayClassName:ae,mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:e.getPopupContainer},{overlay:()=>s(vn,{onClick:B=>{let{key:F,domEvent:_}=B;T(F,_),r(!1)},id:b.value,tabindex:-1,role:"listbox","aria-activedescendant":y.value,selectedKeys:[o.value],"aria-label":K!==void 0?K:"expanded dropdown"},{default:()=>[w.map(B=>{var F,_;const U=v&&B.closable!==!1&&!B.disabled;return s(bn,{key:B.key,id:`${b.value}-${B.key}`,role:"option","aria-controls":m&&`${m}-panel-${B.key}`,disabled:B.disabled},{default:()=>[s("span",null,[typeof B.tab=="function"?B.tab():B.tab]),U&&s("button",{type:"button","aria-label":e.removeAriaLabel||"remove",tabindex:0,class:`${I}-menu-item-remove`,onClick:Z=>{Z.stopPropagation(),h(Z,B.key)}},[((F=B.closeIcon)===null||F===void 0?void 0:F.call(B))||((_=v.removeIcon)===null||_===void 0?void 0:_.call(v))||"×"])]})})]}),default:()=>s("button",{type:"button",class:`${u}-nav-more`,style:ee,tabindex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":b.value,id:`${m}-more`,"aria-expanded":i.value,onKeydown:f},[L])});return s("div",{class:W(`${u}-nav-operations`,n.class),style:n.style},[le,s(Wt,{prefixCls:u,locale:x,editable:v},null)])}}}),Gt=Symbol("tabsContextKey"),la=e=>{In(Gt,e)},jt=()=>An(Gt,{tabs:Q([]),prefixCls:Q()}),sa=.1,Rt=.01,ke=20,It=Math.pow(.995,ke);function da(e,t){const[n,a]=j(),[i,r]=j(0),[o,c]=j(0),[g,f]=j(),b=Q();function y(v){const{screenX:S,screenY:p}=v.touches[0];a({x:S,y:p}),clearInterval(b.value)}function h(v){if(!n.value)return;v.preventDefault();const{screenX:S,screenY:p}=v.touches[0],T=S-n.value.x,E=p-n.value.y;t(T,E),a({x:S,y:p});const I=Date.now();c(I-i.value),r(I),f({x:T,y:E})}function l(){if(!n.value)return;const v=g.value;if(a(null),f(null),v){const S=v.x/o.value,p=v.y/o.value,T=Math.abs(S),E=Math.abs(p);if(Math.max(T,E)<sa)return;let I=S,K=p;b.value=setInterval(()=>{if(Math.abs(I)<Rt&&Math.abs(K)<Rt){clearInterval(b.value);return}I*=It,K*=It,t(I*ke,K*ke)},ke)}}const u=Q();function m(v){const{deltaX:S,deltaY:p}=v;let T=0;const E=Math.abs(S),I=Math.abs(p);E===I?T=u.value==="x"?S:p:E>I?(T=S,u.value="x"):(T=p,u.value="y"),t(-T,-T)&&v.preventDefault()}const w=Q({onTouchStart:y,onTouchMove:h,onTouchEnd:l,onWheel:m});function x(v){w.value.onTouchStart(v)}function R(v){w.value.onTouchMove(v)}function L(v){w.value.onTouchEnd(v)}function O(v){w.value.onWheel(v)}Ge(()=>{var v,S;document.addEventListener("touchmove",R,{passive:!1}),document.addEventListener("touchend",L,{passive:!1}),(v=e.value)===null||v===void 0||v.addEventListener("touchstart",x,{passive:!1}),(S=e.value)===null||S===void 0||S.addEventListener("wheel",O,{passive:!1})}),ft(()=>{document.removeEventListener("touchmove",R),document.removeEventListener("touchend",L)})}function At(e,t){const n=Q(e);function a(i){const r=typeof i=="function"?i(n.value):i;r!==n.value&&t(r,n.value),n.value=r}return[n,a]}const Et={width:0,height:0,left:0,top:0,right:0},ca=()=>({id:{type:String},tabPosition:{type:String},activeKey:{type:[String,Number]},rtl:{type:Boolean},animated:Ee(),editable:Ee(),moreIcon:A.any,moreTransitionName:{type:String},mobile:{type:Boolean},tabBarGutter:{type:Number},renderTabBar:{type:Function},locale:Ee(),popupClassName:String,getPopupContainer:ne(),onTabClick:{type:Function},onTabScroll:{type:Function}}),ua=(e,t)=>{const{offsetWidth:n,offsetHeight:a,offsetTop:i,offsetLeft:r}=e,{width:o,height:c,x:g,y:f}=e.getBoundingClientRect();return Math.abs(o-n)<1?[o,c,g-t.x,f-t.y]:[n,a,r,i]},Mt=N({compatConfig:{MODE:3},name:"TabNavList",inheritAttrs:!1,props:ca(),slots:Object,emits:["tabClick","tabScroll"],setup(e,t){let{attrs:n,slots:a}=t;const{tabs:i,prefixCls:r}=jt(),o=q(),c=q(),g=q(),f=q(),[b,y]=gn(),h=D(()=>e.tabPosition==="top"||e.tabPosition==="bottom"),[l,u]=At(0,(C,$)=>{h.value&&e.onTabScroll&&e.onTabScroll({direction:C>$?"left":"right"})}),[m,w]=At(0,(C,$)=>{!h.value&&e.onTabScroll&&e.onTabScroll({direction:C>$?"top":"bottom"})}),[x,R]=j(0),[L,O]=j(0),[v,S]=j(null),[p,T]=j(null),[E,I]=j(0),[K,ee]=j(0),[ae,le]=na(new Map),B=ia(i,ae),F=D(()=>`${r.value}-nav-operations-hidden`),_=q(0),U=q(0);We(()=>{h.value?e.rtl?(_.value=0,U.value=Math.max(0,x.value-v.value)):(_.value=Math.min(0,v.value-x.value),U.value=0):(_.value=Math.min(0,p.value-L.value),U.value=0)});const Z=C=>C<_.value?_.value:C>U.value?U.value:C,fe=q(),[X,pe]=j(),ve=()=>{pe(Date.now())},me=()=>{clearTimeout(fe.value)},ye=(C,$)=>{C(P=>Z(P+$))};da(o,(C,$)=>{if(h.value){if(v.value>=x.value)return!1;ye(u,C)}else{if(p.value>=L.value)return!1;ye(w,$)}return me(),ve(),!0}),ce(X,()=>{me(),X.value&&(fe.value=setTimeout(()=>{pe(0)},100))});const be=function(){let C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activeKey;const $=B.value.get(C)||{width:0,height:0,left:0,right:0,top:0};if(h.value){let P=l.value;e.rtl?$.right<l.value?P=$.right:$.right+$.width>l.value+v.value&&(P=$.right+$.width-v.value):$.left<-l.value?P=-$.left:$.left+$.width>-l.value+v.value&&(P=-($.left+$.width-v.value)),w(0),u(Z(P))}else{let P=m.value;$.top<-m.value?P=-$.top:$.top+$.height>-m.value+p.value&&(P=-($.top+$.height-p.value)),u(0),w(Z(P))}},se=q(0),Se=q(0);We(()=>{let C,$,P,M,k,H;const ie=B.value;["top","bottom"].includes(e.tabPosition)?(C="width",M=v.value,k=x.value,H=E.value,$=e.rtl?"right":"left",P=Math.abs(l.value)):(C="height",M=p.value,k=x.value,H=K.value,$="top",P=-m.value);let V=M;k+H>M&&k<M&&(V=M-H);const te=i.value;if(!te.length)return[se.value,Se.value]=[0,0];const oe=te.length;let he=oe;for(let J=0;J<oe;J+=1){const de=ie.get(te[J].key)||Et;if(de[$]+de[C]>P+V){he=J-1;break}}let G=0;for(let J=oe-1;J>=0;J-=1)if((ie.get(te[J].key)||Et)[$]<P){G=J+1;break}return[se.value,Se.value]=[G,he]});const $e=()=>{le(()=>{var C;const $=new Map,P=(C=c.value)===null||C===void 0?void 0:C.getBoundingClientRect();return i.value.forEach(M=>{let{key:k}=M;const H=y.value.get(k),ie=(H==null?void 0:H.$el)||H;if(ie){const[V,te,oe,he]=ua(ie,P);$.set(k,{width:V,height:te,left:oe,top:he})}}),$})};ce(()=>i.value.map(C=>C.key).join("%%"),()=>{$e()},{flush:"post"});const xe=()=>{var C,$,P,M,k;const H=((C=o.value)===null||C===void 0?void 0:C.offsetWidth)||0,ie=(($=o.value)===null||$===void 0?void 0:$.offsetHeight)||0,V=((P=f.value)===null||P===void 0?void 0:P.$el)||{},te=V.offsetWidth||0,oe=V.offsetHeight||0;S(H),T(ie),I(te),ee(oe);const he=(((M=c.value)===null||M===void 0?void 0:M.offsetWidth)||0)-te,G=(((k=c.value)===null||k===void 0?void 0:k.offsetHeight)||0)-oe;R(he),O(G),$e()},Ce=D(()=>[...i.value.slice(0,se.value),...i.value.slice(Se.value+1)]),[Yt,Ut]=j(),ge=D(()=>B.value.get(e.activeKey)),bt=q(),gt=()=>{Ae.cancel(bt.value)};ce([ge,h,()=>e.rtl],()=>{const C={};ge.value&&(h.value?(e.rtl?C.right=Re(ge.value.right):C.left=Re(ge.value.left),C.width=Re(ge.value.width)):(C.top=Re(ge.value.top),C.height=Re(ge.value.height))),gt(),bt.value=Ae(()=>{Ut(C)})}),ce([()=>e.activeKey,ge,B,h],()=>{be()},{flush:"post"}),ce([()=>e.rtl,()=>e.tabBarGutter,()=>e.activeKey,()=>i.value],()=>{xe()},{flush:"post"});const Ve=C=>{let{position:$,prefixCls:P,extra:M}=C;if(!M)return null;const k=M==null?void 0:M({position:$});return k?s("div",{class:`${P}-extra-content`},[k]):null};return ft(()=>{me(),gt()}),()=>{const{id:C,animated:$,activeKey:P,rtl:M,editable:k,locale:H,tabPosition:ie,tabBarGutter:V,onTabClick:te}=e,{class:oe,style:he}=n,G=r.value,J=!!Ce.value.length,de=`${G}-nav-wrap`;let qe,Ye,ht,mt;h.value?M?(Ye=l.value>0,qe=l.value+v.value<x.value):(qe=l.value<0,Ye=-l.value+v.value<x.value):(ht=m.value<0,mt=-m.value+p.value<L.value);const He={};ie==="top"||ie==="bottom"?He[M?"marginRight":"marginLeft"]=typeof V=="number"?`${V}px`:V:He.marginTop=typeof V=="number"?`${V}px`:V;const $t=i.value.map((Ue,Zt)=>{const{key:Be}=Ue;return s(aa,{id:C,prefixCls:G,key:Be,tab:Ue,style:Zt===0?void 0:He,closable:Ue.closable,editable:k,active:Be===P,removeAriaLabel:H==null?void 0:H.removeAriaLabel,ref:b(Be),onClick:Jt=>{te(Be,Jt)},onFocus:()=>{be(Be),ve(),o.value&&(M||(o.value.scrollLeft=0),o.value.scrollTop=0)}},a)});return s("div",{role:"tablist",class:W(`${G}-nav`,oe),style:he,onKeydown:()=>{ve()}},[s(Ve,{position:"left",prefixCls:G,extra:a.leftExtra},null),s(xt,{onResize:xe},{default:()=>[s("div",{class:W(de,{[`${de}-ping-left`]:qe,[`${de}-ping-right`]:Ye,[`${de}-ping-top`]:ht,[`${de}-ping-bottom`]:mt}),ref:o},[s(xt,{onResize:xe},{default:()=>[s("div",{ref:c,class:`${G}-nav-list`,style:{transform:`translate(${l.value}px, ${m.value}px)`,transition:X.value?"none":void 0}},[$t,s(Wt,{ref:f,prefixCls:G,locale:H,editable:k,style:d(d({},$t.length===0?void 0:He),{visibility:J?"hidden":null})},null),s("div",{class:W(`${G}-ink-bar`,{[`${G}-ink-bar-animated`]:$.inkBar}),style:Yt.value},null)])]})])]}),s(ra,z(z({},e),{},{removeAriaLabel:H==null?void 0:H.removeAriaLabel,ref:g,prefixCls:G,tabs:Ce.value,class:!J&&F.value}),kt(a,["moreIcon"])),s(Ve,{position:"right",prefixCls:G,extra:a.rightExtra},null),s(Ve,{position:"right",prefixCls:G,extra:a.tabBarExtraContent},null)])}}}),fa=N({compatConfig:{MODE:3},name:"TabPanelList",inheritAttrs:!1,props:{activeKey:{type:[String,Number]},id:{type:String},rtl:{type:Boolean},animated:{type:Object,default:void 0},tabPosition:{type:String},destroyInactiveTabPane:{type:Boolean}},setup(e){const{tabs:t,prefixCls:n}=jt();return()=>{const{id:a,activeKey:i,animated:r,tabPosition:o,rtl:c,destroyInactiveTabPane:g}=e,f=r.tabPane,b=n.value,y=t.value.findIndex(h=>h.key===i);return s("div",{class:`${b}-content-holder`},[s("div",{class:[`${b}-content`,`${b}-content-${o}`,{[`${b}-content-animated`]:f}],style:y&&f?{[c?"marginRight":"marginLeft"]:`-${y}00%`}:null},[t.value.map(h=>at(h.node,{key:h.key,prefixCls:b,tabKey:h.key,id:a,animated:f,active:h.key===i,destroyInactiveTabPane:g}))])])}}});var pa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"};function Lt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},a=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable}))),a.forEach(function(i){va(e,i,n[i])})}return e}function va(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var pt=function(t,n){var a=Lt({},t,n.attrs);return s(hn,Lt({},a,{icon:pa}),null)};pt.displayName="PlusOutlined";pt.inheritAttrs=!1;const ba=e=>{const{componentCls:t,motionDurationSlow:n}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${n}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${n}`}}}}},[Ct(e,"slide-up"),Ct(e,"slide-down")]]},ga=e=>{const{componentCls:t,tabsCardHorizontalPadding:n,tabsCardHeadBackground:a,tabsCardGutter:i,colorSplit:r}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:n,background:a,border:`${e.lineWidth}px ${e.lineType} ${r}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:e.colorPrimary,background:e.colorBgContainer},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:`${i}px`}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:`${i}px`}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${e.borderRadiusLG}px 0 0 ${e.borderRadiusLG}px`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},ha=e=>{const{componentCls:t,tabsHoverColor:n,dropdownEdgeChildVerticalPadding:a}=e;return{[`${t}-dropdown`]:d(d({},dt(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${a}px 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":d(d({},ct),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${e.paddingXXS}px ${e.paddingSM}px`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},ma=e=>{const{componentCls:t,margin:n,colorSplit:a}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:`0 0 ${n}px 0`,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${e.lineWidth}px ${e.lineType} ${a}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:`${n}px`,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:e.controlHeight*1.25,[`${t}-tab`]:{padding:`${e.paddingXS}px ${e.paddingLG}px`,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:`${e.margin}px 0 0 0`},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:`-${e.lineWidth}px`},borderLeft:{_skip_check_:!0,value:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:-e.lineWidth},borderRight:{_skip_check_:!0,value:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},$a=e=>{const{componentCls:t,padding:n}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXS}px 0`,fontSize:e.fontSize}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${n}px 0`,fontSize:e.fontSizeLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXXS*1.5}px ${n}px`}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${e.borderRadius}px ${e.borderRadius}px`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${e.borderRadius}px ${e.borderRadius}px 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${e.borderRadius}px ${e.borderRadius}px 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${e.borderRadius}px 0 0 ${e.borderRadius}px`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:`${e.paddingXS}px ${n}px ${e.paddingXXS*1.5}px`}}}}}},ya=e=>{const{componentCls:t,tabsActiveColor:n,tabsHoverColor:a,iconCls:i,tabsHorizontalGutter:r}=e,o=`${t}-tab`;return{[o]:{position:"relative",display:"inline-flex",alignItems:"center",padding:`${e.paddingSM}px 0`,fontSize:`${e.fontSize}px`,background:"transparent",border:0,outline:"none",cursor:"pointer","&-btn, &-remove":d({"&:focus:not(:focus-visible), &:active":{color:n}},zt(e)),"&-btn":{outline:"none",transition:"all 0.3s"},"&-remove":{flex:"none",marginRight:{_skip_check_:!0,value:-e.marginXXS},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},"&:hover":{color:a},[`&${o}-active ${o}-btn`]:{color:e.colorPrimary,textShadow:e.tabsActiveTextShadow},[`&${o}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${o}-disabled ${o}-btn, &${o}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${o}-remove ${i}`]:{margin:0},[i]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${o} + ${o}`]:{margin:{_skip_check_:!0,value:`0 0 0 ${r}px`}}}},Sa=e=>{const{componentCls:t,tabsHorizontalGutter:n,iconCls:a,tabsCardGutter:i}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:`0 0 0 ${n}px`},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:`${e.marginSM}px`}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:`${e.marginXS}px`},marginLeft:{_skip_check_:!0,value:`-${e.marginXXS}px`},[a]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:`${i}px`},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},xa=e=>{const{componentCls:t,tabsCardHorizontalPadding:n,tabsCardHeight:a,tabsCardGutter:i,tabsHoverColor:r,tabsActiveColor:o,colorSplit:c}=e;return{[t]:d(d(d(d({},dt(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:n,background:"transparent",border:0,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.controlHeightLG/8,transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:d({minWidth:`${a}px`,marginLeft:{_skip_check_:!0,value:`${i}px`},padding:`0 ${e.paddingXS}px`,background:"transparent",border:`${e.lineWidth}px ${e.lineType} ${c}`,borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:r},"&:active, &:focus:not(:focus-visible)":{color:o}},zt(e))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.colorPrimary,pointerEvents:"none"}}),ya(e)),{[`${t}-content`]:{position:"relative",display:"flex",width:"100%","&-animated":{transition:"margin 0.3s"}},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:{outline:"none",flex:"none",width:"100%"}}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping'])`]:{justifyContent:"center"}}}}}},Ca=lt("Tabs",e=>{const t=e.controlHeightLG,n=st(e,{tabsHoverColor:e.colorPrimaryHover,tabsActiveColor:e.colorPrimaryActive,tabsCardHorizontalPadding:`${(t-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth}px ${e.padding}px`,tabsCardHeight:t,tabsCardGutter:e.marginXXS/2,tabsHorizontalGutter:32,tabsCardHeadBackground:e.colorFillAlter,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120});return[$a(n),Sa(n),ma(n),ha(n),ga(n),xa(n),ba(n)]},e=>({zIndexPopup:e.zIndexPopupBase+50}));let Ot=0;const Kt=()=>({prefixCls:{type:String},id:{type:String},popupClassName:String,getPopupContainer:ne(),activeKey:{type:[String,Number]},defaultActiveKey:{type:[String,Number]},direction:ze(),animated:mn([Boolean,Object]),renderTabBar:ne(),tabBarGutter:{type:Number},tabBarStyle:Ee(),tabPosition:ze(),destroyInactiveTabPane:$n(),hideAdd:Boolean,type:ze(),size:ze(),centered:Boolean,onEdit:ne(),onChange:ne(),onTabClick:ne(),onTabScroll:ne(),"onUpdate:activeKey":ne(),locale:Ee(),onPrevClick:ne(),onNextClick:ne(),tabBarExtraContent:A.any});function wa(e){return e.map(t=>{if(yn(t)){const n=d({},t.props||{});for(const[h,l]of Object.entries(n))delete n[h],n[Sn(h)]=l;const a=t.children||{},i=t.key!==void 0?t.key:void 0,{tab:r=a.tab,disabled:o,forceRender:c,closable:g,animated:f,active:b,destroyInactiveTabPane:y}=n;return d(d({key:i},n),{node:t,closeIcon:a.closeIcon,tab:r,disabled:o===""||o,forceRender:c===""||c,closable:g===""||g,animated:f===""||f,active:b===""||b,destroyInactiveTabPane:y===""||y})}return null}).filter(t=>t)}const Ta=N({compatConfig:{MODE:3},name:"InternalTabs",inheritAttrs:!1,props:d(d({},Le(Kt(),{tabPosition:"top",animated:{inkBar:!0,tabPane:!1}})),{tabs:xn()}),slots:Object,setup(e,t){let{attrs:n,slots:a}=t;Ne(e.onPrevClick===void 0&&e.onNextClick===void 0,"Tabs","`onPrevClick / @prevClick` and `onNextClick / @nextClick` has been removed. Please use `onTabScroll / @tabScroll` instead."),Ne(e.tabBarExtraContent===void 0,"Tabs","`tabBarExtraContent` prop has been removed. Please use `rightExtra` slot instead."),Ne(a.tabBarExtraContent===void 0,"Tabs","`tabBarExtraContent` slot is deprecated. Please use `rightExtra` slot instead.");const{prefixCls:i,direction:r,size:o,rootPrefixCls:c,getPopupContainer:g}=ue("tabs",e),[f,b]=Ca(i),y=D(()=>r.value==="rtl"),h=D(()=>{const{animated:p,tabPosition:T}=e;return p===!1||["left","right"].includes(T)?{inkBar:!1,tabPane:!1}:p===!0?{inkBar:!0,tabPane:!0}:d({inkBar:!0,tabPane:!1},typeof p=="object"?p:{})}),[l,u]=j(!1);Ge(()=>{u(Cn())});const[m,w]=wt(()=>{var p;return(p=e.tabs[0])===null||p===void 0?void 0:p.key},{value:D(()=>e.activeKey),defaultValue:e.defaultActiveKey}),[x,R]=j(()=>e.tabs.findIndex(p=>p.key===m.value));We(()=>{var p;let T=e.tabs.findIndex(E=>E.key===m.value);T===-1&&(T=Math.max(0,Math.min(x.value,e.tabs.length-1)),w((p=e.tabs[T])===null||p===void 0?void 0:p.key)),R(T)});const[L,O]=wt(null,{value:D(()=>e.id)}),v=D(()=>l.value&&!["left","right"].includes(e.tabPosition)?"top":e.tabPosition);Ge(()=>{e.id||(O(`rc-tabs-${Ot}`),Ot+=1)});const S=(p,T)=>{var E,I;(E=e.onTabClick)===null||E===void 0||E.call(e,p,T);const K=p!==m.value;w(p),K&&((I=e.onChange)===null||I===void 0||I.call(e,p))};return la({tabs:D(()=>e.tabs),prefixCls:i}),()=>{const{id:p,type:T,tabBarGutter:E,tabBarStyle:I,locale:K,destroyInactiveTabPane:ee,renderTabBar:ae=a.renderTabBar,onTabScroll:le,hideAdd:B,centered:F}=e,_={id:L.value,activeKey:m.value,animated:h.value,tabPosition:v.value,rtl:y.value,mobile:l.value};let U;T==="editable-card"&&(U={onEdit:(pe,ve)=>{let{key:me,event:ye}=ve;var be;(be=e.onEdit)===null||be===void 0||be.call(e,pe==="add"?ye:me,pe)},removeIcon:()=>s(wn,null,null),addIcon:a.addIcon?a.addIcon:()=>s(pt,null,null),showAdd:B!==!0});let Z;const fe=d(d({},_),{moreTransitionName:`${c.value}-slide-up`,editable:U,locale:K,tabBarGutter:E,onTabClick:S,onTabScroll:le,style:I,getPopupContainer:g.value,popupClassName:W(e.popupClassName,b.value)});ae?Z=ae(d(d({},fe),{DefaultTabBar:Mt})):Z=s(Mt,fe,kt(a,["moreIcon","leftExtra","rightExtra","tabBarExtraContent"]));const X=i.value;return f(s("div",z(z({},n),{},{id:p,class:W(X,`${X}-${v.value}`,{[b.value]:!0,[`${X}-${o.value}`]:o.value,[`${X}-card`]:["card","editable-card"].includes(T),[`${X}-editable-card`]:T==="editable-card",[`${X}-centered`]:F,[`${X}-mobile`]:l.value,[`${X}-editable`]:T==="editable-card",[`${X}-rtl`]:y.value},n.class)}),[Z,s(fa,z(z({destroyInactiveTabPane:ee},_),{},{animated:h.value}),null)]))}}}),_e=N({compatConfig:{MODE:3},name:"ATabs",inheritAttrs:!1,props:Le(Kt(),{tabPosition:"top",animated:{inkBar:!0,tabPane:!1}}),slots:Object,setup(e,t){let{attrs:n,slots:a,emit:i}=t;const r=o=>{i("update:activeKey",o),i("change",o)};return()=>{var o;const c=wa(Nt((o=a.default)===null||o===void 0?void 0:o.call(a)));return s(Ta,z(z(z({},ut(e,["onUpdate:activeKey"])),n),{},{onChange:r,tabs:c}),a)}}}),_a=()=>({tab:A.any,disabled:{type:Boolean},forceRender:{type:Boolean},closable:{type:Boolean},animated:{type:Boolean},active:{type:Boolean},destroyInactiveTabPane:{type:Boolean},prefixCls:{type:String},tabKey:{type:[String,Number]},id:{type:String}}),it=N({compatConfig:{MODE:3},name:"ATabPane",inheritAttrs:!1,__ANT_TAB_PANE:!0,props:_a(),slots:Object,setup(e,t){let{attrs:n,slots:a}=t;const i=Q(e.forceRender);ce([()=>e.active,()=>e.destroyInactiveTabPane],()=>{e.active?i.value=!0:e.destroyInactiveTabPane&&(i.value=!1)},{immediate:!0});const r=D(()=>e.active?{}:e.animated?{visibility:"hidden",height:0,overflowY:"hidden"}:{display:"none"});return()=>{var o;const{prefixCls:c,forceRender:g,id:f,active:b,tabKey:y}=e;return s("div",{id:f&&`${f}-panel-${y}`,role:"tabpanel",tabindex:b?0:-1,"aria-labelledby":f&&`${f}-tab-${y}`,"aria-hidden":!b,style:[r.value,n.style],class:[`${c}-tabpane`,b&&`${c}-tabpane-active`,n.class]},[(b||i.value||g)&&((o=a.default)===null||o===void 0?void 0:o.call(a))])}}});_e.TabPane=it;_e.install=function(e){return e.component(_e.name,_e),e.component(it.name,it),e};const Pa=e=>{const{antCls:t,componentCls:n,cardHeadHeight:a,cardPaddingBase:i,cardHeadTabsMarginBottom:r}=e;return d(d({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:a,marginBottom:-1,padding:`0 ${i}px`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,background:"transparent",borderBottom:`${e.lineWidth}px ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`},Ke()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":d(d({display:"inline-block",flex:1},ct),{[`
          > ${n}-typography,
          > ${n}-typography-edit-content
        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:r,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${e.lineWidth}px ${e.lineType} ${e.colorBorderSecondary}`}}})},Ba=e=>{const{cardPaddingBase:t,colorBorderSecondary:n,cardShadow:a,lineWidth:i}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`
      ${i}px 0 0 0 ${n},
      0 ${i}px 0 0 ${n},
      ${i}px ${i}px 0 0 ${n},
      ${i}px 0 0 0 ${n} inset,
      0 ${i}px 0 0 ${n} inset;
    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:a}}},Ra=e=>{const{componentCls:t,iconCls:n,cardActionsLiMargin:a,cardActionsIconSize:i,colorBorderSecondary:r}=e;return d(d({margin:0,padding:0,listStyle:"none",background:e.colorBgContainer,borderTop:`${e.lineWidth}px ${e.lineType} ${r}`,display:"flex",borderRadius:`0 0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px `},Ke()),{"& > li":{margin:a,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.cardActionsIconSize*2,fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${n}`]:{display:"inline-block",width:"100%",color:e.colorTextDescription,lineHeight:`${e.fontSize*e.lineHeight}px`,transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${n}`]:{fontSize:i,lineHeight:`${i*e.lineHeight}px`}},"&:not(:last-child)":{borderInlineEnd:`${e.lineWidth}px ${e.lineType} ${r}`}}})},Ia=e=>d(d({margin:`-${e.marginXXS}px 0`,display:"flex"},Ke()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":d({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},ct),"&-description":{color:e.colorTextDescription}}),Aa=e=>{const{componentCls:t,cardPaddingBase:n,colorFillAlter:a}=e;return{[`${t}-head`]:{padding:`0 ${n}px`,background:a,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${e.padding}px ${n}px`}}},Ea=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},Ma=e=>{const{componentCls:t,cardShadow:n,cardHeadPadding:a,colorBorderSecondary:i,boxShadow:r,cardPaddingBase:o}=e;return{[t]:d(d({},dt(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:r},[`${t}-head`]:Pa(e),[`${t}-extra`]:{marginInlineStart:"auto",color:"",fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:d({padding:o,borderRadius:` 0 0 ${e.borderRadiusLG}px ${e.borderRadiusLG}px`},Ke()),[`${t}-grid`]:Ba(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%"},img:{borderRadius:`${e.borderRadiusLG}px ${e.borderRadiusLG}px 0 0`}},[`${t}-actions`]:Ra(e),[`${t}-meta`]:Ia(e)}),[`${t}-bordered`]:{border:`${e.lineWidth}px ${e.lineType} ${i}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:n}},[`${t}-contain-grid`]:{[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:-e.lineWidth,marginInlineStart:-e.lineWidth,padding:0}},[`${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:a}}},[`${t}-type-inner`]:Aa(e),[`${t}-loading`]:Ea(e),[`${t}-rtl`]:{direction:"rtl"}}},La=e=>{const{componentCls:t,cardPaddingSM:n,cardHeadHeightSM:a}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:a,padding:`0 ${n}px`,fontSize:e.fontSize,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:n}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{minHeight:a,paddingTop:0,display:"flex",alignItems:"center"}}}}},Oa=lt("Card",e=>{const t=st(e,{cardShadow:e.boxShadowCard,cardHeadHeight:e.fontSizeLG*e.lineHeightLG+e.padding*2,cardHeadHeightSM:e.fontSize*e.lineHeight+e.paddingXS*2,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardHeadTabsMarginBottom:-e.padding-e.lineWidth,cardActionsLiMargin:`${e.paddingSM}px 0`,cardActionsIconSize:e.fontSize,cardPaddingSM:12});return[Ma(t),La(t)]}),Da=()=>({prefixCls:String,width:{type:[Number,String]}}),vt=N({compatConfig:{MODE:3},name:"SkeletonTitle",props:Da(),setup(e){return()=>{const{prefixCls:t,width:n}=e,a=typeof n=="number"?`${n}px`:n;return s("h3",{class:t,style:{width:a}},null)}}}),Ha=()=>({prefixCls:String,width:{type:[Number,String,Array]},rows:Number}),za=N({compatConfig:{MODE:3},name:"SkeletonParagraph",props:Ha(),setup(e){const t=n=>{const{width:a,rows:i=2}=e;if(Array.isArray(a))return a[n];if(i-1===n)return a};return()=>{const{prefixCls:n,rows:a}=e,i=[...Array(a)].map((r,o)=>{const c=t(o);return s("li",{key:o,style:{width:typeof c=="number"?`${c}px`:c}},null)});return s("ul",{class:n},[i])}}}),Xe=()=>({prefixCls:String,size:[String,Number],shape:String,active:{type:Boolean,default:void 0}}),Oe=e=>{const{prefixCls:t,size:n,shape:a}=e,i=W({[`${t}-lg`]:n==="large",[`${t}-sm`]:n==="small"}),r=W({[`${t}-circle`]:a==="circle",[`${t}-square`]:a==="square",[`${t}-round`]:a==="round"}),o=typeof n=="number"?{width:`${n}px`,height:`${n}px`,lineHeight:`${n}px`}:{};return s("span",{class:W(t,i,r),style:o},null)};Oe.displayName="SkeletonElement";const Na=new Tn("ant-skeleton-loading",{"0%":{transform:"translateX(-37.5%)"},"100%":{transform:"translateX(37.5%)"}}),Fe=e=>({height:e,lineHeight:`${e}px`}),Pe=e=>d({width:e},Fe(e)),ka=e=>({position:"relative",zIndex:0,overflow:"hidden",background:"transparent","&::after":{position:"absolute",top:0,insetInlineEnd:"-150%",bottom:0,insetInlineStart:"-150%",background:e.skeletonLoadingBackground,animationName:Na,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite",content:'""'}}),Qe=e=>d({width:e*5,minWidth:e*5},Fe(e)),Wa=e=>{const{skeletonAvatarCls:t,color:n,controlHeight:a,controlHeightLG:i,controlHeightSM:r}=e;return{[`${t}`]:d({display:"inline-block",verticalAlign:"top",background:n},Pe(a)),[`${t}${t}-circle`]:{borderRadius:"50%"},[`${t}${t}-lg`]:d({},Pe(i)),[`${t}${t}-sm`]:d({},Pe(r))}},Ga=e=>{const{controlHeight:t,borderRadiusSM:n,skeletonInputCls:a,controlHeightLG:i,controlHeightSM:r,color:o}=e;return{[`${a}`]:d({display:"inline-block",verticalAlign:"top",background:o,borderRadius:n},Qe(t)),[`${a}-lg`]:d({},Qe(i)),[`${a}-sm`]:d({},Qe(r))}},Dt=e=>d({width:e},Fe(e)),ja=e=>{const{skeletonImageCls:t,imageSizeBase:n,color:a,borderRadiusSM:i}=e;return{[`${t}`]:d(d({display:"flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",background:a,borderRadius:i},Dt(n*2)),{[`${t}-path`]:{fill:"#bfbfbf"},[`${t}-svg`]:d(d({},Dt(n)),{maxWidth:n*4,maxHeight:n*4}),[`${t}-svg${t}-svg-circle`]:{borderRadius:"50%"}}),[`${t}${t}-circle`]:{borderRadius:"50%"}}},et=(e,t,n)=>{const{skeletonButtonCls:a}=e;return{[`${n}${a}-circle`]:{width:t,minWidth:t,borderRadius:"50%"},[`${n}${a}-round`]:{borderRadius:t}}},tt=e=>d({width:e*2,minWidth:e*2},Fe(e)),Ka=e=>{const{borderRadiusSM:t,skeletonButtonCls:n,controlHeight:a,controlHeightLG:i,controlHeightSM:r,color:o}=e;return d(d(d(d(d({[`${n}`]:d({display:"inline-block",verticalAlign:"top",background:o,borderRadius:t,width:a*2,minWidth:a*2},tt(a))},et(e,a,n)),{[`${n}-lg`]:d({},tt(i))}),et(e,i,`${n}-lg`)),{[`${n}-sm`]:d({},tt(r))}),et(e,r,`${n}-sm`))},Xa=e=>{const{componentCls:t,skeletonAvatarCls:n,skeletonTitleCls:a,skeletonParagraphCls:i,skeletonButtonCls:r,skeletonInputCls:o,skeletonImageCls:c,controlHeight:g,controlHeightLG:f,controlHeightSM:b,color:y,padding:h,marginSM:l,borderRadius:u,skeletonTitleHeight:m,skeletonBlockRadius:w,skeletonParagraphLineHeight:x,controlHeightXS:R,skeletonParagraphMarginTop:L}=e;return{[`${t}`]:{display:"table",width:"100%",[`${t}-header`]:{display:"table-cell",paddingInlineEnd:h,verticalAlign:"top",[`${n}`]:d({display:"inline-block",verticalAlign:"top",background:y},Pe(g)),[`${n}-circle`]:{borderRadius:"50%"},[`${n}-lg`]:d({},Pe(f)),[`${n}-sm`]:d({},Pe(b))},[`${t}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[`${a}`]:{width:"100%",height:m,background:y,borderRadius:w,[`+ ${i}`]:{marginBlockStart:b}},[`${i}`]:{padding:0,"> li":{width:"100%",height:x,listStyle:"none",background:y,borderRadius:w,"+ li":{marginBlockStart:R}}},[`${i}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${t}-content`]:{[`${a}, ${i} > li`]:{borderRadius:u}}},[`${t}-with-avatar ${t}-content`]:{[`${a}`]:{marginBlockStart:l,[`+ ${i}`]:{marginBlockStart:L}}},[`${t}${t}-element`]:d(d(d(d({display:"inline-block",width:"auto"},Ka(e)),Wa(e)),Ga(e)),ja(e)),[`${t}${t}-block`]:{width:"100%",[`${r}`]:{width:"100%"},[`${o}`]:{width:"100%"}},[`${t}${t}-active`]:{[`
        ${a},
        ${i} > li,
        ${n},
        ${r},
        ${o},
        ${c}
      `]:d({},ka(e))}}},De=lt("Skeleton",e=>{const{componentCls:t}=e,n=st(e,{skeletonAvatarCls:`${t}-avatar`,skeletonTitleCls:`${t}-title`,skeletonParagraphCls:`${t}-paragraph`,skeletonButtonCls:`${t}-button`,skeletonInputCls:`${t}-input`,skeletonImageCls:`${t}-image`,imageSizeBase:e.controlHeight*1.5,skeletonTitleHeight:e.controlHeight/2,skeletonBlockRadius:e.borderRadiusSM,skeletonParagraphLineHeight:e.controlHeight/2,skeletonParagraphMarginTop:e.marginLG+e.marginXXS,borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${e.color} 25%, ${e.colorGradientEnd} 37%, ${e.color} 63%)`,skeletonLoadingMotionDuration:"1.4s"});return[Xa(n)]},e=>{const{colorFillContent:t,colorFill:n}=e;return{color:t,colorGradientEnd:n}}),Fa=()=>({active:{type:Boolean,default:void 0},loading:{type:Boolean,default:void 0},prefixCls:String,avatar:{type:[Boolean,Object],default:void 0},title:{type:[Boolean,Object],default:void 0},paragraph:{type:[Boolean,Object],default:void 0},round:{type:Boolean,default:void 0}});function nt(e){return e&&typeof e=="object"?e:{}}function Va(e,t){return e&&!t?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function qa(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}function Ya(e,t){const n={};return(!e||!t)&&(n.width="61%"),!e&&t?n.rows=3:n.rows=2,n}const Y=N({compatConfig:{MODE:3},name:"ASkeleton",props:Le(Fa(),{avatar:!1,title:!0,paragraph:!0}),setup(e,t){let{slots:n}=t;const{prefixCls:a,direction:i}=ue("skeleton",e),[r,o]=De(a);return()=>{var c;const{loading:g,avatar:f,title:b,paragraph:y,active:h,round:l}=e,u=a.value;if(g||e.loading===void 0){const m=!!f||f==="",w=!!b||b==="",x=!!y||y==="";let R;if(m){const v=d(d({prefixCls:`${u}-avatar`},Va(w,x)),nt(f));R=s("div",{class:`${u}-header`},[s(Oe,v,null)])}let L;if(w||x){let v;if(w){const p=d(d({prefixCls:`${u}-title`},qa(m,x)),nt(b));v=s(vt,p,null)}let S;if(x){const p=d(d({prefixCls:`${u}-paragraph`},Ya(m,w)),nt(y));S=s(za,p,null)}L=s("div",{class:`${u}-content`},[v,S])}const O=W(u,{[`${u}-with-avatar`]:m,[`${u}-active`]:h,[`${u}-rtl`]:i.value==="rtl",[`${u}-round`]:l,[o.value]:!0});return r(s("div",{class:O},[R,L]))}return(c=n.default)===null||c===void 0?void 0:c.call(n)}}}),Ua=()=>d(d({},Xe()),{size:String,block:Boolean}),Xt=N({compatConfig:{MODE:3},name:"ASkeletonButton",props:Le(Ua(),{size:"default"}),setup(e){const{prefixCls:t}=ue("skeleton",e),[n,a]=De(t),i=D(()=>W(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active,[`${t.value}-block`]:e.block},a.value));return()=>n(s("div",{class:i.value},[s(Oe,z(z({},e),{},{prefixCls:`${t.value}-button`}),null)]))}}),Ft=N({compatConfig:{MODE:3},name:"ASkeletonInput",props:d(d({},ut(Xe(),["shape"])),{size:String,block:Boolean}),setup(e){const{prefixCls:t}=ue("skeleton",e),[n,a]=De(t),i=D(()=>W(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active,[`${t.value}-block`]:e.block},a.value));return()=>n(s("div",{class:i.value},[s(Oe,z(z({},e),{},{prefixCls:`${t.value}-input`}),null)]))}}),Za="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",Vt=N({compatConfig:{MODE:3},name:"ASkeletonImage",props:ut(Xe(),["size","shape","active"]),setup(e){const{prefixCls:t}=ue("skeleton",e),[n,a]=De(t),i=D(()=>W(t.value,`${t.value}-element`,a.value));return()=>n(s("div",{class:i.value},[s("div",{class:`${t.value}-image`},[s("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",class:`${t.value}-image-svg`},[s("path",{d:Za,class:`${t.value}-image-path`},null)])])]))}}),Ja=()=>d(d({},Xe()),{shape:String}),qt=N({compatConfig:{MODE:3},name:"ASkeletonAvatar",props:Le(Ja(),{size:"default",shape:"circle"}),setup(e){const{prefixCls:t}=ue("skeleton",e),[n,a]=De(t),i=D(()=>W(t.value,`${t.value}-element`,{[`${t.value}-active`]:e.active},a.value));return()=>n(s("div",{class:i.value},[s(Oe,z(z({},e),{},{prefixCls:`${t.value}-avatar`}),null)]))}});Y.Button=Xt;Y.Avatar=qt;Y.Input=Ft;Y.Image=Vt;Y.Title=vt;Y.install=function(e){return e.component(Y.name,Y),e.component(Y.Button.name,Xt),e.component(Y.Avatar.name,qt),e.component(Y.Input.name,Ft),e.component(Y.Image.name,Vt),e.component(Y.Title.name,vt),e};const{TabPane:Qa}=_e,ei=()=>({prefixCls:String,title:A.any,extra:A.any,bordered:{type:Boolean,default:!0},bodyStyle:{type:Object,default:void 0},headStyle:{type:Object,default:void 0},loading:{type:Boolean,default:!1},hoverable:{type:Boolean,default:!1},type:{type:String},size:{type:String},actions:A.any,tabList:{type:Array},tabBarExtraContent:A.any,activeTabKey:String,defaultActiveTabKey:String,cover:A.any,onTabChange:{type:Function}}),Me=N({compatConfig:{MODE:3},name:"ACard",inheritAttrs:!1,props:ei(),slots:Object,setup(e,t){let{slots:n,attrs:a}=t;const{prefixCls:i,direction:r,size:o}=ue("card",e),[c,g]=Oa(i),f=h=>h.map((u,m)=>Tt(u)&&!Pn(u)||!Tt(u)?s("li",{style:{width:`${100/h.length}%`},key:`action-${m}`},[s("span",null,[u])]):null),b=h=>{var l;(l=e.onTabChange)===null||l===void 0||l.call(e,h)},y=function(){let h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],l;return h.forEach(u=>{u&&Bn(u.type)&&u.type.__ANT_CARD_GRID&&(l=!0)}),l};return()=>{var h,l,u,m,w,x;const{headStyle:R={},bodyStyle:L={},loading:O,bordered:v=!0,type:S,tabList:p,hoverable:T,activeTabKey:E,defaultActiveTabKey:I,tabBarExtraContent:K=Ie((h=n.tabBarExtraContent)===null||h===void 0?void 0:h.call(n)),title:ee=Ie((l=n.title)===null||l===void 0?void 0:l.call(n)),extra:ae=Ie((u=n.extra)===null||u===void 0?void 0:u.call(n)),actions:le=Ie((m=n.actions)===null||m===void 0?void 0:m.call(n)),cover:B=Ie((w=n.cover)===null||w===void 0?void 0:w.call(n))}=e,F=Nt((x=n.default)===null||x===void 0?void 0:x.call(n)),_=i.value,U={[`${_}`]:!0,[g.value]:!0,[`${_}-loading`]:O,[`${_}-bordered`]:v,[`${_}-hoverable`]:!!T,[`${_}-contain-grid`]:y(F),[`${_}-contain-tabs`]:p&&p.length,[`${_}-${o.value}`]:o.value,[`${_}-type-${S}`]:!!S,[`${_}-rtl`]:r.value==="rtl"},Z=s(Y,{loading:!0,active:!0,paragraph:{rows:4},title:!1},{default:()=>[F]}),fe=E!==void 0,X={size:"large",[fe?"activeKey":"defaultActiveKey"]:fe?E:I,onChange:b,class:`${_}-head-tabs`};let pe;const ve=p&&p.length?s(_e,X,{default:()=>[p.map(se=>{const{tab:Se,slots:$e}=se,xe=$e==null?void 0:$e.tab;Ne(!$e,"Card","tabList slots is deprecated, Please use `customTab` instead.");let Ce=Se!==void 0?Se:n[xe]?n[xe](se):null;return Ce=_n(n,"customTab",se,()=>[Ce]),s(Qa,{tab:Ce,key:se.key,disabled:se.disabled},null)})],rightExtra:K?()=>K:null}):null;(ee||ae||ve)&&(pe=s("div",{class:`${_}-head`,style:R},[s("div",{class:`${_}-head-wrapper`},[ee&&s("div",{class:`${_}-head-title`},[ee]),ae&&s("div",{class:`${_}-extra`},[ae])]),ve]));const me=B?s("div",{class:`${_}-cover`},[B]):null,ye=s("div",{class:`${_}-body`,style:L},[O?Z:F]),be=le&&le.length?s("ul",{class:`${_}-actions`},[f(le)]):null;return c(s("div",z(z({ref:"cardContainerRef"},a),{},{class:[U,a.class]}),[pe,me,F.length?ye:null,be]))}}}),ti=()=>({prefixCls:String,title:Je(),description:Je(),avatar:Je()}),ot=N({compatConfig:{MODE:3},name:"ACardMeta",props:ti(),slots:Object,setup(e,t){let{slots:n}=t;const{prefixCls:a}=ue("card",e);return()=>{const i={[`${a.value}-meta`]:!0},r=Ze(n,e,"avatar"),o=Ze(n,e,"title"),c=Ze(n,e,"description"),g=r?s("div",{class:`${a.value}-meta-avatar`},[r]):null,f=o?s("div",{class:`${a.value}-meta-title`},[o]):null,b=c?s("div",{class:`${a.value}-meta-description`},[c]):null,y=f||b?s("div",{class:`${a.value}-meta-detail`},[f,b]):null;return s("div",{class:i},[g,y])}}}),ni=()=>({prefixCls:String,hoverable:{type:Boolean,default:!0}}),rt=N({compatConfig:{MODE:3},name:"ACardGrid",__ANT_CARD_GRID:!0,props:ni(),setup(e,t){let{slots:n}=t;const{prefixCls:a}=ue("card",e),i=D(()=>({[`${a.value}-grid`]:!0,[`${a.value}-grid-hoverable`]:e.hoverable}));return()=>{var r;return s("div",{class:i.value},[(r=n.default)===null||r===void 0?void 0:r.call(n)])}}});Me.Meta=ot;Me.Grid=rt;Me.install=function(e){return e.component(Me.name,Me),e.component(ot.name,ot),e.component(rt.name,rt),e};export{Me as C};
