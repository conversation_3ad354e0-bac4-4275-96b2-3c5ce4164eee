import { default as proj4 } from 'proj4';
proj4.defs([
  [
    'EPSG:4326',
    '+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees',
  ],
  [
    'CRS:84',
    '+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees',
  ],
  [
    'EPSG:4269',
    '+title=NAD83 (long/lat) +proj=longlat +a=6378137.0 +b=6356752.31414036 +ellps=GRS80 +datum=NAD83 +units=degrees',
  ],
  [
    'EPSG:102100',
    'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["EPSG","102100"]]',
  ],
  [
    'EPSG:3395',
    '+proj=merc +lon_0=0 +k=1 +x_0=0 +y_0=0 +ellps=WGS84 +datum=WGS84 +units=m +no_defs',
  ],
  //# Xian 1980
  ['EPSG:4610', '+proj=longlat +a=6378140 +b=6356755.288157528 +no_defs'],
  //# China Geodetic Coordinate System 2000
  ['EPSG:4490', '+proj=longlat +ellps=GRS80 +no_defs'],
  //# WGS 84
  //['EPSG:4326','+proj=longlat +datum=WGS84 +no_defs'],
  //# CGCS2000 / Gauss-Kruger zone 13
  [
    'EPSG:4491',
    '+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=13500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger zone 14
  [
    'EPSG:4492',
    '+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=14500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger zone 15
  [
    'EPSG:4493',
    '+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=15500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger zone 16
  [
    'EPSG:4494',
    '+proj=tmerc +lat_0=0 +lon_0=93 +k=1 +x_0=16500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger zone 17
  [
    'EPSG:4495',
    '+proj=tmerc +lat_0=0 +lon_0=99 +k=1 +x_0=17500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger zone 18
  [
    'EPSG:4496',
    '+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=18500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger zone 19
  [
    'EPSG:4497',
    '+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=19500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger zone 20
  [
    'EPSG:4498',
    '+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=20500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger zone 21
  [
    'EPSG:4499',
    '+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=21500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger zone 22
  [
    'EPSG:4500',
    '+proj=tmerc +lat_0=0 +lon_0=129 +k=1 +x_0=22500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger zone 23
  [
    'EPSG:4501',
    '+proj=tmerc +lat_0=0 +lon_0=135 +k=1 +x_0=23500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger CM 75E
  [
    'EPSG:4502',
    '+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger CM 81E
  [
    'EPSG:4503',
    '+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger CM 87E
  [
    'EPSG:4504',
    '+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger CM 93E
  [
    'EPSG:4505',
    '+proj=tmerc +lat_0=0 +lon_0=93 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger CM 99E
  [
    'EPSG:4506',
    '+proj=tmerc +lat_0=0 +lon_0=99 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger CM 105E
  [
    'EPSG:4507',
    '+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger CM 111E
  [
    'EPSG:4508',
    '+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger CM 117E
  [
    'EPSG:4509',
    '+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger CM 123E
  [
    'EPSG:4510',
    '+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger CM 129E
  [
    'EPSG:4511',
    '+proj=tmerc +lat_0=0 +lon_0=129 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / Gauss-Kruger CM 135E
  [
    'EPSG:4512',
    '+proj=tmerc +lat_0=0 +lon_0=135 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 25
  [
    'EPSG:4513',
    '+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=25500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 26
  [
    'EPSG:4514',
    '+proj=tmerc +lat_0=0 +lon_0=78 +k=1 +x_0=26500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 27
  [
    'EPSG:4515',
    '+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=27500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 28
  [
    'EPSG:4516',
    '+proj=tmerc +lat_0=0 +lon_0=84 +k=1 +x_0=28500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 29
  [
    'EPSG:4517',
    '+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=29500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 30
  [
    'EPSG:4518',
    '+proj=tmerc +lat_0=0 +lon_0=90 +k=1 +x_0=30500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 31
  [
    'EPSG:4519',
    '+proj=tmerc +lat_0=0 +lon_0=93 +k=1 +x_0=31500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 32
  [
    'EPSG:4520',
    '+proj=tmerc +lat_0=0 +lon_0=96 +k=1 +x_0=32500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 33
  [
    'EPSG:4521',
    '+proj=tmerc +lat_0=0 +lon_0=99 +k=1 +x_0=33500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 34
  [
    'EPSG:4522',
    '+proj=tmerc +lat_0=0 +lon_0=102 +k=1 +x_0=34500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 35
  [
    'EPSG:4523',
    '+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=35500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 36
  [
    'EPSG:4524',
    '+proj=tmerc +lat_0=0 +lon_0=108 +k=1 +x_0=36500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 37
  [
    'EPSG:4525',
    '+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=37500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 38
  [
    'EPSG:4526',
    '+proj=tmerc +lat_0=0 +lon_0=114 +k=1 +x_0=38500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 39
  [
    'EPSG:4527',
    '+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=39500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 40
  [
    'EPSG:4528',
    '+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=40500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 41
  [
    'EPSG:4529',
    '+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=41500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 42
  [
    'EPSG:4530',
    '+proj=tmerc +lat_0=0 +lon_0=126 +k=1 +x_0=42500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 43
  [
    'EPSG:4531',
    '+proj=tmerc +lat_0=0 +lon_0=129 +k=1 +x_0=43500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 44
  [
    'EPSG:4532',
    '+proj=tmerc +lat_0=0 +lon_0=132 +k=1 +x_0=44500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger zone 45
  [
    'EPSG:4533',
    '+proj=tmerc +lat_0=0 +lon_0=135 +k=1 +x_0=45500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 75E
  [
    'EPSG:4534',
    '+proj=tmerc +lat_0=0 +lon_0=75 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 78E
  [
    'EPSG:4535',
    '+proj=tmerc +lat_0=0 +lon_0=78 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 81E
  [
    'EPSG:4536',
    '+proj=tmerc +lat_0=0 +lon_0=81 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 84E
  [
    'EPSG:4537',
    '+proj=tmerc +lat_0=0 +lon_0=84 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 87E
  [
    'EPSG:4538',
    '+proj=tmerc +lat_0=0 +lon_0=87 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 90E
  [
    'EPSG:4539',
    '+proj=tmerc +lat_0=0 +lon_0=90 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 93E
  [
    'EPSG:4540',
    '+proj=tmerc +lat_0=0 +lon_0=93 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 96E
  [
    'EPSG:4541',
    '+proj=tmerc +lat_0=0 +lon_0=96 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 99E
  [
    'EPSG:4542',
    '+proj=tmerc +lat_0=0 +lon_0=99 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 102E
  [
    'EPSG:4543',
    '+proj=tmerc +lat_0=0 +lon_0=102 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 105E
  [
    'EPSG:4544',
    '+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 108E
  [
    'EPSG:4545',
    '+proj=tmerc +lat_0=0 +lon_0=108 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 111E
  [
    'EPSG:4546',
    '+proj=tmerc +lat_0=0 +lon_0=111 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 114E
  [
    'EPSG:4547',
    '+proj=tmerc +lat_0=0 +lon_0=114 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 117E
  [
    'EPSG:4548',
    '+proj=tmerc +lat_0=0 +lon_0=117 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 120E
  [
    'EPSG:4549',
    '+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 123E
  [
    'EPSG:4550',
    '+proj=tmerc +lat_0=0 +lon_0=123 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 126E
  [
    'EPSG:4551',
    '+proj=tmerc +lat_0=0 +lon_0=126 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 129E
  [
    'EPSG:4552',
    '+proj=tmerc +lat_0=0 +lon_0=129 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 132E
  [
    'EPSG:4553',
    '+proj=tmerc +lat_0=0 +lon_0=132 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
  //# CGCS2000 / 3-degree Gauss-Kruger CM 135E
  [
    'EPSG:4554',
    '+proj=tmerc +lat_0=0 +lon_0=135 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs',
  ],
]);

export default proj4;
