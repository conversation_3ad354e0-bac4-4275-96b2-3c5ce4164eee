import{by as n}from"./bootstrap-DShsrVit.js";import{a4 as s,af as i,ag as m,ah as o,a3 as p,an as a}from"../jse/index-index-BMh_AyeW.js";import{u as f}from"./use-drawer-Qcdpj8Bl.js";const C=s({__name:"base-demo",setup(l){const[r,t]=f({onCancel(){t.close()},onConfirm(){n.info("onConfirm")}});return(u,e)=>(i(),m(p(r),{title:"基础抽屉示例","title-tooltip":"标题提示内容"},{extra:o(()=>e[0]||(e[0]=[a(" extra ")])),default:o(()=>[e[1]||(e[1]=a(" base demo "))]),_:1}))}});export{C as _};
