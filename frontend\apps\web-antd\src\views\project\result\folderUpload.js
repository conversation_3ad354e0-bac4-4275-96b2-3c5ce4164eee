// uploadManager.js
import { requestClient } from '#/api/request';
import { useAppConfig } from "@vben/hooks";
import pLimit from 'p-limit';
import { nanoid } from 'nanoid'
import { useAccessStore } from '@vben/stores';

const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

const formatToken = (token ) => {
  return token ? `Bearer ${token}` : null;
}

let stopUploading = false; //停止上传标志

class FolderUploadManager {
  constructor(folder, urls = {}, chunkSize = 5 * 1024 * 1024) {
    this.folder = folder;
    this.chunkSize = chunkSize;
    this.identifier = null;
    this.aborted = false;
    this.completed = false;
    // 设置默认 URL
    this.urls = {
      initUpload: '/upload/init',
      uploadChunk: '/upload/chunk',
      uploadProgress: '/upload/progress',
      ...urls
    };
  }

  abort() {
    this.aborted = true;
  }


  createChunks(file) {
    const chunks = [];
    let start = 0;

    while (start < file.size) {
      chunks.push(file.slice(start, start + this.chunkSize));
      start += this.chunkSize;
    }

    return chunks;
  }

  async getFolderStructure() {
    const items = [];
    for await (const entry of this.folder) {
      items.push({
        relativePath: entry.webkitRelativePath,
        fileName: entry.name,
        isDirectory: false,
        size: entry.size || 0,
      });
    }
    return items;
  }

  async initUpload(folderStructure) {
    const data = await requestClient.post(this.urls.initUpload, folderStructure);
    return data;
  }

  async start() {
    try {

      // 开始上传文件
      await this.uploadFiles();

    } catch (error) {
      console.error('Upload failed:', error);
      throw error;
    }
  }

  async uploadChunk(fileId, file, chunk, chunkNumber, totalChunks) {
    if(stopUploading) {
      return;
    }
    const formData = new FormData();
    formData.append('file', chunk ? chunk : new Blob([], { type: "application/octet-stream" }));
    formData.append('fileId', fileId);
    formData.append('folderId', this.identifier);
    formData.append('relativePath', file.webkitRelativePath && file.webkitRelativePath.length > 0 ? file.webkitRelativePath : file.name);
    formData.append('chunkNumber', chunkNumber);
    formData.append('totalChunks', totalChunks);
    formData.append('chunkSize', chunk ? chunk.size : 0);
    formData.append('totalSize', file.size);
    formData.append('fileName', file.name);

    const headers = new Headers();
    headers.append('Authorization', formatToken(useAccessStore().accessToken));

    const response = await fetch(`${apiURL}${this.urls.uploadChunk}`, {
      method: 'POST',
      body: formData,
      headers: headers,
    });

    if (!response.ok) {
      throw new Error(`Chunk upload failed: ${response.statusText}`);
    }
  }

  getRandomInt(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  async uploadFile(file) {
    if(stopUploading) {
      return;
    }
    const chunks = this.createChunks(file);
    const uploads = [];

    let fileId  = nanoid() //=> "V1StGXR8_Z5jdHi6B-myT"
    if(chunks.length > 0) {
      for (let i = 0; i < chunks.length; i++) {
        if (this.aborted) break;

        uploads.push(this.uploadChunk(fileId,file, chunks[i], i + 1, chunks.length));
      }
    }else {
      uploads.push(this.uploadChunk(fileId,file, undefined, 1, 1));
    }


    await Promise.all(uploads);
  }


  async uploadFiles() {
    stopUploading = false;
    const limit = pLimit(1);

    const uploads = [];
    for await (const file of this.folder) {
      if (!file.isDirectory) {
        uploads.push(limit(() => this.uploadFile(file)));
      }
    }
    await Promise.all(uploads);
  }
}

// progressMonitor.js
class UploadProgressMonitor {


  constructor(identifier,urls,onConnect, onProgress) {
    this.identifier = identifier;
    this.onProgress = onProgress;
    this.onConnect = onConnect;
    this.urls = urls;
  }


   checkProgress() {
    try {
      const accessStore = useAccessStore();

      let token = accessStore.accessToken;
      // Use Server-Sent Events (SSE) to monitor progress
      this.eventSource = new EventSource(`${apiURL}${this.urls.uploadProgress}/${this.identifier}?token=${token}`);

      this.eventSource.addEventListener('connect', (event) => {
        console.log('收到连接确认消息:', event.data);
        console.log('事件ID:', event.lastEventId);
        // 可以在这里执行一些初始化操作 this.initializeAfterConnection(); });
        this.onConnect && this.onConnect();
      });

      this.eventSource.addEventListener('progress', (event) => {
        console.log(event.data)
        const data = JSON.parse(event.data); // 处理进度数据
        this.onProgress && this.onProgress(data);
      });
      // 监听完成事件
      this.eventSource.addEventListener('complete', (event) => {
        console.log('Complete:', event.data);
        this.onProgress && this.onProgress( {"folderProgress":100,"chunkProgress":100});
        this.stop();
      });
    } catch (error) {
      console.error('Failed to fetch progress:', error);
    }
  }

  start() {
    this.checkProgress()
  }

  stop() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }
}


export function stopUpload() {
  stopUploading = true;
}

// 使用示例
export async function uploadFolder(folderInput, onSuccess, urls = {}, onError, onProgress) {
  const folder = folderInput;
  const uploader = new FolderUploadManager(folder, urls);

  try {
    // 获取文件夹结构
    const folderStructure = await uploader.getFolderStructure();

    // 初始化上传
    uploader.identifier = await uploader.initUpload(folderStructure);

    const monitor = new UploadProgressMonitor(
      uploader.identifier,
      urls,
      () => {
        //进度链接创建后，开始上传
        uploader.start();
      },
      (progress) => {
        // 更新进度
        onProgress && onProgress(progress);

        if (progress.folderProgress === 100 && !uploader.completed) {
          uploader.completed = true;
          onSuccess && onSuccess(uploader.identifier);
        }
      },
    );

    monitor.start();
  } catch (error) {
    console.error('Upload failed:', error);
    onError && onError(error);
  }
}
