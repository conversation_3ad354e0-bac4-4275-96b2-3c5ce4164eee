<template>
  <a-select
    v-model:value="selectedProject"
    show-search
    placeholder="请选择项目"
    :filter-option="filterOption"
    :loading="loading"
    @change="handleChange"
  >
    <a-select-option
      v-for="project in projectList"
      :key="project.id"
      :value="project.id"
    >
      {{ project.name }}
    </a-select-option>
  </a-select>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { list } from '#/views/project/project.api';
import { message } from 'ant-design-vue';

const selectedProject = ref();
const projectList = ref([]);
const loading = ref(false);

// 初始化获取项目列表
onMounted(async () => {
  try {
    loading.value = true;
    const res = await list({ pageSize: 1000 }); // 获取所有项目
    projectList.value = res.data?.records || [];
  } catch (error) {
    message.error('获取项目列表失败');
    console.error(error);
  } finally {
    loading.value = false;
  }
});

// 搜索筛选
const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 选择项目事件
const handleChange = (value) => {
  emit('change', value);
};

defineExpose({
  selectedProject,
  projectList
});
</script>

<style scoped lang="less">
@import '#/styles/dark-antd.less';
</style>
