var f=Object.getOwnPropertySymbols;var b=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable;var r=(s,a)=>{var t={};for(var e in s)b.call(s,e)&&a.indexOf(e)<0&&(t[e]=s[e]);if(s!=null&&f)for(var e of f(s))a.indexOf(e)<0&&h.call(s,e)&&(t[e]=s[e]);return t};import{x as v,y,l as B,m as x}from"./bootstrap-DShsrVit.js";import{a4 as c,af as i,ag as u,ah as d,ae as p,ai as C,aj as P,a3 as n,J as m,ac as g,aX as _}from"../jse/index-index-BMh_AyeW.js";const j=c({__name:"Tabs",props:{defaultValue:{},orientation:{},dir:{},activationMode:{},modelValue:{},asChild:{type:Boolean},as:{}},emits:["update:modelValue"],setup(s,{emit:a}){const l=v(s,a);return(o,$)=>(i(),u(n(y),C(P(n(l))),{default:d(()=>[p(o.$slots,"default")]),_:3},16))}}),w=c({__name:"TabsContent",props:{class:{},value:{},forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(s){const a=s,t=m(()=>{const o=a,{class:e}=o;return r(o,["class"])});return(e,l)=>(i(),u(n(B),g({class:n(_)("ring-offset-background focus-visible:ring-ring mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",a.class)},t.value),{default:d(()=>[p(e.$slots,"default")]),_:3},16,["class"]))}}),M=c({__name:"TabsList",props:{class:{},loop:{type:Boolean},asChild:{type:Boolean},as:{}},setup(s){const a=s,t=m(()=>{const o=a,{class:e}=o;return r(o,["class"])});return(e,l)=>(i(),u(n(x),g(t.value,{class:n(_)("bg-muted text-muted-foreground inline-flex h-9 items-center justify-center rounded-lg p-1",a.class)}),{default:d(()=>[p(e.$slots,"default")]),_:3},16,["class"]))}});export{M as _,w as a,j as b};
