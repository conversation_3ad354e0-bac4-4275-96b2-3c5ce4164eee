import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { OrganizationsModule } from './modules/organizations/organizations.module';
import { PositionsModule } from './modules/positions/positions.module';
import { RolesModule } from './modules/roles/roles.module';
import { UserGroupsModule } from './modules/user-groups/user-groups.module';
import { ApplicationsModule } from './modules/applications/applications.module';
import { FunctionsModule } from './modules/functions/functions.module';
import { DictModule } from './modules/dict/dict.module';
import { SysConfigModule } from './modules/sys-config/sys-config.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST'),
        port: +configService.get('DB_PORT'),
        username: configService.get('DB_USERNAME'),
        password: configService.get('DB_PASSWORD'),
        database: configService.get('DB_DATABASE'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: false, // 生产环境设为false
        logging: true,
        timezone: '+08:00',
      }),
      inject: [ConfigService],
    }),

    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN'),
        },
      }),
      inject: [ConfigService],
      global: true,
    }),

    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // 业务模块
    AuthModule,
    UsersModule,
    OrganizationsModule,
    PositionsModule,
    RolesModule,
    UserGroupsModule,
    ApplicationsModule,
    FunctionsModule,
    DictModule,
    SysConfigModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
