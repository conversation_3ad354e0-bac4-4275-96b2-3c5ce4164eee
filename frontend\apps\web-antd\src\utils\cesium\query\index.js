// 数据查询模块

// 基于sql查询
import * as Cesium from "cesium";

export function queryBySql(layer, sql) {}

// 基于几何查询
export function queryByGeometry(layer, geometry) {}

export function startSelectCircleArea (viewer) {
  let centerPosition = null; // 圆的中心点
  let circleEntity = null;   // 圆的实体
  let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

  let clickAction = (clickEvent) => {
    const earthPosition = viewer.scene.pickPosition(clickEvent.position);
    if (Cesium.defined(earthPosition)) {
      if (!centerPosition) {
        // 设置中心点
        centerPosition = Cesium.Cartesian3.clone(earthPosition);
        circleEntity = viewer.entities.add({
          position: centerPosition,
          ellipse: {
            semiMajorAxis: 1.0, // 初始半径
            semiMinorAxis: 1.0, // 初始半径
            material: Cesium.Color.BLUE.withAlpha(0.5),
          },
        });
      } else {
        // 确定最终半径
        centerPosition = null; // 重置中心点
        handler.removeInputAction(clickAction);
        handler.removeInputAction(moveAction);
      }
    }
  };

  let moveAction = (moveEvent) => {
    if (centerPosition && circleEntity) {
      const earthPosition = viewer.scene.pickPosition(moveEvent.endPosition);
      if (Cesium.defined(earthPosition)) {
        const radius = Cesium.Cartesian3.distance(centerPosition, earthPosition);
        circleEntity.ellipse.semiMajorAxis = radius;
        circleEntity.ellipse.semiMinorAxis = radius;
      }
    }
  }
  // 鼠标左键点击设置中心点
  handler.setInputAction(clickAction, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  // 鼠标移动动态调整圆的半径
  handler.setInputAction(moveAction, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
}


export function startSelectRectangleArea (viewer) {
  let startPoint = null; // 矩形的起始点
  let rectangleEntity = null; // 矩形实体
  let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

  let clickAction = (clickEvent) => {
    const earthPosition = viewer.scene.pickPosition(clickEvent.position);
    if (Cesium.defined(earthPosition)) {
      if (!startPoint) {
        // 设置矩形的起点
        startPoint = Cesium.Cartographic.fromCartesian(earthPosition);
        rectangleEntity = viewer.entities.add({
          rectangle: {
            coordinates: Cesium.Rectangle.fromDegrees(0, 0, 0, 0), // 初始矩形
            material: Cesium.Color.BLUE.withAlpha(0.5),
          },
        });
      } else {
        // 确认矩形范围
        startPoint = null; // 重置起点
        handler.removeInputAction(clickAction);
        handler.removeInputAction(moveAction);
      }
    }
  };
// 鼠标左键点击：设置起点或确定矩形
  handler.setInputAction(clickAction, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  let moveAction = (moveEvent) => {
    if (startPoint && rectangleEntity) {
      const earthPosition = viewer.scene.pickPosition(moveEvent.endPosition);
      if (Cesium.defined(earthPosition)) {
        const endPoint = Cesium.Cartographic.fromCartesian(earthPosition);

        // 获取起点和当前鼠标位置，计算矩形范围
        const west = Math.min(startPoint.longitude, endPoint.longitude);
        const south = Math.min(startPoint.latitude, endPoint.latitude);
        const east = Math.max(startPoint.longitude, endPoint.longitude);
        const north = Math.max(startPoint.latitude, endPoint.latitude);

        rectangleEntity.rectangle.coordinates = new Cesium.CallbackProperty(() => {
          return Cesium.Rectangle.fromRadians(west, south, east, north);
        }, false);
      }
    }
  };
// 鼠标移动：动态调整矩形范围
  handler.setInputAction(moveAction, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

}
