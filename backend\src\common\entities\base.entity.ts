import {
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Column,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

export abstract class BaseEntity {
  @ApiProperty({ description: 'ID' })
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({
    name: 'created_time',
    type: 'datetime',
    comment: '创建时间',
  })
  createdTime: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn({
    name: 'updated_time',
    type: 'datetime',
    comment: '更新时间',
  })
  updatedTime: Date;

  @ApiProperty({ description: '创建人ID', required: false })
  @Column({
    name: 'created_by',
    type: 'bigint',
    nullable: true,
    comment: '创建人',
  })
  createdBy?: number;

  @ApiProperty({ description: '更新人ID', required: false })
  @Column({
    name: 'updated_by',
    type: 'bigint',
    nullable: true,
    comment: '更新人',
  })
  updatedBy?: number;
}
