import * as Cesium from 'cesium';
import Base from '../core/Base.js';
import Graphics from '../core/Graphics';
import Math3d from '../core/Math3d';
import LabelDiv from '../components/LabelDiv';
/**
 * 通视分析
 * @param {*} params
 */
function VisibilityAnalysis(params) {
  if (params && params.positions) {
    this._viewer = params.that._viewer;
    this._base = new Base(this._viewer);
    this._graphics = new Graphics(this._viewer);
    this._math3d = new Math3d(this._viewer);

    var positions = params.positions,
      $this = this,
      that = params.that,
      points = [],
      lines = [],
      pickedObjs = [],
      position1 = this._base.transformWGS84ToCartesian({
        lng: positions[0].lng,
        lat: positions[0].lat,
        alt: positions[0].alt + params.height,
      }),
      position2 = this._base.transformWGS84ToCartesian(positions[1]);
    points = this._graphics.createPointsGraphics({
      point: true,
      positions: [position1, position2],
    });

    var results = this._math3d.getIntersectObj(
      position1,
      position2,
      points,
      false,
    ); //碰撞检测
    console.log(results);
    if (results.length === 0) {
      results.push({
        position: position2.clone(),
      });
      //alert("没有取到相交点 , 请检查是否开启深度检测。")
      //return false
    }
    //显示相交对象 高亮
    function showIntersections() {
      for (let i = 0; i < results.length; ++i) {
        var object = results[i].object;
        if (object) {
          if (object instanceof Cesium.Cesium3DTileFeature) {
            pickedObjs.push(object);
            object.oldColor = object.color.clone();
            object.color = Cesium.Color.fromAlpha(
              Cesium.Color.YELLOW,
              object.color.alpha,
            );
          } else if (object.id instanceof Cesium.Entity) {
            var entity = object.id;
            entity.unclickable = true;
            pickedObjs.push(entity);
            //var color = entity.polygon.material.color.getValue();
            //entity.polygon.oldColor = color.clone();
            //entity.polygon.material = Cesium.Color.fromAlpha(Cesium.Color.YELLOW, color.alpha);
          }
        }
        //相交点
        points.push(
          that._analysisLayer.entities.add({
            position: results[i].position,
            ellipsoid: {
              radii: new Cesium.Cartesian3(0.8, 0.8, 0.8),
              material: Cesium.Color.RED,
            },
          }),
        );
      }
    }
    // 计算分析结果
    function computesResult() {
      //分析一下是否都有position
      for (let index = results.length - 1; index >= 0; index--) {
        const element = results[index];
        if (!Cesium.defined(element.position)) {
          results.splice(index, 1);
        }
      }
      console.log(results);
      if (!Cesium.defined(results[0].position)) {
        throw new Cesium.DeveloperError('position is undefined');
      }
      var pickPos1 = results[0].position;
      //var dis = Cesium.Cartesian3.distance(pickPos1, position2);
      //var bVisibility = dis < 5 ? true : false; //
      debugger;
      var dis = Cesium.Cartesian3.distance(position1, position2);
      var dis1 = Cesium.Cartesian3.distance(position1, pickPos1);
      var dis2 = Cesium.Cartesian3.distance(position2, pickPos1);
      console.log(dis, dis1, dis2);
      var bVisibility = dis1 + dis2 - dis > -0.5 && dis2 < 0.5 ? true : false;
      var arrowPositions = [
        position1,
        bVisibility ? position2 : results[0].position,
      ];
      //通视线
      var greenLine = that._analysisLayer.entities.add({
        polyline: {
          positions: arrowPositions,
          width: 3,
          arcType: Cesium.ArcType.NONE,
          material: Cesium.Color.GREEN,
          depthFailMaterial: new Cesium.PolylineDashMaterialProperty({
            color: Cesium.Color.GREEN,
          }),
        },
      });
      lines.push(greenLine);
      //不通视
      if (!bVisibility) {
        var unArrowPositions = [results[0].position, position2];
        var redLine = that._analysisLayer.entities.add({
          polyline: {
            positions: unArrowPositions,
            width: 3,
            arcType: Cesium.ArcType.NONE,
            material: Cesium.Color.RED,
            depthFailMaterial: new Cesium.PolylineDashMaterialProperty({
              color: Cesium.Color.RED,
            }),
          },
        });

        lines.push(redLine);
      }
      //showIntersections()
      var rad1 = Cesium.Cartographic.fromCartesian(position1);
      var rad2 = Cesium.Cartographic.fromCartesian(position2);
      var degree1 = {
        longitude: (rad1.longitude / Math.PI) * 180,
        latitude: (rad1.latitude / Math.PI) * 180,
        height: rad1.height,
      };
      var degree2 = {
        longitude: (rad2.longitude / Math.PI) * 180,
        latitude: (rad2.latitude / Math.PI) * 180,
        height: rad2.height,
      };

      var length_ping = Math.sqrt(
        Math.pow(position1.x - position2.x, 2) +
          Math.pow(position1.y - position2.y, 2) +
          Math.pow(position1.z - position2.z, 2),
      );
      var length_h = Math.abs(degree2.height - degree1.height);
      var length = Math.sqrt(Math.pow(length_ping, 2) + Math.pow(length_h, 2));

      var visTxt = bVisibility ? '是' : '否';
      var text =
        '起点坐标: ' +
        ('(' + degree1.longitude.toFixed(6)) +
        '\u00B0' +
        ',' +
        degree1.latitude.toFixed(6) +
        '\u00B0' +
        ',' +
        degree1.height.toFixed(2) +
        ')' +
        '\n终点坐标: ' +
        ('(' + degree2.longitude.toFixed(6)) +
        '\u00B0' +
        ',' +
        degree2.latitude.toFixed(6) +
        '\u00B0' +
        ',' +
        degree2.height.toFixed(2) +
        ')' +
        '\n垂直距离: ' +
        '' +
        length_h.toFixed(2) +
        'm' +
        '\n水平距离: ' +
        '' +
        length_ping.toFixed(2) +
        'm' +
        '\n空间距离: ' +
        '' +
        length.toFixed(2) +
        'm' +
        '\n是否可视: ' +
        '' +
        visTxt;

      /* if (points && points[0]) {
				points[0].label = {
					text: text,
					showBackground: true,
					font: '14px monospace',
					fillColor: Cesium.Color.YELLOW,
					pixelOffset: {
						x: 0,
						y: -20
					},
					verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
					horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
					disableDepthTestDistance: Number.POSITIVE_INFINITY,
				}
			} */
      $this._label = new LabelDiv({
        viewer: that._viewer,
        html: `<pre>${text}</pre>`,
        style: 'width:350px;',
        position: position1.clone(),
        toolbox: {
          size: 20,
          fill: '#fff',
          buttons: {
            copy: {
              click: () => {
                return text;
              },
            },
            close: {
              click: () => {
                params.close && params.close();
                $this.remove();
              },
            },
          },
        },
      });
    }

    computesResult(); // 计算相交结果
  }

  VisibilityAnalysis.prototype.remove = function () {
    console.log(points);
    points.forEach((a) => {
      if (a.owner) {
        a.owner.entities.remove(a);
      } else if (a.entityCollection && a.entityCollection.owner) {
        a.entityCollection.owner.entities.remove(a);
      }
      //that._analysisLayer.entities.remove(a);
    });
    lines.forEach((a) => {
      that._analysisLayer.entities.remove(a);
    });
    if ($this._label) {
      $this._label.destroy();
      $this._label = null;
    }
    points = [];
    lines = [];
  };
}

export default VisibilityAnalysis;
