import {
  require_xe_utils
} from "./chunk-DULHHPCE.js";
import {
  __toESM
} from "./chunk-YHD4RJOZ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/ui/src/vn.js
var import_xe_utils = __toESM(require_xe_utils());
function getOnName(type) {
  return "on" + type.substring(0, 1).toLocaleUpperCase() + type.substring(1);
}
function getModelEvent(name) {
  switch (name) {
    case "input":
    case "textarea":
      return "input";
    case "select":
      return "change";
  }
  return "update:modelValue";
}
function getChangeEvent(name) {
  switch (name) {
    case "input":
    case "textarea":
    case "VxeInput":
    case "VxeTextarea":
    case "$input":
    // 已废弃
    case "$textarea":
      return "input";
  }
  return "change";
}
function getSlotVNs(vns) {
  if (import_xe_utils.default.isArray(vns)) {
    return vns;
  }
  return vns ? [vns] : [];
}

export {
  getOnName,
  getModelEvent,
  getChangeEvent,
  getSlotVNs
};
//# sourceMappingURL=chunk-UWMYBLIJ.js.map
