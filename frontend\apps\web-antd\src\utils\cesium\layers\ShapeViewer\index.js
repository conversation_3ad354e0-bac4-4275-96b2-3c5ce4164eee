import * as Cesium from 'cesium'
import {
	h,reactive,render
} from 'vue'
import eb,{
	eventbus
} from '../../../../ztu/eventbus'
import fileSelector from '../../../../utils/fileSelector.js'
import Drawer from '../../../../utils/cesium/mapTools/Drawer'
import Box from '../../../../components/JS/box'
import core from "../../../../utils/cesium/core"
import ShapeConfig from './config'
import {Tree} from 'ant-design-vue';
import ShapeSetting from './components/shapeSetting.vue'
import readAsText from '../../../../utils/readAsText'
import utils from '../../../utils'

const SHAPE_TYPES_CN = {
	'point': '点',
	'polyline': '线',
	'polygon': '多边形',
	'rectangle': '矩形',
	'ellipse': '圆'
}
const SHAPE_LAYER_NAME = 'layer/shapes';

export default class ShapeViewer extends eventbus{
	constructor(options){
		super();
		this.viewer = options.viewer;
		this.drawer = options.drawer || new Drawer(this.viewer);
		this.shapes = [];
		this.treeData = reactive([{title:'我的绘图',
				key:SHAPE_LAYER_NAME,
				children:[]}]);
		this.checkedKeys = reactive([]);
		this.selectedKeys = reactive([]);

		this.drawer.doubleClick((entity) => {
			console.log(".......entity", entity)
			// if(this.shapes.includes(entity))
				// this.showEditor(entity._id)
		})
		if(options.el){
			this.mount(options.el);
		}
		this.on('change',(shapes)=>{
			let nodes = shapes.map(a=>{
				return {
					title: (a.label && a.label.text._value) || SHAPE_TYPES_CN[a.name],
					key:a.id,
					type:(a.polyline?'polyline':'')
						|| (a.polygon?'polygon':'')
						|| (a.rectangle?'rectangle':'')
						|| (a.ellipse?'ellipse':'')
						|| a.name || '未知类型',
					show:a.show
				}
			});
			// localStorage.setItem(SHAPE_LAYER_NAME,JSON.stringify(shapes.map(a=>{
			// 	return core.entity2Json(a);
			// })));

			console.log(nodes)
			let types = Array.from(new Set(nodes.map(a=>a.type)));
			this.treeData[0].children = types.map(a=>{
				return {
					title: SHAPE_TYPES_CN[a],
					key:a,
					children:nodes.filter(b=>b.type == a)
				}
			});
			this.checkedKeys.splice(0,this.checkedKeys.length);
			console.log(this.checkedKeys)
			nodes.filter(a=>a.show).map(a=>a.key).forEach(a=>{
				this.checkedKeys.push(a)
			});
			console.log(this.checkedKeys)
		})
		this.init(options)
	}
	init(options){
		//从本地存储内读出数据，并勾选显示
		// if(options.useLocalStorage){
		// 	let data = JSON.parse(localStorage.getItem(SHAPE_LAYER_NAME) || '[]');
		// 	if(data instanceof Array){
		// 		this.loadByJson(data);
		// 	}
		// }
		if(options.data){
			this.loadByJson(options.data);
		}
		//清空事件
		eb.on('clearAll',(e)=>{
			this.removeAll(true);
		})
		eb.on('captureEnd',(e)=>{
			this.drawer.drawEnd();
		})
	}
	mount(el){
		if(typeof el === 'string'){
			el = document.getElementById(el);
		}
		if(!(el instanceof HTMLElement)){
			return console.log('POIViewer绑定节点无效')
		}
		//let checkedKeys = [];
		let node = h(Tree,{
			selectable:true,
			blockNode:true,
			checkable:true,
			//defaultExpandAll:true,
			checkedKeys: this.checkedKeys,
			selectedKeys:this.selectedKeys,
			//'onUpdate:checkedKeys': (value) => this.checkedKeys = value,
			onCheck:(keys)=>this.show(keys),
			onSelect:(keys)=>{
				//this.selectedKeys.splice(0,this.selectedKeys.length);
				//keys.forEach(a=>{
				//	this.selectedKeys.push(a)
				//});
				this.showAndZoom(keys[0]);
			},
			treeData:this.treeData,
			style:{
				margin:'0px',
				marginTop:0
			}
		},{
			title:({title, key,type})=>{
				if(key==SHAPE_LAYER_NAME){
					return h('div',{
						style:'display:flex;align-items: center;justify-content: space-between;user-select: none;'
					},[
						h('span',null,title),
						h('span',null,[
							h('span',{
								style:'cursor: pointer;padding:0 4px;',
								title:'导出JSON',
								onClick:()=>this.exportJson()
							},[
								h('span',{
									class:"iconfont icon-daochu"
								})
							]),
							h('span',{
								style:'cursor: pointer;padding:0 4px;',
								title:'清空',
								onClick:()=>this.removeAll()
							},[
								h('span',{
									class:"iconfont icon-act_qingkong"
								})
							]),
							h('span',{
								style:'cursor: pointer;padding:0 4px;margin-right:-4px;',
								title:'导入本地文件',
								onClick:()=>this.loadFile()
							},[
								h('span',{
									class:"iconfont icon-jia"
								})
							]),
						])
					])
				}
				else if(!type){
					return h('span',null,title);
				}
				else{
					return h('div',{
						style:'display:flex;align-items: center;justify-content: space-between;user-select: none;'
					},[
						h('span',null,title),
						h('span',null,[
							h('span',{
								style:'cursor: pointer;padding:0 4px;margin-right:-4px;',
								title:'编辑',
								onClick:()=>this.showEditor(key)
							},[
								h('span',{
									class:"iconfont icon-bianji"
								})
							]),
							h('span',{
								style:'cursor: pointer;padding:0 4px;margin-right:-4px;',
								title:'删除',
								onClick:()=>this.remove(key)
							},[
								h('span',{
									class:"iconfont icon-shanchu"
								})
							])
						])
					])
				}
			}
		});
		render(node,el);
	}
	getById(id){
		return this.shapes.find(a=>a.id == id);
	}
	getIndexById(id){
		return this.shapes.findIndex(a=>a.id == id);
	}
	add(shapeType,options){
		eb.emit('captureEnd',{target:'shapeViewer'});
		this.drawer.drawStart(shapeType, {
			...(ShapeConfig[shapeType + 'Style']),
			...options
		}, (entity)=>{
			this.shapes.push(entity);
			// this.showEditor(entity.id);
			this.emit('change',this.shapes);
		});
	}
	remove(id){
		let index  = this.getIndexById(id);
		if(index==-1) return;
		Box.confirm('删除','确定要删除吗?',()=>{
			if(this.selectEntity===this.shapes[index]){
				this.editor && Box.close(this.editor);
				this.selectEntity=null;
				this.editor = null;
			}
			this.drawer.remove(this.shapes[index]);
			this.shapes.splice(index,1);
			this.emit('change',this.shapes);
		})
	}
	showEditor(id){
		let entity = this.getById(id);
		if (!entity) return;
		this.selectEntity = entity;
		this.editor = Box.open({
			title: '属性',
			beforeClose:()=>{
				this.editor = null;
				this.selectEntity = null;
			}
		}, [h(ShapeSetting, {
			entity: entity
		})],this.editor);
	}
	save(params){
		if(!this.selectEntity) return;
		core.json2Entity(params,this.selectEntity,ShapeConfig);
		console.log(params,this.selectEntity)
		this.emit('change',this.shapes);
	}
	loadByJson(json){
		if (json instanceof Array) {
			return json.forEach(j => {
				this.loadByJson(j);
			})
		}
		let entity = core.json2Entity(json,null,ShapeConfig);
		if (entity) {
			this.drawer.drawEntity(entity);
			this.shapes.push(entity);
			this.emit('change',this.shapes);
		}
	}
	loadFile(){
		fileSelector.openFile((files) => {
			for (let i = 0; i < files.length; i++) {
				let file = files[i];
				if (file.name.toLocaleLowerCase().indexOf('.json') > 0) {
					readAsText(file).then((result) => {
						console.log(result)
						let json = JSON.parse(result);
						this.loadByJson(json);
					})
				}
			}
		}, {
			multiple: false,
			accept: ".json",
		})
	}
	exportJson(){
		if (this.shapes.length) {
			let jsonArr = [];
			this.shapes.forEach(entity=>{
				jsonArr.push(core.entity2Json(entity));
			})
			utils.saveAs(JSON.stringify(jsonArr), 'export.json', '.json');
		} else {
			Box.info('提示', '绘图数据为空，无需导出')
		}
	}
	removeAll(flag=false){
		let ok = ()=>{
			this.shapes.forEach(entity=>{
				this.drawer.remove(entity);
			})
			this.shapes = [];
			this.emit('change',this.shapes);
		};
		flag?ok():Box.confirm('清空','确定要清空吗?',ok);
	}
	show(keys){
		this.shapes.forEach(entity=>{
			entity.show = keys.includes(entity.id);
		})
		this.emit('change',this.shapes);
	}
	hide(keys){
		if(keys instanceof Array){
			return keys.forEach(key=>{
				this.hide(key);
			})
		}
		else if(typeof keys === 'string'){
			let key = keys;
			let entity = this.getById(key);
			if(!entity || !entity.show) return;
			entity.show = false;
			this.emit('change',this.shapes);
			return entity;
		}
	}
	showAndZoom(id){
		let entity = this.getById(id);
		if(!entity) return;
		entity.show = true;
		this.emit('change',this.shapes);
		this.viewer.flyTo(entity)
	}
}
export let shapeViewer = null;
export function initShapeViewer(options) {
	shapeViewer = new ShapeViewer(options);
	return shapeViewer;
}
