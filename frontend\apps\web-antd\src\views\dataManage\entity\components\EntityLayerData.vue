<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onMounted, ref, watch } from "vue";

import {VbenButton, type VbenFormProps } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  columns,
  getUploadStatusLabel,
  searchFormSchema
} from "#/views/dataManage/entity/entity.data";
import {showConform} from '#/utils/alert.js'
import { getLayerTableDatas, list } from "#/views/dataManage/entity/entity.api";

const emit = defineEmits(['handleCancel']);
const props = defineProps(['columns','layerId']);
let tableDats = ref([]);
let tableColumns = ref([]);
let layerId = props.layerId;


const clearData = ()=>{
  tableDats.value=[];
  tableColumns.value = [];
}


watch(() => props.layerId, (newValue, oldValue) => {
  console.log(`count changed from ${oldValue} to ${newValue}`);
    layerId = newValue;
    console.log(newValue);
    gridApi.reload();
    // loadTableDatas(newValue);
});



const gridOptions: VxeGridProps<RowType> = {
  columns:tableColumns.value,
  data: tableDats.value,
  height:'320px',
  pagerConfig: {
    enabled: true,
    pagerCount: 4
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        if(!layerId) {
          gridApi.setGridOptions({
            columns:[],
          });
          return {
            total: 0,
            items: [],
            }
        }
        const data = await getLayerTableDatas(layerId,{
          page : {
            current: page.currentPage,
            size: page.pageSize,
            searchCount:true
          }
        });

        if(page.currentPage == 1) {
          if(data.columns) {
            let columns = [];
            for(let i=0;i<data.columns.length;i++) {
              columns.push( { title: data.columns[i], field: data.columns[i], width: 120 })
            }
            gridApi.setGridOptions({
              columns:columns,
            });
          }
        }

        return {
          total: data.pageValues.total,
          items:data.pageValues.records,
        };
      },
    },
  },
  sortConfig: {
    multiple: true,
  },
};

const [Grid,gridApi] = useVbenVxeGrid({ gridOptions });

// loadTableDatas(props.layerId);


// Fetch data on component mounted
onMounted(() => {
  layerId = props.layerId;
});
</script>

<template>
  <view class="app-container">
    <!--引用表格-->
    <Grid>
    </Grid>


  </view>
</template>

<style scoped>
.app-container {
}

.a-form-item {
  margin-right: 20px;
}

.actionButton {
  margin-right: 5px;
  font-size: 16px;
}
</style>
