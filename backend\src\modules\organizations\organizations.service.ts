import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Organization } from './entities/organization.entity';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { QueryOrganizationDto } from './dto/query-organization.dto';

@Injectable()
export class OrganizationsService {
  constructor(
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
  ) {}

  async create(createOrganizationDto: CreateOrganizationDto, currentUserId?: number): Promise<Organization> {
    // 检查编码是否已存在
    const existingOrg = await this.organizationRepository.findOne({
      where: { code: createOrganizationDto.code },
    });
    if (existingOrg) {
      throw new ConflictException('机构编码已存在');
    }

    // 如果指定了上级机构，检查上级机构是否存在
    if (createOrganizationDto.parentId && createOrganizationDto.parentId !== 0) {
      const parentOrg = await this.organizationRepository.findOne({
        where: { id: createOrganizationDto.parentId },
      });
      if (!parentOrg) {
        throw new NotFoundException('上级机构不存在');
      }
    }

    const organization = this.organizationRepository.create({
      ...createOrganizationDto,
      parentId: createOrganizationDto.parentId || 0,
      sortOrder: createOrganizationDto.sortOrder || 0,
      status: createOrganizationDto.status ?? 1,
      createdBy: currentUserId,
    });

    return this.organizationRepository.save(organization);
  }

  async findAll(queryDto?: QueryOrganizationDto): Promise<Organization[]> {
    const queryBuilder = this.organizationRepository.createQueryBuilder('org')
      .leftJoinAndSelect('org.defaultRole', 'role')
      .orderBy('org.sortOrder', 'ASC')
      .addOrderBy('org.createdTime', 'ASC');

    if (queryDto?.name) {
      queryBuilder.andWhere('org.name LIKE :name', { name: `%${queryDto.name}%` });
    }

    if (queryDto?.code) {
      queryBuilder.andWhere('org.code LIKE :code', { code: `%${queryDto.code}%` });
    }

    if (queryDto?.type) {
      queryBuilder.andWhere('org.type = :type', { type: queryDto.type });
    }

    if (queryDto?.status !== undefined) {
      queryBuilder.andWhere('org.status = :status', { status: queryDto.status });
    }

    if (queryDto?.parentId !== undefined) {
      queryBuilder.andWhere('org.parentId = :parentId', { parentId: queryDto.parentId });
    }

    const organizations = await queryBuilder.getMany();

    // 添加默认角色名称并确保id为number类型
    return organizations.map(org => ({
      ...org,
      id: Number(org.id),
      parentId: Number(org.parentId),
      defaultRoleName: org.defaultRole?.name || '',
    }));
  }

  async findTree(queryDto?: QueryOrganizationDto): Promise<Organization[]> {
    const organizations = await this.findAll(queryDto);
    return this.buildTree(organizations);
  }

  async findOne(id: number): Promise<Organization> {
    const organization = await this.organizationRepository.findOne({
      where: { id },
      relations: ['defaultRole', 'parent', 'children'],
    });

    if (!organization) {
      throw new NotFoundException('组织不存在');
    }

    return organization;
  }

  async update(id: number, updateOrganizationDto: UpdateOrganizationDto, currentUserId?: number): Promise<Organization> {
    const organization = await this.findOne(id);

    // 检查编码是否已存在（排除当前组织）
    if (updateOrganizationDto.code && updateOrganizationDto.code !== organization.code) {
      const existingOrg = await this.organizationRepository.findOne({
        where: { code: updateOrganizationDto.code },
      });
      if (existingOrg && existingOrg.id !== id) {
        throw new ConflictException('机构编码已存在');
      }
    }

    // 如果指定了上级机构，检查上级机构是否存在且不能是自己或自己的子机构
    if (updateOrganizationDto.parentId && updateOrganizationDto.parentId !== 0) {
      if (updateOrganizationDto.parentId === id) {
        throw new ConflictException('不能将自己设置为上级机构');
      }

      const parentOrg = await this.organizationRepository.findOne({
        where: { id: updateOrganizationDto.parentId },
      });
      if (!parentOrg) {
        throw new NotFoundException('上级机构不存在');
      }

      // 检查是否会形成循环引用
      if (await this.isDescendant(updateOrganizationDto.parentId, id)) {
        throw new ConflictException('不能将子机构设置为上级机构');
      }
    }

    Object.assign(organization, {
      ...updateOrganizationDto,
      updatedBy: currentUserId,
    });

    return this.organizationRepository.save(organization);
  }

  async remove(id: number): Promise<void> {
    const organization = await this.findOne(id);

    // 检查是否有子机构
    const children = await this.organizationRepository.find({
      where: { parentId: id },
    });

    if (children.length > 0) {
      throw new ConflictException('存在子机构，无法删除');
    }

    await this.organizationRepository.remove(organization);
  }

  async batchRemove(ids: number[]): Promise<void> {
    for (const id of ids) {
      await this.remove(id);
    }
  }

  // 构建树形结构
  private buildTree(organizations: Organization[], parentId = 0): Organization[] {
    const tree: Organization[] = [];

    organizations.forEach(org => {
      // 确保类型一致性，将两边都转换为数字进行比较
      const orgParentId = Number(org.parentId);
      const currentParentId = Number(parentId);
      
      if (orgParentId === currentParentId) {
        const children = this.buildTree(organizations, org.id);
        if (children.length > 0) {
          org.children = children;
        }
        tree.push(org);
      }
    });

    return tree;
  }

  // 检查是否为子孙节点
  private async isDescendant(ancestorId: number, descendantId: number): Promise<boolean> {
    const descendants = await this.getDescendants(descendantId);
    return descendants.some(desc => desc.id === ancestorId);
  }

  // 获取所有子孙节点
  private async getDescendants(parentId: number): Promise<Organization[]> {
    const children = await this.organizationRepository.find({
      where: { parentId },
    });

    let descendants = [...children];

    for (const child of children) {
      const childDescendants = await this.getDescendants(child.id);
      descendants = descendants.concat(childDescendants);
    }

    return descendants;
  }
}
