import{ao as e,k as a,z as l,I as n,l as t,ay as r,j as o}from"./chunks/framework.C8U7mBUf.js";const s=JSON.parse('{"title":"定制开发","description":"","frontmatter":{},"headers":[],"relativePath":"commercial/customized.md","filePath":"commercial/customized.md"}');const i=e({name:"commercial/customized.md"},[["render",function(e,s,i,m,c,u){const d=r("NolebaseGitContributors"),h=r("NolebaseGitChangelog");return o(),a("div",null,[s[0]||(s[0]=l("h1",{id:"定制开发",tabindex:"-1"},[n("定制开发 "),l("a",{class:"header-anchor",href:"#定制开发","aria-label":'Permalink to "定制开发"'},"​")],-1)),s[1]||(s[1]=l("p",null,"我们提供基于 Vben Admin 的技术支持服务及定制开发，基本需求我们都可以满足。",-1)),s[2]||(s[2]=l("p",null,"详细需求可添加作者了解，并注明来意：",-1)),s[3]||(s[3]=l("ul",null,[l("li",null,[n("通过邮箱联系开发者： "),l("a",{href:"mailto:<EMAIL>",target:"_blank",rel:"noreferrer"},"<EMAIL>")]),l("li",null,"通过微信联系开发者：")],-1)),s[4]||(s[4]=l("img",{src:"https://unpkg.com/@vbenjs/static-source@0.1.7/source/wechat.jpg",style:{width:"300px"}},null,-1)),s[5]||(s[5]=l("p",null,"我们会在第一时间回复您，定制费用根据需求而定。",-1)),t(d),t(h)])}]]);export{s as __pageData,i as default};
