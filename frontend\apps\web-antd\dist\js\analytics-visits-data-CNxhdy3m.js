import{u as t,_ as r}from"./use-echarts-CfT8MSbz.js";import{d as o,r as s,o as n,j as d,b as m,k as i}from"../jse/index-index-DyHD_jbN.js";import"./bootstrap-5OPUVRWy.js";const h=o({__name:"analytics-visits-data",setup(c){const a=s(),{renderEcharts:e}=t(a);return n(()=>{e({legend:{bottom:0,data:["访问","趋势"]},radar:{indicator:[{name:"网页"},{name:"移动端"},{name:"Ipad"},{name:"客户端"},{name:"第三方"},{name:"其它"}],radius:"60%",splitNumber:8},series:[{areaStyle:{opacity:1,shadowBlur:0,shadowColor:"rgba(0,0,0,.2)",shadowOffsetX:0,shadowOffsetY:10},data:[{itemStyle:{color:"#b6a2de"},name:"访问",value:[90,50,86,40,50,20]},{itemStyle:{color:"#5ab1ef"},name:"趋势",value:[70,75,70,76,20,85]}],itemStyle:{borderRadius:10,borderWidth:2},symbolSize:0,type:"radar"}],tooltip:{}})}),(l,f)=>(m(),d(i(r),{ref_key:"chartRef",ref:a},null,512))}});export{h as default};
