<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import { VbenTree, Loading, Fallback } from '@vben/common-ui';
import { getOrganizationTree } from '../organization.api';

// 定义组件属性
interface Props {
  selectedOrganizationId?: number | null;
}

// 定义事件
interface Emits {
  (e: 'select', organizationId: number | null, organizationName: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  selectedOrganizationId: null,
});

const emit = defineEmits<Emits>();

// 响应式数据
const organizationTreeData = ref([]);
const selectedKeys = ref<number[]>([]);
const selectedKey = ref<number | undefined>(undefined); // 单选模式下的选中值
const expandedKeys = ref<number[]>([]);
const loading = ref(false);
const error = ref('');

// 加载组织树数据
const loadOrganizationTree = async () => {
  try {
    loading.value = true;
    error.value = '';
    const data = await getOrganizationTree();
    organizationTreeData.value = data || [];

    // 默认展开所有节点
    const allKeys = getAllKeys(data || []);
    expandedKeys.value = allKeys;

    // 默认选中第一个部门
    if (!props.selectedOrganizationId && data && data.length > 0) {
      const firstNode = data[0];
      if (firstNode && firstNode.id !== undefined && firstNode.id !== null) {
        selectedKey.value = firstNode.id;
        selectedKeys.value = [firstNode.id];
        ensureNodeVisible(firstNode.id);
        emit('select', firstNode.id, firstNode.name || '');
      }
    }
  } catch (err) {
    console.error('加载组织树失败:', err);
    error.value = '加载组织数据失败，请重试';
  } finally {
    loading.value = false;
  }
};

// 获取所有节点的key
const getAllKeys = (data: any[]): number[] => {
  const keys: number[] = [];
  const traverse = (nodes: any[]) => {
    nodes.forEach(node => {
      if (node.id !== null && node.id !== undefined) {
        keys.push(node.id);
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(data);
  return keys;
};

// VbenTree的选择处理
const handleTreeSelect = (item: any) => {
  const selectedKeyValue = item.value?.id;
  console.log('handleTreeSelect called:', selectedKeyValue);
  console.log('current selectedKey:', selectedKey.value);

  // 更新我们的状态以保持同步
  selectedKeys.value = selectedKeyValue ? [selectedKeyValue] : [];
  console.log('Updated selectedKeys:', selectedKeys.value);

  if (selectedKeyValue) {
    emit('select', selectedKeyValue, item.value?.name || '');

    // 确保选中的节点可见（展开父节点）
    ensureNodeVisible(selectedKeyValue);
  } else {
    emit('select', null, '');
  }
};

// 监听selectedKey的变化，同步更新selectedKeys
watch(selectedKey, (newVal) => {
  console.log('selectedKey changed to:', newVal);
  selectedKeys.value = newVal ? [newVal] : [];
}, { immediate: true });

// 计算属性：检查节点是否被选中
const isNodeSelected = (nodeId: number) => {
  const result = selectedKey.value === nodeId;
  console.log(`isNodeSelected(${nodeId}):`, result, 'selectedKey:', selectedKey.value);
  return result;
};

// 确保节点可见（展开所有父节点）
const ensureNodeVisible = (nodeId: number) => {
  const parentIds = getParentIds(organizationTreeData.value, nodeId);
  const newExpandedKeys = [...new Set([...expandedKeys.value, ...parentIds])];
  expandedKeys.value = newExpandedKeys;
};

// 获取节点的所有父节点ID
const getParentIds = (nodes: any[], targetId: number, parentIds: number[] = []): number[] => {
  for (const node of nodes) {
    if (node.id === targetId) {
      return parentIds;
    }
    if (node.children && node.children.length > 0) {
      const found = getParentIds(node.children, targetId, [...parentIds, node.id]);
      if (found.length > parentIds.length || found.some((id: number) => !parentIds.includes(id))) {
        return found;
      }
    }
  }
  return parentIds;
};

// 根据ID查找节点
const findNodeById = (nodes: any[], id: number): any => {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

// 处理展开/收起
const handleExpand = (item: any) => {
  // VbenTree的expand事件处理
  console.log('Tree expand:', item);
};



// 清除选择
const clearSelection = () => {
  selectedKey.value = undefined;
  selectedKeys.value = [];
  emit('select', null, '');
};

// 程序化设置选中状态
const setSelectedOrganization = (organizationId: number | null) => {
  if (organizationId) {
    selectedKey.value = organizationId;
    selectedKeys.value = [organizationId];
    ensureNodeVisible(organizationId);

    // 查找节点名称
    const node = findNodeById(organizationTreeData.value, organizationId);
    emit('select', organizationId, node?.name || '');
  } else {
    clearSelection();
  }
};

// 监听外部选择变化
watch(() => props.selectedOrganizationId, (newVal, oldVal) => {
  // 避免重复设置相同的值
  if (newVal !== oldVal) {
    if (newVal) {
      selectedKey.value = newVal;
      selectedKeys.value = [newVal];
      // 确保选中的节点可见
      if (organizationTreeData.value.length > 0) {
        ensureNodeVisible(newVal);
      }
    } else {
      selectedKey.value = undefined;
      selectedKeys.value = [];
    }
  }
}, { immediate: true });



// 组件挂载时加载数据
onMounted(() => {
  loadOrganizationTree();
});



// 暴露方法给父组件
defineExpose({
  loadOrganizationTree,
  clearSelection,
  setSelectedOrganization,
  findNodeById,
});
</script>

<template>
  <div class="organization-sidebar">
    <!-- 标题栏 -->
    <div class="sidebar-header">
      <div class="header-title">
        <span class="title-text">组织架构</span>
      </div>
    </div>

    <!-- 组织树 -->
    <div class="sidebar-content">
      <Loading :spinning="loading">
        <VbenTree
          v-if="organizationTreeData.length > 0"
          v-model="selectedKey"
          :tree-data="organizationTreeData"
          :default-expanded-level="10"
          label-field="name"
          value-field="id"
          children-field="children"
          :show-icon="false"
          class="organization-tree"
          @select="handleTreeSelect"
          @expand="handleExpand"
        >
          <template #node="{ value }">
            <div
              class="tree-node-content"
              :class="{
                'is-selected': isNodeSelected(value.id),
                'has-children': value.children && value.children.length > 0
              }"
              :title="`ID: ${value.id}, Selected: ${selectedKey}, IsSelected: ${isNodeSelected(value.id)}`"
            >
              <span class="node-name">{{ value.name }}</span>
            </div>
          </template>
        </VbenTree>

        <div v-else-if="error" class="error-state">
          <Fallback
            status="500"
            :title="error"
            description="请检查网络连接或联系管理员"
          >
            <template #action>
              <VbenButton @click="loadOrganizationTree">
                重新加载
              </VbenButton>
            </template>
          </Fallback>
        </div>

        <div v-else-if="!loading" class="empty-state">
          <div class="flex flex-col items-center justify-center py-8">
            <div class="text-muted-foreground text-sm">暂无组织数据</div>
          </div>
        </div>
      </Loading>
    </div>
  </div>
</template>

<style scoped>
/* 组件内部样式 */
.organization-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: hsl(var(--background));
  border-right: 1px solid hsl(var(--border));
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid hsl(var(--border));
  flex-shrink: 0;
}

.header-title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.title-text {
  font-size: 1rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.sidebar-content {
  flex: 1;
  padding: 0.5rem;
  overflow-y: auto;
}

/* 树节点内容样式 - 自定义节点内容 */
.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
  min-height: 32px;
  background: transparent;
  padding: 4px 8px;
  width: 100%;
  flex: 1;
}


/* 使用深度选择器确保整个树节点行都有背景效果 */
:deep(.organization-tree .tree-node) {
  background-color: hsl(var(--muted) / 0.3) !important;
  border-radius: 4px !important;
  margin: 1px 0 !important;
  transition: all 0.2s ease !important;
  width: 100% !important;
}

:deep(.organization-tree .tree-node:hover) {
  background-color: hsl(var(--primary) / 0.15) !important;
}

/* 覆盖VbenTree的默认选中样式 */
:deep(.organization-tree .tree-node[data-selected]) {
  background-color: hsl(var(--primary)) !important;
}

:deep(.organization-tree .tree-node[data-selected]:hover) {
  background-color: hsl(var(--primary) / 0.9) !important;
}

/* 当自定义节点内容有is-selected类时，也应用选中样式到父节点 */
:deep(.organization-tree .tree-node:has(.tree-node-content.is-selected)) {
  background-color: hsl(var(--primary)) !important;
}

:deep(.organization-tree .tree-node:has(.tree-node-content.is-selected):hover) {
  background-color: hsl(var(--primary) / 0.9) !important;
}

/* 选中状态下的文字颜色 */
:deep(.organization-tree .tree-node[data-selected] .node-name),
.tree-node-content.is-selected .node-name {
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 500 !important;
}

/* 确保覆盖Tailwind的bg-accent类 */
:deep(.organization-tree .tree-node.bg-accent) {
  background-color: hsl(var(--primary)) !important;
}

.node-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.875rem;
  line-height: 1.4;
  padding: 4px 8px;
  transition: all 0.2s ease;
  color: hsl(var(--foreground));
}

.tree-node-content.is-selected .node-name {
  color: hsl(var(--primary-foreground));
  font-weight: 500;
}



.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 12rem;
}

.error-state {
  padding: 1.25rem;
  text-align: center;
}


</style>


