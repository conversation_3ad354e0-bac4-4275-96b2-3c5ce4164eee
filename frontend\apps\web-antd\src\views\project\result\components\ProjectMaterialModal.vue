<template>
  <Modal
    class="w-[1400px]"
    title="项目资料管理"
    :canFullscreen="false"
    :width="800"
  >
    <div class="material-container">
      <Tabs v-model="activeTab">
        <TabsList>
          <TabsTrigger value="acceptance">验收资料</TabsTrigger>
          <TabsTrigger value="result">成果资料</TabsTrigger>
          <TabsTrigger value="process">重要过程资料</TabsTrigger>
        </TabsList>
        <TabsContent value="acceptance">
          <ProjectMaterialTabContent
            :activeTab="activeTab"
            :row="row"
            :initialState="tabStates.acceptance"
            @stateChange="(state) => updateTabState('acceptance', state)"
          />
        </TabsContent>
        <TabsContent value="result">
          <ProjectMaterialTabContent
            :activeTab="activeTab"
            :row="row"
            :initialState="tabStates.result"
            @stateChange="(state) => updateTabState('result', state)"
          />
        </TabsContent>
        <TabsContent value="process">
          <ProjectMaterialTabContent
            :activeTab="activeTab"
            :row="row"
            :initialState="tabStates.process"
            @stateChange="(state) => updateTabState('process', state)"
          />
        </TabsContent>
      </Tabs>
    </div>
  </Modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { VbenModal, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { listMaterial } from '#/views/project/project.api';
import { showLoading, showSuccess } from "#/utils/toast.js";
import { stopUpload, uploadFolder } from "#/utils/folderUpload.js";
import { uploadFile } from '#/utils/fileUpload.js';
import { showConform } from "#/utils/alert";
import ProjectMaterialTabContent from './ProjectMaterialTabContent.vue';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@vben/common-ui';

let fileInput: any = null;
const startUpload = ref(false);
const uploadType = ref('0');

const activeTab = ref('acceptance');
let row = ref({});

// 存储每个标签页的目录层级状态
const tabStates = ref({
  acceptance: {
    currentParentAppendixId: 0,
    breadcrumbs: [{ id: 0, name: '根目录' }]
  },
  result: {
    currentParentAppendixId: 0,
    breadcrumbs: [{ id: 0, name: '根目录' }]
  },
  process: {
    currentParentAppendixId: 0,
    breadcrumbs: [{ id: 0, name: '根目录' }]
  }
});

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  footer:false,
  onCancel() {
    clearData();
    modalApi.close();
  },
  onConfirm() {
    clearData();
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = modalApi.getData();
      row.value = data.row;
    } else {
      clearData();
    }
  },
});

// 清除数据的方法
const clearData = () => {
  activeTab.value = 'acceptance';
  row.value = {};
  fileInput = null;
  startUpload.value = false;
  uploadType.value = '0';
  // 重置所有标签页的状态
  Object.keys(tabStates.value).forEach(key => {
    tabStates.value[key] = {
      currentParentAppendixId: 0,
      breadcrumbs: [{ id: 0, name: '根目录' }]
    };
  });
};

// 更新标签页状态
const updateTabState = (tab: string, state: any) => {
  tabStates.value[tab] = state;
};

</script>

<style scoped>
@import '#/styles/dark-antd.less';
.material-container {
  padding: 0px 20px;
}

.upload-section {
  margin-bottom: 20px;
}

.upload-actions {
  margin-top: 6px;
  display: flex;
  gap: 8px;
}

</style>
