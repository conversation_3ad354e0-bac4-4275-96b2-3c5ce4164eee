import { requestClient } from '#/api/request';

enum Api {
  list = '/positions',
  tree = '/positions/tree',
  create = '/positions',
  update = '/positions',
  delete = '/positions',
  detail = '/positions',
}

/**
 * 获取岗位列表
 * @param params 查询参数
 */
export const getPositionList = (params?: any) => {
  return requestClient.get(Api.list, { params });
};

/**
 * 获取岗位树形结构
 * @param params 查询参数
 */
export const getPositionTree = (params?: any) => {
  return requestClient.get(Api.tree, { params });
};

/**
 * 创建岗位
 * @param data 岗位数据
 */
export const createPosition = (data: any) => {
  return requestClient.post(Api.create, data);
};

/**
 * 更新岗位
 * @param id 岗位ID
 * @param data 岗位数据
 */
export const updatePosition = (id: number, data: any) => {
  return requestClient.put(`${Api.update}/${id}`, data);
};

/**
 * 删除岗位
 * @param id 岗位ID
 */
export const deletePosition = (id: number) => {
  return requestClient.delete(`${Api.delete}/${id}`);
};

/**
 * 获取岗位详情
 * @param id 岗位ID
 */
export const getPositionDetail = (id: number) => {
  return requestClient.get(`${Api.detail}/${id}`);
};

/**
 * 批量删除岗位
 * @param ids 岗位ID数组
 */
export const batchDeletePositions = (ids: number[]) => {
  return requestClient.delete(Api.delete, { data: { ids } });
};
