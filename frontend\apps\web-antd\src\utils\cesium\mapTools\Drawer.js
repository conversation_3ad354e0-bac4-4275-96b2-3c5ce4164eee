import * as Cesium from 'cesium';
import core from '../core.js';
import { GISMap } from '../index';
import eb, { eventbus } from '../../../ztu/eventbus.js';

export default class Drawer extends eventbus {
  constructor(viewer, options) {
    super();
    this.viewer = viewer || GISMap.viewer;
    this.entities = [];
    this.handler = null;
    this.tipEntity = null;
    this.tipPosition = null;
    this.isDrawing = false;
    this.dragHandler = null;
    this.options = {
      selectable: false,
      draggable: false,
      removeable: false,
      highlightable: false,
      ...options,
    };
    // 根据scene.mode判断当前场景模式
    this.is2DMode = this.viewer.scene.mode === Cesium.SceneMode.SCENE2D;
    
    // 监听场景模式变化
    this.viewer.scene.morphComplete.addEventListener(() => {
      this.is2DMode = this.viewer.scene.mode === Cesium.SceneMode.SCENE2D;
      // 更新所有实体的显示状态
      this.entities.forEach(entity => {
        if(entity.heightReference) {
          entity.heightReference = this.is2DMode ? 
            Cesium.HeightReference.NONE : 
            Cesium.HeightReference.RELATIVE_TO_GROUND;
        }
      });
    });
    if (this.options.draggable) {
      this.dragEntity();
    }
  }


  drawPoint(position, options) {
    let defaultOptions = {
      pixelSize: 4,
      color: Cesium.Color.RED,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 2,
      heightReference: this.is2DMode ? Cesium.HeightReference.NONE : Cesium.HeightReference.RELATIVE_TO_GROUND,
    };
    let entity = this.viewer.entities.add({
      name: 'point',
      entityType: 'point',
      position: position,
      point: {
        ...defaultOptions,
        ...options,
      },
    });
    this.entities.push(entity);
    return entity;
  }
  drawLine(positions, center, options) {
    let defaultOptions = {
      show: true,
      positions: positions,
      material: Cesium.Color.CHARTREUSE,
      width: 2,
      clampToGround: false,
      heightReference: this.is2DMode ? Cesium.HeightReference.NONE : Cesium.HeightReference.RELATIVE_TO_GROUND,
    };
    let entity = this.viewer.entities.add({
      position: center,
      name: 'polyline',
      polyline: {
        ...defaultOptions,
        ...options,
      },
    });
    this.entities.push(entity);
    return entity;
  }
  drawMarker(position, options) {
    let defaultOptions = {
      show: true,
      scale: 1,
      heightReference: this.is2DMode ? Cesium.HeightReference.NONE : Cesium.HeightReference.RELATIVE_TO_GROUND,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM
    };
    let entity = this.viewer.entities.add({
      name: 'billboard',
      show: true,
      position: position,
      billboard: {
        ...defaultOptions,
        ...options,
      },
    });
    this.entities.push(entity);
    return entity;
  }
  drawCircle(position, options) {
    let defaultOptions = {
      semiMinorAxis: 10,
      semiMajorAxis: 10,
      material: Cesium.Color.BLUE.withAlpha(0.5),
      heightReference: this.is2DMode ? Cesium.HeightReference.NONE : Cesium.HeightReference.RELATIVE_TO_GROUND,
      height: this.is2DMode ? 0 : undefined,
      outline: true,
      outlineColor: Cesium.Color.WHITE
    };
    let entity = this.viewer.entities.add({
      name: 'ellipse',
      position: position,
      ellipse: {
        ...defaultOptions,
        ...options,
      },
    });
    this.entities.push(entity);
    return entity;
  }
  drawRectangle(positions, center, options) {
    let defaultOptions = {
      coordinates: positions,
      material: Cesium.Color.GREEN.withAlpha(0.5),
      heightReference: this.is2DMode ? Cesium.HeightReference.NONE : Cesium.HeightReference.RELATIVE_TO_GROUND,
      height: this.is2DMode ? 0 : options.height,
      outline: true,
      outlineColor: Cesium.Color.WHITE
    };
    let entity = this.viewer.entities.add({
      position: center,
      name: 'rectangle',
      rectangle: {
        ...defaultOptions,
        ...options,
      },
    });
    this.entities.push(entity);
    return entity;
  }
  drawPolygon(positions, center, options) {
    let defaultOptions = {
      hierarchy: positions,
      material: Cesium.Color.GREEN.withAlpha(0.5),
      heightReference: this.is2DMode ? Cesium.HeightReference.NONE : Cesium.HeightReference.RELATIVE_TO_GROUND,
      height: this.is2DMode ? 0 : undefined,
      extrudedHeight: this.is2DMode ? undefined : options.extrudedHeight,
      classificationType: this.is2DMode ? undefined : Cesium.ClassificationType.BOTH
    };
    let entity = this.viewer.entities.add({
      position: center,
      name: 'polygon',
      polygon: {
        ...defaultOptions,
        ...options,
      },
    });
    this.entities.push(entity);
    return entity;
  }
  drawModel(position, options) {
    let hpRoll = new Cesium.HeadingPitchRoll(Cesium.Math.toRadians(0), 0, 0);
    let orientation =
      options.orientation ||
      Cesium.Transforms.headingPitchRollQuaternion(position, hpRoll);
    //console.log(orientation,position,options)
    let defaultOptions = {
      //uri: './models/siteModel/scene.gltf',   // 模型路径，自己换成自己的模型
      scale: 1,
      show: true, // 模型是否可见
    };
    let entity = this.viewer.entities.add({
      name: 'model',
      position: position,
      orientation: orientation,
      model: {
        ...defaultOptions,
        ...options,
      },
    });
    this.entities.push(entity);
    return entity;
  }
  drawText(position, options) {
    let defaultOptions = {
      text: '',
      font: '12px sans-serif',
      fillColor: Cesium.Color.WHITE,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      outlineWidth: 2,
      verticalOrigin: Cesium.VerticalOrigin.TOP,
      horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
      pixelOffset: new Cesium.Cartesian2(20, -20),
    };
    let entity = this.viewer.entities.add({
      name: 'label',
      position: position,
      label: {
        ...defaultOptions,
        ...options,
      },
    });
    this.entities.push(entity);
    return entity;
  }
  drawEntity(entity) {
    if (!entity) return entity;
    this.entities.push(entity);
    this.viewer.entities.add(entity);
    this.emit('add', entity);
    return entity;
  }
  select(entity) {
    let idx = this.entities.indexOf(entity);
    if (idx >= 0) {
      entity.color = Cesium.Color.RED;
    }
  }
  remove(entities) {
    if (!entities) return;
    if (!(entities instanceof Array)) entities = [entities];
    while (entities.length) {
      let entity = entities.pop();
      let idx = this.entities.indexOf(entity);
      if (idx >= 0) {
        this.viewer.entities.remove(entity);
        this.entities.splice(idx, 1);
      }
    }
  }
  clear() {
    this.entities.forEach((entity) => {
      this.viewer.entities.remove(entity);
    });
    this.entities = [];
    this.emit('clear');
  }
  show() {
    this.entities.forEach((entity) => {
      entity.show = true;
    });
  }
  hide() {
    this.entities.forEach((entity) => {
      entity.show = false;
    });
  }
  drawStart(mode, options, callback) {
    this.drawEnd();
    var positions = [],
      _positions = [];
    var entity = null;
    var positionMaxLength = 0;
    var positionMinLength = 0;
    this.isDrawing = true;
    eb.emit('draw', { from: 'Drawer' });
    this.tipEntity = this.drawText(
      new Cesium.CallbackProperty(() => {
        return this.tipPosition;
      }, false),
      {
        text: new Cesium.CallbackProperty(function () {
          if (positions.length == 0) return '点击开始,右击取消';
          else if (positions.length <= positionMinLength)
            return '点击继续,右击取消';
          return '点击继续,右击结束';
        }, false),
        font: '12px sans-serif',
        fillColor: Cesium.Color.WHITE,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        pixelOffset: new Cesium.Cartesian2(5, 5),
        showBackground: true, //指定标签后面背景的可见性
        backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
        backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
    );
    this.viewer._element.style.cursor = 'crosshair';
    this.viewer.enableCursorStyle = true;
    // 注册鼠标左击事件
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.position);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(this.viewer, movement.position);
      // 存储第一个点
      if (!cartesian) {
        return false;
      }

      if (positions.length == 0) {
        positions.push(cartesian.clone());
        positions.push(cartesian);
        _positions.push(cartesian.clone());
        _positions.push(cartesian);
        console.log(cartesian);
        //添加一个蓝点
        if (mode == 'point') {
          entity = this.drawPoint(
            new Cesium.CallbackProperty(function () {
              return positions[0];
            }, false),
            options,
          );
          positionMaxLength = 1;
        } else if (mode == 'polyline') {
          entity = this.drawLine(
            new Cesium.CallbackProperty(function () {
              return positions;
            }, false),
            new Cesium.CallbackProperty(function () {
              return core.getPolylineCenter(positions);
            }, false),
            options,
          );
          positionMinLength = 2;
        } else if (mode == 'polygon') {
          entity = this.drawPolygon(
            new Cesium.CallbackProperty(function () {
              return new Cesium.PolygonHierarchy(positions);
            }, false),
            new Cesium.CallbackProperty(function () {
              return core.getPolygonCenter(positions);
            }, false),
            options,
          );
          positionMinLength = 3;
        } else if (mode == 'marker') {
          entity = this.drawMarker(
            new Cesium.CallbackProperty(function () {
              return positions[0];
            }, false),
            options,
          );
          positionMaxLength = 1;
        } else if (mode == 'model') {
          entity = this.drawModel(positions[0], options);
          positionMaxLength = 1;
          //console.log(entity)
          //this.viewer.zoomTo(entity)
        } else if (mode == 'circle') {
          entity = this.drawCircle(
            new Cesium.CallbackProperty(() => {
              return positions[0].clone();
            }, false),
            {
              semiMinorAxis: new Cesium.CallbackProperty(() => {
                let radius = Cesium.Cartesian3.distance(
                  _positions[0],
                  _positions[1],
                );
                return (Math.max(0.01, radius) / 1.0).toFixed(2) * 1.0;
              }, false),
              semiMajorAxis: new Cesium.CallbackProperty(() => {
                let radius = Cesium.Cartesian3.distance(
                  _positions[0],
                  _positions[1],
                );
                return (Math.max(0.01, radius) / 1.0).toFixed(2) * 1.0;
              }, false),
              ...(() => {
                if (options.clampToGround) return {};
                else
                  return {
                    height: new Cesium.CallbackProperty(() => {
                      return this.viewer.scene.globe.ellipsoid.cartesianToCartographic(
                        positions[0],
                      ).height;
                    }, false),
                  };
              })(),
              ...options,
            },
          );
          positionMaxLength = 2;
          positionMinLength = 2;
        } else if (mode == 'rectangle') {
          entity = this.drawRectangle(
            new Cesium.CallbackProperty(function () {
              return Cesium.Rectangle.fromCartesianArray(positions);
            }, false),
            new Cesium.CallbackProperty(function () {
              return core.getPolygonCenter(positions);
            }, false),
            options,
          );
          positionMaxLength = 2;
          positionMinLength = 2;
        } else if (mode == 'text') {
          entity = this.drawText(
            new Cesium.CallbackProperty(function () {
              return positions[0];
            }, false),
            options,
          );
          positionMaxLength = 1;
        } else {
          this.drawEnd();
        }
        if (positionMaxLength == 1) {
          this.drawEnd(entity, callback);
        }
      } else {
        positions.push(cartesian);
        _positions.push(cartesian.clone());
        if (positionMaxLength && positions.length >= positionMaxLength) {
          this.drawEnd(entity, callback);
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // 注册鼠标移动事件
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.endPosition);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(
        this.viewer,
        movement.endPosition,
      );
      if (!cartesian) {
        return false;
      }
      if (positions.length > 0) {
        positions.pop();
        positions.push(cartesian);
        _positions.pop();
        _positions.push(cartesian.clone());
      }
      this.tipPosition = cartesian;
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    //注册鼠标右击事件
    this.handler.setInputAction((movement) => {
      if (positions.length == 0) this.drawEnd();
      else if (positions.length <= positionMinLength) {
        this.remove(entity);
        this.drawEnd();
      } else {
        positions.pop();
        this.drawEnd(entity, callback);
      }
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }
  drawEnd(entity, callback) {
    if (this.handler && !this.handler.isDestroyed()) {
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      this.handler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK,
      );
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      this.handler.destroy();
    }
    this.remove(this.tipEntity);
    this.isDrawing = false;

    this.viewer._element.style.cursor = 'default';
    this.viewer.enableCursorStyle = false;
    console.log(entity);
    entity && this.emit('add', entity);
    if (entity && typeof callback === 'function') {
      callback(entity);
      //console.log(core.entity2Json(entity))
    }
    eb.emit('drawEnd', { from: 'Drawer' });
  }
  toJson() {
    let json = [];
    this.entities.forEach((entity) => {
      json.push(core.entity2Json(entity));
    });
    return json;
  }
  doubleClick(callback) {
    let handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    handler.setInputAction((movement) => {
      let picks = this.viewer.scene.drillPick(movement.position);
      if (!picks || picks.length == 0) return;
      let pointDraged = picks[0].id;
      if (!pointDraged) return;
      if (!pointDraged || !this.entities.includes(pointDraged)) return;
      callback && callback(pointDraged);
    }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
  }
  dragEntity(callBack) {
    var leftDownFlag = false;
    var pointDraged = null;
    var viewer = this.viewer;
    var handler, cartesian;
    var startPoint;
    var polylinePreviousCoordinates;
    var polygonPreviousHierarchy;
    var rectanglePreviousCoordinates = {};
    var rectanglePreviousHeight = 0;
    if (!this.dragHandler || this.dragHandler.isDestroyed()) {
      this.dragHandler = new Cesium.ScreenSpaceEventHandler(
        viewer.scene.canvas,
      );
    }
    handler = this.dragHandler;

    // Select plane when mouse down
    handler.setInputAction((movement) => {
      //console.log(this.isDrawing,this.draggable)
      if (this.isDrawing || !this.options.draggable) return;
      //pointDraged = viewer.scene.pick(movement.position);//选取当前的entity
      let picks = viewer.scene.drillPick(movement.position);
      if (!picks || picks.length == 0) return;
      pointDraged = picks[0].primitive;
      console.log(pointDraged, pointDraged._boundingSpheresKeys);
      console.log(pointDraged instanceof Cesium.GroundPrimitive);
      if (!pointDraged) return;
      pointDraged =
        pointDraged.id ||
        (pointDraged instanceof Cesium.GroundPrimitive &&
          pointDraged._boundingSpheresKeys[0]);
      if (!pointDraged || !this.entities.includes(pointDraged)) return;
      this.select(pointDraged);
      //记录按下去的坐标
      startPoint = core.getCatesian3FromPX(this.viewer, movement.position);
      //startPoint = viewer.scene.globe.pick(viewer.camera.getPickRay(movement.position), viewer.scene);
      //startPoint = viewer.scene.pickPosition(movement.position);
      viewer.scene.screenSpaceCameraController.enableRotate = false; //锁定相机
      viewer.scene.screenSpaceCameraController.enableInputs = false;

      // Release plane on mouse up
      handler.setInputAction(function () {
        viewer.scene.screenSpaceCameraController.enableInputs = true;
        viewer.scene.screenSpaceCameraController.enableRotate = true; //锁定相机
        if (callBack) {
          callBack(pointDraged, cartesian);
          pointDraged = null;
        }
        handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
        handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      }, Cesium.ScreenSpaceEventType.LEFT_UP);
      // Update plane on mouse move
      handler.setInputAction((movement) => {
        if (pointDraged != null) {
          //记录尾随的坐标
          //let startPosition = viewer.scene.globe.pick(viewer.camera.getPickRay(movement.startPosition), viewer.scene)
          //let endPosition = viewer.scene.globe.pick(viewer.camera.getPickRay(movement.endPosition), viewer.scene)
          let startPosition = core.getCatesian3FromPX(
            this.viewer,
            movement.startPosition,
          );
          let endPosition = core.getCatesian3FromPX(
            this.viewer,
            movement.endPosition,
          );
          if (!startPosition || !endPosition) return;

          //计算每次的偏差
          let changed_x = endPosition.x - startPosition.x;
          let changed_y = endPosition.y - startPosition.y;
          let changed_z = endPosition.z - startPosition.z;

          if (pointDraged.position) {
            pointDraged.position = new Cesium.CallbackProperty(function () {
              /* console.log(pointDraged.position)
							let position =pointDraged.position.getValue();
							console.log(pointDraged.position,position)
							position.x=position.x+changed_x;
							position.y=position.y+changed_y;
							position.z=position.z+changed_z;
							return position; */
              return endPosition;
            }, false); //防止闪烁，在移动的过程console.log(pointDraged.id);
          }
          if (pointDraged.polyline) {
            let polylinePreviousCoordinates =
              pointDraged.polyline.positions.getValue();
            for (let i = 0; i < polylinePreviousCoordinates.length; i++) {
              //与之前的算差 替换掉
              polylinePreviousCoordinates[i].x =
                polylinePreviousCoordinates[i].x + changed_x;
              polylinePreviousCoordinates[i].y =
                polylinePreviousCoordinates[i].y + changed_y;
              polylinePreviousCoordinates[i].z =
                polylinePreviousCoordinates[i].z + changed_z;
            }
            pointDraged.polyline.positions = new Cesium.CallbackProperty(
              function () {
                return polylinePreviousCoordinates;
              },
              false,
            );
          }

          if (pointDraged.polygon) {
            let polygonPreviousHierarchy =
              pointDraged.polygon.hierarchy.getValue();
            let polygonPositions = polygonPreviousHierarchy.positions;
            let polygonHoles = polygonPreviousHierarchy.holes;
            for (let i = 0; i < polygonPositions.length; i++) {
              polygonPositions[i].x = polygonPositions[i].x + changed_x;
              polygonPositions[i].y = polygonPositions[i].y + changed_y;
              polygonPositions[i].z = polygonPositions[i].z + changed_z;
            }
            for (let i = 0; i < polygonHoles.length; i++) {
              polygonHoles[i].x = polygonHoles[i].x + changed_x;
              polygonHoles[i].y = polygonHoles[i].y + changed_y;
              polygonHoles[i].z = polygonHoles[i].z + changed_z;
            }
            pointDraged.polygon.hierarchy = new Cesium.CallbackProperty(
              function () {
                return new Cesium.PolygonHierarchy(
                  polygonPositions,
                  polygonHoles,
                );
              },
              false,
            );
          }

          if (pointDraged.rectangle) {
            let rectanglePreviousCoordinates =
              pointDraged.rectangle.coordinates.getValue();
            let rectanglePreviousHeight =
              pointDraged.rectangle.height.getValue();
            let storePoint = new Cesium.Cartesian3.fromRadiansArrayHeights([
              rectanglePreviousCoordinates.west,
              rectanglePreviousCoordinates.north,
              rectanglePreviousHeight,
              rectanglePreviousCoordinates.east,
              rectanglePreviousCoordinates.south,
              rectanglePreviousHeight,
            ]);
            for (let i = 0; i < storePoint.length; i++) {
              storePoint[i].x = storePoint[i].x + changed_x;
              storePoint[i].y = storePoint[i].y + changed_y;
              storePoint[i].z = storePoint[i].z + changed_z;
            }
            pointDraged.rectangle.coordinates = new Cesium.CallbackProperty(
              function () {
                return Cesium.Rectangle.fromCartesianArray(storePoint);
              },
              false,
            );
          }

          if (pointDraged.ellipse) {
            let position_end = endPosition;
            let cartographic_end =
              Cesium.Cartographic.fromCartesian(position_end);
            let height_end = cartographic_end.height;
            pointDraged.ellipse.height = new Cesium.CallbackProperty(
              function () {
                return height_end;
              },
              false,
            );
          }
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
  }
}
