import { Entity, Column, Index, ManyToMany, JoinTable } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '@/common/entities/base.entity';
import { User } from '@/modules/users/entities/user.entity';
import { Role } from '@/modules/roles/entities/role.entity';

@Entity('user_groups')
@Index(['status'])
export class UserGroup extends BaseEntity {
  @ApiProperty({ description: '用户组名称' })
  @Column({
    type: 'varchar',
    length: 100,
    comment: '用户组名称',
  })
  name: string;

  @ApiProperty({ description: '用户组描述' })
  @Column({
    type: 'text',
    nullable: true,
    comment: '用户组描述',
  })
  description?: string;

  @ApiProperty({ description: '状态：1启用，0禁用' })
  @Column({
    type: 'tinyint',
    default: 1,
    comment: '状态：1启用，0禁用',
  })
  status: number;

  // 关联关系
  @ManyToMany(() => User)
  @JoinTable({
    name: 'user_group_members',
    joinColumn: { name: 'user_group_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' },
  })
  users?: User[];

  @ManyToMany(() => Role)
  @JoinTable({
    name: 'user_group_roles',
    joinColumn: { name: 'user_group_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
  })
  roles?: Role[];
}
