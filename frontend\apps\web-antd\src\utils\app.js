import axios from 'axios';
import * as Cesium from 'cesium';

import * as api from '../api/core/map.js';
import Box from '../components/JS/box';
import proj4 from './cesium/config/proj4Defs.js';
import XMLToJSON from './XMLToJSON';
import ztu from "#/ztu/index.js";

let baseLayers = [
  {
    fid: 0,
    id: 99,
    title: '底图',
    key: 'imagery',
  },
  // http://localhost:9005/terrain/srgznFSa/layer.json
  // https://localhost:6443/arcgis/rest/services/fzdem/MapServer/0
  {
    fid: 99,
    id: 323_391,
    title: '信息学院1223',
    key: 'xzqh1221',
    data: {
      url: 'http://fzzt.fzjhdn.com:6080/arcgis/rest/services/MyMapService/MapServer',
      layers: 0,
    },
    type: 'arcgis:geojson',
  },
  {
    fid: 99,
    id: 20,
    title: '天地图标记',
    key: 'tianditu4',
    data: {
      url:
        'http://t0.tianditu.com/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg' +
        '&tk=' +
        '9189606639aa352dc18b332505a47597',
      // layer: "tdtAnnoLayer",
      // style: "default",
      // maximumLevel: 19, //天地图的最大缩放级别
      // format: "image/jpeg",
      // tileMatrixSetID: "GoogleMapsCompatible",
      // show: true,
    },
    type: 'wmts',
  },
  {
    fid: 99,
    id: 18,
    title: '天地图地形',
    key: 'tianditu3',
    data: {
      url: 'http://t0.tianditu.gov.cn/ter_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=ter&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=9189606639aa352dc18b332505a47597',
      // subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      // layer: "tdtImgLayer",
      // style: "default",
      // format: "image/jpeg",
      // tileMatrixSetID: "GoogleMapsCompatible", //使用谷歌的瓦片切片方式
      // show: true
    },
    type: 'wmts',
  },
  {
    fid: 99,
    id: 17,
    title: '天地图矢量',
    key: 'tianditu2',
    data: {
      url: 'http://t0.tianditu.gov.cn/vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=9189606639aa352dc18b332505a47597',
      // subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      // layer: "tdtImgLayer",
      // style: "default",
      // format: "image/jpeg",
      // tileMatrixSetID: "GoogleMapsCompatible", //使用谷歌的瓦片切片方式
      // show: true
    },
    type: 'wmts',
  },
  {
    fid: 99,
    id: 16,
    title: '天地图影像',
    key: 'tianditu1',
    data: {
      url: 'http://t0.tianditu.com/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=9189606639aa352dc18b332505a47597',
      // subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      // layer: "tdtImgLayer",
      // style: "default",
      // format: "image/jpeg",
      // tileMatrixSetID: "GoogleMapsCompatible", //使用谷歌的瓦片切片方式
      // show: true
    },
    type: 'wmts',
  },
  {
    fid: 99,
    id: 39,
    title: '福州地形',
    key: 'fzdx',
    data: {
      url: 'http://fzzt.fzjhdn.com:19005/terrain/srgznFSa',
    },
    type: 'terrain',
  },
  {
    fid: 99,
    id: 101,
    title: '全球地形',
    key: 'terrain',
    data: {
      provider: '',
    },
    type: 'terrain',
  },
  {
    fid: 99,
    id: 100,
    title: 'Bing影像',
    key: 'bingmap',
    data: {},
    type: 'bing',
  },
];

const layerFields = {
  隧道: [
    {
      name: 'SDMC',
      cnName: '隧道名称',
      allowQuery: true,
      allowDisplay: true,
    },
    {
      name: 'SQS_Q',
      cnName: '城市',
      allowQuery: true,
      allowDisplay: true,
    },
    {
      name: 'XZDJ',
      cnName: '公路等级',
      allowQuery: true,
      allowDisplay: true,
    },
    {
      name: 'SZLDBH',
      cnName: '隧道编码',
      allowQuery: true,
      allowDisplay: true,
    },
  ],
};
const types = {
  esriFieldTypeDouble: 'number',
  esriFieldTypeString: 'string',
  esriFieldTypeOID: 'number',
  esriFieldTypeInteger: 'number',
  int: 'number',
  number: 'number',
  string: 'string',
};
export class LayerInfo {
  constructor(options) {
    options = options || {};
    this.fullTitle = options.fullTitle || '';
    this.title = options.title || '';
    this.key = options.key || '';
    this.type = options.type || '';
    this._data = options.data || null;
    this.isStaticLayer = options.isStaticLayer || !!this._data;
    this.refLayer = null;
    this.show = false;
    this._fieldList = null;
    this._capabilities = null;
    this.isBaseLayer = options.isBaseLayer || false;
  }
  clone() {
    const layerInfo = new LayerInfo({
      fullTitle: this.fullTitle,
      title: this.title,
      key: this.key,
      type: this.type,
      data: JSON.parse(JSON.stringify(this.data)),
      isStaticLayer: this.isStaticLayer,
    });
    layerInfo._fieldList = this._fieldList;
    layerInfo._capabilities = this._capabilities;
    return layerInfo;
  }
  async getCapabilities(verifyAuth = true) {
    if (this.isStaticLayer) return null;
    // console.log(capp.router.currentRoute)
    const funcTitle = capp.router.currentRoute.value.meta.func;
    if (verifyAuth && funcTitle) {
      const func = capp.funs[funcTitle];
      if (func) {
        await api.verifyFuncAuth({
          funcId: func.id,
          funcPath: func.fullTitle,
          layerId: this.key,
          layerPath: this.fullTitle,
        });
      } else {
        await capp.message('该功能未授权');
        return capp.router.replace({
          name: 'login',
        });
      }
    }
    if (this._capabilities) return this._capabilities;
    const data = await this.getData();
    if (!data) return;
    if (this.type.toLocaleLowerCase().startsWith('arcgis:mapserver')) {
      const cap = await axios.get(
        `${data.url.trim('/')}/${data.layers}?f=pjson&token=${data.token || ''}`,
      );
      if (cap && cap.data.error) {
        await this.getData(true);
        return await getCapabilities(false);
      }
      // console.log(cap)
      cap &&
        (this._capabilities = {
          useStandardizedQueries: cap.data.useStandardizedQueries || false,
          supportsPagination:
            (cap.data.supportsAdvancedQueries &&
              cap.data.advancedQueryCapabilities.supportsPagination) ||
            false,
          maxRecordCount: cap.data.maxRecordCount || 0,
          supportsStatistics: cap.data.supportsStatistics || false,
          supportsAdvancedQueries: cap.data.supportsAdvancedQueries || false,
          fields: (cap.data.fields || []).map((a) => {
            return {
              name: a.name,
              cnName: a.alias,
              allowQuery: a.type == 'esriFieldTypeString',
              allowDisplay: a.type != 'esriFieldTypeGeometry',
            };
          }),
          extent: {
            ...(cap.data.extent || cap.data.fullExtent),
            wkid: `EPSG:${
              (cap.data.extent || cap.data.fullExtent).spatialReference.wkid
            }`,
            wkt: (cap.data.extent || cap.data.fullExtent).spatialReference.wkt,
          },
          isRasterLayer: cap.data.type == 'Raster Layer',
          isFeatureLayer: cap.data.type == 'Feature Layer',
        });
      // console.log(this._capabilities.isRasterLayer,this.type)
      // if(this._capabilities.isRasterLayer && this.type.toLocaleLowerCase()=='arcgis:mapserver'){
      // console.log(this._capabilities.isRasterLayer,this.type)
      //	this.type = 'arcgis:tms';
      // }
    } else if (this.type.toLocaleLowerCase().startsWith('geoserver:wms')) {
      let res = await axios.get(
        `${data.url.trim('/')}?service=wfs&version=2.0.0` +
          `&request=DescribeFeatureType&typeNames=${data.layers}&outputFormat=application%2Fjson`,
      );
      let fieldList = [];
      if (
        res &&
        res.data &&
        Array.isArray(res.data.featureTypes) &&
        res.data.featureTypes.length > 0 &&
        Array.isArray(res.data.featureTypes[0].properties)
      ) {
        fieldList = res.data.featureTypes[0].properties.map((a) => {
          return {
            name: a.name,
            cnName: a.name,
            allowQuery: a.localType == 'string',
            allowDisplay: a.name != 'the_geom',
          };
        });
      }
      // console.log(fieldList)
      res = await axios.get(`${data.url.trim('/')}?request=getCapabilities`);
      // console.log(res)
      if (res && res.data) {
        const json = new XMLToJSON().fromStr(res.data);
        console.log(json);
        const layers = json.WMS_Capabilities.Capability.Layer.Layer;
        const layer = Array.isArray(layers)
          ? layers.find((a) => a.Name['#text'] == this.data.layers)
          : layers;
        if (layer) {
          const queryable = layer['@attributes'].queryable == '1';
          this._capabilities = {
            useStandardizedQueries: queryable,
            supportsPagination: queryable,
            maxRecordCount: 0,
            supportsStatistics: queryable,
            supportsAdvancedQueries: queryable,
            fields: fieldList,
            extent: layer.BoundingBox.map((a) => {
              const extent = a['@attributes'];
              return {
                xmin: Number.parseFloat(extent.minx),
                ymin: Number.parseFloat(extent.miny),
                xmax: Number.parseFloat(extent.maxx),
                ymax: Number.parseFloat(extent.maxy),
                wkid: extent.CRS,
              };
            }).shift(), // .filter(a=>a.wkid.startsWith('EPSG:')).shift()
          };
        }
        console.log(layer, this._capabilities);
      }
    }

    if (this._capabilities && this._capabilities.extent) {
      const extent = this._capabilities.extent;
      console.log(extent);
      const r1 = proj4(
        this._capabilities.extent.wkt || this._capabilities.extent.wkid,
        'EPSG:4326',
        [extent.xmin, extent.ymin],
      );
      const r2 = proj4(
        this._capabilities.extent.wkt || this._capabilities.extent.wkid,
        'EPSG:4326',
        [extent.xmax, extent.ymax],
      );
      console.log(r1, r2);
      this.data.rectangle = Cesium.Rectangle.fromDegrees(
        r1[0],
        r1[1],
        r2[0],
        r2[1],
      );
    } else this._capabilities = {};
    // console.log(this._capabilities)
    return this._capabilities;
  }
  async getData(reload = false) {
    if (this.isStaticLayer) return this._data;
    if (this._data && !reload) return this._data;
    const data = null;
    if (data) {
      this._data = {
        layers: '0',
      };
      for (const p in data || {}) {
        if (p == 'gisType') {
          if (data[p] && !this.type.includes(':')) {
            this.type = `${data.gisType}:${this.type}`.toLocaleLowerCase();
          }
          if (data.gisType == 'cesiumlab') {
            // this.type = '3dtiles'
            if (data.url && data.url.endsWith('model')) {
              data.url = `${data.url}/${data.signName}/${data.sign}`;
            }
          } else if (data.gisType == 'geoserver') {
            // data.url = data.url.replace('8086','18086');
          }
        } else if (p == 'token') data[p] && (this._data.token = data.token);
        else if (p == 'sign') {
          this._data.layers = data.sign || data.signName;
        } else this._data[p] = data[p];
      }
    }
    return this._data;
  }

  async getFieldList() {
    if (this.isBaseLayer) return [];
    if (this._fieldList) return this._fieldList;
    const data = await api.getFieldList({
      layerId: this.key,
    });
    if (data && data.length > 0) {
      this._fieldList = data.map((a) => {
        return {
          name: a.attrName,
          cnName: a.alias,
          allowQuery: a.searchable,
          allowDisplay: a.visible,
          type: types[a.type],
        };
      });
      return this._fieldList;
    }
    this._fieldList = layerFields[this.title];
    if (!this._fieldList) {
      await this.getCapabilities();
      if (this._capabilities) {
        this._fieldList = this._capabilities.fields;
      }
    }
    this._fieldList = this._fieldList || [];
    return this._fieldList;
  }
  get capabilities() {
    return this._capabilities;
  }
  get data() {
    return this._data;
  }
  get fieldList() {
    return this._fieldList || [];
  }
}

class App {
  constructor(router) {
    this.router = router;
    this.userInfo = {};
    this.appInfo = null;
    this._layersOrigin = [];
    this._funsOrigin = [];
    this.layers = {};
    this.funs = {};
    this.isReady = false;
    this.extent = null;
  }
  chkFuns(fun) {
    return !!this.funs[fun];
  }
  getLayer(key) {
    return this.layers[key];
  }
  goto(funcName) {
    if (!funcName || !this.chkFuns(funcName)) return;
    const route = this.router
      .getRoutes()
      .find((a) => a.meta && a.meta.func === funcName);
    if (!route) return;
    this.router.replace({
      name: route.name,
    });
  }

  async initLogin(data) {
    const res = await api.getAppBaseMap();
    if (res) {
      this.defaultShowLayerKey = res.showKey;
      baseLayers = res.layers;
      this.layersOrigin = [];

      // sceneLayers && sceneLayersmap(a => {
      //   this.layersOrigin.push({
      //     fid: a.parentId == '-1' ? 0 : a.parentId,
      //     id: a.id,
      //     title: a.name,
      //     key: a.id,
      //     type: a.type || '',
      //     //layers: a.layers || '0'
      //   })
      // })
    }
    this.isReady = true;
  }


  //添加图层数据到加载图层的列表中
  async addLayerDatas(layerDatas) {
    let GISLayers = ztu.global.GISLayers;
    layerDatas && layerDatas.map(a => {

          let old = null;
          this.layersOrigin.forEach(item=>{
            if(item.id === a.id){
              old = item;
            }
          })
          if(!old){
            this.layersOrigin.push({
              fid: a.parentId == '-1' ? 0 : a.parentId,
              id: a.id,
              title: a.name,
              key: a.id,
              type: a.type || '',
              data:{
                url:a.url,
                style:''
              }
              //layers: a.layers || '0'
            })
          }

          GISLayers.loadLayer([{
            type:a.type ,
            key:a.id,
            title: a.name,
            show:false,
            data:{
              url:a.url,
              style:''
            }
          }])
        })
  }

  logout() {
    localStorage.removeItem('user/token');
    localStorage.removeItem('user/account');
    localStorage.removeItem('layer/checked');
    localStorage.removeItem('layer/shapes');
    this.router.replace({
      name: 'login',
    });
  }
  message(p1, p2) {
    return new Promise((resolve, reject) => {
      const title = p2 ? p1 : '提示';
      const message = p2 || p1;
      const h = window.innerHeight;
      const w = window.innerWidth;
      const defaultOptions = {
        mask: true,
        style: {
          left: `${w / 2 - 150}px`,
          top: `${h / 2 - 80}px`,
          width: '300px',
        },
        title,
        beforeClose: () => {
          resolve();
        },
      };
      Box.open(defaultOptions, message);
    });
  }
  get account() {
    return localStorage.getItem('user/account');
  }
  set account(account) {
    if (account != this.account) {
      localStorage.removeItem('layer/checked');
      localStorage.removeItem('layer/shapes');
    }
    localStorage.setItem('user/account', account || '');
  }
  get chkLayers() {
    console.log('checklayers');
    const layers = JSON.parse(localStorage.getItem('layer/checked') || '[]');
    /* console.log(layers)
		if (!layers || layers.length == 0) {
			return [this.defaultShowLayerKey || 'bingmap'];
		} */
    console.log(layers);
    if (
      this.defaultShowLayerKey &&
      layers.filter((a) => a.startsWith('baseMap_')).length === 0
    ) {
      layers.push(this.defaultShowLayerKey);
    }
    console.log(layers);
    return layers;
  }
  set chkLayers(keys) {
    if (keys && typeof keys === 'string') keys = [keys];
    if (!Array.isArray(keys)) keys = [];
    localStorage.setItem('layer/checked', JSON.stringify(keys));
    console.log(keys);
  }
  get funsOrigin() {
    return this._funsOrigin;
  }
  set funsOrigin(funs) {
    funs = funs || [];
    this._funsOrigin = funs;
    this.funs = {};
    const loop = (fid = 0) => {
      const f = funs.find((a) => a.id == fid);
      funs
        .filter((a) => a.fid == fid)
        .forEach((a) => {
          if (f) {
            a.fullTitle = `${f.fullTitle || f.title}.${a.title}`;
          }
          loop(a.id);
        });
    };
    loop();
    funs.forEach((a) => {
      this.funs[a.fullTitle] = a;
    });
  }
  get layersOrigin() {
    return this._layersOrigin;
  }
  set layersOrigin(layers) {
    layers = layers || [];
    this.layers = {};
    const loop = (fid = 0) => {
      const f = layers.find((a) => a.id == fid);
      layers
        .filter((a) => a.fid == fid)
        .forEach((a) => {
          if (f) {
            a.fullTitle = `${f.fullTitle || f.title}.${a.title}`;
          }
          loop(a.id);
        });
    };
    loop();
    (() => {
      const a = (i = 0) => {
        console.log(i);
        if (i < 10) a(i + 1);
      };
      return a;
    })();
    baseLayers.forEach((a) => (a.isStaticLayer = true));
    this._layersOrigin = [...layers, ...baseLayers];
    this._layersOrigin
      .filter((a) => a.type)
      .forEach((a) => {
        this.layers[a.key] = new LayerInfo(a);
      });
    // console.log(layers)
  }
  get logined() {
    return !!localStorage.getItem('user/token');
  }
  get shapes() {
    return JSON.parse(localStorage.getItem('layer/shapes') || '[]');
  }
  set shapes(entitys) {
    if (!Array.isArray(entitys)) entitys = [];
    localStorage.setItem('layer/shapes', JSON.stringify(entitys));
  }
  get token() {
    return localStorage.getItem('user/token');
  }
  set token(token) {
    localStorage.setItem('user/token', token || '');
  }
}

export let capp = null;

export function appInit(vue) {
  capp = capp || new App(vue);
  return capp;
}

/*
http://fzzt.fzjhdn.com:18086/geoserver/anxi_xiaoban/wms?request=getCapabilities

http://fzzt.fzjhdn.com:18086/geoserver/topp/wms?
SERVICE=WMS&
VERSION=1.1.1&
REQUEST=GetMap&
FORMAT=image%2Fpng&
TRANSPARENT=true&
LAYERS=topp%3Astates&
exceptions=application%2Fvnd.ogc.se_inimage&
CQL_FILTER=PERSONS%3E%2015000000&
SRS=EPSG%3A4326&
STYLES=&
WIDTH=768&
HEIGHT=330&
BBOX=-129.638671875%2C22.67578125%2C-62.138671875%2C51.6796875

http://fzzt.fzjhdn.com:18086/geoserver/topp/wms?
SERVICE=WMS&VERSION=1.1.1&REQUEST=GetMap&FORMAT=image%2Fpng&TRANSPARENT=true&LAYERS=topp%3Astates&exceptions=application%2Fvnd.ogc.se_inimage&
CQL_FILTER=BBOX(the_geom%2C-90%2C40%2C-60%2C45)&
SRS=EPSG%3A4326&STYLES=&WIDTH=768&HEIGHT=330
BBOX=-155.56640625%2C4.21875%2C-20.56640625%2C62.2265625

http://fzzt.fzjhdn.com:18086/geoserver/topp/wms?SERVICE=WMS&VERSION=1.1.1&REQUEST=GetMap&FORMAT=image%2Fpng&TRANSPARENT=true&
tiled=true&LAYERS=topp%3Astates&exceptions=application%2Fvnd.ogc.se_inimage&tilesOrigin=-124.73142200000001%2C24.955967&WIDTH=256&HEIGHT=256&
SRS=EPSG%3A4326&STYLES=&BBOX=-90%2C0%2C-45%2C45

// http://fzzt.fzjhdn.com:18086/geoserver/wfs?
// request=GetFeature&
// typeName=topp:states&
// propertyName=STATE_NAME,LAND_KM,the_geom&
// CQL_FILTER=LAND_KM%20BETWEEN%20100000%20AND%20150000&
// version=1.0.0&
// outputFormat=application%2Fjson

http://fzzt.fzjhdn.com:18086/geoserver/topp/wms?
typeName=states&
SERVICE=wfs&VERSION=1.0.0&REQUEST=GetFeature&
CQL_FILTER=BBOX(the_geom%2C-90%2C40%2C-60%2C45)&
SRS=EPSG%3A4326&outputFormat=application%2Fjson
startIndex
maxFeatures
 */
