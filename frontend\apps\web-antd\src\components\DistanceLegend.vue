<script>
import { Cartesian2, defined, EllipsoidGeodesic } from 'cesium';

import { getGisMap, GISMap } from '../utils/cesium';

export default {
  data() {
    return {
      barWidth: 0,
      distance: 0,
      displayType: 0,
      distances: [
        1,
        2,
        3,
        5,
        10,
        20,
        30,
        50,
        100,
        200,
        300,
        500,
        1000,
        2000,
        3000,
        5000,
        10_000,
        20_000,
        30_000,
        50_000,
        100_000,
        200_000,
        300_000,
        500_000,
        1_000_000, // ,
        // 2000000,
        // 3000000,
        // 5000000,
        // 10000000,
        // 20000000,
        // 30000000,
        // 50000000
      ],
    };
  },
  computed: {
    distanceLabel() {
      if (!this.distance) {
        return '';
      }
      if (this.displayType == 1) {
        return `1:${this.distance}`;
      } else if (this.distance >= 1000) return `${this.distance / 1000}km`;
      else if (this.distance >= 1) return `${this.distance}m`;
      else return '';
    },
  },
  mounted() {
    const that = this;
    getGisMap((GISMap) => {
      const scene = GISMap.viewer.scene;
      scene.postRender.addEventListener(function () {
        const geodesic = new EllipsoidGeodesic();
        // Find the distance between two pixels at the bottom center of the screen.
        const width = scene.canvas.clientWidth;
        const height = scene.canvas.clientHeight;
        const left = scene.camera.getPickRay(
          new Cartesian2((width / 2) | 0, height - 1),
        );
        const right = scene.camera.getPickRay(
          new Cartesian2((1 + width / 2) | 0, height - 1),
        );
        const { globe } = scene;
        const leftPosition = globe.pick(left, scene);
        const rightPosition = globe.pick(right, scene);
        if (!defined(leftPosition) || !defined(rightPosition)) {
          this.barWidth = undefined;
          this.distance = undefined;
          return;
        }
        const leftCartographic =
          globe.ellipsoid.cartesianToCartographic(leftPosition);
        const rightCartographic =
          globe.ellipsoid.cartesianToCartographic(rightPosition);
        geodesic.setEndPoints(leftCartographic, rightCartographic);
        const pixelDistance = geodesic.surfaceDistance;
        // Find the first distance that makes the scale bar less than 100 pixels.
        const maxBarWidth = 100;
        let distance;
        const distances = this.distances;
        for (let i = distances.length - 1; !defined(distance) && i >= 0; --i) {
          if (distances[i] / pixelDistance < maxBarWidth) {
            distance = distances[i];
          }
        }
        this.barWidth = distance / pixelDistance;
        this.distance = distance;
      }, that);
    });
  },
  methods: {
    setDistanceLegend(distance) {
      const viewer = GISMap.viewer;
      const _pc = viewer.camera.positionCartographic.clone();
      const m = distance / this.distance;
      if (m < 1) {
        viewer.camera.moveForward(_pc.height * (1 - m));
      } else if (m > 1) {
        viewer.camera.moveBackward(_pc.height * (m - 1));
      }
    },
  },
};
</script>

<template>
  <div
    v-if="distanceLabel"
    class="distance-legend"
    @click="displayType = (displayType + 1) % 2"
  >
    <div class="distance-label">1 : {{ distanceLabel }}</div>
    <div :style="{ width: `${barWidth}px` }" class="bar"></div>
    <div class="distance-select">
      <div
        v-for="d in distances"
        :key="d"
        class="select-item"
        @click.stop="setDistanceLegend(d)"
      >
        1:{{ d > 1000 ? `${d / 1000}km` : `${d}m` }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.distance-legend {
  position: absolute;
  left: 20px;
  bottom: 20px;
  cursor: pointer;
  .distance-label {
    color: #eee;
    font-size: 12px;
    text-align: center;
    user-select: none;
  }
  .bar {
    box-sizing: border-box;
    height: 8px;
    border: 4px solid #eee;
    border-top: 0;
  }
  &:hover .distance-select {
    display: block;
  }
  .distance-select {
    position: absolute;
    left: 0;
    bottom: 100%;
    width: 120px;
    height: 200px;
    background: rgba(40, 44, 52, 0.5);
    border: 1px solid rgba(40, 44, 52, 0.8);
    display: none;
    overflow: auto;
    font-size: 12px;
    .select-item {
      height: 30px;
      text-align: left;
      padding: 0 10px;
      color: #eee;
      line-height: 30px;
      &:hover {
        color: #fff;
        background: rgba(40, 44, 52, 0.5);
      }
    }
  }
}
</style>
