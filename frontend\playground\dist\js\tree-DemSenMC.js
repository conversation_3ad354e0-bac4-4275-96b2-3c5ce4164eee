import{u as f}from"./vxe-table-a0ubJ4nQ.js";import{a as u}from"./table-data-D75cgFtg.js";import{B as o}from"./bootstrap-DShsrVit.js";import{_}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as c,af as g,ag as x,ah as a,a3 as r,n as i,an as n}from"../jse/index-index-BMh_AyeW.js";import"./empty-icon-Crs2MoG4.js";import"./loading-Cqdke3S1.js";import"./form-DnT3S1ma.js";const V=c({__name:"tree",setup(A){const s={columns:[{type:"seq",width:70},{field:"name",minWidth:300,title:"Name",treeNode:!0},{field:"size",title:"Size"},{field:"type",title:"Type"},{field:"date",title:"Date"}],data:u,pagerConfig:{enabled:!1},treeConfig:{parentField:"parentId",rowField:"id",transform:!0}},[p,l]=f({gridOptions:s}),d=()=>{var e;(e=l.grid)==null||e.setAllTreeExpand(!0)},m=()=>{var e;(e=l.grid)==null||e.setAllTreeExpand(!1)};return(e,t)=>(g(),x(r(_),null,{default:a(()=>[i(r(p),{"table-title":"数据列表","table-title-help":"提示"},{"toolbar-tools":a(()=>[i(r(o),{class:"mr-2",type:"primary",onClick:d},{default:a(()=>t[0]||(t[0]=[n(" 展开全部 ")])),_:1}),i(r(o),{type:"primary",onClick:m},{default:a(()=>t[1]||(t[1]=[n(" 折叠全部 ")])),_:1})]),_:1})]),_:1}))}});export{V as default};
