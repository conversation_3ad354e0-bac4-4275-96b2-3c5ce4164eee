const axios = require('axios');

// API基础URL
const API_BASE_URL = 'http://localhost:3000/api';

// 测试用的认证token（需要先登录获取）
let authToken = '';

async function testPositionsAPI() {
  console.log('=== 岗位管理API测试 ===\n');
  
  // 1. 测试登录获取token
  try {
    console.log('--- 1. 用户登录 ---');
    const loginResponse = await axios.post(`${API_BASE_URL}/system/login`, {
      account: 'admin',
      password: 'admin123'
    });

    authToken = loginResponse.data.data.token;
    console.log('✅ 登录成功');
    console.log('Token:', authToken.substring(0, 20) + '...');

  } catch (error) {
    console.error('❌ 登录失败:');
    console.error('状态码:', error.response?.status);
    console.error('错误信息:', error.response?.data || error.message);
    return;
  }
  
  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
  
  // 2. 测试获取岗位列表
  try {
    console.log('\n--- 2. 获取岗位列表 ---');
    const response = await axios.get(`${API_BASE_URL}/positions`, { headers });
    
    console.log('✅ 获取岗位列表成功');
    console.log('响应状态:', response.status);
    console.log('岗位数量:', response.data.data?.length || 0);
    
  } catch (error) {
    console.error('❌ 获取岗位列表失败:');
    console.error('状态码:', error.response?.status);
    console.error('错误信息:', error.response?.data || error.message);
  }
  
  // 3. 测试获取岗位树形结构
  try {
    console.log('\n--- 3. 获取岗位树形结构 ---');
    const response = await axios.get(`${API_BASE_URL}/positions/tree`, { headers });
    
    console.log('✅ 获取岗位树形结构成功');
    console.log('响应状态:', response.status);
    console.log('根节点数量:', response.data.data?.length || 0);
    
  } catch (error) {
    console.error('❌ 获取岗位树形结构失败:');
    console.error('状态码:', error.response?.status);
    console.error('错误信息:', error.response?.data || error.message);
  }
  
  // 4. 测试创建岗位
  let createdPositionId = null;
  try {
    console.log('\n--- 4. 创建测试岗位 ---');
    const createData = {
      name: '测试岗位',
      code: 'TEST_POS_' + Date.now(),
      parentId: 0,
      departmentId: 1, // 假设存在ID为1的组织
      type: 'department',
      sortOrder: 1,
      status: 1,
      description: '这是一个测试岗位'
    };
    
    const response = await axios.post(`${API_BASE_URL}/positions`, createData, { headers });
    
    createdPositionId = response.data.data.id;
    console.log('✅ 创建岗位成功');
    console.log('响应状态:', response.status);
    console.log('岗位ID:', createdPositionId);
    console.log('岗位名称:', response.data.data.name);
    
  } catch (error) {
    console.error('❌ 创建岗位失败:');
    console.error('状态码:', error.response?.status);
    console.error('错误信息:', error.response?.data || error.message);
  }
  
  // 5. 测试获取岗位详情
  if (createdPositionId) {
    try {
      console.log('\n--- 5. 获取岗位详情 ---');
      const response = await axios.get(`${API_BASE_URL}/positions/${createdPositionId}`, { headers });
      
      console.log('✅ 获取岗位详情成功');
      console.log('响应状态:', response.status);
      console.log('岗位名称:', response.data.data.name);
      console.log('岗位编码:', response.data.data.code);
      
    } catch (error) {
      console.error('❌ 获取岗位详情失败:');
      console.error('状态码:', error.response?.status);
      console.error('错误信息:', error.response?.data || error.message);
    }
  }
  
  // 6. 测试更新岗位
  if (createdPositionId) {
    try {
      console.log('\n--- 6. 更新岗位 ---');
      const updateData = {
        name: '更新后的测试岗位',
        description: '这是更新后的岗位描述'
      };
      
      const response = await axios.put(`${API_BASE_URL}/positions/${createdPositionId}`, updateData, { headers });
      
      console.log('✅ 更新岗位成功');
      console.log('响应状态:', response.status);
      console.log('更新后名称:', response.data.data.name);
      
    } catch (error) {
      console.error('❌ 更新岗位失败:');
      console.error('状态码:', error.response?.status);
      console.error('错误信息:', error.response?.data || error.message);
    }
  }
  
  // 7. 测试删除岗位
  if (createdPositionId) {
    try {
      console.log('\n--- 7. 删除岗位 ---');
      const response = await axios.delete(`${API_BASE_URL}/positions/${createdPositionId}`, { headers });
      
      console.log('✅ 删除岗位成功');
      console.log('响应状态:', response.status);
      
    } catch (error) {
      console.error('❌ 删除岗位失败:');
      console.error('状态码:', error.response?.status);
      console.error('错误信息:', error.response?.data || error.message);
    }
  }
  
  console.log('\n=== 岗位管理API测试完成 ===');
}

// 运行测试
testPositionsAPI().catch(console.error);
