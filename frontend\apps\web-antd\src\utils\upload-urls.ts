/**
 * 获取上传相关的URLs
 * @param dataType 数据类型
 * @param fileFormat 文件格式
 * @returns 上传相关的URLs对象
 */
export const getUploadUrls = (dataType: string, fileFormat: string) => {
  return {
    initUpload: `/file-manage/folder/structure/${dataType}/${fileFormat}`,
    uploadChunk: `/file-manage/file/chunk/${dataType}`,
    uploadProgress: `/file-manage/folder/progress`
  };
};

/**
 * 上传URLs类型定义
 */
export interface UploadUrls {
  initUpload: string;
  uploadChunk: string;
  uploadProgress: string;
}
