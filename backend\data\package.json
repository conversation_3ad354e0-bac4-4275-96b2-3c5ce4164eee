{"name": "system-manage-database-tools", "version": "1.0.0", "description": "系统管理平台数据库初始化工具", "main": "init-database.js", "scripts": {"init": "node init-database.js", "test-connection": "node test-db-connection.js", "test-init": "node test-init.js", "install-deps": "cd .. && npm install mysql2"}, "dependencies": {"mysql2": "^3.6.0"}, "keywords": ["database", "mysql", "initialization", "system-management"], "author": "System Management Team", "license": "MIT", "engines": {"node": ">=14.0.0"}}