import * as Cesium from 'cesium';

import { executeJob } from './GPExecute';

export default class GPAnalysis {
  constructor(options) {
    this.viewer = options.viewer;
    this.gp = options.gp;
  }
  async execute(options) {
    this.remove();
    const viewer = this.viewer;
    options = {
      layerInfos: [],
      featureInfos: [],
      numbers: [],
      strings: [],
      ...options,
    };
    const urls = options.layerInfos.map((layerInfo) => {
      return (
        `${layerInfo.data.url.trim('/')}/`.replaceAll(
          /\/mapserver\//gi,
          '/FeatureServer/',
        ) + layerInfo.data.layers
      );
    });

    const params = {
      layerInfos: JSON.stringify(
        urls.map((url) => {
          url;
        }),
      ),
      featureInfos: JSON.stringify(options.featureInfos),
      numbers: JSON.stringify(options.numbers),
      strings: JSON.stringify(options.strings),
      f: 'json',
    };
    const res = await executeJob(this.gp, params);
    console.log(res);
    const result = res.data.results[0].value;
    // delete result.crs
    // delete result.exceededTransferLimit
    this.dataSource = await Cesium.GeoJsonDataSource.load(result, {
      clampToGround: true,
    });
    viewer.value.dataSources.add(this.dataSource);
    viewer.value.flyTo(this.dataSource);
  }
  remove() {
    const viewer = this.viewer;
    if (this.dataSource) {
      viewer.value.dataSources.remove(this.dataSource);
    }
  }
}
