/**
 * 并集
 * @param {Array} array1 
 * @param {Array} array2 
 * @param {String} key 比较字段
 * @returns {Array}
 * @example 
	let a = [1, 2, 3, 4, 5]
	let b = [1, 2, 4, 5, 6]
	Array.union(a, b) // [1,2,3,4,5,6]
	 
	let a1 = [
		{ id: 1, name: '张三', age: 20 },
		{ id: 2, name: '李四', age: 21 },
		{ id: 3, name: '小二', age: 23 }
	]
	let b1 = [
		{ id: 2, name: '李四', age: 21 },
		{ id: 4, name: '小明', age: 24 },
		{ id: 5, name: '小红', age: 25 }
	]
	 
	// 通过 id 获取并集
	Array.union(a1, b1, 'id')
	// [
	//   {id: 1, name: "张三", age: 20}
	//   {id: 2, name: "李四", age: 21}
	//   {id: 3, name: "小二", age: 23}
	//   {id: 4, name: "小明", age: 24}
	//   {id: 5, name: "小红", age: 25}
	// ]
 */
Array.union = function (array1, array2, key) {
  return array1.concat(
    array2.filter((i) =>
      key ? !array1.map((i) => i[key]).includes(i[key]) : !array1.includes(key),
    ),
  );
};

/**
 * 交集
 * @param {Array} array1 
 * @param {Array} array2 
 * @param {String} key 比较字段
 * @returns {Array}
 * @example
	 // 实例1：
	  
	 let a = [1, 2, 3, 4, 5]
	 let b = [1, 2, 4, 5, 6]
	 Array.intersection(a, b) // [1,2,4,5]
	  
	 // 实例2：
	 let a1 = [
		 { id: 1, name: '张三', age: 20 },
		 { id: 2, name: '李四', age: 21 },
		 { id: 3, name: '小二', age: 23 }
	 ]
	 let b1 = [
		 { id: 2, name: '李四', age: 21 },
		 { id: 4, name: '小明', age: 24 },
		 { id: 5, name: '小红', age: 25 }
	 ]
	 Array.intersection(a1, b1, 'id') //[ { id: 2, name: '李四', age: 21 }]
 */
Array.intersection = function (array1, array2, key) {
  return array1.filter((t) =>
    key ? array2.map((i) => i[key]).includes(t[key]) : array2.includes(t),
  );
};

/**
 * 差集
 * @param {Array} array1
 * @param {Array} array2 
 * @param {String} key 比较字段
 * @returns {Array}
 * @example
	 // 实例1：
	  
	 let a = [1, 2, 3, 4, 5]
	 let b = [1, 2, 4, 5, 6]
	  
	 Array.except(a, b) // [3,6]
	  
	  
	 // 实例2：
	 let a1 = [
		 { id: 1, name: '张三', age: 20 },
		 { id: 2, name: '李四', age: 21 },
		 { id: 3, name: '小二', age: 23 }
	 ]
	 let b1 = [
		 { id: 2, name: '李四', age: 21 },
		 { id: 4, name: '小明', age: 24 },
		 { id: 5, name: '小红', age: 25 }
	 ]
	  
	  
	 Array.except(a1, b1, 'id')

	 // [
	 //   {id: 1, name: "张三", age: 20}
	 //   {id: 3, name: "小二", age: 23}
	 //   {id: 4, name: "小明", age: 24}
	 //   {id: 5, name: "小红", age: 25}
	 // ]
 */
Array.except = function (a, b, k) {
  return [...a, ...b].filter(
    (i) =>
      ![a, b].every((t) =>
        k ? t.map((i) => i[k]).includes(i[k]) : t.includes(i),
      ),
  );
};
/**
 * @description: 一维数组转二维数组 (分块)
 * @param {Array} arr:数组
 * @param {Number} chunkSize: 平分基数（chunkSize 个为一组进行分组（归档））
 * @returns {Array}
 * @example
 // 实例1：
   Array.chunk([1,2,3,4,5,6,7,8,9,10],2) // [[1,2],[3,4],[5,6],[7,8],[9,10]]
  
 // 实例2：
   Array.chunk([1,2,3,4,5,6,7,8,9,10],3) // [[1,2,3],[4,5,6],[7,8,9],[10]]
 */
Array.chunk = function (arr, chunkSize) {
  return [...Array(Math.ceil(arr.length / chunkSize)).keys()].reduce(
    (p, _, i) => (p.push(arr.slice(i * chunkSize, (i + 1) * chunkSize)), p),
    [],
  );
};

/**
 * @description: 生成 起止数字间（包含起止数字）的升序数组
 * @param {Number} min : 最小值
 * @param {Number} max ：最大值
 * @example
	range(0,10) // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
	range(1,9)  // [1, 2, 3, 4, 5, 6, 7, 8, 9]
 */
Array.range = function (min, max) {
  return Array.from({ length: max - min + 1 }, (_, i) => i + min);
};

/**
 * 打乱数组
 *@example console.log(Array.shuffle([1, 2, 3, 4]));
// Result: [ 1, 4, 3, 2 ]
 */
Array.shuffle = function () {
  return this.sort(() => 0.5 - Math.random());
};
