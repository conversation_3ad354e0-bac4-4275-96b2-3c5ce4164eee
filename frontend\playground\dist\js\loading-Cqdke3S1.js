import{a4 as m,O as t,V as p,af as s,am as o,ap as r,F as f,as as v,ao as g,al as _,aZ as b,a3 as x,aX as y}from"../jse/index-index-BMh_AyeW.js";import{aI as k}from"./bootstrap-DShsrVit.js";const T={class:"dot relative inline-block size-9 text-3xl"},V={key:0,class:"mt-4 text-xs"},h=m({name:"VbenLoading",__name:"loading",props:{class:{},minLoadingTime:{default:50},spinning:{type:<PERSON>olean},text:{default:""}},setup(c){const n=c,e=t(!1),i=t(!0),l=t();p(()=>n.spinning,a=>{if(!a){e.value=!1,clearTimeout(l.value);return}l.value=setTimeout(()=>{e.value=!0,e.value&&(i.value=!0)},n.minLoadingTime)},{immediate:!0});function u(){e.value||(i.value=!1)}return(a,z)=>(s(),o("div",{class:b(x(y)("z-100 dark:bg-overlay bg-overlay-content pointer-events-none absolute left-0 top-0 flex size-full flex-col items-center justify-center transition-all duration-500",{"invisible opacity-0":!e.value},n.class)),onTransitionend:u},[r("span",T,[(s(),o(f,null,v(4,d=>r("i",{key:d,class:"bg-primary absolute block size-4 origin-[50%_50%] scale-75 rounded-full opacity-30"})),64))]),a.text?(s(),o("div",V,g(a.text),1)):_("",!0)],34))}}),C=k(h,[["__scopeId","data-v-9dfd3a94"]]);export{C as V};
