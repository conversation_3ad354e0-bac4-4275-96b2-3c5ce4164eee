import { defineStore } from 'pinia';
import { ref } from 'vue';

interface UploadTask {
  id: string;
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
}

export const useUploadStore = defineStore('upload', () => {
  const tasks = ref<UploadTask[]>([]);

  // 添加上传任务
  const addTask = (fileName: string) => {
    const task: UploadTask = {
      id: Date.now().toString(),
      fileName,
      progress: 0,
      status: 'uploading'
    };
    tasks.value.push(task);
    return task.id;
  };

  // 更新任务进度
  const updateProgress = (taskId: string, progress: number) => {
    const task = tasks.value.find(t => t.id === taskId);
    if (task) {
      task.progress = Math.min(Math.round(progress), 100);
      if (task.progress === 100) {
        task.status = 'completed';
      }
    }
  };

  // 设置任务状态
  const setTaskStatus = (taskId: string, status: UploadTask['status']) => {
    const task = tasks.value.find(t => t.id === taskId);
    if (task) {
      task.status = status;
    }
  };

  // 清除已完成的任务
  const clearCompletedTasks = () => {
    tasks.value = tasks.value.filter(task => task.status === 'uploading');
  };

  // 移除指定任务
  const removeTask = (taskId: string) => {
    tasks.value = tasks.value.filter(task => task.id !== taskId);
  };

  return {
    tasks,
    addTask,
    updateProgress,
    setTaskStatus,
    clearCompletedTasks,
    removeTask
  };
});
