export const basicLayers = [
  {
    fid: 0,
    id: 100,
    title: '底图',
    key: 'basic',
    type: '',
  },
  // 底图配置
  {
    fid: 100,
    id: 1002,
    title: '矢量注记',
    key: 'tianditu2',
    data: {
      url: 'http://t{s}.tianditu.gov.cn/cva_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cva&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=76620c5b63143f80f875dffb556b57ef',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      layer: 'tdtImgLayer',
      style: 'default',
      format: 'image/jpeg',
      tileMatrixSetID: 'GoogleMapsCompatible', // 使用谷歌的瓦片切片方式
      show: true,
      maximumLevel: 20,
    },
    type: 'wmts',
  },
  {
    fid: 100,
    id: 1001,
    title: '天地图矢量',
    key: 'tianditu1',
    data: {
      url: 'http://t{s}.tianditu.gov.cn/vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=76620c5b63143f80f875dffb556b57ef',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      layer: 'tdtImgLayer',
      style: 'default',
      format: 'image/jpeg',
      tileMatrixSetID: 'GoogleMapsCompatible', // 使用谷歌的瓦片切片方式
      show: true,
      maximumLevel: 20,
    },
    type: 'wmts',
  },

  {
    fid: 100,
    id: 1003,
    title: '天地图影像',
    key: 'tianditu3',
    data: {
      url: 'http://t{s}.tianditu.com/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=76620c5b63143f80f875dffb556b57ef',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      layer: 'tdtImgLayer',
      style: 'default',
      format: 'image/jpeg',
      tileMatrixSetID: 'GoogleMapsCompatible', // 使用谷歌的瓦片切片方式
      show: true,
      maximumLevel: 19,
    },
    type: 'wmts',
  },
  {
    fid: 100,
    id: 1005,
    title: '天地图地形',
    key: 'tianditu5',
    data: {
      url: 'http://t{s}.tianditu.gov.cn/ter_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=ter&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=76620c5b63143f80f875dffb556b57ef',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      layer: 'tdtImgLayer',
      style: 'default',
      format: 'image/jpeg',
      tileMatrixSetID: 'GoogleMapsCompatible', // 使用谷歌的瓦片切片方式
      show: true,
    },
    type: 'wmts',
  },
  {
    fid: 100,
    id: 1006,
    title: 'Bing影像',
    key: 'Bingimage',
    data: {},
    type: 'bing',
  },
  {
    fid: 100,
    id: 1007,
    title: 'Cesium地形',
    key: 'Cesiumterrain',
    data: {},
    type: 'terrain',
  },
];
