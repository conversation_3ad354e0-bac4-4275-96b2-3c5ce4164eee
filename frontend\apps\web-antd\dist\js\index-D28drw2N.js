import{C as d,S as f,B as e,d as a,h as w}from"./bootstrap-5OPUVRWy.js";import{_ as g}from"./page.vue_vue_type_script_setup_true_lang-D2AkmoUI.js";import{d as y,j as $,b as v,q as t,s as l,k as o,v as i}from"../jse/index-index-DyHD_jbN.js";const V=y({__name:"index",setup(x){function m(){a.info("How many roads must a man walk down")}function p(){a.error({content:"Once upon a time you dressed so fine",duration:2500})}function k(){a.warning("How many roads must a man walk down")}function C(){a.success("Cause you walked hand in hand With another man in my place")}function s(u){w[u]({duration:2500,message:"说点啥呢",type:u})}return(u,n)=>(v(),$(o(g),{description:"支持多语言，主题功能集成切换等",title:"Ant Design Vue组件使用演示"},{default:t(()=>[l(o(d),{class:"mb-5",title:"按钮"},{default:t(()=>[l(o(f),null,{default:t(()=>[l(o(e),null,{default:t(()=>n[4]||(n[4]=[i("Default")])),_:1}),l(o(e),{type:"primary"},{default:t(()=>n[5]||(n[5]=[i(" Primary ")])),_:1}),l(o(e),null,{default:t(()=>n[6]||(n[6]=[i(" Info ")])),_:1}),l(o(e),{danger:""},{default:t(()=>n[7]||(n[7]=[i(" Error ")])),_:1})]),_:1})]),_:1}),l(o(d),{class:"mb-5",title:"Message"},{default:t(()=>[l(o(f),null,{default:t(()=>[l(o(e),{onClick:m},{default:t(()=>n[8]||(n[8]=[i(" 信息 ")])),_:1}),l(o(e),{danger:"",onClick:p},{default:t(()=>n[9]||(n[9]=[i(" 错误 ")])),_:1}),l(o(e),{onClick:k},{default:t(()=>n[10]||(n[10]=[i(" 警告 ")])),_:1}),l(o(e),{onClick:C},{default:t(()=>n[11]||(n[11]=[i(" 成功 ")])),_:1})]),_:1})]),_:1}),l(o(d),{class:"mb-5",title:"Notification"},{default:t(()=>[l(o(f),null,{default:t(()=>[l(o(e),{onClick:n[0]||(n[0]=r=>s("info"))},{default:t(()=>n[12]||(n[12]=[i(" 信息 ")])),_:1}),l(o(e),{danger:"",onClick:n[1]||(n[1]=r=>s("error"))},{default:t(()=>n[13]||(n[13]=[i(" 错误 ")])),_:1}),l(o(e),{onClick:n[2]||(n[2]=r=>s("warning"))},{default:t(()=>n[14]||(n[14]=[i(" 警告 ")])),_:1}),l(o(e),{onClick:n[3]||(n[3]=r=>s("success"))},{default:t(()=>n[15]||(n[15]=[i(" 成功 ")])),_:1})]),_:1})]),_:1})]),_:1}))}});export{V as default};
