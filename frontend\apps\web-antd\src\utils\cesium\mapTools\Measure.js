import * as Cesium from 'cesium';
import core from '../core.js';
import { GISMap } from '../index';
import Drawer from './Drawer';
import eb, { eventbus } from '../../../ztu/eventbus.js';

export default class Measure {
  constructor(viewer) {
    this.viewer = viewer || GISMap.viewer;
    this.d = new Drawer(this.viewer);
    this.handler = null;
    this.tipEntity = null;
    this.tipPosition = null;
  }
  getLengthText(positions) {
    let wgs84Arr = core.transformCartesianArrayToWGS84Array(positions);
    let distance = core.getPositionDistance(wgs84Arr);
    return core.getLengthText(distance);
  }
  measureEnd() {
    if (this.handler && !this.handler.isDestroyed()) {
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      this.handler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK,
      );
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      this.handler.destroy();
    }
    this.d.remove(this.tipEntity);
    eb.emit('drawEnd', { from: 'Measure' });
  }
  measurePolyLine(options = {}) {
    this.measureEnd();
    var positions = [];
    var positionMinLength = 2;
    var labelEntity = null; // 标签实体
    var lineEntity = null;
    var lengthText = '';
    var entities = [];
    eb.emit('draw', { from: 'Measure' });
    this.tipEntity = this.d.drawText(
      new Cesium.CallbackProperty(() => {
        return this.tipPosition;
      }, false),
      {
        text: new Cesium.CallbackProperty(function () {
          return '点击开始,右击结束';
        }, false),
        font: '12px sans-serif',
        fillColor: Cesium.Color.WHITE,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        pixelOffset: new Cesium.Cartesian2(5, 5),
        showBackground: true, //指定标签后面背景的可见性
        backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
        backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
    );
    // 注册鼠标左击事件
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.position);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(this.viewer, movement.position);
      // 存储第一个点
      if (!cartesian) {
        return false;
      }

      if (positions.length == 0) {
        positions.push(cartesian.clone());
        positions.push(cartesian);
        //添加一个蓝点
        entities.push(this.d.drawPoint(cartesian));
        lineEntity = this.d.drawLine(
          new Cesium.CallbackProperty(function () {
            //console.log(positions)
            return positions;
          }, false),
          new Cesium.CallbackProperty(function () {
            return core.getPolylineCenter(positions);
          }, false),
          options,
        );
        labelEntity = this.d.drawText(
          new Cesium.CallbackProperty(function () {
            return positions[positions.length - 1];
          }, false),
          {
            text: new Cesium.CallbackProperty(function () {
              return lengthText;
            }, false),
            font: '14px sans-serif',
            fillColor: Cesium.Color.GOLD,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            outlineWidth: 2,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            pixelOffset: new Cesium.Cartesian2(0, -10),
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
          },
        );
        entities.push(lineEntity);
        entities.push(labelEntity);
      } else {
        positions.push(cartesian);
        // 存储第二个点
        entities.push(this.d.drawPoint(cartesian));
        //handler.destroy();
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // 注册鼠标移动事件
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.endPosition);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(
        this.viewer,
        movement.endPosition,
      );
      if (!cartesian) {
        return false;
      }
      if (positions.length > 0) {
        positions.pop();
        positions.push(cartesian);
        // 计算距离
        lengthText = this.getLengthText(positions);
      }
      this.tipPosition = cartesian;
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    //注册鼠标右击事件
    this.handler.setInputAction((movement) => {
      this.measureEnd();
      if (positions.length) {
        positions.pop();
        if (positionMinLength > positions.length) {
          this.d.remove(entities);
        } else lengthText = this.getLengthText(positions);
      }
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }
  getAngleText(points) {
    let wgs84Arr = core.transformCartesianArrayToWGS84Array(points);
    let angle = core.getPositionsAngle(wgs84Arr).toFixed(2);
    wgs84Arr.forEach((wgs84) => {
      wgs84.alt = 0;
    });
    let angle2 = core.getPositionsAngle(wgs84Arr).toFixed(2);
    if (angle == angle2) return Cesium.Math.toDegrees(angle).toFixed(2) + '度';
    return (
      Cesium.Math.toDegrees(angle).toFixed(2) +
      '度(3D)/' +
      Cesium.Math.toDegrees(angle2).toFixed(2) +
      '度(2D)'
    ); //core.getAngleText(angle);
  }
  //计算多边形面积
  getArea(points) {
    let wgs84Arr = core.transformCartesianArrayToWGS84Array(points);
    let area = core.getPositionsArea(wgs84Arr);
    return core.getAreaText(area);
  }
  measurePolygon(options = {}) {
    this.measureEnd();
    var positions = [];
    var _positions = [];
    var positionMinLength = 3;
    var labelEntity = null; // 标签实体
    var areaText = '';
    var polyEntity = null;
    var labelPosition = null;
    var entities = [];
    eb.emit('draw', { from: 'Measure' });
    this.tipEntity = this.d.drawText(
      new Cesium.CallbackProperty(() => {
        return this.tipPosition;
      }, false),
      {
        text: new Cesium.CallbackProperty(function () {
          return '点击开始,右击结束';
        }, false),
        font: '12px sans-serif',
        fillColor: Cesium.Color.WHITE,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        pixelOffset: new Cesium.Cartesian2(5, 5),
        showBackground: true, //指定标签后面背景的可见性
        backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
        backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
    );
    // 注册鼠标左击事件
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.position);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(this.viewer, movement.position);
      // 存储第一个点
      if (!cartesian) {
        return false;
      }

      if (positions.length == 0) {
        positions.push(cartesian.clone());
        positions.push(cartesian);
        _positions.push(cartesian.clone());
        _positions.push(cartesian);
        //添加一个蓝点
        entities.push(this.d.drawPoint(cartesian));
        labelPosition = cartesian;
        polyEntity = this.d.drawPolygon(
          new Cesium.CallbackProperty(() => {
            return new Cesium.PolygonHierarchy(positions);
          }, false),
          new Cesium.CallbackProperty(function () {
            return core.getPolylineCenter(positions);
          }, false),
          options,
        );
        labelEntity = this.d.drawText(
          new Cesium.CallbackProperty(function () {
            //console.log(positions[positions.length-1]);
            return core.getPolygonCenter(_positions); //_positions[_positions.length-1];//this.getCenterOfGravityPoint(positions);
          }, false),
          {
            text: new Cesium.CallbackProperty(function () {
              return areaText;
            }, false),
            show: new Cesium.CallbackProperty(function () {
              return positions.length > 2;
            }, false),
            font: '14px sans-serif',
            fillColor: Cesium.Color.GOLD,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            outlineWidth: 2,
            verticalOrigin: Cesium.VerticalOrigin.CENTER,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            pixelOffset: new Cesium.Cartesian2(0, 0),
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
          },
        );
        entities.push(polyEntity);
        entities.push(labelEntity);
      } else {
        positions.push(cartesian);
        _positions.push(cartesian.clone());
        // 存储第二个点
        entities.push(this.d.drawPoint(cartesian));
        //handler.destroy();
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // 注册鼠标移动事件
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.endPosition);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(
        this.viewer,
        movement.endPosition,
      );
      if (!cartesian) {
        return false;
      }
      if (positions.length > 0) {
        positions.pop();
        positions.push(cartesian.clone());
        _positions.pop();
        _positions.push(cartesian.clone());
        // 计算面积
        areaText = this.getArea(_positions);
      }
      labelPosition = this.tipPosition = cartesian;
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    //注册鼠标右击事件
    this.handler.setInputAction((movement) => {
      this.measureEnd();
      if (positions.length) {
        positions.pop();
        _positions.pop();
        if (positionMinLength > positions.length) {
          this.d.remove(entities);
        } else areaText = this.getArea(_positions);
      }
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }

  measureHeight(options = {}) {
    this.measureEnd();
    var positions = [];
    var lengthText_1 = null; // 标签实体
    var lengthText_2 = null; // 标签实体
    var lengthText_3 = null; // 标签实体
    var entities = [];
    var flag = true; //起点比始点高时为true
    eb.emit('draw', { from: 'Measure' });
    this.tipEntity = this.d.drawText(
      new Cesium.CallbackProperty(() => {
        return this.tipPosition;
      }, false),
      {
        text: new Cesium.CallbackProperty(function () {
          if (positions.length == 0) return '点击选择起点,右击结束';
          else return '点击选择终点,右击结束';
        }, false),
        font: '12px sans-serif',
        fillColor: Cesium.Color.WHITE,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        pixelOffset: new Cesium.Cartesian2(5, 5),
        showBackground: true, //指定标签后面背景的可见性
        backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
        backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
    );
    // 注册鼠标左击事件
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.position);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(this.viewer, movement.position);
      // 存储第一个点
      if (!cartesian) {
        return false;
      }

      if (positions.length == 0) {
        positions.push(cartesian.clone());
        positions.push(cartesian.clone()); //end
        positions.push(cartesian); //第3个点
        //添加一个蓝点
        entities.push(this.d.drawPoint(cartesian));
        entities.push(
          this.d.drawLine(
            new Cesium.CallbackProperty(() => {
              if (positions.length == 3) return [positions[0], positions[1]];
              return [positions[0], positions[0]]; //直线
            }, false),
            new Cesium.CallbackProperty(function () {
              return core.getPolylineCenter(positions);
            }, false),
            {
              arcType: Cesium.ArcType.NONE,
              material: Cesium.Color.CHARTREUSE,
              depthFailMaterial: new Cesium.PolylineDashMaterialProperty({
                color: Cesium.Color.RED,
              }),
              ...options,
            },
          ),
        );
        entities.push(
          this.d.drawLine(
            new Cesium.CallbackProperty(() => {
              if (positions.length == 3)
                return [positions[flag ? 0 : 1], positions[2]];
              return [positions[0], positions[0]]; //水平
            }, false),
            new Cesium.CallbackProperty(function () {
              return core.getPolylineCenter(positions);
            }, false),
            {
              arcType: Cesium.ArcType.NONE,
              material: Cesium.Color.GREEN,
              depthFailMaterial: new Cesium.PolylineDashMaterialProperty({
                color: Cesium.Color.RED,
              }),
              ...options,
            },
          ),
        );
        entities.push(
          this.d.drawLine(
            new Cesium.CallbackProperty(() => {
              if (positions.length == 3)
                return [positions[flag ? 1 : 0], positions[2]];
              return [positions[0], positions[0]]; //垂直
            }, false),
            new Cesium.CallbackProperty(function () {
              return core.getPolylineCenter(positions);
            }, false),
            {
              arcType: Cesium.ArcType.NONE,
              material: Cesium.Color.BLUE,
              depthFailMaterial: new Cesium.PolylineDashMaterialProperty({
                color: Cesium.Color.RED,
              }),
              ...options,
            },
          ),
        );
        entities.push(
          this.d.drawText(
            new Cesium.CallbackProperty(() => {
              return core.getPolylineCenter([positions[0], positions[1]]); //this.getCenterOfGravityPoint(positions);
            }, false),
            {
              text: new Cesium.CallbackProperty(function () {
                return lengthText_1;
              }, false),
              font: '14px sans-serif',
              fillColor: Cesium.Color.GOLD,
              style: Cesium.LabelStyle.FILL_AND_OUTLINE,
              outlineWidth: 2,
              verticalOrigin: Cesium.VerticalOrigin.CENTER,
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
              pixelOffset: new Cesium.Cartesian2(0, 0),
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
            },
          ),
        );
        entities.push(
          this.d.drawText(
            new Cesium.CallbackProperty(() => {
              return core.getPolylineCenter([
                positions[flag ? 0 : 1],
                positions[2],
              ]);
            }, false),
            {
              text: new Cesium.CallbackProperty(function () {
                return lengthText_2;
              }, false),
              font: '14px sans-serif',
              fillColor: Cesium.Color.GOLD,
              style: Cesium.LabelStyle.FILL_AND_OUTLINE,
              outlineWidth: 2,
              verticalOrigin: Cesium.VerticalOrigin.CENTER,
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
              pixelOffset: new Cesium.Cartesian2(0, 0),
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
            },
          ),
        );
        entities.push(
          this.d.drawText(
            new Cesium.CallbackProperty(() => {
              return core.getPolylineCenter([
                positions[flag ? 1 : 0],
                positions[2],
              ]); //this.getCenterOfGravityPoint(positions);
            }, false),
            {
              text: new Cesium.CallbackProperty(function () {
                return lengthText_3;
              }, false),
              font: '14px sans-serif',
              fillColor: Cesium.Color.GOLD,
              style: Cesium.LabelStyle.FILL_AND_OUTLINE,
              outlineWidth: 2,
              verticalOrigin: Cesium.VerticalOrigin.CENTER,
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
              pixelOffset: new Cesium.Cartesian2(0, 0),
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
            },
          ),
        );
      } else {
        // 存储第二个点
        this.measureEnd();
        entities.push(this.d.drawPoint(positions[1]));
        //entities.push(this.d.drawPoint(positions[2]));
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // 注册鼠标移动事件
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.endPosition);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(
        this.viewer,
        movement.endPosition,
      );
      if (!cartesian) {
        return false;
      }
      if (positions.length > 0) {
        positions.pop();
        positions.pop();
        positions.push(cartesian);

        var start = Cesium.Cartographic.fromCartesian(positions[0]);
        var end = Cesium.Cartographic.fromCartesian(cartesian);

        flag = start.height > end.height;
        var position = Cesium.Cartesian3.fromDegrees(
          Cesium.Math.toDegrees((flag ? end : start).longitude),
          Cesium.Math.toDegrees((flag ? end : start).latitude),
          (flag ? start : end).height,
        );

        positions.push(position);

        // 计算距离
        lengthText_1 =
          '直线距离：' + this.getLengthText([positions[0], positions[1]]);
        // 计算距离
        lengthText_2 =
          '水平距离：' +
          this.getLengthText([positions[flag ? 0 : 1], positions[2]]);
        // 计算距离
        lengthText_3 =
          '垂直距离：' +
          this.getLengthText([positions[flag ? 1 : 0], positions[2]]);
      }
      this.tipPosition = cartesian;
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    //注册鼠标右击事件
    this.handler.setInputAction((movement) => {
      this.measureEnd();
      if (positions.length) {
        this.d.remove(entities);
      }
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }
  /**
   * @description 直线距离
   * @param {*} options
   */
  measureLDistance(options = {}) {
    this.measureEnd();
    var positions = [];
    var lengthText_1 = null; // 标签实体
    var entities = [];
    eb.emit('draw', { from: 'Measure' });
    this.tipEntity = this.d.drawText(
      new Cesium.CallbackProperty(() => {
        return this.tipPosition;
      }, false),
      {
        text: new Cesium.CallbackProperty(function () {
          if (positions.length == 0) return '点击选择起点,右击结束';
          else return '点击选择终点,右击结束';
        }, false),
        font: '12px sans-serif',
        fillColor: Cesium.Color.WHITE,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        pixelOffset: new Cesium.Cartesian2(5, 5),
        showBackground: true, //指定标签后面背景的可见性
        backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
        backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
    );
    // 注册鼠标左击事件
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.position);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(this.viewer, movement.position);
      // 存储第一个点
      if (!cartesian) {
        return false;
      }

      if (positions.length == 0) {
        positions.push(cartesian.clone());
        positions.push(cartesian); //第3个点
        //添加一个蓝点
        entities.push(this.d.drawPoint(cartesian));
        entities.push(
          this.d.drawLine(
            new Cesium.CallbackProperty(() => {
              return [positions[0], positions[1]]; //直线
            }, false),
            new Cesium.CallbackProperty(function () {
              return core.getPolylineCenter(positions);
            }, false),
            {
              arcType: Cesium.ArcType.NONE,
              material: Cesium.Color.YELLOW,
              depthFailMaterial: new Cesium.PolylineDashMaterialProperty({
                color: Cesium.Color.RED,
              }),
              ...options,
            },
          ),
        );
        entities.push(
          this.d.drawText(
            new Cesium.CallbackProperty(() => {
              return core.getPolylineCenter([positions[0], positions[1]]); //this.getCenterOfGravityPoint(positions);
            }, false),
            {
              text: new Cesium.CallbackProperty(function () {
                return lengthText_1;
              }, false),
              font: '14px sans-serif',
              fillColor: Cesium.Color.GOLD,
              style: Cesium.LabelStyle.FILL_AND_OUTLINE,
              outlineWidth: 2,
              verticalOrigin: Cesium.VerticalOrigin.CENTER,
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
              pixelOffset: new Cesium.Cartesian2(0, 0),
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
            },
          ),
        );
      } else {
        // 存储第二个点
        this.measureEnd();
        entities.push(this.d.drawPoint(positions[1]));
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // 注册鼠标移动事件
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.endPosition);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(
        this.viewer,
        movement.endPosition,
      );
      if (!cartesian) {
        return false;
      }
      if (positions.length > 0) {
        positions.pop();
        positions.push(cartesian);

        // 计算距离
        lengthText_1 =
          '直线距离：' + this.getLengthText([positions[0], positions[1]]);
      }
      this.tipPosition = cartesian;
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    //注册鼠标右击事件
    this.handler.setInputAction((movement) => {
      this.measureEnd();
      if (positions.length) {
        this.d.remove(entities);
      }
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }
  /**
   * @description 垂直距离
   * @param {*} options
   */
  measureVDistance(options = {}) {
    this.measureEnd();
    var positions = [];
    var lengthText_3 = null; // 标签实体
    var entities = [];
    var flag = true; //起点比始点高时为true
    eb.emit('draw', { from: 'Measure' });
    this.tipEntity = this.d.drawText(
      new Cesium.CallbackProperty(() => {
        return this.tipPosition;
      }, false),
      {
        text: new Cesium.CallbackProperty(function () {
          if (positions.length == 0) return '点击选择起点,右击结束';
          else return '点击选择终点,右击结束';
        }, false),
        font: '12px sans-serif',
        fillColor: Cesium.Color.WHITE,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        pixelOffset: new Cesium.Cartesian2(5, 5),
        showBackground: true, //指定标签后面背景的可见性
        backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
        backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
    );
    // 注册鼠标左击事件
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.position);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(this.viewer, movement.position);
      // 存储第一个点
      if (!cartesian) {
        return false;
      }

      if (positions.length == 0) {
        positions.push(cartesian.clone());
        positions.push(cartesian.clone()); //end
        positions.push(cartesian); //第3个点
        //添加一个蓝点
        entities.push(this.d.drawPoint(cartesian));

        entities.push(
          this.d.drawLine(
            new Cesium.CallbackProperty(() => {
              if (positions.length == 3)
                return [positions[flag ? 0 : 1], positions[2]];
              return [positions[0], positions[0]]; //水平
            }, false),
            new Cesium.CallbackProperty(function () {
              return core.getPolylineCenter(positions);
            }, false),
            {
              arcType: Cesium.ArcType.NONE,
              material: new Cesium.PolylineDashMaterialProperty({
                color: Cesium.Color.GRAY,
                gapColor: Cesium.Color.TRANSPARENT,
                dashLength: 20,
              }),
              ...options,
            },
          ),
        );
        entities.push(
          this.d.drawLine(
            new Cesium.CallbackProperty(() => {
              if (positions.length == 3)
                return [positions[flag ? 1 : 0], positions[2]];
              return [positions[0], positions[0]]; //垂直
            }, false),
            new Cesium.CallbackProperty(function () {
              return core.getPolylineCenter(positions);
            }, false),
            {
              arcType: Cesium.ArcType.NONE,
              material: Cesium.Color.YELLOW,
              ...options,
            },
          ),
        );

        entities.push(
          this.d.drawText(
            new Cesium.CallbackProperty(() => {
              return core.getPolylineCenter([
                positions[flag ? 1 : 0],
                positions[2],
              ]); //this.getCenterOfGravityPoint(positions);
            }, false),
            {
              text: new Cesium.CallbackProperty(function () {
                return lengthText_3;
              }, false),
              font: '14px sans-serif',
              fillColor: Cesium.Color.GOLD,
              style: Cesium.LabelStyle.FILL_AND_OUTLINE,
              outlineWidth: 2,
              verticalOrigin: Cesium.VerticalOrigin.CENTER,
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
              pixelOffset: new Cesium.Cartesian2(0, 0),
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
            },
          ),
        );
      } else {
        // 存储第二个点
        this.measureEnd();
        entities.push(this.d.drawPoint(positions[1]));
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // 注册鼠标移动事件
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.endPosition);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(
        this.viewer,
        movement.endPosition,
      );
      if (!cartesian) {
        return false;
      }
      if (positions.length > 0) {
        positions.pop();
        positions.pop();
        positions.push(cartesian);

        var start = Cesium.Cartographic.fromCartesian(positions[0]);
        var end = Cesium.Cartographic.fromCartesian(cartesian);

        flag = start.height > end.height;
        var position = Cesium.Cartesian3.fromDegrees(
          Cesium.Math.toDegrees((flag ? end : start).longitude),
          Cesium.Math.toDegrees((flag ? end : start).latitude),
          (flag ? start : end).height,
        );

        positions.push(position);

        // 计算距离
        lengthText_3 =
          '垂直距离：' +
          this.getLengthText([positions[flag ? 1 : 0], positions[2]]);
      }
      this.tipPosition = cartesian;
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    //注册鼠标右击事件
    this.handler.setInputAction((movement) => {
      this.measureEnd();
      if (positions.length) {
        this.d.remove(entities);
      }
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }
  /**
   * @description 水平距离
   * @param {*} options
   */
  measureHDistance(options = {}) {
    this.measureEnd();
    var positions = [];
    var lengthText_2 = null; // 标签实体
    var entities = [];
    var flag = true;
    eb.emit('draw', { from: 'Measure' });
    this.tipEntity = this.d.drawText(
      new Cesium.CallbackProperty(() => {
        return this.tipPosition;
      }, false),
      {
        text: new Cesium.CallbackProperty(function () {
          if (positions.length == 0) return '点击选择起点,右击结束';
          else return '点击选择终点,右击结束';
        }, false),
        font: '12px sans-serif',
        fillColor: Cesium.Color.WHITE,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        pixelOffset: new Cesium.Cartesian2(5, 5),
        showBackground: true, //指定标签后面背景的可见性
        backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
        backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
    );
    // 注册鼠标左击事件
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.position);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(this.viewer, movement.position);
      // 存储第一个点
      if (!cartesian) {
        return false;
      }

      if (positions.length == 0) {
        positions.push(cartesian.clone());
        positions.push(cartesian.clone()); //end
        positions.push(cartesian); //第3个点
        //添加一个蓝点
        entities.push(this.d.drawPoint(cartesian));

        entities.push(
          this.d.drawLine(
            new Cesium.CallbackProperty(() => {
              if (positions.length == 3)
                return [positions[flag ? 0 : 1], positions[2]];
              return [positions[0], positions[0]]; //水平
            }, false),
            new Cesium.CallbackProperty(function () {
              return core.getPolylineCenter(positions);
            }, false),
            {
              arcType: Cesium.ArcType.NONE,
              material: Cesium.Color.YELLOW,
              ...options,
            },
          ),
        );
        entities.push(
          this.d.drawLine(
            new Cesium.CallbackProperty(() => {
              if (positions.length == 3)
                return [positions[flag ? 1 : 0], positions[2]];
              return [positions[0], positions[0]]; //垂直
            }, false),
            new Cesium.CallbackProperty(function () {
              return core.getPolylineCenter(positions);
            }, false),
            {
              arcType: Cesium.ArcType.NONE,
              material: new Cesium.PolylineDashMaterialProperty({
                color: Cesium.Color.GRAY,
                gapColor: Cesium.Color.TRANSPARENT,
                dashLength: 20,
              }),
              ...options,
            },
          ),
        );

        entities.push(
          this.d.drawText(
            new Cesium.CallbackProperty(() => {
              return core.getPolylineCenter([
                positions[flag ? 0 : 1],
                positions[2],
              ]);
            }, false),
            {
              text: new Cesium.CallbackProperty(function () {
                return lengthText_2;
              }, false),
              font: '14px sans-serif',
              fillColor: Cesium.Color.GOLD,
              style: Cesium.LabelStyle.FILL_AND_OUTLINE,
              outlineWidth: 2,
              verticalOrigin: Cesium.VerticalOrigin.CENTER,
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
              pixelOffset: new Cesium.Cartesian2(0, 0),
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
            },
          ),
        );
      } else {
        // 存储第二个点
        this.measureEnd();
        entities.push(this.d.drawPoint(positions[1]));
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // 注册鼠标移动事件
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.endPosition);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(
        this.viewer,
        movement.endPosition,
      );
      if (!cartesian) {
        return false;
      }
      if (positions.length > 0) {
        positions.pop();
        positions.pop();
        positions.push(cartesian);

        var start = Cesium.Cartographic.fromCartesian(positions[0]);
        var end = Cesium.Cartographic.fromCartesian(cartesian);

        flag = start.height > end.height;
        var position = Cesium.Cartesian3.fromDegrees(
          Cesium.Math.toDegrees((flag ? end : start).longitude),
          Cesium.Math.toDegrees((flag ? end : start).latitude),
          (flag ? start : end).height,
        );

        positions.push(position);

        // 计算距离
        lengthText_2 =
          '水平距离：' +
          this.getLengthText([positions[flag ? 0 : 1], positions[2]]);
      }
      this.tipPosition = cartesian;
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    //注册鼠标右击事件
    this.handler.setInputAction((movement) => {
      this.measureEnd();
      if (positions.length) {
        this.d.remove(entities);
      }
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }
  measureAngle(options = {}) {
    this.measureEnd();
    var positions = [];
    var labelEntity = null; // 标签实体
    var lineEntity = null;
    var angleText = '';
    var entities = [];
    eb.emit('draw', { from: 'Measure' });
    this.tipEntity = this.d.drawText(
      new Cesium.CallbackProperty(() => {
        return this.tipPosition;
      }, false),
      {
        text: new Cesium.CallbackProperty(function () {
          return '点击开始,右击结束';
        }, false),
        font: '12px sans-serif',
        fillColor: Cesium.Color.WHITE,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        pixelOffset: new Cesium.Cartesian2(5, 5),
        showBackground: true, //指定标签后面背景的可见性
        backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
        backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
    );
    // 注册鼠标左击事件
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas);
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.position);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(this.viewer, movement.position);
      // 存储第一个点
      if (!cartesian) {
        return false;
      }

      if (positions.length == 0) {
        positions.push(cartesian.clone());
        positions.push(cartesian);
        //添加一个蓝点
        entities.push(this.d.drawPoint(cartesian));
        lineEntity = this.d.drawLine(
          new Cesium.CallbackProperty(function () {
            //console.log(positions)
            return positions;
          }, false),
          new Cesium.CallbackProperty(function () {
            return core.getPolylineCenter(positions);
          }, false),
          {
            arcType: Cesium.ArcType.NONE,
            width: 1,
            depthFailMaterial: new Cesium.PolylineDashMaterialProperty({
              color: Cesium.Color.CHARTREUSE,
              dashLength: 20,
            }),
            ...options,
          },
        );
        entities.push(lineEntity);
        labelEntity = this.d.drawText(
          new Cesium.CallbackProperty(function () {
            return positions.length > 1 ? positions[1] : positions[0];
          }, false),
          {
            text: new Cesium.CallbackProperty(function () {
              return angleText;
            }, false),
            font: '14px sans-serif',
            fillColor: Cesium.Color.GOLD,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            outlineWidth: 2,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            pixelOffset: new Cesium.Cartesian2(0, -10),
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
          },
        );
        entities.push(labelEntity);
      } else {
        positions.push(cartesian);
        // 存储第二个点
        entities.push(this.d.drawPoint(cartesian));
        if (positions.length > 3) {
          positions.pop();
          this.measureEnd();
          angleText = this.getAngleText(positions);
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    // 注册鼠标移动事件
    this.handler.setInputAction((movement) => {
      //let ray = this.viewer.camera.getPickRay(movement.endPosition);
      //let cartesian = this.viewer.scene.globe.pick(ray, this.viewer.scene);
      let cartesian = core.getCatesian3FromPX(
        this.viewer,
        movement.endPosition,
      );
      if (!cartesian) {
        return false;
      }
      if (positions.length > 0) {
        positions.pop();
        positions.push(cartesian);
        // 计算角度
        angleText = this.getAngleText(positions);
      }
      this.tipPosition = cartesian;
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    //注册鼠标右击事件
    this.handler.setInputAction((movement) => {
      this.measureEnd();
      if (positions.length) {
        //positions.pop();
        //angleText = this.getAngleText(positions);

        this.d.remove(entities);
      }
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }
  removeMeasure() {
    this.measureEnd();
    this.d.clear();
  }
}
