<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  width: {
    type: [Number, String],
    default: 450
  },
  height: {
    type: [Number, String],
    default: 280
  }
});

const isError = ref(false);

const handleError = () => {
  isError.value = true;
};

// 监听 src 变化，重置错误状态
watch(() => props.src, () => {
  isError.value = false;
});
</script>

<template>
  <div class="image-container" :style="{ width: typeof width === 'number' ? width + 'px' : width, height: typeof height === 'number' ? height + 'px' : height }">
    <template v-if="!isError">
      <a-image
        :src="src"
        :width="width"
        :height="height"
        @error="handleError"
      />
    </template>
    <template v-else>
      <div class="error-container">
        <div class="error-icon">
          <svg viewBox="64 64 896 896" focusable="false" data-icon="picture" width="1em" height="1em" fill="currentColor" aria-hidden="true">
            <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zM338 304c35.3 0 64 28.7 64 64s-28.7 64-64 64-64-28.7-64-64 28.7-64 64-64zm513.9 437.1a8.11 8.11 0 01-5.2 1.9H177.2c-4.4 0-8-3.6-8-8 0-1.9.7-3.7 1.9-5.2l170.3-202c2.8-3.4 7.9-3.8 11.3-1 .******* 1 1l99.4 118 158.1-187.5c2.8-3.4 7.9-3.8 11.3-1 .******* 1 1l229.6 271.6c2.6 3.3 2.2 8.4-1.2 11.2z"></path>
          </svg>
        </div>
        <div class="error-text">图片不存在</div>
      </div>
    </template>
  </div>
</template>

<style scoped>
.image-container {
  position: relative;
  border: 1px solid hsl(var(--border));
  border-radius: 4px;
  overflow: hidden;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f5f5f5;
  color: #999;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

.error-text {
  font-size: 14px;
  color: #999;
}
</style>
