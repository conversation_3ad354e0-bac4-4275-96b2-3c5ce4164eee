import{b as y,d as I,e as O,f as S,c as V,a as v,g as C,N as F,h as N,i as U,_ as D}from"./layout.vue_vue_type_script_setup_true_lang-D-GDfR6Z.js";import{A as G,_ as M,a as R}from"./authentication-PgL29TSA.js";import{h as q,g as z}from"./theme-toggle.vue_vue_type_script_setup_true_lang-DzmrFWHD.js";import{aI as o}from"./bootstrap-DShsrVit.js";import{O as a,af as t,am as s}from"../jse/index-index-BMh_AyeW.js";import"./avatar.vue_vue_type_script_setup_true_lang-CyCVfWwM.js";import"./bell-D0icksiV.js";import"./popover.vue_vue_type_script_setup_true_lang-CFXFI0jw.js";import"./use-modal-B0smF4x0.js";import"./x-B-ntYT_e.js";import"./loading-Cqdke3S1.js";import"./icon.vue_vue_type_script_setup_true_lang-BK5optdP.js";import"./use-tabs-C64_EnSy.js";import"./use-drawer-Qcdpj8Bl.js";import"./TabsList.vue_vue_type_script_setup_true_lang-DfMjVjnv.js";import"./sun-ChDfqwZ7.js";import"./rotate-cw-B0JNpqtv.js";const r=a(!1);function T(){function e(){r.value=!0}return{handleOpenPreference:e,openPreferences:r}}const n={};function c(e,i){return t(),s("div")}const w=o(n,[["render",c]]);export{G as AuthPageLayout,M as AuthenticationColorToggle,R as AuthenticationLayoutToggle,y as BasicLayout,I as Breadcrumb,O as CheckUpdates,S as GlobalSearch,V as IFrameRouterView,w as IFrameView,q as LanguageToggle,v as LockScreen,C as LockScreenModal,F as Notification,N as Preferences,U as PreferencesButton,z as ThemeToggle,D as UserDropdown,T as useOpenPreferences};
