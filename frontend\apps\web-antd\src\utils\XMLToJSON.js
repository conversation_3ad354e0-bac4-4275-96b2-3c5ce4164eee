function XMLToJSON() {
  const me = this; // stores the object instantce

  // gets the content of an xml file and returns it in
  me.fromFile = function (xml, rstr) {
    // Cretes a instantce of XMLHttpRequest object
    const xhttp = window.XMLHttpRequest
      ? new XMLHttpRequest()
      : new ActiveXObject('Microsoft.XMLHTTP');
    // sets and sends the request for calling "xml"
    xhttp.open('GET', xml, false);
    xhttp.send(null);

    // gets the JSON string
    const json_str = jsontoStr(setJsonObj(xhttp.responseXML));

    // sets and returns the JSON object, if "rstr" undefined (not passed), else, returns JSON string
    return rstr === undefined ? JSON.parse(json_str) : json_str;
  };

  // returns XML DOM from string with xml content
  me.fromStr = function (xml, rstr) {
    // for non IE browsers
    if (window.DOMParser) {
      const getxml = new DOMParser();
      var xmlDoc = getxml.parseFromString(xml, 'text/xml');
    } else {
      // for Internet Explorer
      var xmlDoc = new ActiveXObject('Microsoft.XMLDOM');
      xmlDoc.async = 'false';
    }

    // gets the JSON string
    const json_str = jsontoStr(setJsonObj(xmlDoc));

    // sets and returns the JSON object, if "rstr" undefined (not passed), else, returns JSON string
    return rstr === undefined ? JSON.parse(json_str) : json_str;
  };

  // receives XML DOM object, returns converted JSON object
  var setJsonObj = function (xml) {
    let js_obj = {};
    if (xml.nodeType == 1) {
      if (xml.attributes.length > 0) {
        js_obj['@attributes'] = {};
        for (let j = 0; j < xml.attributes.length; j++) {
          const attribute = xml.attributes.item(j);
          js_obj['@attributes'][attribute.nodeName] = attribute.value;
        }
      }
    } else if (xml.nodeType == 3) {
      js_obj = xml.nodeValue;
    }
    if (xml.hasChildNodes()) {
      for (let i = 0; i < xml.childNodes.length; i++) {
        const item = xml.childNodes.item(i);
        const nodeName = item.nodeName;
        if (js_obj[nodeName] === undefined) {
          js_obj[nodeName] = setJsonObj(item);
        } else {
          if (js_obj[nodeName].push === undefined) {
            const old = js_obj[nodeName];
            js_obj[nodeName] = [];
            js_obj[nodeName].push(old);
          }
          js_obj[nodeName].push(setJsonObj(item));
        }
      }
    }
    return js_obj;
  };

  // converts JSON object to string (human readablle).
  // Removes '\t\r\n', rows with multiples '""', multiple empty rows, '  "",', and "  ",; replace empty [] with ""
  var jsontoStr = function (js_obj) {
    const rejsn = JSON.stringify(js_obj, undefined, 2)
      .replaceAll(/(\\t|\\r|\\n)/g, '')
      .replaceAll(/"",\s+"",*/g, '')
      .replaceAll(/(\n\s*\n)/g, '')
      .replaceAll(/\s{2,}"",?/g, '')
      .replaceAll(/"\s+",?/g, '');
    return rejsn.includes('"parsererror": {') ? 'Invalid XML format' : rejsn;
  };
}

export default XMLToJSON;
