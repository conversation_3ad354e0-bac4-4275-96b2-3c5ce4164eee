var g=Object.defineProperty;var p=Object.getOwnPropertySymbols;var h=Object.prototype.hasOwnProperty,b=Object.prototype.propertyIsEnumerable;var m=(a,t,e)=>t in a?g(a,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[t]=e,s=(a,t)=>{for(var e in t||(t={}))h.call(t,e)&&m(a,e,t[e]);if(p)for(var e of p(t))b.call(t,e)&&m(a,e,t[e]);return a};var f=(a,t,e)=>new Promise((i,r)=>{var l=o=>{try{n(e.next(o))}catch(c){r(c)}},d=o=>{try{n(e.throw(o))}catch(c){r(c)}},n=o=>o.done?i(o.value):Promise.resolve(o.value).then(l,d);n((e=e.apply(a,t)).next())});import{u as C}from"./vxe-table-a0ubJ4nQ.js";import{by as y}from"./bootstrap-DShsrVit.js";import{g as N}from"./table-eRKxsFfH.js";import{_}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as x,af as P,ag as k,ah as w,a3 as u,n as D}from"../jse/index-index-BMh_AyeW.js";import"./empty-icon-Crs2MoG4.js";import"./loading-Cqdke3S1.js";import"./form-DnT3S1ma.js";const G=x({__name:"form",setup(a){const t={collapsed:!1,schema:[{component:"Input",defaultValue:"1",fieldName:"category",label:"Category"},{component:"Input",fieldName:"productName",label:"ProductName"},{component:"Input",fieldName:"price",label:"Price"},{component:"Select",componentProps:{allowClear:!0,options:[{label:"Color1",value:"1"},{label:"Color2",value:"2"}],placeholder:"请选择"},fieldName:"color",label:"Color"},{component:"DatePicker",fieldName:"datePicker",label:"Date"}],showCollapseButton:!0,submitOnEnter:!1},e={checkboxConfig:{highlight:!0,labelField:"name"},columns:[{title:"序号",type:"seq",width:50},{align:"left",title:"Name",type:"checkbox",width:100},{field:"category",title:"Category"},{field:"color",title:"Color"},{field:"productName",title:"Product Name"},{field:"price",title:"Price"},{field:"releaseDate",formatter:"formatDateTime",title:"Date"}],height:"auto",keepSource:!0,pagerConfig:{},proxyConfig:{ajax:{query:(d,n)=>f(this,[d,n],function*({page:r},l){return y.success(`Query params: ${JSON.stringify(l)}`),yield N(s({page:r.currentPage,pageSize:r.pageSize},l))})}}},[i]=C({formOptions:t,gridOptions:e});return(r,l)=>(P(),k(u(_),{"auto-content-height":""},{default:w(()=>[D(u(i))]),_:1}))}});export{G as default};
