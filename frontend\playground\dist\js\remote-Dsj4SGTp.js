var f=(u,m,r)=>new Promise((a,o)=>{var e=t=>{try{p(r.next(t))}catch(n){o(n)}},c=t=>{try{p(r.throw(t))}catch(n){o(n)}},p=t=>t.done?a(t.value):Promise.resolve(t.value).then(e,c);p((r=r.apply(u,m)).next())});import{u as x}from"./vxe-table-a0ubJ4nQ.js";import{B as d}from"./bootstrap-DShsrVit.js";import{g as y}from"./table-eRKxsFfH.js";import{_ as C}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as b,af as h,ag as k,ah as l,a3 as i,n as s,an as g}from"../jse/index-index-BMh_AyeW.js";import"./empty-icon-Crs2MoG4.js";import"./loading-Cqdke3S1.js";import"./form-DnT3S1ma.js";const P=b({__name:"remote",setup(u){const m={checkboxConfig:{highlight:!0,labelField:"name"},columns:[{title:"序号",type:"seq",width:50},{align:"left",title:"Name",type:"checkbox",width:100},{field:"category",title:"Category"},{field:"color",title:"Color"},{field:"productName",title:"Product Name"},{field:"price",title:"Price"},{field:"releaseDate",formatter:"formatDateTime",title:"DateTime"}],exportConfig:{},height:"auto",keepSource:!0,proxyConfig:{ajax:{query:e=>f(this,[e],function*({page:o}){return yield y({page:o.currentPage,pageSize:o.pageSize})})}},toolbarConfig:{custom:!0,export:!0,refresh:!0,zoom:!0}},[r,a]=x({gridOptions:m});return(o,e)=>(h(),k(i(C),{"auto-content-height":""},{default:l(()=>[s(i(r),{"table-title":"数据列表","table-title-help":"提示"},{"toolbar-tools":l(()=>[s(i(d),{class:"mr-2",type:"primary",onClick:e[0]||(e[0]=()=>i(a).query())},{default:l(()=>e[2]||(e[2]=[g(" 刷新当前页面 ")])),_:1}),s(i(d),{type:"primary",onClick:e[1]||(e[1]=()=>i(a).reload())},{default:l(()=>e[3]||(e[3]=[g(" 刷新并返回第一页 ")])),_:1})]),_:1})]),_:1}))}});export{P as default};
