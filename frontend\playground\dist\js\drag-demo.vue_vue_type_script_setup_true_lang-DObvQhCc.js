import{by as r}from"./bootstrap-DShsrVit.js";import{a4 as n,af as t,ag as s,ah as m,a3 as i,an as f}from"../jse/index-index-BMh_AyeW.js";import{u as p}from"./use-modal-B0smF4x0.js";const C=n({__name:"drag-demo",setup(d){const[o,e]=p({draggable:!0,onCancel(){e.close()},onConfirm(){r.info("onConfirm")}});return(l,a)=>(t(),s(i(o),{title:"可拖拽示例"},{default:m(()=>a[0]||(a[0]=[f(" 鼠标移动到 header 上，可拖拽弹窗 ")])),_:1}))}});export{C as _};
