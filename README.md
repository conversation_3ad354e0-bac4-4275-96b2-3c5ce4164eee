需求点

生成基于管理后台需求，实现截图中的功能，前端使用最新版本的vben admin框架，参考https://doc.vben.pro/，后端使用nodejs nestjs，前端和后端分别存放一个目录，根据以下需求进行数据库设计，并且创建后端项目代码和前端项目代码 需求点如下 

1. 用户管理模块
用户列表管理：支持按用户名、姓名、状态、岗位、部门等条件筛选
用户信息维护：包含工号、用户名、姓名、手机号、任职部门、岗位等字段
用户状态控制：启用/禁用用户账户
批量操作：支持新增、修改、删除、权限分配、重置密码等操作
新增对话框底部包含岗位列表，可添加和删除用户的岗位信息
2. 组织管理
组织管理：机构名称、编码、排序、类别（部门，科室）、上级机构、默认角色，状态（启用，停用）
组织管理显示：支持树形结构的部门组织架构
排序功能：支持岗位和部门的排序调整
3. 岗位管理
岗位管理：岗位名称、编码、排序、类别（部门，科室）、主管上级等层级关系、归属部门，状态（启用，停用）
部门管理：支持树形结构的部门组织架构
排序功能：支持岗位和部门的排序调整
4. 权限管理模块
用户组管理：用户组的创建、编辑和成员管理，包含用户组名称，关联角色，用户组描述
角色管理：角色名称、描述、状态控制，创建时间，分配权限
应用管理：应用名称、Key、联系人、状态等信息维护
5. 功能权限控制
功能名称管理：按功能类型（菜单/按钮）分类
功能路径配置：如 /user-id、# 等路径设置
权限状态控制：启用/禁用功能权限
字段：应用名称，上级功能，功能名称，功能类型（目录，菜单，按钮）功能路径，功能图标，关联资源，排序，状态（启用，停用）
6. 字典管理
字典标签：福州市及下属区县的层级管理
学典值配置：350122等编码体系
排序管理：支持地域信息的排序
7. 系统配置
参数配置：系统级参数的键值对管理，是否内置，创建时间
标签页视图：如 sys.index.tagsView 等系统配置项
机构管理：支持机构信息的层级管理
8. 仪表板功能
欢迎信息：个性化用户欢迎界面
在线统计：当前在线用户数量统计
系统消息：系统通知和消息展示
数据图表：系统运行状态的可视化展示

