import * as Cesium from 'cesium';

/**
 * 天际线分析
 * @param {*} params
 */
function SkylineAnalysis(params) {
  if (params) {
    this._viewer = params.that._viewer;
    //加载天际线
    var collection = this._viewer.scene.postProcessStages;
    var edgeDetection =
      Cesium.PostProcessStageLibrary.createEdgeDetectionStage();

    var postProccessStage = new Cesium.PostProcessStage({
      //此后处理阶段的唯一名称，供组合中的其他阶段参考。如果未提供名称，将生成 GUID。
      //name: 'Skyline',
      //fragmentShader 细绳 uniform着色器对象  textureScale
      fragmentShader:
        'uniform sampler2D colorTexture;' +
        'uniform sampler2D depthTexture;' +
        'varying vec2 v_textureCoordinates;' +
        'void main(void)' +
        '{' +
        'float depth = czm_readDepth(depthTexture, v_textureCoordinates);' +
        'vec4 color = texture2D(colorTexture, v_textureCoordinates);' +
        'if(depth<1.0 - 0.000001){' +
        'gl_FragColor = color;' +
        '}' +
        'else{' +
        'gl_FragColor = vec4(1.0,0.0,0.0,1.0);' +
        '}' +
        '}',
    });

    //PostProcessStage:要使用的片段着色器。默认sampler2D制服是colorTexture和depthTexture。
    var postProccessStage1 = new Cesium.PostProcessStage({
      //name:此后处理阶段的唯一名称，供组合中的其他阶段参考。如果未提供名称，将生成 GUID。
      name: 'czm_skylinetemp1',
      //fragmentShader 细绳 uniform着色器对象  textureScale
      fragmentShader:
        'uniform sampler2D colorTexture;' +
        'uniform sampler2D redTexture;' +
        'uniform sampler2D silhouetteTexture;' +
        'varying vec2 v_textureCoordinates;' +
        'void main(void)' +
        '{' +
        'vec4 redcolor=texture2D(redTexture, v_textureCoordinates);' +
        'vec4 silhouetteColor = texture2D(silhouetteTexture, v_textureCoordinates);' +
        'vec4 color = texture2D(colorTexture, v_textureCoordinates);' +
        'if(redcolor.r == 1.0){' +
        'gl_FragColor = mix(color, vec4(5.0,0.0,0.0,1.0), silhouetteColor.a);' +
        '}' +
        'else{' +
        'gl_FragColor = color;' +
        '}' +
        '}',
      //uniform着色器对象
      uniforms: {
        redTexture: postProccessStage.name,
        silhouetteTexture: edgeDetection.name,
      },
    });
    // 如果inputPreviousStageTexture是true，则每个阶段的输入是场景渲染到的输出纹理或之前执行的阶段的输出纹理。
    // 如果inputPreviousStageTexture是false，则合成中每个阶段的输入纹理都是相同的。
    var postProccessStage = new Cesium.PostProcessStageComposite({
      //PostProcessStage要按顺序执行 的 s 或组合的数组。
      stages: [edgeDetection, postProccessStage, postProccessStage1],
      //是否执行每个后处理阶段，其中一个阶段的输入是前一个阶段的输出。
      //否则，每个包含阶段的输入是在组合之前执行的阶段的输出。
      inputPreviousStageTexture: false,
      //后处理阶段制服的别名。
      uniforms: edgeDetection.uniforms,
    });
    collection.add(postProccessStage);
  }

  SkylineAnalysis.prototype.remove = function () {
    if (collection) {
      collection.remove(postProccessStage);
    }
  };
}

export default SkylineAnalysis;
