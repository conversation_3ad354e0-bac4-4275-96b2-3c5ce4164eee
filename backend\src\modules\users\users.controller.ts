import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Put,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { User } from './entities/user.entity';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { ResponseDto } from '@/common/dto/response.dto';
import { PaginationResult } from '@/common/dto/pagination.dto';

@ApiTags('用户管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({ status: 201, description: '创建成功', type: User })
  async create(
    @Body() createUserDto: CreateUserDto,
    @CurrentUser() currentUser: User,
  ): Promise<ResponseDto<User>> {
    const user = await this.usersService.create(createUserDto, currentUser.id);
    return ResponseDto.success(user, '创建成功');
  }

  @Get()
  @ApiOperation({ summary: '获取用户列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async findAll(@Query() queryDto: QueryUserDto): Promise<ResponseDto<PaginationResult<User>>> {
    const result = await this.usersService.findAll(queryDto);
    return ResponseDto.success(result, '获取成功');
  }

  @Get(':id')
  @ApiOperation({ summary: '获取用户详情' })
  @ApiResponse({ status: 200, description: '获取成功', type: User })
  async findOne(@Param('id') id: string): Promise<ResponseDto<User>> {
    const user = await this.usersService.findOne(+id);
    return ResponseDto.success(user, '获取成功');
  }

  @Put(':id')
  @ApiOperation({ summary: '更新用户' })
  @ApiResponse({ status: 200, description: '更新成功', type: User })
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @CurrentUser() currentUser: User,
  ): Promise<ResponseDto<User>> {
    const user = await this.usersService.update(+id, updateUserDto, currentUser.id);
    return ResponseDto.success(user, '更新成功');
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除用户' })
  @ApiResponse({ status: 200, description: '删除成功' })
  async remove(@Param('id') id: string): Promise<ResponseDto<void>> {
    await this.usersService.remove(+id);
    return ResponseDto.success(undefined, '删除成功');
  }

  @Delete('batch')
  @ApiOperation({ summary: '批量删除用户' })
  @ApiResponse({ status: 200, description: '批量删除成功' })
  async batchRemove(@Body('ids') ids: number[]): Promise<ResponseDto<void>> {
    await this.usersService.batchRemove(ids);
    return ResponseDto.success(undefined, '批量删除成功');
  }

  @Post(':id/reset-password')
  @ApiOperation({ summary: '重置用户密码' })
  @ApiResponse({ status: 200, description: '重置成功' })
  async resetPassword(
    @Param('id') id: string,
    @Body('password') password: string,
    @CurrentUser() currentUser: User,
  ): Promise<ResponseDto<void>> {
    await this.usersService.resetPassword(+id, password, currentUser.id);
    return ResponseDto.success(undefined, '重置成功');
  }

  @Patch(':id/status')
  @ApiOperation({ summary: '更新用户状态' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateStatus(
    @Param('id') id: string,
    @Body('status') status: number,
    @CurrentUser() currentUser: User,
  ): Promise<ResponseDto<void>> {
    await this.usersService.updateStatus(+id, status, currentUser.id);
    return ResponseDto.success(undefined, '更新成功');
  }

  @Put('batch-status')
  @ApiOperation({ summary: '批量更新用户状态' })
  @ApiResponse({ status: 200, description: '批量更新成功' })
  async batchUpdateStatus(
    @Body() body: { ids: number[]; status: number },
    @CurrentUser() currentUser: User,
  ): Promise<ResponseDto<void>> {
    await this.usersService.batchUpdateStatus(body.ids, body.status, currentUser.id);
    return ResponseDto.success(undefined, '批量更新成功');
  }

  @Put('reset-password/batch')
  @ApiOperation({ summary: '批量重置用户密码' })
  @ApiResponse({ status: 200, description: '批量重置成功' })
  async batchResetPassword(
    @Body() body: { ids: number[]; password: string },
    @CurrentUser() currentUser: User,
  ): Promise<ResponseDto<void>> {
    await this.usersService.batchResetPassword(body.ids, body.password, currentUser.id);
    return ResponseDto.success(undefined, '批量重置成功');
  }

  @Put('assign-roles/:id')
  @ApiOperation({ summary: '分配用户角色' })
  @ApiResponse({ status: 200, description: '分配成功' })
  async assignRoles(
    @Param('id') id: string,
    @Body() body: { roleIds: number[] },
    @CurrentUser() currentUser: User,
  ): Promise<ResponseDto<void>> {
    await this.usersService.assignRoles(+id, body.roleIds, currentUser.id);
    return ResponseDto.success(undefined, '分配成功');
  }

  @Put('batch-assign-roles')
  @ApiOperation({ summary: '批量分配用户角色' })
  @ApiResponse({ status: 200, description: '批量分配成功' })
  async batchAssignRoles(
    @Body() body: { userIds: number[]; roleIds: number[] },
    @CurrentUser() currentUser: User,
  ): Promise<ResponseDto<void>> {
    await this.usersService.batchAssignRoles(body.userIds, body.roleIds, currentUser.id);
    return ResponseDto.success(undefined, '批量分配成功');
  }
}
