import { useAccessStore } from "@vben/stores";

export function getLastPathSegment()  {
  const url = window.location.href;
  const segments = url.split('/');
  return segments.pop() || segments.pop(); // 防止末尾有空字符串
};

export function  getCategoryFromLastPath (path : string)  {
  return path;
  // switch(path) {
  //   case 'terrain':
  //     return '地形级';
  //   case 'city':
  //     return '城市级';
  //   case 'part':
  //     return '部件级';
  //   default:
  //     return '地形级';
  // }
}


export function appendTokenToUrl(url) {
 let accessToken =  useAccessStore().accessToken;
 let token = accessToken ? `Bearer ${accessToken}` : null;
 return token ? `${url}?token=${token}` : url;
}
