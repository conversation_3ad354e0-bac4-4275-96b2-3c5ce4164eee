<script lang="ts" setup>
import { computed, ref, unref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';

import { datasetTypeOptions, getFormSchemaByCategory } from "../scene.data";
import type { FileInfo } from "cspell/dist/esm/util/fileHelper";
import { save, edit, updateFileState } from "#/views/dataManage/scene/scene.api";
import { message } from "ant-design-vue";
import { showLoading, showSuccess } from "#/utils/toast.js";
import { uploadFolder } from "#/utils/folderUpload.js";
import  {uploadFile} from '#/utils/fileUpload.js'
import {RegionUtils} from "#/utils/regionUtils";

// 声明Emits
const emit = defineEmits(['register', 'success']);

let id = null;
let reginCodeValue = null;

const [Modal, modalApi] = useVbenModal({
  closeOnClickModal:false,
  onCancel() {
    modalApi.close();
  },
  onClosed() {
    clearData();
  },
  onConfirm() {},
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      formApi.resetForm();
      const data = modalApi.getData<Record<string, any>>();
      console.log(data);
      isUpdate.value = state.value && state.value.update;
      isUpload.value = state.value && state.value.upload;
      category = state.value && state.value.category;
      dataType = state.value && state.value.row && state.value.row.dataType;

      let rowData = {...state.value.row}
      let regionCode = [parseInt(rowData.regionProvinceCode),parseInt(rowData.regionCityCode), parseInt(rowData.regionCountyCode)];
      let regionName = `${rowData.regionProvinceName || ''} ${rowData.regionCityName || ''} ${rowData.regionCityName || ''}`.trim();
      rowData.regionCode = regionName;
      reginCodeValue = regionCode;


      delete rowData.regionProvinceCode;
      delete rowData.regionCityCode;
      delete rowData.regionCountyCode;
      delete rowData.regionProvinceName;
      delete rowData.regionCityName;
      delete rowData.regionCountyName;

      let values = {}
      if (isUpdate.value) {
        values = {...rowData};
      }
      id = values.id;
      values.update = isUpdate.value;
      values.upload = isUpload.value;
      formApi.setValues(values);
    }
  },
});

const state = modalApi.useStore();

const formModel = ref({
  id: '', // 确保初始化
  datasetName: '',  // 其他字段
});
let isUpdate = ref(false);
let isUpload = ref(false);
let category;
let dataType;
const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
      style: {
        '--disabled-text-color': 'hsl(var(--text-color))',
        '--disabled-bg-color': 'hsl(var(--background))',
      }
    },
    labelClass: 'text-right pr-4',
    labelWidth: 100,
  },
  // 提交函数
  handleSubmit,
  handleReset,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: getFormSchemaByCategory(category),
  wrapperClass: 'grid-cols-1',
  submitButtonOptions:{
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  }
});
let fileInput : any= null;
const fileList = ref([]);
const uploadType = ref('0');
const startUpload = ref(false);

const headers = {
    authorization: 'authorization-text',
  };

// 设置标题
const title = computed(() => (unref(isUpdate) ? '编辑' : '新增'));

const clearData = () => {
  formApi.setState({
    commonConfig: { disabled: false },
    submitButtonOptions: { disabled: false },
    resetButtonOptions: { disabled: false }
  });
}

async function handleReset(values) {
  let id = values.id;
  await formApi.resetForm();
  await formApi.setFieldValue("id",id);
  await formApi.setFieldValue("dataType",dataType);
}

// 表单提交事件
async function handleSubmit(values) {
  try {
    values.category = category;
    // startUpload.value = true;

    let regionCode = values.regionCode;
    if(regionCode != null && typeof regionCode === "string"){
      regionCode = reginCodeValue;
    }

    let regionProvinceCode = regionCode[0];
    let regionCityCode = regionCode[1];
    let regionCountyCode = regionCode[2];

    // 从表单中获取省市区名称
    let region = RegionUtils.getRegionNameByCode(''+regionCountyCode);
    let regionProvinceName = region.province;
    let regionCityName = region.city;
    let regionCountyName = region.area;

    // 将省市区名称添加到values中
    values.regionProvinceCode = regionProvinceCode;
    values.regionProvinceName = regionProvinceName;
    values.regionCityCode = regionCityCode;
    values.regionCityName = regionCityName;
    values.regionCountyCode = regionCountyCode;
    values.regionCountyName = regionCountyName;

    showLoading('操作处理中...');
    let result =  isUpdate.value ? await edit(values,id) :  await save(values) ;
    delete values.regionCode;
    if(!isUpdate.value){
      if(fileInput.files == null || fileInput.files.length == 0) {
        onSuccess && onSuccess();
        return;
      }
      let onUpload = async (uploadId)=> {
        await updateFileState({
          id: result,
          fileId:uploadId,
          dataType:values.dataType
        })
        onSuccess && onSuccess();
      }
      let dataType = values.dataType;
      let fileFormat = values.fileFormat;
      uploadType.value === '0' ?
          await uploadFolder(fileInput,onUpload,dataType,fileFormat) : await uploadFile(fileInput,onUpload,dataType,fileFormat);
      return;
    }
    onSuccess && onSuccess();
  } finally {
  }
}

const onSuccess = () =>{
  // 关闭弹窗
  modalApi.close();
  showSuccess('操作成功！');
  emit('success');
}

const handleChange = (info: FileInfo) => {
  if (info.file.status !== 'uploading') {
    console.log(info.file, info.fileList);
  }
  if (info.file.status === 'done') {
  } else if (info.file.status === 'error') {
  }
};

const handleRemove = (file) => {
  const index = fileList.value.indexOf(file);
  const newFileList = fileList.value.slice();
  newFileList.splice(index, 1);
  fileList.value = newFileList;
};

const beforeUpload = (file) => {
  fileList.value = [...fileList.value, file];
  return false;
};

const handleUploadTypeChange = (data) =>{
  uploadType.value = data.target.value;
}

const handleFolderSelect = (data) =>{
  console.log(data);
  fileInput = data.target;
  // uploadFolder(fileInput);
  const fileName = document.getElementById('fileName');
  fileName.textContent = fileInput.files.length > 0 ? '已选'+fileInput.files.length+'个文件' : '没有选择文件';
  formApi.setFieldValue("fileUpload",fileInput.files);
}

const triggerFileInput = () => {
  document.getElementById('fileInput').click();
}
</script>
<template>
  <Modal
    v-bind="$attrs"
    :footer="false"
    :title="title"
    class="w-[800px]"
    destroy-on-close
    :maskClosable="false"
  >
    <div style="padding-left: 20px;padding-right: 20px;padding-top: 20px">
      <Form>
        <!--      <template #uploadType="slotProps">-->
        <!--        <a-radio-group v-show="!isUpdate" v-model:value="uploadType" @change="handleUploadTypeChange":disabled="true">-->
        <!--          <a-radio value="0">目录</a-radio>-->
        <!--          <a-radio value="1">文件</a-radio>-->
        <!--        </a-radio-group>-->
        <!--      </template>-->
<!--        <template #fileUpload="slotProps">
          <input
            class="hidden"
            v-show="!isUpdate"  id="fileInput" type="file"  :directory="uploadType ==='0'"  :webkitdirectory="uploadType ==='0'" @change="handleFolderSelect"  />

          &lt;!&ndash; 自定义按钮 &ndash;&gt;
          <label for="fileInput" onclick="triggerFileInput"  class="custom-label bg-primary-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400">
            选择文件
          </label>

          &lt;!&ndash; 显示选择的文件名称 &ndash;&gt;
          <span id="fileName" class="ml-4">没有选择文件</span>
        </template>-->
      </Form>
    </div>

    <view v-show="startUpload" style="width:900px">
      <div id="folderProgressArea" class="folderProgressArea">
        文件夹总进度：<div id="folderProgress" class="folderProgress">0%</div>
      </div>
      <div v-show="startUpload" style="display: flex;flex-direction:column;justify-content: flex-start;align-items: flex-start">
        <div style="margin-bottom: 5px">当前文件进度：</div>
        <div id="fileProgress" class="fileProgress bg-primary text-white "></div>
      </div>
    </view>

  </Modal>
</template>
<style lang="less"  scoped>
@import '#/styles/dark-antd.less';

.treeList {
  height: 100px;
  max-height: 100px;
  overflow-y: auto;
}

.pannelArea {
  height: 330px;
  background: transparent;
}

.itemTitle {
  display: flex;
  width: 100%;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
  margin-top: 5px;
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 5px;
}

.sitemTitle {
  display: flex;
  width: 100%;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
  padding-top: 5px;
  padding-bottom: 5px;
}

/* 元数据区域的样式 */
.metadata-section {
  margin-bottom: 10px;
  border-radius: 8px; /* 添加圆角效果 */
}

.metadata-row {
  display: flex;
  justify-content: flex-start;
}

.metadata-row span  {
  margin-right: 5px;
  width: 280px;
  margin-top:5px;
}

.smetadata-row {
  display: flex;
  font-size: 14px;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.smetadata-row span  {
  width: 250px;
  margin-top:5px;
  margin-left: 5px;
}

.spacedata-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.spacedata-row view:first-child {
  width: 330px;
}

.layerTree {
  width: 250px;
  overflow-y: auto;
  max-height: 400px; /* 设置最大高度 */
  margin-right: 10px;
  margin-top: 10px;
}

.tabClass {
  width: 97%;
  margin-top: 0px;
  background: transparent;
}

.metadata-row span:first-child {
  width: 250px;
}

/* 模态框内容的整体布局 */
a-modal {
  padding: 20px; /* 模态框内部内容增加填充空间 */
}

/* 预览图片区域样式 */
a-image {
  margin-top: 10px; /* 与上方标题拉开距离 */
  border: 1px solid #d9d9d9; /* 为预览图片添加边框 */
  border-radius: 4px; /* 添加圆角 */
}

/* 日志信息部分的样式 */
a-tab-pane {
  padding: 20px; /* 各个Tab Pane内部增加填充空间 */
}

.truncate-text {
  max-width: 550px; /* 设置最大宽度 */
  word-wrap: break-word; /* 自动换行 */
  overflow-wrap: break-word; /* 兼容旧浏览器 */
  margin-bottom: 10px;
}
</style>
