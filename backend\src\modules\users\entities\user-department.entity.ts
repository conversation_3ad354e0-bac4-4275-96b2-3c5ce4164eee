import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { User } from './user.entity';
import { Organization } from '@/modules/organizations/entities/organization.entity';
import { Position } from '@/modules/positions/entities/position.entity';

@Entity('user_departments')
@Index(['userId'])
@Index(['departmentId'])
@Index(['positionId'])
export class UserDepartment {
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '用户ID' })
  @Column({
    name: 'user_id',
    type: 'bigint',
    comment: '用户ID',
  })
  userId: number;

  @ApiProperty({ description: '部门ID' })
  @Column({
    name: 'department_id',
    type: 'bigint',
    nullable: true,
    comment: '部门ID',
  })
  departmentId?: number;

  @ApiProperty({ description: '岗位ID' })
  @Column({
    name: 'position_id',
    type: 'bigint',
    nullable: true,
    comment: '岗位ID',
  })
  positionId?: number;

  @ApiProperty({ description: '创建时间' })
  @Column({
    name: 'created_time',
    type: 'datetime',
    default: () => 'CURRENT_TIMESTAMP',
    comment: '创建时间',
  })
  createdTime: Date;

  @ApiProperty({ description: '更新时间' })
  @Column({
    name: 'updated_time',
    type: 'datetime',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    comment: '更新时间',
  })
  updatedTime: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.userDepartments)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Organization, { eager: true })
  @JoinColumn({ name: 'department_id' })
  department?: Organization;

  @ManyToOne(() => Position, { eager: true })
  @JoinColumn({ name: 'position_id' })
  position?: Position;
}
