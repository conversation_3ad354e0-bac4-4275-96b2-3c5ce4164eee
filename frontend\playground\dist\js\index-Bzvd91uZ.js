import{_ as V}from"./doc-button.vue_vue_type_script_setup_true_lang-BzJdr9vE.js";import{_ as H}from"./auto-height-demo.vue_vue_type_script_setup_true_lang-DLjvHbOd.js";import{_ as N}from"./base-demo.vue_vue_type_script_setup_true_lang-D7iqctKN.js";import{_ as v}from"./drag-demo.vue_vue_type_script_setup_true_lang-DObvQhCc.js";import{_ as F}from"./dynamic-demo.vue_vue_type_script_setup_true_lang-sh3BEwZ7.js";import{_ as T}from"./form-modal-demo.vue_vue_type_script_setup_true_lang-DZT47kLy.js";import{_ as w}from"./shared-data-demo.vue_vue_type_script_setup_true_lang-BU3anNOp.js";import{B as e}from"./bootstrap-DShsrVit.js";import{C as i}from"./index-B_b7xM74.js";import{_ as U}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as j,af as q,ag as z,ah as n,a3 as t,n as a,ap as m,an as s}from"../jse/index-index-BMh_AyeW.js";import{u as p}from"./use-modal-B0smF4x0.js";import"./form-DnT3S1ma.js";import"./x-B-ntYT_e.js";import"./loading-Cqdke3S1.js";const ao=j({__name:"index",setup(E){const[f,u]=p({connectedComponent:N}),[c,_]=p({connectedComponent:H}),[y,M]=p({connectedComponent:v}),[b,l]=p({connectedComponent:F}),[C,r]=p({connectedComponent:w}),[g,d]=p({connectedComponent:T});function k(){u.open()}function A(){_.open()}function D(){M.open()}function $(){l.open()}function B(){r.setData({content:"外部传递的数据 content",payload:"外部传递的数据 payload"}),r.open()}function x(){l.setState({title:"外部动态标题"}),l.open()}function S(){d.setData({values:{field1:"abc"}}),d.open()}return(G,o)=>(q(),z(t(U),{description:"弹窗组件常用于在不离开当前页面的情况下，显示额外的信息、表单或操作提示，更多api请查看组件文档。",title:"弹窗组件示例"},{extra:n(()=>[a(V,{path:"/components/common-ui/vben-modal"})]),default:n(()=>[a(t(f)),a(t(c)),a(t(y)),a(t(b)),a(t(C)),a(t(g)),a(t(i),{class:"mb-4",title:"基本使用"},{default:n(()=>[o[1]||(o[1]=m("p",{class:"mb-3"},"一个基础的弹窗示例",-1)),a(t(e),{type:"primary",onClick:k},{default:n(()=>o[0]||(o[0]=[s("打开弹窗")])),_:1})]),_:1}),a(t(i),{class:"mb-4",title:"内容高度自适应"},{default:n(()=>[o[3]||(o[3]=m("p",{class:"mb-3"},"可根据内容并自动调整高度",-1)),a(t(e),{type:"primary",onClick:A},{default:n(()=>o[2]||(o[2]=[s("打开弹窗")])),_:1})]),_:1}),a(t(i),{class:"mb-4",title:"可拖拽示例"},{default:n(()=>[o[5]||(o[5]=m("p",{class:"mb-3"},"配置 draggable 可开启拖拽功能",-1)),a(t(e),{type:"primary",onClick:D},{default:n(()=>o[4]||(o[4]=[s("打开弹窗")])),_:1})]),_:1}),a(t(i),{class:"mb-4",title:"动态配置示例"},{default:n(()=>[o[8]||(o[8]=m("p",{class:"mb-3"},"通过 setState 动态调整弹窗数据",-1)),a(t(e),{type:"primary",onClick:$},{default:n(()=>o[6]||(o[6]=[s("打开弹窗")])),_:1}),a(t(e),{class:"ml-2",type:"primary",onClick:x},{default:n(()=>o[7]||(o[7]=[s(" 从外部修改标题并打开 ")])),_:1})]),_:1}),a(t(i),{class:"mb-4",title:"内外数据共享示例"},{default:n(()=>[o[10]||(o[10]=m("p",{class:"mb-3"},"通过共享 sharedData 来进行数据交互",-1)),a(t(e),{type:"primary",onClick:B},{default:n(()=>o[9]||(o[9]=[s(" 打开弹窗并传递数据 ")])),_:1})]),_:1}),a(t(i),{class:"mb-4",title:"表单弹窗示例"},{default:n(()=>[o[12]||(o[12]=m("p",{class:"mb-3"},"弹窗与表单结合",-1)),a(t(e),{type:"primary",onClick:S},{default:n(()=>o[11]||(o[11]=[s(" 打开弹窗 ")])),_:1})]),_:1})]),_:1}))}});export{ao as default};
