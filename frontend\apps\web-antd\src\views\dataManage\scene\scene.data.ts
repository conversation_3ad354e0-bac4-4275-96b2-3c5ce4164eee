import {RegionUtils} from "#/utils/regionUtils";

const categoryOptions = [
  { label: '城市级', value: '城市级' },
  { label: '地形级', value: '地形级' },
  { label: '部件级', value: '部件级' },
];

const dataSituationOptions = [
  { label: '正式数据', value: 1 },
  { label: '过程数据', value: 2 },
];

export const datasetTypeOptions = [
  { label: 'OSGB', value: 'osgb',fileType:'folder' },
  { label: '3DTiles', value: 'tiles',fileType:'folder'},
  { label: 'DEM', value: 'dem' ,fileType:'folder'},
  { label: 'DOM', value: 'dom' ,fileType:'folder'},
  { label: 'OBJ', value: 'obj' ,fileType:'folder'},
];

export function getDataTypeValuesByCategory(category) {
  let datatypes = dataTypeOptions[category];
  let values = datatypes.map(item => item.value )
  return values;
}

export const dataTypeOptions = {
  dom:[{ label: 'dom', value: 'dom',fileType:'folder'}],
  dem : [{ label: 'dem', value: 'dem',fileType:'folder'}],
  dsm : [{ label: 'dsm', value: 'dsm',fileType:'folder'}],

  lod: [{ label: 'osgb', value: 'osgb',fileType:'folder'},{ label: 'obj', value: 'obj',fileType:'folder'},{ label: '3DTiles', value: 'tiles',fileType:'folder'}],
  mesh: [{ label: 'osgb', value: 'osgb',fileType:'folder'},{ label: 'obj', value: 'obj',fileType:'folder'},{ label: '3DTiles', value: 'tiles',fileType:'folder'}],
  "point-cloud":[{ label: 'point-cloud', value: 'point-cloud',fileType:'folder'}],

  osgb: [{ label: 'osgb', value: 'osgb',fileType:'folder'}],
  tiles : [{ label: '3DTiles', value: 'tiles',fileType:'folder'}],
  vector:[{ label: 'shp', value: 'shp',fileType:'folder'},{ label: 'gdb', value: 'gdb',fileType:'folder'}],


};

export const fileFormatOptions = {
 dom:[{ label: 'tif', value: 'tif'},{ label: 'img', value: 'img'}],
 dem : [{ label: 'tif', value: 'tif'},{ label: 'img', value: 'img'}],
 dsm : [{ label: 'tif', value: 'tif'},{ label: 'img', value: 'img'}],

 lod: [{ label: 'osgb', value: 'osgb'},{ label: 'obj', value: 'obj'},{ label: 'tiles', value: 'tiles'}],
 mesh: [{ label: 'osgb', value: 'osgb'},{ label: 'obj', value: 'obj'},{ label: 'tiles', value: 'tiles'}],
 'point-cloud':[{ label: 'las', value: 'las'}],

 osgb: [{ label: 'osgb', value: 'osgb'}],
 obj: [{ label: 'obj', value: 'obj'}],
 tiles : [{ label: 'tiles', value: 'tiles'}],

 vector:[{ label: 'shp', value: 'shp'},{ label: 'gdb', value: 'gdb'}],
};



export function getUploadStatusLabel(row) {
  let uploadStatus = row.uploadStatus;
  for(let i=0; i<statusOptions.length; i++) {
    if(statusOptions[i].value === uploadStatus) {
      return statusOptions[i].label;
    }
  }
  return '';
}

const createTimeOptions = [
];

let nowYear = new Date().getFullYear();
for(let i=nowYear; i>2000; i--) {
  createTimeOptions.push( { label: ''+i, value: ''+i })
}

const statusOptions = [
  { label: '待上传', value: 0 },
  { label: '已上传', value: 1 },
  { label: '解析中', value: 2 },
  { label: '解析失败', value: 3 },
];

export const columns = [
  {
    title: '数据集名称',
    dataIndex: 'name',
    field: 'name',
    width: 300,
  },
  // {
  //   title: '类别',
  //   dataIndex: 'category',
  //   field: 'category',
  //   width: 130,
  // },
  {
    title: '数据格式',
    dataIndex: 'dataType',
    field: 'dataType',
    width: 100,
  },
  // {
  //   title: '文件格式',
  //   dataIndex: 'fileFormat',
  //   field: 'fileFormat',
  //   width: 80,
  // },
  {
    title: '数据年份',
    dataIndex: 'dataYear',
    field: 'dataYear',
    width: 120,
  },
  // {
  //   title: '分辨率(m)',
  //   dataIndex: 'resolution',
  //   field: 'resolution',
  //   width: 100,
  // },
  {
    title: '行政区划',
    dataIndex: 'regionName',
    field: 'regionName',
    width: 180,
    slots: { default: 'regionName' },
  },
  {
    title: '面积(平方米)',
    dataIndex: 'area',
    field: 'area',
    width: 180,
    slots: { default: 'area' },
  },
  {
    title: '数据量',
    dataIndex: 'dataSize',
    field: 'dataSize',
    width: 120,
  },
  {
    title: '数据来源',
    dataIndex: 'provider',
    field: 'provider',
    width: 200,
  },
  {
    title: '数据情况',
    dataIndex: 'dataSituation',
    field: 'dataSituation',
    width: 140,
    slots: { default: 'dataSituation' },
  },
  {
    title: '状态',
    dataIndex: 'uploadStatus',
    field: 'uploadStatus',
    slots: { default: 'upload-status' },
    width: 100,
  },
  {
    title: '创建人',
    dataIndex: 'userNameCreated',
    field: 'userNameCreated',
    width: 160,
  },
  {
    title: '创建时间',
    dataIndex: 'timeCreated',
    field: 'timeCreated',
    width: 180,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 210,
  },
];

export const searchFormSchema = [
  {
    label: '数据集名称',
    fieldName: 'name',
    component: 'Input',
  },
  // {
  //   label: '类别',
  //   fieldName: 'category',
  //   component: 'Select',
  //   componentProps: {
  //     options: categoryOptions,
  //   },
  // },
  // {
  //   label: '数据类型',
  //   fieldName: 'dataType',
  //   component: 'Select',
  //   componentProps: {
  //     options: datasetTypeOptions,
  //   },
  // },
  {
    label: '行政区划',
    fieldName: 'regionCode',
    component: 'Cascader',
    componentProps: {
      options: RegionUtils.getProvinces(),
      changeOnSelect:true,
      placeholder: '请选择',
    },
  },
  {
    label: '数据来源',
    fieldName: 'provider',
    component: 'Input',
  },
  {
    label: '数据年份',
    fieldName: 'dataYear',
    component: 'Select',
    componentProps: {
      options: createTimeOptions,
    },
  },
  {
    label: '状态',
    fieldName: 'uploadStatus',
    component: 'Select',
    componentProps: {
      options: statusOptions,
    },

  },
];


// 修改表单中的行政区划字段
export function getFormSchemaByCategory(category,regionCode) {
  let catedatasetTypes = fileFormatOptions[category];
  let dateTypes = dataTypeOptions[category];
  return [
    {
      fieldName: 'id',
      label: '主键',
      component: 'Input',
      dependencies: {
        show:false,
        triggerFields: ['upload'],
      },
    },
    {
      fieldName: 'name',
      label: '数据集名称',
      rules: 'required',
      component: 'Input',
      componentProps: {
        placeholder: '请输入数据集名称',
      },
      dependencies: {
        show(values) {
          return !values.upload;
        },
        triggerFields: ['upload'],
      },
    },
    {
      label: '数据类型',
      fieldName: 'dataType',
      component: 'Select',
      componentProps: {
        options: dateTypes,
        class: 'w-full',
      },
      rules: 'selectRequired',
      dependencies: {
        disabled(values) {
          return (values.id != null && values.id.length > 0);
        },
        triggerFields: ['id'],
      },
    },
    // {
    //   label: '类别',
    //   fieldName: 'category',
    //   component: 'Select',
    //   componentProps: {
    //     options: categoryOptions,
    //   },
    //   rules: 'selectRequired',
    // },
    {
      label: '建设年份',
      fieldName: 'dataYear',
      component: 'Select',
      componentProps: {
        options: createTimeOptions,
      },
      rules: 'selectRequired',
    },
    {
      label: '行政区划',
      fieldName: 'regionCode',
      component: 'Cascader',
      componentProps: {
        options:  RegionUtils.getProvinces(),
        placeholder: '请选择省/市/区',
        changeOnSelect: true,
        defaultValue:regionCode
      },
      rules: 'selectRequired',
    },
    {
      label: '数据来源',
      fieldName: 'provider',
      component: 'Input',
      rules: 'required',
    },
    {
      label: '数据情况',
      fieldName: 'dataSituation',
      component: 'Select',
      componentProps: {
        options: dataSituationOptions,
      },
      rules: 'selectRequired',
    },
  ]

}

export function getUploadFormSchemaByCategory(category) {
  let catedatasetTypes = fileFormatOptions[category];
  return [
    {
      label: '数据类型',
      fieldName: 'dataType',
      component: 'Select',
      componentProps: {
        options: datasetTypeOptions,
      },
      rules: 'selectRequired',
      dependencies: {
        show:false,
        triggerFields: ['id'],
      },
    },
    {
      label: '文件格式',
      fieldName: 'fileFormat',
      component: 'Select',
      componentProps: {
        options: catedatasetTypes,
        placeholder: '请选择',
      },
      rules: 'selectRequired',
      dependencies: {
        show(values) {
          return !(values.id != null && values.id.length > 0);
        },
        triggerFields: ['dataType'],
        componentProps(values) {
          if(values.dataType == null || values.dataType.length == 0) {
            return {
              options: catedatasetTypes,
              placeholder: '请选择',
              value:null,
            };
          }
          // values.fileFormat = '';
          return {
            options: fileFormatOptions[values.dataType],
            placeholder: '请选择',
            value:null,
          };
        },
      },
    },
    // {
    //   component: 'RadioGroup',
    //   componentProps: {
    //     options: [
    //       {
    //         label: '目录',
    //         value: '1',
    //       },
    //       {
    //         label: '文件',
    //         value: '2',
    //       },
    //     ],
    //   },
    //   fieldName: 'uploadType',
    //   label: '',
    // },
    {
      fieldName: 'fileUpload',
      label: '文件',
      component: 'Upload',
      dependencies: {
        rules(values) {
          if (values.id == null || values.id.length == 0) {
            return 'selectRequired';
          }
          return null;
        },
        show(values) {
          return (values.id == null || values.id.length == 0);
        },
        triggerFields: ['id'],
      },
    },
  ]
}
