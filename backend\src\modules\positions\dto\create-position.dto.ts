import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsEnum, Min, Max } from 'class-validator';

export class CreatePositionDto {
  @ApiProperty({ description: '岗位名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '岗位编码' })
  @IsString()
  code: string;

  @ApiProperty({ description: '上级岗位ID，0为根节点', required: false })
  @IsOptional()
  @IsNumber()
  parentId?: number;

  @ApiProperty({ description: '归属部门ID' })
  @IsNumber()
  departmentId: number;

  @ApiProperty({ description: '岗位类别：department部门，office科室' })
  @IsEnum(['department', 'office'])
  type: string;

  @ApiProperty({ description: '排序', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  sortOrder?: number;

  @ApiProperty({ description: '状态：1启用，0停用', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  status?: number;

  @ApiProperty({ description: '岗位描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;
}
