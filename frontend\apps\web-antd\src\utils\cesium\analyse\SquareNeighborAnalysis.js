import * as Cesium from 'cesium';
import { executeTask, executeJob } from './GPExecute';
import GPConfig from '../../../config/GPConfig';
import {
  arcgisToGeoJSON,
  geojsonToArcGIS,
} from '@esri/arcgis-to-geojson-utils';

export default class SquareNeighborAnalysis {
  constructor(params) {
    this.viewer = params.that._viewer;
    this.gp = params.gp || GPConfig.squareneighbor;
    this.complete = params.complete;
    this.error = params.error;
  }
  async execute(options, positions) {
    this.remove();
    positions = positions.map((p) => [p.lng, p.lat]);
    const params = {
      ...options,
      filterFeature: JSON.stringify({
        objectIdFieldName: 'objectid',
        globalIdFieldName: '',
        geometryType: 'esriGeometryPolygon',
        spatialReference: {
          wkid: 4326,
          latestWkid: 4326,
        },
        fields: [],
        features: [
          {
            geometry: {
              rings: [positions],
            },
          },
        ],
      }),
      'env:outSR': 4326,
      'env:processSR': 4326,
      f: 'json',
    };
    const res = await executeJob(this.gp, params, (err) => {
      this.error && this.error(err);
    });
    console.log('...........res', res);
    if (this.complete) this.complete(res.value);
  }
  remove() {
    let viewer = this.viewer;
    if (this.dataSource) {
      viewer.dataSources.remove(this.dataSource);
    }
  }
}
