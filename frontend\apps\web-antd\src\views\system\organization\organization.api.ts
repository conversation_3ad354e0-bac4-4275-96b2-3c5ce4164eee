import { requestClient } from '#/api/request';

enum Api {
  list = '/organizations',
  tree = '/organizations/tree',
  create = '/organizations',
  update = '/organizations',
  delete = '/organizations',
  detail = '/organizations',
  roles = '/roles/list',
}

/**
 * 获取组织列表
 * @param params 查询参数
 */
export const getOrganizationList = (params?: any) => {
  return requestClient.get(Api.list, { params });
};

/**
 * 获取组织树形结构
 * @param params 查询参数
 */
export const getOrganizationTree = (params?: any) => {
  return requestClient.get(Api.tree, { params });
};

/**
 * 创建组织
 * @param data 组织数据
 */
export const createOrganization = (data: any) => {
  return requestClient.post(Api.create, data);
};

/**
 * 更新组织
 * @param id 组织ID
 * @param data 组织数据
 */
export const updateOrganization = (id: number, data: any) => {
  return requestClient.put(`${Api.update}/${id}`, data);
};

/**
 * 删除组织
 * @param id 组织ID
 */
export const deleteOrganization = (id: number) => {
  return requestClient.delete(`${Api.delete}/${id}`);
};

/**
 * 获取组织详情
 * @param id 组织ID
 */
export const getOrganizationDetail = (id: number) => {
  return requestClient.get(`${Api.detail}/${id}`);
};

/**
 * 获取角色列表（用于下拉选择）
 */
export const getRoleList = () => {
  return requestClient.get(Api.roles);
};

/**
 * 批量删除组织
 * @param ids 组织ID数组
 */
export const batchDeleteOrganizations = (ids: number[]) => {
  return requestClient.delete(Api.delete, { data: { ids } });
};
