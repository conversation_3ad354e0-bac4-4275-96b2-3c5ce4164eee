//选取空间查询区域
import * as Cesium from 'cesium';


export function getCoordinatesParams (shapeViewer)  {
  let coordinates = [];
  let shapes = shapeViewer.shapes;
  if (shapes.length > 0) {
    if(shapes[0].polygon) {//多边形
      const hierarchy = shapes[0].polygon.hierarchy.getValue(Cesium.JulianDate.now());
      const positions = hierarchy.positions;
      for (let i = 0; i < positions.length; i++) {
        const cartographic = Cesium.Cartographic.fromCartesian(positions[i]);
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);
        const height = cartographic.height;
        coordinates.push([longitude, latitude]);
      }
      return {coordinates:coordinates};
    }

    if(shapes[0].rectangle) {//矩形
      // 获取矩形的边界坐标
      const rectangleCoordinates = shapes[0].rectangle.coordinates.getValue(Cesium.JulianDate.now());

      // 转换为经纬度
      const west = Cesium.Math.toDegrees(rectangleCoordinates.west);
      const south = Cesium.Math.toDegrees(rectangleCoordinates.south);
      const east = Cesium.Math.toDegrees(rectangleCoordinates.east);
      const north = Cesium.Math.toDegrees(rectangleCoordinates.north);

      coordinates = [
        [ west, south ], // 西南角
        [ west, north ], // 西北角
        [ east, north ], // 东北角
        [ east, south ], // 东南角
      ];
      return {coordinates:coordinates};
    }

    if(shapes[0].ellipse) {//圆形
      // 获取椭圆的中心点
      const centerCartesian = shapes[0].position.getValue(Cesium.JulianDate.now());
      const centerCartographic = Cesium.Cartographic.fromCartesian(centerCartesian);
      const center = [
        Cesium.Math.toDegrees(centerCartographic.longitude),
        Cesium.Math.toDegrees(centerCartographic.latitude)
      ];

      // 获取椭圆的半径
      const semiMajorAxis = shapes[0].ellipse.semiMajorAxis.getValue(Cesium.JulianDate.now());
      const semiMinorAxis = shapes[0].ellipse.semiMinorAxis.getValue(Cesium.JulianDate.now());

      console.log("椭圆的中心点：", center);
      console.log("椭圆的半长轴：", semiMajorAxis, "米");
      console.log("椭圆的半短轴：", semiMinorAxis, "米");

      return {coordinates:[center],radius:semiMajorAxis}
    }

  }
  return {coordinates:coordinates};
}

export function startSpaceAreaSelect(gisMap) {
  let viewer = gisMap.viewer;
  const entities = viewer.entities;
  const stripeMaterial = new Cesium.StripeMaterialProperty({
    evenColor: Cesium.Color.WHITE.withAlpha(0.5),
    oddColor: Cesium.Color.BLUE.withAlpha(0.5),
    repeat: 5.0,
  });

  entities.add({
    rectangle: {
      coordinates: Cesium.Rectangle.fromDegrees(-92.0, 20.0, -86.0, 27.0),
      outline: true,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 4,
      stRotation: Cesium.Math.toRadians(45),
      material: stripeMaterial,
    },
  });

  viewer.zoomTo(viewer.entities);

  if (true) {
    return;
  }

  let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

  let startPosition;
  let rectangle; // 存储绘制的矩形
  let selectedEntities = []; // 用来存储被选中的实体
  let highlightedEntities = []; // 用来存储高亮的实体

  handler.setInputAction(function (movement) {
    startPosition = movement.position;

    // 创建一个矩形，显示选择区域
    rectangle = viewer.entities.add({
      name: 'Selection Rectangle',
      rectangle: {
        coordinates: new Cesium.CallbackProperty(function () {
          let start = viewer.camera.pickEllipsoid(
            startPosition,
            viewer.scene.globe.ellipsoid,
          );
          let end = viewer.camera.pickEllipsoid(
            movement.endPosition,
            viewer.scene.globe.ellipsoid,
          );
          if (start && end) {
            let west = Math.min(start.x, end.x);
            let east = Math.max(start.x, end.x);
            let south = Math.min(start.y, end.y);
            let north = Math.max(start.y, end.y);
            return Cesium.Rectangle.fromDegrees(west, south, east, north);
          }
          return Cesium.Rectangle.EMPTY;
        }, false),
        material: Cesium.Color.fromCssColorString('rgba(0, 255, 0, 0.5)'), // 半透明绿色
        outline: true,
        outlineColor: Cesium.Color.YELLOW,
      },
    });
  }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

  // 在鼠标移动时更新矩形
  handler.setInputAction(function (movement) {
    if (rectangle) {
      // 更新矩形的大小
      let start = viewer.camera.pickEllipsoid(
        startPosition,
        viewer.scene.globe.ellipsoid,
      );
      let end = viewer.camera.pickEllipsoid(
        movement.endPosition,
        viewer.scene.globe.ellipsoid,
      );
      if (start && end) {
        let west = Math.min(start.x, end.x);
        let east = Math.max(start.x, end.x);
        let south = Math.min(start.y, end.y);
        let north = Math.max(start.y, end.y);
        rectangle.rectangle.coordinates = Cesium.Rectangle.fromDegrees(
          west,
          south,
          east,
          north,
        );
      }
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

  // 在鼠标释放时选中区域内的实体
  handler.setInputAction(function () {
    if (rectangle) {
      // 获取矩形区域的坐标
      let rectangleCoordinates = rectangle.rectangle.coordinates.getValue();
      let entities = viewer.entities.values;

      // 清除之前的高亮
      highlightedEntities.forEach(function (entity) {
        entity.point.color = Cesium.Color.RED;
      });
      highlightedEntities = [];

      // 查询所有实体，判断其位置是否在矩形内
      entities.forEach(function (entity) {
        let position = entity.position;
        if (position) {
          let cartographic = Cesium.Cartographic.fromCartesian(position._value);
          if (Cesium.Rectangle.contains(rectangleCoordinates, cartographic)) {
            // 如果实体在矩形内，改变颜色高亮显示
            entity.point.color = Cesium.Color.YELLOW;
            highlightedEntities.push(entity);
          }
        }
      });
    }
    // 清理选择区域
    viewer.entities.remove(rectangle);
    rectangle = undefined;
  }, Cesium.ScreenSpaceEventType.LEFT_UP);
}
