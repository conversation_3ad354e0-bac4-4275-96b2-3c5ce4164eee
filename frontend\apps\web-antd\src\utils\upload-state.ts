import { ref } from 'vue';

// 使用 Set 来存储正在上传的任务ID
const uploadingTasks = ref(new Set<string>());

/**
 * 检查任务是否正在上传
 * @param taskId 任务ID
 * @returns boolean
 */
export const isTaskUploading = (taskId: string): boolean => {
  return uploadingTasks.value.has(taskId);
};

/**
 * 添加任务到上传中状态
 * @param taskId 任务ID
 */
export const addUploadingTask = (taskId: string): void => {
  uploadingTasks.value.add(taskId);
};

/**
 * 从上传中状态移除任务
 * @param taskId 任务ID
 */
export const removeUploadingTask = (taskId: string): void => {
  uploadingTasks.value.delete(taskId);
};

/**
 * 获取所有正在上传的任务ID
 * @returns string[]
 */
export const getUploadingTasks = (): string[] => {
  return Array.from(uploadingTasks.value);
};

/**
 * 清空所有上传中的任务
 */
export const clearUploadingTasks = (): void => {
  uploadingTasks.value.clear();
};
