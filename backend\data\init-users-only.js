const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: 'system_manage'
};

async function initUsers() {
  let connection;
  
  try {
    console.log('🚀 开始初始化用户数据...');
    
    // 连接到数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 成功连接到数据库');
    
    // 检查用户表是否存在
    const [tables] = await connection.execute("SHOW TABLES LIKE 'users'");
    if (tables.length === 0) {
      console.log('❌ users表不存在，请先运行完整的数据库初始化脚本');
      return;
    }
    
    // 清空现有用户数据
    await connection.execute('DELETE FROM user_roles');
    await connection.execute('DELETE FROM users');
    console.log('✅ 清空现有用户数据');
    
    // 插入admin用户
    const adminSql = `
      INSERT INTO users (id, employee_id, username, password, real_name, status) 
      VALUES (1, 'admin', 'admin', ?, '系统管理员', 1)
    `;
    await connection.execute(adminSql, ['$2b$10$MvaLUvYvSfRqMKVd.Tz0c.2j3U0LG2OnN0xrXIboiE/6bbZSU7BcG']);
    console.log('✅ 插入admin用户');
    
    // 插入测试用户
    const testUserSql = `
      INSERT INTO users (id, employee_id, username, password, real_name, phone, status) 
      VALUES (2, 'test001', 'testuser', ?, '何', '15259630375', 1)
    `;
    await connection.execute(testUserSql, ['$2b$10$4HoGJ.gGo7ffRDcu4E5DDub0qN5IvXiuuwUMMSVF2YiDeOtSgwVnm']);
    console.log('✅ 插入测试用户');
    
    // 检查角色表是否存在
    const [rolesTables] = await connection.execute("SHOW TABLES LIKE 'roles'");
    if (rolesTables.length > 0) {
      // 检查是否有角色数据
      const [roles] = await connection.execute('SELECT COUNT(*) as count FROM roles');
      if (roles[0].count > 0) {
        // 分配角色
        await connection.execute('INSERT INTO user_roles (user_id, role_id) VALUES (1, 1)');
        await connection.execute('INSERT INTO user_roles (user_id, role_id) VALUES (2, 2)');
        console.log('✅ 分配用户角色');
      }
    }
    
    // 验证插入的数据
    const [users] = await connection.execute('SELECT id, username, real_name, phone FROM users');
    console.log('📊 用户数据验证：');
    users.forEach(user => {
      console.log(`  - ID: ${user.id}, 用户名: ${user.username}, 姓名: ${user.real_name}, 手机: ${user.phone || '无'}`);
    });
    
    // 测试密码验证
    const bcrypt = require('bcryptjs');
    const [adminUser] = await connection.execute('SELECT password FROM users WHERE username = ?', ['admin']);
    if (adminUser.length > 0) {
      const isValid = await bcrypt.compare('admin123', adminUser[0].password);
      console.log(`🔐 admin密码验证: ${isValid ? '✅ 正确' : '❌ 错误'}`);
    }
    
    const [testUser] = await connection.execute('SELECT password FROM users WHERE username = ?', ['testuser']);
    if (testUser.length > 0) {
      const isValid = await bcrypt.compare('8888a8888#@', testUser[0].password);
      console.log(`🔐 testuser密码验证: ${isValid ? '✅ 正确' : '❌ 错误'}`);
    }
    
    console.log('🎉 用户数据初始化完成！');
    
  } catch (error) {
    console.error('❌ 用户数据初始化失败：', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行初始化
initUsers().catch(console.error);
