<script lang="ts" setup>
import { onMounted, watch } from 'vue';

import * as Cesium from 'cesium';

import { initMap } from '#/utils/cesium';
import { layerService } from '#/utils/cesium/layers';

import SplitMap from '../../../utils/cesium/layers/SplitMap';
import ztu from '../../../ztu';
// import { appInit, capp } from "#/utils/app";
import { useRouter } from 'vue-router';

import { appInit, capp } from '#/utils/app.js';
import {initShapeViewer} from '#/utils/cesium/layers/ShapeViewer'
// Props: Receive selected layers from parent (App.vue)
const props = defineProps({
  visibleLayers: Array,
});
const emits = defineEmits(['viewMapLoaded']);
const router = useRouter();
let viewer;
let layers;
onMounted(async () => {
  if (!capp) {
    appInit(router);
  }
  await capp.initLogin({});
  init();
});

watch(
  () => props.visibleLayers,
  (newLayer) => {
    console.log('New Layer Selected:', newLayer);
    toggleLayers();
  },
);

const toggleLayers = () => {
  if (!layers) {
    return;
  }
  for (const [key, layer] of Object.entries(layers)) {
    if (layer.show === undefined) {
      if (props.visibleLayers.includes(key)) {
        if (!viewer.entities.contains(layer)) {
          viewer.entities.add(layer);
        }
      } else {
        viewer.entities.remove(layer);
      }
    } else {
      layer.show = props.visibleLayers.includes(key);
    }
  }
};

const init = async () => {
  // const viewer = new Cesium.Viewer('viewDiv', { infoBox: false });

  const map = initMap('viewDiv', async (viewer) => {
    ztu.global.GISLayers = new layerService(viewer);
    ztu.global.SplitMap = new SplitMap(viewer);
    // ztu.global.MyLayerService = new MyLayerService(viewer);
    // initPOIViewer({
    //   viewer:viewer
    // });
    initShapeViewer({
      viewer:viewer,
      drawer:null,
      useLocalStorage:true,
      data:null
    });
    // initLocalViewer({
    //   viewer:viewer
    // });
    viewer.scene.morphStart.addEventListener((e, e1, e2, e3) => {
      e._morphCancelled = true;
      console.log(e, e1, e2, e3);
    });

    const GISLayers = ztu.global.GISLayers;
    if (GISLayers.data.length === 0 && capp) {
      const data = capp.layersOrigin;
      GISLayers.loadData(data);
      const checkedKeys = capp.chkLayers;
      const showKeys = data
        .filter((a) => checkedKeys.includes(a.key))
        .map((a) => a.key);
      GISLayers.show(showKeys);

      // 分屏事件监听
      GISLayers.on('reloadlayer', () => {
        checkedKeys.value = [];
      });
      GISLayers.on('showChanged', (keys) => {
        checkedKeys.value = keys;
      });
    }

    // 福建地区经纬度和高度
    const fujianCoordinates = {
      longitude: 118.089425, // 福建省中心经度
      latitude: 26.079353,  // 福建省中心纬度
      height: 3000000       // 视图高度 (单位: 米)
    };

    // 设置视图到福建
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        fujianCoordinates.longitude,
        fujianCoordinates.latitude,
        fujianCoordinates.height
      ),
    });


    // viewer.zoomTo(circle);
    emits('viewMapLoaded', true);
  });
};
</script>

<template>
  <div id="cesiumContainer">
    <div id="viewDiv"></div>
  </div>
</template>

<style scoped>
#cesiumContainer {
  position: relative;
  width: 100%;
  height: 100%;

  #viewDiv {
    width: 100%;
    height: 100%;

    .cesium-infoBox {
      position: absolute;
      top: 100px !important;
      right: 500px !important;
      display: block;
      width: 300px !important;
      max-width: 480px;
      color: #edffff;
      background: rgb(38 38 38 / 95%);
      border: 1px solid #444;
      border-right: none;
      border-top-left-radius: 7px;
      border-bottom-left-radius: 7px;
      box-shadow: 0 0 10px 1px #000;
      transform: translate(100%, 0);
    }
  }

  :deep(.cesium-viewer) {
    width: 100%;
    height: 100%;
  }

  :deep(.cesium-viewer-cesiumWidgetContainer) {
    width: 100%;
    height: 100%;
  }

  :deep(.cesium-widget) {
    width: 100%;
    height: 100%;
  }

  :deep(canvas) {
    width: 100%;
    height: 100%;
  }
}
</style>
