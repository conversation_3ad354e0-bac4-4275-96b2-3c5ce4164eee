import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { UserDepartment } from './entities/user-department.entity';
import { Organization } from '@/modules/organizations/entities/organization.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { PaginationResult } from '@/common/dto/pagination.dto';
export declare class UsersService {
    private userRepository;
    private userDepartmentRepository;
    private organizationRepository;
    constructor(userRepository: Repository<User>, userDepartmentRepository: Repository<UserDepartment>, organizationRepository: Repository<Organization>);
    create(createUserDto: CreateUserDto, currentUserId?: number): Promise<User>;
    findAll(queryDto: QueryUserDto): Promise<PaginationResult<User>>;
    findOne(id: number): Promise<User>;
    findById(id: number): Promise<User | null>;
    findByUsername(username: string): Promise<User | null>;
    findByPhone(phone: string): Promise<User | null>;
    update(id: number, updateUserDto: UpdateUserDto, currentUserId?: number): Promise<User>;
    remove(id: number): Promise<void>;
    batchRemove(ids: number[]): Promise<void>;
    resetPassword(id: number, newPassword: string, currentUserId?: number): Promise<void>;
    updatePassword(id: number, newPassword: string, currentUserId?: number): Promise<void>;
    updateStatus(id: number, status: number, currentUserId?: number): Promise<void>;
    updateLastLoginTime(id: number): Promise<void>;
    validatePassword(user: User, password: string): Promise<boolean>;
    batchUpdateStatus(ids: number[], status: number, currentUserId?: number): Promise<void>;
    batchResetPassword(ids: number[], newPassword: string, currentUserId?: number): Promise<void>;
    assignRoles(userId: number, roleIds: number[], currentUserId?: number): Promise<void>;
    batchAssignRoles(userIds: number[], roleIds: number[], currentUserId?: number): Promise<void>;
    private saveDepartments;
}
