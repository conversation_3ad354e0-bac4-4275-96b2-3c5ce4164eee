# 数据库初始化工具

## 概述

本目录包含系统管理平台的数据库初始化工具，用于创建数据库、表结构和初始数据。

## 文件说明

- `database-init.sql` - 数据库初始化SQL脚本
- `init-database.js` - Node.js数据库初始化工具
- `test-db-connection.js` - 数据库连接测试工具
- `init-database.bat` - Windows批处理脚本
- `init-database.sh` - Linux/Mac Shell脚本
- `setup-database.bat` - 一键安装和初始化脚本
- `database-config.env` - 数据库配置模板
- `package.json` - 依赖管理文件

## 快速开始

### Windows环境（推荐）

1. **一键安装（推荐）**
   ```cmd
   双击运行 setup-database.bat
   ```

2. **分步执行**
   ```cmd
   双击运行 init-database.bat
   ```

### Linux/Mac环境

```bash
# 给脚本执行权限
chmod +x init-database.sh

# 运行脚本
./init-database.sh
```

### 手动执行

```bash
# 安装依赖（在backend目录下）
cd ..
npm install mysql2
cd data

# 设置环境变量
export DB_HOST=localhost
export DB_PORT=3306
export DB_USERNAME=root
export DB_PASSWORD=123456

# 测试连接
node test-db-connection.js

# 初始化数据库（标准版）
node init-database.js

# 如果标准版失败，使用简化版
node init-database-simple.js
```

## 配置说明

### 环境变量配置

- `DB_HOST` - 数据库主机地址（默认：localhost）
- `DB_PORT` - 数据库端口（默认：3306）
- `DB_USERNAME` - 数据库用户名（默认：root）
- `DB_PASSWORD` - 数据库密码

### 配置方式

1. **设置环境变量**（推荐）
2. **修改配置文件** - 复制 `database-config.env` 为 `.env`
3. **修改脚本** - 直接修改 `init-database.js` 中的配置

## 数据库结构

### 核心表
- `users` - 用户表
- `organizations` - 组织机构表
- `positions` - 岗位表
- `roles` - 角色表
- `user_groups` - 用户组表

### 关联表
- `user_roles` - 用户角色关联表
- `user_group_members` - 用户组成员表
- `user_group_roles` - 用户组角色关联表
- `role_functions` - 角色功能关联表

### 系统表
- `applications` - 应用表
- `functions` - 功能表
- `dict_types` - 字典类型表
- `dict_data` - 字典数据表
- `sys_config` - 系统配置表

## 初始数据

### 默认角色
- **超级管理员** (super_admin) - 拥有所有权限
- **系统管理员** (admin) - 拥有大部分管理权限
- **普通用户** (user) - 拥有基础功能权限

### 默认用户
- **用户名**: admin
- **密码**: admin123
- **角色**: 超级管理员

### 默认应用
- **应用名称**: 系统管理中心
- **应用Key**: system_manage

## 常见问题

### 1. 连接失败
```
❌ 数据库初始化失败: connect ECONNREFUSED
```
**解决方案**: 检查MySQL服务是否启动，确认主机地址和端口是否正确

### 2. 权限不足
```
❌ 数据库初始化失败: ER_ACCESS_DENIED_ERROR
```
**解决方案**: 检查数据库用户名和密码是否正确，确认用户有创建数据库的权限

### 3. SQL语法错误
```
❌ You have an error in your SQL syntax
```
**解决方案**: 这通常是由于MySQL版本差异导致的。脚本会自动尝试逐条执行SQL语句来解决此问题

### 4. 依赖包缺失
```
❌ Cannot find module 'mysql2'
```
**解决方案**: 在backend目录下运行 `npm install mysql2`

### 5. 多语句执行问题
如果遇到多语句执行问题，可以运行测试脚本检查：
```bash
node test-init.js
```

## 验证初始化结果

初始化完成后，脚本会显示：

1. ✅ 创建的数据表列表
2. 📊 初始数据统计
3. 👤 默认管理员账号信息

## 安全建议

1. **修改默认密码** - 首次登录后立即修改admin用户密码
2. **创建专用数据库用户** - 不要使用root用户连接应用
3. **设置复杂密码** - 为数据库用户设置强密码
4. **限制网络访问** - 配置防火墙限制数据库访问

## 后续步骤

数据库初始化完成后：

1. 更新后端 `.env` 文件中的数据库配置
2. 启动后端服务
3. 使用默认管理员账号登录系统
4. 根据需要创建其他用户和角色

## 目录结构

```
backend/data/
├── database-init.sql          # 数据库初始化SQL脚本
├── init-database.js           # Node.js初始化工具
├── test-db-connection.js      # 连接测试工具
├── init-database.bat          # Windows批处理脚本
├── init-database.sh           # Linux/Mac Shell脚本
├── setup-database.bat         # 一键安装脚本
├── database-config.env        # 配置模板
├── package.json               # 依赖管理
└── README.md                  # 使用说明
```

## 技术支持

如果在初始化过程中遇到问题，请检查：

1. MySQL服务状态
2. 网络连接
3. 用户权限
4. 配置参数

或查看详细的错误日志进行排查。
