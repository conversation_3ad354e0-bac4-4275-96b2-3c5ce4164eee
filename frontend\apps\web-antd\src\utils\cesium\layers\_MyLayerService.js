import * as Cesium from 'cesium'
import {
	createVNode,h
} from 'vue'
import {
	GISMap
} from '../index'
import {
	eventbus
} from '../../../ztu/eventbus'
import fileSelector from '../../fileSelector.js'
import shp from './shp'
import Drawer from '../mapTools/Drawer'
import readAsText from '../../readAsText'
import Box from '../../../components/JS/box'
import ShapeSetting from './ShapeViewer/components/shapeSetting.vue'
import DrawerProperty from '../../../components/DrawerProperty.vue'
import POIEdit from '../../../components/POIEdit.vue'
import core from "../core"
import utils from '../../utils'
import {
	toRaw
} from '@vue/reactivity'
import * as api from '../../../api/index'
let entityTypes = {
	'point': '点',
	'polyline': '线',
	'polygon': '多边形',
	'rectangle': '矩形',
	'ellipse': '圆'
};


export default class MyLayerService extends eventbus {
	constructor(viewer) {
		super();
		this.viewer = viewer || GISMap.viewer;
		this.data = [{
			title: '本地图层',
			key: 'localLayer',
			children: []
		}, {
			title: '我的兴趣点',
			key: 'myPoi',
			children: []
		}, {
			title: '我的绘图',
			key: 'myDrawing',
			children: []
		}];
		this.drawer = new Drawer(this.viewer);
		this.drawer.on('add', (entity) => {
			this.addEntity(entity);
		})
		this.drawer.on('clear', () => {
			this.removeDrawing();
		})
		this.drawer.doubleClick((entity) => {
			console.log(".......entity", entity)
			this.showproperty(entity._id)
		})
		this.dataSources = [];
		this.#init();
		this.poidataSources = [];
	}
	#init() {

	}
	addShapeLayer() {
		let that = this;

		return new Promise((resolve, reject) => {
			let addDataSource = (dataSource, title) => {
				this.viewer.dataSources.add(dataSource);
				//console.log(dataSource.entities)
				this.viewer.zoomTo(dataSource)
				let key = 'localLayer' + Math.random().toString().substr(2);
				if (!this.data[0].children)
					this.data[0].children = [];
				this.data[0].children.push({
					key: key,
					title: title,
					show: true
				})
				dataSource.name = key;
				this.dataSources.push(dataSource);
				this.emit('change', this.data);
				resolve(dataSource);
			};
			let addGeoJsonDataSource = (data, title) => {
				Cesium.GeoJsonDataSource.load(data, {
					clampToGround: true
				}).then((dataSource) => {
					addDataSource(dataSource, title);
				})
			}
			let addKmlDataSource = (data, title) => {
				Cesium.KmlDataSource.load(data, {}).then((dataSource) => {
					addDataSource(dataSource, title);
				})
			}

			fileSelector.openFile((files) => {
				let fs = [];
				for (let i = 0; i < files.length; i++) {
					let file = files[i];
					if (file.name.toLocaleLowerCase().indexOf('.json') > 0) {
						readAsText(file).then((result) => {
							console.log(result)
							let json = JSON.parse(result);
							addGeoJsonDataSource(json, file.fileName);
						})
					} else if (file.name.toLocaleLowerCase().indexOf('.kmz') > 0 || file.name
						.toLocaleLowerCase().indexOf('.kml') > 0) {
						readAsText(file).then((result) => {
							console.log(result)
							let json = JSON.parse(result);
							addKmlDataSource(json, file.fileName);
						})
					} else
						fs.push(file)
				}
				if (!fs.length) return;
				shp.parseShpFiles(fs, 'gb2312').then((result) => {
					if (!result.crs) {
						//result.crs = {"type":"name","properties":{"name":"urn:ogc:def:crs:EPSG::4490"}};
					}
					addGeoJsonDataSource(result, result.fileName);
				}).catch(err => {
					reject(err)
				})
			}, {
				multiple: true,
				accept: ".dbf,.prj,.shp,.cpg,.json",
			})
		})
	}
	addEntityByJson(json) {
		if (json instanceof Array) {
			json.forEach(j => {
				this.addEntityByJson(j);
			})
			this.viewer.flyTo(this.drawer.entities)
			return;
		}
		let entity = core.json2Entity(json);
		console.log(entity, this.drawer, this.drawer.entities)
		if (entity) {
			this.drawer.drawEntity(entity);
		}
	}
	addPOIByJson(dataSource, json) {
		console.log(".....json", json)
		let entity = core.json2Entity(json);
		if (entity) {
			entity.billboard.width = 24;
			entity.billboard.height = 24;
			//entity.billboard.clampToS3M = true;
			entity.billboard.heightReference = Cesium.HeightReference.CLAMP_TO_GROUND;
			//entity.billboard.disableDepthTestDistance = Number.POSITIVE_INFINITY;
			entity.label.pixelOffset = new Cesium.Cartesian2(0, -34);
			//entity.label.disableDepthTestDistance = Number.POSITIVE_INFINITY;
			entity.label.heightReference = Cesium.HeightReference.CLAMP_TO_GROUND;
			//entity.label.clampToS3M = true;
			dataSource.entities.add(entity);
			entity.objectId = json.id;
			entity.tagType = json.tagType;
			entity.resId = json.resId;
		}
	}
	addEntity(entity) {
		this.data[2].children = this.data[2].children || [];
		let node = this.data[2].children.find(a => a.key == entity._name);
		if (!node) {
			node = {
				key: entity._name,
				title: entityTypes[entity._name] || entity._name,
				children: [],
				type: "shape"
			};
			this.data[2].children.push(node);
		}
		node.children.push({
			key: entity._id,
			title: (entity.label && entity.label.text._value) || entityTypes[entity._name] || entity._name,
			show: true
		})
		console.log(this.data)
		this.emit('change', this.data);
	}
	rebuildData() {
		this.data[2].children.forEach(node => {
			node.children.forEach(node => {
				let entity = this.drawer.entities.find(a => a._id == node.key);
				if (entity)
					node.title = (entity.label && entity.label.text._value) || entityTypes[entity
						._name] || entity._name;
			})
		});
		this.emit('change', this.data);
	}
	find(key) {
		this.data[0].children = this.data[0].children || [];
		this.data[1].children = this.data[1].children || [];
		this.data[2].children = this.data[2].children || [];
		for (let i = 0; i < this.data.length; i++) {
			for (let j = 0; j < this.data[i].children.length; j++) {
				if (key == this.data[i].children[j].key) {
					return {
						i,
						j
					}
				}
				if (i == 2) {
					for (let k = 0; k < this.data[i].children[j].children.length; k++) {
						if (key == this.data[i].children[j].children[k].key) {
							return {
								i,
								j,
								k
							}
						}
					}
				}
			}
		}
		return null;
	}
	edit(key) {
		let node = this.find(key);
		if (!node) return;
		let {
			i,
			j,
			k
		} = node;
		if (i != 2) return;
		let entity = this.viewer.entities.getById(key);
		if (!entity) return;
		Box.closeAll();
		Box.open({
			title: '属性',
		}, [createVNode(ShapeSetting, {
			entity: entity
		})]);
	}
	remove(key) {
		let node = this.find(key);
		if (!node) return;
		let {
			i,
			j,
			k
		} = node;
		switch (i) {
			case 0:
				let ds = this.viewer.dataSources.getByName(key);
				if (ds.length) {
					this.viewer.dataSources.remove(ds[0], true);
				}
				this.dataSources.splice(j, 1)
				this.data[i].children.splice(j, 1);
				break;
			case 1:
				this.data[i].children.splice(j, 1);
				break;
			case 2:
				if (k === undefined) {
					this.data[i].children[j].children.forEach(node => {
						let entity = this.viewer.entities.getById(node.key);
						if (entity) {
							this.drawer.remove(entity)
						}
					})
					this.data[i].children.splice(j, 1);
				} else {
					let entity = this.viewer.entities.getById(key);
					console.log(entity, key)
					if (entity) {
						this.drawer.remove(entity)
					}
					this.data[i].children[j].children.splice(k, 1);
				}

				break;
			default:
				break;
		}
		this.emit('change', this.data)
		Box.closeAll();
	}
	removeDrawing() {
		this.data[2].children = [];
		this.emit('change', this.data);
		this.drawer.clear()
		Box.closeAll();
	}
	show(keys) {
		let time = new Date().getTime()
		this.data[0].children.forEach(data => {
			data.show = keys.includes(data.key);
		})
		this.dataSources.forEach(ds => {
			ds.show = keys.includes(ds.name);
		})
		this.data[2].children.forEach(node => {
			node.children.forEach(data => {
				data.show = keys.includes(data.key);
			})
		})
		this.drawer.entities.forEach(entity => {
			entity.show = keys.includes(entity._id);
		})
		//this.data[1].show=keys.includes(this.data[1].key);
		this.data[1].children.forEach(data => {
			data.show = keys.includes(data.key);
		})
		this.poidataSources.forEach(ds => {
			ds.dataSource.show = keys.includes(ds.dataSource.name + '_' + ds.type);
			if (ds.dataSource.show && !ds.isLoaded) {
				this.loadPOIs(ds.type);
			}
		})

		this.emit('change', this.data);
		Box.closeAll();
		console.log(new Date().getTime() - time)
	}
	 showAndZoomToLayer(key) {
		this.drawer.entities.forEach(entity => {
			console.log("......._id", entity._id)
			if (entity._id == key) {
				console.log("...........entity", entity)
				var nowentity = toRaw(entity)

				console.log("...........nowentity", nowentity, entity)
				this.viewer.flyTo(nowentity)
				return
			}

		})
	}
	exportJson() {
		if (this.drawer.entities.length) {
			let json = this.drawer.toJson();
			utils.saveAs(JSON.stringify(json), 'export.json', '.json');
		} else {
			Box.info('提示', '绘图数据为空，无需导出')
		}
	}
	showproperty(key) {
		// let node = this.find(key);
		// if(!node) return;
		// let {i,j,k} = node;
		// if(i!=2) return;
		if (key) {
			let entity = this.viewer.entities.getById(key);
			if (!entity) return;
			Box.closeAll();
			Box.open({
				title: '属性',
			}, [createVNode(DrawerProperty, {
				entity: entity
			})]);
		}
	}
	async loadPOITypes() {
		let typeList = await api.getUserVisibleTypeList();
		typeList.sort();
		typeList.forEach((type, index) => {
			let idx = this.poidataSources.findIndex(a => a.type == type);
			if (idx == -1) {
				let ds = new Cesium.CustomDataSource();
				ds.name = "poi_layer";
				ds.show = false;
				this.viewer.dataSources.add(ds);
				this.poidataSources.push({
					type: type,
					dataSource: ds,
					isLoaded: false
				})
				this.data[1].children.push({
					key: ds.name + '_' + type,
					title: type,
					show: false
				})
			}
		})
		this.emit('change', this.data);
	}
	async loadPOIs(poiType, reload = false) {
		let idx = this.poidataSources.findIndex(a => a.type == poiType);
		if (idx == -1) return;
		let poids = this.poidataSources[idx];
		let ds = poids.dataSource;
		if (!reload && poids.isLoaded) return;
		ds.entities.removeAll();
		let res = await api.getLayerTag({
			tagType: poiType
		});
		res && res.forEach(item => {
			let entityjson = {
				id: item.id,
				name: item.name,
				tagType: item.tagType || "",
				position: Cesium.Cartesian3.fromDegrees(item.longitude, item.latitude),
				billboard: {
					image: item.resUrl,
					//width:24,
					//height:24
				},
				label: {
					fillColor: item.nameColor || "",
					font: (item.nameSize || 14)+'px sans-serif',
					text: item.name,
					//pixelOffset: new Cesium.Cartesian2(0, -34),
				},
				description: item.remark,
				resId: item.resId,
			}
			this.addPOIByJson(ds, entityjson)
		})
		poids.isLoaded = true;
		if(reload){
			poids.show=true;
			poids.dataSource.show = true;
			this.data[1].children[idx].show=true;
			this.emit('change',this.data)
		}
	}
	deletePOI(id) {
		let entity = null;
		for (let i = 0; i < this.poidataSources.length; i++) {
			entity = this.poidataSources[i].dataSource.entities.values.find(a => a.objectId == id);
			if (entity) {
				this.poidataSources[i].dataSource.entities.remove(entity);
				break;
			}
		}
	}
	movePOI(row){
		let entity = null;
		let id= row.id;
		for (let i = 0; i < this.poidataSources.length; i++) {
			entity = this.poidataSources[i].dataSource.entities.values.find(a => a.objectId == id);
			if (entity) {
				break;
			}
		}
		if(entity){
			let d =null
			let viewer = GISMap.viewer
			d= new Drawer(viewer);
			d.drawStart('marker',null,(newentity) =>{
				let loca = newentity.position.getValue()
				d.remove(newentity);
				loca && (entity.position._value = loca);
				loca = core.transformCartesianToWGS84(loca)

				let poiparams = {
					id:id,
					latitude:loca.lat|| 0,
					longitude:loca.lng || 0,
					name:row.name,
					nameColor:row.nameColor,
					nameSize:row.nameSize,
					remark:row.remark,
					resId: row.resId,
					tagType:row.tagType,
				}
				api.editLayerTag(poiparams);
			});
		}
	}
	editPOI(id) {
		let entity = null;
		for (let i = 0; i < this.poidataSources.length; i++) {
			entity = this.poidataSources[i].dataSource.entities.values.find(a => a.objectId == id);
			if (entity) {
				break;
			}
		}
		console.log('editPOI',id,entity)
		if (entity) {
			Box.closeAll()
			Box.open({
				moverect: {
					left: 50,
					top: 64,
					right: 0,
					bottom: 0
				},
				style: {
					right: '100px',
					top: '140px',
					left: "unset"
				},
				title: '属性',
			}, [h(POIEdit, {
				entity: entity,
				type: 'billboard'
			})])
		}
	}

}
