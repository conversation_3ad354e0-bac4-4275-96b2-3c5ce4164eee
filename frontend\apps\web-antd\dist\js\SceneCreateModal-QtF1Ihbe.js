var Q=Object.defineProperty;var N=Object.getOwnPropertySymbols;var W=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable;var U=(i,a,e)=>a in i?Q(i,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[a]=e,A=(i,a)=>{for(var e in a||(a={}))W.call(a,e)&&U(i,e,a[e]);if(N)for(var e of N(a))X.call(a,e)&&U(i,e,a[e]);return i};var y=(i,a,e)=>new Promise((w,C)=>{var S=l=>{try{o(e.next(l))}catch(m){C(m)}},n=l=>{try{o(e.throw(l))}catch(m){C(m)}},o=l=>l.done?w(l.value):Promise.resolve(l.value).then(S,n);o((e=e.apply(i,a)).next())});import{v as Y,a as Z}from"./bootstrap-5OPUVRWy.js";import{u as M}from"./form-DdFfsSWf.js";import{f as ee,u as te}from"./scene.data-BMXeOdST.js";import{e as se,s as ae,u as oe}from"./scene.api-DEW02Ykq.js";import{s as E,a as D}from"./toast-CQjPPeQ1.js";import{u as le,a as ie,s as re}from"./fileUpload-DI0dJ9zY.js";import{s as ne}from"./alert-DJKWbMfG.js";import{d as de,r as p,c as ue,k as h,j as _,b as v,q as j,f,e as k,a as I,H as ce,v as fe,I as pe}from"../jse/index-index-DyHD_jbN.js";import{u as me}from"./use-modal-uChFuhJy.js";import"./loading-DzjUKA94.js";const be={style:{"padding-left":"20px","padding-right":"20px","padding-top":"20px"}},ge=["directory","webkitdirectory"],ye={key:0,for:"fileInput",onclick:"triggerFileInput",class:"custom-label bg-gray-200 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400"},ve={key:1,for:"fileInput",onclick:"triggerFileInput",class:"custom-label bg-primary-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400"},he={key:2,style:{width:"900px"}},we=de({__name:"SceneCreateModal",emits:["register","success"],setup(i,{emit:a}){const e=a;let w=null;const C=()=>{B.value=1,u.value=!1,r.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}}),g.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}})},[S,n]=me({destroyOnClose:!0,closeOnClickModal:!1,onCancel(){n.close()},onClosed(){C()},onBeforeClose(){return u.value?(ne("提示","关闭窗口将停止上传，是否确定关闭窗口？",()=>{re(),u.value=!1,n.close(),e("success")}),!1):!0},onConfirm(){},onOpenChange(t){if(t){r.resetForm();const s=n.getData();console.log(s),l.value=o.value&&o.value.update,m.value=o.value&&o.value.upload,P=o.value&&o.value.category,b=o.value&&o.value.row&&o.value.row.dataType;let c={};l.value&&(c=A({},o.value.row)),w=c.id,c.update=l.value,c.upload=m.value,r.setValues(c)}}}),o=n.useStore();p({id:"",datasetName:""});let l=p(!1),m=p(!1),P="",b="";const[z,r]=M({commonConfig:{componentProps:{class:"w-full"},labelClass:"w-2/8"},handleSubmit:L,handleReset:T,layout:"horizontal",schema:ee,wrapperClass:"grid-cols-1",submitButtonOptions:{content:"下一步"}}),[R,g]=M({commonConfig:{componentProps:{class:"w-full"},labelClass:"w-2/8"},handleSubmit:H,handleReset:T,layout:"horizontal",schema:te,wrapperClass:"grid-cols-1",resetButtonOptions:{show:!1}});let d=null;p([]);const x=p("0"),u=p(!1),B=p(1);let V="";const q=ue(()=>h(l)?"编辑":"新增");function T(t){return y(this,null,function*(){let s=t.id;yield r.resetForm(),yield r.setFieldValue("id",s),yield r.setFieldValue("dataType",b)})}function H(t){return y(this,null,function*(){if(u.value=!0,g.setState({commonConfig:{disabled:!0},submitButtonOptions:{disabled:!0},resetButtonOptions:{disabled:!0}}),d.files==null||d.files.length==0){F&&F();return}let s=K=>y(null,null,function*(){try{E("文件上传成功，解析文件中..."),yield oe({id:V,fileId:K,dataType:t.dataType}),F&&F()}catch(Ce){O&&O()}}),c=t.fileFormat;x.value==="0"?yield le(d,s,b,c,O):yield ie(d,s,b)})}function L(t){return y(this,null,function*(){try{t.category=P,b=t.dataType,r.setState({commonConfig:{disabled:!0},submitButtonOptions:{disabled:!0},resetButtonOptions:{disabled:!0}}),E("操作处理中...");try{let s=l.value?yield se(t,w):yield ae(t);D("创建数据集完成，请上传数据!"),G(s)}catch(s){$()}}finally{}})}const F=()=>{u.value=!1,g.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}}),n.close(),D("操作成功！"),e("success")},O=()=>{u.value=!1,g.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}}),n.close(),e("success")},$=()=>{r.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}})},G=t=>y(null,null,function*(){V=t,B.value=2,r.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}}),e("success"),yield g.setFieldValue("dataType",b)}),J=t=>{console.log(t),d=t.target;const s=document.getElementById("fileName");s.textContent=d.files.length>0?"已选"+d.files.length+"个文件":"没有选择文件",g.setFieldValue("fileUpload",d.files)};return(t,s)=>(v(),_(h(S),pe(t.$attrs,{footer:!1,title:q.value,class:"w-[800px]","destroy-on-close":!0,maskClosable:!1}),{default:j(()=>[f("div",be,[B.value===1?(v(),_(h(z),{key:0})):k("",!0),B.value===2?(v(),_(h(R),{key:1},{fileUpload:j(c=>[ce(f("input",{class:"hidden",id:"fileInput",type:"file",directory:x.value==="0",webkitdirectory:x.value==="0",onChange:J},null,40,ge),[[Y,!h(l)]]),u.value?(v(),I("label",ye," 选择文件 ")):(v(),I("label",ve," 选择文件 ")),s[0]||(s[0]=f("span",{id:"fileName",class:"ml-4"},"没有选择文件",-1))]),_:1})):k("",!0),u.value?(v(),I("view",he,s[1]||(s[1]=[f("div",{id:"folderProgressArea",class:"folderProgressArea"},[fe(" 文件夹总进度："),f("div",{id:"folderProgress",class:"folderProgress"},"0%")],-1),f("div",{style:{display:"flex","flex-direction":"column","justify-content":"flex-start","align-items":"flex-start"}},[f("div",{style:{"margin-bottom":"5px"}},"当前文件进度："),f("div",{id:"fileProgress",class:"fileProgress bg-primary text-white"})],-1)]))):k("",!0)])]),_:1},16,["title"]))}}),Ne=Z(we,[["__scopeId","data-v-d4f81e27"]]);export{Ne as default};
