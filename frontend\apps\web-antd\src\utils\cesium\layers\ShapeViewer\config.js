import * as Cesium from 'cesium'
export default {
	pointStyle:{
		color: Cesium.Color.RED,
		pixelSize: 10,
		outlineColor: Cesium.Color.BLACK,
		outlineWidth: 0,
		show: true,
		disableDepthTestDistance: 5000000,
		//distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.1, 25000.0)
	},
	rectangleStyle:{
		material: Cesium.Color.GREEN.withAlpha(0.5),
		disableDepthTestDistance: 5000000,
	},
	circleStyle:{
		material: Cesium.Color.GREEN.withAlpha(0.5),
		disableDepthTestDistance: 5000000,
		clampToGround:true
	},
	polygonStyle:{
		disableDepthTestDistance: 5000000,
	},
	polylineStyle:{
		disableDepthTestDistance: 5000000,
		material: Cesium.Color.GREEN,
		depthFailMaterial:new Cesium.PolylineDashMaterialProperty({
			color: Cesium.Color.GREEN
		})
	},
	textStyle:{
		text:'我在这儿',
		/* verticalOrigin: Cesium.VerticalOrigin.TOP,
		horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
		pixelOffset: new Cesium.Cartesian2(5, 5),
		showBackground: true, //指定标签后面背景的可见性
		backgroundColor: new Cesium.Color(0.165, 0.165, 0.165, 0.8), // 背景颜色
		backgroundPadding: new Cesium.Cartesian2(6, 6), //指定以像素为单位的水平和垂直背景填充padding */
		disableDepthTestDistance: 5000000,
		//heightReference:Cesium.HeightReference.CLAMP_TO_GROUND,
		distanceDisplayCondition:new Cesium.DistanceDisplayCondition(null, 5000000)
	},
	modelStyle:{
		uri: '/assets/models/Cesium_Man.glb',   // 模型路径，自己换成自己的模型
		scale: 1000,
	}
}

