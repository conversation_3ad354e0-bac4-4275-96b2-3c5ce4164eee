import {
  config,
  setup,
  ui_default,
  version
} from "./chunk-VRANVM3Q.js";
import {
  GLOBAL_EVENT_KEYS,
  VxeCore,
  VxeUI,
  clipboard,
  commands,
  component,
  coreVersion,
  createEvent,
  formats,
  getComponent,
  getConfig,
  getI18n,
  getIcon,
  getLanguage,
  getTheme,
  globalEvents,
  globalResize,
  globalStore,
  handleCheckInfo,
  hasLanguage,
  hooks,
  interceptor,
  log,
  menus,
  permission,
  renderEmptyElement,
  renderer,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  use,
  useFns,
  usePermission,
  useSize,
  validators
} from "./chunk-DULHHPCE.js";
import "./chunk-3X4K7UGJ.js";
import "./chunk-5XE5RK6E.js";
import "./chunk-OWQEP5NU.js";
import "./chunk-YHD4RJOZ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-ui/index.js
var vxe_ui_default = ui_default;
export {
  GLOBAL_EVENT_KEYS,
  VxeCore,
  VxeUI,
  clipboard,
  commands,
  component,
  config,
  coreVersion,
  createEvent,
  vxe_ui_default as default,
  formats,
  getComponent,
  getConfig,
  getI18n,
  getIcon,
  getLanguage,
  getTheme,
  globalEvents,
  globalResize,
  globalStore,
  handleCheckInfo,
  hasLanguage,
  hooks,
  interceptor,
  log,
  menus,
  permission,
  renderEmptyElement,
  renderer,
  setConfig,
  setI18n,
  setIcon,
  setLanguage,
  setTheme,
  setup,
  use,
  useFns,
  usePermission,
  useSize,
  validators,
  version
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-ui_index__js.js.map
