import shp from 'shpjs/dist/shp';
import readAsText from '../../readAsText';
import readAsArrayBuffer from '../../readAsArrayBuffer.js';
shp.parseShpFiles = function (files, encoding = 'UTF-16LE') {
  //gbk
  return new Promise((resolve, reject) => {
    if (!files || files.length > 0) {
      var shpFile, dbfFile, prjFile, cpgFile;
      for (var i = 0; i < files.length; i++) {
        if (files[i].name.toLocaleLowerCase().indexOf('.shp') > 0) {
          shpFile = files[i];
        }
        if (files[i].name.toLocaleLowerCase().indexOf('.prj') > 0) {
          prjFile = files[i];
        }
        if (files[i].name.toLocaleLowerCase().indexOf('.dbf') > 0) {
          dbfFile = files[i];
        }
        if (files[i].name.toLocaleLowerCase().indexOf('.cpg') > 0) {
          cpgFile = files[i];
        }
      }
      if (!shpFile || !prjFile || !dbfFile || !cpgFile) {
        throw new Error(
          '打开文件失败,请通过ctrl+同时选择shp、prj、dbf、cpg四个文件',
        );
        //reject(new Error("打开文件失败,请通过ctrl+同时选择shp、prj、dbf三个文件"));
      }
      readAsArrayBuffer(shpFile)
        .then(function (shpBuffer) {
          readAsText(prjFile)
            .then(function (prjBuffer) {
              readAsArrayBuffer(dbfFile)
                .then(function (dbfBuffer) {
                  let combine = (cpg) => {
                    var parsed = shp.combine([
                      shp.parseShp(
                        shpBuffer,
                        prjBuffer.replace('Gauss_Kruger', 'tmerc'),
                        //proj4('+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees',prjBuffer)
                      ),
                      shp.parseDbf(dbfBuffer, cpg || encoding),
                    ]);
                    //console.log(parsed)
                    parsed.fileName = shpFile.name.toLocaleLowerCase();
                    resolve(parsed);
                  };
                  if (cpgFile) {
                    readAsText(cpgFile).then(function (cpg) {
                      combine(cpg);
                    });
                  } else {
                    combine(encoding);
                  }
                })
                .catch(function (err) {
                  reject(err);
                });
            })
            .catch(function (err) {
              reject(err);
            });
        })
        .catch(function (err) {
          reject(err);
        });
    } else {
      throw new Error('文件列表不能为空');
    }
  });
};

export default shp;
