import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class LoginDto {
  @ApiProperty({ description: '账号（用户名或手机号）', example: '***********' })
  @IsNotEmpty({ message: '账号不能为空' })
  @IsString({ message: '账号必须是字符串' })
  account: string;

  @ApiProperty({ description: '密码', example: '8888a8888#@' })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码必须是字符串' })
  password: string;

  @ApiProperty({ description: '验证码验证（已禁用）', example: true, required: false })
  @IsOptional()
  captcha?: any; // 接受任何类型，但不进行验证
}

export class LoginResponseDto {
  @ApiProperty({ description: '角色类型' })
  roleType: number;

  @ApiProperty({ description: '用户姓名' })
  name: string;

  @ApiProperty({ description: '访问令牌' })
  token: string;

  @ApiProperty({ description: '账号' })
  account: string;
}
