import{a_ as n,a$ as l,b0 as d,b1 as m,b2 as f,b3 as _,b4 as p,b5 as g,b6 as v,i as x,k as b,S as z,j as I}from"./bootstrap-DShsrVit.js";import{_ as o}from"./icon-picker.vue_vue_type_script_setup_true_lang-LdsWWE0x.js";import{C as i}from"./index-B_b7xM74.js";import{_ as h}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as u,af as S,ag as y,ah as e,a3 as a,ap as t,an as r,n as s}from"../jse/index-index-BMh_AyeW.js";import"./empty-icon-Crs2MoG4.js";import"./icon.vue_vue_type_script_setup_true_lang-BK5optdP.js";import"./popover.vue_vue_type_script_setup_true_lang-CFXFI0jw.js";const C={class:"flex items-center gap-5"},M={class:"flex items-center gap-5"},k={class:"flex items-center gap-5"},w={class:"flex items-center gap-5"},F=u({__name:"index",setup(A){return(B,c)=>(S(),y(a(h),{title:"图标"},{description:e(()=>c[0]||(c[0]=[t("div",{class:"text-foreground/80 mt-2"},[r(" 图标可在 "),t("a",{class:"text-primary",href:"https://icon-sets.iconify.design/",target:"_blank"}," Iconify "),r(" 中查找，支持多种图标库，如 Material Design, Font Awesome, Jam Icons 等。 ")],-1)])),default:e(()=>[s(a(i),{class:"mb-5",title:"Iconify"},{default:e(()=>[t("div",C,[s(a(n),{class:"size-8"}),s(a(l),{class:"size-8 text-red-500"}),s(a(d),{class:"size-8 text-green-500"}),s(a(m),{class:"size-8"}),s(a(f),{class:"size-8"})])]),_:1}),s(a(i),{class:"mb-5",title:"Svg Icons"},{default:e(()=>[t("div",M,[s(a(_),{class:"size-8"}),s(a(p),{class:"size-8 text-red-500"}),s(a(g),{class:"size-8 text-green-500"}),s(a(v),{class:"size-8"}),s(a(x),{class:"size-8"}),s(a(b),{class:"size-8"}),s(a(z),{class:"size-8"}),s(a(I),{class:"size-8"})])]),_:1}),s(a(i),{class:"mb-5",title:"图标选择器(Iconify)"},{default:e(()=>[t("div",k,[s(o,{width:"300px"})])]),_:1}),s(a(i),{title:"图标选择器(Svg)"},{default:e(()=>[t("div",w,[s(o,{prefix:"svg",width:"300px"})])]),_:1})]),_:1}))}});export{F as default};
