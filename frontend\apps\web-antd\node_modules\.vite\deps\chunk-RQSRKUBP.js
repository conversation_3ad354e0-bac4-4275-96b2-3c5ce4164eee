import {
  select_default
} from "./chunk-GOG5GJNE.js";
import {
  dynamicApp
} from "./chunk-VRANVM3Q.js";
import {
  VxeUI
} from "./chunk-DULHHPCE.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/select/index.js
var VxeSelect = Object.assign(select_default, {
  install: function(app) {
    app.component(select_default.name, select_default);
  }
});
dynamicApp.use(VxeSelect);
VxeUI.component(select_default);
var Select = VxeSelect;
var select_default2 = VxeSelect;

export {
  VxeSelect,
  Select,
  select_default2 as select_default
};
//# sourceMappingURL=chunk-RQSRKUBP.js.map
