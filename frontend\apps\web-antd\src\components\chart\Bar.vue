<script lang="ts">
import { defineComponent, reactive, ref, watchEffect } from 'vue';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

import { cloneDeep } from 'lodash-es';

export default defineComponent({
  name: 'Bar',
  components: { EchartsUI },
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
    option: {
      type: Object,
      default: () => ({}),
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: 'calc(100vh - 78px)',
    },
    // update-begin--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
    seriesColor: {
      type: String,
      default: '#1890ff',
    },
    // update-end--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
  },
  setup(props) {
    const chartRef = ref<EchartsUIType>();
    const { renderEcharts } = useEcharts(chartRef);
    const option = reactive({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          label: {
            show: true,
            backgroundColor: '#333',
          },
        },
      },
      grid: {
        top: '5%', // 设置图表绘制区域距离容器顶部的距离为容器高度的10%
        // 其他可选属性：left, right, bottom, width, height, containLabel
      },
      legend: {
        // 底部分类标签
        show: true,
        bottom: 0,
      },
      xAxis: {
        type: 'category',
        data: [],
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: 'bar',
          type: 'bar',
          data: [],
          color: props.seriesColor,
        },
      ],
    });

    watchEffect(() => {
      props.chartData && initCharts();
    });

    function initCharts() {
      if (props.option) {
        Object.assign(option, cloneDeep(props.option));
      }
      if (props.chartData.seriesData) {
        option.series =
          props.chartData.seriesData &&
          props.chartData.seriesData.map(
            (item: { data: number[]; name: string }) => ({
              name: item.name,
              type: 'bar',
              data: item.data,
            }),
          );
        option.xAxis.data = props.chartData.xAxisData;
        option.xAxis.axisLabel =
          props.chartData.xAxisLabel && props.chartData.xAxisLabel;
        renderEcharts(option);
      } else {
        const seriesData = props.chartData.map((item) => {
          return item.value;
        });
        const xAxisData = props.chartData.map((item) => {
          return item.name;
        });
        option.series[0].data = seriesData;
        // update-begin--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
        option.series[0].color = props.seriesColor;
        // update-end--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
        option.xAxis.data = xAxisData;
        renderEcharts(option);
      }
    }
    return { chartRef };
  },
});
</script>
<template>
  <EchartsUI ref="chartRef" />
</template>
