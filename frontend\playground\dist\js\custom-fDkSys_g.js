import{u as f}from"./form-DnT3S1ma.js";import{by as m,aH as t}from"./bootstrap-DShsrVit.js";import{C as p}from"./index-B_b7xM74.js";import{_ as c}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as u,h as s,af as d,ag as _,ah as o,a3 as e,n as a,ac as b}from"../jse/index-index-BMh_AyeW.js";const y=u({__name:"custom",setup(h){const[n]=f({commonConfig:{componentProps:{class:"w-full"},labelClass:"w-2/6"},handleSubmit:r,layout:"horizontal",schema:[{component:"Input",fieldName:"field",label:"自定义后缀",suffix:()=>s("span",{class:"text-red-600"},"元")},{component:"Input",fieldName:"field1",label:"自定义组件slot",renderComponentContent:()=>({prefix:()=>"prefix",suffix:()=>"suffix"})},{component:s(t,{placeholder:"请输入"}),fieldName:"field2",label:"自定义组件",rules:"required"},{component:"Input",fieldName:"field3",label:"自定义组件(slot)",rules:"required"}],wrapperClass:"grid-cols-1 md:grid-cols-2"});function r(l){m.success({content:`form values: ${JSON.stringify(l)}`})}return(l,x)=>(d(),_(e(c),{description:"表单组件自定义示例",title:"表单组件"},{default:o(()=>[a(e(p),{title:"基础示例"},{default:o(()=>[a(e(n),null,{field3:o(i=>[a(e(t),b({placeholder:"请输入"},i),null,16)]),_:1})]),_:1})]),_:1}))}});export{y as default};
