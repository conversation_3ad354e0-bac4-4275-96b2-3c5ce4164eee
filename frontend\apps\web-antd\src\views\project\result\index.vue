<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { Page } from '@vben/common-ui';
import { onMounted, ref } from 'vue';

import { useVbenModal, VbenButton, type VbenFormProps } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import ProjectCreateModal from './components/ProjectResultCreateModal.vue';
// import ProjectUpdateModal from './components/ProjectResultUpdateModal.vue'; // 移除此行
import ProjectDetailModal from './components/ProjectResultViewModal.vue';
import ProjectMaterialModal from './components/ProjectMaterialModal.vue'; // 新增：引入项目资料模态框组件
import { listResult, saveResult,deleteOneResult } from '#/views/project/project.api';
import { columns, searchFormSchema } from './result.data.js';
import { showConform } from '#/utils/alert.js';

const [CreateModal, createModalApi] = useVbenModal({
  connectedComponent: ProjectCreateModal,
  fullscreenButton: false,
  destroyOnClose: true,
  key: 'uniqueModalKey',
  onClosed: () => {
    gridApi.query();
  },
});

// 移除 UpdateModal 的定义
// const [UpdateModal, updateModalApi] = useVbenModal({
//   connectedComponent: ProjectUpdateModal,
//   fullscreenButton: false,
//   destroyOnClose: true,
//   onClosed: () => {
//     gridApi.query();
//   },
// });

const [DetailModal, detailModalApi] = useVbenModal({
  connectedComponent: ProjectDetailModal,
  fullscreenButton: false,
  destroyOnClose: true,
});

// 新增：项目资料模态框定义
const [MaterialModal, materialModalApi] = useVbenModal({
  connectedComponent: ProjectMaterialModal,
  fullscreenButton: false,
  destroyOnClose: true,
});

const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchFormSchema,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  },
  submitOnEnter: false,
};

const gridOptions: VxeGridProps = {
  columns,
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let search = { ...formValues};
        let regionCode = search.regionCode;
        if(regionCode){
          let regionProvinceCode = regionCode[0];
          let regionCityCode = regionCode[1];
          let regionCountyCode = regionCode[2];

          search.regionProvinceCode = regionProvinceCode;
          search.regionCityCode = regionCityCode;
          search.regionCountyCode = regionCountyCode;

          delete search.regionCode;
        }

        const data = await listResult({
          page: {
            current: page.currentPage,
            size: page.pageSize,
            searchCount: true,
          },
          condition: search,
        });
        return {
          total: data.total,
          items: data.records,
        };
      },
    },
  },
  rowConfig: {
    isHover: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const currentRow = ref(null);

const handleAdd = () => {
  createModalApi.setData({ update: false });
  createModalApi.open();
};

function handleEdit(row) {
  createModalApi.setData({ row, update: true }); // 修改为调用 createModalApi
  createModalApi.open();
}

const onCancelDetailModal = () => {
  detailModalApi.close();
};

async function handleDelete(record) {
  showConform('提示', '确定要删除？', async () => {
    await deleteOneResult(record.id);
    gridApi.reload()
  });
}

const handleView = (row) => {
  currentRow.value = row;
  detailModalApi.setData({ row });
  detailModalApi.open();
};

// 新增：处理项目资料查看的方法
const handleMaterialView = (row) => {
  currentRow.value = row;
  materialModalApi.setData({ row });
  materialModalApi.open();
};

onMounted(() => {});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-tools>
        <VbenButton pre-icon="ant-design:plus-outlined" type="primary" @click="handleAdd">
          新建项目成果
        </VbenButton>
      </template>

      <template #organizationalAcceptance="{ row }">
        <view>{{ row.organizationalAcceptance === 1 ? '是' : '否' }}</view>
      </template>

      <template #regionName="{ row }">
        <view>{{ row.regionProvinceName }}{{ row.regionCityName }}{{ row.regionCountyName }}</view>
      </template>

      <template #projectNature="{ row }">
        <view>{{ row.projectNature === 0 ? '内外业均有' : row.projectNature === 1 ? '外业项目' : row.projectNature === 2 ? '内业项目' : ''}}</view>
      </template>


      <template #action="{ row }">
        <div class="actionBar">
          <a-button class="actionButton" type="link" @click="handleEdit(row)"> 编辑 </a-button>
          <a-button class="actionButton" type="link" @click="handleDelete(row)"> 删除 </a-button>
          <a-button class="actionButton" type="link" @click="handleView(row)"> 查看 </a-button>
          <a-button class="actionButton" type="link" @click="handleMaterialView(row)"> 项目资料 </a-button> <!-- 新增：项目资料按钮 -->
        </div>
      </template>
    </Grid>

    <CreateModal @success="gridApi.reload()" />
    <!-- 移除 UpdateModal 的使用 -->
    <!-- <UpdateModal :row="currentRow" @success="gridApi.reload()" /> -->
    <DetailModal :row="currentRow" @on-cancel="onCancelDetailModal" />
    <MaterialModal :row="currentRow" /> <!-- 新增：项目资料模态框组件 -->
  </Page>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';

.actionButton {
  padding: 6px;
  color:hsl(var(--primary));
}

.actionButton:hover {
  color:hsl(var(--primary));
}


.actionBar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
</style>
