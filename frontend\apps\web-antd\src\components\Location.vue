<script>
import { h } from 'vue';

import * as Cesium from 'cesium';

import VueEvent from '../config/event.js';
import { getGisMap, GISMap } from '../utils/cesium';
import core from '../utils/cesium/core.js';
import Box from './JS/box';

let handler = null;
export default {
  data() {
    return {
      lng: 0,
      lat: 0,
      h: 0,
      ch: 0,
      type: 0,
    };
  },
  computed: {
    longitude() {
      if (this.type == 0) {
        return this.lng;
      }
      const d = Math.floor(this.lng);
      let f = (this.lng - d) * 60;
      let m = (f - Math.floor(f)) * 60;
      f = `0${Math.floor(f)}`.slice(-2);
      m = `0${m.toFixed(2)}`.slice(-5);
      return `${d}°${f}′${m}″`;
    },
    latitude() {
      if (this.type == 0) {
        return this.lat;
      }
      const d = Math.floor(this.lat);
      let f = (this.lat - d) * 60;
      let m = (f - Math.floor(f)) * 60;
      f = `0${Math.floor(f)}`.slice(-2);
      m = `0${m.toFixed(2)}`.slice(-5);
      return `${d}°${f}′${m}″`;
    },
    height() {
      if (this.h > 1000) return `${(this.h / 1000).toFixed(2)}km`;
      // console.log(this.h)
      return `${this.h}m`;
    },
    cameraHeight() {
      if (this.ch > 1000) return `${(this.ch / 1000).toFixed(2)}km`;
      return `${this.ch}m`;
    },
  },
  mounted() {
    getGisMap((GISMap) => {
      const viewer = GISMap.viewer;
      handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
      const i = 0;
      handler.setInputAction((movement) => {
        // let ray = viewer.camera.getPickRay(movement.endPosition);
        // let cartesian = viewer.scene.globe.pick(ray, viewer.scene);

        const cartesian = core.getCatesian3FromPX(viewer, movement.endPosition);
        if (!cartesian) {
          return false;
        }
        // console.log(cartesian)
        const wgs84 = core.transformCartesianToWGS84(cartesian);
        this.lng = wgs84.lng.toFixed(6);
        this.lat = wgs84.lat.toFixed(6);
        this.h = wgs84.alt.toFixed(2);
        // let position = Cesium.Ellipsoid.WGS84.cartesianToCartographic(cartesian)
        // this.lng = (position.longitude/Math.PI*180).toFixed(6);
        // this.lat = (position.latitude/Math.PI*180).toFixed(6);
        // this.h = position.height.toFixed(2);
        this.ch = viewer.camera.positionCartographic.clone().height.toFixed(2);
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      viewer.scene.camera.changed.addEventListener(() => {
        this.ch = viewer.camera.positionCartographic.clone().height.toFixed(2);
      });
      this.ch = viewer.camera.positionCartographic.clone().height.toFixed(2);
      VueEvent.on('getlocation', this.pickLocation);
    });
  },
  unmounted() {
    VueEvent.off('getlocation', this.pickLocation);
    this.pickLocationEnd();
    if (handler && !handler.isDestroyed()) {
      handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      handler = handler && handler.destroy();
    }
    handler = null;
  },
  methods: {
    copy(text) {
      // var text = text || `经度:${this.longitude} 纬度:${this.latitude} 高度:${this.height} 相机高度:${this.cameraHeight}`;
      var text =
        text ||
        `经度:${this.longitude} 纬度:${this.latitude} 高度:${this.height}`;
      const input = document.createElement('input');
      document.body.append(input);
      input.setAttribute('value', text);
      input.select();
      document.execCommand('copy'); // 执行浏览器复制命令
      if (document.execCommand('copy')) {
        document.execCommand('copy');
        console.log(text);
        alert(`${text}\n已复制好，可贴粘。`);
      }
      input.remove();
    },
    pickLocation() {
      const viewer = GISMap.viewer;
      const that = this;
      // let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
      let node = null;
      handler.setInputAction((movement) => {
        // pickLocationEnd();
        // this.copy()
        // var text = `经度:${this.longitude} 纬度:${this.latitude} 高度:${this.height} 相机高度:${this.cameraHeight}`;
        const text = `经度:${this.longitude} 纬度:${this.latitude} 高度:${this.height}`;
        node = Box.open(
          {
            title: '拾取坐标',
            beforeClose: () => {
              that.pickLocationEnd();
            },
          },
          [
            h(
              'div',
              {
                style: {
                  color: '#fff',
                  padding: '10px',
                },
              },
              [
                h('div', {}, `经度:${this.longitude}`),
                h('div', {}, `纬度:${this.latitude}`),
                h('div', {}, `高度:${this.height}`),
                // h('div',{},`相机高度:${this.cameraHeight}`)
              ],
            ),
            h(
              'div',
              {
                style: {
                  'text-align': 'center',
                  'border-top': '1px solid rgba(40, 44, 52, 0.8)',
                  padding: '5px',
                },
              },
              [
                h(
                  'button',
                  {
                    style: {
                      padding: '2px 20px',
                    },
                    onclick: () => {
                      this.copy(text);
                      Box.close(node);
                    },
                  },
                  '复制',
                ),
              ],
            ),
          ],
          node,
        );
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      handler.setInputAction((movement) => {
        that.pickLocationEnd();
      }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      viewer._element.style.cursor = 'crosshair';
      viewer.enableCursorStyle = true;
    },
    pickLocationEnd() {
      if (!handler) return;
      const viewer = GISMap.viewer;
      handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
      handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
      viewer._element.style.cursor = 'default';
      viewer.enableCursorStyle = false;
    },
  },
};
</script>

<template>
  <div class="location-container" @click="type = (type + 1) % 2">
    <span>经度:{{ longitude }}</span>
    <span>纬度:{{ latitude }}</span>
    <span>高度:{{ height }}</span>
    <span>相机高度:{{ cameraHeight }}</span>
    <!-- <a @click.stop="pickLocation" title="地图上拾取坐标">拾取</a> -->
  </div>
</template>

<style lang="less" scoped>
.location-container {
  position: absolute;
  left: 20px;
  bottom: 20px;
  cursor: pointer;
  span,
  a {
    color: #eee;
    font-size: 16px;
    text-align: center;
    user-select: none;
    padding: 0 5px;
  }
  a {
    color: #bbb;
    font-size: 14px;
    &:hover {
      color: #eee;
    }
  }
}
</style>
