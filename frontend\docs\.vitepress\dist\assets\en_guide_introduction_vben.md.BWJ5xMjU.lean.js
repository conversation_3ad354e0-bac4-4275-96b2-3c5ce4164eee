import{ao as e,k as t,aP as o,l as r,ay as i,j as n}from"./chunks/framework.C8U7mBUf.js";const a=JSON.parse('{"title":"About Vben Admin","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/introduction/vben.md","filePath":"en/guide/introduction/vben.md"}');const s=e({name:"en/guide/introduction/vben.md"},[["render",function(e,a,s,l,d,c){const u=i("NolebaseGitContributors"),h=i("NolebaseGitChangelog");return n(),t("div",null,[a[0]||(a[0]=o('<h1 id="about-vben-admin" tabindex="-1">About Vben Admin <a class="header-anchor" href="#about-vben-admin" aria-label="Permalink to &quot;About Vben Admin&quot;">​</a></h1><div class="info custom-block"><p class="custom-block-title">You are reading the documentation for <a href="https://github.com/vbenjs/vue-vben-admin" target="_blank" rel="noreferrer">Vben Admin</a> version <code>5.0</code>!</p><ul><li>Vben Admin 2.x is currently archived and only receives critical fixes.</li><li>The new version is not compatible with the old version. If you are using the old version (v2, v3), please refer to the <a href="https://doc.vvbin.cn" target="_blank" rel="noreferrer">Vue Vben Admin 2.x Documentation</a>.</li><li>If you find any errors in the documentation, feel free to submit an issue to help us improve.</li><li>If you just want to experience it, you can check out the <a href="./quick-start.html">Quick Start</a>.</li></ul></div><p><a href="https://github.com/vbenjs/vue-vben-admin" target="_blank" rel="noreferrer">Vben Admin</a> is a backend solution based on <a href="https://github.com/vuejs/core" target="_blank" rel="noreferrer">Vue 3.0</a>, <a href="https://github.com/vitejs/vite" target="_blank" rel="noreferrer">Vite</a>, and <a href="https://www.typescriptlang.org/" target="_blank" rel="noreferrer">TypeScript</a>, aimed at providing an out-of-the-box solution for developing medium to large-scale projects. It includes features like component re-encapsulation, utilities, hooks, dynamic menus, permission validation, multi-theme configurations, and button-level permission control. The project uses the latest frontend technology stack, making it a good starting template for quickly building enterprise-level mid- to backend product prototypes. It can also serve as an example for learning <code>vue3</code>, <code>vite</code>, <code>ts</code>, and other mainstream technologies. The project will continue to follow the latest technologies and apply them within the project.</p><h2 id="features" tabindex="-1">Features <a class="header-anchor" href="#features" aria-label="Permalink to &quot;Features&quot;">​</a></h2><ul><li><strong>Latest Technology Stack</strong>: Developed using cutting-edge frontend technologies like <code>Vue 3</code>, <code>Vite</code>, and <code>TypeScript</code>.</li><li><strong>Internationalization</strong>: Built-in comprehensive internationalization solutions with multi-language support.</li><li><strong>Permission Validation</strong>: Comprehensive permission validation solutions, including button-level permission control.</li><li><strong>Multi-Theme</strong>: Built-in multiple theme configurations &amp; dark mode to meet personalized needs.</li><li><strong>Dynamic Menu</strong>: Supports dynamic menus that can display based on permissions.</li><li><strong>Mock Data</strong>: High-performance local Mock data solution based on Nitro.</li><li><strong>Rich Components</strong>: Provides a wide range of components to meet most business needs.</li><li><strong>Standardization</strong>: Code quality is ensured with tools like <code>ESLint</code>, <code>Prettier</code>, <code>Stylelint</code>, <code>Publint</code>, and <code>CSpell</code>.</li><li><strong>Engineering</strong>: Development efficiency is improved with tools like <code>Pnpm Monorepo</code>, <code>TurboRepo</code>, and <code>Changeset</code>.</li><li><strong>Multi-UI Library Support</strong>: Supports mainstream UI libraries like <code>Ant Design Vue</code>, <code>Element Plus</code>, and <code>Vuetify</code>, without being restricted to a specific framework.</li></ul><h2 id="browser-support" tabindex="-1">Browser Support <a class="header-anchor" href="#browser-support" aria-label="Permalink to &quot;Browser Support&quot;">​</a></h2><p><strong>Local development</strong> is recommended using the <strong>latest version of Chrome</strong>. <strong>Versions below Chrome 80 are not supported</strong>.</p><p><strong>Production environment</strong> supports modern browsers, IE is not supported.</p><table tabindex="0"><thead><tr><th style="text-align:center;"><a href="http://godban.github.io/browsers-support-badges/" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/archive/internet-explorer_9-11/internet-explorer_9-11_48x48.png" alt="IE" width="24px" height="24px"></a>IE</th><th style="text-align:center;"><a href="http://godban.github.io/browsers-support-badges/" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px"></a>Edge</th><th style="text-align:center;"><a href="http://godban.github.io/browsers-support-badges/" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px"></a>Firefox</th><th style="text-align:center;"><a href="http://godban.github.io/browsers-support-badges/" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px"></a>Chrome</th><th style="text-align:center;"><a href="http://godban.github.io/browsers-support-badges/" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px"></a>Safari</th></tr></thead><tbody><tr><td style="text-align:center;">not support</td><td style="text-align:center;">last 2 versions</td><td style="text-align:center;">last 2 versions</td><td style="text-align:center;">last 2 versions</td><td style="text-align:center;">last 2 versions</td></tr></tbody></table><h2 id="contribution" tabindex="-1">Contribution <a class="header-anchor" href="#contribution" aria-label="Permalink to &quot;Contribution&quot;">​</a></h2><ul><li><a href="https://github.com/vbenjs/vue-vben-admin" target="_blank" rel="noreferrer">Vben Admin</a> is still being actively updated. Contributions are welcome to help maintain and improve the project, aiming to create a better mid- to backend solution.</li><li>If you wish to join us, you can provide suggestions or submit pull requests. We will invite you to join based on your activity.</li></ul><div class="info custom-block"><p class="custom-block-title">Join Us</p><p>If you wish to join us, you can start by contributing in the following ways, and we will invite you to join based on your activity:</p><ul><li>Regularly submit <code>PRs</code>.</li><li>Provide valuable suggestions.</li><li>Participate in discussions and help resolve some <code>issues</code>.</li><li>Help maintain the documentation.</li></ul></div>',12)),r(u),r(h)])}]]);export{a as __pageData,s as default};
