/**
 * 格式化字符串
 * @param {String} str
 * @param  {...any|Array} values
 * @returns {String}
 * @example String.format('Hello. My name is {0} {1}.', firstName, lastName);
 */
String.format = function (str, ...values) {
  str = str || '';
  values = values || [];
  if (values.length == 1 && values[0] instanceof Array) {
    values = values[0];
  }

  for (var i = 0; i < values.length; i++) {
    var re = new RegExp('\\{' + i + '\\}', 'gm');
    str = str.replace(re, values[i]);
  }
  return str;
};
String.prototype.format = function (...values) {
  return String.format(this, values);
};
/**
 *删除左右两端的空格
 */
String.prototype.trim = function () {
  return this.replace(/(^\s*)|(\s*$)/g, '');
};
/**
 *删除左边的空格
 */
String.prototype.ltrim = function () {
  return this.replace(/(^\s*)/g, '');
};
/**
 *删除右边的空格
 */
String.prototype.rtrim = function () {
  return this.replace(/(\s*$)/g, '');
};
/**
 * 把字符串转换成日期
 * @returns 转换成的日期
 */
String.prototype.toDate = function () {
  return new Date(this);
};

/**
 * 首字母大写
 * @example let str = "follow for more"; str.capitalize() //Follow for more
 */
String.prototype.capitalize = () =>
  this.charAt(0).toUpperCase() + this.slice(1);

/**
 * 去除字符串首尾的指定字符
 * @param {Object} char
 * @param {Object} type
 */
String.prototype.trim = function (char, type) {
  if (char) {
    if (type == 'left') {
      return this.replace(new RegExp('^\\' + char + '+', 'g'), '');
    } else if (type == 'right') {
      return this.replace(new RegExp('\\' + char + '+$', 'g'), '');
    }
    return this.replace(
      new RegExp('^\\' + char + '+|\\' + char + '+$', 'g'),
      '',
    );
  }
  return this.replace(/^\s+|\s+$/g, '');
};

/* 
// 去除字符串首尾的全部空白
var str = ' Ruchee ';
console.log('xxx' + str.trim() + 'xxx'); // xxxRucheexxx


// 去除字符串左侧空白
str = ' Ruchee ';
console.log('xxx' + str.trim(' ', 'left') + 'xxx'); // xxxRuchee xxx


// 去除字符串右侧空白
str = ' Ruchee ';
console.log('xxx' + str.trim(' ', 'right') + 'xxx'); // xxx Rucheexxx


// 去除字符串两侧指定字符
str = '/Ruchee/';
console.log(str.trim('/')); // Ruchee


// 去除字符串左侧指定字符
str = '/Ruchee/';
console.log(str.trim('/', 'left')); // Ruchee/


// 去除字符串右侧指定字符
str = '/Ruchee/';
console.log(str.trim('/', 'right')); // /Ruchee */
