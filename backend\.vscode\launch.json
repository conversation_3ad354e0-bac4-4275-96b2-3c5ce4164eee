{"version": "0.2.0", "configurations": [{"name": "Debug NestJS", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/main.ts", "args": [], "runtimeArgs": ["-r", "ts-node/register", "-r", "tsconfig-paths/register"], "sourceMaps": true, "envFile": "${workspaceFolder}/.env", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "restart": true, "protocol": "inspector", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "skipFiles": ["<node_internals>/**"]}, {"name": "Debug NestJS (nodemon)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/nodemon/bin/nodemon.js", "args": ["--config", "nodemon.json"], "runtimeArgs": [], "sourceMaps": true, "envFile": "${workspaceFolder}/.env", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "restart": true, "protocol": "inspector", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "skipFiles": ["<node_internals>/**"]}, {"name": "Attach to NestJS", "type": "node", "request": "attach", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}", "protocol": "inspector", "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"], "skipFiles": ["<node_internals>/**"]}]}