Date.prototype.timeDiff = function (date) {
  let diff;
  diff = date - this;
  return diff;
};

Date.prototype.dayDiff = function (date) {
  return Math.ceil(Math.abs(this.getTime() - date.getTime()) / 86400000);
};
const months = {
  Jan: {
    en: 'January',
    cn: '一',
  },
  Feb: {
    en: 'February',
    cn: '二',
  },
  Mar: {
    en: 'March',
    cn: '三',
  },
  April: {
    en: 'April',
    cn: '四',
  },
  May: {
    en: 'May',
    cn: '五',
  },
  Jun: {
    en: 'June',
    cn: '六',
  },
  Jul: {
    en: 'July',
    cn: '七',
  },
  Aug: {
    en: 'August',
    cn: '八',
  },
  Sep: {
    en: 'September',
    cn: '九',
  },
  Oct: {
    en: 'October',
    cn: '十',
  },
  Nov: {
    en: 'November',
    cn: '十一',
  },
  Dec: {
    en: 'December',
    cn: '十二',
  },
};
const weeks = {
  Mon: {
    en: 'Monday',
    cn: '一',
  },
  Tue: {
    en: 'Tuesday',
    cn: '二',
  },
  Wed: {
    en: 'Wednesday',
    cn: '三',
  },
  Thu: {
    en: 'Thursday',
    cn: '四',
  },
  Fri: {
    en: 'Friday',
    cn: '五',
  },
  Sat: {
    en: 'Saturday',
    cn: '六',
  },
  Sun: {
    en: 'Sunday',
    cn: '日',
  },
};
//  对Date的扩展，将 Date 转化为指定格式的String
//  月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
//  年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
//  例子：
//  (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
//  (new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18
Date.prototype.format2 = function (fmt) {
  // author: meizz
  var o = {
    'M+': this.getMonth() + 1, // 月份
    'd+': this.getDate(), // 日
    'h+': this.getHours(), // 小时
    'm+': this.getMinutes(), // 分
    's+': this.getSeconds(), // 秒
    'q+': Math.floor((this.getMonth() + 3) / 3), // 季度
    S: this.getMilliseconds(), // 毫秒
  };
  if (/(y+)/.test(fmt))
    fmt = fmt.replace(
      RegExp.$1,
      (this.getFullYear() + '').substr(4 - RegExp.$1.length),
    );
  for (var k in o)
    if (new RegExp('(' + k + ')').test(fmt))
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length),
      );
  return fmt;
};
Date.prototype.format = function (format) {
  date = this;
  format = format || 'Y-m-d';
  let _date = new Date(date);
  let [year, month, day, weekDay, hours, minutes, seconds] = [
    _date.getFullYear() + '',
    _date.getMonth() + 1 + '',
    _date.getDate() + '',
    _date.getDay() + '',
    _date.getHours() + '',
    _date.getMinutes() + '',
    _date.getSeconds() + '',
  ];
  let [monthEn, weekEn] = [
    _date.toUTCString().substr(8, 3),
    _date.toUTCString().substr(0, 3),
  ];
  let weekDay_ISO8601 = weekDay === '0' ? '7' : weekDay;
  return format
    .replace(/Y/g, year) //1970
    .replace(/y/g, year.slice(-2)) //70
    .replace(/m/g, ('0' + month).slice(-2)) //09
    .replace(/n/g, month) //9
    .replace(/M/g, monthEn) //Sep
    .replace(/F/g, months[monthEn].en) //September
    .replace(/\_F/g, months[monthEn].cn) //九
    .replace(/j/g, day) //9
    .replace(/d/g, ('0' + day).slice(-2)) //09
    .replace(/D/g, weekEn) //Sun
    .replace(/l/g, weeks[weekEn].en) //Sunday
    .replace(/_l/g, weeks[weekEn].cn) //日
    .replace(/w/g, weekDay) //0
    .replace(/N/g, weekDay_ISO8601) //7
    .replace(/H/g, ('0' + hours).slice(-2)) //06
    .replace(/G/g, hours) //6
    .replace(/i/g, ('0' + minutes).slice(-2)) //06
    .replace(/s/g, ('0' + seconds).slice(-2)); //06
};
Date.prototype.addSeconds = function (seconds) {
  // Making a copy with the Date() constructor
  const dateCopy = new Date(this);
  dateCopy.setSeconds(dateCopy.getSeconds() + seconds);
  return dateCopy;
};
Date.prototype.addMinutes = function (minutes) {
  // Making a copy with the Date() constructor
  const dateCopy = new Date(this);
  dateCopy.setMinutes(dateCopy.getMinutes() + minutes);
  return dateCopy;
};
