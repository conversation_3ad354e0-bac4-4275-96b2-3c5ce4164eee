import * as Cesium from 'cesium';

import Box from '../../../components/JS/box';
import { clipCanvas } from '../../../components/JS/export';
import VueEvent from '../../../config/event';
import core from '../core.js';
import { GISMap } from '../index.js';
// 测量
import Measure from './Measure';
// 二三维切换
// 图形绘制
// import FlyView from "../../../components/flyView.vue"
import eb from '../../../ztu/eventbus';

function morph() {
  const scene = GISMap.viewer.scene;
  console.log(scene.mode, Cesium.SceneMode.SCENE3D);
  GISMap.keepViewer.update();
  // 切换二维
  if (scene.mode == Cesium.SceneMode.SCENE3D) {
    scene.morphTo2D(0);
  }
  // 切换三维
  else if (scene.mode == Cesium.SceneMode.SCENE2D) {
    scene.morphTo3D(0);
  }

  GISMap.keepViewer.keep();
  return scene.mode;
}

function zoomIn(mapView) {
  const viewer = mapView || GISMap.viewer;
  const _pc = viewer.camera.positionCartographic.clone();
  viewer.camera.moveForward(_pc.height * 0.5);
}

function zoomOut(mapView) {
  const viewer = mapView || GISMap.viewer;
  const _pc = viewer.camera.positionCartographic.clone();
  viewer.camera.moveBackward(_pc.height * 1);
}

function fullMap(mapView) {
  const viewer = mapView || GISMap.viewer;
  const rectangle = Cesium.Camera.DEFAULT_VIEW_RECTANGLE;
  viewer.camera.flyTo({
    destination: rectangle,
  });
  // viewer.camera.flyTo({
  // 	destination:Cesium.Cartesian3.fromDegrees(119.28,26.08,30000000)
  // })
}

function fullScreen(el) {
  el = el || document.body;
  if (!Cesium.Fullscreen.enabled) {
    return false;
  }
  if (Cesium.Fullscreen.fullscreen) Cesium.Fullscreen.exitFullscreen();
  else Cesium.Fullscreen.requestFullscreen(el); // Cesium.Fullscreen.requestFullscreen(this.viewer.scene.canvas);
  return Cesium.Fullscreen.fullscreen;
}

// 测量
let m = null;
// 空间距离
function calLength(
  mapView,
  options = {
    clampToGround: true,
  },
) {
  drawEnd(mapView);
  m.measurePolyLine(options);
}

function calHeight(mapView, options = {}) {
  drawEnd(mapView);
  m.measureHeight(options);
}

function calArea(
  mapView,
  options = {
    clampToGround: true,
  },
) {
  drawEnd(mapView);
  m.measurePolygon(options);
}

function calAngle(mapView, options = {}) {
  drawEnd(mapView);
  m.measureAngle(options);
}
// 直线距离
function calLDistance(mapView, options = {}) {
  drawEnd(mapView);
  m.measureLDistance(options);
}
// 水平距离
function calHDistance(mapView, options = {}) {
  drawEnd(mapView);
  m.measureHDistance(options);
}
// 垂直距离
function calVDistance(mapView, options = {}) {
  drawEnd(mapView);
  m.measureVDistance(options);
}

// 绘图
function drawEnd() {
  const viewer = GISMap.viewer;
  m = m || new Measure(viewer);
  eb.emit('captureEnd', { target: 'measure' });
}
eb.on('clearAll', (e) => {
  m && m.removeMeasure();
});
eb.on('captureEnd', (e) => {
  m && m.measureEnd();
});
function removeAll() {
  Box.confirm('清除', '确定要清除吗?', () => {
    eb.emit('captureEnd', { target: 'tool' });
    eb.emit('clearAll', { target: 'tool' });
  });
}

// 截图
const saveAs = (blob, filename) => {
  if (blob === null) {
    // console.log(blob)
    return;
  }
  // console.log(blob)
  if (window.navigator.msSaveOrOpenBlob) {
    navigator.msSaveBlob(blob, filename);
  } else {
    const target = document.createElement('a');
    const body = document.querySelector('body');
    target.href = window.URL.createObjectURL(blob);
    target.download = filename;
    target.style.display = 'none';
    body.append(target);
    target.click();
    target.remove();
    window.URL.revokeObjectURL(target.href);
  }
};

function cameraPhoto(mapView) {
  const viewer = mapView || GISMap.viewer;
  const promise = new Promise((resolve, reject) => {
    try {
      core
        .boxSelect(viewer, {
          offsetTop: 64,
        })
        .then((res) => {
          const infoBox = Box.info('地图截取', '正在生成图片...');
          const cesiumCanvas = viewer.scene.canvas;
          clipCanvas(cesiumCanvas, res.rectangle).then((canvas) => {
            canvas.toBlob((blod) => {
              resolve(blod);
              Box.close(infoBox);
              Box.info('地图截取', '地图截取成功并保存');
            }, 'image/png');
          });
        });

      // deferred.resolve(canvas.toDataURL("image/png"));
      // console.log('s')
    } catch (error) {
      // console.log('ss')
      reject(error);
    }
  });
  return promise.then((blob) => {
    // 获取地图数据
    blob && saveAs(blob, `${Date.now()}.png`);
  });
}
// 俯视
function fushi(mapView) {
  const viewer = mapView || GISMap.viewer;
  const height = viewer.camera.positionCartographic.clone().height;
  const mapcenter = core.getMapCenter(viewer) || core.getDefaultViewCenter();
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      mapcenter.lng,
      mapcenter.lat,
      height / Math.abs(Math.sin(viewer.camera.pitch)),
    ),
    orientation: {
      heading: viewer.camera.heading,
      pitch: Cesium.Math.toRadians(-90),
      roll: 0,
    },
  });
}

//开始空间查询
export function onStartSpaceQuery(selectedShape) {
  const viewer = GISMap.viewer;
  const scene = viewer.scene;
  // 鼠标事件处理器
  const handler = new Cesium.ScreenSpaceEventHandler(scene.canvas);

  let centerCartesian = null; // 矩形中心点
  let rectangleEntity = null; // 矩形实体
  let isDrawing = false; // 是否正在绘制

// 鼠标点击事件
  handler.setInputAction((click) => {
    const position = click.position; // 获取鼠标屏幕位置
    const cartesian = viewer.scene.pickPosition(position);

    if (!cartesian) {
      console.error("无法拾取位置");
      return;
    }

    if (!isDrawing) {
      // 开始绘制矩形
      centerCartesian = cartesian;
      const centerCartographic = Cesium.Cartographic.fromCartesian(centerCartesian);

      rectangleEntity = viewer.entities.add({
        rectangle: {
          coordinates: new Cesium.CallbackProperty(() => {
            if (!centerCartesian || !isDrawing) return undefined;

            const startCartographic = Cesium.Cartographic.fromCartesian(centerCartesian);
            const endCartesian = viewer.scene.pickPosition(lastMouseMovePosition);
            if (!endCartesian) return undefined;

            const endCartographic = Cesium.Cartographic.fromCartesian(endCartesian);

            return Cesium.Rectangle.fromCartographicArray([startCartographic, endCartographic]);
          }, false),
          material: Cesium.Color.BLUE.withAlpha(0.5),
          outline: true,
          outlineColor: Cesium.Color.BLACK,
        },
      });

      isDrawing = true;
    } else {
      // 停止绘制
      isDrawing = false;
      centerCartesian = null;
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  let lastMouseMovePosition = null; // 保存最后的鼠标位置

// 鼠标移动事件
  handler.setInputAction((movement) => {
    if (!isDrawing || !centerCartesian) return;

    lastMouseMovePosition = movement.endPosition; // 更新鼠标移动位置
    viewer.scene.requestRender(); // 强制重新渲染
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
}

// 坐标点拾取
function getLocation(mapView) {
  // 触发事件总线
  VueEvent.emit('getlocation');
}
const flyBox = null;
function fly() {
  // if(flyBox){
  // 	return Box.show(flyBox);
  // }
  // flyBox = Box.open({
  // 	title: '漫游',
  // 	beforeClose:()=>{
  // 		Box.hide(flyBox);
  // 		return true;
  // 	}
  // }, FlyView);
}
export default {
  onStartSpaceQuery,
  morph,
  zoomIn,
  zoomOut,
  fullMap,
  fullScreen,
  calLength,
  calAngle,
  calHeight,
  calArea,
  removeAll,
  cameraPhoto,
  fushi,
  getLocation,
  calLDistance,
  calHDistance,
  calVDistance,
  fly,
};
