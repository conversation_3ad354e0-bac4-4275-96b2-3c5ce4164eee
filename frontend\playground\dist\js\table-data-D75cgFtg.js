const a=["User","Admin","Manager","Guest"],d=(()=>{const t=[];for(let e=0;e<40;e++)t.push({address:`New York${e}`,age:e+1,id:e,name:`Test${e}`,nickname:`Test${e}`,role:a[Math.floor(Math.random()*a.length)]});return t})(),s=[{date:"2020-08-01",id:1e4,name:"Test1",parentId:null,size:1024,type:"mp3"},{date:"2021-04-01",id:10050,name:"Test2",parentId:null,size:0,type:"mp4"},{date:"2020-03-01",id:24300,name:"Test3",parentId:10050,size:1024,type:"avi"},{date:"2021-04-01",id:20045,name:"Test4",parentId:24300,size:600,type:"html"},{date:"2021-04-01",id:10053,name:"<PERSON><PERSON>",parentId:24300,size:0,type:"avi"},{date:"2021-10-01",id:24330,name:"Test6",parentId:10053,size:25,type:"txt"},{date:"2020-01-01",id:21011,name:"Test7",parentId:10053,size:512,type:"pdf"},{date:"2021-06-01",id:22200,name:"Test8",parentId:10053,size:1024,type:"js"},{date:"2020-11-01",id:23666,name:"Test9",parentId:null,size:2048,type:"xlsx"},{date:"2021-06-01",id:23677,name:"Test10",parentId:23666,size:1024,type:"js"},{date:"2021-06-01",id:23671,name:"Test11",parentId:23677,size:1024,type:"js"},{date:"2021-06-01",id:23672,name:"Test12",parentId:23677,size:1024,type:"js"},{date:"2021-06-01",id:23688,name:"Test13",parentId:23666,size:1024,type:"js"},{date:"2021-06-01",id:23681,name:"Test14",parentId:23688,size:1024,type:"js"},{date:"2021-06-01",id:23682,name:"Test15",parentId:23688,size:1024,type:"js"},{date:"2020-10-01",id:24555,name:"Test16",parentId:null,size:224,type:"avi"},{date:"2021-06-01",id:24566,name:"Test17",parentId:24555,size:1024,type:"js"},{date:"2021-06-01",id:24577,name:"Test18",parentId:24555,size:1024,type:"js"}];export{d as M,s as a};
