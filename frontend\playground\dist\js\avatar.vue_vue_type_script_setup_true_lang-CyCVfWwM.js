import{p as f,F as m,q as h,N as _}from"./bootstrap-DShsrVit.js";import{a4 as c,af as l,ag as d,ah as n,ae as p,aZ as o,a3 as s,aX as v,ai as g,aj as y,ac as b,J as x,am as i,n as u,an as C,ao as k,al as z}from"../jse/index-index-BMh_AyeW.js";const B=f("inline-flex items-center justify-center font-normal text-foreground select-none shrink-0 bg-secondary overflow-hidden",{variants:{shape:{circle:"rounded-full",square:"rounded-md"},size:{base:"h-16 w-16 text-2xl",lg:"h-32 w-32 text-5xl",sm:"h-10 w-10 text-xs"}}}),w=c({__name:"Avatar",props:{class:{},shape:{default:"circle"},size:{default:"sm"}},setup(t){const a=t;return(e,r)=>(l(),d(s(m),{class:o(s(v)(s(B)({size:e.size,shape:e.shape}),a.class))},{default:n(()=>[p(e.$slots,"default")]),_:3},8,["class"]))}}),N=c({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{}},setup(t){const a=t;return(e,r)=>(l(),d(s(h),g(y(a)),{default:n(()=>[p(e.$slots,"default")]),_:3},16))}}),P=c({__name:"AvatarImage",props:{src:{},referrerPolicy:{},asChild:{type:Boolean},as:{}},setup(t){const a=t;return(e,r)=>(l(),d(s(_),b(a,{class:"h-full w-full object-cover"}),null,16))}}),j=c({inheritAttrs:!1,__name:"avatar",props:{alt:{default:"avatar"},class:{},dot:{type:Boolean,default:!1},dotClass:{default:"bg-green-500"},asChild:{type:Boolean},as:{default:"button"},delayMs:{},src:{},referrerPolicy:{}},setup(t){const a=t,e=x(()=>a.alt.slice(-2).toUpperCase());return(r,$)=>(l(),i("div",{class:o([a.class,"relative flex flex-shrink-0 items-center"])},[u(s(w),{class:o([a.class,"size-full"])},{default:n(()=>[u(s(P),{alt:r.alt,src:r.src},null,8,["alt","src"]),u(s(N),null,{default:n(()=>[C(k(e.value),1)]),_:1})]),_:1},8,["class"]),r.dot?(l(),i("span",{key:0,class:o([r.dotClass,"border-background absolute bottom-0 right-0 size-3 rounded-full border-2"])},null,2)):z("",!0)],2))}});export{j as _};
