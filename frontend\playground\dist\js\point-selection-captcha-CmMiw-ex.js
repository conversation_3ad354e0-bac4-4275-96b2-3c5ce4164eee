import{$ as i,bv as P,aR as J,d as W,aH as I,bw as R,bx as C,by as q}from"./bootstrap-DShsrVit.js";import{W as F,a4 as E,J as S,af as r,ag as $,ah as o,n,a3 as e,ae as b,an as w,ao as d,am as x,ap as c,ak as X,ar as A,al as B,as as L,F as z,O as K}from"../jse/index-index-BMh_AyeW.js";import{_ as Q,a as Z,b as ee,c as te}from"./CardTitle.vue_vue_type_script_setup_true_lang-D9WinrTz.js";import{_ as ae}from"./CardFooter.vue_vue_type_script_setup_true_lang-BLjMzuQQ.js";import{R as le}from"./rotate-cw-B0JNpqtv.js";import{C as ie}from"./index-B_b7xM74.js";import{_ as se}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";function ne(){const u=F([]);function p(f){u.push(f)}function t(){u.splice(0,u.length)}return{addPoint:p,clearPoints:t,points:u}}const oe={key:1},ce={class:"flex items-center justify-end"},re=["alt","src"],de={class:"absolute inset-0"},pe=E({__name:"point-selection-captcha-card",props:{captchaImage:{},height:{default:"220px"},paddingX:{default:"12px"},paddingY:{default:"16px"},title:{default:""},width:{default:"300px"}},emits:["click"],setup(u,{emit:p}){const t=u,f=p,h=a=>{if(typeof a=="number")return a;const v=Number.parseFloat(a);return Number.isNaN(v)?0:v},y=S(()=>({padding:`${h(t.paddingY)}px ${h(t.paddingX)}px`,width:`${h(t.width)+h(t.paddingX)*2}px`})),m=S(()=>({height:`${h(t.height)}px`,width:`${h(t.width)}px`}));function l(a){f("click",a)}return(a,v)=>(r(),$(e(te),{style:A(y.value),"aria-labelledby":"captcha-title",role:"region"},{default:o(()=>[n(e(Z),{class:"p-0"},{default:o(()=>[n(e(Q),{id:"captcha-title",class:"flex items-center justify-between"},{default:o(()=>[a.$slots.title?b(a.$slots,"title",{key:0},()=>[w(d(e(i)("ui.captcha.title")),1)]):(r(),x("span",oe,d(a.title),1)),c("div",ce,[b(a.$slots,"extra")])]),_:3})]),_:3}),n(e(ee),{class:"relative mt-2 flex w-full overflow-hidden rounded p-0"},{default:o(()=>[X(c("img",{alt:e(i)("ui.captcha.alt"),src:a.captchaImage,style:A(m.value),class:"relative z-10",onClick:l},null,12,re),[[P,a.captchaImage]]),c("div",de,[b(a.$slots,"default")])]),_:3}),n(e(ae),{class:"mt-2 flex justify-between p-0"},{default:o(()=>[b(a.$slots,"footer")]),_:3})]),_:3},8,["style"]))}}),he=["aria-label"],me=["alt","src"],ue={key:1,class:"border-border flex-center h-10 w-full rounded border"},D=11,fe=E({__name:"index",props:{showConfirm:{type:Boolean,default:!1},hintImage:{default:""},hintText:{default:""},captchaImage:{},height:{default:"220px"},paddingX:{default:"12px"},paddingY:{default:"16px"},title:{default:""},width:{default:"300px"}},emits:["click","confirm","refresh"],setup(u,{emit:p}){const t=u,f=p,{addPoint:h,clearPoints:y,points:m}=ne();!t.hintImage&&!t.hintText&&console.warn("At least one of hint image or hint text must be provided");function l(s){const g=s.getBoundingClientRect();return{x:g.left+window.scrollX,y:g.top+window.scrollY}}function a(s){try{const g=s.currentTarget;if(!g)throw new Error("Element not found");const{x:k,y:_}=l(g),Y=s.clientX+window.scrollX,N=s.clientY+window.scrollY;if(typeof Y!="number"||typeof N!="number")throw new TypeError("Mouse coordinates not found");const T=Y-k,U=N-_,j=g.getBoundingClientRect();if(T<0||U<0||T>j.width||U>j.height){console.warn("Click position is out of the valid range");return}const V=Math.ceil(T),G=Math.ceil(U),H={i:m.length,t:Date.now(),x:V,y:G};h(H),f("click",H),s.stopPropagation(),s.preventDefault()}catch(g){console.error("Error in handleClick:",g)}}function v(){try{y()}catch(s){console.error("Error in clear:",s)}}function M(){try{v(),f("refresh")}catch(s){console.error("Error in handleRefresh:",s)}}function O(){if(t.showConfirm)try{f("confirm",m,v)}catch(s){console.error("Error in handleConfirm:",s)}}return(s,g)=>(r(),$(pe,{"captcha-image":s.captchaImage,height:s.height,"padding-x":s.paddingX,"padding-y":s.paddingY,title:s.title,width:s.width,onClick:a},{title:o(()=>[b(s.$slots,"title",{},()=>[w(d(e(i)("ui.captcha.title")),1)])]),extra:o(()=>[n(e(J),{"aria-label":e(i)("ui.captcha.refreshAriaLabel"),class:"ml-1",onClick:M},{default:o(()=>[n(e(le),{class:"size-5"})]),_:1},8,["aria-label"]),s.showConfirm?(r(),$(e(W),{key:0,"aria-label":e(i)("ui.captcha.confirmAriaLabel"),class:"ml-2",size:"sm",onClick:O},{default:o(()=>[w(d(e(i)("ui.captcha.confirm")),1)]),_:1},8,["aria-label"])):B("",!0)]),footer:o(()=>[s.hintImage?(r(),x("img",{key:0,alt:e(i)("ui.captcha.alt"),src:s.hintImage,class:"border-border h-10 w-full rounded border"},null,8,me)):s.hintText?(r(),x("div",ue,d(`${e(i)("ui.captcha.clickInOrder")}【${s.hintText}】`),1)):B("",!0)]),default:o(()=>[(r(!0),x(z,null,L(e(m),(k,_)=>(r(),x("div",{key:_,"aria-label":e(i)("ui.captcha.pointAriaLabel")+(_+1),style:A({top:`${k.y-D}px`,left:`${k.x-D}px`}),class:"bg-primary text-primary-50 border-primary-50 absolute z-20 flex h-5 w-5 cursor-default items-center justify-center rounded-full border-2",role:"button",tabindex:"0"},d(_+1),13,he))),128))]),_:3},8,["captcha-image","height","padding-x","padding-y","title","width"]))}}),ge={class:"mb-3 flex items-center justify-start"},we={class:"ml-8 flex w-96 items-center"},xe={class:"mb-3 flex items-center justify-start"},ve={class:"ml-8"},ye={class:"ml-8"},_e={class:"ml-8"},be={class:"float-left p-5"},ke={class:"mr-3 w-16"},Ie={class:"mr-3 w-52"},Ce={class:"mr-3 w-16"},$e={class:"mr-3 w-16"},Te="https://unpkg.com/@vbenjs/static-source@0.1.7/source/default-captcha-image.jpeg",Ue="https://unpkg.com/@vbenjs/static-source@0.1.7/source/default-hint-image.png",He=E({__name:"point-selection-captcha",setup(u){const p=K([]),t=F({captchaImage:"",captchaImageUrl:Te,height:void 0,hintImage:"",hintImageUrl:Ue,hintText:"唇，燕，碴，找",paddingX:void 0,paddingY:void 0,showConfirm:!0,showHintImage:!1,title:"",width:void 0}),f=(m,l)=>{q.success({content:`captcha points: ${JSON.stringify(m)}`}),l(),p.value=[]},h=()=>{p.value=[]},y=m=>{p.value.push(m)};return(m,l)=>(r(),$(e(se),{description:e(i)("examples.captcha.pageDescription"),title:e(i)("examples.captcha.pageTitle")},{default:o(()=>[n(e(ie),{title:e(i)("examples.captcha.basic"),class:"mb-4 overflow-x-auto"},{default:o(()=>[c("div",ge,[n(e(I),{value:t.title,"onUpdate:value":l[0]||(l[0]=a=>t.title=a),placeholder:e(i)("examples.captcha.titlePlaceholder"),class:"w-64"},null,8,["value","placeholder"]),n(e(I),{value:t.captchaImageUrl,"onUpdate:value":l[1]||(l[1]=a=>t.captchaImageUrl=a),placeholder:e(i)("examples.captcha.captchaImageUrlPlaceholder"),class:"ml-8 w-64"},null,8,["value","placeholder"]),c("div",we,[n(e(R),{checked:t.showHintImage,"onUpdate:checked":l[2]||(l[2]=a=>t.showHintImage=a),"checked-children":e(i)("examples.captcha.hintImage"),"un-checked-children":e(i)("examples.captcha.hintText"),class:"mr-4 w-40"},null,8,["checked","checked-children","un-checked-children"]),X(n(e(I),{value:t.hintImageUrl,"onUpdate:value":l[3]||(l[3]=a=>t.hintImageUrl=a),placeholder:e(i)("examples.captcha.hintImagePlaceholder")},null,8,["value","placeholder"]),[[P,t.showHintImage]]),X(n(e(I),{value:t.hintText,"onUpdate:value":l[4]||(l[4]=a=>t.hintText=a),placeholder:e(i)("examples.captcha.hintTextPlaceholder")},null,8,["value","placeholder"]),[[P,!t.showHintImage]])]),n(e(R),{checked:t.showConfirm,"onUpdate:checked":l[5]||(l[5]=a=>t.showConfirm=a),"checked-children":e(i)("examples.captcha.showConfirm"),"un-checked-children":e(i)("examples.captcha.hideConfirm"),class:"ml-8 w-28"},null,8,["checked","checked-children","un-checked-children"])]),c("div",xe,[c("div",null,[n(e(C),{value:t.width,"onUpdate:value":l[6]||(l[6]=a=>t.width=a),min:1,placeholder:e(i)("examples.captcha.widthPlaceholder"),precision:0,step:1,class:"w-64"},{addonAfter:o(()=>l[10]||(l[10]=[w("px")])),_:1},8,["value","placeholder"])]),c("div",ve,[n(e(C),{value:t.height,"onUpdate:value":l[7]||(l[7]=a=>t.height=a),min:1,placeholder:e(i)("examples.captcha.heightPlaceholder"),precision:0,step:1,class:"w-64"},{addonAfter:o(()=>l[11]||(l[11]=[w("px")])),_:1},8,["value","placeholder"])]),c("div",ye,[n(e(C),{value:t.paddingX,"onUpdate:value":l[8]||(l[8]=a=>t.paddingX=a),min:1,placeholder:e(i)("examples.captcha.paddingXPlaceholder"),precision:0,step:1,class:"w-64"},{addonAfter:o(()=>l[12]||(l[12]=[w("px")])),_:1},8,["value","placeholder"])]),c("div",_e,[n(e(C),{value:t.paddingY,"onUpdate:value":l[9]||(l[9]=a=>t.paddingY=a),min:1,placeholder:e(i)("examples.captcha.paddingYPlaceholder"),precision:0,step:1,class:"w-64"},{addonAfter:o(()=>l[13]||(l[13]=[w("px")])),_:1},8,["value","placeholder"])])]),n(e(fe),{"captcha-image":t.captchaImageUrl||t.captchaImage,height:t.height||220,"hint-image":t.showHintImage?t.hintImageUrl||t.hintImage:"","hint-text":t.hintText,"padding-x":t.paddingX,"padding-y":t.paddingY,"show-confirm":t.showConfirm,width:t.width||300,class:"float-left",onClick:y,onConfirm:f,onRefresh:h},{title:o(()=>[w(d(t.title||e(i)("examples.captcha.captchaCardTitle")),1)]),_:1},8,["captcha-image","height","hint-image","hint-text","padding-x","padding-y","show-confirm","width"]),c("ol",be,[(r(!0),x(z,null,L(p.value,a=>(r(),x("li",{key:a.i,class:"flex"},[c("span",ke,d(e(i)("examples.captcha.index")+a.i),1),c("span",Ie,d(e(i)("examples.captcha.timestamp")+a.t),1),c("span",Ce,d(e(i)("examples.captcha.x")+a.x),1),c("span",$e,d(e(i)("examples.captcha.y")+a.y),1)]))),128))])]),_:1},8,["title"])]),_:1},8,["description","title"]))}});export{He as default};
