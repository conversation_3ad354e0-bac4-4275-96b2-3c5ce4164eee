import{u as l}from"./vxe-table-a0ubJ4nQ.js";import{_ as d}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as c,a5 as p,af as m,ag as f,ah as u,a3 as a,n as _}from"../jse/index-index-BMh_AyeW.js";import"./bootstrap-DShsrVit.js";import"./empty-icon-Crs2MoG4.js";import"./loading-Cqdke3S1.js";import"./form-DnT3S1ma.js";const O=c({__name:"virtual",setup(h){const r={columns:[{type:"seq",width:70},{field:"name",title:"Name"},{field:"role",title:"Role"},{field:"sex",title:"Sex"}],data:[],height:"auto",pagerConfig:{enabled:!1},scrollY:{enabled:!0,gt:0},showOverflow:!0},[i,s]=l({gridOptions:r}),n=(o=200)=>{try{const e=[];for(let t=0;t<o;t++)e.push({id:1e4+t,name:`Test${t}`,role:"Developer",sex:"男"});s.setGridOptions({data:e})}catch(e){console.error("Failed to load data:",e)}};return p(()=>{n(1e3)}),(o,e)=>(m(),f(a(d),{"auto-content-height":""},{default:u(()=>[_(a(i))]),_:1}))}});export{O as default};
