import{by as s,B as i}from"./bootstrap-DShsrVit.js";import{a4 as l,af as m,ag as p,ah as t,a3 as o,ap as c,n as f,an as d}from"../jse/index-index-BMh_AyeW.js";import{u}from"./use-drawer-Qcdpj8Bl.js";const _={class:"flex-col-center"},b=l({__name:"dynamic-demo",setup(C){const[n,a]=u({onCancel(){a.close()},onConfirm(){s.info("onConfirm")},title:"动态修改配置示例"});function r(){a.setState({title:"内部动态标题"})}return(x,e)=>(m(),p(o(n),null,{default:t(()=>[c("div",_,[f(o(i),{class:"mb-3",type:"primary",onClick:e[0]||(e[0]=B=>r())},{default:t(()=>e[1]||(e[1]=[d(" 内部动态修改标题 ")])),_:1})])]),_:1}))}});export{b as _};
