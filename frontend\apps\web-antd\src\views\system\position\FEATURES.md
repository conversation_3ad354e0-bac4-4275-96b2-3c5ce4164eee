# 岗位管理功能特性说明

## 🎯 新增功能：组织筛选侧边栏

### 功能概述
在岗位管理页面左侧添加了组织筛选侧边栏，用户可以通过点击组织树节点来筛选对应组织下的岗位，大大提升了数据查找和管理的效率。

### 主要特性

#### 1. 组织树形结构显示
- **完整组织架构**: 显示系统中所有组织的树形结构
- **层级关系清晰**: 支持无限层级的组织嵌套显示
- **展开/收起**: 可以展开或收起组织节点
- **默认展开**: 初始加载时默认展开所有节点

#### 2. 智能筛选功能
- **点击筛选**: 点击任意组织节点即可筛选该组织下的岗位
- **全部组织**: 提供"全部组织"根节点，点击可查看所有岗位
- **实时更新**: 筛选结果实时更新，无需刷新页面
- **筛选状态**: 右侧显示当前筛选的组织名称

#### 3. 搜索功能
- **组织搜索**: 支持在组织树中搜索组织名称
- **实时搜索**: 输入关键词即时过滤显示匹配的组织
- **高亮显示**: 搜索结果高亮显示匹配内容
- **清除搜索**: 支持一键清除搜索条件

#### 4. 用户体验优化
- **加载状态**: 显示数据加载进度
- **错误处理**: 网络错误时显示友好的错误提示
- **重试机制**: 支持重新加载数据
- **清除选择**: 支持一键清除当前选择

#### 5. 响应式设计
- **桌面端**: 左右分栏布局，侧边栏固定宽度280px
- **平板端**: 侧边栏宽度调整为240px
- **移动端**: 上下布局，侧边栏高度300px
- **自适应**: 根据屏幕尺寸自动调整布局

### 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│                    岗位管理页面                              │
├─────────────────┬───────────────────────────────────────────┤
│   组织筛选侧边栏  │              岗位管理内容区域              │
│                │                                          │
│ 🔍 搜索组织      │  📋 当前筛选: [组织名称] ❌                │
│                │                                          │
│ 📁 全部组织      │  🔍 搜索表单                              │
│ ├─ 📁 总公司     │  ┌─────────────────────────────────────┐  │
│ │  ├─ 📁 研发部  │  │         岗位数据表格                │  │
│ │  ├─ 📁 销售部  │  │                                   │  │
│ │  └─ 📁 财务部  │  │  岗位名称 | 编码 | 类别 | 状态 | 操作  │  │
│ └─ 📁 分公司     │  │  ─────────────────────────────────  │  │
│    ├─ 📁 市场部  │  │  开发工程师 | DEV001 | 部门 | 启用 | ... │  │
│    └─ 📁 运营部  │  │  产品经理   | PM001  | 部门 | 启用 | ... │  │
│                │  └─────────────────────────────────────┘  │
└─────────────────┴───────────────────────────────────────────┘
```

### 技术实现

#### 组件结构
- **OrganizationSidebar.vue**: 组织筛选侧边栏组件（位于 ../organization/components/）
- **index.vue**: 主页面，集成侧边栏和表格
- **position.api.ts**: 支持按组织ID筛选的API接口

#### 核心功能
1. **数据加载**: 异步加载组织树数据
2. **状态管理**: 管理选中的组织ID和名称
3. **事件通信**: 父子组件间的事件传递
4. **数据筛选**: 根据组织ID筛选岗位数据

#### 样式特性
- **现代化设计**: 使用卡片式布局和阴影效果
- **交互反馈**: 悬停、选中状态的视觉反馈
- **主题适配**: 支持明暗主题自动切换
- **无障碍**: 支持键盘导航和屏幕阅读器

### 使用场景

1. **按部门查看岗位**: HR可以快速查看特定部门下的所有岗位
2. **组织架构管理**: 管理员可以直观地看到组织与岗位的关系
3. **数据筛选**: 在大量岗位数据中快速定位目标岗位
4. **层级管理**: 便于管理复杂的组织层级结构

### 性能优化

- **懒加载**: 组织树数据按需加载
- **缓存机制**: 避免重复请求相同数据
- **虚拟滚动**: 大量数据时的性能优化
- **防抖搜索**: 搜索输入防抖处理

### 兼容性

- **浏览器**: 支持现代浏览器（Chrome 80+, Firefox 75+, Safari 13+）
- **设备**: 支持桌面端、平板和移动设备
- **分辨率**: 适配各种屏幕分辨率
- **无障碍**: 符合WCAG 2.1 AA标准
