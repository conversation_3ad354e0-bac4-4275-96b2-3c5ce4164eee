import{a4 as g,O as k,bI as c,af as x,ag as F,ah as l,a3 as e,n,ap as a,an as o,ao as u}from"../jse/index-index-BMh_AyeW.js";import{B as r}from"./bootstrap-DShsrVit.js";import{C as m}from"./index-B_b7xM74.js";import{_ as y}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";const S={class:"flex flex-wrap items-center gap-4"},w={class:"text-nowrap"},V=g({__name:"index",setup(D){const i=k(),{enter:f,exit:p,isFullscreen:s,toggle:_}=c(),{isFullscreen:C,toggle:d}=c(i);return(b,t)=>(x(),F(e(y),{title:"全屏示例"},{default:l(()=>[n(e(m),{title:"Window Full Screen"},{default:l(()=>[a("div",S,[n(e(r),{disabled:e(s),type:"primary",onClick:e(f)},{default:l(()=>t[0]||(t[0]=[o(" Enter Window Full Screen ")])),_:1},8,["disabled","onClick"]),n(e(r),{onClick:e(_)},{default:l(()=>t[1]||(t[1]=[o(" Toggle Window Full Screen ")])),_:1},8,["onClick"]),n(e(r),{disabled:!e(s),danger:"",onClick:e(p)},{default:l(()=>t[2]||(t[2]=[o(" Exit Window Full Screen ")])),_:1},8,["disabled","onClick"]),a("span",w," Current State: "+u(e(s)),1)])]),_:1}),n(e(m),{class:"mt-5",title:"Dom Full Screen"},{default:l(()=>[n(e(r),{type:"primary",onClick:e(d)},{default:l(()=>t[3]||(t[3]=[o(" Enter Dom Full Screen ")])),_:1},8,["onClick"])]),_:1}),a("div",{ref_key:"domRef",ref:i,class:"mx-auto mt-10 flex h-64 w-1/2 items-center justify-center rounded-md bg-yellow-400"},[n(e(r),{class:"mr-2",type:"primary",onClick:e(d)},{default:l(()=>[o(u(e(C)?"Exit Dom Full Screen":"Enter Dom Full Screen"),1)]),_:1},8,["onClick"])],512)]),_:1}))}});export{V as default};
