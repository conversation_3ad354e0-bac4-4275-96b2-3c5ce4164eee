<script lang="ts" setup>
import { computed, ref, unref, watch, onMounted } from "vue";

import { save, edit, updateFileState, deleteOne, getEpsgCodes } from "#/views/dataManage/entity/entity.api";
import { message } from "ant-design-vue";
import { showLoading, showSuccess } from "#/utils/toast.js";
import { fileUploader } from '#/utils/upload';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from "#/adapter/form";
import { showConform } from "#/utils/alert";
import { getUploadFormSchemaByCategory } from "#/views/dataManage/entity/entity.data";
import { getUploadUrls } from '#/utils/upload-urls';
import { isTaskUploading, addUploadingTask, removeUploadingTask } from '#/utils/upload-state';
import emitter from '#/utils/mitt';

// 声明Emits
const emit = defineEmits(['handleCancel']);
const props = defineProps(['category']);
// 控制模态框的显示与隐藏

let id = null;
let row = null;
let fileInput : any= null;
const fileList = ref([]);
const uploadType = ref('0');
const startUpload = ref(false);
const epsgList = ref([]);
const headers = {
    authorization: 'authorization-text',
  };
let category = props.category;

// 获取坐标系列表
const fetchEpsgCodes = async () => {
  try {
    const res = await getEpsgCodes({});
    if (res && res.records) {
      epsgList.value = res.records.map(item => ({
        label: `${item.name} (${item.code})`,
        value: item.code
      }));
    }
  } catch (error) {
    console.error('获取坐标系列表失败:', error);
  }
};

const clearData = () => {
  formApi.setState({
    commonConfig: { disabled: false },
    submitButtonOptions: { disabled: false },
    resetButtonOptions: { disabled: false }
  });
}

const [Modal, modalApi] = useVbenModal({
  closeOnClickModal:false,
  onCancel() {
    modalApi.close();
  },
  onClosed() {
    clearData();
  },
  onConfirm() {},
  onOpenChange(isOpen) {
    if (isOpen) {
      const state = modalApi.useStore();
      row = state.value.row;

      formApi.setFieldValue("dataType",row.dataType);
      startUpload.value = false;
      // 页面打开时获取坐标系数据
      fetchEpsgCodes();
    }

  },
});

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelWidth: 80,
  },
  // 提交函数
  handleSubmit:handleSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: getUploadFormSchemaByCategory(category),
  wrapperClass: 'grid-cols-1',
  resetButtonOptions: { show: false },
  submitButtonOptions:{
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  }
});


// 监听epsgList的变化，更新表单的options
watch(epsgList, (newValue) => {
  formApi.updateSchema([
    {
      fieldName: 'epsgCode',
      componentProps: {
        options: newValue,
        placeholder: '请选择坐标系'
      }
    }
  ]);
});

// 表单提交事件
async function handleSubmit(values) {
  if (startUpload.value) return;
  startUpload.value = true;

  try {
    // 检查任务是否正在上传
    if (isTaskUploading(row.id)) {
      message.warning('该任务已经在上传中，请稍后再试');
      startUpload.value = false;
      return;
    }

    // 添加到正在上传的任务集合中
    addUploadingTask(row.id);

    showLoading('操作处理中...');
    formApi.setState({
      commonConfig: { disabled: true },
      submitButtonOptions: { disabled: true },
      resetButtonOptions: { disabled: true }
    });

    const urls = getUploadUrls(row.dataType, values.fileFormat);

    const taskId = row.id;
    fileUploader.uploadFiles(
      fileInput.files,
      { urls, taskName: row.name },
      {
        onSuccess: async (uploadId) => {
          try {
            showLoading('文件上传成功，解析文件中...');
            await updateFileState({
              id: row.id,
              fileId: uploadId,
              dataType: row.dataType,
              epsgCode: values.epsgCode
            });
            onSuccess(taskId);
          } catch (e) {
            onError(taskId);
          }
        },
        onError: () => {
          onError(taskId);
        },
      }
    );
    modalApi.close();
  } catch (error) {
    onError(taskId);
  }
}

const onSuccess = (taskId) =>{
  // 从正在上传的任务集合中移除
  if (taskId) {
    removeUploadingTask(taskId);
  }

  // 关闭弹窗
  startUpload.value = false;
  formApi.setState({
    commonConfig: { disabled: false },
    submitButtonOptions: { disabled: false },
    resetButtonOptions: { disabled: false }
  });
  modalApi.close();
  showSuccess('操作成功！');
  emitter.emit('uploadSuccess');
}

const onError = (taskId) =>{
  // 从正在上传的任务集合中移除
  if (taskId) {
    removeUploadingTask(taskId);
  }

  // 关闭弹窗
  startUpload.value = false;
  formApi.setState({
    commonConfig: { disabled: false },
    submitButtonOptions: { disabled: false },
    resetButtonOptions: { disabled: false }
  });
  modalApi.close();
  emitter.emit('uploadSuccess');
}



const handleUploadTypeChange = (data) =>{
  uploadType.value = data.target.value;
}
// 关闭模态框
const handleCancel = () => {
  emit('onCancel', {});
};
const handleFolderSelect = (data) =>{
  if(startUpload.value) {
    return;
  }
  console.log(data);
  fileInput = data.target;
  // uploadFolder(fileInput);
  const fileName = document.getElementById('fileName');
  fileName.textContent = fileInput.files.length > 0 ? '已选'+fileInput.files.length+'个文件' : '没有选择文件';
  formApi.setFieldValue("fileUpload",fileInput.files);
}

const triggerFileInput = () => {
  if(startUpload.value) {
    return;
  }
  document.getElementById('fileInput').click();
}
</script>
<template>
   <Modal
    title="上传文件"
    class="w-[600px]"
    @cancel="handleCancel"
    @ok="handleSubmit"
    :maskClosable="false"
    :footer="false"
  >
     <div style="padding-left: 20px;padding-right: 20px;padding-top: 20px">
       <Form>
         <!--      <template #uploadType="slotProps">-->
         <!--        <a-radio-group v-show="!isUpdate" v-model:value="uploadType" @change="handleUploadTypeChange":disabled="true">-->
         <!--          <a-radio value="0">目录</a-radio>-->
         <!--          <a-radio value="1">文件</a-radio>-->
         <!--        </a-radio-group>-->
         <!--      </template>-->
         <template #fileUpload="slotProps">
           <input
             class="hidden" id="fileInput" type="file"  :directory="uploadType ==='0'"  :webkitdirectory="uploadType ==='0'" @change="handleFolderSelect"  />

           <!-- 自定义按钮 -->
           <label for="fileInput" v-if="startUpload" onclick="triggerFileInput"  class="custom-label bg-gray-200 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400">
             选择文件
           </label>

           <label for="fileInput" v-else   onclick="triggerFileInput"  class="custom-label  bg-primary-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400">
             选择文件
           </label>

           <!-- 显示选择的文件名称 -->
           <span id="fileName" class="ml-4">没有选择文件</span>
         </template>
       </Form>
       <view v-if="startUpload" style="width:900px">
         <div id="folderProgressArea" class="folderProgressArea">
           文件夹总进度：<div id="folderProgress" class="folderProgress ">0%</div>
         </div>
         <div style="display: flex;flex-direction:column;justify-content: flex-start;align-items: flex-start">
           <div style="margin-bottom: 5px">当前文件进度：</div>
           <div id="fileProgress" class="fileProgress bg-primary text-white "></div>
         </div>
       </view>
     </div>
  </Modal>
</template>
<style scoped lang="less">
@import '#/styles/dark-antd.less';

.root {
 display: flex;
  flex-direction: column;
}
.uploadInput{
  margin-top: 10px;
}
.fileProgress {
  color:white;
}
.folderProgressArea {
  width:100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-bottom: 5px;
  visibility:hidden;
}
</style>

