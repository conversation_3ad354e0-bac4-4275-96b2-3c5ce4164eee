var p=(c,s,e)=>new Promise((t,a)=>{var n=o=>{try{l(e.next(o))}catch(m){a(m)}},r=o=>{try{l(e.throw(o))}catch(m){a(m)}},l=o=>o.done?t(o.value):Promise.resolve(o.value).then(n,r);l((e=e.apply(c,s)).next())});import{u}from"./form-DnT3S1ma.js";import{a4 as f,af as d,ag as _,ah as h,a3 as i,n as w}from"../jse/index-index-BMh_AyeW.js";import{u as D}from"./use-drawer-Qcdpj8Bl.js";const g=f({name:"FormDrawerDemo",__name:"form-drawer-demo",setup(c){const[s,e]=u({schema:[{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field1",label:"字段1",rules:"required"},{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field2",label:"字段2",rules:"required"}],showDefaultActions:!1}),[t,a]=D({onCancel(){a.close()},onConfirm:()=>p(this,null,function*(){yield e.submitForm(),a.close()}),onOpenChange(n){if(n){const{values:r}=a.getData();r&&e.setValues(r)}},title:"内嵌表单示例"});return(n,r)=>(d(),_(i(t),null,{default:h(()=>[w(i(s))]),_:1}))}});export{g as _};
