import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  UseGuards,
  Put,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { OrganizationsService } from './organizations.service';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { QueryOrganizationDto } from './dto/query-organization.dto';
import { Organization } from './entities/organization.entity';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { ResponseDto } from '@/common/dto/response.dto';
import { User } from '@/modules/users/entities/user.entity';

@ApiTags('组织管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('organizations')
export class OrganizationsController {
  constructor(private readonly organizationsService: OrganizationsService) {}

  @Post()
  @ApiOperation({ summary: '创建组织' })
  @ApiResponse({ status: 201, description: '创建成功', type: Organization })
  async create(
    @Body() createOrganizationDto: CreateOrganizationDto,
    @CurrentUser() currentUser: User,
  ): Promise<ResponseDto<Organization>> {
    const organization = await this.organizationsService.create(createOrganizationDto, currentUser.id);
    return ResponseDto.success(organization, '创建成功');
  }

  @Get()
  @ApiOperation({ summary: '获取组织列表' })
  @ApiResponse({ status: 200, description: '获取成功', type: [Organization] })
  async findAll(@Query() queryDto: QueryOrganizationDto): Promise<ResponseDto<Organization[]>> {
    const organizations = await this.organizationsService.findAll(queryDto);
    return ResponseDto.success(organizations, '获取成功');
  }

  @Get('tree')
  @ApiOperation({ summary: '获取组织树形结构' })
  @ApiResponse({ status: 200, description: '获取成功', type: [Organization] })
  async findTree(@Query() queryDto: QueryOrganizationDto): Promise<ResponseDto<Organization[]>> {
    const tree = await this.organizationsService.findTree(queryDto);
    return ResponseDto.success(tree, '获取成功');
  }

  @Get(':id')
  @ApiOperation({ summary: '获取组织详情' })
  @ApiResponse({ status: 200, description: '获取成功', type: Organization })
  async findOne(@Param('id') id: string): Promise<ResponseDto<Organization>> {
    const organization = await this.organizationsService.findOne(+id);
    return ResponseDto.success(organization, '获取成功');
  }

  @Put(':id')
  @ApiOperation({ summary: '更新组织' })
  @ApiResponse({ status: 200, description: '更新成功', type: Organization })
  async update(
    @Param('id') id: string,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
    @CurrentUser() currentUser: User,
  ): Promise<ResponseDto<Organization>> {
    const organization = await this.organizationsService.update(+id, updateOrganizationDto, currentUser.id);
    return ResponseDto.success(organization, '更新成功');
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除组织' })
  @ApiResponse({ status: 200, description: '删除成功' })
  async remove(@Param('id') id: string): Promise<ResponseDto<void>> {
    await this.organizationsService.remove(+id);
    return ResponseDto.success(null, '删除成功');
  }

  @Delete()
  @ApiOperation({ summary: '批量删除组织' })
  @ApiResponse({ status: 200, description: '批量删除成功' })
  async batchRemove(@Body() body: { ids: number[] }): Promise<ResponseDto<void>> {
    await this.organizationsService.batchRemove(body.ids);
    return ResponseDto.success(null, '批量删除成功');
  }
}
