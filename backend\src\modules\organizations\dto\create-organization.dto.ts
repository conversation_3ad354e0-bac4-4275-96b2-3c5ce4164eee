import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsEnum, Min, Max } from 'class-validator';

export class CreateOrganizationDto {
  @ApiProperty({ description: '机构名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '机构编码' })
  @IsString()
  code: string;

  @ApiProperty({ description: '上级机构ID，0为根节点', required: false })
  @IsOptional()
  @IsNumber()
  parentId?: number;

  @ApiProperty({ description: '类别：department部门，office科室' })
  @IsEnum(['department', 'office'])
  type: string;

  @ApiProperty({ description: '排序', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  sortOrder?: number;

  @ApiProperty({ description: '默认角色ID', required: false })
  @IsOptional()
  @IsNumber()
  defaultRoleId?: number;

  @ApiProperty({ description: '状态：1启用，0停用', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  status?: number;

  @ApiProperty({ description: '描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;
}
