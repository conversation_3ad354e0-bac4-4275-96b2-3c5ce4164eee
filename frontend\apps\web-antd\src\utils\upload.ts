import { uploadFolder as uploadFolderApi } from '#/views/project/result/folderUpload.js';
import { useUploadStore } from '#/stores/modules/upload';
import { showSuccess, showWarn } from '#/utils/toast.js';

interface UploadOptions {
  urls?: {
    initUpload?: string;
    uploadChunk?: string;
    uploadProgress?: string;
  };
}

interface UploadCallbacks {
  onSuccess?: () => void;
  onError?: () => void;
  onProgress?: (progress: number) => void;
}

interface FileSystemEntry {
  isFile: boolean;
  isDirectory: boolean;
  file: (callback: (file: File) => void) => void;
  createReader: () => {
    readEntries: (callback: (entries: FileSystemEntry[]) => void) => void;
  };
  fullPath: string;
}

export class FileUploader {
  private uploadStore = useUploadStore();

  async traverseFileTree(item: FileSystemEntry, fileList: File[]): Promise<void> {
    if (item.isFile) {
      return new Promise<void>((resolve) => {
        item.file((file: File) => {
          Object.defineProperty(file, 'webkitRelativePath', {
            writable: true,
            value: item.fullPath.substring(1),
          });
          fileList.push(file);
          resolve();
        });
      });
    } else if (item.isDirectory) {
      const directoryReader = item.createReader();
      return new Promise<void>((resolve) => {
        directoryReader.readEntries(async (entries: FileSystemEntry[]) => {
          for (const entry of entries) {
            await this.traverseFileTree(entry, fileList);
          }
          resolve();
        });
      });
    }
  }

  async handleDroppedFiles(dataTransfer: DataTransfer): Promise<File[]> {
    const droppedFiles: File[] = [];
    if (dataTransfer?.items) {
      const items = dataTransfer.items;
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item?.kind === 'file') {
          const entry = item.webkitGetAsEntry();
          if (entry) {
            await this.traverseFileTree(entry as unknown as FileSystemEntry, droppedFiles);
          }
        }
      }
    }
    return droppedFiles;
  }

  async uploadFiles(
    files: File[],
    options: UploadOptions = {},
    callbacks: UploadCallbacks = {}
  ): Promise<void> {
    if (!files.length) {
      showWarn('请选择文件或文件夹');
      return;
    }

    let currentTaskId: string | undefined;

    try {
      // 获取第一个文件的相对路径，取其第一段作为文件夹名称
      const firstFile = files[0];
      const folderName = firstFile?.webkitRelativePath.split('/')[0];
      const firstFileName = firstFile?.name || '';
      const fileCount = files.length;
      let taskName = null;
      if(options.taskName){
        taskName = `${options.taskName} (${fileCount}个文件)`;
      }else {
        taskName = folderName && folderName.length > 0
        ? `${folderName} (${fileCount}个文件)`
        : firstFileName;
      }

      currentTaskId = this.uploadStore.addTask(taskName, { fileCount });

      const handleError = (taskId: string) => {
        this.uploadStore.setTaskStatus(taskId, 'error');
        callbacks.onError?.();
      };

      const onUpload = async (uploadId) => {
        try {
          showSuccess('上传成功！');
          if (currentTaskId) {
            this.uploadStore.setTaskStatus(currentTaskId, 'completed');
            // 延迟一小段时间后删除任务，让用户能看到完成状态
            setTimeout(() => {
              if (currentTaskId) {
                this.uploadStore.removeTask(currentTaskId);
              }
            }, 1000);
          }
          callbacks.onSuccess?.(uploadId);
        } catch (error) {
          if (currentTaskId) {
            handleError(currentTaskId);
          }
        }
      };

      const onProgress = (progress: { folderProgress: number; chunkProgress: number }) => {
        if (currentTaskId) {
          this.uploadStore.updateProgress(currentTaskId, progress);
        }
        callbacks.onProgress?.(progress.folderProgress);
      };

      await uploadFolderApi(
        files,
        onUpload,
        options.urls,
        () => currentTaskId && handleError(currentTaskId),
        onProgress
      );

    } catch (error) {
      if (currentTaskId) {
        this.uploadStore.setTaskStatus(currentTaskId, 'error');
        callbacks.onError?.();
      }
    }
  }
}

export const fileUploader = new FileUploader();
