var G=Object.defineProperty,H=Object.defineProperties;var T=Object.getOwnPropertyDescriptors;var V=Object.getOwnPropertySymbols;var I=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable;var w=(s,o,t)=>o in s?G(s,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[o]=t,A=(s,o)=>{for(var t in o||(o={}))I.call(o,t)&&w(s,t,o[t]);if(V)for(var t of V(o))R.call(o,t)&&w(s,t,o[t]);return s},D=(s,o)=>H(s,T(o));var x=(s,o,t)=>new Promise((_,c)=>{var k=i=>{try{m(t.next(i))}catch(f){c(f)}},C=i=>{try{m(t.throw(i))}catch(f){c(f)}},m=i=>i.done?_(i.value):Promise.resolve(i.value).then(k,C);m((t=t.apply(s,o)).next())});import{v as J,f as K,a as Q}from"./bootstrap-5OPUVRWy.js";import{u as W}from"./vxe-table-CZ9gPHn5.js";import X from"./EntityDetialModal-CdtxQuDW.js";import Y from"./EntityCreateModal-9qv7l48P.js";import Z from"./EntityUpdateModal-D_UmvE2A.js";import{d as tt,l as ot}from"./entity.api-CPgpBrqe.js";import{g as et,c as nt,s as at}from"./entity.data-u4HDUExc.js";import{s as st}from"./alert-DJKWbMfG.js";import lt from"./EntityUploadModal-DySgipYG.js";import{g as it,b as rt}from"./util-BadkgFi3.js";import{_ as dt}from"./page.vue_vue_type_script_setup_true_lang-D2AkmoUI.js";import{d as ut,r as pt,o as ct,x as mt,j as ft,b as Ct,q as l,s as r,k as d,v as p,f as g,H as yt,t as b}from"../jse/index-index-DyHD_jbN.js";import{u as v}from"./use-modal-uChFuhJy.js";import"./loading-DzjUKA94.js";import"./form-DdFfsSWf.js";import"./EntityLayerData-B2GLrKMm.js";import"./Base-xeJpkIWP.js";/* empty css                                                                     */import"./toast-CQjPPeQ1.js";import"./fileUpload-DI0dJ9zY.js";import"./scene.data-BMXeOdST.js";const gt={class:"actionBar"},vt=ut({__name:"index",setup(s){let o=it(),t=rt(o);const[_,c]=v({connectedComponent:Y,fullscreenButton:!1,destroyOnClose:!0,onClosed:()=>{S.query()}}),[k,C]=v({connectedComponent:Z,fullscreenButton:!1,destroyOnClose:!0,onClosed:()=>{S.query()}}),[m,i]=v({connectedComponent:X,fullscreenButton:!1,destroyOnClose:!0,onClosed:()=>{}}),[f,M]=v({connectedComponent:lt,fullscreenButton:!1,destroyOnClose:!0,onClosed:()=>{}}),E={collapsed:!1,schema:at,showCollapseButton:!0,submitButtonOptions:{content:"查询"},submitOnEnter:!1},$={columns:nt,pagerConfig:{},height:"auto",proxyConfig:{ajax:{query:(y,a)=>x(null,[y,a],function*({page:e},n){const u=yield ot({page:{current:e.currentPage,size:e.pageSize,searchCount:!0},condition:D(A({},n),{category:t})});return{total:u.total,items:u.records}})}},rowConfig:{isHover:!0}},[q,S]=W({formOptions:E,gridOptions:$}),h=pt(null),F=()=>{c.setState({upload:!1,update:!1,category:t}),c.open()};function P(e){C.setState({row:e,upload:!1,update:!0,category:t}),C.open()}const L=()=>{i.close()},O=()=>{M.close()},B=()=>{O(),U()};function N(e){return x(this,null,function*(){st("提示","确定要删除？",()=>{tt(e.dataType,e.id,U)})})}const j=e=>{h.value=e,i.setState({row:e}),i.open()},z=e=>{h.value=e,M.setState({row:e}),M.open()},U=()=>{S.reload()};return ct(()=>{}),(e,n)=>{const y=mt("a-button");return Ct(),ft(d(dt),{"auto-content-height":""},{default:l(()=>[r(d(q),null,{"toolbar-tools":l(()=>[r(d(K),{"pre-icon":"ant-design:plus-outlined",type:"primary",onClick:F},{default:l(()=>n[0]||(n[0]=[p(" 新建数据集 ")])),_:1})]),area:l(({row:a})=>[g("view",null,b(a.area&&a.area.toFixed(2)),1)]),length:l(({row:a})=>[g("view",null,b(a.length&&a.length.toFixed(2)),1)]),"upload-status":l(({row:a})=>[g("view",null,b(d(et)(a)),1)]),action:l(({row:a})=>[g("div",gt,[r(y,{class:"actionButton",type:"link",onClick:u=>P(a)},{default:l(()=>n[1]||(n[1]=[p(" 编辑 ")])),_:2},1032,["onClick"]),r(y,{class:"actionButton",type:"link",onClick:u=>N(a)},{default:l(()=>n[2]||(n[2]=[p(" 删除 ")])),_:2},1032,["onClick"]),r(y,{class:"actionButton",type:"link",onClick:u=>j(a)},{default:l(()=>n[3]||(n[3]=[p(" 查看 ")])),_:2},1032,["onClick"]),yt(r(y,{class:"actionButton",type:"link",onClick:u=>z(a)},{default:l(()=>n[4]||(n[4]=[p(" 上传 ")])),_:2},1032,["onClick"]),[[J,a.uploadStatus==0||a.uploadStatus==3]])])]),default:l(()=>[n[5]||(n[5]=p(" ------ "))]),_:1}),r(d(_),{onSuccess:B}),r(d(k),{onSuccess:B}),r(d(m),{onOnCancel:L}),r(d(f),{row:h.value,onOnCancel:O,onSuccess:B},null,8,["row"])]),_:1})}}}),zt=Q(vt,[["__scopeId","data-v-ab961bf3"]]);export{zt as default};
