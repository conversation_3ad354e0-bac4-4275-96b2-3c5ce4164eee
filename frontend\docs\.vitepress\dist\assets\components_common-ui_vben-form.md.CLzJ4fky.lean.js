import{u as s,s as i}from"./chunks/form.D5WaYQwC.js";import{o as a,I as l,B as h,S as k}from"./chunks/theme.TDvSnEYR.js";import{f as n,j as t,y as E,u as e,h as p,m as r,l as d,p as g,k as y,I as F,aP as c,z as o,ay as C}from"./chunks/framework.C8U7mBUf.js";import"./chunks/index.DyNOf6Q_.js";const B=n({__name:"index",setup(i){const[l]=s({commonConfig:{componentProps:{class:"w-full"}},handleSubmit:function(s){a.success({content:`form values: ${JSON.stringify(s)}`})},layout:"horizontal",schema:[{component:"Input",componentProps:{placeholder:"请输入用户名"},fieldName:"username",label:"字符串"},{component:"InputPassword",componentProps:{placeholder:"请输入密码"},fieldName:"password",label:"密码"},{component:"InputNumber",componentProps:{placeholder:"请输入"},fieldName:"number",label:"数字(带后缀)",suffix:()=>"¥"},{component:"Select",componentProps:{allowClear:!0,filterOption:!0,options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}],placeholder:"请选择",showSearch:!0},fieldName:"options",label:"下拉选"},{component:"RadioGroup",componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]},fieldName:"radioGroup",label:"单选组"},{component:"Radio",fieldName:"radio",label:"",renderComponentContent:()=>({default:()=>["Radio"]})},{component:"CheckboxGroup",componentProps:{name:"cname",options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]},fieldName:"checkboxGroup",label:"多选组"},{component:"Checkbox",fieldName:"checkbox",label:"",renderComponentContent:()=>({default:()=>["我已阅读并同意"]})},{component:"Mentions",componentProps:{options:[{label:"afc163",value:"afc163"},{label:"zombieJ",value:"zombieJ"}],placeholder:"请输入"},fieldName:"mentions",label:"提及"},{component:"Rate",fieldName:"rate",label:"评分"},{component:"Switch",componentProps:{class:"w-auto"},fieldName:"switch",label:"开关"},{component:"DatePicker",fieldName:"datePicker",label:"日期选择框"},{component:"RangePicker",fieldName:"rangePicker",label:"范围选择器"},{component:"TimePicker",fieldName:"timePicker",label:"时间选择框"},{component:"TreeSelect",componentProps:{allowClear:!0,placeholder:"请选择",showSearch:!0,treeData:[{label:"root 1",value:"root 1",children:[{label:"parent 1",value:"parent 1",children:[{label:"parent 1-0",value:"parent 1-0",children:[{label:"my leaf",value:"leaf1"},{label:"your leaf",value:"leaf2"}]},{label:"parent 1-1",value:"parent 1-1"}]},{label:"parent 2",value:"parent 2"}]}],treeNodeFilterProp:"label"},fieldName:"treeSelect",label:"树选择"}],wrapperClass:"grid-cols-1"});return(s,i)=>(t(),E(e(l)))}}),u=n({__name:"index",setup(i){const[l]=s({collapsed:!1,commonConfig:{componentProps:{class:"w-full"}},handleSubmit:function(s){a.success({content:`form values: ${JSON.stringify(s)}`})},layout:"horizontal",schema:[{component:"Input",componentProps:{placeholder:"请输入用户名"},fieldName:"username",label:"字符串"},{component:"InputPassword",componentProps:{placeholder:"请输入密码"},fieldName:"password",label:"密码"},{component:"InputNumber",componentProps:{placeholder:"请输入"},fieldName:"number",label:"数字(带后缀)",suffix:()=>"¥"},{component:"Select",componentProps:{allowClear:!0,filterOption:!0,options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}],placeholder:"请选择",showSearch:!0},fieldName:"options",label:"下拉选"},{component:"DatePicker",fieldName:"datePicker",label:"日期选择框"}],showCollapseButton:!0,submitButtonOptions:{content:"查询"},wrapperClass:"grid-cols-1 md:grid-cols-2"});return(s,i)=>(t(),E(e(l)))}}),m=n({__name:"index",setup(l){const[h]=s({commonConfig:{componentProps:{class:"w-full"}},handleSubmit:function(s){a.success({content:`form values: ${JSON.stringify(s)}`})},layout:"horizontal",schema:[{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field1",label:"字段1",rules:"required"},{component:"Input",componentProps:{placeholder:"请输入"},defaultValue:"默认值",fieldName:"field2",label:"默认值(必填)",rules:"required"},{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field3",label:"默认值(非必填)",rules:i().default("默认值").optional()},{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field31",label:"自定义信息",rules:i().min(1,{message:"最少输入1个字符"})},{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field4",label:"邮箱",rules:i().email("请输入正确的邮箱")},{component:"InputNumber",componentProps:{placeholder:"请输入"},fieldName:"number",label:"数字",rules:"required"},{component:"Select",componentProps:{allowClear:!0,filterOption:!0,options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}],placeholder:"请选择",showSearch:!0},defaultValue:void 0,fieldName:"options",label:"下拉选",rules:"selectRequired"},{component:"RadioGroup",componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]},fieldName:"radioGroup",label:"单选组",rules:"selectRequired"},{component:"CheckboxGroup",componentProps:{name:"cname",options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]},fieldName:"checkboxGroup",label:"多选组",rules:"selectRequired"},{component:"Checkbox",fieldName:"checkbox",label:"",renderComponentContent:()=>({default:()=>["我已阅读并同意"]}),rules:"selectRequired"},{component:"DatePicker",defaultValue:void 0,fieldName:"datePicker",label:"日期选择框",rules:"selectRequired"},{component:"RangePicker",defaultValue:void 0,fieldName:"rangePicker",label:"区间选择框",rules:"selectRequired"},{component:"InputPassword",componentProps:{placeholder:"请输入"},fieldName:"password",label:"密码",rules:"required"}],wrapperClass:"grid-cols-1"});return(s,i)=>(t(),E(e(h)))}}),A=n({__name:"index",setup(i){const[l]=s({handleSubmit:function(s){a.success({content:`form values: ${JSON.stringify(s)}`})},schema:[{component:"Input",defaultValue:"hidden value",dependencies:{show:!1,triggerFields:["field1Switch"]},fieldName:"hiddenField",label:"隐藏字段"},{component:"Switch",defaultValue:!0,fieldName:"field1Switch",help:"通过Dom控制销毁",label:"显示字段1"},{component:"Switch",defaultValue:!0,fieldName:"field2Switch",help:"通过css控制隐藏",label:"显示字段2"},{component:"Switch",fieldName:"field3Switch",label:"禁用字段3"},{component:"Switch",fieldName:"field4Switch",label:"字段4必填"},{component:"Input",dependencies:{if:s=>!!s.field1Switch,triggerFields:["field1Switch"]},fieldName:"field1",label:"字段1"},{component:"Input",dependencies:{show:s=>!!s.field2Switch,triggerFields:["field2Switch"]},fieldName:"field2",label:"字段2"},{component:"Input",dependencies:{disabled:s=>!!s.field3Switch,triggerFields:["field3Switch"]},fieldName:"field3",label:"字段3"},{component:"Input",dependencies:{required:s=>!!s.field4Switch,triggerFields:["field4Switch"]},fieldName:"field4",label:"字段4"},{component:"Input",dependencies:{rules:s=>"123"===s.field1?"required":null,triggerFields:["field1"]},fieldName:"field5",help:"当字段1的值为`123`时，必填",label:"动态rules"},{component:"Select",componentProps:{allowClear:!0,class:"w-full",filterOption:!0,options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}],placeholder:"请选择",showSearch:!0},dependencies:{componentProps:s=>"123"===s.field2?{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"},{label:"选项3",value:"3"}]}:{},triggerFields:["field2"]},fieldName:"field6",help:"当字段2的值为`123`时，更改下拉选项",label:"动态配置"}],wrapperClass:"grid-cols-1 md:grid-cols-2"});return(s,i)=>(t(),E(e(l)))}}),D=n({__name:"index",setup(i){const[h]=s({commonConfig:{componentProps:{class:"w-full"},labelClass:"w-2/6"},handleSubmit:function(s){a.success({content:`form values: ${JSON.stringify(s)}`})},layout:"horizontal",schema:[{component:"Input",fieldName:"field",label:"自定义后缀",suffix:()=>p("span",{class:"text-red-600"},"元")},{component:"Input",fieldName:"field1",label:"自定义组件slot",renderComponentContent:()=>({prefix:()=>"prefix",suffix:()=>"suffix"})},{component:p(l,{placeholder:"请输入"}),fieldName:"field2",label:"自定义组件",rules:"required"},{component:"Input",fieldName:"field3",label:"自定义组件(slot)",rules:"required"}],wrapperClass:"grid-cols-1"});return(s,i)=>(t(),E(e(h),null,{field3:r((s=>[d(e(l),g({placeholder:"请输入"},s),null,16)])),_:1}))}}),b=n({__name:"index",setup(i){const[l,n]=s({commonConfig:{componentProps:{class:"w-full"}},handleSubmit:function(s){a.success({content:`form values: ${JSON.stringify(s)}`})},layout:"horizontal",schema:[{component:"Input",componentProps:{placeholder:"请输入用户名"},fieldName:"field1",label:"field1"},{component:"Select",componentProps:{allowClear:!0,filterOption:!0,options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}],placeholder:"请选择",showSearch:!0},fieldName:"fieldOptions",label:"下拉选"}],wrapperClass:"grid-cols-1 md:grid-cols-2"});function E(s){switch(s){case"batchAddSchema":n.setState((s=>{const i=(null==s?void 0:s.schema)??[],a=[];for(let l=0;l<2;l++)a.push({component:"Input",componentProps:{placeholder:"请输入"},fieldName:`field${l}${Date.now()}`,label:"field+"});return{schema:[...i,...a]}}));break;case"batchDeleteSchema":n.setState((s=>({schema:((null==s?void 0:s.schema)??[]).slice(0,-2)})));break;case"disabled":n.setState({commonConfig:{disabled:!0}});break;case"hiddenAction":n.setState({showDefaultActions:!1});break;case"hiddenResetButton":n.setState({resetButtonOptions:{show:!1}});break;case"hiddenSubmitButton":n.setState({submitButtonOptions:{show:!1}});break;case"labelWidth":n.setState({commonConfig:{labelWidth:150}});break;case"resetDisabled":n.setState({commonConfig:{disabled:!1}});break;case"resetLabelWidth":n.setState({commonConfig:{labelWidth:100}});break;case"showAction":n.setState({showDefaultActions:!0});break;case"showResetButton":n.setState({resetButtonOptions:{show:!0}});break;case"showSubmitButton":n.setState({submitButtonOptions:{show:!0}});break;case"updateActionAlign":n.setState({actionWrapperClass:"text-center"});break;case"updateResetButton":n.setState({resetButtonOptions:{disabled:!0}});break;case"updateSchema":n.updateSchema([{componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"},{label:"选项3",value:"3"}]},fieldName:"fieldOptions"}]),a.success("字段 `fieldOptions` 下拉选项更新成功。");break;case"updateSubmitButton":n.setState({submitButtonOptions:{loading:!0}})}}return(s,i)=>(t(),y("div",null,[d(e(k),{class:"mb-5 flex-wrap"},{default:r((()=>[d(e(h),{onClick:i[0]||(i[0]=s=>E("updateSchema"))},{default:r((()=>i[16]||(i[16]=[F("updateSchema")]))),_:1}),d(e(h),{onClick:i[1]||(i[1]=s=>E("labelWidth"))},{default:r((()=>i[17]||(i[17]=[F("更改labelWidth")]))),_:1}),d(e(h),{onClick:i[2]||(i[2]=s=>E("resetLabelWidth"))},{default:r((()=>i[18]||(i[18]=[F("还原labelWidth")]))),_:1}),d(e(h),{onClick:i[3]||(i[3]=s=>E("disabled"))},{default:r((()=>i[19]||(i[19]=[F("禁用表单")]))),_:1}),d(e(h),{onClick:i[4]||(i[4]=s=>E("resetDisabled"))},{default:r((()=>i[20]||(i[20]=[F("解除禁用")]))),_:1}),d(e(h),{onClick:i[5]||(i[5]=s=>E("hiddenAction"))},{default:r((()=>i[21]||(i[21]=[F("隐藏操作按钮")]))),_:1}),d(e(h),{onClick:i[6]||(i[6]=s=>E("showAction"))},{default:r((()=>i[22]||(i[22]=[F("显示操作按钮")]))),_:1}),d(e(h),{onClick:i[7]||(i[7]=s=>E("hiddenResetButton"))},{default:r((()=>i[23]||(i[23]=[F("隐藏重置按钮")]))),_:1}),d(e(h),{onClick:i[8]||(i[8]=s=>E("showResetButton"))},{default:r((()=>i[24]||(i[24]=[F("显示重置按钮")]))),_:1}),d(e(h),{onClick:i[9]||(i[9]=s=>E("hiddenSubmitButton"))},{default:r((()=>i[25]||(i[25]=[F("隐藏提交按钮")]))),_:1}),d(e(h),{onClick:i[10]||(i[10]=s=>E("showSubmitButton"))},{default:r((()=>i[26]||(i[26]=[F("显示提交按钮")]))),_:1}),d(e(h),{onClick:i[11]||(i[11]=s=>E("updateResetButton"))},{default:r((()=>i[27]||(i[27]=[F("修改重置按钮")]))),_:1}),d(e(h),{onClick:i[12]||(i[12]=s=>E("updateSubmitButton"))},{default:r((()=>i[28]||(i[28]=[F("修改提交按钮")]))),_:1}),d(e(h),{onClick:i[13]||(i[13]=s=>E("updateActionAlign"))},{default:r((()=>i[29]||(i[29]=[F(" 调整操作按钮位置 ")]))),_:1}),d(e(h),{onClick:i[14]||(i[14]=s=>E("batchAddSchema"))},{default:r((()=>i[30]||(i[30]=[F(" 批量添加表单项 ")]))),_:1}),d(e(h),{onClick:i[15]||(i[15]=s=>E("batchDeleteSchema"))},{default:r((()=>i[31]||(i[31]=[F(" 批量删除表单项 ")]))),_:1})])),_:1}),d(e(l))]))}}),f=JSON.parse('{"title":"Vben Form 表单","description":"","frontmatter":{"outline":"deep"},"headers":[],"relativePath":"components/common-ui/vben-form.md","filePath":"components/common-ui/vben-form.md"}'),v={name:"components/common-ui/vben-form.md"},S=Object.assign(v,{setup:s=>(s,i)=>{const a=C("DemoPreview"),l=C("NolebaseGitContributors"),h=C("NolebaseGitChangelog");return t(),y("div",null,[i[6]||(i[6]=c('<h1 id="vben-form-表单" tabindex="-1">Vben Form 表单 <a class="header-anchor" href="#vben-form-表单" aria-label="Permalink to &quot;Vben Form 表单&quot;">​</a></h1><p>框架提供的表单组件，可适配 <code>Element Plus</code>、<code>Ant Design Vue</code>、<code>Naive UI</code> 等框架。</p><blockquote><p>如果文档内没有参数说明，可以尝试在在线示例内寻找</p></blockquote><div class="info custom-block"><p class="custom-block-title">写在前面</p><p>如果你觉得现有组件的封装不够理想，或者不完全符合你的需求，大可以直接使用原生组件，亦或亲手封装一个适合的组件。框架提供的组件并非束缚，使用与否，完全取决于你的需求与自由。</p></div><h2 id="适配器" tabindex="-1">适配器 <a class="header-anchor" href="#适配器" aria-label="Permalink to &quot;适配器&quot;">​</a></h2><p>表单底层使用 <a href="https://vee-validate.logaretm.com/v4/" target="_blank" rel="noreferrer">vee-validate</a> 进行表单验证，所以你可以使用 <code>vee-validate</code> 的所有功能。对于不同的 UI 框架，我们提供了适配器，以便更好的适配不同的 UI 框架。</p><h3 id="适配器说明" tabindex="-1">适配器说明 <a class="header-anchor" href="#适配器说明" aria-label="Permalink to &quot;适配器说明&quot;">​</a></h3><p>每个应用都有不同的 UI 框架，所以在应用的 <code>src/adapter/form</code> 和 <code>src/adapter/component</code> 内部，你可以根据自己的需求，进行组件适配。下面是 <code>Ant Design Vue</code> 的适配器示例代码，可根据注释查看说明：</p><details class="details custom-block"><summary>ant design vue 表单适配器</summary><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> type</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  VbenFormSchema </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">as</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> FormSchema,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  VbenFormProps,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">} </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;@vben/common-ui&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> type</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { ComponentType } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;./component&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { setupVbenForm, useVbenForm </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">as</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> useForm, z } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;@vben/common-ui&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { $t } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;@vben/locales&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">setupVbenForm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">ComponentType</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;({</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  config: {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // ant design vue组件库默认都是 v-model:value</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    baseModelPropName: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;value&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 一些组件是 v-model:checked 或者 v-model:fileList</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    modelPropNameMap: {</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      Checkbox: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;checked&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      Radio: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;checked&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      Switch: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;checked&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      Upload: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;fileList&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  },</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  defineRules: {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 输入项目必填国际化适配</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    required</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: (</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">value</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">_params</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">ctx</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">      if</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> (value </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">===</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> undefined</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> ||</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> value </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">===</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> null</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> ||</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> value.</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">length</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> ===</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) {</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">        return</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> $t</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;ui.formRules.required&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, [ctx.label]);</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      }</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">      return</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> true</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 选择项目必填国际化适配</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    selectRequired</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: (</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">value</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">_params</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">ctx</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">      if</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> (value </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">===</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> undefined</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> ||</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> value </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">===</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> null</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) {</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">        return</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> $t</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;ui.formRules.selectRequired&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, [ctx.label]);</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      }</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">      return</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> true</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  },</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> useVbenForm</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> useForm</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">&lt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ComponentType</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">export</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { useVbenForm, z };</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">export</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> type</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> VbenFormSchema</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> FormSchema</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">ComponentType</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">export</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> type</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { VbenFormProps };</span></span></code></pre></div></details><details class="details custom-block"><summary>ant design vue 组件适配器</summary><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">/**</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> * 通用组件共同的使用的基础组件，原先放在 adapter/form 内部，限制了使用范围，这里提取出来，方便其他地方使用</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> * 可用于 vben-form、vben-modal、vben-drawer 等组件使用,</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> */</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> type</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { BaseFormComponentType } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;@vben/common-ui&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> type</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { Component, SetupContext } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;vue&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { h } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;vue&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { globalShareState } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;@vben/common-ui&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { $t } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;@vben/locales&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  AutoComplete,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Button,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Checkbox,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  CheckboxGroup,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  DatePicker,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Divider,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Input,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  InputNumber,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  InputPassword,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Mentions,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  notification,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Radio,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  RadioGroup,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  RangePicker,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Rate,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Select,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Space,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Switch,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Textarea,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  TimePicker,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  TreeSelect,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  Upload,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">} </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;ant-design-vue&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> withDefaultPlaceholder</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> &lt;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">T</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> extends</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> Component</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;(</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  component</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> T</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  type</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;input&#39;</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;select&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  return</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> (</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">props</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> any</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, { </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">attrs</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">slots</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> }</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> Omit</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">SetupContext</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;expose&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">    const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> placeholder</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> props?.placeholder </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">||</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> $t</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">`ui.placeholder.${</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">type</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">}`</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">);</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">    return</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> h</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(component, { </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">...</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">props, </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">...</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">attrs, placeholder }, slots);</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  };</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">};</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 这里需要自行根据业务组件库进行适配，需要用到的组件都需要在这里类型说明</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">export</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> type</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ComponentType</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;AutoComplete&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;Checkbox&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;CheckboxGroup&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;DatePicker&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;DefaultButton&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;Divider&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;Input&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;InputNumber&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;InputPassword&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;Mentions&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;PrimaryButton&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;Radio&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;RadioGroup&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;RangePicker&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;Rate&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;Select&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;Space&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;Switch&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;Textarea&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;TimePicker&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;TreeSelect&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;Upload&#39;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  |</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> BaseFormComponentType</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">async</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> function</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> initComponentAdapter</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">() {</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  const</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> components</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> Partial</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">Record</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">ComponentType</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">Component</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;&gt; </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 如果你的组件体积比较大，可以使用异步加载</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // Button: () =&gt;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // import(&#39;xxx&#39;).then((res) =&gt; res.Button),</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    AutoComplete,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Checkbox,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    CheckboxGroup,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    DatePicker,</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 自定义默认按钮</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    DefaultButton</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: (</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">props</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, { </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">attrs</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">slots</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> }) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">      return</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> h</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(Button, { </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">...</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">props, attrs, type: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;default&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> }, slots);</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Divider,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Input: </span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">withDefaultPlaceholder</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(Input, </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;input&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">),</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    InputNumber: </span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">withDefaultPlaceholder</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(InputNumber, </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;input&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">),</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    InputPassword: </span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">withDefaultPlaceholder</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(InputPassword, </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;input&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">),</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Mentions: </span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">withDefaultPlaceholder</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(Mentions, </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;input&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">),</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 自定义主要按钮</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    PrimaryButton</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: (</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">props</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, { </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">attrs</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">slots</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> }) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">      return</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> h</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(Button, { </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">...</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">props, attrs, type: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;primary&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> }, slots);</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Radio,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    RadioGroup,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    RangePicker,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Rate,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Select: </span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">withDefaultPlaceholder</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(Select, </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;select&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">),</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Space,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Switch,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Textarea: </span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">withDefaultPlaceholder</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(Textarea, </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;input&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">),</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    TimePicker,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    TreeSelect: </span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">withDefaultPlaceholder</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(TreeSelect, </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;select&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">),</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    Upload,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  };</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 将组件注册到全局共享状态中</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  globalShareState.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">setComponents</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(components);</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 定义全局共享状态中的消息提示</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  globalShareState.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">defineMessage</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 复制成功消息提示</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    copyPreferencesSuccess</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: (</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">title</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">content</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      notification.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">success</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        description: content,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        message: title,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">        placement: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;bottomRight&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">      });</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    },</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  });</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">export</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { initComponentAdapter };</span></span></code></pre></div></details><h2 id="基础用法" tabindex="-1">基础用法 <a class="header-anchor" href="#基础用法" aria-label="Permalink to &quot;基础用法&quot;">​</a></h2><div class="tip custom-block"><p class="custom-block-title">README</p><p>下方示例代码中的，存在一些国际化、主题色未适配问题，这些问题只在文档内会出现，实际使用并不会有这些问题，可忽略，不必纠结。</p></div><p>使用 <code>useVbenForm</code> 创建最基础的表单。</p>',13)),d(a,{files:"%5B%22index.vue%22%5D"},{"index.vue":r((()=>i[0]||(i[0]=[o("div",{class:"language-vue vp-adaptive-theme"},[o("button",{title:"Copy Code",class:"copy"}),o("span",{class:"lang"},"vue"),o("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[o("code",null,[o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { message } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'ant-design-vue'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenForm } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '#/adapter/form'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"BaseForm"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenForm"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 所有表单项共用，可单独在表单内覆盖")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  commonConfig: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"    // 所有表单项")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      class: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'w-full'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 提交函数")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  handleSubmit: onSubmit,")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 垂直布局，label和input在不同行，值为vertical")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 水平布局，label和input在同一行")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  layout: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'horizontal'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  schema: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 组件需要在 #/adapter.ts内注册，并加上类型")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 对应组件的参数")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入用户名'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 字段名")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'username'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 界面显示的label")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'字符串'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'InputPassword'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入密码'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'password'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'密码'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'InputNumber'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'number'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'数字(带后缀)'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"      suffix"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": () "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '¥'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Select'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        allowClear: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        filterOption: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        options: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请选择'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        showSearch: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'options'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'下拉选'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'RadioGroup'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        options: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'radioGroup'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'单选组'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Radio'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'radio'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"''"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"      renderComponentContent"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": () "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"        return"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"          default"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": () "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Radio'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        };")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'CheckboxGroup'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        name: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'cname'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        options: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'checkboxGroup'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'多选组'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Checkbox'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'checkbox'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"''"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"      renderComponentContent"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": () "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"        return"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"          default"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": () "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'我已阅读并同意'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        };")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Mentions'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        options: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'afc163'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'afc163'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'zombieJ'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'zombieJ'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'mentions'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'提及'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Rate'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'rate'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'评分'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        class: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'w-auto'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'开关'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'DatePicker'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'datePicker'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'日期选择框'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'RangePicker'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'rangePicker'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'范围选择器'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'TimePicker'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'timePicker'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'时间选择框'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'TreeSelect'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        allowClear: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请选择'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        showSearch: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        treeData: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'root 1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'root 1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            children: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'parent 1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'parent 1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                children: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                  {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                    label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'parent 1-0'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                    value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'parent 1-0'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                    children: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                      {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                        label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'my leaf'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                        value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'leaf1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                      {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                        label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'your leaf'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                        value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'leaf2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                    ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                  },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                  {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                    label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'parent 1-1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                    value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'parent 1-1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                  },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'parent 2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'parent 2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        treeNodeFilterProp: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'label'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'treeSelect'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'树选择'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  wrapperClass: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'grid-cols-1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," onSubmit"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"values"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},":"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," Record"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"string"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"any"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">) {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  message."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"success"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    content: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"`form values: ${"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"JSON"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"stringify"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"("),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"values"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"}`"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"BaseForm"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," />")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:r((()=>[d(B)])),_:1}),i[7]||(i[7]=o("h2",{id:"查询表单",tabindex:"-1"},[F("查询表单 "),o("a",{class:"header-anchor",href:"#查询表单","aria-label":'Permalink to "查询表单"'},"​")],-1)),i[8]||(i[8]=o("p",null,"查询表单是一种特殊的表单，用于查询数据。查询表单不会触发表单验证，只会触发查询事件。",-1)),d(a,{files:"%5B%22index.vue%22%5D"},{"index.vue":r((()=>i[1]||(i[1]=[o("div",{class:"language-vue vp-adaptive-theme"},[o("button",{title:"Copy Code",class:"copy"}),o("span",{class:"lang"},"vue"),o("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[o("code",null,[o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { message } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'ant-design-vue'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenForm } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '#/adapter/form'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"QueryForm"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenForm"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 默认展开")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  collapsed: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"false"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 所有表单项共用，可单独在表单内覆盖")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  commonConfig: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"    // 所有表单项")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      class: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'w-full'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 提交函数")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  handleSubmit: onSubmit,")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 垂直布局，label和input在不同行，值为vertical")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 水平布局，label和input在同一行")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  layout: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'horizontal'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  schema: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 组件需要在 #/adapter.ts内注册，并加上类型")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 对应组件的参数")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入用户名'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 字段名")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'username'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 界面显示的label")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'字符串'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'InputPassword'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入密码'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'password'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'密码'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'InputNumber'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'number'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'数字(带后缀)'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"      suffix"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": () "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '¥'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Select'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        allowClear: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        filterOption: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        options: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请选择'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        showSearch: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'options'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'下拉选'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'DatePicker'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'datePicker'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'日期选择框'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 是否可展开")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  showCollapseButton: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  submitButtonOptions: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    content: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'查询'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  wrapperClass: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'grid-cols-1 md:grid-cols-2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," onSubmit"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"values"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},":"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," Record"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"string"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"any"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">) {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  message."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"success"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    content: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"`form values: ${"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"JSON"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"stringify"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"("),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"values"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"}`"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"QueryForm"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," />")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:r((()=>[d(u)])),_:1}),i[9]||(i[9]=o("h2",{id:"表单校验",tabindex:"-1"},[F("表单校验 "),o("a",{class:"header-anchor",href:"#表单校验","aria-label":'Permalink to "表单校验"'},"​")],-1)),i[10]||(i[10]=o("p",null,[F("表单校验是一个非常重要的功能，可以通过 "),o("code",null,"rules"),F(" 属性进行校验。")],-1)),d(a,{files:"%5B%22index.vue%22%5D"},{"index.vue":r((()=>i[2]||(i[2]=[o("div",{class:"language-vue vp-adaptive-theme"},[o("button",{title:"Copy Code",class:"copy"}),o("span",{class:"lang"},"vue"),o("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[o("code",null,[o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { message } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'ant-design-vue'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenForm, z } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '#/adapter/form'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"Form"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenForm"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 所有表单项共用，可单独在表单内覆盖")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  commonConfig: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"    // 所有表单项")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      class: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'w-full'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 提交函数")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  handleSubmit: onSubmit,")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 垂直布局，label和input在不同行，值为vertical")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 水平布局，label和input在同一行")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  layout: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'horizontal'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  schema: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 组件需要在 #/adapter.ts内注册，并加上类型")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 对应组件的参数")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 字段名")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 界面显示的label")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'字段1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'required'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      defaultValue: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'默认值'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'默认值(必填)'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'required'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field3'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'默认值(非必填)'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: z."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"string"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"()."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"default"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'默认值'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"optional"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"(),")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field31'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'自定义信息'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: z."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"string"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"()."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"min"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"1"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", { message: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'最少输入1个字符'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," }),")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 对应组件的参数")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 字段名")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field4'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 界面显示的label")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'邮箱'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: z."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"string"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"()."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"email"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入正确的邮箱'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"),")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'InputNumber'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'number'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'数字'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'required'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Select'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        allowClear: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        filterOption: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        options: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请选择'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        showSearch: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      defaultValue: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"undefined"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'options'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'下拉选'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'selectRequired'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'RadioGroup'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        options: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'radioGroup'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'单选组'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'selectRequired'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'CheckboxGroup'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        name: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'cname'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        options: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'checkboxGroup'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'多选组'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'selectRequired'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Checkbox'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'checkbox'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"''"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"      renderComponentContent"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": () "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"        return"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"          default"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": () "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'我已阅读并同意'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        };")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'selectRequired'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'DatePicker'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      defaultValue: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"undefined"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'datePicker'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'日期选择框'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'selectRequired'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'RangePicker'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      defaultValue: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"undefined"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'rangePicker'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'区间选择框'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'selectRequired'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'InputPassword'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'password'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'密码'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'required'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  wrapperClass: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'grid-cols-1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," onSubmit"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"values"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},":"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," Record"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"string"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"any"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">) {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  message."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"success"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    content: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"`form values: ${"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"JSON"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"stringify"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"("),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"values"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"}`"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Form"),o("span",{style:{"--shiki-light":"#B31D28","--shiki-light-font-style":"italic","--shiki-dark":"#FDAEB7","--shiki-dark-font-style":"italic"}}," /"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:r((()=>[d(m)])),_:1}),i[11]||(i[11]=o("h2",{id:"表单联动",tabindex:"-1"},[F("表单联动 "),o("a",{class:"header-anchor",href:"#表单联动","aria-label":'Permalink to "表单联动"'},"​")],-1)),i[12]||(i[12]=o("p",null,[F("表单联动是一个非常常见的功能，可以通过 "),o("code",null,"dependencies"),F(" 属性进行联动。")],-1)),i[13]||(i[13]=o("p",null,[o("em",null,"注意"),F(" 需要指定 "),o("code",null,"dependencies"),F(" 的 "),o("code",null,"triggerFields"),F(" 属性，设置由谁的改动来触发，以便表单组件能够正确的联动。")],-1)),d(a,{files:"%5B%22index.vue%22%5D"},{"index.vue":r((()=>i[3]||(i[3]=[o("div",{class:"language-vue vp-adaptive-theme"},[o("button",{title:"Copy Code",class:"copy"}),o("span",{class:"lang"},"vue"),o("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[o("code",null,[o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { message } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'ant-design-vue'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenForm } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '#/adapter/form'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"Form"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenForm"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 提交函数")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  handleSubmit: onSubmit,")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  schema: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      defaultValue: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'hidden value'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      dependencies: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        show: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"false"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"        // 随意一个字段改变时，都会触发")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        triggerFields: ["),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field1Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'hiddenField'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'隐藏字段'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      defaultValue: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field1Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      help: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'通过Dom控制销毁'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'显示字段1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      defaultValue: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field2Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      help: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'通过css控制隐藏'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'显示字段2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field3Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'禁用字段3'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field4Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'字段4必填'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      dependencies: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"        if"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"values"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"          return"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}}," !!"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"values.field1Switch;")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"        // 只有指定的字段改变时，才会触发")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        triggerFields: ["),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field1Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 字段名")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 界面显示的label")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'字段1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      dependencies: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"        show"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"values"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"          return"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}}," !!"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"values.field2Switch;")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        triggerFields: ["),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field2Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'字段2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      dependencies: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"        disabled"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"values"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"          return"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}}," !!"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"values.field3Switch;")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        triggerFields: ["),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field3Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field3'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'字段3'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      dependencies: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"        required"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"values"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"          return"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}}," !!"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"values.field4Switch;")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        triggerFields: ["),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field4Switch'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field4'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'字段4'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      dependencies: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"        rules"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"values"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"          if"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," (values.field1 "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"==="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '123'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"            return"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'required'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"          return"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," null"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        triggerFields: ["),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field5'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      help: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'当字段1的值为`123`时，必填'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'动态rules'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Select'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        allowClear: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        class: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'w-full'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        filterOption: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        options: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请选择'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        showSearch: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      dependencies: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"        componentProps"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"values"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"          if"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," (values.field2 "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"==="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '123'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"            return"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              options: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                  label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                  value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                  label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                  value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                  label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项3'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                  value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'3'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            };")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"          return"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {};")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        triggerFields: ["),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field6'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      help: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'当字段2的值为`123`时，更改下拉选项'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'动态配置'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  wrapperClass: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'grid-cols-1 md:grid-cols-2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," onSubmit"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"values"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},":"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," Record"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"string"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"any"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">) {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  message."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"success"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    content: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"`form values: ${"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"JSON"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"stringify"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"("),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"values"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"}`"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Form"),o("span",{style:{"--shiki-light":"#B31D28","--shiki-light-font-style":"italic","--shiki-dark":"#FDAEB7","--shiki-dark-font-style":"italic"}}," /"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:r((()=>[d(A)])),_:1}),i[14]||(i[14]=o("h2",{id:"自定义组件",tabindex:"-1"},[F("自定义组件 "),o("a",{class:"header-anchor",href:"#自定义组件","aria-label":'Permalink to "自定义组件"'},"​")],-1)),i[15]||(i[15]=o("p",null,"如果你的业务组件库没有提供某个组件，你可以自行封装一个组件，然后加到表单内部。",-1)),d(a,{files:"%5B%22index.vue%22%5D"},{"index.vue":r((()=>i[4]||(i[4]=[o("div",{class:"language-vue vp-adaptive-theme"},[o("button",{title:"Copy Code",class:"copy"}),o("span",{class:"lang"},"vue"),o("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[o("code",null,[o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { h } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'vue'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { Input, message } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'ant-design-vue'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenForm } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '#/adapter/form'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"Form"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenForm"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 所有表单项共用，可单独在表单内覆盖")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  commonConfig: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"    // 所有表单项")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      class: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'w-full'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    labelClass: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'w-2/6'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 提交函数")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  handleSubmit: onSubmit,")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 垂直布局，label和input在不同行，值为vertical")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 水平布局，label和input在同一行")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  layout: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'horizontal'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  schema: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 组件需要在 #/adapter.ts内注册，并加上类型")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'自定义后缀'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"      suffix"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": () "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," h"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'span'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", { class: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'text-red-600'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," }, "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'元'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"),")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'自定义组件slot'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"      renderComponentContent"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": () "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"        prefix"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": () "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'prefix'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"        suffix"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": () "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'suffix'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      }),")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"h"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"(Input, { placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," }),")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'自定义组件'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'required'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field3'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'自定义组件(slot)'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      rules: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'required'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  wrapperClass: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'grid-cols-1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," onSubmit"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"values"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},":"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," Record"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"string"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"any"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">) {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  message."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"success"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    content: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"`form values: ${"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"JSON"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"stringify"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"("),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"values"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"}`"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Form"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," #"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"field3"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"slotProps"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Input"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," placeholder"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"请输入"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," v-bind"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"slotProps"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," />")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    </"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  </"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Form"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:r((()=>[d(D)])),_:1}),i[16]||(i[16]=o("h2",{id:"操作",tabindex:"-1"},[F("操作 "),o("a",{class:"header-anchor",href:"#操作","aria-label":'Permalink to "操作"'},"​")],-1)),i[17]||(i[17]=o("p",null,"一些常见的表单操作。",-1)),d(a,{files:"%5B%22index.vue%22%5D"},{"index.vue":r((()=>i[5]||(i[5]=[o("div",{class:"language-vue vp-adaptive-theme"},[o("button",{title:"Copy Code",class:"copy"}),o("span",{class:"lang"},"vue"),o("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[o("code",null,[o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { Button, message, Space } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'ant-design-vue'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenForm } "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '#/adapter/form'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"BaseForm"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"formApi"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenForm"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 所有表单项共用，可单独在表单内覆盖")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  commonConfig: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"    // 所有表单项")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      class: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'w-full'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 使用 tailwindcss grid布局")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 提交函数")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  handleSubmit: onSubmit,")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 垂直布局，label和input在不同行，值为vertical")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  layout: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'horizontal'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 水平布局，label和input在同一行")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  schema: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 组件需要在 #/adapter.ts内注册，并加上类型")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 对应组件的参数")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入用户名'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 字段名")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"      // 界面显示的label")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'field1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Select'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        allowClear: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        filterOption: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        options: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请选择'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        showSearch: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'fieldOptions'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'下拉选'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  wrapperClass: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'grid-cols-1 md:grid-cols-2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," onSubmit"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"values"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},":"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," Record"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"string"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"any"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">) {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  message."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"success"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    content: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"`form values: ${"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"JSON"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"stringify"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"("),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"values"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"}`"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"(")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"  action"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},":")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'batchAddSchema'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'batchDeleteSchema'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'disabled'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'hiddenAction'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'hiddenResetButton'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'hiddenSubmitButton'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'labelWidth'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'resetDisabled'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'resetLabelWidth'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'showAction'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'showResetButton'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'showSubmitButton'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'updateActionAlign'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'updateResetButton'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'updateSchema'")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    |"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'updateSubmitButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"  switch"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," (action) {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'batchAddSchema'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"(("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"prev"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"        const"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," currentSchema"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}}," ="),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," prev?.schema "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"??"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," [];")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"        const"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," newSchema"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}}," ="),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," [];")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"        for"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ("),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"let"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," i "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," 0"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"; i "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"<"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," 2"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"; i"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"++"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          newSchema."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"push"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            component: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'Input'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              placeholder: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'请输入'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"`field${"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"i"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"}${"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"Date"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"now"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"()"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"}`"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"`field+`"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"        return"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          schema: ["),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"..."),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"currentSchema, "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"..."),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"newSchema],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        };")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'batchDeleteSchema'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"(("),o("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"prev"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"        const"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," currentSchema"),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}}," ="),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," prev?.schema "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"??"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," [];")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"        return"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          schema: currentSchema."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"slice"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"0"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"-"),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"2"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"),")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        };")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'disabled'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ commonConfig: { disabled: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," } });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'hiddenAction'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ showDefaultActions: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"false"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'hiddenResetButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ resetButtonOptions: { show: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"false"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," } });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'hiddenSubmitButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ submitButtonOptions: { show: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"false"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," } });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'labelWidth'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        commonConfig: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          labelWidth: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"150"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'resetDisabled'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ commonConfig: { disabled: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"false"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," } });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'resetLabelWidth'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        commonConfig: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          labelWidth: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"100"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'showAction'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ showDefaultActions: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'showResetButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ resetButtonOptions: { show: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," } });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'showSubmitButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ submitButtonOptions: { show: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," } });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'updateActionAlign'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"        // 可以自行调整class")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        actionWrapperClass: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'text-center'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'updateResetButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        resetButtonOptions: { disabled: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'updateSchema'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"updateSchema"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"([")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          componentProps: {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            options: [")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'1'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'2'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                label: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'选项3'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"                value: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'3'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"              },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"            ],")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"          fieldName: "),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'fieldOptions'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      ]);")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      message."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"success"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'字段 `fieldOptions` 下拉选项更新成功。'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},");")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    case"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'updateSubmitButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},": {")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      formApi."),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        submitButtonOptions: { loading: "),o("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," },")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      });")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      break"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  }")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"}),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Space"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," class"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"mb-5 flex-wrap"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'updateSchema'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">updateSchema</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'labelWidth'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">更改labelWidth</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'resetLabelWidth'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">还原labelWidth</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'disabled'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">禁用表单</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'resetDisabled'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">解除禁用</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'hiddenAction'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">隐藏操作按钮</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'showAction'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">显示操作按钮</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'hiddenResetButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">隐藏重置按钮</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'showResetButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">显示重置按钮</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'hiddenSubmitButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">隐藏提交按钮</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'showSubmitButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">显示提交按钮</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'updateResetButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">修改重置按钮</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'updateSubmitButton'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">修改提交按钮</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'updateActionAlign'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        调整操作按钮位置")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      </"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'batchAddSchema'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"> 批量添加表单项 </"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleClick"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'batchDeleteSchema'"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),o("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        批量删除表单项")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      </"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Button"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    </"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Space"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"BaseForm"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," />")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  </"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),F("\n"),o("span",{class:"line"},[o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),o("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),o("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:r((()=>[d(b)])),_:1}),i[18]||(i[18]=c('<h2 id="api" tabindex="-1">API <a class="header-anchor" href="#api" aria-label="Permalink to &quot;API&quot;">​</a></h2><p><code>useVbenForm</code> 返回一个数组，第一个元素是表单组件，第二个元素是表单的方法。</p><div class="language-vue vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">vue</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> setup</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> lang</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;ts&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { useVbenForm } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;#/adapter/form&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// Form 为弹窗组件</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// formApi 为弹窗的方法</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> [</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">Form</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">formApi</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">] </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useVbenForm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 属性</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 事件</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">script</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">Form</span><span style="--shiki-light:#B31D28;--shiki-light-font-style:italic;--shiki-dark:#FDAEB7;--shiki-dark-font-style:italic;"> /</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><h3 id="formapi" tabindex="-1">FormApi <a class="header-anchor" href="#formapi" aria-label="Permalink to &quot;FormApi&quot;">​</a></h3><p>useVbenForm 返回的第二个参数，是一个对象，包含了一些表单的方法。</p><table tabindex="0"><thead><tr><th>方法名</th><th>描述</th><th>类型</th></tr></thead><tbody><tr><td>submitForm</td><td>提交表单</td><td><code>(e:Event)=&gt;Promise&lt;Record&lt;string,any&gt;&gt;</code></td></tr><tr><td>validateAndSubmitForm</td><td>提交并校验表单</td><td><code>(e:Event)=&gt;Promise&lt;Record&lt;string,any&gt;&gt;</code></td></tr><tr><td>resetForm</td><td>重置表单</td><td><code>()=&gt;Promise&lt;void&gt;</code></td></tr><tr><td>setValues</td><td>设置表单值, 默认会过滤不在schema中定义的field, 可通过filterFields形参关闭过滤</td><td><code>(fields: Record&lt;string, any&gt;, filterFields?: boolean, shouldValidate?: boolean) =&gt; Promise&lt;void&gt;</code></td></tr><tr><td>getValues</td><td>获取表单值</td><td><code>(fields:Record&lt;string, any&gt;,shouldValidate: boolean = false)=&gt;Promise&lt;void&gt;</code></td></tr><tr><td>validate</td><td>表单校验</td><td><code>()=&gt;Promise&lt;void&gt;</code></td></tr><tr><td>resetValidate</td><td>重置表单校验</td><td><code>()=&gt;Promise&lt;void&gt;</code></td></tr><tr><td>updateSchema</td><td>更新formSchema</td><td><code>(schema:FormSchema[])=&gt;void</code></td></tr><tr><td>setFieldValue</td><td>设置字段值</td><td><code>(field: string, value: any, shouldValidate?: boolean)=&gt;Promise&lt;void&gt;</code></td></tr><tr><td>setState</td><td>设置组件状态（props）</td><td><code>(stateOrFn:| ((prev: VbenFormProps) =&gt; Partial&lt;VbenFormProps&gt;)| Partial&lt;VbenFormProps&gt;)=&gt;Promise&lt;void&gt;</code></td></tr><tr><td>getState</td><td>获取组件状态（props）</td><td><code>()=&gt;Promise&lt;VbenFormProps&gt;</code></td></tr><tr><td>form</td><td>表单对象实例，可以操作表单，见 <a href="https://vee-validate.logaretm.com/v4/api/use-form/" target="_blank" rel="noreferrer">useForm</a></td><td>-</td></tr></tbody></table><h2 id="props" tabindex="-1">Props <a class="header-anchor" href="#props" aria-label="Permalink to &quot;Props&quot;">​</a></h2><p>所有属性都可以传入 <code>useVbenForm</code> 的第一个参数中。</p><table tabindex="0"><thead><tr><th>属性名</th><th>描述</th><th>类型</th><th>默认值</th></tr></thead><tbody><tr><td>layout</td><td>表单项布局</td><td><code>&#39;horizontal&#39; | &#39;vertical&#39;</code></td><td><code>horizontal</code></td></tr><tr><td>showCollapseButton</td><td>是否显示折叠按钮</td><td><code>boolean</code></td><td><code>false</code></td></tr><tr><td>wrapperClass</td><td>表单的布局，基于tailwindcss</td><td><code>any</code></td><td>-</td></tr><tr><td>actionWrapperClass</td><td>表单操作区域class</td><td><code>any</code></td><td>-</td></tr><tr><td>handleReset</td><td>表单重置回调</td><td><code>(values: Record&lt;string, any&gt;,) =&gt; Promise&lt;void&gt; | void</code></td><td>-</td></tr><tr><td>handleSubmit</td><td>表单提交回调</td><td><code>(values: Record&lt;string, any&gt;,) =&gt; Promise&lt;void&gt; | void</code></td><td>-</td></tr><tr><td>resetButtonOptions</td><td>重置按钮组件参数</td><td><code>ActionButtonOptions</code></td><td>-</td></tr><tr><td>submitButtonOptions</td><td>提交按钮组件参数</td><td><code>ActionButtonOptions</code></td><td>-</td></tr><tr><td>showDefaultActions</td><td>是否显示默认操作按钮</td><td><code>boolean</code></td><td><code>true</code></td></tr><tr><td>collapsed</td><td>是否折叠，在<code>是否展开，在showCollapseButton=true</code>时生效</td><td><code>boolean</code></td><td><code>false</code></td></tr><tr><td>collapseTriggerResize</td><td>折叠时，触发<code>resize</code>事件</td><td><code>boolean</code></td><td><code>false</code></td></tr><tr><td>collapsedRows</td><td>折叠时保持的行数</td><td><code>number</code></td><td><code>1</code></td></tr><tr><td>fieldMappingTime</td><td>用于将表单内时间区域的应设成 2 个字段</td><td><code>[string, [string, string], string?][]</code></td><td>-</td></tr><tr><td>commonConfig</td><td>表单项的通用配置，每个配置都会传递到每个表单项，表单项可覆盖</td><td><code>FormCommonConfig</code></td><td>-</td></tr><tr><td>schema</td><td>表单项的每一项配置</td><td><code>FormSchema</code></td><td>-</td></tr><tr><td>submitOnEnter</td><td>按下回车健时提交表单</td><td><code>boolean</code></td><td>false</td></tr></tbody></table><h3 id="ts-类型说明" tabindex="-1">TS 类型说明 <a class="header-anchor" href="#ts-类型说明" aria-label="Permalink to &quot;TS 类型说明&quot;">​</a></h3><details class="details custom-block"><summary>ActionButtonOptions</summary><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">export</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> interface</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ActionButtonOptions</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 样式 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  class</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ClassType</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 是否禁用 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  disabled</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> boolean</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 是否加载中 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  loading</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> boolean</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 按钮大小 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  size</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ButtonVariantSize</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 按钮类型 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  variant</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ButtonVariants</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 是否显示 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  show</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> boolean</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 按钮文本 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  text</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 任意属性 */</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  [</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">key</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">]</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> any</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div></details><details class="details custom-block"><summary>FormCommonConfig</summary><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">export</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> interface</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> FormCommonConfig</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /**</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * 所有表单项的props</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  componentProps</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ComponentProps</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /**</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * 所有表单项的控件样式</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  controlClass</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /**</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * 所有表单项的禁用状态</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">@default</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> false</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  disabled</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> boolean</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /**</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * 所有表单项的控件样式</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">@default</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {}</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  formFieldProps</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> Partial</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">typeof</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> Field&gt;;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /**</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * 所有表单项的栅格布局</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">@default</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;&quot;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  formItemClass</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /**</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * 隐藏所有表单项label</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">@default</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> false</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  hideLabel</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> boolean</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /**</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * 是否隐藏必填标记</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">@default</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> false</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  hideRequiredMark</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> boolean</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /**</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * 所有表单项的label样式</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">@default</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;&quot;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  labelClass</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /**</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * 所有表单项的label宽度</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  labelWidth</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> number</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /**</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   * 所有表单项的wrapper样式</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">   */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  wrapperClass</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div></details><details class="details custom-block"><summary>FormSchema</summary><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">export</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> interface</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> FormSchema</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  T</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> extends</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> BaseFormComponentType</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> =</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> BaseFormComponentType</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt; </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">extends</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> FormCommonConfig</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 组件 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  component</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> Component</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;"> |</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> T</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 组件参数 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  componentProps</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> ComponentProps</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 默认值 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  defaultValue</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> any</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 依赖 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  dependencies</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> FormItemDependencies</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 描述 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  description</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 字段名 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  fieldName</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 帮助信息 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  help</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 表单项 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  label</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 自定义组件内部渲染</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  renderComponentContent</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> RenderComponentContentType</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 字段规则 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  rules</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> FormSchemaRuleType</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /** 后缀 */</span></span>\n<span class="line"><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">  suffix</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?:</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> CustomRenderType</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div></details><h3 id="表单联动-1" tabindex="-1">表单联动 <a class="header-anchor" href="#表单联动-1" aria-label="Permalink to &quot;表单联动&quot;">​</a></h3><p>表单联动需要通过 schema 内的 <code>dependencies</code> 属性进行联动，允许您添加字段之间的依赖项，以根据其他字段的值控制字段。</p><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">dependencies</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 只有当 name 字段的值变化时，才会触发联动</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  triggerFields</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;name&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">],</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 动态判断当前字段是否需要显示，不显示则直接销毁</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  if</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(values,formApi){},</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 动态判断当前字段是否需要显示，不显示用css隐藏</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  show</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(values,formApi){},</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 动态判断当前字段是否需要禁用</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  disabled</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(values,formApi){},</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 字段变更时，都会触发该函数</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  trigger</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(values,formApi){},</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 动态rules</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  rules</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(values,formApi){},</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 动态必填</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  required</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(values,formApi){},</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 动态组件参数</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  componentProps</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(values,formApi){},</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><h3 id="表单校验-1" tabindex="-1">表单校验 <a class="header-anchor" href="#表单校验-1" aria-label="Permalink to &quot;表单校验&quot;">​</a></h3><p>表单联动需要通过 schema 内的 <code>rules</code> 属性进行配置。</p><p>rules的值可以是一个字符串，也可以是一个zod的schema。</p><h4 id="字符串" tabindex="-1">字符串 <a class="header-anchor" href="#字符串" aria-label="Permalink to &quot;字符串&quot;">​</a></h4><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 表示字段必填，默认会根据适配器的required进行国际化</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  rules</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;required&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 表示字段必填，默认会根据适配器的required进行国际化，用于下拉选择之类</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  rules</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;selectRequired&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><h4 id="zod" tabindex="-1">zod <a class="header-anchor" href="#zod" aria-label="Permalink to &quot;zod&quot;">​</a></h4><p>rules也支持 zod 的 schema，可以进行更复杂的校验，zod 的使用请查看 <a href="https://zod.dev/" target="_blank" rel="noreferrer">zod文档</a>。</p><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { z } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;#/adapter/form&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 基础类型</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  rules</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: z.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">().</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">min</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, { message: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;请输入字符串&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> });</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 可选，并且携带默认值</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">   rules</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: z.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">().</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">default</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;默认值&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">).</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">optional</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(),</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// 复杂校验</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">   z.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">string</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">().</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">min</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, { message: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;请输入&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> })</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">            .</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">refine</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">((</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">value</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> value </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">===</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;123&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, {</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">              message: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;值必须为123&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">            });</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div>',24)),d(l),d(h)])}});export{f as __pageData,S as default};
