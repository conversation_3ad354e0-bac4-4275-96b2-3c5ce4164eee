import { z } from '#/adapter/form';

// 用户状态选项
export const statusOptions = [
  { label: '全部', value: null },
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
];

// 性别选项
export const genderOptions = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' },
];

// 表格列配置
export const columns = [
  {
    type: 'checkbox',
    width: 50,
    align: 'center',
    showHeaderOverflow: false,
    showOverflow: false,
  },
  {
    title: '工号',
    dataIndex: 'employeeId',
    field: 'employeeId',
    width: 120,
  },
  {
    title: '用户名',
    dataIndex: 'username',
    field: 'username',
    width: 120,
  },
  {
    title: '姓名',
    dataIndex: 'realName',
    field: 'realName',
    width: 100,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    field: 'phone',
    width: 130,
  },
  {
    title: '任职部门',
    dataIndex: 'department',
    field: 'department',
    width: 150,
    formatter: ({ row }: any) => {
      return row.department?.name || '';
    },
  },
  {
    title: '岗位',
    dataIndex: 'position',
    field: 'position',
    width: 120,
    formatter: ({ row }: any) => {
      return row.position?.name || '';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    field: 'status',
    width: 80,
    slots: { default: 'status' },
  },
  {
    title: '创建时间',
    dataIndex: 'createdTime',
    field: 'createdTime',
    width: 180,
  },
  {
    field: 'action',
    fixed: 'right' as const,
    slots: { default: 'action' },
    title: '操作',
    width: 200,
  },
];

// 搜索表单配置
export const searchFormSchema = [
  {
    label: '用户名',
    fieldName: 'username',
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户名',
    },
  },
  {
    label: '姓名',
    fieldName: 'realName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入姓名',
    },
  },
  {
    label: '状态',
    fieldName: 'status',
    component: 'Select',
    componentProps: {
      options: statusOptions,
      placeholder: '请选择状态',
    },
  },
  {
    label: '岗位',
    fieldName: 'positionId',
    component: 'Select',
    componentProps: {
      placeholder: '请选择岗位',
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  {
    label: '部门',
    fieldName: 'departmentId',
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择部门',
      treeDefaultExpandAll: true,
      showSearch: true,
      treeNodeFilterProp: 'title',
    },
  },
];

// 角色选项
export const roleOptions = [
  { label: '选项1', value: 1 },
  { label: '选项2', value: 2 },
  { label: '选项3', value: 3 },
];

// 技术级别选项
export const technicalLevelOptions = [
  { label: '选项1', value: 1 },
  { label: '选项2', value: 2 },
  { label: '选项3', value: 3 },
];

// 管理级别选项
export const managementLevelOptions = [
  { label: '选项1', value: 1 },
  { label: '选项2', value: 2 },
  { label: '选项3', value: 3 },
];

// 用户组选项
export const userGroupOptions = [
  { label: '选项1', value: 1 },
  { label: '选项2', value: 2 },
  { label: '选项3', value: 3 },
];

// 用户编辑表单配置
export const formSchema = [
  // 左列字段
  {
    fieldName: 'username',
    label: '用户名',
    rules: 'required',
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户名',
    },
  },
  {
    fieldName: 'realName',
    label: '姓名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入姓名',
    },
  },
  {
    fieldName: 'phone',
    label: '手机号',
    component: 'Input',
    rules: z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号格式').optional(),
    componentProps: {
      placeholder: '请输入手机号',
    },
  },
  {
    fieldName: 'gender',
    label: '性别',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' },
      ],
    },
    defaultValue: 'male',
  },
  {
    fieldName: 'email',
    label: '邮件',
    component: 'Input',
    rules: z.string().email('请输入正确的邮箱格式').optional(),
    componentProps: {
      placeholder: '请输入邮件',
    },
  },
  {
    fieldName: 'status',
    label: '状态',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    defaultValue: 1,
  },
  {
    fieldName: 'roleIds',
    label: '角色',
    component: 'Select',
    componentProps: {
      placeholder: '请选择角色',
      mode: 'multiple',
      options: roleOptions,
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  {
    fieldName: 'userGroupIds',
    label: '用户组',
    component: 'Select',
    componentProps: {
      placeholder: '请选择用户组',
      mode: 'multiple',
      options: userGroupOptions,
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  {
    fieldName: 'employeeId',
    label: '工号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入工号',
    },
  },
  {
    fieldName: 'managementLevel',
    label: '管理级别',
    component: 'Select',
    componentProps: {
      placeholder: '请选择管理级别',
      options: managementLevelOptions,
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  // 右列字段
  {
    fieldName: 'technicalLevel',
    label: '技术级别',
    component: 'Select',
    componentProps: {
      placeholder: '请选择技术级别',
      options: technicalLevelOptions,
      showSearch: true,
      optionFilterProp: 'label',
    },
  }





];

// 岗位表单配置（用于用户岗位管理）
export const positionFormSchema = [
  {
    fieldName: 'positionId',
    label: '岗位',
    rules: 'required',
    component: 'Select',
    componentProps: {
      placeholder: '请选择岗位',
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
];

// 获取状态标签
export const getStatusLabel = (status: number) => {
  return status === 1 ? '启用' : '禁用';
};

// 构建树形选择数据
export const buildTreeSelectData = (data: any[], valueField = 'id', titleField = 'name', childrenField = 'children') => {
  return data.map(item => ({
    value: item[valueField],
    title: item[titleField],
    key: item[valueField],
    children: item[childrenField] ? buildTreeSelectData(item[childrenField], valueField, titleField, childrenField) : undefined,
  }));
};

// 构建选择器选项数据
export const buildSelectOptions = (data: any[], valueField = 'id', labelField = 'name') => {
  return data.map(item => ({
    value: item[valueField],
    label: item[labelField],
  }));
};

// 用户岗位表格列配置
export const userPositionColumns = [
  {
    title: '岗位名称',
    dataIndex: 'name',
    field: 'name',
    width: 200,
  },
  {
    title: '岗位编码',
    dataIndex: 'code',
    field: 'code',
    width: 150,
  },
  {
    title: '归属部门',
    dataIndex: 'department',
    field: 'department',
    width: 200,
    formatter: ({ row }: any) => {
      return row.department?.name || '';
    },
  },
  {
    field: 'action',
    fixed: 'right' as const,
    slots: { default: 'action' },
    title: '操作',
    width: 100,
  },
];

