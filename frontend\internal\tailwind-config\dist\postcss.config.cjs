const { createJiti } = require("../../../node_modules/.pnpm/jiti@2.4.2/node_modules/jiti/lib/jiti.cjs")

const jiti = createJiti(__filename, {
  "interopDefault": true,
  "alias": {
    "@vben/tailwind-config": "E:/work/git/system-manage-fed/frontend/internal/tailwind-config"
  },
  "transformOptions": {
    "babel": {
      "plugins": []
    }
  }
})

/** @type {import("E:/work/git/system-manage-fed/frontend/internal/tailwind-config/src/postcss.config.js")} */
module.exports = jiti("E:/work/git/system-manage-fed/frontend/internal/tailwind-config/src/postcss.config.ts")