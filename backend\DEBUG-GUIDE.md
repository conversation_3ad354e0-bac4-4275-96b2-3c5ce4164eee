# NestJS 接口断点调试指南

## 调试方法

### 方法1：VS Code 内置调试（推荐）

1. **设置断点**
   - 在代码行号左侧点击设置红色断点
   - 或按 `F9` 切换断点

2. **启动调试**
   - 按 `F5` 或点击调试面板的"开始调试"
   - 选择 "Debug NestJS" 配置

3. **调试控制**
   - `F5` - 继续执行
   - `F10` - 单步跳过
   - `F11` - 单步进入
   - `Shift+F11` - 单步跳出
   - `Ctrl+Shift+F5` - 重启调试

### 方法2：命令行调试

```bash
# 启动调试模式
npm run debug

# 在另一个终端或VS Code中连接调试器
# VS Code: 选择 "Attach to NestJS" 配置
```

### 方法3：Chrome DevTools 调试

```bash
# 启动调试模式
npm run debug:brk

# 打开Chrome浏览器，访问：
# chrome://inspect
# 点击 "Open dedicated DevTools for Node"
```

## 常用断点位置

### 1. 控制器断点
```typescript
// 在控制器方法中设置断点
@Post('login')
async login(@Body() loginDto: LoginDto) {
  debugger; // 或在此行设置断点
  const result = await this.authService.login(loginDto);
  return ResponseDto.success(result, '登录成功');
}
```

### 2. 服务层断点
```typescript
// 在服务方法中设置断点
async login(loginDto: LoginDto) {
  debugger; // 或在此行设置断点
  const user = await this.validateUser(loginDto.account, loginDto.password);
  if (!user) {
    throw new UnauthorizedException('账号或密码错误');
  }
  // ... 其他逻辑
}
```

### 3. 数据库查询断点
```typescript
// 在数据库查询前后设置断点
async findByUsername(username: string): Promise<User | null> {
  debugger; // 查询前断点
  const user = await this.userRepository.findOne({
    where: { username },
    relations: ['department', 'position', 'roles'],
  });
  debugger; // 查询后断点
  return user;
}
```

### 4. 中间件/守卫断点
```typescript
// JWT守卫中设置断点
async canActivate(context: ExecutionContext): Promise<boolean> {
  debugger; // 在守卫逻辑中设置断点
  const request = context.switchToHttp().getRequest();
  const token = this.extractTokenFromHeader(request);
  // ... 验证逻辑
}
```

## 调试技巧

### 1. 条件断点
- 右键断点 → "编辑断点" → 设置条件
- 例如：`user.id === 1` 只在特定用户时触发

### 2. 日志断点
- 右键断点 → "编辑断点" → 设置日志消息
- 不会暂停执行，只输出日志

### 3. 监视变量
- 在调试面板的"监视"区域添加变量
- 实时查看变量值变化

### 4. 调用堆栈
- 查看函数调用链
- 点击堆栈中的函数跳转到对应代码

### 5. 变量面板
- 查看当前作用域的所有变量
- 展开对象查看详细属性

## 常见调试场景

### 1. 登录接口调试
```typescript
// 在 auth.service.ts 中设置断点
async login(loginDto: LoginDto) {
  console.log('登录请求参数:', loginDto); // 日志输出
  
  // 断点1: 验证用户前
  const user = await this.validateUser(loginDto.account, loginDto.password);
  
  // 断点2: 验证用户后
  if (!user) {
    throw new UnauthorizedException('账号或密码错误');
  }
  
  // 断点3: 生成token前
  const token = this.jwtService.sign(payload);
  
  // 断点4: 返回结果前
  return { roleType: 1, name: user.realName, token, account: loginDto.account };
}
```

### 2. 数据库查询调试
```typescript
// 在 users.service.ts 中设置断点
async findByUsername(username: string): Promise<User | null> {
  console.log('查询用户:', username);
  
  // 断点: 查询前检查参数
  const user = await this.userRepository.findOne({
    where: { username },
    relations: ['department', 'position', 'roles'],
  });
  
  // 断点: 查询后检查结果
  console.log('查询结果:', user);
  return user;
}
```

### 3. 异常处理调试
```typescript
// 在异常过滤器中设置断点
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    debugger; // 捕获所有异常时触发
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    
    console.log('异常信息:', exception);
    // ... 异常处理逻辑
  }
}
```

## 环境变量调试

在 `.env` 文件中添加调试相关配置：

```bash
# 调试模式
NODE_ENV=development
DEBUG=true

# 数据库调试
DB_LOGGING=true
DB_SYNCHRONIZE=true

# JWT调试
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d
```

## 调试工具推荐

### 1. VS Code 插件
- **NestJS Files** - NestJS文件模板
- **REST Client** - API测试
- **Thunder Client** - API调试工具
- **Database Client** - 数据库查看

### 2. 外部工具
- **Postman** - API测试
- **MySQL Workbench** - 数据库管理
- **Redis Desktop Manager** - Redis调试

## 性能调试

### 1. 添加性能监控
```typescript
// 在服务方法中添加性能监控
async login(loginDto: LoginDto) {
  const startTime = Date.now();
  
  try {
    const result = await this.performLogin(loginDto);
    const endTime = Date.now();
    console.log(`登录耗时: ${endTime - startTime}ms`);
    return result;
  } catch (error) {
    const endTime = Date.now();
    console.log(`登录失败耗时: ${endTime - startTime}ms`);
    throw error;
  }
}
```

### 2. 数据库查询性能
```typescript
// 启用数据库查询日志
// 在 app.module.ts 中配置
TypeOrmModule.forRoot({
  // ... 其他配置
  logging: true, // 启用SQL日志
  logger: 'advanced-console', // 详细日志
}),
```

## 故障排除

### 1. 断点不生效
- 检查 sourceMaps 是否启用
- 确认 TypeScript 编译配置正确
- 重启调试会话

### 2. 无法连接调试器
- 检查端口 9229 是否被占用
- 确认防火墙设置
- 重启 VS Code

### 3. 变量显示 undefined
- 检查变量作用域
- 确认异步操作完成
- 使用 console.log 辅助调试

## 最佳实践

1. **合理设置断点** - 不要设置过多断点影响性能
2. **使用条件断点** - 只在特定条件下触发
3. **结合日志输出** - 断点和日志相结合
4. **及时清理断点** - 调试完成后清理无用断点
5. **保护敏感信息** - 避免在日志中输出密码等敏感信息
