import{u as N,B as r,aH as O}from"./bootstrap-DShsrVit.js";import{C as u}from"./index-B_b7xM74.js";import{_ as V}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as D,O as F,af as d,ag as A,ah as s,a3 as e,n as l,ap as n,an as o,am as L,as as R,ao as E,F as H}from"../jse/index-index-BMh_AyeW.js";import{u as I}from"./use-tabs-C64_EnSy.js";const K={class:"flex flex-wrap gap-3"},P={class:"flex flex-wrap gap-3"},S={class:"flex flex-wrap items-center gap-3"},U={class:"flex flex-wrap items-center gap-3"},Q=D({__name:"index",setup(W){const p=N(),i=F(""),{closeAllTabs:m,closeCurrentTab:b,closeLeftTabs:v,closeOtherTabs:y,closeRightTabs:T,closeTabByKey:x,refreshTab:C,resetTabTitle:k,setTabTitle:g}=I();function $(){p.push({name:"VbenAbout"})}function w(f){p.push({name:"FeatureTabDetailDemo",params:{id:f}})}function B(){i.value="",k()}return(f,t)=>(d(),A(e(V),{description:"用于需要操作标签页的场景",title:"标签页"},{default:s(()=>[l(e(u),{class:"mb-5",title:"打开/关闭标签页"},{default:s(()=>[t[11]||(t[11]=n("div",{class:"text-foreground/80 mb-3"}," 如果标签页存在，直接跳转切换。如果标签页不存在，则打开新的标签页。 ",-1)),n("div",K,[l(e(r),{type:"primary",onClick:$},{default:s(()=>t[9]||(t[9]=[o(' 打开 "关于" 标签页 ')])),_:1}),l(e(r),{type:"primary",onClick:t[0]||(t[0]=a=>e(x)("/vben-admin/about"))},{default:s(()=>t[10]||(t[10]=[o(' 关闭 "关于" 标签页 ')])),_:1})])]),_:1}),l(e(u),{class:"mb-5",title:"标签页操作"},{default:s(()=>[t[18]||(t[18]=n("div",{class:"text-foreground/80 mb-3"},"用于动态控制标签页的各种操作",-1)),n("div",P,[l(e(r),{type:"primary",onClick:t[1]||(t[1]=a=>e(b)())},{default:s(()=>t[12]||(t[12]=[o(" 关闭当前标签页 ")])),_:1}),l(e(r),{type:"primary",onClick:t[2]||(t[2]=a=>e(v)())},{default:s(()=>t[13]||(t[13]=[o(" 关闭左侧标签页 ")])),_:1}),l(e(r),{type:"primary",onClick:t[3]||(t[3]=a=>e(T)())},{default:s(()=>t[14]||(t[14]=[o(" 关闭右侧标签页 ")])),_:1}),l(e(r),{type:"primary",onClick:t[4]||(t[4]=a=>e(m)())},{default:s(()=>t[15]||(t[15]=[o(" 关闭所有标签页 ")])),_:1}),l(e(r),{type:"primary",onClick:t[5]||(t[5]=a=>e(y)())},{default:s(()=>t[16]||(t[16]=[o(" 关闭其他标签页 ")])),_:1}),l(e(r),{type:"primary",onClick:t[6]||(t[6]=a=>e(C)())},{default:s(()=>t[17]||(t[17]=[o(" 刷新当前标签页 ")])),_:1})])]),_:1}),l(e(u),{class:"mb-5",title:"动态标题"},{default:s(()=>[t[21]||(t[21]=n("div",{class:"text-foreground/80 mb-3"}," 该操作不会影响页面标题，仅修改Tab标题 ",-1)),n("div",S,[l(e(O),{value:i.value,"onUpdate:value":t[7]||(t[7]=a=>i.value=a),class:"w-40",placeholder:"请输入新标题"},null,8,["value"]),l(e(r),{type:"primary",onClick:t[8]||(t[8]=()=>e(g)(i.value))},{default:s(()=>t[19]||(t[19]=[o(" 修改 ")])),_:1}),l(e(r),{onClick:B},{default:s(()=>t[20]||(t[20]=[o(" 重置 ")])),_:1})])]),_:1}),l(e(u),{class:"mb-5",title:"最大打开数量"},{default:s(()=>[t[22]||(t[22]=n("div",{class:"text-foreground/80 mb-3"}," 限制带参数的tab打开的最大数量，由 `route.meta.maxNumOfOpenTab` 控制 ",-1)),n("div",U,[(d(),L(H,null,R(5,a=>l(e(r),{key:a,type:"primary",onClick:j=>w(a)},{default:s(()=>[o(" 打开"+E(a)+"详情页 ",1)]),_:2},1032,["onClick"])),64))])]),_:1})]),_:1}))}});export{Q as default};
