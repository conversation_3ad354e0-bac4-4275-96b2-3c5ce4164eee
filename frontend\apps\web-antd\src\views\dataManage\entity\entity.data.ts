import {RegionUtils} from "#/utils/regionUtils";

const categoryOptions = [
  { label: '地形级', value: '地形级' },
  { label: '城市级', value: '城市级' },
];

const dataSituationOptions = [
  { label: '正式数据', value: 1 },
  { label: '过程数据', value: 2 },
];

const datasetTypeOptions = [
  { label: 'Vector', value: 'vector',fileType:'folder' },
]

export const dataTypeOptions = {
  gdb:[{ label: 'gdb', value: 'vector',fileType:'folder'}],
  mdb : [{ label: 'mdb', value: 'vector',fileType:'folder'}],
  shp : [{ label: 'shp', value: 'vector',fileType:'folder'}],
};


const fileFormatOptions = {
  all:[{ label: 'gdb', value: 'gdb'},{ label: 'shp', value: 'shp'},{ label: 'dwg', value: 'dwg'}],
  gdb: [{ label: 'gdb', value: 'gdb'}],
  shp : [{ label: 'shp', value: 'shp'}],
  dwg : [{ label: 'dwg', value: 'dwg'}],
};

const statusOptions = [
  { label: '待上传', value: 0 },
  { label: '已上传', value: 1 },
  { label: '解析中', value: 2 },
  { label: '解析失败', value: 3 },
];


export function getUploadStatusLabel(row) {
  let uploadStatus = row.uploadStatus;
  for(let i=0; i<statusOptions.length; i++) {
    if(statusOptions[i].value === uploadStatus) {
      return statusOptions[i].label;
    }
  }
  return '';
}


const createTimeOptions = [
];

let nowYear = new Date().getFullYear();
for(let i=nowYear; i>2000; i--) {
  createTimeOptions.push( { label: ''+i, value: ''+i })
}


export const columns = [
  {
    title: '数据集名称',
    dataIndex: 'name',
    field: 'name',
    width: 300,
  },
  // {
  //   title: '类别',
  //   dataIndex: 'category',
  //   field: 'category',
  //   width: 120,
  // },

  {
    title: '数据类型',
    dataIndex: 'dataType',
    field: 'dataType',
    width: 100,
  },
  {
    title: '建设年份',
    dataIndex: 'dataYear',
    field: 'dataYear',
    width: 100,
  },
  // {
  //   title: '分辨率(m)',
  //   dataIndex: 'resolution',
  //   field: 'resolution',
  //   width: 100,
  // },
  {
    title: '行政区划',
    dataIndex: 'regionName',
    field: 'regionName',
    width: 180,
    slots: { default: 'regionName' },
  },
  {
    title: '面积(平方米)',
    dataIndex: 'area',
    field: 'area',
    width: 180,
    slots: { default: 'area' },
  },
  {
    title: '长度(米)',
    dataIndex: 'length',
    field: 'length',
    width: 120,
    slots: { default: 'length' },
  },
  {
    title: '图层个数',
    dataIndex: 'layerCount',
    field: 'layerCount',
    width: 100,
  },
  {
    title: '数据量',
    dataIndex: 'dataSize',
    field: 'dataSize',
    width: 120,
  },
  {
    title: '数据来源',
    dataIndex: 'provider',
    field: 'provider',
    width: 180,
  },
  {
    title: '数据情况',
    dataIndex: 'dataSituation',
    field: 'dataSituation',
    width: 140,
    slots: { default: 'dataSituation' },
  },
  {
    title: '状态',
    dataIndex: 'uploadStatus',
    field: 'uploadStatus',
    slots: { default: 'upload-status' },
    width: 100,
  },
  {
    title: '创建人',
    dataIndex: 'userNameCreated',
    field: 'userNameCreated',
    width: 150,
  },
  {
    title: '创建时间',
    dataIndex: 'timeCreated',
    field: 'timeCreated',
    width: 180,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 210,
  },
];

export const searchFormSchema = [
  {
    label: '数据集名称',
    fieldName: 'name',
    component: 'Input',
  },
  // {
  //   label: '类别',
  //   fieldName: 'category',
  //   component: 'Select',
  //   componentProps: {
  //     options: categoryOptions,
  //   },
  // },

  {
    label: '行政区划',
    fieldName: 'regionCode',
    component: 'Cascader',
    componentProps: {
      options: RegionUtils.getProvinces(),
      changeOnSelect:true,
      placeholder: '请选择',
    },
  },
  {
    label: '数据来源',
    fieldName: 'provider',
    component: 'Input',
  },
  {
    label: '建设年份',
    fieldName: 'dataYear',
    component: 'Select',
    componentProps: {
      options: createTimeOptions,
    },
  },
  {
    label: '状态',
    fieldName: 'uploadStatus',
    component: 'Select',
    componentProps: {
      options: statusOptions,
    },

  },
];


export function getFormSchemaByCategory(category,regionCode) {
  let catedatasetTypes = fileFormatOptions[category];
  let dateTypes = dataTypeOptions[category];
  return [
    {
      fieldName: 'id',
      label: '主键',
      component: 'Input',
      dependencies: {
        show:false,
        triggerFields: ['upload'],
      },
    },
    {
      fieldName: 'name',
      label: '数据集名称',
      rules: 'required',
      component: 'Input',
      componentProps: {
        placeholder: '请输入数据集名称',
      },
      dependencies: {
        show(values) {
          return !values.upload;
        },
        triggerFields: ['upload'],
      },
    },
    {
      label: '数据类型',
      fieldName: 'dataType',
      component: 'Select',
      componentProps: {
        options: datasetTypeOptions,
      },
      rules: 'selectRequired',
      dependencies: {
        disabled(values) {
          return (values.id != null && values.id.length > 0);
        },
        triggerFields: ['id'],
      },
    },

    // {
    //   label: '类别',
    //   fieldName: 'category',
    //   component: 'Select',
    //   componentProps: {
    //     options: categoryOptions,
    //   },
    //   rules: 'selectRequired',
    // },
    {
      label: '建设年份',
      fieldName: 'dataYear',
      component: 'Select',
      componentProps: {
        options: createTimeOptions,
      },
      rules: 'selectRequired',
    },
    {
      label: '行政区划',
      fieldName: 'regionCode',
      component: 'Cascader',
      componentProps: {
        options:  RegionUtils.getProvinces(),
        placeholder: '请选择省/市/区',
        changeOnSelect: true,
        defaultValue:regionCode
      },
      rules: 'selectRequired',
    },
    {
      label: '数据来源',
      fieldName: 'provider',
      component: 'Input',
      rules: 'required',
    },
    {
      label: '数据情况',
      fieldName: 'dataSituation',
      component: 'Select',
      componentProps: {
        options: dataSituationOptions,
      },
      rules: 'selectRequired',
    },
  ]
}

export function getUploadFormSchemaByCategory(category) {
  let catedatasetTypes = fileFormatOptions['all'];
  return [
    {
      label: '数据类型',
      fieldName: 'dataType',
      component: 'Select',
      componentProps: {
        options: datasetTypeOptions,
      },
      rules: 'selectRequired',
      dependencies: {
        show:false,
        triggerFields: ['id'],
      },
    },
    {
      label: '文件格式',
      fieldName: 'fileFormat',
      component: 'Select',
      componentProps: {
        options: catedatasetTypes,
        placeholder: '请选择',
      },
      rules: 'selectRequired',
    },
    {
      fieldName: 'epsgCode',
      label: '坐标系',
      component: 'Select',
      componentProps: {
        placeholder: '请选择坐标系',
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label'
      },
      rules: 'required',
      dependencies: {
        show(values) {
          return values.fileFormat === 'dwg';
        },
        triggerFields: ['fileFormat'],
      }
    },
    {
      fieldName: 'fileUpload',
      label: '文件',
      component: 'Upload',
      dependencies: {
        rules(values) {
          if (values.id == null || values.id.length == 0) {
            return 'selectRequired';
          }
          return null;
        },
        show(values) {
          return (values.id == null || values.id.length == 0);
        },
        triggerFields: ['id'],
      },
    },
  ]
}

