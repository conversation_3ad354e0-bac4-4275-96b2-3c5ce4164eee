/**
 * 测试用户API是否会泄露密码信息
 * 这个测试脚本用于验证 /api/users 接口不会在响应中包含密码字段
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// 测试用户凭据
const TEST_USER = {
  account: 'admin',
  password: 'admin123'
};

async function testPasswordSecurity() {
  try {
    console.log('🔐 开始测试用户API密码安全性...\n');

    // 1. 登录获取token
    console.log('1. 正在登录...');
    const loginResponse = await axios.post(`${BASE_URL}/system/login`, TEST_USER);
    
    if (loginResponse.data.code !== 200) {
      throw new Error('登录失败: ' + loginResponse.data.message);
    }
    
    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功');

    // 2. 测试获取用户列表接口
    console.log('\n2. 测试获取用户列表接口...');
    const usersResponse = await axios.get(`${BASE_URL}/users`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (usersResponse.data.code !== 200) {
      throw new Error('获取用户列表失败: ' + usersResponse.data.message);
    }

    const users = usersResponse.data.data.list;
    console.log(`📋 获取到 ${users.length} 个用户`);

    // 检查是否包含密码字段
    let hasPasswordField = false;
    users.forEach((user, index) => {
      if (user.hasOwnProperty('password')) {
        console.log(`❌ 用户 ${index + 1} (${user.username}) 包含密码字段: ${user.password}`);
        hasPasswordField = true;
      }
    });

    if (!hasPasswordField) {
      console.log('✅ 用户列表接口安全：未发现密码字段');
    }

    // 3. 测试获取单个用户接口
    if (users.length > 0) {
      console.log('\n3. 测试获取单个用户接口...');
      const userId = users[0].id;
      const userResponse = await axios.get(`${BASE_URL}/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (userResponse.data.code !== 200) {
        throw new Error('获取用户详情失败: ' + userResponse.data.message);
      }

      const user = userResponse.data.data;
      console.log(`👤 获取用户详情: ${user.username}`);

      if (user.hasOwnProperty('password')) {
        console.log(`❌ 用户详情包含密码字段: ${user.password}`);
        hasPasswordField = true;
      } else {
        console.log('✅ 用户详情接口安全：未发现密码字段');
      }
    }

    // 4. 测试获取用户资料接口
    console.log('\n4. 测试获取用户资料接口...');
    const profileResponse = await axios.get(`${BASE_URL}/system/profile`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (profileResponse.data.code !== 200) {
      throw new Error('获取用户资料失败: ' + profileResponse.data.message);
    }

    const profile = profileResponse.data.data;
    console.log(`👤 获取用户资料: ${profile.username}`);

    if (profile.hasOwnProperty('password')) {
      console.log(`❌ 用户资料包含密码字段: ${profile.password}`);
      hasPasswordField = true;
    } else {
      console.log('✅ 用户资料接口安全：未发现密码字段');
    }

    // 总结
    console.log('\n📊 测试结果总结:');
    if (hasPasswordField) {
      console.log('❌ 发现安全问题：某些接口返回了密码字段');
      console.log('🔧 建议：确保所有用户相关接口都使用了 ClassSerializerInterceptor');
      process.exit(1);
    } else {
      console.log('✅ 所有测试通过：用户API未泄露密码信息');
      console.log('🛡️ 安全状态：良好');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    process.exit(1);
  }
}

// 运行测试
testPasswordSecurity();
