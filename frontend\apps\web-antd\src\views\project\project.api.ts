import { requestClient } from '#/api/request';
import { useAppConfig } from '@vben/hooks';
import { useAccessStore } from '@vben/stores';

/**
 * 获取项目列表
 */
export async function list(params) {
  return requestClient.post('/project-info/page', params);
}

/**
 * 导入项目信息
 * @param {string} fileId - 上传文件返回的文件ID
 */
export async function importProject(fileId) {
  return requestClient.post(`/project-info/import/${fileId}`, {  });
}

// 上传文件
export function uploadFile(file) {
  const formData = new FormData();
  formData.append('file', file);

  return requestClient.post('/file-manage/file/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 保存项目信息
 * @param {object} params - 项目信息
 */
export async function save(params) {
  return requestClient.post('/project-result/save', params);
}



export function saveResult(params) {
  let nParams = {...params};
  delete nParams.regionCode;
  return requestClient.post('/project-result/save',
    nParams,
  );
}

export function listResult(params) {
  return requestClient.post( '/project-result/page',
    params,
  );
}

export function deleteOneResult(id) {
  return requestClient.delete(`/project-result/delete/${id}`,{});
}


export function getResultDetail(projectResultId) {
  return requestClient.get( `/project-result/detail/${projectResultId}`,
    {},
  );
}


export function listMaterial(params) {
  return requestClient.post('/project-result-appendix/appendix/page',
    params,
  );
}


export function deleteOneMaterial(projectResultId, appendixCategory, appendixId) {
  return requestClient.delete(`/project-result-appendix/${projectResultId}/${appendixCategory}/${appendixId}`,{});
}

export function deleteBatchMaterial(projectResultId, appendixCategory, appendixIds) {
  return requestClient.post(`/project-result-appendix/delete`,{projectResultId,appendixCategory,appendixIds});
}



export function downloadMaterial(projectResultId, appendixCategory, appendixId) {
  const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);
  const accessStore = useAccessStore();
  return `${apiURL}/project-result-appendix/download/${projectResultId}/${appendixCategory}/${appendixId}?token=${accessStore.accessToken}`;
}


export function previewMaterial(projectResultId, appendixCategory, appendixId) {
  const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);
  const accessStore = useAccessStore();
  return `${apiURL}/project-result-appendix/preview/${projectResultId}/${appendixCategory}/${appendixId}?token=${accessStore.accessToken}`;
}