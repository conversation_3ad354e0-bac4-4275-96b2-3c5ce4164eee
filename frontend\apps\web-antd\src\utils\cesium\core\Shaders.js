/**
 * 着色器模块
 */
let Shaders = {
	// 流动线
	getFlowLineShader: function(options) {
		if (options && options.get) {
			return "uniform vec4 color;\n\
                uniform float duration;\n\
                \n\
                czm_material czm_getMaterial(czm_materialInput materialInput){\n\
                    czm_material material = czm_getDefaultMaterial(materialInput);\n\
                    vec2 st = materialInput.st;\n\
                    float t =fract(czm_frameNumber / duration);\n\
                    t *= 1.03;\n\
                    float alpha = smoothstep(t- 0.03, t, st.s) * step(-t, -st.s);\n\
                    alpha += 0.1;\n\
                    vec4 fragColor;\n\
                    fragColor.rgb = (color.rgb) / 0.5;\n\
                    fragColor = czm_gammaCorrect(fragColor);\n\
                    material.diffuse = fragColor.rgb;\n\
                    material.alpha = alpha;\n\
                    material.emission = fragColor.rgb;\n\
                    return material;\n\
                }\n\
                ";
		}
	},
	// 动态线
	getDynamicLineShader: function(options) {
		if (options && options.get) {
			return "czm_material czm_getMaterial(czm_materialInput materialInput)\n\
                {\n\
                    czm_material material = czm_getDefaultMaterial(materialInput);\n\
                    vec2 st = materialInput.st;\n\
                    \n\
                    if(texture2D(image, vec2(0.0, 0.0)).a == 1.0){\n\
                        discard;\n\
                    }else{\n\
                        material.alpha = texture2D(image, vec2(1.0 - fract(time - st.s), st.t)).a * color.a;\n\
                    }\n\
                    \n\
                    material.diffuse = max(color.rgb * material.alpha * 3.0, color.rgb);\n\
                    \n\
                    return material;\n\
                }\n\
                ";
		}
	},
	// 动态泛光线
	getDynamicLightLineShader: function(options) {
		if (options && options.get) {
			return "czm_material czm_getMaterial(czm_materialInput materialInput)\n\
                {\n\
                    czm_material material = czm_getDefaultMaterial(materialInput);\n\
                    vec2 st = materialInput.st;\n\
                    \n\
                    vec4 colorImage = texture2D(image, vec2(fract(1.0 *st.s - time), fract(st.t)));\n\
                    \n\
                    vec4 fragColor;\n\
                    fragColor.rgb = (colorImage.rgb+color.rgb) / 1.0;\n\
                    fragColor = czm_gammaCorrect(fragColor);\n\
                    material.diffuse = colorImage.rgb;\n\
                    material.alpha = colorImage.a;\n\
                    material.emission = fragColor.rgb;\n\
                    \n\
                    return material;\n\
                }\n\
                ";
			// material.diffuse = max(color.rgb * material.alpha * 3.0, color.rgb);\n\
			// material.alpha = texture2D(image, vec2(1.0 - fract(time - st.s), st.t)).a * color.a;\n\
		}
	},
	/**
	 * 带方向的墙体
	 * @param {*} options 
	 */
	getDirectionWallShader: function(options) {

		if (options && options.get) {
			var materail =
				"czm_material czm_getMaterial(czm_materialInput materialInput)\n\
                    {\n\
                    czm_material material = czm_getDefaultMaterial(materialInput);\n\
                    vec2 st = materialInput.st;\n\
                    \n\ ";
			if (options.freely == "vertical") { //（由下到上）

				materail += "vec4 colorImage = texture2D(image, vec2(fract(float(" + options.count +
					")*st.t " + options.direction + " time), fract(st.s)));\n\ ";
			} else { //（逆时针）

				materail += "vec4 colorImage = texture2D(image, vec2(fract(float(" + options.count +
					")*st.s " + options.direction + " time), fract(st.t)));\n\ ";
			}
			//泛光
			materail += "vec4 fragColor;\n\
                    fragColor.rgb = (colorImage.rgb+color.rgb) / 1.0;\n\
                    fragColor = czm_gammaCorrect(fragColor);\n\ "

			materail += " material.diffuse = colorImage.rgb;\n\
                    material.alpha = colorImage.a;\n\
                    material.emission = fragColor.rgb;\n\
                    \n\
                    return material;\n\
                    }\n\
                    ";

			return materail
		}
	},
	getCircleFadeShader: function(options) {

		if (options && options.get) {

			return `czm_material czm_getMaterial(czm_materialInput materialInput)\n                
                    {\n                    
                        czm_material material = czm_getDefaultMaterial(materialInput);\n                    
                        material.diffuse = 1.5 * color.rgb;\n                    
                        vec2 st = materialInput.st;\n                    
                        float dis = distance(st, vec2(0.5, 0.5));\n                    
                        float per = fract(time);\n                    
                        if(dis > per * 0.5){\n                        
                            //material.alpha = 0.0;\n                        
                            discard;\n                    
                        }else {\n                            
                            material.alpha = color.a  * dis / per / 2.0;\n                    
                        }\n                    
                        return material;\n                
                    }`
		}
	},
	// 波动圆
	getDynamicCircleShader: function(options) {

		if (options && options.get) {
			return "uniform vec4 color;\n\
                uniform float duration;\n\
                uniform float count;\n\
                uniform float gradient;\n\
                \n\
                czm_material czm_getMaterial(czm_materialInput materialInput)\n\
                {\n\
                    czm_material material = czm_getDefaultMaterial(materialInput);\n\
                    material.diffuse = 1.5 * color.rgb;\n\
                    vec2 st = materialInput.st;\n\
                    vec3 str = materialInput.str;\n\
                    float dis = distance(st, vec2(0.5, 0.5));\n\
                    float per = fract(czm_frameNumber / duration);\n\
                    if(abs(str.z) > 0.001){\n\
                        discard;\n\
                    }\n\
                    if(dis > 0.5){\n\
                        discard;\n\
                    } else {\n\
                        float perDis = 0.5 / count;\n\
                        float disNum;\n\
                        float bl = .0;\n\
                        for (int i = 0; i <= 10; i++) {\n\
                            if (float(i) <= count) {\n\
                                disNum = perDis * float(i) - dis + per / count;\n\
                                if (disNum > 0.0) {\n\
                                    if (disNum < perDis) {\n\
                                        bl = 1.0 - disNum / perDis;\n\
                                    } else if (disNum - perDis < perDis) {\n\
                                        bl = 1.0 - abs(1.0 - disNum / perDis);\n\
                                    }\n\
                                    material.alpha = pow(bl, gradient);\n\
                                }\n\
                            }\n\
                        }\n\
                    }\n\
                    return material;\n\
                }\n\
                ";
		}
	},
	// 雷达扫描
	getRadarScanShader: function(options) {

		if (options && options.get) {
			return "uniform sampler2D colorTexture;\n\
                uniform sampler2D depthTexture;\n\
                varying vec2 v_textureCoordinates;\n\
                uniform vec4 u_scanCenterEC;\n\
                uniform vec3 u_scanPlaneNormalEC;\n\
                uniform vec3 u_scanLineNormalEC;\n\
                uniform float u_radius;\n\
                uniform vec4 u_scanColor;\n\
                \n\
                vec4 toEye(in vec2 uv, in float depth){\n\
                vec2 xy = vec2((uv.x * 2.0 - 1.0),(uv.y * 2.0 - 1.0));\n\
                vec4 posInCamera =czm_inverseProjection * vec4(xy, depth, 1.0);\n\
                posInCamera =posInCamera / posInCamera.w;\n\
                return posInCamera;\n\
                }\n\
                \n\
                bool isPointOnLineRight(in vec3 ptOnLine, in vec3 lineNormal, in vec3 testPt){\n\
                vec3 v01 = testPt - ptOnLine;\n\
                normalize(v01);\n\
                vec3 temp = cross(v01, lineNormal);\n\
                float d = dot(temp, u_scanPlaneNormalEC);\n\
                return d > 0.5;\n\
                }\n\
                \n\
                vec3 pointProjectOnPlane(in vec3 planeNormal, in vec3 planeOrigin, in vec3 point){\n\
                vec3 v01 = point -planeOrigin;\n\
                float d = dot(planeNormal, v01) ;\n\
                return (point - planeNormal * d);\n\
                }\n\
                \n\
                float distancePointToLine(in vec3 ptOnLine, in vec3 lineNormal, in vec3 testPt){\n\
                vec3 tempPt = pointProjectOnPlane(lineNormal, ptOnLine, testPt);\n\
                return length(tempPt - ptOnLine);\n\
                }\n\
                \n\
                float getDepth(in vec4 depth){\n\
                float z_window = czm_unpackDepth(depth);\n\
                z_window = czm_reverseLogDepth(z_window);\n\
                float n_range = czm_depthRange.near;\n\
                float f_range = czm_depthRange.far;\n\
                return (2.0 * z_window - n_range - f_range) / (f_range - n_range);\n\
                }\n\
                \n\
                void main(){\n\
                gl_FragColor = texture2D(colorTexture, v_textureCoordinates);\n\
                float depth = getDepth( texture2D(depthTexture, v_textureCoordinates));\n\
                vec4 viewPos = toEye(v_textureCoordinates, depth);\n\
                vec3 prjOnPlane = pointProjectOnPlane(u_scanPlaneNormalEC.xyz, u_scanCenterEC.xyz, viewPos.xyz);\n\
                float dis = length(prjOnPlane.xyz - u_scanCenterEC.xyz);\n\
                float twou_radius = u_radius * 2.0;\n\
                if(dis < u_radius){\n\
                    float f0 = 1.0 -abs(u_radius - dis) / u_radius;\n\
                    f0 = pow(f0, 64.0);\n\
                    vec3 lineEndPt = vec3(u_scanCenterEC.xyz) + u_scanLineNormalEC * u_radius;\n\
                    float f = 0.0;\n\
                    if(isPointOnLineRight(u_scanCenterEC.xyz, u_scanLineNormalEC.xyz, prjOnPlane.xyz)){\n\
                        float dis1= length(prjOnPlane.xyz - lineEndPt);\n\
                        f = abs(twou_radius -dis1) / twou_radius;\n\
                        f = pow(f, float(" + options.width + "));\n\
                    }\n\
                    if(float(" + options.border + ") > 0.0){\n\
                        gl_FragColor = mix(gl_FragColor, u_scanColor, f + f0);\n\
                    } else {\n\
                        gl_FragColor = mix(gl_FragColor, u_scanColor, f);\n\
                    }\n\
                    }\n\
                }\n\
                ";
		}
	},
	// 圆形扫描
	getCircleScanShader: function(options) {

		if (options && options.get) {
			return "uniform sampler2D colorTexture;\n\
                uniform sampler2D depthTexture;\n\
                varying vec2 v_textureCoordinates;\n\
                uniform vec4 u_scanCenterEC;\n\
                uniform vec3 u_scanPlaneNormalEC;\n\
                uniform float u_radius;\n\
                uniform vec4 u_scanColor;\n\
                \n\
                vec4 toEye(in vec2 uv, in float depth){\n\
                  vec2 xy = vec2((uv.x * 2.0 - 1.0),(uv.y * 2.0 - 1.0));\n\
                  vec4 posInCamera = czm_inverseProjection * vec4(xy, depth, 1.0);\n\
                  posInCamera =posInCamera / posInCamera.w;\n\
                  return posInCamera;\n\
                }\n\
                \n\
                vec3 pointProjectOnPlane(in vec3 planeNormal, in vec3 planeOrigin, in vec3 point){\n\
                    vec3 v01 = point - planeOrigin;\n\
                    float d = dot(planeNormal, v01) ;\n\
                    return (point - planeNormal * d);\n\
                }\n\
                \n\
                float getDepth(in vec4 depth){\n\
                    float z_window = czm_unpackDepth(depth);\n\
                    z_window = czm_reverseLogDepth(z_window);\n\
                    float n_range = czm_depthRange.near;\n\
                    float f_range = czm_depthRange.far;\n\
                    return (2.0 * z_window - n_range - f_range) / (f_range - n_range);\n\
                }\n\
                \n\
                void main(){\n\
                    gl_FragColor = texture2D(colorTexture, v_textureCoordinates);\n\
                    float depth = getDepth(texture2D(depthTexture, v_textureCoordinates));\n\
                    vec4 viewPos = toEye(v_textureCoordinates, depth);\n\
                    vec3 prjOnPlane = pointProjectOnPlane(u_scanPlaneNormalEC.xyz, u_scanCenterEC.xyz, viewPos.xyz);\n\
                    float dis = length(prjOnPlane.xyz - u_scanCenterEC.xyz);\n\
                    if(dis < u_radius){\n\
                      float f = 1.0 - abs(u_radius - dis) / u_radius;\n\
                      f = pow(f, float(" + options.border + "));\n\
                      gl_FragColor = mix(gl_FragColor, u_scanColor, f);\n\
                    }\n\
                  }\n\
                  ";
		}
	},
	getVideoShedShader:function(options) {
		if (options && options.get) {
			return `
			 varying vec2 v_textureCoordinates;
			 uniform sampler2D colorTexture;
			 uniform sampler2D depthTexture;
			 uniform sampler2D shadowMap_texture; 
			 uniform mat4 shadowMap_matrix; 
			 uniform vec4 shadowMap_lightPositionEC; 
			 uniform vec4 shadowMap_normalOffsetScaleDistanceMaxDistanceAndDarkness;
			 uniform vec4 shadowMap_texelSizeDepthBiasAndNormalShadingSmooth;
			 uniform int helsing_textureType;
			 uniform sampler2D helsing_texture;
			 uniform float helsing_alpha;
			 uniform vec4 helsing_visibleAreaColor;
			 uniform vec4 helsing_invisibleAreaColor;
			 
			 vec4 toEye(in vec2 uv, in float depth){
				 vec2 xy = vec2((uv.x * 2.0 - 1.0),(uv.y * 2.0 - 1.0));
				 vec4 posInCamera =czm_inverseProjection * vec4(xy, depth, 1.0);
				 posInCamera =posInCamera / posInCamera.w;
				 return posInCamera;
			 }
			 float getDepth(in vec4 depth){
				 float z_window = czm_unpackDepth(depth);
				 z_window = czm_reverseLogDepth(z_window);
				 float n_range = czm_depthRange.near;
				 float f_range = czm_depthRange.far;
				 return (2.0 * z_window - n_range - f_range) / (f_range - n_range);
			 }
			 float _czm_sampleShadowMap(sampler2D shadowMap, vec2 uv){
				 return texture2D(shadowMap, uv).r;
			 }
			 float _czm_shadowDepthCompare(sampler2D shadowMap, vec2 uv, float depth){
				 return step(depth, _czm_sampleShadowMap(shadowMap, uv));
			 }
			 float _czm_shadowVisibility(sampler2D shadowMap, czm_shadowParameters shadowParameters){
				 float depthBias = shadowParameters.depthBias;
				 float depth = shadowParameters.depth;
				 float nDotL = shadowParameters.nDotL;
				 float normalShadingSmooth = shadowParameters.normalShadingSmooth;
				 float darkness = shadowParameters.darkness;
				 vec2 uv = shadowParameters.texCoords;
				 depth -= depthBias;
				 vec2 texelStepSize = shadowParameters.texelStepSize;
				 float radius = 1.0;
				 float dx0 = -texelStepSize.x * radius;
				 float dy0 = -texelStepSize.y * radius;
				 float dx1 = texelStepSize.x * radius;
				 float dy1 = texelStepSize.y * radius;
				 float visibility = (_czm_shadowDepthCompare(shadowMap, uv, depth)
					 + _czm_shadowDepthCompare(shadowMap, uv + vec2(dx0, dy0), depth)
					 + _czm_shadowDepthCompare(shadowMap, uv + vec2(0.0, dy0), depth)
					 + _czm_shadowDepthCompare(shadowMap, uv + vec2(dx1, dy0), depth)
					 + _czm_shadowDepthCompare(shadowMap, uv + vec2(dx0, 0.0), depth) 
					 + _czm_shadowDepthCompare(shadowMap, uv + vec2(dx1, 0.0), depth)
					 + _czm_shadowDepthCompare(shadowMap, uv + vec2(dx0, dy1), depth)
					 + _czm_shadowDepthCompare(shadowMap, uv + vec2(0.0, dy1), depth)
					 + _czm_shadowDepthCompare(shadowMap, uv + vec2(dx1, dy1), depth)
				 ) * (1.0 / 9.0);
				 return visibility;
			 }
			 vec3 pointProjectOnPlane(in vec3 planeNormal, in vec3 planeOrigin, in vec3 point){
				 vec3 v01 = point -planeOrigin;
				 float d = dot(planeNormal, v01) ;
				 return (point - planeNormal * d);
			 }
			 
			 void main(){
				 const float PI = 3.141592653589793;
				 vec4 color = texture2D(colorTexture, v_textureCoordinates);
				 vec4 currentDepth = texture2D(depthTexture, v_textureCoordinates);
				 if(currentDepth.r >= 1.0){
					 gl_FragColor = color;
					 return;
				 }
				 float depth = getDepth(currentDepth);
				 vec4 positionEC = toEye(v_textureCoordinates, depth);
				 vec3 normalEC = vec3(1.0);
				 czm_shadowParameters shadowParameters;
				 shadowParameters.texelStepSize = shadowMap_texelSizeDepthBiasAndNormalShadingSmooth.xy;
				 shadowParameters.depthBias = shadowMap_texelSizeDepthBiasAndNormalShadingSmooth.z;
				 shadowParameters.normalShadingSmooth = shadowMap_texelSizeDepthBiasAndNormalShadingSmooth.w;
				 shadowParameters.darkness = shadowMap_normalOffsetScaleDistanceMaxDistanceAndDarkness.w;
				 shadowParameters.depthBias *= max(depth * 0.01, 1.0);
				 vec3 directionEC = normalize(positionEC.xyz - shadowMap_lightPositionEC.xyz);
				 float nDotL = clamp(dot(normalEC, -directionEC), 0.0, 1.0);
				 vec4 shadowPosition = shadowMap_matrix * positionEC;
				 shadowPosition /= shadowPosition.w;
				 if (any(lessThan(shadowPosition.xyz, vec3(0.0))) || any(greaterThan(shadowPosition.xyz, vec3(1.0)))){
					 gl_FragColor = color;
					 return;
				 }
				 shadowParameters.texCoords = shadowPosition.xy;
				 shadowParameters.depth = shadowPosition.z;
				 shadowParameters.nDotL = nDotL;
				 float visibility = _czm_shadowVisibility(shadowMap_texture, shadowParameters);
				 
				 if (helsing_textureType < 2){ // 视频或图片模式
					 vec4 videoColor = texture2D(helsing_texture, shadowPosition.xy);
					 if (visibility == 1.0){
						 gl_FragColor =  mix(color, vec4(videoColor.xyz, 1.0), helsing_alpha * videoColor.a);
					 }
					 else{
						 if(abs(shadowPosition.z - 0.0) < 0.01){
							 return;
						 }
						 gl_FragColor = color;
					 }
				 }
				 else{ // 可视域模式
					 if (visibility == 1.0){
						 gl_FragColor = mix(color, helsing_visibleAreaColor, helsing_alpha);
					 }
					 else{
						 if(abs(shadowPosition.z - 0.0) < 0.01){
							 return;
						 }
						 gl_FragColor = mix(color, helsing_invisibleAreaColor, helsing_alpha);
					 }
				 }
			 }`
		}
	},
	getViewShedShader:function(options){
		if(options && options.get){
			return `
			 #define USE_CUBE_MAP_SHADOW true
			 uniform sampler2D colorTexture;
			 uniform sampler2D depthTexture;
			 varying vec2 v_textureCoordinates;
			 uniform mat4 camera_projection_matrix;
			 uniform mat4 camera_view_matrix;
			 uniform samplerCube shadowMap_textureCube;
			 uniform mat4 shadowMap_matrix;
			 uniform vec4 shadowMap_lightPositionEC;
			 uniform vec4 shadowMap_normalOffsetScaleDistanceMaxDistanceAndDarkness;
			 uniform vec4 shadowMap_texelSizeDepthBiasAndNormalShadingSmooth;
			 uniform float helsing_viewDistance;
			 uniform vec4 helsing_visibleAreaColor;
			 uniform vec4 helsing_invisibleAreaColor;

			 struct zx_shadowParameters
			 {
				 vec3 texCoords;
				 float depthBias;
				 float depth;
				 float nDotL;
				 vec2 texelStepSize;
				 float normalShadingSmooth;
				 float darkness;
			 };

			 float czm_shadowVisibility(samplerCube shadowMap, zx_shadowParameters shadowParameters)
			 {
				 float depthBias = shadowParameters.depthBias;
				 float depth = shadowParameters.depth;
				 float nDotL = shadowParameters.nDotL;
				 float normalShadingSmooth = shadowParameters.normalShadingSmooth;
				 float darkness = shadowParameters.darkness;
				 vec3 uvw = shadowParameters.texCoords;
				 depth -= depthBias;
				 float visibility = czm_shadowDepthCompare(shadowMap, uvw, depth);
				 return czm_private_shadowVisibility(visibility, nDotL, normalShadingSmooth, darkness);
			 }

			 vec4 getPositionEC(){
				 return czm_windowToEyeCoordinates(gl_FragCoord);
			 }

			 vec3 getNormalEC(){
				 return vec3(1.);
			 }

			 vec4 toEye(in vec2 uv,in float depth){
				 vec2 xy=vec2((uv.x*2.-1.),(uv.y*2.-1.));
				 vec4 posInCamera=czm_inverseProjection*vec4(xy,depth,1.);
				 posInCamera=posInCamera/posInCamera.w;
				 return posInCamera;
			 }

			 vec3 pointProjectOnPlane(in vec3 planeNormal,in vec3 planeOrigin,in vec3 point){
				 vec3 v01=point-planeOrigin;
				 float d=dot(planeNormal,v01);
				 return(point-planeNormal*d);
			 }

			 float getDepth(in vec4 depth){
				 float z_window=czm_unpackDepth(depth);
				 z_window=czm_reverseLogDepth(z_window);
				 float n_range=czm_depthRange.near;
				 float f_range=czm_depthRange.far;
				 return(2.*z_window-n_range-f_range)/(f_range-n_range);
			 }

			 float shadow(in vec4 positionEC){
				 vec3 normalEC=getNormalEC();
				 zx_shadowParameters shadowParameters;
				 shadowParameters.texelStepSize=shadowMap_texelSizeDepthBiasAndNormalShadingSmooth.xy;
				 shadowParameters.depthBias=shadowMap_texelSizeDepthBiasAndNormalShadingSmooth.z;
				 shadowParameters.normalShadingSmooth=shadowMap_texelSizeDepthBiasAndNormalShadingSmooth.w;
				 shadowParameters.darkness=shadowMap_normalOffsetScaleDistanceMaxDistanceAndDarkness.w;
				 vec3 directionEC=positionEC.xyz-shadowMap_lightPositionEC.xyz;
				 float distance=length(directionEC);
				 directionEC=normalize(directionEC);
				 float radius=shadowMap_lightPositionEC.w;
				 if(distance>radius)
				 {
					 return 2.0;
				 }
				 vec3 directionWC=czm_inverseViewRotation*directionEC;
				 shadowParameters.depth=distance/radius-0.0003;
				 shadowParameters.nDotL=clamp(dot(normalEC,-directionEC),0.,1.);
				 shadowParameters.texCoords=directionWC;
				 float visibility=czm_shadowVisibility(shadowMap_textureCube,shadowParameters);
				 return visibility;
			 }

			 bool visible(in vec4 result)
			 {
				 result.x/=result.w;
				 result.y/=result.w;
				 result.z/=result.w;
				 return result.x>=-1.&&result.x<=1.
				 &&result.y>=-1.&&result.y<=1.
				 &&result.z>=-1.&&result.z<=1.;
			 }

			 void main(){
				 // 釉色 = 结构二维(颜色纹理, 纹理坐标)
				 gl_FragColor = texture2D(colorTexture, v_textureCoordinates);
				 // 深度 = 获取深度(结构二维(深度纹理, 纹理坐标))
				 float depth = getDepth(texture2D(depthTexture, v_textureCoordinates));
				 // 视角 = (纹理坐标, 深度)
				 vec4 viewPos = toEye(v_textureCoordinates, depth);
				 // 世界坐标
				 vec4 wordPos = czm_inverseView * viewPos;
				 // 虚拟相机中坐标
				 vec4 vcPos = camera_view_matrix * wordPos;
				 float near = .001 * helsing_viewDistance;
				 float dis = length(vcPos.xyz);
				 if(dis > near && dis < helsing_viewDistance){
					 // 透视投影
					 vec4 posInEye = camera_projection_matrix * vcPos;
					 // 可视区颜色
					 // vec4 helsing_visibleAreaColor=vec4(0.,1.,0.,.5);
					 // vec4 helsing_invisibleAreaColor=vec4(1.,0.,0.,.5);
					 if(visible(posInEye)){
						 float vis = shadow(viewPos);
						 if(vis > 0.3){
							 gl_FragColor = mix(gl_FragColor,helsing_visibleAreaColor,.5);
						 } else{
							 gl_FragColor = mix(gl_FragColor,helsing_invisibleAreaColor,.5);
						 }
					 }
				 }
			 }`
		}
	}
}
export default Shaders;
