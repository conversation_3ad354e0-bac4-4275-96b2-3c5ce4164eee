#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/circular-dependency-scanner@2.3.0/node_modules/circular-dependency-scanner/dist/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/circular-dependency-scanner@2.3.0/node_modules/circular-dependency-scanner/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/circular-dependency-scanner@2.3.0/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/circular-dependency-scanner@2.3.0/node_modules/circular-dependency-scanner/dist/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/circular-dependency-scanner@2.3.0/node_modules/circular-dependency-scanner/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/circular-dependency-scanner@2.3.0/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../circular-dependency-scanner/dist/cli.js" "$@"
else
  exec node  "$basedir/../circular-dependency-scanner/dist/cli.js" "$@"
fi
