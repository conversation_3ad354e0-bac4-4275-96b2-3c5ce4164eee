import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationDto } from '@/common/dto/pagination.dto';

export class QueryUserDto extends PaginationDto {
  @ApiProperty({ description: '用户名', required: false })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiProperty({ description: '真实姓名', required: false })
  @IsOptional()
  @IsString()
  realName?: string;

  @ApiProperty({ description: '状态：1启用，0禁用', required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  status?: number;

  @ApiProperty({ description: '部门ID', required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  departmentId?: number;

  @ApiProperty({ description: '岗位ID', required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  positionId?: number;
}
