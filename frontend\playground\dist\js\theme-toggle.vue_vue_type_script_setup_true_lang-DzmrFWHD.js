var T=Object.getOwnPropertySymbols;var q=Object.prototype.hasOwnProperty,F=Object.prototype.propertyIsEnumerable;var k=(s,t)=>{var e={};for(var o in s)q.call(s,o)&&t.indexOf(o)<0&&(e[o]=s[o]);if(s!=null&&T)for(var o of T(s))t.indexOf(o)<0&&F.call(s,o)&&(e[o]=s[o]);return e};var M=(s,t,e)=>new Promise((o,r)=>{var n=d=>{try{u(e.next(d))}catch(h){r(h)}},l=d=>{try{u(e.throw(d))}catch(h){r(h)}},u=d=>d.done?o(d.value):Promise.resolve(d.value).then(n,l);u((e=e.apply(s,t)).next())});import{a4 as f,af as i,ag as m,ah as c,ae as _,ai as L,aj as j,a3 as a,J as v,n as g,ac as x,aX as B,az as H,U as W,a$ as I,b0 as O,am as y,as as A,aZ as D,aq as N,al as w,an as G,ao as b,F as R,a1 as z,a0 as U,ap as p,q as X}from"../jse/index-index-BMh_AyeW.js";import{g as V,x as P,c_ as J,c$ as K,d0 as Q,d1 as Y,A as C,d2 as Z,d3 as ee,p as ae,d4 as te,d5 as oe,cS as se,aR as ne,cQ as le,cz as re,d as de,aI as ie,h as ce,$,bR as ue}from"./bootstrap-DShsrVit.js";import{S as pe}from"./sun-ChDfqwZ7.js";const me=V("LanguagesIcon",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]]);const fe=V("MoonStarIcon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9",key:"4ay0iu"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}]]);const he=V("SunMoonIcon",[["path",{d:"M12 8a2.83 2.83 0 0 0 4 4 4 4 0 1 1-4-4",key:"1fu5g2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.9 4.9 1.4 1.4",key:"b9915j"}],["path",{d:"m17.7 17.7 1.4 1.4",key:"qc3ed3"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.3 17.7-1.4 1.4",key:"5gca6"}],["path",{d:"m19.1 4.9-1.4 1.4",key:"wpu9u6"}]]),ge=f({__name:"DropdownMenu",props:{defaultOpen:{type:Boolean},open:{type:Boolean},dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(s,{emit:t}){const r=P(s,t);return(n,l)=>(i(),m(a(J),L(j(a(r))),{default:c(()=>[_(n.$slots,"default")]),_:3},16))}}),ye=f({__name:"DropdownMenuContent",props:{class:{},forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(s,{emit:t}){const e=s,o=t,r=v(()=>{const d=e,{class:l}=d;return k(d,["class"])}),n=P(r,o);return(l,u)=>(i(),m(a(Q),null,{default:c(()=>[g(a(K),x(a(n),{class:a(B)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-[1000] min-w-32 overflow-hidden rounded-md border p-1 shadow-md",e.class)}),{default:c(()=>[_(l.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),_e=f({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{}},setup(s){const t=s;return(e,o)=>(i(),m(a(Y),L(j(t)),{default:c(()=>[_(e.$slots,"default")]),_:3},16))}}),ve=f({__name:"DropdownMenuItem",props:{class:{},inset:{type:Boolean},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},setup(s){const t=s,e=v(()=>{const l=t,{class:r}=l;return k(l,["class"])}),o=C(e);return(r,n)=>(i(),m(a(Z),x(a(o),{class:a(B)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r.inset&&"pl-8",t.class)}),{default:c(()=>[_(r.$slots,"default")]),_:3},16,["class"]))}}),xe=f({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(s){const e=C(s);return(o,r)=>(i(),m(a(ee),x({class:"outline-none"},a(e)),{default:c(()=>[_(o.$slots,"default")]),_:3},16))}}),ke=ae("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground",{defaultVariants:{size:"default",variant:"default"},variants:{size:{default:"h-9 px-3",lg:"h-10 px-3",sm:"h-8 px-2"},variant:{default:"bg-transparent",outline:"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground"}}}),be=f({__name:"ToggleGroup",props:{class:{},size:{},variant:{},rovingFocus:{type:Boolean},disabled:{type:Boolean},orientation:{},dir:{},loop:{type:Boolean},asChild:{type:Boolean},as:{},type:{},modelValue:{},defaultValue:{}},emits:["update:modelValue"],setup(s,{emit:t}){const e=s,o=t;H("toggleGroup",{size:e.size,variant:e.variant});const r=v(()=>{const d=e,{class:l}=d;return k(d,["class"])}),n=P(r,o);return(l,u)=>(i(),m(a(te),x(a(n),{class:a(B)("flex items-center justify-center gap-1",e.class)}),{default:c(()=>[_(l.$slots,"default")]),_:3},16,["class"]))}}),we=f({__name:"ToggleGroupItem",props:{class:{},size:{},variant:{},value:{},defaultValue:{type:Boolean},pressed:{type:Boolean},disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(s){const t=s,e=W("toggleGroup"),o=v(()=>{const h=t,{class:n,size:l,variant:u}=h;return k(h,["class","size","variant"])}),r=C(o);return(n,l)=>{var u,d;return i(),m(a(oe),x(a(r),{class:a(B)(a(ke)({variant:((u=a(e))==null?void 0:u.variant)||n.variant,size:((d=a(e))==null?void 0:d.size)||n.size}),t.class)}),{default:c(()=>[_(n.$slots,"default")]),_:3},16,["class"])}}}),Be=f({name:"DropdownRadioMenu",__name:"dropdown-radio-menu",props:I({menus:{}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(s){const t=O(s,"modelValue");function e(o){t.value=o}return(o,r)=>(i(),m(a(ge),null,{default:c(()=>[g(a(xe),{"as-child":"",class:"flex items-center gap-1"},{default:c(()=>[_(o.$slots,"default")]),_:3}),g(a(ye),{align:"start"},{default:c(()=>[g(a(_e),null,{default:c(()=>[(i(!0),y(R,null,A(o.menus,n=>(i(),m(a(ve),{key:n.key,class:D([n.value===t.value?"bg-accent text-accent-foreground":"","data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground text-foreground/80 mb-1 cursor-pointer"]),onClick:l=>e(n.value)},{default:c(()=>[n.icon?(i(),m(N(n.icon),{key:0,class:"mr-2 size-4"})):w("",!0),n.icon?w("",!0):(i(),y("span",{key:1,class:D([n.value===t.value?"bg-foreground":"","mr-2 size-1.5 rounded-full"])},null,2)),G(" "+b(n.label),1)]),_:2},1032,["class","onClick"]))),128))]),_:1})]),_:1})]),_:3}))}}),Me={class:"text-md flex-center"},$e=["href"],ze=["href"],je=f({name:"Copyright",__name:"copyright",props:{companyName:{default:"Vben Admin"},companySiteLink:{default:""},date:{default:"2024"},icp:{default:""},icpLink:{default:""}},setup(s){return(t,e)=>(i(),y("div",Me,[t.icp?(i(),y("a",{key:0,href:t.icpLink||"javascript:void(0)",class:"hover:text-primary-hover mx-1",target:"_blank"},b(t.icp),9,$e)):w("",!0),G(" Copyright © "+b(t.date)+" ",1),t.companyName?(i(),y("a",{key:1,href:t.companySiteLink||"javascript:void(0)",class:"hover:text-primary-hover mx-1",target:"_blank"},b(t.companyName),9,ze)):w("",!0)]))}}),Ie=f({name:"LanguageToggle",__name:"language-toggle",setup(s){function t(e){return M(this,null,function*(){const o=e;z({app:{locale:o}}),yield se(o)})}return(e,o)=>(i(),y("div",null,[g(a(Be),{menus:a(le),"model-value":a(U).app.locale,"onUpdate:modelValue":t},{default:c(()=>[g(a(ne),null,{default:c(()=>[g(a(me),{class:"text-foreground size-4"})]),_:1})]),_:1},8,["menus","model-value"])]))}}),Ve=f({name:"ThemeToggleButton",__name:"theme-button",props:I({type:{default:"normal"}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(s){const t=s,e=O(s,"modelValue"),o=v(()=>e.value?"light":"dark"),r=v(()=>t.type==="normal"?{variant:"heavy"}:{class:"rounded-full",size:"icon",style:{padding:"7px"},variant:"icon"});function n(l){if(!(document.startViewTransition&&!window.matchMedia("(prefers-reduced-motion: reduce)").matches)||!l){e.value=!e.value;return}const d=l.clientX,h=l.clientY,E=Math.hypot(Math.max(d,innerWidth-d),Math.max(h,innerHeight-h));document.startViewTransition(()=>M(this,null,function*(){e.value=!e.value,yield X()})).ready.then(()=>{const S=[`circle(0px at ${d}px ${h}px)`,`circle(${E}px at ${d}px ${h}px)`];document.documentElement.animate({clipPath:e.value?[...S].reverse():S},{duration:450,easing:"ease-in",pseudoElement:e.value?"::view-transition-old(root)":"::view-transition-new(root)"})})}return(l,u)=>(i(),m(a(de),x({"aria-label":o.value,class:[[`is-${o.value}`],"theme-toggle cursor-pointer border-none bg-none"],"aria-live":"polite"},r.value,{onClick:re(n,["stop"])}),{default:c(()=>u[0]||(u[0]=[p("svg",{"aria-hidden":"true",height:"24",viewBox:"0 0 24 24",width:"24"},[p("mask",{id:"theme-toggle-moon",class:"theme-toggle__moon",fill:"hsl(var(--foreground)/80%)",stroke:"none"},[p("rect",{fill:"white",height:"100%",width:"100%",x:"0",y:"0"}),p("circle",{cx:"40",cy:"8",fill:"black",r:"11"})]),p("circle",{id:"sun",class:"theme-toggle__sun",cx:"12",cy:"12",mask:"url(#theme-toggle-moon)",r:"11"}),p("g",{class:"theme-toggle__sun-beams"},[p("line",{x1:"12",x2:"12",y1:"1",y2:"3"}),p("line",{x1:"12",x2:"12",y1:"21",y2:"23"}),p("line",{x1:"4.22",x2:"5.64",y1:"4.22",y2:"5.64"}),p("line",{x1:"18.36",x2:"19.78",y1:"18.36",y2:"19.78"}),p("line",{x1:"1",x2:"3",y1:"12",y2:"12"}),p("line",{x1:"21",x2:"23",y1:"12",y2:"12"}),p("line",{x1:"4.22",x2:"5.64",y1:"19.78",y2:"18.36"}),p("line",{x1:"18.36",x2:"19.78",y1:"5.64",y2:"4.22"})])],-1)])),_:1},16,["aria-label","class"]))}}),Pe=ie(Ve,[["__scopeId","data-v-ffaeb18c"]]),Oe=f({name:"ThemeToggle",__name:"theme-toggle",props:{shouldOnHover:{type:Boolean,default:!1}},setup(s){function t(r){z({theme:{mode:r?"dark":"light"}})}const{isDark:e}=ce(),o=[{icon:pe,name:"light",title:$("preferences.theme.light")},{icon:fe,name:"dark",title:$("preferences.theme.dark")},{icon:he,name:"auto",title:$("preferences.followSystem")}];return(r,n)=>(i(),y("div",null,[g(a(ue),{disabled:!r.shouldOnHover,side:"bottom"},{trigger:c(()=>[g(Pe,{"model-value":a(e),type:"icon","onUpdate:modelValue":t},null,8,["model-value"])]),default:c(()=>[g(a(be),{"model-value":a(U).theme.mode,class:"gap-2",type:"single",variant:"outline","onUpdate:modelValue":n[0]||(n[0]=l=>a(z)({theme:{mode:l}}))},{default:c(()=>[(i(),y(R,null,A(o,l=>g(a(we),{key:l.name,value:l.name},{default:c(()=>[(i(),m(N(l.icon),{class:"size-5"}))]),_:2},1032,["value"])),64))]),_:1},8,["model-value"])]),_:1},8,["disabled"])]))}});export{fe as M,he as S,ge as _,xe as a,ye as b,ve as c,_e as d,we as e,be as f,Oe as g,Ie as h,je as i,Be as j};
