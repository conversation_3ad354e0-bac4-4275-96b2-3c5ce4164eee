import { createVNode, render } from 'vue';

import { deepMerge } from '../../utils/utils.js';
import ztu from '../../ztu';
import BoxComponent from '../box.vue';

const mountNodes = [];
const Box = {
  open(options, content, mountNode, parent) {
    const defaultOptions = {
      moverect: { left: 0, top: 64, right: 50, bottom: 0 },
      style: { left: 'unset', right: '100px', top: '144px' },
      close: () => {
        if (options.beforeClose && options.beforeClose()) {
          return true;
        }
        const node = mountNodes.find((a) => a.el == mountNode);
        if (node) {
          node.inst = null;
          this.close(mountNode);
        }
      },
    };
    if (typeof content === 'string') {
      content = [
        createVNode(
          'div',
          {
            style: {
              padding: '10px',
            },
          },
          content,
        ),
      ];
    } else if (!Array.isArray(content)) {
      content = [createVNode(content)];
    }
    if (typeof parent === 'string') {
      parent = document.getElementById(parent);
    }
    if (!(parent instanceof HTMLElement)) {
      parent = document.body;
    }
    let mountNodeId = '';
    if (mountNode instanceof HTMLElement) {
      mountNodeId = mountNode.id;
    } else if (typeof mountNode === 'string') {
      mountNodeId = mountNode;
      mountNode = document.getElementById(mountNode);
    }
    if (mountNode && mountNode.firstChild) {
      options = deepMerge(
        {
          style: {
            left: mountNode.firstChild.style.left,
            top: mountNode.firstChild.style.top,
            transform: mountNode.firstChild.style.transform,
          },
        },
        options,
      );
    }
    // 将options参数传入，并将BoxComponent组件转换成虚拟DOM，并赋值给app
    const app = createVNode(
      BoxComponent,
      deepMerge(defaultOptions, options),
      content,
    );
    if (mountNode) {
      const idx = mountNodes.findIndex((a) => a.el == mountNode);
      if (idx !== -1) {
        mountNode.remove();
        mountNodes[idx].inst = null;
        mountNodes.splice(idx, 1);
      }
    }
    // 创建一个空的div
    mountNode = document.createElement('div');
    mountNodeId && (mountNode.id = mountNodeId);
    // render函数的作用就是将Notice组件的虚拟DOM转换成真实DOM并插入到mountNode元素里
    render(app, mountNode);
    // 然后把转换成真实DOM的Notice组件插入到body里

    parent.append(mountNode);
    // document.getElementById('app').appendChild(mountNode);
    mountNodes.push({
      el: mountNode,
      inst: app,
    });
    return mountNode;
  },
  alert(message, mountNode) {
    const h = window.innerHeight;
    const w = window.innerWidth;
    const defaultOptions = {
      mask: true,
      maskClosable: true,
      hasHeader: false,
      style: {
        left: `${w / 2 - 150}px`,
        top: `${h / 2 - 80}px`,
        width: '300px',
      },
    };
    return this.open(defaultOptions, message, mountNode);
  },
  info(title, message, mountNode) {
    const h = window.innerHeight;
    const w = window.innerWidth;
    const defaultOptions = {
      mask: true,
      style: {
        left: `${w / 2 - 150}px`,
        top: `${h / 2 - 80}px`,
        width: '300px',
      },
      ...(typeof title === 'string' ? { title } : title),
    };
    return this.open(defaultOptions, message, mountNode);
  },
  confirm(title, message, ok, cancel) {
    const h = window.innerHeight;
    const w = window.innerWidth;
    const defaultOptions = {
      mask: true,
      style: {
        left: `${w / 2 - 150}px`,
        top: `${h / 2 - 80}px`,
        width: '300px',
      },
      ...(typeof title === 'string' ? { title } : title),
    };
    const node = this.open(defaultOptions, [
      createVNode(
        'div',
        {
          style: { padding: '10px' },
        },
        message,
      ),
      createVNode(
        'div',
        {
          style: {
            'text-align': 'center',
            'border-top': '1px solid',
            padding: '5px',
          },
        },
        [
          createVNode(
            'button',
            {
              style: {
                padding: '2px 20px',
                margin: '5px',
              },
              onclick: () => {
                if (cancel && typeof cancel === 'function') cancel();
                Box.close(node);
              },
            },
            '取消',
          ),
          createVNode(
            'button',
            {
              style: {
                padding: '2px 20px',
                margin: '5px',
              },
              onclick: () => {
                if (ok && typeof ok === 'function') ok();
                Box.close(node);
              },
            },
            '确定',
          ),
        ],
      ),
    ]);
  },
  close(mountNode) {
    const idx = mountNodes.findIndex((a) => a.el == mountNode);
    if (idx !== -1) {
      console.log(mountNodes[idx].inst);
      if (mountNodes[idx].inst) {
        mountNodes[idx].inst.props.close(mountNode);
      } else {
        mountNode.remove();
        mountNodes[idx].inst = null;
        mountNodes.splice(idx, 1);
      }
    }
  },
  closeAll() {
    ztu.utils.one(() => {
      const data = mountNodes.map((a) => a);
      data.forEach((mountNode) => {
        this.close(mountNode.el);
      });
    }, '950DC476-6710-929A-7013-79A4A39FC497');
    /* 		if(this.doing) return;
        this.doing = true;
        while(mountNodes.length){
          let mountNode = mountNodes[0]
          this.close(mountNode.el);
        }
        this.doing=false; */
  },
  show(mountNode) {
    if (mountNode) {
      const item = mountNodes.find((a) => a.el === mountNode);
      if (!item) return;
      if (
        item.inst &&
        item.inst.component &&
        item.inst.component.data.isMinimize
      ) {
        item.inst.component.data.isMinimize = false;
      }
    }
    mountNode && (mountNode.style.display = 'unset');
  },
  hide(mountNode) {
    mountNode && (mountNode.style.display = 'none');
  },
};
export default Box;
/*
// 将组件挂在到节点上！
const creatComp  = (comp,prop)=>{
    var app = createApp(comp,{
        ...prop
    })
    var divEle = document.createElement("div")
    // 让我们节点挂在到一个dom元素上
    document.body.appendChild(divEle)
    app.mount(divEle)
    // 解除绑定的时候
   onUnmounted(()=>{
       app.unmount(divEle)
       document.body.removeChild(divEle)
   })
}
 */
