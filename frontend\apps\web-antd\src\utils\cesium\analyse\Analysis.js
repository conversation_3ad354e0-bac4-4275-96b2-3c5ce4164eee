import * as Cesium from 'cesium';

import core from '../core.js';
import Base from '../core/Base';
import Draw from '../core/Draw';
import JCesium from '../core/JCesium';
import Plugin from '../core/Plugin';
import Primitive from '../core/Primitive';
import BufferAnalysis from './BufferAnalysis';
import CutVolumeAnalysis from './CutVolumeAnalysis';
import LookAroundAnalysis from './LookAroundAnalysis';
import OverlayAnalysis from './OverlayAnalysis';
import PointDistanceAnalysis from './PointDistanceAnalysis';
import SkylineAnalysis from './SkylineAnalysis';
import SlopeAnalysis from './SlopeAnalysis';
import SquareNeighborAnalysis from './SquareNeighborAnalysis';
import VisibilityAnalysis from './VisibilityAnalysis';
import VisualFieldAnalysis from './VisualFieldAnalysis';
/**
 *
 * 分析模块
 * @param {*} viewer
 */
class Analysis {
  constructor(viewer) {
    if (viewer) {
      this._viewer = viewer;
      this._draw = new Draw(this._viewer);
      this._base = new Base(this._viewer);
      this._primitive = new Primitive(this._viewer);
      this._plugin = new Plugin(this._viewer);
      this._analysisLayer = new Cesium.CustomDataSource('analysisLayer');
      viewer && viewer.dataSources.add(this._analysisLayer);
    }
  }
  /**
   * 缓冲区分析
   * @param {*} options
   */
  createBufferAnalysis(options) {
    options = {
      layerInfo: null,
      distance: 10,
      callback: () => {},
      ...options,
    };
    if (this._viewer && options) {
      const $this = this;
      this._base = this._base || new Base(this._viewer);
      this._draw = this._draw || new Draw(this._viewer);
      $this._draw.drawPolygonGraphics({
        async callback(wall, wallobj) {
          if (
            wall.length < 3 ||
            (wall.length == 3 &&
              wall[2].lng == wall[0].lng &&
              wall[2].lat == wall[0].lat)
          ) {
            $this._draw._drawLayer.entities.remove(wallobj);
            return options.callback('cancel');
          }
          const _bufferAnalysis = new BufferAnalysis({
            that: $this,
          });
          _bufferAnalysis
            .execute({
              layerInfo: options.layerInfo,
              distance: options.distance,
              positions: wall,
            })
            .then((res) => {
              options.callback('complete');
            })
            .catch((error) => {
              console.log(error);
              options.callback('error', error);
            });
          options.callback('ready', {
            remove: () => {
              _bufferAnalysis.remove();
              $this._draw._drawLayer.entities.remove(wallobj);
            },
          });
        },
      });
    }
  }
  /**
   * 地形开挖分析
   * @param {*} options
   */
  createClipPlanAnalysis(options) {
    options = options || {};
    if (this._viewer && options) {
      const $this = this;
      const _bottomImg =
        options.bottomImg || 'data/images/excavate_bottom_min.jpg';
      const _height = options.height || 30;
      const _splitNum = options.splitNum || 50;
      const _wallImg = options.wallImg || 'data/images/excavate_side_min.jpg';

      $this._draw.drawPolygonGraphics({
        callback(polygon, polygonObj) {
          $this._draw._drawLayer.entities.remove(polygonObj);

          const terrainClipPlan = new JCesium.TerrainClipPlan($this._viewer, {
            height: _height,
            splitNum: _splitNum,
            wallImg: _wallImg,
            bottomImg: _bottomImg,
          });
          terrainClipPlan.updateData(
            $this._base.transformWGS84ArrayToCartesianArray(polygon),
          );

          if (typeof options.callback === 'function') {
            options.callback({
              remove: () => {
                terrainClipPlan.clear();
              },
            });
          }
        },
      });
    }
  }
  /**
   * 方量分析
   * @param {*} options
   */
  createCutVolumeAnalysis(options) {
    options = {
      height: 20,
      size: 1,
      ...options,
    };
    if (this._viewer && options) {
      const $this = this;
      this._base = this._base || new Base(this._viewer);
      this._draw = this._draw || new Draw(this._viewer);
      $this._draw.drawWallGraphics({
        callback(wall, wallobj) {
          $this._viewer.scene.globe.depthTestAgainstTerrain = true;
          const _cutVolumeAnalysis = new CutVolumeAnalysis({
            height: options.height,
            size: options.size,
            progress: options.progress,
            positions: wall,
            close: options.close,
            that: $this,
          });

          if (typeof options.callback === 'function') {
            options.callback({
              remove: () => {
                _cutVolumeAnalysis.remove();
                $this._draw._drawLayer.entities.remove(wallobj);
                $this._viewer.scene.globe.depthTestAgainstTerrain = false;
              },
            });
          }
        },
      });
    }
  }
  /**
   * 光照分析
   * @param {*} options
   */
  createLightingShadowAnalysis(options) {
    options = options || {};
    if (this._viewer && options) {
      const $this = this;
      this._base = this._base || new Base(this._viewer);
      $this._viewer.scene.globe.depthTestAgainstTerrain = true;
      $this._viewer.scene.globe.enableLighting = true;
      $this._viewer.shadows = true;
      $this._viewer.terrainShadows = Cesium.ShadowMode.RECEIVE_ONLY;
      $this._viewer.shadowMap.darkness = 0.02; // 阴影透明度--越大越透明
      // this._base.setDarkEffect();
      // DirectionalLight 表示 从无限远的地方向单一方向发射的光。
      // viewer.scene.light = new Cesium.DirectionalLight({
      //   direction: new Cesium.Cartesian3(0.354925, -0.890918, -0.283358)
      // })
      if (typeof options.callback === 'function') {
        options.callback({
          remove: () => {
            $this._viewer.scene.globe.enableLighting = false;
            $this._viewer.shadows = false;
            $this._viewer.terrainShadows = Cesium.ShadowMode.DISABLED;
            $this._viewer.scene.globe.depthTestAgainstTerrain = false;
          },
        });
      }
    }
  }
  /**
   * 创建环视分析
   * @param {*} options
   */
  createLookAroundAnalysis(options) {
    options = {
      height: 0,
      space: 10,
      ...options,
    };
    if (this._viewer && options) {
      const $this = this;
      $this._draw.drawCircleGraphics({
        unclickable: true,
        callback(result, obj) {
          $this._draw._drawLayer.entities.remove(obj);

          const _lookAroundAnalysis = new LookAroundAnalysis({
            that: $this,
            radius: result.radius,
            center: result.center,
            viewHeight: options.height,
            spaceAngle: options.space,
          });

          if (typeof options.callback === 'function') {
            console.log(result.center);
            options.callback(_lookAroundAnalysis, {
              radius: result.radius,
              center: $this._base.transformCartesianToWGS84(result.center),
            });
          }
        },
      });
    }
  }
  /**
   * 叠置分析
   * @param {*} options
   */
  createOverlayAnalysis(options) {
    if (this._viewer && options) {
      const $this = this;
      this._base = this._base || new Base(this._viewer);
      this._draw = this._draw || new Draw(this._viewer);
      $this._draw.drawPolygonGraphics({
        async callback(wall, wallobj) {
          $this._draw._drawLayer.entities.remove(wallobj);
          $this._viewer.scene.globe.depthTestAgainstTerrain = false;
          const _overlayAnalysis = new OverlayAnalysis({
            overlayType: options.overlayType,
            that: $this,
          });
          await _overlayAnalysis.execute(options.params, wall);

          if (typeof options.callback === 'function') {
            options.callback({
              remove: () => {
                _overlayAnalysis.remove();
                // $this._draw._drawLayer.entities.remove(wallobj);
              },
            });
          }
        },
      });
    }
  }
  /**
   * 点距离分析
   * @param {*} options
   */
  createPointDistanceAnalysis(options) {
    if (this._viewer && options) {
      const $this = this;
      this._base = this._base || new Base(this._viewer);
      this._draw = this._draw || new Draw(this._viewer);
      $this._draw.drawPolygonGraphics({
        async callback(wall, wallobj) {
          $this._viewer.scene.globe.depthTestAgainstTerrain = false;
          const _pointdistanceAnalysis = new PointDistanceAnalysis({
            complete: options.complete,
            that: $this,
          });
          _pointdistanceAnalysis
            .execute(options.params, wall)
            // .then((res=>{
            // 	if (typeof options.complete === 'function') {
            // 		options.complete(res);
            // 	}
            // }))
            .catch((error) => {
              // console.log(err,'sssssssssssssssssss')
              options.error && options.error(error);
            });
          if (typeof options.callback === 'function') {
            options.callback({
              remove: () => {
                _pointdistanceAnalysis.remove();
                $this._draw._drawLayer.entities.remove(wallobj);
              },
            });
          }
        },
      });
    }
  }
  /**
   * 天际线分析
   * @param {*} options
   */
  createSkylineAnalysis(options) {
    options = options || {};
    if (this._viewer && options) {
      const $this = this;

      const _skylineAnalysis = new SkylineAnalysis({
        that: $this,
      });

      if (typeof options.callback === 'function') {
        options.callback(_skylineAnalysis);
      }
    }
  }
  /**
   * 创建坡度分析
   * @param {*} options
   */
  createSlopeAnalysis(options) {
    options = {
      pointSum: 10,
      ...options,
    };
    /* if (!echarts) {
	
			alert('需要引入echarts库')
			return false;
		} */
    if (this._viewer && options) {
      const $this = this;
      this._draw = this._draw || new Draw(this._viewer);
      $this._draw.drawLineGraphics({
        type: 'straightLine',
        clampToGround: true,
        callback(line, lineObj) {
          var _slopeAnalysis = new SlopeAnalysis({
            pointSum: Math.max(10, Math.min(100, options.pointSum)),
            positions: line,
            close:
              options.close ||
              (() => {
                _slopeAnalysis.remove();
                $this._draw._drawLayer.entities.remove(lineObj);
              }),
            that: $this,
          });

          if (typeof options.callback === 'function') {
            options.callback({
              remove: () => {
                _slopeAnalysis.remove();
                $this._draw._drawLayer.entities.remove(lineObj);
              },
            });
          }
        },
      });
    }
  }
  /**
   * 面邻域分析
   * @param {*} options
   */
  createSquareNeighborAnalysis(options) {
    if (this._viewer && options) {
      const $this = this;
      this._base = this._base || new Base(this._viewer);
      this._draw = this._draw || new Draw(this._viewer);
      $this._draw.drawPolygonGraphics({
        async callback(wall, wallobj) {
          $this._viewer.scene.globe.depthTestAgainstTerrain = false;
          const _squareneighborAnalysis = new SquareNeighborAnalysis({
            complete: options.complete,
            error: options.error,
            that: $this,
          });
          _squareneighborAnalysis.execute(options.params, wall);
          if (typeof options.callback === 'function') {
            options.callback({
              remove: () => {
                _squareneighborAnalysis.remove();
                $this._draw._drawLayer.entities.remove(wallobj);
              },
            });
          }
        },
      });
    }
  }
  /**
   * 创建淹没分析
   * @param {*} options
   */
  createSubmergedAnalysis(options) {
    options = {
      ...options,
    };
    if (this._viewer && options) {
      const $this = this;
      const _baseH = options.baseH || 0;
      const _interval = options.interval || 10;
      const _maxH = options.maxH || 15;
      let _speed = (options.speed || 1) / 100;
      let st = null;
      // _maxH = _maxH+_baseH;
      this._draw = this._draw || new Draw(this._viewer);
      $this._draw.drawPolygonGraphics({
        height: 1,
        callback(polygon, polygonObj) {
          $this._viewer.scene.globe.depthTestAgainstTerrain = true;
          let h = _baseH;
          const heightChange = (height) => {
            if (st) {
              clearInterval(st);
              st = null;
            }
            _speed = Math.abs(_speed) * (height + _baseH > h ? 1 : -1);
            st = setInterval(() => {
              h = h + _speed;
              if (_speed > 0 && h >= height + _baseH) {
                h = height + _baseH;
                if (st) {
                  clearTimeout(st);
                  st = null;
                }
              } else if (_speed <= 0 && h <= height + _baseH) {
                h = height + _baseH;
                if (st) {
                  clearTimeout(st);
                  st = null;
                }
              } else if (h <= _baseH) {
                h = _baseH;
                if (st) {
                  clearTimeout(st);
                  st = null;
                }
              }
              polygonObj.polygon.extrudedHeight = h;
            }, _interval);
          };
          if (polygonObj) {
            setTimeout(() => {
              polygonObj.polygon.heightReference = 'CLAMP_TO_GROUND';
              // polygonObj.polygon.material = "data/images/water.png";
              polygonObj.polygon.material = Cesium.Color.fromBytes(
                64,
                157,
                253,
                150,
              );
              polygonObj.polygon.perPositionHeight = true;
              polygonObj.polygon.extrudedHeight = 0;
              polygonObj.position = core.getPolygonCenter(
                polygonObj.polygon.hierarchy.getValue().positions,
              );
              polygonObj.label = {
                text: new Cesium.CallbackProperty((time, result) => {
                  return `水深:${(h - _baseH).toFixed(2)}米${
                    st ? (_speed > 0 ? '水位正在上涨' : '水位正在下降') : ''
                  }`;
                }, false),
                showBackground: true,
                font: '14px monospace',
                fillColor: Cesium.Color.YELLOW,
                pixelOffset: {
                  x: 0,
                  y: -20,
                },
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
              };
              heightChange(_maxH);
            }, 2000);
          }
          if (options.callback) {
            options.callback({
              remove: () => {
                if (st) {
                  clearInterval(st);
                  st = null;
                }
                $this._draw._drawLayer.entities.remove(polygonObj);
                $this._viewer.scene.globe.depthTestAgainstTerrain = false;
              },
              setHeight: heightChange,
            });
          }
        },
      });
    }
  }
  /**
   * 地表透明分析
   * @param {*} options
   */
  createTransparentSurfaceAnalysis(options) {
    options = {
      alpha: 0.5,
      ...options,
    };

    if (this._viewer && options) {
      const $this = this;
      let alpha = Number(options.alpha);
      alpha = isNaN(alpha) ? 1 : alpha;
      alpha = Cesium.Math.clamp(alpha, 0, 1);
      // screenSpaceCameraController获取用于摄像机输入处理的控制器
      // enableCollisionDetectio启用或禁用摄影机与地形的碰撞检测,为true不会进入地下
      $this._viewer.scene.screenSpaceCameraController.enableCollisionDetection = false;
      // translucency 控制全局半透明性的属性。frontFaceAlphaByDistance表示标量值在视角空间中的近距离和远距离处的下限和上限。
      $this._viewer.scene.globe.translucency.frontFaceAlphaByDistance =
        new Cesium.NearFarScalar(
          400, // 摄像机范围的下限。
          0, // 摄像机范围下限的值。
          800, // 摄像机范围的上限。
          1, // 摄像机范围上限的值。
        );
      $this._viewer.scene.globe.translucency.enabled = true;

      $this._viewer.scene.globe.translucency.frontFaceAlphaByDistance.nearValue =
        alpha;
      $this._viewer.scene.globe.translucency.frontFaceAlphaByDistance.farValue =
        alpha;

      if (typeof options.callback === 'function') {
        options.callback({
          remove: () => {
            $this._viewer.scene.screenSpaceCameraController.enableCollisionDetection = true;
            $this._viewer.scene.globe.translucency.enabled = false;
          },
        });
      }
    }
  }
  /**
   * 创建通视分析
   * @param {*} options
   */
  createVisibilityAnalysis(options) {
    options = {
      height: 0,
      ...options,
    };
    const $this = this;
    $this._draw.drawLineGraphics({
      type: 'straightLine',
      clampToGround: false,
      unclickable: true,
      callback(line, lineObj) {
        console.log(options.height);
        const _visibilityAnalysis = new VisibilityAnalysis({
          positions: line,
          that: $this,
          close: options.close,
          height: options.height,
        });
        $this._draw._drawLayer.entities.remove(lineObj);
        if (typeof options.callback === 'function') {
          options.callback(_visibilityAnalysis);
        }
      },
    });
  }
  /**
   * 创建可视域分析
   * @param {*} options
   */
  createVisualFieldAnalysis(options) {
    options = {
      horizontalFov: 60,
      verticalFov: 45,
      height: 1.8,
      ...options,
    };
    if (this._viewer && options) {
      const $this = this;
      $this._draw.drawLineGraphics({
        type: 'straightLine',
        clampToGround: false,
        unclickable: true,
        callback(line, lineObj) {
          $this._viewer.scene.globe.depthTestAgainstTerrain = true;
          line.forEach((a) => {
            a.alt = a.alt + options.height;
          });
          const _visualFieldAnalysis = new VisualFieldAnalysis({
            horizontalViewAngle: options.horizontalFov,
            verticalViewAngle: options.verticalFov,
            viewPosition: line[0],
            viewPositionEnd: line[1],
            that: $this,
          });
          $this._draw._drawLayer.entities.remove(lineObj);
          if (typeof options.callback === 'function') {
            options.callback(_visualFieldAnalysis);
          }
        },
      });
      /* var $this = this,
				_shadowPrimitive = null;
			 $this._base.bindHandelEvent(
				function click(event, _handlers) {
					var position = $this._viewer.scene.pickPosition(event.position);
					if (!position) return false
					if (!Cesium.defined(_shadowPrimitive)) {
						// 创建shadowPrimitve
						_shadowPrimitive = new JCesium.ShadowPrimitive({
							horizontalFov:options.horizontalFov,
							verticalFov:options.verticalFov,
							scene: $this._viewer.scene,
							viewerPosition: position
						});
	
						$this._analysisLayer._primitives.add(_shadowPrimitive);
					} else {
	
						_handlers.destroy()
						_handlers = null
						if(options.callback){
							options.callback({
								remove:()=>{
									_shadowPrimitive && ($this._analysisLayer._primitives.remove(_shadowPrimitive));
								},
								// setOptions(options){
								// 	for(let p in options){
								// 		_shadowPrimitive[p]=options[p];
								// 	}
								// },
								// getOptions(){
								// 	return{
								// 		invisiblyColor:_shadowPrimitive.invisiblyColor,
								// 		visiblyColor:_shadowPrimitive.visiblyColor
								// 		viewerPosition:_shadowPrimitive.viewerPosition
								// 		direction:_shadowPrimitive.direction
								// 		pitch:_shadowPrimitive.pitch
								// 		horizontalFov:_shadowPrimitive.horizontalFov
								// 		verticalFov:_shadowPrimitive.verticalFov
								// 		distance:_shadowPrimitive.distance
								// 	}
								// },
								shaowPrimitive:_shadowPrimitive
							})
						}
					}
				},
				function move(event) {
	
					var position = $this._viewer.scene.pickPosition(event.endPosition);
					if (!position) return false
					if (_shadowPrimitive) _shadowPrimitive.setPoseByTargetPoint(position);
				}) */
    }
  }
}

export default Analysis;
