{"version": 3, "sources": ["../../../../../node_modules/.pnpm/watermark-js-plus@1.6.0/node_modules/watermark-js-plus/node_modules/style-inject/dist/style-inject.es.js", "../../../../../node_modules/.pnpm/watermark-js-plus@1.6.0/node_modules/watermark-js-plus/node_modules/tslib/tslib.es6.js", "../../../../../node_modules/.pnpm/watermark-js-plus@1.6.0/node_modules/watermark-js-plus/src/utils/index.ts", "../../../../../node_modules/.pnpm/watermark-js-plus@1.6.0/node_modules/watermark-js-plus/src/utils/initialization.ts", "../../../../../node_modules/.pnpm/watermark-js-plus@1.6.0/node_modules/watermark-js-plus/src/utils/protection.ts", "../../../../../node_modules/.pnpm/watermark-js-plus@1.6.0/node_modules/watermark-js-plus/src/core/canvas.ts", "../../../../../node_modules/.pnpm/watermark-js-plus@1.6.0/node_modules/watermark-js-plus/src/core/layout/grid.ts", "../../../../../node_modules/.pnpm/watermark-js-plus@1.6.0/node_modules/watermark-js-plus/src/core/layout/index.ts", "../../../../../node_modules/.pnpm/watermark-js-plus@1.6.0/node_modules/watermark-js-plus/src/core/watermark.ts", "../../../../../node_modules/.pnpm/watermark-js-plus@1.6.0/node_modules/watermark-js-plus/src/core/blind.ts", "../../../../../node_modules/.pnpm/watermark-js-plus@1.6.0/node_modules/watermark-js-plus/src/core/image.ts"], "sourcesContent": ["function styleInject(css, ref) {\n  if ( ref === void 0 ) ref = {};\n  var insertAt = ref.insertAt;\n\n  if (!css || typeof document === 'undefined') { return; }\n\n  var head = document.head || document.getElementsByTagName('head')[0];\n  var style = document.createElement('style');\n  style.type = 'text/css';\n\n  if (insertAt === 'top') {\n    if (head.firstChild) {\n      head.insertBefore(style, head.firstChild);\n    } else {\n      head.appendChild(style);\n    }\n  } else {\n    head.appendChild(style);\n  }\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nexport default styleInject;\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\r\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\r\n    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nvar ownKeys = function(o) {\r\n    ownKeys = Object.getOwnPropertyNames || function (o) {\r\n        var ar = [];\r\n        for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\r\n        return ar;\r\n    };\r\n    return ownKeys(o);\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose, inner;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n            if (async) inner = dispose;\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    var r, s = 0;\r\n    function next() {\r\n        while (r = env.stack.pop()) {\r\n            try {\r\n                if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\r\n                if (r.dispose) {\r\n                    var result = r.dispose.call(r.value);\r\n                    if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n                }\r\n                else s |= 1;\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\r\n    if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\r\n        return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\r\n            return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\r\n        });\r\n    }\r\n    return path;\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __esDecorate: __esDecorate,\r\n    __runInitializers: __runInitializers,\r\n    __propKey: __propKey,\r\n    __setFunctionName: __setFunctionName,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n    __rewriteRelativeImportExtension: __rewriteRelativeImportExtension,\r\n};\r\n", "import type { CustomContentSVGType, WatermarkOptions } from '../types'\n\nexport const convertImage = (canvas: HTMLCanvasElement): string => {\n  return canvas.toDataURL('image/png', 1)\n}\n\nexport const isFunction = (value: Function): boolean => {\n  return typeof value === 'function'\n}\n\nexport const isUndefined = (value: any): boolean => {\n  return value === undefined\n}\n\nexport const isString = (value: any): boolean => {\n  return typeof value === 'string'\n}\n\nexport const createSVGElement = (\n  tagName: string,\n  attrs: { [key: string]: string } = {},\n  namespaceURI = 'http://www.w3.org/2000/svg',\n): Element => {\n  const element = document.createElementNS(namespaceURI, tagName)\n  for (const attr in attrs) {\n    element.setAttribute(attr, attrs[attr])\n  }\n  return element\n}\n\nexport const getMultiLineData = (ctx: CanvasRenderingContext2D, text: string, maxWidth: number) => {\n  const result = []\n  let str = ''\n  let word = ''\n  for (let i = 0, len = text.length; i < len; i++) {\n    word = text.charAt(i)\n    if (word === '\\n') {\n      result.push(str)\n      str = ''\n      continue\n    }\n    str += word\n    if (ctx.measureText(str).width > maxWidth) {\n      result.push(str.substring(0, str.length - 1))\n      str = ''\n      i--\n    }\n  }\n  result.push(str)\n  return result\n}\n\nexport const createCustomContentSVG = async (\n  ctx: CanvasRenderingContext2D,\n  options: WatermarkOptions,\n): Promise<CustomContentSVGType> => {\n  const svgElement = createSVGElement('svg', {\n    xmlns: 'http://www.w3.org/2000/svg',\n  })\n  const bodyElement = document.createElement('div')\n  bodyElement.setAttribute('xmlns', 'http://www.w3.org/1999/xhtml')\n  bodyElement.style.cssText = `\n  text-align: center;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  font: ${ctx.font};\n  color: ${options.fontColor};\n`\n  bodyElement.innerHTML = `<div class='rich-text-content'>${options.content}</div>`\n  document.body.appendChild(bodyElement)\n  // convert all images to base64\n  await convertImgToBase64(bodyElement)\n  const rect = bodyElement.querySelector('.rich-text-content')?.getBoundingClientRect()\n  const rectWidth = rect?.width\n  const rectHeight = rect?.height\n  document.body.removeChild(bodyElement)\n  const width = options.richTextWidth || rectWidth || options.width\n  const height = options.richTextHeight || rectHeight || options.height\n  svgElement.setAttribute('width', width.toString())\n  svgElement.setAttribute('height', height.toString())\n  const foreignObjectElement = createSVGElement('foreignObject', {\n    width: width.toString(),\n    height: height.toString(),\n  })\n  foreignObjectElement.appendChild(bodyElement)\n  svgElement.appendChild(foreignObjectElement)\n  return {\n    element: svgElement,\n    width,\n    height,\n  }\n}\n\nasync function convertImgToBase64(bodyElement: HTMLElement) {\n  const imgElements = bodyElement.querySelectorAll('img')\n\n  for (const img of Array.from(imgElements)) {\n    const src = img.getAttribute('src')\n    if (src) {\n      try {\n        const response = await fetch(src)\n        const blob = await response.blob()\n        const imgData = await new Promise((resolve, reject) => {\n          const reader = new FileReader()\n          reader.onloadend = () => resolve(reader.result)\n          reader.onerror = reject\n          reader.readAsDataURL(blob)\n        })\n        if (isString(imgData)) {\n          img.setAttribute('src', imgData as string)\n        }\n      } catch (error) {\n        console.error(`Error converting ${src} to base64:`, error)\n      }\n    }\n  }\n}\n\nexport const convertSVGToImage = (svg: Element): string => {\n  const richContent = svg.outerHTML\n    .replace(/<(img|br|input|hr|embed)(.*?)>/g, '<$1$2/>')\n    .replace(/\\n/g, '')\n    .replace(/\\t/g, '')\n    .replace(/#/g, '%23')\n  return `data:image/svg+xml;charset=utf-8,${richContent}`\n}\n\nexport const getValue = (v1: any, v2: any) => {\n  if (isUndefined(v1)) {\n    return v2\n  } else {\n    return v1\n  }\n}\n\nexport const loadImage = (\n  url: string,\n  width: number | undefined = undefined,\n  height: number | undefined = undefined,\n): Promise<HTMLImageElement> => {\n  const image = new Image()\n  image.setAttribute('crossOrigin', 'anonymous')\n  !isUndefined(width) && (image.width = <number>width)\n  !isUndefined(height) && (image.height = <number>height)\n  image.src = url\n  return new Promise(resolve => {\n    image.onload = () => {\n      resolve(image)\n    }\n  })\n}\n\nexport const generateMatrix = (rows: number, columns: number, value: any) => {\n  return Array.from({ length: rows }, () => new Array(columns).fill(value))\n}\n\nexport const generateAnimationStyle = (movable: boolean, backgroundRepeat: string) => {\n  if (!movable) {\n    return ''\n  }\n  const horizontalDuration = Math.random() * (8 - 2) + 2\n  const verticalDuration = Math.random() * (4 - 2) + 2\n  switch (backgroundRepeat) {\n    case 'repeat':\n      return 'animation: 200s linear 0s infinite alternate watermark !important;'\n    case 'repeat-x':\n      return `animation: ${horizontalDuration}s ease-in 0s infinite alternate watermark-vertical !important;'`\n    case 'repeat-y':\n      return `animation: ${verticalDuration}s ease-out 0s infinite alternate watermark-horizontal !important;'`\n    case 'no-repeat':\n      return `animation: ${horizontalDuration}s ease-in 0s infinite alternate watermark-horizontal, ${verticalDuration}s ease-out 0s infinite alternate watermark-vertical !important;`\n    default:\n      return ''\n  }\n}\n", "import type { AdvancedStyleParamsType, TextAlignType, TextBaselineType, WatermarkOptions } from '../types'\nimport { getMultiLineData, isUndefined } from './index'\n\nexport const initialOptions: WatermarkOptions = {\n  width: 300,\n  height: 300,\n  rotate: 45,\n  layout: 'default',\n  auxiliaryLine: false,\n  translatePlacement: 'middle',\n  contentType: 'text',\n  content: 'hello watermark-js-plus',\n  textType: 'fill',\n  imageWidth: 0,\n  imageHeight: 0,\n  lineHeight: 30,\n  zIndex: 2147483647,\n  backgroundPosition: '0 0',\n  backgroundRepeat: 'repeat',\n  fontSize: '20px',\n  fontFamily: 'sans-serif',\n  fontStyle: '',\n  fontVariant: '',\n  fontColor: '#000',\n  fontWeight: 'normal',\n  filter: 'none',\n  letterSpacing: '0px',\n  wordSpacing: '0px',\n  globalAlpha: 0.5,\n  mode: 'default',\n  mutationObserve: true,\n  monitorProtection: false,\n  movable: false,\n  parent: 'body',\n  onSuccess: () => {},\n  onBeforeDestroy: () => {},\n  onDestroyed: () => {},\n  onObserveError: () => {},\n}\n\nexport const generateRecommendOptions = (\n  canvas: HTMLCanvasElement,\n  options: WatermarkOptions,\n  args: Partial<WatermarkOptions>,\n) => {\n  const ctx = canvas.getContext('2d')\n  if (ctx === null) {\n    throw new Error('get context error')\n  }\n  ctx.font = `${options.fontStyle} ${options.fontVariant} ${options.fontWeight} ${options.fontSize} ${options.fontFamily}`\n  ctx.filter = options.filter\n  // @ts-ignore\n  ctx.letterSpacing = options.letterSpacing\n  ctx.wordSpacing = options.wordSpacing\n  if (options?.rotate) {\n    options.rotate = (360 - (options.rotate % 360)) * (Math.PI / 180)\n  }\n  if (isUndefined(args.textRowMaxWidth)) {\n    options.textRowMaxWidth = options.width\n  }\n  const result = {\n    image: {\n      rect: {\n        width: options.imageWidth,\n        height: options.imageHeight,\n      },\n      position: {\n        x: 0,\n        y: 0,\n      },\n    },\n    textLine: {\n      data: [] as string[],\n      yOffsetValue: 0,\n    },\n    advancedStyleParams: {\n      linear: {\n        x0: 0,\n        x1: 0,\n      },\n      radial: {\n        x0: 0,\n        y0: 0,\n        r0: 0,\n        x1: 0,\n        y1: 0,\n        r1: 0,\n      },\n      conic: {\n        x: 0,\n        y: 0,\n        startAngle: 0,\n      },\n      pattern: {},\n    } as AdvancedStyleParamsType,\n  }\n  switch (options.contentType) {\n    case 'text':\n      result.textLine.data = [options.content]\n      break\n    case 'multi-line-text':\n      result.textLine.data = getMultiLineData(ctx, options.content, <number>options.textRowMaxWidth)\n      break\n    // case 'image':\n    //   break\n    // case 'rich-text':\n    //   break\n  }\n  let translateX: number = options.width / 2\n  let translateY: number = options.height / 2\n  let textBaseline: TextBaselineType = 'middle'\n  let textAlign: TextAlignType = 'center'\n\n  if (!isUndefined(args?.translateX) && !isUndefined(args?.translateY)) {\n    translateX = <number>args?.translateX\n    translateY = <number>args?.translateY\n    textBaseline = 'top'\n    textAlign = 'left'\n  } else {\n    // default middle\n    // translateX = options.width / 2\n    // translateY = options.height / 2\n    // TextBaselineType = 'middle'\n    // textAlign = 'center'\n    result.advancedStyleParams.linear.x0 = -options.width / 2\n    result.advancedStyleParams.linear.x1 = options.width / 2\n    // result.advancedStyleParams.radial.x0 = 0\n    // result.advancedStyleParams.radial.y0 = 0\n    result.advancedStyleParams.radial.r0 = 0\n    // result.advancedStyleParams.radial.x1 = 0\n    // result.advancedStyleParams.radial.y1 = 0\n    result.advancedStyleParams.radial.r1 = options.width / 2\n    // result.advancedStyleParams.conic.x = 0\n    // result.advancedStyleParams.conic.y = 0\n  }\n  switch (args.translatePlacement) {\n    case 'top':\n      translateX = options.width / 2\n      translateY = 0\n      textBaseline = 'top'\n      // textAlign = 'center'\n      result.advancedStyleParams.linear.x0 = -options.width / 2\n      result.advancedStyleParams.linear.x1 = options.width / 2\n      // result.advancedStyleParams.radial.x0 = 0\n      result.advancedStyleParams.radial.y0 = (result.textLine.data.length * options.lineHeight) / 2\n      // result.advancedStyleParams.radial.r0 = 0\n      // result.advancedStyleParams.radial.x1 = 0\n      // result.advancedStyleParams.radial.y1 = 0\n      result.advancedStyleParams.radial.y1 = (result.textLine.data.length * options.lineHeight) / 2\n      // result.advancedStyleParams.radial.r1 = options.width / 2\n      // result.advancedStyleParams.conic.x = 0\n      result.advancedStyleParams.conic.y = (result.textLine.data.length * options.lineHeight) / 2\n      break\n    case 'top-start':\n      translateX = 0\n      translateY = 0\n      textBaseline = 'top'\n      textAlign = 'start'\n      result.advancedStyleParams.linear.x0 = 0\n      result.advancedStyleParams.linear.x1 = options.width\n      result.advancedStyleParams.radial.x0 = options.width / 2\n      result.advancedStyleParams.radial.y0 = (result.textLine.data.length * options.lineHeight) / 2\n      // result.advancedStyleParams.radial.r0 = 0\n      result.advancedStyleParams.radial.x1 = options.width / 2\n      result.advancedStyleParams.radial.y1 = (result.textLine.data.length * options.lineHeight) / 2\n      // result.advancedStyleParams.radial.r1 = options.width / 2\n      result.advancedStyleParams.conic.x = options.width / 2\n      result.advancedStyleParams.conic.y = (result.textLine.data.length * options.lineHeight) / 2\n      break\n    case 'top-end':\n      translateX = options.width\n      translateY = 0\n      textBaseline = 'top'\n      textAlign = 'end'\n      result.advancedStyleParams.linear.x0 = 0\n      result.advancedStyleParams.linear.x1 = -options.width\n      result.advancedStyleParams.radial.x0 = -options.width / 2\n      result.advancedStyleParams.radial.y0 = (result.textLine.data.length * options.lineHeight) / 2\n      // result.advancedStyleParams.radial.r0 = 0\n      result.advancedStyleParams.radial.x1 = -options.width / 2\n      result.advancedStyleParams.radial.y1 = (result.textLine.data.length * options.lineHeight) / 2\n      // result.advancedStyleParams.radial.r1 = options.width / 2\n      result.advancedStyleParams.conic.x = -options.width / 2\n      result.advancedStyleParams.conic.y = (result.textLine.data.length * options.lineHeight) / 2\n      break\n    case 'bottom':\n      translateX = options.width / 2\n      translateY = options.height\n      textBaseline = 'bottom'\n      // textAlign = 'center'\n      result.advancedStyleParams.linear.x0 = -options.width / 2\n      result.advancedStyleParams.linear.x1 = options.width / 2\n      // result.advancedStyleParams.radial.x0 = 0\n      result.advancedStyleParams.radial.y0 = (-result.textLine.data.length * options.lineHeight) / 2\n      // result.advancedStyleParams.radial.r0 = 0\n      // result.advancedStyleParams.radial.x1 = 0\n      result.advancedStyleParams.radial.y1 = (-result.textLine.data.length * options.lineHeight) / 2\n      // result.advancedStyleParams.radial.r1 = options.width / 2\n      result.advancedStyleParams.conic.x = 0\n      result.advancedStyleParams.conic.y = (-result.textLine.data.length * options.lineHeight) / 2\n      break\n    case 'bottom-start':\n      translateX = 0\n      translateY = options.height\n      textBaseline = 'bottom'\n      textAlign = 'start'\n      result.advancedStyleParams.linear.x0 = 0\n      result.advancedStyleParams.linear.x1 = options.width\n      result.advancedStyleParams.radial.x0 = options.width / 2\n      result.advancedStyleParams.radial.y0 = (-result.textLine.data.length * options.lineHeight) / 2\n      // result.advancedStyleParams.radial.r0 = 0\n      result.advancedStyleParams.radial.x1 = options.width / 2\n      result.advancedStyleParams.radial.y1 = (-result.textLine.data.length * options.lineHeight) / 2\n      // result.advancedStyleParams.radial.r1 = options.width / 2\n      result.advancedStyleParams.conic.x = options.width / 2\n      result.advancedStyleParams.conic.y = (-result.textLine.data.length * options.lineHeight) / 2\n      break\n    case 'bottom-end':\n      translateX = options.width\n      translateY = options.height\n      textBaseline = 'bottom'\n      textAlign = 'end'\n      result.advancedStyleParams.linear.x0 = 0\n      result.advancedStyleParams.linear.x1 = -options.width\n      result.advancedStyleParams.radial.x0 = -options.width / 2\n      result.advancedStyleParams.radial.y0 = (-result.textLine.data.length * options.lineHeight) / 2\n      // result.advancedStyleParams.radial.r0 = 0\n      result.advancedStyleParams.radial.x1 = -options.width / 2\n      result.advancedStyleParams.radial.y1 = (-result.textLine.data.length * options.lineHeight) / 2\n      // result.advancedStyleParams.radial.r1 = options.width / 2\n      result.advancedStyleParams.conic.x = -options.width / 2\n      result.advancedStyleParams.conic.y = (-result.textLine.data.length * options.lineHeight) / 2\n      break\n    case 'left':\n      translateX = 0\n      translateY = options.height / 2\n      // TextBaselineType = 'middle'\n      textAlign = 'start'\n      result.advancedStyleParams.linear.x0 = 0\n      result.advancedStyleParams.linear.x1 = options.width\n      result.advancedStyleParams.radial.x0 = options.width / 2\n      // result.advancedStyleParams.radial.y0 = 0\n      // result.advancedStyleParams.radial.r0 = 0\n      result.advancedStyleParams.radial.x1 = options.width / 2\n      // result.advancedStyleParams.radial.y1 = 0\n      // result.advancedStyleParams.radial.r1 = options.width / 2\n      result.advancedStyleParams.conic.x = options.width / 2\n      result.advancedStyleParams.conic.y = 0\n      break\n    case 'right':\n      translateX = options.width\n      translateY = options.height / 2\n      // TextBaselineType = 'middle'\n      textAlign = 'end'\n      result.advancedStyleParams.linear.x0 = 0\n      result.advancedStyleParams.linear.x1 = -options.width\n      result.advancedStyleParams.radial.x0 = -options.width / 2\n      // result.advancedStyleParams.radial.y0 = 0\n      // result.advancedStyleParams.radial.r0 = 0\n      result.advancedStyleParams.radial.x1 = -options.width / 2\n      // result.advancedStyleParams.radial.y1 = 0\n      // result.advancedStyleParams.radial.r1 = options.width / 2\n      result.advancedStyleParams.conic.x = -options.width / 2\n      result.advancedStyleParams.conic.y = 0\n      break\n  }\n  options.translateX = translateX\n  options.translateY = translateY\n  isUndefined(args?.textBaseline) && (options.textBaseline = textBaseline)\n  isUndefined(args?.textAlign) && (options.textAlign = textAlign)\n\n  if (['text', 'multi-line-text'].includes(options.contentType)) {\n    switch (options.textBaseline) {\n      case 'middle':\n        result.textLine.yOffsetValue = ((result.textLine.data.length - 1) * options.lineHeight) / 2\n        break\n      case 'bottom':\n      case 'alphabetic':\n      case 'ideographic':\n        result.textLine.yOffsetValue =\n          (result.textLine.data.length - 1) * options.lineHeight + (options.lineHeight - parseInt(options.fontSize)) / 2\n        break\n      case 'top':\n      case 'hanging':\n        result.textLine.yOffsetValue = -options.lineHeight / 2 + parseInt(options.fontSize) / 2\n        break\n    }\n  }\n\n  return result\n}\n", "export default (need: boolean) => {\n  if (typeof window === 'undefined') {\n    return\n  }\n  if (need) {\n    Object.defineProperty(window, 'MutationObserver', {\n      writable: false,\n      configurable: false,\n    })\n    Object.defineProperty(window, 'requestAnimationFrame', {\n      writable: false,\n      configurable: false,\n    })\n  }\n}\n", "import type { WatermarkOptions } from '../types'\nimport { convertSVGToImage, createCustomContentSVG, getValue, isFunction, isUndefined, loadImage } from '../utils'\nimport { generateRecommendOptions } from '../utils/initialization'\nclass WatermarkCanvas {\n  private readonly options: WatermarkOptions\n  private readonly props?: Partial<WatermarkOptions>\n  private readonly canvas: HTMLCanvasElement\n  public recommendOptions\n\n  constructor(args: Partial<WatermarkOptions>, options: WatermarkOptions) {\n    this.props = args\n    this.options = options\n    this.canvas = WatermarkCanvas.createCanvas(this.options.width, this.options.height)\n    this.recommendOptions = generateRecommendOptions(this.canvas, this.options, this.props)\n  }\n\n  /**\n   * Create an HD canvas.\n   * @param width - width of canvas\n   * @param height - height of canvas\n   */\n  static createCanvas(width: number, height: number): HTMLCanvasElement {\n    const ratio = window.devicePixelRatio || 1\n    const canvas = document.createElement('canvas')\n    canvas.width = width * ratio // actual rendered pixel\n    canvas.height = height * ratio // actual rendered pixel\n    canvas.style.width = `${width}px` // control display size\n    canvas.style.height = `${height}px` // control display size\n    canvas.getContext('2d')?.setTransform(ratio, 0, 0, ratio, 0, 0)\n    return canvas\n  }\n\n  /**\n   * Clean the canvas\n   * @param canvas\n   */\n  static clearCanvas(canvas: HTMLCanvasElement) {\n    const ctx = canvas.getContext('2d')\n    if (ctx === null) {\n      throw new Error('get context error')\n    }\n    ctx.restore()\n    ctx.resetTransform()\n    ctx.clearRect(0, 0, canvas.width, canvas.height)\n    const ratio = window.devicePixelRatio || 1\n    ctx.setTransform(ratio, 0, 0, ratio, 0, 0)\n  }\n\n  getCanvas(): HTMLCanvasElement {\n    return this.canvas\n  }\n\n  clear() {\n    WatermarkCanvas.clearCanvas(this.canvas)\n  }\n\n  draw(): Promise<HTMLCanvasElement> {\n    const ctx = this.canvas.getContext('2d')\n    if (ctx === null) {\n      throw new Error('get context error')\n    }\n\n    if (this.options.auxiliaryLine) {\n      ctx.beginPath()\n      ctx.rect(0, 0, this.options.width, this.options.height)\n      ctx.lineWidth = 1\n      ctx.strokeStyle = '#000'\n      ctx.stroke()\n      ctx.closePath()\n\n      ctx.beginPath()\n      ctx.rect(this.options.translateX as number, this.options.translateY as number, 1, 1)\n      ctx.lineWidth = 1\n      ctx.strokeStyle = '#f00'\n      ctx.stroke()\n      ctx.closePath()\n    }\n\n    this.setStyle(ctx)\n    ctx.save()\n    ctx.translate(this.options.translateX as number, this.options.translateY as number)\n    ctx.rotate(this.options.rotate)\n    return new Promise(resolve => {\n      switch (this.options.contentType) {\n        case 'text':\n          this.drawText(ctx, resolve)\n          break\n        case 'image':\n          this.drawImage(ctx, resolve)\n          break\n        case 'multi-line-text':\n          this.drawMultiLineText(ctx, resolve)\n          break\n        case 'rich-text':\n          this.drawRichText(ctx, resolve)\n          break\n      }\n    })\n  }\n\n  private setStyle(ctx: CanvasRenderingContext2D) {\n    let propName: 'fillStyle' | 'strokeStyle' = 'fillStyle'\n    if (this.options.textType === 'stroke') {\n      propName = 'strokeStyle'\n    }\n    let style: string | CanvasGradient | CanvasPattern | null = this.options.fontColor\n    if (this.options?.advancedStyle) {\n      switch (this.options.advancedStyle.type) {\n        case 'linear':\n          style = this.createLinearGradient(ctx)\n          break\n        case 'radial':\n          style = this.createRadialGradient(ctx)\n          break\n        case 'conic':\n          style = this.createConicGradient(ctx)\n          break\n        case 'pattern':\n          style = this.createPattern(ctx)\n          break\n      }\n    }\n    ctx[propName] && style && (ctx[propName] = style)\n\n    this.options.textAlign && (ctx.textAlign = this.options.textAlign)\n    this.options.textBaseline && (ctx.textBaseline = this.options.textBaseline)\n    ctx.globalAlpha = this.options.globalAlpha\n    if (this.options.shadowStyle) {\n      ctx.shadowBlur = getValue(this.options.shadowStyle.shadowBlur, 0)\n      ctx.shadowColor = getValue(this.options.shadowStyle.shadowColor, '#00000000')\n      ctx.shadowOffsetX = getValue(this.options.shadowStyle.shadowOffsetX, 0)\n      ctx.shadowOffsetY = getValue(this.options.shadowStyle.shadowOffsetY, 0)\n    }\n    if (isFunction(<Function>this.options.extraDrawFunc)) {\n      ;(<Function>this.options.extraDrawFunc)(ctx)\n    }\n  }\n\n  private createLinearGradient(ctx: CanvasRenderingContext2D): CanvasGradient {\n    const gradient = ctx.createLinearGradient(\n      <number>(\n        getValue(this.options.advancedStyle?.params?.linear?.x0, this.recommendOptions.advancedStyleParams.linear.x0)\n      ),\n      <number>getValue(this.options.advancedStyle?.params?.linear?.y0, 0),\n      <number>(\n        getValue(this.options.advancedStyle?.params?.linear?.x1, this.recommendOptions.advancedStyleParams.linear.x1)\n      ),\n      <number>getValue(this.options.advancedStyle?.params?.linear?.y1, 0),\n    )\n    this.options?.advancedStyle?.colorStops?.forEach(item => {\n      gradient.addColorStop(item.offset, item.color)\n    })\n    return gradient\n  }\n\n  private createConicGradient(ctx: CanvasRenderingContext2D): CanvasGradient {\n    const gradient = ctx.createConicGradient(\n      <number>getValue(this.options?.advancedStyle?.params?.conic?.startAngle, 0),\n      <number>(\n        getValue(this.options?.advancedStyle?.params?.conic?.x, this.recommendOptions.advancedStyleParams.conic.x)\n      ),\n      <number>(\n        getValue(this.options?.advancedStyle?.params?.conic?.y, this.recommendOptions.advancedStyleParams.conic.y)\n      ),\n    )\n    this.options?.advancedStyle?.colorStops?.forEach(item => {\n      gradient.addColorStop(item.offset, item.color)\n    })\n    return gradient\n  }\n\n  private createRadialGradient(ctx: CanvasRenderingContext2D): CanvasGradient {\n    const gradient = ctx.createRadialGradient(\n      <number>(\n        getValue(this.options?.advancedStyle?.params?.radial?.x0, this.recommendOptions.advancedStyleParams.radial.x0)\n      ),\n      <number>(\n        getValue(this.options?.advancedStyle?.params?.radial?.y0, this.recommendOptions.advancedStyleParams.radial.y0)\n      ),\n      <number>(\n        getValue(this.options?.advancedStyle?.params?.radial?.r0, this.recommendOptions.advancedStyleParams.radial.r0)\n      ),\n      <number>(\n        getValue(this.options?.advancedStyle?.params?.radial?.x1, this.recommendOptions.advancedStyleParams.radial.x1)\n      ),\n      <number>(\n        getValue(this.options?.advancedStyle?.params?.radial?.y1, this.recommendOptions.advancedStyleParams.radial.y1)\n      ),\n      <number>(\n        getValue(this.options?.advancedStyle?.params?.radial?.r1, this.recommendOptions.advancedStyleParams.radial.r1)\n      ),\n    )\n    this.options?.advancedStyle?.colorStops?.forEach(item => {\n      gradient.addColorStop(item.offset, item.color)\n    })\n    return gradient\n  }\n\n  private createPattern(ctx: CanvasRenderingContext2D): CanvasPattern | null {\n    return ctx.createPattern(\n      <HTMLImageElement | SVGImageElement | HTMLVideoElement | HTMLCanvasElement | ImageBitmap | OffscreenCanvas>(\n        this.options?.advancedStyle?.params?.pattern?.image\n      ),\n      this.options?.advancedStyle?.params?.pattern?.repetition || '',\n    )\n  }\n\n  private setText(ctx: CanvasRenderingContext2D, params: { text: string; x: number; y: number; maxWidth?: number }) {\n    let methodName: 'fillText' | 'strokeText' = 'fillText'\n    if (this.options.textType === 'stroke') {\n      methodName = 'strokeText'\n    }\n    ctx[methodName] && ctx[methodName](params.text, params.x, params.y, params.maxWidth)\n  }\n\n  private drawText(ctx: CanvasRenderingContext2D, resolve: Function) {\n    this.setText(ctx, {\n      text: this.options.content,\n      x: 0,\n      y: 0 - this.recommendOptions.textLine.yOffsetValue,\n      maxWidth: this.options.textRowMaxWidth || this.options.width,\n    })\n    resolve(ctx.canvas)\n  }\n\n  private drawImage(ctx: CanvasRenderingContext2D, resolve: Function) {\n    loadImage(<string>this.options.image).then(image => {\n      const { width: imageWidth, height: imageHeight } = this.getImageRect(image)\n      const imagePosition = this.getDrawImagePosition(imageWidth, imageHeight)\n      ctx.drawImage(image, imagePosition.x, imagePosition.y, imageWidth, imageHeight)\n      resolve(ctx.canvas)\n    })\n  }\n\n  private drawMultiLineText(ctx: CanvasRenderingContext2D, resolve: Function) {\n    // image.width = this.options.width\n    // image.height = this.options.height\n    // const element = createCustomContentSvg(context, this.options)\n    // image.src = convertSVGToImage(element)\n    // image.onload = () => {\n    //   context.translate(this.options.width / 2, this.options.height / 2)\n    //   context.rotate(this.options.rotate)\n    //   context.drawImage(\n    //     image,\n    //     -this.options.width / 2,\n    //     -this.options.height / 2,\n    //     context.canvas.width,\n    //     context.canvas.height\n    //   )\n    //   resolve(canvas)\n    // }\n    const lines = this.recommendOptions.textLine.data\n    const yOffsetValue = this.recommendOptions.textLine.yOffsetValue\n    lines.forEach((text, index) => {\n      this.setText(ctx, {\n        text,\n        x: 0,\n        y: this.options.lineHeight * index - yOffsetValue,\n        maxWidth: this.options.textRowMaxWidth || this.options.width,\n      })\n    })\n    resolve(ctx.canvas)\n  }\n\n  private async drawRichText(ctx: CanvasRenderingContext2D, resolve: Function) {\n    const obj = await createCustomContentSVG(ctx, this.options)\n    loadImage(convertSVGToImage(obj.element), obj.width, obj.height).then(image => {\n      const imagePosition = this.getDrawImagePosition(image.width, image.height)\n      ctx.drawImage(image, imagePosition.x, imagePosition.y, image.width, image.height)\n      resolve(ctx.canvas)\n    })\n  }\n\n  private getImageRect(image: HTMLImageElement) {\n    const rect = { width: this.options.imageWidth || 0, height: this.options.imageHeight || 0 }\n    switch (true) {\n      case rect.width !== 0 && rect.height === 0:\n        rect.height = (rect.width * image.height) / image.width\n        break\n      case rect.width === 0 && rect.height !== 0:\n        rect.width = (rect.height * image.width) / image.height\n        break\n      case rect.width === 0 && rect.height === 0:\n        rect.width = image.width\n        rect.height = image.height\n        break\n    }\n    return rect\n  }\n\n  private getDrawImagePosition(imageWidth: number, imageHeight: number) {\n    const result = {\n      x: -imageWidth / 2,\n      y: -imageHeight / 2,\n    }\n    switch (this.options.translatePlacement) {\n      case 'top':\n        result.x = -imageWidth / 2\n        result.y = 0\n        break\n      case 'top-start':\n        result.x = 0\n        result.y = 0\n        break\n      case 'top-end':\n        result.x = -imageWidth\n        result.y = 0\n        break\n      case 'bottom':\n        result.x = -imageWidth / 2\n        result.y = -imageHeight\n        break\n      case 'bottom-start':\n        result.x = 0\n        result.y = -imageHeight\n        break\n      case 'bottom-end':\n        result.x = -imageWidth\n        result.y = -imageHeight\n        break\n      case 'left':\n        result.x = 0\n        result.y = -imageHeight / 2\n        break\n      case 'right':\n        result.x = -imageWidth\n        result.y = -imageHeight / 2\n        break\n    }\n    !isUndefined(this.props?.translateX) && (result.x = 0)\n    !isUndefined(this.props?.translateY) && (result.y = 0)\n    return result\n  }\n}\n\nexport { WatermarkCanvas }\n", "import type { WatermarkOptions } from '../../types'\nimport { Matrix } from '../../types'\nimport { generateMatrix } from '../../utils'\nimport { WatermarkCanvas } from '../canvas'\n\nclass GridLayout {\n  private readonly options: WatermarkOptions\n  private readonly partialCanvas: HTMLCanvasElement\n  private readonly partialWidth: number\n  private readonly partialHeight: number\n  private readonly cols: number\n  private readonly rows: number\n  private readonly matrix: Matrix<number>\n  private readonly gap: [number, number]\n\n  constructor(args: WatermarkOptions, partialCanvas: HTMLCanvasElement) {\n    this.options = args\n    this.partialWidth = this.options.width\n    this.partialHeight = this.options.height\n    this.rows = this.options.gridLayoutOptions?.rows || 1\n    this.cols = this.options.gridLayoutOptions?.cols || 1\n    this.matrix = this.options.gridLayoutOptions?.matrix || generateMatrix(this.rows, this.cols, 1)\n    this.gap = this.options.gridLayoutOptions?.gap || [0, 0]\n    this.partialCanvas = partialCanvas\n  }\n\n  draw(): HTMLCanvasElement {\n    const layoutCanvas = WatermarkCanvas.createCanvas(\n      this.options.gridLayoutOptions?.width || this.partialWidth * this.cols + this.gap[0] * this.cols,\n      this.options.gridLayoutOptions?.height || this.partialHeight * this.rows + this.gap[1] * this.rows,\n    )\n    const layoutContext = layoutCanvas.getContext('2d')\n    if (this.options.gridLayoutOptions?.backgroundImage) {\n      layoutContext?.drawImage(\n        this.options.gridLayoutOptions?.backgroundImage,\n        0,\n        0,\n        <number>this.options.gridLayoutOptions?.width,\n        <number>this.options.gridLayoutOptions?.height,\n      )\n    }\n    for (let rowIndex = 0; rowIndex < this.rows; rowIndex++) {\n      for (let colIndex = 0; colIndex < this.cols; colIndex++) {\n        if (!this.matrix?.[rowIndex]?.[colIndex]) {\n          continue\n        }\n        layoutContext?.drawImage(\n          this.partialCanvas,\n          this.partialWidth * colIndex + this.gap[0] * colIndex,\n          this.partialHeight * rowIndex + this.gap[1] * rowIndex,\n          this.partialWidth,\n          this.partialHeight,\n        )\n      }\n    }\n    return layoutCanvas\n  }\n}\n\nexport { GridLayout }\n", "import { GridLayout } from './grid'\nimport type { WatermarkOptions } from '../../types'\n\nconst renderLayout = (options: WatermarkOptions, partialCanvas: HTMLCanvasElement) => {\n  switch (options.layout) {\n    case 'grid':\n      return new GridLayout(options, partialCanvas).draw()\n    default:\n      return partialCanvas\n  }\n}\n\nconst generateBackgroundSize = (options: WatermarkOptions) => {\n  switch (options.layout) {\n    case 'grid': {\n      const cols = options.gridLayoutOptions?.cols || 1\n      const rows = options.gridLayoutOptions?.rows || 1\n      const gap = options.gridLayoutOptions?.gap || [0, 0]\n      return [options.width * cols + gap[0] * cols, options.height * rows + gap[1] * rows]\n    }\n    default:\n      return [options.width, options.height]\n  }\n}\n\nexport { renderLayout, generateBackgroundSize }\n", "import { convertImage, generateAnimationStyle, isUndefined } from '../utils'\nimport type { ChangeOptionsMode, WatermarkDom, WatermarkOptions } from '../types'\nimport { initialOptions } from '../utils/initialization'\nimport protection from '../utils/protection'\nimport { generateBackgroundSize, renderLayout } from './layout'\nimport { WatermarkCanvas } from './canvas'\n\n/**\n * Watermark class\n */\nclass Watermark {\n  protected options: WatermarkOptions\n  private parentElement: Element = document.body\n  private observer?: MutationObserver\n  private parentObserve?: MutationObserver\n  private watermarkDom?: WatermarkDom\n  private props?: Partial<WatermarkOptions>\n  private layoutCanvas?: HTMLCanvasElement\n  private checkWatermarkElementRequestID?: number\n  private watermarkCanvas?: WatermarkCanvas\n  private isCreating: Boolean = false\n\n  /**\n   * Watermark constructor\n   * @param args - watermark args\n   */\n  constructor(args: Partial<WatermarkOptions> = {}) {\n    this.props = args\n    this.options = {\n      ...initialOptions,\n      ...args,\n    }\n    this.changeParentElement(this.options.parent)\n    this.watermarkCanvas = new WatermarkCanvas(this.props, this.options)\n    protection(this.options.monitorProtection)\n  }\n\n  /**\n   * Change watermark options\n   * @param args\n   * @param mode\n   * @param redraw\n   */\n  async changeOptions(\n    args: Partial<WatermarkOptions> = {},\n    mode: ChangeOptionsMode = 'overwrite',\n    redraw: boolean = true,\n  ) {\n    this.initConfigData(args, mode)\n    protection(this.options.monitorProtection)\n    if (redraw) {\n      this.remove()\n      await this.create()\n    }\n  }\n\n  /**\n   * Creating a watermark.\n   */\n  async create() {\n    if (this.isCreating) {\n      return\n    }\n    this.isCreating = true\n    if (!this.validateUnique()) {\n      this.isCreating = false\n      return\n    }\n\n    if (!this.validateContent()) {\n      this.isCreating = false\n      return\n    }\n    const firstDraw = isUndefined(this.watermarkDom)\n\n    await this.watermarkCanvas?.draw()\n    this.layoutCanvas = renderLayout(this.options, <HTMLCanvasElement>this.watermarkCanvas?.getCanvas())\n    const image = convertImage(this.layoutCanvas)\n    this.watermarkCanvas?.clear()\n    this.watermarkDom = document.createElement('div')\n    const watermarkInnerDom = document.createElement('div')\n    this.watermarkDom.__WATERMARK__ = 'watermark'\n    this.watermarkDom.__WATERMARK__INSTANCE__ = this\n    const parentElementType = this.checkParentElementType()\n    this.watermarkDom.style.cssText = `\n      z-index:${this.options.zIndex}!important;display:block!important;visibility:visible!important;transform:none!important;scale:none!important;\n      ${parentElementType === 'custom' ? 'top:0!important;bottom:0!important;left:0!important;right:0!important;height:100%!important;pointer-events:none!important;position:absolute!important;' : 'position:relative!important;'}\n    `\n    const backgroundSize = generateBackgroundSize(this.options)\n    watermarkInnerDom.style.cssText = `\n      display:block!important;visibility:visible!important;pointer-events:none;top:0;bottom:0;left:0;right:0;transform:none!important;scale:none!important;\n      position:${parentElementType === 'root' ? 'fixed' : 'absolute'}!important;-webkit-print-color-adjust:exact!important;width:100%!important;height:100%!important;\n      z-index:${this.options.zIndex}!important;background-image:url(${image})!important;background-repeat:${this.options.backgroundRepeat}!important;\n      background-size:${backgroundSize[0]}px ${backgroundSize[1]}px!important;background-position:${this.options.backgroundPosition};\n      ${generateAnimationStyle(this.options.movable, this.options.backgroundRepeat)}\n    `\n    this.watermarkDom.appendChild(watermarkInnerDom)\n    this.parentElement.appendChild(this.watermarkDom)\n\n    if (this.options.mutationObserve) {\n      try {\n        this.bindMutationObserve()\n      } catch {\n        this.options.onObserveError?.()\n      }\n    }\n    firstDraw && this.options.onSuccess?.()\n    this.isCreating = false\n  }\n\n  /**\n   * Delete this watermark.\n   */\n  destroy() {\n    this.remove()\n    this.watermarkDom = undefined\n  }\n\n  async check() {\n    return this.parentElement.contains(<Node>this.watermarkDom)\n  }\n\n  protected remove() {\n    this.options.onBeforeDestroy?.()\n    this.observer?.disconnect()\n    this.parentObserve?.disconnect()\n    this.unbindCheckWatermarkElementEvent()\n    this.watermarkDom?.parentNode?.removeChild(this.watermarkDom)\n    this.options.onDestroyed?.()\n  }\n\n  protected initConfigData(args: Partial<WatermarkOptions>, mode: ChangeOptionsMode = 'overwrite') {\n    if (mode === 'append') {\n      Object.keys(args).forEach(key => {\n        this.props && (this.props[key as keyof WatermarkOptions] = <never>args[key as keyof WatermarkOptions])\n      })\n    } else {\n      this.props = args\n    }\n    this.options = {\n      ...initialOptions,\n      ...this.props,\n    }\n    this.changeParentElement(this.options.parent)\n    this.watermarkCanvas = new WatermarkCanvas(<Partial<WatermarkOptions>>this.props, this.options)\n  }\n\n  private changeParentElement(parent: Element | string) {\n    if (typeof parent === 'string') {\n      const parentElement = document.querySelector(parent)\n      parentElement && (this.parentElement = parentElement)\n    } else {\n      this.parentElement = parent\n    }\n\n    if (!this.parentElement) {\n      console.error('[WatermarkJsPlus]: please pass a valid parent element.')\n    }\n  }\n\n  private validateUnique(): boolean {\n    let result = true\n\n    Array.from(this.parentElement.childNodes).forEach(node => {\n      if (!result) {\n        return\n      }\n      if (Object.hasOwnProperty.call(node, '__WATERMARK__')) {\n        result = false\n        // throw new Error('duplicate watermark error')\n      }\n    })\n    return result\n  }\n\n  private validateContent(): boolean {\n    switch (this.options.contentType) {\n      case 'image':\n        return Object.hasOwnProperty.call(this.options, 'image')\n      case 'multi-line-text':\n      case 'rich-text':\n      case 'text':\n        return this.options.content.length > 0\n    }\n  }\n\n  private checkParentElementType() {\n    if (['html', 'body'].includes(this.parentElement.tagName.toLocaleLowerCase())) {\n      return 'root'\n    }\n    return 'custom'\n  }\n\n  private async checkWatermarkElement() {\n    if (!this.parentElement.contains(<Node>this.watermarkDom)) {\n      this.remove()\n      await this.create()\n    }\n    this.bindCheckWatermarkElementEvent()\n  }\n\n  private bindMutationObserve(): void {\n    if (!this.watermarkDom) {\n      return\n    }\n    this.bindCheckWatermarkElementEvent()\n    this.observer = new MutationObserver(async (mutationsList: MutationRecord[]) => {\n      if (mutationsList.length > 0) {\n        this.remove()\n        await this.create()\n      }\n    })\n    this.observer.observe(this.watermarkDom, {\n      attributes: true, // 属性的变动\n      childList: true, // 子节点的变动（指新增，删除或者更改）\n      subtree: true, // 布尔值，表示是否将该观察器应用于该节点的所有后代节点。\n      characterData: true, // 节点内容或节点文本的变动。\n    })\n    this.parentObserve = new MutationObserver(async (mutationsList: MutationRecord[]) => {\n      for (const item of mutationsList) {\n        if (\n          item?.target === this.watermarkDom ||\n          item?.removedNodes?.[0] === this.watermarkDom ||\n          (item.type === 'childList' &&\n            item.target === this.parentElement &&\n            item.target.lastChild !== this.watermarkDom)\n        ) {\n          this.remove()\n          await this.create()\n        }\n      }\n    })\n    this.parentObserve.observe(this.parentElement, {\n      attributes: true, // 属性的变动\n      childList: true, // 子节点的变动（指新增，删除或者更改）\n      subtree: true, // 布尔值，表示是否将该观察器应用于该节点的所有后代节点。\n      characterData: true, // 节点内容或节点文本的变动。\n    })\n  }\n\n  private bindCheckWatermarkElementEvent(): void {\n    this.unbindCheckWatermarkElementEvent()\n    this.checkWatermarkElementRequestID = requestAnimationFrame(this.checkWatermarkElement.bind(this))\n  }\n\n  private unbindCheckWatermarkElementEvent(): void {\n    if (!isUndefined(this.checkWatermarkElementRequestID)) {\n      cancelAnimationFrame(<number>this.checkWatermarkElementRequestID)\n    }\n  }\n}\n\nexport { Watermark }\n", "import type { ChangeOptionsMode, DecodeBlindWatermarkOptions, WatermarkOptions } from '../types'\nimport { convertImage, isFunction } from '../utils'\nimport { Watermark } from './watermark'\nimport { WatermarkCanvas } from './canvas'\nimport protection from '../utils/protection'\n\n/**\n * BlindWatermark class\n */\nclass BlindWatermark extends Watermark {\n  /**\n   * BlindWatermark constructor\n   * @param props - blind watermark options\n   */\n  constructor(props: Partial<WatermarkOptions> = {}) {\n    const defaultProps: Partial<WatermarkOptions> = {\n      globalAlpha: 0.005,\n      mode: 'blind',\n    }\n    super({ ...props, ...defaultProps })\n  }\n\n  async changeOptions(\n    args: Partial<WatermarkOptions> = {},\n    mode: ChangeOptionsMode = 'overwrite',\n    redraw: boolean = true,\n  ) {\n    args.globalAlpha = 0.005\n    args.mode = 'blind'\n    this.initConfigData(args, mode)\n    protection(this.options.monitorProtection)\n    if (redraw) {\n      this.remove()\n      await this.create()\n    }\n  }\n\n  /**\n   * Decode blind watermark.\n   * @param props - decode options\n   */\n  static decode(props: Partial<DecodeBlindWatermarkOptions>) {\n    const {\n      url = '',\n      fillColor = '#000',\n      compositeOperation = 'color-burn',\n      mode = 'canvas',\n      compositeTimes = 3,\n      onSuccess,\n    } = props\n    if (!url) {\n      return\n    }\n    if (mode === 'canvas') {\n      const img = new Image()\n      img.src = url\n      img.addEventListener('load', () => {\n        const { width, height } = img\n        const canvas = WatermarkCanvas.createCanvas(width, height)\n        const ctx = canvas.getContext('2d')\n        if (!ctx) {\n          throw new Error('get context error')\n        }\n        ctx.drawImage(img, 0, 0, width, height)\n        ctx.globalCompositeOperation = compositeOperation as any\n        ctx.fillStyle = fillColor\n        for (let i = 0; i < compositeTimes; i++) {\n          ctx.fillRect(0, 0, width, height)\n        }\n        const resultImage = convertImage(canvas)\n        if (isFunction(<Function>onSuccess)) {\n          onSuccess?.(resultImage)\n        }\n      })\n    }\n  }\n}\n\nexport { BlindWatermark }\n", "import type { ImageWatermarkOptions, WatermarkOptions } from '../types'\nimport { WatermarkCanvas } from './canvas'\nimport { initialOptions } from '../utils/initialization'\nimport { renderLayout } from './layout'\nimport { convertImage } from '../utils'\n\n/**\n * ImageWatermark class\n */\nclass ImageWatermark {\n  private readonly options: {} & WatermarkOptions & Partial<ImageWatermarkOptions>\n  private readonly props?: Partial<ImageWatermarkOptions>\n  private watermarkCanvas?: WatermarkCanvas\n  private layoutCanvas?: HTMLCanvasElement\n  private readonly originalSrc?: string\n  private readonly backgroundImage?: HTMLImageElement\n  private drew = false\n\n  /**\n   * ImageWatermark constructor\n   * @param args - image watermark args\n   */\n  constructor(args: Partial<ImageWatermarkOptions> = {}) {\n    this.props = args\n    this.options = {\n      ...initialOptions,\n      ...args,\n    }\n    if (this.props.crossOrigin) {\n      this.props.dom?.setAttribute('crossOrigin', 'anonymous')\n    }\n    this.watermarkCanvas = new WatermarkCanvas(this.props, this.options)\n    this.originalSrc = this.props.dom?.src\n    this.backgroundImage = this.getBackgroundImage()\n  }\n\n  async create() {\n    if (this.drew) {\n      return\n    }\n    await this.watermarkCanvas?.draw()\n    this.options.layout = 'grid'\n    this.options.gridLayoutOptions = {\n      ...this.options.gridLayoutOptions,\n      width: this.backgroundImage?.width,\n      height: this.backgroundImage?.height,\n      backgroundImage: this.backgroundImage,\n    }\n    this.layoutCanvas = renderLayout(this.options, <HTMLCanvasElement>this.watermarkCanvas?.getCanvas())\n    this.options.dom!.src = convertImage(this.layoutCanvas)\n    this.watermarkCanvas?.clear()\n    this.drew = true\n  }\n\n  destroy() {\n    this.options.dom!.src = <string>this.originalSrc\n    this.drew = false\n  }\n\n  private getBackgroundImage() {\n    if (this.options.dom) {\n      return this.options.dom\n    }\n  }\n}\n\nexport { ImageWatermark }\n"], "mappings": ";;;AAAA,SAAS,YAAY,KAAK,KAAK;AAC7B,MAAK,QAAQ,OAAS,OAAM,CAAA;AAC5B,MAAI,WAAW,IAAI;AAEnB,MAAY,OAAO,aAAa,aAAa;AAAE;EAAO;AAEtD,MAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,MAAI,QAAQ,SAAS,cAAc,OAAO;AAC1C,QAAM,OAAO;AAEb,MAAI,aAAa,OAAO;AACtB,QAAI,KAAK,YAAY;AACnB,WAAK,aAAa,OAAO,KAAK,UAAU;IAC9C,OAAW;AACL,WAAK,YAAY,KAAK;IAC5B;EACA,OAAS;AACL,SAAK,YAAY,KAAK;EAC1B;AAEE,MAAI,MAAM,YAAY;AACpB,UAAM,WAAW,UAAU;EAC/B,OAAS;AACL,UAAM,YAAY,SAAS,eAAe,GAAG,CAAC;EAClD;AACA;;;ACTA,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAA,EAAE,aAAc,SAAS,SAAUA,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;EAAE,KACzE,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;EAAE;AACnG,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEO,SAAS,UAAU,GAAG,GAAG;AAC5B,MAAI,OAAO,MAAM,cAAc,MAAM;AACjC,UAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;EAAE;AACrC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAE;AACrF;AAEO,IAAI,WAAW,WAAW;AAC7B,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;IACvF;AACQ,WAAO;EACf;AACI,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AA0EO,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AACzD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;IAAE,CAAE;EAAE;AAC1G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;MAAE,SAAU,GAAG;AAAE,eAAO,CAAC;MAAE;IAAE;AACzF,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;MAAE,SAAU,GAAG;AAAE,eAAO,CAAC;MAAE;IAAE;AAC5F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;IAAE;AAC5G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAA,CAAE,GAAG,KAAI,CAAE;EAC5E,CAAK;AACL;AAEO,SAAS,YAAY,SAAS,MAAM;AACvC,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;EAAE,GAAI,MAAM,CAAA,GAAI,KAAK,CAAA,EAAE,GAAI,GAAG,GAAG,GAAG,IAAI,OAAO,QAAQ,OAAO,aAAa,aAAa,WAAW,QAAQ,SAAS;AAC/L,SAAO,EAAE,OAAO,KAAK,CAAC,GAAG,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE,QAAQ,IAAI,KAAK,CAAC,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;EAAK,IAAK;AAC1J,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;IAAE;EAAG;AAChE,WAAS,KAAK,IAAI;AACd,QAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK,EAAG,KAAI;AAC1C,UAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,UAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,cAAQ,GAAG,CAAC,GAAC;QACT,KAAK;QAAG,KAAK;AAAG,cAAI;AAAI;QACxB,KAAK;AAAG,YAAE;AAAS,iBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAK;QACrD,KAAK;AAAG,YAAE;AAAS,cAAI,GAAG,CAAC;AAAG,eAAK,CAAC,CAAC;AAAG;QACxC,KAAK;AAAG,eAAK,EAAE,IAAI,IAAG;AAAI,YAAE,KAAK,IAAG;AAAI;QACxC;AACI,cAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,gBAAI;AAAG;UAAS;AAC1G,cAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,cAAE,QAAQ,GAAG,CAAC;AAAG;UAAM;AACpF,cAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,gBAAI;AAAI;UAAM;AACnE,cAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,cAAE,IAAI,KAAK,EAAE;AAAG;UAAM;AACjE,cAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAG;AACnB,YAAE,KAAK,IAAG;AAAI;MAClC;AACY,WAAK,KAAK,KAAK,SAAS,CAAC;IACrC,SAAiB,GAAG;AAAE,WAAK,CAAC,GAAG,CAAC;AAAG,UAAI;IAAE,UAAE;AAAW,UAAI,IAAI;IAAE;AACxD,QAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAI;EACtF;AACA;ACpJO,IAAM,eAAe,SAAC,QAAyB;AACpD,SAAO,OAAO,UAAU,aAAa,CAAC;AACxC;AAEO,IAAM,aAAa,SAAC,OAAe;AACxC,SAAO,OAAO,UAAU;AAC1B;AAEO,IAAM,cAAc,SAAC,OAAU;AACpC,SAAO,UAAU;AACnB;AAEO,IAAM,WAAW,SAAC,OAAU;AACjC,SAAO,OAAO,UAAU;AAC1B;AAEO,IAAM,mBAAmB,SAC9B,SACA,OACA,cAA2C;AAD3C,MAAA,UAAA,QAAA;AAAA,YAAqC,CAAA;EAAA;AACrC,MAAA,iBAAA,QAAA;AAAA,mBAA2C;EAAA;AAE3C,MAAM,UAAU,SAAS,gBAAgB,cAAc,OAAO;AAC9D,WAAW,QAAQ,OAAO;AACxB,YAAQ,aAAa,MAAM,MAAM,IAAI,CAAC;;AAExC,SAAO;AACT;AAEO,IAAM,mBAAmB,SAAC,KAA+B,MAAc,UAAgB;AAC5F,MAAM,SAAS,CAAA;AACf,MAAI,MAAM;AACV,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC/C,WAAO,KAAK,OAAO,CAAC;AACpB,QAAI,SAAS,MAAM;AACjB,aAAO,KAAK,GAAG;AACf,YAAM;AACN;;AAEF,WAAO;AACP,QAAI,IAAI,YAAY,GAAG,EAAE,QAAQ,UAAU;AACzC,aAAO,KAAK,IAAI,UAAU,GAAG,IAAI,SAAS,CAAC,CAAC;AAC5C,YAAM;AACN;;;AAGJ,SAAO,KAAK,GAAG;AACf,SAAO;AACT;AAEO,IAAM,yBAAyB,SACpC,KACA,SAAyB;AAAA,SAAA,UAAA,QAAA,QAAA,QAAA,WAAA;;;;;;AAEnB,uBAAa,iBAAiB,OAAO;YACzC,OAAO;UACR,CAAA;AACK,wBAAc,SAAS,cAAc,KAAK;AAChD,sBAAY,aAAa,SAAS,8BAA8B;AAChE,sBAAY,MAAM,UAAU,2IAOpB,OAAA,IAAI,MACH,cAAA,EAAA,OAAA,QAAQ,WAAS,KAAA;AAE1B,sBAAY,YAAY,kCAAA,OAAkC,QAAQ,SAAO,QAAA;AACzE,mBAAS,KAAK,YAAY,WAAW;AAErC,iBAAA,CAAA,GAAM,mBAAmB,WAAW,CAAC;;AAArC,aAAA,KAAA;AACM,kBAAO,KAAA,YAAY,cAAc,oBAAoB,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,sBAAqB;AAC7E,sBAAY,SAAA,QAAA,SAAI,SAAA,SAAJ,KAAM;AAClB,uBAAa,SAAA,QAAA,SAAI,SAAA,SAAJ,KAAM;AACzB,mBAAS,KAAK,YAAY,WAAW;AAC/B,kBAAQ,QAAQ,iBAAiB,aAAa,QAAQ;AACtD,mBAAS,QAAQ,kBAAkB,cAAc,QAAQ;AAC/D,qBAAW,aAAa,SAAS,MAAM,SAAQ,CAAE;AACjD,qBAAW,aAAa,UAAU,OAAO,SAAQ,CAAE;AAC7C,iCAAuB,iBAAiB,iBAAiB;YAC7D,OAAO,MAAM,SAAQ;YACrB,QAAQ,OAAO,SAAQ;UACxB,CAAA;AACD,+BAAqB,YAAY,WAAW;AAC5C,qBAAW,YAAY,oBAAoB;AAC3C,iBAAO,CAAA,GAAA;YACL,SAAS;YACT;YACA;WACD;;;;;AAGH,SAAe,mBAAmB,aAAwB;;;;;;AAClD,wBAAc,YAAY,iBAAiB,KAAK;6BAE3CC,MAAG;;;;;AACN,wBAAMA,KAAI,aAAa,KAAK;AAC9B,sBAAA,CAAA,IAAA,QAAG,CAAA,GAAA,CAAA;;;;AAEc,yBAAA,CAAA,GAAM,MAAM,GAAG,CAAC;;AAA3B,6BAAW,GAAgB,KAAA;AACpB,yBAAA,CAAA,GAAM,SAAS,KAAI,CAAE;;AAA5B,2BAAO,GAAqB,KAAA;AAClB,yBAAA,CAAA,GAAM,IAAI,QAAQ,SAAC,SAAS,QAAM;AAChD,wBAAM,SAAS,IAAI,WAAU;AAC7B,2BAAO,YAAY,WAAA;AAAM,6BAAA,QAAQ,OAAO,MAAM;oBAAC;AAC/C,2BAAO,UAAU;AACjB,2BAAO,cAAc,MAAI;kBAC3B,CAAC,CAAC;;AALI,4BAAU,GAKd,KAAA;AACF,sBAAI,SAAS,OAAO,GAAG;AACrB,oBAAAA,KAAI,aAAa,OAAO,OAAiB;;;;;AAG3C,0BAAQ,MAAM,oBAAA,OAAoB,KAAgB,aAAA,GAAE,OAAK;;;;;;;;;;AAhBtB,eAAA,GAAvB,KAAA,MAAM,KAAK,WAAW;;;AAAtB,cAAA,EAAA,KAAA,GAAA,QAAuB,QAAA,CAAA,GAAA,CAAA;AAA9B,gBAAG,GAAA,EAAA;6BAAH,GAAG,CAAA;;;;;AAAI;;;;;;;;;;AAoBnB;AAEM,IAAM,oBAAoB,SAAC,KAAY;AAC5C,MAAM,cAAc,IAAI,UACrB,QAAQ,mCAAmC,SAAS,EACpD,QAAQ,OAAO,EAAE,EACjB,QAAQ,OAAO,EAAE,EACjB,QAAQ,MAAM,KAAK;AACtB,SAAO,oCAAA,OAAoC,WAAW;AACxD;AAEO,IAAM,WAAW,SAAC,IAAS,IAAO;AACvC,MAAI,YAAY,EAAE,GAAG;AACnB,WAAO;SACF;AACL,WAAO;;AAEX;AAEO,IAAM,YAAY,SACvB,KACA,OACA,QAAsC;AADtC,MAAA,UAAA,QAAA;AAAA,YAAqC;EAAA;AACrC,MAAA,WAAA,QAAA;AAAA,aAAsC;EAAA;AAEtC,MAAM,QAAQ,IAAI,MAAK;AACvB,QAAM,aAAa,eAAe,WAAW;AAC7C,GAAC,YAAY,KAAK,MAAM,MAAM,QAAgB;AAC9C,GAAC,YAAY,MAAM,MAAM,MAAM,SAAiB;AAChD,QAAM,MAAM;AACZ,SAAO,IAAI,QAAQ,SAAA,SAAO;AACxB,UAAM,SAAS,WAAA;AACb,cAAQ,KAAK;IACf;EACF,CAAC;AACH;AAEO,IAAM,iBAAiB,SAAC,MAAc,SAAiB,OAAU;AACtE,SAAO,MAAM,KAAK,EAAE,QAAQ,KAAI,GAAI,WAAM;AAAA,WAAA,IAAI,MAAM,OAAO,EAAE,KAAK,KAAK;EAAC,CAAA;AAC1E;AAEO,IAAM,yBAAyB,SAAC,SAAkB,kBAAwB;AAC/E,MAAI,CAAC,SAAS;AACZ,WAAO;;AAET,MAAM,qBAAqB,KAAK,OAAM,KAAM,IAAI,KAAK;AACrD,MAAM,mBAAmB,KAAK,OAAM,KAAM,IAAI,KAAK;AACnD,UAAQ,kBAAgB;IACtB,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO,cAAA,OAAc,oBAAkB,iEAAA;IACzC,KAAK;AACH,aAAO,cAAA,OAAc,kBAAgB,oEAAA;IACvC,KAAK;AACH,aAAO,cAAc,OAAA,oBAA2E,wDAAA,EAAA,OAAA,kBAAgB,iEAAA;IAClH;AACE,aAAO;;AAEb;AC9KO,IAAM,iBAAmC;EAC9C,OAAO;EACP,QAAQ;EACR,QAAQ;EACR,QAAQ;EACR,eAAe;EACf,oBAAoB;EACpB,aAAa;EACb,SAAS;EACT,UAAU;EACV,YAAY;EACZ,aAAa;EACb,YAAY;EACZ,QAAQ;EACR,oBAAoB;EACpB,kBAAkB;EAClB,UAAU;EACV,YAAY;EACZ,WAAW;EACX,aAAa;EACb,WAAW;EACX,YAAY;EACZ,QAAQ;EACR,eAAe;EACf,aAAa;EACb,aAAa;EACb,MAAM;EACN,iBAAiB;EACjB,mBAAmB;EACnB,SAAS;EACT,QAAQ;EACR,WAAW,WAAA;EAAA;EACX,iBAAiB,WAAA;EAAA;EACjB,aAAa,WAAA;EAAA;EACb,gBAAgB,WAAA;EAAA;;AAGX,IAAM,2BAA2B,SACtC,QACA,SACA,MAA+B;AAE/B,MAAM,MAAM,OAAO,WAAW,IAAI;AAClC,MAAI,QAAQ,MAAM;AAChB,UAAM,IAAI,MAAM,mBAAmB;;AAErC,MAAI,OAAO,GAAG,OAAA,QAAQ,WAAS,GAAA,EAAA,OAAI,QAAQ,aAAW,GAAA,EAAA,OAAI,QAAQ,YAAc,GAAA,EAAA,OAAA,QAAQ,UAAQ,GAAA,EAAA,OAAI,QAAQ,UAAU;AACtH,MAAI,SAAS,QAAQ;AAErB,MAAI,gBAAgB,QAAQ;AAC5B,MAAI,cAAc,QAAQ;AAC1B,MAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,QAAQ;AACnB,YAAQ,UAAU,MAAO,QAAQ,SAAS,QAAS,KAAK,KAAK;;AAE/D,MAAI,YAAY,KAAK,eAAe,GAAG;AACrC,YAAQ,kBAAkB,QAAQ;;AAEpC,MAAM,SAAS;IACb,OAAO;MACL,MAAM;QACJ,OAAO,QAAQ;QACf,QAAQ,QAAQ;MACjB;MACD,UAAU;QACR,GAAG;QACH,GAAG;MACJ;IACF;IACD,UAAU;MACR,MAAM,CAAA;MACN,cAAc;IACf;IACD,qBAAqB;MACnB,QAAQ;QACN,IAAI;QACJ,IAAI;MACL;MACD,QAAQ;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;MACL;MACD,OAAO;QACL,GAAG;QACH,GAAG;QACH,YAAY;MACb;MACD,SAAS,CAAA;IACiB;;AAE9B,UAAQ,QAAQ,aAAW;IACzB,KAAK;AACH,aAAO,SAAS,OAAO,CAAC,QAAQ,OAAO;AACvC;IACF,KAAK;AACH,aAAO,SAAS,OAAO,iBAAiB,KAAK,QAAQ,SAAiB,QAAQ,eAAe;AAC7F;;AAMJ,MAAI,aAAqB,QAAQ,QAAQ;AACzC,MAAI,aAAqB,QAAQ,SAAS;AAC1C,MAAI,eAAiC;AACrC,MAAI,YAA2B;AAE/B,MAAI,CAAC,YAAY,SAAI,QAAJ,SAAA,SAAA,SAAA,KAAM,UAAU,KAAK,CAAC,YAAY,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,UAAU,GAAG;AACpE,iBAAqB,SAAA,QAAA,SAAI,SAAA,SAAJ,KAAM;AAC3B,iBAAqB,SAAA,QAAA,SAAI,SAAA,SAAJ,KAAM;AAC3B,mBAAe;AACf,gBAAY;SACP;AAML,WAAO,oBAAoB,OAAO,KAAK,CAAC,QAAQ,QAAQ;AACxD,WAAO,oBAAoB,OAAO,KAAK,QAAQ,QAAQ;AAGvD,WAAO,oBAAoB,OAAO,KAAK;AAGvC,WAAO,oBAAoB,OAAO,KAAK,QAAQ,QAAQ;;AAIzD,UAAQ,KAAK,oBAAkB;IAC7B,KAAK;AACH,mBAAa,QAAQ,QAAQ;AAC7B,mBAAa;AACb,qBAAe;AAEf,aAAO,oBAAoB,OAAO,KAAK,CAAC,QAAQ,QAAQ;AACxD,aAAO,oBAAoB,OAAO,KAAK,QAAQ,QAAQ;AAEvD,aAAO,oBAAoB,OAAO,KAAM,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAI5F,aAAO,oBAAoB,OAAO,KAAM,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAG5F,aAAO,oBAAoB,MAAM,IAAK,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAC1F;IACF,KAAK;AACH,mBAAa;AACb,mBAAa;AACb,qBAAe;AACf,kBAAY;AACZ,aAAO,oBAAoB,OAAO,KAAK;AACvC,aAAO,oBAAoB,OAAO,KAAK,QAAQ;AAC/C,aAAO,oBAAoB,OAAO,KAAK,QAAQ,QAAQ;AACvD,aAAO,oBAAoB,OAAO,KAAM,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAE5F,aAAO,oBAAoB,OAAO,KAAK,QAAQ,QAAQ;AACvD,aAAO,oBAAoB,OAAO,KAAM,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAE5F,aAAO,oBAAoB,MAAM,IAAI,QAAQ,QAAQ;AACrD,aAAO,oBAAoB,MAAM,IAAK,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAC1F;IACF,KAAK;AACH,mBAAa,QAAQ;AACrB,mBAAa;AACb,qBAAe;AACf,kBAAY;AACZ,aAAO,oBAAoB,OAAO,KAAK;AACvC,aAAO,oBAAoB,OAAO,KAAK,CAAC,QAAQ;AAChD,aAAO,oBAAoB,OAAO,KAAK,CAAC,QAAQ,QAAQ;AACxD,aAAO,oBAAoB,OAAO,KAAM,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAE5F,aAAO,oBAAoB,OAAO,KAAK,CAAC,QAAQ,QAAQ;AACxD,aAAO,oBAAoB,OAAO,KAAM,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAE5F,aAAO,oBAAoB,MAAM,IAAI,CAAC,QAAQ,QAAQ;AACtD,aAAO,oBAAoB,MAAM,IAAK,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAC1F;IACF,KAAK;AACH,mBAAa,QAAQ,QAAQ;AAC7B,mBAAa,QAAQ;AACrB,qBAAe;AAEf,aAAO,oBAAoB,OAAO,KAAK,CAAC,QAAQ,QAAQ;AACxD,aAAO,oBAAoB,OAAO,KAAK,QAAQ,QAAQ;AAEvD,aAAO,oBAAoB,OAAO,KAAM,CAAC,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAG7F,aAAO,oBAAoB,OAAO,KAAM,CAAC,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAE7F,aAAO,oBAAoB,MAAM,IAAI;AACrC,aAAO,oBAAoB,MAAM,IAAK,CAAC,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAC3F;IACF,KAAK;AACH,mBAAa;AACb,mBAAa,QAAQ;AACrB,qBAAe;AACf,kBAAY;AACZ,aAAO,oBAAoB,OAAO,KAAK;AACvC,aAAO,oBAAoB,OAAO,KAAK,QAAQ;AAC/C,aAAO,oBAAoB,OAAO,KAAK,QAAQ,QAAQ;AACvD,aAAO,oBAAoB,OAAO,KAAM,CAAC,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAE7F,aAAO,oBAAoB,OAAO,KAAK,QAAQ,QAAQ;AACvD,aAAO,oBAAoB,OAAO,KAAM,CAAC,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAE7F,aAAO,oBAAoB,MAAM,IAAI,QAAQ,QAAQ;AACrD,aAAO,oBAAoB,MAAM,IAAK,CAAC,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAC3F;IACF,KAAK;AACH,mBAAa,QAAQ;AACrB,mBAAa,QAAQ;AACrB,qBAAe;AACf,kBAAY;AACZ,aAAO,oBAAoB,OAAO,KAAK;AACvC,aAAO,oBAAoB,OAAO,KAAK,CAAC,QAAQ;AAChD,aAAO,oBAAoB,OAAO,KAAK,CAAC,QAAQ,QAAQ;AACxD,aAAO,oBAAoB,OAAO,KAAM,CAAC,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAE7F,aAAO,oBAAoB,OAAO,KAAK,CAAC,QAAQ,QAAQ;AACxD,aAAO,oBAAoB,OAAO,KAAM,CAAC,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAE7F,aAAO,oBAAoB,MAAM,IAAI,CAAC,QAAQ,QAAQ;AACtD,aAAO,oBAAoB,MAAM,IAAK,CAAC,OAAO,SAAS,KAAK,SAAS,QAAQ,aAAc;AAC3F;IACF,KAAK;AACH,mBAAa;AACb,mBAAa,QAAQ,SAAS;AAE9B,kBAAY;AACZ,aAAO,oBAAoB,OAAO,KAAK;AACvC,aAAO,oBAAoB,OAAO,KAAK,QAAQ;AAC/C,aAAO,oBAAoB,OAAO,KAAK,QAAQ,QAAQ;AAGvD,aAAO,oBAAoB,OAAO,KAAK,QAAQ,QAAQ;AAGvD,aAAO,oBAAoB,MAAM,IAAI,QAAQ,QAAQ;AACrD,aAAO,oBAAoB,MAAM,IAAI;AACrC;IACF,KAAK;AACH,mBAAa,QAAQ;AACrB,mBAAa,QAAQ,SAAS;AAE9B,kBAAY;AACZ,aAAO,oBAAoB,OAAO,KAAK;AACvC,aAAO,oBAAoB,OAAO,KAAK,CAAC,QAAQ;AAChD,aAAO,oBAAoB,OAAO,KAAK,CAAC,QAAQ,QAAQ;AAGxD,aAAO,oBAAoB,OAAO,KAAK,CAAC,QAAQ,QAAQ;AAGxD,aAAO,oBAAoB,MAAM,IAAI,CAAC,QAAQ,QAAQ;AACtD,aAAO,oBAAoB,MAAM,IAAI;AACrC;;AAEJ,UAAQ,aAAa;AACrB,UAAQ,aAAa;AACrB,cAAY,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,YAAY,MAAM,QAAQ,eAAe;AAC3D,cAAY,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,SAAS,MAAM,QAAQ,YAAY;AAErD,MAAI,CAAC,QAAQ,iBAAiB,EAAE,SAAS,QAAQ,WAAW,GAAG;AAC7D,YAAQ,QAAQ,cAAY;MAC1B,KAAK;AACH,eAAO,SAAS,gBAAiB,OAAO,SAAS,KAAK,SAAS,KAAK,QAAQ,aAAc;AAC1F;MACF,KAAK;MACL,KAAK;MACL,KAAK;AACH,eAAO,SAAS,gBACb,OAAO,SAAS,KAAK,SAAS,KAAK,QAAQ,cAAc,QAAQ,aAAa,SAAS,QAAQ,QAAQ,KAAK;AAC/G;MACF,KAAK;MACL,KAAK;AACH,eAAO,SAAS,eAAe,CAAC,QAAQ,aAAa,IAAI,SAAS,QAAQ,QAAQ,IAAI;AACtF;;;AAIN,SAAO;AACT;AClSA,IAAA,aAAe,SAAC,MAAa;AAC3B,MAAI,OAAO,WAAW,aAAa;AACjC;;AAEF,MAAI,MAAM;AACR,WAAO,eAAe,QAAQ,oBAAoB;MAChD,UAAU;MACV,cAAc;IACf,CAAA;AACD,WAAO,eAAe,QAAQ,yBAAyB;MACrD,UAAU;MACV,cAAc;IACf,CAAA;;AAEL;ACXA,IAAA;;EAAA,WAAA;AAME,aAAYC,iBAAA,MAAiC,SAAyB;AACpE,WAAK,QAAQ;AACb,WAAK,UAAU;AACf,WAAK,SAASA,iBAAgB,aAAa,KAAK,QAAQ,OAAO,KAAK,QAAQ,MAAM;AAClF,WAAK,mBAAmB,yBAAyB,KAAK,QAAQ,KAAK,SAAS,KAAK,KAAK;;AAQjF,IAAAA,iBAAA,eAAP,SAAoB,OAAe,QAAc;;AAC/C,UAAM,QAAQ,OAAO,oBAAoB;AACzC,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,aAAO,QAAQ,QAAQ;AACvB,aAAO,SAAS,SAAS;AACzB,aAAO,MAAM,QAAQ,GAAA,OAAG,OAAK,IAAA;AAC7B,aAAO,MAAM,SAAS,GAAA,OAAG,QAAM,IAAA;AAC/B,OAAA,KAAA,OAAO,WAAW,IAAI,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,aAAa,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAC9D,aAAO;;AAOF,IAAAA,iBAAW,cAAlB,SAAmB,QAAyB;AAC1C,UAAM,MAAM,OAAO,WAAW,IAAI;AAClC,UAAI,QAAQ,MAAM;AAChB,cAAM,IAAI,MAAM,mBAAmB;;AAErC,UAAI,QAAO;AACX,UAAI,eAAc;AAClB,UAAI,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC/C,UAAM,QAAQ,OAAO,oBAAoB;AACzC,UAAI,aAAa,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;;AAG3C,IAAAA,iBAAA,UAAA,YAAA,WAAA;AACE,aAAO,KAAK;;AAGd,IAAAA,iBAAA,UAAA,QAAA,WAAA;AACE,MAAAA,iBAAgB,YAAY,KAAK,MAAM;;AAGzC,IAAAA,iBAAA,UAAA,OAAA,WAAA;AAAA,UA0CC,QAAA;AAzCC,UAAM,MAAM,KAAK,OAAO,WAAW,IAAI;AACvC,UAAI,QAAQ,MAAM;AAChB,cAAM,IAAI,MAAM,mBAAmB;;AAGrC,UAAI,KAAK,QAAQ,eAAe;AAC9B,YAAI,UAAS;AACb,YAAI,KAAK,GAAG,GAAG,KAAK,QAAQ,OAAO,KAAK,QAAQ,MAAM;AACtD,YAAI,YAAY;AAChB,YAAI,cAAc;AAClB,YAAI,OAAM;AACV,YAAI,UAAS;AAEb,YAAI,UAAS;AACb,YAAI,KAAK,KAAK,QAAQ,YAAsB,KAAK,QAAQ,YAAsB,GAAG,CAAC;AACnF,YAAI,YAAY;AAChB,YAAI,cAAc;AAClB,YAAI,OAAM;AACV,YAAI,UAAS;;AAGf,WAAK,SAAS,GAAG;AACjB,UAAI,KAAI;AACR,UAAI,UAAU,KAAK,QAAQ,YAAsB,KAAK,QAAQ,UAAoB;AAClF,UAAI,OAAO,KAAK,QAAQ,MAAM;AAC9B,aAAO,IAAI,QAAQ,SAAA,SAAO;AACxB,gBAAQ,MAAK,QAAQ,aAAW;UAC9B,KAAK;AACH,kBAAK,SAAS,KAAK,OAAO;AAC1B;UACF,KAAK;AACH,kBAAK,UAAU,KAAK,OAAO;AAC3B;UACF,KAAK;AACH,kBAAK,kBAAkB,KAAK,OAAO;AACnC;UACF,KAAK;AACH,kBAAK,aAAa,KAAK,OAAO;AAC9B;;MAEN,CAAC;;AAGK,IAAAA,iBAAQ,UAAA,WAAhB,SAAiB,KAA6B;;AAC5C,UAAI,WAAwC;AAC5C,UAAI,KAAK,QAAQ,aAAa,UAAU;AACtC,mBAAW;;AAEb,UAAI,QAAwD,KAAK,QAAQ;AACzE,WAAI,KAAA,KAAK,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA,eAAe;AAC/B,gBAAQ,KAAK,QAAQ,cAAc,MAAI;UACrC,KAAK;AACH,oBAAQ,KAAK,qBAAqB,GAAG;AACrC;UACF,KAAK;AACH,oBAAQ,KAAK,qBAAqB,GAAG;AACrC;UACF,KAAK;AACH,oBAAQ,KAAK,oBAAoB,GAAG;AACpC;UACF,KAAK;AACH,oBAAQ,KAAK,cAAc,GAAG;AAC9B;;;AAGN,UAAI,QAAQ,KAAK,UAAU,IAAI,QAAQ,IAAI;AAE3C,WAAK,QAAQ,cAAc,IAAI,YAAY,KAAK,QAAQ;AACxD,WAAK,QAAQ,iBAAiB,IAAI,eAAe,KAAK,QAAQ;AAC9D,UAAI,cAAc,KAAK,QAAQ;AAC/B,UAAI,KAAK,QAAQ,aAAa;AAC5B,YAAI,aAAa,SAAS,KAAK,QAAQ,YAAY,YAAY,CAAC;AAChE,YAAI,cAAc,SAAS,KAAK,QAAQ,YAAY,aAAa,WAAW;AAC5E,YAAI,gBAAgB,SAAS,KAAK,QAAQ,YAAY,eAAe,CAAC;AACtE,YAAI,gBAAgB,SAAS,KAAK,QAAQ,YAAY,eAAe,CAAC;;AAExE,UAAI,WAAqB,KAAK,QAAQ,aAAa,GAAG;AACxC,aAAK,QAAQ,cAAe,GAAG;;;AAIvC,IAAAA,iBAAoB,UAAA,uBAA5B,SAA6B,KAA6B;;AACxD,UAAM,WAAW,IAAI,qBAEjB,UAAS,MAAA,MAAA,KAAA,KAAK,QAAQ,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,IAAI,KAAK,iBAAiB,oBAAoB,OAAO,EAAE,GAEtG,UAAS,MAAA,MAAA,KAAA,KAAK,QAAQ,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,IAAI,CAAC,GAEhE,UAAS,MAAA,MAAA,KAAA,KAAK,QAAQ,mBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,IAAI,KAAK,iBAAiB,oBAAoB,OAAO,EAAE,GAEtG,UAAS,MAAA,MAAA,KAAA,KAAK,QAAQ,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,IAAI,CAAC,CAAC;AAErE,OAAA,MAAA,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ,SAAA,MAAI;AACnD,iBAAS,aAAa,KAAK,QAAQ,KAAK,KAAK;MAC/C,CAAC;AACD,aAAO;;AAGD,IAAAA,iBAAmB,UAAA,sBAA3B,SAA4B,KAA6B;;AACvD,UAAM,WAAW,IAAI,oBACX,UAAS,MAAA,MAAA,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,YAAY,CAAC,GAExE,UAAS,MAAA,MAAA,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,mBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,GAAG,KAAK,iBAAiB,oBAAoB,MAAM,CAAC,GAGzG,UAAS,MAAA,MAAA,MAAA,KAAA,KAAK,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,GAAG,KAAK,iBAAiB,oBAAoB,MAAM,CAAC,CAAC;AAG9G,OAAA,MAAA,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ,SAAA,MAAI;AACnD,iBAAS,aAAa,KAAK,QAAQ,KAAK,KAAK;MAC/C,CAAC;AACD,aAAO;;AAGD,IAAAA,iBAAoB,UAAA,uBAA5B,SAA6B,KAA6B;;AACxD,UAAM,WAAW,IAAI,qBAEjB,UAAS,MAAA,MAAA,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,IAAI,KAAK,iBAAiB,oBAAoB,OAAO,EAAE,GAG7G,UAAS,MAAA,MAAA,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,IAAI,KAAK,iBAAiB,oBAAoB,OAAO,EAAE,GAG7G,UAAS,MAAA,MAAA,MAAA,KAAA,KAAK,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,IAAI,KAAK,iBAAiB,oBAAoB,OAAO,EAAE,GAG7G,UAAS,MAAA,MAAA,MAAA,KAAA,KAAK,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,IAAI,KAAK,iBAAiB,oBAAoB,OAAO,EAAE,GAG7G,UAAS,MAAA,MAAA,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,IAAI,KAAK,iBAAiB,oBAAoB,OAAO,EAAE,GAG7G,UAAS,MAAA,MAAA,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,mBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,IAAI,KAAK,iBAAiB,oBAAoB,OAAO,EAAE,CAAC;AAGlH,OAAA,MAAA,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ,SAAA,MAAI;AACnD,iBAAS,aAAa,KAAK,QAAQ,KAAK,KAAK;MAC/C,CAAC;AACD,aAAO;;AAGD,IAAAA,iBAAa,UAAA,gBAArB,SAAsB,KAA6B;;AACjD,aAAO,IAAI,eAEP,MAAA,MAAA,MAAA,KAAA,KAAK,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA,SAEhD,MAAA,MAAA,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,mBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,eAAc,EAAE;;AAI1D,IAAAA,iBAAA,UAAA,UAAR,SAAgB,KAA+B,QAAiE;AAC9G,UAAI,aAAwC;AAC5C,UAAI,KAAK,QAAQ,aAAa,UAAU;AACtC,qBAAa;;AAEf,UAAI,UAAU,KAAK,IAAI,UAAU,EAAE,OAAO,MAAM,OAAO,GAAG,OAAO,GAAG,OAAO,QAAQ;;AAG7E,IAAAA,iBAAA,UAAA,WAAR,SAAiB,KAA+B,SAAiB;AAC/D,WAAK,QAAQ,KAAK;QAChB,MAAM,KAAK,QAAQ;QACnB,GAAG;QACH,GAAG,IAAI,KAAK,iBAAiB,SAAS;QACtC,UAAU,KAAK,QAAQ,mBAAmB,KAAK,QAAQ;MACxD,CAAA;AACD,cAAQ,IAAI,MAAM;;AAGZ,IAAAA,iBAAA,UAAA,YAAR,SAAkB,KAA+B,SAAiB;AAAlE,UAOC,QAAA;AANC,gBAAkB,KAAK,QAAQ,KAAK,EAAE,KAAK,SAAA,OAAK;AACxC,YAAA,KAA6C,MAAK,aAAa,KAAK,GAA3D,aAAU,GAAA,OAAU,cAAW,GAAA;AAC9C,YAAM,gBAAgB,MAAK,qBAAqB,YAAY,WAAW;AACvE,YAAI,UAAU,OAAO,cAAc,GAAG,cAAc,GAAG,YAAY,WAAW;AAC9E,gBAAQ,IAAI,MAAM;MACpB,CAAC;;AAGK,IAAAA,iBAAA,UAAA,oBAAR,SAA0B,KAA+B,SAAiB;AAA1E,UA4BC,QAAA;AAXC,UAAM,QAAQ,KAAK,iBAAiB,SAAS;AAC7C,UAAM,eAAe,KAAK,iBAAiB,SAAS;AACpD,YAAM,QAAQ,SAAC,MAAM,OAAK;AACxB,cAAK,QAAQ,KAAK;UAChB;UACA,GAAG;UACH,GAAG,MAAK,QAAQ,aAAa,QAAQ;UACrC,UAAU,MAAK,QAAQ,mBAAmB,MAAK,QAAQ;QACxD,CAAA;MACH,CAAC;AACD,cAAQ,IAAI,MAAM;;AAGN,IAAAA,iBAAA,UAAA,eAAd,SAA2B,KAA+B,SAAiB;;;;;;;AAC7D,qBAAM,CAAA,GAAA,uBAAuB,KAAK,KAAK,OAAO,CAAC;;AAArD,oBAAM,GAA+C,KAAA;AAC3D,wBAAU,kBAAkB,IAAI,OAAO,GAAG,IAAI,OAAO,IAAI,MAAM,EAAE,KAAK,SAAA,OAAK;AACzE,oBAAM,gBAAgB,MAAK,qBAAqB,MAAM,OAAO,MAAM,MAAM;AACzE,oBAAI,UAAU,OAAO,cAAc,GAAG,cAAc,GAAG,MAAM,OAAO,MAAM,MAAM;AAChF,wBAAQ,IAAI,MAAM;cACpB,CAAC;;;;;;;;IACF;AAEO,IAAAA,iBAAY,UAAA,eAApB,SAAqB,OAAuB;AAC1C,UAAM,OAAO,EAAE,OAAO,KAAK,QAAQ,cAAc,GAAG,QAAQ,KAAK,QAAQ,eAAe,EAAC;AACzF,cAAQ,MAAI;QACV,MAAK,KAAK,UAAU,KAAK,KAAK,WAAW;AACvC,eAAK,SAAU,KAAK,QAAQ,MAAM,SAAU,MAAM;AAClD;QACF,MAAK,KAAK,UAAU,KAAK,KAAK,WAAW;AACvC,eAAK,QAAS,KAAK,SAAS,MAAM,QAAS,MAAM;AACjD;QACF,MAAK,KAAK,UAAU,KAAK,KAAK,WAAW;AACvC,eAAK,QAAQ,MAAM;AACnB,eAAK,SAAS,MAAM;AACpB;;AAEJ,aAAO;;AAGD,IAAAA,iBAAA,UAAA,uBAAR,SAA6B,YAAoB,aAAmB;;AAClE,UAAM,SAAS;QACb,GAAG,CAAC,aAAa;QACjB,GAAG,CAAC,cAAc;;AAEpB,cAAQ,KAAK,QAAQ,oBAAkB;QACrC,KAAK;AACH,iBAAO,IAAI,CAAC,aAAa;AACzB,iBAAO,IAAI;AACX;QACF,KAAK;AACH,iBAAO,IAAI;AACX,iBAAO,IAAI;AACX;QACF,KAAK;AACH,iBAAO,IAAI,CAAC;AACZ,iBAAO,IAAI;AACX;QACF,KAAK;AACH,iBAAO,IAAI,CAAC,aAAa;AACzB,iBAAO,IAAI,CAAC;AACZ;QACF,KAAK;AACH,iBAAO,IAAI;AACX,iBAAO,IAAI,CAAC;AACZ;QACF,KAAK;AACH,iBAAO,IAAI,CAAC;AACZ,iBAAO,IAAI,CAAC;AACZ;QACF,KAAK;AACH,iBAAO,IAAI;AACX,iBAAO,IAAI,CAAC,cAAc;AAC1B;QACF,KAAK;AACH,iBAAO,IAAI,CAAC;AACZ,iBAAO,IAAI,CAAC,cAAc;AAC1B;;AAEJ,OAAC,aAAY,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,UAAU,MAAM,OAAO,IAAI;AACpD,OAAC,aAAY,KAAA,KAAK,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,UAAU,MAAM,OAAO,IAAI;AACpD,aAAO;;AAEX,WAACA;EAAD,EAAC;;ACxUD,IAAA;;EAAA,WAAA;AAUE,aAAYC,YAAA,MAAwB,eAAgC;;AAClE,WAAK,UAAU;AACf,WAAK,eAAe,KAAK,QAAQ;AACjC,WAAK,gBAAgB,KAAK,QAAQ;AAClC,WAAK,SAAO,KAAA,KAAK,QAAQ,uBAAiB,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ;AACpD,WAAK,SAAO,KAAA,KAAK,QAAQ,uBAAiB,QAAA,OAAA,SAAA,SAAA,GAAE,SAAQ;AACpD,WAAK,WAAS,KAAA,KAAK,QAAQ,uBAAmB,QAAA,OAAA,SAAA,SAAA,GAAA,WAAU,eAAe,KAAK,MAAM,KAAK,MAAM,CAAC;AAC9F,WAAK,QAAM,KAAA,KAAK,QAAQ,uBAAiB,QAAA,OAAA,SAAA,SAAA,GAAE,QAAO,CAAC,GAAG,CAAC;AACvD,WAAK,gBAAgB;;AAGvB,IAAAA,YAAA,UAAA,OAAA,WAAA;;AACE,UAAM,eAAe,gBAAgB,eACnC,KAAA,KAAK,QAAQ,uBAAmB,QAAA,OAAA,SAAA,SAAA,GAAA,UAAS,KAAK,eAAe,KAAK,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,QAC5F,KAAA,KAAK,QAAQ,uBAAmB,QAAA,OAAA,SAAA,SAAA,GAAA,WAAU,KAAK,gBAAgB,KAAK,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI;AAEpG,UAAM,gBAAgB,aAAa,WAAW,IAAI;AAClD,WAAI,KAAA,KAAK,QAAQ,uBAAiB,QAAA,OAAA,SAAA,SAAA,GAAE,iBAAiB;AACnD,0BAAa,QAAb,kBAAa,SAAA,SAAb,cAAe,WACb,KAAA,KAAK,QAAQ,uBAAiB,QAAA,OAAA,SAAA,SAAA,GAAE,iBAChC,GACA,IACQ,KAAA,KAAK,QAAQ,uBAAiB,QAAA,OAAA,SAAA,SAAA,GAAE,QAChC,KAAA,KAAK,QAAQ,uBAAmB,QAAA,OAAA,SAAA,SAAA,GAAA,MAAM;;AAGlD,eAAS,WAAW,GAAG,WAAW,KAAK,MAAM,YAAY;AACvD,iBAAS,WAAW,GAAG,WAAW,KAAK,MAAM,YAAY;AACvD,cAAI,GAAC,MAAA,KAAA,KAAK,YAAS,QAAA,OAAA,SAAA,SAAA,GAAA,QAAQ,OAAI,QAAA,OAAA,SAAA,SAAA,GAAA,QAAQ,IAAG;AACxC;;AAEF,4BAAa,QAAb,kBAAA,SAAA,SAAA,cAAe,UACb,KAAK,eACL,KAAK,eAAe,WAAW,KAAK,IAAI,CAAC,IAAI,UAC7C,KAAK,gBAAgB,WAAW,KAAK,IAAI,CAAC,IAAI,UAC9C,KAAK,cACL,KAAK,aAAa;;;AAIxB,aAAO;;AAEX,WAACA;EAAD,EAAC;;ACtDD,IAAM,eAAe,SAAC,SAA2B,eAAgC;AAC/E,UAAQ,QAAQ,QAAM;IACpB,KAAK;AACH,aAAO,IAAI,WAAW,SAAS,aAAa,EAAE,KAAI;IACpD;AACE,aAAO;;AAEb;AAEA,IAAM,yBAAyB,SAAC,SAAyB;;AACvD,UAAQ,QAAQ,QAAM;IACpB,KAAK,QAAQ;AACX,UAAM,SAAO,KAAA,QAAQ,uBAAmB,QAAA,OAAA,SAAA,SAAA,GAAA,SAAQ;AAChD,UAAM,SAAO,KAAA,QAAQ,uBAAmB,QAAA,OAAA,SAAA,SAAA,GAAA,SAAQ;AAChD,UAAM,QAAM,KAAA,QAAQ,uBAAmB,QAAA,OAAA,SAAA,SAAA,GAAA,QAAO,CAAC,GAAG,CAAC;AACnD,aAAO,CAAC,QAAQ,QAAQ,OAAO,IAAI,CAAC,IAAI,MAAM,QAAQ,SAAS,OAAO,IAAI,CAAC,IAAI,IAAI;;IAErF;AACE,aAAO,CAAC,QAAQ,OAAO,QAAQ,MAAM;;AAE3C;ACbA,IAAA;;EAAA,WAAA;AAgBE,aAAAC,WAAY,MAAoC;AAApC,UAAA,SAAA,QAAA;AAAA,eAAoC,CAAA;MAAA;AAdxC,WAAA,gBAAyB,SAAS;AAQlC,WAAU,aAAY;AAO5B,WAAK,QAAQ;AACb,WAAK,UAAO,SAAA,SAAA,CAAA,GACP,cAAc,GACd,IAAI;AAET,WAAK,oBAAoB,KAAK,QAAQ,MAAM;AAC5C,WAAK,kBAAkB,IAAI,gBAAgB,KAAK,OAAO,KAAK,OAAO;AACnE,iBAAW,KAAK,QAAQ,iBAAiB;;AASrC,IAAAA,WAAA,UAAA,gBAAN,WAAA;yDACE,MACA,MACA,QAAsB;AAFtB,YAAA,SAAA,QAAA;AAAA,iBAAoC,CAAA;QAAA;AACpC,YAAA,SAAA,QAAA;AAAA,iBAAqC;QAAA;AACrC,YAAA,WAAA,QAAA;AAAA,mBAAsB;QAAA;;;;AAEtB,mBAAK,eAAe,MAAM,IAAI;AAC9B,yBAAW,KAAK,QAAQ,iBAAiB;AACrC,kBAAA,CAAA,OAAA,QAAM,CAAA,GAAA,CAAA;AACR,mBAAK,OAAM;AACX,qBAAA,CAAA,GAAM,KAAK,OAAM,CAAE;;AAAnB,iBAAA,KAAA;;;;;;;;;;IAEH;AAKK,IAAAA,WAAA,UAAA,SAAN,WAAA;;;;;;;AACE,kBAAI,KAAK,YAAY;AACnB,uBAAM;kBAAA;;gBAAA;;AAER,mBAAK,aAAa;AAClB,kBAAI,CAAC,KAAK,eAAc,GAAI;AAC1B,qBAAK,aAAa;AAClB,uBAAM;kBAAA;;gBAAA;;AAGR,kBAAI,CAAC,KAAK,gBAAe,GAAI;AAC3B,qBAAK,aAAa;AAClB,uBAAM;kBAAA;;gBAAA;;AAEF,0BAAY,YAAY,KAAK,YAAY;AAE/C,qBAAM,CAAA,IAAA,KAAA,KAAK,qBAAiB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI,CAAE;;AAAlC,iBAAA,KAAA;AACA,mBAAK,eAAe,aAAa,KAAK,UAA4B,KAAA,KAAK,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,CAAE;AAC7F,sBAAQ,aAAa,KAAK,YAAY;AAC5C,eAAA,KAAA,KAAK,qBAAiB,QAAA,OAAA,SAAA,SAAA,GAAA,MAAK;AAC3B,mBAAK,eAAe,SAAS,cAAc,KAAK;AAC1C,kCAAoB,SAAS,cAAc,KAAK;AACtD,mBAAK,aAAa,gBAAgB;AAClC,mBAAK,aAAa,0BAA0B;AACtC,kCAAoB,KAAK,uBAAsB;AACrD,mBAAK,aAAa,MAAM,UAAU,mBACtB,OAAA,KAAK,QAAQ,QAAM,wHAAA,EAAA,OAC3B,sBAAsB,WAAW,2JAA2J,gCAA8B,QAAA;AAExN,+BAAiB,uBAAuB,KAAK,OAAO;AAC1D,gCAAkB,MAAM,UAAU,iLAAA,OAErB,sBAAsB,SAAS,UAAU,YAAU,mHAAA,EAAA,OACpD,KAAK,QAAQ,QAAM,kCAAA,EAAA,OAAmC,OAAK,gCAAA,EAAA,OAAiC,KAAK,QAAQ,kBACjG,qCAAA,EAAA,OAAA,eAAe,CAAC,GAAC,KAAA,EAAA,OAAM,eAAe,CAAC,GAAqC,mCAAA,EAAA,OAAA,KAAK,QAAQ,oBAAkB,WAAA,EAAA,OAC3H,uBAAuB,KAAK,QAAQ,SAAS,KAAK,QAAQ,gBAAgB,GAAC,QAAA;AAE/E,mBAAK,aAAa,YAAY,iBAAiB;AAC/C,mBAAK,cAAc,YAAY,KAAK,YAAY;AAEhD,kBAAI,KAAK,QAAQ,iBAAiB;AAChC,oBAAI;AACF,uBAAK,oBAAmB;yBACxB,IAAM;AACN,mBAAA,MAAA,KAAA,KAAK,SAAQ,oBAAc,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;;;AAG/B,6BAAa,MAAA,KAAA,KAAK,SAAQ,eAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;AACvC,mBAAK,aAAa;;;;;;;;IACnB;AAKD,IAAAA,WAAA,UAAA,UAAA,WAAA;AACE,WAAK,OAAM;AACX,WAAK,eAAe;;AAGhB,IAAAA,WAAA,UAAA,QAAN,WAAA;;;AACE,iBAAO,CAAA,GAAA,KAAK,cAAc,SAAe,KAAK,YAAY,CAAC;;;IAC5D;AAES,IAAAA,WAAA,UAAA,SAAV,WAAA;;AACE,OAAA,MAAA,KAAA,KAAK,SAAQ,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;AAC5B,OAAA,KAAA,KAAK,cAAU,QAAA,OAAA,SAAA,SAAA,GAAA,WAAU;AACzB,OAAA,KAAA,KAAK,mBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,WAAU;AAC9B,WAAK,iCAAgC;AACrC,OAAA,MAAA,KAAA,KAAK,kBAAc,QAAA,OAAA,SAAA,SAAA,GAAA,gBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,YAAY,KAAK,YAAY;AAC5D,OAAA,MAAA,KAAA,KAAK,SAAQ,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;;AAGhB,IAAAA,WAAA,UAAA,iBAAV,SAAyB,MAAiC,MAAqC;AAA/F,UAcC,QAAA;AAdyD,UAAA,SAAA,QAAA;AAAA,eAAqC;MAAA;AAC7F,UAAI,SAAS,UAAU;AACrB,eAAO,KAAK,IAAI,EAAE,QAAQ,SAAA,KAAG;AAC3B,gBAAK,UAAU,MAAK,MAAM,GAA6B,IAAW,KAAK,GAA6B;QACtG,CAAC;aACI;AACL,aAAK,QAAQ;;AAEf,WAAK,UACA,SAAA,SAAA,CAAA,GAAA,cAAc,GACd,KAAK,KAAK;AAEf,WAAK,oBAAoB,KAAK,QAAQ,MAAM;AAC5C,WAAK,kBAAkB,IAAI,gBAA2C,KAAK,OAAO,KAAK,OAAO;;AAGxF,IAAAA,WAAmB,UAAA,sBAA3B,SAA4B,QAAwB;AAClD,UAAI,OAAO,WAAW,UAAU;AAC9B,YAAM,gBAAgB,SAAS,cAAc,MAAM;AACnD,0BAAkB,KAAK,gBAAgB;aAClC;AACL,aAAK,gBAAgB;;AAGvB,UAAI,CAAC,KAAK,eAAe;AACvB,gBAAQ,MAAM,wDAAwD;;;AAIlE,IAAAA,WAAA,UAAA,iBAAR,WAAA;AACE,UAAI,SAAS;AAEb,YAAM,KAAK,KAAK,cAAc,UAAU,EAAE,QAAQ,SAAA,MAAI;AACpD,YAAI,CAAC,QAAQ;AACX;;AAEF,YAAI,OAAO,eAAe,KAAK,MAAM,eAAe,GAAG;AACrD,mBAAS;;MAGb,CAAC;AACD,aAAO;;AAGD,IAAAA,WAAA,UAAA,kBAAR,WAAA;AACE,cAAQ,KAAK,QAAQ,aAAW;QAC9B,KAAK;AACH,iBAAO,OAAO,eAAe,KAAK,KAAK,SAAS,OAAO;QACzD,KAAK;QACL,KAAK;QACL,KAAK;AACH,iBAAO,KAAK,QAAQ,QAAQ,SAAS;;;AAInC,IAAAA,WAAA,UAAA,yBAAR,WAAA;AACE,UAAI,CAAC,QAAQ,MAAM,EAAE,SAAS,KAAK,cAAc,QAAQ,kBAAiB,CAAE,GAAG;AAC7E,eAAO;;AAET,aAAO;;AAGK,IAAAA,WAAA,UAAA,wBAAd,WAAA;;;;;mBACM,CAAC,KAAK,cAAc,SAAe,KAAK,YAAY,EAApD,QAAqD,CAAA,GAAA,CAAA;AACvD,mBAAK,OAAM;AACX,qBAAA,CAAA,GAAM,KAAK,OAAM,CAAE;;AAAnB,iBAAA,KAAA;;;AAEF,mBAAK,+BAA8B;;;;;;;;IACpC;AAEO,IAAAA,WAAA,UAAA,sBAAR,WAAA;AAAA,UAqCC,QAAA;AApCC,UAAI,CAAC,KAAK,cAAc;AACtB;;AAEF,WAAK,+BAA8B;AACnC,WAAK,WAAW,IAAI,iBAAiB,SAAO,eAA+B;AAAA,eAAA,UAAA,OAAA,QAAA,QAAA,WAAA;;;;AACrE,oBAAA,EAAA,cAAc,SAAS,GAAvB,QAAwB,CAAA,GAAA,CAAA;AAC1B,qBAAK,OAAM;AACX,uBAAA,CAAA,GAAM,KAAK,OAAM,CAAE;;AAAnB,mBAAA,KAAA;;;;;;;;;QAEH,CAAA;MAAA,CAAA;AACD,WAAK,SAAS,QAAQ,KAAK,cAAc;QACvC,YAAY;;QACZ,WAAW;;QACX,SAAS;;QACT,eAAe;;MAChB,CAAA;AACD,WAAK,gBAAgB,IAAI,iBAAiB,SAAO,eAA+B;AAAA,eAAA,UAAA,OAAA,QAAA,QAAA,WAAA;;;;;;AAC9C,qBAAA,GAAb,kBAAa;;;AAAb,oBAAA,EAAA,KAAA,gBAAA,QAAa,QAAA,CAAA,GAAA,CAAA;AAArB,uBAAI,gBAAA,EAAA;uBAEX,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,YAAW,KAAK,kBACtB,KAAA,SAAA,QAAA,SAAI,SAAA,SAAJ,KAAM,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAG,CAAC,OAAM,KAAK,gBAChC,KAAK,SAAS,eACb,KAAK,WAAW,KAAK,iBACrB,KAAK,OAAO,cAAc,KAAK,cAJjC,QAI8C,CAAA,GAAA,CAAA;AAE9C,qBAAK,OAAM;AACX,uBAAA,CAAA,GAAM,KAAK,OAAM,CAAE;;AAAnB,mBAAA,KAAA;;;AATe;;;;;;;;;QAYpB,CAAA;MAAA,CAAA;AACD,WAAK,cAAc,QAAQ,KAAK,eAAe;QAC7C,YAAY;;QACZ,WAAW;;QACX,SAAS;;QACT,eAAe;;MAChB,CAAA;;AAGK,IAAAA,WAAA,UAAA,iCAAR,WAAA;AACE,WAAK,iCAAgC;AACrC,WAAK,iCAAiC,sBAAsB,KAAK,sBAAsB,KAAK,IAAI,CAAC;;AAG3F,IAAAA,WAAA,UAAA,mCAAR,WAAA;AACE,UAAI,CAAC,YAAY,KAAK,8BAA8B,GAAG;AACrD,6BAA6B,KAAK,8BAA8B;;;AAGtE,WAACA;EAAD,EAAC;;ACjPD,IAAA;;EAAA,SAAA,QAAA;AAA6B,cAASC,iBAAA,MAAA;AAKpC,aAAAA,gBAAY,OAAqC;AAArC,UAAA,UAAA,QAAA;AAAA,gBAAqC,CAAA;MAAA;AAC/C,UAAM,eAA0C;QAC9C,aAAa;QACb,MAAM;;AAER,aAAA,OAAW,KAAA,MAAA,SAAA,SAAA,CAAA,GAAA,KAAK,GAAK,YAAY,CAAA,KAAG;;AAGhC,IAAAA,gBAAA,UAAA,gBAAN,WAAA;yDACE,MACA,MACA,QAAsB;AAFtB,YAAA,SAAA,QAAA;AAAA,iBAAoC,CAAA;QAAA;AACpC,YAAA,SAAA,QAAA;AAAA,iBAAqC;QAAA;AACrC,YAAA,WAAA,QAAA;AAAA,mBAAsB;QAAA;;;;AAEtB,mBAAK,cAAc;AACnB,mBAAK,OAAO;AACZ,mBAAK,eAAe,MAAM,IAAI;AAC9B,yBAAW,KAAK,QAAQ,iBAAiB;AACrC,kBAAA,CAAA,OAAA,QAAM,CAAA,GAAA,CAAA;AACR,mBAAK,OAAM;AACX,qBAAA,CAAA,GAAM,KAAK,OAAM,CAAE;;AAAnB,iBAAA,KAAA;;;;;;;;;;IAEH;AAMM,IAAAA,gBAAM,SAAb,SAAc,OAA2C;AAErD,UAAA,KAME,MANM,KAAR,MAAM,OAAA,SAAA,KAAE,IACR,KAKE,MAAK,WALP,YAAS,OAAA,SAAG,SAAM,IAClB,KAIE,MAJ+B,oBAAjC,qBAAkB,OAAA,SAAG,eAAY,IACjC,KAGE,MAAK,MAHP,OAAI,OAAA,SAAG,WAAQ,IACf,KAEE,MAFgB,gBAAlB,iBAAc,OAAA,SAAG,IAAC,IAClB,YACE,MAAK;AACT,UAAI,CAAC,KAAK;AACR;;AAEF,UAAI,SAAS,UAAU;AACrB,YAAM,QAAM,IAAI,MAAK;AACrB,cAAI,MAAM;AACV,cAAI,iBAAiB,QAAQ,WAAA;AACnB,cAAA,QAAkB,MAAG,OAAd,SAAW,MAAG;AAC7B,cAAM,SAAS,gBAAgB,aAAa,OAAO,MAAM;AACzD,cAAM,MAAM,OAAO,WAAW,IAAI;AAClC,cAAI,CAAC,KAAK;AACR,kBAAM,IAAI,MAAM,mBAAmB;;AAErC,cAAI,UAAU,OAAK,GAAG,GAAG,OAAO,MAAM;AACtC,cAAI,2BAA2B;AAC/B,cAAI,YAAY;AAChB,mBAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,gBAAI,SAAS,GAAG,GAAG,OAAO,MAAM;;AAElC,cAAM,cAAc,aAAa,MAAM;AACvC,cAAI,WAAqB,SAAS,GAAG;AACnC,0BAAS,QAAT,cAAS,SAAA,SAAT,UAAY,WAAW;;QAE3B,CAAC;;;AAGP,WAACA;EAAD,EAnE6B,SAAS;;ACAtC,IAAA;;EAAA,WAAA;AAaE,aAAAC,gBAAY,MAAyC;AAAzC,UAAA,SAAA,QAAA;AAAA,eAAyC,CAAA;MAAA;;AAN7C,WAAI,OAAG;AAOb,WAAK,QAAQ;AACb,WAAK,UAAO,SAAA,SAAA,CAAA,GACP,cAAc,GACd,IAAI;AAET,UAAI,KAAK,MAAM,aAAa;AAC1B,SAAA,KAAA,KAAK,MAAM,SAAG,QAAA,OAAA,SAAA,SAAA,GAAE,aAAa,eAAe,WAAW;;AAEzD,WAAK,kBAAkB,IAAI,gBAAgB,KAAK,OAAO,KAAK,OAAO;AACnE,WAAK,eAAc,KAAA,KAAK,MAAM,SAAK,QAAA,OAAA,SAAA,SAAA,GAAA;AACnC,WAAK,kBAAkB,KAAK,mBAAkB;;AAG1C,IAAAA,gBAAA,UAAA,SAAN,WAAA;;;;;;AACE,kBAAI,KAAK,MAAM;AACb,uBAAM;kBAAA;;gBAAA;;AAER,qBAAM,CAAA,IAAA,KAAA,KAAK,qBAAiB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAI,CAAE;;AAAlC,iBAAA,KAAA;AACA,mBAAK,QAAQ,SAAS;AACtB,mBAAK,QAAQ,oBAAiB,SAAA,SAAA,CAAA,GACzB,KAAK,QAAQ,iBAAiB,GACjC,EAAA,QAAO,KAAA,KAAK,qBAAiB,QAAA,OAAA,SAAA,SAAA,GAAA,OAC7B,SAAQ,KAAA,KAAK,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAE,QAC9B,iBAAiB,KAAK,gBAAe,CAAA;AAEvC,mBAAK,eAAe,aAAa,KAAK,UAA4B,KAAA,KAAK,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS,CAAE;AACnG,mBAAK,QAAQ,IAAK,MAAM,aAAa,KAAK,YAAY;AACtD,eAAA,KAAA,KAAK,qBAAiB,QAAA,OAAA,SAAA,SAAA,GAAA,MAAK;AAC3B,mBAAK,OAAO;;;;;;;;IACb;AAED,IAAAA,gBAAA,UAAA,UAAA,WAAA;AACE,WAAK,QAAQ,IAAK,MAAc,KAAK;AACrC,WAAK,OAAO;;AAGN,IAAAA,gBAAA,UAAA,qBAAR,WAAA;AACE,UAAI,KAAK,QAAQ,KAAK;AACpB,eAAO,KAAK,QAAQ;;;AAG1B,WAACA;EAAD,EAAC;;", "names": ["d", "b", "__assign", "img", "WatermarkCanvas", "GridLayout", "Watermark", "BlindWatermark", "ImageWatermark"]}