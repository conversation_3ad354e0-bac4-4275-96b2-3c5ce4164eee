const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

async function testLoginAPI() {
  console.log('=== 测试登录API ===\n');
  
  const testCases = [
    {
      name: 'admin用户登录',
      data: {
        account: 'admin',
        password: 'admin123',
        captcha: true
      }
    },
    {
      name: '手机号登录',
      data: {
        account: '***********',
        password: '8888a8888#@',
        captcha: true
      }
    },
    {
      name: '用户名登录',
      data: {
        account: 'testuser',
        password: '8888a8888#@',
        captcha: true
      }
    },
    {
      name: '错误密码',
      data: {
        account: 'admin',
        password: 'wrongpassword',
        captcha: true
      }
    }
  ];
  
  for (const testCase of testCases) {
    console.log(`--- ${testCase.name} ---`);
    console.log('请求数据:', JSON.stringify(testCase.data, null, 2));
    
    try {
      const response = await axios.post(`${API_BASE_URL}/system/login`, testCase.data, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('响应状态:', response.status);
      console.log('响应数据:', JSON.stringify(response.data, null, 2));
      console.log('结果: ✅ 登录成功');
      
    } catch (error) {
      if (error.response) {
        console.log('响应状态:', error.response.status);
        console.log('错误信息:', JSON.stringify(error.response.data, null, 2));
        
        if (testCase.name.includes('错误密码')) {
          console.log('结果: ✅ 正确拒绝（密码错误）');
        } else {
          console.log('结果: ❌ 意外失败');
        }
      } else if (error.code === 'ECONNREFUSED') {
        console.log('结果: ❌ 无法连接到服务器');
        console.log('💡 请确保后端服务已启动：npm run start:dev');
        break;
      } else {
        console.log('结果: ❌ 请求错误:', error.message);
      }
    }
    
    console.log('');
  }
}

// 运行测试
testLoginAPI().catch(console.error);
