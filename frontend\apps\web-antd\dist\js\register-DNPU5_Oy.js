var v=(p,u,o)=>new Promise((r,t)=>{var l=n=>{try{c(o.next(n))}catch(m){t(m)}},i=n=>{try{c(o.throw(n))}catch(m){t(m)}},c=n=>n.done?r(n.value):Promise.resolve(n.value).then(l,i);c((o=o.apply(p,u)).next())});import{d as k,y,c as S,a as $,b as _,s as g,f as P,q as b,g as w,v as h,t as d,k as s,$ as e,h as V,r as B,C,j as N}from"../jse/index-index-DyHD_jbN.js";import{b as x,u as A,T as F,f as R,j as T,k as q}from"./bootstrap-5OPUVRWy.js";const I={class:"mt-4 text-center text-sm"},L=k({name:"RegisterForm",__name:"register",props:{formSchema:{default:()=>[]},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(p,{expose:u,emit:o}){const r=p,t=o,[l,i]=x(y({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:S(()=>r.formSchema),showDefaultActions:!1})),c=A();function n(){return v(this,null,function*(){const{valid:a}=yield i.validate(),f=yield i.getValues();a&&t("submit",f)})}function m(){c.push(r.loginPath)}return u({getFormApi:()=>i}),(a,f)=>(_(),$("div",null,[g(F,null,{desc:b(()=>[w(a.$slots,"subTitle",{},()=>[h(d(a.subTitle||s(e)("authentication.signUpSubtitle")),1)])]),default:b(()=>[w(a.$slots,"title",{},()=>[h(d(a.title||s(e)("authentication.createAnAccount"))+" 🚀 ",1)])]),_:3}),g(s(l)),g(s(R),{class:V([{"cursor-wait":a.loading},"mt-2 w-full"]),loading:a.loading,"aria-label":"register",onClick:n},{default:b(()=>[w(a.$slots,"submitButtonText",{},()=>[h(d(a.submitButtonText||s(e)("authentication.signUp")),1)])]),_:3},8,["class","loading"]),P("div",I,[h(d(s(e)("authentication.alreadyHaveAccount"))+" ",1),P("span",{class:"vben-link text-sm font-normal",onClick:f[0]||(f[0]=j=>m())},d(s(e)("authentication.goToLogin")),1)])]))}}),E=k({name:"Register",__name:"register",setup(p){const u=B(!1),o=S(()=>[{component:"VbenInput",componentProps:{placeholder:e("authentication.usernameTip")},fieldName:"username",label:e("authentication.username"),rules:T().min(1,{message:e("authentication.usernameTip")})},{component:"VbenInputPassword",componentProps:{passwordStrength:!0,placeholder:e("authentication.password")},fieldName:"password",label:e("authentication.password"),renderComponentContent(){return{strengthText:()=>e("authentication.passwordStrength")}},rules:T().min(1,{message:e("authentication.passwordTip")})},{component:"VbenInputPassword",componentProps:{placeholder:e("authentication.confirmPassword")},dependencies:{rules(t){const{password:l}=t;return T({required_error:e("authentication.passwordTip")}).min(1,{message:e("authentication.passwordTip")}).refine(i=>i===l,{message:e("authentication.confirmPasswordTip")})},triggerFields:["password"]},fieldName:"confirmPassword",label:e("authentication.confirmPassword")},{component:"VbenCheckbox",fieldName:"agreePolicy",renderComponentContent:()=>({default:()=>C("span",[e("authentication.agree"),C("a",{class:"vben-link ml-1 ",href:""},`${e("authentication.privacyPolicy")} & ${e("authentication.terms")}`)])}),rules:q().refine(t=>!!t,{message:e("authentication.agreeTip")})}]);function r(t){console.log("register submit:",t)}return(t,l)=>(_(),N(s(L),{"form-schema":o.value,loading:u.value,onSubmit:r},null,8,["form-schema","loading"]))}});export{E as default};
