// generator 处理原来的函数
function * fnc_ () {
    let i = 0
    const start = performance.now()
    while (performance.now() - start <= 5000) {
        yield i++
    }
    return i
}
// 简易时间分片
export default function timeSlice (fnc, cb = setTimeout) {
    if(fnc.constructor.name !== 'GeneratorFunction') return fnc()
    return async function (...args) {
        const fnc_ = fnc(...args)
        let data
        do {
            data = fnc_.next(await data?.value)
            // 每执行一步就休眠，注册一个宏任务 setTimeout 来叫醒他
            await new Promise( resolve => cb(resolve))
        } while (!data.done)
        return data.value
    }
}
/* setTimeout(async () => {
    const fnc = timeSlice(fnc_)
    const start = performance.now()
    console.log('开始')
    const num = await fnc()
    console.log('结束', `${(performance.now() - start)/ 1000}s`)
    console.log(num)
}, 1000) */