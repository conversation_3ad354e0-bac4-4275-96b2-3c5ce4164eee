const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 测试数据库配置
const testConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '123456',
  multipleStatements: true
};

async function testInit() {
  let connection;
  
  try {
    console.log('🧪 开始测试数据库初始化...');
    
    // 连接数据库
    connection = await mysql.createConnection(testConfig);
    console.log('✅ 数据库连接成功');
    
    // 读取SQL文件的前几行进行测试
    const sqlFilePath = path.join(__dirname, 'database-init.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // 提取前几个SQL语句进行测试
    const testSql = `
      CREATE DATABASE IF NOT EXISTS test_system_manage DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
      USE test_system_manage;
      
      CREATE TABLE IF NOT EXISTS test_users (
        id BIGINT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
        password VARCHAR(255) NOT NULL COMMENT '密码',
        real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
        status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
        created_time DATETIME DEFAULT CURRENT_TIMESTAMP
      );
      
      INSERT INTO test_users (username, password, real_name) VALUES ('test', 'test123', '测试用户');
    `;
    
    console.log('📝 执行测试SQL...');
    
    try {
      // 尝试使用multipleStatements执行
      await connection.query(testSql);
      console.log('✅ multipleStatements执行成功');
    } catch (error) {
      console.log('⚠️  multipleStatements执行失败，错误信息:', error.message);
      
      // 尝试逐条执行
      const statements = testSql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      console.log('📝 尝试逐条执行...');
      for (const statement of statements) {
        if (statement.trim()) {
          try {
            await connection.execute(statement);
            console.log(`  ✅ 执行成功: ${statement.substring(0, 50)}...`);
          } catch (execError) {
            console.log(`  ❌ 执行失败: ${statement.substring(0, 50)}...`);
            console.log(`     错误: ${execError.message}`);
          }
        }
      }
    }
    
    // 验证结果
    try {
      await connection.execute('USE test_system_manage');
      const [rows] = await connection.execute('SELECT * FROM test_users');
      console.log('✅ 测试数据查询成功，记录数:', rows.length);
      
      // 清理测试数据库
      await connection.execute('DROP DATABASE IF EXISTS test_system_manage');
      console.log('✅ 测试数据库清理完成');
      
    } catch (error) {
      console.log('⚠️  验证测试结果时出错:', error.message);
    }
    
    console.log('🎉 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 请检查数据库用户名和密码');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 请确保MySQL服务已启动');
    }
    
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 显示配置信息
console.log('🔧 测试配置:');
console.log(`  主机: ${testConfig.host}`);
console.log(`  端口: ${testConfig.port}`);
console.log(`  用户: ${testConfig.user}`);
console.log(`  密码: ${testConfig.password ? '***' : '(未设置)'}`);
console.log('');

// 运行测试
testInit().catch(console.error);
