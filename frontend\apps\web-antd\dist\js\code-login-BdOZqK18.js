var T=(d,u,i)=>new Promise((c,t)=>{var r=n=>{try{l(i.next(n))}catch(m){t(m)}},b=n=>{try{l(i.throw(n))}catch(m){t(m)}},l=n=>n.done?c(n.value):Promise.resolve(n.value).then(r,b);l((i=i.apply(d,u)).next())});import{d as C,y as S,c as $,a as w,b as N,s as f,q as p,g as _,v as h,t as g,k as s,$ as e,f as V,h as v,r as x,j as y}from"../jse/index-index-DyHD_jbN.js";import{u as L,b as P,T as A,f as k,j as B}from"./bootstrap-5OPUVRWy.js";const F={class:"text-muted-foreground"},j=C({name:"AuthenticationCodeLogin",__name:"code-login",props:{formSchema:{},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(d,{expose:u,emit:i}){const c=d,t=i,r=L(),[b,l]=P(S({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:$(()=>c.formSchema),showDefaultActions:!1}));function n(){return T(this,null,function*(){const{valid:o}=yield l.validate(),a=yield l.getValues();o&&t("submit",{code:a==null?void 0:a.code,phoneNumber:a==null?void 0:a.phoneNumber})})}function m(){r.push(c.loginPath)}return u({getFormApi:()=>l}),(o,a)=>(N(),w("div",null,[f(A,null,{desc:p(()=>[V("span",F,[_(o.$slots,"subTitle",{},()=>[h(g(o.subTitle||s(e)("authentication.codeSubtitle")),1)])])]),default:p(()=>[_(o.$slots,"title",{},()=>[h(g(o.title||s(e)("authentication.welcomeBack"))+" 📲 ",1)])]),_:3}),f(s(b)),f(s(k),{class:v([{"cursor-wait":o.loading},"w-full"]),loading:o.loading,onClick:n},{default:p(()=>[_(o.$slots,"submitButtonText",{},()=>[h(g(o.submitButtonText||s(e)("common.login")),1)])]),_:3},8,["class","loading"]),f(s(k),{class:"mt-4 w-full",variant:"outline",onClick:a[0]||(a[0]=q=>m())},{default:p(()=>[h(g(s(e)("common.back")),1)]),_:1})]))}}),R=C({name:"CodeLogin",__name:"code-login",setup(d){const u=x(!1),i=$(()=>[{component:"VbenInput",componentProps:{placeholder:e("authentication.mobile")},fieldName:"phoneNumber",label:e("authentication.mobile"),rules:B().min(1,{message:e("authentication.mobileTip")}).refine(t=>/^\d{11}$/.test(t),{message:e("authentication.mobileErrortip")})},{component:"VbenPinInput",componentProps:{createText:t=>t>0?e("authentication.sendText",[t]):e("authentication.sendCode"),placeholder:e("authentication.code")},fieldName:"code",label:e("authentication.code"),rules:B().min(1,{message:e("authentication.codeTip")})}]);function c(t){return T(this,null,function*(){console.log(t)})}return(t,r)=>(N(),y(s(j),{"form-schema":i.value,loading:u.value,onSubmit:c},null,8,["form-schema","loading"]))}});export{R as default};
