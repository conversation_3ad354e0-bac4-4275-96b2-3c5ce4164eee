/**
 * 本地存储
 */
let localStorage = {
  /**
   * 存储数据
   */
  setItem: (key, value) => {
    // 将数组、对象类型的数据转换为 JSON 格式字符串进行存储
    if (typeof value === 'object') {
      value = JSON.stringify(value);
    }
    window.localStorage.setItem(key, value);
  },
  /**
   * 获取数据
   */
  getItem: (key) => {
    const data = window.localStorage.getItem(key);
    try {
      if (!data) {
        return '';
      }
      return JSON.parse(data);
    } catch (err) {
      return data;
    }
  },
  /**
   * 删除数据
   */
  removeItem: (key) => {
    window.localStorage.removeItem(key);
  },
};
export default localStorage;
