const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '123456',
};

async function initDatabase() {
  let connection;
  
  try {
    console.log('🚀 开始初始化数据库（简化版）...');
    
    // 连接到MySQL服务器
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 成功连接到MySQL服务器');
    
    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'database-init.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    console.log('✅ 成功读取数据库初始化脚本');
    
    // 手动分割和执行SQL语句
    console.log('📝 开始执行数据库初始化脚本...');
    
    // 1. 创建数据库
    console.log('  📝 创建数据库...');
    await connection.execute('CREATE DATABASE IF NOT EXISTS system_manage DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    console.log('  ✅ 数据库创建成功');
    
    // 2. 切换到目标数据库
    await connection.execute('USE system_manage');
    console.log('  ✅ 切换到system_manage数据库');
    
    // 3. 提取并执行CREATE TABLE语句
    const createTableRegex = /CREATE TABLE[\s\S]*?;/gi;
    const createTableStatements = sqlContent.match(createTableRegex) || [];
    
    console.log(`  📝 创建 ${createTableStatements.length} 个数据表...`);
    for (const statement of createTableStatements) {
      const tableName = statement.match(/CREATE TABLE\s+(\w+)/i)?.[1];
      try {
        await connection.execute(statement);
        console.log(`    ✅ 创建表: ${tableName}`);
      } catch (error) {
        if (error.code === 'ER_TABLE_EXISTS_ERROR') {
          console.log(`    ⚠️  表已存在: ${tableName}`);
        } else {
          throw error;
        }
      }
    }
    
    // 4. 提取并执行INSERT语句
    const insertRegex = /INSERT INTO[\s\S]*?;/gi;
    const insertStatements = sqlContent.match(insertRegex) || [];
    
    console.log(`  📝 插入 ${insertStatements.length} 组初始数据...`);
    for (const statement of insertStatements) {
      const tableName = statement.match(/INSERT INTO\s+(\w+)/i)?.[1];
      try {
        await connection.execute(statement);
        console.log(`    ✅ 插入数据到: ${tableName}`);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`    ⚠️  数据已存在: ${tableName}`);
        } else {
          console.log(`    ❌ 插入数据失败: ${tableName} - ${error.message}`);
        }
      }
    }
    
    console.log('✅ 数据库初始化脚本执行完成');
    
    // 验证初始化结果
    console.log('🔍 验证数据库初始化结果...');
    
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('📋 已创建的表：');
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });
    
    // 检查初始数据
    const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [roles] = await connection.execute('SELECT COUNT(*) as count FROM roles');
    const [applications] = await connection.execute('SELECT COUNT(*) as count FROM applications');
    
    console.log('📊 初始数据统计：');
    console.log(`  - 用户数量: ${users[0].count}`);
    console.log(`  - 角色数量: ${roles[0].count}`);
    console.log(`  - 应用数量: ${applications[0].count}`);
    
    // 显示默认管理员账号信息
    const [adminUser] = await connection.execute(
      'SELECT username, real_name FROM users WHERE username = "admin"'
    );
    
    if (adminUser.length > 0) {
      console.log('👤 默认管理员账号信息：');
      console.log(`  - 用户名: ${adminUser[0].username}`);
      console.log(`  - 真实姓名: ${adminUser[0].real_name}`);
      console.log(`  - 默认密码: admin123`);
    }
    
    console.log('🎉 数据库初始化完成！');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败：', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 请检查数据库连接配置（用户名、密码）');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 请确保MySQL服务已启动');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 检查配置
function checkConfig() {
  console.log('🔧 数据库配置：');
  console.log(`  - 主机: ${dbConfig.host}`);
  console.log(`  - 端口: ${dbConfig.port}`);
  console.log(`  - 用户: ${dbConfig.user}`);
  console.log(`  - 密码: ${dbConfig.password ? '***' : '(未设置)'}`);
  console.log('');
}

// 主函数
async function main() {
  console.log('='.repeat(50));
  console.log('    系统管理数据库初始化工具（简化版）');
  console.log('='.repeat(50));
  console.log('');
  
  checkConfig();
  await initDatabase();
}

// 运行初始化
main().catch(console.error);
