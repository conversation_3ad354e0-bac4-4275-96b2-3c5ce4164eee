import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { User } from './entities/user.entity';
import { UserDepartment } from './entities/user-department.entity';
import { Organization } from '@/modules/organizations/entities/organization.entity';

@Module({
  imports: [TypeOrmModule.forFeature([User, UserDepartment, Organization])],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
