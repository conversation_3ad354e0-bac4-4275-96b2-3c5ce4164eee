var ze=Object.defineProperty,Re=Object.defineProperties;var Ye=Object.getOwnPropertyDescriptors;var J=Object.getOwnPropertySymbols;var me=Object.prototype.hasOwnProperty,he=Object.prototype.propertyIsEnumerable;var ne=(s,e,o)=>e in s?ze(s,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):s[e]=o,v=(s,e)=>{for(var o in e||(e={}))me.call(e,o)&&ne(s,o,e[o]);if(J)for(var o of J(e))he.call(e,o)&&ne(s,o,e[o]);return s},F=(s,e)=>Re(s,Ye(e));var X=(s,e)=>{var o={};for(var n in s)me.call(s,n)&&e.indexOf(n)<0&&(o[n]=s[n]);if(s!=null&&J)for(var n of J(s))e.indexOf(n)<0&&he.call(s,n)&&(o[n]=s[n]);return o};var z=(s,e,o)=>ne(s,typeof e!="symbol"?e+"":e,o);var ae=(s,e,o)=>new Promise((n,l)=>{var i=c=>{try{C(o.next(c))}catch(r){l(r)}},f=c=>{try{C(o.throw(c))}catch(r){l(r)}},C=c=>c.done?n(c.value):Promise.resolve(c.value).then(i,f);C((o=o.apply(s,e)).next())});import{d as O,j as m,b as u,k as t,O as je,P as Ne,q as g,g as _,a1 as He,a2 as be,a as re,c as j,r as $,s as I,e as k,h as S,J as B,I as ie,y as xe,o as We,W as qe,a3 as Ke,a4 as le,a5 as Ue,a6 as Je,a7 as Ge,a8 as Qe,w as ye,f as Ze,v as R,t as Y,p as ge,i as Be,a9 as Oe,aa as et,T as tt,C as ve}from"../jse/index-index-DyHD_jbN.js";import{c as ce,E as Me,F as ot,I as st,y as nt,X as at,G as lt,O as De,L as rt,N as it,Q as ct,U as dt,V as ut,A as pt,f as Ce,W as ft,Y as mt}from"./bootstrap-5OPUVRWy.js";import{V as ht}from"./loading-DzjUKA94.js";const yt=ce("expand",[["path",{d:"m15 15 6 6",key:"1s409w"}],["path",{d:"m15 9 6-6",key:"ko1vev"}],["path",{d:"M21 16v5h-5",key:"1ck2sf"}],["path",{d:"M21 8V3h-5",key:"1qoq8a"}],["path",{d:"M3 16v5h5",key:"1t08am"}],["path",{d:"m3 21 6-6",key:"wwnumi"}],["path",{d:"M3 8V3h5",key:"1ln10m"}],["path",{d:"M9 9 3 3",key:"v551iv"}]]);const gt=ce("shrink",[["path",{d:"m15 15 6 6m-6-6v4.8m0-4.8h4.8",key:"17vawe"}],["path",{d:"M9 19.8V15m0 0H4.2M9 15l-6 6",key:"chjx8e"}],["path",{d:"M15 4.2V9m0 0h4.8M15 9l6-6",key:"lav6yq"}],["path",{d:"M9 4.2V9m0 0H4.2M9 9 3 3",key:"1pxi2q"}]]);const vt=ce("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Ct=O({__name:"Dialog",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(s,{emit:e}){const l=Me(s,e);return(i,f)=>(u(),m(t(ot),je(Ne(t(l))),{default:g(()=>[_(i.$slots,"default")]),_:3},16))}}),_t=["data-dismissable-modal"],kt=O({__name:"DialogOverlay",setup(s){He();const e=be("DISMISSABLE_MODAL_ID");return(o,n)=>(u(),re("div",{"data-dismissable-modal":t(e),class:"bg-overlay fixed inset-0 z-[1000]"},null,8,_t))}}),wt=O({__name:"DialogContent",props:{class:{},closeClass:{},modal:{type:Boolean},open:{type:Boolean},showClose:{type:Boolean,default:!0},forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["close","closed","opened","escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(s,{expose:e,emit:o}){const n=s,l=o,i=j(()=>{const b=n,{class:r,modal:p,open:h,showClose:d}=b;return X(b,["class","modal","open","showClose"])}),f=Me(i,l),C=$(null);function c(){n.open?l("opened"):l("closed")}return e({getContentRef:()=>C.value}),(r,p)=>(u(),m(t(st),null,{default:g(()=>[I(nt,{name:"fade"},{default:g(()=>[r.open&&r.modal?(u(),m(kt,{key:0,onClick:p[0]||(p[0]=()=>l("close"))})):k("",!0)]),_:1}),I(t(lt),ie({ref_key:"contentRef",ref:C,onAnimationend:c},t(f),{class:t(B)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-top-[48%] fixed z-[1000] w-full p-6 shadow-lg outline-none sm:rounded-xl",n.class)}),{default:g(()=>[_(r.$slots,"default"),r.showClose?(u(),m(t(at),{key:0,class:S(t(B)("data-[state=open]:bg-accent data-[state=open]:text-muted-foreground hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-3 top-3 h-6 w-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none",n.closeClass)),onClick:p[1]||(p[1]=()=>l("close"))},{default:g(()=>[I(t(vt),{class:"h-4 w-4"})]),_:1},8,["class"])):k("",!0)]),_:3},16,["class"])]),_:3}))}}),_e=O({__name:"DialogDescription",props:{class:{},asChild:{type:Boolean},as:{}},setup(s){const e=s,o=j(()=>{const f=e,{class:l}=f;return X(f,["class"])}),n=De(o);return(l,i)=>(u(),m(t(rt),ie(t(n),{class:t(B)("text-muted-foreground text-sm",e.class)}),{default:g(()=>[_(l.$slots,"default")]),_:3},16,["class"]))}}),bt=O({__name:"DialogFooter",props:{class:{}},setup(s){const e=s;return(o,n)=>(u(),re("div",{class:S(t(B)("flex flex-col-reverse sm:flex-row sm:justify-end sm:gap-x-2",e.class))},[_(o.$slots,"default")],2))}}),xt=O({__name:"DialogHeader",props:{class:{}},setup(s){const e=s;return(o,n)=>(u(),re("div",{class:S(t(B)("flex flex-col gap-y-1.5 text-center sm:text-left",e.class))},[_(o.$slots,"default")],2))}}),ke=O({__name:"DialogTitle",props:{class:{},asChild:{type:Boolean},as:{}},setup(s){const e=s,o=j(()=>{const f=e,{class:l}=f;return X(f,["class"])}),n=De(o);return(l,i)=>(u(),m(t(it),ie(t(n),{class:t(B)("text-lg font-semibold leading-none tracking-tight",e.class)}),{default:g(()=>[_(l.$slots,"default")]),_:3},16,["class"]))}});function Bt(s,e,o){const n=xe({offsetX:0,offsetY:0}),l=$(!1),i=r=>{const p=r.clientX,h=r.clientY;if(!s.value)return;const d=s.value.getBoundingClientRect(),{offsetX:w,offsetY:b}=n,D=d.left,N=d.top,H=d.width,G=d.height,W=document.documentElement,Q=W.clientWidth,Z=W.clientHeight,E=-D+w,ee=-N+b,te=Q-D-H+w,oe=Z-N-G+b,L=P=>{let T=w+P.clientX-p,M=b+P.clientY-h;T=Math.min(Math.max(T,E),te),M=Math.min(Math.max(M,ee),oe),n.offsetX=T,n.offsetY=M,s.value&&(s.value.style.transform=`translate(${T}px, ${M}px)`,l.value=!0)},q=()=>{l.value=!1,document.removeEventListener("mousemove",L),document.removeEventListener("mouseup",q)};document.addEventListener("mousemove",L),document.addEventListener("mouseup",q)},f=()=>{const r=le(e);r&&s.value&&r.addEventListener("mousedown",i)},C=()=>{const r=le(e);r&&s.value&&r.removeEventListener("mousedown",i)},c=()=>{n.offsetX=0,n.offsetY=0;const r=le(s);r&&(r.style.transform="none")};return We(()=>{qe(()=>{o.value?f():C()})}),Ke(()=>{C()}),{dragging:l,resetPosition:c,transform:n}}const Ot=O({__name:"modal",props:{modalApi:{default:void 0},bordered:{type:Boolean},cancelText:{},centered:{type:Boolean},class:{},closable:{type:Boolean},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},draggable:{type:Boolean},footer:{type:Boolean},footerClass:{},fullscreen:{type:Boolean},fullscreenButton:{type:Boolean},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},title:{},titleTooltip:{}},setup(s){var U,fe;const e=s,o=ct.getComponents(),n=$(),l=$(),i=$(),f=$(),C=$(),c=Ue();Oe("DISMISSABLE_MODAL_ID",c);const{$t:r}=Je(),{isMobile:p}=Ge(),h=(fe=(U=e.modalApi)==null?void 0:U.useStore)==null?void 0:fe.call(U),{bordered:d,cancelText:w,centered:b,class:D,closable:N,closeOnClickModal:H,closeOnPressEscape:G,confirmLoading:W,confirmText:Q,contentClass:Z,description:E,draggable:ee,footer:te,footerClass:oe,fullscreen:L,fullscreenButton:q,header:P,headerClass:T,loading:M,modal:Ae,openAutoFocus:$e,showCancelButton:Se,showConfirmButton:Ee,title:K,titleTooltip:de}=Qe(e,h),se=j(()=>L.value&&P.value||p.value),ue=j(()=>ee.value&&!se.value&&P.value),{dragging:Le,transform:Pe}=Bt(i,f,ue);ye(()=>{var a;return(a=h==null?void 0:h.value)==null?void 0:a.isOpen},a=>ae(null,null,function*(){if(a){if(yield Be(),!n.value)return;const y=n.value.getContentRef();i.value=y.$el;const{offsetX:V,offsetY:A}=Pe;i.value.style.transform=`translate(${V}px, ${A}px)`}})),ye(()=>M.value,a=>{a&&l.value&&l.value.scrollTo({top:0})});function Te(){var a;(a=e.modalApi)==null||a.setState(y=>F(v({},y),{fullscreen:!L.value}))}function Ve(a){H.value||(a.preventDefault(),a.stopPropagation())}function Ie(a){G.value||a.preventDefault()}function Fe(a){$e.value||a==null||a.preventDefault()}function Xe(a){const y=a.target,V=y==null?void 0:y.dataset.dismissableModal;(!H.value||V!==c)&&(a.preventDefault(),a.stopPropagation())}function pe(a){a.preventDefault(),a.stopPropagation()}return(a,y)=>{var V;return u(),m(t(Ct),{modal:!1,open:(V=t(h))==null?void 0:V.isOpen,"onUpdate:open":y[4]||(y[4]=()=>{var A;return(A=a.modalApi)==null?void 0:A.close()})},{default:g(()=>{var A;return[I(t(wt),{ref_key:"contentRef",ref:n,class:S(t(B)("left-0 right-0 top-[10vh] mx-auto flex max-h-[80%] w-[520px] flex-col p-0 sm:rounded-2xl",t(D),{"border-border border":t(d),"shadow-3xl":!t(d),"left-0 top-0 size-full max-h-full !translate-x-0 !translate-y-0":se.value,"top-1/2 !-translate-y-1/2":t(b)&&!se.value,"duration-300":!t(Le)})),modal:t(Ae),open:(A=t(h))==null?void 0:A.isOpen,"show-close":t(N),"close-class":"top-3",onCloseAutoFocus:pe,onClosed:y[2]||(y[2]=()=>{var x;return(x=a.modalApi)==null?void 0:x.onClosed()}),onEscapeKeyDown:Ie,onFocusOutside:pe,onInteractOutside:Ve,onOpenAutoFocus:Fe,onOpened:y[3]||(y[3]=()=>{var x;return(x=a.modalApi)==null?void 0:x.onOpened()}),onPointerDownOutside:Xe},{default:g(()=>[I(t(xt),{ref_key:"headerRef",ref:f,class:S(t(B)("px-5 py-4",{"border-b":t(d),hidden:!t(P),"cursor-move select-none":ue.value},t(T)))},{default:g(()=>[t(K)?(u(),m(t(ke),{key:0,class:"text-left"},{default:g(()=>[_(a.$slots,"title",{},()=>[R(Y(t(K))+" ",1),t(de)?_(a.$slots,"titleTooltip",{key:0},()=>[I(t(dt),{"trigger-class":"pb-1"},{default:g(()=>[R(Y(t(de)),1)]),_:1})]):k("",!0)])]),_:3})):k("",!0),t(E)?(u(),m(t(_e),{key:1},{default:g(()=>[_(a.$slots,"description",{},()=>[R(Y(t(E)),1)])]),_:3})):k("",!0),!t(K)||!t(E)?(u(),m(t(ut),{key:2},{default:g(()=>[t(K)?k("",!0):(u(),m(t(ke),{key:0})),t(E)?k("",!0):(u(),m(t(_e),{key:1}))]),_:1})):k("",!0)]),_:3},8,["class"]),Ze("div",{ref_key:"wrapperRef",ref:l,class:S(t(B)("relative min-h-40 flex-1 overflow-y-auto p-3",t(Z),{"overflow-hidden":t(M)}))},[t(M)?(u(),m(t(ht),{key:0,class:"size-full h-auto min-h-full",spinning:""})):k("",!0),_(a.$slots,"default")],2),t(q)?(u(),m(t(pt),{key:0,class:"hover:bg-accent hover:text-accent-foreground text-foreground/80 flex-center absolute right-10 top-3 hidden size-6 rounded-full px-1 text-lg opacity-70 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none sm:block",onClick:Te},{default:g(()=>[t(L)?(u(),m(t(gt),{key:0,class:"size-3.5"})):(u(),m(t(yt),{key:1,class:"size-3.5"}))]),_:1})):k("",!0),t(te)?(u(),m(t(bt),{key:1,ref_key:"footerRef",ref:C,class:S(t(B)("flex-row items-center justify-end p-2",{"border-t":t(d)},t(oe)))},{default:g(()=>[_(a.$slots,"prepend-footer"),_(a.$slots,"footer",{},()=>[t(Se)?(u(),m(ge(t(o).DefaultButton||t(Ce)),{key:0,variant:"ghost",onClick:y[0]||(y[0]=()=>{var x;return(x=a.modalApi)==null?void 0:x.onCancel()})},{default:g(()=>[_(a.$slots,"cancelText",{},()=>[R(Y(t(w)||t(r)("cancel")),1)])]),_:3})):k("",!0),t(Ee)?(u(),m(ge(t(o).PrimaryButton||t(Ce)),{key:1,loading:t(W),onClick:y[1]||(y[1]=()=>{var x;return(x=a.modalApi)==null?void 0:x.onConfirm()})},{default:g(()=>[_(a.$slots,"confirmText",{},()=>[R(Y(t(Q)||t(r)("confirm")),1)])]),_:3},8,["loading"])):k("",!0)]),_(a.$slots,"append-footer")]),_:3},8,["class"])):k("",!0)]),_:3},8,["class","modal","open","show-close"])]}),_:3},8,["open"])}}});class Mt{constructor(e={}){z(this,"api");z(this,"state");z(this,"sharedData",{payload:{}});z(this,"store");const h=e,{connectedComponent:o,onBeforeClose:n,onCancel:l,onClosed:i,onConfirm:f,onOpenChange:C,onOpened:c}=h,r=X(h,["connectedComponent","onBeforeClose","onCancel","onClosed","onConfirm","onOpenChange","onOpened"]),p={bordered:!0,centered:!1,class:"",closeOnClickModal:!0,closeOnPressEscape:!0,confirmLoading:!1,contentClass:"",draggable:!1,footer:!0,footerClass:"",fullscreen:!1,fullscreenButton:!0,header:!0,headerClass:"",isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,showCancelButton:!0,showConfirmButton:!0,title:""};this.store=new ft(v(v({},p),r),{onUpdate:()=>{var w,b,D;const d=this.store.state;(d==null?void 0:d.isOpen)===((w=this.state)==null?void 0:w.isOpen)?this.state=d:(this.state=d,(D=(b=this.api).onOpenChange)==null||D.call(b,!!(d!=null&&d.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:n,onCancel:l,onClosed:i,onConfirm:f,onOpenChange:C,onOpened:c},et(this)}batchStore(e){this.store.batch(e)}close(){var o,n,l;((l=(n=(o=this.api).onBeforeClose)==null?void 0:n.call(o))!=null?l:!0)&&this.store.setState(i=>F(v({},i),{isOpen:!1}))}getData(){var e,o;return(o=(e=this.sharedData)==null?void 0:e.payload)!=null?o:{}}onCancel(){var e,o;this.api.onCancel?(o=(e=this.api).onCancel)==null||o.call(e):this.close()}onClosed(){var e,o;this.state.isOpen||(o=(e=this.api).onClosed)==null||o.call(e)}onConfirm(){var e,o;(o=(e=this.api).onConfirm)==null||o.call(e)}onOpened(){var e,o;this.state.isOpen&&((o=(e=this.api).onOpened)==null||o.call(e))}open(){this.store.setState(e=>F(v({},e),{isOpen:!0}))}setData(e){this.sharedData.payload=e}setState(e){tt(e)?this.store.setState(e):this.store.setState(o=>v(v({},o),e))}}const we=Symbol("VBEN_MODAL_INJECT");function Lt(s={}){var C;const{connectedComponent:e}=s;if(e){const c=xe({});return[O((p,{attrs:h,slots:d})=>(Oe(we,{extendApi(w){Object.setPrototypeOf(c,w)},options:s}),Dt(c,v(v(v({},p),h),d)),()=>ve(e,v(v({},p),h),d)),{inheritAttrs:!1,name:"VbenParentModal"}),c]}const o=be(we,{}),n=v(v({},o.options),s);n.onOpenChange=c=>{var r,p,h;(r=s.onOpenChange)==null||r.call(s,c),(h=(p=o.options)==null?void 0:p.onOpenChange)==null||h.call(p,c)};const l=new Mt(n),i=l;i.useStore=c=>mt(l.store,c);const f=O((c,{attrs:r,slots:p})=>()=>ve(Ot,F(v(v({},c),r),{modalApi:i}),p),{inheritAttrs:!1,name:"VbenModal"});return(C=o.extendApi)==null||C.call(o,i),[f,i]}function Dt(s,e){return ae(this,null,function*(){var l;if(!e||Object.keys(e).length===0)return;yield Be();const o=(l=s==null?void 0:s.store)==null?void 0:l.state;if(!o)return;const n=new Set(Object.keys(o));for(const i of Object.keys(e))n.has(i)&&!["class"].includes(i)&&console.warn(`[Vben Modal]: When 'connectedComponent' exists, do not set props or slots '${i}', which will increase complexity. If you need to modify the props of Modal, please use useVbenModal or api.`)})}export{vt as X,Lt as u};
