import{ao as e,k as t,aP as r,l as o,ay as a,j as i}from"./chunks/framework.C8U7mBUf.js";const n=JSON.parse('{"title":"关于 Vben Admin","description":"","frontmatter":{},"headers":[],"relativePath":"guide/introduction/vben.md","filePath":"guide/introduction/vben.md"}');const s=e({name:"guide/introduction/vben.md"},[["render",function(e,n,s,l,d,c){const h=a("NolebaseGitContributors"),g=a("NolebaseGitChangelog");return i(),t("div",null,[n[0]||(n[0]=r('<h1 id="关于-vben-admin" tabindex="-1">关于 Vben Admin <a class="header-anchor" href="#关于-vben-admin" aria-label="Permalink to &quot;关于 Vben Admin&quot;">​</a></h1><div class="info custom-block"><p class="custom-block-title">你正在阅读的是 <a href="https://github.com/vbenjs/vue-vben-admin" target="_blank" rel="noreferrer">Vben Admin</a> <code>5.0</code>版本的文档！</p><ul><li>Vben Admin 2.x 目前已存档，仅进行重大问题修复。</li><li>新版本与旧版本不兼容，如果你使用的是旧版本（v2、v3），请查看 <a href="https://doc.vvbin.cn" target="_blank" rel="noreferrer">Vue Vben Admin 2.x 文档</a></li><li>如发现文档有误，欢迎提交 <a href="https://github.com/vbenjs/vue-vben-admin/issues" target="_blank" rel="noreferrer">issue</a> 帮助我们改进。</li><li>如果你只是想体验一下，你可以查看<a href="./quick-start.html">快速开始</a>。</li></ul></div><p><a href="https://github.com/vbenjs/vue-vben-admin" target="_blank" rel="noreferrer">Vben Admin</a> 是一个基于 <a href="https://github.com/vuejs/core" target="_blank" rel="noreferrer">Vue3.0</a>、<a href="https://github.com/vitejs/vite" target="_blank" rel="noreferrer">Vite</a>、 <a href="https://www.typescriptlang.org/" target="_blank" rel="noreferrer">TypeScript</a> 的中后台解决方案，目标是为开发中大型项目提供开箱即用的解决方案。包括二次封装组件、utils、hooks、动态菜单、权限校验、多主题配置、按钮级别权限控制等功能。项目会使用前端较新的技术栈，可以作为项目的启动模板，以帮助你快速搭建企业级中后台产品原型。也可以作为一个示例，用于学习 <code>vue3</code>、<code>vite</code>、<code>ts</code> 等主流技术。该项目会持续跟进最新技术，并将其应用在项目中。</p><h2 id="特点" tabindex="-1">特点 <a class="header-anchor" href="#特点" aria-label="Permalink to &quot;特点&quot;">​</a></h2><ul><li><strong>最新技术栈</strong>：使用 <code>Vue3</code>、<code>Vite</code>、<code>TypeScript</code> 等前端前沿技术开发。</li><li><strong>国际化</strong>：内置完善的国际化方案，支持多语言切换。</li><li><strong>权限验证</strong>：完善的权限验证方案，按钮级别权限控制。</li><li><strong>多主题</strong>：内置多种主题配置和黑暗模式，满足个性化需求。</li><li><strong>动态菜单</strong>：支持动态菜单，可以根据权限配置显示菜单。</li><li><strong>Mock 数据</strong>：基于 Nitro 的本地高性能 Mock 数据方案。</li><li><strong>组件丰富</strong>：提供了丰富的组件，可以满足大部分的业务需求。</li><li><strong>规范</strong>：代码规范，使用 <code>ESLint</code>、<code>Prettier</code>、<code>Stylelint</code>、<code>Publint</code>、<code>CSpell</code> 等工具保证代码质量。</li><li><strong>工程化</strong>：使用 <code>Pnpm Monorepo</code>、<code>TurboRepo</code>、<code>Changeset</code> 等工具，提高开发效率。</li><li><strong>多UI库支持</strong>：支持 <code>Ant Design Vue</code>、<code>Element Plus</code>、<code>Naive</code> 等主流 UI 库，不再限制于特定框架。</li></ul><h2 id="浏览器支持" tabindex="-1">浏览器支持 <a class="header-anchor" href="#浏览器支持" aria-label="Permalink to &quot;浏览器支持&quot;">​</a></h2><p><strong>本地开发</strong>推荐使用<code>Chrome 最新版</code>浏览器，<strong>不支持</strong><code>Chrome 80</code>以下版本。</p><p><strong>生产环境</strong>支持现代浏览器，不支持 IE。</p><table tabindex="0"><thead><tr><th style="text-align:center;"><a href="http://godban.github.io/browsers-support-badges/" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/archive/internet-explorer_9-11/internet-explorer_9-11_48x48.png" alt="IE" width="24px" height="24px"></a>IE</th><th style="text-align:center;"><a href="http://godban.github.io/browsers-support-badges/" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px"></a>Edge</th><th style="text-align:center;"><a href="http://godban.github.io/browsers-support-badges/" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px"></a>Firefox</th><th style="text-align:center;"><a href="http://godban.github.io/browsers-support-badges/" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px"></a>Chrome</th><th style="text-align:center;"><a href="http://godban.github.io/browsers-support-badges/" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px"></a>Safari</th></tr></thead><tbody><tr><td style="text-align:center;">不支持</td><td style="text-align:center;">last 2 versions</td><td style="text-align:center;">last 2 versions</td><td style="text-align:center;">last 2 versions</td><td style="text-align:center;">last 2 versions</td></tr></tbody></table><h2 id="贡献" tabindex="-1">贡献 <a class="header-anchor" href="#贡献" aria-label="Permalink to &quot;贡献&quot;">​</a></h2><ul><li><a href="https://github.com/vbenjs/vue-vben-admin" target="_blank" rel="noreferrer">Vben Admin</a> 还在持续更新中，本项目欢迎您的参与，共同维护，逐步完善，打造更好的中后台解决方案。</li><li>如果你想加入我们，可以提供有价值的建议或者参与讨论，协助解决 issue，- 如果你想加入我们，可以提供有价值的建议或者参与讨论，协助解决 issue，我们会根据你的活跃度邀请你加入。。</li></ul><div class="info custom-block"><p class="custom-block-title">加入我们</p><ul><li>长期提交 <code>PR</code>。</li><li>提供一些好的建议。</li><li>参与讨论，帮助解决一些 <code>issue</code>。</li><li>共同维护文档。</li></ul></div>',12)),o(h),o(g)])}]]);export{n as __pageData,s as default};
