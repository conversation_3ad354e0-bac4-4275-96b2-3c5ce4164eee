import * as Cesium from 'cesium';
export default {
  geoserver: {
    wms: {
      url: 'http://localhost:9999/geoserver/wms',
      layers: 'test:layer',
      parameters: {
        service: 'WMS',
        format: 'image/png',
        transparent: true,
        format_options: 'dpi:300',
        srs: 'EPSG:4326'
      },
    },
    wmts: {
      url: 'http://localhost:9999/geoserver/gwc/service/wmts/rest/test:layer/{style}/{TileMatrixSetID}/{TileMatrixSetID}:{TileMatrix}/{TileRow}/{TileColumn}?format:image/png',
      layer: 'test:layer',
      style: '',
      format: 'image/png',
      tileMatrixSetID: 'EPSG:900913',
    },
    tms: {
      url: 'http://localhost:9999/geoserver/gwc/service/tms/1.0.0/test:layer@EPSG：900913@png/{z} /{x}/{reverseY}',
      tilingScheme: new Cesium.GeographicTilingScheme(),
      fileExtension: 'png',
      minimumLevel: 0,
      maximumLevel: 19,
    },
    geojson: {
      options: {
        clampToGround: true,
      },
      url: 'http://fzzt.fzjhdn.com:18086/geoserver/anxi_xiaoban/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=anxi_xiaoban%3Aanxilinban&maxFeatures=50&outputFormat=application%2Fjson',
    },
    kml: {},
  },
  arcgis: {
    mapserver: {
      url: 'http://localhost:9999/arcgis/mapserver',
      usePreCachedTilesIfAvailable: false,
      //enablePickFeatures:false
    },
  },
  bing: {
    url: 'https://dev.virtualearth.net/',
    key: 'AmXdbd8UeUJtaRSn7yVwyXgQlBBUqliLbHpgn2c76DfuHwAXfRrgS5qwfHU6Rhm8',
    mapStyle: Cesium.BingMapsStyle.AERIAL,
  },
  '3dtiles': {
    url: 'http://fzzt.fzjhdn.com:19003/keshan3DTile/tileset.json',
    skipLevelOfDetail: true,
    baseScreenSpaceError: 1024,
    skipLevels: 1,
    skipScreenSpaceErrorFactor: 16,
    //         immediatelyLoadDesiredLevelOfDetail: false,
    //         loadSiblings: false,
    //      	maximumScreenSpaceError:32,
    //         cullWithChildrenBounds: true,
    //         cullRequestsWhileMoving: false,
    //         cullRequestsWhileMovingMultiplier: 10, // 值越小能够更快的剔除
    //         preloadWhenHidden: true,
    //         preloadFlightDestinations: true,
    //         preferLeaves: true,
    //         maximumMemoryUsage: 512, // 内存分配变小有利于倾斜摄影数据回收，提升性能体验
    //         progressiveResolutionHeightFraction: 0.3, // 数值偏于0能够让初始加载变得模糊
    //         foveatedScreenSpaceError: true,
    //         foveatedConeSize: 0.1,
    //         foveatedMinimumScreenSpaceErrorRelaxation: 0.0,
    //         foveatedTimeDelay: 0.2,
    //         dynamicScreenSpaceErrorDensity:10, // 数值加大，能让周边加载变快
    //         dynamicScreenSpaceErrorFactor: 4, // 不知道起了什么作用没，反正放着吧先
    //         dynamicScreenSpaceError:true,// 根据测试，有了这个后，会在真正的全屏加载完之后才清晰化房屋

    // skipLevelOfDetail: true,
    // baseScreenSpaceError: 1024,
    // skipLevels: 1,
    // skipScreenSpaceErrorFactor: 16,
    // immediatelyLoadDesiredLevelOfDetail: false,
    // loadSiblings:false,
    // maximumScreenSpaceError: 64,
    // cullWithChildrenBounds: true,
    // cullRequestsWhileMoving: false,
    // cullRequestsWhileMovingMultiplier: 20, // 值越小能够更快的剔除
    // preloadWhenHidden: true,
    // preloadFlightDestinations: true,
    // preferLeaves: true,
    // maximumMemoryUsage: 512, // 内存分配变小有利于倾斜摄影数据回收，提升性能体验
    // progressiveResolutionHeightFraction: 0.3, // 数值偏于0能够让初始加载变得模糊
    // foveatedScreenSpaceError: true,
    // foveatedConeSize: 0.1,
    // foveatedMinimumScreenSpaceErrorRelaxation: 0.0,
    // foveatedTimeDelay: 0.2,
    // dynamicScreenSpaceErrorDensity: 20, // 数值加大，能让周边加载变快
    // dynamicScreenSpaceErrorFactor: 4, // 不知道起了什么作用没，反正放着吧先
    // dynamicScreenSpaceError: true,// 根据测试，有了这个后，会在真正的全屏加载完之后才清晰化房屋
  },
  terrain: {
    /**
     * 提供者
     */
    provider: '', //createWorldTerrain
    url: '',
    requestVertexNormals: true,
    requestWaterMask: true,
  },
};
