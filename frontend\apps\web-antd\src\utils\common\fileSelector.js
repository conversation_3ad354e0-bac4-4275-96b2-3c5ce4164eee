/**
 * 使用方法：
 *  openLocalREsource(function(fileList){
 *    console.log('文件选择的结果=>'，fileList);
 *  },'file');
 *
 *  openLocalREsource(function(fileList){
 *    console.log('文件夹选择的结果=>'，fileList);
 *  },'folder');
 */
let fileSelector = {
  open: (change, type, options) => {
    options = {
      multiple: false,
      maxSize: 0,
      accept: '',
      ...options,
    };
    const fileDom = document.querySelector(`#${type}InputDom`);

    // 限制类型
    if (['file', 'folder'].indexOf(type) < 0) {
      return false;
    }

    // 判断结果，进行移除fileDom
    if (fileDom) {
      document.body.removeChild(fileDom);
    }

    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    if (options.multiple) input.setAttribute('multiple', 'multiple');
    //accept:'*.png,*.json'
    if (options.accept) input.setAttribute('accept', options.accept);
    input.setAttribute('style', 'width:0;height:0;display:none;');
    input.id = type + 'InputDom';
    // 判断是不是选择文件夹
    if (type === 'folder') {
      input.setAttribute('webkitdirectory', '');
    }
    document.body.appendChild(input);
    input.onchange = function (e) {
      document.body.removeChild(input);
      if (options.maxSize) {
        let flag = false;
        e.target.files.forEach((file) => {
          flag = flag || file.size > options.maxSize;
        });
        if (flag) {
          change(null, true);
          return;
        }
      }
      change(e.target.files);
    };
    input.click();
  },
  openFile(change, options) {
    this.open(change, 'file', options);
  },
  openFolder(change) {
    this.open(change, 'folder', options);
  },
};

export default fileSelector;
