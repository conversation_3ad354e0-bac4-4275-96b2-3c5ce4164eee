import { RegionUtils } from '#/utils/regionUtils';

export const columns = [
  {
    title: '项目名称',
    dataIndex: 'projectName',
    field: 'projectName',
    width: 280,
  },
  {
    title: '项目编号',
    dataIndex: 'projectCode',
    field: 'projectCode',
    width: 150,
  },

  {
    title: '项目性质',
    dataIndex: 'projectNature',
    field: 'projectNature',
    width: 120,
    slots: { default: 'projectNature' },
  },

  {
    title: '归属部门',
    dataIndex: 'belongDept',
    field: 'belongDept',
    width: 120,
  },
  {
    title: '项目类型',
    dataIndex: 'projectType',
    field: 'projectType',
    width: 120,
  },
  {
    title: '项目地区',
    dataIndex: 'regionName',
    field: 'regionName',
    width: 180,
    slots: { default: 'regionName' },
  },
  {
    title: '项目负责人',
    dataIndex: 'projectManager',
    field: 'projectManager',
    width: 120,
  },

  {
    title: '创建时间',
    dataIndex: 'timeCreated',
    field: 'timeCreated',
    width: 180,
  },
  {
    title: '创建人',
    dataIndex: 'userNameCreated',
    field: 'userNameCreated',
    width: 120,
  },

  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 250,
  },
];

export const searchFormSchema = [
  {
    label: '项目名称',
    fieldName: 'projectName',
    component: 'Input',
  },
  {
    label: '项目编号',
    fieldName: 'projectCode',
    component: 'Input',
  },
  {
    label: '项目性质',
    fieldName: 'projectNatures',
    component: 'Select',
    componentProps: {
      options: [
        { label: '内外业均有', value: 0 },
        { label: '外业项目', value: 1 },
        { label: '内业项目', value: 2 },
      ],
      mode: 'multiple',
    },
  },
  {
    label: '项目类型',
    fieldName: 'projectType',
    component: 'Input',
  },

  {
    label: '项目地区',
    fieldName: 'regionCode',
    component: 'Cascader',
    componentProps: {
      options: RegionUtils.getProvinces(),
      changeOnSelect: true,
      placeholder: '请选择',
    },
  },

];
const getProjectData = (values,field) => {
  const projectNameSchema = formSchema.find(item => item.fieldName === 'projectName');
  const selectedProject = projectNameSchema.componentProps.options.find(option => option.value === values.projectName);
  if (selectedProject && selectedProject.data) {
   return selectedProject.data;
  }
  return null;
}
const getProjectValue = (values,field) => {
  let data = getProjectData(values,field);
  return data ? data[field] : '';
}




export const formSchema = [
  {
    fieldName: 'projectName',
    label: '项目名称',
    component: 'Select',
    componentProps: {
      showSearch: true,
      filterOption: (input, option) => {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      options: [],
      placeholder: '请输入并筛选项目名称',
      style: { width: '100%' },
    },
    rules: 'required'
  },
  {
    fieldName: 'projectCode',
    label: '项目编号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入项目编号',
    },
    disabled: true,
    dependencies: {
      trigger(values,formApi){
        let data = getProjectData(values,'projectCode');
        if(data){
          formApi.setFieldValue('projectCode', data.projectCode);
          formApi.setFieldValue('projectInfoId', data.id);
        }
      },
      triggerFields: ['projectName'],
    }
  },
  {
    fieldName: 'projectType',
    label: '项目类型',
    // rules: 'required',
    component: 'Input',
    componentProps: {
      placeholder: '请输入项目类型',
    },
    disabled: true,
    dependencies: {
      trigger(values,formApi){
        let projectValue = getProjectValue(values,'projectType');
        if(projectValue){
          formApi.setFieldValue('projectType', projectValue);
        }
      },
      triggerFields: ['projectName'],
    }
  },
  {
    fieldName: 'commissionUnit',
    label: '委托单位',
    // rules: 'required',
    component: 'Input',
    componentProps: {
      placeholder: '请输入委托单位',
    },
    disabled: true,
    dependencies: {
      trigger(values,formApi){
        let projectValue = getProjectValue(values,'commissionUnit');
        if(projectValue){
          formApi.setFieldValue('commissionUnit', projectValue);
        }
      },
      triggerFields: ['projectName'],
    }
  },
  {
    fieldName: 'belongDept',
    label: '归属部门',
    // rules: 'required',
    component: 'Input',
    componentProps: {
      placeholder: '请输入归属部门',
    },
    disabled: true,
    dependencies: {
      trigger(values,formApi){
        let projectValue = getProjectValue(values,'belongDept');
        if(projectValue){
          formApi.setFieldValue('belongDept', projectValue);
        }
      },
      triggerFields: ['projectName'],
    }
  },



  {
    fieldName: 'operationDept',
    label: '作业参与部门',
    // rules: 'required',
    component: 'Select',
    componentProps: {
      placeholder: '请选择作业参与部门',
      options: [
        { label: '智慧国土部', value: '001' },
        { label: '遥感与航测部', value: '002' },
        { label: '工程部', value: '003' },
        { label: '海洋与制图部', value: '004' },
        { label: '生态修复设计部', value: '005' },
        { label: '国土空间规划院', value: '006' },
        { label: '研发部', value: '007' },
      ],
    },
  },
  {
    fieldName: 'projectNature',
    label: '项目性质',
    component: 'Select',
    componentProps: {
      options: [
        { label: '内外业均有', value: 0 },
        { label: '外业项目', value: 1 },
        { label: '内业项目', value: 2 },
      ],
      placeholder: '请选择项目性质',
    },
    // rules: 'selectRequired',
  },

  {
    label: '项目地区',
    fieldName: 'regionCode',
    component: 'Cascader',
    componentProps: {
      options: RegionUtils.getProvinces(),
      changeOnSelect: true,
      placeholder: '请选择项目地区',
    },
    // rules: 'selectRequired',
  },
  {
    fieldName: 'projectManager',
    label: '项目负责人',
    // rules: 'required',
    component: 'Input',
    componentProps: {
      placeholder: '请输入项目负责人',
    },
  },
  {
    fieldName: 'beginDate',
    label: '开工日期',
    // rules: 'required',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择开工日期',
      style: 'width: 100%',
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
    },
  },
  {
    fieldName: 'endDate',
    label: '完工日期',
    // rules: 'required',
    component: 'DatePicker',
    componentProps: {
      placeholder: '请选择完工日期',
      style: 'width: 100%',
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
    },
  },
  {
    fieldName: 'organizationalAcceptance',
    label: '验收状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未验收', value: 0 },
        { label: '已验收', value: 1 },
      ],
      placeholder: '请选择验收状态',
    },
    // rules: 'selectRequired',
  },
  {
    fieldName: 'projectContent',
    label: '项目内容',
    component: 'Textarea',
    componentProps: {
      placeholder: '项目地点、内容工作内容和工作描述',
      rows: 3,
      style: {
        width: '1000px',
        maxWidth: '1000px',
      },
    },
  },
];
