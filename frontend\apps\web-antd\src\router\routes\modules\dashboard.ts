import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component:  () => import('#/views/dashboard/analytics/index.vue'),
    meta: {
      icon: 'lucide:layout-template',
      order: -1,
      title:'概览',
    },
    name: 'Dashboard',
    path: '/analytics',
  },
];

export default routes;
