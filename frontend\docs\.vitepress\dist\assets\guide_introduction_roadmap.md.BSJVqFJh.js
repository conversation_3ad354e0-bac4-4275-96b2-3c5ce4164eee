import{ao as a,k as e,z as t,I as r,l as o,ay as i,j as n}from"./chunks/framework.C8U7mBUf.js";const s=JSON.parse('{"title":"路线图","description":"","frontmatter":{},"headers":[],"relativePath":"guide/introduction/roadmap.md","filePath":"guide/introduction/roadmap.md"}');const d=a({name:"guide/introduction/roadmap.md"},[["render",function(a,s,d,l,u,m){const c=i("NolebaseGitContributors"),h=i("NolebaseGitChangelog");return n(),e("div",null,[s[0]||(s[0]=t("h1",{id:"路线图",tabindex:"-1"},[r("路线图 "),t("a",{class:"header-anchor",href:"#路线图","aria-label":'Permalink to "路线图"'},"​")],-1)),s[1]||(s[1]=t("p",null,"TODO:",-1)),o(c),o(h)])}]]);export{s as __pageData,d as default};
