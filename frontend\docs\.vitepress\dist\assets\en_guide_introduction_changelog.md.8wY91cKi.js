import{ao as e,k as a,z as n,I as o,l as t,ay as r,j as i}from"./chunks/framework.C8U7mBUf.js";const l=JSON.parse('{"title":"CHANGE LOG","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/introduction/changelog.md","filePath":"en/guide/introduction/changelog.md"}');const s=e({name:"en/guide/introduction/changelog.md"},[["render",function(e,l,s,d,c,g){const h=r("NolebaseGitContributors"),u=r("NolebaseGitChangelog");return i(),a("div",null,[l[0]||(l[0]=n("h1",{id:"change-log",tabindex:"-1"},[o("CHANGE LOG "),n("a",{class:"header-anchor",href:"#change-log","aria-label":'Permalink to "CHANGE LOG"'},"​")],-1)),l[1]||(l[1]=n("p",null,"TODO",-1)),t(h),t(u)])}]]);export{l as __pageData,s as default};
