import{ao as a,k as e,z as t,I as r,l as i,ay as o,j as s}from"./chunks/framework.C8U7mBUf.js";const n=JSON.parse('{"title":"布局","description":"","frontmatter":{},"headers":[],"relativePath":"guide/in-depth/layout.md","filePath":"guide/in-depth/layout.md"}');const d=a({name:"guide/in-depth/layout.md"},[["render",function(a,n,d,l,h,u){const m=o("NolebaseGitContributors"),c=o("NolebaseGitChangelog");return s(),e("div",null,[n[0]||(n[0]=t("h1",{id:"布局",tabindex:"-1"},[r("布局 "),t("a",{class:"header-anchor",href:"#布局","aria-label":'Permalink to "布局"'},"​")],-1)),i(m),i(c)])}]]);export{n as __pageData,d as default};
