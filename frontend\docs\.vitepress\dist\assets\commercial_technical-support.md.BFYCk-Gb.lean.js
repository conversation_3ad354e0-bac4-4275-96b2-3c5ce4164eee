import{ao as e,k as a,aP as r,l as t,ay as s,j as i}from"./chunks/framework.C8U7mBUf.js";const o=JSON.parse('{"title":"技术支持","description":"","frontmatter":{},"headers":[],"relativePath":"commercial/technical-support.md","filePath":"commercial/technical-support.md"}');const n=e({name:"commercial/technical-support.md"},[["render",function(e,o,n,l,c,u){const h=s("NolebaseGitContributors"),m=s("NolebaseGitChangelog");return i(),a("div",null,[o[0]||(o[0]=r('<h1 id="技术支持" tabindex="-1">技术支持 <a class="header-anchor" href="#技术支持" aria-label="Permalink to &quot;技术支持&quot;">​</a></h1><h2 id="问题反馈" tabindex="-1">问题反馈 <a class="header-anchor" href="#问题反馈" aria-label="Permalink to &quot;问题反馈&quot;">​</a></h2><p>在使用项目的过程中，如果遇到问题，你可以先详细阅读本文档，未找到解决方案时，可以通过以下方式获取技术支持：</p><ul><li>通过 <a href="https://github.com/vbenjs/vue-vben-admin/issues" target="_blank" rel="noreferrer">GitHub Issues</a></li><li>通过 <a href="https://github.com/vbenjs/vue-vben-admin/discussions" target="_blank" rel="noreferrer">GitHub Discussions</a></li></ul>',4)),t(h),t(m)])}]]);export{o as __pageData,n as default};
