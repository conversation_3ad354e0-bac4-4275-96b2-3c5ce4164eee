{"name": "@vben/eslint-config", "version": "5.0.0", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/lint-configs/eslint-config"}, "license": "MIT", "type": "module", "scripts": {"stub": "pnpm unbuild --stub"}, "files": ["dist"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs"}}, "dependencies": {"eslint-config-turbo": "catalog:", "eslint-plugin-command": "catalog:", "eslint-plugin-import-x": "catalog:"}, "devDependencies": {"@eslint/js": "catalog:", "@types/eslint": "catalog:", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "eslint": "catalog:", "eslint-plugin-eslint-comments": "catalog:", "eslint-plugin-jsdoc": "catalog:", "eslint-plugin-jsonc": "catalog:", "eslint-plugin-n": "catalog:", "eslint-plugin-no-only-tests": "catalog:", "eslint-plugin-perfectionist": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-regexp": "catalog:", "eslint-plugin-unicorn": "catalog:", "eslint-plugin-unused-imports": "catalog:", "eslint-plugin-vitest": "catalog:", "eslint-plugin-vue": "catalog:", "globals": "catalog:", "jsonc-eslint-parser": "catalog:", "vue-eslint-parser": "catalog:"}}