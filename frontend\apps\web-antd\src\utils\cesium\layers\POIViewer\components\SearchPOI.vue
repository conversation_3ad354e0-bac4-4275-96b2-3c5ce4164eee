<script>
import {
  Button as AButton,
  Input as AInput,
  Table as ATable,
} from 'ant-design-vue';
import * as Cesium from 'cesium';

import core from '../../../core';
import { GISMap } from '../../../index.js';
import { poiViewer } from '../index.js';

export default {
  components: {
    AButton,
    AInput,
    ATable,
  },
  props: {
    poiType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      searchKey: '',
      selectValue: null,
      poilist: [],
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          ellipsis: true,
        },
        {
          title: '经度',
          dataIndex: 'lon',
          key: 'lon',
          ellipsis: true,
          width: 100,
        },
        {
          title: '纬度',
          dataIndex: 'lat',
          key: 'lat',
          ellipsis: true,
          width: 100,
        },
        {
          title: '图标',
          dataIndex: 'image',
          key: 'image',
          width: 46,
          align: 'center',
          scopedSlots: {
            customRender: 'image',
          },
        },
        {
          title: '操作',
          dataIndex: 'operation',
          key: 'operation',
        },
      ],
    };
  },
  mounted() {
    this.search();
    poiViewer.on('dataChange', (poiLayer) => {
      if (poiLayer.type == this.poiType) this.search();
    });
    poiViewer.on('select', (poi) => {
      if (poi && poi.refData) this.selectValue = poi.refData.id;
    });
  },
  methods: {
    // 查询POI
    search() {
      const poiLayers = poiViewer.poiLayers.filter(
        (a) => a.type == this.poiType,
      );
      if (poiLayers && Array.isArray(poiLayers) && poiLayers.length > 0) {
        const poiLayer = poiLayers[0];
        console.log(poiLayer);
        if (poiLayer && poiLayer.refData && Array.isArray(poiLayer.refData)) {
          this.poilist = poiLayer.refData
            .filter(
              (a) =>
                !this.searchKey || (a.name && a.name.includes(this.searchKey)),
            )
            .map((item) => {
              return {
                id: item.id,
                name: item.name,
                image: item.resUrl,
                type: item.tagType,
                lon: Number(item.longitude.toFixed(6)),
                lat: Number(item.latitude.toFixed(6)),
                editable: item.editable,
              };
            });
        }
      }
    },
    rowClassName(record, index) {
      return record.id == this.selectValue ? 'table-striped' : null;
    },
    rowClick(record, index) {
      const viewer = GISMap.viewer;
      return {
        onClick: () => {
          this.selectValue = record.id;
          console.log('rowClick', record.lon, record.lat, index);

          viewer.camera.flyTo({
            destination: core.computeExtent(
              [Cesium.Cartesian3.fromDegrees(record.lon, record.lat, 0)],
              1,
            ),
          });
        },
      };
    },
    // 删除POI
    deletePOI(row) {
      /* console.log("deletePOI",row)
				api.deleteLayerTag({id:row.id}).then(res =>
					{
						this.search()
						MyLayerService.deletePOI(row.id)
					}
				) */
      poiViewer.remove(row.id);
    },
    // 移动POI
    async movePOI(row) {
      // MyLayerService.movePOI(row)
      const data = await poiViewer.moveStart(row.id);
      await poiViewer.save(data);
    },
    editPOI(row) {
      // MyLayerService.editPOI(row.id)
      poiViewer.showEditor(row.id);
    },
    handleChange(e) {
      console.log('handleChange', e);
    },
  },
};
</script>

<template>
  <div class="region-container">
    <div class="condition-title b">查询条件</div>
    <div class="searchCondition">
      <AInput
        v-model:value="searchKey"
        placeholder="请输入兴趣点名称"
        style="
          width: 180px;
          margin-right: 10px;
          color: #fff;
          background: transparent;
        "
        @keypress.enter="search"
      />
      <AButton
        style="display: block; color: #fff; background: transparent"
        type="primary"
        @click.stop="search"
      >
        查询
      </AButton>
    </div>
    <div class="condition-title b">兴趣点列表</div>
    <ATable
      v-if="poilist.length > 0"
      :columns="columns"
      :custom-row="rowClick"
      :data-source="poilist"
      :row-class-name="rowClassName"
      bordered
      size="middle"
      sticky
    >
      <template #bodyCell="{ column, record }">
        <template
          v-if="column.key === 'image'"
          style="justify-content: space-around"
        >
          <span slot="image" slot-scope="text, record" style="width: 40px">
            <img :src="record.image" style="width: 24px; heigth: 24px" />
          </span>
        </template>
        <template
          v-if="column.key === 'operation'"
          style="justify-content: center"
        >
          <a
            v-if="record.editable"
            style="margin: 0 5px"
            title="删除"
            @click.prevent.stop="deletePOI(record)"
          >
            <span class="iconfont icon-act_qingkong"></span
          ></a>
          <a
            v-if="record.editable"
            style="margin: 0 5px"
            title="移动"
            @click.prevent.stop="movePOI(record)"
          >
            <span class="iconfont icon-jia"></span
          ></a>
          <a
            v-if="record.editable"
            style="margin: 0 5px"
            title="编辑"
            @click.prevent="editPOI(record)"
          >
            <span class="iconfont icon-bianji"></span
          ></a>
        </template>
      </template>
    </ATable>
    <div v-else class="condition-title">没有兴趣点记录</div>
  </div>
</template>
<style lang="less">
.region-container {
  margin: 0 auto;
  padding: 0 10px;

  .condition-title {
    color: #fff;
    line-height: 30px;
    font-size: 14px;

    &.b {
      font-weight: 700;
    }
  }

  .ant-table,
  .ant-table-thead > tr > th,
  .ant-table-header {
    background: transparent;
    color: #fff;
  }

  .ant-table-tbody > tr.ant-table-row:hover > td,
  .ant-table-tbody > tr > td.ant-table-cell-row-hover {
    //background: rgba(40,44,52,0.5);
    background: rgba(218, 214, 22, 0.493);
  }

  .ant-table-cell-scrollbar {
    box-shadow: unset;
  }

  .ant-table.ant-table-middle .ant-table-title,
  .ant-table.ant-table-middle .ant-table-footer,
  .ant-table.ant-table-middle .ant-table-thead > tr > th,
  .ant-table.ant-table-middle .ant-table-tbody > tr > td,
  .ant-table.ant-table-middle tfoot > tr > th,
  .ant-table.ant-table-middle tfoot > tr > td {
    padding: 4px 8px;
  }

  .ant-table-row.ant-table-row-level-0.table-striped {
    background: rgba(247, 242, 0, 0.493);
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    background: transparent;
  }

  .searchCondition {
    // background: transparent;
    // color: #fff;
    display: flex;
    justify-content: start;
    margin: 3px 0px 3px 0px;
  }
}
</style>
