<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    
    <title>Directory Explanation | Vben Admin</title>
    <meta name="description" content="Vben Admin & Enterprise level management system framework">
    <meta name="generator" content="VitePress v1.5.0">
    <link rel="preload stylesheet" href="/assets/style.PjDwLoZW.css" as="style">
    <link rel="preload stylesheet" href="/vp-icons.css" as="style">
    
    <script type="module" src="/assets/app.BR3tRqYw.js"></script>
    <link rel="preload" href="/assets/inter-roman-latin.Di8DUHzh.woff2" as="font" type="font/woff2" crossorigin="">
    <link rel="modulepreload" href="/assets/chunks/theme.TDvSnEYR.js">
    <link rel="modulepreload" href="/assets/chunks/framework.C8U7mBUf.js">
    <link rel="modulepreload" href="/assets/en_guide_project_dir.md.1HUpj9N1.lean.js">
    <meta content="Vbenjs Team" name="author">
    <meta content="vben, vitejs, vite, shacdn-ui, vue" name="keywords">
    <link href="/favicon.ico" rel="icon" type="image/svg+xml">
    <meta content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" name="viewport">
    <meta content="vben admin docs" name="keywords">
    <link href="/favicon.ico" rel="icon">
    <script id="check-dark-mode">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"dark",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
    <script id="check-mac-os">document.documentElement.classList.toggle("mac",/Mac|iPhone|iPod|iPad/i.test(navigator.platform));</script>
    <link rel="manifest" href="/manifest.webmanifest">
    <script id="vite-plugin-pwa:register-sw" src="/registerSW.js"></script>
  </head>
  <body>
    <div id="app"><!--[--><!--[--><!--[--><div class="Layout" data-v-8ee6be8d><!--[--><!--]--><!--[--><span tabindex="-1" data-v-6df81371></span><a href="#VPContent" class="VPSkipLink visually-hidden" data-v-6df81371> Skip to content </a><!--]--><!----><header class="VPNav" data-v-8ee6be8d data-v-7bd04492><div class="VPNavBar" data-v-7bd04492 data-v-8424412b><div class="wrapper" data-v-8424412b><div class="container" data-v-8424412b><div class="title" data-v-8424412b><div class="VPNavBarTitle has-sidebar" data-v-8424412b data-v-597c1cdc><a class="title" href="/en/" data-v-597c1cdc><!--[--><!--]--><!--[--><img class="VPImage logo" src="https://unpkg.com/@vbenjs/static-source@0.1.7/source/logo-v1.webp" alt data-v-0d18bd34><!--]--><span data-v-597c1cdc>Vben Admin</span><!--[--><!--]--></a></div></div><div class="content" data-v-8424412b><div class="content-body" data-v-8424412b><!--[--><!--]--><div class="VPNavBarSearch search" data-v-8424412b><!--[--><!----><div id="local-search"><button type="button" class="DocSearch DocSearch-Button" aria-label="Search"><span class="DocSearch-Button-Container"><span class="vp-icon DocSearch-Search-Icon"></span><span class="DocSearch-Button-Placeholder">Search</span></span><span class="DocSearch-Button-Keys"><kbd class="DocSearch-Button-Key"></kbd><kbd class="DocSearch-Button-Key">K</kbd></span></button></div><!--]--></div><nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu menu" data-v-8424412b data-v-1fe73b82><span id="main-nav-aria-label" class="visually-hidden" data-v-1fe73b82> Main Navigation </span><!--[--><!--[--><div class="VPFlyout VPNavBarMenuGroup active" data-v-1fe73b82 data-v-27308979><button type="button" class="button" aria-haspopup="true" aria-expanded="false" data-v-27308979><span class="text" data-v-27308979><!----><span data-v-27308979>Doc</span><span class="vpi-chevron-down text-icon" data-v-27308979></span></span></button><div class="menu" data-v-27308979><div class="VPMenu" data-v-27308979 data-v-b1bc7863><div class="items" data-v-b1bc7863><!--[--><!--[--><div class="VPMenuLink" data-v-b1bc7863 data-v-c01e5ae6><a class="VPLink link active" href="/en/guide/introduction/vben.html" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Guide</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuGroup" data-v-b1bc7863 data-v-85893c1c><p class="title" data-v-85893c1c>Historical Versions</p><!--[--><!--[--><div class="VPMenuLink" data-v-85893c1c data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://doc.vvbin.cn" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>2.x Version Documentation</span><!--]--></a></div><!--]--><!--]--></div><!--]--><!--]--></div><!--[--><!--]--></div></div></div><!--]--><!--[--><div class="VPFlyout VPNavBarMenuGroup" data-v-1fe73b82 data-v-27308979><button type="button" class="button" aria-haspopup="true" aria-expanded="false" data-v-27308979><span class="text" data-v-27308979><!----><span data-v-27308979>Demo</span><span class="vpi-chevron-down text-icon" data-v-27308979></span></span></button><div class="menu" data-v-27308979><div class="VPMenu" data-v-27308979 data-v-b1bc7863><div class="items" data-v-b1bc7863><!--[--><!--[--><div class="VPMenuGroup" data-v-b1bc7863 data-v-85893c1c><p class="title" data-v-85893c1c>Vben Admin</p><!--[--><!--[--><div class="VPMenuLink" data-v-85893c1c data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://www.vben.pro" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Demo Version</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-85893c1c data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://ant.vben.pro" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Ant Design Vue Version</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-85893c1c data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://naive.vben.pro" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Naive Version</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-85893c1c data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://ele.vben.pro" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Element Plus Version</span><!--]--></a></div><!--]--><!--]--></div><!--]--><!--[--><div class="VPMenuGroup" data-v-b1bc7863 data-v-85893c1c><p class="title" data-v-85893c1c>Others</p><!--[--><!--[--><div class="VPMenuLink" data-v-85893c1c data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://vben.vvbin.cn" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Vben Admin 2.x</span><!--]--></a></div><!--]--><!--]--></div><!--]--><!--]--></div><!--[--><!--]--></div></div></div><!--]--><!--[--><div class="VPFlyout VPNavBarMenuGroup" data-v-1fe73b82 data-v-27308979><button type="button" class="button" aria-haspopup="true" aria-expanded="false" data-v-27308979><span class="text" data-v-27308979><!----><span data-v-27308979>5.4.6</span><span class="vpi-chevron-down text-icon" data-v-27308979></span></span></button><div class="menu" data-v-27308979><div class="VPMenu" data-v-27308979 data-v-b1bc7863><div class="items" data-v-b1bc7863><!--[--><!--[--><div class="VPMenuLink" data-v-b1bc7863 data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://github.com/vbenjs/vue-vben-admin/releases" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Changelog</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-b1bc7863 data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://github.com/orgs/vbenjs/projects/5" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Roadmap</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-b1bc7863 data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://github.com/vbenjs/vue-vben-admin/blob/main/.github/contributing.md" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Contribution</span><!--]--></a></div><!--]--><!--]--></div><!--[--><!--]--></div></div></div><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/commercial/technical-support.html" tabindex="0" data-v-1fe73b82 data-v-d3981cb9><!--[--><span data-v-d3981cb9>🦄 Tech Support</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/sponsor/personal.html" tabindex="0" data-v-1fe73b82 data-v-d3981cb9><!--[--><span data-v-d3981cb9>✨ Sponsor</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/commercial/community.html" tabindex="0" data-v-1fe73b82 data-v-d3981cb9><!--[--><span data-v-d3981cb9>👨‍👦‍👦 Community</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/friend-links/" tabindex="0" data-v-1fe73b82 data-v-d3981cb9><!--[--><span data-v-d3981cb9>🤝 Friend Links</span><!--]--></a><!--]--><!--]--></nav><div class="VPFlyout VPNavBarTranslations translations" data-v-8424412b data-v-d8f13475 data-v-27308979><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="Language" data-v-27308979><span class="text" data-v-27308979><span class="vpi-languages option-icon" data-v-27308979></span><!----><span class="vpi-chevron-down text-icon" data-v-27308979></span></span></button><div class="menu" data-v-27308979><div class="VPMenu" data-v-27308979 data-v-b1bc7863><!----><!--[--><!--[--><div class="items" data-v-d8f13475><p class="title" data-v-d8f13475>English</p><!--[--><div class="VPMenuLink" data-v-d8f13475 data-v-c01e5ae6><a class="VPLink link" href="/guide/project/dir.html" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>简体中文</span><!--]--></a></div><!--]--></div><!--]--><!--]--></div></div></div><div class="VPNavBarAppearance appearance" data-v-8424412b data-v-3be21465><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title aria-checked="true" data-v-3be21465 data-v-169bc600 data-v-013e1365><span class="check" data-v-013e1365><span class="icon" data-v-013e1365><!--[--><span class="vpi-sun sun" data-v-169bc600></span><span class="vpi-moon moon" data-v-169bc600></span><!--]--></span></span></button></div><div class="VPSocialLinks VPNavBarSocialLinks social-links" data-v-8424412b data-v-f68fde0a data-v-4af35d43><!--[--><a class="VPSocialLink no-icon" href="https://github.com/vbenjs/vue-vben-admin" aria-label="github" target="_blank" rel="noopener" data-v-4af35d43 data-v-33e06d91><span class="vpi-social-github"></span></a><!--]--></div><div class="VPFlyout VPNavBarExtra extra" data-v-8424412b data-v-931559f6 data-v-27308979><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="extra navigation" data-v-27308979><span class="vpi-more-horizontal icon" data-v-27308979></span></button><div class="menu" data-v-27308979><div class="VPMenu" data-v-27308979 data-v-b1bc7863><!----><!--[--><!--[--><div class="group translations" data-v-931559f6><p class="trans-title" data-v-931559f6>English</p><!--[--><div class="VPMenuLink" data-v-931559f6 data-v-c01e5ae6><a class="VPLink link" href="/guide/project/dir.html" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>简体中文</span><!--]--></a></div><!--]--></div><div class="group" data-v-931559f6><div class="item appearance" data-v-931559f6><p class="label" data-v-931559f6>Theme</p><div class="appearance-action" data-v-931559f6><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title aria-checked="true" data-v-931559f6 data-v-169bc600 data-v-013e1365><span class="check" data-v-013e1365><span class="icon" data-v-013e1365><!--[--><span class="vpi-sun sun" data-v-169bc600></span><span class="vpi-moon moon" data-v-169bc600></span><!--]--></span></span></button></div></div></div><div class="group" data-v-931559f6><div class="item social-links" data-v-931559f6><div class="VPSocialLinks social-links-list" data-v-931559f6 data-v-4af35d43><!--[--><a class="VPSocialLink no-icon" href="https://github.com/vbenjs/vue-vben-admin" aria-label="github" target="_blank" rel="noopener" data-v-4af35d43 data-v-33e06d91><span class="vpi-social-github"></span></a><!--]--></div></div></div><!--]--><!--]--></div></div></div><!--[--><!--]--><button type="button" class="VPNavBarHamburger hamburger" aria-label="mobile navigation" aria-expanded="false" aria-controls="VPNavScreen" data-v-8424412b data-v-f79c6a8f><span class="container" data-v-f79c6a8f><span class="top" data-v-f79c6a8f></span><span class="middle" data-v-f79c6a8f></span><span class="bottom" data-v-f79c6a8f></span></span></button></div></div></div></div><div class="divider" data-v-8424412b><div class="divider-line" data-v-8424412b></div></div></div><!----></header><div class="VPLocalNav has-sidebar empty" data-v-8ee6be8d data-v-23aa38bb><div class="container" data-v-23aa38bb><button class="menu" aria-expanded="false" aria-controls="VPSidebarNav" data-v-23aa38bb><span class="vpi-align-left menu-icon" data-v-23aa38bb></span><span class="menu-text" data-v-23aa38bb>Menu</span></button><div class="VPLocalNavOutlineDropdown" style="--vp-vh:0px;" data-v-23aa38bb data-v-34175b7f><button data-v-34175b7f>Back to top</button><!----></div></div></div><aside class="VPSidebar" data-v-8ee6be8d data-v-831f1005><div class="curtain" data-v-831f1005></div><nav class="nav" id="VPSidebarNav" aria-labelledby="sidebar-aria-label" tabindex="-1" data-v-831f1005><span class="visually-hidden" id="sidebar-aria-label" data-v-831f1005> Sidebar Navigation </span><!--[--><!--]--><!--[--><div class="no-transition group" data-v-7983d959><section class="VPSidebarItem level-0 collapsible" data-v-7983d959 data-v-6f438999><div class="item" role="button" tabindex="0" data-v-6f438999><div class="indicator" data-v-6f438999></div><h2 class="text" data-v-6f438999>Introduction</h2><div class="caret" role="button" aria-label="toggle section" tabindex="0" data-v-6f438999><span class="vpi-chevron-right caret-icon" data-v-6f438999></span></div></div><div class="items" data-v-6f438999><!--[--><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/introduction/vben.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>About Vben Admin</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/introduction/why.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Why Choose Us?</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/introduction/quick-start.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Quick Start</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/introduction/thin.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Lite Version</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><div class="no-transition group" data-v-7983d959><section class="VPSidebarItem level-0" data-v-7983d959 data-v-6f438999><div class="item" role="button" tabindex="0" data-v-6f438999><div class="indicator" data-v-6f438999></div><h2 class="text" data-v-6f438999>Basics</h2><!----></div><div class="items" data-v-6f438999><!--[--><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/essentials/concept.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Basic Concepts</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/essentials/development.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Local Development</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/essentials/route.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Routing and Menu</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/essentials/settings.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Configuration</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/essentials/icons.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Icons</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/essentials/styles.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Styles</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/essentials/external-module.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>External Modules</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/essentials/build.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Build and Deployment</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/essentials/server.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Server Interaction and Data Mock</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><div class="no-transition group" data-v-7983d959><section class="VPSidebarItem level-0" data-v-7983d959 data-v-6f438999><div class="item" role="button" tabindex="0" data-v-6f438999><div class="indicator" data-v-6f438999></div><h2 class="text" data-v-6f438999>Advanced</h2><!----></div><div class="items" data-v-6f438999><!--[--><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/in-depth/login.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Login</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/in-depth/theme.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Theme</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/in-depth/access.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Access Control</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/in-depth/locale.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Internationalization</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/in-depth/features.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Common Features</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/in-depth/check-updates.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Check Updates</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/in-depth/loading.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Global Loading</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/in-depth/ui-framework.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>UI Framework Switching</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><div class="no-transition group" data-v-7983d959><section class="VPSidebarItem level-0 has-active" data-v-7983d959 data-v-6f438999><div class="item" role="button" tabindex="0" data-v-6f438999><div class="indicator" data-v-6f438999></div><h2 class="text" data-v-6f438999>Engineering</h2><!----></div><div class="items" data-v-6f438999><!--[--><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/project/standard.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Standards</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/project/cli.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>CLI</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/project/dir.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Directory Explanation</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/project/test.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Unit Testing</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/project/tailwindcss.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Tailwind CSS</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/project/changeset.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Changeset</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/project/vite.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Vite Config</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><div class="no-transition group" data-v-7983d959><section class="VPSidebarItem level-0" data-v-7983d959 data-v-6f438999><div class="item" role="button" tabindex="0" data-v-6f438999><div class="indicator" data-v-6f438999></div><h2 class="text" data-v-6f438999>Others</h2><!----></div><div class="items" data-v-6f438999><!--[--><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/other/project-update.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Project Update</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/other/remove-code.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Remove Code</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/en/guide/other/faq.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>FAQ</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><!--]--><!--[--><!--]--></nav></aside><div class="VPContent has-sidebar" id="VPContent" data-v-8ee6be8d data-v-5ebfaf97><div class="VPDoc has-sidebar has-aside" data-v-5ebfaf97 data-v-243c1dc3><!--[--><!--]--><div class="container" data-v-243c1dc3><div class="aside" data-v-243c1dc3><div class="aside-curtain" data-v-243c1dc3></div><div class="aside-container" data-v-243c1dc3><div class="aside-content" data-v-243c1dc3><div class="VPDocAside" data-v-243c1dc3 data-v-8347cfb5><!--[--><!--]--><!--[--><!--]--><nav aria-labelledby="doc-outline-aria-label" class="VPDocAsideOutline" data-v-8347cfb5 data-v-2f034185><div class="content" data-v-2f034185><div class="outline-marker" data-v-2f034185></div><div aria-level="2" class="outline-title" id="doc-outline-aria-label" role="heading" data-v-2f034185>Navigate</div><ul class="VPDocOutlineItem root" data-v-2f034185 data-v-743dbc50><!--[--><!--]--></ul></div></nav><!--[--><!--]--><div class="spacer" data-v-8347cfb5></div><!--[--><!--]--><!----><!--[--><!--]--><!--[--><!--]--></div></div></div></div><div class="content" data-v-243c1dc3><div class="content-container" data-v-243c1dc3><!--[--><!--]--><main class="main" data-v-243c1dc3><div style="position:relative;" class="vp-doc _en_guide_project_dir" data-v-243c1dc3><div><h1 id="directory-explanation" tabindex="-1">Directory Explanation <a class="header-anchor" href="#directory-explanation" aria-label="Permalink to &quot;Directory Explanation&quot;">​</a></h1><p>The directory uses Monorepo management, and the project structure is as follows:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">.</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Dockerfile</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Docker image build file</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> README.md</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Project documentation</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> apps</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Project applications directory</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> backend-mock</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Backend mock service application</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> web-antd</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Frontend application based on Ant Design Vue</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> web-ele</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Frontend application based on Element Plus</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> └──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> web-naive</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Frontend application based on Naive UI</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> build-local-docker-image.sh</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Script for building Docker images locally</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> cspell.json</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # CSpell configuration file</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> docs</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Project documentation directory</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> eslint.config.mjs</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # ESLint configuration file</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> internal</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Internal tools directory</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> lint-configs</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Code linting configurations</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> commitlint-config</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Commitlint configuration</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> eslint-config</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # ESLint configuration</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> prettier-config</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Prettier configuration</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> └──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> stylelint-config</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Stylelint configuration</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> node-utils</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Node.js tools</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> tailwind-config</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Tailwind configuration</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> tsconfig</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Common tsconfig settings</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> └──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vite-config</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Common Vite configuration</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> package.json</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Project dependency configuration</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> packages</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Project packages directory</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> @core</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Core package</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> base</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Base package</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> design</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Design related</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> icons</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Icons</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> shared</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Shared</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> └──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> typings</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Type definitions</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> composables</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Composable APIs</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> preferences</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Preferences</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> └──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ui-kit</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # UI component collection</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">     ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> layout-ui</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Layout UI</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">     ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> menu-ui</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # Menu UI</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">     ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> shadcn-ui</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # shadcn UI</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">     └──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> tabs-ui</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Tabs UI</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> constants</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Constants</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> effects</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Effects related packages</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> access</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Access control</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> plugins</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Plugins</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> common-ui</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Common UI</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> hooks</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Composable APIs</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> layouts</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Layouts</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> │  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> └──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> request</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Request</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> icons</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Icons</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> locales</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Internationalization</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> preferences</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # Preferences</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> stores</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # State management</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> styles</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Styles</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> types</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Type definitions</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> └──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> utils</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Utilities</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> playground</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Demo directory</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> pnpm-lock.yaml</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # pnpm lock file</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> pnpm-workspace.yaml</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # pnpm workspace configuration file</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> scripts</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Scripts directory</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> ├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> turbo-run</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Turbo run script</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│  </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> └──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # VSH script</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> stylelint.config.mjs</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Stylelint configuration file</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> turbo.json</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Turbo configuration file</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">├──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vben-admin.code-workspace</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # VS Code workspace configuration file</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">└──</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vitest.config.ts</span><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"> # Vite configuration file</span></span></code></pre></div><!--[--><h2 id="contributors">Contributors <a class="header-anchor" href="#contributors" aria-label="Permalink to &#39;Contributors&#39;"></a></h2><div class="vp-nolebase-git-changelog vp-nolebase-git-changelog-contributors vp-nolebase-git-changelog-contributors-container vp-nolebase-git-changelog-contributors-list" flex flex-wrap gap-4 pt-2><!--[--><!--[--><div class="flex items-center gap-2"><img src="https://gravatar.com/avatar/6562db53c1fe917b9bc26347d1e1ab8e6c701cc11f88dda1880ea151d54cde52?d=retro" alt="The avatar of contributor named as linbing" class="h-8 w-8 rounded-full"> linbing</div><!--]--><!--]--></div><!--]--><!--[--><h2 id="changelog" data-v-73eeb447>Changelog <a class="header-anchor" href="#changelog" aria-label="Permalink to &#39;Changelog&#39;" data-v-73eeb447></a></h2><div class="bg-$vp-custom-block-details-bg vp-nolebase-git-changelog vp-nolebase-git-changelog-history vp-nolebase-git-changelog-history-list vp-nolebase-git-changelog-history-container" rounded-lg p-4 data-v-73eeb447><label cursor-pointer data-v-73eeb447><div class="vp-nolebase-git-changelog-title flex select-none items-center justify-between" transition="color ease-in-out" text="&lt;sm:xs" duration-200 data-v-73eeb447><span class="vp-nolebase-git-changelog-last-edited-title inline-flex items-center gap-3" data-v-73eeb447><div class="i-octicon:history-16" data-v-73eeb447></div><span data-v-73eeb447>Last edited 6 months ago</span></span><div class="i-octicon:sort-desc-16" ml-auto mr-4 cursor-pointer data-v-73eeb447></div><span class="vp-nolebase-git-changelog-view-full-history-title inline-flex cursor-pointer items-center gap-3" data-v-73eeb447><span class="&lt;sm:hidden" data-v-73eeb447>View full history</span><svg class="rotate-0 i-octicon:chevron-down-16" transition="transform ease-in-out" duration-200 data-v-73eeb447></svg></span></div></label><div style="display:none;" class="grid grid-cols-[30px_auto] mt-3 gap-1.5 children:my-auto -ml-1.5" text="&lt;sm:xs" data-v-73eeb447><!--[--><!--[--><!--[--><div class="i-octicon:git-commit-16 m-auto rotate-90 transform op-30" data-v-09c8433a></div><div flex gap-1 align-baseline data-v-09c8433a><a href="https://github.com/vbenjs/vue-vben-admin/commit/b1be5e06d283e34342b7792083ddb2b847e78c3a" target="_blank" class="no-icon" data-v-09c8433a><code class="text-xs text-$vp-c-brand-1 hover:text-$vp-c-brand-1" transition="color ease-in-out" duration-200 data-v-09c8433a>b1be5e0</code></a><span data-v-09c8433a>-</span><span data-v-09c8433a><span class="text-sm &lt;sm:text-xs" data-v-09c8433a>vben admin 框架替换部分代码</span><!----><!----></span></div><!--]--><!--]--><!--]--></div></div><!--]--></div></div></main><footer class="VPDocFooter" data-v-243c1dc3 data-v-9f803837><!--[--><!--]--><div class="edit-info" data-v-9f803837><div class="edit-link" data-v-9f803837><a class="VPLink link vp-external-link-icon no-icon edit-link-button" href="https://github.com/vbenjs/vue-vben-admin/edit/main/docs/src/en/guide/project/dir.md" target="_blank" rel="noreferrer" data-v-9f803837><!--[--><span class="vpi-square-pen edit-link-icon" data-v-9f803837></span> Edit this page on GitHub<!--]--></a></div><!----></div><nav class="prev-next" aria-labelledby="doc-footer-aria-label" data-v-9f803837><span class="visually-hidden" id="doc-footer-aria-label" data-v-9f803837>Pager</span><div class="pager" data-v-9f803837><a class="VPLink link pager-link prev" href="/en/guide/project/cli.html" data-v-9f803837><!--[--><span class="desc" data-v-9f803837>Previous Page</span><span class="title" data-v-9f803837>CLI</span><!--]--></a></div><div class="pager" data-v-9f803837><a class="VPLink link pager-link next" href="/en/guide/project/test.html" data-v-9f803837><!--[--><span class="desc" data-v-9f803837>Next Page</span><span class="title" data-v-9f803837>Unit Testing</span><!--]--></a></div></nav></footer><!--[--><!--]--></div></div></div><!--[--><!--]--></div></div><footer class="VPFooter has-sidebar" data-v-8ee6be8d data-v-3b5caf9d><div class="container" data-v-3b5caf9d><p class="message" data-v-3b5caf9d>Released under the MIT License.</p><p class="copyright" data-v-3b5caf9d>Copyright © 2020-2025 Vben</p></div></footer><!--[--><!--]--></div><!--]--><!--]--><!--]--></div>
    <script>window.__VP_HASH_MAP__=JSON.parse("{\"commercial_community.md\":\"BQ4EpBPV\",\"commercial_customized.md\":\"DSawm_Q-\",\"commercial_technical-support.md\":\"BFYCk-Gb\",\"components_common-ui_vben-count-to-animator.md\":\"BhKqyXNB\",\"components_common-ui_vben-drawer.md\":\"B33mnOV3\",\"components_common-ui_vben-form.md\":\"CLzJ4fky\",\"components_common-ui_vben-modal.md\":\"Bx6murLj\",\"components_common-ui_vben-vxe-table.md\":\"CivaGbeF\",\"components_introduction.md\":\"CmwGdCoV\",\"en_guide_essentials_build.md\":\"MIih9kir\",\"en_guide_essentials_concept.md\":\"DNmIhCiA\",\"en_guide_essentials_development.md\":\"ibGZim_0\",\"en_guide_essentials_external-module.md\":\"q3dxfwiu\",\"en_guide_essentials_icons.md\":\"BjPbd9JU\",\"en_guide_essentials_route.md\":\"Dp38MXpi\",\"en_guide_essentials_server.md\":\"CSQr1B2I\",\"en_guide_essentials_settings.md\":\"DcAyDDU9\",\"en_guide_essentials_styles.md\":\"DxhGSj_6\",\"en_guide_in-depth_access.md\":\"Chfb1-SK\",\"en_guide_in-depth_check-updates.md\":\"-R9oMLqS\",\"en_guide_in-depth_features.md\":\"Bejfz70K\",\"en_guide_in-depth_layout.md\":\"D-15-xeX\",\"en_guide_in-depth_loading.md\":\"CmZJfmkY\",\"en_guide_in-depth_locale.md\":\"0Aeuao-6\",\"en_guide_in-depth_login.md\":\"BArKpw5r\",\"en_guide_in-depth_theme.md\":\"B9KJ8e5L\",\"en_guide_in-depth_ui-framework.md\":\"7omv8PEF\",\"en_guide_introduction_changelog.md\":\"8wY91cKi\",\"en_guide_introduction_quick-start.md\":\"BytLPvPF\",\"en_guide_introduction_roadmap.md\":\"40ztGFQt\",\"en_guide_introduction_thin.md\":\"CXqskMY5\",\"en_guide_introduction_vben.md\":\"BWJ5xMjU\",\"en_guide_introduction_why.md\":\"z9Xu63fv\",\"en_guide_other_faq.md\":\"BgmNWqTH\",\"en_guide_other_project-update.md\":\"CDFRjDYN\",\"en_guide_other_remove-code.md\":\"CwXtJBRo\",\"en_guide_project_changeset.md\":\"BD2tPmDu\",\"en_guide_project_cli.md\":\"C-wc3LTU\",\"en_guide_project_dir.md\":\"1HUpj9N1\",\"en_guide_project_standard.md\":\"CQc_4pMI\",\"en_guide_project_tailwindcss.md\":\"qo2umvYt\",\"en_guide_project_test.md\":\"IcLXrOtF\",\"en_guide_project_vite.md\":\"B8fkU20S\",\"en_index.md\":\"BK0EQWpa\",\"friend-links_index.md\":\"D77oQbQ-\",\"guide_essentials_build.md\":\"CQkLo245\",\"guide_essentials_concept.md\":\"ysVXMpe-\",\"guide_essentials_development.md\":\"CF3SXatH\",\"guide_essentials_external-module.md\":\"C_kmTK3-\",\"guide_essentials_icons.md\":\"D0KFfSIs\",\"guide_essentials_route.md\":\"uOqQTT6J\",\"guide_essentials_server.md\":\"DEvXY5t3\",\"guide_essentials_settings.md\":\"BLrqQw1B\",\"guide_essentials_styles.md\":\"DNNZSM1G\",\"guide_in-depth_access.md\":\"fqh6BPIB\",\"guide_in-depth_check-updates.md\":\"BOEFzrpM\",\"guide_in-depth_features.md\":\"HNLQtK2P\",\"guide_in-depth_layout.md\":\"DECXVOlM\",\"guide_in-depth_loading.md\":\"tN6N3kYp\",\"guide_in-depth_locale.md\":\"8wyk1Yqv\",\"guide_in-depth_login.md\":\"B0uIcPWn\",\"guide_in-depth_theme.md\":\"DWjDTe1g\",\"guide_in-depth_ui-framework.md\":\"Bd452hrK\",\"guide_introduction_changelog.md\":\"DvtHokp3\",\"guide_introduction_quick-start.md\":\"uxGzUiCf\",\"guide_introduction_roadmap.md\":\"BSJVqFJh\",\"guide_introduction_thin.md\":\"BkO-ybF-\",\"guide_introduction_vben.md\":\"DXXb_m7A\",\"guide_introduction_why.md\":\"7F7cA3dM\",\"guide_other_faq.md\":\"D6nD4Ts7\",\"guide_other_project-update.md\":\"NXdU7BDQ\",\"guide_other_remove-code.md\":\"BhBdmNJH\",\"guide_project_changeset.md\":\"DIv7y1Ob\",\"guide_project_cli.md\":\"CTvt9NDx\",\"guide_project_dir.md\":\"B727TzC-\",\"guide_project_standard.md\":\"egezrkKp\",\"guide_project_tailwindcss.md\":\"DGd2QTPf\",\"guide_project_test.md\":\"BdJV3p8J\",\"guide_project_vite.md\":\"t313fnW6\",\"index.md\":\"B-Mue5Vq\",\"sponsor_personal.md\":\"DDsxIVU3\"}");window.__VP_SITE_DATA__=JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"Vben Admin\",\"description\":\"A VitePress site\",\"base\":\"/\",\"head\":[],\"router\":{\"prefetchLinks\":true},\"appearance\":\"dark\",\"themeConfig\":{\"i18nRouting\":true,\"logo\":\"https://unpkg.com/@vbenjs/static-source@0.1.7/source/logo-v1.webp\",\"search\":{\"options\":{\"locales\":{\"root\":{\"placeholder\":\"搜索文档\",\"translations\":{\"button\":{\"buttonAriaLabel\":\"搜索文档\",\"buttonText\":\"搜索文档\"},\"modal\":{\"errorScreen\":{\"helpText\":\"你可能需要检查你的网络连接\",\"titleText\":\"无法获取结果\"},\"footer\":{\"closeText\":\"关闭\",\"navigateText\":\"切换\",\"searchByText\":\"搜索提供者\",\"selectText\":\"选择\"},\"noResultsScreen\":{\"noResultsText\":\"无法找到相关结果\",\"reportMissingResultsLinkText\":\"点击反馈\",\"reportMissingResultsText\":\"你认为该查询应该有结果？\",\"suggestedQueryText\":\"你可以尝试查询\"},\"searchBox\":{\"cancelButtonAriaLabel\":\"取消\",\"cancelButtonText\":\"取消\",\"resetButtonAriaLabel\":\"清除查询条件\",\"resetButtonTitle\":\"清除查询条件\"},\"startScreen\":{\"favoriteSearchesTitle\":\"收藏\",\"noRecentSearchesText\":\"没有搜索历史\",\"recentSearchesTitle\":\"搜索历史\",\"removeFavoriteSearchButtonTitle\":\"从收藏中移除\",\"removeRecentSearchButtonTitle\":\"从搜索历史中移除\",\"saveRecentSearchButtonTitle\":\"保存至搜索历史\"}}}}}},\"provider\":\"local\"},\"siteTitle\":\"Vben Admin\",\"socialLinks\":[{\"icon\":\"github\",\"link\":\"https://github.com/vbenjs/vue-vben-admin\"}]},\"locales\":{\"en\":{\"label\":\"English\",\"lang\":\"en-US\",\"link\":\"/en/\",\"description\":\"Vben Admin & Enterprise level management system framework\",\"themeConfig\":{\"darkModeSwitchLabel\":\"Theme\",\"darkModeSwitchTitle\":\"Switch to Dark Mode\",\"docFooter\":{\"next\":\"Next Page\",\"prev\":\"Previous Page\"},\"editLink\":{\"pattern\":\"https://github.com/vbenjs/vue-vben-admin/edit/main/docs/src/:path\",\"text\":\"Edit this page on GitHub\"},\"footer\":{\"copyright\":\"Copyright © 2020-2025 Vben\",\"message\":\"Released under the MIT License.\"},\"langMenuLabel\":\"Language\",\"lastUpdated\":{\"formatOptions\":{\"dateStyle\":\"short\",\"timeStyle\":\"medium\"},\"text\":\"Last updated on\"},\"lightModeSwitchTitle\":\"Switch to Light Mode\",\"nav\":[{\"activeMatch\":\"^/en/(guide|components)/\",\"text\":\"Doc\",\"items\":[{\"activeMatch\":\"^/en/guide/\",\"link\":\"/en/guide/introduction/vben\",\"text\":\"Guide\"},{\"text\":\"Historical Versions\",\"items\":[{\"link\":\"https://doc.vvbin.cn\",\"text\":\"2.x Version Documentation\"}]}]},{\"text\":\"Demo\",\"items\":[{\"text\":\"Vben Admin\",\"items\":[{\"link\":\"https://www.vben.pro\",\"text\":\"Demo Version\"},{\"link\":\"https://ant.vben.pro\",\"text\":\"Ant Design Vue Version\"},{\"link\":\"https://naive.vben.pro\",\"text\":\"Naive Version\"},{\"link\":\"https://ele.vben.pro\",\"text\":\"Element Plus Version\"}]},{\"text\":\"Others\",\"items\":[{\"link\":\"https://vben.vvbin.cn\",\"text\":\"Vben Admin 2.x\"}]}]},{\"text\":\"5.4.6\",\"items\":[{\"link\":\"https://github.com/vbenjs/vue-vben-admin/releases\",\"text\":\"Changelog\"},{\"link\":\"https://github.com/orgs/vbenjs/projects/5\",\"text\":\"Roadmap\"},{\"link\":\"https://github.com/vbenjs/vue-vben-admin/blob/main/.github/contributing.md\",\"text\":\"Contribution\"}]},{\"link\":\"/commercial/technical-support\",\"text\":\"🦄 Tech Support\"},{\"link\":\"/sponsor/personal\",\"text\":\"✨ Sponsor\"},{\"link\":\"/commercial/community\",\"text\":\"👨‍👦‍👦 Community\"},{\"link\":\"/friend-links/\",\"text\":\"🤝 Friend Links\"}],\"outline\":{\"label\":\"Navigate\"},\"returnToTopLabel\":\"Back to top\",\"sidebar\":{\"/en/commercial/\":{\"base\":\"/en/commercial/\",\"items\":[{\"link\":\"community\",\"text\":\"Community\"},{\"link\":\"technical-support\",\"text\":\"Technical-support\"},{\"link\":\"customized\",\"text\":\"Customized\"}]},\"/en/guide/\":{\"base\":\"/en/guide/\",\"items\":[{\"collapsed\":false,\"text\":\"Introduction\",\"items\":[{\"link\":\"introduction/vben\",\"text\":\"About Vben Admin\"},{\"link\":\"introduction/why\",\"text\":\"Why Choose Us?\"},{\"link\":\"introduction/quick-start\",\"text\":\"Quick Start\"},{\"link\":\"introduction/thin\",\"text\":\"Lite Version\"}]},{\"text\":\"Basics\",\"items\":[{\"link\":\"essentials/concept\",\"text\":\"Basic Concepts\"},{\"link\":\"essentials/development\",\"text\":\"Local Development\"},{\"link\":\"essentials/route\",\"text\":\"Routing and Menu\"},{\"link\":\"essentials/settings\",\"text\":\"Configuration\"},{\"link\":\"essentials/icons\",\"text\":\"Icons\"},{\"link\":\"essentials/styles\",\"text\":\"Styles\"},{\"link\":\"essentials/external-module\",\"text\":\"External Modules\"},{\"link\":\"essentials/build\",\"text\":\"Build and Deployment\"},{\"link\":\"essentials/server\",\"text\":\"Server Interaction and Data Mock\"}]},{\"text\":\"Advanced\",\"items\":[{\"link\":\"in-depth/login\",\"text\":\"Login\"},{\"link\":\"in-depth/theme\",\"text\":\"Theme\"},{\"link\":\"in-depth/access\",\"text\":\"Access Control\"},{\"link\":\"in-depth/locale\",\"text\":\"Internationalization\"},{\"link\":\"in-depth/features\",\"text\":\"Common Features\"},{\"link\":\"in-depth/check-updates\",\"text\":\"Check Updates\"},{\"link\":\"in-depth/loading\",\"text\":\"Global Loading\"},{\"link\":\"in-depth/ui-framework\",\"text\":\"UI Framework Switching\"}]},{\"text\":\"Engineering\",\"items\":[{\"link\":\"project/standard\",\"text\":\"Standards\"},{\"link\":\"project/cli\",\"text\":\"CLI\"},{\"link\":\"project/dir\",\"text\":\"Directory Explanation\"},{\"link\":\"project/test\",\"text\":\"Unit Testing\"},{\"link\":\"project/tailwindcss\",\"text\":\"Tailwind CSS\"},{\"link\":\"project/changeset\",\"text\":\"Changeset\"},{\"link\":\"project/vite\",\"text\":\"Vite Config\"}]},{\"text\":\"Others\",\"items\":[{\"link\":\"other/project-update\",\"text\":\"Project Update\"},{\"link\":\"other/remove-code\",\"text\":\"Remove Code\"},{\"link\":\"other/faq\",\"text\":\"FAQ\"}]}]}}}},\"root\":{\"label\":\"简体中文\",\"lang\":\"zh-Hans\",\"description\":\"Vben Admin & 企业级管理系统框架\",\"themeConfig\":{\"darkModeSwitchLabel\":\"主题\",\"darkModeSwitchTitle\":\"切换到深色模式\",\"docFooter\":{\"next\":\"下一页\",\"prev\":\"上一页\"},\"editLink\":{\"pattern\":\"https://github.com/vbenjs/vue-vben-admin/edit/main/docs/src/:path\",\"text\":\"在 GitHub 上编辑此页面\"},\"footer\":{\"copyright\":\"Copyright © 2020-2025 Vben\",\"message\":\"基于 MIT 许可发布.\"},\"langMenuLabel\":\"多语言\",\"lastUpdated\":{\"formatOptions\":{\"dateStyle\":\"short\",\"timeStyle\":\"medium\"},\"text\":\"最后更新于\"},\"lightModeSwitchTitle\":\"切换到浅色模式\",\"nav\":[{\"activeMatch\":\"^/(guide|components)/\",\"text\":\"文档\",\"items\":[{\"activeMatch\":\"^/guide/\",\"link\":\"/guide/introduction/vben\",\"text\":\"指南\"},{\"activeMatch\":\"^/components/\",\"link\":\"/components/introduction\",\"text\":\"组件\"},{\"text\":\"历史版本\",\"items\":[{\"link\":\"https://doc.vvbin.cn\",\"text\":\"2.x版本文档\"}]}]},{\"text\":\"演示\",\"items\":[{\"text\":\"Vben Admin\",\"items\":[{\"link\":\"https://www.vben.pro\",\"text\":\"演示版本\"},{\"link\":\"https://ant.vben.pro\",\"text\":\"Ant Design Vue 版本\"},{\"link\":\"https://naive.vben.pro\",\"text\":\"Naive 版本\"},{\"link\":\"https://ele.vben.pro\",\"text\":\"Element Plus版本\"}]},{\"text\":\"其他\",\"items\":[{\"link\":\"https://vben.vvbin.cn\",\"text\":\"Vben Admin 2.x\"}]}]},{\"text\":\"5.4.6\",\"items\":[{\"link\":\"https://github.com/vbenjs/vue-vben-admin/releases\",\"text\":\"更新日志\"},{\"link\":\"https://github.com/orgs/vbenjs/projects/5\",\"text\":\"路线图\"},{\"link\":\"https://github.com/vbenjs/vue-vben-admin/blob/main/.github/contributing.md\",\"text\":\"贡献\"}]},{\"link\":\"/commercial/technical-support\",\"text\":\"🦄 技术支持\"},{\"link\":\"/sponsor/personal\",\"text\":\"✨ 赞助\"},{\"link\":\"/commercial/community\",\"text\":\"👨‍👦‍👦 社区\"},{\"link\":\"/friend-links/\",\"text\":\"🤝 友情链接\"}],\"outline\":{\"label\":\"页面导航\"},\"returnToTopLabel\":\"回到顶部\",\"sidebar\":{\"/commercial/\":{\"base\":\"/commercial/\",\"items\":[{\"link\":\"community\",\"text\":\"社区\"},{\"link\":\"technical-support\",\"text\":\"技术支持\"},{\"link\":\"customized\",\"text\":\"定制开发\"}]},\"/components/\":{\"base\":\"/components/\",\"items\":[{\"text\":\"组件\",\"items\":[{\"link\":\"introduction\",\"text\":\"介绍\"}]},{\"collapsed\":false,\"text\":\"通用组件\",\"items\":[{\"link\":\"common-ui/vben-modal\",\"text\":\"Modal 模态框\"},{\"link\":\"common-ui/vben-drawer\",\"text\":\"Drawer 抽屉\"},{\"link\":\"common-ui/vben-form\",\"text\":\"Form 表单\"},{\"link\":\"common-ui/vben-vxe-table\",\"text\":\"Vxe Table 表格\"},{\"link\":\"common-ui/vben-count-to-animator\",\"text\":\"CountToAnimator 数字动画\"}]}]},\"/guide/\":{\"base\":\"/guide/\",\"items\":[{\"collapsed\":false,\"text\":\"简介\",\"items\":[{\"link\":\"introduction/vben\",\"text\":\"关于 Vben Admin\"},{\"link\":\"introduction/why\",\"text\":\"为什么选择我们?\"},{\"link\":\"introduction/quick-start\",\"text\":\"快速开始\"},{\"link\":\"introduction/thin\",\"text\":\"精简版本\"},{\"base\":\"/\",\"link\":\"components/introduction\",\"text\":\"组件文档\"}]},{\"text\":\"基础\",\"items\":[{\"link\":\"essentials/concept\",\"text\":\"基础概念\"},{\"link\":\"essentials/development\",\"text\":\"本地开发\"},{\"link\":\"essentials/route\",\"text\":\"路由和菜单\"},{\"link\":\"essentials/settings\",\"text\":\"配置\"},{\"link\":\"essentials/icons\",\"text\":\"图标\"},{\"link\":\"essentials/styles\",\"text\":\"样式\"},{\"link\":\"essentials/external-module\",\"text\":\"外部模块\"},{\"link\":\"essentials/build\",\"text\":\"构建与部署\"},{\"link\":\"essentials/server\",\"text\":\"服务端交互与数据Mock\"}]},{\"text\":\"深入\",\"items\":[{\"link\":\"in-depth/login\",\"text\":\"登录\"},{\"link\":\"in-depth/theme\",\"text\":\"主题\"},{\"link\":\"in-depth/access\",\"text\":\"权限\"},{\"link\":\"in-depth/locale\",\"text\":\"国际化\"},{\"link\":\"in-depth/features\",\"text\":\"常用功能\"},{\"link\":\"in-depth/check-updates\",\"text\":\"检查更新\"},{\"link\":\"in-depth/loading\",\"text\":\"全局loading\"},{\"link\":\"in-depth/ui-framework\",\"text\":\"组件库切换\"}]},{\"text\":\"工程\",\"items\":[{\"link\":\"project/standard\",\"text\":\"规范\"},{\"link\":\"project/cli\",\"text\":\"CLI\"},{\"link\":\"project/dir\",\"text\":\"目录说明\"},{\"link\":\"project/test\",\"text\":\"单元测试\"},{\"link\":\"project/tailwindcss\",\"text\":\"Tailwind CSS\"},{\"link\":\"project/changeset\",\"text\":\"Changeset\"},{\"link\":\"project/vite\",\"text\":\"Vite Config\"}]},{\"text\":\"其他\",\"items\":[{\"link\":\"other/project-update\",\"text\":\"项目更新\"},{\"link\":\"other/remove-code\",\"text\":\"移除代码\"},{\"link\":\"other/faq\",\"text\":\"常见问题\"}]}]}},\"sidebarMenuLabel\":\"菜单\"}}},\"scrollOffset\":134,\"cleanUrls\":false}");</script>
    
  </body>
</html>