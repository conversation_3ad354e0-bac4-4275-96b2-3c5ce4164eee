import{ao as e,k as a,aP as r,l as t,ay as i,j as l}from"./chunks/framework.C8U7mBUf.js";const n=JSON.parse('{"title":"友情链接","description":"","frontmatter":{},"headers":[],"relativePath":"friend-links/index.md","filePath":"friend-links/index.md"}');const o=e({name:"friend-links/index.md"},[["render",function(e,n,o,s,h,d){const c=i("NolebaseGitContributors"),u=i("NolebaseGitChangelog");return l(),a("div",null,[n[0]||(n[0]=r('<h1 id="友情链接" tabindex="-1">友情链接 <a class="header-anchor" href="#友情链接" aria-label="Permalink to &quot;友情链接&quot;">​</a></h1><p>如果您的网站是与 Vben Admin 相关的，也属于开源项目，欢迎联系我们，我们会将与您的网站加入交换友情链接。</p><h2 id="交换方式" tabindex="-1">交换方式 <a class="header-anchor" href="#交换方式" aria-label="Permalink to &quot;交换方式&quot;">​</a></h2><h3 id="添加作者-并注明来意" tabindex="-1">添加作者，并注明来意 <a class="header-anchor" href="#添加作者-并注明来意" aria-label="Permalink to &quot;添加作者，并注明来意&quot;">​</a></h3><ul><li>通过邮箱联系作者： <a href="mailto:<EMAIL>" target="_blank" rel="noreferrer"><EMAIL></a></li><li>通过微信联系作者：</li></ul><img src="https://unpkg.com/@vbenjs/static-source@0.1.7/source/wechat.jpg" style="width:300px;"><h3 id="提供资料" tabindex="-1">提供资料 <a class="header-anchor" href="#提供资料" aria-label="Permalink to &quot;提供资料&quot;">​</a></h3><p>提供您的网站名称、链接、描述、LOGO（可选）等信息，我们会在第一时间添加您的网站。</p><h3 id="友情链接-1" tabindex="-1">友情链接 <a class="header-anchor" href="#友情链接-1" aria-label="Permalink to &quot;友情链接&quot;">​</a></h3><ul><li><p>在您的网站上添加我们的友情链接，链接如下：</p><ul><li>名称：Vben Admin</li><li>链接：<a href="https://www.vben.pro" target="_blank" rel="noreferrer">https://www.vben.pro</a></li><li>描述：Vben Admin 企业级开箱即用的中后台前端解决方案</li><li>Logo：<a href="https://unpkg.com/@vbenjs/static-source@0.1.7/source/logo-v1.webp" target="_blank" rel="noreferrer">https://unpkg.com/@vbenjs/static-source@0.1.7/source/logo-v1.webp</a></li></ul></li></ul><p>我们将定期的检查友情链接，如果发现您的网站已经删除了我们的友情链接以及链接地址是否正确。</p>',11)),t(c),t(u)])}]]);export{n as __pageData,o as default};
