import{ao as e,k as a,aP as t,l as s,ay as i,j as o}from"./chunks/framework.C8U7mBUf.js";const h=JSON.parse('{"title":"CLI","description":"","frontmatter":{"outline":"deep"},"headers":[],"relativePath":"en/guide/project/cli.md","filePath":"en/guide/project/cli.md"}');const n=e({name:"en/guide/project/cli.md"},[["render",function(e,h,n,d,r,l){const c=i("NolebaseGitContributors"),p=i("NolebaseGitChangelog");return o(),a("div",null,[h[0]||(h[0]=t('<h1 id="cli" tabindex="-1">CLI <a class="header-anchor" href="#cli" aria-label="Permalink to &quot;CLI&quot;">​</a></h1><p>In the project, some command-line tools are provided for common operations, located in <code>scripts</code>.</p><h2 id="vsh" tabindex="-1">vsh <a class="header-anchor" href="#vsh" aria-label="Permalink to &quot;vsh&quot;">​</a></h2><p>Used for some project operations, such as cleaning the project, checking the project, etc.</p><h3 id="usage" tabindex="-1">Usage <a class="header-anchor" href="#usage" aria-label="Permalink to &quot;Usage&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> [command] [options]</span></span></code></pre></div><h3 id="vsh-check-circular" tabindex="-1">vsh check-circular <a class="header-anchor" href="#vsh-check-circular" aria-label="Permalink to &quot;vsh check-circular&quot;">​</a></h3><p>Check for circular references throughout the project. If there are circular references, the modules involved will be output to the console.</p><h4 id="usage-1" tabindex="-1">Usage <a class="header-anchor" href="#usage-1" aria-label="Permalink to &quot;Usage&quot;">​</a></h4><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> check-circular</span></span></code></pre></div><h4 id="options" tabindex="-1">Options <a class="header-anchor" href="#options" aria-label="Permalink to &quot;Options&quot;">​</a></h4><table tabindex="0"><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><code>--staged</code></td><td>Only check files in the git staging area, default <code>false</code></td></tr></tbody></table><h3 id="vsh-check-dep" tabindex="-1">vsh check-dep <a class="header-anchor" href="#vsh-check-dep" aria-label="Permalink to &quot;vsh check-dep&quot;">​</a></h3><p>Check the dependency situation of the entire project and output <code>unused dependencies</code>, <code>uninstalled dependencies</code> information to the console.</p><h4 id="usage-2" tabindex="-1">Usage <a class="header-anchor" href="#usage-2" aria-label="Permalink to &quot;Usage&quot;">​</a></h4><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> check-dep</span></span></code></pre></div><h3 id="vsh-lint" tabindex="-1">vsh lint <a class="header-anchor" href="#vsh-lint" aria-label="Permalink to &quot;vsh lint&quot;">​</a></h3><p>Lint checks the project to see if the code in the project conforms to standards.</p><h4 id="usage-3" tabindex="-1">Usage <a class="header-anchor" href="#usage-3" aria-label="Permalink to &quot;Usage&quot;">​</a></h4><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> lint</span></span></code></pre></div><h4 id="options-1" tabindex="-1">Options <a class="header-anchor" href="#options-1" aria-label="Permalink to &quot;Options&quot;">​</a></h4><table tabindex="0"><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><code>--format</code></td><td>Check and try to fix errors, default <code>false</code></td></tr></tbody></table><h3 id="vsh-publint" tabindex="-1">vsh publint <a class="header-anchor" href="#vsh-publint" aria-label="Permalink to &quot;vsh publint&quot;">​</a></h3><p>Perform package standard checks on <code>Monorepo</code> projects to see if the packages in the project conform to standards.</p><h4 id="usage-4" tabindex="-1">Usage <a class="header-anchor" href="#usage-4" aria-label="Permalink to &quot;Usage&quot;">​</a></h4><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> publint</span></span></code></pre></div><h4 id="options-2" tabindex="-1">Options <a class="header-anchor" href="#options-2" aria-label="Permalink to &quot;Options&quot;">​</a></h4><table tabindex="0"><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><code>--check</code></td><td>Only perform checks, default <code>false</code></td></tr></tbody></table><h3 id="vsh-code-workspace" tabindex="-1">vsh code-workspace <a class="header-anchor" href="#vsh-code-workspace" aria-label="Permalink to &quot;vsh code-workspace&quot;">​</a></h3><p>Generate <code>vben-admin.code-workspace</code> file. Currently, it does not need to be executed manually and will be executed automatically when code is committed.</p><h4 id="usage-5" tabindex="-1">Usage <a class="header-anchor" href="#usage-5" aria-label="Permalink to &quot;Usage&quot;">​</a></h4><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> code-workspace</span></span></code></pre></div><h4 id="options-3" tabindex="-1">Options <a class="header-anchor" href="#options-3" aria-label="Permalink to &quot;Options&quot;">​</a></h4><table tabindex="0"><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><code>--auto-commit</code></td><td>Automatically commit during <code>git commit</code>, default <code>false</code></td></tr><tr><td><code>--spaces</code></td><td>Indentation format, default <code>2</code> spaces</td></tr></tbody></table><h2 id="turbo-run" tabindex="-1">turbo-run <a class="header-anchor" href="#turbo-run" aria-label="Permalink to &quot;turbo-run&quot;">​</a></h2><p>Used to quickly execute scripts in the large repository and provide option-based interactive selection.</p><h3 id="usage-6" tabindex="-1">Usage <a class="header-anchor" href="#usage-6" aria-label="Permalink to &quot;Usage&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> turbo-run</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> [command]</span></span></code></pre></div><h3 id="turbo-run-dev" tabindex="-1">turbo-run dev <a class="header-anchor" href="#turbo-run-dev" aria-label="Permalink to &quot;turbo-run dev&quot;">​</a></h3><p>Quickly execute the <code>dev</code> command and provide option-based interactive selection.</p>',40)),s(c),s(p)])}]]);export{h as __pageData,n as default};
