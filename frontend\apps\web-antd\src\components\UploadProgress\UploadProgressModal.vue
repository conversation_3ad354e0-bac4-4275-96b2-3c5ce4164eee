<template>
  <div class="fixed bottom-4 right-4 z-50">
    <!-- 最小化状态 -->
    <div v-if="isMinimized" @click="isMinimized = false"
      class="bg-white rounded-lg shadow-lg p-3 cursor-pointer hover:shadow-xl transition-shadow">
      <div class="flex items-center">
        <div class="mr-2">
          <loading-outlined spin class="text-primary text-lg" />
        </div>
        <div class="flex-1">
          <div class="text-sm font-medium">正在上传 ({{ tasks.length }})</div>
        </div>
      </div>
    </div>

    <!-- 展开状态 -->
    <div v-else class="bg-white rounded-lg shadow-lg w-80">
      <div class="p-3 border-b flex items-center justify-between">
        <div class="font-medium">上传进度</div>
        <div class="flex items-center space-x-2">
          <minus-outlined class="cursor-pointer hover:text-primary" @click="isMinimized = true" />
          <close-outlined class="cursor-pointer hover:text-primary" @click="handleClose" />
        </div>
      </div>

      <div class="max-h-96 overflow-y-auto">
        <div v-for="task in tasks" :key="task.id" class="p-3 border-b last:border-b-0">
          <div class="flex items-center justify-between mb-2">
            <div class="text-sm truncate flex-1" :title="task.fileName">{{ task.fileName }}</div>
            <div class="text-xs text-gray-500 ml-2">{{ task.progress }}%</div>
          </div>
          <a-progress :percent="task.progress" size="small" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { LoadingOutlined, MinusOutlined, CloseOutlined } from '@ant-design/icons-vue';
import { useUploadStore } from './useUploadStore';

const isMinimized = ref(false);
const uploadStore = useUploadStore();
const tasks = computed(() => uploadStore.tasks);

const handleClose = () => {
  uploadStore.clearCompletedTasks();
  if (tasks.value.length === 0) {
    isMinimized.value = false;
  }
};

// 监听任务变化，当有新任务时自动展开
watch(() => tasks.value.length, (newLength, oldLength) => {
  if (newLength > oldLength && newLength > 0) {
    isMinimized.value = false;
  }
});
</script>

<style scoped>
.upload-progress-enter-active,
.upload-progress-leave-active {
  transition: all 0.3s ease;
}

.upload-progress-enter-from,
.upload-progress-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>
