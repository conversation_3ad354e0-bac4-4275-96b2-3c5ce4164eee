<script lang="ts" setup>
import { ref } from 'vue';
import type { FormApi } from '@vben/form-ui';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { showSuccess } from '#/utils/toast.js';
import { updatePassword } from '#/apis/auth';
import { message } from 'ant-design-vue';

interface ModalInstance {
  open: () => void;
  close: () => void;
}

const emit = defineEmits(['success']);

// 表单配置
const formSchema = [
  {
    fieldName: 'oldPassword',
    label: '原密码',
    rules: 'required',
    component: 'InputPassword',
    componentProps: {
      placeholder: '请输入原密码',
    },
  },
  {
    fieldName: 'newPassword',
    label: '新密码',
    rules: 'required',
    component: 'InputPassword',
    componentProps: {
      placeholder: '请输入新密码',
    },
  },
  {
    fieldName: 'confirmPassword',
    label: '确认密码',
    rules: 'required',
    component: 'InputPassword',
    componentProps: {
      placeholder: '请再次输入新密码',
    },
  },
];

const [Form, formApi] = useVbenForm({
  schema: formSchema,
  submitButtonOptions: {
    content: '确定',
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  },
  resetButtonOptions: {
    content: '取消',
  },
  handleSubmit,
  handleReset,
});

// 表单提交
async function handleSubmit(values: any) {
  if (values.newPassword !== values.confirmPassword) {
    message.error('两次输入的密码不一致');
    return;
  }

  try {
    await updatePassword({
      oldPassword: values.oldPassword,
      newPassword: values.newPassword,
    });
    showSuccess('密码修改成功');
    emit('success');
    modalApi.close();
  } catch (error) {
    console.error('修改密码失败:', error);
  }
}

// 表单重置
function handleReset() {
  modalApi.close();
}

const [Modal, modalApi] = useVbenModal({
  title: '修改密码',
  footer: false,
  destroyOnClose: true,
});
</script>

<template>
  <Modal>
    <div class="p-6">
      <Form />
    </div>
  </Modal>
</template>]]>
