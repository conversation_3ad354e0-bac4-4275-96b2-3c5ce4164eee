<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { Page } from '@vben/common-ui';
import { onMounted, ref } from 'vue';

import { useVbenModal, VbenButton, type VbenFormProps } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { list } from '#/views/project/project.api';
import { columns, searchFormSchema } from './project.data';

import { uploadFile, importProject } from '#/views/project/project.api';
import { message } from 'ant-design-vue';



const formOptions: VbenFormProps = {
  collapsed: false,
  schema: searchFormSchema,
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  },
  submitOnEnter: false,

};

const gridOptions: VxeGridProps = {
  columns,
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const data = await list({
          page: {
            current: page.currentPage,
            size: page.pageSize,
            searchCount: true,
          },
          condition: formValues,
        });
        return {
          total: data.total,
          items: data.records,
        };
      },
    },
  },
  rowConfig: {
    isHover: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const handleAdd = async () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.xlsx,.xls';

  input.onchange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      // 上传文件
      const uploadRes = await uploadFile(file);
      if (!uploadRes) {
        message.error('文件上传失败');
        return;
      }

      // 导入项目
      await importProject(uploadRes);
      message.success('导入成功');

      // 刷新列表
      gridApi.reload();
    } catch (error) {
      console.error('导入失败:', error);
      // message.error('导入失败');
    }
  };

  input.click();
};

</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-tools>
        <VbenButton pre-icon="ant-design:upload-outlined" type="primary" @click="handleAdd">
          导入项目
        </VbenButton>
      </template>
      <template #timeCreated="{ row }">
        <view>{{row.timeUpdated ? row.timeUpdated  : row.timeCreated }}</view>
      </template>
      <template #userNameCreated="{ row }">
        <view>{{row.userNameUpdated ? row.userNameUpdated  : row.userNameCreated }}</view>
      </template>
    </Grid>
  </Page>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';

.actionButton {
  padding: 6px;
  color:hsl(var(--primary));
}

.actionButton:hover {
  color:hsl(var(--primary));
}


.actionBar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
</style>
