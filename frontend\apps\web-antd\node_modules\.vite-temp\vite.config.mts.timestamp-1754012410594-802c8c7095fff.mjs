// vite.config.mts
import { defineConfig } from "file:///E:/work/git/system-manage-fed/frontend/internal/vite-config/dist/index.mjs";
var vite_config_default = defineConfig(async () => {
  return {
    application: {},
    vite: {
      server: {
        proxy: {
          "/api": {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, ""),
            // mock代理目标地址
            // target: 'http://**************:8830',
            target: "http://localhost:3000/api",
            ws: true
          }
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcubXRzIl0sCiAgInNvdXJjZVJvb3QiOiAiRTpcXHdvcmtcXGdpdFxcc3lzdGVtLW1hbmFnZS1mZWRcXGZyb250ZW5kXFxhcHBzXFx3ZWItYW50ZFxcIiwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJFOlxcXFx3b3JrXFxcXGdpdFxcXFxzeXN0ZW0tbWFuYWdlLWZlZFxcXFxmcm9udGVuZFxcXFxhcHBzXFxcXHdlYi1hbnRkXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCJFOlxcXFx3b3JrXFxcXGdpdFxcXFxzeXN0ZW0tbWFuYWdlLWZlZFxcXFxmcm9udGVuZFxcXFxhcHBzXFxcXHdlYi1hbnRkXFxcXHZpdGUuY29uZmlnLm10c1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vRTovd29yay9naXQvc3lzdGVtLW1hbmFnZS1mZWQvZnJvbnRlbmQvYXBwcy93ZWItYW50ZC92aXRlLmNvbmZpZy5tdHNcIjtpbXBvcnQgeyBkZWZpbmVDb25maWcgfSBmcm9tICdAdmJlbi92aXRlLWNvbmZpZyc7XG5cbmV4cG9ydCBkZWZhdWx0IGRlZmluZUNvbmZpZyhhc3luYyAoKSA9PiB7XG4gIHJldHVybiB7XG4gICAgYXBwbGljYXRpb246IHt9LFxuICAgIHZpdGU6IHtcbiAgICAgIHNlcnZlcjoge1xuICAgICAgICBwcm94eToge1xuICAgICAgICAgICcvYXBpJzoge1xuICAgICAgICAgICAgY2hhbmdlT3JpZ2luOiB0cnVlLFxuICAgICAgICAgICAgcmV3cml0ZTogKHBhdGgpID0+IHBhdGgucmVwbGFjZSgvXlxcL2FwaS8sICcnKSxcbiAgICAgICAgICAgIC8vIG1vY2tcdTRFRTNcdTc0MDZcdTc2RUVcdTY4MDdcdTU3MzBcdTU3NDBcbiAgICAgICAgICAgIC8vIHRhcmdldDogJ2h0dHA6Ly8xOTIuMTY4LjEwLjE2Njo4ODMwJyxcbiAgICAgICAgICAgIHRhcmdldDogJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGknLFxuICAgICAgICAgICAgd3M6IHRydWUsXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgfSxcbiAgfTtcbn0pO1xuIl0sCiAgIm1hcHBpbmdzIjogIjtBQUE4VixTQUFTLG9CQUFvQjtBQUUzWCxJQUFPLHNCQUFRLGFBQWEsWUFBWTtBQUN0QyxTQUFPO0FBQUEsSUFDTCxhQUFhLENBQUM7QUFBQSxJQUNkLE1BQU07QUFBQSxNQUNKLFFBQVE7QUFBQSxRQUNOLE9BQU87QUFBQSxVQUNMLFFBQVE7QUFBQSxZQUNOLGNBQWM7QUFBQSxZQUNkLFNBQVMsQ0FBQyxTQUFTLEtBQUssUUFBUSxVQUFVLEVBQUU7QUFBQTtBQUFBO0FBQUEsWUFHNUMsUUFBUTtBQUFBLFlBQ1IsSUFBSTtBQUFBLFVBQ047QUFBQSxRQUNGO0FBQUEsTUFDRjtBQUFBLElBQ0Y7QUFBQSxFQUNGO0FBQ0YsQ0FBQzsiLAogICJuYW1lcyI6IFtdCn0K
