#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/publint@0.3.12/node_modules/publint/src/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/publint@0.3.12/node_modules/publint/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/publint@0.3.12/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/publint@0.3.12/node_modules/publint/src/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/publint@0.3.12/node_modules/publint/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/publint@0.3.12/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../publint/src/cli.js" "$@"
else
  exec node  "$basedir/../publint/src/cli.js" "$@"
fi
