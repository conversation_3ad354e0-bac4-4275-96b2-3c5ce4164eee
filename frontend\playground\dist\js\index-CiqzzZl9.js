const A=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,w=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,O=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function $(r,n){if(r==="__proto__"||r==="constructor"&&n&&typeof n=="object"&&"prototype"in n){E(r);return}return n}function E(r){console.warn(`[destr] Dropping "${r}" key to prevent prototype pollution.`)}function P(r,n={}){if(typeof r!="string")return r;const t=r.trim();if(r[0]==='"'&&r.endsWith('"')&&!r.includes("\\"))return t.slice(1,-1);if(t.length<=9){const e=t.toLowerCase();if(e==="true")return!0;if(e==="false")return!1;if(e==="undefined")return;if(e==="null")return null;if(e==="nan")return Number.NaN;if(e==="infinity")return Number.POSITIVE_INFINITY;if(e==="-infinity")return Number.NEGATIVE_INFINITY}if(!O.test(r)){if(n.strict)throw new SyntaxError("[destr] Invalid JSON");return r}try{if(A.test(r)||w.test(r)){if(n.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(r,$)}return JSON.parse(r)}catch(e){if(n.strict)throw e;return r}}function h(r,n){if(r==null)return;let t=r;for(let e=0;e<n.length;e++){if(t==null||t[n[e]]==null)return;t=t[n[e]]}return t}function d(r,n,t){if(t.length===0)return n;const e=t[0];return t.length>1&&(n=d(typeof r!="object"||r===null||!Object.prototype.hasOwnProperty.call(r,e)?Number.isInteger(Number(t[1]))?[]:{}:r[e],n,Array.prototype.slice.call(t,1))),Number.isInteger(Number(e))&&Array.isArray(r)?r.slice()[e]:Object.assign({},r,{[e]:n})}function N(r,n){if(r==null||n.length===0)return r;if(n.length===1){if(r==null)return r;if(Number.isInteger(n[0])&&Array.isArray(r))return Array.prototype.slice.call(r,0).splice(n[0],1);const t={};for(const e in r)t[e]=r[e];return delete t[n[0]],t}if(r[n[0]]==null){if(Number.isInteger(n[0])&&Array.isArray(r))return Array.prototype.concat.call([],r);const t={};for(const e in r)t[e]=r[e];return t}return d(r,N(r[n[0]],Array.prototype.slice.call(n,1)),[n[0]])}function I(r,n){return n.map(t=>t.split(".")).map(t=>[t,h(r,t)]).filter(t=>t[1]!==void 0).reduce((t,e)=>d(t,e[1],e[0]),{})}function S(r,n){return n.map(t=>t.split(".")).reduce((t,e)=>N(t,e),r)}function g(r,{storage:n,serializer:t,key:e,debug:s,pick:o,omit:l,beforeHydrate:u,afterHydrate:i},c,a=!0){try{a&&(u==null||u(c));const f=n.getItem(e);if(f){const p=t.deserialize(f),y=o?I(p,o):p,_=l?S(y,l):y;r.$patch(_)}a&&(i==null||i(c))}catch(f){s&&console.error("[pinia-plugin-persistedstate]",f)}}function m(r,{storage:n,serializer:t,key:e,debug:s,pick:o,omit:l}){try{const u=o?I(r,o):r,i=l?S(u,l):u,c=t.serialize(i);n.setItem(e,c)}catch(u){s&&console.error("[pinia-plugin-persistedstate]",u)}}function z(r,n,t){const{pinia:e,store:s,options:{persist:o=t}}=r;if(!o)return;if(!(s.$id in e.state.value)){const i=e._s.get(s.$id.replace("__hot:",""));i&&Promise.resolve().then(()=>i.$persist());return}const u=(Array.isArray(o)?o:o===!0?[{}]:[o]).map(n);s.$hydrate=({runHooks:i=!0}={})=>{u.forEach(c=>{g(s,c,r,i)})},s.$persist=()=>{u.forEach(i=>{m(s.$state,i)})},u.forEach(i=>{g(s,i,r),s.$subscribe((c,a)=>m(a,i),{detached:!0})})}function F(r={}){return function(n){var t;z(n,e=>{var s,o,l,u,i,c,a;return{key:(r.key?r.key:f=>f)((s=e.key)!=null?s:n.store.$id),debug:(l=(o=e.debug)!=null?o:r.debug)!=null?l:!1,serializer:(i=(u=e.serializer)!=null?u:r.serializer)!=null?i:{serialize:f=>JSON.stringify(f),deserialize:f=>P(f)},storage:(a=(c=e.storage)!=null?c:r.storage)!=null?a:window.localStorage,beforeHydrate:e.beforeHydrate,afterHydrate:e.afterHydrate,pick:e.pick,omit:e.omit}},(t=r.auto)!=null?t:!1)}}export{F as createPersistedState};
