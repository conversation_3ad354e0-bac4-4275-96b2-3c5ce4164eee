var X=Object.defineProperty;var M=Object.getOwnPropertySymbols;var q=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable;var V=(r,t,e)=>t in r?X(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,f=(r,t)=>{for(var e in t||(t={}))q.call(t,e)&&V(r,e,t[e]);if(M)for(var e of M(t))Q.call(t,e)&&V(r,e,t[e]);return r};var S=(r,t,e)=>new Promise((i,o)=>{var n=l=>{try{s(e.next(l))}catch(u){o(u)}},a=l=>{try{s(e.throw(l))}catch(u){o(u)}},s=l=>l.done?i(l.value):Promise.resolve(l.value).then(n,a);s((e=e.apply(r,t)).next())});import{G as T,b as c,B as N}from"./index-D4Q7xmlJ.js";import{D as j,a as h,s as R}from"./index-BYvs451p.js";import{r as D,g as J,R as Z,P as ee,w as te,a as ie}from"./bootstrap-5OPUVRWy.js";import"./Base-xeJpkIWP.js";import{u as re}from"./vxe-table-CZ9gPHn5.js";import{a as ne}from"./util-BadkgFi3.js";import"./scene.data-BMXeOdST.js";import"./entity.data-u4HDUExc.js";import{d as se,r as H,w as ae,o as oe,x as k,a as le,e as z,b as U,f as y,j as ue,q as v,s as w,k as x,v as A}from"../jse/index-index-DyHD_jbN.js";import{b as ce}from"./toast-CQjPPeQ1.js";new Date().getFullYear();const Ce=[{title:"数据集名称",dataIndex:"layerName",field:"layerName",width:180},{title:"类别",dataIndex:"dataType",field:"dataType",width:120},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",width:160}],it=[{align:"left",title:"Name",type:"checkbox",width:290}];function rt(r,t,e){return S(this,null,function*(){return e||(e=""),D.get(`/layer-data/layers/${r}/${t}?collectionId=${e}`,{collectionId:e})})}function nt(r,t){return S(this,null,function*(){return D.get(`/layer-data/release/${r}/${t}`,{})})}const pe=r=>D.post("/layer-data/query",f({},r)),st=r=>D.post("/layer-data/coverage-layer/page",f({},r)),me=r=>{const{apiURL:t}=J();let e=ne(`${t}/layer-data/export/${r}`);const i=document.createElement("a");i.href=e,i.setAttribute("download","merged-content.png"),document.body.appendChild(i),i.click()};function he(r){return{all:r=r||new Map,on:function(t,e){var i=r.get(t);i?i.push(e):r.set(t,[e])},off:function(t,e){var i=r.get(t);i&&(e?i.splice(i.indexOf(e)>>>0,1):r.set(t,[]))},emit:function(t,e){var i=r.get(t);i&&i.slice().map(function(o){o(e)}),(i=r.get("*"))&&i.slice().map(function(o){o(t,e)})}}}const de=he();function ge(r,{x:t,y:e,width:i,height:o}){return new Promise((n,a)=>{r.toBlob(s=>{const l=new FileReader;l.addEventListener("load",u=>{const C=new Image;C.addEventListener("load",()=>{const m=document.createElement("canvas");m.width=i,m.height=o,m.getContext("2d").drawImage(C,-t,-e),n(m)}),C.src=u.target.result}),l.readAsDataURL(s)})})}class fe{constructor(t){this.viewer=t||T.viewer,this.d=new j(this.viewer),this.handler=null,this.tipEntity=null,this.tipPosition=null}getLengthText(t){let e=c.transformCartesianArrayToWGS84Array(t),i=c.getPositionDistance(e);return c.getLengthText(i)}measureEnd(){this.handler&&!this.handler.isDestroyed()&&(this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE),this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK),this.handler.destroy()),this.d.remove(this.tipEntity),h.emit("drawEnd",{from:"Measure"})}measurePolyLine(t={}){this.measureEnd();var e=[],i=2,o=null,n=null,a="",s=[];h.emit("draw",{from:"Measure"}),this.tipEntity=this.d.drawText(new Cesium.CallbackProperty(()=>this.tipPosition,!1),{text:new Cesium.CallbackProperty(function(){return"点击开始,右击结束"},!1),font:"12px sans-serif",fillColor:Cesium.Color.WHITE,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.TOP,horizontalOrigin:Cesium.HorizontalOrigin.LEFT,pixelOffset:new Cesium.Cartesian2(5,5),showBackground:!0,backgroundColor:new Cesium.Color(.165,.165,.165,.8),backgroundPadding:new Cesium.Cartesian2(6,6),disableDepthTestDistance:Number.POSITIVE_INFINITY}),this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.handler.setInputAction(l=>{let u=c.getCatesian3FromPX(this.viewer,l.position);if(!u)return!1;e.length==0?(e.push(u.clone()),e.push(u),s.push(this.d.drawPoint(u)),n=this.d.drawLine(new Cesium.CallbackProperty(function(){return e},!1),new Cesium.CallbackProperty(function(){return c.getPolylineCenter(e)},!1),t),o=this.d.drawText(new Cesium.CallbackProperty(function(){return e[e.length-1]},!1),{text:new Cesium.CallbackProperty(function(){return a},!1),font:"14px sans-serif",fillColor:Cesium.Color.GOLD,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.BOTTOM,horizontalOrigin:Cesium.HorizontalOrigin.CENTER,pixelOffset:new Cesium.Cartesian2(0,-10),disableDepthTestDistance:Number.POSITIVE_INFINITY}),s.push(n),s.push(o)):(e.push(u),s.push(this.d.drawPoint(u)))},Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.setInputAction(l=>{let u=c.getCatesian3FromPX(this.viewer,l.endPosition);if(!u)return!1;e.length>0&&(e.pop(),e.push(u),a=this.getLengthText(e)),this.tipPosition=u},Cesium.ScreenSpaceEventType.MOUSE_MOVE),this.handler.setInputAction(l=>{this.measureEnd(),e.length&&(e.pop(),i>e.length?this.d.remove(s):a=this.getLengthText(e))},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}getAngleText(t){let e=c.transformCartesianArrayToWGS84Array(t),i=c.getPositionsAngle(e).toFixed(2);e.forEach(n=>{n.alt=0});let o=c.getPositionsAngle(e).toFixed(2);return i==o?Cesium.Math.toDegrees(i).toFixed(2)+"度":Cesium.Math.toDegrees(i).toFixed(2)+"度(3D)/"+Cesium.Math.toDegrees(o).toFixed(2)+"度(2D)"}getArea(t){let e=c.transformCartesianArrayToWGS84Array(t),i=c.getPositionsArea(e);return c.getAreaText(i)}measurePolygon(t={}){this.measureEnd();var e=[],i=[],o=3,n=null,a="",s=null,l=[];h.emit("draw",{from:"Measure"}),this.tipEntity=this.d.drawText(new Cesium.CallbackProperty(()=>this.tipPosition,!1),{text:new Cesium.CallbackProperty(function(){return"点击开始,右击结束"},!1),font:"12px sans-serif",fillColor:Cesium.Color.WHITE,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.TOP,horizontalOrigin:Cesium.HorizontalOrigin.LEFT,pixelOffset:new Cesium.Cartesian2(5,5),showBackground:!0,backgroundColor:new Cesium.Color(.165,.165,.165,.8),backgroundPadding:new Cesium.Cartesian2(6,6),disableDepthTestDistance:Number.POSITIVE_INFINITY}),this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.handler.setInputAction(u=>{let C=c.getCatesian3FromPX(this.viewer,u.position);if(!C)return!1;e.length==0?(e.push(C.clone()),e.push(C),i.push(C.clone()),i.push(C),l.push(this.d.drawPoint(C)),s=this.d.drawPolygon(new Cesium.CallbackProperty(()=>new Cesium.PolygonHierarchy(e),!1),new Cesium.CallbackProperty(function(){return c.getPolylineCenter(e)},!1),t),n=this.d.drawText(new Cesium.CallbackProperty(function(){return c.getPolygonCenter(i)},!1),{text:new Cesium.CallbackProperty(function(){return a},!1),show:new Cesium.CallbackProperty(function(){return e.length>2},!1),font:"14px sans-serif",fillColor:Cesium.Color.GOLD,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.CENTER,horizontalOrigin:Cesium.HorizontalOrigin.CENTER,pixelOffset:new Cesium.Cartesian2(0,0),disableDepthTestDistance:Number.POSITIVE_INFINITY}),l.push(s),l.push(n)):(e.push(C),i.push(C.clone()),l.push(this.d.drawPoint(C)))},Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.setInputAction(u=>{let C=c.getCatesian3FromPX(this.viewer,u.endPosition);if(!C)return!1;e.length>0&&(e.pop(),e.push(C.clone()),i.pop(),i.push(C.clone()),a=this.getArea(i)),this.tipPosition=C},Cesium.ScreenSpaceEventType.MOUSE_MOVE),this.handler.setInputAction(u=>{this.measureEnd(),e.length&&(e.pop(),i.pop(),o>e.length?this.d.remove(l):a=this.getArea(i))},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}measureHeight(t={}){this.measureEnd();var e=[],i=null,o=null,n=null,a=[],s=!0;h.emit("draw",{from:"Measure"}),this.tipEntity=this.d.drawText(new Cesium.CallbackProperty(()=>this.tipPosition,!1),{text:new Cesium.CallbackProperty(function(){return e.length==0?"点击选择起点,右击结束":"点击选择终点,右击结束"},!1),font:"12px sans-serif",fillColor:Cesium.Color.WHITE,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.TOP,horizontalOrigin:Cesium.HorizontalOrigin.LEFT,pixelOffset:new Cesium.Cartesian2(5,5),showBackground:!0,backgroundColor:new Cesium.Color(.165,.165,.165,.8),backgroundPadding:new Cesium.Cartesian2(6,6),disableDepthTestDistance:Number.POSITIVE_INFINITY}),this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.handler.setInputAction(l=>{let u=c.getCatesian3FromPX(this.viewer,l.position);if(!u)return!1;e.length==0?(e.push(u.clone()),e.push(u.clone()),e.push(u),a.push(this.d.drawPoint(u)),a.push(this.d.drawLine(new Cesium.CallbackProperty(()=>e.length==3?[e[0],e[1]]:[e[0],e[0]],!1),new Cesium.CallbackProperty(function(){return c.getPolylineCenter(e)},!1),f({arcType:Cesium.ArcType.NONE,material:Cesium.Color.CHARTREUSE,depthFailMaterial:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.RED})},t))),a.push(this.d.drawLine(new Cesium.CallbackProperty(()=>e.length==3?[e[s?0:1],e[2]]:[e[0],e[0]],!1),new Cesium.CallbackProperty(function(){return c.getPolylineCenter(e)},!1),f({arcType:Cesium.ArcType.NONE,material:Cesium.Color.GREEN,depthFailMaterial:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.RED})},t))),a.push(this.d.drawLine(new Cesium.CallbackProperty(()=>e.length==3?[e[s?1:0],e[2]]:[e[0],e[0]],!1),new Cesium.CallbackProperty(function(){return c.getPolylineCenter(e)},!1),f({arcType:Cesium.ArcType.NONE,material:Cesium.Color.BLUE,depthFailMaterial:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.RED})},t))),a.push(this.d.drawText(new Cesium.CallbackProperty(()=>c.getPolylineCenter([e[0],e[1]]),!1),{text:new Cesium.CallbackProperty(function(){return i},!1),font:"14px sans-serif",fillColor:Cesium.Color.GOLD,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.CENTER,horizontalOrigin:Cesium.HorizontalOrigin.CENTER,pixelOffset:new Cesium.Cartesian2(0,0),disableDepthTestDistance:Number.POSITIVE_INFINITY})),a.push(this.d.drawText(new Cesium.CallbackProperty(()=>c.getPolylineCenter([e[s?0:1],e[2]]),!1),{text:new Cesium.CallbackProperty(function(){return o},!1),font:"14px sans-serif",fillColor:Cesium.Color.GOLD,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.CENTER,horizontalOrigin:Cesium.HorizontalOrigin.CENTER,pixelOffset:new Cesium.Cartesian2(0,0),disableDepthTestDistance:Number.POSITIVE_INFINITY})),a.push(this.d.drawText(new Cesium.CallbackProperty(()=>c.getPolylineCenter([e[s?1:0],e[2]]),!1),{text:new Cesium.CallbackProperty(function(){return n},!1),font:"14px sans-serif",fillColor:Cesium.Color.GOLD,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.CENTER,horizontalOrigin:Cesium.HorizontalOrigin.CENTER,pixelOffset:new Cesium.Cartesian2(0,0),disableDepthTestDistance:Number.POSITIVE_INFINITY}))):(this.measureEnd(),a.push(this.d.drawPoint(e[1])))},Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.setInputAction(l=>{let u=c.getCatesian3FromPX(this.viewer,l.endPosition);if(!u)return!1;if(e.length>0){e.pop(),e.pop(),e.push(u);var C=Cesium.Cartographic.fromCartesian(e[0]),m=Cesium.Cartographic.fromCartesian(u);s=C.height>m.height;var I=Cesium.Cartesian3.fromDegrees(Cesium.Math.toDegrees((s?m:C).longitude),Cesium.Math.toDegrees((s?m:C).latitude),(s?C:m).height);e.push(I),i="直线距离："+this.getLengthText([e[0],e[1]]),o="水平距离："+this.getLengthText([e[s?0:1],e[2]]),n="垂直距离："+this.getLengthText([e[s?1:0],e[2]])}this.tipPosition=u},Cesium.ScreenSpaceEventType.MOUSE_MOVE),this.handler.setInputAction(l=>{this.measureEnd(),e.length&&this.d.remove(a)},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}measureLDistance(t={}){this.measureEnd();var e=[],i=null,o=[];h.emit("draw",{from:"Measure"}),this.tipEntity=this.d.drawText(new Cesium.CallbackProperty(()=>this.tipPosition,!1),{text:new Cesium.CallbackProperty(function(){return e.length==0?"点击选择起点,右击结束":"点击选择终点,右击结束"},!1),font:"12px sans-serif",fillColor:Cesium.Color.WHITE,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.TOP,horizontalOrigin:Cesium.HorizontalOrigin.LEFT,pixelOffset:new Cesium.Cartesian2(5,5),showBackground:!0,backgroundColor:new Cesium.Color(.165,.165,.165,.8),backgroundPadding:new Cesium.Cartesian2(6,6),disableDepthTestDistance:Number.POSITIVE_INFINITY}),this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.handler.setInputAction(n=>{let a=c.getCatesian3FromPX(this.viewer,n.position);if(!a)return!1;e.length==0?(e.push(a.clone()),e.push(a),o.push(this.d.drawPoint(a)),o.push(this.d.drawLine(new Cesium.CallbackProperty(()=>[e[0],e[1]],!1),new Cesium.CallbackProperty(function(){return c.getPolylineCenter(e)},!1),f({arcType:Cesium.ArcType.NONE,material:Cesium.Color.YELLOW,depthFailMaterial:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.RED})},t))),o.push(this.d.drawText(new Cesium.CallbackProperty(()=>c.getPolylineCenter([e[0],e[1]]),!1),{text:new Cesium.CallbackProperty(function(){return i},!1),font:"14px sans-serif",fillColor:Cesium.Color.GOLD,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.CENTER,horizontalOrigin:Cesium.HorizontalOrigin.CENTER,pixelOffset:new Cesium.Cartesian2(0,0),disableDepthTestDistance:Number.POSITIVE_INFINITY}))):(this.measureEnd(),o.push(this.d.drawPoint(e[1])))},Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.setInputAction(n=>{let a=c.getCatesian3FromPX(this.viewer,n.endPosition);if(!a)return!1;e.length>0&&(e.pop(),e.push(a),i="直线距离："+this.getLengthText([e[0],e[1]])),this.tipPosition=a},Cesium.ScreenSpaceEventType.MOUSE_MOVE),this.handler.setInputAction(n=>{this.measureEnd(),e.length&&this.d.remove(o)},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}measureVDistance(t={}){this.measureEnd();var e=[],i=null,o=[],n=!0;h.emit("draw",{from:"Measure"}),this.tipEntity=this.d.drawText(new Cesium.CallbackProperty(()=>this.tipPosition,!1),{text:new Cesium.CallbackProperty(function(){return e.length==0?"点击选择起点,右击结束":"点击选择终点,右击结束"},!1),font:"12px sans-serif",fillColor:Cesium.Color.WHITE,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.TOP,horizontalOrigin:Cesium.HorizontalOrigin.LEFT,pixelOffset:new Cesium.Cartesian2(5,5),showBackground:!0,backgroundColor:new Cesium.Color(.165,.165,.165,.8),backgroundPadding:new Cesium.Cartesian2(6,6),disableDepthTestDistance:Number.POSITIVE_INFINITY}),this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.handler.setInputAction(a=>{let s=c.getCatesian3FromPX(this.viewer,a.position);if(!s)return!1;e.length==0?(e.push(s.clone()),e.push(s.clone()),e.push(s),o.push(this.d.drawPoint(s)),o.push(this.d.drawLine(new Cesium.CallbackProperty(()=>e.length==3?[e[n?0:1],e[2]]:[e[0],e[0]],!1),new Cesium.CallbackProperty(function(){return c.getPolylineCenter(e)},!1),f({arcType:Cesium.ArcType.NONE,material:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.GRAY,gapColor:Cesium.Color.TRANSPARENT,dashLength:20})},t))),o.push(this.d.drawLine(new Cesium.CallbackProperty(()=>e.length==3?[e[n?1:0],e[2]]:[e[0],e[0]],!1),new Cesium.CallbackProperty(function(){return c.getPolylineCenter(e)},!1),f({arcType:Cesium.ArcType.NONE,material:Cesium.Color.YELLOW},t))),o.push(this.d.drawText(new Cesium.CallbackProperty(()=>c.getPolylineCenter([e[n?1:0],e[2]]),!1),{text:new Cesium.CallbackProperty(function(){return i},!1),font:"14px sans-serif",fillColor:Cesium.Color.GOLD,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.CENTER,horizontalOrigin:Cesium.HorizontalOrigin.CENTER,pixelOffset:new Cesium.Cartesian2(0,0),disableDepthTestDistance:Number.POSITIVE_INFINITY}))):(this.measureEnd(),o.push(this.d.drawPoint(e[1])))},Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.setInputAction(a=>{let s=c.getCatesian3FromPX(this.viewer,a.endPosition);if(!s)return!1;if(e.length>0){e.pop(),e.pop(),e.push(s);var l=Cesium.Cartographic.fromCartesian(e[0]),u=Cesium.Cartographic.fromCartesian(s);n=l.height>u.height;var C=Cesium.Cartesian3.fromDegrees(Cesium.Math.toDegrees((n?u:l).longitude),Cesium.Math.toDegrees((n?u:l).latitude),(n?l:u).height);e.push(C),i="垂直距离："+this.getLengthText([e[n?1:0],e[2]])}this.tipPosition=s},Cesium.ScreenSpaceEventType.MOUSE_MOVE),this.handler.setInputAction(a=>{this.measureEnd(),e.length&&this.d.remove(o)},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}measureHDistance(t={}){this.measureEnd();var e=[],i=null,o=[],n=!0;h.emit("draw",{from:"Measure"}),this.tipEntity=this.d.drawText(new Cesium.CallbackProperty(()=>this.tipPosition,!1),{text:new Cesium.CallbackProperty(function(){return e.length==0?"点击选择起点,右击结束":"点击选择终点,右击结束"},!1),font:"12px sans-serif",fillColor:Cesium.Color.WHITE,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.TOP,horizontalOrigin:Cesium.HorizontalOrigin.LEFT,pixelOffset:new Cesium.Cartesian2(5,5),showBackground:!0,backgroundColor:new Cesium.Color(.165,.165,.165,.8),backgroundPadding:new Cesium.Cartesian2(6,6),disableDepthTestDistance:Number.POSITIVE_INFINITY}),this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.handler.setInputAction(a=>{let s=c.getCatesian3FromPX(this.viewer,a.position);if(!s)return!1;e.length==0?(e.push(s.clone()),e.push(s.clone()),e.push(s),o.push(this.d.drawPoint(s)),o.push(this.d.drawLine(new Cesium.CallbackProperty(()=>e.length==3?[e[n?0:1],e[2]]:[e[0],e[0]],!1),new Cesium.CallbackProperty(function(){return c.getPolylineCenter(e)},!1),f({arcType:Cesium.ArcType.NONE,material:Cesium.Color.YELLOW},t))),o.push(this.d.drawLine(new Cesium.CallbackProperty(()=>e.length==3?[e[n?1:0],e[2]]:[e[0],e[0]],!1),new Cesium.CallbackProperty(function(){return c.getPolylineCenter(e)},!1),f({arcType:Cesium.ArcType.NONE,material:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.GRAY,gapColor:Cesium.Color.TRANSPARENT,dashLength:20})},t))),o.push(this.d.drawText(new Cesium.CallbackProperty(()=>c.getPolylineCenter([e[n?0:1],e[2]]),!1),{text:new Cesium.CallbackProperty(function(){return i},!1),font:"14px sans-serif",fillColor:Cesium.Color.GOLD,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.CENTER,horizontalOrigin:Cesium.HorizontalOrigin.CENTER,pixelOffset:new Cesium.Cartesian2(0,0),disableDepthTestDistance:Number.POSITIVE_INFINITY}))):(this.measureEnd(),o.push(this.d.drawPoint(e[1])))},Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.setInputAction(a=>{let s=c.getCatesian3FromPX(this.viewer,a.endPosition);if(!s)return!1;if(e.length>0){e.pop(),e.pop(),e.push(s);var l=Cesium.Cartographic.fromCartesian(e[0]),u=Cesium.Cartographic.fromCartesian(s);n=l.height>u.height;var C=Cesium.Cartesian3.fromDegrees(Cesium.Math.toDegrees((n?u:l).longitude),Cesium.Math.toDegrees((n?u:l).latitude),(n?l:u).height);e.push(C),i="水平距离："+this.getLengthText([e[n?0:1],e[2]])}this.tipPosition=s},Cesium.ScreenSpaceEventType.MOUSE_MOVE),this.handler.setInputAction(a=>{this.measureEnd(),e.length&&this.d.remove(o)},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}measureAngle(t={}){this.measureEnd();var e=[],i=null,o=null,n="",a=[];h.emit("draw",{from:"Measure"}),this.tipEntity=this.d.drawText(new Cesium.CallbackProperty(()=>this.tipPosition,!1),{text:new Cesium.CallbackProperty(function(){return"点击开始,右击结束"},!1),font:"12px sans-serif",fillColor:Cesium.Color.WHITE,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.TOP,horizontalOrigin:Cesium.HorizontalOrigin.LEFT,pixelOffset:new Cesium.Cartesian2(5,5),showBackground:!0,backgroundColor:new Cesium.Color(.165,.165,.165,.8),backgroundPadding:new Cesium.Cartesian2(6,6),disableDepthTestDistance:Number.POSITIVE_INFINITY}),this.handler=new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas),this.handler.setInputAction(s=>{let l=c.getCatesian3FromPX(this.viewer,s.position);if(!l)return!1;e.length==0?(e.push(l.clone()),e.push(l),a.push(this.d.drawPoint(l)),o=this.d.drawLine(new Cesium.CallbackProperty(function(){return e},!1),new Cesium.CallbackProperty(function(){return c.getPolylineCenter(e)},!1),f({arcType:Cesium.ArcType.NONE,width:1,depthFailMaterial:new Cesium.PolylineDashMaterialProperty({color:Cesium.Color.CHARTREUSE,dashLength:20})},t)),a.push(o),i=this.d.drawText(new Cesium.CallbackProperty(function(){return e.length>1?e[1]:e[0]},!1),{text:new Cesium.CallbackProperty(function(){return n},!1),font:"14px sans-serif",fillColor:Cesium.Color.GOLD,style:Cesium.LabelStyle.FILL_AND_OUTLINE,outlineWidth:2,verticalOrigin:Cesium.VerticalOrigin.BOTTOM,horizontalOrigin:Cesium.HorizontalOrigin.CENTER,pixelOffset:new Cesium.Cartesian2(0,-10),disableDepthTestDistance:Number.POSITIVE_INFINITY}),a.push(i)):(e.push(l),a.push(this.d.drawPoint(l)),e.length>3&&(e.pop(),this.measureEnd(),n=this.getAngleText(e)))},Cesium.ScreenSpaceEventType.LEFT_CLICK),this.handler.setInputAction(s=>{let l=c.getCatesian3FromPX(this.viewer,s.endPosition);if(!l)return!1;e.length>0&&(e.pop(),e.push(l),n=this.getAngleText(e)),this.tipPosition=l},Cesium.ScreenSpaceEventType.MOUSE_MOVE),this.handler.setInputAction(s=>{this.measureEnd(),e.length&&this.d.remove(a)},Cesium.ScreenSpaceEventType.RIGHT_CLICK)}removeMeasure(){this.measureEnd(),this.d.clear()}}function ye(){const r=T.viewer.scene;return console.log(r.mode,Cesium.SceneMode.SCENE3D),T.keepViewer.update(),r.mode==Cesium.SceneMode.SCENE3D?r.morphTo2D(0):r.mode==Cesium.SceneMode.SCENE2D&&r.morphTo3D(0),T.keepViewer.keep(),r.mode}function we(r){const t=r||T.viewer,e=t.camera.positionCartographic.clone();t.camera.moveForward(e.height*.5)}function Te(r){const t=r||T.viewer,e=t.camera.positionCartographic.clone();t.camera.moveBackward(e.height*1)}function Ee(r){const t=r||T.viewer,e=Cesium.Camera.DEFAULT_VIEW_RECTANGLE;t.camera.flyTo({destination:e})}function ve(r){return r=r||document.body,Cesium.Fullscreen.enabled?(Cesium.Fullscreen.fullscreen?Cesium.Fullscreen.exitFullscreen():Cesium.Fullscreen.requestFullscreen(r),Cesium.Fullscreen.fullscreen):!1}let g=null;function Pe(r,t={clampToGround:!0}){P(),g.measurePolyLine(t)}function Ie(r,t={}){P(),g.measureHeight(t)}function Oe(r,t={clampToGround:!0}){P(),g.measurePolygon(t)}function Le(r,t={}){P(),g.measureAngle(t)}function be(r,t={}){P(),g.measureLDistance(t)}function Se(r,t={}){P(),g.measureHDistance(t)}function xe(r,t={}){P(),g.measureVDistance(t)}function P(){const r=T.viewer;g=g||new fe(r),h.emit("captureEnd",{target:"measure"})}h.on("clearAll",r=>{g&&g.removeMeasure()});h.on("captureEnd",r=>{g&&g.measureEnd()});function Ne(){N.confirm("清除","确定要清除吗?",()=>{h.emit("captureEnd",{target:"tool"}),h.emit("clearAll",{target:"tool"})})}const De=(r,t)=>{if(r!==null)if(window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(r,t);else{const e=document.createElement("a"),i=document.querySelector("body");e.href=window.URL.createObjectURL(r),e.download=t,e.style.display="none",i.append(e),e.click(),e.remove(),window.URL.revokeObjectURL(e.href)}};function _e(r){const t=r||T.viewer;return new Promise((i,o)=>{try{c.boxSelect(t,{offsetTop:64}).then(n=>{const a=N.info("地图截取","正在生成图片..."),s=t.scene.canvas;ge(s,n.rectangle).then(l=>{l.toBlob(u=>{i(u),N.close(a),N.info("地图截取","地图截取成功并保存")},"image/png")})})}catch(n){o(n)}}).then(i=>{i&&De(i,`${Date.now()}.png`)})}function ke(r){const t=r||T.viewer,e=t.camera.positionCartographic.clone().height,i=c.getMapCenter(t)||c.getDefaultViewCenter();t.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(i.lng,i.lat,e/Math.abs(Math.sin(t.camera.pitch))),orientation:{heading:t.camera.heading,pitch:Cesium.Math.toRadians(-90),roll:0}})}function Ae(r){const t=T.viewer,e=t.scene,i=new Cesium.ScreenSpaceEventHandler(e.canvas);let o=null,n=!1;i.setInputAction(s=>{const l=s.position,u=t.scene.pickPosition(l);if(!u){console.error("无法拾取位置");return}n?(n=!1,o=null):(o=u,Cesium.Cartographic.fromCartesian(o),t.entities.add({rectangle:{coordinates:new Cesium.CallbackProperty(()=>{if(!o||!n)return;const C=Cesium.Cartographic.fromCartesian(o),m=t.scene.pickPosition(a);if(!m)return;const I=Cesium.Cartographic.fromCartesian(m);return Cesium.Rectangle.fromCartographicArray([C,I])},!1),material:Cesium.Color.BLUE.withAlpha(.5),outline:!0,outlineColor:Cesium.Color.BLACK}}),n=!0)},Cesium.ScreenSpaceEventType.LEFT_CLICK);let a=null;i.setInputAction(s=>{!n||!o||(a=s.endPosition,t.scene.requestRender())},Cesium.ScreenSpaceEventType.MOUSE_MOVE)}function Fe(r){de.emit("getlocation")}function Me(){}const at={onStartSpaceQuery:Ae,morph:ye,zoomIn:we,zoomOut:Te,fullMap:Ee,fullScreen:ve,calLength:Pe,calAngle:Le,calHeight:Ie,calArea:Oe,removeAll:Ne,cameraPhoto:_e,fushi:ke,getLocation:Fe,calLDistance:be,calHDistance:Se,calVDistance:xe,fly:Me};function Ve(r){let t=[],e=r.shapes;if(e.length>0){if(e[0].polygon){const o=e[0].polygon.hierarchy.getValue(Cesium.JulianDate.now()).positions;for(let n=0;n<o.length;n++){const a=Cesium.Cartographic.fromCartesian(o[n]),s=Cesium.Math.toDegrees(a.longitude),l=Cesium.Math.toDegrees(a.latitude);a.height,t.push([s,l])}return{coordinates:t}}if(e[0].rectangle){const i=e[0].rectangle.coordinates.getValue(Cesium.JulianDate.now()),o=Cesium.Math.toDegrees(i.west),n=Cesium.Math.toDegrees(i.south),a=Cesium.Math.toDegrees(i.east),s=Cesium.Math.toDegrees(i.north);return t=[[o,n],[o,s],[a,s],[a,n]],{coordinates:t}}if(e[0].ellipse){const i=e[0].position.getValue(Cesium.JulianDate.now()),o=Cesium.Cartographic.fromCartesian(i),n=[Cesium.Math.toDegrees(o.longitude),Cesium.Math.toDegrees(o.latitude)],a=e[0].ellipse.semiMajorAxis.getValue(Cesium.JulianDate.now()),s=e[0].ellipse.semiMinorAxis.getValue(Cesium.JulianDate.now());return console.log("椭圆的中心点：",n),console.log("椭圆的半长轴：",a,"米"),console.log("椭圆的半短轴：",s,"米"),{coordinates:[n],radius:a}}}return{coordinates:t}}const Re={key:0},He={class:"spatial-query-container"},ze={class:"query-area"},Ue={style:{"margin-top":"16px","margin-bottom":"10px","text-align":"right"}},We={class:"query-result"},Ge=se({__name:"SpaceQuery",props:["visible","checkOptLayers"],emits:["startQuery","onCLoseSpaceQuery"],setup(r,{emit:t}){const e=r,i=t,o=H(!0);ae(()=>e.visible,(d,p)=>{console.log(`count changed from ${p} to ${d}`)}),oe(()=>{h.on("clearData",s)});const n=H(""),a=d=>{s(),I(),n.value=d,m.reload(),R.add(d)},s=()=>{h.emit("captureEnd",{target:"tool"}),h.emit("clearAll",{target:"tool"}),o.value=!0,n.value=""},l=d=>{if(!d)return[];let p=[];return d.forEach(E=>{p.push({layerId:E.layerId,dataType:E.dataType})}),p},u={columns:Ce,pagerConfig:{enabled:!1},height:"300px",proxyConfig:{ajax:{query:(E,$)=>S(null,[E,$],function*({page:d},p){if(n.value=="")return{total:0,items:[]};let _=e.checkOptLayers,O=l(_),L=Ve(R);if(L.coordinates.length==0)return{total:0,items:[]};let F={page:{current:d.currentPage,size:d.pageSize,searchCount:!0},spatialRegionType:n.value,layers:O,coordinates:L.coordinates};L.radius&&(F.radius=L.radius);let b=yield pe(F);return b||(b=[]),{total:b&&b.length,items:b}})}},rowConfig:{isHover:!0}},[C,m]=re({gridOptions:u,gridClass:"custom-table-class"}),I=()=>{m.reload()},W=()=>{s(),I()},G=()=>S(null,null,function*(){console.log("Query performed for shape:",n.value),n.value.length==0&&(ce("请先选择几何图形！"),reutrn),m.reload()}),B=()=>{i("onCancel",{})},Y=()=>{i("onCLoseSpaceQuery",{})};function K(d){me(d.searchKey)}return(d,p)=>{const E=k("a-button"),$=k("a-space"),_=k("a-card");return e.visible?(U(),le("view",Re,[y("a",{class:"foldMenu text-lin",onClick:Y},"隐藏"),o.value?(U(),ue(_,{key:0,class:"spaceQueryModal",width:"900px",onCancel:B,bodyStyle:"padding:0px"},{default:v(()=>[y("div",He,[y("div",ze,[p[8]||(p[8]=y("div",{style:{"margin-bottom":"10px"}},[y("h3",null,"查询范围"),y("div",{class:"text-light",style:{color:"#9a9999"}},"(选择下列几何图形工具在界面上绘制需要查询的空间范围，仅支持倾斜摄影、矢量、栅格数据)")],-1)),w($,{size:"large"},{default:v(()=>[w(E,{type:n.value==="circle"?"primary":"default",onClick:p[0]||(p[0]=O=>a("circle")),class:"selectButton flex flex-col-center"},{default:v(()=>[w(x(Z),{class:"size-8"}),p[3]||(p[3]=y("div",{style:{"margin-top":"2px"}},"圆形",-1))]),_:1},8,["type"]),w(E,{type:n.value==="polygon"?"primary":"default",onClick:p[1]||(p[1]=O=>a("polygon")),class:"selectButton flex flex-col-center"},{default:v(()=>[w(x(ee),{class:"size-8"}),p[4]||(p[4]=y("div",{style:{"margin-top":"2px"}},"多边形",-1))]),_:1},8,["type"]),w(E,{type:n.value==="rectangle"?"primary":"default",onClick:p[2]||(p[2]=O=>a("rectangle")),class:"selectButton flex flex-col-center"},{default:v(()=>[w(x(te),{class:"size-8"}),p[5]||(p[5]=y("div",{style:{"margin-top":"2px"}},"矩形",-1))]),_:1},8,["type"])]),_:1}),y("div",Ue,[w(E,{style:{"margin-right":"10px"},type:"primary",onClick:W},{default:v(()=>p[6]||(p[6]=[A("清除")])),_:1}),w(E,{type:"primary",onClick:G},{default:v(()=>p[7]||(p[7]=[A("查询")])),_:1})])]),y("div",We,[p[10]||(p[10]=y("h3",null,"查询结果",-1)),w(x(C),null,{action:v(({row:O})=>[w(E,{class:"actionButton",type:"link",onClick:L=>K(O)},{default:v(()=>p[9]||(p[9]=[A(" 导出 ")])),_:2},1032,["onClick"])]),_:1})])])]),_:1})):z("",!0)])):z("",!0)}}}),Be=ie(Ge,[["__scopeId","data-v-d8863aaa"]]),ot=Object.freeze(Object.defineProperty({__proto__:null,default:Be},Symbol.toStringTag,{value:"Module"}));export{Be as S,de as V,nt as a,rt as b,ot as c,Ve as g,it as l,at as m,st as q};
