{"version": 3, "sources": ["../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/global.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/constants/values.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/constants/PrimeMeridian.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/constants/units.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/match.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projString.js", "../../../../../node_modules/.pnpm/wkt-parser@1.5.2/node_modules/wkt-parser/PROJJSONBuilderBase.js", "../../../../../node_modules/.pnpm/wkt-parser@1.5.2/node_modules/wkt-parser/PROJJSONBuilder2015.js", "../../../../../node_modules/.pnpm/wkt-parser@1.5.2/node_modules/wkt-parser/PROJJSONBuilder2019.js", "../../../../../node_modules/.pnpm/wkt-parser@1.5.2/node_modules/wkt-parser/buildPROJJSON.js", "../../../../../node_modules/.pnpm/wkt-parser@1.5.2/node_modules/wkt-parser/detectWKTVersion.js", "../../../../../node_modules/.pnpm/wkt-parser@1.5.2/node_modules/wkt-parser/parser.js", "../../../../../node_modules/.pnpm/wkt-parser@1.5.2/node_modules/wkt-parser/process.js", "../../../../../node_modules/.pnpm/wkt-parser@1.5.2/node_modules/wkt-parser/util.js", "../../../../../node_modules/.pnpm/wkt-parser@1.5.2/node_modules/wkt-parser/transformPROJJSON.js", "../../../../../node_modules/.pnpm/wkt-parser@1.5.2/node_modules/wkt-parser/index.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/defs.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/parseCode.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/extend.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/msfnz.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/sign.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/adjust_lon.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/tsfnz.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/phi2z.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/merc.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/longlat.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/constants/Ellipsoid.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/deriveConstants.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/constants/Datum.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/datum.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/nadgrid.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/Proj.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/datumUtils.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/datum_transform.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/adjust_axis.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/toPoint.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/checkSanity.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/transform.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/core.js", "../../../../../node_modules/.pnpm/mgrs@1.0.0/node_modules/mgrs/mgrs.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/Point.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/pj_enfn.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/pj_mlfn.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/pj_inv_mlfn.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/tmerc.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/sinh.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/hypot.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/log1py.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/asinhy.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/gatg.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/clens.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/cosh.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/clens_cmplx.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/etmerc.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/adjust_zone.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/utm.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/srat.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/gauss.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/sterea.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/stere.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/somerc.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/omerc.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/lcc.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/krovak.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/mlfn.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/e0fn.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/e1fn.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/e2fn.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/e3fn.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/gN.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/adjust_lat.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/imlfn.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/cass.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/qsfnz.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/laea.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/asinz.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/aea.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/gnom.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/iqsfnz.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/cea.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/eqc.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/poly.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/nzmg.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/mill.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/sinu.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/moll.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/eqdc.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/vandg.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/common/vincenty.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/aeqd.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/ortho.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/qsc.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/robin.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/geocent.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/tpers.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/geos.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/eqearth.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/projections/bonne.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/projs.js", "../../../../../node_modules/.pnpm/proj4@2.19.3_geotiff@2.1.3/node_modules/proj4/lib/index.js"], "sourcesContent": ["export default function (defs) {\n  defs('EPSG:4326', '+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees');\n  defs('EPSG:4269', '+title=NAD83 (long/lat) +proj=longlat +a=6378137.0 +b=6356752.31414036 +ellps=GRS80 +datum=NAD83 +units=degrees');\n  defs('EPSG:3857', '+title=WGS 84 / Pseudo-Mercator +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs');\n  // UTM WGS84\n  for (var i = 1; i <= 60; ++i) {\n    defs('EPSG:' + (32600 + i), '+proj=utm +zone=' + i + ' +datum=WGS84 +units=m');\n    defs('EPSG:' + (32700 + i), '+proj=utm +zone=' + i + ' +south +datum=WGS84 +units=m');\n  }\n\n  defs.WGS84 = defs['EPSG:4326'];\n  defs['EPSG:3785'] = defs['EPSG:3857']; // maintain backward compat, official code is 3857\n  defs.GOOGLE = defs['EPSG:3857'];\n  defs['EPSG:900913'] = defs['EPSG:3857'];\n  defs['EPSG:102113'] = defs['EPSG:3857'];\n}\n", "export var PJD_3PARAM = 1;\nexport var PJD_7PARAM = 2;\nexport var PJD_GRIDSHIFT = 3;\nexport var PJD_WGS84 = 4; // WGS84 or equivalent\nexport var PJD_NODATUM = 5; // WGS84 or equivalent\nexport var SRS_WGS84_SEMIMAJOR = 6378137.0; // only used in grid shift transforms\nexport var SRS_WGS84_SEMIMINOR = 6356752.314; // only used in grid shift transforms\nexport var SRS_WGS84_ESQUARED = 0.0066943799901413165; // only used in grid shift transforms\nexport var SEC_TO_RAD = 4.84813681109535993589914102357e-6;\nexport var HALF_PI = Math.PI / 2;\n// ellipoid pj_set_ell.c\nexport var SIXTH = 0.1666666666666666667;\n/* 1/6 */\nexport var RA4 = 0.04722222222222222222;\n/* 17/360 */\nexport var RA6 = 0.02215608465608465608;\nexport var EPSLN = 1.0e-10;\n// you'd think you could use Number.EPSILON above but that makes\n// <PERSON><PERSON><PERSON><PERSON> get into an infinate loop.\n\nexport var D2R = 0.01745329251994329577;\nexport var R2D = 57.29577951308232088;\nexport var FORTPI = Math.PI / 4;\nexport var TWO_PI = Math.PI * 2;\n// SPI is slightly greater than Math.PI, so values that exceed the -180..180\n// degree range by a tiny amount don't get wrapped. This prevents points that\n// have drifted from their original location along the 180th meridian (due to\n// floating point error) from changing their sign.\nexport var SPI = 3.14159265359;\n", "var primeMeridian = {};\n\nprimeMeridian.greenwich = 0.0; // \"0dE\",\nprimeMeridian.lisbon = -9.131906111111; // \"9d07'54.862\\\"W\",\nprimeMeridian.paris = 2.337229166667; // \"2d20'14.025\\\"E\",\nprimeMeridian.bogota = -74.080916666667; // \"74d04'51.3\\\"W\",\nprimeMeridian.madrid = -3.687938888889; // \"3d41'16.58\\\"W\",\nprimeMeridian.rome = 12.452333333333; // \"12d27'8.4\\\"E\",\nprimeMeridian.bern = 7.439583333333; // \"7d26'22.5\\\"E\",\nprimeMeridian.jakarta = 106.807719444444; // \"106d48'27.79\\\"E\",\nprimeMeridian.ferro = -17.666666666667; // \"17d40'W\",\nprimeMeridian.brussels = 4.367975; // \"4d22'4.71\\\"E\",\nprimeMeridian.stockholm = 18.058277777778; // \"18d3'29.8\\\"E\",\nprimeMeridian.athens = 23.7163375; // \"23d42'58.815\\\"E\",\nprimeMeridian.oslo = 10.722916666667; // \"10d43'22.5\\\"E\"\n\nexport default primeMeridian;\n", "export default {\n  mm: { to_meter: 0.001 },\n  cm: { to_meter: 0.01 },\n  ft: { to_meter: 0.3048 },\n  'us-ft': { to_meter: 1200 / 3937 },\n  fath: { to_meter: 1.8288 },\n  kmi: { to_meter: 1852 },\n  'us-ch': { to_meter: 20.1168402336805 },\n  'us-mi': { to_meter: 1609.34721869444 },\n  km: { to_meter: 1000 },\n  'ind-ft': { to_meter: 0.30479841 },\n  'ind-yd': { to_meter: 0.91439523 },\n  mi: { to_meter: 1609.344 },\n  yd: { to_meter: 0.9144 },\n  ch: { to_meter: 20.1168 },\n  link: { to_meter: 0.201168 },\n  dm: { to_meter: 0.1 },\n  in: { to_meter: 0.0254 },\n  'ind-ch': { to_meter: 20.11669506 },\n  'us-in': { to_meter: 0.025400050800101 },\n  'us-yd': { to_meter: 0.914401828803658 }\n};\n", "var ignoredChar = /[\\s_\\-\\/\\(\\)]/g;\nexport default function match(obj, key) {\n  if (obj[key]) {\n    return obj[key];\n  }\n  var keys = Object.keys(obj);\n  var lkey = key.toLowerCase().replace(ignoredChar, '');\n  var i = -1;\n  var testkey, processedKey;\n  while (++i < keys.length) {\n    testkey = keys[i];\n    processedKey = testkey.toLowerCase().replace(ignoredChar, '');\n    if (processedKey === lkey) {\n      return obj[testkey];\n    }\n  }\n}\n", "import { D2R } from './constants/values';\nimport PrimeMeridian from './constants/PrimeMeridian';\nimport units from './constants/units';\nimport match from './match';\n\n/**\n * @param {string} defData\n * @returns {import('./defs').ProjectionDefinition}\n */\nexport default function (defData) {\n  /** @type {import('./defs').ProjectionDefinition} */\n  var self = {};\n  var paramObj = defData.split('+').map(function (v) {\n    return v.trim();\n  }).filter(function (a) {\n    return a;\n  }).reduce(function (p, a) {\n    /** @type {Array<?>} */\n    var split = a.split('=');\n    split.push(true);\n    p[split[0].toLowerCase()] = split[1];\n    return p;\n  }, {});\n  var paramName, paramVal, paramOutname;\n  var params = {\n    proj: 'projName',\n    datum: 'datumCode',\n    rf: function (v) {\n      self.rf = parseFloat(v);\n    },\n    lat_0: function (v) {\n      self.lat0 = v * D2R;\n    },\n    lat_1: function (v) {\n      self.lat1 = v * D2R;\n    },\n    lat_2: function (v) {\n      self.lat2 = v * D2R;\n    },\n    lat_ts: function (v) {\n      self.lat_ts = v * D2R;\n    },\n    lon_0: function (v) {\n      self.long0 = v * D2R;\n    },\n    lon_1: function (v) {\n      self.long1 = v * D2R;\n    },\n    lon_2: function (v) {\n      self.long2 = v * D2R;\n    },\n    alpha: function (v) {\n      self.alpha = parseFloat(v) * D2R;\n    },\n    gamma: function (v) {\n      self.rectified_grid_angle = parseFloat(v) * D2R;\n    },\n    lonc: function (v) {\n      self.longc = v * D2R;\n    },\n    x_0: function (v) {\n      self.x0 = parseFloat(v);\n    },\n    y_0: function (v) {\n      self.y0 = parseFloat(v);\n    },\n    k_0: function (v) {\n      self.k0 = parseFloat(v);\n    },\n    k: function (v) {\n      self.k0 = parseFloat(v);\n    },\n    a: function (v) {\n      self.a = parseFloat(v);\n    },\n    b: function (v) {\n      self.b = parseFloat(v);\n    },\n    r: function (v) {\n      self.a = self.b = parseFloat(v);\n    },\n    r_a: function () {\n      self.R_A = true;\n    },\n    zone: function (v) {\n      self.zone = parseInt(v, 10);\n    },\n    south: function () {\n      self.utmSouth = true;\n    },\n    towgs84: function (v) {\n      self.datum_params = v.split(',').map(function (a) {\n        return parseFloat(a);\n      });\n    },\n    to_meter: function (v) {\n      self.to_meter = parseFloat(v);\n    },\n    units: function (v) {\n      self.units = v;\n      var unit = match(units, v);\n      if (unit) {\n        self.to_meter = unit.to_meter;\n      }\n    },\n    from_greenwich: function (v) {\n      self.from_greenwich = v * D2R;\n    },\n    pm: function (v) {\n      var pm = match(PrimeMeridian, v);\n      self.from_greenwich = (pm ? pm : parseFloat(v)) * D2R;\n    },\n    nadgrids: function (v) {\n      if (v === '@null') {\n        self.datumCode = 'none';\n      } else {\n        self.nadgrids = v;\n      }\n    },\n    axis: function (v) {\n      var legalAxis = 'ewnsud';\n      if (v.length === 3 && legalAxis.indexOf(v.substr(0, 1)) !== -1 && legalAxis.indexOf(v.substr(1, 1)) !== -1 && legalAxis.indexOf(v.substr(2, 1)) !== -1) {\n        self.axis = v;\n      }\n    },\n    approx: function () {\n      self.approx = true;\n    }\n  };\n  for (paramName in paramObj) {\n    paramVal = paramObj[paramName];\n    if (paramName in params) {\n      paramOutname = params[paramName];\n      if (typeof paramOutname === 'function') {\n        paramOutname(paramVal);\n      } else {\n        self[paramOutname] = paramVal;\n      }\n    } else {\n      self[paramName] = paramVal;\n    }\n  }\n  if (typeof self.datumCode === 'string' && self.datumCode !== 'WGS84') {\n    self.datumCode = self.datumCode.toLowerCase();\n  }\n  return self;\n}\n", "class PROJJSONBuilderBase {\n  static getId(node) {\n    const idNode = node.find((child) => Array.isArray(child) && child[0] === 'ID');\n    if (idNode && idNode.length >= 3) {\n      return {\n        authority: idNode[1],\n        code: parseInt(idNode[2], 10),\n      };\n    }\n    return null;\n  }\n\n  static convertUnit(node, type = 'unit') {\n    if (!node || node.length < 3) {\n      return { type, name: 'unknown', conversion_factor: null };\n    }\n\n    const name = node[1];\n    const conversionFactor = parseFloat(node[2]) || null;\n\n    const idNode = node.find((child) => Array.isArray(child) && child[0] === 'ID');\n    const id = idNode\n      ? {\n        authority: idNode[1],\n        code: parseInt(idNode[2], 10),\n      }\n      : null;\n\n    return {\n      type,\n      name,\n      conversion_factor: conversionFactor,\n      id,\n    };\n  }\n\n  static convertAxis(node) {\n    const name = node[1] || 'Unknown';\n\n    // Determine the direction\n    let direction;\n    const abbreviationMatch = name.match(/^\\((.)\\)$/); // Match abbreviations like \"(E)\" or \"(N)\"\n    if (abbreviationMatch) {\n      // Use the abbreviation to determine the direction\n      const abbreviation = abbreviationMatch[1].toUpperCase();\n      if (abbreviation === 'E') direction = 'east';\n      else if (abbreviation === 'N') direction = 'north';\n      else if (abbreviation === 'U') direction = 'up';\n      else throw new Error(`Unknown axis abbreviation: ${abbreviation}`);\n    } else {\n      // Use the explicit direction provided in the AXIS node\n      direction = node[2] ? node[2].toLowerCase() : 'unknown';\n    }\n\n    const orderNode = node.find((child) => Array.isArray(child) && child[0] === 'ORDER');\n    const order = orderNode ? parseInt(orderNode[1], 10) : null;\n\n    const unitNode = node.find(\n      (child) =>\n        Array.isArray(child) &&\n        (child[0] === 'LENGTHUNIT' || child[0] === 'ANGLEUNIT' || child[0] === 'SCALEUNIT')\n    );\n    const unit = this.convertUnit(unitNode);\n\n    return {\n      name,\n      direction, // Use the valid PROJJSON direction value\n      unit,\n      order,\n    };\n  }\n\n  static extractAxes(node) {\n    return node\n      .filter((child) => Array.isArray(child) && child[0] === 'AXIS')\n      .map((axis) => this.convertAxis(axis))\n      .sort((a, b) => (a.order || 0) - (b.order || 0)); // Sort by the \"order\" property\n  }\n\n  static convert(node, result = {}) {\n\n    switch (node[0]) {\n      case 'PROJCRS':\n        result.type = 'ProjectedCRS';\n        result.name = node[1];\n        result.base_crs = node.find((child) => Array.isArray(child) && child[0] === 'BASEGEOGCRS')\n          ? this.convert(node.find((child) => Array.isArray(child) && child[0] === 'BASEGEOGCRS'))\n          : null;\n        result.conversion = node.find((child) => Array.isArray(child) && child[0] === 'CONVERSION')\n          ? this.convert(node.find((child) => Array.isArray(child) && child[0] === 'CONVERSION'))\n          : null;\n\n        const csNode = node.find((child) => Array.isArray(child) && child[0] === 'CS');\n        if (csNode) {\n          result.coordinate_system = {\n            type: csNode[1],\n            axis: this.extractAxes(node),\n          };\n        }\n\n        const lengthUnitNode = node.find((child) => Array.isArray(child) && child[0] === 'LENGTHUNIT');\n        if (lengthUnitNode) {\n          const unit = this.convertUnit(lengthUnitNode);\n          result.coordinate_system.unit = unit; // Add unit to coordinate_system\n        }\n\n        result.id = this.getId(node);\n        break;\n\n      case 'BASEGEOGCRS':\n      case 'GEOGCRS':\n        result.type = 'GeographicCRS';\n        result.name = node[1];\n      \n        // Handle DATUM or ENSEMBLE\n        const datumOrEnsembleNode = node.find(\n          (child) => Array.isArray(child) && (child[0] === 'DATUM' || child[0] === 'ENSEMBLE')\n        );\n        if (datumOrEnsembleNode) {\n          const datumOrEnsemble = this.convert(datumOrEnsembleNode);\n          if (datumOrEnsembleNode[0] === 'ENSEMBLE') {\n            result.datum_ensemble = datumOrEnsemble;\n          } else {\n            result.datum = datumOrEnsemble;\n          }\n          const primem = node.find((child) => Array.isArray(child) && child[0] === 'PRIMEM');\n          if (primem && primem[1] !== 'Greenwich') {\n            datumOrEnsemble.prime_meridian = {\n              name: primem[1],\n              longitude: parseFloat(primem[2]),\n            }\n          }\n        }\n      \n        result.coordinate_system = {\n          type: 'ellipsoidal',\n          axis: this.extractAxes(node),\n        };\n      \n        result.id = this.getId(node);\n        break;\n\n      case 'DATUM':\n        result.type = 'GeodeticReferenceFrame';\n        result.name = node[1];\n        result.ellipsoid = node.find((child) => Array.isArray(child) && child[0] === 'ELLIPSOID')\n          ? this.convert(node.find((child) => Array.isArray(child) && child[0] === 'ELLIPSOID'))\n          : null;\n        break;\n      \n      case 'ENSEMBLE':\n        result.type = 'DatumEnsemble';\n        result.name = node[1];\n      \n        // Extract ensemble members\n        result.members = node\n          .filter((child) => Array.isArray(child) && child[0] === 'MEMBER')\n          .map((member) => ({\n            type: 'DatumEnsembleMember',\n            name: member[1],\n            id: this.getId(member), // Extract ID as { authority, code }\n          }));\n      \n        // Extract accuracy\n        const accuracyNode = node.find((child) => Array.isArray(child) && child[0] === 'ENSEMBLEACCURACY');\n        if (accuracyNode) {\n          result.accuracy = parseFloat(accuracyNode[1]);\n        }\n      \n        // Extract ellipsoid\n        const ellipsoidNode = node.find((child) => Array.isArray(child) && child[0] === 'ELLIPSOID');\n        if (ellipsoidNode) {\n          result.ellipsoid = this.convert(ellipsoidNode); // Convert the ellipsoid node\n        }\n      \n        // Extract identifier for the ensemble\n        result.id = this.getId(node);\n        break;\n\n      case 'ELLIPSOID':\n        result.type = 'Ellipsoid';\n        result.name = node[1];\n        result.semi_major_axis = parseFloat(node[2]);\n        result.inverse_flattening = parseFloat(node[3]);\n        const units = node.find((child) => Array.isArray(child) && child[0] === 'LENGTHUNIT')\n          ? this.convert(node.find((child) => Array.isArray(child) && child[0] === 'LENGTHUNIT'), result)\n          : null;\n        break;\n\n      case 'CONVERSION':\n        result.type = 'Conversion';\n        result.name = node[1];\n        result.method = node.find((child) => Array.isArray(child) && child[0] === 'METHOD')\n          ? this.convert(node.find((child) => Array.isArray(child) && child[0] === 'METHOD'))\n          : null;\n        result.parameters = node\n          .filter((child) => Array.isArray(child) && child[0] === 'PARAMETER')\n          .map((param) => this.convert(param));\n        break;\n\n      case 'METHOD':\n        result.type = 'Method';\n        result.name = node[1];\n        result.id = this.getId(node);\n        break;\n\n      case 'PARAMETER':\n        result.type = 'Parameter';\n        result.name = node[1];\n        result.value = parseFloat(node[2]);\n        result.unit = this.convertUnit(\n          node.find(\n            (child) =>\n              Array.isArray(child) &&\n              (child[0] === 'LENGTHUNIT' || child[0] === 'ANGLEUNIT' || child[0] === 'SCALEUNIT')\n          )\n        );\n        result.id = this.getId(node);\n        break;\n\n      case 'BOUNDCRS':\n        result.type = 'BoundCRS';\n\n        // Process SOURCECRS\n        const sourceCrsNode = node.find((child) => Array.isArray(child) && child[0] === 'SOURCECRS');\n        if (sourceCrsNode) {\n          const sourceCrsContent = sourceCrsNode.find((child) => Array.isArray(child));\n          result.source_crs = sourceCrsContent ? this.convert(sourceCrsContent) : null;\n        }\n\n        // Process TARGETCRS\n        const targetCrsNode = node.find((child) => Array.isArray(child) && child[0] === 'TARGETCRS');\n        if (targetCrsNode) {\n          const targetCrsContent = targetCrsNode.find((child) => Array.isArray(child));\n          result.target_crs = targetCrsContent ? this.convert(targetCrsContent) : null;\n        }\n\n        // Process ABRIDGEDTRANSFORMATION\n        const transformationNode = node.find((child) => Array.isArray(child) && child[0] === 'ABRIDGEDTRANSFORMATION');\n        if (transformationNode) {\n          result.transformation = this.convert(transformationNode);\n        } else {\n          result.transformation = null;\n        }\n        break;\n\n      case 'ABRIDGEDTRANSFORMATION':\n        result.type = 'Transformation';\n        result.name = node[1];\n        result.method = node.find((child) => Array.isArray(child) && child[0] === 'METHOD')\n          ? this.convert(node.find((child) => Array.isArray(child) && child[0] === 'METHOD'))\n          : null;\n\n        result.parameters = node\n          .filter((child) => Array.isArray(child) && (child[0] === 'PARAMETER' || child[0] === 'PARAMETERFILE'))\n          .map((param) => {\n            if (param[0] === 'PARAMETER') {\n              return this.convert(param);\n            } else if (param[0] === 'PARAMETERFILE') {\n              return {\n                name: param[1],\n                value: param[2],\n                id: {\n                  'authority': 'EPSG',\n                  'code': 8656\n                }\n              };\n            }\n          });\n\n        // Adjust the Scale difference parameter if present\n        if (result.parameters.length === 7) {\n          const scaleDifference = result.parameters[6];\n          if (scaleDifference.name === 'Scale difference') {\n            scaleDifference.value = Math.round((scaleDifference.value - 1) * 1e12) / 1e6;\n          }\n        }\n\n        result.id = this.getId(node);\n        break;\n      \n      case 'AXIS':\n        if (!result.coordinate_system) {\n          result.coordinate_system = { type: 'unspecified', axis: [] };\n        }\n        result.coordinate_system.axis.push(this.convertAxis(node));\n        break;\n      \n      case 'LENGTHUNIT':\n        const unit = this.convertUnit(node, 'LinearUnit');\n        if (result.coordinate_system && result.coordinate_system.axis) {\n          result.coordinate_system.axis.forEach((axis) => {\n            if (!axis.unit) {\n              axis.unit = unit;\n            }\n          });\n        }\n        if (unit.conversion_factor && unit.conversion_factor !== 1) {\n          if (result.semi_major_axis) {\n            result.semi_major_axis = {\n              value: result.semi_major_axis,\n              unit,\n            }\n          }\n        }\n        break;\n\n      default:\n        result.keyword = node[0];\n        break;\n    }\n\n    return result;\n  }\n}\n\nexport default PROJJSONBuilderBase;", "import PROJJSONBuilderBase from './PROJJSONBuilderBase.js';\n\nclass PROJJSONBuilder2015 extends PROJJSONBuilderBase {\n  static convert(node, result = {}) {\n    super.convert(node, result);\n\n    // Skip `CS` and `USAGE` nodes for WKT2-2015\n    if (result.coordinate_system && result.coordinate_system.subtype === 'Cartesian') {\n      delete result.coordinate_system;\n    }\n    if (result.usage) {\n      delete result.usage;\n    }\n\n    return result;\n  }\n}\n\nexport default PROJJSONBuilder2015;", "import PROJJSONBuilderBase from './PROJJSONBuilderBase.js';\n\nclass PROJJSONBuilder2019 extends PROJJSONBuilderBase {\n  static convert(node, result = {}) {\n    super.convert(node, result);\n\n    // Handle `CS` node for WKT2-2019\n    const csNode = node.find((child) => Array.isArray(child) && child[0] === 'CS');\n    if (csNode) {\n      result.coordinate_system = {\n        subtype: csNode[1],\n        axis: this.extractAxes(node),\n      };\n    }\n\n    // Handle `USAGE` node for WKT2-2019\n    const usageNode = node.find((child) => Array.isArray(child) && child[0] === 'USAGE');\n    if (usageNode) {\n      const scope = usageNode.find((child) => Array.isArray(child) && child[0] === 'SCOPE');\n      const area = usageNode.find((child) => Array.isArray(child) && child[0] === 'AREA');\n      const bbox = usageNode.find((child) => Array.isArray(child) && child[0] === 'BBOX');\n      result.usage = {};\n      if (scope) {\n        result.usage.scope = scope[1];\n      }\n      if (area) {\n        result.usage.area = area[1];\n      }\n      if (bbox) {\n        result.usage.bbox = bbox.slice(1);\n      }\n    }\n\n    return result;\n  }\n}\n\nexport default PROJJSONBuilder2019;", "import PROJJSONBuilder2015 from './PROJJSONBuilder2015.js';\nimport PROJJSONBuilder2019 from './PROJJSONBuilder2019.js';\n\n/**\n * Detects the WKT2 version based on the structure of the WKT.\n * @param {Array} root The root WKT array node.\n * @returns {string} The detected version (\"2015\" or \"2019\").\n */\nfunction detectWKT2Version(root) {\n  // Check for WKT2-2019-specific nodes\n  if (root.find((child) => Array.isArray(child) && child[0] === 'USAGE')) {\n    return '2019'; // `USAGE` is specific to WKT2-2019\n  }\n\n  // Check for WKT2-2015-specific nodes\n  if (root.find((child) => Array.isArray(child) && child[0] === 'CS')) {\n    return '2015'; // `CS` is valid in both, but default to 2015 unless `USAGE` is present\n  }\n\n  if (root[0] === 'BOUNDCRS' || root[0] === 'PROJCRS' || root[0] === 'GEOGCRS') {\n    return '2015'; // These are valid in both, but default to 2015\n  }\n\n  // Default to WKT2-2015 if no specific indicators are found\n  return '2015';\n}\n\n/**\n * Builds a PROJJSON object from a WKT array structure.\n * @param {Array} root The root WKT array node.\n * @returns {Object} The PROJJSON object.\n */\nexport function buildPROJJSON(root) {\n  const version = detectWKT2Version(root);\n  const builder = version === '2019' ? PROJJSONBuilder2019 : PROJJSONBuilder2015;\n  return builder.convert(root);\n}\n", "/**\n * Detects whether the WKT string is WKT1 or WKT2.\n * @param {string} wkt The WKT string.\n * @returns {string} The detected version (\"WKT1\" or \"WKT2\").\n */\nexport function detectWKTVersion(wkt) {\n  // Normalize the WKT string for easier keyword matching\n  const normalizedWKT = wkt.toUpperCase();\n\n  // Check for WKT2-specific keywords\n  if (\n    normalizedWKT.includes('PROJCRS') ||\n    normalizedWKT.includes('GEOGCRS') ||\n    normalizedWKT.includes('BOUNDCRS') ||\n    normalizedWKT.includes('VERTCRS') ||\n    normalizedWKT.includes('LENGTHUNIT') ||\n    normalizedWKT.includes('ANGLEUNIT') ||\n    normalizedWKT.includes('SCALEUNIT')\n  ) {\n    return 'WKT2';\n  }\n\n  // Check for WKT1-specific keywords\n  if (\n    normalizedWKT.includes('PROJCS') ||\n    normalizedWKT.includes('GEOGCS') ||\n    normalizedWKT.includes('LOCAL_CS') ||\n    normalizedWKT.includes('VERT_CS') ||\n    normalizedWKT.includes('UNIT')\n  ) {\n    return 'WKT1';\n  }\n\n  // Default to WKT1 if no specific indicators are found\n  return 'WKT1';\n}", "export default parseString;\n\nvar NEUTRAL = 1;\nvar KEYWORD = 2;\nvar NUMBER = 3;\nvar QUOTED = 4;\nvar AFTERQUOTE = 5;\nvar ENDED = -1;\nvar whitespace = /\\s/;\nvar latin = /[A-Za-z]/;\nvar keyword = /[A-Za-z84_]/;\nvar endThings = /[,\\]]/;\nvar digets = /[\\d\\.E\\-\\+]/;\n// const ignoredChar = /[\\s_\\-\\/\\(\\)]/g;\nfunction Parser(text) {\n  if (typeof text !== 'string') {\n    throw new Error('not a string');\n  }\n  this.text = text.trim();\n  this.level = 0;\n  this.place = 0;\n  this.root = null;\n  this.stack = [];\n  this.currentObject = null;\n  this.state = NEUTRAL;\n}\nParser.prototype.readCharicter = function() {\n  var char = this.text[this.place++];\n  if (this.state !== QUOTED) {\n    while (whitespace.test(char)) {\n      if (this.place >= this.text.length) {\n        return;\n      }\n      char = this.text[this.place++];\n    }\n  }\n  switch (this.state) {\n    case NEUTRAL:\n      return this.neutral(char);\n    case KEYWORD:\n      return this.keyword(char)\n    case QUOTED:\n      return this.quoted(char);\n    case AFTERQUOTE:\n      return this.afterquote(char);\n    case NUMBER:\n      return this.number(char);\n    case ENDED:\n      return;\n  }\n};\nParser.prototype.afterquote = function(char) {\n  if (char === '\"') {\n    this.word += '\"';\n    this.state = QUOTED;\n    return;\n  }\n  if (endThings.test(char)) {\n    this.word = this.word.trim();\n    this.afterItem(char);\n    return;\n  }\n  throw new Error('havn\\'t handled \"' +char + '\" in afterquote yet, index ' + this.place);\n};\nParser.prototype.afterItem = function(char) {\n  if (char === ',') {\n    if (this.word !== null) {\n      this.currentObject.push(this.word);\n    }\n    this.word = null;\n    this.state = NEUTRAL;\n    return;\n  }\n  if (char === ']') {\n    this.level--;\n    if (this.word !== null) {\n      this.currentObject.push(this.word);\n      this.word = null;\n    }\n    this.state = NEUTRAL;\n    this.currentObject = this.stack.pop();\n    if (!this.currentObject) {\n      this.state = ENDED;\n    }\n\n    return;\n  }\n};\nParser.prototype.number = function(char) {\n  if (digets.test(char)) {\n    this.word += char;\n    return;\n  }\n  if (endThings.test(char)) {\n    this.word = parseFloat(this.word);\n    this.afterItem(char);\n    return;\n  }\n  throw new Error('havn\\'t handled \"' +char + '\" in number yet, index ' + this.place);\n};\nParser.prototype.quoted = function(char) {\n  if (char === '\"') {\n    this.state = AFTERQUOTE;\n    return;\n  }\n  this.word += char;\n  return;\n};\nParser.prototype.keyword = function(char) {\n  if (keyword.test(char)) {\n    this.word += char;\n    return;\n  }\n  if (char === '[') {\n    var newObjects = [];\n    newObjects.push(this.word);\n    this.level++;\n    if (this.root === null) {\n      this.root = newObjects;\n    } else {\n      this.currentObject.push(newObjects);\n    }\n    this.stack.push(this.currentObject);\n    this.currentObject = newObjects;\n    this.state = NEUTRAL;\n    return;\n  }\n  if (endThings.test(char)) {\n    this.afterItem(char);\n    return;\n  }\n  throw new Error('havn\\'t handled \"' +char + '\" in keyword yet, index ' + this.place);\n};\nParser.prototype.neutral = function(char) {\n  if (latin.test(char)) {\n    this.word = char;\n    this.state = KEYWORD;\n    return;\n  }\n  if (char === '\"') {\n    this.word = '';\n    this.state = QUOTED;\n    return;\n  }\n  if (digets.test(char)) {\n    this.word = char;\n    this.state = NUMBER;\n    return;\n  }\n  if (endThings.test(char)) {\n    this.afterItem(char);\n    return;\n  }\n  throw new Error('havn\\'t handled \"' +char + '\" in neutral yet, index ' + this.place);\n};\nParser.prototype.output = function() {\n  while (this.place < this.text.length) {\n    this.readCharicter();\n  }\n  if (this.state === ENDED) {\n    return this.root;\n  }\n  throw new Error('unable to parse string \"' +this.text + '\". State is ' + this.state);\n};\n\nfunction parseString(txt) {\n  var parser = new Parser(txt);\n  return parser.output();\n}\n", "\n\nfunction mapit(obj, key, value) {\n  if (Array.isArray(key)) {\n    value.unshift(key);\n    key = null;\n  }\n  var thing = key ? {} : obj;\n\n  var out = value.reduce(function(newObj, item) {\n    sExpr(item, newObj);\n    return newObj\n  }, thing);\n  if (key) {\n    obj[key] = out;\n  }\n}\n\nexport function sExpr(v, obj) {\n  if (!Array.isArray(v)) {\n    obj[v] = true;\n    return;\n  }\n  var key = v.shift();\n  if (key === 'PARAMETER') {\n    key = v.shift();\n  }\n  if (v.length === 1) {\n    if (Array.isArray(v[0])) {\n      obj[key] = {};\n      sExpr(v[0], obj[key]);\n      return;\n    }\n    obj[key] = v[0];\n    return;\n  }\n  if (!v.length) {\n    obj[key] = true;\n    return;\n  }\n  if (key === 'TOWGS84') {\n    obj[key] = v;\n    return;\n  }\n  if (key === 'AXIS') {\n    if (!(key in obj)) {\n      obj[key] = [];\n    }\n    obj[key].push(v);\n    return;\n  }\n  if (!Array.isArray(key)) {\n    obj[key] = {};\n  }\n\n  var i;\n  switch (key) {\n    case 'UNIT':\n    case 'PRIMEM':\n    case 'VERT_DATUM':\n      obj[key] = {\n        name: v[0].toLowerCase(),\n        convert: v[1]\n      };\n      if (v.length === 3) {\n        sExpr(v[2], obj[key]);\n      }\n      return;\n    case 'SPHEROID':\n    case 'ELLIPSOID':\n      obj[key] = {\n        name: v[0],\n        a: v[1],\n        rf: v[2]\n      };\n      if (v.length === 4) {\n        sExpr(v[3], obj[key]);\n      }\n      return;\n    case 'EDATUM':\n    case 'ENGINEERINGDATUM':\n    case 'LOCAL_DATUM':\n    case 'DATUM':\n    case 'VERT_CS':\n    case 'VERTCRS':\n    case 'VERTICALCRS':\n      v[0] = ['name', v[0]];\n      mapit(obj, key, v);\n      return;\n    case 'COMPD_CS':\n    case 'COMPOUNDCRS':\n    case 'FITTED_CS':\n    // the followings are the crs defined in\n    // https://github.com/proj4js/proj4js/blob/1da4ed0b865d0fcb51c136090569210cdcc9019e/lib/parseCode.js#L11\n    case 'PROJECTEDCRS':\n    case 'PROJCRS':\n    case 'GEOGCS':\n    case 'GEOCCS':\n    case 'PROJCS':\n    case 'LOCAL_CS':\n    case 'GEODCRS':\n    case 'GEODETICCRS':\n    case 'GEODETICDATUM':\n    case 'ENGCRS':\n    case 'ENGINEERINGCRS':\n      v[0] = ['name', v[0]];\n      mapit(obj, key, v);\n      obj[key].type = key;\n      return;\n    default:\n      i = -1;\n      while (++i < v.length) {\n        if (!Array.isArray(v[i])) {\n          return sExpr(v, obj[key]);\n        }\n      }\n      return mapit(obj, key, v);\n  }\n}\n", "var D2R = 0.01745329251994329577;\n\nexport function d2r(input) {\n  return input * D2R;\n}\n\nexport function applyProjectionDefaults(wkt) {\n  // Normalize projName for WKT2 compatibility\n  const normalizedProjName = (wkt.projName || '').toLowerCase().replace(/_/g, ' ');\n\n  if (!wkt.long0 && wkt.longc && (normalizedProjName === 'albers conic equal area' || normalizedProjName === 'lambert azimuthal equal area')) {\n    wkt.long0 = wkt.longc;\n  }\n  if (!wkt.lat_ts && wkt.lat1 && (normalizedProjName === 'stereographic south pole' || normalizedProjName === 'polar stereographic (variant b)')) {\n    wkt.lat0 = d2r(wkt.lat1 > 0 ? 90 : -90);\n    wkt.lat_ts = wkt.lat1;\n    delete wkt.lat1;\n  } else if (!wkt.lat_ts && wkt.lat0 && (normalizedProjName === 'polar stereographic' || normalizedProjName === 'polar stereographic (variant a)')) {\n    wkt.lat_ts = wkt.lat0;\n    wkt.lat0 = d2r(wkt.lat0 > 0 ? 90 : -90);\n    delete wkt.lat1;\n  }\n}", "import { applyProjectionDefaults } from './util.js';\n\n// Helper function to process units and to_meter\nfunction processUnit(unit) {\n  let result = { units: null, to_meter: undefined };\n\n  if (typeof unit === 'string') {\n    result.units = unit.toLowerCase();\n    if (result.units === 'metre') {\n      result.units = 'meter'; // Normalize 'metre' to 'meter'\n    }\n    if (result.units === 'meter') {\n      result.to_meter = 1; // Only set to_meter if units are 'meter'\n    }\n  } else if (unit && unit.name) {\n    result.units = unit.name.toLowerCase();\n    if (result.units === 'metre') {\n      result.units = 'meter'; // Normalize 'metre' to 'meter'\n    }\n    result.to_meter = unit.conversion_factor;\n  }\n\n  return result;\n}\n\nfunction toValue(valueOrObject) {\n  if (typeof valueOrObject === 'object') {\n    return valueOrObject.value * valueOrObject.unit.conversion_factor;\n  }\n  return valueOrObject;\n}\n\nfunction calculateEllipsoid(value, result) {\n  if (value.ellipsoid.radius) {\n    result.a = value.ellipsoid.radius;\n    result.rf = 0;\n  } else {\n    result.a = toValue(value.ellipsoid.semi_major_axis);\n    if (value.ellipsoid.inverse_flattening !== undefined) {\n      result.rf = value.ellipsoid.inverse_flattening;\n    } else if (value.ellipsoid.semi_major_axis !== undefined && value.ellipsoid.semi_minor_axis !== undefined) {\n      result.rf = result.a / (result.a - toValue(value.ellipsoid.semi_minor_axis));\n    }\n  }\n}\n\nexport function transformPROJJSON(projjson, result = {}) {\n  if (!projjson || typeof projjson !== 'object') {\n    return projjson; // Return primitive values as-is\n  }\n\n  if (projjson.type === 'BoundCRS') {\n    transformPROJJSON(projjson.source_crs, result);\n\n    if (projjson.transformation) {\n      if (projjson.transformation.method && projjson.transformation.method.name === 'NTv2') {\n        // Set nadgrids to the filename from the parameterfile\n        result.nadgrids = projjson.transformation.parameters[0].value;\n      } else {\n        // Populate datum_params if no parameterfile is found\n        result.datum_params = projjson.transformation.parameters.map((param) => param.value);\n      }\n    }\n    return result; // Return early for BoundCRS\n  }\n\n  // Handle specific keys in PROJJSON\n  Object.keys(projjson).forEach((key) => {\n    const value = projjson[key];\n    if (value === null) {\n      return;\n    }\n\n    switch (key) {\n      case 'name':\n        if (result.srsCode) {\n          break;\n        }\n        result.name = value;\n        result.srsCode = value; // Map `name` to `srsCode`\n        break;\n\n      case 'type':\n        if (value === 'GeographicCRS') {\n          result.projName = 'longlat';\n        } else if (value === 'ProjectedCRS' && projjson.conversion && projjson.conversion.method) {\n          result.projName = projjson.conversion.method.name; // Retain original capitalization\n        }\n        break;\n\n      case 'datum':\n      case 'datum_ensemble': // Handle both datum and ensemble\n        if (value.ellipsoid) {\n          // Extract ellipsoid properties\n          result.ellps = value.ellipsoid.name;\n          calculateEllipsoid(value, result);\n        }\n        if (value.prime_meridian) {\n          result.from_greenwich = value.prime_meridian.longitude * Math.PI / 180; // Convert to radians\n        }\n        break;\n\n      case 'ellipsoid':\n        result.ellps = value.name;\n        calculateEllipsoid(value, result);\n        break;\n\n      case 'prime_meridian':\n        result.long0 = (value.longitude || 0) * Math.PI / 180; // Convert to radians\n        break;\n\n      case 'coordinate_system':\n        if (value.axis) {\n          result.axis = value.axis\n            .map((axis) => {\n              const direction = axis.direction;\n              if (direction === 'east') return 'e';\n              if (direction === 'north') return 'n';\n              if (direction === 'west') return 'w';\n              if (direction === 'south') return 's';\n              throw new Error(`Unknown axis direction: ${direction}`);\n            })\n            .join('') + 'u'; // Combine into a single string (e.g., \"enu\")\n\n          if (value.unit) {\n            const { units, to_meter } = processUnit(value.unit);\n            result.units = units;\n            result.to_meter = to_meter;\n          } else if (value.axis[0] && value.axis[0].unit) {\n            const { units, to_meter } = processUnit(value.axis[0].unit);\n            result.units = units;\n            result.to_meter = to_meter;\n          }\n        }\n        break;\n        \n      case 'id':\n        if (value.authority && value.code) {\n          result.title = value.authority + ':' + value.code;\n        }\n        break;\n\n      case 'conversion':\n        if (value.method && value.method.name) {\n          result.projName = value.method.name; // Retain original capitalization\n        }\n        if (value.parameters) {\n          value.parameters.forEach((param) => {\n            const paramName = param.name.toLowerCase().replace(/\\s+/g, '_');\n            const paramValue = param.value;\n            if (param.unit && param.unit.conversion_factor) {\n              result[paramName] = paramValue * param.unit.conversion_factor; // Convert to radians or meters\n            } else if (param.unit === 'degree') {\n              result[paramName] = paramValue * Math.PI / 180; // Convert to radians\n            } else {\n              result[paramName] = paramValue;\n            }\n          });\n        }\n        break;\n\n      case 'unit':\n        if (value.name) {\n          result.units = value.name.toLowerCase();\n          if (result.units === 'metre') {\n            result.units = 'meter';\n          }\n        }\n        if (value.conversion_factor) {\n          result.to_meter = value.conversion_factor;\n        }\n        break;\n\n      case 'base_crs':\n        transformPROJJSON(value, result); // Pass `result` directly\n        result.datumCode = value.id ? value.id.authority + '_' + value.id.code : value.name; // Set datumCode\n        break;\n\n      default:\n        // Ignore irrelevant or unneeded properties\n        break;\n    }\n  });\n\n  // Additional calculated properties\n  if (result.latitude_of_false_origin !== undefined) {\n    result.lat0 = result.latitude_of_false_origin; // Already in radians\n  }\n  if (result.longitude_of_false_origin !== undefined) {\n    result.long0 = result.longitude_of_false_origin;\n  }\n  if (result.latitude_of_standard_parallel !== undefined) {\n    result.lat0 = result.latitude_of_standard_parallel;\n    result.lat1 = result.latitude_of_standard_parallel;\n  }\n  if (result.latitude_of_1st_standard_parallel !== undefined) {\n    result.lat1 = result.latitude_of_1st_standard_parallel;\n  }\n  if (result.latitude_of_2nd_standard_parallel !== undefined) {\n    result.lat2 = result.latitude_of_2nd_standard_parallel; \n  }\n  if (result.latitude_of_projection_centre !== undefined) {\n    result.lat0 = result.latitude_of_projection_centre;\n  }\n  if (result.longitude_of_projection_centre !== undefined) {\n    result.longc = result.longitude_of_projection_centre;\n  }\n  if (result.easting_at_false_origin !== undefined) {\n    result.x0 = result.easting_at_false_origin;\n  }\n  if (result.northing_at_false_origin !== undefined) {\n    result.y0 = result.northing_at_false_origin;\n  }\n  if (result.latitude_of_natural_origin !== undefined) {\n    result.lat0 = result.latitude_of_natural_origin;\n  }\n  if (result.longitude_of_natural_origin !== undefined) {\n    result.long0 = result.longitude_of_natural_origin;\n  }\n  if (result.longitude_of_origin !== undefined) {\n    result.long0 = result.longitude_of_origin;\n  }\n  if (result.false_easting !== undefined) {\n    result.x0 = result.false_easting;\n  }\n  if (result.easting_at_projection_centre) {\n    result.x0 = result.easting_at_projection_centre;\n  }\n  if (result.false_northing !== undefined) {\n    result.y0 = result.false_northing;\n  }\n  if (result.northing_at_projection_centre) {\n    result.y0 = result.northing_at_projection_centre;\n  }\n  if (result.standard_parallel_1 !== undefined) {\n    result.lat1 = result.standard_parallel_1;\n  }\n  if (result.standard_parallel_2 !== undefined) {\n    result.lat2 = result.standard_parallel_2;\n  }\n  if (result.scale_factor_at_natural_origin !== undefined) {\n    result.k0 = result.scale_factor_at_natural_origin;\n  }\n  if (result.scale_factor_at_projection_centre !== undefined) {\n    result.k0 = result.scale_factor_at_projection_centre;\n  }\n  if (result.scale_factor_on_pseudo_standard_parallel !== undefined) {  \n    result.k0 = result.scale_factor_on_pseudo_standard_parallel;\n  }\n  if (result.azimuth !== undefined) {\n    result.alpha = result.azimuth;\n  }\n  if (result.azimuth_at_projection_centre !== undefined) {\n    result.alpha = result.azimuth_at_projection_centre;\n  }\n  if (result.angle_from_rectified_to_skew_grid) {\n    result.rectified_grid_angle = result.angle_from_rectified_to_skew_grid;\n  }\n\n  // Apply projection defaults\n  applyProjectionDefaults(result);\n\n  return result;\n}", "import { buildPROJJSON } from './buildPROJJSON.js';\nimport { detectWKTVersion } from './detectWKTVersion.js';\nimport parser from './parser.js';\nimport {sExpr} from './process.js';\nimport { transformPROJJSON } from './transformPROJJSON.js';\nimport { applyProjectionDefaults, d2r } from './util.js';\n\nvar knownTypes = ['PROJECTEDCRS', 'PROJCRS', 'GEOGCS', 'GEOCCS', 'PROJCS', 'LOCAL_CS', 'GEODCRS',\n  'GEODETICCRS', 'GEODETICDATUM', 'ENGCRS', 'ENGINEERINGCRS'];\n\nfunction rename(obj, params) {\n  var outName = params[0];\n  var inName = params[1];\n  if (!(outName in obj) && (inName in obj)) {\n    obj[outName] = obj[inName];\n    if (params.length === 3) {\n      obj[outName] = params[2](obj[outName]);\n    }\n  }\n}\n\nfunction cleanWKT(wkt) {\n  var keys = Object.keys(wkt);\n  for (var i = 0, ii = keys.length; i <ii; ++i) {\n    var key = keys[i];\n    // the followings are the crs defined in\n    // https://github.com/proj4js/proj4js/blob/1da4ed0b865d0fcb51c136090569210cdcc9019e/lib/parseCode.js#L11\n    if (knownTypes.indexOf(key) !== -1) {\n      setPropertiesFromWkt(wkt[key]);\n    }\n    if (typeof wkt[key] === 'object') {\n      cleanWKT(wkt[key]);\n    }\n  }\n}\n\nfunction setPropertiesFromWkt(wkt) {\n  if (wkt.AUTHORITY) {\n    var authority = Object.keys(wkt.AUTHORITY)[0];\n    if (authority && authority in wkt.AUTHORITY) {\n      wkt.title = authority + ':' + wkt.AUTHORITY[authority];\n    }\n  }\n  if (wkt.type === 'GEOGCS') {\n    wkt.projName = 'longlat';\n  } else if (wkt.type === 'LOCAL_CS') {\n    wkt.projName = 'identity';\n    wkt.local = true;\n  } else {\n    if (typeof wkt.PROJECTION === 'object') {\n      wkt.projName = Object.keys(wkt.PROJECTION)[0];\n    } else {\n      wkt.projName = wkt.PROJECTION;\n    }\n  }\n  if (wkt.AXIS) {\n    var axisOrder = '';\n    for (var i = 0, ii = wkt.AXIS.length; i < ii; ++i) {\n      var axis = [wkt.AXIS[i][0].toLowerCase(), wkt.AXIS[i][1].toLowerCase()];\n      if (axis[0].indexOf('north') !== -1 || ((axis[0] === 'y' || axis[0] === 'lat') && axis[1] === 'north')) {\n        axisOrder += 'n';\n      } else if (axis[0].indexOf('south') !== -1 || ((axis[0] === 'y' || axis[0] === 'lat') && axis[1] === 'south')) {\n        axisOrder += 's';\n      } else if (axis[0].indexOf('east') !== -1 || ((axis[0] === 'x' || axis[0] === 'lon') && axis[1] === 'east')) {\n        axisOrder += 'e';\n      } else if (axis[0].indexOf('west') !== -1 || ((axis[0] === 'x' || axis[0] === 'lon') && axis[1] === 'west')) {\n        axisOrder += 'w';\n      }\n    }\n    if (axisOrder.length === 2) {\n      axisOrder += 'u';\n    }\n    if (axisOrder.length === 3) {\n      wkt.axis = axisOrder;\n    }\n  }\n  if (wkt.UNIT) {\n    wkt.units = wkt.UNIT.name.toLowerCase();\n    if (wkt.units === 'metre') {\n      wkt.units = 'meter';\n    }\n    if (wkt.UNIT.convert) {\n      if (wkt.type === 'GEOGCS') {\n        if (wkt.DATUM && wkt.DATUM.SPHEROID) {\n          wkt.to_meter = wkt.UNIT.convert*wkt.DATUM.SPHEROID.a;\n        }\n      } else {\n        wkt.to_meter = wkt.UNIT.convert;\n      }\n    }\n  }\n  var geogcs = wkt.GEOGCS;\n  if (wkt.type === 'GEOGCS') {\n    geogcs = wkt;\n  }\n  if (geogcs) {\n    //if(wkt.GEOGCS.PRIMEM&&wkt.GEOGCS.PRIMEM.convert){\n    //  wkt.from_greenwich=wkt.GEOGCS.PRIMEM.convert*D2R;\n    //}\n    if (geogcs.DATUM) {\n      wkt.datumCode = geogcs.DATUM.name.toLowerCase();\n    } else {\n      wkt.datumCode = geogcs.name.toLowerCase();\n    }\n    if (wkt.datumCode.slice(0, 2) === 'd_') {\n      wkt.datumCode = wkt.datumCode.slice(2);\n    }\n    if (wkt.datumCode === 'new_zealand_1949') {\n      wkt.datumCode = 'nzgd49';\n    }\n    if (wkt.datumCode === 'wgs_1984' || wkt.datumCode === 'world_geodetic_system_1984') {\n      if (wkt.PROJECTION === 'Mercator_Auxiliary_Sphere') {\n        wkt.sphere = true;\n      }\n      wkt.datumCode = 'wgs84';\n    }\n    if (wkt.datumCode === 'belge_1972') {\n      wkt.datumCode = 'rnb72';\n    }\n    if (geogcs.DATUM && geogcs.DATUM.SPHEROID) {\n      wkt.ellps = geogcs.DATUM.SPHEROID.name.replace('_19', '').replace(/[Cc]larke\\_18/, 'clrk');\n      if (wkt.ellps.toLowerCase().slice(0, 13) === 'international') {\n        wkt.ellps = 'intl';\n      }\n\n      wkt.a = geogcs.DATUM.SPHEROID.a;\n      wkt.rf = parseFloat(geogcs.DATUM.SPHEROID.rf, 10);\n    }\n\n    if (geogcs.DATUM && geogcs.DATUM.TOWGS84) {\n      wkt.datum_params = geogcs.DATUM.TOWGS84;\n    }\n    if (~wkt.datumCode.indexOf('osgb_1936')) {\n      wkt.datumCode = 'osgb36';\n    }\n    if (~wkt.datumCode.indexOf('osni_1952')) {\n      wkt.datumCode = 'osni52';\n    }\n    if (~wkt.datumCode.indexOf('tm65')\n      || ~wkt.datumCode.indexOf('geodetic_datum_of_1965')) {\n      wkt.datumCode = 'ire65';\n    }\n    if (wkt.datumCode === 'ch1903+') {\n      wkt.datumCode = 'ch1903';\n    }\n    if (~wkt.datumCode.indexOf('israel')) {\n      wkt.datumCode = 'isr93';\n    }\n  }\n  if (wkt.b && !isFinite(wkt.b)) {\n    wkt.b = wkt.a;\n  }\n  if (wkt.rectified_grid_angle) {\n    wkt.rectified_grid_angle = d2r(wkt.rectified_grid_angle);\n  }\n\n  function toMeter(input) {\n    var ratio = wkt.to_meter || 1;\n    return input * ratio;\n  }\n  var renamer = function(a) {\n    return rename(wkt, a);\n  };\n  var list = [\n    ['standard_parallel_1', 'Standard_Parallel_1'],\n    ['standard_parallel_1', 'Latitude of 1st standard parallel'],\n    ['standard_parallel_2', 'Standard_Parallel_2'],\n    ['standard_parallel_2', 'Latitude of 2nd standard parallel'],\n    ['false_easting', 'False_Easting'],\n    ['false_easting', 'False easting'],\n    ['false-easting', 'Easting at false origin'],\n    ['false_northing', 'False_Northing'],\n    ['false_northing', 'False northing'],\n    ['false_northing', 'Northing at false origin'],\n    ['central_meridian', 'Central_Meridian'],\n    ['central_meridian', 'Longitude of natural origin'],\n    ['central_meridian', 'Longitude of false origin'],\n    ['latitude_of_origin', 'Latitude_Of_Origin'],\n    ['latitude_of_origin', 'Central_Parallel'],\n    ['latitude_of_origin', 'Latitude of natural origin'],\n    ['latitude_of_origin', 'Latitude of false origin'],\n    ['scale_factor', 'Scale_Factor'],\n    ['k0', 'scale_factor'],\n    ['latitude_of_center', 'Latitude_Of_Center'],\n    ['latitude_of_center', 'Latitude_of_center'],\n    ['lat0', 'latitude_of_center', d2r],\n    ['longitude_of_center', 'Longitude_Of_Center'],\n    ['longitude_of_center', 'Longitude_of_center'],\n    ['longc', 'longitude_of_center', d2r],\n    ['x0', 'false_easting', toMeter],\n    ['y0', 'false_northing', toMeter],\n    ['long0', 'central_meridian', d2r],\n    ['lat0', 'latitude_of_origin', d2r],\n    ['lat0', 'standard_parallel_1', d2r],\n    ['lat1', 'standard_parallel_1', d2r],\n    ['lat2', 'standard_parallel_2', d2r],\n    ['azimuth', 'Azimuth'],\n    ['alpha', 'azimuth', d2r],\n    ['srsCode', 'name']\n  ];\n  list.forEach(renamer);\n  applyProjectionDefaults(wkt);\n}\nexport default function(wkt) {\n  if (typeof wkt === 'object') {\n    return transformPROJJSON(wkt);\n  }\n  const version = detectWKTVersion(wkt);\n  var lisp = parser(wkt);\n  if (version === 'WKT2') {\n    const projjson = buildPROJJSON(lisp);\n    return transformPROJJSON(projjson);\n  }\n  var type = lisp[0];\n  var obj = {};\n  sExpr(lisp, obj);\n  cleanWKT(obj);\n  return obj[type];\n}\n", "import globals from './global';\nimport parseProj from './projString';\nimport wkt from 'wkt-parser';\n\n/**\n * @typedef {Object} ProjectionDefinition\n * @property {string} title\n * @property {string} [projName]\n * @property {string} [ellps]\n * @property {import('./Proj.js').DatumDefinition} [datum]\n * @property {string} [datumName]\n * @property {number} [rf]\n * @property {number} [lat0]\n * @property {number} [lat1]\n * @property {number} [lat2]\n * @property {number} [lat_ts]\n * @property {number} [long0]\n * @property {number} [long1]\n * @property {number} [long2]\n * @property {number} [alpha]\n * @property {number} [longc]\n * @property {number} [x0]\n * @property {number} [y0]\n * @property {number} [k0]\n * @property {number} [a]\n * @property {number} [b]\n * @property {true} [R_A]\n * @property {number} [zone]\n * @property {true} [utmSouth]\n * @property {string|Array<number>} [datum_params]\n * @property {number} [to_meter]\n * @property {string} [units]\n * @property {number} [from_greenwich]\n * @property {string} [datumCode]\n * @property {string} [nadgrids]\n * @property {string} [axis]\n * @property {boolean} [sphere]\n * @property {number} [rectified_grid_angle]\n * @property {boolean} [approx]\n * @property {<T extends import('./core').TemplateCoordinates>(coordinates: T, enforceAxis?: boolean) => T} inverse\n * @property {<T extends import('./core').TemplateCoordinates>(coordinates: T, enforceAxis?: boolean) => T} forward\n */\n\n/**\n * @overload\n * @param {string} name\n * @param {string|ProjectionDefinition|import('./core.js').PROJJSONDefinition} projection\n * @returns {void}\n */\n/**\n * @overload\n * @param {Array<[string, string]>} name\n * @returns {Array<ProjectionDefinition|undefined>}\n */\n/**\n * @overload\n * @param {string} name\n * @returns {ProjectionDefinition}\n */\n\n/**\n * @param {string | Array<Array<string>> | Partial<Record<'EPSG'|'ESRI'|'IAU2000', ProjectionDefinition>>} name\n * @returns {ProjectionDefinition | Array<ProjectionDefinition|undefined> | void}\n */\nfunction defs(name) {\n  /* global console */\n  var that = this;\n  if (arguments.length === 2) {\n    var def = arguments[1];\n    if (typeof def === 'string') {\n      if (def.charAt(0) === '+') {\n        defs[/** @type {string} */ (name)] = parseProj(arguments[1]);\n      } else {\n        defs[/** @type {string} */ (name)] = wkt(arguments[1]);\n      }\n    } else {\n      defs[/** @type {string} */ (name)] = def;\n    }\n  } else if (arguments.length === 1) {\n    if (Array.isArray(name)) {\n      return name.map(function (v) {\n        if (Array.isArray(v)) {\n          return defs.apply(that, v);\n        } else {\n          return defs(v);\n        }\n      });\n    } else if (typeof name === 'string') {\n      if (name in defs) {\n        return defs[name];\n      }\n    } else if ('EPSG' in name) {\n      defs['EPSG:' + name.EPSG] = name;\n    } else if ('ESRI' in name) {\n      defs['ESRI:' + name.ESRI] = name;\n    } else if ('IAU2000' in name) {\n      defs['IAU2000:' + name.IAU2000] = name;\n    } else {\n      console.log(name);\n    }\n    return;\n  }\n}\nglobals(defs);\nexport default defs;\n", "import defs from './defs';\nimport wkt from 'wkt-parser';\nimport projStr from './projString';\nimport match from './match';\nfunction testObj(code) {\n  return typeof code === 'string';\n}\nfunction testDef(code) {\n  return code in defs;\n}\nfunction testWKT(code) {\n  return (code.indexOf('+') !== 0 && code.indexOf('[') !== -1) || (typeof code === 'object' && !('srsCode' in code));\n}\nvar codes = ['3857', '900913', '3785', '102113'];\nfunction checkMercator(item) {\n  var auth = match(item, 'authority');\n  if (!auth) {\n    return;\n  }\n  var code = match(auth, 'epsg');\n  return code && codes.indexOf(code) > -1;\n}\nfunction checkProjStr(item) {\n  var ext = match(item, 'extension');\n  if (!ext) {\n    return;\n  }\n  return match(ext, 'proj4');\n}\nfunction testProj(code) {\n  return code[0] === '+';\n}\n/**\n * @param {string | import('./core').PROJJSONDefinition | import('./defs').ProjectionDefinition} code\n * @returns {import('./defs').ProjectionDefinition}\n */\nfunction parse(code) {\n  if (testObj(code)) {\n    // check to see if this is a WKT string\n    if (testDef(code)) {\n      return defs[code];\n    }\n    if (testWKT(code)) {\n      var out = wkt(code);\n      // test of spetial case, due to this being a very common and often malformed\n      if (checkMercator(out)) {\n        return defs['EPSG:3857'];\n      }\n      var maybeProjStr = checkProjStr(out);\n      if (maybeProjStr) {\n        return projStr(maybeProjStr);\n      }\n      return out;\n    }\n    if (testProj(code)) {\n      return projStr(code);\n    }\n  } else if (!('projName' in code)) {\n    return wkt(code);\n  } else {\n    return code;\n  }\n}\n\nexport default parse;\n", "export default function (destination, source) {\n  destination = destination || {};\n  var value, property;\n  if (!source) {\n    return destination;\n  }\n  for (property in source) {\n    value = source[property];\n    if (value !== undefined) {\n      destination[property] = value;\n    }\n  }\n  return destination;\n}\n", "export default function (eccent, sinphi, cosphi) {\n  var con = eccent * sinphi;\n  return cosphi / (Math.sqrt(1 - con * con));\n}\n", "export default function (x) {\n  return x < 0 ? -1 : 1;\n}\n", "import { TWO_PI, SPI } from '../constants/values';\nimport sign from './sign';\n\nexport default function (x) {\n  return (Math.abs(x) <= SPI) ? x : (x - (sign(x) * TWO_PI));\n}\n", "import { HALF_PI } from '../constants/values';\n\nexport default function (eccent, phi, sinphi) {\n  var con = eccent * sinphi;\n  var com = 0.5 * eccent;\n  con = Math.pow(((1 - con) / (1 + con)), com);\n  return (Math.tan(0.5 * (HALF_PI - phi)) / con);\n}\n", "import { HALF_PI } from '../constants/values';\n\nexport default function (eccent, ts) {\n  var eccnth = 0.5 * eccent;\n  var con, dphi;\n  var phi = HALF_PI - 2 * Math.atan(ts);\n  for (var i = 0; i <= 15; i++) {\n    con = eccent * Math.sin(phi);\n    dphi = HALF_PI - 2 * Math.atan(ts * (Math.pow(((1 - con) / (1 + con)), eccnth))) - phi;\n    phi += dphi;\n    if (Math.abs(dphi) <= 0.0000000001) {\n      return phi;\n    }\n  }\n  // console.log(\"phi2z has NoConvergence\");\n  return -9999;\n}\n", "import msfnz from '../common/msfnz';\n\nimport adjust_lon from '../common/adjust_lon';\nimport tsfnz from '../common/tsfnz';\nimport phi2z from '../common/phi2z';\nimport { FORTPI, R2D, EPSLN, HALF_PI } from '../constants/values';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} es\n * @property {number} e\n * @property {number} k\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  var con = this.b / this.a;\n  this.es = 1 - con * con;\n  if (!('x0' in this)) {\n    this.x0 = 0;\n  }\n  if (!('y0' in this)) {\n    this.y0 = 0;\n  }\n  this.e = Math.sqrt(this.es);\n  if (this.lat_ts) {\n    if (this.sphere) {\n      this.k0 = Math.cos(this.lat_ts);\n    } else {\n      this.k0 = msfnz(this.e, Math.sin(this.lat_ts), Math.cos(this.lat_ts));\n    }\n  } else {\n    if (!this.k0) {\n      if (this.k) {\n        this.k0 = this.k;\n      } else {\n        this.k0 = 1;\n      }\n    }\n  }\n}\n\n/* Mercator forward equations--mapping lat,long to x,y\n  -------------------------------------------------- */\n\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n  // convert to radians\n  if (lat * R2D > 90 && lat * R2D < -90 && lon * R2D > 180 && lon * R2D < -180) {\n    return null;\n  }\n\n  var x, y;\n  if (Math.abs(Math.abs(lat) - HALF_PI) <= EPSLN) {\n    return null;\n  } else {\n    if (this.sphere) {\n      x = this.x0 + this.a * this.k0 * adjust_lon(lon - this.long0);\n      y = this.y0 + this.a * this.k0 * Math.log(Math.tan(FORTPI + 0.5 * lat));\n    } else {\n      var sinphi = Math.sin(lat);\n      var ts = tsfnz(this.e, lat, sinphi);\n      x = this.x0 + this.a * this.k0 * adjust_lon(lon - this.long0);\n      y = this.y0 - this.a * this.k0 * Math.log(ts);\n    }\n    p.x = x;\n    p.y = y;\n    return p;\n  }\n}\n\n/* Mercator inverse equations--mapping x,y to lat/long\n  -------------------------------------------------- */\nexport function inverse(p) {\n  var x = p.x - this.x0;\n  var y = p.y - this.y0;\n  var lon, lat;\n\n  if (this.sphere) {\n    lat = HALF_PI - 2 * Math.atan(Math.exp(-y / (this.a * this.k0)));\n  } else {\n    var ts = Math.exp(-y / (this.a * this.k0));\n    lat = phi2z(this.e, ts);\n    if (lat === -9999) {\n      return null;\n    }\n  }\n  lon = adjust_lon(this.long0 + x / (this.a * this.k0));\n\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = ['Mercator', 'Popular Visualisation Pseudo Mercator', 'Mercator_1SP', 'Mercator_Auxiliary_Sphere', 'Mercator_Variant_A', 'merc'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "export function init() {\n  // no-op for longlat\n}\n\nfunction identity(pt) {\n  return pt;\n}\nexport { identity as forward };\nexport { identity as inverse };\nexport var names = ['longlat', 'identity'];\nexport default {\n  init: init,\n  forward: identity,\n  inverse: identity,\n  names: names\n};\n", "import merc from './projections/merc';\nimport longlat from './projections/longlat';\nvar projs = [merc, longlat];\nvar names = {};\nvar projStore = [];\n\nfunction add(proj, i) {\n  var len = projStore.length;\n  if (!proj.names) {\n    console.log(i);\n    return true;\n  }\n  projStore[len] = proj;\n  proj.names.forEach(function (n) {\n    names[n.toLowerCase()] = len;\n  });\n  return this;\n}\n\nexport { add };\n\nexport function getNormalizedProjName(n) {\n  return n.replace(/[-\\(\\)\\s]+/g, ' ').trim().replace(/ /g, '_');\n}\n\nexport function get(name) {\n  if (!name) {\n    return false;\n  }\n  var n = name.toLowerCase();\n  if (typeof names[n] !== 'undefined' && projStore[names[n]]) {\n    return projStore[names[n]];\n  }\n  n = getNormalizedProjName(n);\n  if (n in names && projStore[names[n]]) {\n    return projStore[names[n]];\n  }\n}\n\nexport function start() {\n  projs.forEach(add);\n}\nexport default {\n  start: start,\n  add: add,\n  get: get\n};\n", "var ellipsoids = {\n  MERIT: {\n    a: 6378137,\n    rf: 298.257,\n    ellipseName: 'MERIT 1983'\n  },\n  SGS85: {\n    a: 6378136,\n    rf: 298.257,\n    ellipseName: 'Soviet Geodetic System 85'\n  },\n  GRS80: {\n    a: 6378137,\n    rf: 298.257222101,\n    ellipseName: 'GRS 1980(IUGG, 1980)'\n  },\n  IAU76: {\n    a: 6378140,\n    rf: 298.257,\n    ellipseName: 'IAU 1976'\n  },\n  airy: {\n    a: 6377563.396,\n    b: 6356256.91,\n    ellipseName: 'Airy 1830'\n  },\n  APL4: {\n    a: 6378137,\n    rf: 298.25,\n    ellipseName: 'Appl. Physics. 1965'\n  },\n  NWL9D: {\n    a: 6378145,\n    rf: 298.25,\n    ellipseName: 'Naval Weapons Lab., 1965'\n  },\n  mod_airy: {\n    a: 6377340.189,\n    b: 6356034.446,\n    ellipseName: 'Modified Airy'\n  },\n  andrae: {\n    a: 6377104.43,\n    rf: 300,\n    ellipseName: '<PERSON><PERSON> 1876 (Den., Iclnd.)'\n  },\n  aust_SA: {\n    a: 6378160,\n    rf: 298.25,\n    ellipseName: 'Australian Natl & S. Amer. 1969'\n  },\n  GRS67: {\n    a: 6378160,\n    rf: 298.*********,\n    ellipseName: 'GRS 67(IUGG 1967)'\n  },\n  bessel: {\n    a: 6377397.155,\n    rf: 299.1528128,\n    ellipseName: 'Bessel 1841'\n  },\n  bess_nam: {\n    a: 6377483.865,\n    rf: 299.1528128,\n    ellipseName: 'Bessel 1841 (Namibia)'\n  },\n  clrk66: {\n    a: 6378206.4,\n    b: 6356583.8,\n    ellipseName: 'Clarke 1866'\n  },\n  clrk80: {\n    a: 6378249.145,\n    rf: 293.4663,\n    ellipseName: 'Clarke 1880 mod.'\n  },\n  clrk80ign: {\n    a: 6378249.2,\n    b: 6356515,\n    rf: 293.4660213,\n    ellipseName: 'Clarke 1880 (IGN)'\n  },\n  clrk58: {\n    a: 6378293.*********,\n    rf: 294.2606763692654,\n    ellipseName: 'Clarke 1858'\n  },\n  CPM: {\n    a: 6375738.7,\n    rf: 334.29,\n    ellipseName: 'Comm. des Poids et Mesures 1799'\n  },\n  delmbr: {\n    a: 6376428,\n    rf: 311.5,\n    ellipseName: 'Delambre 1810 (Belgium)'\n  },\n  engelis: {\n    a: 6378136.05,\n    rf: 298.2566,\n    ellipseName: 'Engelis 1985'\n  },\n  evrst30: {\n    a: 6377276.345,\n    rf: 300.8017,\n    ellipseName: 'Everest 1830'\n  },\n  evrst48: {\n    a: 6377304.063,\n    rf: 300.8017,\n    ellipseName: 'Everest 1948'\n  },\n  evrst56: {\n    a: 6377301.243,\n    rf: 300.8017,\n    ellipseName: 'Everest 1956'\n  },\n  evrst69: {\n    a: 6377295.664,\n    rf: 300.8017,\n    ellipseName: 'Everest 1969'\n  },\n  evrstSS: {\n    a: 6377298.556,\n    rf: 300.8017,\n    ellipseName: 'Everest (Sabah & Sarawak)'\n  },\n  fschr60: {\n    a: 6378166,\n    rf: 298.3,\n    ellipseName: 'Fischer (Mercury Datum) 1960'\n  },\n  fschr60m: {\n    a: 6378155,\n    rf: 298.3,\n    ellipseName: 'Fischer 1960'\n  },\n  fschr68: {\n    a: 6378150,\n    rf: 298.3,\n    ellipseName: 'Fischer 1968'\n  },\n  helmert: {\n    a: 6378200,\n    rf: 298.3,\n    ellipseName: 'Helmert 1906'\n  },\n  hough: {\n    a: 6378270,\n    rf: 297,\n    ellipseName: 'Hough'\n  },\n  intl: {\n    a: 6378388,\n    rf: 297,\n    ellipseName: 'International 1909 (Hayford)'\n  },\n  kaula: {\n    a: 6378163,\n    rf: 298.24,\n    ellipseName: 'Kaula 1961'\n  },\n  lerch: {\n    a: 6378139,\n    rf: 298.257,\n    ellipseName: 'Lerch 1979'\n  },\n  mprts: {\n    a: 6397300,\n    rf: 191,\n    ellipseName: 'Maupertius 1738'\n  },\n  new_intl: {\n    a: 6378157.5,\n    b: 6356772.2,\n    ellipseName: 'New International 1967'\n  },\n  plessis: {\n    a: 6376523,\n    rf: 6355863,\n    ellipseName: 'Plessis 1817 (France)'\n  },\n  krass: {\n    a: 6378245,\n    rf: 298.3,\n    ellipseName: 'Krassovsky, 1942'\n  },\n  SEasia: {\n    a: 6378155,\n    b: 6356773.3205,\n    ellipseName: 'Southeast Asia'\n  },\n  walbeck: {\n    a: 6376896,\n    b: 6355834.8467,\n    ellipseName: 'Walbeck'\n  },\n  WGS60: {\n    a: 6378165,\n    rf: 298.3,\n    ellipseName: 'WGS 60'\n  },\n  WGS66: {\n    a: 6378145,\n    rf: 298.25,\n    ellipseName: 'WGS 66'\n  },\n  WGS7: {\n    a: 6378135,\n    rf: 298.26,\n    ellipseName: 'WGS 72'\n  },\n  WGS84: {\n    a: 6378137,\n    rf: 298.257223563,\n    ellipseName: 'WGS 84'\n  },\n  sphere: {\n    a: 6370997,\n    b: 6370997,\n    ellipseName: 'Normal Sphere (r=6370997)'\n  }\n};\n\nexport default ellipsoids;\n", "import { SIXTH, RA4, RA6, EPSLN } from './constants/values';\nimport { default as Ellipsoid } from './constants/Ellipsoid';\nimport match from './match';\n\nconst WGS84 = Ellipsoid.WGS84; // default ellipsoid\n\nexport function eccentricity(a, b, rf, R_A) {\n  var a2 = a * a; // used in geocentric\n  var b2 = b * b; // used in geocentric\n  var es = (a2 - b2) / a2; // e ^ 2\n  var e = 0;\n  if (R_A) {\n    a *= 1 - es * (SIXTH + es * (RA4 + es * RA6));\n    a2 = a * a;\n    es = 0;\n  } else {\n    e = Math.sqrt(es); // eccentricity\n  }\n  var ep2 = (a2 - b2) / b2; // used in geocentric\n  return {\n    es: es,\n    e: e,\n    ep2: ep2\n  };\n}\nexport function sphere(a, b, rf, ellps, sphere) {\n  if (!a) { // do we have an ellipsoid?\n    var ellipse = match(Ellipsoid, ellps);\n    if (!ellipse) {\n      ellipse = WGS84;\n    }\n    a = ellipse.a;\n    b = ellipse.b;\n    rf = ellipse.rf;\n  }\n\n  if (rf && !b) {\n    b = (1.0 - 1.0 / rf) * a;\n  }\n  if (rf === 0 || Math.abs(a - b) < EPSLN) {\n    sphere = true;\n    b = a;\n  }\n  return {\n    a: a,\n    b: b,\n    rf: rf,\n    sphere: sphere\n  };\n}\n", "var datums = {\n  wgs84: {\n    towgs84: '0,0,0',\n    ellipse: 'WGS84',\n    datum<PERSON>ame: 'WGS84'\n  },\n  ch1903: {\n    towgs84: '674.374,15.056,405.346',\n    ellipse: 'bessel',\n    datumName: 'swiss'\n  },\n  ggrs87: {\n    towgs84: '-199.87,74.79,246.62',\n    ellipse: 'GRS80',\n    datumName: 'Greek_Geodetic_Reference_System_1987'\n  },\n  nad83: {\n    towgs84: '0,0,0',\n    ellipse: 'GRS80',\n    datumName: 'North_American_Datum_1983'\n  },\n  nad27: {\n    nadgrids: '@conus,@alaska,@ntv2_0.gsb,@ntv1_can.dat',\n    ellipse: 'clrk66',\n    datumName: 'North_American_Datum_1927'\n  },\n  potsdam: {\n    towgs84: '598.1,73.7,418.2,0.202,0.045,-2.455,6.7',\n    ellipse: 'bessel',\n    datumName: 'Potsdam Rauenberg 1950 DHDN'\n  },\n  carthage: {\n    towgs84: '-263.0,6.0,431.0',\n    ellipse: 'clark80',\n    datum<PERSON>ame: 'Carthage 1934 Tunisia'\n  },\n  her<PERSON><PERSON><PERSON>: {\n    towgs84: '577.326,90.129,463.919,5.137,1.474,5.297,2.4232',\n    ellipse: 'bessel',\n    datum<PERSON>ame: '<PERSON>skogel'\n  },\n  mgi: {\n    towgs84: '577.326,90.129,463.919,5.137,1.474,5.297,2.4232',\n    ellipse: 'bessel',\n    datumName: 'Militar-Geographische Institut'\n  },\n  osni52: {\n    towgs84: '482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15',\n    ellipse: 'airy',\n    datumName: 'Irish National'\n  },\n  ire65: {\n    towgs84: '482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15',\n    ellipse: 'mod_airy',\n    datumName: 'Ireland 1965'\n  },\n  rassadiran: {\n    towgs84: '-133.63,-157.5,-158.62',\n    ellipse: 'intl',\n    datumName: 'Rassadiran'\n  },\n  nzgd49: {\n    towgs84: '59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993',\n    ellipse: 'intl',\n    datumName: 'New Zealand Geodetic Datum 1949'\n  },\n  osgb36: {\n    towgs84: '446.448,-125.157,542.060,0.1502,0.2470,0.8421,-20.4894',\n    ellipse: 'airy',\n    datumName: 'Ordnance Survey of Great Britain 1936'\n  },\n  s_jtsk: {\n    towgs84: '589,76,480',\n    ellipse: 'bessel',\n    datumName: 'S-JTSK (Ferro)'\n  },\n  beduaram: {\n    towgs84: '-106,-87,188',\n    ellipse: 'clrk80',\n    datumName: 'Beduaram'\n  },\n  gunung_segara: {\n    towgs84: '-403,684,41',\n    ellipse: 'bessel',\n    datumName: 'Gunung Segara Jakarta'\n  },\n  rnb72: {\n    towgs84: '106.869,-52.2978,103.724,-0.33657,0.456955,-1.84218,1',\n    ellipse: 'intl',\n    datumName: 'Reseau National Belge 1972'\n  },\n  EPSG_5451: {\n    towgs84: '6.41,-49.05,-11.28,1.5657,0.5242,6.9718,-5.7649'\n  },\n  IGNF_LURESG: {\n    towgs84: '-192.986,13.673,-39.309,-0.4099,-2.9332,2.6881,0.43'\n  },\n  EPSG_4614: {\n    towgs84: '-119.4248,-303.65872,-11.00061,1.164298,0.174458,1.096259,3.657065'\n  },\n  EPSG_4615: {\n    towgs84: '-494.088,-312.129,279.877,-1.423,-1.013,1.59,-0.748'\n  },\n  ESRI_37241: {\n    towgs84: '-76.822,257.457,-12.817,2.136,-0.033,-2.392,-0.031'\n  },\n  ESRI_37249: {\n    towgs84: '-440.296,58.548,296.265,1.128,10.202,4.559,-0.438'\n  },\n  ESRI_37245: {\n    towgs84: '-511.151,-181.269,139.609,1.05,2.703,1.798,3.071'\n  },\n  EPSG_4178: {\n    towgs84: '24.9,-126.4,-93.2,-0.063,-0.247,-0.041,1.01'\n  },\n  EPSG_4622: {\n    towgs84: '-472.29,-5.63,-304.12,0.4362,-0.8374,0.2563,1.8984'\n  },\n  EPSG_4625: {\n    towgs84: '126.93,547.94,130.41,-2.7867,5.1612,-0.8584,13.8227'\n  },\n  EPSG_5252: {\n    towgs84: '0.023,0.036,-0.068,0.00176,0.00912,-0.01136,0.00439'\n  },\n  EPSG_4314: {\n    towgs84: '597.1,71.4,412.1,0.894,0.068,-1.563,7.58'\n  },\n  EPSG_4282: {\n    towgs84: '-178.3,-316.7,-131.5,5.278,6.077,10.979,19.166'\n  },\n  EPSG_4231: {\n    towgs84: '-83.11,-97.38,-117.22,0.0276,-0.2167,0.2147,0.1218'\n  },\n  EPSG_4274: {\n    towgs84: '-230.994,102.591,25.199,0.633,-0.239,0.9,1.95'\n  },\n  EPSG_4134: {\n    towgs84: '-180.624,-225.516,173.919,-0.81,-1.898,8.336,16.71006'\n  },\n  EPSG_4254: {\n    towgs84: '18.38,192.45,96.82,0.056,-0.142,-0.2,-0.0013'\n  },\n  EPSG_4159: {\n    towgs84: '-194.513,-63.978,-25.759,-3.4027,3.756,-3.352,-0.9175'\n  },\n  EPSG_4687: {\n    towgs84: '0.072,-0.507,-0.245,0.0183,-0.0003,0.007,-0.0093'\n  },\n  EPSG_4227: {\n    towgs84: '-83.58,-397.54,458.78,-17.595,-2.847,4.256,3.225'\n  },\n  EPSG_4746: {\n    towgs84: '599.4,72.4,419.2,-0.062,-0.022,-2.723,6.46'\n  },\n  EPSG_4745: {\n    towgs84: '612.4,77,440.2,-0.054,0.057,-2.797,2.55'\n  },\n  EPSG_6311: {\n    towgs84: '8.846,-4.394,-1.122,-0.00237,-0.146528,0.130428,0.783926'\n  },\n  EPSG_4289: {\n    towgs84: '565.7381,50.4018,465.2904,-1.91514,1.60363,-9.09546,4.07244'\n  },\n  EPSG_4230: {\n    towgs84: '-68.863,-134.888,-111.49,-0.53,-0.14,0.57,-3.4'\n  },\n  EPSG_4154: {\n    towgs84: '-123.02,-158.95,-168.47'\n  },\n  EPSG_4156: {\n    towgs84: '570.8,85.7,462.8,4.998,1.587,5.261,3.56'\n  },\n  EPSG_4299: {\n    towgs84: '482.5,-130.6,564.6,-1.042,-0.214,-0.631,8.15'\n  },\n  EPSG_4179: {\n    towgs84: '33.4,-146.6,-76.3,-0.359,-0.053,0.844,-0.84'\n  },\n  EPSG_4313: {\n    towgs84: '-106.8686,52.2978,-103.7239,0.3366,-0.457,1.8422,-1.2747'\n  },\n  EPSG_4194: {\n    towgs84: '163.511,127.533,-159.789'\n  },\n  EPSG_4195: {\n    towgs84: '105,326,-102.5'\n  },\n  EPSG_4196: {\n    towgs84: '-45,417,-3.5'\n  },\n  EPSG_4611: {\n    towgs84: '-162.619,-276.959,-161.764,0.067753,-2.243649,-1.158827,-1.094246'\n  },\n  EPSG_4633: {\n    towgs84: '137.092,131.66,91.475,-1.9436,-11.5993,-4.3321,-7.4824'\n  },\n  EPSG_4641: {\n    towgs84: '-408.809,366.856,-412.987,1.8842,-0.5308,2.1655,-121.0993'\n  },\n  EPSG_4643: {\n    towgs84: '-480.26,-438.32,-643.429,16.3119,20.1721,-4.0349,-111.7002'\n  },\n  EPSG_4300: {\n    towgs84: '482.5,-130.6,564.6,-1.042,-0.214,-0.631,8.15'\n  },\n  EPSG_4188: {\n    towgs84: '482.5,-130.6,564.6,-1.042,-0.214,-0.631,8.15'\n  },\n  EPSG_4660: {\n    towgs84: '982.6087,552.753,-540.873,32.39344,-153.25684,-96.2266,16.805'\n  },\n  EPSG_4662: {\n    towgs84: '97.295,-263.247,310.882,-1.5999,0.8386,3.1409,13.3259'\n  },\n  EPSG_3906: {\n    towgs84: '577.88891,165.22205,391.18289,4.9145,-0.94729,-13.05098,7.78664'\n  },\n  EPSG_4307: {\n    towgs84: '-209.3622,-87.8162,404.6198,0.0046,3.4784,0.5805,-1.4547'\n  },\n  EPSG_6892: {\n    towgs84: '-76.269,-16.683,68.562,-6.275,10.536,-4.286,-13.686'\n  },\n  EPSG_4690: {\n    towgs84: '221.597,152.441,176.523,2.403,1.3893,0.884,11.4648'\n  },\n  EPSG_4691: {\n    towgs84: '218.769,150.75,176.75,3.5231,2.0037,1.288,10.9817'\n  },\n  EPSG_4629: {\n    towgs84: '72.51,345.411,79.241,-1.5862,-0.8826,-0.5495,1.3653'\n  },\n  EPSG_4630: {\n    towgs84: '165.804,216.213,180.26,-0.6251,-0.4515,-0.0721,7.4111'\n  },\n  EPSG_4692: {\n    towgs84: '217.109,86.452,23.711,0.0183,-0.0003,0.007,-0.0093'\n  },\n  EPSG_9333: {\n    towgs84: '0,0,0,-8.393,0.749,-10.276,0'\n  },\n  EPSG_9059: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4312: {\n    towgs84: '601.705,84.263,485.227,4.7354,1.3145,5.393,-2.3887'\n  },\n  EPSG_4123: {\n    towgs84: '-96.062,-82.428,-121.753,4.801,0.345,-1.376,1.496'\n  },\n  EPSG_4309: {\n    towgs84: '-124.45,183.74,44.64,-0.4384,0.5446,-0.9706,-2.1365'\n  },\n  ESRI_104106: {\n    towgs84: '-283.088,-70.693,117.445,-1.157,0.059,-0.652,-4.058'\n  },\n  EPSG_4281: {\n    towgs84: '-219.247,-73.802,269.529'\n  },\n  EPSG_4322: {\n    towgs84: '0,0,4.5'\n  },\n  EPSG_4324: {\n    towgs84: '0,0,1.9'\n  },\n  EPSG_4284: {\n    towgs84: '43.822,-108.842,-119.585,1.455,-0.761,0.737,0.549'\n  },\n  EPSG_4277: {\n    towgs84: '446.448,-125.157,542.06,0.15,0.247,0.842,-20.489'\n  },\n  EPSG_4207: {\n    towgs84: '-282.1,-72.2,120,-1.529,0.145,-0.89,-4.46'\n  },\n  EPSG_4688: {\n    towgs84: '347.175,1077.618,2623.677,33.9058,-70.6776,9.4013,186.0647'\n  },\n  EPSG_4689: {\n    towgs84: '410.793,54.542,80.501,-2.5596,-2.3517,-0.6594,17.3218'\n  },\n  EPSG_4720: {\n    towgs84: '0,0,4.5'\n  },\n  EPSG_4273: {\n    towgs84: '278.3,93,474.5,7.889,0.05,-6.61,6.21'\n  },\n  EPSG_4240: {\n    towgs84: '204.64,834.74,293.8'\n  },\n  EPSG_4817: {\n    towgs84: '278.3,93,474.5,7.889,0.05,-6.61,6.21'\n  },\n  ESRI_104131: {\n    towgs84: '426.62,142.62,460.09,4.98,4.49,-12.42,-17.1'\n  },\n  EPSG_4265: {\n    towgs84: '-104.1,-49.1,-9.9,0.971,-2.917,0.714,-11.68'\n  },\n  EPSG_4263: {\n    towgs84: '-111.92,-87.85,114.5,1.875,0.202,0.219,0.032'\n  },\n  EPSG_4298: {\n    towgs84: '-689.5937,623.84046,-65.93566,-0.02331,1.17094,-0.80054,5.88536'\n  },\n  EPSG_4270: {\n    towgs84: '-253.4392,-148.452,386.5267,0.15605,0.43,-0.1013,-0.0424'\n  },\n  EPSG_4229: {\n    towgs84: '-121.8,98.1,-10.7'\n  },\n  EPSG_4220: {\n    towgs84: '-55.5,-348,-229.2'\n  },\n  EPSG_4214: {\n    towgs84: '12.646,-155.176,-80.863'\n  },\n  EPSG_4232: {\n    towgs84: '-345,3,223'\n  },\n  EPSG_4238: {\n    towgs84: '-1.977,-13.06,-9.993,0.364,0.254,0.689,-1.037'\n  },\n  EPSG_4168: {\n    towgs84: '-170,33,326'\n  },\n  EPSG_4131: {\n    towgs84: '199,931,318.9'\n  },\n  EPSG_4152: {\n    towgs84: '-0.9102,2.0141,0.5602,0.029039,0.010065,0.010101,0'\n  },\n  EPSG_5228: {\n    towgs84: '572.213,85.334,461.94,4.9732,1.529,5.2484,3.5378'\n  },\n  EPSG_8351: {\n    towgs84: '485.021,169.465,483.839,7.786342,4.397554,4.102655,0'\n  },\n  EPSG_4683: {\n    towgs84: '-127.62,-67.24,-47.04,-3.068,4.903,1.578,-1.06'\n  },\n  EPSG_4133: {\n    towgs84: '0,0,0'\n  },\n  EPSG_7373: {\n    towgs84: '0.819,-0.5762,-1.6446,-0.00378,-0.03317,0.00318,0.0693'\n  },\n  EPSG_9075: {\n    towgs84: '-0.9102,2.0141,0.5602,0.029039,0.010065,0.010101,0'\n  },\n  EPSG_9072: {\n    towgs84: '-0.9102,2.0141,0.5602,0.029039,0.010065,0.010101,0'\n  },\n  EPSG_9294: {\n    towgs84: '1.16835,-1.42001,-2.24431,-0.00822,-0.05508,0.01818,0.23388'\n  },\n  EPSG_4212: {\n    towgs84: '-267.434,173.496,181.814,-13.4704,8.7154,7.3926,14.7492'\n  },\n  EPSG_4191: {\n    towgs84: '-44.183,-0.58,-38.489,2.3867,2.7072,-3.5196,-8.2703'\n  },\n  EPSG_4237: {\n    towgs84: '52.684,-71.194,-13.975,-0.312,-0.1063,-0.3729,1.0191'\n  },\n  EPSG_4740: {\n    towgs84: '-1.08,-0.27,-0.9'\n  },\n  EPSG_4124: {\n    towgs84: '419.3836,99.3335,591.3451,0.850389,1.817277,-7.862238,-0.99496'\n  },\n  EPSG_5681: {\n    towgs84: '584.9636,107.7175,413.8067,1.1155,0.2824,-3.1384,7.9922'\n  },\n  EPSG_4141: {\n    towgs84: '23.772,17.49,17.859,-0.3132,-1.85274,1.67299,-5.4262'\n  },\n  EPSG_4204: {\n    towgs84: '-85.645,-273.077,-79.708,2.289,-1.421,2.532,3.194'\n  },\n  EPSG_4319: {\n    towgs84: '226.702,-193.337,-35.371,-2.229,-4.391,9.238,0.9798'\n  },\n  EPSG_4200: {\n    towgs84: '24.82,-131.21,-82.66'\n  },\n  EPSG_4130: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4127: {\n    towgs84: '-82.875,-57.097,-156.768,-2.158,1.524,-0.982,-0.359'\n  },\n  EPSG_4149: {\n    towgs84: '674.374,15.056,405.346'\n  },\n  EPSG_4617: {\n    towgs84: '-0.991,1.9072,0.5129,1.25033e-7,4.6785e-8,5.6529e-8,0'\n  },\n  EPSG_4663: {\n    towgs84: '-210.502,-66.902,-48.476,2.094,-15.067,-5.817,0.485'\n  },\n  EPSG_4664: {\n    towgs84: '-211.939,137.626,58.3,-0.089,0.251,0.079,0.384'\n  },\n  EPSG_4665: {\n    towgs84: '-105.854,165.589,-38.312,-0.003,-0.026,0.024,-0.048'\n  },\n  EPSG_4666: {\n    towgs84: '631.392,-66.551,481.442,1.09,-4.445,-4.487,-4.43'\n  },\n  EPSG_4756: {\n    towgs84: '-192.873,-39.382,-111.202,-0.00205,-0.0005,0.00335,0.0188'\n  },\n  EPSG_4723: {\n    towgs84: '-179.483,-69.379,-27.584,-7.862,8.163,6.042,-13.925'\n  },\n  EPSG_4726: {\n    towgs84: '8.853,-52.644,180.304,-0.393,-2.323,2.96,-24.081'\n  },\n  EPSG_4267: {\n    towgs84: '-8.0,160.0,176.0'\n  },\n  EPSG_5365: {\n    towgs84: '-0.16959,0.35312,0.51846,0.03385,-0.16325,0.03446,0.03693'\n  },\n  EPSG_4218: {\n    towgs84: '304.5,306.5,-318.1'\n  },\n  EPSG_4242: {\n    towgs84: '-33.722,153.789,94.959,-8.581,-4.478,4.54,8.95'\n  },\n  EPSG_4216: {\n    towgs84: '-292.295,248.758,429.447,4.9971,2.99,6.6906,1.0289'\n  },\n  ESRI_104105: {\n    towgs84: '631.392,-66.551,481.442,1.09,-4.445,-4.487,-4.43'\n  },\n  ESRI_104129: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4673: {\n    towgs84: '174.05,-25.49,112.57'\n  },\n  EPSG_4202: {\n    towgs84: '-124,-60,154'\n  },\n  EPSG_4203: {\n    towgs84: '-117.763,-51.51,139.061,0.292,0.443,0.277,-0.191'\n  },\n  EPSG_3819: {\n    towgs84: '595.48,121.69,515.35,4.115,-2.9383,0.853,-3.408'\n  },\n  EPSG_8694: {\n    towgs84: '-93.799,-132.737,-219.073,-1.844,0.648,-6.37,-0.169'\n  },\n  EPSG_4145: {\n    towgs84: '275.57,676.78,229.6'\n  },\n  EPSG_4283: {\n    towgs84: '61.55,-10.87,-40.19,39.4924,32.7221,32.8979,-9.994'\n  },\n  EPSG_4317: {\n    towgs84: '2.3287,-147.0425,-92.0802,-0.3092483,0.32482185,0.49729934,5.68906266'\n  },\n  EPSG_4272: {\n    towgs84: '59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993'\n  },\n  EPSG_4248: {\n    towgs84: '-307.7,265.3,-363.5'\n  },\n  EPSG_5561: {\n    towgs84: '24,-121,-76'\n  },\n  EPSG_5233: {\n    towgs84: '-0.293,766.95,87.713,0.195704,1.695068,3.473016,-0.039338'\n  },\n  ESRI_104130: {\n    towgs84: '-86,-98,-119'\n  },\n  ESRI_104102: {\n    towgs84: '682,-203,480'\n  },\n  ESRI_37207: {\n    towgs84: '7,-10,-26'\n  },\n  EPSG_4675: {\n    towgs84: '59.935,118.4,-10.871'\n  },\n  ESRI_104109: {\n    towgs84: '-89.121,-348.182,260.871'\n  },\n  ESRI_104112: {\n    towgs84: '-185.583,-230.096,281.361'\n  },\n  ESRI_104113: {\n    towgs84: '25.1,-275.6,222.6'\n  },\n  IGNF_WGS72G: {\n    towgs84: '0,12,6'\n  },\n  IGNF_NTFG: {\n    towgs84: '-168,-60,320'\n  },\n  IGNF_EFATE57G: {\n    towgs84: '-127,-769,472'\n  },\n  IGNF_PGP50G: {\n    towgs84: '324.8,153.6,172.1'\n  },\n  IGNF_REUN47G: {\n    towgs84: '94,-948,-1262'\n  },\n  IGNF_CSG67G: {\n    towgs84: '-186,230,110'\n  },\n  IGNF_GUAD48G: {\n    towgs84: '-467,-16,-300'\n  },\n  IGNF_TAHI51G: {\n    towgs84: '162,117,154'\n  },\n  IGNF_TAHAAG: {\n    towgs84: '65,342,77'\n  },\n  IGNF_NUKU72G: {\n    towgs84: '84,274,65'\n  },\n  IGNF_PETRELS72G: {\n    towgs84: '365,194,166'\n  },\n  IGNF_WALL78G: {\n    towgs84: '253,-133,-127'\n  },\n  IGNF_MAYO50G: {\n    towgs84: '-382,-59,-262'\n  },\n  IGNF_TANNAG: {\n    towgs84: '-139,-967,436'\n  },\n  IGNF_IGN72G: {\n    towgs84: '-13,-348,292'\n  },\n  IGNF_ATIGG: {\n    towgs84: '1118,23,66'\n  },\n  IGNF_FANGA84G: {\n    towgs84: '150.57,158.33,118.32'\n  },\n  IGNF_RUSAT84G: {\n    towgs84: '202.13,174.6,-15.74'\n  },\n  IGNF_KAUE70G: {\n    towgs84: '126.74,300.1,-75.49'\n  },\n  IGNF_MOP90G: {\n    towgs84: '-10.8,-1.8,12.77'\n  },\n  IGNF_MHPF67G: {\n    towgs84: '338.08,212.58,-296.17'\n  },\n  IGNF_TAHI79G: {\n    towgs84: '160.61,116.05,153.69'\n  },\n  IGNF_ANAA92G: {\n    towgs84: '1.5,3.84,4.81'\n  },\n  IGNF_MARQUI72G: {\n    towgs84: '330.91,-13.92,58.56'\n  },\n  IGNF_APAT86G: {\n    towgs84: '143.6,197.82,74.05'\n  },\n  IGNF_TUBU69G: {\n    towgs84: '237.17,171.61,-77.84'\n  },\n  IGNF_STPM50G: {\n    towgs84: '11.363,424.148,373.13'\n  },\n  EPSG_4150: {\n    towgs84: '674.374,15.056,405.346'\n  },\n  EPSG_4754: {\n    towgs84: '-208.4058,-109.8777,-2.5764'\n  },\n  ESRI_104101: {\n    towgs84: '374,150,588'\n  },\n  EPSG_4693: {\n    towgs84: '0,-0.15,0.68'\n  },\n  EPSG_6207: {\n    towgs84: '293.17,726.18,245.36'\n  },\n  EPSG_4153: {\n    towgs84: '-133.63,-157.5,-158.62'\n  },\n  EPSG_4132: {\n    towgs84: '-241.54,-163.64,396.06'\n  },\n  EPSG_4221: {\n    towgs84: '-154.5,150.7,100.4'\n  },\n  EPSG_4266: {\n    towgs84: '-80.7,-132.5,41.1'\n  },\n  EPSG_4193: {\n    towgs84: '-70.9,-151.8,-41.4'\n  },\n  EPSG_5340: {\n    towgs84: '-0.41,0.46,-0.35'\n  },\n  EPSG_4246: {\n    towgs84: '-294.7,-200.1,525.5'\n  },\n  EPSG_4318: {\n    towgs84: '-3.2,-5.7,2.8'\n  },\n  EPSG_4121: {\n    towgs84: '-199.87,74.79,246.62'\n  },\n  EPSG_4223: {\n    towgs84: '-260.1,5.5,432.2'\n  },\n  EPSG_4158: {\n    towgs84: '-0.465,372.095,171.736'\n  },\n  EPSG_4285: {\n    towgs84: '-128.16,-282.42,21.93'\n  },\n  EPSG_4613: {\n    towgs84: '-404.78,685.68,45.47'\n  },\n  EPSG_4607: {\n    towgs84: '195.671,332.517,274.607'\n  },\n  EPSG_4475: {\n    towgs84: '-381.788,-57.501,-256.673'\n  },\n  EPSG_4208: {\n    towgs84: '-157.84,308.54,-146.6'\n  },\n  EPSG_4743: {\n    towgs84: '70.995,-335.916,262.898'\n  },\n  EPSG_4710: {\n    towgs84: '-323.65,551.39,-491.22'\n  },\n  EPSG_7881: {\n    towgs84: '-0.077,0.079,0.086'\n  },\n  EPSG_4682: {\n    towgs84: '283.729,735.942,261.143'\n  },\n  EPSG_4739: {\n    towgs84: '-156,-271,-189'\n  },\n  EPSG_4679: {\n    towgs84: '-80.01,253.26,291.19'\n  },\n  EPSG_4750: {\n    towgs84: '-56.263,16.136,-22.856'\n  },\n  EPSG_4644: {\n    towgs84: '-10.18,-350.43,291.37'\n  },\n  EPSG_4695: {\n    towgs84: '-103.746,-9.614,-255.95'\n  },\n  EPSG_4292: {\n    towgs84: '-355,21,72'\n  },\n  EPSG_4302: {\n    towgs84: '-61.702,284.488,472.052'\n  },\n  EPSG_4143: {\n    towgs84: '-124.76,53,466.79'\n  },\n  EPSG_4606: {\n    towgs84: '-153,153,307'\n  },\n  EPSG_4699: {\n    towgs84: '-770.1,158.4,-498.2'\n  },\n  EPSG_4247: {\n    towgs84: '-273.5,110.6,-357.9'\n  },\n  EPSG_4160: {\n    towgs84: '8.88,184.86,106.69'\n  },\n  EPSG_4161: {\n    towgs84: '-233.43,6.65,173.64'\n  },\n  EPSG_9251: {\n    towgs84: '-9.5,122.9,138.2'\n  },\n  EPSG_9253: {\n    towgs84: '-78.1,101.6,133.3'\n  },\n  EPSG_4297: {\n    towgs84: '-198.383,-240.517,-107.909'\n  },\n  EPSG_4269: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4301: {\n    towgs84: '-147,506,687'\n  },\n  EPSG_4618: {\n    towgs84: '-59,-11,-52'\n  },\n  EPSG_4612: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4678: {\n    towgs84: '44.585,-131.212,-39.544'\n  },\n  EPSG_4250: {\n    towgs84: '-130,29,364'\n  },\n  EPSG_4144: {\n    towgs84: '214,804,268'\n  },\n  EPSG_4147: {\n    towgs84: '-17.51,-108.32,-62.39'\n  },\n  EPSG_4259: {\n    towgs84: '-254.1,-5.36,-100.29'\n  },\n  EPSG_4164: {\n    towgs84: '-76,-138,67'\n  },\n  EPSG_4211: {\n    towgs84: '-378.873,676.002,-46.255'\n  },\n  EPSG_4182: {\n    towgs84: '-422.651,-172.995,84.02'\n  },\n  EPSG_4224: {\n    towgs84: '-143.87,243.37,-33.52'\n  },\n  EPSG_4225: {\n    towgs84: '-205.57,168.77,-4.12'\n  },\n  EPSG_5527: {\n    towgs84: '-67.35,3.88,-38.22'\n  },\n  EPSG_4752: {\n    towgs84: '98,390,-22'\n  },\n  EPSG_4310: {\n    towgs84: '-30,190,89'\n  },\n  EPSG_9248: {\n    towgs84: '-192.26,65.72,132.08'\n  },\n  EPSG_4680: {\n    towgs84: '124.5,-63.5,-281'\n  },\n  EPSG_4701: {\n    towgs84: '-79.9,-158,-168.9'\n  },\n  EPSG_4706: {\n    towgs84: '-146.21,112.63,4.05'\n  },\n  EPSG_4805: {\n    towgs84: '682,-203,480'\n  },\n  EPSG_4201: {\n    towgs84: '-165,-11,206'\n  },\n  EPSG_4210: {\n    towgs84: '-157,-2,-299'\n  },\n  EPSG_4183: {\n    towgs84: '-104,167,-38'\n  },\n  EPSG_4139: {\n    towgs84: '11,72,-101'\n  },\n  EPSG_4668: {\n    towgs84: '-86,-98,-119'\n  },\n  EPSG_4717: {\n    towgs84: '-2,151,181'\n  },\n  EPSG_4732: {\n    towgs84: '102,52,-38'\n  },\n  EPSG_4280: {\n    towgs84: '-377,681,-50'\n  },\n  EPSG_4209: {\n    towgs84: '-138,-105,-289'\n  },\n  EPSG_4261: {\n    towgs84: '31,146,47'\n  },\n  EPSG_4658: {\n    towgs84: '-73,46,-86'\n  },\n  EPSG_4721: {\n    towgs84: '265.025,384.929,-194.046'\n  },\n  EPSG_4222: {\n    towgs84: '-136,-108,-292'\n  },\n  EPSG_4601: {\n    towgs84: '-255,-15,71'\n  },\n  EPSG_4602: {\n    towgs84: '725,685,536'\n  },\n  EPSG_4603: {\n    towgs84: '72,213.7,93'\n  },\n  EPSG_4605: {\n    towgs84: '9,183,236'\n  },\n  EPSG_4621: {\n    towgs84: '137,248,-430'\n  },\n  EPSG_4657: {\n    towgs84: '-28,199,5'\n  },\n  EPSG_4316: {\n    towgs84: '103.25,-100.4,-307.19'\n  },\n  EPSG_4642: {\n    towgs84: '-13,-348,292'\n  },\n  EPSG_4698: {\n    towgs84: '145,-187,103'\n  },\n  EPSG_4192: {\n    towgs84: '-206.1,-174.7,-87.7'\n  },\n  EPSG_4311: {\n    towgs84: '-265,120,-358'\n  },\n  EPSG_4135: {\n    towgs84: '58,-283,-182'\n  },\n  ESRI_104138: {\n    towgs84: '198,-226,-347'\n  },\n  EPSG_4245: {\n    towgs84: '-11,851,5'\n  },\n  EPSG_4142: {\n    towgs84: '-125,53,467'\n  },\n  EPSG_4213: {\n    towgs84: '-106,-87,188'\n  },\n  EPSG_4253: {\n    towgs84: '-133,-77,-51'\n  },\n  EPSG_4129: {\n    towgs84: '-132,-110,-335'\n  },\n  EPSG_4713: {\n    towgs84: '-77,-128,142'\n  },\n  EPSG_4239: {\n    towgs84: '217,823,299'\n  },\n  EPSG_4146: {\n    towgs84: '295,736,257'\n  },\n  EPSG_4155: {\n    towgs84: '-83,37,124'\n  },\n  EPSG_4165: {\n    towgs84: '-173,253,27'\n  },\n  EPSG_4672: {\n    towgs84: '175,-38,113'\n  },\n  EPSG_4236: {\n    towgs84: '-637,-549,-203'\n  },\n  EPSG_4251: {\n    towgs84: '-90,40,88'\n  },\n  EPSG_4271: {\n    towgs84: '-2,374,172'\n  },\n  EPSG_4175: {\n    towgs84: '-88,4,101'\n  },\n  EPSG_4716: {\n    towgs84: '298,-304,-375'\n  },\n  EPSG_4315: {\n    towgs84: '-23,259,-9'\n  },\n  EPSG_4744: {\n    towgs84: '-242.2,-144.9,370.3'\n  },\n  EPSG_4244: {\n    towgs84: '-97,787,86'\n  },\n  EPSG_4293: {\n    towgs84: '616,97,-251'\n  },\n  EPSG_4714: {\n    towgs84: '-127,-769,472'\n  },\n  EPSG_4736: {\n    towgs84: '260,12,-147'\n  },\n  EPSG_6883: {\n    towgs84: '-235,-110,393'\n  },\n  EPSG_6894: {\n    towgs84: '-63,176,185'\n  },\n  EPSG_4205: {\n    towgs84: '-43,-163,45'\n  },\n  EPSG_4256: {\n    towgs84: '41,-220,-134'\n  },\n  EPSG_4262: {\n    towgs84: '639,405,60'\n  },\n  EPSG_4604: {\n    towgs84: '174,359,365'\n  },\n  EPSG_4169: {\n    towgs84: '-115,118,426'\n  },\n  EPSG_4620: {\n    towgs84: '-106,-129,165'\n  },\n  EPSG_4184: {\n    towgs84: '-203,141,53'\n  },\n  EPSG_4616: {\n    towgs84: '-289,-124,60'\n  },\n  EPSG_9403: {\n    towgs84: '-307,-92,127'\n  },\n  EPSG_4684: {\n    towgs84: '-133,-321,50'\n  },\n  EPSG_4708: {\n    towgs84: '-491,-22,435'\n  },\n  EPSG_4707: {\n    towgs84: '114,-116,-333'\n  },\n  EPSG_4709: {\n    towgs84: '145,75,-272'\n  },\n  EPSG_4712: {\n    towgs84: '-205,107,53'\n  },\n  EPSG_4711: {\n    towgs84: '124,-234,-25'\n  },\n  EPSG_4718: {\n    towgs84: '230,-199,-752'\n  },\n  EPSG_4719: {\n    towgs84: '211,147,111'\n  },\n  EPSG_4724: {\n    towgs84: '208,-435,-229'\n  },\n  EPSG_4725: {\n    towgs84: '189,-79,-202'\n  },\n  EPSG_4735: {\n    towgs84: '647,1777,-1124'\n  },\n  EPSG_4722: {\n    towgs84: '-794,119,-298'\n  },\n  EPSG_4728: {\n    towgs84: '-307,-92,127'\n  },\n  EPSG_4734: {\n    towgs84: '-632,438,-609'\n  },\n  EPSG_4727: {\n    towgs84: '912,-58,1227'\n  },\n  EPSG_4729: {\n    towgs84: '185,165,42'\n  },\n  EPSG_4730: {\n    towgs84: '170,42,84'\n  },\n  EPSG_4733: {\n    towgs84: '276,-57,149'\n  },\n  ESRI_37218: {\n    towgs84: '230,-199,-752'\n  },\n  ESRI_37240: {\n    towgs84: '-7,215,225'\n  },\n  ESRI_37221: {\n    towgs84: '252,-209,-751'\n  },\n  ESRI_4305: {\n    towgs84: '-123,-206,219'\n  },\n  ESRI_104139: {\n    towgs84: '-73,-247,227'\n  },\n  EPSG_4748: {\n    towgs84: '51,391,-36'\n  },\n  EPSG_4219: {\n    towgs84: '-384,664,-48'\n  },\n  EPSG_4255: {\n    towgs84: '-333,-222,114'\n  },\n  EPSG_4257: {\n    towgs84: '-587.8,519.75,145.76'\n  },\n  EPSG_4646: {\n    towgs84: '-963,510,-359'\n  },\n  EPSG_6881: {\n    towgs84: '-24,-203,268'\n  },\n  EPSG_6882: {\n    towgs84: '-183,-15,273'\n  },\n  EPSG_4715: {\n    towgs84: '-104,-129,239'\n  },\n  IGNF_RGF93GDD: {\n    towgs84: '0,0,0'\n  },\n  IGNF_RGM04GDD: {\n    towgs84: '0,0,0'\n  },\n  IGNF_RGSPM06GDD: {\n    towgs84: '0,0,0'\n  },\n  IGNF_RGTAAF07GDD: {\n    towgs84: '0,0,0'\n  },\n  IGNF_RGFG95GDD: {\n    towgs84: '0,0,0'\n  },\n  IGNF_RGNCG: {\n    towgs84: '0,0,0'\n  },\n  IGNF_RGPFGDD: {\n    towgs84: '0,0,0'\n  },\n  IGNF_ETRS89G: {\n    towgs84: '0,0,0'\n  },\n  IGNF_RGR92GDD: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4173: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4180: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4619: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4667: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4075: {\n    towgs84: '0,0,0'\n  },\n  EPSG_6706: {\n    towgs84: '0,0,0'\n  },\n  EPSG_7798: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4661: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4669: {\n    towgs84: '0,0,0'\n  },\n  EPSG_8685: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4151: {\n    towgs84: '0,0,0'\n  },\n  EPSG_9702: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4758: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4761: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4765: {\n    towgs84: '0,0,0'\n  },\n  EPSG_8997: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4023: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4670: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4694: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4148: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4163: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4167: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4189: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4190: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4176: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4659: {\n    towgs84: '0,0,0'\n  },\n  EPSG_3824: {\n    towgs84: '0,0,0'\n  },\n  EPSG_3889: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4046: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4081: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4558: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4483: {\n    towgs84: '0,0,0'\n  },\n  EPSG_5013: {\n    towgs84: '0,0,0'\n  },\n  EPSG_5264: {\n    towgs84: '0,0,0'\n  },\n  EPSG_5324: {\n    towgs84: '0,0,0'\n  },\n  EPSG_5354: {\n    towgs84: '0,0,0'\n  },\n  EPSG_5371: {\n    towgs84: '0,0,0'\n  },\n  EPSG_5373: {\n    towgs84: '0,0,0'\n  },\n  EPSG_5381: {\n    towgs84: '0,0,0'\n  },\n  EPSG_5393: {\n    towgs84: '0,0,0'\n  },\n  EPSG_5489: {\n    towgs84: '0,0,0'\n  },\n  EPSG_5593: {\n    towgs84: '0,0,0'\n  },\n  EPSG_6135: {\n    towgs84: '0,0,0'\n  },\n  EPSG_6365: {\n    towgs84: '0,0,0'\n  },\n  EPSG_5246: {\n    towgs84: '0,0,0'\n  },\n  EPSG_7886: {\n    towgs84: '0,0,0'\n  },\n  EPSG_8431: {\n    towgs84: '0,0,0'\n  },\n  EPSG_8427: {\n    towgs84: '0,0,0'\n  },\n  EPSG_8699: {\n    towgs84: '0,0,0'\n  },\n  EPSG_8818: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4757: {\n    towgs84: '0,0,0'\n  },\n  EPSG_9140: {\n    towgs84: '0,0,0'\n  },\n  EPSG_8086: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4686: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4737: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4702: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4747: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4749: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4674: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4755: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4759: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4762: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4763: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4764: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4166: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4170: {\n    towgs84: '0,0,0'\n  },\n  EPSG_5546: {\n    towgs84: '0,0,0'\n  },\n  EPSG_7844: {\n    towgs84: '0,0,0'\n  },\n  EPSG_4818: {\n    towgs84: '589,76,480'\n  }\n};\n\nfor (var key in datums) {\n  var datum = datums[key];\n  if (!datum.datumName) {\n    continue;\n  }\n  datums[datum.datumName] = datum;\n}\n\nexport default datums;\n", "import { PJD_3PARAM, PJD_7PARAM, PJD_GRIDSHIFT, PJD_WGS84, PJD_NODATUM, SEC_TO_RAD } from './constants/values';\n\nfunction datum(datumCode, datum_params, a, b, es, ep2, nadgrids) {\n  var out = {};\n\n  if (datumCode === undefined || datumCode === 'none') {\n    out.datum_type = PJD_NODATUM;\n  } else {\n    out.datum_type = PJD_WGS84;\n  }\n\n  if (datum_params) {\n    out.datum_params = datum_params.map(parseFloat);\n    if (out.datum_params[0] !== 0 || out.datum_params[1] !== 0 || out.datum_params[2] !== 0) {\n      out.datum_type = PJD_3PARAM;\n    }\n    if (out.datum_params.length > 3) {\n      if (out.datum_params[3] !== 0 || out.datum_params[4] !== 0 || out.datum_params[5] !== 0 || out.datum_params[6] !== 0) {\n        out.datum_type = PJD_7PARAM;\n        out.datum_params[3] *= SEC_TO_RAD;\n        out.datum_params[4] *= SEC_TO_RAD;\n        out.datum_params[5] *= SEC_TO_RAD;\n        out.datum_params[6] = (out.datum_params[6] / 1000000.0) + 1.0;\n      }\n    }\n  }\n\n  if (nadgrids) {\n    out.datum_type = PJD_GRIDSHIFT;\n    out.grids = nadgrids;\n  }\n  out.a = a; // datum object also uses these values\n  out.b = b;\n  out.es = es;\n  out.ep2 = ep2;\n  return out;\n}\n\nexport default datum;\n", "/**\n * Resources for details of NTv2 file formats:\n * - https://web.archive.org/web/20140127204822if_/http://www.mgs.gov.on.ca:80/stdprodconsume/groups/content/@mgs/@iandit/documents/resourcelist/stel02_047447.pdf\n * - http://mimaka.com/help/gs/html/004_NTV2%20Data%20Format.htm\n */\n\n/**\n * @typedef {Object} NadgridInfo\n * @property {string} name The name of the NAD grid or 'null' if not specified.\n * @property {boolean} mandatory Indicates if the grid is mandatory (true) or optional (false).\n * @property {*} grid The loaded NAD grid object, or null if not loaded or not applicable.\n * @property {boolean} isNull True if the grid is explicitly 'null', otherwise false.\n */\n\n/**\n * @typedef {Object} NTV2GridOptions\n * @property {boolean} [includeErrorFields=true] Whether to include error fields in the subgrids.\n */\n\n/**\n * @typedef {Object} NadgridHeader\n * @property {number} [nFields] Number of fields in the header.\n * @property {number} [nSubgridFields] Number of fields in each subgrid header.\n * @property {number} nSubgrids Number of subgrids in the file.\n * @property {string} [shiftType] Type of shift (e.g., \"SECONDS\").\n * @property {number} [fromSemiMajorAxis] Source ellipsoid semi-major axis.\n * @property {number} [fromSemiMinorAxis] Source ellipsoid semi-minor axis.\n * @property {number} [toSemiMajorAxis] Target ellipsoid semi-major axis.\n * @property {number} [toSemiMinorAxis] Target ellipsoid semi-minor axis.\n */\n\n/**\n * @typedef {Object} Subgrid\n * @property {Array<number>} ll Lower left corner of the grid in radians [longitude, latitude].\n * @property {Array<number>} del Grid spacing in radians [longitude interval, latitude interval].\n * @property {Array<number>} lim Number of columns in the grid [longitude columns, latitude columns].\n * @property {number} [count] Total number of grid nodes.\n * @property {Array} cvs Mapped node values for the grid.\n */\n\n/** @typedef {{header: NadgridHeader, subgrids: Array<Subgrid>}} NADGrid */\n\nvar loadedNadgrids = {};\n\n/**\n * @overload\n * @param {string} key - The key to associate with the loaded grid.\n * @param {ArrayBuffer} data - The NTv2 grid data as an ArrayBuffer.\n * @param {NTV2GridOptions} [options] - Optional parameters for loading the grid.\n * @returns {NADGrid} - The loaded NAD grid information.\n */\n/**\n * @overload\n * @param {string} key - The key to associate with the loaded grid.\n * @param {import('geotiff').GeoTIFF} data - The GeoTIFF instance to read the grid from.\n * @returns {{ready: Promise<NADGrid>}} - A promise that resolves to the loaded grid information.\n */\n/**\n * Load either a NTv2 file (.gsb) or a Geotiff (.tif) to a key that can be used in a proj string like +nadgrids=<key>. Pass the NTv2 file\n * as an ArrayBuffer. Pass Geotiff as a GeoTIFF instance from the geotiff.js library.\n * @param {string} key - The key to associate with the loaded grid.\n * @param {ArrayBuffer|import('geotiff').GeoTIFF} data The data to load, either an ArrayBuffer for NTv2 or a GeoTIFF instance.\n * @param {NTV2GridOptions} [options] Optional parameters.\n * @returns {{ready: Promise<NADGrid>}|NADGrid} - A promise that resolves to the loaded grid information.\n */\nexport default function nadgrid(key, data, options) {\n  if (data instanceof ArrayBuffer) {\n    return readNTV2Grid(key, data, options);\n  }\n  return { ready: readGeotiffGrid(key, data) };\n}\n\n/**\n * @param {string} key The key to associate with the loaded grid.\n * @param {ArrayBuffer} data The NTv2 grid data as an ArrayBuffer.\n * @param {NTV2GridOptions} [options] Optional parameters for loading the grid.\n * @returns {NADGrid} The loaded NAD grid information.\n */\nfunction readNTV2Grid(key, data, options) {\n  var includeErrorFields = true;\n  if (options !== undefined && options.includeErrorFields === false) {\n    includeErrorFields = false;\n  }\n  var view = new DataView(data);\n  var isLittleEndian = detectLittleEndian(view);\n  var header = readHeader(view, isLittleEndian);\n  var subgrids = readSubgrids(view, header, isLittleEndian, includeErrorFields);\n  var nadgrid = { header: header, subgrids: subgrids };\n  loadedNadgrids[key] = nadgrid;\n  return nadgrid;\n}\n\n/**\n * @param {string} key The key to associate with the loaded grid.\n * @param {import('geotiff').GeoTIFF} tiff The GeoTIFF instance to read the grid from.\n * @returns {Promise<NADGrid>} A promise that resolves to the loaded NAD grid information.\n */\nasync function readGeotiffGrid(key, tiff) {\n  var subgrids = [];\n  var subGridCount = await tiff.getImageCount();\n  // proj produced tiff grid shift files appear to organize lower res subgrids first, higher res/ child subgrids last.\n  for (var subgridIndex = subGridCount - 1; subgridIndex >= 0; subgridIndex--) {\n    var image = await tiff.getImage(subgridIndex);\n\n    var rasters = await image.readRasters();\n    var data = rasters;\n    var lim = [image.getWidth(), image.getHeight()];\n    var imageBBoxRadians = image.getBoundingBox().map(degreesToRadians);\n    var del = [image.fileDirectory.ModelPixelScale[0], image.fileDirectory.ModelPixelScale[1]].map(degreesToRadians);\n\n    var maxX = imageBBoxRadians[0] + (lim[0] - 1) * del[0];\n    var minY = imageBBoxRadians[3] - (lim[1] - 1) * del[1];\n\n    var latitudeOffsetBand = data[0];\n    var longitudeOffsetBand = data[1];\n    var nodes = [];\n\n    for (let i = lim[1] - 1; i >= 0; i--) {\n      for (let j = lim[0] - 1; j >= 0; j--) {\n        var index = i * lim[0] + j;\n        nodes.push([-secondsToRadians(longitudeOffsetBand[index]), secondsToRadians(latitudeOffsetBand[index])]);\n      }\n    }\n\n    subgrids.push({\n      del: del,\n      lim: lim,\n      ll: [-maxX, minY],\n      cvs: nodes\n    });\n  }\n\n  var tifGrid = {\n    header: {\n      nSubgrids: subGridCount\n    },\n    subgrids: subgrids\n  };\n  loadedNadgrids[key] = tifGrid;\n  return tifGrid;\n};\n\n/**\n * Given a proj4 value for nadgrids, return an array of loaded grids\n * @param {string} nadgrids A comma-separated list of grid names, optionally prefixed with '@' to indicate optional grids.\n * @returns\n */\nexport function getNadgrids(nadgrids) {\n  // Format details: http://proj.maptools.org/gen_parms.html\n  if (nadgrids === undefined) {\n    return null;\n  }\n  var grids = nadgrids.split(',');\n  return grids.map(parseNadgridString);\n}\n\n/**\n * @param {string} value The nadgrid string to get information for.\n * @returns {NadgridInfo|null} An object with grid information, or null if the input is empty.\n */\nfunction parseNadgridString(value) {\n  if (value.length === 0) {\n    return null;\n  }\n  var optional = value[0] === '@';\n  if (optional) {\n    value = value.slice(1);\n  }\n  if (value === 'null') {\n    return { name: 'null', mandatory: !optional, grid: null, isNull: true };\n  }\n  return {\n    name: value,\n    mandatory: !optional,\n    grid: loadedNadgrids[value] || null,\n    isNull: false\n  };\n}\n\nfunction degreesToRadians(degrees) {\n  return (degrees) * Math.PI / 180;\n}\n\nfunction secondsToRadians(seconds) {\n  return (seconds / 3600) * Math.PI / 180;\n}\n\nfunction detectLittleEndian(view) {\n  var nFields = view.getInt32(8, false);\n  if (nFields === 11) {\n    return false;\n  }\n  nFields = view.getInt32(8, true);\n  if (nFields !== 11) {\n    console.warn('Failed to detect nadgrid endian-ness, defaulting to little-endian');\n  }\n  return true;\n}\n\nfunction readHeader(view, isLittleEndian) {\n  return {\n    nFields: view.getInt32(8, isLittleEndian),\n    nSubgridFields: view.getInt32(24, isLittleEndian),\n    nSubgrids: view.getInt32(40, isLittleEndian),\n    shiftType: decodeString(view, 56, 56 + 8).trim(),\n    fromSemiMajorAxis: view.getFloat64(120, isLittleEndian),\n    fromSemiMinorAxis: view.getFloat64(136, isLittleEndian),\n    toSemiMajorAxis: view.getFloat64(152, isLittleEndian),\n    toSemiMinorAxis: view.getFloat64(168, isLittleEndian)\n  };\n}\n\nfunction decodeString(view, start, end) {\n  return String.fromCharCode.apply(null, new Uint8Array(view.buffer.slice(start, end)));\n}\n\nfunction readSubgrids(view, header, isLittleEndian, includeErrorFields) {\n  var gridOffset = 176;\n  var grids = [];\n  for (var i = 0; i < header.nSubgrids; i++) {\n    var subHeader = readGridHeader(view, gridOffset, isLittleEndian);\n    var nodes = readGridNodes(view, gridOffset, subHeader, isLittleEndian, includeErrorFields);\n    var lngColumnCount = Math.round(\n      1 + (subHeader.upperLongitude - subHeader.lowerLongitude) / subHeader.longitudeInterval);\n    var latColumnCount = Math.round(\n      1 + (subHeader.upperLatitude - subHeader.lowerLatitude) / subHeader.latitudeInterval);\n    // Proj4 operates on radians whereas the coordinates are in seconds in the grid\n    grids.push({\n      ll: [secondsToRadians(subHeader.lowerLongitude), secondsToRadians(subHeader.lowerLatitude)],\n      del: [secondsToRadians(subHeader.longitudeInterval), secondsToRadians(subHeader.latitudeInterval)],\n      lim: [lngColumnCount, latColumnCount],\n      count: subHeader.gridNodeCount,\n      cvs: mapNodes(nodes)\n    });\n    var rowSize = 16;\n    if (includeErrorFields === false) {\n      rowSize = 8;\n    }\n    gridOffset += 176 + subHeader.gridNodeCount * rowSize;\n  }\n  return grids;\n}\n\n/**\n * @param {*} nodes\n * @returns Array<Array<number>>\n */\nfunction mapNodes(nodes) {\n  return nodes.map(function (r) {\n    return [secondsToRadians(r.longitudeShift), secondsToRadians(r.latitudeShift)];\n  });\n}\n\nfunction readGridHeader(view, offset, isLittleEndian) {\n  return {\n    name: decodeString(view, offset + 8, offset + 16).trim(),\n    parent: decodeString(view, offset + 24, offset + 24 + 8).trim(),\n    lowerLatitude: view.getFloat64(offset + 72, isLittleEndian),\n    upperLatitude: view.getFloat64(offset + 88, isLittleEndian),\n    lowerLongitude: view.getFloat64(offset + 104, isLittleEndian),\n    upperLongitude: view.getFloat64(offset + 120, isLittleEndian),\n    latitudeInterval: view.getFloat64(offset + 136, isLittleEndian),\n    longitudeInterval: view.getFloat64(offset + 152, isLittleEndian),\n    gridNodeCount: view.getInt32(offset + 168, isLittleEndian)\n  };\n}\n\nfunction readGridNodes(view, offset, gridHeader, isLittleEndian, includeErrorFields) {\n  var nodesOffset = offset + 176;\n  var gridRecordLength = 16;\n\n  if (includeErrorFields === false) {\n    gridRecordLength = 8;\n  }\n\n  var gridShiftRecords = [];\n  for (var i = 0; i < gridHeader.gridNodeCount; i++) {\n    var record = {\n      latitudeShift: view.getFloat32(nodesOffset + i * gridRecordLength, isLittleEndian),\n      longitudeShift: view.getFloat32(nodesOffset + i * gridRecordLength + 4, isLittleEndian)\n\n    };\n\n    if (includeErrorFields !== false) {\n      record.latitudeAccuracy = view.getFloat32(nodesOffset + i * gridRecordLength + 8, isLittleEndian);\n      record.longitudeAccuracy = view.getFloat32(nodesOffset + i * gridRecordLength + 12, isLittleEndian);\n    }\n\n    gridShiftRecords.push(record);\n  }\n  return gridShiftRecords;\n}\n", "import parseCode from './parseCode';\nimport extend from './extend';\nimport projections from './projections';\nimport { sphere as dc_sphere, eccentricity as dc_eccentricity } from './deriveConstants';\nimport Datum from './constants/Datum';\nimport datum from './datum';\nimport match from './match';\nimport { getNadgrids } from './nadgrid';\n\n/**\n * @typedef {Object} DatumDefinition\n * @property {number} datum_type - The type of datum.\n * @property {number} a - Semi-major axis of the ellipsoid.\n * @property {number} b - Semi-minor axis of the ellipsoid.\n * @property {number} es - Eccentricity squared of the ellipsoid.\n * @property {number} ep2 - Second eccentricity squared of the ellipsoid.\n */\n\n/**\n * @param {string | import('./core').PROJJSONDefinition | import('./defs').ProjectionDefinition} srsCode\n * @param {(errorMessage?: string, instance?: Projection) => void} [callback]\n */\nfunction Projection(srsCode, callback) {\n  if (!(this instanceof Projection)) {\n    return new Projection(srsCode);\n  }\n  /** @type {<T extends import('./core').TemplateCoordinates>(coordinates: T, enforceAxis?: boolean) => T} */\n  this.forward = null;\n  /** @type {<T extends import('./core').TemplateCoordinates>(coordinates: T, enforceAxis?: boolean) => T} */\n  this.inverse = null;\n  /** @type {string} */\n  this.name;\n  /** @type {string} */\n  this.title;\n  callback = callback || function (error) {\n    if (error) {\n      throw error;\n    }\n  };\n  var json = parseCode(srsCode);\n  if (typeof json !== 'object') {\n    callback('Could not parse to valid json: ' + srsCode);\n    return;\n  }\n  var ourProj = Projection.projections.get(json.projName);\n  if (!ourProj) {\n    callback('Could not get projection name from: ' + srsCode);\n    return;\n  }\n  if (json.datumCode && json.datumCode !== 'none') {\n    var datumDef = match(Datum, json.datumCode);\n    if (datumDef) {\n      json.datum_params = json.datum_params || (datumDef.towgs84 ? datumDef.towgs84.split(',') : null);\n      json.ellps = datumDef.ellipse;\n      json.datumName = datumDef.datumName ? datumDef.datumName : json.datumCode;\n    }\n  }\n  json.k0 = json.k0 || 1.0;\n  json.axis = json.axis || 'enu';\n  json.ellps = json.ellps || 'wgs84';\n  json.lat1 = json.lat1 || json.lat0; // Lambert_Conformal_Conic_1SP, for example, needs this\n\n  var sphere_ = dc_sphere(json.a, json.b, json.rf, json.ellps, json.sphere);\n  var ecc = dc_eccentricity(sphere_.a, sphere_.b, sphere_.rf, json.R_A);\n  var nadgrids = getNadgrids(json.nadgrids);\n  /** @type {DatumDefinition} */\n  var datumObj = json.datum || datum(json.datumCode, json.datum_params, sphere_.a, sphere_.b, ecc.es, ecc.ep2,\n    nadgrids);\n\n  extend(this, json); // transfer everything over from the projection because we don't know what we'll need\n  extend(this, ourProj); // transfer all the methods from the projection\n\n  // copy the 4 things over we calculated in deriveConstants.sphere\n  this.a = sphere_.a;\n  this.b = sphere_.b;\n  this.rf = sphere_.rf;\n  this.sphere = sphere_.sphere;\n\n  // copy the 3 things we calculated in deriveConstants.eccentricity\n  this.es = ecc.es;\n  this.e = ecc.e;\n  this.ep2 = ecc.ep2;\n\n  // add in the datum object\n  this.datum = datumObj;\n\n  // init the projection\n  if ('init' in this && typeof this.init === 'function') {\n    this.init();\n  }\n\n  // legecy callback from back in the day when it went to spatialreference.org\n  callback(null, this);\n}\nProjection.projections = projections;\nProjection.projections.start();\nexport default Projection;\n", "'use strict';\nimport { PJD_3PARAM, PJD_7PARAM, HALF_PI } from './constants/values';\nexport function compareDatums(source, dest) {\n  if (source.datum_type !== dest.datum_type) {\n    return false; // false, datums are not equal\n  } else if (source.a !== dest.a || Math.abs(source.es - dest.es) > 0.000000000050) {\n    // the tolerance for es is to ensure that GRS80 and WGS84\n    // are considered identical\n    return false;\n  } else if (source.datum_type === PJD_3PARAM) {\n    return (source.datum_params[0] === dest.datum_params[0] && source.datum_params[1] === dest.datum_params[1] && source.datum_params[2] === dest.datum_params[2]);\n  } else if (source.datum_type === PJD_7PARAM) {\n    return (source.datum_params[0] === dest.datum_params[0] && source.datum_params[1] === dest.datum_params[1] && source.datum_params[2] === dest.datum_params[2] && source.datum_params[3] === dest.datum_params[3] && source.datum_params[4] === dest.datum_params[4] && source.datum_params[5] === dest.datum_params[5] && source.datum_params[6] === dest.datum_params[6]);\n  } else {\n    return true; // datums are equal\n  }\n} // cs_compare_datums()\n\n/*\n * The function Convert_Geodetic_To_Geocentric converts geodetic coordinates\n * (latitude, longitude, and height) to geocentric coordinates (X, Y, Z),\n * according to the current ellipsoid parameters.\n *\n *    Latitude  : Geodetic latitude in radians                     (input)\n *    Longitude : Geodetic longitude in radians                    (input)\n *    Height    : Geodetic height, in meters                       (input)\n *    X         : Calculated Geocentric X coordinate, in meters    (output)\n *    Y         : Calculated Geocentric Y coordinate, in meters    (output)\n *    Z         : Calculated Geocentric Z coordinate, in meters    (output)\n *\n */\nexport function geodeticToGeocentric(p, es, a) {\n  var Longitude = p.x;\n  var Latitude = p.y;\n  var Height = p.z ? p.z : 0; // Z value not always supplied\n\n  var Rn; /*  Earth radius at location  */\n  var Sin_Lat; /*  Math.sin(Latitude)  */\n  var Sin2_Lat; /*  Square of Math.sin(Latitude)  */\n  var Cos_Lat; /*  Math.cos(Latitude)  */\n\n  /*\n   ** Don't blow up if Latitude is just a little out of the value\n   ** range as it may just be a rounding issue.  Also removed longitude\n   ** test, it should be wrapped by Math.cos() and Math.sin().  NFW for PROJ.4, Sep/2001.\n   */\n  if (Latitude < -HALF_PI && Latitude > -1.001 * HALF_PI) {\n    Latitude = -HALF_PI;\n  } else if (Latitude > HALF_PI && Latitude < 1.001 * HALF_PI) {\n    Latitude = HALF_PI;\n  } else if (Latitude < -HALF_PI) {\n    /* Latitude out of range */\n    // ..reportError('geocent:lat out of range:' + Latitude);\n    return { x: -Infinity, y: -Infinity, z: p.z };\n  } else if (Latitude > HALF_PI) {\n    /* Latitude out of range */\n    return { x: Infinity, y: Infinity, z: p.z };\n  }\n\n  if (Longitude > Math.PI) {\n    Longitude -= (2 * Math.PI);\n  }\n  Sin_Lat = Math.sin(Latitude);\n  Cos_Lat = Math.cos(Latitude);\n  Sin2_Lat = Sin_Lat * Sin_Lat;\n  Rn = a / (Math.sqrt(1.0e0 - es * Sin2_Lat));\n  return {\n    x: (Rn + Height) * Cos_Lat * Math.cos(Longitude),\n    y: (Rn + Height) * Cos_Lat * Math.sin(Longitude),\n    z: ((Rn * (1 - es)) + Height) * Sin_Lat\n  };\n} // cs_geodetic_to_geocentric()\n\nexport function geocentricToGeodetic(p, es, a, b) {\n  /* local defintions and variables */\n  /* end-criterium of loop, accuracy of sin(Latitude) */\n  var genau = 1e-12;\n  var genau2 = (genau * genau);\n  var maxiter = 30;\n\n  var P; /* distance between semi-minor axis and location */\n  var RR; /* distance between center and location */\n  var CT; /* sin of geocentric latitude */\n  var ST; /* cos of geocentric latitude */\n  var RX;\n  var RK;\n  var RN; /* Earth radius at location */\n  var CPHI0; /* cos of start or old geodetic latitude in iterations */\n  var SPHI0; /* sin of start or old geodetic latitude in iterations */\n  var CPHI; /* cos of searched geodetic latitude */\n  var SPHI; /* sin of searched geodetic latitude */\n  var SDPHI; /* end-criterium: addition-theorem of sin(Latitude(iter)-Latitude(iter-1)) */\n  var iter; /* # of continous iteration, max. 30 is always enough (s.a.) */\n\n  var X = p.x;\n  var Y = p.y;\n  var Z = p.z ? p.z : 0.0; // Z value not always supplied\n  var Longitude;\n  var Latitude;\n  var Height;\n\n  P = Math.sqrt(X * X + Y * Y);\n  RR = Math.sqrt(X * X + Y * Y + Z * Z);\n\n  /*      special cases for latitude and longitude */\n  if (P / a < genau) {\n    /*  special case, if P=0. (X=0., Y=0.) */\n    Longitude = 0.0;\n\n    /*  if (X,Y,Z)=(0.,0.,0.) then Height becomes semi-minor axis\n     *  of ellipsoid (=center of mass), Latitude becomes PI/2 */\n    if (RR / a < genau) {\n      Latitude = HALF_PI;\n      Height = -b;\n      return {\n        x: p.x,\n        y: p.y,\n        z: p.z\n      };\n    }\n  } else {\n    /*  ellipsoidal (geodetic) longitude\n     *  interval: -PI < Longitude <= +PI */\n    Longitude = Math.atan2(Y, X);\n  }\n\n  /* --------------------------------------------------------------\n   * Following iterative algorithm was developped by\n   * \"Institut for Erdmessung\", University of Hannover, July 1988.\n   * Internet: www.ife.uni-hannover.de\n   * Iterative computation of CPHI,SPHI and Height.\n   * Iteration of CPHI and SPHI to 10**-12 radian resp.\n   * 2*10**-7 arcsec.\n   * --------------------------------------------------------------\n   */\n  CT = Z / RR;\n  ST = P / RR;\n  RX = 1.0 / Math.sqrt(1.0 - es * (2.0 - es) * ST * ST);\n  CPHI0 = ST * (1.0 - es) * RX;\n  SPHI0 = CT * RX;\n  iter = 0;\n\n  /* loop to find sin(Latitude) resp. Latitude\n   * until |sin(Latitude(iter)-Latitude(iter-1))| < genau */\n  do {\n    iter++;\n    RN = a / Math.sqrt(1.0 - es * SPHI0 * SPHI0);\n\n    /*  ellipsoidal (geodetic) height */\n    Height = P * CPHI0 + Z * SPHI0 - RN * (1.0 - es * SPHI0 * SPHI0);\n\n    RK = es * RN / (RN + Height);\n    RX = 1.0 / Math.sqrt(1.0 - RK * (2.0 - RK) * ST * ST);\n    CPHI = ST * (1.0 - RK) * RX;\n    SPHI = CT * RX;\n    SDPHI = SPHI * CPHI0 - CPHI * SPHI0;\n    CPHI0 = CPHI;\n    SPHI0 = SPHI;\n  }\n  while (SDPHI * SDPHI > genau2 && iter < maxiter);\n\n  /*      ellipsoidal (geodetic) latitude */\n  Latitude = Math.atan(SPHI / Math.abs(CPHI));\n  return {\n    x: Longitude,\n    y: Latitude,\n    z: Height\n  };\n} // cs_geocentric_to_geodetic()\n\n/****************************************************************/\n// pj_geocentic_to_wgs84( p )\n//  p = point to transform in geocentric coordinates (x,y,z)\n\n/** point object, nothing fancy, just allows values to be\n    passed back and forth by reference rather than by value.\n    Other point classes may be used as long as they have\n    x and y properties, which will get modified in the transform method.\n*/\nexport function geocentricToWgs84(p, datum_type, datum_params) {\n  if (datum_type === PJD_3PARAM) {\n    // if( x[io] === HUGE_VAL )\n    //    continue;\n    return {\n      x: p.x + datum_params[0],\n      y: p.y + datum_params[1],\n      z: p.z + datum_params[2]\n    };\n  } else if (datum_type === PJD_7PARAM) {\n    var Dx_BF = datum_params[0];\n    var Dy_BF = datum_params[1];\n    var Dz_BF = datum_params[2];\n    var Rx_BF = datum_params[3];\n    var Ry_BF = datum_params[4];\n    var Rz_BF = datum_params[5];\n    var M_BF = datum_params[6];\n    // if( x[io] === HUGE_VAL )\n    //    continue;\n    return {\n      x: M_BF * (p.x - Rz_BF * p.y + Ry_BF * p.z) + Dx_BF,\n      y: M_BF * (Rz_BF * p.x + p.y - Rx_BF * p.z) + Dy_BF,\n      z: M_BF * (-Ry_BF * p.x + Rx_BF * p.y + p.z) + Dz_BF\n    };\n  }\n} // cs_geocentric_to_wgs84\n\n/****************************************************************/\n// pj_geocentic_from_wgs84()\n//  coordinate system definition,\n//  point to transform in geocentric coordinates (x,y,z)\nexport function geocentricFromWgs84(p, datum_type, datum_params) {\n  if (datum_type === PJD_3PARAM) {\n    // if( x[io] === HUGE_VAL )\n    //    continue;\n    return {\n      x: p.x - datum_params[0],\n      y: p.y - datum_params[1],\n      z: p.z - datum_params[2]\n    };\n  } else if (datum_type === PJD_7PARAM) {\n    var Dx_BF = datum_params[0];\n    var Dy_BF = datum_params[1];\n    var Dz_BF = datum_params[2];\n    var Rx_BF = datum_params[3];\n    var Ry_BF = datum_params[4];\n    var Rz_BF = datum_params[5];\n    var M_BF = datum_params[6];\n    var x_tmp = (p.x - Dx_BF) / M_BF;\n    var y_tmp = (p.y - Dy_BF) / M_BF;\n    var z_tmp = (p.z - Dz_BF) / M_BF;\n    // if( x[io] === HUGE_VAL )\n    //    continue;\n\n    return {\n      x: x_tmp + Rz_BF * y_tmp - Ry_BF * z_tmp,\n      y: -Rz_BF * x_tmp + y_tmp + Rx_BF * z_tmp,\n      z: Ry_BF * x_tmp - Rx_BF * y_tmp + z_tmp\n    };\n  } // cs_geocentric_from_wgs84()\n}\n", "import {\n  PJD_3PARAM,\n  PJD_7PARAM,\n  PJD_GRIDSHIFT,\n  PJD_NODATUM,\n  R2D,\n  SRS_WGS84_ESQUARED,\n  SRS_WGS84_SEMIMAJOR, SRS_WGS84_SEMIMINOR\n} from './constants/values';\n\nimport { geodeticToGeocentric, geocentricToGeodetic, geocentricToWgs84, geocentricFromWgs84, compareDatums } from './datumUtils';\nimport adjust_lon from './common/adjust_lon';\nfunction checkParams(type) {\n  return (type === PJD_3PARAM || type === PJD_7PARAM);\n}\n\nexport default function (source, dest, point) {\n  // Short cut if the datums are identical.\n  if (compareDatums(source, dest)) {\n    return point; // in this case, zero is sucess,\n    // whereas cs_compare_datums returns 1 to indicate TRUE\n    // confusing, should fix this\n  }\n\n  // Explicitly skip datum transform by setting 'datum=none' as parameter for either source or dest\n  if (source.datum_type === PJD_NODATUM || dest.datum_type === PJD_NODATUM) {\n    return point;\n  }\n\n  // If this datum requires grid shifts, then apply it to geodetic coordinates.\n  var source_a = source.a;\n  var source_es = source.es;\n  if (source.datum_type === PJD_GRIDSHIFT) {\n    var gridShiftCode = applyGridShift(source, false, point);\n    if (gridShiftCode !== 0) {\n      return undefined;\n    }\n    source_a = SRS_WGS84_SEMIMAJOR;\n    source_es = SRS_WGS84_ESQUARED;\n  }\n\n  var dest_a = dest.a;\n  var dest_b = dest.b;\n  var dest_es = dest.es;\n  if (dest.datum_type === PJD_GRIDSHIFT) {\n    dest_a = SRS_WGS84_SEMIMAJOR;\n    dest_b = SRS_WGS84_SEMIMINOR;\n    dest_es = SRS_WGS84_ESQUARED;\n  }\n\n  // Do we need to go through geocentric coordinates?\n  if (source_es === dest_es && source_a === dest_a && !checkParams(source.datum_type) && !checkParams(dest.datum_type)) {\n    return point;\n  }\n\n  // Convert to geocentric coordinates.\n  point = geodeticToGeocentric(point, source_es, source_a);\n  // Convert between datums\n  if (checkParams(source.datum_type)) {\n    point = geocentricToWgs84(point, source.datum_type, source.datum_params);\n  }\n  if (checkParams(dest.datum_type)) {\n    point = geocentricFromWgs84(point, dest.datum_type, dest.datum_params);\n  }\n  point = geocentricToGeodetic(point, dest_es, dest_a, dest_b);\n\n  if (dest.datum_type === PJD_GRIDSHIFT) {\n    var destGridShiftResult = applyGridShift(dest, true, point);\n    if (destGridShiftResult !== 0) {\n      return undefined;\n    }\n  }\n\n  return point;\n}\n\nexport function applyGridShift(source, inverse, point) {\n  if (source.grids === null || source.grids.length === 0) {\n    console.log('Grid shift grids not found');\n    return -1;\n  }\n  var input = { x: -point.x, y: point.y };\n  var output = { x: Number.NaN, y: Number.NaN };\n  var attemptedGrids = [];\n  outer:\n  for (var i = 0; i < source.grids.length; i++) {\n    var grid = source.grids[i];\n    attemptedGrids.push(grid.name);\n    if (grid.isNull) {\n      output = input;\n      break;\n    }\n    if (grid.grid === null) {\n      if (grid.mandatory) {\n        console.log('Unable to find mandatory grid \\'' + grid.name + '\\'');\n        return -1;\n      }\n      continue;\n    }\n    var subgrids = grid.grid.subgrids;\n    for (var j = 0, jj = subgrids.length; j < jj; j++) {\n      var subgrid = subgrids[j];\n      // skip tables that don't match our point at all\n      var epsilon = (Math.abs(subgrid.del[1]) + Math.abs(subgrid.del[0])) / 10000.0;\n      var minX = subgrid.ll[0] - epsilon;\n      var minY = subgrid.ll[1] - epsilon;\n      var maxX = subgrid.ll[0] + (subgrid.lim[0] - 1) * subgrid.del[0] + epsilon;\n      var maxY = subgrid.ll[1] + (subgrid.lim[1] - 1) * subgrid.del[1] + epsilon;\n      if (minY > input.y || minX > input.x || maxY < input.y || maxX < input.x) {\n        continue;\n      }\n      output = applySubgridShift(input, inverse, subgrid);\n      if (!isNaN(output.x)) {\n        break outer;\n      }\n    }\n  }\n  if (isNaN(output.x)) {\n    console.log('Failed to find a grid shift table for location \\''\n      + -input.x * R2D + ' ' + input.y * R2D + ' tried: \\'' + attemptedGrids + '\\'');\n    return -1;\n  }\n  point.x = -output.x;\n  point.y = output.y;\n  return 0;\n}\n\nfunction applySubgridShift(pin, inverse, ct) {\n  var val = { x: Number.NaN, y: Number.NaN };\n  if (isNaN(pin.x)) {\n    return val;\n  }\n  var tb = { x: pin.x, y: pin.y };\n  tb.x -= ct.ll[0];\n  tb.y -= ct.ll[1];\n  tb.x = adjust_lon(tb.x - Math.PI) + Math.PI;\n  var t = nadInterpolate(tb, ct);\n  if (inverse) {\n    if (isNaN(t.x)) {\n      return val;\n    }\n    t.x = tb.x - t.x;\n    t.y = tb.y - t.y;\n    var i = 9, tol = 1e-12;\n    var dif, del;\n    do {\n      del = nadInterpolate(t, ct);\n      if (isNaN(del.x)) {\n        console.log('Inverse grid shift iteration failed, presumably at grid edge.  Using first approximation.');\n        break;\n      }\n      dif = { x: tb.x - (del.x + t.x), y: tb.y - (del.y + t.y) };\n      t.x += dif.x;\n      t.y += dif.y;\n    } while (i-- && Math.abs(dif.x) > tol && Math.abs(dif.y) > tol);\n    if (i < 0) {\n      console.log('Inverse grid shift iterator failed to converge.');\n      return val;\n    }\n    val.x = adjust_lon(t.x + ct.ll[0]);\n    val.y = t.y + ct.ll[1];\n  } else {\n    if (!isNaN(t.x)) {\n      val.x = pin.x + t.x;\n      val.y = pin.y + t.y;\n    }\n  }\n  return val;\n}\n\nfunction nadInterpolate(pin, ct) {\n  var t = { x: pin.x / ct.del[0], y: pin.y / ct.del[1] };\n  var indx = { x: Math.floor(t.x), y: Math.floor(t.y) };\n  var frct = { x: t.x - 1.0 * indx.x, y: t.y - 1.0 * indx.y };\n  var val = { x: Number.NaN, y: Number.NaN };\n  var inx;\n  if (indx.x < 0 || indx.x >= ct.lim[0]) {\n    return val;\n  }\n  if (indx.y < 0 || indx.y >= ct.lim[1]) {\n    return val;\n  }\n  inx = (indx.y * ct.lim[0]) + indx.x;\n  var f00 = { x: ct.cvs[inx][0], y: ct.cvs[inx][1] };\n  inx++;\n  var f10 = { x: ct.cvs[inx][0], y: ct.cvs[inx][1] };\n  inx += ct.lim[0];\n  var f11 = { x: ct.cvs[inx][0], y: ct.cvs[inx][1] };\n  inx--;\n  var f01 = { x: ct.cvs[inx][0], y: ct.cvs[inx][1] };\n  var m11 = frct.x * frct.y, m10 = frct.x * (1.0 - frct.y),\n    m00 = (1.0 - frct.x) * (1.0 - frct.y), m01 = (1.0 - frct.x) * frct.y;\n  val.x = (m00 * f00.x + m10 * f10.x + m01 * f01.x + m11 * f11.x);\n  val.y = (m00 * f00.y + m10 * f10.y + m01 * f01.y + m11 * f11.y);\n  return val;\n}\n", "export default function (crs, denorm, point) {\n  var xin = point.x,\n    yin = point.y,\n    zin = point.z || 0.0;\n  var v, t, i;\n  /** @type {import(\"./core\").InterfaceCoordinates} */\n  var out = {};\n  for (i = 0; i < 3; i++) {\n    if (denorm && i === 2 && point.z === undefined) {\n      continue;\n    }\n    if (i === 0) {\n      v = xin;\n      if ('ew'.indexOf(crs.axis[i]) !== -1) {\n        t = 'x';\n      } else {\n        t = 'y';\n      }\n    } else if (i === 1) {\n      v = yin;\n      if ('ns'.indexOf(crs.axis[i]) !== -1) {\n        t = 'y';\n      } else {\n        t = 'x';\n      }\n    } else {\n      v = zin;\n      t = 'z';\n    }\n    switch (crs.axis[i]) {\n      case 'e':\n        out[t] = v;\n        break;\n      case 'w':\n        out[t] = -v;\n        break;\n      case 'n':\n        out[t] = v;\n        break;\n      case 's':\n        out[t] = -v;\n        break;\n      case 'u':\n        if (point[t] !== undefined) {\n          out.z = v;\n        }\n        break;\n      case 'd':\n        if (point[t] !== undefined) {\n          out.z = -v;\n        }\n        break;\n      default:\n      // console.log(\"ERROR: unknow axis (\"+crs.axis[i]+\") - check definition of \"+crs.projName);\n        return null;\n    }\n  }\n  return out;\n}\n", "/**\n * @param {Array<number>} array\n * @returns {import(\"../core\").InterfaceCoordinates}\n */\nexport default function (array) {\n  var out = {\n    x: array[0],\n    y: array[1]\n  };\n  if (array.length > 2) {\n    out.z = array[2];\n  }\n  if (array.length > 3) {\n    out.m = array[3];\n  }\n  return out;\n}\n", "export default function (point) {\n  checkCoord(point.x);\n  checkCoord(point.y);\n}\nfunction checkCoord(num) {\n  if (typeof Number.isFinite === 'function') {\n    if (Number.isFinite(num)) {\n      return;\n    }\n    throw new TypeError('coordinates must be finite numbers');\n  }\n  if (typeof num !== 'number' || num !== num || !isFinite(num)) {\n    throw new TypeError('coordinates must be finite numbers');\n  }\n}\n", "import { D2R, R2D, PJD_3PARAM, PJD_7PARAM, PJD_GRIDSHIFT } from './constants/values';\nimport datum_transform from './datum_transform';\nimport adjust_axis from './adjust_axis';\nimport proj from './Proj';\nimport toPoint from './common/toPoint';\nimport checkSanity from './checkSanity';\n\nfunction checkNotWGS(source, dest) {\n  return (\n    (source.datum.datum_type === PJD_3PARAM || source.datum.datum_type === PJD_7PARAM || source.datum.datum_type === PJD_GRIDSHIFT) && dest.datumCode !== 'WGS84')\n  || ((dest.datum.datum_type === PJD_3PARAM || dest.datum.datum_type === PJD_7PARAM || dest.datum.datum_type === PJD_GRIDSHIFT) && source.datumCode !== 'WGS84');\n}\n\n/**\n * @param {import('./defs').ProjectionDefinition} source\n * @param {import('./defs').ProjectionDefinition} dest\n * @param {import('./core').TemplateCoordinates} point\n * @param {boolean} enforceAxis\n * @returns {import('./core').InterfaceCoordinates | undefined}\n */\nexport default function transform(source, dest, point, enforceAxis) {\n  var wgs84;\n  if (Array.isArray(point)) {\n    point = toPoint(point);\n  } else {\n    // Clone the point object so inputs don't get modified\n    point = {\n      x: point.x,\n      y: point.y,\n      z: point.z,\n      m: point.m\n    };\n  }\n  var hasZ = point.z !== undefined;\n  checkSanity(point);\n  // Workaround for datum shifts towgs84, if either source or destination projection is not wgs84\n  if (source.datum && dest.datum && checkNotWGS(source, dest)) {\n    wgs84 = new proj('WGS84');\n    point = transform(source, wgs84, point, enforceAxis);\n    source = wgs84;\n  }\n  // DGR, 2010/11/12\n  if (enforceAxis && source.axis !== 'enu') {\n    point = adjust_axis(source, false, point);\n  }\n  // Transform source points to long/lat, if they aren't already.\n  if (source.projName === 'longlat') {\n    point = {\n      x: point.x * D2R,\n      y: point.y * D2R,\n      z: point.z || 0\n    };\n  } else {\n    if (source.to_meter) {\n      point = {\n        x: point.x * source.to_meter,\n        y: point.y * source.to_meter,\n        z: point.z || 0\n      };\n    }\n    point = source.inverse(point); // Convert Cartesian to longlat\n    if (!point) {\n      return;\n    }\n  }\n  // Adjust for the prime meridian if necessary\n  if (source.from_greenwich) {\n    point.x += source.from_greenwich;\n  }\n\n  // Convert datums if needed, and if possible.\n  point = datum_transform(source.datum, dest.datum, point);\n  if (!point) {\n    return;\n  }\n\n  point = /** @type {import('./core').InterfaceCoordinates} */ (point);\n\n  // Adjust for the prime meridian if necessary\n  if (dest.from_greenwich) {\n    point = {\n      x: point.x - dest.from_greenwich,\n      y: point.y,\n      z: point.z || 0\n    };\n  }\n\n  if (dest.projName === 'longlat') {\n    // convert radians to decimal degrees\n    point = {\n      x: point.x * R2D,\n      y: point.y * R2D,\n      z: point.z || 0\n    };\n  } else { // else project\n    point = dest.forward(point);\n    if (dest.to_meter) {\n      point = {\n        x: point.x / dest.to_meter,\n        y: point.y / dest.to_meter,\n        z: point.z || 0\n      };\n    }\n  }\n\n  // DGR, 2010/11/12\n  if (enforceAxis && dest.axis !== 'enu') {\n    return adjust_axis(dest, true, point);\n  }\n\n  if (point && !hasZ) {\n    delete point.z;\n  }\n  return point;\n}\n", "import proj from './Proj';\nimport transform from './transform';\nvar wgs84 = proj('WGS84');\n\n/**\n * @typedef {{x: number, y: number, z?: number, m?: number}} InterfaceCoordinates\n */\n\n/**\n * @typedef {Array<number> | InterfaceCoordinates} TemplateCoordinates\n */\n\n/**\n * @typedef {Object} Converter\n * @property {<T extends TemplateCoordinates>(coordinates: T, enforceAxis?: boolean) => T} forward\n * @property {<T extends TemplateCoordinates>(coordinates: T, enforceAxis?: boolean) => T} inverse\n * @property {proj} [oProj]\n */\n\n/**\n * @typedef {Object} PROJJSONDefinition\n * @property {string} [$schema]\n * @property {string} type\n * @property {string} [name]\n * @property {{authority: string, code: number}} [id]\n * @property {string} [scope]\n * @property {string} [area]\n * @property {{south_latitude: number, west_longitude: number, north_latitude: number, east_longitude: number}} [bbox]\n * @property {PROJJSONDefinition[]} [components]\n * @property {{type: string, name: string}} [datum]\n * @property {{\n *   name: string,\n *   members: Array<{\n *     name: string,\n *     id?: {authority: string, code: number}\n *   }>,\n *   ellipsoid?: {\n *     name: string,\n *     semi_major_axis: number,\n *     inverse_flattening?: number\n *   },\n *   accuracy?: string,\n *   id?: {authority: string, code: number}\n * }} [datum_ensemble]\n * @property {{\n *   subtype: string,\n *   axis: Array<{\n *     name: string,\n *     abbreviation?: string,\n *     direction: string,\n *     unit: string\n *   }>\n * }} [coordinate_system]\n * @property {{\n *   name: string,\n *   method: {name: string},\n *   parameters: Array<{\n *     name: string,\n *     value: number,\n *     unit?: string\n *   }>\n * }} [conversion]\n * @property {{\n *   name: string,\n *   method: {name: string},\n *   parameters: Array<{\n *     name: string,\n *     value: number,\n *     unit?: string,\n *     type?: string,\n *     file_name?: string\n *   }>\n * }} [transformation]\n */\n\n/**\n * @template {TemplateCoordinates} T\n * @param {proj} from\n * @param {proj} to\n * @param {T} coords\n * @param {boolean} [enforceAxis]\n * @returns {T}\n */\nfunction transformer(from, to, coords, enforceAxis) {\n  var transformedArray, out, keys;\n  if (Array.isArray(coords)) {\n    transformedArray = transform(from, to, coords, enforceAxis) || { x: NaN, y: NaN };\n    if (coords.length > 2) {\n      if ((typeof from.name !== 'undefined' && from.name === 'geocent') || (typeof to.name !== 'undefined' && to.name === 'geocent')) {\n        if (typeof transformedArray.z === 'number') {\n          return /** @type {T} */ ([transformedArray.x, transformedArray.y, transformedArray.z].concat(coords.slice(3)));\n        } else {\n          return /** @type {T} */ ([transformedArray.x, transformedArray.y, coords[2]].concat(coords.slice(3)));\n        }\n      } else {\n        return /** @type {T} */ ([transformedArray.x, transformedArray.y].concat(coords.slice(2)));\n      }\n    } else {\n      return /** @type {T} */ ([transformedArray.x, transformedArray.y]);\n    }\n  } else {\n    out = transform(from, to, coords, enforceAxis);\n    keys = Object.keys(coords);\n    if (keys.length === 2) {\n      return /** @type {T} */ (out);\n    }\n    keys.forEach(function (key) {\n      if ((typeof from.name !== 'undefined' && from.name === 'geocent') || (typeof to.name !== 'undefined' && to.name === 'geocent')) {\n        if (key === 'x' || key === 'y' || key === 'z') {\n          return;\n        }\n      } else {\n        if (key === 'x' || key === 'y') {\n          return;\n        }\n      }\n      out[key] = coords[key];\n    });\n    return /** @type {T} */ (out);\n  }\n}\n\n/**\n * @param {proj | string | PROJJSONDefinition | Converter} item\n * @returns {import('./Proj').default}\n */\nfunction checkProj(item) {\n  if (item instanceof proj) {\n    return item;\n  }\n  if (typeof item === 'object' && 'oProj' in item) {\n    return item.oProj;\n  }\n  return proj(/** @type {string | PROJJSONDefinition} */ (item));\n}\n\n/**\n * @overload\n * @param {string | PROJJSONDefinition | proj} toProj\n * @returns {Converter}\n */\n/**\n * @overload\n * @param {string | PROJJSONDefinition | proj} fromProj\n * @param {string | PROJJSONDefinition | proj} toProj\n * @returns {Converter}\n */\n/**\n * @template {TemplateCoordinates} T\n * @overload\n * @param {string | PROJJSONDefinition | proj} toProj\n * @param {T} coord\n * @returns {T}\n */\n/**\n * @template {TemplateCoordinates} T\n * @overload\n * @param {string | PROJJSONDefinition | proj} fromProj\n * @param {string | PROJJSONDefinition | proj} toProj\n * @param {T} coord\n * @returns {T}\n */\n/**\n * @template {TemplateCoordinates} T\n * @param {string | PROJJSONDefinition | proj} fromProjOrToProj\n * @param {string | PROJJSONDefinition | proj | TemplateCoordinates} [toProjOrCoord]\n * @param {T} [coord]\n * @returns {T|Converter}\n */\nfunction proj4(fromProjOrToProj, toProjOrCoord, coord) {\n  /** @type {proj} */\n  var fromProj;\n  /** @type {proj} */\n  var toProj;\n  var single = false;\n  /** @type {Converter} */\n  var obj;\n  if (typeof toProjOrCoord === 'undefined') {\n    toProj = checkProj(fromProjOrToProj);\n    fromProj = wgs84;\n    single = true;\n  } else if (typeof /** @type {?} */ (toProjOrCoord).x !== 'undefined' || Array.isArray(toProjOrCoord)) {\n    coord = /** @type {T} */ (/** @type {?} */ (toProjOrCoord));\n    toProj = checkProj(fromProjOrToProj);\n    fromProj = wgs84;\n    single = true;\n  }\n  if (!fromProj) {\n    fromProj = checkProj(fromProjOrToProj);\n  }\n  if (!toProj) {\n    toProj = checkProj(/** @type {string | PROJJSONDefinition | proj } */ (toProjOrCoord));\n  }\n  if (coord) {\n    return transformer(fromProj, toProj, coord);\n  } else {\n    obj = {\n      /**\n       * @template {TemplateCoordinates} T\n       * @param {T} coords\n       * @param {boolean=} enforceAxis\n       * @returns {T}\n       */\n      forward: function (coords, enforceAxis) {\n        return transformer(fromProj, toProj, coords, enforceAxis);\n      },\n      /**\n       * @template {TemplateCoordinates} T\n       * @param {T} coords\n       * @param {boolean=} enforceAxis\n       * @returns {T}\n       */\n      inverse: function (coords, enforceAxis) {\n        return transformer(toProj, fromProj, coords, enforceAxis);\n      }\n    };\n    if (single) {\n      obj.oProj = toProj;\n    }\n    return obj;\n  }\n}\n\nexport default proj4;\n", "\n\n\n/**\n * UTM zones are grouped, and assigned to one of a group of 6\n * sets.\n *\n * {int} @private\n */\nvar NUM_100K_SETS = 6;\n\n/**\n * The column letters (for easting) of the lower left value, per\n * set.\n *\n * {string} @private\n */\nvar SET_ORIGIN_COLUMN_LETTERS = 'AJSAJS';\n\n/**\n * The row letters (for northing) of the lower left value, per\n * set.\n *\n * {string} @private\n */\nvar SET_ORIGIN_ROW_LETTERS = 'AFAFAF';\n\nvar A = 65; // A\nvar I = 73; // I\nvar O = 79; // O\nvar V = 86; // V\nvar Z = 90; // Z\nexport default {\n  forward: forward,\n  inverse: inverse,\n  toPoint: toPoint\n};\n/**\n * Conversion of lat/lon to MGRS.\n *\n * @param {object} ll Object literal with lat and lon properties on a\n *     WGS84 ellipsoid.\n * @param {int} accuracy Accuracy in digits (5 for 1 m, 4 for 10 m, 3 for\n *      100 m, 2 for 1000 m or 1 for 10000 m). Optional, default is 5.\n * @return {string} the MGRS string for the given location and accuracy.\n */\nexport function forward(ll, accuracy) {\n  accuracy = accuracy || 5; // default accuracy 1m\n  return encode(LLtoUTM({\n    lat: ll[1],\n    lon: ll[0]\n  }), accuracy);\n};\n\n/**\n * Conversion of MGRS to lat/lon.\n *\n * @param {string} mgrs MGRS string.\n * @return {array} An array with left (longitude), bottom (latitude), right\n *     (longitude) and top (latitude) values in WGS84, representing the\n *     bounding box for the provided MGRS reference.\n */\nexport function inverse(mgrs) {\n  var bbox = UTMtoLL(decode(mgrs.toUpperCase()));\n  if (bbox.lat && bbox.lon) {\n    return [bbox.lon, bbox.lat, bbox.lon, bbox.lat];\n  }\n  return [bbox.left, bbox.bottom, bbox.right, bbox.top];\n};\n\nexport function toPoint(mgrs) {\n  var bbox = UTMtoLL(decode(mgrs.toUpperCase()));\n  if (bbox.lat && bbox.lon) {\n    return [bbox.lon, bbox.lat];\n  }\n  return [(bbox.left + bbox.right) / 2, (bbox.top + bbox.bottom) / 2];\n};\n/**\n * Conversion from degrees to radians.\n *\n * @private\n * @param {number} deg the angle in degrees.\n * @return {number} the angle in radians.\n */\nfunction degToRad(deg) {\n  return (deg * (Math.PI / 180.0));\n}\n\n/**\n * Conversion from radians to degrees.\n *\n * @private\n * @param {number} rad the angle in radians.\n * @return {number} the angle in degrees.\n */\nfunction radToDeg(rad) {\n  return (180.0 * (rad / Math.PI));\n}\n\n/**\n * Converts a set of Longitude and Latitude co-ordinates to UTM\n * using the WGS84 ellipsoid.\n *\n * @private\n * @param {object} ll Object literal with lat and lon properties\n *     representing the WGS84 coordinate to be converted.\n * @return {object} Object literal containing the UTM value with easting,\n *     northing, zoneNumber and zoneLetter properties, and an optional\n *     accuracy property in digits. Returns null if the conversion failed.\n */\nfunction LLtoUTM(ll) {\n  var Lat = ll.lat;\n  var Long = ll.lon;\n  var a = 6378137.0; //ellip.radius;\n  var eccSquared = 0.00669438; //ellip.eccsq;\n  var k0 = 0.9996;\n  var LongOrigin;\n  var eccPrimeSquared;\n  var N, T, C, A, M;\n  var LatRad = degToRad(Lat);\n  var LongRad = degToRad(Long);\n  var LongOriginRad;\n  var ZoneNumber;\n  // (int)\n  ZoneNumber = Math.floor((Long + 180) / 6) + 1;\n\n  //Make sure the longitude 180.00 is in Zone 60\n  if (Long === 180) {\n    ZoneNumber = 60;\n  }\n\n  // Special zone for Norway\n  if (Lat >= 56.0 && Lat < 64.0 && Long >= 3.0 && Long < 12.0) {\n    ZoneNumber = 32;\n  }\n\n  // Special zones for Svalbard\n  if (Lat >= 72.0 && Lat < 84.0) {\n    if (Long >= 0.0 && Long < 9.0) {\n      ZoneNumber = 31;\n    }\n    else if (Long >= 9.0 && Long < 21.0) {\n      ZoneNumber = 33;\n    }\n    else if (Long >= 21.0 && Long < 33.0) {\n      ZoneNumber = 35;\n    }\n    else if (Long >= 33.0 && Long < 42.0) {\n      ZoneNumber = 37;\n    }\n  }\n\n  LongOrigin = (ZoneNumber - 1) * 6 - 180 + 3; //+3 puts origin\n  // in middle of\n  // zone\n  LongOriginRad = degToRad(LongOrigin);\n\n  eccPrimeSquared = (eccSquared) / (1 - eccSquared);\n\n  N = a / Math.sqrt(1 - eccSquared * Math.sin(LatRad) * Math.sin(LatRad));\n  T = Math.tan(LatRad) * Math.tan(LatRad);\n  C = eccPrimeSquared * Math.cos(LatRad) * Math.cos(LatRad);\n  A = Math.cos(LatRad) * (LongRad - LongOriginRad);\n\n  M = a * ((1 - eccSquared / 4 - 3 * eccSquared * eccSquared / 64 - 5 * eccSquared * eccSquared * eccSquared / 256) * LatRad - (3 * eccSquared / 8 + 3 * eccSquared * eccSquared / 32 + 45 * eccSquared * eccSquared * eccSquared / 1024) * Math.sin(2 * LatRad) + (15 * eccSquared * eccSquared / 256 + 45 * eccSquared * eccSquared * eccSquared / 1024) * Math.sin(4 * LatRad) - (35 * eccSquared * eccSquared * eccSquared / 3072) * Math.sin(6 * LatRad));\n\n  var UTMEasting = (k0 * N * (A + (1 - T + C) * A * A * A / 6.0 + (5 - 18 * T + T * T + 72 * C - 58 * eccPrimeSquared) * A * A * A * A * A / 120.0) + 500000.0);\n\n  var UTMNorthing = (k0 * (M + N * Math.tan(LatRad) * (A * A / 2 + (5 - T + 9 * C + 4 * C * C) * A * A * A * A / 24.0 + (61 - 58 * T + T * T + 600 * C - 330 * eccPrimeSquared) * A * A * A * A * A * A / 720.0)));\n  if (Lat < 0.0) {\n    UTMNorthing += 10000000.0; //10000000 meter offset for\n    // southern hemisphere\n  }\n\n  return {\n    northing: Math.round(UTMNorthing),\n    easting: Math.round(UTMEasting),\n    zoneNumber: ZoneNumber,\n    zoneLetter: getLetterDesignator(Lat)\n  };\n}\n\n/**\n * Converts UTM coords to lat/long, using the WGS84 ellipsoid. This is a convenience\n * class where the Zone can be specified as a single string eg.\"60N\" which\n * is then broken down into the ZoneNumber and ZoneLetter.\n *\n * @private\n * @param {object} utm An object literal with northing, easting, zoneNumber\n *     and zoneLetter properties. If an optional accuracy property is\n *     provided (in meters), a bounding box will be returned instead of\n *     latitude and longitude.\n * @return {object} An object literal containing either lat and lon values\n *     (if no accuracy was provided), or top, right, bottom and left values\n *     for the bounding box calculated according to the provided accuracy.\n *     Returns null if the conversion failed.\n */\nfunction UTMtoLL(utm) {\n\n  var UTMNorthing = utm.northing;\n  var UTMEasting = utm.easting;\n  var zoneLetter = utm.zoneLetter;\n  var zoneNumber = utm.zoneNumber;\n  // check the ZoneNummber is valid\n  if (zoneNumber < 0 || zoneNumber > 60) {\n    return null;\n  }\n\n  var k0 = 0.9996;\n  var a = 6378137.0; //ellip.radius;\n  var eccSquared = 0.00669438; //ellip.eccsq;\n  var eccPrimeSquared;\n  var e1 = (1 - Math.sqrt(1 - eccSquared)) / (1 + Math.sqrt(1 - eccSquared));\n  var N1, T1, C1, R1, D, M;\n  var LongOrigin;\n  var mu, phi1Rad;\n\n  // remove 500,000 meter offset for longitude\n  var x = UTMEasting - 500000.0;\n  var y = UTMNorthing;\n\n  // We must know somehow if we are in the Northern or Southern\n  // hemisphere, this is the only time we use the letter So even\n  // if the Zone letter isn't exactly correct it should indicate\n  // the hemisphere correctly\n  if (zoneLetter < 'N') {\n    y -= 10000000.0; // remove 10,000,000 meter offset used\n    // for southern hemisphere\n  }\n\n  // There are 60 zones with zone 1 being at West -180 to -174\n  LongOrigin = (zoneNumber - 1) * 6 - 180 + 3; // +3 puts origin\n  // in middle of\n  // zone\n\n  eccPrimeSquared = (eccSquared) / (1 - eccSquared);\n\n  M = y / k0;\n  mu = M / (a * (1 - eccSquared / 4 - 3 * eccSquared * eccSquared / 64 - 5 * eccSquared * eccSquared * eccSquared / 256));\n\n  phi1Rad = mu + (3 * e1 / 2 - 27 * e1 * e1 * e1 / 32) * Math.sin(2 * mu) + (21 * e1 * e1 / 16 - 55 * e1 * e1 * e1 * e1 / 32) * Math.sin(4 * mu) + (151 * e1 * e1 * e1 / 96) * Math.sin(6 * mu);\n  // double phi1 = ProjMath.radToDeg(phi1Rad);\n\n  N1 = a / Math.sqrt(1 - eccSquared * Math.sin(phi1Rad) * Math.sin(phi1Rad));\n  T1 = Math.tan(phi1Rad) * Math.tan(phi1Rad);\n  C1 = eccPrimeSquared * Math.cos(phi1Rad) * Math.cos(phi1Rad);\n  R1 = a * (1 - eccSquared) / Math.pow(1 - eccSquared * Math.sin(phi1Rad) * Math.sin(phi1Rad), 1.5);\n  D = x / (N1 * k0);\n\n  var lat = phi1Rad - (N1 * Math.tan(phi1Rad) / R1) * (D * D / 2 - (5 + 3 * T1 + 10 * C1 - 4 * C1 * C1 - 9 * eccPrimeSquared) * D * D * D * D / 24 + (61 + 90 * T1 + 298 * C1 + 45 * T1 * T1 - 252 * eccPrimeSquared - 3 * C1 * C1) * D * D * D * D * D * D / 720);\n  lat = radToDeg(lat);\n\n  var lon = (D - (1 + 2 * T1 + C1) * D * D * D / 6 + (5 - 2 * C1 + 28 * T1 - 3 * C1 * C1 + 8 * eccPrimeSquared + 24 * T1 * T1) * D * D * D * D * D / 120) / Math.cos(phi1Rad);\n  lon = LongOrigin + radToDeg(lon);\n\n  var result;\n  if (utm.accuracy) {\n    var topRight = UTMtoLL({\n      northing: utm.northing + utm.accuracy,\n      easting: utm.easting + utm.accuracy,\n      zoneLetter: utm.zoneLetter,\n      zoneNumber: utm.zoneNumber\n    });\n    result = {\n      top: topRight.lat,\n      right: topRight.lon,\n      bottom: lat,\n      left: lon\n    };\n  }\n  else {\n    result = {\n      lat: lat,\n      lon: lon\n    };\n  }\n  return result;\n}\n\n/**\n * Calculates the MGRS letter designator for the given latitude.\n *\n * @private\n * @param {number} lat The latitude in WGS84 to get the letter designator\n *     for.\n * @return {char} The letter designator.\n */\nfunction getLetterDesignator(lat) {\n  //This is here as an error flag to show that the Latitude is\n  //outside MGRS limits\n  var LetterDesignator = 'Z';\n\n  if ((84 >= lat) && (lat >= 72)) {\n    LetterDesignator = 'X';\n  }\n  else if ((72 > lat) && (lat >= 64)) {\n    LetterDesignator = 'W';\n  }\n  else if ((64 > lat) && (lat >= 56)) {\n    LetterDesignator = 'V';\n  }\n  else if ((56 > lat) && (lat >= 48)) {\n    LetterDesignator = 'U';\n  }\n  else if ((48 > lat) && (lat >= 40)) {\n    LetterDesignator = 'T';\n  }\n  else if ((40 > lat) && (lat >= 32)) {\n    LetterDesignator = 'S';\n  }\n  else if ((32 > lat) && (lat >= 24)) {\n    LetterDesignator = 'R';\n  }\n  else if ((24 > lat) && (lat >= 16)) {\n    LetterDesignator = 'Q';\n  }\n  else if ((16 > lat) && (lat >= 8)) {\n    LetterDesignator = 'P';\n  }\n  else if ((8 > lat) && (lat >= 0)) {\n    LetterDesignator = 'N';\n  }\n  else if ((0 > lat) && (lat >= -8)) {\n    LetterDesignator = 'M';\n  }\n  else if ((-8 > lat) && (lat >= -16)) {\n    LetterDesignator = 'L';\n  }\n  else if ((-16 > lat) && (lat >= -24)) {\n    LetterDesignator = 'K';\n  }\n  else if ((-24 > lat) && (lat >= -32)) {\n    LetterDesignator = 'J';\n  }\n  else if ((-32 > lat) && (lat >= -40)) {\n    LetterDesignator = 'H';\n  }\n  else if ((-40 > lat) && (lat >= -48)) {\n    LetterDesignator = 'G';\n  }\n  else if ((-48 > lat) && (lat >= -56)) {\n    LetterDesignator = 'F';\n  }\n  else if ((-56 > lat) && (lat >= -64)) {\n    LetterDesignator = 'E';\n  }\n  else if ((-64 > lat) && (lat >= -72)) {\n    LetterDesignator = 'D';\n  }\n  else if ((-72 > lat) && (lat >= -80)) {\n    LetterDesignator = 'C';\n  }\n  return LetterDesignator;\n}\n\n/**\n * Encodes a UTM location as MGRS string.\n *\n * @private\n * @param {object} utm An object literal with easting, northing,\n *     zoneLetter, zoneNumber\n * @param {number} accuracy Accuracy in digits (1-5).\n * @return {string} MGRS string for the given UTM location.\n */\nfunction encode(utm, accuracy) {\n  // prepend with leading zeroes\n  var seasting = \"00000\" + utm.easting,\n    snorthing = \"00000\" + utm.northing;\n\n  return utm.zoneNumber + utm.zoneLetter + get100kID(utm.easting, utm.northing, utm.zoneNumber) + seasting.substr(seasting.length - 5, accuracy) + snorthing.substr(snorthing.length - 5, accuracy);\n}\n\n/**\n * Get the two letter 100k designator for a given UTM easting,\n * northing and zone number value.\n *\n * @private\n * @param {number} easting\n * @param {number} northing\n * @param {number} zoneNumber\n * @return the two letter 100k designator for the given UTM location.\n */\nfunction get100kID(easting, northing, zoneNumber) {\n  var setParm = get100kSetForZone(zoneNumber);\n  var setColumn = Math.floor(easting / 100000);\n  var setRow = Math.floor(northing / 100000) % 20;\n  return getLetter100kID(setColumn, setRow, setParm);\n}\n\n/**\n * Given a UTM zone number, figure out the MGRS 100K set it is in.\n *\n * @private\n * @param {number} i An UTM zone number.\n * @return {number} the 100k set the UTM zone is in.\n */\nfunction get100kSetForZone(i) {\n  var setParm = i % NUM_100K_SETS;\n  if (setParm === 0) {\n    setParm = NUM_100K_SETS;\n  }\n\n  return setParm;\n}\n\n/**\n * Get the two-letter MGRS 100k designator given information\n * translated from the UTM northing, easting and zone number.\n *\n * @private\n * @param {number} column the column index as it relates to the MGRS\n *        100k set spreadsheet, created from the UTM easting.\n *        Values are 1-8.\n * @param {number} row the row index as it relates to the MGRS 100k set\n *        spreadsheet, created from the UTM northing value. Values\n *        are from 0-19.\n * @param {number} parm the set block, as it relates to the MGRS 100k set\n *        spreadsheet, created from the UTM zone. Values are from\n *        1-60.\n * @return two letter MGRS 100k code.\n */\nfunction getLetter100kID(column, row, parm) {\n  // colOrigin and rowOrigin are the letters at the origin of the set\n  var index = parm - 1;\n  var colOrigin = SET_ORIGIN_COLUMN_LETTERS.charCodeAt(index);\n  var rowOrigin = SET_ORIGIN_ROW_LETTERS.charCodeAt(index);\n\n  // colInt and rowInt are the letters to build to return\n  var colInt = colOrigin + column - 1;\n  var rowInt = rowOrigin + row;\n  var rollover = false;\n\n  if (colInt > Z) {\n    colInt = colInt - Z + A - 1;\n    rollover = true;\n  }\n\n  if (colInt === I || (colOrigin < I && colInt > I) || ((colInt > I || colOrigin < I) && rollover)) {\n    colInt++;\n  }\n\n  if (colInt === O || (colOrigin < O && colInt > O) || ((colInt > O || colOrigin < O) && rollover)) {\n    colInt++;\n\n    if (colInt === I) {\n      colInt++;\n    }\n  }\n\n  if (colInt > Z) {\n    colInt = colInt - Z + A - 1;\n  }\n\n  if (rowInt > V) {\n    rowInt = rowInt - V + A - 1;\n    rollover = true;\n  }\n  else {\n    rollover = false;\n  }\n\n  if (((rowInt === I) || ((rowOrigin < I) && (rowInt > I))) || (((rowInt > I) || (rowOrigin < I)) && rollover)) {\n    rowInt++;\n  }\n\n  if (((rowInt === O) || ((rowOrigin < O) && (rowInt > O))) || (((rowInt > O) || (rowOrigin < O)) && rollover)) {\n    rowInt++;\n\n    if (rowInt === I) {\n      rowInt++;\n    }\n  }\n\n  if (rowInt > V) {\n    rowInt = rowInt - V + A - 1;\n  }\n\n  var twoLetter = String.fromCharCode(colInt) + String.fromCharCode(rowInt);\n  return twoLetter;\n}\n\n/**\n * Decode the UTM parameters from a MGRS string.\n *\n * @private\n * @param {string} mgrsString an UPPERCASE coordinate string is expected.\n * @return {object} An object literal with easting, northing, zoneLetter,\n *     zoneNumber and accuracy (in meters) properties.\n */\nfunction decode(mgrsString) {\n\n  if (mgrsString && mgrsString.length === 0) {\n    throw (\"MGRSPoint coverting from nothing\");\n  }\n\n  var length = mgrsString.length;\n\n  var hunK = null;\n  var sb = \"\";\n  var testChar;\n  var i = 0;\n\n  // get Zone number\n  while (!(/[A-Z]/).test(testChar = mgrsString.charAt(i))) {\n    if (i >= 2) {\n      throw (\"MGRSPoint bad conversion from: \" + mgrsString);\n    }\n    sb += testChar;\n    i++;\n  }\n\n  var zoneNumber = parseInt(sb, 10);\n\n  if (i === 0 || i + 3 > length) {\n    // A good MGRS string has to be 4-5 digits long,\n    // ##AAA/#AAA at least.\n    throw (\"MGRSPoint bad conversion from: \" + mgrsString);\n  }\n\n  var zoneLetter = mgrsString.charAt(i++);\n\n  // Should we check the zone letter here? Why not.\n  if (zoneLetter <= 'A' || zoneLetter === 'B' || zoneLetter === 'Y' || zoneLetter >= 'Z' || zoneLetter === 'I' || zoneLetter === 'O') {\n    throw (\"MGRSPoint zone letter \" + zoneLetter + \" not handled: \" + mgrsString);\n  }\n\n  hunK = mgrsString.substring(i, i += 2);\n\n  var set = get100kSetForZone(zoneNumber);\n\n  var east100k = getEastingFromChar(hunK.charAt(0), set);\n  var north100k = getNorthingFromChar(hunK.charAt(1), set);\n\n  // We have a bug where the northing may be 2000000 too low.\n  // How\n  // do we know when to roll over?\n\n  while (north100k < getMinNorthing(zoneLetter)) {\n    north100k += 2000000;\n  }\n\n  // calculate the char index for easting/northing separator\n  var remainder = length - i;\n\n  if (remainder % 2 !== 0) {\n    throw (\"MGRSPoint has to have an even number \\nof digits after the zone letter and two 100km letters - front \\nhalf for easting meters, second half for \\nnorthing meters\" + mgrsString);\n  }\n\n  var sep = remainder / 2;\n\n  var sepEasting = 0.0;\n  var sepNorthing = 0.0;\n  var accuracyBonus, sepEastingString, sepNorthingString, easting, northing;\n  if (sep > 0) {\n    accuracyBonus = 100000.0 / Math.pow(10, sep);\n    sepEastingString = mgrsString.substring(i, i + sep);\n    sepEasting = parseFloat(sepEastingString) * accuracyBonus;\n    sepNorthingString = mgrsString.substring(i + sep);\n    sepNorthing = parseFloat(sepNorthingString) * accuracyBonus;\n  }\n\n  easting = sepEasting + east100k;\n  northing = sepNorthing + north100k;\n\n  return {\n    easting: easting,\n    northing: northing,\n    zoneLetter: zoneLetter,\n    zoneNumber: zoneNumber,\n    accuracy: accuracyBonus\n  };\n}\n\n/**\n * Given the first letter from a two-letter MGRS 100k zone, and given the\n * MGRS table set for the zone number, figure out the easting value that\n * should be added to the other, secondary easting value.\n *\n * @private\n * @param {char} e The first letter from a two-letter MGRS 100´k zone.\n * @param {number} set The MGRS table set for the zone number.\n * @return {number} The easting value for the given letter and set.\n */\nfunction getEastingFromChar(e, set) {\n  // colOrigin is the letter at the origin of the set for the\n  // column\n  var curCol = SET_ORIGIN_COLUMN_LETTERS.charCodeAt(set - 1);\n  var eastingValue = 100000.0;\n  var rewindMarker = false;\n\n  while (curCol !== e.charCodeAt(0)) {\n    curCol++;\n    if (curCol === I) {\n      curCol++;\n    }\n    if (curCol === O) {\n      curCol++;\n    }\n    if (curCol > Z) {\n      if (rewindMarker) {\n        throw (\"Bad character: \" + e);\n      }\n      curCol = A;\n      rewindMarker = true;\n    }\n    eastingValue += 100000.0;\n  }\n\n  return eastingValue;\n}\n\n/**\n * Given the second letter from a two-letter MGRS 100k zone, and given the\n * MGRS table set for the zone number, figure out the northing value that\n * should be added to the other, secondary northing value. You have to\n * remember that Northings are determined from the equator, and the vertical\n * cycle of letters mean a 2000000 additional northing meters. This happens\n * approx. every 18 degrees of latitude. This method does *NOT* count any\n * additional northings. You have to figure out how many 2000000 meters need\n * to be added for the zone letter of the MGRS coordinate.\n *\n * @private\n * @param {char} n Second letter of the MGRS 100k zone\n * @param {number} set The MGRS table set number, which is dependent on the\n *     UTM zone number.\n * @return {number} The northing value for the given letter and set.\n */\nfunction getNorthingFromChar(n, set) {\n\n  if (n > 'V') {\n    throw (\"MGRSPoint given invalid Northing \" + n);\n  }\n\n  // rowOrigin is the letter at the origin of the set for the\n  // column\n  var curRow = SET_ORIGIN_ROW_LETTERS.charCodeAt(set - 1);\n  var northingValue = 0.0;\n  var rewindMarker = false;\n\n  while (curRow !== n.charCodeAt(0)) {\n    curRow++;\n    if (curRow === I) {\n      curRow++;\n    }\n    if (curRow === O) {\n      curRow++;\n    }\n    // fixing a bug making whole application hang in this loop\n    // when 'n' is a wrong character\n    if (curRow > V) {\n      if (rewindMarker) { // making sure that this loop ends\n        throw (\"Bad character: \" + n);\n      }\n      curRow = A;\n      rewindMarker = true;\n    }\n    northingValue += 100000.0;\n  }\n\n  return northingValue;\n}\n\n/**\n * The function getMinNorthing returns the minimum northing value of a MGRS\n * zone.\n *\n * Ported from Geotrans' c Lattitude_Band_Value structure table.\n *\n * @private\n * @param {char} zoneLetter The MGRS zone to get the min northing for.\n * @return {number}\n */\nfunction getMinNorthing(zoneLetter) {\n  var northing;\n  switch (zoneLetter) {\n  case 'C':\n    northing = 1100000.0;\n    break;\n  case 'D':\n    northing = 2000000.0;\n    break;\n  case 'E':\n    northing = 2800000.0;\n    break;\n  case 'F':\n    northing = 3700000.0;\n    break;\n  case 'G':\n    northing = 4600000.0;\n    break;\n  case 'H':\n    northing = 5500000.0;\n    break;\n  case 'J':\n    northing = 6400000.0;\n    break;\n  case 'K':\n    northing = 7300000.0;\n    break;\n  case 'L':\n    northing = 8200000.0;\n    break;\n  case 'M':\n    northing = 9100000.0;\n    break;\n  case 'N':\n    northing = 0.0;\n    break;\n  case 'P':\n    northing = 800000.0;\n    break;\n  case 'Q':\n    northing = 1700000.0;\n    break;\n  case 'R':\n    northing = 2600000.0;\n    break;\n  case 'S':\n    northing = 3500000.0;\n    break;\n  case 'T':\n    northing = 4400000.0;\n    break;\n  case 'U':\n    northing = 5300000.0;\n    break;\n  case 'V':\n    northing = 6200000.0;\n    break;\n  case 'W':\n    northing = 7000000.0;\n    break;\n  case 'X':\n    northing = 7900000.0;\n    break;\n  default:\n    northing = -1.0;\n  }\n  if (northing >= 0.0) {\n    return northing;\n  }\n  else {\n    throw (\"Invalid zone letter: \" + zoneLetter);\n  }\n\n}\n", "import { toPoint, forward } from 'mgrs';\n\n/**\n * @deprecated v3.0.0 - use proj4.toPoint instead\n * @param {number | import('./core').TemplateCoordinates | string} x\n * @param {number} [y]\n * @param {number} [z]\n */\nfunction Point(x, y, z) {\n  if (!(this instanceof Point)) {\n    return new Point(x, y, z);\n  }\n  if (Array.isArray(x)) {\n    this.x = x[0];\n    this.y = x[1];\n    this.z = x[2] || 0.0;\n  } else if (typeof x === 'object') {\n    this.x = x.x;\n    this.y = x.y;\n    this.z = x.z || 0.0;\n  } else if (typeof x === 'string' && typeof y === 'undefined') {\n    var coords = x.split(',');\n    this.x = parseFloat(coords[0]);\n    this.y = parseFloat(coords[1]);\n    this.z = parseFloat(coords[2]) || 0.0;\n  } else {\n    this.x = x;\n    this.y = y;\n    this.z = z || 0.0;\n  }\n  console.warn('proj4.Point will be removed in version 3, use proj4.toPoint');\n}\n\nPoint.fromMGRS = function (mgrsStr) {\n  return new Point(toPoint(mgrsStr));\n};\nPoint.prototype.toMGRS = function (accuracy) {\n  return forward([this.x, this.y], accuracy);\n};\nexport default Point;\n", "var C00 = 1;\nvar C02 = 0.25;\nvar C04 = 0.046875;\nvar C06 = 0.01953125;\nvar C08 = 0.01068115234375;\nvar C22 = 0.75;\nvar C44 = 0.46875;\nvar C46 = 0.01302083333333333333;\nvar C48 = 0.00712076822916666666;\nvar C66 = 0.36458333333333333333;\nvar C68 = 0.00569661458333333333;\nvar C88 = 0.3076171875;\n\nexport default function (es) {\n  var en = [];\n  en[0] = C00 - es * (C02 + es * (C04 + es * (C06 + es * C08)));\n  en[1] = es * (C22 - es * (C04 + es * (C06 + es * C08)));\n  var t = es * es;\n  en[2] = t * (C44 - es * (C46 + es * C48));\n  t *= es;\n  en[3] = t * (C66 - es * C68);\n  en[4] = t * es * C88;\n  return en;\n}\n", "export default function (phi, sphi, cphi, en) {\n  cphi *= sphi;\n  sphi *= sphi;\n  return (en[0] * phi - cphi * (en[1] + sphi * (en[2] + sphi * (en[3] + sphi * en[4]))));\n}\n", "import pj_mlfn from './pj_mlfn';\nimport { EPSLN } from '../constants/values';\n\nvar MAX_ITER = 20;\n\nexport default function (arg, es, en) {\n  var k = 1 / (1 - es);\n  var phi = arg;\n  for (var i = MAX_ITER; i; --i) { /* rarely goes over 2 iterations */\n    var s = Math.sin(phi);\n    var t = 1 - es * s * s;\n    // t = this.pj_mlfn(phi, s, Math.cos(phi), en) - arg;\n    // phi -= t * (t * Math.sqrt(t)) * k;\n    t = (pj_mlfn(phi, s, Math.cos(phi), en) - arg) * (t * Math.sqrt(t)) * k;\n    phi -= t;\n    if (Math.abs(t) < EPSLN) {\n      return phi;\n    }\n  }\n  // ..reportError(\"cass:pj_inv_mlfn: Convergence error\");\n  return phi;\n}\n", "// Heavily based on this tmerc projection implementation\n// https://github.com/mbloch/mapshaper-proj/blob/master/src/projections/tmerc.js\n\nimport pj_enfn from '../common/pj_enfn';\nimport pj_mlfn from '../common/pj_mlfn';\nimport pj_inv_mlfn from '../common/pj_inv_mlfn';\nimport adjust_lon from '../common/adjust_lon';\n\nimport { EPSLN, HALF_PI } from '../constants/values';\nimport sign from '../common/sign';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} es\n * @property {Array<number>} en\n * @property {number} ml0\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  this.x0 = this.x0 !== undefined ? this.x0 : 0;\n  this.y0 = this.y0 !== undefined ? this.y0 : 0;\n  this.long0 = this.long0 !== undefined ? this.long0 : 0;\n  this.lat0 = this.lat0 !== undefined ? this.lat0 : 0;\n\n  if (this.es) {\n    this.en = pj_enfn(this.es);\n    this.ml0 = pj_mlfn(this.lat0, Math.sin(this.lat0), Math.cos(this.lat0), this.en);\n  }\n}\n\n/**\n    Transverse Mercator Forward  - long/lat to x/y\n    long/lat in radians\n  */\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n\n  var delta_lon = adjust_lon(lon - this.long0);\n  var con;\n  var x, y;\n  var sin_phi = Math.sin(lat);\n  var cos_phi = Math.cos(lat);\n\n  if (!this.es) {\n    var b = cos_phi * Math.sin(delta_lon);\n\n    if ((Math.abs(Math.abs(b) - 1)) < EPSLN) {\n      return (93);\n    } else {\n      x = 0.5 * this.a * this.k0 * Math.log((1 + b) / (1 - b)) + this.x0;\n      y = cos_phi * Math.cos(delta_lon) / Math.sqrt(1 - Math.pow(b, 2));\n      b = Math.abs(y);\n\n      if (b >= 1) {\n        if ((b - 1) > EPSLN) {\n          return (93);\n        } else {\n          y = 0;\n        }\n      } else {\n        y = Math.acos(y);\n      }\n\n      if (lat < 0) {\n        y = -y;\n      }\n\n      y = this.a * this.k0 * (y - this.lat0) + this.y0;\n    }\n  } else {\n    var al = cos_phi * delta_lon;\n    var als = Math.pow(al, 2);\n    var c = this.ep2 * Math.pow(cos_phi, 2);\n    var cs = Math.pow(c, 2);\n    var tq = Math.abs(cos_phi) > EPSLN ? Math.tan(lat) : 0;\n    var t = Math.pow(tq, 2);\n    var ts = Math.pow(t, 2);\n    con = 1 - this.es * Math.pow(sin_phi, 2);\n    al = al / Math.sqrt(con);\n    var ml = pj_mlfn(lat, sin_phi, cos_phi, this.en);\n\n    x = this.a * (this.k0 * al * (1\n      + als / 6 * (1 - t + c\n        + als / 20 * (5 - 18 * t + ts + 14 * c - 58 * t * c\n          + als / 42 * (61 + 179 * ts - ts * t - 479 * t)))))\n        + this.x0;\n\n    y = this.a * (this.k0 * (ml - this.ml0\n      + sin_phi * delta_lon * al / 2 * (1\n        + als / 12 * (5 - t + 9 * c + 4 * cs\n          + als / 30 * (61 + ts - 58 * t + 270 * c - 330 * t * c\n            + als / 56 * (1385 + 543 * ts - ts * t - 3111 * t))))))\n          + this.y0;\n  }\n\n  p.x = x;\n  p.y = y;\n\n  return p;\n}\n\n/**\n    Transverse Mercator Inverse  -  x/y to long/lat\n  */\nexport function inverse(p) {\n  var con, phi;\n  var lat, lon;\n  var x = (p.x - this.x0) * (1 / this.a);\n  var y = (p.y - this.y0) * (1 / this.a);\n\n  if (!this.es) {\n    var f = Math.exp(x / this.k0);\n    var g = 0.5 * (f - 1 / f);\n    var temp = this.lat0 + y / this.k0;\n    var h = Math.cos(temp);\n    con = Math.sqrt((1 - Math.pow(h, 2)) / (1 + Math.pow(g, 2)));\n    lat = Math.asin(con);\n\n    if (y < 0) {\n      lat = -lat;\n    }\n\n    if ((g === 0) && (h === 0)) {\n      lon = 0;\n    } else {\n      lon = adjust_lon(Math.atan2(g, h) + this.long0);\n    }\n  } else { // ellipsoidal form\n    con = this.ml0 + y / this.k0;\n    phi = pj_inv_mlfn(con, this.es, this.en);\n\n    if (Math.abs(phi) < HALF_PI) {\n      var sin_phi = Math.sin(phi);\n      var cos_phi = Math.cos(phi);\n      var tan_phi = Math.abs(cos_phi) > EPSLN ? Math.tan(phi) : 0;\n      var c = this.ep2 * Math.pow(cos_phi, 2);\n      var cs = Math.pow(c, 2);\n      var t = Math.pow(tan_phi, 2);\n      var ts = Math.pow(t, 2);\n      con = 1 - this.es * Math.pow(sin_phi, 2);\n      var d = x * Math.sqrt(con) / this.k0;\n      var ds = Math.pow(d, 2);\n      con = con * tan_phi;\n\n      lat = phi - (con * ds / (1 - this.es)) * 0.5 * (1\n        - ds / 12 * (5 + 3 * t - 9 * c * t + c - 4 * cs\n          - ds / 30 * (61 + 90 * t - 252 * c * t + 45 * ts + 46 * c\n            - ds / 56 * (1385 + 3633 * t + 4095 * ts + 1574 * ts * t))));\n\n      lon = adjust_lon(this.long0 + (d * (1\n        - ds / 6 * (1 + 2 * t + c\n          - ds / 20 * (5 + 28 * t + 24 * ts + 8 * c * t + 6 * c\n            - ds / 42 * (61 + 662 * t + 1320 * ts + 720 * ts * t)))) / cos_phi));\n    } else {\n      lat = HALF_PI * sign(y);\n      lon = 0;\n    }\n  }\n\n  p.x = lon;\n  p.y = lat;\n\n  return p;\n}\n\nexport var names = ['Fast_Transverse_Mercator', 'Fast Transverse Mercator'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "export default function (x) {\n  var r = Math.exp(x);\n  r = (r - 1 / r) / 2;\n  return r;\n}\n", "export default function (x, y) {\n  x = Math.abs(x);\n  y = Math.abs(y);\n  var a = Math.max(x, y);\n  var b = Math.min(x, y) / (a ? a : 1);\n\n  return a * Math.sqrt(1 + Math.pow(b, 2));\n}\n", "export default function (x) {\n  var y = 1 + x;\n  var z = y - 1;\n\n  return z === 0 ? x : x * Math.log(y) / z;\n}\n", "import hypot from './hypot';\nimport log1py from './log1py';\n\nexport default function (x) {\n  var y = Math.abs(x);\n  y = log1py(y * (1 + y / (hypot(1, y) + 1)));\n\n  return x < 0 ? -y : y;\n}\n", "export default function (pp, B) {\n  var cos_2B = 2 * Math.cos(2 * B);\n  var i = pp.length - 1;\n  var h1 = pp[i];\n  var h2 = 0;\n  var h;\n\n  while (--i >= 0) {\n    h = -h2 + cos_2B * h1 + pp[i];\n    h2 = h1;\n    h1 = h;\n  }\n\n  return (B + h * Math.sin(2 * B));\n}\n", "export default function (pp, arg_r) {\n  var r = 2 * Math.cos(arg_r);\n  var i = pp.length - 1;\n  var hr1 = pp[i];\n  var hr2 = 0;\n  var hr;\n\n  while (--i >= 0) {\n    hr = -hr2 + r * hr1 + pp[i];\n    hr2 = hr1;\n    hr1 = hr;\n  }\n\n  return Math.sin(arg_r) * hr;\n}\n", "export default function (x) {\n  var r = Math.exp(x);\n  r = (r + 1 / r) / 2;\n  return r;\n}\n", "import sinh from './sinh';\nimport cosh from './cosh';\n\nexport default function (pp, arg_r, arg_i) {\n  var sin_arg_r = Math.sin(arg_r);\n  var cos_arg_r = Math.cos(arg_r);\n  var sinh_arg_i = sinh(arg_i);\n  var cosh_arg_i = cosh(arg_i);\n  var r = 2 * cos_arg_r * cosh_arg_i;\n  var i = -2 * sin_arg_r * sinh_arg_i;\n  var j = pp.length - 1;\n  var hr = pp[j];\n  var hi1 = 0;\n  var hr1 = 0;\n  var hi = 0;\n  var hr2;\n  var hi2;\n\n  while (--j >= 0) {\n    hr2 = hr1;\n    hi2 = hi1;\n    hr1 = hr;\n    hi1 = hi;\n    hr = -hr2 + r * hr1 - i * hi1 + pp[j];\n    hi = -hi2 + i * hr1 + r * hi1;\n  }\n\n  r = sin_arg_r * cosh_arg_i;\n  i = cos_arg_r * sinh_arg_i;\n\n  return [r * hr - i * hi, r * hi + i * hr];\n}\n", "// Heavily based on this etmerc projection implementation\n// https://github.com/mbloch/mapshaper-proj/blob/master/src/projections/etmerc.js\n\nimport tmerc from '../projections/tmerc';\nimport sinh from '../common/sinh';\nimport hypot from '../common/hypot';\nimport asinhy from '../common/asinhy';\nimport gatg from '../common/gatg';\nimport clens from '../common/clens';\nimport clens_cmplx from '../common/clens_cmplx';\nimport adjust_lon from '../common/adjust_lon';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} es\n * @property {Array<number>} cbg\n * @property {Array<number>} cgb\n * @property {Array<number>} utg\n * @property {Array<number>} gtu\n * @property {number} Qn\n * @property {number} Zb\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  if (!this.approx && (isNaN(this.es) || this.es <= 0)) {\n    throw new Error('Incorrect elliptical usage. Try using the +approx option in the proj string, or PROJECTION[\"Fast_Transverse_Mercator\"] in the WKT.');\n  }\n  if (this.approx) {\n    // When '+approx' is set, use tmerc instead\n    tmerc.init.apply(this);\n    this.forward = tmerc.forward;\n    this.inverse = tmerc.inverse;\n  }\n\n  this.x0 = this.x0 !== undefined ? this.x0 : 0;\n  this.y0 = this.y0 !== undefined ? this.y0 : 0;\n  this.long0 = this.long0 !== undefined ? this.long0 : 0;\n  this.lat0 = this.lat0 !== undefined ? this.lat0 : 0;\n\n  this.cgb = [];\n  this.cbg = [];\n  this.utg = [];\n  this.gtu = [];\n\n  var f = this.es / (1 + Math.sqrt(1 - this.es));\n  var n = f / (2 - f);\n  var np = n;\n\n  this.cgb[0] = n * (2 + n * (-2 / 3 + n * (-2 + n * (116 / 45 + n * (26 / 45 + n * (-2854 / 675))))));\n  this.cbg[0] = n * (-2 + n * (2 / 3 + n * (4 / 3 + n * (-82 / 45 + n * (32 / 45 + n * (4642 / 4725))))));\n\n  np = np * n;\n  this.cgb[1] = np * (7 / 3 + n * (-8 / 5 + n * (-227 / 45 + n * (2704 / 315 + n * (2323 / 945)))));\n  this.cbg[1] = np * (5 / 3 + n * (-16 / 15 + n * (-13 / 9 + n * (904 / 315 + n * (-1522 / 945)))));\n\n  np = np * n;\n  this.cgb[2] = np * (56 / 15 + n * (-136 / 35 + n * (-1262 / 105 + n * (73814 / 2835))));\n  this.cbg[2] = np * (-26 / 15 + n * (34 / 21 + n * (8 / 5 + n * (-12686 / 2835))));\n\n  np = np * n;\n  this.cgb[3] = np * (4279 / 630 + n * (-332 / 35 + n * (-399572 / 14175)));\n  this.cbg[3] = np * (1237 / 630 + n * (-12 / 5 + n * (-24832 / 14175)));\n\n  np = np * n;\n  this.cgb[4] = np * (4174 / 315 + n * (-144838 / 6237));\n  this.cbg[4] = np * (-734 / 315 + n * (109598 / 31185));\n\n  np = np * n;\n  this.cgb[5] = np * (601676 / 22275);\n  this.cbg[5] = np * (444337 / 155925);\n\n  np = Math.pow(n, 2);\n  this.Qn = this.k0 / (1 + n) * (1 + np * (1 / 4 + np * (1 / 64 + np / 256)));\n\n  this.utg[0] = n * (-0.5 + n * (2 / 3 + n * (-37 / 96 + n * (1 / 360 + n * (81 / 512 + n * (-96199 / 604800))))));\n  this.gtu[0] = n * (0.5 + n * (-2 / 3 + n * (5 / 16 + n * (41 / 180 + n * (-127 / 288 + n * (7891 / 37800))))));\n\n  this.utg[1] = np * (-1 / 48 + n * (-1 / 15 + n * (437 / 1440 + n * (-46 / 105 + n * (1118711 / 3870720)))));\n  this.gtu[1] = np * (13 / 48 + n * (-3 / 5 + n * (557 / 1440 + n * (281 / 630 + n * (-1983433 / 1935360)))));\n\n  np = np * n;\n  this.utg[2] = np * (-17 / 480 + n * (37 / 840 + n * (209 / 4480 + n * (-5569 / 90720))));\n  this.gtu[2] = np * (61 / 240 + n * (-103 / 140 + n * (15061 / 26880 + n * (167603 / 181440))));\n\n  np = np * n;\n  this.utg[3] = np * (-4397 / 161280 + n * (11 / 504 + n * (830251 / 7257600)));\n  this.gtu[3] = np * (49561 / 161280 + n * (-179 / 168 + n * (6601661 / 7257600)));\n\n  np = np * n;\n  this.utg[4] = np * (-4583 / 161280 + n * (108847 / 3991680));\n  this.gtu[4] = np * (34729 / 80640 + n * (-3418889 / 1995840));\n\n  np = np * n;\n  this.utg[5] = np * (-20648693 / 638668800);\n  this.gtu[5] = np * (212378941 / 319334400);\n\n  var Z = gatg(this.cbg, this.lat0);\n  this.Zb = -this.Qn * (Z + clens(this.gtu, 2 * Z));\n}\n\nexport function forward(p) {\n  var Ce = adjust_lon(p.x - this.long0);\n  var Cn = p.y;\n\n  Cn = gatg(this.cbg, Cn);\n  var sin_Cn = Math.sin(Cn);\n  var cos_Cn = Math.cos(Cn);\n  var sin_Ce = Math.sin(Ce);\n  var cos_Ce = Math.cos(Ce);\n\n  Cn = Math.atan2(sin_Cn, cos_Ce * cos_Cn);\n  Ce = Math.atan2(sin_Ce * cos_Cn, hypot(sin_Cn, cos_Cn * cos_Ce));\n  Ce = asinhy(Math.tan(Ce));\n\n  var tmp = clens_cmplx(this.gtu, 2 * Cn, 2 * Ce);\n\n  Cn = Cn + tmp[0];\n  Ce = Ce + tmp[1];\n\n  var x;\n  var y;\n\n  if (Math.abs(Ce) <= 2.623395162778) {\n    x = this.a * (this.Qn * Ce) + this.x0;\n    y = this.a * (this.Qn * Cn + this.Zb) + this.y0;\n  } else {\n    x = Infinity;\n    y = Infinity;\n  }\n\n  p.x = x;\n  p.y = y;\n\n  return p;\n}\n\nexport function inverse(p) {\n  var Ce = (p.x - this.x0) * (1 / this.a);\n  var Cn = (p.y - this.y0) * (1 / this.a);\n\n  Cn = (Cn - this.Zb) / this.Qn;\n  Ce = Ce / this.Qn;\n\n  var lon;\n  var lat;\n\n  if (Math.abs(Ce) <= 2.623395162778) {\n    var tmp = clens_cmplx(this.utg, 2 * Cn, 2 * Ce);\n\n    Cn = Cn + tmp[0];\n    Ce = Ce + tmp[1];\n    Ce = Math.atan(sinh(Ce));\n\n    var sin_Cn = Math.sin(Cn);\n    var cos_Cn = Math.cos(Cn);\n    var sin_Ce = Math.sin(Ce);\n    var cos_Ce = Math.cos(Ce);\n\n    Cn = Math.atan2(sin_Cn * cos_Ce, hypot(sin_Ce, cos_Ce * cos_Cn));\n    Ce = Math.atan2(sin_Ce, cos_Ce * cos_Cn);\n\n    lon = adjust_lon(Ce + this.long0);\n    lat = gatg(this.cgb, Cn);\n  } else {\n    lon = Infinity;\n    lat = Infinity;\n  }\n\n  p.x = lon;\n  p.y = lat;\n\n  return p;\n}\n\nexport var names = ['Extended_Transverse_Mercator', 'Extended Transverse Mercator', 'etmerc', 'Transverse_Mercator', 'Transverse Mercator', 'Gauss Kruger', 'Gauss_Kruger', 'tmerc'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import adjust_lon from './adjust_lon';\n\nexport default function (zone, lon) {\n  if (zone === undefined) {\n    zone = Math.floor((adjust_lon(lon) + Math.PI) * 30 / Math.PI) + 1;\n\n    if (zone < 0) {\n      return 0;\n    } else if (zone > 60) {\n      return 60;\n    }\n  }\n  return zone;\n}\n", "import adjust_zone from '../common/adjust_zone';\nimport etmerc from './etmerc';\nexport var dependsOn = 'etmerc';\nimport { D2R } from '../constants/values';\n\n/** @this {import('../defs.js').ProjectionDefinition} */\nexport function init() {\n  var zone = adjust_zone(this.zone, this.long0);\n  if (zone === undefined) {\n    throw new Error('unknown utm zone');\n  }\n  this.lat0 = 0;\n  this.long0 = ((6 * Math.abs(zone)) - 183) * D2R;\n  this.x0 = 500000;\n  this.y0 = this.utmSouth ? 10000000 : 0;\n  this.k0 = 0.9996;\n\n  etmerc.init.apply(this);\n  this.forward = etmerc.forward;\n  this.inverse = etmerc.inverse;\n}\n\nexport var names = ['Universal Transverse Mercator System', 'utm'];\nexport default {\n  init: init,\n  names: names,\n  dependsOn: dependsOn\n};\n", "export default function (esinp, exp) {\n  return (Math.pow((1 - esinp) / (1 + esinp), exp));\n}\n", "import srat from '../common/srat';\nvar MAX_ITER = 20;\nimport { HALF_PI, FORTPI } from '../constants/values';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} rc\n * @property {number} C\n * @property {number} phic0\n * @property {number} ratexp\n * @property {number} K\n * @property {number} e\n * @property {number} es\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  var sphi = Math.sin(this.lat0);\n  var cphi = Math.cos(this.lat0);\n  cphi *= cphi;\n  this.rc = Math.sqrt(1 - this.es) / (1 - this.es * sphi * sphi);\n  this.C = Math.sqrt(1 + this.es * cphi * cphi / (1 - this.es));\n  this.phic0 = Math.asin(sphi / this.C);\n  this.ratexp = 0.5 * this.C * this.e;\n  this.K = Math.tan(0.5 * this.phic0 + FORTPI) / (Math.pow(Math.tan(0.5 * this.lat0 + FORTPI), this.C) * srat(this.e * sphi, this.ratexp));\n}\n\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n\n  p.y = 2 * Math.atan(this.K * Math.pow(Math.tan(0.5 * lat + FORTPI), this.C) * srat(this.e * Math.sin(lat), this.ratexp)) - HALF_PI;\n  p.x = this.C * lon;\n  return p;\n}\n\nexport function inverse(p) {\n  var DEL_TOL = 1e-14;\n  var lon = p.x / this.C;\n  var lat = p.y;\n  var num = Math.pow(Math.tan(0.5 * lat + FORTPI) / this.K, 1 / this.C);\n  for (var i = MAX_ITER; i > 0; --i) {\n    lat = 2 * Math.atan(num * srat(this.e * Math.sin(p.y), -0.5 * this.e)) - HALF_PI;\n    if (Math.abs(lat - p.y) < DEL_TOL) {\n      break;\n    }\n    p.y = lat;\n  }\n  /* convergence failed */\n  if (!i) {\n    return null;\n  }\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = ['gauss'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import gauss from './gauss';\nimport adjust_lon from '../common/adjust_lon';\nimport hypot from '../common/hypot';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} sinc0\n * @property {number} cosc0\n * @property {number} R2\n * @property {number} rc\n * @property {number} phic0\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  gauss.init.apply(this);\n  if (!this.rc) {\n    return;\n  }\n  this.sinc0 = Math.sin(this.phic0);\n  this.cosc0 = Math.cos(this.phic0);\n  this.R2 = 2 * this.rc;\n  if (!this.title) {\n    this.title = 'Oblique Stereographic Alternative';\n  }\n}\n\nexport function forward(p) {\n  var sinc, cosc, cosl, k;\n  p.x = adjust_lon(p.x - this.long0);\n  gauss.forward.apply(this, [p]);\n  sinc = Math.sin(p.y);\n  cosc = Math.cos(p.y);\n  cosl = Math.cos(p.x);\n  k = this.k0 * this.R2 / (1 + this.sinc0 * sinc + this.cosc0 * cosc * cosl);\n  p.x = k * cosc * Math.sin(p.x);\n  p.y = k * (this.cosc0 * sinc - this.sinc0 * cosc * cosl);\n  p.x = this.a * p.x + this.x0;\n  p.y = this.a * p.y + this.y0;\n  return p;\n}\n\nexport function inverse(p) {\n  var sinc, cosc, lon, lat, rho;\n  p.x = (p.x - this.x0) / this.a;\n  p.y = (p.y - this.y0) / this.a;\n\n  p.x /= this.k0;\n  p.y /= this.k0;\n  if ((rho = hypot(p.x, p.y))) {\n    var c = 2 * Math.atan2(rho, this.R2);\n    sinc = Math.sin(c);\n    cosc = Math.cos(c);\n    lat = Math.asin(cosc * this.sinc0 + p.y * sinc * this.cosc0 / rho);\n    lon = Math.atan2(p.x * sinc, rho * this.cosc0 * cosc - p.y * this.sinc0 * sinc);\n  } else {\n    lat = this.phic0;\n    lon = 0;\n  }\n\n  p.x = lon;\n  p.y = lat;\n  gauss.inverse.apply(this, [p]);\n  p.x = adjust_lon(p.x + this.long0);\n  return p;\n}\n\nexport var names = ['Stereographic_North_Pole', 'Oblique_Stereographic', 'sterea', 'Oblique Stereographic Alternative', 'Double_Stereographic'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import { EPSLN, HALF_PI } from '../constants/values';\n\nimport sign from '../common/sign';\nimport msfnz from '../common/msfnz';\nimport tsfnz from '../common/tsfnz';\nimport phi2z from '../common/phi2z';\nimport adjust_lon from '../common/adjust_lon';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} coslat0\n * @property {number} sinlat0\n * @property {number} ms1\n * @property {number} X0\n * @property {number} cosX0\n * @property {number} sinX0\n * @property {number} con\n * @property {number} cons\n * @property {number} e\n */\n\nexport function ssfn_(phit, sinphi, eccen) {\n  sinphi *= eccen;\n  return (Math.tan(0.5 * (HALF_PI + phit)) * Math.pow((1 - sinphi) / (1 + sinphi), 0.5 * eccen));\n}\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  // setting default parameters\n  this.x0 = this.x0 || 0;\n  this.y0 = this.y0 || 0;\n  this.lat0 = this.lat0 || 0;\n  this.long0 = this.long0 || 0;\n\n  this.coslat0 = Math.cos(this.lat0);\n  this.sinlat0 = Math.sin(this.lat0);\n  if (this.sphere) {\n    if (this.k0 === 1 && !isNaN(this.lat_ts) && Math.abs(this.coslat0) <= EPSLN) {\n      this.k0 = 0.5 * (1 + sign(this.lat0) * Math.sin(this.lat_ts));\n    }\n  } else {\n    if (Math.abs(this.coslat0) <= EPSLN) {\n      if (this.lat0 > 0) {\n        // North pole\n        // trace('stere:north pole');\n        this.con = 1;\n      } else {\n        // South pole\n        // trace('stere:south pole');\n        this.con = -1;\n      }\n    }\n    this.cons = Math.sqrt(Math.pow(1 + this.e, 1 + this.e) * Math.pow(1 - this.e, 1 - this.e));\n    if (this.k0 === 1 && !isNaN(this.lat_ts) && Math.abs(this.coslat0) <= EPSLN && Math.abs(Math.cos(this.lat_ts)) > EPSLN) {\n      // When k0 is 1 (default value) and lat_ts is a vaild number and lat0 is at a pole and lat_ts is not at a pole\n      // Recalculate k0 using formula 21-35 from p161 of Snyder, 1987\n      this.k0 = 0.5 * this.cons * msfnz(this.e, Math.sin(this.lat_ts), Math.cos(this.lat_ts)) / tsfnz(this.e, this.con * this.lat_ts, this.con * Math.sin(this.lat_ts));\n    }\n    this.ms1 = msfnz(this.e, this.sinlat0, this.coslat0);\n    this.X0 = 2 * Math.atan(ssfn_(this.lat0, this.sinlat0, this.e)) - HALF_PI;\n    this.cosX0 = Math.cos(this.X0);\n    this.sinX0 = Math.sin(this.X0);\n  }\n}\n\n// Stereographic forward equations--mapping lat,long to x,y\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n  var sinlat = Math.sin(lat);\n  var coslat = Math.cos(lat);\n  var A, X, sinX, cosX, ts, rh;\n  var dlon = adjust_lon(lon - this.long0);\n\n  if (Math.abs(Math.abs(lon - this.long0) - Math.PI) <= EPSLN && Math.abs(lat + this.lat0) <= EPSLN) {\n    // case of the origine point\n    // trace('stere:this is the origin point');\n    p.x = NaN;\n    p.y = NaN;\n    return p;\n  }\n  if (this.sphere) {\n    // trace('stere:sphere case');\n    A = 2 * this.k0 / (1 + this.sinlat0 * sinlat + this.coslat0 * coslat * Math.cos(dlon));\n    p.x = this.a * A * coslat * Math.sin(dlon) + this.x0;\n    p.y = this.a * A * (this.coslat0 * sinlat - this.sinlat0 * coslat * Math.cos(dlon)) + this.y0;\n    return p;\n  } else {\n    X = 2 * Math.atan(ssfn_(lat, sinlat, this.e)) - HALF_PI;\n    cosX = Math.cos(X);\n    sinX = Math.sin(X);\n    if (Math.abs(this.coslat0) <= EPSLN) {\n      ts = tsfnz(this.e, lat * this.con, this.con * sinlat);\n      rh = 2 * this.a * this.k0 * ts / this.cons;\n      p.x = this.x0 + rh * Math.sin(lon - this.long0);\n      p.y = this.y0 - this.con * rh * Math.cos(lon - this.long0);\n      // trace(p.toString());\n      return p;\n    } else if (Math.abs(this.sinlat0) < EPSLN) {\n      // Eq\n      // trace('stere:equateur');\n      A = 2 * this.a * this.k0 / (1 + cosX * Math.cos(dlon));\n      p.y = A * sinX;\n    } else {\n      // other case\n      // trace('stere:normal case');\n      A = 2 * this.a * this.k0 * this.ms1 / (this.cosX0 * (1 + this.sinX0 * sinX + this.cosX0 * cosX * Math.cos(dlon)));\n      p.y = A * (this.cosX0 * sinX - this.sinX0 * cosX * Math.cos(dlon)) + this.y0;\n    }\n    p.x = A * cosX * Math.sin(dlon) + this.x0;\n  }\n  // trace(p.toString());\n  return p;\n}\n\n//* Stereographic inverse equations--mapping x,y to lat/long\nexport function inverse(p) {\n  p.x -= this.x0;\n  p.y -= this.y0;\n  var lon, lat, ts, ce, Chi;\n  var rh = Math.sqrt(p.x * p.x + p.y * p.y);\n  if (this.sphere) {\n    var c = 2 * Math.atan(rh / (2 * this.a * this.k0));\n    lon = this.long0;\n    lat = this.lat0;\n    if (rh <= EPSLN) {\n      p.x = lon;\n      p.y = lat;\n      return p;\n    }\n    lat = Math.asin(Math.cos(c) * this.sinlat0 + p.y * Math.sin(c) * this.coslat0 / rh);\n    if (Math.abs(this.coslat0) < EPSLN) {\n      if (this.lat0 > 0) {\n        lon = adjust_lon(this.long0 + Math.atan2(p.x, -1 * p.y));\n      } else {\n        lon = adjust_lon(this.long0 + Math.atan2(p.x, p.y));\n      }\n    } else {\n      lon = adjust_lon(this.long0 + Math.atan2(p.x * Math.sin(c), rh * this.coslat0 * Math.cos(c) - p.y * this.sinlat0 * Math.sin(c)));\n    }\n    p.x = lon;\n    p.y = lat;\n    return p;\n  } else {\n    if (Math.abs(this.coslat0) <= EPSLN) {\n      if (rh <= EPSLN) {\n        lat = this.lat0;\n        lon = this.long0;\n        p.x = lon;\n        p.y = lat;\n        // trace(p.toString());\n        return p;\n      }\n      p.x *= this.con;\n      p.y *= this.con;\n      ts = rh * this.cons / (2 * this.a * this.k0);\n      lat = this.con * phi2z(this.e, ts);\n      lon = this.con * adjust_lon(this.con * this.long0 + Math.atan2(p.x, -1 * p.y));\n    } else {\n      ce = 2 * Math.atan(rh * this.cosX0 / (2 * this.a * this.k0 * this.ms1));\n      lon = this.long0;\n      if (rh <= EPSLN) {\n        Chi = this.X0;\n      } else {\n        Chi = Math.asin(Math.cos(ce) * this.sinX0 + p.y * Math.sin(ce) * this.cosX0 / rh);\n        lon = adjust_lon(this.long0 + Math.atan2(p.x * Math.sin(ce), rh * this.cosX0 * Math.cos(ce) - p.y * this.sinX0 * Math.sin(ce)));\n      }\n      lat = -1 * phi2z(this.e, Math.tan(0.5 * (HALF_PI + Chi)));\n    }\n  }\n  p.x = lon;\n  p.y = lat;\n\n  // trace(p.toString());\n  return p;\n}\n\nexport var names = ['stere', 'Stereographic_South_Pole', 'Polar_Stereographic_variant_A', 'Polar_Stereographic_variant_B', 'Polar_Stereographic'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names,\n  ssfn_: ssfn_\n};\n", "/*\n  references:\n    Formules et constantes pour le Calcul pour la\n    projection cylindrique conforme à axe oblique et pour la transformation entre\n    des systèmes de référence.\n    http://www.swisstopo.admin.ch/internet/swisstopo/fr/home/<USER>/survey/sys/refsys/switzerland.parsysrelated1.31216.downloadList.77004.DownloadFile.tmp/swissprojectionfr.pdf\n  */\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} lambda0\n * @property {number} e\n * @property {number} R\n * @property {number} b0\n * @property {number} K\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  var phy0 = this.lat0;\n  this.lambda0 = this.long0;\n  var sinPhy0 = Math.sin(phy0);\n  var semiMajorAxis = this.a;\n  var invF = this.rf;\n  var flattening = 1 / invF;\n  var e2 = 2 * flattening - Math.pow(flattening, 2);\n  var e = this.e = Math.sqrt(e2);\n  this.R = this.k0 * semiMajorAxis * Math.sqrt(1 - e2) / (1 - e2 * Math.pow(sinPhy0, 2));\n  this.alpha = Math.sqrt(1 + e2 / (1 - e2) * Math.pow(Math.cos(phy0), 4));\n  this.b0 = Math.asin(sinPhy0 / this.alpha);\n  var k1 = Math.log(Math.tan(Math.PI / 4 + this.b0 / 2));\n  var k2 = Math.log(Math.tan(Math.PI / 4 + phy0 / 2));\n  var k3 = Math.log((1 + e * sinPhy0) / (1 - e * sinPhy0));\n  this.K = k1 - this.alpha * k2 + this.alpha * e / 2 * k3;\n}\n\nexport function forward(p) {\n  var Sa1 = Math.log(Math.tan(Math.PI / 4 - p.y / 2));\n  var Sa2 = this.e / 2 * Math.log((1 + this.e * Math.sin(p.y)) / (1 - this.e * Math.sin(p.y)));\n  var S = -this.alpha * (Sa1 + Sa2) + this.K;\n\n  // spheric latitude\n  var b = 2 * (Math.atan(Math.exp(S)) - Math.PI / 4);\n\n  // spheric longitude\n  var I = this.alpha * (p.x - this.lambda0);\n\n  // psoeudo equatorial rotation\n  var rotI = Math.atan(Math.sin(I) / (Math.sin(this.b0) * Math.tan(b) + Math.cos(this.b0) * Math.cos(I)));\n\n  var rotB = Math.asin(Math.cos(this.b0) * Math.sin(b) - Math.sin(this.b0) * Math.cos(b) * Math.cos(I));\n\n  p.y = this.R / 2 * Math.log((1 + Math.sin(rotB)) / (1 - Math.sin(rotB))) + this.y0;\n  p.x = this.R * rotI + this.x0;\n  return p;\n}\n\nexport function inverse(p) {\n  var Y = p.x - this.x0;\n  var X = p.y - this.y0;\n\n  var rotI = Y / this.R;\n  var rotB = 2 * (Math.atan(Math.exp(X / this.R)) - Math.PI / 4);\n\n  var b = Math.asin(Math.cos(this.b0) * Math.sin(rotB) + Math.sin(this.b0) * Math.cos(rotB) * Math.cos(rotI));\n  var I = Math.atan(Math.sin(rotI) / (Math.cos(this.b0) * Math.cos(rotI) - Math.sin(this.b0) * Math.tan(rotB)));\n\n  var lambda = this.lambda0 + I / this.alpha;\n\n  var S = 0;\n  var phy = b;\n  var prevPhy = -1000;\n  var iteration = 0;\n  while (Math.abs(phy - prevPhy) > 0.0000001) {\n    if (++iteration > 20) {\n      // ...reportError(\"omercFwdInfinity\");\n      return;\n    }\n    // S = Math.log(Math.tan(Math.PI / 4 + phy / 2));\n    S = 1 / this.alpha * (Math.log(Math.tan(Math.PI / 4 + b / 2)) - this.K) + this.e * Math.log(Math.tan(Math.PI / 4 + Math.asin(this.e * Math.sin(phy)) / 2));\n    prevPhy = phy;\n    phy = 2 * Math.atan(Math.exp(S)) - Math.PI / 2;\n  }\n\n  p.x = lambda;\n  p.y = phy;\n  return p;\n}\n\nexport var names = ['somerc'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import tsfnz from '../common/tsfnz';\nimport adjust_lon from '../common/adjust_lon';\nimport phi2z from '../common/phi2z';\nimport { EPSLN, HALF_PI, TWO_PI, FORTPI } from '../constants/values';\nimport { getNormalizedProjName } from '../projections';\n\n/**\n * @typedef {Object} LocalThis\n * @property {boolean} no_off\n * @property {boolean} no_rot\n * @property {number} rectified_grid_angle\n * @property {number} es\n * @property {number} A\n * @property {number} B\n * @property {number} E\n * @property {number} e\n * @property {number} lam0\n * @property {number} singam\n * @property {number} cosgam\n * @property {number} sinrot\n * @property {number} cosrot\n * @property {number} rB\n * @property {number} ArB\n * @property {number} BrA\n * @property {number} u_0\n * @property {number} v_pole_n\n * @property {number} v_pole_s\n */\n\nvar TOL = 1e-7;\n\nfunction isTypeA(P) {\n  var typeAProjections = ['Hotine_Oblique_Mercator', 'Hotine_Oblique_Mercator_variant_A', 'Hotine_Oblique_Mercator_Azimuth_Natural_Origin'];\n  var projectionName = typeof P.projName === 'object' ? Object.keys(P.projName)[0] : P.projName;\n\n  return 'no_uoff' in P || 'no_off' in P || typeAProjections.indexOf(projectionName) !== -1 || typeAProjections.indexOf(getNormalizedProjName(projectionName)) !== -1;\n}\n\n/**\n * Initialize the Oblique Mercator  projection\n * @this {import('../defs.js').ProjectionDefinition & LocalThis}\n */\nexport function init() {\n  var con, com, cosph0, D, F, H, L, sinph0, p, J, gamma = 0,\n    gamma0, lamc = 0, lam1 = 0, lam2 = 0, phi1 = 0, phi2 = 0, alpha_c = 0;\n\n  // only Type A uses the no_off or no_uoff property\n  // https://github.com/OSGeo/proj.4/issues/104\n  this.no_off = isTypeA(this);\n  this.no_rot = 'no_rot' in this;\n\n  var alp = false;\n  if ('alpha' in this) {\n    alp = true;\n  }\n\n  var gam = false;\n  if ('rectified_grid_angle' in this) {\n    gam = true;\n  }\n\n  if (alp) {\n    alpha_c = this.alpha;\n  }\n\n  if (gam) {\n    gamma = this.rectified_grid_angle;\n  }\n\n  if (alp || gam) {\n    lamc = this.longc;\n  } else {\n    lam1 = this.long1;\n    phi1 = this.lat1;\n    lam2 = this.long2;\n    phi2 = this.lat2;\n\n    if (Math.abs(phi1 - phi2) <= TOL || (con = Math.abs(phi1)) <= TOL\n      || Math.abs(con - HALF_PI) <= TOL || Math.abs(Math.abs(this.lat0) - HALF_PI) <= TOL\n      || Math.abs(Math.abs(phi2) - HALF_PI) <= TOL) {\n      throw new Error();\n    }\n  }\n\n  var one_es = 1.0 - this.es;\n  com = Math.sqrt(one_es);\n\n  if (Math.abs(this.lat0) > EPSLN) {\n    sinph0 = Math.sin(this.lat0);\n    cosph0 = Math.cos(this.lat0);\n    con = 1 - this.es * sinph0 * sinph0;\n    this.B = cosph0 * cosph0;\n    this.B = Math.sqrt(1 + this.es * this.B * this.B / one_es);\n    this.A = this.B * this.k0 * com / con;\n    D = this.B * com / (cosph0 * Math.sqrt(con));\n    F = D * D - 1;\n\n    if (F <= 0) {\n      F = 0;\n    } else {\n      F = Math.sqrt(F);\n      if (this.lat0 < 0) {\n        F = -F;\n      }\n    }\n\n    this.E = F += D;\n    this.E *= Math.pow(tsfnz(this.e, this.lat0, sinph0), this.B);\n  } else {\n    this.B = 1 / com;\n    this.A = this.k0;\n    this.E = D = F = 1;\n  }\n\n  if (alp || gam) {\n    if (alp) {\n      gamma0 = Math.asin(Math.sin(alpha_c) / D);\n      if (!gam) {\n        gamma = alpha_c;\n      }\n    } else {\n      gamma0 = gamma;\n      alpha_c = Math.asin(D * Math.sin(gamma0));\n    }\n    this.lam0 = lamc - Math.asin(0.5 * (F - 1 / F) * Math.tan(gamma0)) / this.B;\n  } else {\n    H = Math.pow(tsfnz(this.e, phi1, Math.sin(phi1)), this.B);\n    L = Math.pow(tsfnz(this.e, phi2, Math.sin(phi2)), this.B);\n    F = this.E / H;\n    p = (L - H) / (L + H);\n    J = this.E * this.E;\n    J = (J - L * H) / (J + L * H);\n    con = lam1 - lam2;\n\n    if (con < -Math.PI) {\n      lam2 -= TWO_PI;\n    } else if (con > Math.PI) {\n      lam2 += TWO_PI;\n    }\n\n    this.lam0 = adjust_lon(0.5 * (lam1 + lam2) - Math.atan(J * Math.tan(0.5 * this.B * (lam1 - lam2)) / p) / this.B);\n    gamma0 = Math.atan(2 * Math.sin(this.B * adjust_lon(lam1 - this.lam0)) / (F - 1 / F));\n    gamma = alpha_c = Math.asin(D * Math.sin(gamma0));\n  }\n\n  this.singam = Math.sin(gamma0);\n  this.cosgam = Math.cos(gamma0);\n  this.sinrot = Math.sin(gamma);\n  this.cosrot = Math.cos(gamma);\n\n  this.rB = 1 / this.B;\n  this.ArB = this.A * this.rB;\n  this.BrA = 1 / this.ArB;\n\n  if (this.no_off) {\n    this.u_0 = 0;\n  } else {\n    this.u_0 = Math.abs(this.ArB * Math.atan(Math.sqrt(D * D - 1) / Math.cos(alpha_c)));\n\n    if (this.lat0 < 0) {\n      this.u_0 = -this.u_0;\n    }\n  }\n\n  F = 0.5 * gamma0;\n  this.v_pole_n = this.ArB * Math.log(Math.tan(FORTPI - F));\n  this.v_pole_s = this.ArB * Math.log(Math.tan(FORTPI + F));\n}\n\n/* Oblique Mercator forward equations--mapping lat,long to x,y\n    ---------------------------------------------------------- */\nexport function forward(p) {\n  var coords = {};\n  var S, T, U, V, W, temp, u, v;\n  p.x = p.x - this.lam0;\n\n  if (Math.abs(Math.abs(p.y) - HALF_PI) > EPSLN) {\n    W = this.E / Math.pow(tsfnz(this.e, p.y, Math.sin(p.y)), this.B);\n\n    temp = 1 / W;\n    S = 0.5 * (W - temp);\n    T = 0.5 * (W + temp);\n    V = Math.sin(this.B * p.x);\n    U = (S * this.singam - V * this.cosgam) / T;\n\n    if (Math.abs(Math.abs(U) - 1.0) < EPSLN) {\n      throw new Error();\n    }\n\n    v = 0.5 * this.ArB * Math.log((1 - U) / (1 + U));\n    temp = Math.cos(this.B * p.x);\n\n    if (Math.abs(temp) < TOL) {\n      u = this.A * p.x;\n    } else {\n      u = this.ArB * Math.atan2((S * this.cosgam + V * this.singam), temp);\n    }\n  } else {\n    v = p.y > 0 ? this.v_pole_n : this.v_pole_s;\n    u = this.ArB * p.y;\n  }\n\n  if (this.no_rot) {\n    coords.x = u;\n    coords.y = v;\n  } else {\n    u -= this.u_0;\n    coords.x = v * this.cosrot + u * this.sinrot;\n    coords.y = u * this.cosrot - v * this.sinrot;\n  }\n\n  coords.x = (this.a * coords.x + this.x0);\n  coords.y = (this.a * coords.y + this.y0);\n\n  return coords;\n}\n\nexport function inverse(p) {\n  var u, v, Qp, Sp, Tp, Vp, Up;\n  var coords = {};\n\n  p.x = (p.x - this.x0) * (1.0 / this.a);\n  p.y = (p.y - this.y0) * (1.0 / this.a);\n\n  if (this.no_rot) {\n    v = p.y;\n    u = p.x;\n  } else {\n    v = p.x * this.cosrot - p.y * this.sinrot;\n    u = p.y * this.cosrot + p.x * this.sinrot + this.u_0;\n  }\n\n  Qp = Math.exp(-this.BrA * v);\n  Sp = 0.5 * (Qp - 1 / Qp);\n  Tp = 0.5 * (Qp + 1 / Qp);\n  Vp = Math.sin(this.BrA * u);\n  Up = (Vp * this.cosgam + Sp * this.singam) / Tp;\n\n  if (Math.abs(Math.abs(Up) - 1) < EPSLN) {\n    coords.x = 0;\n    coords.y = Up < 0 ? -HALF_PI : HALF_PI;\n  } else {\n    coords.y = this.E / Math.sqrt((1 + Up) / (1 - Up));\n    coords.y = phi2z(this.e, Math.pow(coords.y, 1 / this.B));\n\n    if (coords.y === Infinity) {\n      throw new Error();\n    }\n\n    coords.x = -this.rB * Math.atan2((Sp * this.cosgam - Vp * this.singam), Math.cos(this.BrA * u));\n  }\n\n  coords.x += this.lam0;\n\n  return coords;\n}\n\nexport var names = ['Hotine_Oblique_Mercator', 'Hotine Oblique Mercator', 'Hotine_Oblique_Mercator_variant_A', 'Hotine_Oblique_Mercator_Variant_B', 'Hotine_Oblique_Mercator_Azimuth_Natural_Origin', 'Hotine_Oblique_Mercator_Two_Point_Natural_Origin', 'Hotine_Oblique_Mercator_Azimuth_Center', 'Oblique_Mercator', 'omerc'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import msfnz from '../common/msfnz';\nimport tsfnz from '../common/tsfnz';\nimport sign from '../common/sign';\nimport adjust_lon from '../common/adjust_lon';\nimport phi2z from '../common/phi2z';\nimport { HALF_PI, EPSLN } from '../constants/values';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} e\n * @property {number} ns\n * @property {number} f0\n * @property {number} rh\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  // double lat0;                    /* the reference latitude               */\n  // double long0;                   /* the reference longitude              */\n  // double lat1;                    /* first standard parallel              */\n  // double lat2;                    /* second standard parallel             */\n  // double r_maj;                   /* major axis                           */\n  // double r_min;                   /* minor axis                           */\n  // double false_east;              /* x offset in meters                   */\n  // double false_north;             /* y offset in meters                   */\n\n  // the above value can be set with proj4.defs\n  // example: proj4.defs(\"EPSG:2154\",\"+proj=lcc +lat_1=49 +lat_2=44 +lat_0=46.5 +lon_0=3 +x_0=700000 +y_0=6600000 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs\");\n\n  if (!this.lat2) {\n    this.lat2 = this.lat1;\n  } // if lat2 is not defined\n  if (!this.k0) {\n    this.k0 = 1;\n  }\n  this.x0 = this.x0 || 0;\n  this.y0 = this.y0 || 0;\n  // Standard Parallels cannot be equal and on opposite sides of the equator\n  if (Math.abs(this.lat1 + this.lat2) < EPSLN) {\n    return;\n  }\n\n  var temp = this.b / this.a;\n  this.e = Math.sqrt(1 - temp * temp);\n\n  var sin1 = Math.sin(this.lat1);\n  var cos1 = Math.cos(this.lat1);\n  var ms1 = msfnz(this.e, sin1, cos1);\n  var ts1 = tsfnz(this.e, this.lat1, sin1);\n\n  var sin2 = Math.sin(this.lat2);\n  var cos2 = Math.cos(this.lat2);\n  var ms2 = msfnz(this.e, sin2, cos2);\n  var ts2 = tsfnz(this.e, this.lat2, sin2);\n\n  var ts0 = Math.abs(Math.abs(this.lat0) - HALF_PI) < EPSLN\n    ? 0 // Handle poles by setting ts0 to 0\n    : tsfnz(this.e, this.lat0, Math.sin(this.lat0));\n\n  if (Math.abs(this.lat1 - this.lat2) > EPSLN) {\n    this.ns = Math.log(ms1 / ms2) / Math.log(ts1 / ts2);\n  } else {\n    this.ns = sin1;\n  }\n  if (isNaN(this.ns)) {\n    this.ns = sin1;\n  }\n  this.f0 = ms1 / (this.ns * Math.pow(ts1, this.ns));\n  this.rh = this.a * this.f0 * Math.pow(ts0, this.ns);\n  if (!this.title) {\n    this.title = 'Lambert Conformal Conic';\n  }\n}\n\n// Lambert Conformal conic forward equations--mapping lat,long to x,y\n// -----------------------------------------------------------------\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n\n  // singular cases :\n  if (Math.abs(2 * Math.abs(lat) - Math.PI) <= EPSLN) {\n    lat = sign(lat) * (HALF_PI - 2 * EPSLN);\n  }\n\n  var con = Math.abs(Math.abs(lat) - HALF_PI);\n  var ts, rh1;\n  if (con > EPSLN) {\n    ts = tsfnz(this.e, lat, Math.sin(lat));\n    rh1 = this.a * this.f0 * Math.pow(ts, this.ns);\n  } else {\n    con = lat * this.ns;\n    if (con <= 0) {\n      return null;\n    }\n    rh1 = 0;\n  }\n  var theta = this.ns * adjust_lon(lon - this.long0);\n  p.x = this.k0 * (rh1 * Math.sin(theta)) + this.x0;\n  p.y = this.k0 * (this.rh - rh1 * Math.cos(theta)) + this.y0;\n\n  return p;\n}\n\n// Lambert Conformal Conic inverse equations--mapping x,y to lat/long\n// -----------------------------------------------------------------\nexport function inverse(p) {\n  var rh1, con, ts;\n  var lat, lon;\n  var x = (p.x - this.x0) / this.k0;\n  var y = (this.rh - (p.y - this.y0) / this.k0);\n  if (this.ns > 0) {\n    rh1 = Math.sqrt(x * x + y * y);\n    con = 1;\n  } else {\n    rh1 = -Math.sqrt(x * x + y * y);\n    con = -1;\n  }\n  var theta = 0;\n  if (rh1 !== 0) {\n    theta = Math.atan2((con * x), (con * y));\n  }\n  if ((rh1 !== 0) || (this.ns > 0)) {\n    con = 1 / this.ns;\n    ts = Math.pow((rh1 / (this.a * this.f0)), con);\n    lat = phi2z(this.e, ts);\n    if (lat === -9999) {\n      return null;\n    }\n  } else {\n    lat = -HALF_PI;\n  }\n  lon = adjust_lon(theta / this.ns + this.long0);\n\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = [\n  'Lambert Tangential Conformal Conic Projection',\n  'Lambert_Conformal_Conic',\n  'Lambert_Conformal_Conic_1SP',\n  'Lambert_Conformal_Conic_2SP',\n  'lcc',\n  'Lambert Conic Conformal (1SP)',\n  'Lambert Conic Conformal (2SP)'\n];\n\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import adjust_lon from '../common/adjust_lon';\n\nexport function init() {\n  this.a = 6377397.155;\n  this.es = 0.006674372230614;\n  this.e = Math.sqrt(this.es);\n  if (!this.lat0) {\n    this.lat0 = 0.863937979737193;\n  }\n  if (!this.long0) {\n    this.long0 = 0.7417649320975901 - 0.308341501185665;\n  }\n  /* if scale not set default to 0.9999 */\n  if (!this.k0) {\n    this.k0 = 0.9999;\n  }\n  this.s45 = 0.785398163397448; /* 45 */\n  this.s90 = 2 * this.s45;\n  this.fi0 = this.lat0;\n  this.e2 = this.es;\n  this.e = Math.sqrt(this.e2);\n  this.alfa = Math.sqrt(1 + (this.e2 * Math.pow(Math.cos(this.fi0), 4)) / (1 - this.e2));\n  this.uq = 1.04216856380474;\n  this.u0 = Math.asin(Math.sin(this.fi0) / this.alfa);\n  this.g = Math.pow((1 + this.e * Math.sin(this.fi0)) / (1 - this.e * Math.sin(this.fi0)), this.alfa * this.e / 2);\n  this.k = Math.tan(this.u0 / 2 + this.s45) / Math.pow(Math.tan(this.fi0 / 2 + this.s45), this.alfa) * this.g;\n  this.k1 = this.k0;\n  this.n0 = this.a * Math.sqrt(1 - this.e2) / (1 - this.e2 * Math.pow(Math.sin(this.fi0), 2));\n  this.s0 = 1.37008346281555;\n  this.n = Math.sin(this.s0);\n  this.ro0 = this.k1 * this.n0 / Math.tan(this.s0);\n  this.ad = this.s90 - this.uq;\n}\n\n/* ellipsoid */\n/* calculate xy from lat/lon */\n/* Constants, identical to inverse transform function */\nexport function forward(p) {\n  var gfi, u, deltav, s, d, eps, ro;\n  var lon = p.x;\n  var lat = p.y;\n  var delta_lon = adjust_lon(lon - this.long0);\n  /* Transformation */\n  gfi = Math.pow(((1 + this.e * Math.sin(lat)) / (1 - this.e * Math.sin(lat))), (this.alfa * this.e / 2));\n  u = 2 * (Math.atan(this.k * Math.pow(Math.tan(lat / 2 + this.s45), this.alfa) / gfi) - this.s45);\n  deltav = -delta_lon * this.alfa;\n  s = Math.asin(Math.cos(this.ad) * Math.sin(u) + Math.sin(this.ad) * Math.cos(u) * Math.cos(deltav));\n  d = Math.asin(Math.cos(u) * Math.sin(deltav) / Math.cos(s));\n  eps = this.n * d;\n  ro = this.ro0 * Math.pow(Math.tan(this.s0 / 2 + this.s45), this.n) / Math.pow(Math.tan(s / 2 + this.s45), this.n);\n  p.y = ro * Math.cos(eps) / 1;\n  p.x = ro * Math.sin(eps) / 1;\n\n  if (!this.czech) {\n    p.y *= -1;\n    p.x *= -1;\n  }\n  return (p);\n}\n\n/* calculate lat/lon from xy */\nexport function inverse(p) {\n  var u, deltav, s, d, eps, ro, fi1;\n  var ok;\n\n  /* Transformation */\n  /* revert y, x */\n  var tmp = p.x;\n  p.x = p.y;\n  p.y = tmp;\n  if (!this.czech) {\n    p.y *= -1;\n    p.x *= -1;\n  }\n  ro = Math.sqrt(p.x * p.x + p.y * p.y);\n  eps = Math.atan2(p.y, p.x);\n  d = eps / Math.sin(this.s0);\n  s = 2 * (Math.atan(Math.pow(this.ro0 / ro, 1 / this.n) * Math.tan(this.s0 / 2 + this.s45)) - this.s45);\n  u = Math.asin(Math.cos(this.ad) * Math.sin(s) - Math.sin(this.ad) * Math.cos(s) * Math.cos(d));\n  deltav = Math.asin(Math.cos(s) * Math.sin(d) / Math.cos(u));\n  p.x = this.long0 - deltav / this.alfa;\n  fi1 = u;\n  ok = 0;\n  var iter = 0;\n  do {\n    p.y = 2 * (Math.atan(Math.pow(this.k, -1 / this.alfa) * Math.pow(Math.tan(u / 2 + this.s45), 1 / this.alfa) * Math.pow((1 + this.e * Math.sin(fi1)) / (1 - this.e * Math.sin(fi1)), this.e / 2)) - this.s45);\n    if (Math.abs(fi1 - p.y) < 0.0000000001) {\n      ok = 1;\n    }\n    fi1 = p.y;\n    iter += 1;\n  } while (ok === 0 && iter < 15);\n  if (iter >= 15) {\n    return null;\n  }\n\n  return (p);\n}\n\nexport var names = ['Krovak', 'krovak'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "export default function (e0, e1, e2, e3, phi) {\n  return (e0 * phi - e1 * Math.sin(2 * phi) + e2 * Math.sin(4 * phi) - e3 * Math.sin(6 * phi));\n}\n", "export default function (x) {\n  return (1 - 0.25 * x * (1 + x / 16 * (3 + 1.25 * x)));\n}\n", "export default function (x) {\n  return (0.375 * x * (1 + 0.25 * x * (1 + 0.46875 * x)));\n}\n", "export default function (x) {\n  return (0.05859375 * x * x * (1 + 0.75 * x));\n}\n", "export default function (x) {\n  return (x * x * x * (35 / 3072));\n}\n", "export default function (a, e, sinphi) {\n  var temp = e * sinphi;\n  return a / Math.sqrt(1 - temp * temp);\n}\n", "import { HALF_PI } from '../constants/values';\nimport sign from './sign';\n\nexport default function (x) {\n  return (Math.abs(x) < HALF_PI) ? x : (x - (sign(x) * Math.PI));\n}\n", "export default function (ml, e0, e1, e2, e3) {\n  var phi;\n  var dphi;\n\n  phi = ml / e0;\n  for (var i = 0; i < 15; i++) {\n    dphi = (ml - (e0 * phi - e1 * Math.sin(2 * phi) + e2 * Math.sin(4 * phi) - e3 * Math.sin(6 * phi))) / (e0 - 2 * e1 * Math.cos(2 * phi) + 4 * e2 * Math.cos(4 * phi) - 6 * e3 * Math.cos(6 * phi));\n    phi += dphi;\n    if (Math.abs(dphi) <= 0.0000000001) {\n      return phi;\n    }\n  }\n\n  // ..reportError(\"IMLFN-CONV:Latitude failed to converge after 15 iterations\");\n  return NaN;\n}\n", "import mlfn from '../common/mlfn';\nimport e0fn from '../common/e0fn';\nimport e1fn from '../common/e1fn';\nimport e2fn from '../common/e2fn';\nimport e3fn from '../common/e3fn';\nimport gN from '../common/gN';\nimport adjust_lon from '../common/adjust_lon';\nimport adjust_lat from '../common/adjust_lat';\nimport imlfn from '../common/imlfn';\nimport { HALF_PI, EPSLN } from '../constants/values';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} es\n * @property {number} e0\n * @property {number} e1\n * @property {number} e2\n * @property {number} e3\n * @property {number} ml0\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  if (!this.sphere) {\n    this.e0 = e0fn(this.es);\n    this.e1 = e1fn(this.es);\n    this.e2 = e2fn(this.es);\n    this.e3 = e3fn(this.es);\n    this.ml0 = this.a * mlfn(this.e0, this.e1, this.e2, this.e3, this.lat0);\n  }\n}\n\n/* <PERSON>ini forward equations--mapping lat,long to x,y\n  ----------------------------------------------------------------------- */\nexport function forward(p) {\n  /* Forward equations\n      ----------------- */\n  var x, y;\n  var lam = p.x;\n  var phi = p.y;\n  lam = adjust_lon(lam - this.long0);\n\n  if (this.sphere) {\n    x = this.a * Math.asin(Math.cos(phi) * Math.sin(lam));\n    y = this.a * (Math.atan2(Math.tan(phi), Math.cos(lam)) - this.lat0);\n  } else {\n    // ellipsoid\n    var sinphi = Math.sin(phi);\n    var cosphi = Math.cos(phi);\n    var nl = gN(this.a, this.e, sinphi);\n    var tl = Math.tan(phi) * Math.tan(phi);\n    var al = lam * Math.cos(phi);\n    var asq = al * al;\n    var cl = this.es * cosphi * cosphi / (1 - this.es);\n    var ml = this.a * mlfn(this.e0, this.e1, this.e2, this.e3, phi);\n\n    x = nl * al * (1 - asq * tl * (1 / 6 - (8 - tl + 8 * cl) * asq / 120));\n    y = ml - this.ml0 + nl * sinphi / cosphi * asq * (0.5 + (5 - tl + 6 * cl) * asq / 24);\n  }\n\n  p.x = x + this.x0;\n  p.y = y + this.y0;\n  return p;\n}\n\n/* Inverse equations\n  ----------------- */\nexport function inverse(p) {\n  p.x -= this.x0;\n  p.y -= this.y0;\n  var x = p.x / this.a;\n  var y = p.y / this.a;\n  var phi, lam;\n\n  if (this.sphere) {\n    var dd = y + this.lat0;\n    phi = Math.asin(Math.sin(dd) * Math.cos(x));\n    lam = Math.atan2(Math.tan(x), Math.cos(dd));\n  } else {\n    /* ellipsoid */\n    var ml1 = this.ml0 / this.a + y;\n    var phi1 = imlfn(ml1, this.e0, this.e1, this.e2, this.e3);\n    if (Math.abs(Math.abs(phi1) - HALF_PI) <= EPSLN) {\n      p.x = this.long0;\n      p.y = HALF_PI;\n      if (y < 0) {\n        p.y *= -1;\n      }\n      return p;\n    }\n    var nl1 = gN(this.a, this.e, Math.sin(phi1));\n\n    var rl1 = nl1 * nl1 * nl1 / this.a / this.a * (1 - this.es);\n    var tl1 = Math.pow(Math.tan(phi1), 2);\n    var dl = x * this.a / nl1;\n    var dsq = dl * dl;\n    phi = phi1 - nl1 * Math.tan(phi1) / rl1 * dl * dl * (0.5 - (1 + 3 * tl1) * dl * dl / 24);\n    lam = dl * (1 - dsq * (tl1 / 3 + (1 + 3 * tl1) * tl1 * dsq / 15)) / Math.cos(phi1);\n  }\n\n  p.x = adjust_lon(lam + this.long0);\n  p.y = adjust_lat(phi);\n  return p;\n}\n\nexport var names = ['Cassini', 'Cassini_Soldner', 'cass'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "export default function (eccent, sinphi) {\n  var con;\n  if (eccent > 1.0e-7) {\n    con = eccent * sinphi;\n    return ((1 - eccent * eccent) * (sinphi / (1 - con * con) - (0.5 / eccent) * Math.log((1 - con) / (1 + con))));\n  } else {\n    return (2 * sinphi);\n  }\n}\n", "import { HALF_PI, EPSLN, FORTPI } from '../constants/values';\n\nimport qsfnz from '../common/qsfnz';\nimport adjust_lon from '../common/adjust_lon';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} mode\n * @property {Array<number>} apa\n * @property {number} dd\n * @property {number} e\n * @property {number} es\n * @property {number} mmf\n * @property {number} rq\n * @property {number} qp\n * @property {number} sinb1\n * @property {number} cosb1\n * @property {number} ymf\n * @property {number} xmf\n * @property {number} sinph0\n * @property {number} cosph0\n */\n\n/*\n  reference\n    \"New Equal-Area Map Projections for Noncircular Regions\", <PERSON>,\n    The American Cartographer, Vol 15, No. 4, October 1988, pp. 341-355.\n  */\n\nexport var S_POLE = 1;\nexport var N_POLE = 2;\nexport var EQUIT = 3;\nexport var OBLIQ = 4;\n\n/**\n * Initialize the Lambert Azimuthal Equal Area projection\n * @this {import('../defs.js').ProjectionDefinition & LocalThis}\n */\nexport function init() {\n  var t = Math.abs(this.lat0);\n  if (Math.abs(t - HALF_PI) < EPSLN) {\n    this.mode = this.lat0 < 0 ? S_POLE : N_POLE;\n  } else if (Math.abs(t) < EPSLN) {\n    this.mode = EQUIT;\n  } else {\n    this.mode = OBLIQ;\n  }\n  if (this.es > 0) {\n    var sinphi;\n\n    this.qp = qsfnz(this.e, 1);\n    this.mmf = 0.5 / (1 - this.es);\n    this.apa = authset(this.es);\n    switch (this.mode) {\n      case N_POLE:\n        this.dd = 1;\n        break;\n      case S_POLE:\n        this.dd = 1;\n        break;\n      case EQUIT:\n        this.rq = Math.sqrt(0.5 * this.qp);\n        this.dd = 1 / this.rq;\n        this.xmf = 1;\n        this.ymf = 0.5 * this.qp;\n        break;\n      case OBLIQ:\n        this.rq = Math.sqrt(0.5 * this.qp);\n        sinphi = Math.sin(this.lat0);\n        this.sinb1 = qsfnz(this.e, sinphi) / this.qp;\n        this.cosb1 = Math.sqrt(1 - this.sinb1 * this.sinb1);\n        this.dd = Math.cos(this.lat0) / (Math.sqrt(1 - this.es * sinphi * sinphi) * this.rq * this.cosb1);\n        this.ymf = (this.xmf = this.rq) / this.dd;\n        this.xmf *= this.dd;\n        break;\n    }\n  } else {\n    if (this.mode === OBLIQ) {\n      this.sinph0 = Math.sin(this.lat0);\n      this.cosph0 = Math.cos(this.lat0);\n    }\n  }\n}\n\n/* Lambert Azimuthal Equal Area forward equations--mapping lat,long to x,y\n  ----------------------------------------------------------------------- */\nexport function forward(p) {\n  /* Forward equations\n      ----------------- */\n  var x, y, coslam, sinlam, sinphi, q, sinb, cosb, b, cosphi;\n  var lam = p.x;\n  var phi = p.y;\n\n  lam = adjust_lon(lam - this.long0);\n  if (this.sphere) {\n    sinphi = Math.sin(phi);\n    cosphi = Math.cos(phi);\n    coslam = Math.cos(lam);\n    if (this.mode === this.OBLIQ || this.mode === this.EQUIT) {\n      y = (this.mode === this.EQUIT) ? 1 + cosphi * coslam : 1 + this.sinph0 * sinphi + this.cosph0 * cosphi * coslam;\n      if (y <= EPSLN) {\n        return null;\n      }\n      y = Math.sqrt(2 / y);\n      x = y * cosphi * Math.sin(lam);\n      y *= (this.mode === this.EQUIT) ? sinphi : this.cosph0 * sinphi - this.sinph0 * cosphi * coslam;\n    } else if (this.mode === this.N_POLE || this.mode === this.S_POLE) {\n      if (this.mode === this.N_POLE) {\n        coslam = -coslam;\n      }\n      if (Math.abs(phi + this.lat0) < EPSLN) {\n        return null;\n      }\n      y = FORTPI - phi * 0.5;\n      y = 2 * ((this.mode === this.S_POLE) ? Math.cos(y) : Math.sin(y));\n      x = y * Math.sin(lam);\n      y *= coslam;\n    }\n  } else {\n    sinb = 0;\n    cosb = 0;\n    b = 0;\n    coslam = Math.cos(lam);\n    sinlam = Math.sin(lam);\n    sinphi = Math.sin(phi);\n    q = qsfnz(this.e, sinphi);\n    if (this.mode === this.OBLIQ || this.mode === this.EQUIT) {\n      sinb = q / this.qp;\n      cosb = Math.sqrt(1 - sinb * sinb);\n    }\n    switch (this.mode) {\n      case this.OBLIQ:\n        b = 1 + this.sinb1 * sinb + this.cosb1 * cosb * coslam;\n        break;\n      case this.EQUIT:\n        b = 1 + cosb * coslam;\n        break;\n      case this.N_POLE:\n        b = HALF_PI + phi;\n        q = this.qp - q;\n        break;\n      case this.S_POLE:\n        b = phi - HALF_PI;\n        q = this.qp + q;\n        break;\n    }\n    if (Math.abs(b) < EPSLN) {\n      return null;\n    }\n    switch (this.mode) {\n      case this.OBLIQ:\n      case this.EQUIT:\n        b = Math.sqrt(2 / b);\n        if (this.mode === this.OBLIQ) {\n          y = this.ymf * b * (this.cosb1 * sinb - this.sinb1 * cosb * coslam);\n        } else {\n          y = (b = Math.sqrt(2 / (1 + cosb * coslam))) * sinb * this.ymf;\n        }\n        x = this.xmf * b * cosb * sinlam;\n        break;\n      case this.N_POLE:\n      case this.S_POLE:\n        if (q >= 0) {\n          x = (b = Math.sqrt(q)) * sinlam;\n          y = coslam * ((this.mode === this.S_POLE) ? b : -b);\n        } else {\n          x = y = 0;\n        }\n        break;\n    }\n  }\n\n  p.x = this.a * x + this.x0;\n  p.y = this.a * y + this.y0;\n  return p;\n}\n\n/* Inverse equations\n  ----------------- */\nexport function inverse(p) {\n  p.x -= this.x0;\n  p.y -= this.y0;\n  var x = p.x / this.a;\n  var y = p.y / this.a;\n  var lam, phi, cCe, sCe, q, rho, ab;\n  if (this.sphere) {\n    var cosz = 0,\n      rh, sinz = 0;\n\n    rh = Math.sqrt(x * x + y * y);\n    phi = rh * 0.5;\n    if (phi > 1) {\n      return null;\n    }\n    phi = 2 * Math.asin(phi);\n    if (this.mode === this.OBLIQ || this.mode === this.EQUIT) {\n      sinz = Math.sin(phi);\n      cosz = Math.cos(phi);\n    }\n    switch (this.mode) {\n      case this.EQUIT:\n        phi = (Math.abs(rh) <= EPSLN) ? 0 : Math.asin(y * sinz / rh);\n        x *= sinz;\n        y = cosz * rh;\n        break;\n      case this.OBLIQ:\n        phi = (Math.abs(rh) <= EPSLN) ? this.lat0 : Math.asin(cosz * this.sinph0 + y * sinz * this.cosph0 / rh);\n        x *= sinz * this.cosph0;\n        y = (cosz - Math.sin(phi) * this.sinph0) * rh;\n        break;\n      case this.N_POLE:\n        y = -y;\n        phi = HALF_PI - phi;\n        break;\n      case this.S_POLE:\n        phi -= HALF_PI;\n        break;\n    }\n    lam = (y === 0 && (this.mode === this.EQUIT || this.mode === this.OBLIQ)) ? 0 : Math.atan2(x, y);\n  } else {\n    ab = 0;\n    if (this.mode === this.OBLIQ || this.mode === this.EQUIT) {\n      x /= this.dd;\n      y *= this.dd;\n      rho = Math.sqrt(x * x + y * y);\n      if (rho < EPSLN) {\n        p.x = this.long0;\n        p.y = this.lat0;\n        return p;\n      }\n      sCe = 2 * Math.asin(0.5 * rho / this.rq);\n      cCe = Math.cos(sCe);\n      x *= (sCe = Math.sin(sCe));\n      if (this.mode === this.OBLIQ) {\n        ab = cCe * this.sinb1 + y * sCe * this.cosb1 / rho;\n        q = this.qp * ab;\n        y = rho * this.cosb1 * cCe - y * this.sinb1 * sCe;\n      } else {\n        ab = y * sCe / rho;\n        q = this.qp * ab;\n        y = rho * cCe;\n      }\n    } else if (this.mode === this.N_POLE || this.mode === this.S_POLE) {\n      if (this.mode === this.N_POLE) {\n        y = -y;\n      }\n      q = (x * x + y * y);\n      if (!q) {\n        p.x = this.long0;\n        p.y = this.lat0;\n        return p;\n      }\n      ab = 1 - q / this.qp;\n      if (this.mode === this.S_POLE) {\n        ab = -ab;\n      }\n    }\n    lam = Math.atan2(x, y);\n    phi = authlat(Math.asin(ab), this.apa);\n  }\n\n  p.x = adjust_lon(this.long0 + lam);\n  p.y = phi;\n  return p;\n}\n\n/* determine latitude from authalic latitude */\nvar P00 = 0.33333333333333333333;\n\nvar P01 = 0.17222222222222222222;\nvar P02 = 0.10257936507936507936;\nvar P10 = 0.06388888888888888888;\nvar P11 = 0.06640211640211640211;\nvar P20 = 0.01641501294219154443;\n\nfunction authset(es) {\n  var t;\n  var APA = [];\n  APA[0] = es * P00;\n  t = es * es;\n  APA[0] += t * P01;\n  APA[1] = t * P10;\n  t *= es;\n  APA[0] += t * P02;\n  APA[1] += t * P11;\n  APA[2] = t * P20;\n  return APA;\n}\n\nfunction authlat(beta, APA) {\n  var t = beta + beta;\n  return (beta + APA[0] * Math.sin(t) + APA[1] * Math.sin(t + t) + APA[2] * Math.sin(t + t + t));\n}\n\nexport var names = ['Lambert Azimuthal Equal Area', 'Lambert_Azimuthal_Equal_Area', 'laea'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names,\n  S_POLE: S_POLE,\n  N_POLE: N_POLE,\n  EQUIT: EQUIT,\n  OBLIQ: OBLIQ\n};\n", "export default function (x) {\n  if (Math.abs(x) > 1) {\n    x = (x > 1) ? 1 : -1;\n  }\n  return Math.asin(x);\n}\n", "import msfnz from '../common/msfnz';\nimport qsfnz from '../common/qsfnz';\nimport adjust_lon from '../common/adjust_lon';\nimport asinz from '../common/asinz';\nimport { EPSLN } from '../constants/values';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} temp\n * @property {number} es\n * @property {number} e3\n * @property {number} sin_po\n * @property {number} cos_po\n * @property {number} t1\n * @property {number} con\n * @property {number} ms1\n * @property {number} qs1\n * @property {number} t2\n * @property {number} ms2\n * @property {number} qs2\n * @property {number} t3\n * @property {number} qs0\n * @property {number} ns0\n * @property {number} c\n * @property {number} rh\n * @property {number} sin_phi\n * @property {number} cos_phi\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  if (Math.abs(this.lat1 + this.lat2) < EPSLN) {\n    return;\n  }\n  this.temp = this.b / this.a;\n  this.es = 1 - Math.pow(this.temp, 2);\n  this.e3 = Math.sqrt(this.es);\n\n  this.sin_po = Math.sin(this.lat1);\n  this.cos_po = Math.cos(this.lat1);\n  this.t1 = this.sin_po;\n  this.con = this.sin_po;\n  this.ms1 = msfnz(this.e3, this.sin_po, this.cos_po);\n  this.qs1 = qsfnz(this.e3, this.sin_po);\n\n  this.sin_po = Math.sin(this.lat2);\n  this.cos_po = Math.cos(this.lat2);\n  this.t2 = this.sin_po;\n  this.ms2 = msfnz(this.e3, this.sin_po, this.cos_po);\n  this.qs2 = qsfnz(this.e3, this.sin_po);\n\n  this.sin_po = Math.sin(this.lat0);\n  this.cos_po = Math.cos(this.lat0);\n  this.t3 = this.sin_po;\n  this.qs0 = qsfnz(this.e3, this.sin_po);\n\n  if (Math.abs(this.lat1 - this.lat2) > EPSLN) {\n    this.ns0 = (this.ms1 * this.ms1 - this.ms2 * this.ms2) / (this.qs2 - this.qs1);\n  } else {\n    this.ns0 = this.con;\n  }\n  this.c = this.ms1 * this.ms1 + this.ns0 * this.qs1;\n  this.rh = this.a * Math.sqrt(this.c - this.ns0 * this.qs0) / this.ns0;\n}\n\n/* Albers Conical Equal Area forward equations--mapping lat,long to x,y\n  ------------------------------------------------------------------- */\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n\n  this.sin_phi = Math.sin(lat);\n  this.cos_phi = Math.cos(lat);\n\n  var qs = qsfnz(this.e3, this.sin_phi);\n  var rh1 = this.a * Math.sqrt(this.c - this.ns0 * qs) / this.ns0;\n  var theta = this.ns0 * adjust_lon(lon - this.long0);\n  var x = rh1 * Math.sin(theta) + this.x0;\n  var y = this.rh - rh1 * Math.cos(theta) + this.y0;\n\n  p.x = x;\n  p.y = y;\n  return p;\n}\n\nexport function inverse(p) {\n  var rh1, qs, con, theta, lon, lat;\n\n  p.x -= this.x0;\n  p.y = this.rh - p.y + this.y0;\n  if (this.ns0 >= 0) {\n    rh1 = Math.sqrt(p.x * p.x + p.y * p.y);\n    con = 1;\n  } else {\n    rh1 = -Math.sqrt(p.x * p.x + p.y * p.y);\n    con = -1;\n  }\n  theta = 0;\n  if (rh1 !== 0) {\n    theta = Math.atan2(con * p.x, con * p.y);\n  }\n  con = rh1 * this.ns0 / this.a;\n  if (this.sphere) {\n    lat = Math.asin((this.c - con * con) / (2 * this.ns0));\n  } else {\n    qs = (this.c - con * con) / this.ns0;\n    lat = this.phi1z(this.e3, qs);\n  }\n\n  lon = adjust_lon(theta / this.ns0 + this.long0);\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\n/* Function to compute phi1, the latitude for the inverse of the\n   Albers Conical Equal-Area projection.\n------------------------------------------- */\nexport function phi1z(eccent, qs) {\n  var sinphi, cosphi, con, com, dphi;\n  var phi = asinz(0.5 * qs);\n  if (eccent < EPSLN) {\n    return phi;\n  }\n\n  var eccnts = eccent * eccent;\n  for (var i = 1; i <= 25; i++) {\n    sinphi = Math.sin(phi);\n    cosphi = Math.cos(phi);\n    con = eccent * sinphi;\n    com = 1 - con * con;\n    dphi = 0.5 * com * com / cosphi * (qs / (1 - eccnts) - sinphi / com + 0.5 / eccent * Math.log((1 - con) / (1 + con)));\n    phi = phi + dphi;\n    if (Math.abs(dphi) <= 1e-7) {\n      return phi;\n    }\n  }\n  return null;\n}\n\nexport var names = ['Albers_Conic_Equal_Area', 'Albers_Equal_Area', 'Albers', 'aea'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names,\n  phi1z: phi1z\n};\n", "import adjust_lon from '../common/adjust_lon';\nimport asinz from '../common/asinz';\nimport { EPSLN } from '../constants/values';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} sin_p14\n * @property {number} cos_p14\n * @property {number} infinity_dist\n * @property {number} rc\n */\n\n/**\n  reference:\n    Wolfram Mathworld \"Gnomonic Projection\"\n    http://mathworld.wolfram.com/GnomonicProjection.html\n    Accessed: 12th November 2009\n   @this {import('../defs.js').ProjectionDefinition & LocalThis}\n */\nexport function init() {\n  /* Place parameters in static storage for common use\n      ------------------------------------------------- */\n  this.sin_p14 = Math.sin(this.lat0);\n  this.cos_p14 = Math.cos(this.lat0);\n  // Approximation for projecting points to the horizon (infinity)\n  this.infinity_dist = 1000 * this.a;\n  this.rc = 1;\n}\n\n/* Gnomonic forward equations--mapping lat,long to x,y\n    --------------------------------------------------- */\nexport function forward(p) {\n  var sinphi, cosphi; /* sin and cos value        */\n  var dlon; /* delta longitude value      */\n  var coslon; /* cos of longitude        */\n  var ksp; /* scale factor          */\n  var g;\n  var x, y;\n  var lon = p.x;\n  var lat = p.y;\n  /* Forward equations\n      ----------------- */\n  dlon = adjust_lon(lon - this.long0);\n\n  sinphi = Math.sin(lat);\n  cosphi = Math.cos(lat);\n\n  coslon = Math.cos(dlon);\n  g = this.sin_p14 * sinphi + this.cos_p14 * cosphi * coslon;\n  ksp = 1;\n  if ((g > 0) || (Math.abs(g) <= EPSLN)) {\n    x = this.x0 + this.a * ksp * cosphi * Math.sin(dlon) / g;\n    y = this.y0 + this.a * ksp * (this.cos_p14 * sinphi - this.sin_p14 * cosphi * coslon) / g;\n  } else {\n    // Point is in the opposing hemisphere and is unprojectable\n    // We still need to return a reasonable point, so we project\n    // to infinity, on a bearing\n    // equivalent to the northern hemisphere equivalent\n    // This is a reasonable approximation for short shapes and lines that\n    // straddle the horizon.\n\n    x = this.x0 + this.infinity_dist * cosphi * Math.sin(dlon);\n    y = this.y0 + this.infinity_dist * (this.cos_p14 * sinphi - this.sin_p14 * cosphi * coslon);\n  }\n  p.x = x;\n  p.y = y;\n  return p;\n}\n\nexport function inverse(p) {\n  var rh; /* Rho */\n  var sinc, cosc;\n  var c;\n  var lon, lat;\n\n  /* Inverse equations\n      ----------------- */\n  p.x = (p.x - this.x0) / this.a;\n  p.y = (p.y - this.y0) / this.a;\n\n  p.x /= this.k0;\n  p.y /= this.k0;\n\n  if ((rh = Math.sqrt(p.x * p.x + p.y * p.y))) {\n    c = Math.atan2(rh, this.rc);\n    sinc = Math.sin(c);\n    cosc = Math.cos(c);\n\n    lat = asinz(cosc * this.sin_p14 + (p.y * sinc * this.cos_p14) / rh);\n    lon = Math.atan2(p.x * sinc, rh * this.cos_p14 * cosc - p.y * this.sin_p14 * sinc);\n    lon = adjust_lon(this.long0 + lon);\n  } else {\n    lat = this.phic0;\n    lon = 0;\n  }\n\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = ['gnom'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import { HALF_PI } from '../constants/values';\n\nexport default function (eccent, q) {\n  var temp = 1 - (1 - eccent * eccent) / (2 * eccent) * Math.log((1 - eccent) / (1 + eccent));\n  if (Math.abs(Math.abs(q) - temp) < 1.0E-6) {\n    if (q < 0) {\n      return (-1 * HALF_PI);\n    } else {\n      return HALF_PI;\n    }\n  }\n  // var phi = 0.5* q/(1-eccent*eccent);\n  var phi = Math.asin(0.5 * q);\n  var dphi;\n  var sin_phi;\n  var cos_phi;\n  var con;\n  for (var i = 0; i < 30; i++) {\n    sin_phi = Math.sin(phi);\n    cos_phi = Math.cos(phi);\n    con = eccent * sin_phi;\n    dphi = Math.pow(1 - con * con, 2) / (2 * cos_phi) * (q / (1 - eccent * eccent) - sin_phi / (1 - con * con) + 0.5 / eccent * Math.log((1 - con) / (1 + con)));\n    phi += dphi;\n    if (Math.abs(dphi) <= 0.0000000001) {\n      return phi;\n    }\n  }\n\n  // console.log(\"IQSFN-CONV:Latitude failed to converge after 30 iterations\");\n  return NaN;\n}\n", "import adjust_lon from '../common/adjust_lon';\nimport qsfnz from '../common/qsfnz';\nimport msfnz from '../common/msfnz';\nimport iqsfnz from '../common/iqsfnz';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} e\n */\n\n/**\n  reference:\n    \"Cartographic Projection Procedures for the UNIX Environment-\n    A User's Manual\" by <PERSON>,\n    USGS Open File Report 90-284and Release 4 Interim Reports (2003)\n  @this {import('../defs.js').ProjectionDefinition & LocalThis}\n*/\nexport function init() {\n  // no-op\n  if (!this.sphere) {\n    this.k0 = msfnz(this.e, Math.sin(this.lat_ts), Math.cos(this.lat_ts));\n  }\n}\n\n/* Cylindrical Equal Area forward equations--mapping lat,long to x,y\n    ------------------------------------------------------------ */\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n  var x, y;\n  /* Forward equations\n      ----------------- */\n  var dlon = adjust_lon(lon - this.long0);\n  if (this.sphere) {\n    x = this.x0 + this.a * dlon * Math.cos(this.lat_ts);\n    y = this.y0 + this.a * Math.sin(lat) / Math.cos(this.lat_ts);\n  } else {\n    var qs = qsfnz(this.e, Math.sin(lat));\n    x = this.x0 + this.a * this.k0 * dlon;\n    y = this.y0 + this.a * qs * 0.5 / this.k0;\n  }\n\n  p.x = x;\n  p.y = y;\n  return p;\n}\n\n/* Cylindrical Equal Area inverse equations--mapping x,y to lat/long\n    ------------------------------------------------------------ */\nexport function inverse(p) {\n  p.x -= this.x0;\n  p.y -= this.y0;\n  var lon, lat;\n\n  if (this.sphere) {\n    lon = adjust_lon(this.long0 + (p.x / this.a) / Math.cos(this.lat_ts));\n    lat = Math.asin((p.y / this.a) * Math.cos(this.lat_ts));\n  } else {\n    lat = iqsfnz(this.e, 2 * p.y * this.k0 / this.a);\n    lon = adjust_lon(this.long0 + p.x / (this.a * this.k0));\n  }\n\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = ['cea'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import adjust_lon from '../common/adjust_lon';\nimport adjust_lat from '../common/adjust_lat';\n\nexport function init() {\n  this.x0 = this.x0 || 0;\n  this.y0 = this.y0 || 0;\n  this.lat0 = this.lat0 || 0;\n  this.long0 = this.long0 || 0;\n  this.lat_ts = this.lat_ts || 0;\n  this.title = this.title || 'Equidistant Cylindrical (Plate Carre)';\n\n  this.rc = Math.cos(this.lat_ts);\n}\n\n// forward equations--mapping lat,long to x,y\n// -----------------------------------------------------------------\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n\n  var dlon = adjust_lon(lon - this.long0);\n  var dlat = adjust_lat(lat - this.lat0);\n  p.x = this.x0 + (this.a * dlon * this.rc);\n  p.y = this.y0 + (this.a * dlat);\n  return p;\n}\n\n// inverse equations--mapping x,y to lat/long\n// -----------------------------------------------------------------\nexport function inverse(p) {\n  var x = p.x;\n  var y = p.y;\n\n  p.x = adjust_lon(this.long0 + ((x - this.x0) / (this.a * this.rc)));\n  p.y = adjust_lat(this.lat0 + ((y - this.y0) / (this.a)));\n  return p;\n}\n\nexport var names = ['Equirectangular', 'Equidistant_Cylindrical', 'Equidistant_Cylindrical_Spherical', 'eqc'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import e0fn from '../common/e0fn';\nimport e1fn from '../common/e1fn';\nimport e2fn from '../common/e2fn';\nimport e3fn from '../common/e3fn';\nimport adjust_lon from '../common/adjust_lon';\nimport adjust_lat from '../common/adjust_lat';\nimport mlfn from '../common/mlfn';\nimport { EPSLN } from '../constants/values';\n\nimport gN from '../common/gN';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} temp\n * @property {number} es\n * @property {number} e\n * @property {number} e0\n * @property {number} e1\n * @property {number} e2\n * @property {number} e3\n * @property {number} ml0\n */\n\nvar MAX_ITER = 20;\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  /* Place parameters in static storage for common use\n      ------------------------------------------------- */\n  this.temp = this.b / this.a;\n  this.es = 1 - Math.pow(this.temp, 2); // devait etre dans tmerc.js mais n y est pas donc je commente sinon retour de valeurs nulles\n  this.e = Math.sqrt(this.es);\n  this.e0 = e0fn(this.es);\n  this.e1 = e1fn(this.es);\n  this.e2 = e2fn(this.es);\n  this.e3 = e3fn(this.es);\n  this.ml0 = this.a * mlfn(this.e0, this.e1, this.e2, this.e3, this.lat0); // si que des zeros le calcul ne se fait pas\n}\n\n/* Polyconic forward equations--mapping lat,long to x,y\n    --------------------------------------------------- */\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n  var x, y, el;\n  var dlon = adjust_lon(lon - this.long0);\n  el = dlon * Math.sin(lat);\n  if (this.sphere) {\n    if (Math.abs(lat) <= EPSLN) {\n      x = this.a * dlon;\n      y = -1 * this.a * this.lat0;\n    } else {\n      x = this.a * Math.sin(el) / Math.tan(lat);\n      y = this.a * (adjust_lat(lat - this.lat0) + (1 - Math.cos(el)) / Math.tan(lat));\n    }\n  } else {\n    if (Math.abs(lat) <= EPSLN) {\n      x = this.a * dlon;\n      y = -1 * this.ml0;\n    } else {\n      var nl = gN(this.a, this.e, Math.sin(lat)) / Math.tan(lat);\n      x = nl * Math.sin(el);\n      y = this.a * mlfn(this.e0, this.e1, this.e2, this.e3, lat) - this.ml0 + nl * (1 - Math.cos(el));\n    }\n  }\n  p.x = x + this.x0;\n  p.y = y + this.y0;\n  return p;\n}\n\n/* Inverse equations\n  ----------------- */\nexport function inverse(p) {\n  var lon, lat, x, y, i;\n  var al, bl;\n  var phi, dphi;\n  x = p.x - this.x0;\n  y = p.y - this.y0;\n\n  if (this.sphere) {\n    if (Math.abs(y + this.a * this.lat0) <= EPSLN) {\n      lon = adjust_lon(x / this.a + this.long0);\n      lat = 0;\n    } else {\n      al = this.lat0 + y / this.a;\n      bl = x * x / this.a / this.a + al * al;\n      phi = al;\n      var tanphi;\n      for (i = MAX_ITER; i; --i) {\n        tanphi = Math.tan(phi);\n        dphi = -1 * (al * (phi * tanphi + 1) - phi - 0.5 * (phi * phi + bl) * tanphi) / ((phi - al) / tanphi - 1);\n        phi += dphi;\n        if (Math.abs(dphi) <= EPSLN) {\n          lat = phi;\n          break;\n        }\n      }\n      lon = adjust_lon(this.long0 + (Math.asin(x * Math.tan(phi) / this.a)) / Math.sin(lat));\n    }\n  } else {\n    if (Math.abs(y + this.ml0) <= EPSLN) {\n      lat = 0;\n      lon = adjust_lon(this.long0 + x / this.a);\n    } else {\n      al = (this.ml0 + y) / this.a;\n      bl = x * x / this.a / this.a + al * al;\n      phi = al;\n      var cl, mln, mlnp, ma;\n      var con;\n      for (i = MAX_ITER; i; --i) {\n        con = this.e * Math.sin(phi);\n        cl = Math.sqrt(1 - con * con) * Math.tan(phi);\n        mln = this.a * mlfn(this.e0, this.e1, this.e2, this.e3, phi);\n        mlnp = this.e0 - 2 * this.e1 * Math.cos(2 * phi) + 4 * this.e2 * Math.cos(4 * phi) - 6 * this.e3 * Math.cos(6 * phi);\n        ma = mln / this.a;\n        dphi = (al * (cl * ma + 1) - ma - 0.5 * cl * (ma * ma + bl)) / (this.es * Math.sin(2 * phi) * (ma * ma + bl - 2 * al * ma) / (4 * cl) + (al - ma) * (cl * mlnp - 2 / Math.sin(2 * phi)) - mlnp);\n        phi -= dphi;\n        if (Math.abs(dphi) <= EPSLN) {\n          lat = phi;\n          break;\n        }\n      }\n\n      // lat=phi4z(this.e,this.e0,this.e1,this.e2,this.e3,al,bl,0,0);\n      cl = Math.sqrt(1 - this.es * Math.pow(Math.sin(lat), 2)) * Math.tan(lat);\n      lon = adjust_lon(this.long0 + Math.asin(x * cl / this.a) / Math.sin(lat));\n    }\n  }\n\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = ['Polyconic', 'American_Polyconic', 'poly'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import { SEC_TO_RAD } from '../constants/values';\n\n/*\n  reference\n    Department of Land and Survey Technical Circular 1973/32\n      http://www.linz.govt.nz/docs/miscellaneous/nz-map-definition.pdf\n    OSG Technical Report 4.1\n      http://www.linz.govt.nz/docs/miscellaneous/nzmg.pdf\n  */\n\n/**\n * iterations: Number of iterations to refine inverse transform.\n *     0 -> km accuracy\n *     1 -> m accuracy -- suitable for most mapping applications\n *     2 -> mm accuracy\n */\nexport var iterations = 1;\n\nexport function init() {\n  this.A = [];\n  this.A[1] = 0.6399175073;\n  this.A[2] = -0.1358797613;\n  this.A[3] = 0.063294409;\n  this.A[4] = -0.02526853;\n  this.A[5] = 0.0117879;\n  this.A[6] = -0.0055161;\n  this.A[7] = 0.0026906;\n  this.A[8] = -0.001333;\n  this.A[9] = 0.00067;\n  this.A[10] = -0.00034;\n\n  this.B_re = [];\n  this.B_im = [];\n  this.B_re[1] = 0.7557853228;\n  this.B_im[1] = 0;\n  this.B_re[2] = 0.249204646;\n  this.B_im[2] = 0.003371507;\n  this.B_re[3] = -0.001541739;\n  this.B_im[3] = 0.041058560;\n  this.B_re[4] = -0.10162907;\n  this.B_im[4] = 0.01727609;\n  this.B_re[5] = -0.26623489;\n  this.B_im[5] = -0.36249218;\n  this.B_re[6] = -0.6870983;\n  this.B_im[6] = -1.1651967;\n\n  this.C_re = [];\n  this.C_im = [];\n  this.C_re[1] = 1.3231270439;\n  this.C_im[1] = 0;\n  this.C_re[2] = -0.577245789;\n  this.C_im[2] = -0.007809598;\n  this.C_re[3] = 0.508307513;\n  this.C_im[3] = -0.112208952;\n  this.C_re[4] = -0.15094762;\n  this.C_im[4] = 0.18200602;\n  this.C_re[5] = 1.01418179;\n  this.C_im[5] = 1.64497696;\n  this.C_re[6] = 1.9660549;\n  this.C_im[6] = 2.5127645;\n\n  this.D = [];\n  this.D[1] = 1.5627014243;\n  this.D[2] = 0.5185406398;\n  this.D[3] = -0.03333098;\n  this.D[4] = -0.1052906;\n  this.D[5] = -0.0368594;\n  this.D[6] = 0.007317;\n  this.D[7] = 0.01220;\n  this.D[8] = 0.00394;\n  this.D[9] = -0.0013;\n}\n\n/**\n    New Zealand Map Grid Forward  - long/lat to x/y\n    long/lat in radians\n  */\nexport function forward(p) {\n  var n;\n  var lon = p.x;\n  var lat = p.y;\n\n  var delta_lat = lat - this.lat0;\n  var delta_lon = lon - this.long0;\n\n  // 1. Calculate d_phi and d_psi    ...                          // and d_lambda\n  // For this algorithm, delta_latitude is in seconds of arc x 10-5, so we need to scale to those units. Longitude is radians.\n  var d_phi = delta_lat / SEC_TO_RAD * 1E-5;\n  var d_lambda = delta_lon;\n  var d_phi_n = 1; // d_phi^0\n\n  var d_psi = 0;\n  for (n = 1; n <= 10; n++) {\n    d_phi_n = d_phi_n * d_phi;\n    d_psi = d_psi + this.A[n] * d_phi_n;\n  }\n\n  // 2. Calculate theta\n  var th_re = d_psi;\n  var th_im = d_lambda;\n\n  // 3. Calculate z\n  var th_n_re = 1;\n  var th_n_im = 0; // theta^0\n  var th_n_re1;\n  var th_n_im1;\n\n  var z_re = 0;\n  var z_im = 0;\n  for (n = 1; n <= 6; n++) {\n    th_n_re1 = th_n_re * th_re - th_n_im * th_im;\n    th_n_im1 = th_n_im * th_re + th_n_re * th_im;\n    th_n_re = th_n_re1;\n    th_n_im = th_n_im1;\n    z_re = z_re + this.B_re[n] * th_n_re - this.B_im[n] * th_n_im;\n    z_im = z_im + this.B_im[n] * th_n_re + this.B_re[n] * th_n_im;\n  }\n\n  // 4. Calculate easting and northing\n  p.x = (z_im * this.a) + this.x0;\n  p.y = (z_re * this.a) + this.y0;\n\n  return p;\n}\n\n/**\n    New Zealand Map Grid Inverse  -  x/y to long/lat\n  */\nexport function inverse(p) {\n  var n;\n  var x = p.x;\n  var y = p.y;\n\n  var delta_x = x - this.x0;\n  var delta_y = y - this.y0;\n\n  // 1. Calculate z\n  var z_re = delta_y / this.a;\n  var z_im = delta_x / this.a;\n\n  // 2a. Calculate theta - first approximation gives km accuracy\n  var z_n_re = 1;\n  var z_n_im = 0; // z^0\n  var z_n_re1;\n  var z_n_im1;\n\n  var th_re = 0;\n  var th_im = 0;\n  for (n = 1; n <= 6; n++) {\n    z_n_re1 = z_n_re * z_re - z_n_im * z_im;\n    z_n_im1 = z_n_im * z_re + z_n_re * z_im;\n    z_n_re = z_n_re1;\n    z_n_im = z_n_im1;\n    th_re = th_re + this.C_re[n] * z_n_re - this.C_im[n] * z_n_im;\n    th_im = th_im + this.C_im[n] * z_n_re + this.C_re[n] * z_n_im;\n  }\n\n  // 2b. Iterate to refine the accuracy of the calculation\n  //        0 iterations gives km accuracy\n  //        1 iteration gives m accuracy -- good enough for most mapping applications\n  //        2 iterations bives mm accuracy\n  for (var i = 0; i < this.iterations; i++) {\n    var th_n_re = th_re;\n    var th_n_im = th_im;\n    var th_n_re1;\n    var th_n_im1;\n\n    var num_re = z_re;\n    var num_im = z_im;\n    for (n = 2; n <= 6; n++) {\n      th_n_re1 = th_n_re * th_re - th_n_im * th_im;\n      th_n_im1 = th_n_im * th_re + th_n_re * th_im;\n      th_n_re = th_n_re1;\n      th_n_im = th_n_im1;\n      num_re = num_re + (n - 1) * (this.B_re[n] * th_n_re - this.B_im[n] * th_n_im);\n      num_im = num_im + (n - 1) * (this.B_im[n] * th_n_re + this.B_re[n] * th_n_im);\n    }\n\n    th_n_re = 1;\n    th_n_im = 0;\n    var den_re = this.B_re[1];\n    var den_im = this.B_im[1];\n    for (n = 2; n <= 6; n++) {\n      th_n_re1 = th_n_re * th_re - th_n_im * th_im;\n      th_n_im1 = th_n_im * th_re + th_n_re * th_im;\n      th_n_re = th_n_re1;\n      th_n_im = th_n_im1;\n      den_re = den_re + n * (this.B_re[n] * th_n_re - this.B_im[n] * th_n_im);\n      den_im = den_im + n * (this.B_im[n] * th_n_re + this.B_re[n] * th_n_im);\n    }\n\n    // Complex division\n    var den2 = den_re * den_re + den_im * den_im;\n    th_re = (num_re * den_re + num_im * den_im) / den2;\n    th_im = (num_im * den_re - num_re * den_im) / den2;\n  }\n\n  // 3. Calculate d_phi              ...                                    // and d_lambda\n  var d_psi = th_re;\n  var d_lambda = th_im;\n  var d_psi_n = 1; // d_psi^0\n\n  var d_phi = 0;\n  for (n = 1; n <= 9; n++) {\n    d_psi_n = d_psi_n * d_psi;\n    d_phi = d_phi + this.D[n] * d_psi_n;\n  }\n\n  // 4. Calculate latitude and longitude\n  // d_phi is calcuated in second of arc * 10^-5, so we need to scale back to radians. d_lambda is in radians.\n  var lat = this.lat0 + (d_phi * SEC_TO_RAD * 1E5);\n  var lon = this.long0 + d_lambda;\n\n  p.x = lon;\n  p.y = lat;\n\n  return p;\n}\n\nexport var names = ['New_Zealand_Map_Grid', 'nzmg'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import adjust_lon from '../common/adjust_lon';\n\n/*\n  reference\n    \"New Equal-Area Map Projections for Noncircular Regions\", <PERSON>,\n    The American Cartographer, Vol 15, No. 4, October 1988, pp. 341-355.\n  */\n\n/* Initialize the Miller Cylindrical projection\n  ------------------------------------------- */\nexport function init() {\n  // no-op\n}\n\n/* Miller Cylindrical forward equations--mapping lat,long to x,y\n    ------------------------------------------------------------ */\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n  /* Forward equations\n      ----------------- */\n  var dlon = adjust_lon(lon - this.long0);\n  var x = this.x0 + this.a * dlon;\n  var y = this.y0 + this.a * Math.log(Math.tan((Math.PI / 4) + (lat / 2.5))) * 1.25;\n\n  p.x = x;\n  p.y = y;\n  return p;\n}\n\n/* Miller Cylindrical inverse equations--mapping x,y to lat/long\n    ------------------------------------------------------------ */\nexport function inverse(p) {\n  p.x -= this.x0;\n  p.y -= this.y0;\n\n  var lon = adjust_lon(this.long0 + p.x / this.a);\n  var lat = 2.5 * (Math.atan(Math.exp(0.8 * p.y / this.a)) - Math.PI / 4);\n\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = ['Miller_Cylindrical', 'mill'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import adjust_lon from '../common/adjust_lon';\nimport adjust_lat from '../common/adjust_lat';\nimport pj_enfn from '../common/pj_enfn';\nvar MAX_ITER = 20;\nimport pj_mlfn from '../common/pj_mlfn';\nimport pj_inv_mlfn from '../common/pj_inv_mlfn';\nimport { EPSLN, HALF_PI } from '../constants/values';\n\nimport asinz from '../common/asinz';\n\n/**\n * @typedef {Object} LocalThis\n * @property {Array<number>} en\n * @property {number} n\n * @property {number} m\n * @property {number} C_y\n * @property {number} C_x\n * @property {number} es\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  /* Place parameters in static storage for common use\n    ------------------------------------------------- */\n\n  if (!this.sphere) {\n    this.en = pj_enfn(this.es);\n  } else {\n    this.n = 1;\n    this.m = 0;\n    this.es = 0;\n    this.C_y = Math.sqrt((this.m + 1) / this.n);\n    this.C_x = this.C_y / (this.m + 1);\n  }\n}\n\n/* Sinusoidal forward equations--mapping lat,long to x,y\n  ----------------------------------------------------- */\nexport function forward(p) {\n  var x, y;\n  var lon = p.x;\n  var lat = p.y;\n  /* Forward equations\n    ----------------- */\n  lon = adjust_lon(lon - this.long0);\n\n  if (this.sphere) {\n    if (!this.m) {\n      lat = this.n !== 1 ? Math.asin(this.n * Math.sin(lat)) : lat;\n    } else {\n      var k = this.n * Math.sin(lat);\n      for (var i = MAX_ITER; i; --i) {\n        var V = (this.m * lat + Math.sin(lat) - k) / (this.m + Math.cos(lat));\n        lat -= V;\n        if (Math.abs(V) < EPSLN) {\n          break;\n        }\n      }\n    }\n    x = this.a * this.C_x * lon * (this.m + Math.cos(lat));\n    y = this.a * this.C_y * lat;\n  } else {\n    var s = Math.sin(lat);\n    var c = Math.cos(lat);\n    y = this.a * pj_mlfn(lat, s, c, this.en);\n    x = this.a * lon * c / Math.sqrt(1 - this.es * s * s);\n  }\n\n  p.x = x;\n  p.y = y;\n  return p;\n}\n\nexport function inverse(p) {\n  var lat, temp, lon, s;\n\n  p.x -= this.x0;\n  lon = p.x / this.a;\n  p.y -= this.y0;\n  lat = p.y / this.a;\n\n  if (this.sphere) {\n    lat /= this.C_y;\n    lon = lon / (this.C_x * (this.m + Math.cos(lat)));\n    if (this.m) {\n      lat = asinz((this.m * lat + Math.sin(lat)) / this.n);\n    } else if (this.n !== 1) {\n      lat = asinz(Math.sin(lat) / this.n);\n    }\n    lon = adjust_lon(lon + this.long0);\n    lat = adjust_lat(lat);\n  } else {\n    lat = pj_inv_mlfn(p.y / this.a, this.es, this.en);\n    s = Math.abs(lat);\n    if (s < HALF_PI) {\n      s = Math.sin(lat);\n      temp = this.long0 + p.x * Math.sqrt(1 - this.es * s * s) / (this.a * Math.cos(lat));\n      // temp = this.long0 + p.x / (this.a * Math.cos(lat));\n      lon = adjust_lon(temp);\n    } else if ((s - EPSLN) < HALF_PI) {\n      lon = this.long0;\n    }\n  }\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = ['Sinusoidal', 'sinu'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import adjust_lon from '../common/adjust_lon';\nexport function init() {}\nimport { EPSLN } from '../constants/values';\n/* <PERSON>llweide forward equations--mapping lat,long to x,y\n    ---------------------------------------------------- */\nexport function forward(p) {\n  /* Forward equations\n      ----------------- */\n  var lon = p.x;\n  var lat = p.y;\n\n  var delta_lon = adjust_lon(lon - this.long0);\n  var theta = lat;\n  var con = Math.PI * Math.sin(lat);\n\n  /* Iterate using the <PERSON><PERSON><PERSON> method to find theta\n      ----------------------------------------------------- */\n  while (true) {\n    var delta_theta = -(theta + Math.sin(theta) - con) / (1 + Math.cos(theta));\n    theta += delta_theta;\n    if (Math.abs(delta_theta) < EPSLN) {\n      break;\n    }\n  }\n  theta /= 2;\n\n  /* If the latitude is 90 deg, force the x coordinate to be \"0 + false easting\"\n       this is done here because of precision problems with \"cos(theta)\"\n       -------------------------------------------------------------------------- */\n  if (Math.PI / 2 - Math.abs(lat) < EPSLN) {\n    delta_lon = 0;\n  }\n  var x = 0.900316316158 * this.a * delta_lon * Math.cos(theta) + this.x0;\n  var y = 1.4142135623731 * this.a * Math.sin(theta) + this.y0;\n\n  p.x = x;\n  p.y = y;\n  return p;\n}\n\nexport function inverse(p) {\n  var theta;\n  var arg;\n\n  /* Inverse equations\n      ----------------- */\n  p.x -= this.x0;\n  p.y -= this.y0;\n  arg = p.y / (1.4142135623731 * this.a);\n\n  /* Because of division by zero problems, 'arg' can not be 1.  Therefore\n       a number very close to one is used instead.\n       ------------------------------------------------------------------- */\n  if (Math.abs(arg) > 0.999999999999) {\n    arg = 0.999999999999;\n  }\n  theta = Math.asin(arg);\n  var lon = adjust_lon(this.long0 + (p.x / (0.900316316158 * this.a * Math.cos(theta))));\n  if (lon < (-Math.PI)) {\n    lon = -Math.PI;\n  }\n  if (lon > Math.PI) {\n    lon = Math.PI;\n  }\n  arg = (2 * theta + Math.sin(2 * theta)) / Math.PI;\n  if (Math.abs(arg) > 1) {\n    arg = 1;\n  }\n  var lat = Math.asin(arg);\n\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = ['Mollweide', 'moll'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import e0fn from '../common/e0fn';\nimport e1fn from '../common/e1fn';\nimport e2fn from '../common/e2fn';\nimport e3fn from '../common/e3fn';\nimport msfnz from '../common/msfnz';\nimport mlfn from '../common/mlfn';\nimport adjust_lon from '../common/adjust_lon';\nimport adjust_lat from '../common/adjust_lat';\nimport imlfn from '../common/imlfn';\nimport { EPSLN } from '../constants/values';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} temp\n * @property {number} es\n * @property {number} e\n * @property {number} e0\n * @property {number} e1\n * @property {number} e2\n * @property {number} e3\n * @property {number} sin_phi\n * @property {number} cos_phi\n * @property {number} ms1\n * @property {number} ml1\n * @property {number} ms2\n * @property {number} ml2\n * @property {number} ns\n * @property {number} g\n * @property {number} ml0\n * @property {number} rh\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  /* Place parameters in static storage for common use\n      ------------------------------------------------- */\n  // Standard Parallels cannot be equal and on opposite sides of the equator\n  if (Math.abs(this.lat1 + this.lat2) < EPSLN) {\n    return;\n  }\n  this.lat2 = this.lat2 || this.lat1;\n  this.temp = this.b / this.a;\n  this.es = 1 - Math.pow(this.temp, 2);\n  this.e = Math.sqrt(this.es);\n  this.e0 = e0fn(this.es);\n  this.e1 = e1fn(this.es);\n  this.e2 = e2fn(this.es);\n  this.e3 = e3fn(this.es);\n\n  this.sin_phi = Math.sin(this.lat1);\n  this.cos_phi = Math.cos(this.lat1);\n\n  this.ms1 = msfnz(this.e, this.sin_phi, this.cos_phi);\n  this.ml1 = mlfn(this.e0, this.e1, this.e2, this.e3, this.lat1);\n\n  if (Math.abs(this.lat1 - this.lat2) < EPSLN) {\n    this.ns = this.sin_phi;\n  } else {\n    this.sin_phi = Math.sin(this.lat2);\n    this.cos_phi = Math.cos(this.lat2);\n    this.ms2 = msfnz(this.e, this.sin_phi, this.cos_phi);\n    this.ml2 = mlfn(this.e0, this.e1, this.e2, this.e3, this.lat2);\n    this.ns = (this.ms1 - this.ms2) / (this.ml2 - this.ml1);\n  }\n  this.g = this.ml1 + this.ms1 / this.ns;\n  this.ml0 = mlfn(this.e0, this.e1, this.e2, this.e3, this.lat0);\n  this.rh = this.a * (this.g - this.ml0);\n}\n\n/* Equidistant Conic forward equations--mapping lat,long to x,y\n  ----------------------------------------------------------- */\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n  var rh1;\n\n  /* Forward equations\n      ----------------- */\n  if (this.sphere) {\n    rh1 = this.a * (this.g - lat);\n  } else {\n    var ml = mlfn(this.e0, this.e1, this.e2, this.e3, lat);\n    rh1 = this.a * (this.g - ml);\n  }\n  var theta = this.ns * adjust_lon(lon - this.long0);\n  var x = this.x0 + rh1 * Math.sin(theta);\n  var y = this.y0 + this.rh - rh1 * Math.cos(theta);\n  p.x = x;\n  p.y = y;\n  return p;\n}\n\n/* Inverse equations\n  ----------------- */\nexport function inverse(p) {\n  p.x -= this.x0;\n  p.y = this.rh - p.y + this.y0;\n  var con, rh1, lat, lon;\n  if (this.ns >= 0) {\n    rh1 = Math.sqrt(p.x * p.x + p.y * p.y);\n    con = 1;\n  } else {\n    rh1 = -Math.sqrt(p.x * p.x + p.y * p.y);\n    con = -1;\n  }\n  var theta = 0;\n  if (rh1 !== 0) {\n    theta = Math.atan2(con * p.x, con * p.y);\n  }\n\n  if (this.sphere) {\n    lon = adjust_lon(this.long0 + theta / this.ns);\n    lat = adjust_lat(this.g - rh1 / this.a);\n    p.x = lon;\n    p.y = lat;\n    return p;\n  } else {\n    var ml = this.g - rh1 / this.a;\n    lat = imlfn(ml, this.e0, this.e1, this.e2, this.e3);\n    lon = adjust_lon(this.long0 + theta / this.ns);\n    p.x = lon;\n    p.y = lat;\n    return p;\n  }\n}\n\nexport var names = ['Equidistant_Conic', 'eqdc'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import adjust_lon from '../common/adjust_lon';\n\nimport { HALF_PI, EPSLN } from '../constants/values';\n\nimport asinz from '../common/asinz';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} R - Radius of the Earth\n */\n\n/**\n * Initialize the Van Der Grinten projection\n * @this {import('../defs.js').ProjectionDefinition & LocalThis}\n */\nexport function init() {\n  // this.R = 6370997; //Radius of earth\n  this.R = this.a;\n}\n\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n\n  /* Forward equations\n    ----------------- */\n  var dlon = adjust_lon(lon - this.long0);\n  var x, y;\n\n  if (Math.abs(lat) <= EPSLN) {\n    x = this.x0 + this.R * dlon;\n    y = this.y0;\n  }\n  var theta = asinz(2 * Math.abs(lat / Math.PI));\n  if ((Math.abs(dlon) <= EPSLN) || (Math.abs(Math.abs(lat) - HALF_PI) <= EPSLN)) {\n    x = this.x0;\n    if (lat >= 0) {\n      y = this.y0 + Math.PI * this.R * Math.tan(0.5 * theta);\n    } else {\n      y = this.y0 + Math.PI * this.R * -Math.tan(0.5 * theta);\n    }\n    //  return(OK);\n  }\n  var al = 0.5 * Math.abs((Math.PI / dlon) - (dlon / Math.PI));\n  var asq = al * al;\n  var sinth = Math.sin(theta);\n  var costh = Math.cos(theta);\n\n  var g = costh / (sinth + costh - 1);\n  var gsq = g * g;\n  var m = g * (2 / sinth - 1);\n  var msq = m * m;\n  var con = Math.PI * this.R * (al * (g - msq) + Math.sqrt(asq * (g - msq) * (g - msq) - (msq + asq) * (gsq - msq))) / (msq + asq);\n  if (dlon < 0) {\n    con = -con;\n  }\n  x = this.x0 + con;\n  // con = Math.abs(con / (Math.PI * this.R));\n  var q = asq + g;\n  con = Math.PI * this.R * (m * q - al * Math.sqrt((msq + asq) * (asq + 1) - q * q)) / (msq + asq);\n  if (lat >= 0) {\n    // y = this.y0 + Math.PI * this.R * Math.sqrt(1 - con * con - 2 * al * con);\n    y = this.y0 + con;\n  } else {\n    // y = this.y0 - Math.PI * this.R * Math.sqrt(1 - con * con - 2 * al * con);\n    y = this.y0 - con;\n  }\n  p.x = x;\n  p.y = y;\n  return p;\n}\n\n/* Van Der Grinten inverse equations--mapping x,y to lat/long\n  --------------------------------------------------------- */\nexport function inverse(p) {\n  var lon, lat;\n  var xx, yy, xys, c1, c2, c3;\n  var a1;\n  var m1;\n  var con;\n  var th1;\n  var d;\n\n  /* inverse equations\n    ----------------- */\n  p.x -= this.x0;\n  p.y -= this.y0;\n  con = Math.PI * this.R;\n  xx = p.x / con;\n  yy = p.y / con;\n  xys = xx * xx + yy * yy;\n  c1 = -Math.abs(yy) * (1 + xys);\n  c2 = c1 - 2 * yy * yy + xx * xx;\n  c3 = -2 * c1 + 1 + 2 * yy * yy + xys * xys;\n  d = yy * yy / c3 + (2 * c2 * c2 * c2 / c3 / c3 / c3 - 9 * c1 * c2 / c3 / c3) / 27;\n  a1 = (c1 - c2 * c2 / 3 / c3) / c3;\n  m1 = 2 * Math.sqrt(-a1 / 3);\n  con = ((3 * d) / a1) / m1;\n  if (Math.abs(con) > 1) {\n    if (con >= 0) {\n      con = 1;\n    } else {\n      con = -1;\n    }\n  }\n  th1 = Math.acos(con) / 3;\n  if (p.y >= 0) {\n    lat = (-m1 * Math.cos(th1 + Math.PI / 3) - c2 / 3 / c3) * Math.PI;\n  } else {\n    lat = -(-m1 * Math.cos(th1 + Math.PI / 3) - c2 / 3 / c3) * Math.PI;\n  }\n\n  if (Math.abs(xx) < EPSLN) {\n    lon = this.long0;\n  } else {\n    lon = adjust_lon(this.long0 + Math.PI * (xys - 1 + Math.sqrt(1 + 2 * (xx * xx - yy * yy) + xys * xys)) / 2 / xx);\n  }\n\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = ['Van_der_Grinten_I', 'VanDerGrinten', 'Van_der_Grinten', 'vandg'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "/**\n * Calculates the inverse geodesic problem using <PERSON><PERSON>'s formulae.\n * Computes the forward azimuth and ellipsoidal distance between two points\n * specified by latitude and longitude on the surface of an ellipsoid.\n *\n * @param {number} lat1 Latitude of the first point in radians.\n * @param {number} lon1 Longitude of the first point in radians.\n * @param {number} lat2 Latitude of the second point in radians.\n * @param {number} lon2 Longitude of the second point in radians.\n * @param {number} a Semi-major axis of the ellipsoid (meters).\n * @param {number} f Flattening of the ellipsoid.\n * @returns {{ azi1: number, s12: number }} An object containing:\n *   - azi1: Forward azimuth from the first point to the second point (radians).\n *   - s12: Ellipsoidal distance between the two points (meters).\n */\nexport function vincentyInverse(lat1, lon1, lat2, lon2, a, f) {\n  const L = lon2 - lon1;\n  const U1 = Math.atan((1 - f) * Math.tan(lat1));\n  const U2 = Math.atan((1 - f) * Math.tan(lat2));\n  const sinU1 = Math.sin(U1), cosU1 = Math.cos(U1);\n  const sinU2 = Math.sin(U2), cosU2 = Math.cos(U2);\n\n  let lambda = L, lambdaP, iterLimit = 100;\n  let sinLambda, cosLambda, sinSigma, cosSigma, sigma, sinAlpha, cos2Alpha, cos2SigmaM, C;\n  let uSq, A, B, deltaSigma, s;\n\n  do {\n    sinLambda = Math.sin(lambda);\n    cosLambda = Math.cos(lambda);\n    sinSigma = Math.sqrt(\n      (cosU2 * sinLambda) * (cosU2 * sinLambda)\n      + (cosU1 * sinU2 - sinU1 * cosU2 * cosLambda)\n      * (cosU1 * sinU2 - sinU1 * cosU2 * cosLambda)\n    );\n    if (sinSigma === 0) {\n      return { azi1: 0, s12: 0 }; // coincident points\n    }\n    cosSigma = sinU1 * sinU2 + cosU1 * cosU2 * cosLambda;\n    sigma = Math.atan2(sinSigma, cosSigma);\n    sinAlpha = cosU1 * cosU2 * sinLambda / sinSigma;\n    cos2Alpha = 1 - sinAlpha * sinAlpha;\n    cos2SigmaM = (cos2Alpha !== 0) ? (cosSigma - 2 * sinU1 * sinU2 / cos2Alpha) : 0;\n    C = f / 16 * cos2Alpha * (4 + f * (4 - 3 * cos2Alpha));\n    lambdaP = lambda;\n    lambda = L + (1 - C) * f * sinAlpha\n    * (sigma + C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));\n  } while (Math.abs(lambda - lambdaP) > 1e-12 && --iterLimit > 0);\n\n  if (iterLimit === 0) {\n    return { azi1: NaN, s12: NaN }; // formula failed to converge\n  }\n\n  uSq = cos2Alpha * (a * a - (a * (1 - f)) * (a * (1 - f))) / ((a * (1 - f)) * (a * (1 - f)));\n  A = 1 + uSq / 16384 * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));\n  B = uSq / 1024 * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));\n  deltaSigma = B * sinSigma * (cos2SigmaM + B / 4 * (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)\n    - B / 6 * cos2SigmaM * (-3 + 4 * sinSigma * sinSigma) * (-3 + 4 * cos2SigmaM * cos2SigmaM)));\n\n  s = (a * (1 - f)) * A * (sigma - deltaSigma);\n\n  // Forward azimuth\n  const azi1 = Math.atan2(cosU2 * sinLambda, cosU1 * sinU2 - sinU1 * cosU2 * cosLambda);\n\n  return { azi1, s12: s };\n}\n\n/**\n * Solves the direct geodetic problem using Vincenty's formulae.\n * Given a starting point, initial azimuth, and distance, computes the destination point on the ellipsoid.\n *\n * @param {number} lat1 Latitude of the starting point in radians.\n * @param {number} lon1 Longitude of the starting point in radians.\n * @param {number} azi1 Initial azimuth (forward azimuth) in radians.\n * @param {number} s12 Distance to travel from the starting point in meters.\n * @param {number} a Semi-major axis of the ellipsoid in meters.\n * @param {number} f Flattening of the ellipsoid.\n * @returns {{lat2: number, lon2: number}} The latitude and longitude (in radians) of the destination point.\n */\nexport function vincentyDirect(lat1, lon1, azi1, s12, a, f) {\n  const U1 = Math.atan((1 - f) * Math.tan(lat1));\n  const sinU1 = Math.sin(U1), cosU1 = Math.cos(U1);\n  const sinAlpha1 = Math.sin(azi1), cosAlpha1 = Math.cos(azi1);\n\n  const sigma1 = Math.atan2(sinU1, cosU1 * cosAlpha1);\n  const sinAlpha = cosU1 * sinAlpha1;\n  const cos2Alpha = 1 - sinAlpha * sinAlpha;\n  const uSq = cos2Alpha * (a * a - (a * (1 - f)) * (a * (1 - f))) / ((a * (1 - f)) * (a * (1 - f)));\n  const A = 1 + uSq / 16384 * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));\n  const B = uSq / 1024 * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));\n\n  let sigma = s12 / ((a * (1 - f)) * A), sigmaP, iterLimit = 100;\n  let cos2SigmaM, sinSigma, cosSigma, deltaSigma;\n\n  do {\n    cos2SigmaM = Math.cos(2 * sigma1 + sigma);\n    sinSigma = Math.sin(sigma);\n    cosSigma = Math.cos(sigma);\n    deltaSigma = B * sinSigma * (cos2SigmaM + B / 4 * (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)\n      - B / 6 * cos2SigmaM * (-3 + 4 * sinSigma * sinSigma) * (-3 + 4 * cos2SigmaM * cos2SigmaM)));\n    sigmaP = sigma;\n    sigma = s12 / ((a * (1 - f)) * A) + deltaSigma;\n  } while (Math.abs(sigma - sigmaP) > 1e-12 && --iterLimit > 0);\n\n  if (iterLimit === 0) {\n    return { lat2: NaN, lon2: NaN };\n  }\n\n  const tmp = sinU1 * sinSigma - cosU1 * cosSigma * cosAlpha1;\n  const lat2 = Math.atan2(\n    sinU1 * cosSigma + cosU1 * sinSigma * cosAlpha1,\n    (1 - f) * Math.sqrt(sinAlpha * sinAlpha + tmp * tmp)\n  );\n  const lambda = Math.atan2(\n    sinSigma * sinAlpha1,\n    cosU1 * cosSigma - sinU1 * sinSigma * cosAlpha1\n  );\n  const C = f / 16 * cos2Alpha * (4 + f * (4 - 3 * cos2Alpha));\n  const L = lambda - (1 - C) * f * sinAlpha\n    * (sigma + C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));\n  const lon2 = lon1 + L;\n\n  return { lat2, lon2 };\n}\n", "import adjust_lon from '../common/adjust_lon';\nimport { HALF_PI, EPSLN } from '../constants/values';\nimport mlfn from '../common/mlfn';\nimport e0fn from '../common/e0fn';\nimport e1fn from '../common/e1fn';\nimport e2fn from '../common/e2fn';\nimport e3fn from '../common/e3fn';\nimport asinz from '../common/asinz';\nimport imlfn from '../common/imlfn';\nimport { vincentyDirect, vincentyInverse } from '../common/vincenty';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} es\n * @property {number} sin_p12\n * @property {number} cos_p12\n * @property {number} a\n * @property {number} f\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  this.sin_p12 = Math.sin(this.lat0);\n  this.cos_p12 = Math.cos(this.lat0);\n  // flattening for ellipsoid\n  this.f = this.es / (1 + Math.sqrt(1 - this.es));\n}\n\nexport function forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n  var sinphi = Math.sin(p.y);\n  var cosphi = Math.cos(p.y);\n  var dlon = adjust_lon(lon - this.long0);\n  var e0, e1, e2, e3, Mlp, Ml, c, kp, cos_c, vars, azi1;\n  if (this.sphere) {\n    if (Math.abs(this.sin_p12 - 1) <= EPSLN) {\n      // North Pole case\n      p.x = this.x0 + this.a * (HALF_PI - lat) * Math.sin(dlon);\n      p.y = this.y0 - this.a * (HALF_PI - lat) * Math.cos(dlon);\n      return p;\n    } else if (Math.abs(this.sin_p12 + 1) <= EPSLN) {\n      // South Pole case\n      p.x = this.x0 + this.a * (HALF_PI + lat) * Math.sin(dlon);\n      p.y = this.y0 + this.a * (HALF_PI + lat) * Math.cos(dlon);\n      return p;\n    } else {\n      // default case\n      cos_c = this.sin_p12 * sinphi + this.cos_p12 * cosphi * Math.cos(dlon);\n      c = Math.acos(cos_c);\n      kp = c ? c / Math.sin(c) : 1;\n      p.x = this.x0 + this.a * kp * cosphi * Math.sin(dlon);\n      p.y = this.y0 + this.a * kp * (this.cos_p12 * sinphi - this.sin_p12 * cosphi * Math.cos(dlon));\n      return p;\n    }\n  } else {\n    e0 = e0fn(this.es);\n    e1 = e1fn(this.es);\n    e2 = e2fn(this.es);\n    e3 = e3fn(this.es);\n    if (Math.abs(this.sin_p12 - 1) <= EPSLN) {\n      // North Pole case\n      Mlp = this.a * mlfn(e0, e1, e2, e3, HALF_PI);\n      Ml = this.a * mlfn(e0, e1, e2, e3, lat);\n      p.x = this.x0 + (Mlp - Ml) * Math.sin(dlon);\n      p.y = this.y0 - (Mlp - Ml) * Math.cos(dlon);\n      return p;\n    } else if (Math.abs(this.sin_p12 + 1) <= EPSLN) {\n      // South Pole case\n      Mlp = this.a * mlfn(e0, e1, e2, e3, HALF_PI);\n      Ml = this.a * mlfn(e0, e1, e2, e3, lat);\n      p.x = this.x0 + (Mlp + Ml) * Math.sin(dlon);\n      p.y = this.y0 + (Mlp + Ml) * Math.cos(dlon);\n      return p;\n    } else {\n      // Default case\n      if (Math.abs(lon) < EPSLN && Math.abs(lat - this.lat0) < EPSLN) {\n        p.x = p.y = 0;\n        return p;\n      }\n      vars = vincentyInverse(this.lat0, this.long0, lat, lon, this.a, this.f);\n      azi1 = vars.azi1;\n      p.x = vars.s12 * Math.sin(azi1);\n      p.y = vars.s12 * Math.cos(azi1);\n      return p;\n    }\n  }\n}\n\nexport function inverse(p) {\n  p.x -= this.x0;\n  p.y -= this.y0;\n  var rh, z, sinz, cosz, lon, lat, con, e0, e1, e2, e3, Mlp, M, azi1, s12, vars;\n  if (this.sphere) {\n    rh = Math.sqrt(p.x * p.x + p.y * p.y);\n    if (rh > (2 * HALF_PI * this.a)) {\n      return;\n    }\n    z = rh / this.a;\n\n    sinz = Math.sin(z);\n    cosz = Math.cos(z);\n\n    lon = this.long0;\n    if (Math.abs(rh) <= EPSLN) {\n      lat = this.lat0;\n    } else {\n      lat = asinz(cosz * this.sin_p12 + (p.y * sinz * this.cos_p12) / rh);\n      con = Math.abs(this.lat0) - HALF_PI;\n      if (Math.abs(con) <= EPSLN) {\n        if (this.lat0 >= 0) {\n          lon = adjust_lon(this.long0 + Math.atan2(p.x, -p.y));\n        } else {\n          lon = adjust_lon(this.long0 - Math.atan2(-p.x, p.y));\n        }\n      } else {\n        lon = adjust_lon(this.long0 + Math.atan2(p.x * sinz, rh * this.cos_p12 * cosz - p.y * this.sin_p12 * sinz));\n      }\n    }\n\n    p.x = lon;\n    p.y = lat;\n    return p;\n  } else {\n    e0 = e0fn(this.es);\n    e1 = e1fn(this.es);\n    e2 = e2fn(this.es);\n    e3 = e3fn(this.es);\n    if (Math.abs(this.sin_p12 - 1) <= EPSLN) {\n      // North pole case\n      Mlp = this.a * mlfn(e0, e1, e2, e3, HALF_PI);\n      rh = Math.sqrt(p.x * p.x + p.y * p.y);\n      M = Mlp - rh;\n      lat = imlfn(M / this.a, e0, e1, e2, e3);\n      lon = adjust_lon(this.long0 + Math.atan2(p.x, -1 * p.y));\n      p.x = lon;\n      p.y = lat;\n      return p;\n    } else if (Math.abs(this.sin_p12 + 1) <= EPSLN) {\n      // South pole case\n      Mlp = this.a * mlfn(e0, e1, e2, e3, HALF_PI);\n      rh = Math.sqrt(p.x * p.x + p.y * p.y);\n      M = rh - Mlp;\n\n      lat = imlfn(M / this.a, e0, e1, e2, e3);\n      lon = adjust_lon(this.long0 + Math.atan2(p.x, p.y));\n      p.x = lon;\n      p.y = lat;\n      return p;\n    } else {\n      // default case\n      azi1 = Math.atan2(p.x, p.y);\n      s12 = Math.sqrt(p.x * p.x + p.y * p.y);\n      vars = vincentyDirect(this.lat0, this.long0, azi1, s12, this.a, this.f);\n\n      p.x = vars.lon2;\n      p.y = vars.lat2;\n      return p;\n    }\n  }\n}\n\nexport var names = ['Azimuthal_Equidistant', 'aeqd'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import adjust_lon from '../common/adjust_lon';\nimport asinz from '../common/asinz';\nimport { EPSLN, HALF_PI } from '../constants/values';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} sin_p14\n * @property {number} cos_p14\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  // double temp;      /* temporary variable    */\n\n  /* Place parameters in static storage for common use\n      ------------------------------------------------- */\n  this.sin_p14 = Math.sin(this.lat0);\n  this.cos_p14 = Math.cos(this.lat0);\n}\n\n/* Orthographic forward equations--mapping lat,long to x,y\n    --------------------------------------------------- */\nexport function forward(p) {\n  var sinphi, cosphi; /* sin and cos value        */\n  var dlon; /* delta longitude value      */\n  var coslon; /* cos of longitude        */\n  var ksp; /* scale factor          */\n  var g, x, y;\n  var lon = p.x;\n  var lat = p.y;\n  /* Forward equations\n      ----------------- */\n  dlon = adjust_lon(lon - this.long0);\n\n  sinphi = Math.sin(lat);\n  cosphi = Math.cos(lat);\n\n  coslon = Math.cos(dlon);\n  g = this.sin_p14 * sinphi + this.cos_p14 * cosphi * coslon;\n  ksp = 1;\n  if ((g > 0) || (Math.abs(g) <= EPSLN)) {\n    x = this.a * ksp * cosphi * Math.sin(dlon);\n    y = this.y0 + this.a * ksp * (this.cos_p14 * sinphi - this.sin_p14 * cosphi * coslon);\n  }\n  p.x = x;\n  p.y = y;\n  return p;\n}\n\nexport function inverse(p) {\n  var rh; /* height above ellipsoid      */\n  var z; /* angle          */\n  var sinz, cosz; /* sin of z and cos of z      */\n  var con;\n  var lon, lat;\n  /* Inverse equations\n      ----------------- */\n  p.x -= this.x0;\n  p.y -= this.y0;\n  rh = Math.sqrt(p.x * p.x + p.y * p.y);\n  z = asinz(rh / this.a);\n\n  sinz = Math.sin(z);\n  cosz = Math.cos(z);\n\n  lon = this.long0;\n  if (Math.abs(rh) <= EPSLN) {\n    lat = this.lat0;\n    p.x = lon;\n    p.y = lat;\n    return p;\n  }\n  lat = asinz(cosz * this.sin_p14 + (p.y * sinz * this.cos_p14) / rh);\n  con = Math.abs(this.lat0) - HALF_PI;\n  if (Math.abs(con) <= EPSLN) {\n    if (this.lat0 >= 0) {\n      lon = adjust_lon(this.long0 + Math.atan2(p.x, -p.y));\n    } else {\n      lon = adjust_lon(this.long0 - Math.atan2(-p.x, p.y));\n    }\n    p.x = lon;\n    p.y = lat;\n    return p;\n  }\n  lon = adjust_lon(this.long0 + Math.atan2((p.x * sinz), rh * this.cos_p14 * cosz - p.y * this.sin_p14 * sinz));\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = ['ortho'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "// QSC projection rewritten from the original PROJ4\n// https://github.com/OSGeo/proj.4/blob/master/src/PJ_qsc.c\n\nimport { EPSLN, TWO_PI, SPI, HALF_PI, FORTPI } from '../constants/values';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} face\n * @property {number} x0\n * @property {number} y0\n * @property {number} es\n * @property {number} one_minus_f\n * @property {number} one_minus_f_squared\n */\n\n/* constants */\nvar FACE_ENUM = {\n  FRONT: 1,\n  RIGHT: 2,\n  BACK: 3,\n  LEFT: 4,\n  TOP: 5,\n  BOTTOM: 6\n};\n\nvar AREA_ENUM = {\n  AREA_0: 1,\n  AREA_1: 2,\n  AREA_2: 3,\n  AREA_3: 4\n};\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  this.x0 = this.x0 || 0;\n  this.y0 = this.y0 || 0;\n  this.lat0 = this.lat0 || 0;\n  this.long0 = this.long0 || 0;\n  this.lat_ts = this.lat_ts || 0;\n  this.title = this.title || 'Quadrilateralized Spherical Cube';\n\n  /* Determine the cube face from the center of projection. */\n  if (this.lat0 >= HALF_PI - FORTPI / 2.0) {\n    this.face = FACE_ENUM.TOP;\n  } else if (this.lat0 <= -(HALF_PI - FORTPI / 2.0)) {\n    this.face = FACE_ENUM.BOTTOM;\n  } else if (Math.abs(this.long0) <= FORTPI) {\n    this.face = FACE_ENUM.FRONT;\n  } else if (Math.abs(this.long0) <= HALF_PI + FORTPI) {\n    this.face = this.long0 > 0.0 ? FACE_ENUM.RIGHT : FACE_ENUM.LEFT;\n  } else {\n    this.face = FACE_ENUM.BACK;\n  }\n\n  /* Fill in useful values for the ellipsoid <-> sphere shift\n   * described in [LK12]. */\n  if (this.es !== 0) {\n    this.one_minus_f = 1 - (this.a - this.b) / this.a;\n    this.one_minus_f_squared = this.one_minus_f * this.one_minus_f;\n  }\n}\n\n// QSC forward equations--mapping lat,long to x,y\n// -----------------------------------------------------------------\nexport function forward(p) {\n  var xy = { x: 0, y: 0 };\n  var lat, lon;\n  var theta, phi;\n  var t, mu;\n  /* nu; */\n  var area = { value: 0 };\n\n  // move lon according to projection's lon\n  p.x -= this.long0;\n\n  /* Convert the geodetic latitude to a geocentric latitude.\n   * This corresponds to the shift from the ellipsoid to the sphere\n   * described in [LK12]. */\n  if (this.es !== 0) { // if (P->es != 0) {\n    lat = Math.atan(this.one_minus_f_squared * Math.tan(p.y));\n  } else {\n    lat = p.y;\n  }\n\n  /* Convert the input lat, lon into theta, phi as used by QSC.\n   * This depends on the cube face and the area on it.\n   * For the top and bottom face, we can compute theta and phi\n   * directly from phi, lam. For the other faces, we must use\n   * unit sphere cartesian coordinates as an intermediate step. */\n  lon = p.x; // lon = lp.lam;\n  if (this.face === FACE_ENUM.TOP) {\n    phi = HALF_PI - lat;\n    if (lon >= FORTPI && lon <= HALF_PI + FORTPI) {\n      area.value = AREA_ENUM.AREA_0;\n      theta = lon - HALF_PI;\n    } else if (lon > HALF_PI + FORTPI || lon <= -(HALF_PI + FORTPI)) {\n      area.value = AREA_ENUM.AREA_1;\n      theta = (lon > 0.0 ? lon - SPI : lon + SPI);\n    } else if (lon > -(HALF_PI + FORTPI) && lon <= -FORTPI) {\n      area.value = AREA_ENUM.AREA_2;\n      theta = lon + HALF_PI;\n    } else {\n      area.value = AREA_ENUM.AREA_3;\n      theta = lon;\n    }\n  } else if (this.face === FACE_ENUM.BOTTOM) {\n    phi = HALF_PI + lat;\n    if (lon >= FORTPI && lon <= HALF_PI + FORTPI) {\n      area.value = AREA_ENUM.AREA_0;\n      theta = -lon + HALF_PI;\n    } else if (lon < FORTPI && lon >= -FORTPI) {\n      area.value = AREA_ENUM.AREA_1;\n      theta = -lon;\n    } else if (lon < -FORTPI && lon >= -(HALF_PI + FORTPI)) {\n      area.value = AREA_ENUM.AREA_2;\n      theta = -lon - HALF_PI;\n    } else {\n      area.value = AREA_ENUM.AREA_3;\n      theta = (lon > 0.0 ? -lon + SPI : -lon - SPI);\n    }\n  } else {\n    var q, r, s;\n    var sinlat, coslat;\n    var sinlon, coslon;\n\n    if (this.face === FACE_ENUM.RIGHT) {\n      lon = qsc_shift_lon_origin(lon, +HALF_PI);\n    } else if (this.face === FACE_ENUM.BACK) {\n      lon = qsc_shift_lon_origin(lon, +SPI);\n    } else if (this.face === FACE_ENUM.LEFT) {\n      lon = qsc_shift_lon_origin(lon, -HALF_PI);\n    }\n    sinlat = Math.sin(lat);\n    coslat = Math.cos(lat);\n    sinlon = Math.sin(lon);\n    coslon = Math.cos(lon);\n    q = coslat * coslon;\n    r = coslat * sinlon;\n    s = sinlat;\n\n    if (this.face === FACE_ENUM.FRONT) {\n      phi = Math.acos(q);\n      theta = qsc_fwd_equat_face_theta(phi, s, r, area);\n    } else if (this.face === FACE_ENUM.RIGHT) {\n      phi = Math.acos(r);\n      theta = qsc_fwd_equat_face_theta(phi, s, -q, area);\n    } else if (this.face === FACE_ENUM.BACK) {\n      phi = Math.acos(-q);\n      theta = qsc_fwd_equat_face_theta(phi, s, -r, area);\n    } else if (this.face === FACE_ENUM.LEFT) {\n      phi = Math.acos(-r);\n      theta = qsc_fwd_equat_face_theta(phi, s, q, area);\n    } else {\n      /* Impossible */\n      phi = theta = 0;\n      area.value = AREA_ENUM.AREA_0;\n    }\n  }\n\n  /* Compute mu and nu for the area of definition.\n   * For mu, see Eq. (3-21) in [OL76], but note the typos:\n   * compare with Eq. (3-14). For nu, see Eq. (3-38). */\n  mu = Math.atan((12 / SPI) * (theta + Math.acos(Math.sin(theta) * Math.cos(FORTPI)) - HALF_PI));\n  t = Math.sqrt((1 - Math.cos(phi)) / (Math.cos(mu) * Math.cos(mu)) / (1 - Math.cos(Math.atan(1 / Math.cos(theta)))));\n\n  /* Apply the result to the real area. */\n  if (area.value === AREA_ENUM.AREA_1) {\n    mu += HALF_PI;\n  } else if (area.value === AREA_ENUM.AREA_2) {\n    mu += SPI;\n  } else if (area.value === AREA_ENUM.AREA_3) {\n    mu += 1.5 * SPI;\n  }\n\n  /* Now compute x, y from mu and nu */\n  xy.x = t * Math.cos(mu);\n  xy.y = t * Math.sin(mu);\n  xy.x = xy.x * this.a + this.x0;\n  xy.y = xy.y * this.a + this.y0;\n\n  p.x = xy.x;\n  p.y = xy.y;\n  return p;\n}\n\n// QSC inverse equations--mapping x,y to lat/long\n// -----------------------------------------------------------------\nexport function inverse(p) {\n  var lp = { lam: 0, phi: 0 };\n  var mu, nu, cosmu, tannu;\n  var tantheta, theta, cosphi, phi;\n  var t;\n  var area = { value: 0 };\n\n  /* de-offset */\n  p.x = (p.x - this.x0) / this.a;\n  p.y = (p.y - this.y0) / this.a;\n\n  /* Convert the input x, y to the mu and nu angles as used by QSC.\n   * This depends on the area of the cube face. */\n  nu = Math.atan(Math.sqrt(p.x * p.x + p.y * p.y));\n  mu = Math.atan2(p.y, p.x);\n  if (p.x >= 0.0 && p.x >= Math.abs(p.y)) {\n    area.value = AREA_ENUM.AREA_0;\n  } else if (p.y >= 0.0 && p.y >= Math.abs(p.x)) {\n    area.value = AREA_ENUM.AREA_1;\n    mu -= HALF_PI;\n  } else if (p.x < 0.0 && -p.x >= Math.abs(p.y)) {\n    area.value = AREA_ENUM.AREA_2;\n    mu = (mu < 0.0 ? mu + SPI : mu - SPI);\n  } else {\n    area.value = AREA_ENUM.AREA_3;\n    mu += HALF_PI;\n  }\n\n  /* Compute phi and theta for the area of definition.\n   * The inverse projection is not described in the original paper, but some\n   * good hints can be found here (as of 2011-12-14):\n   * http://fits.gsfc.nasa.gov/fitsbits/saf.93/saf.9302\n   * (search for \"Message-Id: <9302181759.AA25477 at fits.cv.nrao.edu>\") */\n  t = (SPI / 12) * Math.tan(mu);\n  tantheta = Math.sin(t) / (Math.cos(t) - (1 / Math.sqrt(2)));\n  theta = Math.atan(tantheta);\n  cosmu = Math.cos(mu);\n  tannu = Math.tan(nu);\n  cosphi = 1 - cosmu * cosmu * tannu * tannu * (1 - Math.cos(Math.atan(1 / Math.cos(theta))));\n  if (cosphi < -1) {\n    cosphi = -1;\n  } else if (cosphi > +1) {\n    cosphi = +1;\n  }\n\n  /* Apply the result to the real area on the cube face.\n   * For the top and bottom face, we can compute phi and lam directly.\n   * For the other faces, we must use unit sphere cartesian coordinates\n   * as an intermediate step. */\n  if (this.face === FACE_ENUM.TOP) {\n    phi = Math.acos(cosphi);\n    lp.phi = HALF_PI - phi;\n    if (area.value === AREA_ENUM.AREA_0) {\n      lp.lam = theta + HALF_PI;\n    } else if (area.value === AREA_ENUM.AREA_1) {\n      lp.lam = (theta < 0.0 ? theta + SPI : theta - SPI);\n    } else if (area.value === AREA_ENUM.AREA_2) {\n      lp.lam = theta - HALF_PI;\n    } else /* area.value == AREA_ENUM.AREA_3 */ {\n      lp.lam = theta;\n    }\n  } else if (this.face === FACE_ENUM.BOTTOM) {\n    phi = Math.acos(cosphi);\n    lp.phi = phi - HALF_PI;\n    if (area.value === AREA_ENUM.AREA_0) {\n      lp.lam = -theta + HALF_PI;\n    } else if (area.value === AREA_ENUM.AREA_1) {\n      lp.lam = -theta;\n    } else if (area.value === AREA_ENUM.AREA_2) {\n      lp.lam = -theta - HALF_PI;\n    } else /* area.value == AREA_ENUM.AREA_3 */ {\n      lp.lam = (theta < 0.0 ? -theta - SPI : -theta + SPI);\n    }\n  } else {\n    /* Compute phi and lam via cartesian unit sphere coordinates. */\n    var q, r, s;\n    q = cosphi;\n    t = q * q;\n    if (t >= 1) {\n      s = 0;\n    } else {\n      s = Math.sqrt(1 - t) * Math.sin(theta);\n    }\n    t += s * s;\n    if (t >= 1) {\n      r = 0;\n    } else {\n      r = Math.sqrt(1 - t);\n    }\n    /* Rotate q,r,s into the correct area. */\n    if (area.value === AREA_ENUM.AREA_1) {\n      t = r;\n      r = -s;\n      s = t;\n    } else if (area.value === AREA_ENUM.AREA_2) {\n      r = -r;\n      s = -s;\n    } else if (area.value === AREA_ENUM.AREA_3) {\n      t = r;\n      r = s;\n      s = -t;\n    }\n    /* Rotate q,r,s into the correct cube face. */\n    if (this.face === FACE_ENUM.RIGHT) {\n      t = q;\n      q = -r;\n      r = t;\n    } else if (this.face === FACE_ENUM.BACK) {\n      q = -q;\n      r = -r;\n    } else if (this.face === FACE_ENUM.LEFT) {\n      t = q;\n      q = r;\n      r = -t;\n    }\n    /* Now compute phi and lam from the unit sphere coordinates. */\n    lp.phi = Math.acos(-s) - HALF_PI;\n    lp.lam = Math.atan2(r, q);\n    if (this.face === FACE_ENUM.RIGHT) {\n      lp.lam = qsc_shift_lon_origin(lp.lam, -HALF_PI);\n    } else if (this.face === FACE_ENUM.BACK) {\n      lp.lam = qsc_shift_lon_origin(lp.lam, -SPI);\n    } else if (this.face === FACE_ENUM.LEFT) {\n      lp.lam = qsc_shift_lon_origin(lp.lam, +HALF_PI);\n    }\n  }\n\n  /* Apply the shift from the sphere to the ellipsoid as described\n   * in [LK12]. */\n  if (this.es !== 0) {\n    var invert_sign;\n    var tanphi, xa;\n    invert_sign = (lp.phi < 0 ? 1 : 0);\n    tanphi = Math.tan(lp.phi);\n    xa = this.b / Math.sqrt(tanphi * tanphi + this.one_minus_f_squared);\n    lp.phi = Math.atan(Math.sqrt(this.a * this.a - xa * xa) / (this.one_minus_f * xa));\n    if (invert_sign) {\n      lp.phi = -lp.phi;\n    }\n  }\n\n  lp.lam += this.long0;\n  p.x = lp.lam;\n  p.y = lp.phi;\n  return p;\n}\n\n/* Helper function for forward projection: compute the theta angle\n * and determine the area number. */\nfunction qsc_fwd_equat_face_theta(phi, y, x, area) {\n  var theta;\n  if (phi < EPSLN) {\n    area.value = AREA_ENUM.AREA_0;\n    theta = 0.0;\n  } else {\n    theta = Math.atan2(y, x);\n    if (Math.abs(theta) <= FORTPI) {\n      area.value = AREA_ENUM.AREA_0;\n    } else if (theta > FORTPI && theta <= HALF_PI + FORTPI) {\n      area.value = AREA_ENUM.AREA_1;\n      theta -= HALF_PI;\n    } else if (theta > HALF_PI + FORTPI || theta <= -(HALF_PI + FORTPI)) {\n      area.value = AREA_ENUM.AREA_2;\n      theta = (theta >= 0.0 ? theta - SPI : theta + SPI);\n    } else {\n      area.value = AREA_ENUM.AREA_3;\n      theta += HALF_PI;\n    }\n  }\n  return theta;\n}\n\n/* Helper function: shift the longitude. */\nfunction qsc_shift_lon_origin(lon, offset) {\n  var slon = lon + offset;\n  if (slon < -SPI) {\n    slon += TWO_PI;\n  } else if (slon > +SPI) {\n    slon -= TWO_PI;\n  }\n  return slon;\n}\n\nexport var names = ['Quadrilateralized Spherical Cube', 'Quadrilateralized_Spherical_Cube', 'qsc'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "// Robinson projection\n// Based on https://github.com/OSGeo/proj.4/blob/master/src/PJ_robin.c\n// Polynomial coeficients from http://article.gmane.org/gmane.comp.gis.proj-4.devel/6039\n\nimport { HALF_PI, D2R, R2D, EPSLN } from '../constants/values';\nimport adjust_lon from '../common/adjust_lon';\n\nvar COEFS_X = [\n  [1.0000, 2.2199e-17, -7.15515e-05, 3.1103e-06],\n  [0.9986, -0.000482243, -2.4897e-05, -1.3309e-06],\n  [0.9954, -0.00083103, -4.48605e-05, -9.86701e-07],\n  [0.9900, -0.00135364, -5.9661e-05, 3.6777e-06],\n  [0.9822, -0.00167442, -4.49547e-06, -5.72411e-06],\n  [0.9730, -0.00214868, -9.03571e-05, 1.8736e-08],\n  [0.9600, -0.00305085, -9.00761e-05, 1.64917e-06],\n  [0.9427, -0.00382792, -6.53386e-05, -2.6154e-06],\n  [0.9216, -0.00467746, -0.00010457, 4.81243e-06],\n  [0.8962, -0.00536223, -3.23831e-05, -5.43432e-06],\n  [0.8679, -0.00609363, -0.000113898, 3.32484e-06],\n  [0.8350, -0.00698325, -6.40253e-05, 9.34959e-07],\n  [0.7986, -0.00755338, -5.00009e-05, 9.35324e-07],\n  [0.7597, -0.00798324, -3.5971e-05, -2.27626e-06],\n  [0.7186, -0.00851367, -7.01149e-05, -8.6303e-06],\n  [0.6732, -0.00986209, -0.000199569, 1.91974e-05],\n  [0.6213, -0.010418, 8.83923e-05, 6.24051e-06],\n  [0.5722, -0.00906601, 0.000182, 6.24051e-06],\n  [0.5322, -0.00677797, 0.000275608, 6.24051e-06]\n];\n\nvar COEFS_Y = [\n  [-5.20417e-18, 0.0124, 1.21431e-18, -8.45284e-11],\n  [0.0620, 0.0124, -1.26793e-09, 4.22642e-10],\n  [0.1240, 0.0124, 5.07171e-09, -1.60604e-09],\n  [0.1860, 0.0123999, -1.90189e-08, 6.00152e-09],\n  [0.2480, 0.0124002, 7.10039e-08, -2.24e-08],\n  [0.3100, 0.0123992, -2.64997e-07, 8.35986e-08],\n  [0.3720, 0.0124029, 9.88983e-07, -3.11994e-07],\n  [0.4340, 0.0123893, -3.69093e-06, -4.35621e-07],\n  [0.4958, 0.0123198, -1.02252e-05, -3.45523e-07],\n  [0.5571, 0.0121916, -1.54081e-05, -5.82288e-07],\n  [0.6176, 0.0119938, -2.41424e-05, -5.25327e-07],\n  [0.6769, 0.011713, -3.20223e-05, -5.16405e-07],\n  [0.7346, 0.0113541, -3.97684e-05, -6.09052e-07],\n  [0.7903, 0.0109107, -4.89042e-05, -1.04739e-06],\n  [0.8435, 0.0103431, -6.4615e-05, -1.40374e-09],\n  [0.8936, 0.00969686, -6.4636e-05, -8.547e-06],\n  [0.9394, 0.00840947, -0.000192841, -4.2106e-06],\n  [0.9761, 0.00616527, -0.000256, -4.2106e-06],\n  [1.0000, 0.00328947, -0.000319159, -4.2106e-06]\n];\n\nvar FXC = 0.8487;\nvar FYC = 1.3523;\nvar C1 = R2D / 5; // rad to 5-degree interval\nvar RC1 = 1 / C1;\nvar NODES = 18;\n\nvar poly3_val = function (coefs, x) {\n  return coefs[0] + x * (coefs[1] + x * (coefs[2] + x * coefs[3]));\n};\n\nvar poly3_der = function (coefs, x) {\n  return coefs[1] + x * (2 * coefs[2] + x * 3 * coefs[3]);\n};\n\nfunction newton_rapshon(f_df, start, max_err, iters) {\n  var x = start;\n  for (; iters; --iters) {\n    var upd = f_df(x);\n    x -= upd;\n    if (Math.abs(upd) < max_err) {\n      break;\n    }\n  }\n  return x;\n}\n\nexport function init() {\n  this.x0 = this.x0 || 0;\n  this.y0 = this.y0 || 0;\n  this.long0 = this.long0 || 0;\n  this.es = 0;\n  this.title = this.title || 'Robinson';\n}\n\nexport function forward(ll) {\n  var lon = adjust_lon(ll.x - this.long0);\n\n  var dphi = Math.abs(ll.y);\n  var i = Math.floor(dphi * C1);\n  if (i < 0) {\n    i = 0;\n  } else if (i >= NODES) {\n    i = NODES - 1;\n  }\n  dphi = R2D * (dphi - RC1 * i);\n  var xy = {\n    x: poly3_val(COEFS_X[i], dphi) * lon,\n    y: poly3_val(COEFS_Y[i], dphi)\n  };\n  if (ll.y < 0) {\n    xy.y = -xy.y;\n  }\n\n  xy.x = xy.x * this.a * FXC + this.x0;\n  xy.y = xy.y * this.a * FYC + this.y0;\n  return xy;\n}\n\nexport function inverse(xy) {\n  var ll = {\n    x: (xy.x - this.x0) / (this.a * FXC),\n    y: Math.abs(xy.y - this.y0) / (this.a * FYC)\n  };\n\n  if (ll.y >= 1) { // pathologic case\n    ll.x /= COEFS_X[NODES][0];\n    ll.y = xy.y < 0 ? -HALF_PI : HALF_PI;\n  } else {\n    // find table interval\n    var i = Math.floor(ll.y * NODES);\n    if (i < 0) {\n      i = 0;\n    } else if (i >= NODES) {\n      i = NODES - 1;\n    }\n    for (;;) {\n      if (COEFS_Y[i][0] > ll.y) {\n        --i;\n      } else if (COEFS_Y[i + 1][0] <= ll.y) {\n        ++i;\n      } else {\n        break;\n      }\n    }\n    // linear interpolation in 5 degree interval\n    var coefs = COEFS_Y[i];\n    var t = 5 * (ll.y - coefs[0]) / (COEFS_Y[i + 1][0] - coefs[0]);\n    // find t so that poly3_val(coefs, t) = ll.y\n    t = newton_rapshon(function (x) {\n      return (poly3_val(coefs, x) - ll.y) / poly3_der(coefs, x);\n    }, t, EPSLN, 100);\n\n    ll.x /= poly3_val(COEFS_X[i], t);\n    ll.y = (5 * i + t) * D2R;\n    if (xy.y < 0) {\n      ll.y = -ll.y;\n    }\n  }\n\n  ll.x = adjust_lon(ll.x + this.long0);\n  return ll;\n}\n\nexport var names = ['Robinson', 'robin'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import {\n  geodeticToGeocentric,\n  geocentricToGeodetic\n} from '../datumUtils';\n\nexport function init() {\n  this.name = 'geocent';\n}\n\nexport function forward(p) {\n  var point = geodeticToGeocentric(p, this.es, this.a);\n  return point;\n}\n\nexport function inverse(p) {\n  var point = geocentricToGeodetic(p, this.es, this.a, this.b);\n  return point;\n}\n\nexport var names = ['Geocentric', 'geocentric', 'geocent', 'Geocent'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import { D2R, HALF_PI, EPSLN } from '../constants/values';\nimport hypot from '../common/hypot';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} mode\n * @property {number} sinph0\n * @property {number} cosph0\n * @property {number} pn1\n * @property {number} h\n * @property {number} rp\n * @property {number} p\n * @property {number} h1\n * @property {number} pfact\n * @property {number} es\n * @property {number} tilt\n * @property {number} azi\n * @property {number} cg\n * @property {number} sg\n * @property {number} cw\n * @property {number} sw\n */\n\nvar mode = {\n  N_POLE: 0,\n  S_POLE: 1,\n  EQUIT: 2,\n  OBLIQ: 3\n};\n\nvar params = {\n  h: { def: 100000, num: true }, // default is Karman line, no default in PROJ.7\n  azi: { def: 0, num: true, degrees: true }, // default is North\n  tilt: { def: 0, num: true, degrees: true }, // default is Nadir\n  long0: { def: 0, num: true }, // default is Greenwich, conversion to rad is automatic\n  lat0: { def: 0, num: true } // default is Equator, conversion to rad is automatic\n};\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  Object.keys(params).forEach(function (p) {\n    if (typeof this[p] === 'undefined') {\n      this[p] = params[p].def;\n    } else if (params[p].num && isNaN(this[p])) {\n      throw new Error('Invalid parameter value, must be numeric ' + p + ' = ' + this[p]);\n    } else if (params[p].num) {\n      this[p] = parseFloat(this[p]);\n    }\n    if (params[p].degrees) {\n      this[p] = this[p] * D2R;\n    }\n  }.bind(this));\n\n  if (Math.abs((Math.abs(this.lat0) - HALF_PI)) < EPSLN) {\n    this.mode = this.lat0 < 0 ? mode.S_POLE : mode.N_POLE;\n  } else if (Math.abs(this.lat0) < EPSLN) {\n    this.mode = mode.EQUIT;\n  } else {\n    this.mode = mode.OBLIQ;\n    this.sinph0 = Math.sin(this.lat0);\n    this.cosph0 = Math.cos(this.lat0);\n  }\n\n  this.pn1 = this.h / this.a; // Normalize relative to the Earth's radius\n\n  if (this.pn1 <= 0 || this.pn1 > 1e10) {\n    throw new Error('Invalid height');\n  }\n\n  this.p = 1 + this.pn1;\n  this.rp = 1 / this.p;\n  this.h1 = 1 / this.pn1;\n  this.pfact = (this.p + 1) * this.h1;\n  this.es = 0;\n\n  var omega = this.tilt;\n  var gamma = this.azi;\n  this.cg = Math.cos(gamma);\n  this.sg = Math.sin(gamma);\n  this.cw = Math.cos(omega);\n  this.sw = Math.sin(omega);\n}\n\nexport function forward(p) {\n  p.x -= this.long0;\n  var sinphi = Math.sin(p.y);\n  var cosphi = Math.cos(p.y);\n  var coslam = Math.cos(p.x);\n  var x, y;\n  switch (this.mode) {\n    case mode.OBLIQ:\n      y = this.sinph0 * sinphi + this.cosph0 * cosphi * coslam;\n      break;\n    case mode.EQUIT:\n      y = cosphi * coslam;\n      break;\n    case mode.S_POLE:\n      y = -sinphi;\n      break;\n    case mode.N_POLE:\n      y = sinphi;\n      break;\n  }\n  y = this.pn1 / (this.p - y);\n  x = y * cosphi * Math.sin(p.x);\n\n  switch (this.mode) {\n    case mode.OBLIQ:\n      y *= this.cosph0 * sinphi - this.sinph0 * cosphi * coslam;\n      break;\n    case mode.EQUIT:\n      y *= sinphi;\n      break;\n    case mode.N_POLE:\n      y *= -(cosphi * coslam);\n      break;\n    case mode.S_POLE:\n      y *= cosphi * coslam;\n      break;\n  }\n\n  // Tilt\n  var yt, ba;\n  yt = y * this.cg + x * this.sg;\n  ba = 1 / (yt * this.sw * this.h1 + this.cw);\n  x = (x * this.cg - y * this.sg) * this.cw * ba;\n  y = yt * ba;\n\n  p.x = x * this.a;\n  p.y = y * this.a;\n  return p;\n}\n\nexport function inverse(p) {\n  p.x /= this.a;\n  p.y /= this.a;\n  var r = { x: p.x, y: p.y };\n\n  // Un-Tilt\n  var bm, bq, yt;\n  yt = 1 / (this.pn1 - p.y * this.sw);\n  bm = this.pn1 * p.x * yt;\n  bq = this.pn1 * p.y * this.cw * yt;\n  p.x = bm * this.cg + bq * this.sg;\n  p.y = bq * this.cg - bm * this.sg;\n\n  var rh = hypot(p.x, p.y);\n  if (Math.abs(rh) < EPSLN) {\n    r.x = 0;\n    r.y = p.y;\n  } else {\n    var cosz, sinz;\n    sinz = 1 - rh * rh * this.pfact;\n    sinz = (this.p - Math.sqrt(sinz)) / (this.pn1 / rh + rh / this.pn1);\n    cosz = Math.sqrt(1 - sinz * sinz);\n    switch (this.mode) {\n      case mode.OBLIQ:\n        r.y = Math.asin(cosz * this.sinph0 + p.y * sinz * this.cosph0 / rh);\n        p.y = (cosz - this.sinph0 * Math.sin(r.y)) * rh;\n        p.x *= sinz * this.cosph0;\n        break;\n      case mode.EQUIT:\n        r.y = Math.asin(p.y * sinz / rh);\n        p.y = cosz * rh;\n        p.x *= sinz;\n        break;\n      case mode.N_POLE:\n        r.y = Math.asin(cosz);\n        p.y = -p.y;\n        break;\n      case mode.S_POLE:\n        r.y = -Math.asin(cosz);\n        break;\n    }\n    r.x = Math.atan2(p.x, p.y);\n  }\n\n  p.x = r.x + this.long0;\n  p.y = r.y;\n  return p;\n}\n\nexport var names = ['Tilted_Perspective', 'tpers'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import hypot from '../common/hypot';\n\n/**\n * @typedef {Object} LocalThis\n * @property {1 | 0} flip_axis\n * @property {number} h\n * @property {number} radius_g_1\n * @property {number} radius_g\n * @property {number} radius_p\n * @property {number} radius_p2\n * @property {number} radius_p_inv2\n * @property {'ellipse'|'sphere'} shape\n * @property {number} C\n * @property {string} sweep\n * @property {number} es\n */\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  this.flip_axis = (this.sweep === 'x' ? 1 : 0);\n  this.h = Number(this.h);\n  this.radius_g_1 = this.h / this.a;\n\n  if (this.radius_g_1 <= 0 || this.radius_g_1 > 1e10) {\n    throw new Error();\n  }\n\n  this.radius_g = 1.0 + this.radius_g_1;\n  this.C = this.radius_g * this.radius_g - 1.0;\n\n  if (this.es !== 0.0) {\n    var one_es = 1.0 - this.es;\n    var rone_es = 1 / one_es;\n\n    this.radius_p = Math.sqrt(one_es);\n    this.radius_p2 = one_es;\n    this.radius_p_inv2 = rone_es;\n\n    this.shape = 'ellipse'; // Use as a condition in the forward and inverse functions.\n  } else {\n    this.radius_p = 1.0;\n    this.radius_p2 = 1.0;\n    this.radius_p_inv2 = 1.0;\n\n    this.shape = 'sphere'; // Use as a condition in the forward and inverse functions.\n  }\n\n  if (!this.title) {\n    this.title = 'Geostationary Satellite View';\n  }\n}\n\nfunction forward(p) {\n  var lon = p.x;\n  var lat = p.y;\n  var tmp, v_x, v_y, v_z;\n  lon = lon - this.long0;\n\n  if (this.shape === 'ellipse') {\n    lat = Math.atan(this.radius_p2 * Math.tan(lat));\n    var r = this.radius_p / hypot(this.radius_p * Math.cos(lat), Math.sin(lat));\n\n    v_x = r * Math.cos(lon) * Math.cos(lat);\n    v_y = r * Math.sin(lon) * Math.cos(lat);\n    v_z = r * Math.sin(lat);\n\n    if (((this.radius_g - v_x) * v_x - v_y * v_y - v_z * v_z * this.radius_p_inv2) < 0.0) {\n      p.x = Number.NaN;\n      p.y = Number.NaN;\n      return p;\n    }\n\n    tmp = this.radius_g - v_x;\n    if (this.flip_axis) {\n      p.x = this.radius_g_1 * Math.atan(v_y / hypot(v_z, tmp));\n      p.y = this.radius_g_1 * Math.atan(v_z / tmp);\n    } else {\n      p.x = this.radius_g_1 * Math.atan(v_y / tmp);\n      p.y = this.radius_g_1 * Math.atan(v_z / hypot(v_y, tmp));\n    }\n  } else if (this.shape === 'sphere') {\n    tmp = Math.cos(lat);\n    v_x = Math.cos(lon) * tmp;\n    v_y = Math.sin(lon) * tmp;\n    v_z = Math.sin(lat);\n    tmp = this.radius_g - v_x;\n\n    if (this.flip_axis) {\n      p.x = this.radius_g_1 * Math.atan(v_y / hypot(v_z, tmp));\n      p.y = this.radius_g_1 * Math.atan(v_z / tmp);\n    } else {\n      p.x = this.radius_g_1 * Math.atan(v_y / tmp);\n      p.y = this.radius_g_1 * Math.atan(v_z / hypot(v_y, tmp));\n    }\n  }\n  p.x = p.x * this.a;\n  p.y = p.y * this.a;\n  return p;\n}\n\nfunction inverse(p) {\n  var v_x = -1.0;\n  var v_y = 0.0;\n  var v_z = 0.0;\n  var a, b, det, k;\n\n  p.x = p.x / this.a;\n  p.y = p.y / this.a;\n\n  if (this.shape === 'ellipse') {\n    if (this.flip_axis) {\n      v_z = Math.tan(p.y / this.radius_g_1);\n      v_y = Math.tan(p.x / this.radius_g_1) * hypot(1.0, v_z);\n    } else {\n      v_y = Math.tan(p.x / this.radius_g_1);\n      v_z = Math.tan(p.y / this.radius_g_1) * hypot(1.0, v_y);\n    }\n\n    var v_zp = v_z / this.radius_p;\n    a = v_y * v_y + v_zp * v_zp + v_x * v_x;\n    b = 2 * this.radius_g * v_x;\n    det = (b * b) - 4 * a * this.C;\n\n    if (det < 0.0) {\n      p.x = Number.NaN;\n      p.y = Number.NaN;\n      return p;\n    }\n\n    k = (-b - Math.sqrt(det)) / (2.0 * a);\n    v_x = this.radius_g + k * v_x;\n    v_y *= k;\n    v_z *= k;\n\n    p.x = Math.atan2(v_y, v_x);\n    p.y = Math.atan(v_z * Math.cos(p.x) / v_x);\n    p.y = Math.atan(this.radius_p_inv2 * Math.tan(p.y));\n  } else if (this.shape === 'sphere') {\n    if (this.flip_axis) {\n      v_z = Math.tan(p.y / this.radius_g_1);\n      v_y = Math.tan(p.x / this.radius_g_1) * Math.sqrt(1.0 + v_z * v_z);\n    } else {\n      v_y = Math.tan(p.x / this.radius_g_1);\n      v_z = Math.tan(p.y / this.radius_g_1) * Math.sqrt(1.0 + v_y * v_y);\n    }\n\n    a = v_y * v_y + v_z * v_z + v_x * v_x;\n    b = 2 * this.radius_g * v_x;\n    det = (b * b) - 4 * a * this.C;\n    if (det < 0.0) {\n      p.x = Number.NaN;\n      p.y = Number.NaN;\n      return p;\n    }\n\n    k = (-b - Math.sqrt(det)) / (2.0 * a);\n    v_x = this.radius_g + k * v_x;\n    v_y *= k;\n    v_z *= k;\n\n    p.x = Math.atan2(v_y, v_x);\n    p.y = Math.atan(v_z * Math.cos(p.x) / v_x);\n  }\n  p.x = p.x + this.long0;\n  return p;\n}\n\nexport var names = ['Geostationary Satellite View', 'Geostationary_Satellite', 'geos'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "/**\n * Copyright 2018 <PERSON>, Monash University, Melbourne, Australia.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * Equal Earth is a projection inspired by the Robinson projection, but unlike\n * the <PERSON> projection retains the relative size of areas. The projection\n * was designed in 2018 by <PERSON><PERSON>, <PERSON> and <PERSON>.\n *\n * Publication:\n * <PERSON><PERSON>, <PERSON> & <PERSON> (2018). The Equal Earth map\n * projection, International Journal of Geographical Information Science,\n * DOI: 10.1080/13658816.2018.1504949\n *\n * Code released August 2018\n * Ported to JavaScript and adapted for mapshaper-proj by <PERSON> August 2018\n * Modified for proj4js by <PERSON> by <PERSON> March 2024\n */\n\nimport adjust_lon from '../common/adjust_lon';\n\nvar A1 = 1.340264,\n  A2 = -0.081106,\n  A3 = 0.000893,\n  A4 = 0.003796,\n  M = Math.sqrt(3) / 2.0;\n\nexport function init() {\n  this.es = 0;\n  this.long0 = this.long0 !== undefined ? this.long0 : 0;\n}\n\nexport function forward(p) {\n  var lam = adjust_lon(p.x - this.long0);\n  var phi = p.y;\n  var paramLat = Math.asin(M * Math.sin(phi)),\n    paramLatSq = paramLat * paramLat,\n    paramLatPow6 = paramLatSq * paramLatSq * paramLatSq;\n  p.x = lam * Math.cos(paramLat)\n    / (M * (A1 + 3 * A2 * paramLatSq + paramLatPow6 * (7 * A3 + 9 * A4 * paramLatSq)));\n  p.y = paramLat * (A1 + A2 * paramLatSq + paramLatPow6 * (A3 + A4 * paramLatSq));\n\n  p.x = this.a * p.x + this.x0;\n  p.y = this.a * p.y + this.y0;\n  return p;\n}\n\nexport function inverse(p) {\n  p.x = (p.x - this.x0) / this.a;\n  p.y = (p.y - this.y0) / this.a;\n\n  var EPS = 1e-9,\n    NITER = 12,\n    paramLat = p.y,\n    paramLatSq, paramLatPow6, fy, fpy, dlat, i;\n\n  for (i = 0; i < NITER; ++i) {\n    paramLatSq = paramLat * paramLat;\n    paramLatPow6 = paramLatSq * paramLatSq * paramLatSq;\n    fy = paramLat * (A1 + A2 * paramLatSq + paramLatPow6 * (A3 + A4 * paramLatSq)) - p.y;\n    fpy = A1 + 3 * A2 * paramLatSq + paramLatPow6 * (7 * A3 + 9 * A4 * paramLatSq);\n    paramLat -= dlat = fy / fpy;\n    if (Math.abs(dlat) < EPS) {\n      break;\n    }\n  }\n  paramLatSq = paramLat * paramLat;\n  paramLatPow6 = paramLatSq * paramLatSq * paramLatSq;\n  p.x = M * p.x * (A1 + 3 * A2 * paramLatSq + paramLatPow6 * (7 * A3 + 9 * A4 * paramLatSq))\n    / Math.cos(paramLat);\n  p.y = Math.asin(Math.sin(paramLat) / M);\n\n  p.x = adjust_lon(p.x + this.long0);\n  return p;\n}\n\nexport var names = ['eqearth', 'Equal Earth', 'Equal_Earth'];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n", "import adjust_lat from '../common/adjust_lat';\nimport adjust_lon from '../common/adjust_lon';\nimport hypot from '../common/hypot';\nimport pj_enfn from '../common/pj_enfn';\nimport pj_inv_mlfn from '../common/pj_inv_mlfn';\nimport pj_mlfn from '../common/pj_mlfn';\nimport { HALF_PI } from '../constants/values';\n\n/**\n * @typedef {Object} LocalThis\n * @property {number} phi1\n * @property {number} cphi1\n * @property {number} es\n * @property {Array<number>} en\n * @property {number} m1\n * @property {number} am1\n */\n\nvar EPS10 = 1e-10;\n\n/** @this {import('../defs.js').ProjectionDefinition & LocalThis} */\nexport function init() {\n  var c;\n\n  this.phi1 = this.lat1;\n  if (Math.abs(this.phi1) < EPS10) {\n    throw new Error();\n  }\n  if (this.es) {\n    this.en = pj_enfn(this.es);\n    this.m1 = pj_mlfn(this.phi1, this.am1 = Math.sin(this.phi1),\n      c = Math.cos(this.phi1), this.en);\n    this.am1 = c / (Math.sqrt(1 - this.es * this.am1 * this.am1) * this.am1);\n    this.inverse = e_inv;\n    this.forward = e_fwd;\n  } else {\n    if (Math.abs(this.phi1) + EPS10 >= HALF_PI) {\n      this.cphi1 = 0;\n    } else {\n      this.cphi1 = 1 / Math.tan(this.phi1);\n    }\n    this.inverse = s_inv;\n    this.forward = s_fwd;\n  }\n}\n\nfunction e_fwd(p) {\n  var lam = adjust_lon(p.x - (this.long0 || 0));\n  var phi = p.y;\n  var rh, E, c;\n  rh = this.am1 + this.m1 - pj_mlfn(phi, E = Math.sin(phi), c = Math.cos(phi), this.en);\n  E = c * lam / (rh * Math.sqrt(1 - this.es * E * E));\n  p.x = rh * Math.sin(E);\n  p.y = this.am1 - rh * Math.cos(E);\n\n  p.x = this.a * p.x + (this.x0 || 0);\n  p.y = this.a * p.y + (this.y0 || 0);\n  return p;\n}\n\nfunction e_inv(p) {\n  p.x = (p.x - (this.x0 || 0)) / this.a;\n  p.y = (p.y - (this.y0 || 0)) / this.a;\n\n  var s, rh, lam, phi;\n  rh = hypot(p.x, p.y = this.am1 - p.y);\n  phi = pj_inv_mlfn(this.am1 + this.m1 - rh, this.es, this.en);\n  if ((s = Math.abs(phi)) < HALF_PI) {\n    s = Math.sin(phi);\n    lam = rh * Math.atan2(p.x, p.y) * Math.sqrt(1 - this.es * s * s) / Math.cos(phi);\n  } else if (Math.abs(s - HALF_PI) <= EPS10) {\n    lam = 0;\n  } else {\n    throw new Error();\n  }\n  p.x = adjust_lon(lam + (this.long0 || 0));\n  p.y = adjust_lat(phi);\n  return p;\n}\n\nfunction s_fwd(p) {\n  var lam = adjust_lon(p.x - (this.long0 || 0));\n  var phi = p.y;\n  var E, rh;\n  rh = this.cphi1 + this.phi1 - phi;\n  if (Math.abs(rh) > EPS10) {\n    p.x = rh * Math.sin(E = lam * Math.cos(phi) / rh);\n    p.y = this.cphi1 - rh * Math.cos(E);\n  } else {\n    p.x = p.y = 0;\n  }\n\n  p.x = this.a * p.x + (this.x0 || 0);\n  p.y = this.a * p.y + (this.y0 || 0);\n  return p;\n}\n\nfunction s_inv(p) {\n  p.x = (p.x - (this.x0 || 0)) / this.a;\n  p.y = (p.y - (this.y0 || 0)) / this.a;\n\n  var lam, phi;\n  var rh = hypot(p.x, p.y = this.cphi1 - p.y);\n  phi = this.cphi1 + this.phi1 - rh;\n  if (Math.abs(phi) > HALF_PI) {\n    throw new Error();\n  }\n  if (Math.abs(Math.abs(phi) - HALF_PI) <= EPS10) {\n    lam = 0;\n  } else {\n    lam = rh * Math.atan2(p.x, p.y) / Math.cos(phi);\n  }\n  p.x = adjust_lon(lam + (this.long0 || 0));\n  p.y = adjust_lat(phi);\n  return p;\n}\n\nexport var names = ['bonne', 'Bonne (Werner lat_1=90)'];\nexport default {\n  init: init,\n  names: names\n};\n", "import tmerc from './lib/projections/tmerc';\nimport etmerc from './lib/projections/etmerc';\nimport utm from './lib/projections/utm';\nimport sterea from './lib/projections/sterea';\nimport stere from './lib/projections/stere';\nimport somerc from './lib/projections/somerc';\nimport omerc from './lib/projections/omerc';\nimport lcc from './lib/projections/lcc';\nimport krovak from './lib/projections/krovak';\nimport cass from './lib/projections/cass';\nimport laea from './lib/projections/laea';\nimport aea from './lib/projections/aea';\nimport gnom from './lib/projections/gnom';\nimport cea from './lib/projections/cea';\nimport eqc from './lib/projections/eqc';\nimport poly from './lib/projections/poly';\nimport nzmg from './lib/projections/nzmg';\nimport mill from './lib/projections/mill';\nimport sinu from './lib/projections/sinu';\nimport moll from './lib/projections/moll';\nimport eqdc from './lib/projections/eqdc';\nimport vandg from './lib/projections/vandg';\nimport aeqd from './lib/projections/aeqd';\nimport ortho from './lib/projections/ortho';\nimport qsc from './lib/projections/qsc';\nimport robin from './lib/projections/robin';\nimport geocent from './lib/projections/geocent';\nimport tpers from './lib/projections/tpers';\nimport geos from './lib/projections/geos';\nimport eqearth from './lib/projections/eqearth';\nimport bonne from './lib/projections/bonne';\nexport default function (proj4) {\n  proj4.Proj.projections.add(tmerc);\n  proj4.Proj.projections.add(etmerc);\n  proj4.Proj.projections.add(utm);\n  proj4.Proj.projections.add(sterea);\n  proj4.Proj.projections.add(stere);\n  proj4.Proj.projections.add(somerc);\n  proj4.Proj.projections.add(omerc);\n  proj4.Proj.projections.add(lcc);\n  proj4.Proj.projections.add(krovak);\n  proj4.Proj.projections.add(cass);\n  proj4.Proj.projections.add(laea);\n  proj4.Proj.projections.add(aea);\n  proj4.Proj.projections.add(gnom);\n  proj4.Proj.projections.add(cea);\n  proj4.Proj.projections.add(eqc);\n  proj4.Proj.projections.add(poly);\n  proj4.Proj.projections.add(nzmg);\n  proj4.Proj.projections.add(mill);\n  proj4.Proj.projections.add(sinu);\n  proj4.Proj.projections.add(moll);\n  proj4.Proj.projections.add(eqdc);\n  proj4.Proj.projections.add(vandg);\n  proj4.Proj.projections.add(aeqd);\n  proj4.Proj.projections.add(ortho);\n  proj4.Proj.projections.add(qsc);\n  proj4.Proj.projections.add(robin);\n  proj4.Proj.projections.add(geocent);\n  proj4.Proj.projections.add(tpers);\n  proj4.Proj.projections.add(geos);\n  proj4.Proj.projections.add(eqearth);\n  proj4.Proj.projections.add(bonne);\n}\n", "import core from './core';\nimport Proj from './Proj';\nimport Point from './Point';\nimport common from './common/toPoint';\nimport defs from './defs';\nimport nadgrid from './nadgrid';\nimport transform from './transform';\nimport mgrs from 'mgrs';\nimport includedProjections from '../projs';\n\n/**\n * @typedef {Object} Mgrs\n * @property {(lonlat: [number, number]) => string} forward\n * @property {(mgrsString: string) => [number, number, number, number]} inverse\n * @property {(mgrsString: string) => [number, number]} toPoint\n */\n\n/**\n * @template {import('./core').TemplateCoordinates} T\n * @type {core<T> & {defaultDatum: string, Proj: typeof Proj, WGS84: Proj, Point: typeof Point, toPoint: typeof common, defs: typeof defs, nadgrid: typeof nadgrid, transform: typeof transform, mgrs: Mgrs, version: string}}\n */\nconst proj4 = Object.assign(core, {\n  defaultDatum: 'WGS84',\n  Proj,\n  WGS84: new Proj('WGS84'),\n  Point,\n  toPoint: common,\n  defs,\n  nadgrid,\n  transform,\n  mgrs,\n  version: '__VERSION__'\n});\nincludedProjections(proj4);\nexport default proj4;\n"], "mappings": ";;;AAAe,SAAR,eAAkBA,OAAM;AAC7B,EAAAA,MAAK,aAAa,iFAAiF;AACnG,EAAAA,MAAK,aAAa,iHAAiH;AACnI,EAAAA,MAAK,aAAa,kJAAkJ;AAEpK,WAAS,IAAI,GAAG,KAAK,IAAI,EAAE,GAAG;AAC5B,IAAAA,MAAK,WAAW,QAAQ,IAAI,qBAAqB,IAAI,wBAAwB;AAC7E,IAAAA,MAAK,WAAW,QAAQ,IAAI,qBAAqB,IAAI,+BAA+B;AAAA,EACtF;AAEA,EAAAA,MAAK,QAAQA,MAAK,WAAW;AAC7B,EAAAA,MAAK,WAAW,IAAIA,MAAK,WAAW;AACpC,EAAAA,MAAK,SAASA,MAAK,WAAW;AAC9B,EAAAA,MAAK,aAAa,IAAIA,MAAK,WAAW;AACtC,EAAAA,MAAK,aAAa,IAAIA,MAAK,WAAW;AACxC;;;ACfO,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,gBAAgB;AACpB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,sBAAsB;AAC1B,IAAI,sBAAsB;AAC1B,IAAI,qBAAqB;AACzB,IAAI,aAAa;AACjB,IAAI,UAAU,KAAK,KAAK;AAExB,IAAI,QAAQ;AAEZ,IAAI,MAAM;AAEV,IAAI,MAAM;AACV,IAAI,QAAQ;AAIZ,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,SAAS,KAAK,KAAK;AACvB,IAAI,SAAS,KAAK,KAAK;AAKvB,IAAI,MAAM;;;AC5BjB,IAAI,gBAAgB,CAAC;AAErB,cAAc,YAAY;AAC1B,cAAc,SAAS;AACvB,cAAc,QAAQ;AACtB,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,OAAO;AACrB,cAAc,OAAO;AACrB,cAAc,UAAU;AACxB,cAAc,QAAQ;AACtB,cAAc,WAAW;AACzB,cAAc,YAAY;AAC1B,cAAc,SAAS;AACvB,cAAc,OAAO;AAErB,IAAO,wBAAQ;;;AChBf,IAAO,gBAAQ;AAAA,EACb,IAAI,EAAE,UAAU,KAAM;AAAA,EACtB,IAAI,EAAE,UAAU,KAAK;AAAA,EACrB,IAAI,EAAE,UAAU,OAAO;AAAA,EACvB,SAAS,EAAE,UAAU,OAAO,KAAK;AAAA,EACjC,MAAM,EAAE,UAAU,OAAO;AAAA,EACzB,KAAK,EAAE,UAAU,KAAK;AAAA,EACtB,SAAS,EAAE,UAAU,iBAAiB;AAAA,EACtC,SAAS,EAAE,UAAU,iBAAiB;AAAA,EACtC,IAAI,EAAE,UAAU,IAAK;AAAA,EACrB,UAAU,EAAE,UAAU,WAAW;AAAA,EACjC,UAAU,EAAE,UAAU,WAAW;AAAA,EACjC,IAAI,EAAE,UAAU,SAAS;AAAA,EACzB,IAAI,EAAE,UAAU,OAAO;AAAA,EACvB,IAAI,EAAE,UAAU,QAAQ;AAAA,EACxB,MAAM,EAAE,UAAU,SAAS;AAAA,EAC3B,IAAI,EAAE,UAAU,IAAI;AAAA,EACpB,IAAI,EAAE,UAAU,OAAO;AAAA,EACvB,UAAU,EAAE,UAAU,YAAY;AAAA,EAClC,SAAS,EAAE,UAAU,kBAAkB;AAAA,EACvC,SAAS,EAAE,UAAU,kBAAkB;AACzC;;;ACrBA,IAAI,cAAc;AACH,SAAR,MAAuB,KAAK,KAAK;AACtC,MAAI,IAAI,GAAG,GAAG;AACZ,WAAO,IAAI,GAAG;AAAA,EAChB;AACA,MAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,MAAI,OAAO,IAAI,YAAY,EAAE,QAAQ,aAAa,EAAE;AACpD,MAAI,IAAI;AACR,MAAI,SAAS;AACb,SAAO,EAAE,IAAI,KAAK,QAAQ;AACxB,cAAU,KAAK,CAAC;AAChB,mBAAe,QAAQ,YAAY,EAAE,QAAQ,aAAa,EAAE;AAC5D,QAAI,iBAAiB,MAAM;AACzB,aAAO,IAAI,OAAO;AAAA,IACpB;AAAA,EACF;AACF;;;ACPe,SAAR,mBAAkB,SAAS;AAEhC,MAAI,OAAO,CAAC;AACZ,MAAI,WAAW,QAAQ,MAAM,GAAG,EAAE,IAAI,SAAU,GAAG;AACjD,WAAO,EAAE,KAAK;AAAA,EAChB,CAAC,EAAE,OAAO,SAAU,GAAG;AACrB,WAAO;AAAA,EACT,CAAC,EAAE,OAAO,SAAU,GAAG,GAAG;AAExB,QAAI,QAAQ,EAAE,MAAM,GAAG;AACvB,UAAM,KAAK,IAAI;AACf,MAAE,MAAM,CAAC,EAAE,YAAY,CAAC,IAAI,MAAM,CAAC;AACnC,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,MAAI,WAAW,UAAU;AACzB,MAAIC,UAAS;AAAA,IACX,MAAM;AAAA,IACN,OAAO;AAAA,IACP,IAAI,SAAU,GAAG;AACf,WAAK,KAAK,WAAW,CAAC;AAAA,IACxB;AAAA,IACA,OAAO,SAAU,GAAG;AAClB,WAAK,OAAO,IAAI;AAAA,IAClB;AAAA,IACA,OAAO,SAAU,GAAG;AAClB,WAAK,OAAO,IAAI;AAAA,IAClB;AAAA,IACA,OAAO,SAAU,GAAG;AAClB,WAAK,OAAO,IAAI;AAAA,IAClB;AAAA,IACA,QAAQ,SAAU,GAAG;AACnB,WAAK,SAAS,IAAI;AAAA,IACpB;AAAA,IACA,OAAO,SAAU,GAAG;AAClB,WAAK,QAAQ,IAAI;AAAA,IACnB;AAAA,IACA,OAAO,SAAU,GAAG;AAClB,WAAK,QAAQ,IAAI;AAAA,IACnB;AAAA,IACA,OAAO,SAAU,GAAG;AAClB,WAAK,QAAQ,IAAI;AAAA,IACnB;AAAA,IACA,OAAO,SAAU,GAAG;AAClB,WAAK,QAAQ,WAAW,CAAC,IAAI;AAAA,IAC/B;AAAA,IACA,OAAO,SAAU,GAAG;AAClB,WAAK,uBAAuB,WAAW,CAAC,IAAI;AAAA,IAC9C;AAAA,IACA,MAAM,SAAU,GAAG;AACjB,WAAK,QAAQ,IAAI;AAAA,IACnB;AAAA,IACA,KAAK,SAAU,GAAG;AAChB,WAAK,KAAK,WAAW,CAAC;AAAA,IACxB;AAAA,IACA,KAAK,SAAU,GAAG;AAChB,WAAK,KAAK,WAAW,CAAC;AAAA,IACxB;AAAA,IACA,KAAK,SAAU,GAAG;AAChB,WAAK,KAAK,WAAW,CAAC;AAAA,IACxB;AAAA,IACA,GAAG,SAAU,GAAG;AACd,WAAK,KAAK,WAAW,CAAC;AAAA,IACxB;AAAA,IACA,GAAG,SAAU,GAAG;AACd,WAAK,IAAI,WAAW,CAAC;AAAA,IACvB;AAAA,IACA,GAAG,SAAU,GAAG;AACd,WAAK,IAAI,WAAW,CAAC;AAAA,IACvB;AAAA,IACA,GAAG,SAAU,GAAG;AACd,WAAK,IAAI,KAAK,IAAI,WAAW,CAAC;AAAA,IAChC;AAAA,IACA,KAAK,WAAY;AACf,WAAK,MAAM;AAAA,IACb;AAAA,IACA,MAAM,SAAU,GAAG;AACjB,WAAK,OAAO,SAAS,GAAG,EAAE;AAAA,IAC5B;AAAA,IACA,OAAO,WAAY;AACjB,WAAK,WAAW;AAAA,IAClB;AAAA,IACA,SAAS,SAAU,GAAG;AACpB,WAAK,eAAe,EAAE,MAAM,GAAG,EAAE,IAAI,SAAU,GAAG;AAChD,eAAO,WAAW,CAAC;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,IACA,UAAU,SAAU,GAAG;AACrB,WAAK,WAAW,WAAW,CAAC;AAAA,IAC9B;AAAA,IACA,OAAO,SAAU,GAAG;AAClB,WAAK,QAAQ;AACb,UAAI,OAAO,MAAM,eAAO,CAAC;AACzB,UAAI,MAAM;AACR,aAAK,WAAW,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,gBAAgB,SAAU,GAAG;AAC3B,WAAK,iBAAiB,IAAI;AAAA,IAC5B;AAAA,IACA,IAAI,SAAU,GAAG;AACf,UAAI,KAAK,MAAM,uBAAe,CAAC;AAC/B,WAAK,kBAAkB,KAAK,KAAK,WAAW,CAAC,KAAK;AAAA,IACpD;AAAA,IACA,UAAU,SAAU,GAAG;AACrB,UAAI,MAAM,SAAS;AACjB,aAAK,YAAY;AAAA,MACnB,OAAO;AACL,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAAA,IACA,MAAM,SAAU,GAAG;AACjB,UAAI,YAAY;AAChB,UAAI,EAAE,WAAW,KAAK,UAAU,QAAQ,EAAE,OAAO,GAAG,CAAC,CAAC,MAAM,MAAM,UAAU,QAAQ,EAAE,OAAO,GAAG,CAAC,CAAC,MAAM,MAAM,UAAU,QAAQ,EAAE,OAAO,GAAG,CAAC,CAAC,MAAM,IAAI;AACtJ,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAAA,IACA,QAAQ,WAAY;AAClB,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AACA,OAAK,aAAa,UAAU;AAC1B,eAAW,SAAS,SAAS;AAC7B,QAAI,aAAaA,SAAQ;AACvB,qBAAeA,QAAO,SAAS;AAC/B,UAAI,OAAO,iBAAiB,YAAY;AACtC,qBAAa,QAAQ;AAAA,MACvB,OAAO;AACL,aAAK,YAAY,IAAI;AAAA,MACvB;AAAA,IACF,OAAO;AACL,WAAK,SAAS,IAAI;AAAA,IACpB;AAAA,EACF;AACA,MAAI,OAAO,KAAK,cAAc,YAAY,KAAK,cAAc,SAAS;AACpE,SAAK,YAAY,KAAK,UAAU,YAAY;AAAA,EAC9C;AACA,SAAO;AACT;;;AClJA,IAAM,sBAAN,MAA0B;AAAA,EACxB,OAAO,MAAM,MAAM;AACjB,UAAM,SAAS,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,IAAI;AAC7E,QAAI,UAAU,OAAO,UAAU,GAAG;AAChC,aAAO;AAAA,QACL,WAAW,OAAO,CAAC;AAAA,QACnB,MAAM,SAAS,OAAO,CAAC,GAAG,EAAE;AAAA,MAC9B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,YAAY,MAAM,OAAO,QAAQ;AACtC,QAAI,CAAC,QAAQ,KAAK,SAAS,GAAG;AAC5B,aAAO,EAAE,MAAM,MAAM,WAAW,mBAAmB,KAAK;AAAA,IAC1D;AAEA,UAAM,OAAO,KAAK,CAAC;AACnB,UAAM,mBAAmB,WAAW,KAAK,CAAC,CAAC,KAAK;AAEhD,UAAM,SAAS,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,IAAI;AAC7E,UAAM,KAAK,SACP;AAAA,MACA,WAAW,OAAO,CAAC;AAAA,MACnB,MAAM,SAAS,OAAO,CAAC,GAAG,EAAE;AAAA,IAC9B,IACE;AAEJ,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,mBAAmB;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO,YAAY,MAAM;AACvB,UAAM,OAAO,KAAK,CAAC,KAAK;AAGxB,QAAI;AACJ,UAAM,oBAAoB,KAAK,MAAM,WAAW;AAChD,QAAI,mBAAmB;AAErB,YAAM,eAAe,kBAAkB,CAAC,EAAE,YAAY;AACtD,UAAI,iBAAiB,IAAK,aAAY;AAAA,eAC7B,iBAAiB,IAAK,aAAY;AAAA,eAClC,iBAAiB,IAAK,aAAY;AAAA,UACtC,OAAM,IAAI,MAAM,8BAA8B,YAAY,EAAE;AAAA,IACnE,OAAO;AAEL,kBAAY,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,YAAY,IAAI;AAAA,IAChD;AAEA,UAAM,YAAY,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,OAAO;AACnF,UAAM,QAAQ,YAAY,SAAS,UAAU,CAAC,GAAG,EAAE,IAAI;AAEvD,UAAM,WAAW,KAAK;AAAA,MACpB,CAAC,UACC,MAAM,QAAQ,KAAK,MAClB,MAAM,CAAC,MAAM,gBAAgB,MAAM,CAAC,MAAM,eAAe,MAAM,CAAC,MAAM;AAAA,IAC3E;AACA,UAAM,OAAO,KAAK,YAAY,QAAQ;AAEtC,WAAO;AAAA,MACL;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO,YAAY,MAAM;AACvB,WAAO,KACJ,OAAO,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,MAAM,EAC7D,IAAI,CAAC,SAAS,KAAK,YAAY,IAAI,CAAC,EACpC,KAAK,CAAC,GAAG,OAAO,EAAE,SAAS,MAAM,EAAE,SAAS,EAAE;AAAA,EACnD;AAAA,EAEA,OAAO,QAAQ,MAAM,SAAS,CAAC,GAAG;AAEhC,YAAQ,KAAK,CAAC,GAAG;AAAA,MACf,KAAK;AACH,eAAO,OAAO;AACd,eAAO,OAAO,KAAK,CAAC;AACpB,eAAO,WAAW,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,aAAa,IACrF,KAAK,QAAQ,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,aAAa,CAAC,IACrF;AACJ,eAAO,aAAa,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,YAAY,IACtF,KAAK,QAAQ,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,YAAY,CAAC,IACpF;AAEJ,cAAM,SAAS,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,IAAI;AAC7E,YAAI,QAAQ;AACV,iBAAO,oBAAoB;AAAA,YACzB,MAAM,OAAO,CAAC;AAAA,YACd,MAAM,KAAK,YAAY,IAAI;AAAA,UAC7B;AAAA,QACF;AAEA,cAAM,iBAAiB,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,YAAY;AAC7F,YAAI,gBAAgB;AAClB,gBAAMC,QAAO,KAAK,YAAY,cAAc;AAC5C,iBAAO,kBAAkB,OAAOA;AAAA,QAClC;AAEA,eAAO,KAAK,KAAK,MAAM,IAAI;AAC3B;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AACH,eAAO,OAAO;AACd,eAAO,OAAO,KAAK,CAAC;AAGpB,cAAM,sBAAsB,KAAK;AAAA,UAC/B,CAAC,UAAU,MAAM,QAAQ,KAAK,MAAM,MAAM,CAAC,MAAM,WAAW,MAAM,CAAC,MAAM;AAAA,QAC3E;AACA,YAAI,qBAAqB;AACvB,gBAAM,kBAAkB,KAAK,QAAQ,mBAAmB;AACxD,cAAI,oBAAoB,CAAC,MAAM,YAAY;AACzC,mBAAO,iBAAiB;AAAA,UAC1B,OAAO;AACL,mBAAO,QAAQ;AAAA,UACjB;AACA,gBAAM,SAAS,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,QAAQ;AACjF,cAAI,UAAU,OAAO,CAAC,MAAM,aAAa;AACvC,4BAAgB,iBAAiB;AAAA,cAC/B,MAAM,OAAO,CAAC;AAAA,cACd,WAAW,WAAW,OAAO,CAAC,CAAC;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AAEA,eAAO,oBAAoB;AAAA,UACzB,MAAM;AAAA,UACN,MAAM,KAAK,YAAY,IAAI;AAAA,QAC7B;AAEA,eAAO,KAAK,KAAK,MAAM,IAAI;AAC3B;AAAA,MAEF,KAAK;AACH,eAAO,OAAO;AACd,eAAO,OAAO,KAAK,CAAC;AACpB,eAAO,YAAY,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,WAAW,IACpF,KAAK,QAAQ,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,WAAW,CAAC,IACnF;AACJ;AAAA,MAEF,KAAK;AACH,eAAO,OAAO;AACd,eAAO,OAAO,KAAK,CAAC;AAGpB,eAAO,UAAU,KACd,OAAO,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,QAAQ,EAC/D,IAAI,CAAC,YAAY;AAAA,UAChB,MAAM;AAAA,UACN,MAAM,OAAO,CAAC;AAAA,UACd,IAAI,KAAK,MAAM,MAAM;AAAA;AAAA,QACvB,EAAE;AAGJ,cAAM,eAAe,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,kBAAkB;AACjG,YAAI,cAAc;AAChB,iBAAO,WAAW,WAAW,aAAa,CAAC,CAAC;AAAA,QAC9C;AAGA,cAAM,gBAAgB,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,WAAW;AAC3F,YAAI,eAAe;AACjB,iBAAO,YAAY,KAAK,QAAQ,aAAa;AAAA,QAC/C;AAGA,eAAO,KAAK,KAAK,MAAM,IAAI;AAC3B;AAAA,MAEF,KAAK;AACH,eAAO,OAAO;AACd,eAAO,OAAO,KAAK,CAAC;AACpB,eAAO,kBAAkB,WAAW,KAAK,CAAC,CAAC;AAC3C,eAAO,qBAAqB,WAAW,KAAK,CAAC,CAAC;AAC9C,cAAM,QAAQ,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,YAAY,IAChF,KAAK,QAAQ,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,YAAY,GAAG,MAAM,IAC5F;AACJ;AAAA,MAEF,KAAK;AACH,eAAO,OAAO;AACd,eAAO,OAAO,KAAK,CAAC;AACpB,eAAO,SAAS,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,QAAQ,IAC9E,KAAK,QAAQ,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,QAAQ,CAAC,IAChF;AACJ,eAAO,aAAa,KACjB,OAAO,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,WAAW,EAClE,IAAI,CAAC,UAAU,KAAK,QAAQ,KAAK,CAAC;AACrC;AAAA,MAEF,KAAK;AACH,eAAO,OAAO;AACd,eAAO,OAAO,KAAK,CAAC;AACpB,eAAO,KAAK,KAAK,MAAM,IAAI;AAC3B;AAAA,MAEF,KAAK;AACH,eAAO,OAAO;AACd,eAAO,OAAO,KAAK,CAAC;AACpB,eAAO,QAAQ,WAAW,KAAK,CAAC,CAAC;AACjC,eAAO,OAAO,KAAK;AAAA,UACjB,KAAK;AAAA,YACH,CAAC,UACC,MAAM,QAAQ,KAAK,MAClB,MAAM,CAAC,MAAM,gBAAgB,MAAM,CAAC,MAAM,eAAe,MAAM,CAAC,MAAM;AAAA,UAC3E;AAAA,QACF;AACA,eAAO,KAAK,KAAK,MAAM,IAAI;AAC3B;AAAA,MAEF,KAAK;AACH,eAAO,OAAO;AAGd,cAAM,gBAAgB,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,WAAW;AAC3F,YAAI,eAAe;AACjB,gBAAM,mBAAmB,cAAc,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,CAAC;AAC3E,iBAAO,aAAa,mBAAmB,KAAK,QAAQ,gBAAgB,IAAI;AAAA,QAC1E;AAGA,cAAM,gBAAgB,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,WAAW;AAC3F,YAAI,eAAe;AACjB,gBAAM,mBAAmB,cAAc,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,CAAC;AAC3E,iBAAO,aAAa,mBAAmB,KAAK,QAAQ,gBAAgB,IAAI;AAAA,QAC1E;AAGA,cAAM,qBAAqB,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,wBAAwB;AAC7G,YAAI,oBAAoB;AACtB,iBAAO,iBAAiB,KAAK,QAAQ,kBAAkB;AAAA,QACzD,OAAO;AACL,iBAAO,iBAAiB;AAAA,QAC1B;AACA;AAAA,MAEF,KAAK;AACH,eAAO,OAAO;AACd,eAAO,OAAO,KAAK,CAAC;AACpB,eAAO,SAAS,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,QAAQ,IAC9E,KAAK,QAAQ,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,QAAQ,CAAC,IAChF;AAEJ,eAAO,aAAa,KACjB,OAAO,CAAC,UAAU,MAAM,QAAQ,KAAK,MAAM,MAAM,CAAC,MAAM,eAAe,MAAM,CAAC,MAAM,gBAAgB,EACpG,IAAI,CAAC,UAAU;AACd,cAAI,MAAM,CAAC,MAAM,aAAa;AAC5B,mBAAO,KAAK,QAAQ,KAAK;AAAA,UAC3B,WAAW,MAAM,CAAC,MAAM,iBAAiB;AACvC,mBAAO;AAAA,cACL,MAAM,MAAM,CAAC;AAAA,cACb,OAAO,MAAM,CAAC;AAAA,cACd,IAAI;AAAA,gBACF,aAAa;AAAA,gBACb,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAGH,YAAI,OAAO,WAAW,WAAW,GAAG;AAClC,gBAAM,kBAAkB,OAAO,WAAW,CAAC;AAC3C,cAAI,gBAAgB,SAAS,oBAAoB;AAC/C,4BAAgB,QAAQ,KAAK,OAAO,gBAAgB,QAAQ,KAAK,IAAI,IAAI;AAAA,UAC3E;AAAA,QACF;AAEA,eAAO,KAAK,KAAK,MAAM,IAAI;AAC3B;AAAA,MAEF,KAAK;AACH,YAAI,CAAC,OAAO,mBAAmB;AAC7B,iBAAO,oBAAoB,EAAE,MAAM,eAAe,MAAM,CAAC,EAAE;AAAA,QAC7D;AACA,eAAO,kBAAkB,KAAK,KAAK,KAAK,YAAY,IAAI,CAAC;AACzD;AAAA,MAEF,KAAK;AACH,cAAM,OAAO,KAAK,YAAY,MAAM,YAAY;AAChD,YAAI,OAAO,qBAAqB,OAAO,kBAAkB,MAAM;AAC7D,iBAAO,kBAAkB,KAAK,QAAQ,CAAC,SAAS;AAC9C,gBAAI,CAAC,KAAK,MAAM;AACd,mBAAK,OAAO;AAAA,YACd;AAAA,UACF,CAAC;AAAA,QACH;AACA,YAAI,KAAK,qBAAqB,KAAK,sBAAsB,GAAG;AAC1D,cAAI,OAAO,iBAAiB;AAC1B,mBAAO,kBAAkB;AAAA,cACvB,OAAO,OAAO;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA;AAAA,MAEF;AACE,eAAO,UAAU,KAAK,CAAC;AACvB;AAAA,IACJ;AAEA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,8BAAQ;;;AC1Tf,IAAM,sBAAN,cAAkC,4BAAoB;AAAA,EACpD,OAAO,QAAQ,MAAM,SAAS,CAAC,GAAG;AAChC,UAAM,QAAQ,MAAM,MAAM;AAG1B,QAAI,OAAO,qBAAqB,OAAO,kBAAkB,YAAY,aAAa;AAChF,aAAO,OAAO;AAAA,IAChB;AACA,QAAI,OAAO,OAAO;AAChB,aAAO,OAAO;AAAA,IAChB;AAEA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,8BAAQ;;;AChBf,IAAM,sBAAN,cAAkC,4BAAoB;AAAA,EACpD,OAAO,QAAQ,MAAM,SAAS,CAAC,GAAG;AAChC,UAAM,QAAQ,MAAM,MAAM;AAG1B,UAAM,SAAS,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,IAAI;AAC7E,QAAI,QAAQ;AACV,aAAO,oBAAoB;AAAA,QACzB,SAAS,OAAO,CAAC;AAAA,QACjB,MAAM,KAAK,YAAY,IAAI;AAAA,MAC7B;AAAA,IACF;AAGA,UAAM,YAAY,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,OAAO;AACnF,QAAI,WAAW;AACb,YAAM,QAAQ,UAAU,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,OAAO;AACpF,YAAM,OAAO,UAAU,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,MAAM;AAClF,YAAM,OAAO,UAAU,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,MAAM;AAClF,aAAO,QAAQ,CAAC;AAChB,UAAI,OAAO;AACT,eAAO,MAAM,QAAQ,MAAM,CAAC;AAAA,MAC9B;AACA,UAAI,MAAM;AACR,eAAO,MAAM,OAAO,KAAK,CAAC;AAAA,MAC5B;AACA,UAAI,MAAM;AACR,eAAO,MAAM,OAAO,KAAK,MAAM,CAAC;AAAA,MAClC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,8BAAQ;;;AC7Bf,SAAS,kBAAkB,MAAM;AAE/B,MAAI,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,OAAO,GAAG;AACtE,WAAO;AAAA,EACT;AAGA,MAAI,KAAK,KAAK,CAAC,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,IAAI,GAAG;AACnE,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,CAAC,MAAM,cAAc,KAAK,CAAC,MAAM,aAAa,KAAK,CAAC,MAAM,WAAW;AAC5E,WAAO;AAAA,EACT;AAGA,SAAO;AACT;AAOO,SAAS,cAAc,MAAM;AAClC,QAAM,UAAU,kBAAkB,IAAI;AACtC,QAAM,UAAU,YAAY,SAAS,8BAAsB;AAC3D,SAAO,QAAQ,QAAQ,IAAI;AAC7B;;;AC/BO,SAAS,iBAAiB,KAAK;AAEpC,QAAM,gBAAgB,IAAI,YAAY;AAGtC,MACE,cAAc,SAAS,SAAS,KAChC,cAAc,SAAS,SAAS,KAChC,cAAc,SAAS,UAAU,KACjC,cAAc,SAAS,SAAS,KAChC,cAAc,SAAS,YAAY,KACnC,cAAc,SAAS,WAAW,KAClC,cAAc,SAAS,WAAW,GAClC;AACA,WAAO;AAAA,EACT;AAGA,MACE,cAAc,SAAS,QAAQ,KAC/B,cAAc,SAAS,QAAQ,KAC/B,cAAc,SAAS,UAAU,KACjC,cAAc,SAAS,SAAS,KAChC,cAAc,SAAS,MAAM,GAC7B;AACA,WAAO;AAAA,EACT;AAGA,SAAO;AACT;;;ACnCA,IAAO,iBAAQ;AAEf,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,aAAa;AACjB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,SAAS;AAEb,SAAS,OAAO,MAAM;AACpB,MAAI,OAAO,SAAS,UAAU;AAC5B,UAAM,IAAI,MAAM,cAAc;AAAA,EAChC;AACA,OAAK,OAAO,KAAK,KAAK;AACtB,OAAK,QAAQ;AACb,OAAK,QAAQ;AACb,OAAK,OAAO;AACZ,OAAK,QAAQ,CAAC;AACd,OAAK,gBAAgB;AACrB,OAAK,QAAQ;AACf;AACA,OAAO,UAAU,gBAAgB,WAAW;AAC1C,MAAI,OAAO,KAAK,KAAK,KAAK,OAAO;AACjC,MAAI,KAAK,UAAU,QAAQ;AACzB,WAAO,WAAW,KAAK,IAAI,GAAG;AAC5B,UAAI,KAAK,SAAS,KAAK,KAAK,QAAQ;AAClC;AAAA,MACF;AACA,aAAO,KAAK,KAAK,KAAK,OAAO;AAAA,IAC/B;AAAA,EACF;AACA,UAAQ,KAAK,OAAO;AAAA,IAClB,KAAK;AACH,aAAO,KAAK,QAAQ,IAAI;AAAA,IAC1B,KAAK;AACH,aAAO,KAAK,QAAQ,IAAI;AAAA,IAC1B,KAAK;AACH,aAAO,KAAK,OAAO,IAAI;AAAA,IACzB,KAAK;AACH,aAAO,KAAK,WAAW,IAAI;AAAA,IAC7B,KAAK;AACH,aAAO,KAAK,OAAO,IAAI;AAAA,IACzB,KAAK;AACH;AAAA,EACJ;AACF;AACA,OAAO,UAAU,aAAa,SAAS,MAAM;AAC3C,MAAI,SAAS,KAAK;AAChB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb;AAAA,EACF;AACA,MAAI,UAAU,KAAK,IAAI,GAAG;AACxB,SAAK,OAAO,KAAK,KAAK,KAAK;AAC3B,SAAK,UAAU,IAAI;AACnB;AAAA,EACF;AACA,QAAM,IAAI,MAAM,qBAAqB,OAAO,gCAAgC,KAAK,KAAK;AACxF;AACA,OAAO,UAAU,YAAY,SAAS,MAAM;AAC1C,MAAI,SAAS,KAAK;AAChB,QAAI,KAAK,SAAS,MAAM;AACtB,WAAK,cAAc,KAAK,KAAK,IAAI;AAAA,IACnC;AACA,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb;AAAA,EACF;AACA,MAAI,SAAS,KAAK;AAChB,SAAK;AACL,QAAI,KAAK,SAAS,MAAM;AACtB,WAAK,cAAc,KAAK,KAAK,IAAI;AACjC,WAAK,OAAO;AAAA,IACd;AACA,SAAK,QAAQ;AACb,SAAK,gBAAgB,KAAK,MAAM,IAAI;AACpC,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,QAAQ;AAAA,IACf;AAEA;AAAA,EACF;AACF;AACA,OAAO,UAAU,SAAS,SAAS,MAAM;AACvC,MAAI,OAAO,KAAK,IAAI,GAAG;AACrB,SAAK,QAAQ;AACb;AAAA,EACF;AACA,MAAI,UAAU,KAAK,IAAI,GAAG;AACxB,SAAK,OAAO,WAAW,KAAK,IAAI;AAChC,SAAK,UAAU,IAAI;AACnB;AAAA,EACF;AACA,QAAM,IAAI,MAAM,qBAAqB,OAAO,4BAA4B,KAAK,KAAK;AACpF;AACA,OAAO,UAAU,SAAS,SAAS,MAAM;AACvC,MAAI,SAAS,KAAK;AAChB,SAAK,QAAQ;AACb;AAAA,EACF;AACA,OAAK,QAAQ;AACb;AACF;AACA,OAAO,UAAU,UAAU,SAAS,MAAM;AACxC,MAAI,QAAQ,KAAK,IAAI,GAAG;AACtB,SAAK,QAAQ;AACb;AAAA,EACF;AACA,MAAI,SAAS,KAAK;AAChB,QAAI,aAAa,CAAC;AAClB,eAAW,KAAK,KAAK,IAAI;AACzB,SAAK;AACL,QAAI,KAAK,SAAS,MAAM;AACtB,WAAK,OAAO;AAAA,IACd,OAAO;AACL,WAAK,cAAc,KAAK,UAAU;AAAA,IACpC;AACA,SAAK,MAAM,KAAK,KAAK,aAAa;AAClC,SAAK,gBAAgB;AACrB,SAAK,QAAQ;AACb;AAAA,EACF;AACA,MAAI,UAAU,KAAK,IAAI,GAAG;AACxB,SAAK,UAAU,IAAI;AACnB;AAAA,EACF;AACA,QAAM,IAAI,MAAM,qBAAqB,OAAO,6BAA6B,KAAK,KAAK;AACrF;AACA,OAAO,UAAU,UAAU,SAAS,MAAM;AACxC,MAAI,MAAM,KAAK,IAAI,GAAG;AACpB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb;AAAA,EACF;AACA,MAAI,SAAS,KAAK;AAChB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb;AAAA,EACF;AACA,MAAI,OAAO,KAAK,IAAI,GAAG;AACrB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb;AAAA,EACF;AACA,MAAI,UAAU,KAAK,IAAI,GAAG;AACxB,SAAK,UAAU,IAAI;AACnB;AAAA,EACF;AACA,QAAM,IAAI,MAAM,qBAAqB,OAAO,6BAA6B,KAAK,KAAK;AACrF;AACA,OAAO,UAAU,SAAS,WAAW;AACnC,SAAO,KAAK,QAAQ,KAAK,KAAK,QAAQ;AACpC,SAAK,cAAc;AAAA,EACrB;AACA,MAAI,KAAK,UAAU,OAAO;AACxB,WAAO,KAAK;AAAA,EACd;AACA,QAAM,IAAI,MAAM,6BAA4B,KAAK,OAAO,iBAAiB,KAAK,KAAK;AACrF;AAEA,SAAS,YAAY,KAAK;AACxB,MAAI,SAAS,IAAI,OAAO,GAAG;AAC3B,SAAO,OAAO,OAAO;AACvB;;;ACtKA,SAAS,MAAM,KAAK,KAAK,OAAO;AAC9B,MAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,UAAM,QAAQ,GAAG;AACjB,UAAM;AAAA,EACR;AACA,MAAI,QAAQ,MAAM,CAAC,IAAI;AAEvB,MAAI,MAAM,MAAM,OAAO,SAAS,QAAQ,MAAM;AAC5C,UAAM,MAAM,MAAM;AAClB,WAAO;AAAA,EACT,GAAG,KAAK;AACR,MAAI,KAAK;AACP,QAAI,GAAG,IAAI;AAAA,EACb;AACF;AAEO,SAAS,MAAM,GAAG,KAAK;AAC5B,MAAI,CAAC,MAAM,QAAQ,CAAC,GAAG;AACrB,QAAI,CAAC,IAAI;AACT;AAAA,EACF;AACA,MAAI,MAAM,EAAE,MAAM;AAClB,MAAI,QAAQ,aAAa;AACvB,UAAM,EAAE,MAAM;AAAA,EAChB;AACA,MAAI,EAAE,WAAW,GAAG;AAClB,QAAI,MAAM,QAAQ,EAAE,CAAC,CAAC,GAAG;AACvB,UAAI,GAAG,IAAI,CAAC;AACZ,YAAM,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC;AACpB;AAAA,IACF;AACA,QAAI,GAAG,IAAI,EAAE,CAAC;AACd;AAAA,EACF;AACA,MAAI,CAAC,EAAE,QAAQ;AACb,QAAI,GAAG,IAAI;AACX;AAAA,EACF;AACA,MAAI,QAAQ,WAAW;AACrB,QAAI,GAAG,IAAI;AACX;AAAA,EACF;AACA,MAAI,QAAQ,QAAQ;AAClB,QAAI,EAAE,OAAO,MAAM;AACjB,UAAI,GAAG,IAAI,CAAC;AAAA,IACd;AACA,QAAI,GAAG,EAAE,KAAK,CAAC;AACf;AAAA,EACF;AACA,MAAI,CAAC,MAAM,QAAQ,GAAG,GAAG;AACvB,QAAI,GAAG,IAAI,CAAC;AAAA,EACd;AAEA,MAAI;AACJ,UAAQ,KAAK;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,UAAI,GAAG,IAAI;AAAA,QACT,MAAM,EAAE,CAAC,EAAE,YAAY;AAAA,QACvB,SAAS,EAAE,CAAC;AAAA,MACd;AACA,UAAI,EAAE,WAAW,GAAG;AAClB,cAAM,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC;AAAA,MACtB;AACA;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,UAAI,GAAG,IAAI;AAAA,QACT,MAAM,EAAE,CAAC;AAAA,QACT,GAAG,EAAE,CAAC;AAAA,QACN,IAAI,EAAE,CAAC;AAAA,MACT;AACA,UAAI,EAAE,WAAW,GAAG;AAClB,cAAM,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC;AAAA,MACtB;AACA;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,QAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AACpB,YAAM,KAAK,KAAK,CAAC;AACjB;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA;AAAA;AAAA,IAGL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,QAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AACpB,YAAM,KAAK,KAAK,CAAC;AACjB,UAAI,GAAG,EAAE,OAAO;AAChB;AAAA,IACF;AACE,UAAI;AACJ,aAAO,EAAE,IAAI,EAAE,QAAQ;AACrB,YAAI,CAAC,MAAM,QAAQ,EAAE,CAAC,CAAC,GAAG;AACxB,iBAAO,MAAM,GAAG,IAAI,GAAG,CAAC;AAAA,QAC1B;AAAA,MACF;AACA,aAAO,MAAM,KAAK,KAAK,CAAC;AAAA,EAC5B;AACF;;;ACtHA,IAAIC,OAAM;AAEH,SAAS,IAAI,OAAO;AACzB,SAAO,QAAQA;AACjB;AAEO,SAAS,wBAAwB,KAAK;AAE3C,QAAM,sBAAsB,IAAI,YAAY,IAAI,YAAY,EAAE,QAAQ,MAAM,GAAG;AAE/E,MAAI,CAAC,IAAI,SAAS,IAAI,UAAU,uBAAuB,6BAA6B,uBAAuB,iCAAiC;AAC1I,QAAI,QAAQ,IAAI;AAAA,EAClB;AACA,MAAI,CAAC,IAAI,UAAU,IAAI,SAAS,uBAAuB,8BAA8B,uBAAuB,oCAAoC;AAC9I,QAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,GAAG;AACtC,QAAI,SAAS,IAAI;AACjB,WAAO,IAAI;AAAA,EACb,WAAW,CAAC,IAAI,UAAU,IAAI,SAAS,uBAAuB,yBAAyB,uBAAuB,oCAAoC;AAChJ,QAAI,SAAS,IAAI;AACjB,QAAI,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,GAAG;AACtC,WAAO,IAAI;AAAA,EACb;AACF;;;ACnBA,SAAS,YAAY,MAAM;AACzB,MAAI,SAAS,EAAE,OAAO,MAAM,UAAU,OAAU;AAEhD,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,QAAQ,KAAK,YAAY;AAChC,QAAI,OAAO,UAAU,SAAS;AAC5B,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,OAAO,UAAU,SAAS;AAC5B,aAAO,WAAW;AAAA,IACpB;AAAA,EACF,WAAW,QAAQ,KAAK,MAAM;AAC5B,WAAO,QAAQ,KAAK,KAAK,YAAY;AACrC,QAAI,OAAO,UAAU,SAAS;AAC5B,aAAO,QAAQ;AAAA,IACjB;AACA,WAAO,WAAW,KAAK;AAAA,EACzB;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,eAAe;AAC9B,MAAI,OAAO,kBAAkB,UAAU;AACrC,WAAO,cAAc,QAAQ,cAAc,KAAK;AAAA,EAClD;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,OAAO,QAAQ;AACzC,MAAI,MAAM,UAAU,QAAQ;AAC1B,WAAO,IAAI,MAAM,UAAU;AAC3B,WAAO,KAAK;AAAA,EACd,OAAO;AACL,WAAO,IAAI,QAAQ,MAAM,UAAU,eAAe;AAClD,QAAI,MAAM,UAAU,uBAAuB,QAAW;AACpD,aAAO,KAAK,MAAM,UAAU;AAAA,IAC9B,WAAW,MAAM,UAAU,oBAAoB,UAAa,MAAM,UAAU,oBAAoB,QAAW;AACzG,aAAO,KAAK,OAAO,KAAK,OAAO,IAAI,QAAQ,MAAM,UAAU,eAAe;AAAA,IAC5E;AAAA,EACF;AACF;AAEO,SAAS,kBAAkB,UAAU,SAAS,CAAC,GAAG;AACvD,MAAI,CAAC,YAAY,OAAO,aAAa,UAAU;AAC7C,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,SAAS,YAAY;AAChC,sBAAkB,SAAS,YAAY,MAAM;AAE7C,QAAI,SAAS,gBAAgB;AAC3B,UAAI,SAAS,eAAe,UAAU,SAAS,eAAe,OAAO,SAAS,QAAQ;AAEpF,eAAO,WAAW,SAAS,eAAe,WAAW,CAAC,EAAE;AAAA,MAC1D,OAAO;AAEL,eAAO,eAAe,SAAS,eAAe,WAAW,IAAI,CAAC,UAAU,MAAM,KAAK;AAAA,MACrF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAGA,SAAO,KAAK,QAAQ,EAAE,QAAQ,CAAC,QAAQ;AACrC,UAAM,QAAQ,SAAS,GAAG;AAC1B,QAAI,UAAU,MAAM;AAClB;AAAA,IACF;AAEA,YAAQ,KAAK;AAAA,MACX,KAAK;AACH,YAAI,OAAO,SAAS;AAClB;AAAA,QACF;AACA,eAAO,OAAO;AACd,eAAO,UAAU;AACjB;AAAA,MAEF,KAAK;AACH,YAAI,UAAU,iBAAiB;AAC7B,iBAAO,WAAW;AAAA,QACpB,WAAW,UAAU,kBAAkB,SAAS,cAAc,SAAS,WAAW,QAAQ;AACxF,iBAAO,WAAW,SAAS,WAAW,OAAO;AAAA,QAC/C;AACA;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AACH,YAAI,MAAM,WAAW;AAEnB,iBAAO,QAAQ,MAAM,UAAU;AAC/B,6BAAmB,OAAO,MAAM;AAAA,QAClC;AACA,YAAI,MAAM,gBAAgB;AACxB,iBAAO,iBAAiB,MAAM,eAAe,YAAY,KAAK,KAAK;AAAA,QACrE;AACA;AAAA,MAEF,KAAK;AACH,eAAO,QAAQ,MAAM;AACrB,2BAAmB,OAAO,MAAM;AAChC;AAAA,MAEF,KAAK;AACH,eAAO,SAAS,MAAM,aAAa,KAAK,KAAK,KAAK;AAClD;AAAA,MAEF,KAAK;AACH,YAAI,MAAM,MAAM;AACd,iBAAO,OAAO,MAAM,KACjB,IAAI,CAAC,SAAS;AACb,kBAAM,YAAY,KAAK;AACvB,gBAAI,cAAc,OAAQ,QAAO;AACjC,gBAAI,cAAc,QAAS,QAAO;AAClC,gBAAI,cAAc,OAAQ,QAAO;AACjC,gBAAI,cAAc,QAAS,QAAO;AAClC,kBAAM,IAAI,MAAM,2BAA2B,SAAS,EAAE;AAAA,UACxD,CAAC,EACA,KAAK,EAAE,IAAI;AAEd,cAAI,MAAM,MAAM;AACd,kBAAM,EAAE,OAAO,SAAS,IAAI,YAAY,MAAM,IAAI;AAClD,mBAAO,QAAQ;AACf,mBAAO,WAAW;AAAA,UACpB,WAAW,MAAM,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,EAAE,MAAM;AAC9C,kBAAM,EAAE,OAAO,SAAS,IAAI,YAAY,MAAM,KAAK,CAAC,EAAE,IAAI;AAC1D,mBAAO,QAAQ;AACf,mBAAO,WAAW;AAAA,UACpB;AAAA,QACF;AACA;AAAA,MAEF,KAAK;AACH,YAAI,MAAM,aAAa,MAAM,MAAM;AACjC,iBAAO,QAAQ,MAAM,YAAY,MAAM,MAAM;AAAA,QAC/C;AACA;AAAA,MAEF,KAAK;AACH,YAAI,MAAM,UAAU,MAAM,OAAO,MAAM;AACrC,iBAAO,WAAW,MAAM,OAAO;AAAA,QACjC;AACA,YAAI,MAAM,YAAY;AACpB,gBAAM,WAAW,QAAQ,CAAC,UAAU;AAClC,kBAAM,YAAY,MAAM,KAAK,YAAY,EAAE,QAAQ,QAAQ,GAAG;AAC9D,kBAAM,aAAa,MAAM;AACzB,gBAAI,MAAM,QAAQ,MAAM,KAAK,mBAAmB;AAC9C,qBAAO,SAAS,IAAI,aAAa,MAAM,KAAK;AAAA,YAC9C,WAAW,MAAM,SAAS,UAAU;AAClC,qBAAO,SAAS,IAAI,aAAa,KAAK,KAAK;AAAA,YAC7C,OAAO;AACL,qBAAO,SAAS,IAAI;AAAA,YACtB;AAAA,UACF,CAAC;AAAA,QACH;AACA;AAAA,MAEF,KAAK;AACH,YAAI,MAAM,MAAM;AACd,iBAAO,QAAQ,MAAM,KAAK,YAAY;AACtC,cAAI,OAAO,UAAU,SAAS;AAC5B,mBAAO,QAAQ;AAAA,UACjB;AAAA,QACF;AACA,YAAI,MAAM,mBAAmB;AAC3B,iBAAO,WAAW,MAAM;AAAA,QAC1B;AACA;AAAA,MAEF,KAAK;AACH,0BAAkB,OAAO,MAAM;AAC/B,eAAO,YAAY,MAAM,KAAK,MAAM,GAAG,YAAY,MAAM,MAAM,GAAG,OAAO,MAAM;AAC/E;AAAA,MAEF;AAEE;AAAA,IACJ;AAAA,EACF,CAAC;AAGD,MAAI,OAAO,6BAA6B,QAAW;AACjD,WAAO,OAAO,OAAO;AAAA,EACvB;AACA,MAAI,OAAO,8BAA8B,QAAW;AAClD,WAAO,QAAQ,OAAO;AAAA,EACxB;AACA,MAAI,OAAO,kCAAkC,QAAW;AACtD,WAAO,OAAO,OAAO;AACrB,WAAO,OAAO,OAAO;AAAA,EACvB;AACA,MAAI,OAAO,sCAAsC,QAAW;AAC1D,WAAO,OAAO,OAAO;AAAA,EACvB;AACA,MAAI,OAAO,sCAAsC,QAAW;AAC1D,WAAO,OAAO,OAAO;AAAA,EACvB;AACA,MAAI,OAAO,kCAAkC,QAAW;AACtD,WAAO,OAAO,OAAO;AAAA,EACvB;AACA,MAAI,OAAO,mCAAmC,QAAW;AACvD,WAAO,QAAQ,OAAO;AAAA,EACxB;AACA,MAAI,OAAO,4BAA4B,QAAW;AAChD,WAAO,KAAK,OAAO;AAAA,EACrB;AACA,MAAI,OAAO,6BAA6B,QAAW;AACjD,WAAO,KAAK,OAAO;AAAA,EACrB;AACA,MAAI,OAAO,+BAA+B,QAAW;AACnD,WAAO,OAAO,OAAO;AAAA,EACvB;AACA,MAAI,OAAO,gCAAgC,QAAW;AACpD,WAAO,QAAQ,OAAO;AAAA,EACxB;AACA,MAAI,OAAO,wBAAwB,QAAW;AAC5C,WAAO,QAAQ,OAAO;AAAA,EACxB;AACA,MAAI,OAAO,kBAAkB,QAAW;AACtC,WAAO,KAAK,OAAO;AAAA,EACrB;AACA,MAAI,OAAO,8BAA8B;AACvC,WAAO,KAAK,OAAO;AAAA,EACrB;AACA,MAAI,OAAO,mBAAmB,QAAW;AACvC,WAAO,KAAK,OAAO;AAAA,EACrB;AACA,MAAI,OAAO,+BAA+B;AACxC,WAAO,KAAK,OAAO;AAAA,EACrB;AACA,MAAI,OAAO,wBAAwB,QAAW;AAC5C,WAAO,OAAO,OAAO;AAAA,EACvB;AACA,MAAI,OAAO,wBAAwB,QAAW;AAC5C,WAAO,OAAO,OAAO;AAAA,EACvB;AACA,MAAI,OAAO,mCAAmC,QAAW;AACvD,WAAO,KAAK,OAAO;AAAA,EACrB;AACA,MAAI,OAAO,sCAAsC,QAAW;AAC1D,WAAO,KAAK,OAAO;AAAA,EACrB;AACA,MAAI,OAAO,6CAA6C,QAAW;AACjE,WAAO,KAAK,OAAO;AAAA,EACrB;AACA,MAAI,OAAO,YAAY,QAAW;AAChC,WAAO,QAAQ,OAAO;AAAA,EACxB;AACA,MAAI,OAAO,iCAAiC,QAAW;AACrD,WAAO,QAAQ,OAAO;AAAA,EACxB;AACA,MAAI,OAAO,mCAAmC;AAC5C,WAAO,uBAAuB,OAAO;AAAA,EACvC;AAGA,0BAAwB,MAAM;AAE9B,SAAO;AACT;;;AChQA,IAAI,aAAa;AAAA,EAAC;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAY;AAAA,EACrF;AAAA,EAAe;AAAA,EAAiB;AAAA,EAAU;AAAgB;AAE5D,SAAS,OAAO,KAAKC,SAAQ;AAC3B,MAAI,UAAUA,QAAO,CAAC;AACtB,MAAI,SAASA,QAAO,CAAC;AACrB,MAAI,EAAE,WAAW,QAAS,UAAU,KAAM;AACxC,QAAI,OAAO,IAAI,IAAI,MAAM;AACzB,QAAIA,QAAO,WAAW,GAAG;AACvB,UAAI,OAAO,IAAIA,QAAO,CAAC,EAAE,IAAI,OAAO,CAAC;AAAA,IACvC;AAAA,EACF;AACF;AAEA,SAAS,SAAS,KAAK;AACrB,MAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,WAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAG,IAAI,EAAE,GAAG;AAC5C,QAAI,MAAM,KAAK,CAAC;AAGhB,QAAI,WAAW,QAAQ,GAAG,MAAM,IAAI;AAClC,2BAAqB,IAAI,GAAG,CAAC;AAAA,IAC/B;AACA,QAAI,OAAO,IAAI,GAAG,MAAM,UAAU;AAChC,eAAS,IAAI,GAAG,CAAC;AAAA,IACnB;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,KAAK;AACjC,MAAI,IAAI,WAAW;AACjB,QAAI,YAAY,OAAO,KAAK,IAAI,SAAS,EAAE,CAAC;AAC5C,QAAI,aAAa,aAAa,IAAI,WAAW;AAC3C,UAAI,QAAQ,YAAY,MAAM,IAAI,UAAU,SAAS;AAAA,IACvD;AAAA,EACF;AACA,MAAI,IAAI,SAAS,UAAU;AACzB,QAAI,WAAW;AAAA,EACjB,WAAW,IAAI,SAAS,YAAY;AAClC,QAAI,WAAW;AACf,QAAI,QAAQ;AAAA,EACd,OAAO;AACL,QAAI,OAAO,IAAI,eAAe,UAAU;AACtC,UAAI,WAAW,OAAO,KAAK,IAAI,UAAU,EAAE,CAAC;AAAA,IAC9C,OAAO;AACL,UAAI,WAAW,IAAI;AAAA,IACrB;AAAA,EACF;AACA,MAAI,IAAI,MAAM;AACZ,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG;AACjD,UAAI,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,YAAY,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC;AACtE,UAAI,KAAK,CAAC,EAAE,QAAQ,OAAO,MAAM,OAAQ,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,UAAU,KAAK,CAAC,MAAM,SAAU;AACtG,qBAAa;AAAA,MACf,WAAW,KAAK,CAAC,EAAE,QAAQ,OAAO,MAAM,OAAQ,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,UAAU,KAAK,CAAC,MAAM,SAAU;AAC7G,qBAAa;AAAA,MACf,WAAW,KAAK,CAAC,EAAE,QAAQ,MAAM,MAAM,OAAQ,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,UAAU,KAAK,CAAC,MAAM,QAAS;AAC3G,qBAAa;AAAA,MACf,WAAW,KAAK,CAAC,EAAE,QAAQ,MAAM,MAAM,OAAQ,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,UAAU,KAAK,CAAC,MAAM,QAAS;AAC3G,qBAAa;AAAA,MACf;AAAA,IACF;AACA,QAAI,UAAU,WAAW,GAAG;AAC1B,mBAAa;AAAA,IACf;AACA,QAAI,UAAU,WAAW,GAAG;AAC1B,UAAI,OAAO;AAAA,IACb;AAAA,EACF;AACA,MAAI,IAAI,MAAM;AACZ,QAAI,QAAQ,IAAI,KAAK,KAAK,YAAY;AACtC,QAAI,IAAI,UAAU,SAAS;AACzB,UAAI,QAAQ;AAAA,IACd;AACA,QAAI,IAAI,KAAK,SAAS;AACpB,UAAI,IAAI,SAAS,UAAU;AACzB,YAAI,IAAI,SAAS,IAAI,MAAM,UAAU;AACnC,cAAI,WAAW,IAAI,KAAK,UAAQ,IAAI,MAAM,SAAS;AAAA,QACrD;AAAA,MACF,OAAO;AACL,YAAI,WAAW,IAAI,KAAK;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,MAAI,SAAS,IAAI;AACjB,MAAI,IAAI,SAAS,UAAU;AACzB,aAAS;AAAA,EACX;AACA,MAAI,QAAQ;AAIV,QAAI,OAAO,OAAO;AAChB,UAAI,YAAY,OAAO,MAAM,KAAK,YAAY;AAAA,IAChD,OAAO;AACL,UAAI,YAAY,OAAO,KAAK,YAAY;AAAA,IAC1C;AACA,QAAI,IAAI,UAAU,MAAM,GAAG,CAAC,MAAM,MAAM;AACtC,UAAI,YAAY,IAAI,UAAU,MAAM,CAAC;AAAA,IACvC;AACA,QAAI,IAAI,cAAc,oBAAoB;AACxC,UAAI,YAAY;AAAA,IAClB;AACA,QAAI,IAAI,cAAc,cAAc,IAAI,cAAc,8BAA8B;AAClF,UAAI,IAAI,eAAe,6BAA6B;AAClD,YAAI,SAAS;AAAA,MACf;AACA,UAAI,YAAY;AAAA,IAClB;AACA,QAAI,IAAI,cAAc,cAAc;AAClC,UAAI,YAAY;AAAA,IAClB;AACA,QAAI,OAAO,SAAS,OAAO,MAAM,UAAU;AACzC,UAAI,QAAQ,OAAO,MAAM,SAAS,KAAK,QAAQ,OAAO,EAAE,EAAE,QAAQ,iBAAiB,MAAM;AACzF,UAAI,IAAI,MAAM,YAAY,EAAE,MAAM,GAAG,EAAE,MAAM,iBAAiB;AAC5D,YAAI,QAAQ;AAAA,MACd;AAEA,UAAI,IAAI,OAAO,MAAM,SAAS;AAC9B,UAAI,KAAK,WAAW,OAAO,MAAM,SAAS,IAAI,EAAE;AAAA,IAClD;AAEA,QAAI,OAAO,SAAS,OAAO,MAAM,SAAS;AACxC,UAAI,eAAe,OAAO,MAAM;AAAA,IAClC;AACA,QAAI,CAAC,IAAI,UAAU,QAAQ,WAAW,GAAG;AACvC,UAAI,YAAY;AAAA,IAClB;AACA,QAAI,CAAC,IAAI,UAAU,QAAQ,WAAW,GAAG;AACvC,UAAI,YAAY;AAAA,IAClB;AACA,QAAI,CAAC,IAAI,UAAU,QAAQ,MAAM,KAC5B,CAAC,IAAI,UAAU,QAAQ,wBAAwB,GAAG;AACrD,UAAI,YAAY;AAAA,IAClB;AACA,QAAI,IAAI,cAAc,WAAW;AAC/B,UAAI,YAAY;AAAA,IAClB;AACA,QAAI,CAAC,IAAI,UAAU,QAAQ,QAAQ,GAAG;AACpC,UAAI,YAAY;AAAA,IAClB;AAAA,EACF;AACA,MAAI,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC,GAAG;AAC7B,QAAI,IAAI,IAAI;AAAA,EACd;AACA,MAAI,IAAI,sBAAsB;AAC5B,QAAI,uBAAuB,IAAI,IAAI,oBAAoB;AAAA,EACzD;AAEA,WAAS,QAAQ,OAAO;AACtB,QAAI,QAAQ,IAAI,YAAY;AAC5B,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,UAAU,SAAS,GAAG;AACxB,WAAO,OAAO,KAAK,CAAC;AAAA,EACtB;AACA,MAAI,OAAO;AAAA,IACT,CAAC,uBAAuB,qBAAqB;AAAA,IAC7C,CAAC,uBAAuB,mCAAmC;AAAA,IAC3D,CAAC,uBAAuB,qBAAqB;AAAA,IAC7C,CAAC,uBAAuB,mCAAmC;AAAA,IAC3D,CAAC,iBAAiB,eAAe;AAAA,IACjC,CAAC,iBAAiB,eAAe;AAAA,IACjC,CAAC,iBAAiB,yBAAyB;AAAA,IAC3C,CAAC,kBAAkB,gBAAgB;AAAA,IACnC,CAAC,kBAAkB,gBAAgB;AAAA,IACnC,CAAC,kBAAkB,0BAA0B;AAAA,IAC7C,CAAC,oBAAoB,kBAAkB;AAAA,IACvC,CAAC,oBAAoB,6BAA6B;AAAA,IAClD,CAAC,oBAAoB,2BAA2B;AAAA,IAChD,CAAC,sBAAsB,oBAAoB;AAAA,IAC3C,CAAC,sBAAsB,kBAAkB;AAAA,IACzC,CAAC,sBAAsB,4BAA4B;AAAA,IACnD,CAAC,sBAAsB,0BAA0B;AAAA,IACjD,CAAC,gBAAgB,cAAc;AAAA,IAC/B,CAAC,MAAM,cAAc;AAAA,IACrB,CAAC,sBAAsB,oBAAoB;AAAA,IAC3C,CAAC,sBAAsB,oBAAoB;AAAA,IAC3C,CAAC,QAAQ,sBAAsB,GAAG;AAAA,IAClC,CAAC,uBAAuB,qBAAqB;AAAA,IAC7C,CAAC,uBAAuB,qBAAqB;AAAA,IAC7C,CAAC,SAAS,uBAAuB,GAAG;AAAA,IACpC,CAAC,MAAM,iBAAiB,OAAO;AAAA,IAC/B,CAAC,MAAM,kBAAkB,OAAO;AAAA,IAChC,CAAC,SAAS,oBAAoB,GAAG;AAAA,IACjC,CAAC,QAAQ,sBAAsB,GAAG;AAAA,IAClC,CAAC,QAAQ,uBAAuB,GAAG;AAAA,IACnC,CAAC,QAAQ,uBAAuB,GAAG;AAAA,IACnC,CAAC,QAAQ,uBAAuB,GAAG;AAAA,IACnC,CAAC,WAAW,SAAS;AAAA,IACrB,CAAC,SAAS,WAAW,GAAG;AAAA,IACxB,CAAC,WAAW,MAAM;AAAA,EACpB;AACA,OAAK,QAAQ,OAAO;AACpB,0BAAwB,GAAG;AAC7B;AACe,SAAR,mBAAiB,KAAK;AAC3B,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,kBAAkB,GAAG;AAAA,EAC9B;AACA,QAAM,UAAU,iBAAiB,GAAG;AACpC,MAAI,OAAO,eAAO,GAAG;AACrB,MAAI,YAAY,QAAQ;AACtB,UAAM,WAAW,cAAc,IAAI;AACnC,WAAO,kBAAkB,QAAQ;AAAA,EACnC;AACA,MAAI,OAAO,KAAK,CAAC;AACjB,MAAI,MAAM,CAAC;AACX,QAAM,MAAM,GAAG;AACf,WAAS,GAAG;AACZ,SAAO,IAAI,IAAI;AACjB;;;AC1JA,SAAS,KAAK,MAAM;AAElB,MAAI,OAAO;AACX,MAAI,UAAU,WAAW,GAAG;AAC1B,QAAI,MAAM,UAAU,CAAC;AACrB,QAAI,OAAO,QAAQ,UAAU;AAC3B,UAAI,IAAI,OAAO,CAAC,MAAM,KAAK;AACzB;AAAA;AAAA,UAA4B;AAAA,QAAK,IAAI,mBAAU,UAAU,CAAC,CAAC;AAAA,MAC7D,OAAO;AACL;AAAA;AAAA,UAA4B;AAAA,QAAK,IAAI,mBAAI,UAAU,CAAC,CAAC;AAAA,MACvD;AAAA,IACF,OAAO;AACL;AAAA;AAAA,QAA4B;AAAA,MAAK,IAAI;AAAA,IACvC;AAAA,EACF,WAAW,UAAU,WAAW,GAAG;AACjC,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,aAAO,KAAK,IAAI,SAAU,GAAG;AAC3B,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,iBAAO,KAAK,MAAM,MAAM,CAAC;AAAA,QAC3B,OAAO;AACL,iBAAO,KAAK,CAAC;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH,WAAW,OAAO,SAAS,UAAU;AACnC,UAAI,QAAQ,MAAM;AAChB,eAAO,KAAK,IAAI;AAAA,MAClB;AAAA,IACF,WAAW,UAAU,MAAM;AACzB,WAAK,UAAU,KAAK,IAAI,IAAI;AAAA,IAC9B,WAAW,UAAU,MAAM;AACzB,WAAK,UAAU,KAAK,IAAI,IAAI;AAAA,IAC9B,WAAW,aAAa,MAAM;AAC5B,WAAK,aAAa,KAAK,OAAO,IAAI;AAAA,IACpC,OAAO;AACL,cAAQ,IAAI,IAAI;AAAA,IAClB;AACA;AAAA,EACF;AACF;AACA,eAAQ,IAAI;AACZ,IAAO,eAAQ;;;ACpGf,SAAS,QAAQ,MAAM;AACrB,SAAO,OAAO,SAAS;AACzB;AACA,SAAS,QAAQ,MAAM;AACrB,SAAO,QAAQ;AACjB;AACA,SAAS,QAAQ,MAAM;AACrB,SAAQ,KAAK,QAAQ,GAAG,MAAM,KAAK,KAAK,QAAQ,GAAG,MAAM,MAAQ,OAAO,SAAS,YAAY,EAAE,aAAa;AAC9G;AACA,IAAI,QAAQ,CAAC,QAAQ,UAAU,QAAQ,QAAQ;AAC/C,SAAS,cAAc,MAAM;AAC3B,MAAI,OAAO,MAAM,MAAM,WAAW;AAClC,MAAI,CAAC,MAAM;AACT;AAAA,EACF;AACA,MAAI,OAAO,MAAM,MAAM,MAAM;AAC7B,SAAO,QAAQ,MAAM,QAAQ,IAAI,IAAI;AACvC;AACA,SAAS,aAAa,MAAM;AAC1B,MAAI,MAAM,MAAM,MAAM,WAAW;AACjC,MAAI,CAAC,KAAK;AACR;AAAA,EACF;AACA,SAAO,MAAM,KAAK,OAAO;AAC3B;AACA,SAAS,SAAS,MAAM;AACtB,SAAO,KAAK,CAAC,MAAM;AACrB;AAKA,SAAS,MAAM,MAAM;AACnB,MAAI,QAAQ,IAAI,GAAG;AAEjB,QAAI,QAAQ,IAAI,GAAG;AACjB,aAAO,aAAK,IAAI;AAAA,IAClB;AACA,QAAI,QAAQ,IAAI,GAAG;AACjB,UAAI,MAAM,mBAAI,IAAI;AAElB,UAAI,cAAc,GAAG,GAAG;AACtB,eAAO,aAAK,WAAW;AAAA,MACzB;AACA,UAAI,eAAe,aAAa,GAAG;AACnC,UAAI,cAAc;AAChB,eAAO,mBAAQ,YAAY;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AACA,QAAI,SAAS,IAAI,GAAG;AAClB,aAAO,mBAAQ,IAAI;AAAA,IACrB;AAAA,EACF,WAAW,EAAE,cAAc,OAAO;AAChC,WAAO,mBAAI,IAAI;AAAA,EACjB,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,IAAO,oBAAQ;;;AChEA,SAAR,eAAkB,aAAa,QAAQ;AAC5C,gBAAc,eAAe,CAAC;AAC9B,MAAI,OAAO;AACX,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,OAAK,YAAY,QAAQ;AACvB,YAAQ,OAAO,QAAQ;AACvB,QAAI,UAAU,QAAW;AACvB,kBAAY,QAAQ,IAAI;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;;;ACbe,SAAR,cAAkB,QAAQ,QAAQ,QAAQ;AAC/C,MAAI,MAAM,SAAS;AACnB,SAAO,SAAU,KAAK,KAAK,IAAI,MAAM,GAAG;AAC1C;;;ACHe,SAAR,aAAkB,GAAG;AAC1B,SAAO,IAAI,IAAI,KAAK;AACtB;;;ACCe,SAAR,mBAAkB,GAAG;AAC1B,SAAQ,KAAK,IAAI,CAAC,KAAK,MAAO,IAAK,IAAK,aAAK,CAAC,IAAI;AACpD;;;ACHe,SAAR,cAAkB,QAAQ,KAAK,QAAQ;AAC5C,MAAI,MAAM,SAAS;AACnB,MAAI,MAAM,MAAM;AAChB,QAAM,KAAK,KAAM,IAAI,QAAQ,IAAI,MAAO,GAAG;AAC3C,SAAQ,KAAK,IAAI,OAAO,UAAU,IAAI,IAAI;AAC5C;;;ACLe,SAAR,cAAkB,QAAQ,IAAI;AACnC,MAAI,SAAS,MAAM;AACnB,MAAI,KAAK;AACT,MAAI,MAAM,UAAU,IAAI,KAAK,KAAK,EAAE;AACpC,WAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,UAAM,SAAS,KAAK,IAAI,GAAG;AAC3B,WAAO,UAAU,IAAI,KAAK,KAAK,KAAM,KAAK,KAAM,IAAI,QAAQ,IAAI,MAAO,MAAM,CAAE,IAAI;AACnF,WAAO;AACP,QAAI,KAAK,IAAI,IAAI,KAAK,OAAc;AAClC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACDO,SAAS,OAAO;AACrB,MAAI,MAAM,KAAK,IAAI,KAAK;AACxB,OAAK,KAAK,IAAI,MAAM;AACpB,MAAI,EAAE,QAAQ,OAAO;AACnB,SAAK,KAAK;AAAA,EACZ;AACA,MAAI,EAAE,QAAQ,OAAO;AACnB,SAAK,KAAK;AAAA,EACZ;AACA,OAAK,IAAI,KAAK,KAAK,KAAK,EAAE;AAC1B,MAAI,KAAK,QAAQ;AACf,QAAI,KAAK,QAAQ;AACf,WAAK,KAAK,KAAK,IAAI,KAAK,MAAM;AAAA,IAChC,OAAO;AACL,WAAK,KAAK,cAAM,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,GAAG,KAAK,IAAI,KAAK,MAAM,CAAC;AAAA,IACtE;AAAA,EACF,OAAO;AACL,QAAI,CAAC,KAAK,IAAI;AACZ,UAAI,KAAK,GAAG;AACV,aAAK,KAAK,KAAK;AAAA,MACjB,OAAO;AACL,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACF;AAKO,SAAS,QAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAEZ,MAAI,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM;AAC5E,WAAO;AAAA,EACT;AAEA,MAAI,GAAG;AACP,MAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,KAAK,OAAO;AAC9C,WAAO;AAAA,EACT,OAAO;AACL,QAAI,KAAK,QAAQ;AACf,UAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,mBAAW,MAAM,KAAK,KAAK;AAC5D,UAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,SAAS,MAAM,GAAG,CAAC;AAAA,IACxE,OAAO;AACL,UAAI,SAAS,KAAK,IAAI,GAAG;AACzB,UAAI,KAAK,cAAM,KAAK,GAAG,KAAK,MAAM;AAClC,UAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,mBAAW,MAAM,KAAK,KAAK;AAC5D,UAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,EAAE;AAAA,IAC9C;AACA,MAAE,IAAI;AACN,MAAE,IAAI;AACN,WAAO;AAAA,EACT;AACF;AAIO,SAAS,QAAQ,GAAG;AACzB,MAAI,IAAI,EAAE,IAAI,KAAK;AACnB,MAAI,IAAI,EAAE,IAAI,KAAK;AACnB,MAAI,KAAK;AAET,MAAI,KAAK,QAAQ;AACf,UAAM,UAAU,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,GAAG,CAAC;AAAA,EACjE,OAAO;AACL,QAAI,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,GAAG;AACzC,UAAM,cAAM,KAAK,GAAG,EAAE;AACtB,QAAI,QAAQ,OAAO;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,mBAAW,KAAK,QAAQ,KAAK,KAAK,IAAI,KAAK,GAAG;AAEpD,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAI,QAAQ,CAAC,YAAY,yCAAyC,gBAAgB,6BAA6B,sBAAsB,MAAM;AAClJ,IAAO,eAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACrGO,SAASC,QAAO;AAEvB;AAEA,SAAS,SAAS,IAAI;AACpB,SAAO;AACT;AAGO,IAAIC,SAAQ,CAAC,WAAW,UAAU;AACzC,IAAO,kBAAQ;AAAA,EACb,MAAMC;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAOD;AACT;;;ACbA,IAAI,QAAQ,CAAC,cAAM,eAAO;AAC1B,IAAIE,SAAQ,CAAC;AACb,IAAI,YAAY,CAAC;AAEjB,SAAS,IAAI,MAAM,GAAG;AACpB,MAAI,MAAM,UAAU;AACpB,MAAI,CAAC,KAAK,OAAO;AACf,YAAQ,IAAI,CAAC;AACb,WAAO;AAAA,EACT;AACA,YAAU,GAAG,IAAI;AACjB,OAAK,MAAM,QAAQ,SAAU,GAAG;AAC9B,IAAAA,OAAM,EAAE,YAAY,CAAC,IAAI;AAAA,EAC3B,CAAC;AACD,SAAO;AACT;AAIO,SAAS,sBAAsB,GAAG;AACvC,SAAO,EAAE,QAAQ,eAAe,GAAG,EAAE,KAAK,EAAE,QAAQ,MAAM,GAAG;AAC/D;AAEO,SAAS,IAAI,MAAM;AACxB,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,MAAI,IAAI,KAAK,YAAY;AACzB,MAAI,OAAOC,OAAM,CAAC,MAAM,eAAe,UAAUA,OAAM,CAAC,CAAC,GAAG;AAC1D,WAAO,UAAUA,OAAM,CAAC,CAAC;AAAA,EAC3B;AACA,MAAI,sBAAsB,CAAC;AAC3B,MAAI,KAAKA,UAAS,UAAUA,OAAM,CAAC,CAAC,GAAG;AACrC,WAAO,UAAUA,OAAM,CAAC,CAAC;AAAA,EAC3B;AACF;AAEO,SAAS,QAAQ;AACtB,QAAM,QAAQ,GAAG;AACnB;AACA,IAAO,sBAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACF;;;AC9CA,IAAI,aAAa;AAAA,EACf,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,GAAG;AAAA,IACH,GAAG;AAAA,IACH,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,IACH,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,WAAW;AAAA,IACT,GAAG;AAAA,IACH,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,KAAK;AAAA,IACH,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,GAAG;AAAA,IACH,GAAG;AAAA,IACH,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,IACH,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,GAAG;AAAA,IACH,GAAG;AAAA,IACH,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,OAAO;AAAA,IACL,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,aAAa;AAAA,EACf;AAAA,EACA,QAAQ;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,IACH,aAAa;AAAA,EACf;AACF;AAEA,IAAO,oBAAQ;;;AC5Nf,IAAM,QAAQ,kBAAU;AAEjB,SAAS,aAAa,GAAG,GAAG,IAAI,KAAK;AAC1C,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,MAAM,KAAK,MAAM;AACrB,MAAI,IAAI;AACR,MAAI,KAAK;AACP,SAAK,IAAI,MAAM,QAAQ,MAAM,MAAM,KAAK;AACxC,SAAK,IAAI;AACT,SAAK;AAAA,EACP,OAAO;AACL,QAAI,KAAK,KAAK,EAAE;AAAA,EAClB;AACA,MAAI,OAAO,KAAK,MAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACO,SAAS,OAAO,GAAG,GAAG,IAAI,OAAOC,SAAQ;AAC9C,MAAI,CAAC,GAAG;AACN,QAAI,UAAU,MAAM,mBAAW,KAAK;AACpC,QAAI,CAAC,SAAS;AACZ,gBAAU;AAAA,IACZ;AACA,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,SAAK,QAAQ;AAAA,EACf;AAEA,MAAI,MAAM,CAAC,GAAG;AACZ,SAAK,IAAM,IAAM,MAAM;AAAA,EACzB;AACA,MAAI,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,OAAO;AACvC,IAAAA,UAAS;AACT,QAAI;AAAA,EACN;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQA;AAAA,EACV;AACF;;;ACjDA,IAAI,SAAS;AAAA,EACX,OAAO;AAAA,IACL,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,eAAe;AAAA,IACb,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,KAAK;AAAA,IACH,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,eAAe;AAAA,IACb,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,iBAAiB;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,IACd,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA,iBAAiB;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,SAAS;AAAA,EACX;AAAA,EACA,gBAAgB;AAAA,IACd,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACb,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,EACX;AACF;AAEA,KAAS,OAAO,QAAQ;AAClB,EAAAC,SAAQ,OAAO,GAAG;AACtB,MAAI,CAACA,OAAM,WAAW;AACpB;AAAA,EACF;AACA,SAAOA,OAAM,SAAS,IAAIA;AAC5B;AALM,IAAAA;AADG;AAQT,IAAO,gBAAQ;;;AC9vCf,SAAS,MAAM,WAAW,cAAc,GAAG,GAAG,IAAI,KAAK,UAAU;AAC/D,MAAI,MAAM,CAAC;AAEX,MAAI,cAAc,UAAa,cAAc,QAAQ;AACnD,QAAI,aAAa;AAAA,EACnB,OAAO;AACL,QAAI,aAAa;AAAA,EACnB;AAEA,MAAI,cAAc;AAChB,QAAI,eAAe,aAAa,IAAI,UAAU;AAC9C,QAAI,IAAI,aAAa,CAAC,MAAM,KAAK,IAAI,aAAa,CAAC,MAAM,KAAK,IAAI,aAAa,CAAC,MAAM,GAAG;AACvF,UAAI,aAAa;AAAA,IACnB;AACA,QAAI,IAAI,aAAa,SAAS,GAAG;AAC/B,UAAI,IAAI,aAAa,CAAC,MAAM,KAAK,IAAI,aAAa,CAAC,MAAM,KAAK,IAAI,aAAa,CAAC,MAAM,KAAK,IAAI,aAAa,CAAC,MAAM,GAAG;AACpH,YAAI,aAAa;AACjB,YAAI,aAAa,CAAC,KAAK;AACvB,YAAI,aAAa,CAAC,KAAK;AACvB,YAAI,aAAa,CAAC,KAAK;AACvB,YAAI,aAAa,CAAC,IAAK,IAAI,aAAa,CAAC,IAAI,MAAa;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AAEA,MAAI,UAAU;AACZ,QAAI,aAAa;AACjB,QAAI,QAAQ;AAAA,EACd;AACA,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,MAAM;AACV,SAAO;AACT;AAEA,IAAO,gBAAQ;;;ACIf,IAAI,iBAAiB,CAAC;AAuBP,SAAR,QAAyB,KAAK,MAAM,SAAS;AAClD,MAAI,gBAAgB,aAAa;AAC/B,WAAO,aAAa,KAAK,MAAM,OAAO;AAAA,EACxC;AACA,SAAO,EAAE,OAAO,gBAAgB,KAAK,IAAI,EAAE;AAC7C;AAQA,SAAS,aAAa,KAAK,MAAM,SAAS;AACxC,MAAI,qBAAqB;AACzB,MAAI,YAAY,UAAa,QAAQ,uBAAuB,OAAO;AACjE,yBAAqB;AAAA,EACvB;AACA,MAAI,OAAO,IAAI,SAAS,IAAI;AAC5B,MAAI,iBAAiB,mBAAmB,IAAI;AAC5C,MAAI,SAAS,WAAW,MAAM,cAAc;AAC5C,MAAI,WAAW,aAAa,MAAM,QAAQ,gBAAgB,kBAAkB;AAC5E,MAAIC,WAAU,EAAE,QAAgB,SAAmB;AACnD,iBAAe,GAAG,IAAIA;AACtB,SAAOA;AACT;AAOA,eAAe,gBAAgB,KAAK,MAAM;AACxC,MAAI,WAAW,CAAC;AAChB,MAAI,eAAe,MAAM,KAAK,cAAc;AAE5C,WAAS,eAAe,eAAe,GAAG,gBAAgB,GAAG,gBAAgB;AAC3E,QAAI,QAAQ,MAAM,KAAK,SAAS,YAAY;AAE5C,QAAI,UAAU,MAAM,MAAM,YAAY;AACtC,QAAI,OAAO;AACX,QAAI,MAAM,CAAC,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC;AAC9C,QAAI,mBAAmB,MAAM,eAAe,EAAE,IAAI,gBAAgB;AAClE,QAAI,MAAM,CAAC,MAAM,cAAc,gBAAgB,CAAC,GAAG,MAAM,cAAc,gBAAgB,CAAC,CAAC,EAAE,IAAI,gBAAgB;AAE/G,QAAI,OAAO,iBAAiB,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AACrD,QAAI,OAAO,iBAAiB,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAErD,QAAI,qBAAqB,KAAK,CAAC;AAC/B,QAAI,sBAAsB,KAAK,CAAC;AAChC,QAAI,QAAQ,CAAC;AAEb,aAAS,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,KAAK;AACpC,eAAS,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,KAAK;AACpC,YAAI,QAAQ,IAAI,IAAI,CAAC,IAAI;AACzB,cAAM,KAAK,CAAC,CAAC,iBAAiB,oBAAoB,KAAK,CAAC,GAAG,iBAAiB,mBAAmB,KAAK,CAAC,CAAC,CAAC;AAAA,MACzG;AAAA,IACF;AAEA,aAAS,KAAK;AAAA,MACZ;AAAA,MACA;AAAA,MACA,IAAI,CAAC,CAAC,MAAM,IAAI;AAAA,MAChB,KAAK;AAAA,IACP,CAAC;AAAA,EACH;AAEA,MAAI,UAAU;AAAA,IACZ,QAAQ;AAAA,MACN,WAAW;AAAA,IACb;AAAA,IACA;AAAA,EACF;AACA,iBAAe,GAAG,IAAI;AACtB,SAAO;AACT;AAOO,SAAS,YAAY,UAAU;AAEpC,MAAI,aAAa,QAAW;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,SAAS,MAAM,GAAG;AAC9B,SAAO,MAAM,IAAI,kBAAkB;AACrC;AAMA,SAAS,mBAAmB,OAAO;AACjC,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;AAAA,EACT;AACA,MAAI,WAAW,MAAM,CAAC,MAAM;AAC5B,MAAI,UAAU;AACZ,YAAQ,MAAM,MAAM,CAAC;AAAA,EACvB;AACA,MAAI,UAAU,QAAQ;AACpB,WAAO,EAAE,MAAM,QAAQ,WAAW,CAAC,UAAU,MAAM,MAAM,QAAQ,KAAK;AAAA,EACxE;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,WAAW,CAAC;AAAA,IACZ,MAAM,eAAe,KAAK,KAAK;AAAA,IAC/B,QAAQ;AAAA,EACV;AACF;AAEA,SAAS,iBAAiB,SAAS;AACjC,SAAQ,UAAW,KAAK,KAAK;AAC/B;AAEA,SAAS,iBAAiB,SAAS;AACjC,SAAQ,UAAU,OAAQ,KAAK,KAAK;AACtC;AAEA,SAAS,mBAAmB,MAAM;AAChC,MAAI,UAAU,KAAK,SAAS,GAAG,KAAK;AACpC,MAAI,YAAY,IAAI;AAClB,WAAO;AAAA,EACT;AACA,YAAU,KAAK,SAAS,GAAG,IAAI;AAC/B,MAAI,YAAY,IAAI;AAClB,YAAQ,KAAK,mEAAmE;AAAA,EAClF;AACA,SAAO;AACT;AAEA,SAAS,WAAW,MAAM,gBAAgB;AACxC,SAAO;AAAA,IACL,SAAS,KAAK,SAAS,GAAG,cAAc;AAAA,IACxC,gBAAgB,KAAK,SAAS,IAAI,cAAc;AAAA,IAChD,WAAW,KAAK,SAAS,IAAI,cAAc;AAAA,IAC3C,WAAW,aAAa,MAAM,IAAI,KAAK,CAAC,EAAE,KAAK;AAAA,IAC/C,mBAAmB,KAAK,WAAW,KAAK,cAAc;AAAA,IACtD,mBAAmB,KAAK,WAAW,KAAK,cAAc;AAAA,IACtD,iBAAiB,KAAK,WAAW,KAAK,cAAc;AAAA,IACpD,iBAAiB,KAAK,WAAW,KAAK,cAAc;AAAA,EACtD;AACF;AAEA,SAAS,aAAa,MAAMC,QAAO,KAAK;AACtC,SAAO,OAAO,aAAa,MAAM,MAAM,IAAI,WAAW,KAAK,OAAO,MAAMA,QAAO,GAAG,CAAC,CAAC;AACtF;AAEA,SAAS,aAAa,MAAM,QAAQ,gBAAgB,oBAAoB;AACtE,MAAI,aAAa;AACjB,MAAI,QAAQ,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,OAAO,WAAW,KAAK;AACzC,QAAI,YAAY,eAAe,MAAM,YAAY,cAAc;AAC/D,QAAI,QAAQ,cAAc,MAAM,YAAY,WAAW,gBAAgB,kBAAkB;AACzF,QAAI,iBAAiB,KAAK;AAAA,MACxB,KAAK,UAAU,iBAAiB,UAAU,kBAAkB,UAAU;AAAA,IAAiB;AACzF,QAAI,iBAAiB,KAAK;AAAA,MACxB,KAAK,UAAU,gBAAgB,UAAU,iBAAiB,UAAU;AAAA,IAAgB;AAEtF,UAAM,KAAK;AAAA,MACT,IAAI,CAAC,iBAAiB,UAAU,cAAc,GAAG,iBAAiB,UAAU,aAAa,CAAC;AAAA,MAC1F,KAAK,CAAC,iBAAiB,UAAU,iBAAiB,GAAG,iBAAiB,UAAU,gBAAgB,CAAC;AAAA,MACjG,KAAK,CAAC,gBAAgB,cAAc;AAAA,MACpC,OAAO,UAAU;AAAA,MACjB,KAAK,SAAS,KAAK;AAAA,IACrB,CAAC;AACD,QAAI,UAAU;AACd,QAAI,uBAAuB,OAAO;AAChC,gBAAU;AAAA,IACZ;AACA,kBAAc,MAAM,UAAU,gBAAgB;AAAA,EAChD;AACA,SAAO;AACT;AAMA,SAAS,SAAS,OAAO;AACvB,SAAO,MAAM,IAAI,SAAU,GAAG;AAC5B,WAAO,CAAC,iBAAiB,EAAE,cAAc,GAAG,iBAAiB,EAAE,aAAa,CAAC;AAAA,EAC/E,CAAC;AACH;AAEA,SAAS,eAAe,MAAM,QAAQ,gBAAgB;AACpD,SAAO;AAAA,IACL,MAAM,aAAa,MAAM,SAAS,GAAG,SAAS,EAAE,EAAE,KAAK;AAAA,IACvD,QAAQ,aAAa,MAAM,SAAS,IAAI,SAAS,KAAK,CAAC,EAAE,KAAK;AAAA,IAC9D,eAAe,KAAK,WAAW,SAAS,IAAI,cAAc;AAAA,IAC1D,eAAe,KAAK,WAAW,SAAS,IAAI,cAAc;AAAA,IAC1D,gBAAgB,KAAK,WAAW,SAAS,KAAK,cAAc;AAAA,IAC5D,gBAAgB,KAAK,WAAW,SAAS,KAAK,cAAc;AAAA,IAC5D,kBAAkB,KAAK,WAAW,SAAS,KAAK,cAAc;AAAA,IAC9D,mBAAmB,KAAK,WAAW,SAAS,KAAK,cAAc;AAAA,IAC/D,eAAe,KAAK,SAAS,SAAS,KAAK,cAAc;AAAA,EAC3D;AACF;AAEA,SAAS,cAAc,MAAM,QAAQ,YAAY,gBAAgB,oBAAoB;AACnF,MAAI,cAAc,SAAS;AAC3B,MAAI,mBAAmB;AAEvB,MAAI,uBAAuB,OAAO;AAChC,uBAAmB;AAAA,EACrB;AAEA,MAAI,mBAAmB,CAAC;AACxB,WAAS,IAAI,GAAG,IAAI,WAAW,eAAe,KAAK;AACjD,QAAI,SAAS;AAAA,MACX,eAAe,KAAK,WAAW,cAAc,IAAI,kBAAkB,cAAc;AAAA,MACjF,gBAAgB,KAAK,WAAW,cAAc,IAAI,mBAAmB,GAAG,cAAc;AAAA,IAExF;AAEA,QAAI,uBAAuB,OAAO;AAChC,aAAO,mBAAmB,KAAK,WAAW,cAAc,IAAI,mBAAmB,GAAG,cAAc;AAChG,aAAO,oBAAoB,KAAK,WAAW,cAAc,IAAI,mBAAmB,IAAI,cAAc;AAAA,IACpG;AAEA,qBAAiB,KAAK,MAAM;AAAA,EAC9B;AACA,SAAO;AACT;;;AC7QA,SAAS,WAAW,SAAS,UAAU;AACrC,MAAI,EAAE,gBAAgB,aAAa;AACjC,WAAO,IAAI,WAAW,OAAO;AAAA,EAC/B;AAEA,OAAK,UAAU;AAEf,OAAK,UAAU;AAEf,OAAK;AAEL,OAAK;AACL,aAAW,YAAY,SAAU,OAAO;AACtC,QAAI,OAAO;AACT,YAAM;AAAA,IACR;AAAA,EACF;AACA,MAAI,OAAO,kBAAU,OAAO;AAC5B,MAAI,OAAO,SAAS,UAAU;AAC5B,aAAS,oCAAoC,OAAO;AACpD;AAAA,EACF;AACA,MAAI,UAAU,WAAW,YAAY,IAAI,KAAK,QAAQ;AACtD,MAAI,CAAC,SAAS;AACZ,aAAS,yCAAyC,OAAO;AACzD;AAAA,EACF;AACA,MAAI,KAAK,aAAa,KAAK,cAAc,QAAQ;AAC/C,QAAI,WAAW,MAAM,eAAO,KAAK,SAAS;AAC1C,QAAI,UAAU;AACZ,WAAK,eAAe,KAAK,iBAAiB,SAAS,UAAU,SAAS,QAAQ,MAAM,GAAG,IAAI;AAC3F,WAAK,QAAQ,SAAS;AACtB,WAAK,YAAY,SAAS,YAAY,SAAS,YAAY,KAAK;AAAA,IAClE;AAAA,EACF;AACA,OAAK,KAAK,KAAK,MAAM;AACrB,OAAK,OAAO,KAAK,QAAQ;AACzB,OAAK,QAAQ,KAAK,SAAS;AAC3B,OAAK,OAAO,KAAK,QAAQ,KAAK;AAE9B,MAAI,UAAU,OAAU,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM;AACxE,MAAI,MAAM,aAAgB,QAAQ,GAAG,QAAQ,GAAG,QAAQ,IAAI,KAAK,GAAG;AACpE,MAAI,WAAW,YAAY,KAAK,QAAQ;AAExC,MAAI,WAAW,KAAK,SAAS;AAAA,IAAM,KAAK;AAAA,IAAW,KAAK;AAAA,IAAc,QAAQ;AAAA,IAAG,QAAQ;AAAA,IAAG,IAAI;AAAA,IAAI,IAAI;AAAA,IACtG;AAAA,EAAQ;AAEV,iBAAO,MAAM,IAAI;AACjB,iBAAO,MAAM,OAAO;AAGpB,OAAK,IAAI,QAAQ;AACjB,OAAK,IAAI,QAAQ;AACjB,OAAK,KAAK,QAAQ;AAClB,OAAK,SAAS,QAAQ;AAGtB,OAAK,KAAK,IAAI;AACd,OAAK,IAAI,IAAI;AACb,OAAK,MAAM,IAAI;AAGf,OAAK,QAAQ;AAGb,MAAI,UAAU,QAAQ,OAAO,KAAK,SAAS,YAAY;AACrD,SAAK,KAAK;AAAA,EACZ;AAGA,WAAS,MAAM,IAAI;AACrB;AACA,WAAW,cAAc;AACzB,WAAW,YAAY,MAAM;AAC7B,IAAO,eAAQ;;;AC9FR,SAAS,cAAc,QAAQ,MAAM;AAC1C,MAAI,OAAO,eAAe,KAAK,YAAY;AACzC,WAAO;AAAA,EACT,WAAW,OAAO,MAAM,KAAK,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,EAAE,IAAI,OAAgB;AAGhF,WAAO;AAAA,EACT,WAAW,OAAO,eAAe,YAAY;AAC3C,WAAQ,OAAO,aAAa,CAAC,MAAM,KAAK,aAAa,CAAC,KAAK,OAAO,aAAa,CAAC,MAAM,KAAK,aAAa,CAAC,KAAK,OAAO,aAAa,CAAC,MAAM,KAAK,aAAa,CAAC;AAAA,EAC9J,WAAW,OAAO,eAAe,YAAY;AAC3C,WAAQ,OAAO,aAAa,CAAC,MAAM,KAAK,aAAa,CAAC,KAAK,OAAO,aAAa,CAAC,MAAM,KAAK,aAAa,CAAC,KAAK,OAAO,aAAa,CAAC,MAAM,KAAK,aAAa,CAAC,KAAK,OAAO,aAAa,CAAC,MAAM,KAAK,aAAa,CAAC,KAAK,OAAO,aAAa,CAAC,MAAM,KAAK,aAAa,CAAC,KAAK,OAAO,aAAa,CAAC,MAAM,KAAK,aAAa,CAAC,KAAK,OAAO,aAAa,CAAC,MAAM,KAAK,aAAa,CAAC;AAAA,EAC1W,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAeO,SAAS,qBAAqB,GAAG,IAAI,GAAG;AAC7C,MAAI,YAAY,EAAE;AAClB,MAAI,WAAW,EAAE;AACjB,MAAI,SAAS,EAAE,IAAI,EAAE,IAAI;AAEzB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAOJ,MAAI,WAAW,CAAC,WAAW,WAAW,SAAS,SAAS;AACtD,eAAW,CAAC;AAAA,EACd,WAAW,WAAW,WAAW,WAAW,QAAQ,SAAS;AAC3D,eAAW;AAAA,EACb,WAAW,WAAW,CAAC,SAAS;AAG9B,WAAO,EAAE,GAAG,WAAW,GAAG,WAAW,GAAG,EAAE,EAAE;AAAA,EAC9C,WAAW,WAAW,SAAS;AAE7B,WAAO,EAAE,GAAG,UAAU,GAAG,UAAU,GAAG,EAAE,EAAE;AAAA,EAC5C;AAEA,MAAI,YAAY,KAAK,IAAI;AACvB,iBAAc,IAAI,KAAK;AAAA,EACzB;AACA,YAAU,KAAK,IAAI,QAAQ;AAC3B,YAAU,KAAK,IAAI,QAAQ;AAC3B,aAAW,UAAU;AACrB,OAAK,IAAK,KAAK,KAAK,IAAQ,KAAK,QAAQ;AACzC,SAAO;AAAA,IACL,IAAI,KAAK,UAAU,UAAU,KAAK,IAAI,SAAS;AAAA,IAC/C,IAAI,KAAK,UAAU,UAAU,KAAK,IAAI,SAAS;AAAA,IAC/C,IAAK,MAAM,IAAI,MAAO,UAAU;AAAA,EAClC;AACF;AAEO,SAAS,qBAAqB,GAAG,IAAI,GAAG,GAAG;AAGhD,MAAI,QAAQ;AACZ,MAAI,SAAU,QAAQ;AACtB,MAAI,UAAU;AAEd,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,MAAI,IAAI,EAAE;AACV,MAAI,IAAI,EAAE;AACV,MAAIC,KAAI,EAAE,IAAI,EAAE,IAAI;AACpB,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,MAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC3B,OAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAIA,KAAIA,EAAC;AAGpC,MAAI,IAAI,IAAI,OAAO;AAEjB,gBAAY;AAIZ,QAAI,KAAK,IAAI,OAAO;AAClB,iBAAW;AACX,eAAS,CAAC;AACV,aAAO;AAAA,QACL,GAAG,EAAE;AAAA,QACL,GAAG,EAAE;AAAA,QACL,GAAG,EAAE;AAAA,MACP;AAAA,IACF;AAAA,EACF,OAAO;AAGL,gBAAY,KAAK,MAAM,GAAG,CAAC;AAAA,EAC7B;AAWA,OAAKA,KAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAM,KAAK,KAAK,IAAM,MAAM,IAAM,MAAM,KAAK,EAAE;AACpD,UAAQ,MAAM,IAAM,MAAM;AAC1B,UAAQ,KAAK;AACb,SAAO;AAIP,KAAG;AACD;AACA,SAAK,IAAI,KAAK,KAAK,IAAM,KAAK,QAAQ,KAAK;AAG3C,aAAS,IAAI,QAAQA,KAAI,QAAQ,MAAM,IAAM,KAAK,QAAQ;AAE1D,SAAK,KAAK,MAAM,KAAK;AACrB,SAAK,IAAM,KAAK,KAAK,IAAM,MAAM,IAAM,MAAM,KAAK,EAAE;AACpD,WAAO,MAAM,IAAM,MAAM;AACzB,WAAO,KAAK;AACZ,YAAQ,OAAO,QAAQ,OAAO;AAC9B,YAAQ;AACR,YAAQ;AAAA,EACV,SACO,QAAQ,QAAQ,UAAU,OAAO;AAGxC,aAAW,KAAK,KAAK,OAAO,KAAK,IAAI,IAAI,CAAC;AAC1C,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAWO,SAAS,kBAAkB,GAAG,YAAY,cAAc;AAC7D,MAAI,eAAe,YAAY;AAG7B,WAAO;AAAA,MACL,GAAG,EAAE,IAAI,aAAa,CAAC;AAAA,MACvB,GAAG,EAAE,IAAI,aAAa,CAAC;AAAA,MACvB,GAAG,EAAE,IAAI,aAAa,CAAC;AAAA,IACzB;AAAA,EACF,WAAW,eAAe,YAAY;AACpC,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,OAAO,aAAa,CAAC;AAGzB,WAAO;AAAA,MACL,GAAG,QAAQ,EAAE,IAAI,QAAQ,EAAE,IAAI,QAAQ,EAAE,KAAK;AAAA,MAC9C,GAAG,QAAQ,QAAQ,EAAE,IAAI,EAAE,IAAI,QAAQ,EAAE,KAAK;AAAA,MAC9C,GAAG,QAAQ,CAAC,QAAQ,EAAE,IAAI,QAAQ,EAAE,IAAI,EAAE,KAAK;AAAA,IACjD;AAAA,EACF;AACF;AAMO,SAAS,oBAAoB,GAAG,YAAY,cAAc;AAC/D,MAAI,eAAe,YAAY;AAG7B,WAAO;AAAA,MACL,GAAG,EAAE,IAAI,aAAa,CAAC;AAAA,MACvB,GAAG,EAAE,IAAI,aAAa,CAAC;AAAA,MACvB,GAAG,EAAE,IAAI,aAAa,CAAC;AAAA,IACzB;AAAA,EACF,WAAW,eAAe,YAAY;AACpC,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,OAAO,aAAa,CAAC;AACzB,QAAI,SAAS,EAAE,IAAI,SAAS;AAC5B,QAAI,SAAS,EAAE,IAAI,SAAS;AAC5B,QAAI,SAAS,EAAE,IAAI,SAAS;AAI5B,WAAO;AAAA,MACL,GAAG,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,MACnC,GAAG,CAAC,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,MACpC,GAAG,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,IACrC;AAAA,EACF;AACF;;;ACnOA,SAAS,YAAY,MAAM;AACzB,SAAQ,SAAS,cAAc,SAAS;AAC1C;AAEe,SAAR,wBAAkB,QAAQ,MAAM,OAAO;AAE5C,MAAI,cAAc,QAAQ,IAAI,GAAG;AAC/B,WAAO;AAAA,EAGT;AAGA,MAAI,OAAO,eAAe,eAAe,KAAK,eAAe,aAAa;AACxE,WAAO;AAAA,EACT;AAGA,MAAI,WAAW,OAAO;AACtB,MAAI,YAAY,OAAO;AACvB,MAAI,OAAO,eAAe,eAAe;AACvC,QAAI,gBAAgB,eAAe,QAAQ,OAAO,KAAK;AACvD,QAAI,kBAAkB,GAAG;AACvB,aAAO;AAAA,IACT;AACA,eAAW;AACX,gBAAY;AAAA,EACd;AAEA,MAAI,SAAS,KAAK;AAClB,MAAI,SAAS,KAAK;AAClB,MAAI,UAAU,KAAK;AACnB,MAAI,KAAK,eAAe,eAAe;AACrC,aAAS;AACT,aAAS;AACT,cAAU;AAAA,EACZ;AAGA,MAAI,cAAc,WAAW,aAAa,UAAU,CAAC,YAAY,OAAO,UAAU,KAAK,CAAC,YAAY,KAAK,UAAU,GAAG;AACpH,WAAO;AAAA,EACT;AAGA,UAAQ,qBAAqB,OAAO,WAAW,QAAQ;AAEvD,MAAI,YAAY,OAAO,UAAU,GAAG;AAClC,YAAQ,kBAAkB,OAAO,OAAO,YAAY,OAAO,YAAY;AAAA,EACzE;AACA,MAAI,YAAY,KAAK,UAAU,GAAG;AAChC,YAAQ,oBAAoB,OAAO,KAAK,YAAY,KAAK,YAAY;AAAA,EACvE;AACA,UAAQ,qBAAqB,OAAO,SAAS,QAAQ,MAAM;AAE3D,MAAI,KAAK,eAAe,eAAe;AACrC,QAAI,sBAAsB,eAAe,MAAM,MAAM,KAAK;AAC1D,QAAI,wBAAwB,GAAG;AAC7B,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,eAAe,QAAQC,WAAS,OAAO;AACrD,MAAI,OAAO,UAAU,QAAQ,OAAO,MAAM,WAAW,GAAG;AACtD,YAAQ,IAAI,4BAA4B;AACxC,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,EAAE,GAAG,CAAC,MAAM,GAAG,GAAG,MAAM,EAAE;AACtC,MAAI,SAAS,EAAE,GAAG,OAAO,KAAK,GAAG,OAAO,IAAI;AAC5C,MAAI,iBAAiB,CAAC;AACtB;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,MAAM,QAAQ,KAAK;AAC5C,UAAI,OAAO,OAAO,MAAM,CAAC;AACzB,qBAAe,KAAK,KAAK,IAAI;AAC7B,UAAI,KAAK,QAAQ;AACf,iBAAS;AACT;AAAA,MACF;AACA,UAAI,KAAK,SAAS,MAAM;AACtB,YAAI,KAAK,WAAW;AAClB,kBAAQ,IAAI,oCAAqC,KAAK,OAAO,GAAI;AACjE,iBAAO;AAAA,QACT;AACA;AAAA,MACF;AACA,UAAI,WAAW,KAAK,KAAK;AACzB,eAAS,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK;AACjD,YAAI,UAAU,SAAS,CAAC;AAExB,YAAI,WAAW,KAAK,IAAI,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,QAAQ,IAAI,CAAC,CAAC,KAAK;AACtE,YAAI,OAAO,QAAQ,GAAG,CAAC,IAAI;AAC3B,YAAI,OAAO,QAAQ,GAAG,CAAC,IAAI;AAC3B,YAAI,OAAO,QAAQ,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI;AACnE,YAAI,OAAO,QAAQ,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI;AACnE,YAAI,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK,OAAO,MAAM,GAAG;AACxE;AAAA,QACF;AACA,iBAAS,kBAAkB,OAAOA,WAAS,OAAO;AAClD,YAAI,CAAC,MAAM,OAAO,CAAC,GAAG;AACpB,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,MAAI,MAAM,OAAO,CAAC,GAAG;AACnB,YAAQ,IAAI,qDACR,CAAC,MAAM,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM,cAAe,iBAAiB,GAAI;AAC/E,WAAO;AAAA,EACT;AACA,QAAM,IAAI,CAAC,OAAO;AAClB,QAAM,IAAI,OAAO;AACjB,SAAO;AACT;AAEA,SAAS,kBAAkB,KAAKA,WAAS,IAAI;AAC3C,MAAI,MAAM,EAAE,GAAG,OAAO,KAAK,GAAG,OAAO,IAAI;AACzC,MAAI,MAAM,IAAI,CAAC,GAAG;AAChB,WAAO;AAAA,EACT;AACA,MAAI,KAAK,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE;AAC9B,KAAG,KAAK,GAAG,GAAG,CAAC;AACf,KAAG,KAAK,GAAG,GAAG,CAAC;AACf,KAAG,IAAI,mBAAW,GAAG,IAAI,KAAK,EAAE,IAAI,KAAK;AACzC,MAAI,IAAI,eAAe,IAAI,EAAE;AAC7B,MAAIA,WAAS;AACX,QAAI,MAAM,EAAE,CAAC,GAAG;AACd,aAAO;AAAA,IACT;AACA,MAAE,IAAI,GAAG,IAAI,EAAE;AACf,MAAE,IAAI,GAAG,IAAI,EAAE;AACf,QAAI,IAAI,GAAG,MAAM;AACjB,QAAI,KAAK;AACT,OAAG;AACD,YAAM,eAAe,GAAG,EAAE;AAC1B,UAAI,MAAM,IAAI,CAAC,GAAG;AAChB,gBAAQ,IAAI,2FAA2F;AACvG;AAAA,MACF;AACA,YAAM,EAAE,GAAG,GAAG,KAAK,IAAI,IAAI,EAAE,IAAI,GAAG,GAAG,KAAK,IAAI,IAAI,EAAE,GAAG;AACzD,QAAE,KAAK,IAAI;AACX,QAAE,KAAK,IAAI;AAAA,IACb,SAAS,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI;AAC3D,QAAI,IAAI,GAAG;AACT,cAAQ,IAAI,iDAAiD;AAC7D,aAAO;AAAA,IACT;AACA,QAAI,IAAI,mBAAW,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;AACjC,QAAI,IAAI,EAAE,IAAI,GAAG,GAAG,CAAC;AAAA,EACvB,OAAO;AACL,QAAI,CAAC,MAAM,EAAE,CAAC,GAAG;AACf,UAAI,IAAI,IAAI,IAAI,EAAE;AAClB,UAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IACpB;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,eAAe,KAAK,IAAI;AAC/B,MAAI,IAAI,EAAE,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC,EAAE;AACrD,MAAI,OAAO,EAAE,GAAG,KAAK,MAAM,EAAE,CAAC,GAAG,GAAG,KAAK,MAAM,EAAE,CAAC,EAAE;AACpD,MAAI,OAAO,EAAE,GAAG,EAAE,IAAI,IAAM,KAAK,GAAG,GAAG,EAAE,IAAI,IAAM,KAAK,EAAE;AAC1D,MAAI,MAAM,EAAE,GAAG,OAAO,KAAK,GAAG,OAAO,IAAI;AACzC,MAAI;AACJ,MAAI,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,IAAI,CAAC,GAAG;AACrC,WAAO;AAAA,EACT;AACA,MAAI,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,IAAI,CAAC,GAAG;AACrC,WAAO;AAAA,EACT;AACA,QAAO,KAAK,IAAI,GAAG,IAAI,CAAC,IAAK,KAAK;AAClC,MAAI,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,EAAE;AACjD;AACA,MAAI,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,EAAE;AACjD,SAAO,GAAG,IAAI,CAAC;AACf,MAAI,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,EAAE;AACjD;AACA,MAAI,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,EAAE;AACjD,MAAI,MAAM,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,KAAK,IAAM,KAAK,IACpD,OAAO,IAAM,KAAK,MAAM,IAAM,KAAK,IAAI,OAAO,IAAM,KAAK,KAAK,KAAK;AACrE,MAAI,IAAK,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI;AAC7D,MAAI,IAAK,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI;AAC7D,SAAO;AACT;;;ACnMe,SAAR,oBAAkB,KAAK,QAAQ,OAAO;AAC3C,MAAI,MAAM,MAAM,GACd,MAAM,MAAM,GACZ,MAAM,MAAM,KAAK;AACnB,MAAI,GAAG,GAAG;AAEV,MAAI,MAAM,CAAC;AACX,OAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,QAAI,UAAU,MAAM,KAAK,MAAM,MAAM,QAAW;AAC9C;AAAA,IACF;AACA,QAAI,MAAM,GAAG;AACX,UAAI;AACJ,UAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,CAAC,MAAM,IAAI;AACpC,YAAI;AAAA,MACN,OAAO;AACL,YAAI;AAAA,MACN;AAAA,IACF,WAAW,MAAM,GAAG;AAClB,UAAI;AACJ,UAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,CAAC,MAAM,IAAI;AACpC,YAAI;AAAA,MACN,OAAO;AACL,YAAI;AAAA,MACN;AAAA,IACF,OAAO;AACL,UAAI;AACJ,UAAI;AAAA,IACN;AACA,YAAQ,IAAI,KAAK,CAAC,GAAG;AAAA,MACnB,KAAK;AACH,YAAI,CAAC,IAAI;AACT;AAAA,MACF,KAAK;AACH,YAAI,CAAC,IAAI,CAAC;AACV;AAAA,MACF,KAAK;AACH,YAAI,CAAC,IAAI;AACT;AAAA,MACF,KAAK;AACH,YAAI,CAAC,IAAI,CAAC;AACV;AAAA,MACF,KAAK;AACH,YAAI,MAAM,CAAC,MAAM,QAAW;AAC1B,cAAI,IAAI;AAAA,QACV;AACA;AAAA,MACF,KAAK;AACH,YAAI,MAAM,CAAC,MAAM,QAAW;AAC1B,cAAI,IAAI,CAAC;AAAA,QACX;AACA;AAAA,MACF;AAEE,eAAO;AAAA,IACX;AAAA,EACF;AACA,SAAO;AACT;;;ACtDe,SAAR,gBAAkB,OAAO;AAC9B,MAAI,MAAM;AAAA,IACR,GAAG,MAAM,CAAC;AAAA,IACV,GAAG,MAAM,CAAC;AAAA,EACZ;AACA,MAAI,MAAM,SAAS,GAAG;AACpB,QAAI,IAAI,MAAM,CAAC;AAAA,EACjB;AACA,MAAI,MAAM,SAAS,GAAG;AACpB,QAAI,IAAI,MAAM,CAAC;AAAA,EACjB;AACA,SAAO;AACT;;;AChBe,SAAR,oBAAkB,OAAO;AAC9B,aAAW,MAAM,CAAC;AAClB,aAAW,MAAM,CAAC;AACpB;AACA,SAAS,WAAW,KAAK;AACvB,MAAI,OAAO,OAAO,aAAa,YAAY;AACzC,QAAI,OAAO,SAAS,GAAG,GAAG;AACxB;AAAA,IACF;AACA,UAAM,IAAI,UAAU,oCAAoC;AAAA,EAC1D;AACA,MAAI,OAAO,QAAQ,YAAY,QAAQ,OAAO,CAAC,SAAS,GAAG,GAAG;AAC5D,UAAM,IAAI,UAAU,oCAAoC;AAAA,EAC1D;AACF;;;ACPA,SAAS,YAAY,QAAQ,MAAM;AACjC,UACG,OAAO,MAAM,eAAe,cAAc,OAAO,MAAM,eAAe,cAAc,OAAO,MAAM,eAAe,kBAAkB,KAAK,cAAc,YACnJ,KAAK,MAAM,eAAe,cAAc,KAAK,MAAM,eAAe,cAAc,KAAK,MAAM,eAAe,kBAAkB,OAAO,cAAc;AACxJ;AASe,SAAR,UAA2B,QAAQ,MAAM,OAAO,aAAa;AAClE,MAAIC;AACJ,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,YAAQ,gBAAQ,KAAK;AAAA,EACvB,OAAO;AAEL,YAAQ;AAAA,MACN,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,IACX;AAAA,EACF;AACA,MAAI,OAAO,MAAM,MAAM;AACvB,sBAAY,KAAK;AAEjB,MAAI,OAAO,SAAS,KAAK,SAAS,YAAY,QAAQ,IAAI,GAAG;AAC3D,IAAAA,SAAQ,IAAI,aAAK,OAAO;AACxB,YAAQ,UAAU,QAAQA,QAAO,OAAO,WAAW;AACnD,aAASA;AAAA,EACX;AAEA,MAAI,eAAe,OAAO,SAAS,OAAO;AACxC,YAAQ,oBAAY,QAAQ,OAAO,KAAK;AAAA,EAC1C;AAEA,MAAI,OAAO,aAAa,WAAW;AACjC,YAAQ;AAAA,MACN,GAAG,MAAM,IAAI;AAAA,MACb,GAAG,MAAM,IAAI;AAAA,MACb,GAAG,MAAM,KAAK;AAAA,IAChB;AAAA,EACF,OAAO;AACL,QAAI,OAAO,UAAU;AACnB,cAAQ;AAAA,QACN,GAAG,MAAM,IAAI,OAAO;AAAA,QACpB,GAAG,MAAM,IAAI,OAAO;AAAA,QACpB,GAAG,MAAM,KAAK;AAAA,MAChB;AAAA,IACF;AACA,YAAQ,OAAO,QAAQ,KAAK;AAC5B,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AAAA,EACF;AAEA,MAAI,OAAO,gBAAgB;AACzB,UAAM,KAAK,OAAO;AAAA,EACpB;AAGA,UAAQ,wBAAgB,OAAO,OAAO,KAAK,OAAO,KAAK;AACvD,MAAI,CAAC,OAAO;AACV;AAAA,EACF;AAEA;AAAA,EAA8D;AAG9D,MAAI,KAAK,gBAAgB;AACvB,YAAQ;AAAA,MACN,GAAG,MAAM,IAAI,KAAK;AAAA,MAClB,GAAG,MAAM;AAAA,MACT,GAAG,MAAM,KAAK;AAAA,IAChB;AAAA,EACF;AAEA,MAAI,KAAK,aAAa,WAAW;AAE/B,YAAQ;AAAA,MACN,GAAG,MAAM,IAAI;AAAA,MACb,GAAG,MAAM,IAAI;AAAA,MACb,GAAG,MAAM,KAAK;AAAA,IAChB;AAAA,EACF,OAAO;AACL,YAAQ,KAAK,QAAQ,KAAK;AAC1B,QAAI,KAAK,UAAU;AACjB,cAAQ;AAAA,QACN,GAAG,MAAM,IAAI,KAAK;AAAA,QAClB,GAAG,MAAM,IAAI,KAAK;AAAA,QAClB,GAAG,MAAM,KAAK;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAGA,MAAI,eAAe,KAAK,SAAS,OAAO;AACtC,WAAO,oBAAY,MAAM,MAAM,KAAK;AAAA,EACtC;AAEA,MAAI,SAAS,CAAC,MAAM;AAClB,WAAO,MAAM;AAAA,EACf;AACA,SAAO;AACT;;;AChHA,IAAI,QAAQ,aAAK,OAAO;AAiFxB,SAAS,YAAY,MAAM,IAAI,QAAQ,aAAa;AAClD,MAAI,kBAAkB,KAAK;AAC3B,MAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,uBAAmB,UAAU,MAAM,IAAI,QAAQ,WAAW,KAAK,EAAE,GAAG,KAAK,GAAG,IAAI;AAChF,QAAI,OAAO,SAAS,GAAG;AACrB,UAAK,OAAO,KAAK,SAAS,eAAe,KAAK,SAAS,aAAe,OAAO,GAAG,SAAS,eAAe,GAAG,SAAS,WAAY;AAC9H,YAAI,OAAO,iBAAiB,MAAM,UAAU;AAC1C;AAAA;AAAA,YAAyB,CAAC,iBAAiB,GAAG,iBAAiB,GAAG,iBAAiB,CAAC,EAAE,OAAO,OAAO,MAAM,CAAC,CAAC;AAAA;AAAA,QAC9G,OAAO;AACL;AAAA;AAAA,YAAyB,CAAC,iBAAiB,GAAG,iBAAiB,GAAG,OAAO,CAAC,CAAC,EAAE,OAAO,OAAO,MAAM,CAAC,CAAC;AAAA;AAAA,QACrG;AAAA,MACF,OAAO;AACL;AAAA;AAAA,UAAyB,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,EAAE,OAAO,OAAO,MAAM,CAAC,CAAC;AAAA;AAAA,MAC1F;AAAA,IACF,OAAO;AACL;AAAA;AAAA,QAAyB,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAAA;AAAA,IAClE;AAAA,EACF,OAAO;AACL,UAAM,UAAU,MAAM,IAAI,QAAQ,WAAW;AAC7C,WAAO,OAAO,KAAK,MAAM;AACzB,QAAI,KAAK,WAAW,GAAG;AACrB;AAAA;AAAA,QAAyB;AAAA;AAAA,IAC3B;AACA,SAAK,QAAQ,SAAU,KAAK;AAC1B,UAAK,OAAO,KAAK,SAAS,eAAe,KAAK,SAAS,aAAe,OAAO,GAAG,SAAS,eAAe,GAAG,SAAS,WAAY;AAC9H,YAAI,QAAQ,OAAO,QAAQ,OAAO,QAAQ,KAAK;AAC7C;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,QAAQ,OAAO,QAAQ,KAAK;AAC9B;AAAA,QACF;AAAA,MACF;AACA,UAAI,GAAG,IAAI,OAAO,GAAG;AAAA,IACvB,CAAC;AACD;AAAA;AAAA,MAAyB;AAAA;AAAA,EAC3B;AACF;AAMA,SAAS,UAAU,MAAM;AACvB,MAAI,gBAAgB,cAAM;AACxB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,YAAY,WAAW,MAAM;AAC/C,WAAO,KAAK;AAAA,EACd;AACA,SAAO;AAAA;AAAA,IAAiD;AAAA,EAAK;AAC/D;AAmCA,SAAS,MAAM,kBAAkB,eAAe,OAAO;AAErD,MAAI;AAEJ,MAAI;AACJ,MAAI,SAAS;AAEb,MAAI;AACJ,MAAI,OAAO,kBAAkB,aAAa;AACxC,aAAS,UAAU,gBAAgB;AACnC,eAAW;AACX,aAAS;AAAA,EACX,WAAW;AAAA,EAAyB,cAAe,MAAM,eAAe,MAAM,QAAQ,aAAa,GAAG;AACpG;AAAA;AAAA,IAA4C;AAC5C,aAAS,UAAU,gBAAgB;AACnC,eAAW;AACX,aAAS;AAAA,EACX;AACA,MAAI,CAAC,UAAU;AACb,eAAW,UAAU,gBAAgB;AAAA,EACvC;AACA,MAAI,CAAC,QAAQ;AACX,aAAS;AAAA;AAAA,MAA8D;AAAA,IAAc;AAAA,EACvF;AACA,MAAI,OAAO;AACT,WAAO,YAAY,UAAU,QAAQ,KAAK;AAAA,EAC5C,OAAO;AACL,UAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOJ,SAAS,SAAU,QAAQ,aAAa;AACtC,eAAO,YAAY,UAAU,QAAQ,QAAQ,WAAW;AAAA,MAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,SAAS,SAAU,QAAQ,aAAa;AACtC,eAAO,YAAY,QAAQ,UAAU,QAAQ,WAAW;AAAA,MAC1D;AAAA,IACF;AACA,QAAI,QAAQ;AACV,UAAI,QAAQ;AAAA,IACd;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,eAAQ;;;ACtNf,IAAI,gBAAgB;AAQpB,IAAI,4BAA4B;AAQhC,IAAI,yBAAyB;AAE7B,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAO,eAAQ;AAAA,EACb,SAASC;AAAA,EACT,SAASC;AAAA,EACT;AACF;AAUO,SAASD,SAAQ,IAAI,UAAU;AACpC,aAAW,YAAY;AACvB,SAAO,OAAO,QAAQ;AAAA,IACpB,KAAK,GAAG,CAAC;AAAA,IACT,KAAK,GAAG,CAAC;AAAA,EACX,CAAC,GAAG,QAAQ;AACd;AAUO,SAASE,SAAQ,MAAM;AAC5B,MAAI,OAAO,QAAQ,OAAO,KAAK,YAAY,CAAC,CAAC;AAC7C,MAAI,KAAK,OAAO,KAAK,KAAK;AACxB,WAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,EAChD;AACA,SAAO,CAAC,KAAK,MAAM,KAAK,QAAQ,KAAK,OAAO,KAAK,GAAG;AACtD;AAEO,SAAS,QAAQ,MAAM;AAC5B,MAAI,OAAO,QAAQ,OAAO,KAAK,YAAY,CAAC,CAAC;AAC7C,MAAI,KAAK,OAAO,KAAK,KAAK;AACxB,WAAO,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAC5B;AACA,SAAO,EAAE,KAAK,OAAO,KAAK,SAAS,IAAI,KAAK,MAAM,KAAK,UAAU,CAAC;AACpE;AAQA,SAAS,SAAS,KAAK;AACrB,SAAQ,OAAO,KAAK,KAAK;AAC3B;AASA,SAAS,SAAS,KAAK;AACrB,SAAQ,OAAS,MAAM,KAAK;AAC9B;AAaA,SAAS,QAAQ,IAAI;AACnB,MAAI,MAAM,GAAG;AACb,MAAI,OAAO,GAAG;AACd,MAAI,IAAI;AACR,MAAI,aAAa;AACjB,MAAI,KAAK;AACT,MAAI;AACJ,MAAI;AACJ,MAAI,GAAG,GAAG,GAAGC,IAAGC;AAChB,MAAI,SAAS,SAAS,GAAG;AACzB,MAAI,UAAU,SAAS,IAAI;AAC3B,MAAI;AACJ,MAAI;AAEJ,eAAa,KAAK,OAAO,OAAO,OAAO,CAAC,IAAI;AAG5C,MAAI,SAAS,KAAK;AAChB,iBAAa;AAAA,EACf;AAGA,MAAI,OAAO,MAAQ,MAAM,MAAQ,QAAQ,KAAO,OAAO,IAAM;AAC3D,iBAAa;AAAA,EACf;AAGA,MAAI,OAAO,MAAQ,MAAM,IAAM;AAC7B,QAAI,QAAQ,KAAO,OAAO,GAAK;AAC7B,mBAAa;AAAA,IACf,WACS,QAAQ,KAAO,OAAO,IAAM;AACnC,mBAAa;AAAA,IACf,WACS,QAAQ,MAAQ,OAAO,IAAM;AACpC,mBAAa;AAAA,IACf,WACS,QAAQ,MAAQ,OAAO,IAAM;AACpC,mBAAa;AAAA,IACf;AAAA,EACF;AAEA,gBAAc,aAAa,KAAK,IAAI,MAAM;AAG1C,kBAAgB,SAAS,UAAU;AAEnC,oBAAmB,cAAe,IAAI;AAEtC,MAAI,IAAI,KAAK,KAAK,IAAI,aAAa,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,CAAC;AACtE,MAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM;AACtC,MAAI,kBAAkB,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM;AACxD,EAAAD,KAAI,KAAK,IAAI,MAAM,KAAK,UAAU;AAElC,EAAAC,KAAI,MAAM,IAAI,aAAa,IAAI,IAAI,aAAa,aAAa,KAAK,IAAI,aAAa,aAAa,aAAa,OAAO,UAAU,IAAI,aAAa,IAAI,IAAI,aAAa,aAAa,KAAK,KAAK,aAAa,aAAa,aAAa,QAAQ,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,aAAa,aAAa,MAAM,KAAK,aAAa,aAAa,aAAa,QAAQ,KAAK,IAAI,IAAI,MAAM,IAAK,KAAK,aAAa,aAAa,aAAa,OAAQ,KAAK,IAAI,IAAI,MAAM;AAE1b,MAAI,aAAc,KAAK,KAAKD,MAAK,IAAI,IAAI,KAAKA,KAAIA,KAAIA,KAAI,KAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,mBAAmBA,KAAIA,KAAIA,KAAIA,KAAIA,KAAI,OAAS;AAEpJ,MAAI,cAAe,MAAMC,KAAI,IAAI,KAAK,IAAI,MAAM,KAAKD,KAAIA,KAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAKA,KAAIA,KAAIA,KAAIA,KAAI,MAAQ,KAAK,KAAK,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM,mBAAmBA,KAAIA,KAAIA,KAAIA,KAAIA,KAAIA,KAAI;AACxM,MAAI,MAAM,GAAK;AACb,mBAAe;AAAA,EAEjB;AAEA,SAAO;AAAA,IACL,UAAU,KAAK,MAAM,WAAW;AAAA,IAChC,SAAS,KAAK,MAAM,UAAU;AAAA,IAC9B,YAAY;AAAA,IACZ,YAAY,oBAAoB,GAAG;AAAA,EACrC;AACF;AAiBA,SAAS,QAAQ,KAAK;AAEpB,MAAI,cAAc,IAAI;AACtB,MAAI,aAAa,IAAI;AACrB,MAAI,aAAa,IAAI;AACrB,MAAI,aAAa,IAAI;AAErB,MAAI,aAAa,KAAK,aAAa,IAAI;AACrC,WAAO;AAAA,EACT;AAEA,MAAI,KAAK;AACT,MAAI,IAAI;AACR,MAAI,aAAa;AACjB,MAAI;AACJ,MAAI,MAAM,IAAI,KAAK,KAAK,IAAI,UAAU,MAAM,IAAI,KAAK,KAAK,IAAI,UAAU;AACxE,MAAI,IAAI,IAAIE,KAAI,IAAI,GAAGD;AACvB,MAAI;AACJ,MAAI,IAAI;AAGR,MAAI,IAAI,aAAa;AACrB,MAAI,IAAI;AAMR,MAAI,aAAa,KAAK;AACpB,SAAK;AAAA,EAEP;AAGA,gBAAc,aAAa,KAAK,IAAI,MAAM;AAI1C,oBAAmB,cAAe,IAAI;AAEtC,EAAAA,KAAI,IAAI;AACR,OAAKA,MAAK,KAAK,IAAI,aAAa,IAAI,IAAI,aAAa,aAAa,KAAK,IAAI,aAAa,aAAa,aAAa;AAElH,YAAU,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,IAAK,MAAM,KAAK,KAAK,KAAK,KAAM,KAAK,IAAI,IAAI,EAAE;AAG5L,OAAK,IAAI,KAAK,KAAK,IAAI,aAAa,KAAK,IAAI,OAAO,IAAI,KAAK,IAAI,OAAO,CAAC;AACzE,OAAK,KAAK,IAAI,OAAO,IAAI,KAAK,IAAI,OAAO;AACzC,EAAAC,MAAK,kBAAkB,KAAK,IAAI,OAAO,IAAI,KAAK,IAAI,OAAO;AAC3D,OAAK,KAAK,IAAI,cAAc,KAAK,IAAI,IAAI,aAAa,KAAK,IAAI,OAAO,IAAI,KAAK,IAAI,OAAO,GAAG,GAAG;AAChG,MAAI,KAAK,KAAK;AAEd,MAAI,MAAM,UAAW,KAAK,KAAK,IAAI,OAAO,IAAI,MAAO,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,KAAKA,MAAK,IAAIA,MAAKA,MAAK,IAAI,mBAAmB,IAAI,IAAI,IAAI,IAAI,MAAM,KAAK,KAAK,KAAK,MAAMA,MAAK,KAAK,KAAK,KAAK,MAAM,kBAAkB,IAAIA,MAAKA,OAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC5P,QAAM,SAAS,GAAG;AAElB,MAAI,OAAO,KAAK,IAAI,IAAI,KAAKA,OAAM,IAAI,IAAI,IAAI,KAAK,IAAI,IAAIA,MAAK,KAAK,KAAK,IAAIA,MAAKA,MAAK,IAAI,kBAAkB,KAAK,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,OAAO,KAAK,IAAI,OAAO;AAC1K,QAAM,aAAa,SAAS,GAAG;AAE/B,MAAI;AACJ,MAAI,IAAI,UAAU;AAChB,QAAI,WAAW,QAAQ;AAAA,MACrB,UAAU,IAAI,WAAW,IAAI;AAAA,MAC7B,SAAS,IAAI,UAAU,IAAI;AAAA,MAC3B,YAAY,IAAI;AAAA,MAChB,YAAY,IAAI;AAAA,IAClB,CAAC;AACD,aAAS;AAAA,MACP,KAAK,SAAS;AAAA,MACd,OAAO,SAAS;AAAA,MAChB,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACF,OACK;AACH,aAAS;AAAA,MACP;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAUA,SAAS,oBAAoB,KAAK;AAGhC,MAAI,mBAAmB;AAEvB,MAAK,MAAM,OAAS,OAAO,IAAK;AAC9B,uBAAmB;AAAA,EACrB,WACU,KAAK,OAAS,OAAO,IAAK;AAClC,uBAAmB;AAAA,EACrB,WACU,KAAK,OAAS,OAAO,IAAK;AAClC,uBAAmB;AAAA,EACrB,WACU,KAAK,OAAS,OAAO,IAAK;AAClC,uBAAmB;AAAA,EACrB,WACU,KAAK,OAAS,OAAO,IAAK;AAClC,uBAAmB;AAAA,EACrB,WACU,KAAK,OAAS,OAAO,IAAK;AAClC,uBAAmB;AAAA,EACrB,WACU,KAAK,OAAS,OAAO,IAAK;AAClC,uBAAmB;AAAA,EACrB,WACU,KAAK,OAAS,OAAO,IAAK;AAClC,uBAAmB;AAAA,EACrB,WACU,KAAK,OAAS,OAAO,GAAI;AACjC,uBAAmB;AAAA,EACrB,WACU,IAAI,OAAS,OAAO,GAAI;AAChC,uBAAmB;AAAA,EACrB,WACU,IAAI,OAAS,OAAO,IAAK;AACjC,uBAAmB;AAAA,EACrB,WACU,KAAK,OAAS,OAAO,KAAM;AACnC,uBAAmB;AAAA,EACrB,WACU,MAAM,OAAS,OAAO,KAAM;AACpC,uBAAmB;AAAA,EACrB,WACU,MAAM,OAAS,OAAO,KAAM;AACpC,uBAAmB;AAAA,EACrB,WACU,MAAM,OAAS,OAAO,KAAM;AACpC,uBAAmB;AAAA,EACrB,WACU,MAAM,OAAS,OAAO,KAAM;AACpC,uBAAmB;AAAA,EACrB,WACU,MAAM,OAAS,OAAO,KAAM;AACpC,uBAAmB;AAAA,EACrB,WACU,MAAM,OAAS,OAAO,KAAM;AACpC,uBAAmB;AAAA,EACrB,WACU,MAAM,OAAS,OAAO,KAAM;AACpC,uBAAmB;AAAA,EACrB,WACU,MAAM,OAAS,OAAO,KAAM;AACpC,uBAAmB;AAAA,EACrB;AACA,SAAO;AACT;AAWA,SAAS,OAAO,KAAK,UAAU;AAE7B,MAAI,WAAW,UAAU,IAAI,SAC3B,YAAY,UAAU,IAAI;AAE5B,SAAO,IAAI,aAAa,IAAI,aAAa,UAAU,IAAI,SAAS,IAAI,UAAU,IAAI,UAAU,IAAI,SAAS,OAAO,SAAS,SAAS,GAAG,QAAQ,IAAI,UAAU,OAAO,UAAU,SAAS,GAAG,QAAQ;AAClM;AAYA,SAAS,UAAU,SAAS,UAAU,YAAY;AAChD,MAAI,UAAU,kBAAkB,UAAU;AAC1C,MAAI,YAAY,KAAK,MAAM,UAAU,GAAM;AAC3C,MAAI,SAAS,KAAK,MAAM,WAAW,GAAM,IAAI;AAC7C,SAAO,gBAAgB,WAAW,QAAQ,OAAO;AACnD;AASA,SAAS,kBAAkB,GAAG;AAC5B,MAAI,UAAU,IAAI;AAClB,MAAI,YAAY,GAAG;AACjB,cAAU;AAAA,EACZ;AAEA,SAAO;AACT;AAkBA,SAAS,gBAAgB,QAAQ,KAAK,MAAM;AAE1C,MAAI,QAAQ,OAAO;AACnB,MAAI,YAAY,0BAA0B,WAAW,KAAK;AAC1D,MAAI,YAAY,uBAAuB,WAAW,KAAK;AAGvD,MAAI,SAAS,YAAY,SAAS;AAClC,MAAI,SAAS,YAAY;AACzB,MAAI,WAAW;AAEf,MAAI,SAAS,GAAG;AACd,aAAS,SAAS,IAAI,IAAI;AAC1B,eAAW;AAAA,EACb;AAEA,MAAI,WAAW,KAAM,YAAY,KAAK,SAAS,MAAQ,SAAS,KAAK,YAAY,MAAM,UAAW;AAChG;AAAA,EACF;AAEA,MAAI,WAAW,KAAM,YAAY,KAAK,SAAS,MAAQ,SAAS,KAAK,YAAY,MAAM,UAAW;AAChG;AAEA,QAAI,WAAW,GAAG;AAChB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,SAAS,GAAG;AACd,aAAS,SAAS,IAAI,IAAI;AAAA,EAC5B;AAEA,MAAI,SAAS,GAAG;AACd,aAAS,SAAS,IAAI,IAAI;AAC1B,eAAW;AAAA,EACb,OACK;AACH,eAAW;AAAA,EACb;AAEA,MAAM,WAAW,KAAQ,YAAY,KAAO,SAAS,MAAW,SAAS,KAAO,YAAY,MAAO,UAAW;AAC5G;AAAA,EACF;AAEA,MAAM,WAAW,KAAQ,YAAY,KAAO,SAAS,MAAW,SAAS,KAAO,YAAY,MAAO,UAAW;AAC5G;AAEA,QAAI,WAAW,GAAG;AAChB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,SAAS,GAAG;AACd,aAAS,SAAS,IAAI,IAAI;AAAA,EAC5B;AAEA,MAAI,YAAY,OAAO,aAAa,MAAM,IAAI,OAAO,aAAa,MAAM;AACxE,SAAO;AACT;AAUA,SAAS,OAAO,YAAY;AAE1B,MAAI,cAAc,WAAW,WAAW,GAAG;AACzC,UAAO;AAAA,EACT;AAEA,MAAI,SAAS,WAAW;AAExB,MAAI,OAAO;AACX,MAAI,KAAK;AACT,MAAI;AACJ,MAAI,IAAI;AAGR,SAAO,CAAE,QAAS,KAAK,WAAW,WAAW,OAAO,CAAC,CAAC,GAAG;AACvD,QAAI,KAAK,GAAG;AACV,YAAO,oCAAoC;AAAA,IAC7C;AACA,UAAM;AACN;AAAA,EACF;AAEA,MAAI,aAAa,SAAS,IAAI,EAAE;AAEhC,MAAI,MAAM,KAAK,IAAI,IAAI,QAAQ;AAG7B,UAAO,oCAAoC;AAAA,EAC7C;AAEA,MAAI,aAAa,WAAW,OAAO,GAAG;AAGtC,MAAI,cAAc,OAAO,eAAe,OAAO,eAAe,OAAO,cAAc,OAAO,eAAe,OAAO,eAAe,KAAK;AAClI,UAAO,2BAA2B,aAAa,mBAAmB;AAAA,EACpE;AAEA,SAAO,WAAW,UAAU,GAAG,KAAK,CAAC;AAErC,MAAI,MAAM,kBAAkB,UAAU;AAEtC,MAAI,WAAW,mBAAmB,KAAK,OAAO,CAAC,GAAG,GAAG;AACrD,MAAI,YAAY,oBAAoB,KAAK,OAAO,CAAC,GAAG,GAAG;AAMvD,SAAO,YAAY,eAAe,UAAU,GAAG;AAC7C,iBAAa;AAAA,EACf;AAGA,MAAI,YAAY,SAAS;AAEzB,MAAI,YAAY,MAAM,GAAG;AACvB,UAAO,sKAAsK;AAAA,EAC/K;AAEA,MAAI,MAAM,YAAY;AAEtB,MAAI,aAAa;AACjB,MAAI,cAAc;AAClB,MAAI,eAAe,kBAAkB,mBAAmB,SAAS;AACjE,MAAI,MAAM,GAAG;AACX,oBAAgB,MAAW,KAAK,IAAI,IAAI,GAAG;AAC3C,uBAAmB,WAAW,UAAU,GAAG,IAAI,GAAG;AAClD,iBAAa,WAAW,gBAAgB,IAAI;AAC5C,wBAAoB,WAAW,UAAU,IAAI,GAAG;AAChD,kBAAc,WAAW,iBAAiB,IAAI;AAAA,EAChD;AAEA,YAAU,aAAa;AACvB,aAAW,cAAc;AAEzB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,EACZ;AACF;AAYA,SAAS,mBAAmB,GAAG,KAAK;AAGlC,MAAI,SAAS,0BAA0B,WAAW,MAAM,CAAC;AACzD,MAAI,eAAe;AACnB,MAAI,eAAe;AAEnB,SAAO,WAAW,EAAE,WAAW,CAAC,GAAG;AACjC;AACA,QAAI,WAAW,GAAG;AAChB;AAAA,IACF;AACA,QAAI,WAAW,GAAG;AAChB;AAAA,IACF;AACA,QAAI,SAAS,GAAG;AACd,UAAI,cAAc;AAChB,cAAO,oBAAoB;AAAA,MAC7B;AACA,eAAS;AACT,qBAAe;AAAA,IACjB;AACA,oBAAgB;AAAA,EAClB;AAEA,SAAO;AACT;AAkBA,SAAS,oBAAoB,GAAG,KAAK;AAEnC,MAAI,IAAI,KAAK;AACX,UAAO,sCAAsC;AAAA,EAC/C;AAIA,MAAI,SAAS,uBAAuB,WAAW,MAAM,CAAC;AACtD,MAAI,gBAAgB;AACpB,MAAI,eAAe;AAEnB,SAAO,WAAW,EAAE,WAAW,CAAC,GAAG;AACjC;AACA,QAAI,WAAW,GAAG;AAChB;AAAA,IACF;AACA,QAAI,WAAW,GAAG;AAChB;AAAA,IACF;AAGA,QAAI,SAAS,GAAG;AACd,UAAI,cAAc;AAChB,cAAO,oBAAoB;AAAA,MAC7B;AACA,eAAS;AACT,qBAAe;AAAA,IACjB;AACA,qBAAiB;AAAA,EACnB;AAEA,SAAO;AACT;AAYA,SAAS,eAAe,YAAY;AAClC,MAAI;AACJ,UAAQ,YAAY;AAAA,IACpB,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF,KAAK;AACH,iBAAW;AACX;AAAA,IACF;AACE,iBAAW;AAAA,EACb;AACA,MAAI,YAAY,GAAK;AACnB,WAAO;AAAA,EACT,OACK;AACH,UAAO,0BAA0B;AAAA,EACnC;AAEF;;;ACjuBA,SAAS,MAAM,GAAG,GAAG,GAAG;AACtB,MAAI,EAAE,gBAAgB,QAAQ;AAC5B,WAAO,IAAI,MAAM,GAAG,GAAG,CAAC;AAAA,EAC1B;AACA,MAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,SAAK,IAAI,EAAE,CAAC;AACZ,SAAK,IAAI,EAAE,CAAC;AACZ,SAAK,IAAI,EAAE,CAAC,KAAK;AAAA,EACnB,WAAW,OAAO,MAAM,UAAU;AAChC,SAAK,IAAI,EAAE;AACX,SAAK,IAAI,EAAE;AACX,SAAK,IAAI,EAAE,KAAK;AAAA,EAClB,WAAW,OAAO,MAAM,YAAY,OAAO,MAAM,aAAa;AAC5D,QAAI,SAAS,EAAE,MAAM,GAAG;AACxB,SAAK,IAAI,WAAW,OAAO,CAAC,CAAC;AAC7B,SAAK,IAAI,WAAW,OAAO,CAAC,CAAC;AAC7B,SAAK,IAAI,WAAW,OAAO,CAAC,CAAC,KAAK;AAAA,EACpC,OAAO;AACL,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI,KAAK;AAAA,EAChB;AACA,UAAQ,KAAK,6DAA6D;AAC5E;AAEA,MAAM,WAAW,SAAU,SAAS;AAClC,SAAO,IAAI,MAAM,QAAQ,OAAO,CAAC;AACnC;AACA,MAAM,UAAU,SAAS,SAAU,UAAU;AAC3C,SAAOC,SAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,QAAQ;AAC3C;AACA,IAAO,gBAAQ;;;ACvCf,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AAEK,SAAR,gBAAkB,IAAI;AAC3B,MAAI,KAAK,CAAC;AACV,KAAG,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK;AACvD,KAAG,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK;AACjD,MAAI,IAAI,KAAK;AACb,KAAG,CAAC,IAAI,KAAK,MAAM,MAAM,MAAM,KAAK;AACpC,OAAK;AACL,KAAG,CAAC,IAAI,KAAK,MAAM,KAAK;AACxB,KAAG,CAAC,IAAI,IAAI,KAAK;AACjB,SAAO;AACT;;;ACvBe,SAAR,gBAAkB,KAAK,MAAM,MAAM,IAAI;AAC5C,UAAQ;AACR,UAAQ;AACR,SAAQ,GAAG,CAAC,IAAI,MAAM,QAAQ,GAAG,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC;AACnF;;;ACDA,IAAI,WAAW;AAEA,SAAR,oBAAkB,KAAK,IAAI,IAAI;AACpC,MAAI,IAAI,KAAK,IAAI;AACjB,MAAI,MAAM;AACV,WAAS,IAAI,UAAU,GAAG,EAAE,GAAG;AAC7B,QAAI,IAAI,KAAK,IAAI,GAAG;AACpB,QAAI,IAAI,IAAI,KAAK,IAAI;AAGrB,SAAK,gBAAQ,KAAK,GAAG,KAAK,IAAI,GAAG,GAAG,EAAE,IAAI,QAAQ,IAAI,KAAK,KAAK,CAAC,KAAK;AACtE,WAAO;AACP,QAAI,KAAK,IAAI,CAAC,IAAI,OAAO;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACFO,SAASC,QAAO;AACrB,OAAK,KAAK,KAAK,OAAO,SAAY,KAAK,KAAK;AAC5C,OAAK,KAAK,KAAK,OAAO,SAAY,KAAK,KAAK;AAC5C,OAAK,QAAQ,KAAK,UAAU,SAAY,KAAK,QAAQ;AACrD,OAAK,OAAO,KAAK,SAAS,SAAY,KAAK,OAAO;AAElD,MAAI,KAAK,IAAI;AACX,SAAK,KAAK,gBAAQ,KAAK,EAAE;AACzB,SAAK,MAAM,gBAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,EAAE;AAAA,EACjF;AACF;AAMO,SAASC,SAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAEZ,MAAI,YAAY,mBAAW,MAAM,KAAK,KAAK;AAC3C,MAAI;AACJ,MAAI,GAAG;AACP,MAAI,UAAU,KAAK,IAAI,GAAG;AAC1B,MAAI,UAAU,KAAK,IAAI,GAAG;AAE1B,MAAI,CAAC,KAAK,IAAI;AACZ,QAAI,IAAI,UAAU,KAAK,IAAI,SAAS;AAEpC,QAAK,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAK,OAAO;AACvC,aAAQ;AAAA,IACV,OAAO;AACL,UAAI,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,MAAM,IAAI,EAAE,IAAI,KAAK;AAChE,UAAI,UAAU,KAAK,IAAI,SAAS,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC;AAChE,UAAI,KAAK,IAAI,CAAC;AAEd,UAAI,KAAK,GAAG;AACV,YAAK,IAAI,IAAK,OAAO;AACnB,iBAAQ;AAAA,QACV,OAAO;AACL,cAAI;AAAA,QACN;AAAA,MACF,OAAO;AACL,YAAI,KAAK,KAAK,CAAC;AAAA,MACjB;AAEA,UAAI,MAAM,GAAG;AACX,YAAI,CAAC;AAAA,MACP;AAEA,UAAI,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,QAAQ,KAAK;AAAA,IAChD;AAAA,EACF,OAAO;AACL,QAAI,KAAK,UAAU;AACnB,QAAI,MAAM,KAAK,IAAI,IAAI,CAAC;AACxB,QAAI,IAAI,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC;AACtC,QAAI,KAAK,KAAK,IAAI,GAAG,CAAC;AACtB,QAAI,KAAK,KAAK,IAAI,OAAO,IAAI,QAAQ,KAAK,IAAI,GAAG,IAAI;AACrD,QAAI,IAAI,KAAK,IAAI,IAAI,CAAC;AACtB,QAAI,KAAK,KAAK,IAAI,GAAG,CAAC;AACtB,UAAM,IAAI,KAAK,KAAK,KAAK,IAAI,SAAS,CAAC;AACvC,SAAK,KAAK,KAAK,KAAK,GAAG;AACvB,QAAI,KAAK,gBAAQ,KAAK,SAAS,SAAS,KAAK,EAAE;AAE/C,QAAI,KAAK,KAAK,KAAK,KAAK,MAAM,IAC1B,MAAM,KAAK,IAAI,IAAI,IACjB,MAAM,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,IAC9C,MAAM,MAAM,KAAK,MAAM,KAAK,KAAK,IAAI,MAAM,SAC7C,KAAK;AAEX,QAAI,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,MAC/B,UAAU,YAAY,KAAK,KAAK,IAC9B,MAAM,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,KAC9B,MAAM,MAAM,KAAK,KAAK,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,IACjD,MAAM,MAAM,OAAO,MAAM,KAAK,KAAK,IAAI,OAAO,UAChD,KAAK;AAAA,EACf;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AAEN,SAAO;AACT;AAKO,SAASC,SAAQ,GAAG;AACzB,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK;AACpC,MAAI,KAAK,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK;AAEpC,MAAI,CAAC,KAAK,IAAI;AACZ,QAAI,IAAI,KAAK,IAAI,IAAI,KAAK,EAAE;AAC5B,QAAI,IAAI,OAAO,IAAI,IAAI;AACvB,QAAI,OAAO,KAAK,OAAO,IAAI,KAAK;AAChC,QAAI,IAAI,KAAK,IAAI,IAAI;AACrB,UAAM,KAAK,MAAM,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,IAAI,KAAK,IAAI,GAAG,CAAC,EAAE;AAC3D,UAAM,KAAK,KAAK,GAAG;AAEnB,QAAI,IAAI,GAAG;AACT,YAAM,CAAC;AAAA,IACT;AAEA,QAAK,MAAM,KAAO,MAAM,GAAI;AAC1B,YAAM;AAAA,IACR,OAAO;AACL,YAAM,mBAAW,KAAK,MAAM,GAAG,CAAC,IAAI,KAAK,KAAK;AAAA,IAChD;AAAA,EACF,OAAO;AACL,UAAM,KAAK,MAAM,IAAI,KAAK;AAC1B,UAAM,oBAAY,KAAK,KAAK,IAAI,KAAK,EAAE;AAEvC,QAAI,KAAK,IAAI,GAAG,IAAI,SAAS;AAC3B,UAAI,UAAU,KAAK,IAAI,GAAG;AAC1B,UAAI,UAAU,KAAK,IAAI,GAAG;AAC1B,UAAI,UAAU,KAAK,IAAI,OAAO,IAAI,QAAQ,KAAK,IAAI,GAAG,IAAI;AAC1D,UAAI,IAAI,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC;AACtC,UAAI,KAAK,KAAK,IAAI,GAAG,CAAC;AACtB,UAAI,IAAI,KAAK,IAAI,SAAS,CAAC;AAC3B,UAAI,KAAK,KAAK,IAAI,GAAG,CAAC;AACtB,YAAM,IAAI,KAAK,KAAK,KAAK,IAAI,SAAS,CAAC;AACvC,UAAI,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK;AAClC,UAAI,KAAK,KAAK,IAAI,GAAG,CAAC;AACtB,YAAM,MAAM;AAEZ,YAAM,MAAO,MAAM,MAAM,IAAI,KAAK,MAAO,OAAO,IAC5C,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KACzC,KAAK,MAAM,KAAK,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK,KAAK,KAAK,IACpD,KAAK,MAAM,OAAO,OAAO,IAAI,OAAO,KAAK,OAAO,KAAK;AAE7D,YAAM,mBAAW,KAAK,QAAS,KAAK,IAChC,KAAK,KAAK,IAAI,IAAI,IAAI,IACpB,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,IAChD,KAAK,MAAM,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,KAAK,QAAQ,OAAQ;AAAA,IAC3E,OAAO;AACL,YAAM,UAAU,aAAK,CAAC;AACtB,YAAM;AAAA,IACR;AAAA,EACF;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AAEN,SAAO;AACT;AAEO,IAAIC,SAAQ,CAAC,4BAA4B,0BAA0B;AAC1E,IAAO,gBAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;AC7Ke,SAAR,aAAkB,GAAG;AAC1B,MAAI,IAAI,KAAK,IAAI,CAAC;AAClB,OAAK,IAAI,IAAI,KAAK;AAClB,SAAO;AACT;;;ACJe,SAAR,cAAkB,GAAG,GAAG;AAC7B,MAAI,KAAK,IAAI,CAAC;AACd,MAAI,KAAK,IAAI,CAAC;AACd,MAAI,IAAI,KAAK,IAAI,GAAG,CAAC;AACrB,MAAI,IAAI,KAAK,IAAI,GAAG,CAAC,KAAK,IAAI,IAAI;AAElC,SAAO,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC;AACzC;;;ACPe,SAAR,eAAkB,GAAG;AAC1B,MAAI,IAAI,IAAI;AACZ,MAAI,IAAI,IAAI;AAEZ,SAAO,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI;AACzC;;;ACFe,SAAR,eAAkB,GAAG;AAC1B,MAAI,IAAI,KAAK,IAAI,CAAC;AAClB,MAAI,eAAO,KAAK,IAAI,KAAK,cAAM,GAAG,CAAC,IAAI,GAAG;AAE1C,SAAO,IAAI,IAAI,CAAC,IAAI;AACtB;;;ACRe,SAAR,aAAkB,IAAI,GAAG;AAC9B,MAAI,SAAS,IAAI,KAAK,IAAI,IAAI,CAAC;AAC/B,MAAI,IAAI,GAAG,SAAS;AACpB,MAAI,KAAK,GAAG,CAAC;AACb,MAAI,KAAK;AACT,MAAI;AAEJ,SAAO,EAAE,KAAK,GAAG;AACf,QAAI,CAAC,KAAK,SAAS,KAAK,GAAG,CAAC;AAC5B,SAAK;AACL,SAAK;AAAA,EACP;AAEA,SAAQ,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC;AAChC;;;ACde,SAAR,cAAkB,IAAI,OAAO;AAClC,MAAI,IAAI,IAAI,KAAK,IAAI,KAAK;AAC1B,MAAI,IAAI,GAAG,SAAS;AACpB,MAAI,MAAM,GAAG,CAAC;AACd,MAAI,MAAM;AACV,MAAI;AAEJ,SAAO,EAAE,KAAK,GAAG;AACf,SAAK,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC;AAC1B,UAAM;AACN,UAAM;AAAA,EACR;AAEA,SAAO,KAAK,IAAI,KAAK,IAAI;AAC3B;;;ACde,SAAR,aAAkB,GAAG;AAC1B,MAAI,IAAI,KAAK,IAAI,CAAC;AAClB,OAAK,IAAI,IAAI,KAAK;AAClB,SAAO;AACT;;;ACDe,SAAR,oBAAkB,IAAI,OAAO,OAAO;AACzC,MAAI,YAAY,KAAK,IAAI,KAAK;AAC9B,MAAI,YAAY,KAAK,IAAI,KAAK;AAC9B,MAAI,aAAa,aAAK,KAAK;AAC3B,MAAI,aAAa,aAAK,KAAK;AAC3B,MAAI,IAAI,IAAI,YAAY;AACxB,MAAI,IAAI,KAAK,YAAY;AACzB,MAAI,IAAI,GAAG,SAAS;AACpB,MAAI,KAAK,GAAG,CAAC;AACb,MAAI,MAAM;AACV,MAAI,MAAM;AACV,MAAI,KAAK;AACT,MAAI;AACJ,MAAI;AAEJ,SAAO,EAAE,KAAK,GAAG;AACf,UAAM;AACN,UAAM;AACN,UAAM;AACN,UAAM;AACN,SAAK,CAAC,MAAM,IAAI,MAAM,IAAI,MAAM,GAAG,CAAC;AACpC,SAAK,CAAC,MAAM,IAAI,MAAM,IAAI;AAAA,EAC5B;AAEA,MAAI,YAAY;AAChB,MAAI,YAAY;AAEhB,SAAO,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC1C;;;ACPO,SAASC,QAAO;AACrB,MAAI,CAAC,KAAK,WAAW,MAAM,KAAK,EAAE,KAAK,KAAK,MAAM,IAAI;AACpD,UAAM,IAAI,MAAM,oIAAoI;AAAA,EACtJ;AACA,MAAI,KAAK,QAAQ;AAEf,kBAAM,KAAK,MAAM,IAAI;AACrB,SAAK,UAAU,cAAM;AACrB,SAAK,UAAU,cAAM;AAAA,EACvB;AAEA,OAAK,KAAK,KAAK,OAAO,SAAY,KAAK,KAAK;AAC5C,OAAK,KAAK,KAAK,OAAO,SAAY,KAAK,KAAK;AAC5C,OAAK,QAAQ,KAAK,UAAU,SAAY,KAAK,QAAQ;AACrD,OAAK,OAAO,KAAK,SAAS,SAAY,KAAK,OAAO;AAElD,OAAK,MAAM,CAAC;AACZ,OAAK,MAAM,CAAC;AACZ,OAAK,MAAM,CAAC;AACZ,OAAK,MAAM,CAAC;AAEZ,MAAI,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,EAAE;AAC5C,MAAI,IAAI,KAAK,IAAI;AACjB,MAAI,KAAK;AAET,OAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,QAAQ;AAC3F,OAAK,IAAI,CAAC,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO;AAE7F,OAAK,KAAK;AACV,OAAK,IAAI,CAAC,IAAI,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,OAAO,KAAK,KAAK,OAAO,MAAM,KAAK,OAAO;AACzF,OAAK,IAAI,CAAC,IAAI,MAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM,IAAI,KAAK,MAAM,MAAM,KAAK,QAAQ;AAEzF,OAAK,KAAK;AACV,OAAK,IAAI,CAAC,IAAI,MAAM,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,QAAQ,MAAM,KAAK,QAAQ;AAC/E,OAAK,IAAI,CAAC,IAAI,MAAM,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,SAAS;AAEzE,OAAK,KAAK;AACV,OAAK,IAAI,CAAC,IAAI,MAAM,OAAO,MAAM,KAAK,OAAO,KAAK,KAAK,UAAU;AACjE,OAAK,IAAI,CAAC,IAAI,MAAM,OAAO,MAAM,KAAK,MAAM,IAAI,KAAK,SAAS;AAE9D,OAAK,KAAK;AACV,OAAK,IAAI,CAAC,IAAI,MAAM,OAAO,MAAM,KAAK,UAAU;AAChD,OAAK,IAAI,CAAC,IAAI,MAAM,OAAO,MAAM,KAAK,SAAS;AAE/C,OAAK,KAAK;AACV,OAAK,IAAI,CAAC,IAAI,MAAM,SAAS;AAC7B,OAAK,IAAI,CAAC,IAAI,MAAM,SAAS;AAE7B,OAAK,KAAK,IAAI,GAAG,CAAC;AAClB,OAAK,KAAK,KAAK,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK,KAAK;AAErE,OAAK,IAAI,CAAC,IAAI,KAAK,OAAO,KAAK,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,KAAK,SAAS;AACpG,OAAK,IAAI,CAAC,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,MAAM,KAAK,OAAO;AAEnG,OAAK,IAAI,CAAC,IAAI,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,OAAO,KAAK,MAAM,MAAM,KAAK,UAAU;AAC/F,OAAK,IAAI,CAAC,IAAI,MAAM,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,OAAO,KAAK,MAAM,MAAM,KAAK,WAAW;AAE/F,OAAK,KAAK;AACV,OAAK,IAAI,CAAC,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,MAAM,OAAO,KAAK,QAAQ;AAC/E,OAAK,IAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,OAAO,MAAM,KAAK,QAAQ,QAAQ,KAAK,SAAS;AAEpF,OAAK,KAAK;AACV,OAAK,IAAI,CAAC,IAAI,MAAM,QAAQ,SAAS,KAAK,KAAK,MAAM,KAAK,SAAS;AACnE,OAAK,IAAI,CAAC,IAAI,MAAM,QAAQ,SAAS,KAAK,OAAO,MAAM,KAAK,UAAU;AAEtE,OAAK,KAAK;AACV,OAAK,IAAI,CAAC,IAAI,MAAM,QAAQ,SAAS,KAAK,SAAS;AACnD,OAAK,IAAI,CAAC,IAAI,MAAM,QAAQ,QAAQ,KAAK,WAAW;AAEpD,OAAK,KAAK;AACV,OAAK,IAAI,CAAC,IAAI,MAAM,YAAY;AAChC,OAAK,IAAI,CAAC,IAAI,MAAM,YAAY;AAEhC,MAAIC,KAAI,aAAK,KAAK,KAAK,KAAK,IAAI;AAChC,OAAK,KAAK,CAAC,KAAK,MAAMA,KAAI,cAAM,KAAK,KAAK,IAAIA,EAAC;AACjD;AAEO,SAASC,SAAQ,GAAG;AACzB,MAAI,KAAK,mBAAW,EAAE,IAAI,KAAK,KAAK;AACpC,MAAI,KAAK,EAAE;AAEX,OAAK,aAAK,KAAK,KAAK,EAAE;AACtB,MAAI,SAAS,KAAK,IAAI,EAAE;AACxB,MAAI,SAAS,KAAK,IAAI,EAAE;AACxB,MAAI,SAAS,KAAK,IAAI,EAAE;AACxB,MAAI,SAAS,KAAK,IAAI,EAAE;AAExB,OAAK,KAAK,MAAM,QAAQ,SAAS,MAAM;AACvC,OAAK,KAAK,MAAM,SAAS,QAAQ,cAAM,QAAQ,SAAS,MAAM,CAAC;AAC/D,OAAK,eAAO,KAAK,IAAI,EAAE,CAAC;AAExB,MAAI,MAAM,oBAAY,KAAK,KAAK,IAAI,IAAI,IAAI,EAAE;AAE9C,OAAK,KAAK,IAAI,CAAC;AACf,OAAK,KAAK,IAAI,CAAC;AAEf,MAAI;AACJ,MAAI;AAEJ,MAAI,KAAK,IAAI,EAAE,KAAK,gBAAgB;AAClC,QAAI,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK;AACnC,QAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK;AAAA,EAC/C,OAAO;AACL,QAAI;AACJ,QAAI;AAAA,EACN;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AAEN,SAAO;AACT;AAEO,SAASC,SAAQ,GAAG;AACzB,MAAI,MAAM,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK;AACrC,MAAI,MAAM,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK;AAErC,QAAM,KAAK,KAAK,MAAM,KAAK;AAC3B,OAAK,KAAK,KAAK;AAEf,MAAI;AACJ,MAAI;AAEJ,MAAI,KAAK,IAAI,EAAE,KAAK,gBAAgB;AAClC,QAAI,MAAM,oBAAY,KAAK,KAAK,IAAI,IAAI,IAAI,EAAE;AAE9C,SAAK,KAAK,IAAI,CAAC;AACf,SAAK,KAAK,IAAI,CAAC;AACf,SAAK,KAAK,KAAK,aAAK,EAAE,CAAC;AAEvB,QAAI,SAAS,KAAK,IAAI,EAAE;AACxB,QAAI,SAAS,KAAK,IAAI,EAAE;AACxB,QAAI,SAAS,KAAK,IAAI,EAAE;AACxB,QAAI,SAAS,KAAK,IAAI,EAAE;AAExB,SAAK,KAAK,MAAM,SAAS,QAAQ,cAAM,QAAQ,SAAS,MAAM,CAAC;AAC/D,SAAK,KAAK,MAAM,QAAQ,SAAS,MAAM;AAEvC,UAAM,mBAAW,KAAK,KAAK,KAAK;AAChC,UAAM,aAAK,KAAK,KAAK,EAAE;AAAA,EACzB,OAAO;AACL,UAAM;AACN,UAAM;AAAA,EACR;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AAEN,SAAO;AACT;AAEO,IAAIC,SAAQ,CAAC,gCAAgC,gCAAgC,UAAU,uBAAuB,uBAAuB,gBAAgB,gBAAgB,OAAO;AACnL,IAAO,iBAAQ;AAAA,EACb,MAAMJ;AAAA,EACN,SAASE;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;ACnLe,SAAR,oBAAkB,MAAM,KAAK;AAClC,MAAI,SAAS,QAAW;AACtB,WAAO,KAAK,OAAO,mBAAW,GAAG,IAAI,KAAK,MAAM,KAAK,KAAK,EAAE,IAAI;AAEhE,QAAI,OAAO,GAAG;AACZ,aAAO;AAAA,IACT,WAAW,OAAO,IAAI;AACpB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;ACXO,IAAI,YAAY;AAIhB,SAASC,QAAO;AACrB,MAAI,OAAO,oBAAY,KAAK,MAAM,KAAK,KAAK;AAC5C,MAAI,SAAS,QAAW;AACtB,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACpC;AACA,OAAK,OAAO;AACZ,OAAK,SAAU,IAAI,KAAK,IAAI,IAAI,IAAK,OAAO;AAC5C,OAAK,KAAK;AACV,OAAK,KAAK,KAAK,WAAW,MAAW;AACrC,OAAK,KAAK;AAEV,iBAAO,KAAK,MAAM,IAAI;AACtB,OAAK,UAAU,eAAO;AACtB,OAAK,UAAU,eAAO;AACxB;AAEO,IAAIC,SAAQ,CAAC,wCAAwC,KAAK;AACjE,IAAO,cAAQ;AAAA,EACb,MAAMD;AAAA,EACN,OAAOC;AAAA,EACP;AACF;;;AC3Be,SAAR,aAAkB,OAAO,KAAK;AACnC,SAAQ,KAAK,KAAK,IAAI,UAAU,IAAI,QAAQ,GAAG;AACjD;;;ACDA,IAAIC,YAAW;AAeR,SAASC,QAAO;AACrB,MAAI,OAAO,KAAK,IAAI,KAAK,IAAI;AAC7B,MAAI,OAAO,KAAK,IAAI,KAAK,IAAI;AAC7B,UAAQ;AACR,OAAK,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI,KAAK,KAAK,OAAO;AACzD,OAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,OAAO,QAAQ,IAAI,KAAK,GAAG;AAC5D,OAAK,QAAQ,KAAK,KAAK,OAAO,KAAK,CAAC;AACpC,OAAK,SAAS,MAAM,KAAK,IAAI,KAAK;AAClC,OAAK,IAAI,KAAK,IAAI,MAAM,KAAK,QAAQ,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,OAAO,MAAM,GAAG,KAAK,CAAC,IAAI,aAAK,KAAK,IAAI,MAAM,KAAK,MAAM;AACxI;AAEO,SAASC,SAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAEZ,IAAE,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,aAAK,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,MAAM,CAAC,IAAI;AAC3H,IAAE,IAAI,KAAK,IAAI;AACf,SAAO;AACT;AAEO,SAASC,SAAQ,GAAG;AACzB,MAAI,UAAU;AACd,MAAI,MAAM,EAAE,IAAI,KAAK;AACrB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM,MAAM,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC;AACpE,WAAS,IAAIH,WAAU,IAAI,GAAG,EAAE,GAAG;AACjC,UAAM,IAAI,KAAK,KAAK,MAAM,aAAK,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,OAAO,KAAK,CAAC,CAAC,IAAI;AACzE,QAAI,KAAK,IAAI,MAAM,EAAE,CAAC,IAAI,SAAS;AACjC;AAAA,IACF;AACA,MAAE,IAAI;AAAA,EACR;AAEA,MAAI,CAAC,GAAG;AACN,WAAO;AAAA,EACT;AACA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAII,SAAQ,CAAC,OAAO;AAC3B,IAAO,gBAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;ACjDO,SAASC,QAAO;AACrB,gBAAM,KAAK,MAAM,IAAI;AACrB,MAAI,CAAC,KAAK,IAAI;AACZ;AAAA,EACF;AACA,OAAK,QAAQ,KAAK,IAAI,KAAK,KAAK;AAChC,OAAK,QAAQ,KAAK,IAAI,KAAK,KAAK;AAChC,OAAK,KAAK,IAAI,KAAK;AACnB,MAAI,CAAC,KAAK,OAAO;AACf,SAAK,QAAQ;AAAA,EACf;AACF;AAEO,SAASC,SAAQ,GAAG;AACzB,MAAI,MAAM,MAAM,MAAM;AACtB,IAAE,IAAI,mBAAW,EAAE,IAAI,KAAK,KAAK;AACjC,gBAAM,QAAQ,MAAM,MAAM,CAAC,CAAC,CAAC;AAC7B,SAAO,KAAK,IAAI,EAAE,CAAC;AACnB,SAAO,KAAK,IAAI,EAAE,CAAC;AACnB,SAAO,KAAK,IAAI,EAAE,CAAC;AACnB,MAAI,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,QAAQ,OAAO,KAAK,QAAQ,OAAO;AACrE,IAAE,IAAI,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;AAC7B,IAAE,IAAI,KAAK,KAAK,QAAQ,OAAO,KAAK,QAAQ,OAAO;AACnD,IAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK;AAC1B,IAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK;AAC1B,SAAO;AACT;AAEO,SAASC,SAAQ,GAAG;AACzB,MAAI,MAAM,MAAM,KAAK,KAAK;AAC1B,IAAE,KAAK,EAAE,IAAI,KAAK,MAAM,KAAK;AAC7B,IAAE,KAAK,EAAE,IAAI,KAAK,MAAM,KAAK;AAE7B,IAAE,KAAK,KAAK;AACZ,IAAE,KAAK,KAAK;AACZ,MAAK,MAAM,cAAM,EAAE,GAAG,EAAE,CAAC,GAAI;AAC3B,QAAI,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,EAAE;AACnC,WAAO,KAAK,IAAI,CAAC;AACjB,WAAO,KAAK,IAAI,CAAC;AACjB,UAAM,KAAK,KAAK,OAAO,KAAK,QAAQ,EAAE,IAAI,OAAO,KAAK,QAAQ,GAAG;AACjE,UAAM,KAAK,MAAM,EAAE,IAAI,MAAM,MAAM,KAAK,QAAQ,OAAO,EAAE,IAAI,KAAK,QAAQ,IAAI;AAAA,EAChF,OAAO;AACL,UAAM,KAAK;AACX,UAAM;AAAA,EACR;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,gBAAM,QAAQ,MAAM,MAAM,CAAC,CAAC,CAAC;AAC7B,IAAE,IAAI,mBAAW,EAAE,IAAI,KAAK,KAAK;AACjC,SAAO;AACT;AAEO,IAAIC,SAAQ,CAAC,4BAA4B,yBAAyB,UAAU,qCAAqC,sBAAsB;AAC9I,IAAO,iBAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;ACpDO,SAAS,MAAM,MAAM,QAAQ,OAAO;AACzC,YAAU;AACV,SAAQ,KAAK,IAAI,OAAO,UAAU,KAAK,IAAI,KAAK,KAAK,IAAI,WAAW,IAAI,SAAS,MAAM,KAAK;AAC9F;AAGO,SAASC,QAAO;AAErB,OAAK,KAAK,KAAK,MAAM;AACrB,OAAK,KAAK,KAAK,MAAM;AACrB,OAAK,OAAO,KAAK,QAAQ;AACzB,OAAK,QAAQ,KAAK,SAAS;AAE3B,OAAK,UAAU,KAAK,IAAI,KAAK,IAAI;AACjC,OAAK,UAAU,KAAK,IAAI,KAAK,IAAI;AACjC,MAAI,KAAK,QAAQ;AACf,QAAI,KAAK,OAAO,KAAK,CAAC,MAAM,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,OAAO,KAAK,OAAO;AAC3E,WAAK,KAAK,OAAO,IAAI,aAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM;AAAA,IAC7D;AAAA,EACF,OAAO;AACL,QAAI,KAAK,IAAI,KAAK,OAAO,KAAK,OAAO;AACnC,UAAI,KAAK,OAAO,GAAG;AAGjB,aAAK,MAAM;AAAA,MACb,OAAO;AAGL,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AACA,SAAK,OAAO,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC;AACzF,QAAI,KAAK,OAAO,KAAK,CAAC,MAAM,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,OAAO,KAAK,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC,IAAI,OAAO;AAGtH,WAAK,KAAK,MAAM,KAAK,OAAO,cAAM,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,GAAG,KAAK,IAAI,KAAK,MAAM,CAAC,IAAI,cAAM,KAAK,GAAG,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,MAAM,CAAC;AAAA,IAClK;AACA,SAAK,MAAM,cAAM,KAAK,GAAG,KAAK,SAAS,KAAK,OAAO;AACnD,SAAK,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,SAAS,KAAK,CAAC,CAAC,IAAI;AAClE,SAAK,QAAQ,KAAK,IAAI,KAAK,EAAE;AAC7B,SAAK,QAAQ,KAAK,IAAI,KAAK,EAAE;AAAA,EAC/B;AACF;AAGO,SAASC,SAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AACZ,MAAI,SAAS,KAAK,IAAI,GAAG;AACzB,MAAI,SAAS,KAAK,IAAI,GAAG;AACzB,MAAIC,IAAG,GAAG,MAAM,MAAM,IAAI;AAC1B,MAAI,OAAO,mBAAW,MAAM,KAAK,KAAK;AAEtC,MAAI,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK,EAAE,KAAK,SAAS,KAAK,IAAI,MAAM,KAAK,IAAI,KAAK,OAAO;AAGjG,MAAE,IAAI;AACN,MAAE,IAAI;AACN,WAAO;AAAA,EACT;AACA,MAAI,KAAK,QAAQ;AAEf,IAAAA,KAAI,IAAI,KAAK,MAAM,IAAI,KAAK,UAAU,SAAS,KAAK,UAAU,SAAS,KAAK,IAAI,IAAI;AACpF,MAAE,IAAI,KAAK,IAAIA,KAAI,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK;AAClD,MAAE,IAAI,KAAK,IAAIA,MAAK,KAAK,UAAU,SAAS,KAAK,UAAU,SAAS,KAAK,IAAI,IAAI,KAAK,KAAK;AAC3F,WAAO;AAAA,EACT,OAAO;AACL,QAAI,IAAI,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,CAAC,CAAC,IAAI;AAChD,WAAO,KAAK,IAAI,CAAC;AACjB,WAAO,KAAK,IAAI,CAAC;AACjB,QAAI,KAAK,IAAI,KAAK,OAAO,KAAK,OAAO;AACnC,WAAK,cAAM,KAAK,GAAG,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM;AACpD,WAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK;AACtC,QAAE,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK;AAC9C,QAAE,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK;AAEzD,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,KAAK,OAAO,IAAI,OAAO;AAGzC,MAAAA,KAAI,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,OAAO,KAAK,IAAI,IAAI;AACpD,QAAE,IAAIA,KAAI;AAAA,IACZ,OAAO;AAGL,MAAAA,KAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,OAAO,KAAK,SAAS,IAAI,KAAK,QAAQ,OAAO,KAAK,QAAQ,OAAO,KAAK,IAAI,IAAI;AAC9G,QAAE,IAAIA,MAAK,KAAK,QAAQ,OAAO,KAAK,QAAQ,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK;AAAA,IAC5E;AACA,MAAE,IAAIA,KAAI,OAAO,KAAK,IAAI,IAAI,IAAI,KAAK;AAAA,EACzC;AAEA,SAAO;AACT;AAGO,SAASC,SAAQ,GAAG;AACzB,IAAE,KAAK,KAAK;AACZ,IAAE,KAAK,KAAK;AACZ,MAAI,KAAK,KAAK,IAAI,IAAI;AACtB,MAAI,KAAK,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACxC,MAAI,KAAK,QAAQ;AACf,QAAI,IAAI,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,GAAG;AACjD,UAAM,KAAK;AACX,UAAM,KAAK;AACX,QAAI,MAAM,OAAO;AACf,QAAE,IAAI;AACN,QAAE,IAAI;AACN,aAAO;AAAA,IACT;AACA,UAAM,KAAK,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;AAClF,QAAI,KAAK,IAAI,KAAK,OAAO,IAAI,OAAO;AAClC,UAAI,KAAK,OAAO,GAAG;AACjB,cAAM,mBAAW,KAAK,QAAQ,KAAK,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC;AAAA,MACzD,OAAO;AACL,cAAM,mBAAW,KAAK,QAAQ,KAAK,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;AAAA,MACpD;AAAA,IACF,OAAO;AACL,YAAM,mBAAW,KAAK,QAAQ,KAAK,MAAM,EAAE,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,UAAU,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC;AAAA,IACjI;AACA,MAAE,IAAI;AACN,MAAE,IAAI;AACN,WAAO;AAAA,EACT,OAAO;AACL,QAAI,KAAK,IAAI,KAAK,OAAO,KAAK,OAAO;AACnC,UAAI,MAAM,OAAO;AACf,cAAM,KAAK;AACX,cAAM,KAAK;AACX,UAAE,IAAI;AACN,UAAE,IAAI;AAEN,eAAO;AAAA,MACT;AACA,QAAE,KAAK,KAAK;AACZ,QAAE,KAAK,KAAK;AACZ,WAAK,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,KAAK;AACzC,YAAM,KAAK,MAAM,cAAM,KAAK,GAAG,EAAE;AACjC,YAAM,KAAK,MAAM,mBAAW,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC;AAAA,IAC/E,OAAO;AACL,WAAK,IAAI,KAAK,KAAK,KAAK,KAAK,SAAS,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI;AACtE,YAAM,KAAK;AACX,UAAI,MAAM,OAAO;AACf,cAAM,KAAK;AAAA,MACb,OAAO;AACL,cAAM,KAAK,KAAK,KAAK,IAAI,EAAE,IAAI,KAAK,QAAQ,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,QAAQ,EAAE;AAChF,cAAM,mBAAW,KAAK,QAAQ,KAAK,MAAM,EAAE,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,KAAK,QAAQ,KAAK,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,QAAQ,KAAK,IAAI,EAAE,CAAC,CAAC;AAAA,MAChI;AACA,YAAM,KAAK,cAAM,KAAK,GAAG,KAAK,IAAI,OAAO,UAAU,IAAI,CAAC;AAAA,IAC1D;AAAA,EACF;AACA,IAAE,IAAI;AACN,IAAE,IAAI;AAGN,SAAO;AACT;AAEO,IAAIC,SAAQ,CAAC,SAAS,4BAA4B,iCAAiC,iCAAiC,qBAAqB;AAChJ,IAAO,gBAAQ;AAAA,EACb,MAAMJ;AAAA,EACN,SAASC;AAAA,EACT,SAASE;AAAA,EACT,OAAOC;AAAA,EACP;AACF;;;ACtKO,SAASC,QAAO;AACrB,MAAI,OAAO,KAAK;AAChB,OAAK,UAAU,KAAK;AACpB,MAAI,UAAU,KAAK,IAAI,IAAI;AAC3B,MAAI,gBAAgB,KAAK;AACzB,MAAI,OAAO,KAAK;AAChB,MAAI,aAAa,IAAI;AACrB,MAAI,KAAK,IAAI,aAAa,KAAK,IAAI,YAAY,CAAC;AAChD,MAAI,IAAI,KAAK,IAAI,KAAK,KAAK,EAAE;AAC7B,OAAK,IAAI,KAAK,KAAK,gBAAgB,KAAK,KAAK,IAAI,EAAE,KAAK,IAAI,KAAK,KAAK,IAAI,SAAS,CAAC;AACpF,OAAK,QAAQ,KAAK,KAAK,IAAI,MAAM,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AACtE,OAAK,KAAK,KAAK,KAAK,UAAU,KAAK,KAAK;AACxC,MAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AACrD,MAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,OAAO,CAAC,CAAC;AAClD,MAAI,KAAK,KAAK,KAAK,IAAI,IAAI,YAAY,IAAI,IAAI,QAAQ;AACvD,OAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,QAAQ,IAAI,IAAI;AACvD;AAEO,SAASC,SAAQ,GAAG;AACzB,MAAI,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE,IAAI,CAAC,CAAC;AAClD,MAAI,MAAM,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,MAAM,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,EAAE;AAC3F,MAAI,IAAI,CAAC,KAAK,SAAS,MAAM,OAAO,KAAK;AAGzC,MAAI,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK;AAGhD,MAAIC,KAAI,KAAK,SAAS,EAAE,IAAI,KAAK;AAGjC,MAAI,OAAO,KAAK,KAAK,KAAK,IAAIA,EAAC,KAAK,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAIA,EAAC,EAAE;AAEtG,MAAI,OAAO,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAIA,EAAC,CAAC;AAEpG,IAAE,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE,IAAI,KAAK;AAChF,IAAE,IAAI,KAAK,IAAI,OAAO,KAAK;AAC3B,SAAO;AACT;AAEO,SAASC,SAAQ,GAAG;AACzB,MAAI,IAAI,EAAE,IAAI,KAAK;AACnB,MAAI,IAAI,EAAE,IAAI,KAAK;AAEnB,MAAI,OAAO,IAAI,KAAK;AACpB,MAAI,OAAO,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK;AAE5D,MAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC;AAC1G,MAAID,KAAI,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,IAAI,EAAE;AAE5G,MAAI,SAAS,KAAK,UAAUA,KAAI,KAAK;AAErC,MAAI,IAAI;AACR,MAAI,MAAM;AACV,MAAI,UAAU;AACd,MAAI,YAAY;AAChB,SAAO,KAAK,IAAI,MAAM,OAAO,IAAI,MAAW;AAC1C,QAAI,EAAE,YAAY,IAAI;AAEpB;AAAA,IACF;AAEA,QAAI,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;AACzJ,cAAU;AACV,UAAM,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,KAAK;AAAA,EAC/C;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAIE,UAAQ,CAAC,QAAQ;AAC5B,IAAO,iBAAQ;AAAA,EACb,MAAMJ;AAAA,EACN,SAASC;AAAA,EACT,SAASE;AAAA,EACT,OAAOC;AACT;;;AClEA,IAAI,MAAM;AAEV,SAAS,QAAQ,GAAG;AAClB,MAAI,mBAAmB,CAAC,2BAA2B,qCAAqC,gDAAgD;AACxI,MAAI,iBAAiB,OAAO,EAAE,aAAa,WAAW,OAAO,KAAK,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE;AAErF,SAAO,aAAa,KAAK,YAAY,KAAK,iBAAiB,QAAQ,cAAc,MAAM,MAAM,iBAAiB,QAAQ,sBAAsB,cAAc,CAAC,MAAM;AACnK;AAMO,SAASC,SAAO;AACrB,MAAI,KAAK,KAAK,QAAQ,GAAG,GAAG,GAAG,GAAG,QAAQ,GAAG,GAAG,QAAQ,GACtD,QAAQ,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,UAAU;AAItE,OAAK,SAAS,QAAQ,IAAI;AAC1B,OAAK,SAAS,YAAY;AAE1B,MAAI,MAAM;AACV,MAAI,WAAW,MAAM;AACnB,UAAM;AAAA,EACR;AAEA,MAAI,MAAM;AACV,MAAI,0BAA0B,MAAM;AAClC,UAAM;AAAA,EACR;AAEA,MAAI,KAAK;AACP,cAAU,KAAK;AAAA,EACjB;AAEA,MAAI,KAAK;AACP,YAAQ,KAAK;AAAA,EACf;AAEA,MAAI,OAAO,KAAK;AACd,WAAO,KAAK;AAAA,EACd,OAAO;AACL,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO,KAAK;AAEZ,QAAI,KAAK,IAAI,OAAO,IAAI,KAAK,QAAQ,MAAM,KAAK,IAAI,IAAI,MAAM,OACzD,KAAK,IAAI,MAAM,OAAO,KAAK,OAAO,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,OAAO,KAAK,OAC7E,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK;AAC9C,YAAM,IAAI,MAAM;AAAA,IAClB;AAAA,EACF;AAEA,MAAI,SAAS,IAAM,KAAK;AACxB,QAAM,KAAK,KAAK,MAAM;AAEtB,MAAI,KAAK,IAAI,KAAK,IAAI,IAAI,OAAO;AAC/B,aAAS,KAAK,IAAI,KAAK,IAAI;AAC3B,aAAS,KAAK,IAAI,KAAK,IAAI;AAC3B,UAAM,IAAI,KAAK,KAAK,SAAS;AAC7B,SAAK,IAAI,SAAS;AAClB,SAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM;AACzD,SAAK,IAAI,KAAK,IAAI,KAAK,KAAK,MAAM;AAClC,QAAI,KAAK,IAAI,OAAO,SAAS,KAAK,KAAK,GAAG;AAC1C,QAAI,IAAI,IAAI;AAEZ,QAAI,KAAK,GAAG;AACV,UAAI;AAAA,IACN,OAAO;AACL,UAAI,KAAK,KAAK,CAAC;AACf,UAAI,KAAK,OAAO,GAAG;AACjB,YAAI,CAAC;AAAA,MACP;AAAA,IACF;AAEA,SAAK,IAAI,KAAK;AACd,SAAK,KAAK,KAAK,IAAI,cAAM,KAAK,GAAG,KAAK,MAAM,MAAM,GAAG,KAAK,CAAC;AAAA,EAC7D,OAAO;AACL,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,KAAK;AACd,SAAK,IAAI,IAAI,IAAI;AAAA,EACnB;AAEA,MAAI,OAAO,KAAK;AACd,QAAI,KAAK;AACP,eAAS,KAAK,KAAK,KAAK,IAAI,OAAO,IAAI,CAAC;AACxC,UAAI,CAAC,KAAK;AACR,gBAAQ;AAAA,MACV;AAAA,IACF,OAAO;AACL,eAAS;AACT,gBAAU,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC;AAAA,IAC1C;AACA,SAAK,OAAO,OAAO,KAAK,KAAK,OAAO,IAAI,IAAI,KAAK,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK;AAAA,EAC5E,OAAO;AACL,QAAI,KAAK,IAAI,cAAM,KAAK,GAAG,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC;AACxD,QAAI,KAAK,IAAI,cAAM,KAAK,GAAG,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC;AACxD,QAAI,KAAK,IAAI;AACb,SAAK,IAAI,MAAM,IAAI;AACnB,QAAI,KAAK,IAAI,KAAK;AAClB,SAAK,IAAI,IAAI,MAAM,IAAI,IAAI;AAC3B,UAAM,OAAO;AAEb,QAAI,MAAM,CAAC,KAAK,IAAI;AAClB,cAAQ;AAAA,IACV,WAAW,MAAM,KAAK,IAAI;AACxB,cAAQ;AAAA,IACV;AAEA,SAAK,OAAO,mBAAW,OAAO,OAAO,QAAQ,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,KAAK,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC;AAC/G,aAAS,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,mBAAW,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;AACpF,YAAQ,UAAU,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC;AAAA,EAClD;AAEA,OAAK,SAAS,KAAK,IAAI,MAAM;AAC7B,OAAK,SAAS,KAAK,IAAI,MAAM;AAC7B,OAAK,SAAS,KAAK,IAAI,KAAK;AAC5B,OAAK,SAAS,KAAK,IAAI,KAAK;AAE5B,OAAK,KAAK,IAAI,KAAK;AACnB,OAAK,MAAM,KAAK,IAAI,KAAK;AACzB,OAAK,MAAM,IAAI,KAAK;AAEpB,MAAI,KAAK,QAAQ;AACf,SAAK,MAAM;AAAA,EACb,OAAO;AACL,SAAK,MAAM,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,CAAC;AAElF,QAAI,KAAK,OAAO,GAAG;AACjB,WAAK,MAAM,CAAC,KAAK;AAAA,IACnB;AAAA,EACF;AAEA,MAAI,MAAM;AACV,OAAK,WAAW,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,CAAC;AACxD,OAAK,WAAW,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,CAAC;AAC1D;AAIO,SAASC,SAAQ,GAAG;AACzB,MAAI,SAAS,CAAC;AACd,MAAI,GAAG,GAAG,GAAGC,IAAG,GAAG,MAAM,GAAG;AAC5B,IAAE,IAAI,EAAE,IAAI,KAAK;AAEjB,MAAI,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,IAAI,OAAO,IAAI,OAAO;AAC7C,QAAI,KAAK,IAAI,KAAK,IAAI,cAAM,KAAK,GAAG,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC;AAE/D,WAAO,IAAI;AACX,QAAI,OAAO,IAAI;AACf,QAAI,OAAO,IAAI;AACf,IAAAA,KAAI,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC;AACzB,SAAK,IAAI,KAAK,SAASA,KAAI,KAAK,UAAU;AAE1C,QAAI,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,CAAG,IAAI,OAAO;AACvC,YAAM,IAAI,MAAM;AAAA,IAClB;AAEA,QAAI,MAAM,KAAK,MAAM,KAAK,KAAK,IAAI,MAAM,IAAI,EAAE;AAC/C,WAAO,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC;AAE5B,QAAI,KAAK,IAAI,IAAI,IAAI,KAAK;AACxB,UAAI,KAAK,IAAI,EAAE;AAAA,IACjB,OAAO;AACL,UAAI,KAAK,MAAM,KAAK,MAAO,IAAI,KAAK,SAASA,KAAI,KAAK,QAAS,IAAI;AAAA,IACrE;AAAA,EACF,OAAO;AACL,QAAI,EAAE,IAAI,IAAI,KAAK,WAAW,KAAK;AACnC,QAAI,KAAK,MAAM,EAAE;AAAA,EACnB;AAEA,MAAI,KAAK,QAAQ;AACf,WAAO,IAAI;AACX,WAAO,IAAI;AAAA,EACb,OAAO;AACL,SAAK,KAAK;AACV,WAAO,IAAI,IAAI,KAAK,SAAS,IAAI,KAAK;AACtC,WAAO,IAAI,IAAI,KAAK,SAAS,IAAI,KAAK;AAAA,EACxC;AAEA,SAAO,IAAK,KAAK,IAAI,OAAO,IAAI,KAAK;AACrC,SAAO,IAAK,KAAK,IAAI,OAAO,IAAI,KAAK;AAErC,SAAO;AACT;AAEO,SAASC,SAAQ,GAAG;AACzB,MAAI,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI;AAC1B,MAAI,SAAS,CAAC;AAEd,IAAE,KAAK,EAAE,IAAI,KAAK,OAAO,IAAM,KAAK;AACpC,IAAE,KAAK,EAAE,IAAI,KAAK,OAAO,IAAM,KAAK;AAEpC,MAAI,KAAK,QAAQ;AACf,QAAI,EAAE;AACN,QAAI,EAAE;AAAA,EACR,OAAO;AACL,QAAI,EAAE,IAAI,KAAK,SAAS,EAAE,IAAI,KAAK;AACnC,QAAI,EAAE,IAAI,KAAK,SAAS,EAAE,IAAI,KAAK,SAAS,KAAK;AAAA,EACnD;AAEA,OAAK,KAAK,IAAI,CAAC,KAAK,MAAM,CAAC;AAC3B,OAAK,OAAO,KAAK,IAAI;AACrB,OAAK,OAAO,KAAK,IAAI;AACrB,OAAK,KAAK,IAAI,KAAK,MAAM,CAAC;AAC1B,QAAM,KAAK,KAAK,SAAS,KAAK,KAAK,UAAU;AAE7C,MAAI,KAAK,IAAI,KAAK,IAAI,EAAE,IAAI,CAAC,IAAI,OAAO;AACtC,WAAO,IAAI;AACX,WAAO,IAAI,KAAK,IAAI,CAAC,UAAU;AAAA,EACjC,OAAO;AACL,WAAO,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,OAAO,IAAI,GAAG;AACjD,WAAO,IAAI,cAAM,KAAK,GAAG,KAAK,IAAI,OAAO,GAAG,IAAI,KAAK,CAAC,CAAC;AAEvD,QAAI,OAAO,MAAM,UAAU;AACzB,YAAM,IAAI,MAAM;AAAA,IAClB;AAEA,WAAO,IAAI,CAAC,KAAK,KAAK,KAAK,MAAO,KAAK,KAAK,SAAS,KAAK,KAAK,QAAS,KAAK,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EAChG;AAEA,SAAO,KAAK,KAAK;AAEjB,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,2BAA2B,2BAA2B,qCAAqC,qCAAqC,kDAAkD,oDAAoD,0CAA0C,oBAAoB,OAAO;AAC/T,IAAO,gBAAQ;AAAA,EACb,MAAMJ;AAAA,EACN,SAASC;AAAA,EACT,SAASE;AAAA,EACT,OAAOC;AACT;;;ACvPO,SAASC,SAAO;AAarB,MAAI,CAAC,KAAK,MAAM;AACd,SAAK,OAAO,KAAK;AAAA,EACnB;AACA,MAAI,CAAC,KAAK,IAAI;AACZ,SAAK,KAAK;AAAA,EACZ;AACA,OAAK,KAAK,KAAK,MAAM;AACrB,OAAK,KAAK,KAAK,MAAM;AAErB,MAAI,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,IAAI,OAAO;AAC3C;AAAA,EACF;AAEA,MAAI,OAAO,KAAK,IAAI,KAAK;AACzB,OAAK,IAAI,KAAK,KAAK,IAAI,OAAO,IAAI;AAElC,MAAI,OAAO,KAAK,IAAI,KAAK,IAAI;AAC7B,MAAI,OAAO,KAAK,IAAI,KAAK,IAAI;AAC7B,MAAI,MAAM,cAAM,KAAK,GAAG,MAAM,IAAI;AAClC,MAAI,MAAM,cAAM,KAAK,GAAG,KAAK,MAAM,IAAI;AAEvC,MAAI,OAAO,KAAK,IAAI,KAAK,IAAI;AAC7B,MAAI,OAAO,KAAK,IAAI,KAAK,IAAI;AAC7B,MAAI,MAAM,cAAM,KAAK,GAAG,MAAM,IAAI;AAClC,MAAI,MAAM,cAAM,KAAK,GAAG,KAAK,MAAM,IAAI;AAEvC,MAAI,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,QAChD,IACA,cAAM,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,CAAC;AAEhD,MAAI,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,IAAI,OAAO;AAC3C,SAAK,KAAK,KAAK,IAAI,MAAM,GAAG,IAAI,KAAK,IAAI,MAAM,GAAG;AAAA,EACpD,OAAO;AACL,SAAK,KAAK;AAAA,EACZ;AACA,MAAI,MAAM,KAAK,EAAE,GAAG;AAClB,SAAK,KAAK;AAAA,EACZ;AACA,OAAK,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;AAChD,OAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;AAClD,MAAI,CAAC,KAAK,OAAO;AACf,SAAK,QAAQ;AAAA,EACf;AACF;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAGZ,MAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE,KAAK,OAAO;AAClD,UAAM,aAAK,GAAG,KAAK,UAAU,IAAI;AAAA,EACnC;AAEA,MAAI,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO;AAC1C,MAAI,IAAI;AACR,MAAI,MAAM,OAAO;AACf,SAAK,cAAM,KAAK,GAAG,KAAK,KAAK,IAAI,GAAG,CAAC;AACrC,UAAM,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,EAAE;AAAA,EAC/C,OAAO;AACL,UAAM,MAAM,KAAK;AACjB,QAAI,OAAO,GAAG;AACZ,aAAO;AAAA,IACT;AACA,UAAM;AAAA,EACR;AACA,MAAI,QAAQ,KAAK,KAAK,mBAAW,MAAM,KAAK,KAAK;AACjD,IAAE,IAAI,KAAK,MAAM,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK;AAC/C,IAAE,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK;AAEzD,SAAO;AACT;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,KAAK,KAAK;AACd,MAAI,KAAK;AACT,MAAI,KAAK,EAAE,IAAI,KAAK,MAAM,KAAK;AAC/B,MAAI,IAAK,KAAK,MAAM,EAAE,IAAI,KAAK,MAAM,KAAK;AAC1C,MAAI,KAAK,KAAK,GAAG;AACf,UAAM,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC7B,UAAM;AAAA,EACR,OAAO;AACL,UAAM,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC9B,UAAM;AAAA,EACR;AACA,MAAI,QAAQ;AACZ,MAAI,QAAQ,GAAG;AACb,YAAQ,KAAK,MAAO,MAAM,GAAK,MAAM,CAAE;AAAA,EACzC;AACA,MAAK,QAAQ,KAAO,KAAK,KAAK,GAAI;AAChC,UAAM,IAAI,KAAK;AACf,SAAK,KAAK,IAAK,OAAO,KAAK,IAAI,KAAK,KAAM,GAAG;AAC7C,UAAM,cAAM,KAAK,GAAG,EAAE;AACtB,QAAI,QAAQ,OAAO;AACjB,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,UAAM,CAAC;AAAA,EACT;AACA,QAAM,mBAAW,QAAQ,KAAK,KAAK,KAAK,KAAK;AAE7C,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAIC,UAAQ;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAO,cAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;ACxJO,SAASC,SAAO;AACrB,OAAK,IAAI;AACT,OAAK,KAAK;AACV,OAAK,IAAI,KAAK,KAAK,KAAK,EAAE;AAC1B,MAAI,CAAC,KAAK,MAAM;AACd,SAAK,OAAO;AAAA,EACd;AACA,MAAI,CAAC,KAAK,OAAO;AACf,SAAK,QAAQ,qBAAqB;AAAA,EACpC;AAEA,MAAI,CAAC,KAAK,IAAI;AACZ,SAAK,KAAK;AAAA,EACZ;AACA,OAAK,MAAM;AACX,OAAK,MAAM,IAAI,KAAK;AACpB,OAAK,MAAM,KAAK;AAChB,OAAK,KAAK,KAAK;AACf,OAAK,IAAI,KAAK,KAAK,KAAK,EAAE;AAC1B,OAAK,OAAO,KAAK,KAAK,IAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,CAAC,KAAM,IAAI,KAAK,GAAG;AACrF,OAAK,KAAK;AACV,OAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI;AAClD,OAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,MAAM,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,OAAO,KAAK,IAAI,CAAC;AAC/G,OAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,GAAG,GAAG,KAAK,IAAI,IAAI,KAAK;AAC1G,OAAK,KAAK,KAAK;AACf,OAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,CAAC;AACzF,OAAK,KAAK;AACV,OAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AACzB,OAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE;AAC/C,OAAK,KAAK,KAAK,MAAM,KAAK;AAC5B;AAKO,SAASC,UAAQ,GAAG;AACzB,MAAI,KAAK,GAAG,QAAQ,GAAG,GAAG,KAAK;AAC/B,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AACZ,MAAI,YAAY,mBAAW,MAAM,KAAK,KAAK;AAE3C,QAAM,KAAK,KAAM,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAM,KAAK,OAAO,KAAK,IAAI,CAAE;AACtG,MAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,GAAG,GAAG,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK;AAC5F,WAAS,CAAC,YAAY,KAAK;AAC3B,MAAI,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,MAAM,CAAC;AAClG,MAAI,KAAK,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,CAAC,CAAC;AAC1D,QAAM,KAAK,IAAI;AACf,OAAK,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,GAAG,KAAK,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,CAAC;AAChH,IAAE,IAAI,KAAK,KAAK,IAAI,GAAG,IAAI;AAC3B,IAAE,IAAI,KAAK,KAAK,IAAI,GAAG,IAAI;AAE3B,MAAI,CAAC,KAAK,OAAO;AACf,MAAE,KAAK;AACP,MAAE,KAAK;AAAA,EACT;AACA,SAAQ;AACV;AAGO,SAASC,UAAQ,GAAG;AACzB,MAAI,GAAG,QAAQ,GAAG,GAAG,KAAK,IAAI;AAC9B,MAAI;AAIJ,MAAI,MAAM,EAAE;AACZ,IAAE,IAAI,EAAE;AACR,IAAE,IAAI;AACN,MAAI,CAAC,KAAK,OAAO;AACf,MAAE,KAAK;AACP,MAAE,KAAK;AAAA,EACT;AACA,OAAK,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACpC,QAAM,KAAK,MAAM,EAAE,GAAG,EAAE,CAAC;AACzB,MAAI,MAAM,KAAK,IAAI,KAAK,EAAE;AAC1B,MAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK;AAClG,MAAI,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AAC7F,WAAS,KAAK,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AAC1D,IAAE,IAAI,KAAK,QAAQ,SAAS,KAAK;AACjC,QAAM;AACN,OAAK;AACL,MAAI,OAAO;AACX,KAAG;AACD,MAAE,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,GAAG,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK;AACxM,QAAI,KAAK,IAAI,MAAM,EAAE,CAAC,IAAI,OAAc;AACtC,WAAK;AAAA,IACP;AACA,UAAM,EAAE;AACR,YAAQ;AAAA,EACV,SAAS,OAAO,KAAK,OAAO;AAC5B,MAAI,QAAQ,IAAI;AACd,WAAO;AAAA,EACT;AAEA,SAAQ;AACV;AAEO,IAAIC,UAAQ,CAAC,UAAU,QAAQ;AACtC,IAAO,iBAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;ACzGe,SAAR,aAAkB,IAAI,IAAI,IAAI,IAAI,KAAK;AAC5C,SAAQ,KAAK,MAAM,KAAK,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG;AAC5F;;;ACFe,SAAR,aAAkB,GAAG;AAC1B,SAAQ,IAAI,OAAO,KAAK,IAAI,IAAI,MAAM,IAAI,OAAO;AACnD;;;ACFe,SAAR,aAAkB,GAAG;AAC1B,SAAQ,QAAQ,KAAK,IAAI,OAAO,KAAK,IAAI,UAAU;AACrD;;;ACFe,SAAR,aAAkB,GAAG;AAC1B,SAAQ,aAAa,IAAI,KAAK,IAAI,OAAO;AAC3C;;;ACFe,SAAR,aAAkB,GAAG;AAC1B,SAAQ,IAAI,IAAI,KAAK,KAAK;AAC5B;;;ACFe,SAAR,WAAkB,GAAG,GAAG,QAAQ;AACrC,MAAI,OAAO,IAAI;AACf,SAAO,IAAI,KAAK,KAAK,IAAI,OAAO,IAAI;AACtC;;;ACAe,SAAR,mBAAkB,GAAG;AAC1B,SAAQ,KAAK,IAAI,CAAC,IAAI,UAAW,IAAK,IAAK,aAAK,CAAC,IAAI,KAAK;AAC5D;;;ACLe,SAAR,cAAkB,IAAI,IAAI,IAAI,IAAI,IAAI;AAC3C,MAAI;AACJ,MAAI;AAEJ,QAAM,KAAK;AACX,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAQ,MAAM,KAAK,MAAM,KAAK,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,OAAO,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG;AAC/L,WAAO;AACP,QAAI,KAAK,IAAI,IAAI,KAAK,OAAc;AAClC,aAAO;AAAA,IACT;AAAA,EACF;AAGA,SAAO;AACT;;;ACOO,SAASC,SAAO;AACrB,MAAI,CAAC,KAAK,QAAQ;AAChB,SAAK,KAAK,aAAK,KAAK,EAAE;AACtB,SAAK,KAAK,aAAK,KAAK,EAAE;AACtB,SAAK,KAAK,aAAK,KAAK,EAAE;AACtB,SAAK,KAAK,aAAK,KAAK,EAAE;AACtB,SAAK,MAAM,KAAK,IAAI,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AAAA,EACxE;AACF;AAIO,SAASC,UAAQ,GAAG;AAGzB,MAAI,GAAG;AACP,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AACZ,QAAM,mBAAW,MAAM,KAAK,KAAK;AAEjC,MAAI,KAAK,QAAQ;AACf,QAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC;AACpD,QAAI,KAAK,KAAK,KAAK,MAAM,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC,IAAI,KAAK;AAAA,EAChE,OAAO;AAEL,QAAI,SAAS,KAAK,IAAI,GAAG;AACzB,QAAI,SAAS,KAAK,IAAI,GAAG;AACzB,QAAI,KAAK,WAAG,KAAK,GAAG,KAAK,GAAG,MAAM;AAClC,QAAI,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;AACrC,QAAI,KAAK,MAAM,KAAK,IAAI,GAAG;AAC3B,QAAI,MAAM,KAAK;AACf,QAAI,KAAK,KAAK,KAAK,SAAS,UAAU,IAAI,KAAK;AAC/C,QAAI,KAAK,KAAK,IAAI,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAE9D,QAAI,KAAK,MAAM,IAAI,MAAM,MAAM,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM;AACjE,QAAI,KAAK,KAAK,MAAM,KAAK,SAAS,SAAS,OAAO,OAAO,IAAI,KAAK,IAAI,MAAM,MAAM;AAAA,EACpF;AAEA,IAAE,IAAI,IAAI,KAAK;AACf,IAAE,IAAI,IAAI,KAAK;AACf,SAAO;AACT;AAIO,SAASC,UAAQ,GAAG;AACzB,IAAE,KAAK,KAAK;AACZ,IAAE,KAAK,KAAK;AACZ,MAAI,IAAI,EAAE,IAAI,KAAK;AACnB,MAAI,IAAI,EAAE,IAAI,KAAK;AACnB,MAAI,KAAK;AAET,MAAI,KAAK,QAAQ;AACf,QAAI,KAAK,IAAI,KAAK;AAClB,UAAM,KAAK,KAAK,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,CAAC;AAC1C,UAAM,KAAK,MAAM,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC;AAAA,EAC5C,OAAO;AAEL,QAAI,MAAM,KAAK,MAAM,KAAK,IAAI;AAC9B,QAAI,OAAO,cAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AACxD,QAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,OAAO;AAC/C,QAAE,IAAI,KAAK;AACX,QAAE,IAAI;AACN,UAAI,IAAI,GAAG;AACT,UAAE,KAAK;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,QAAI,MAAM,WAAG,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC;AAE3C,QAAI,MAAM,MAAM,MAAM,MAAM,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AACxD,QAAI,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AACpC,QAAI,KAAK,IAAI,KAAK,IAAI;AACtB,QAAI,MAAM,KAAK;AACf,UAAM,OAAO,MAAM,KAAK,IAAI,IAAI,IAAI,MAAM,KAAK,MAAM,OAAO,IAAI,IAAI,OAAO,KAAK,KAAK;AACrF,UAAM,MAAM,IAAI,OAAO,MAAM,KAAK,IAAI,IAAI,OAAO,MAAM,MAAM,OAAO,KAAK,IAAI,IAAI;AAAA,EACnF;AAEA,IAAE,IAAI,mBAAW,MAAM,KAAK,KAAK;AACjC,IAAE,IAAI,mBAAW,GAAG;AACpB,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,WAAW,mBAAmB,MAAM;AACxD,IAAO,eAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;AC/Ge,SAAR,cAAkB,QAAQ,QAAQ;AACvC,MAAI;AACJ,MAAI,SAAS,MAAQ;AACnB,UAAM,SAAS;AACf,YAAS,IAAI,SAAS,WAAW,UAAU,IAAI,MAAM,OAAQ,MAAM,SAAU,KAAK,KAAK,IAAI,QAAQ,IAAI,IAAI;AAAA,EAC7G,OAAO;AACL,WAAQ,IAAI;AAAA,EACd;AACF;;;ACqBO,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,QAAQ;AAMZ,SAASC,SAAO;AACrB,MAAI,IAAI,KAAK,IAAI,KAAK,IAAI;AAC1B,MAAI,KAAK,IAAI,IAAI,OAAO,IAAI,OAAO;AACjC,SAAK,OAAO,KAAK,OAAO,IAAI,SAAS;AAAA,EACvC,WAAW,KAAK,IAAI,CAAC,IAAI,OAAO;AAC9B,SAAK,OAAO;AAAA,EACd,OAAO;AACL,SAAK,OAAO;AAAA,EACd;AACA,MAAI,KAAK,KAAK,GAAG;AACf,QAAI;AAEJ,SAAK,KAAK,cAAM,KAAK,GAAG,CAAC;AACzB,SAAK,MAAM,OAAO,IAAI,KAAK;AAC3B,SAAK,MAAM,QAAQ,KAAK,EAAE;AAC1B,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK;AACH,aAAK,KAAK;AACV;AAAA,MACF,KAAK;AACH,aAAK,KAAK;AACV;AAAA,MACF,KAAK;AACH,aAAK,KAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AACjC,aAAK,KAAK,IAAI,KAAK;AACnB,aAAK,MAAM;AACX,aAAK,MAAM,MAAM,KAAK;AACtB;AAAA,MACF,KAAK;AACH,aAAK,KAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AACjC,iBAAS,KAAK,IAAI,KAAK,IAAI;AAC3B,aAAK,QAAQ,cAAM,KAAK,GAAG,MAAM,IAAI,KAAK;AAC1C,aAAK,QAAQ,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK;AAClD,aAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,SAAS,MAAM,IAAI,KAAK,KAAK,KAAK;AAC3F,aAAK,OAAO,KAAK,MAAM,KAAK,MAAM,KAAK;AACvC,aAAK,OAAO,KAAK;AACjB;AAAA,IACJ;AAAA,EACF,OAAO;AACL,QAAI,KAAK,SAAS,OAAO;AACvB,WAAK,SAAS,KAAK,IAAI,KAAK,IAAI;AAChC,WAAK,SAAS,KAAK,IAAI,KAAK,IAAI;AAAA,IAClC;AAAA,EACF;AACF;AAIO,SAASC,UAAQ,GAAG;AAGzB,MAAI,GAAG,GAAG,QAAQ,QAAQ,QAAQ,GAAG,MAAM,MAAM,GAAG;AACpD,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAEZ,QAAM,mBAAW,MAAM,KAAK,KAAK;AACjC,MAAI,KAAK,QAAQ;AACf,aAAS,KAAK,IAAI,GAAG;AACrB,aAAS,KAAK,IAAI,GAAG;AACrB,aAAS,KAAK,IAAI,GAAG;AACrB,QAAI,KAAK,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,OAAO;AACxD,UAAK,KAAK,SAAS,KAAK,QAAS,IAAI,SAAS,SAAS,IAAI,KAAK,SAAS,SAAS,KAAK,SAAS,SAAS;AACzG,UAAI,KAAK,OAAO;AACd,eAAO;AAAA,MACT;AACA,UAAI,KAAK,KAAK,IAAI,CAAC;AACnB,UAAI,IAAI,SAAS,KAAK,IAAI,GAAG;AAC7B,WAAM,KAAK,SAAS,KAAK,QAAS,SAAS,KAAK,SAAS,SAAS,KAAK,SAAS,SAAS;AAAA,IAC3F,WAAW,KAAK,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,QAAQ;AACjE,UAAI,KAAK,SAAS,KAAK,QAAQ;AAC7B,iBAAS,CAAC;AAAA,MACZ;AACA,UAAI,KAAK,IAAI,MAAM,KAAK,IAAI,IAAI,OAAO;AACrC,eAAO;AAAA,MACT;AACA,UAAI,SAAS,MAAM;AACnB,UAAI,KAAM,KAAK,SAAS,KAAK,SAAU,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAC/D,UAAI,IAAI,KAAK,IAAI,GAAG;AACpB,WAAK;AAAA,IACP;AAAA,EACF,OAAO;AACL,WAAO;AACP,WAAO;AACP,QAAI;AACJ,aAAS,KAAK,IAAI,GAAG;AACrB,aAAS,KAAK,IAAI,GAAG;AACrB,aAAS,KAAK,IAAI,GAAG;AACrB,QAAI,cAAM,KAAK,GAAG,MAAM;AACxB,QAAI,KAAK,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,OAAO;AACxD,aAAO,IAAI,KAAK;AAChB,aAAO,KAAK,KAAK,IAAI,OAAO,IAAI;AAAA,IAClC;AACA,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK,KAAK;AACR,YAAI,IAAI,KAAK,QAAQ,OAAO,KAAK,QAAQ,OAAO;AAChD;AAAA,MACF,KAAK,KAAK;AACR,YAAI,IAAI,OAAO;AACf;AAAA,MACF,KAAK,KAAK;AACR,YAAI,UAAU;AACd,YAAI,KAAK,KAAK;AACd;AAAA,MACF,KAAK,KAAK;AACR,YAAI,MAAM;AACV,YAAI,KAAK,KAAK;AACd;AAAA,IACJ;AACA,QAAI,KAAK,IAAI,CAAC,IAAI,OAAO;AACvB,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AACR,YAAI,KAAK,KAAK,IAAI,CAAC;AACnB,YAAI,KAAK,SAAS,KAAK,OAAO;AAC5B,cAAI,KAAK,MAAM,KAAK,KAAK,QAAQ,OAAO,KAAK,QAAQ,OAAO;AAAA,QAC9D,OAAO;AACL,eAAK,IAAI,KAAK,KAAK,KAAK,IAAI,OAAO,OAAO,KAAK,OAAO,KAAK;AAAA,QAC7D;AACA,YAAI,KAAK,MAAM,IAAI,OAAO;AAC1B;AAAA,MACF,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AACR,YAAI,KAAK,GAAG;AACV,eAAK,IAAI,KAAK,KAAK,CAAC,KAAK;AACzB,cAAI,UAAW,KAAK,SAAS,KAAK,SAAU,IAAI,CAAC;AAAA,QACnD,OAAO;AACL,cAAI,IAAI;AAAA,QACV;AACA;AAAA,IACJ;AAAA,EACF;AAEA,IAAE,IAAI,KAAK,IAAI,IAAI,KAAK;AACxB,IAAE,IAAI,KAAK,IAAI,IAAI,KAAK;AACxB,SAAO;AACT;AAIO,SAASC,UAAQ,GAAG;AACzB,IAAE,KAAK,KAAK;AACZ,IAAE,KAAK,KAAK;AACZ,MAAI,IAAI,EAAE,IAAI,KAAK;AACnB,MAAI,IAAI,EAAE,IAAI,KAAK;AACnB,MAAI,KAAK,KAAK,KAAK,KAAK,GAAG,KAAK;AAChC,MAAI,KAAK,QAAQ;AACf,QAAI,OAAO,GACT,IAAI,OAAO;AAEb,SAAK,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC5B,UAAM,KAAK;AACX,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,UAAM,IAAI,KAAK,KAAK,GAAG;AACvB,QAAI,KAAK,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,OAAO;AACxD,aAAO,KAAK,IAAI,GAAG;AACnB,aAAO,KAAK,IAAI,GAAG;AAAA,IACrB;AACA,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK,KAAK;AACR,cAAO,KAAK,IAAI,EAAE,KAAK,QAAS,IAAI,KAAK,KAAK,IAAI,OAAO,EAAE;AAC3D,aAAK;AACL,YAAI,OAAO;AACX;AAAA,MACF,KAAK,KAAK;AACR,cAAO,KAAK,IAAI,EAAE,KAAK,QAAS,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,SAAS,IAAI,OAAO,KAAK,SAAS,EAAE;AACtG,aAAK,OAAO,KAAK;AACjB,aAAK,OAAO,KAAK,IAAI,GAAG,IAAI,KAAK,UAAU;AAC3C;AAAA,MACF,KAAK,KAAK;AACR,YAAI,CAAC;AACL,cAAM,UAAU;AAChB;AAAA,MACF,KAAK,KAAK;AACR,eAAO;AACP;AAAA,IACJ;AACA,UAAO,MAAM,MAAM,KAAK,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,SAAU,IAAI,KAAK,MAAM,GAAG,CAAC;AAAA,EACjG,OAAO;AACL,SAAK;AACL,QAAI,KAAK,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,OAAO;AACxD,WAAK,KAAK;AACV,WAAK,KAAK;AACV,YAAM,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC7B,UAAI,MAAM,OAAO;AACf,UAAE,IAAI,KAAK;AACX,UAAE,IAAI,KAAK;AACX,eAAO;AAAA,MACT;AACA,YAAM,IAAI,KAAK,KAAK,MAAM,MAAM,KAAK,EAAE;AACvC,YAAM,KAAK,IAAI,GAAG;AAClB,WAAM,MAAM,KAAK,IAAI,GAAG;AACxB,UAAI,KAAK,SAAS,KAAK,OAAO;AAC5B,aAAK,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,QAAQ;AAC/C,YAAI,KAAK,KAAK;AACd,YAAI,MAAM,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ;AAAA,MAChD,OAAO;AACL,aAAK,IAAI,MAAM;AACf,YAAI,KAAK,KAAK;AACd,YAAI,MAAM;AAAA,MACZ;AAAA,IACF,WAAW,KAAK,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,QAAQ;AACjE,UAAI,KAAK,SAAS,KAAK,QAAQ;AAC7B,YAAI,CAAC;AAAA,MACP;AACA,UAAK,IAAI,IAAI,IAAI;AACjB,UAAI,CAAC,GAAG;AACN,UAAE,IAAI,KAAK;AACX,UAAE,IAAI,KAAK;AACX,eAAO;AAAA,MACT;AACA,WAAK,IAAI,IAAI,KAAK;AAClB,UAAI,KAAK,SAAS,KAAK,QAAQ;AAC7B,aAAK,CAAC;AAAA,MACR;AAAA,IACF;AACA,UAAM,KAAK,MAAM,GAAG,CAAC;AACrB,UAAM,QAAQ,KAAK,KAAK,EAAE,GAAG,KAAK,GAAG;AAAA,EACvC;AAEA,IAAE,IAAI,mBAAW,KAAK,QAAQ,GAAG;AACjC,IAAE,IAAI;AACN,SAAO;AACT;AAGA,IAAI,MAAM;AAEV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,MAAM;AAEV,SAAS,QAAQ,IAAI;AACnB,MAAI;AACJ,MAAI,MAAM,CAAC;AACX,MAAI,CAAC,IAAI,KAAK;AACd,MAAI,KAAK;AACT,MAAI,CAAC,KAAK,IAAI;AACd,MAAI,CAAC,IAAI,IAAI;AACb,OAAK;AACL,MAAI,CAAC,KAAK,IAAI;AACd,MAAI,CAAC,KAAK,IAAI;AACd,MAAI,CAAC,IAAI,IAAI;AACb,SAAO;AACT;AAEA,SAAS,QAAQ,MAAM,KAAK;AAC1B,MAAI,IAAI,OAAO;AACf,SAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC;AAC9F;AAEO,IAAIC,UAAQ,CAAC,gCAAgC,gCAAgC,MAAM;AAC1F,IAAO,eAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AChTe,SAAR,cAAkB,GAAG;AAC1B,MAAI,KAAK,IAAI,CAAC,IAAI,GAAG;AACnB,QAAK,IAAI,IAAK,IAAI;AAAA,EACpB;AACA,SAAO,KAAK,KAAK,CAAC;AACpB;;;ACyBO,SAASC,SAAO;AACrB,MAAI,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,IAAI,OAAO;AAC3C;AAAA,EACF;AACA,OAAK,OAAO,KAAK,IAAI,KAAK;AAC1B,OAAK,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC;AACnC,OAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAE3B,OAAK,SAAS,KAAK,IAAI,KAAK,IAAI;AAChC,OAAK,SAAS,KAAK,IAAI,KAAK,IAAI;AAChC,OAAK,KAAK,KAAK;AACf,OAAK,MAAM,KAAK;AAChB,OAAK,MAAM,cAAM,KAAK,IAAI,KAAK,QAAQ,KAAK,MAAM;AAClD,OAAK,MAAM,cAAM,KAAK,IAAI,KAAK,MAAM;AAErC,OAAK,SAAS,KAAK,IAAI,KAAK,IAAI;AAChC,OAAK,SAAS,KAAK,IAAI,KAAK,IAAI;AAChC,OAAK,KAAK,KAAK;AACf,OAAK,MAAM,cAAM,KAAK,IAAI,KAAK,QAAQ,KAAK,MAAM;AAClD,OAAK,MAAM,cAAM,KAAK,IAAI,KAAK,MAAM;AAErC,OAAK,SAAS,KAAK,IAAI,KAAK,IAAI;AAChC,OAAK,SAAS,KAAK,IAAI,KAAK,IAAI;AAChC,OAAK,KAAK,KAAK;AACf,OAAK,MAAM,cAAM,KAAK,IAAI,KAAK,MAAM;AAErC,MAAI,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,IAAI,OAAO;AAC3C,SAAK,OAAO,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM,KAAK;AAAA,EAC5E,OAAO;AACL,SAAK,MAAM,KAAK;AAAA,EAClB;AACA,OAAK,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAC/C,OAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK;AACpE;AAKO,SAASC,UAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAEZ,OAAK,UAAU,KAAK,IAAI,GAAG;AAC3B,OAAK,UAAU,KAAK,IAAI,GAAG;AAE3B,MAAI,KAAK,cAAM,KAAK,IAAI,KAAK,OAAO;AACpC,MAAI,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,KAAK;AAC5D,MAAI,QAAQ,KAAK,MAAM,mBAAW,MAAM,KAAK,KAAK;AAClD,MAAI,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK;AACrC,MAAI,IAAI,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK;AAE/C,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,SAASC,UAAQ,GAAG;AACzB,MAAI,KAAK,IAAI,KAAK,OAAO,KAAK;AAE9B,IAAE,KAAK,KAAK;AACZ,IAAE,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK;AAC3B,MAAI,KAAK,OAAO,GAAG;AACjB,UAAM,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACrC,UAAM;AAAA,EACR,OAAO;AACL,UAAM,CAAC,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACtC,UAAM;AAAA,EACR;AACA,UAAQ;AACR,MAAI,QAAQ,GAAG;AACb,YAAQ,KAAK,MAAM,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;AAAA,EACzC;AACA,QAAM,MAAM,KAAK,MAAM,KAAK;AAC5B,MAAI,KAAK,QAAQ;AACf,UAAM,KAAK,MAAM,KAAK,IAAI,MAAM,QAAQ,IAAI,KAAK,IAAI;AAAA,EACvD,OAAO;AACL,UAAM,KAAK,IAAI,MAAM,OAAO,KAAK;AACjC,UAAM,KAAK,MAAM,KAAK,IAAI,EAAE;AAAA,EAC9B;AAEA,QAAM,mBAAW,QAAQ,KAAK,MAAM,KAAK,KAAK;AAC9C,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAKO,SAAS,MAAM,QAAQ,IAAI;AAChC,MAAI,QAAQ,QAAQ,KAAK,KAAK;AAC9B,MAAI,MAAM,cAAM,MAAM,EAAE;AACxB,MAAI,SAAS,OAAO;AAClB,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,SAAS;AACtB,WAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC5B,aAAS,KAAK,IAAI,GAAG;AACrB,aAAS,KAAK,IAAI,GAAG;AACrB,UAAM,SAAS;AACf,UAAM,IAAI,MAAM;AAChB,WAAO,MAAM,MAAM,MAAM,UAAU,MAAM,IAAI,UAAU,SAAS,MAAM,MAAM,SAAS,KAAK,KAAK,IAAI,QAAQ,IAAI,IAAI;AACnH,UAAM,MAAM;AACZ,QAAI,KAAK,IAAI,IAAI,KAAK,MAAM;AAC1B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,2BAA2B,qBAAqB,UAAU,KAAK;AACnF,IAAO,cAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AAAA,EACP;AACF;;;ACjIO,SAASC,SAAO;AAGrB,OAAK,UAAU,KAAK,IAAI,KAAK,IAAI;AACjC,OAAK,UAAU,KAAK,IAAI,KAAK,IAAI;AAEjC,OAAK,gBAAgB,MAAO,KAAK;AACjC,OAAK,KAAK;AACZ;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,GAAG;AACP,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAGZ,SAAO,mBAAW,MAAM,KAAK,KAAK;AAElC,WAAS,KAAK,IAAI,GAAG;AACrB,WAAS,KAAK,IAAI,GAAG;AAErB,WAAS,KAAK,IAAI,IAAI;AACtB,MAAI,KAAK,UAAU,SAAS,KAAK,UAAU,SAAS;AACpD,QAAM;AACN,MAAK,IAAI,KAAO,KAAK,IAAI,CAAC,KAAK,OAAQ;AACrC,QAAI,KAAK,KAAK,KAAK,IAAI,MAAM,SAAS,KAAK,IAAI,IAAI,IAAI;AACvD,QAAI,KAAK,KAAK,KAAK,IAAI,OAAO,KAAK,UAAU,SAAS,KAAK,UAAU,SAAS,UAAU;AAAA,EAC1F,OAAO;AAQL,QAAI,KAAK,KAAK,KAAK,gBAAgB,SAAS,KAAK,IAAI,IAAI;AACzD,QAAI,KAAK,KAAK,KAAK,iBAAiB,KAAK,UAAU,SAAS,KAAK,UAAU,SAAS;AAAA,EACtF;AACA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,SAASC,UAAQ,GAAG;AACzB,MAAI;AACJ,MAAI,MAAM;AACV,MAAI;AACJ,MAAI,KAAK;AAIT,IAAE,KAAK,EAAE,IAAI,KAAK,MAAM,KAAK;AAC7B,IAAE,KAAK,EAAE,IAAI,KAAK,MAAM,KAAK;AAE7B,IAAE,KAAK,KAAK;AACZ,IAAE,KAAK,KAAK;AAEZ,MAAK,KAAK,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,GAAI;AAC3C,QAAI,KAAK,MAAM,IAAI,KAAK,EAAE;AAC1B,WAAO,KAAK,IAAI,CAAC;AACjB,WAAO,KAAK,IAAI,CAAC;AAEjB,UAAM,cAAM,OAAO,KAAK,UAAW,EAAE,IAAI,OAAO,KAAK,UAAW,EAAE;AAClE,UAAM,KAAK,MAAM,EAAE,IAAI,MAAM,KAAK,KAAK,UAAU,OAAO,EAAE,IAAI,KAAK,UAAU,IAAI;AACjF,UAAM,mBAAW,KAAK,QAAQ,GAAG;AAAA,EACnC,OAAO;AACL,UAAM,KAAK;AACX,UAAM;AAAA,EACR;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,MAAM;AAC1B,IAAO,eAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;ACzGe,SAAR,eAAkB,QAAQ,GAAG;AAClC,MAAI,OAAO,KAAK,IAAI,SAAS,WAAW,IAAI,UAAU,KAAK,KAAK,IAAI,WAAW,IAAI,OAAO;AAC1F,MAAI,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,MAAQ;AACzC,QAAI,IAAI,GAAG;AACT,aAAQ,KAAK;AAAA,IACf,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,MAAM,KAAK,KAAK,MAAM,CAAC;AAC3B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,cAAU,KAAK,IAAI,GAAG;AACtB,cAAU,KAAK,IAAI,GAAG;AACtB,UAAM,SAAS;AACf,WAAO,KAAK,IAAI,IAAI,MAAM,KAAK,CAAC,KAAK,IAAI,YAAY,KAAK,IAAI,SAAS,UAAU,WAAW,IAAI,MAAM,OAAO,MAAM,SAAS,KAAK,KAAK,IAAI,QAAQ,IAAI,IAAI;AAC1J,WAAO;AACP,QAAI,KAAK,IAAI,IAAI,KAAK,OAAc;AAClC,aAAO;AAAA,IACT;AAAA,EACF;AAGA,SAAO;AACT;;;ACbO,SAASC,SAAO;AAErB,MAAI,CAAC,KAAK,QAAQ;AAChB,SAAK,KAAK,cAAM,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,GAAG,KAAK,IAAI,KAAK,MAAM,CAAC;AAAA,EACtE;AACF;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AACZ,MAAI,GAAG;AAGP,MAAI,OAAO,mBAAW,MAAM,KAAK,KAAK;AACtC,MAAI,KAAK,QAAQ;AACf,QAAI,KAAK,KAAK,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,MAAM;AAClD,QAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,KAAK,MAAM;AAAA,EAC7D,OAAO;AACL,QAAI,KAAK,cAAM,KAAK,GAAG,KAAK,IAAI,GAAG,CAAC;AACpC,QAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK;AACjC,QAAI,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,KAAK;AAAA,EACzC;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAIO,SAASC,UAAQ,GAAG;AACzB,IAAE,KAAK,KAAK;AACZ,IAAE,KAAK,KAAK;AACZ,MAAI,KAAK;AAET,MAAI,KAAK,QAAQ;AACf,UAAM,mBAAW,KAAK,QAAS,EAAE,IAAI,KAAK,IAAK,KAAK,IAAI,KAAK,MAAM,CAAC;AACpE,UAAM,KAAK,KAAM,EAAE,IAAI,KAAK,IAAK,KAAK,IAAI,KAAK,MAAM,CAAC;AAAA,EACxD,OAAO;AACL,UAAM,eAAO,KAAK,GAAG,IAAI,EAAE,IAAI,KAAK,KAAK,KAAK,CAAC;AAC/C,UAAM,mBAAW,KAAK,QAAQ,EAAE,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,EACxD;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,KAAK;AACzB,IAAO,cAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;ACtEO,SAASC,SAAO;AACrB,OAAK,KAAK,KAAK,MAAM;AACrB,OAAK,KAAK,KAAK,MAAM;AACrB,OAAK,OAAO,KAAK,QAAQ;AACzB,OAAK,QAAQ,KAAK,SAAS;AAC3B,OAAK,SAAS,KAAK,UAAU;AAC7B,OAAK,QAAQ,KAAK,SAAS;AAE3B,OAAK,KAAK,KAAK,IAAI,KAAK,MAAM;AAChC;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAEZ,MAAI,OAAO,mBAAW,MAAM,KAAK,KAAK;AACtC,MAAI,OAAO,mBAAW,MAAM,KAAK,IAAI;AACrC,IAAE,IAAI,KAAK,KAAM,KAAK,IAAI,OAAO,KAAK;AACtC,IAAE,IAAI,KAAK,KAAM,KAAK,IAAI;AAC1B,SAAO;AACT;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,IAAI,EAAE;AACV,MAAI,IAAI,EAAE;AAEV,IAAE,IAAI,mBAAW,KAAK,SAAU,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK,GAAI;AAClE,IAAE,IAAI,mBAAW,KAAK,QAAS,IAAI,KAAK,MAAO,KAAK,CAAG;AACvD,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,mBAAmB,2BAA2B,qCAAqC,KAAK;AAC5G,IAAO,cAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;ACrBA,IAAIC,YAAW;AAGR,SAASC,SAAO;AAGrB,OAAK,OAAO,KAAK,IAAI,KAAK;AAC1B,OAAK,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC;AACnC,OAAK,IAAI,KAAK,KAAK,KAAK,EAAE;AAC1B,OAAK,KAAK,aAAK,KAAK,EAAE;AACtB,OAAK,KAAK,aAAK,KAAK,EAAE;AACtB,OAAK,KAAK,aAAK,KAAK,EAAE;AACtB,OAAK,KAAK,aAAK,KAAK,EAAE;AACtB,OAAK,MAAM,KAAK,IAAI,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AACxE;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AACZ,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,mBAAW,MAAM,KAAK,KAAK;AACtC,OAAK,OAAO,KAAK,IAAI,GAAG;AACxB,MAAI,KAAK,QAAQ;AACf,QAAI,KAAK,IAAI,GAAG,KAAK,OAAO;AAC1B,UAAI,KAAK,IAAI;AACb,UAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IACzB,OAAO;AACL,UAAI,KAAK,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,GAAG;AACxC,UAAI,KAAK,KAAK,mBAAW,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,KAAK,KAAK,IAAI,GAAG;AAAA,IAC/E;AAAA,EACF,OAAO;AACL,QAAI,KAAK,IAAI,GAAG,KAAK,OAAO;AAC1B,UAAI,KAAK,IAAI;AACb,UAAI,KAAK,KAAK;AAAA,IAChB,OAAO;AACL,UAAI,KAAK,WAAG,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG;AACzD,UAAI,KAAK,KAAK,IAAI,EAAE;AACpB,UAAI,KAAK,IAAI,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,MAAM,MAAM,IAAI,KAAK,IAAI,EAAE;AAAA,IAC/F;AAAA,EACF;AACA,IAAE,IAAI,IAAI,KAAK;AACf,IAAE,IAAI,IAAI,KAAK;AACf,SAAO;AACT;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,KAAK,KAAK,GAAG,GAAG;AACpB,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,EAAE,IAAI,KAAK;AACf,MAAI,EAAE,IAAI,KAAK;AAEf,MAAI,KAAK,QAAQ;AACf,QAAI,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO;AAC7C,YAAM,mBAAW,IAAI,KAAK,IAAI,KAAK,KAAK;AACxC,YAAM;AAAA,IACR,OAAO;AACL,WAAK,KAAK,OAAO,IAAI,KAAK;AAC1B,WAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AACpC,YAAM;AACN,UAAI;AACJ,WAAK,IAAIH,WAAU,GAAG,EAAE,GAAG;AACzB,iBAAS,KAAK,IAAI,GAAG;AACrB,eAAO,MAAM,MAAM,MAAM,SAAS,KAAK,MAAM,OAAO,MAAM,MAAM,MAAM,YAAY,MAAM,MAAM,SAAS;AACvG,eAAO;AACP,YAAI,KAAK,IAAI,IAAI,KAAK,OAAO;AAC3B,gBAAM;AACN;AAAA,QACF;AAAA,MACF;AACA,YAAM,mBAAW,KAAK,QAAS,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,CAAC,IAAK,KAAK,IAAI,GAAG,CAAC;AAAA,IACvF;AAAA,EACF,OAAO;AACL,QAAI,KAAK,IAAI,IAAI,KAAK,GAAG,KAAK,OAAO;AACnC,YAAM;AACN,YAAM,mBAAW,KAAK,QAAQ,IAAI,KAAK,CAAC;AAAA,IAC1C,OAAO;AACL,YAAM,KAAK,MAAM,KAAK,KAAK;AAC3B,WAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AACpC,YAAM;AACN,UAAI,IAAI,KAAK,MAAM;AACnB,UAAI;AACJ,WAAK,IAAIA,WAAU,GAAG,EAAE,GAAG;AACzB,cAAM,KAAK,IAAI,KAAK,IAAI,GAAG;AAC3B,aAAK,KAAK,KAAK,IAAI,MAAM,GAAG,IAAI,KAAK,IAAI,GAAG;AAC5C,cAAM,KAAK,IAAI,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAC3D,eAAO,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,GAAG;AACnH,aAAK,MAAM,KAAK;AAChB,gBAAQ,MAAM,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK;AAC1L,eAAO;AACP,YAAI,KAAK,IAAI,IAAI,KAAK,OAAO;AAC3B,gBAAM;AACN;AAAA,QACF;AAAA,MACF;AAGA,WAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,GAAG;AACvE,YAAM,mBAAW,KAAK,QAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC;AAAA,IAC1E;AAAA,EACF;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAII,UAAQ,CAAC,aAAa,sBAAsB,MAAM;AAC7D,IAAO,eAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;AC1HO,SAASC,SAAO;AACrB,OAAK,IAAI,CAAC;AACV,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,EAAE,IAAI;AAEb,OAAK,OAAO,CAAC;AACb,OAAK,OAAO,CAAC;AACb,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AAEf,OAAK,OAAO,CAAC;AACb,OAAK,OAAO,CAAC;AACb,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AACf,OAAK,KAAK,CAAC,IAAI;AAEf,OAAK,IAAI,CAAC;AACV,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACZ,OAAK,EAAE,CAAC,IAAI;AACd;AAMO,SAASC,UAAQ,GAAG;AACzB,MAAI;AACJ,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAEZ,MAAI,YAAY,MAAM,KAAK;AAC3B,MAAI,YAAY,MAAM,KAAK;AAI3B,MAAI,QAAQ,YAAY,aAAa;AACrC,MAAI,WAAW;AACf,MAAI,UAAU;AAEd,MAAI,QAAQ;AACZ,OAAK,IAAI,GAAG,KAAK,IAAI,KAAK;AACxB,cAAU,UAAU;AACpB,YAAQ,QAAQ,KAAK,EAAE,CAAC,IAAI;AAAA,EAC9B;AAGA,MAAI,QAAQ;AACZ,MAAI,QAAQ;AAGZ,MAAI,UAAU;AACd,MAAI,UAAU;AACd,MAAI;AACJ,MAAI;AAEJ,MAAI,OAAO;AACX,MAAI,OAAO;AACX,OAAK,IAAI,GAAG,KAAK,GAAG,KAAK;AACvB,eAAW,UAAU,QAAQ,UAAU;AACvC,eAAW,UAAU,QAAQ,UAAU;AACvC,cAAU;AACV,cAAU;AACV,WAAO,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,KAAK,KAAK,CAAC,IAAI;AACtD,WAAO,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,KAAK,KAAK,CAAC,IAAI;AAAA,EACxD;AAGA,IAAE,IAAK,OAAO,KAAK,IAAK,KAAK;AAC7B,IAAE,IAAK,OAAO,KAAK,IAAK,KAAK;AAE7B,SAAO;AACT;AAKO,SAASC,UAAQ,GAAG;AACzB,MAAI;AACJ,MAAI,IAAI,EAAE;AACV,MAAI,IAAI,EAAE;AAEV,MAAI,UAAU,IAAI,KAAK;AACvB,MAAI,UAAU,IAAI,KAAK;AAGvB,MAAI,OAAO,UAAU,KAAK;AAC1B,MAAI,OAAO,UAAU,KAAK;AAG1B,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI;AACJ,MAAI;AAEJ,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,OAAK,IAAI,GAAG,KAAK,GAAG,KAAK;AACvB,cAAU,SAAS,OAAO,SAAS;AACnC,cAAU,SAAS,OAAO,SAAS;AACnC,aAAS;AACT,aAAS;AACT,YAAQ,QAAQ,KAAK,KAAK,CAAC,IAAI,SAAS,KAAK,KAAK,CAAC,IAAI;AACvD,YAAQ,QAAQ,KAAK,KAAK,CAAC,IAAI,SAAS,KAAK,KAAK,CAAC,IAAI;AAAA,EACzD;AAMA,WAAS,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK;AACxC,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI;AACJ,QAAI;AAEJ,QAAI,SAAS;AACb,QAAI,SAAS;AACb,SAAK,IAAI,GAAG,KAAK,GAAG,KAAK;AACvB,iBAAW,UAAU,QAAQ,UAAU;AACvC,iBAAW,UAAU,QAAQ,UAAU;AACvC,gBAAU;AACV,gBAAU;AACV,eAAS,UAAU,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,KAAK,KAAK,CAAC,IAAI;AACrE,eAAS,UAAU,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,KAAK,KAAK,CAAC,IAAI;AAAA,IACvE;AAEA,cAAU;AACV,cAAU;AACV,QAAI,SAAS,KAAK,KAAK,CAAC;AACxB,QAAI,SAAS,KAAK,KAAK,CAAC;AACxB,SAAK,IAAI,GAAG,KAAK,GAAG,KAAK;AACvB,iBAAW,UAAU,QAAQ,UAAU;AACvC,iBAAW,UAAU,QAAQ,UAAU;AACvC,gBAAU;AACV,gBAAU;AACV,eAAS,SAAS,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,KAAK,KAAK,CAAC,IAAI;AAC/D,eAAS,SAAS,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,KAAK,KAAK,CAAC,IAAI;AAAA,IACjE;AAGA,QAAI,OAAO,SAAS,SAAS,SAAS;AACtC,aAAS,SAAS,SAAS,SAAS,UAAU;AAC9C,aAAS,SAAS,SAAS,SAAS,UAAU;AAAA,EAChD;AAGA,MAAI,QAAQ;AACZ,MAAI,WAAW;AACf,MAAI,UAAU;AAEd,MAAI,QAAQ;AACZ,OAAK,IAAI,GAAG,KAAK,GAAG,KAAK;AACvB,cAAU,UAAU;AACpB,YAAQ,QAAQ,KAAK,EAAE,CAAC,IAAI;AAAA,EAC9B;AAIA,MAAI,MAAM,KAAK,OAAQ,QAAQ,aAAa;AAC5C,MAAI,MAAM,KAAK,QAAQ;AAEvB,IAAE,IAAI;AACN,IAAE,IAAI;AAEN,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,wBAAwB,MAAM;AAClD,IAAO,eAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;ACvNO,SAASC,SAAO;AAEvB;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAGZ,MAAI,OAAO,mBAAW,MAAM,KAAK,KAAK;AACtC,MAAI,IAAI,KAAK,KAAK,KAAK,IAAI;AAC3B,MAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAK,KAAK,KAAK,IAAM,MAAM,GAAI,CAAC,IAAI;AAE7E,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAIO,SAASC,UAAQ,GAAG;AACzB,IAAE,KAAK,KAAK;AACZ,IAAE,KAAK,KAAK;AAEZ,MAAI,MAAM,mBAAW,KAAK,QAAQ,EAAE,IAAI,KAAK,CAAC;AAC9C,MAAI,MAAM,OAAO,KAAK,KAAK,KAAK,IAAI,MAAM,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK;AAErE,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,sBAAsB,MAAM;AAChD,IAAO,eAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;AC/CA,IAAIC,YAAW;AAkBR,SAASC,SAAO;AAIrB,MAAI,CAAC,KAAK,QAAQ;AAChB,SAAK,KAAK,gBAAQ,KAAK,EAAE;AAAA,EAC3B,OAAO;AACL,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,KAAK;AACV,SAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK,CAAC;AAC1C,SAAK,MAAM,KAAK,OAAO,KAAK,IAAI;AAAA,EAClC;AACF;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,GAAG;AACP,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAGZ,QAAM,mBAAW,MAAM,KAAK,KAAK;AAEjC,MAAI,KAAK,QAAQ;AACf,QAAI,CAAC,KAAK,GAAG;AACX,YAAM,KAAK,MAAM,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AAAA,IAC3D,OAAO;AACL,UAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAC7B,eAAS,IAAIF,WAAU,GAAG,EAAE,GAAG;AAC7B,YAAIG,MAAK,KAAK,IAAI,MAAM,KAAK,IAAI,GAAG,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG;AACnE,eAAOA;AACP,YAAI,KAAK,IAAIA,EAAC,IAAI,OAAO;AACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG;AACpD,QAAI,KAAK,IAAI,KAAK,MAAM;AAAA,EAC1B,OAAO;AACL,QAAI,IAAI,KAAK,IAAI,GAAG;AACpB,QAAI,IAAI,KAAK,IAAI,GAAG;AACpB,QAAI,KAAK,IAAI,gBAAQ,KAAK,GAAG,GAAG,KAAK,EAAE;AACvC,QAAI,KAAK,IAAI,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC;AAAA,EACtD;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,SAASC,UAAQ,GAAG;AACzB,MAAI,KAAK,MAAM,KAAK;AAEpB,IAAE,KAAK,KAAK;AACZ,QAAM,EAAE,IAAI,KAAK;AACjB,IAAE,KAAK,KAAK;AACZ,QAAM,EAAE,IAAI,KAAK;AAEjB,MAAI,KAAK,QAAQ;AACf,WAAO,KAAK;AACZ,UAAM,OAAO,KAAK,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG;AAC9C,QAAI,KAAK,GAAG;AACV,YAAM,eAAO,KAAK,IAAI,MAAM,KAAK,IAAI,GAAG,KAAK,KAAK,CAAC;AAAA,IACrD,WAAW,KAAK,MAAM,GAAG;AACvB,YAAM,cAAM,KAAK,IAAI,GAAG,IAAI,KAAK,CAAC;AAAA,IACpC;AACA,UAAM,mBAAW,MAAM,KAAK,KAAK;AACjC,UAAM,mBAAW,GAAG;AAAA,EACtB,OAAO;AACL,UAAM,oBAAY,EAAE,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE;AAChD,QAAI,KAAK,IAAI,GAAG;AAChB,QAAI,IAAI,SAAS;AACf,UAAI,KAAK,IAAI,GAAG;AAChB,aAAO,KAAK,QAAQ,EAAE,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG;AAEjF,YAAM,mBAAW,IAAI;AAAA,IACvB,WAAY,IAAI,QAAS,SAAS;AAChC,YAAM,KAAK;AAAA,IACb;AAAA,EACF;AACA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,cAAc,MAAM;AACxC,IAAO,eAAQ;AAAA,EACb,MAAMJ;AAAA,EACN,SAASC;AAAA,EACT,SAASE;AAAA,EACT,OAAOC;AACT;;;ACjHO,SAASC,SAAO;AAAC;AAIjB,SAASC,UAAQ,GAAG;AAGzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAEZ,MAAI,YAAY,mBAAW,MAAM,KAAK,KAAK;AAC3C,MAAI,QAAQ;AACZ,MAAI,MAAM,KAAK,KAAK,KAAK,IAAI,GAAG;AAIhC,SAAO,MAAM;AACX,QAAI,cAAc,EAAE,QAAQ,KAAK,IAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,IAAI,KAAK;AACxE,aAAS;AACT,QAAI,KAAK,IAAI,WAAW,IAAI,OAAO;AACjC;AAAA,IACF;AAAA,EACF;AACA,WAAS;AAKT,MAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO;AACvC,gBAAY;AAAA,EACd;AACA,MAAI,IAAI,iBAAiB,KAAK,IAAI,YAAY,KAAK,IAAI,KAAK,IAAI,KAAK;AACrE,MAAI,IAAI,kBAAkB,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AAE1D,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,SAASC,UAAQ,GAAG;AACzB,MAAI;AACJ,MAAI;AAIJ,IAAE,KAAK,KAAK;AACZ,IAAE,KAAK,KAAK;AACZ,QAAM,EAAE,KAAK,kBAAkB,KAAK;AAKpC,MAAI,KAAK,IAAI,GAAG,IAAI,gBAAgB;AAClC,UAAM;AAAA,EACR;AACA,UAAQ,KAAK,KAAK,GAAG;AACrB,MAAI,MAAM,mBAAW,KAAK,QAAS,EAAE,KAAK,iBAAiB,KAAK,IAAI,KAAK,IAAI,KAAK,EAAG;AACrF,MAAI,MAAO,CAAC,KAAK,IAAK;AACpB,UAAM,CAAC,KAAK;AAAA,EACd;AACA,MAAI,MAAM,KAAK,IAAI;AACjB,UAAM,KAAK;AAAA,EACb;AACA,SAAO,IAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK;AAC/C,MAAI,KAAK,IAAI,GAAG,IAAI,GAAG;AACrB,UAAM;AAAA,EACR;AACA,MAAI,MAAM,KAAK,KAAK,GAAG;AAEvB,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,aAAa,MAAM;AACvC,IAAO,eAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;AChDO,SAASC,SAAO;AAIrB,MAAI,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,IAAI,OAAO;AAC3C;AAAA,EACF;AACA,OAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,OAAK,OAAO,KAAK,IAAI,KAAK;AAC1B,OAAK,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC;AACnC,OAAK,IAAI,KAAK,KAAK,KAAK,EAAE;AAC1B,OAAK,KAAK,aAAK,KAAK,EAAE;AACtB,OAAK,KAAK,aAAK,KAAK,EAAE;AACtB,OAAK,KAAK,aAAK,KAAK,EAAE;AACtB,OAAK,KAAK,aAAK,KAAK,EAAE;AAEtB,OAAK,UAAU,KAAK,IAAI,KAAK,IAAI;AACjC,OAAK,UAAU,KAAK,IAAI,KAAK,IAAI;AAEjC,OAAK,MAAM,cAAM,KAAK,GAAG,KAAK,SAAS,KAAK,OAAO;AACnD,OAAK,MAAM,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AAE7D,MAAI,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI,IAAI,OAAO;AAC3C,SAAK,KAAK,KAAK;AAAA,EACjB,OAAO;AACL,SAAK,UAAU,KAAK,IAAI,KAAK,IAAI;AACjC,SAAK,UAAU,KAAK,IAAI,KAAK,IAAI;AACjC,SAAK,MAAM,cAAM,KAAK,GAAG,KAAK,SAAS,KAAK,OAAO;AACnD,SAAK,MAAM,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AAC7D,SAAK,MAAM,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM,KAAK;AAAA,EACrD;AACA,OAAK,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK;AACpC,OAAK,MAAM,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AAC7D,OAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK;AACpC;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AACZ,MAAI;AAIJ,MAAI,KAAK,QAAQ;AACf,UAAM,KAAK,KAAK,KAAK,IAAI;AAAA,EAC3B,OAAO;AACL,QAAI,KAAK,aAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AACrD,UAAM,KAAK,KAAK,KAAK,IAAI;AAAA,EAC3B;AACA,MAAI,QAAQ,KAAK,KAAK,mBAAW,MAAM,KAAK,KAAK;AACjD,MAAI,IAAI,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK;AACtC,MAAI,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK;AAChD,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAIO,SAASC,UAAQ,GAAG;AACzB,IAAE,KAAK,KAAK;AACZ,IAAE,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK;AAC3B,MAAI,KAAK,KAAK,KAAK;AACnB,MAAI,KAAK,MAAM,GAAG;AAChB,UAAM,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACrC,UAAM;AAAA,EACR,OAAO;AACL,UAAM,CAAC,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACtC,UAAM;AAAA,EACR;AACA,MAAI,QAAQ;AACZ,MAAI,QAAQ,GAAG;AACb,YAAQ,KAAK,MAAM,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;AAAA,EACzC;AAEA,MAAI,KAAK,QAAQ;AACf,UAAM,mBAAW,KAAK,QAAQ,QAAQ,KAAK,EAAE;AAC7C,UAAM,mBAAW,KAAK,IAAI,MAAM,KAAK,CAAC;AACtC,MAAE,IAAI;AACN,MAAE,IAAI;AACN,WAAO;AAAA,EACT,OAAO;AACL,QAAI,KAAK,KAAK,IAAI,MAAM,KAAK;AAC7B,UAAM,cAAM,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAClD,UAAM,mBAAW,KAAK,QAAQ,QAAQ,KAAK,EAAE;AAC7C,MAAE,IAAI;AACN,MAAE,IAAI;AACN,WAAO;AAAA,EACT;AACF;AAEO,IAAIC,UAAQ,CAAC,qBAAqB,MAAM;AAC/C,IAAO,eAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;ACrHO,SAASC,SAAO;AAErB,OAAK,IAAI,KAAK;AAChB;AAEO,SAASC,UAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAIZ,MAAI,OAAO,mBAAW,MAAM,KAAK,KAAK;AACtC,MAAI,GAAG;AAEP,MAAI,KAAK,IAAI,GAAG,KAAK,OAAO;AAC1B,QAAI,KAAK,KAAK,KAAK,IAAI;AACvB,QAAI,KAAK;AAAA,EACX;AACA,MAAI,QAAQ,cAAM,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,CAAC;AAC7C,MAAK,KAAK,IAAI,IAAI,KAAK,SAAW,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,KAAK,OAAQ;AAC7E,QAAI,KAAK;AACT,QAAI,OAAO,GAAG;AACZ,UAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK;AAAA,IACvD,OAAO;AACL,UAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,MAAM,KAAK;AAAA,IACxD;AAAA,EAEF;AACA,MAAI,KAAK,MAAM,KAAK,IAAK,KAAK,KAAK,OAAS,OAAO,KAAK,EAAG;AAC3D,MAAI,MAAM,KAAK;AACf,MAAI,QAAQ,KAAK,IAAI,KAAK;AAC1B,MAAI,QAAQ,KAAK,IAAI,KAAK;AAE1B,MAAI,IAAI,SAAS,QAAQ,QAAQ;AACjC,MAAI,MAAM,IAAI;AACd,MAAI,IAAI,KAAK,IAAI,QAAQ;AACzB,MAAI,MAAM,IAAI;AACd,MAAI,MAAM,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI,OAAO,KAAK,KAAK,OAAO,IAAI,QAAQ,IAAI,QAAQ,MAAM,QAAQ,MAAM,IAAI,MAAM,MAAM;AAC5H,MAAI,OAAO,GAAG;AACZ,UAAM,CAAC;AAAA,EACT;AACA,MAAI,KAAK,KAAK;AAEd,MAAI,IAAI,MAAM;AACd,QAAM,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,MAAM,MAAM,QAAQ,MAAM,KAAK,IAAI,CAAC,MAAM,MAAM;AAC5F,MAAI,OAAO,GAAG;AAEZ,QAAI,KAAK,KAAK;AAAA,EAChB,OAAO;AAEL,QAAI,KAAK,KAAK;AAAA,EAChB;AACA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,KAAK;AACT,MAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AACzB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAIJ,IAAE,KAAK,KAAK;AACZ,IAAE,KAAK,KAAK;AACZ,QAAM,KAAK,KAAK,KAAK;AACrB,OAAK,EAAE,IAAI;AACX,OAAK,EAAE,IAAI;AACX,QAAM,KAAK,KAAK,KAAK;AACrB,OAAK,CAAC,KAAK,IAAI,EAAE,KAAK,IAAI;AAC1B,OAAK,KAAK,IAAI,KAAK,KAAK,KAAK;AAC7B,OAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,MAAM;AACvC,MAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM;AAC/E,QAAM,KAAK,KAAK,KAAK,IAAI,MAAM;AAC/B,OAAK,IAAI,KAAK,KAAK,CAAC,KAAK,CAAC;AAC1B,QAAQ,IAAI,IAAK,KAAM;AACvB,MAAI,KAAK,IAAI,GAAG,IAAI,GAAG;AACrB,QAAI,OAAO,GAAG;AACZ,YAAM;AAAA,IACR,OAAO;AACL,YAAM;AAAA,IACR;AAAA,EACF;AACA,QAAM,KAAK,KAAK,GAAG,IAAI;AACvB,MAAI,EAAE,KAAK,GAAG;AACZ,WAAO,CAAC,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,MAAM,KAAK;AAAA,EACjE,OAAO;AACL,UAAM,EAAE,CAAC,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,MAAM,KAAK;AAAA,EAClE;AAEA,MAAI,KAAK,IAAI,EAAE,IAAI,OAAO;AACxB,UAAM,KAAK;AAAA,EACb,OAAO;AACL,UAAM,mBAAW,KAAK,QAAQ,KAAK,MAAM,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,GAAG,KAAK,IAAI,EAAE;AAAA,EACjH;AAEA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,qBAAqB,iBAAiB,mBAAmB,OAAO;AACpF,IAAO,gBAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;AClHO,SAAS,gBAAgB,MAAM,MAAM,MAAM,MAAM,GAAG,GAAG;AAC5D,QAAM,IAAI,OAAO;AACjB,QAAM,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAC7C,QAAM,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAC7C,QAAM,QAAQ,KAAK,IAAI,EAAE,GAAG,QAAQ,KAAK,IAAI,EAAE;AAC/C,QAAM,QAAQ,KAAK,IAAI,EAAE,GAAG,QAAQ,KAAK,IAAI,EAAE;AAE/C,MAAI,SAAS,GAAG,SAAS,YAAY;AACrC,MAAI,WAAW,WAAW,UAAU,UAAU,OAAO,UAAU,WAAW,YAAY;AACtF,MAAI,KAAKC,IAAG,GAAG,YAAY;AAE3B,KAAG;AACD,gBAAY,KAAK,IAAI,MAAM;AAC3B,gBAAY,KAAK,IAAI,MAAM;AAC3B,eAAW,KAAK;AAAA,MACb,QAAQ,aAAc,QAAQ,cAC5B,QAAQ,QAAQ,QAAQ,QAAQ,cAChC,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,IACrC;AACA,QAAI,aAAa,GAAG;AAClB,aAAO,EAAE,MAAM,GAAG,KAAK,EAAE;AAAA,IAC3B;AACA,eAAW,QAAQ,QAAQ,QAAQ,QAAQ;AAC3C,YAAQ,KAAK,MAAM,UAAU,QAAQ;AACrC,eAAW,QAAQ,QAAQ,YAAY;AACvC,gBAAY,IAAI,WAAW;AAC3B,iBAAc,cAAc,IAAM,WAAW,IAAI,QAAQ,QAAQ,YAAa;AAC9E,QAAI,IAAI,KAAK,aAAa,IAAI,KAAK,IAAI,IAAI;AAC3C,cAAU;AACV,aAAS,KAAK,IAAI,KAAK,IAAI,YACxB,QAAQ,IAAI,YAAY,aAAa,IAAI,YAAY,KAAK,IAAI,aAAa;AAAA,EAChF,SAAS,KAAK,IAAI,SAAS,OAAO,IAAI,SAAS,EAAE,YAAY;AAE7D,MAAI,cAAc,GAAG;AACnB,WAAO,EAAE,MAAM,KAAK,KAAK,IAAI;AAAA,EAC/B;AAEA,QAAM,aAAa,IAAI,IAAK,KAAK,IAAI,MAAO,KAAK,IAAI,QAAS,KAAK,IAAI,MAAO,KAAK,IAAI;AACvF,EAAAA,KAAI,IAAI,MAAM,SAAS,OAAO,OAAO,OAAO,OAAO,MAAM,MAAM;AAC/D,MAAI,MAAM,QAAQ,MAAM,OAAO,OAAO,OAAO,KAAK,KAAK;AACvD,eAAa,IAAI,YAAY,aAAa,IAAI,KAAK,YAAY,KAAK,IAAI,aAAa,cACjF,IAAI,IAAI,cAAc,KAAK,IAAI,WAAW,aAAa,KAAK,IAAI,aAAa;AAEjF,MAAK,KAAK,IAAI,KAAMA,MAAK,QAAQ;AAGjC,QAAM,OAAO,KAAK,MAAM,QAAQ,WAAW,QAAQ,QAAQ,QAAQ,QAAQ,SAAS;AAEpF,SAAO,EAAE,MAAM,KAAK,EAAE;AACxB;AAcO,SAAS,eAAe,MAAM,MAAM,MAAM,KAAK,GAAG,GAAG;AAC1D,QAAM,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAC7C,QAAM,QAAQ,KAAK,IAAI,EAAE,GAAG,QAAQ,KAAK,IAAI,EAAE;AAC/C,QAAM,YAAY,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,IAAI,IAAI;AAE3D,QAAM,SAAS,KAAK,MAAM,OAAO,QAAQ,SAAS;AAClD,QAAM,WAAW,QAAQ;AACzB,QAAM,YAAY,IAAI,WAAW;AACjC,QAAM,MAAM,aAAa,IAAI,IAAK,KAAK,IAAI,MAAO,KAAK,IAAI,QAAS,KAAK,IAAI,MAAO,KAAK,IAAI;AAC7F,QAAMA,KAAI,IAAI,MAAM,SAAS,OAAO,OAAO,OAAO,OAAO,MAAM,MAAM;AACrE,QAAM,IAAI,MAAM,QAAQ,MAAM,OAAO,OAAO,OAAO,KAAK,KAAK;AAE7D,MAAI,QAAQ,OAAQ,KAAK,IAAI,KAAMA,KAAI,QAAQ,YAAY;AAC3D,MAAI,YAAY,UAAU,UAAU;AAEpC,KAAG;AACD,iBAAa,KAAK,IAAI,IAAI,SAAS,KAAK;AACxC,eAAW,KAAK,IAAI,KAAK;AACzB,eAAW,KAAK,IAAI,KAAK;AACzB,iBAAa,IAAI,YAAY,aAAa,IAAI,KAAK,YAAY,KAAK,IAAI,aAAa,cACjF,IAAI,IAAI,cAAc,KAAK,IAAI,WAAW,aAAa,KAAK,IAAI,aAAa;AACjF,aAAS;AACT,YAAQ,OAAQ,KAAK,IAAI,KAAMA,MAAK;AAAA,EACtC,SAAS,KAAK,IAAI,QAAQ,MAAM,IAAI,SAAS,EAAE,YAAY;AAE3D,MAAI,cAAc,GAAG;AACnB,WAAO,EAAE,MAAM,KAAK,MAAM,IAAI;AAAA,EAChC;AAEA,QAAM,MAAM,QAAQ,WAAW,QAAQ,WAAW;AAClD,QAAM,OAAO,KAAK;AAAA,IAChB,QAAQ,WAAW,QAAQ,WAAW;AAAA,KACrC,IAAI,KAAK,KAAK,KAAK,WAAW,WAAW,MAAM,GAAG;AAAA,EACrD;AACA,QAAM,SAAS,KAAK;AAAA,IAClB,WAAW;AAAA,IACX,QAAQ,WAAW,QAAQ,WAAW;AAAA,EACxC;AACA,QAAM,IAAI,IAAI,KAAK,aAAa,IAAI,KAAK,IAAI,IAAI;AACjD,QAAM,IAAI,UAAU,IAAI,KAAK,IAAI,YAC5B,QAAQ,IAAI,YAAY,aAAa,IAAI,YAAY,KAAK,IAAI,aAAa;AAChF,QAAM,OAAO,OAAO;AAEpB,SAAO,EAAE,MAAM,KAAK;AACtB;;;ACrGO,SAASC,SAAO;AACrB,OAAK,UAAU,KAAK,IAAI,KAAK,IAAI;AACjC,OAAK,UAAU,KAAK,IAAI,KAAK,IAAI;AAEjC,OAAK,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,EAAE;AAC/C;AAEO,SAASC,UAAQ,GAAG;AACzB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AACZ,MAAI,SAAS,KAAK,IAAI,EAAE,CAAC;AACzB,MAAI,SAAS,KAAK,IAAI,EAAE,CAAC;AACzB,MAAI,OAAO,mBAAW,MAAM,KAAK,KAAK;AACtC,MAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;AACjD,MAAI,KAAK,QAAQ;AACf,QAAI,KAAK,IAAI,KAAK,UAAU,CAAC,KAAK,OAAO;AAEvC,QAAE,IAAI,KAAK,KAAK,KAAK,KAAK,UAAU,OAAO,KAAK,IAAI,IAAI;AACxD,QAAE,IAAI,KAAK,KAAK,KAAK,KAAK,UAAU,OAAO,KAAK,IAAI,IAAI;AACxD,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,KAAK,UAAU,CAAC,KAAK,OAAO;AAE9C,QAAE,IAAI,KAAK,KAAK,KAAK,KAAK,UAAU,OAAO,KAAK,IAAI,IAAI;AACxD,QAAE,IAAI,KAAK,KAAK,KAAK,KAAK,UAAU,OAAO,KAAK,IAAI,IAAI;AACxD,aAAO;AAAA,IACT,OAAO;AAEL,cAAQ,KAAK,UAAU,SAAS,KAAK,UAAU,SAAS,KAAK,IAAI,IAAI;AACrE,UAAI,KAAK,KAAK,KAAK;AACnB,WAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI;AAC3B,QAAE,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,IAAI;AACpD,QAAE,IAAI,KAAK,KAAK,KAAK,IAAI,MAAM,KAAK,UAAU,SAAS,KAAK,UAAU,SAAS,KAAK,IAAI,IAAI;AAC5F,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,SAAK,aAAK,KAAK,EAAE;AACjB,SAAK,aAAK,KAAK,EAAE;AACjB,SAAK,aAAK,KAAK,EAAE;AACjB,SAAK,aAAK,KAAK,EAAE;AACjB,QAAI,KAAK,IAAI,KAAK,UAAU,CAAC,KAAK,OAAO;AAEvC,YAAM,KAAK,IAAI,aAAK,IAAI,IAAI,IAAI,IAAI,OAAO;AAC3C,WAAK,KAAK,IAAI,aAAK,IAAI,IAAI,IAAI,IAAI,GAAG;AACtC,QAAE,IAAI,KAAK,MAAM,MAAM,MAAM,KAAK,IAAI,IAAI;AAC1C,QAAE,IAAI,KAAK,MAAM,MAAM,MAAM,KAAK,IAAI,IAAI;AAC1C,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,KAAK,UAAU,CAAC,KAAK,OAAO;AAE9C,YAAM,KAAK,IAAI,aAAK,IAAI,IAAI,IAAI,IAAI,OAAO;AAC3C,WAAK,KAAK,IAAI,aAAK,IAAI,IAAI,IAAI,IAAI,GAAG;AACtC,QAAE,IAAI,KAAK,MAAM,MAAM,MAAM,KAAK,IAAI,IAAI;AAC1C,QAAE,IAAI,KAAK,MAAM,MAAM,MAAM,KAAK,IAAI,IAAI;AAC1C,aAAO;AAAA,IACT,OAAO;AAEL,UAAI,KAAK,IAAI,GAAG,IAAI,SAAS,KAAK,IAAI,MAAM,KAAK,IAAI,IAAI,OAAO;AAC9D,UAAE,IAAI,EAAE,IAAI;AACZ,eAAO;AAAA,MACT;AACA,aAAO,gBAAgB,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC;AACtE,aAAO,KAAK;AACZ,QAAE,IAAI,KAAK,MAAM,KAAK,IAAI,IAAI;AAC9B,QAAE,IAAI,KAAK,MAAM,KAAK,IAAI,IAAI;AAC9B,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEO,SAASC,UAAQ,GAAG;AACzB,IAAE,KAAK,KAAK;AACZ,IAAE,KAAK,KAAK;AACZ,MAAI,IAAI,GAAG,MAAM,MAAM,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAKC,IAAG,MAAM,KAAK;AACzE,MAAI,KAAK,QAAQ;AACf,SAAK,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACpC,QAAI,KAAM,IAAI,UAAU,KAAK,GAAI;AAC/B;AAAA,IACF;AACA,QAAI,KAAK,KAAK;AAEd,WAAO,KAAK,IAAI,CAAC;AACjB,WAAO,KAAK,IAAI,CAAC;AAEjB,UAAM,KAAK;AACX,QAAI,KAAK,IAAI,EAAE,KAAK,OAAO;AACzB,YAAM,KAAK;AAAA,IACb,OAAO;AACL,YAAM,cAAM,OAAO,KAAK,UAAW,EAAE,IAAI,OAAO,KAAK,UAAW,EAAE;AAClE,YAAM,KAAK,IAAI,KAAK,IAAI,IAAI;AAC5B,UAAI,KAAK,IAAI,GAAG,KAAK,OAAO;AAC1B,YAAI,KAAK,QAAQ,GAAG;AAClB,gBAAM,mBAAW,KAAK,QAAQ,KAAK,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;AAAA,QACrD,OAAO;AACL,gBAAM,mBAAW,KAAK,QAAQ,KAAK,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAAA,QACrD;AAAA,MACF,OAAO;AACL,cAAM,mBAAW,KAAK,QAAQ,KAAK,MAAM,EAAE,IAAI,MAAM,KAAK,KAAK,UAAU,OAAO,EAAE,IAAI,KAAK,UAAU,IAAI,CAAC;AAAA,MAC5G;AAAA,IACF;AAEA,MAAE,IAAI;AACN,MAAE,IAAI;AACN,WAAO;AAAA,EACT,OAAO;AACL,SAAK,aAAK,KAAK,EAAE;AACjB,SAAK,aAAK,KAAK,EAAE;AACjB,SAAK,aAAK,KAAK,EAAE;AACjB,SAAK,aAAK,KAAK,EAAE;AACjB,QAAI,KAAK,IAAI,KAAK,UAAU,CAAC,KAAK,OAAO;AAEvC,YAAM,KAAK,IAAI,aAAK,IAAI,IAAI,IAAI,IAAI,OAAO;AAC3C,WAAK,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACpC,MAAAA,KAAI,MAAM;AACV,YAAM,cAAMA,KAAI,KAAK,GAAG,IAAI,IAAI,IAAI,EAAE;AACtC,YAAM,mBAAW,KAAK,QAAQ,KAAK,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC;AACvD,QAAE,IAAI;AACN,QAAE,IAAI;AACN,aAAO;AAAA,IACT,WAAW,KAAK,IAAI,KAAK,UAAU,CAAC,KAAK,OAAO;AAE9C,YAAM,KAAK,IAAI,aAAK,IAAI,IAAI,IAAI,IAAI,OAAO;AAC3C,WAAK,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACpC,MAAAA,KAAI,KAAK;AAET,YAAM,cAAMA,KAAI,KAAK,GAAG,IAAI,IAAI,IAAI,EAAE;AACtC,YAAM,mBAAW,KAAK,QAAQ,KAAK,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;AAClD,QAAE,IAAI;AACN,QAAE,IAAI;AACN,aAAO;AAAA,IACT,OAAO;AAEL,aAAO,KAAK,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1B,YAAM,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACrC,aAAO,eAAe,KAAK,MAAM,KAAK,OAAO,MAAM,KAAK,KAAK,GAAG,KAAK,CAAC;AAEtE,QAAE,IAAI,KAAK;AACX,QAAE,IAAI,KAAK;AACX,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEO,IAAIC,UAAQ,CAAC,yBAAyB,MAAM;AACnD,IAAO,eAAQ;AAAA,EACb,MAAMJ;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOE;AACT;;;AC7JO,SAASC,SAAO;AAKrB,OAAK,UAAU,KAAK,IAAI,KAAK,IAAI;AACjC,OAAK,UAAU,KAAK,IAAI,KAAK,IAAI;AACnC;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,GAAG,GAAG;AACV,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AAGZ,SAAO,mBAAW,MAAM,KAAK,KAAK;AAElC,WAAS,KAAK,IAAI,GAAG;AACrB,WAAS,KAAK,IAAI,GAAG;AAErB,WAAS,KAAK,IAAI,IAAI;AACtB,MAAI,KAAK,UAAU,SAAS,KAAK,UAAU,SAAS;AACpD,QAAM;AACN,MAAK,IAAI,KAAO,KAAK,IAAI,CAAC,KAAK,OAAQ;AACrC,QAAI,KAAK,IAAI,MAAM,SAAS,KAAK,IAAI,IAAI;AACzC,QAAI,KAAK,KAAK,KAAK,IAAI,OAAO,KAAK,UAAU,SAAS,KAAK,UAAU,SAAS;AAAA,EAChF;AACA,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,SAASC,UAAQ,GAAG;AACzB,MAAI;AACJ,MAAI;AACJ,MAAI,MAAM;AACV,MAAI;AACJ,MAAI,KAAK;AAGT,IAAE,KAAK,KAAK;AACZ,IAAE,KAAK,KAAK;AACZ,OAAK,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACpC,MAAI,cAAM,KAAK,KAAK,CAAC;AAErB,SAAO,KAAK,IAAI,CAAC;AACjB,SAAO,KAAK,IAAI,CAAC;AAEjB,QAAM,KAAK;AACX,MAAI,KAAK,IAAI,EAAE,KAAK,OAAO;AACzB,UAAM,KAAK;AACX,MAAE,IAAI;AACN,MAAE,IAAI;AACN,WAAO;AAAA,EACT;AACA,QAAM,cAAM,OAAO,KAAK,UAAW,EAAE,IAAI,OAAO,KAAK,UAAW,EAAE;AAClE,QAAM,KAAK,IAAI,KAAK,IAAI,IAAI;AAC5B,MAAI,KAAK,IAAI,GAAG,KAAK,OAAO;AAC1B,QAAI,KAAK,QAAQ,GAAG;AAClB,YAAM,mBAAW,KAAK,QAAQ,KAAK,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;AAAA,IACrD,OAAO;AACL,YAAM,mBAAW,KAAK,QAAQ,KAAK,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAAA,IACrD;AACA,MAAE,IAAI;AACN,MAAE,IAAI;AACN,WAAO;AAAA,EACT;AACA,QAAM,mBAAW,KAAK,QAAQ,KAAK,MAAO,EAAE,IAAI,MAAO,KAAK,KAAK,UAAU,OAAO,EAAE,IAAI,KAAK,UAAU,IAAI,CAAC;AAC5G,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,OAAO;AAC3B,IAAO,gBAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;AChFA,IAAI,YAAY;AAAA,EACd,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AACV;AAEA,IAAI,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AACV;AAGO,SAASC,SAAO;AACrB,OAAK,KAAK,KAAK,MAAM;AACrB,OAAK,KAAK,KAAK,MAAM;AACrB,OAAK,OAAO,KAAK,QAAQ;AACzB,OAAK,QAAQ,KAAK,SAAS;AAC3B,OAAK,SAAS,KAAK,UAAU;AAC7B,OAAK,QAAQ,KAAK,SAAS;AAG3B,MAAI,KAAK,QAAQ,UAAU,SAAS,GAAK;AACvC,SAAK,OAAO,UAAU;AAAA,EACxB,WAAW,KAAK,QAAQ,EAAE,UAAU,SAAS,IAAM;AACjD,SAAK,OAAO,UAAU;AAAA,EACxB,WAAW,KAAK,IAAI,KAAK,KAAK,KAAK,QAAQ;AACzC,SAAK,OAAO,UAAU;AAAA,EACxB,WAAW,KAAK,IAAI,KAAK,KAAK,KAAK,UAAU,QAAQ;AACnD,SAAK,OAAO,KAAK,QAAQ,IAAM,UAAU,QAAQ,UAAU;AAAA,EAC7D,OAAO;AACL,SAAK,OAAO,UAAU;AAAA,EACxB;AAIA,MAAI,KAAK,OAAO,GAAG;AACjB,SAAK,cAAc,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK;AAChD,SAAK,sBAAsB,KAAK,cAAc,KAAK;AAAA,EACrD;AACF;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE;AACtB,MAAI,KAAK;AACT,MAAI,OAAO;AACX,MAAI,GAAG;AAEP,MAAI,OAAO,EAAE,OAAO,EAAE;AAGtB,IAAE,KAAK,KAAK;AAKZ,MAAI,KAAK,OAAO,GAAG;AACjB,UAAM,KAAK,KAAK,KAAK,sBAAsB,KAAK,IAAI,EAAE,CAAC,CAAC;AAAA,EAC1D,OAAO;AACL,UAAM,EAAE;AAAA,EACV;AAOA,QAAM,EAAE;AACR,MAAI,KAAK,SAAS,UAAU,KAAK;AAC/B,UAAM,UAAU;AAChB,QAAI,OAAO,UAAU,OAAO,UAAU,QAAQ;AAC5C,WAAK,QAAQ,UAAU;AACvB,cAAQ,MAAM;AAAA,IAChB,WAAW,MAAM,UAAU,UAAU,OAAO,EAAE,UAAU,SAAS;AAC/D,WAAK,QAAQ,UAAU;AACvB,cAAS,MAAM,IAAM,MAAM,MAAM,MAAM;AAAA,IACzC,WAAW,MAAM,EAAE,UAAU,WAAW,OAAO,CAAC,QAAQ;AACtD,WAAK,QAAQ,UAAU;AACvB,cAAQ,MAAM;AAAA,IAChB,OAAO;AACL,WAAK,QAAQ,UAAU;AACvB,cAAQ;AAAA,IACV;AAAA,EACF,WAAW,KAAK,SAAS,UAAU,QAAQ;AACzC,UAAM,UAAU;AAChB,QAAI,OAAO,UAAU,OAAO,UAAU,QAAQ;AAC5C,WAAK,QAAQ,UAAU;AACvB,cAAQ,CAAC,MAAM;AAAA,IACjB,WAAW,MAAM,UAAU,OAAO,CAAC,QAAQ;AACzC,WAAK,QAAQ,UAAU;AACvB,cAAQ,CAAC;AAAA,IACX,WAAW,MAAM,CAAC,UAAU,OAAO,EAAE,UAAU,SAAS;AACtD,WAAK,QAAQ,UAAU;AACvB,cAAQ,CAAC,MAAM;AAAA,IACjB,OAAO;AACL,WAAK,QAAQ,UAAU;AACvB,cAAS,MAAM,IAAM,CAAC,MAAM,MAAM,CAAC,MAAM;AAAA,IAC3C;AAAA,EACF,OAAO;AACL,QAAI,GAAG,GAAG;AACV,QAAI,QAAQ;AACZ,QAAI,QAAQ;AAEZ,QAAI,KAAK,SAAS,UAAU,OAAO;AACjC,YAAM,qBAAqB,KAAK,CAAC,OAAO;AAAA,IAC1C,WAAW,KAAK,SAAS,UAAU,MAAM;AACvC,YAAM,qBAAqB,KAAK,CAAC,GAAG;AAAA,IACtC,WAAW,KAAK,SAAS,UAAU,MAAM;AACvC,YAAM,qBAAqB,KAAK,CAAC,OAAO;AAAA,IAC1C;AACA,aAAS,KAAK,IAAI,GAAG;AACrB,aAAS,KAAK,IAAI,GAAG;AACrB,aAAS,KAAK,IAAI,GAAG;AACrB,aAAS,KAAK,IAAI,GAAG;AACrB,QAAI,SAAS;AACb,QAAI,SAAS;AACb,QAAI;AAEJ,QAAI,KAAK,SAAS,UAAU,OAAO;AACjC,YAAM,KAAK,KAAK,CAAC;AACjB,cAAQ,yBAAyB,KAAK,GAAG,GAAG,IAAI;AAAA,IAClD,WAAW,KAAK,SAAS,UAAU,OAAO;AACxC,YAAM,KAAK,KAAK,CAAC;AACjB,cAAQ,yBAAyB,KAAK,GAAG,CAAC,GAAG,IAAI;AAAA,IACnD,WAAW,KAAK,SAAS,UAAU,MAAM;AACvC,YAAM,KAAK,KAAK,CAAC,CAAC;AAClB,cAAQ,yBAAyB,KAAK,GAAG,CAAC,GAAG,IAAI;AAAA,IACnD,WAAW,KAAK,SAAS,UAAU,MAAM;AACvC,YAAM,KAAK,KAAK,CAAC,CAAC;AAClB,cAAQ,yBAAyB,KAAK,GAAG,GAAG,IAAI;AAAA,IAClD,OAAO;AAEL,YAAM,QAAQ;AACd,WAAK,QAAQ,UAAU;AAAA,IACzB;AAAA,EACF;AAKA,OAAK,KAAK,KAAM,KAAK,OAAQ,QAAQ,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,IAAI,QAAQ;AAC7F,MAAI,KAAK,MAAM,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,EAAE;AAGlH,MAAI,KAAK,UAAU,UAAU,QAAQ;AACnC,UAAM;AAAA,EACR,WAAW,KAAK,UAAU,UAAU,QAAQ;AAC1C,UAAM;AAAA,EACR,WAAW,KAAK,UAAU,UAAU,QAAQ;AAC1C,UAAM,MAAM;AAAA,EACd;AAGA,KAAG,IAAI,IAAI,KAAK,IAAI,EAAE;AACtB,KAAG,IAAI,IAAI,KAAK,IAAI,EAAE;AACtB,KAAG,IAAI,GAAG,IAAI,KAAK,IAAI,KAAK;AAC5B,KAAG,IAAI,GAAG,IAAI,KAAK,IAAI,KAAK;AAE5B,IAAE,IAAI,GAAG;AACT,IAAE,IAAI,GAAG;AACT,SAAO;AACT;AAIO,SAASC,UAAQ,GAAG;AACzB,MAAI,KAAK,EAAE,KAAK,GAAG,KAAK,EAAE;AAC1B,MAAI,IAAI,IAAI,OAAO;AACnB,MAAI,UAAU,OAAO,QAAQ;AAC7B,MAAI;AACJ,MAAI,OAAO,EAAE,OAAO,EAAE;AAGtB,IAAE,KAAK,EAAE,IAAI,KAAK,MAAM,KAAK;AAC7B,IAAE,KAAK,EAAE,IAAI,KAAK,MAAM,KAAK;AAI7B,OAAK,KAAK,KAAK,KAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAC/C,OAAK,KAAK,MAAM,EAAE,GAAG,EAAE,CAAC;AACxB,MAAI,EAAE,KAAK,KAAO,EAAE,KAAK,KAAK,IAAI,EAAE,CAAC,GAAG;AACtC,SAAK,QAAQ,UAAU;AAAA,EACzB,WAAW,EAAE,KAAK,KAAO,EAAE,KAAK,KAAK,IAAI,EAAE,CAAC,GAAG;AAC7C,SAAK,QAAQ,UAAU;AACvB,UAAM;AAAA,EACR,WAAW,EAAE,IAAI,KAAO,CAAC,EAAE,KAAK,KAAK,IAAI,EAAE,CAAC,GAAG;AAC7C,SAAK,QAAQ,UAAU;AACvB,SAAM,KAAK,IAAM,KAAK,MAAM,KAAK;AAAA,EACnC,OAAO;AACL,SAAK,QAAQ,UAAU;AACvB,UAAM;AAAA,EACR;AAOA,MAAK,MAAM,KAAM,KAAK,IAAI,EAAE;AAC5B,aAAW,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAK,IAAI,KAAK,KAAK,CAAC;AACxD,UAAQ,KAAK,KAAK,QAAQ;AAC1B,UAAQ,KAAK,IAAI,EAAE;AACnB,UAAQ,KAAK,IAAI,EAAE;AACnB,WAAS,IAAI,QAAQ,QAAQ,QAAQ,SAAS,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC;AACzF,MAAI,SAAS,IAAI;AACf,aAAS;AAAA,EACX,WAAW,SAAS,GAAI;AACtB,aAAS;AAAA,EACX;AAMA,MAAI,KAAK,SAAS,UAAU,KAAK;AAC/B,UAAM,KAAK,KAAK,MAAM;AACtB,OAAG,MAAM,UAAU;AACnB,QAAI,KAAK,UAAU,UAAU,QAAQ;AACnC,SAAG,MAAM,QAAQ;AAAA,IACnB,WAAW,KAAK,UAAU,UAAU,QAAQ;AAC1C,SAAG,MAAO,QAAQ,IAAM,QAAQ,MAAM,QAAQ;AAAA,IAChD,WAAW,KAAK,UAAU,UAAU,QAAQ;AAC1C,SAAG,MAAM,QAAQ;AAAA,IACnB,OAA4C;AAC1C,SAAG,MAAM;AAAA,IACX;AAAA,EACF,WAAW,KAAK,SAAS,UAAU,QAAQ;AACzC,UAAM,KAAK,KAAK,MAAM;AACtB,OAAG,MAAM,MAAM;AACf,QAAI,KAAK,UAAU,UAAU,QAAQ;AACnC,SAAG,MAAM,CAAC,QAAQ;AAAA,IACpB,WAAW,KAAK,UAAU,UAAU,QAAQ;AAC1C,SAAG,MAAM,CAAC;AAAA,IACZ,WAAW,KAAK,UAAU,UAAU,QAAQ;AAC1C,SAAG,MAAM,CAAC,QAAQ;AAAA,IACpB,OAA4C;AAC1C,SAAG,MAAO,QAAQ,IAAM,CAAC,QAAQ,MAAM,CAAC,QAAQ;AAAA,IAClD;AAAA,EACF,OAAO;AAEL,QAAI,GAAG,GAAG;AACV,QAAI;AACJ,QAAI,IAAI;AACR,QAAI,KAAK,GAAG;AACV,UAAI;AAAA,IACN,OAAO;AACL,UAAI,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK;AAAA,IACvC;AACA,SAAK,IAAI;AACT,QAAI,KAAK,GAAG;AACV,UAAI;AAAA,IACN,OAAO;AACL,UAAI,KAAK,KAAK,IAAI,CAAC;AAAA,IACrB;AAEA,QAAI,KAAK,UAAU,UAAU,QAAQ;AACnC,UAAI;AACJ,UAAI,CAAC;AACL,UAAI;AAAA,IACN,WAAW,KAAK,UAAU,UAAU,QAAQ;AAC1C,UAAI,CAAC;AACL,UAAI,CAAC;AAAA,IACP,WAAW,KAAK,UAAU,UAAU,QAAQ;AAC1C,UAAI;AACJ,UAAI;AACJ,UAAI,CAAC;AAAA,IACP;AAEA,QAAI,KAAK,SAAS,UAAU,OAAO;AACjC,UAAI;AACJ,UAAI,CAAC;AACL,UAAI;AAAA,IACN,WAAW,KAAK,SAAS,UAAU,MAAM;AACvC,UAAI,CAAC;AACL,UAAI,CAAC;AAAA,IACP,WAAW,KAAK,SAAS,UAAU,MAAM;AACvC,UAAI;AACJ,UAAI;AACJ,UAAI,CAAC;AAAA,IACP;AAEA,OAAG,MAAM,KAAK,KAAK,CAAC,CAAC,IAAI;AACzB,OAAG,MAAM,KAAK,MAAM,GAAG,CAAC;AACxB,QAAI,KAAK,SAAS,UAAU,OAAO;AACjC,SAAG,MAAM,qBAAqB,GAAG,KAAK,CAAC,OAAO;AAAA,IAChD,WAAW,KAAK,SAAS,UAAU,MAAM;AACvC,SAAG,MAAM,qBAAqB,GAAG,KAAK,CAAC,GAAG;AAAA,IAC5C,WAAW,KAAK,SAAS,UAAU,MAAM;AACvC,SAAG,MAAM,qBAAqB,GAAG,KAAK,CAAC,OAAO;AAAA,IAChD;AAAA,EACF;AAIA,MAAI,KAAK,OAAO,GAAG;AACjB,QAAI;AACJ,QAAI,QAAQ;AACZ,kBAAe,GAAG,MAAM,IAAI,IAAI;AAChC,aAAS,KAAK,IAAI,GAAG,GAAG;AACxB,SAAK,KAAK,IAAI,KAAK,KAAK,SAAS,SAAS,KAAK,mBAAmB;AAClE,OAAG,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,KAAK,KAAK,cAAc,GAAG;AACjF,QAAI,aAAa;AACf,SAAG,MAAM,CAAC,GAAG;AAAA,IACf;AAAA,EACF;AAEA,KAAG,OAAO,KAAK;AACf,IAAE,IAAI,GAAG;AACT,IAAE,IAAI,GAAG;AACT,SAAO;AACT;AAIA,SAAS,yBAAyB,KAAK,GAAG,GAAG,MAAM;AACjD,MAAI;AACJ,MAAI,MAAM,OAAO;AACf,SAAK,QAAQ,UAAU;AACvB,YAAQ;AAAA,EACV,OAAO;AACL,YAAQ,KAAK,MAAM,GAAG,CAAC;AACvB,QAAI,KAAK,IAAI,KAAK,KAAK,QAAQ;AAC7B,WAAK,QAAQ,UAAU;AAAA,IACzB,WAAW,QAAQ,UAAU,SAAS,UAAU,QAAQ;AACtD,WAAK,QAAQ,UAAU;AACvB,eAAS;AAAA,IACX,WAAW,QAAQ,UAAU,UAAU,SAAS,EAAE,UAAU,SAAS;AACnE,WAAK,QAAQ,UAAU;AACvB,cAAS,SAAS,IAAM,QAAQ,MAAM,QAAQ;AAAA,IAChD,OAAO;AACL,WAAK,QAAQ,UAAU;AACvB,eAAS;AAAA,IACX;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,qBAAqB,KAAK,QAAQ;AACzC,MAAI,OAAO,MAAM;AACjB,MAAI,OAAO,CAAC,KAAK;AACf,YAAQ;AAAA,EACV,WAAW,OAAO,CAAC,KAAK;AACtB,YAAQ;AAAA,EACV;AACA,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,oCAAoC,oCAAoC,KAAK;AACjG,IAAO,cAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;ACjXA,IAAI,UAAU;AAAA,EACZ,CAAC,GAAQ,WAAY,aAAc,SAAU;AAAA,EAC7C,CAAC,QAAQ,YAAc,WAAa,UAAW;AAAA,EAC/C,CAAC,QAAQ,WAAa,aAAc,WAAY;AAAA,EAChD,CAAC,MAAQ,YAAa,WAAa,SAAU;AAAA,EAC7C,CAAC,QAAQ,YAAa,aAAc,WAAY;AAAA,EAChD,CAAC,OAAQ,YAAa,aAAc,SAAU;AAAA,EAC9C,CAAC,MAAQ,YAAa,aAAc,UAAW;AAAA,EAC/C,CAAC,QAAQ,YAAa,aAAc,UAAW;AAAA,EAC/C,CAAC,QAAQ,YAAa,WAAa,UAAW;AAAA,EAC9C,CAAC,QAAQ,YAAa,aAAc,WAAY;AAAA,EAChD,CAAC,QAAQ,YAAa,YAAc,UAAW;AAAA,EAC/C,CAAC,OAAQ,YAAa,aAAc,UAAW;AAAA,EAC/C,CAAC,QAAQ,YAAa,aAAc,UAAW;AAAA,EAC/C,CAAC,QAAQ,YAAa,WAAa,WAAY;AAAA,EAC/C,CAAC,QAAQ,YAAa,aAAc,UAAW;AAAA,EAC/C,CAAC,QAAQ,YAAa,YAAc,UAAW;AAAA,EAC/C,CAAC,QAAQ,WAAW,YAAa,UAAW;AAAA,EAC5C,CAAC,QAAQ,YAAa,QAAU,UAAW;AAAA,EAC3C,CAAC,QAAQ,YAAa,WAAa,UAAW;AAChD;AAEA,IAAI,UAAU;AAAA,EACZ,CAAC,aAAc,QAAQ,YAAa,WAAY;AAAA,EAChD,CAAC,OAAQ,QAAQ,aAAc,UAAW;AAAA,EAC1C,CAAC,OAAQ,QAAQ,YAAa,WAAY;AAAA,EAC1C,CAAC,OAAQ,WAAW,aAAc,UAAW;AAAA,EAC7C,CAAC,OAAQ,WAAW,YAAa,QAAS;AAAA,EAC1C,CAAC,MAAQ,WAAW,aAAc,UAAW;AAAA,EAC7C,CAAC,OAAQ,WAAW,YAAa,WAAY;AAAA,EAC7C,CAAC,OAAQ,WAAW,aAAc,WAAY;AAAA,EAC9C,CAAC,QAAQ,WAAW,aAAc,WAAY;AAAA,EAC9C,CAAC,QAAQ,WAAW,aAAc,WAAY;AAAA,EAC9C,CAAC,QAAQ,WAAW,aAAc,WAAY;AAAA,EAC9C,CAAC,QAAQ,UAAU,aAAc,WAAY;AAAA,EAC7C,CAAC,QAAQ,WAAW,aAAc,WAAY;AAAA,EAC9C,CAAC,QAAQ,WAAW,aAAc,WAAY;AAAA,EAC9C,CAAC,QAAQ,WAAW,WAAa,WAAY;AAAA,EAC7C,CAAC,QAAQ,WAAY,WAAa,QAAU;AAAA,EAC5C,CAAC,QAAQ,WAAY,YAAc,UAAW;AAAA,EAC9C,CAAC,QAAQ,WAAY,SAAW,UAAW;AAAA,EAC3C,CAAC,GAAQ,WAAY,YAAc,UAAW;AAChD;AAEA,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,KAAK,MAAM;AACf,IAAI,MAAM,IAAI;AACd,IAAI,QAAQ;AAEZ,IAAI,YAAY,SAAU,OAAO,GAAG;AAClC,SAAO,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC;AAC/D;AAEA,IAAI,YAAY,SAAU,OAAO,GAAG;AAClC,SAAO,MAAM,CAAC,IAAI,KAAK,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC;AACvD;AAEA,SAAS,eAAe,MAAMC,QAAO,SAAS,OAAO;AACnD,MAAI,IAAIA;AACR,SAAO,OAAO,EAAE,OAAO;AACrB,QAAI,MAAM,KAAK,CAAC;AAChB,SAAK;AACL,QAAI,KAAK,IAAI,GAAG,IAAI,SAAS;AAC3B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEO,SAASC,SAAO;AACrB,OAAK,KAAK,KAAK,MAAM;AACrB,OAAK,KAAK,KAAK,MAAM;AACrB,OAAK,QAAQ,KAAK,SAAS;AAC3B,OAAK,KAAK;AACV,OAAK,QAAQ,KAAK,SAAS;AAC7B;AAEO,SAASC,UAAQ,IAAI;AAC1B,MAAI,MAAM,mBAAW,GAAG,IAAI,KAAK,KAAK;AAEtC,MAAI,OAAO,KAAK,IAAI,GAAG,CAAC;AACxB,MAAI,IAAI,KAAK,MAAM,OAAO,EAAE;AAC5B,MAAI,IAAI,GAAG;AACT,QAAI;AAAA,EACN,WAAW,KAAK,OAAO;AACrB,QAAI,QAAQ;AAAA,EACd;AACA,SAAO,OAAO,OAAO,MAAM;AAC3B,MAAI,KAAK;AAAA,IACP,GAAG,UAAU,QAAQ,CAAC,GAAG,IAAI,IAAI;AAAA,IACjC,GAAG,UAAU,QAAQ,CAAC,GAAG,IAAI;AAAA,EAC/B;AACA,MAAI,GAAG,IAAI,GAAG;AACZ,OAAG,IAAI,CAAC,GAAG;AAAA,EACb;AAEA,KAAG,IAAI,GAAG,IAAI,KAAK,IAAI,MAAM,KAAK;AAClC,KAAG,IAAI,GAAG,IAAI,KAAK,IAAI,MAAM,KAAK;AAClC,SAAO;AACT;AAEO,SAASC,UAAQ,IAAI;AAC1B,MAAI,KAAK;AAAA,IACP,IAAI,GAAG,IAAI,KAAK,OAAO,KAAK,IAAI;AAAA,IAChC,GAAG,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE,KAAK,KAAK,IAAI;AAAA,EAC1C;AAEA,MAAI,GAAG,KAAK,GAAG;AACb,OAAG,KAAK,QAAQ,KAAK,EAAE,CAAC;AACxB,OAAG,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU;AAAA,EAC/B,OAAO;AAEL,QAAI,IAAI,KAAK,MAAM,GAAG,IAAI,KAAK;AAC/B,QAAI,IAAI,GAAG;AACT,UAAI;AAAA,IACN,WAAW,KAAK,OAAO;AACrB,UAAI,QAAQ;AAAA,IACd;AACA,eAAS;AACP,UAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,GAAG,GAAG;AACxB,UAAE;AAAA,MACJ,WAAW,QAAQ,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,GAAG;AACpC,UAAE;AAAA,MACJ,OAAO;AACL;AAAA,MACF;AAAA,IACF;AAEA,QAAI,QAAQ,QAAQ,CAAC;AACrB,QAAI,IAAI,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC;AAE5D,QAAI,eAAe,SAAU,GAAG;AAC9B,cAAQ,UAAU,OAAO,CAAC,IAAI,GAAG,KAAK,UAAU,OAAO,CAAC;AAAA,IAC1D,GAAG,GAAG,OAAO,GAAG;AAEhB,OAAG,KAAK,UAAU,QAAQ,CAAC,GAAG,CAAC;AAC/B,OAAG,KAAK,IAAI,IAAI,KAAK;AACrB,QAAI,GAAG,IAAI,GAAG;AACZ,SAAG,IAAI,CAAC,GAAG;AAAA,IACb;AAAA,EACF;AAEA,KAAG,IAAI,mBAAW,GAAG,IAAI,KAAK,KAAK;AACnC,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,YAAY,OAAO;AACvC,IAAO,gBAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;AC3JO,SAASC,SAAO;AACrB,OAAK,OAAO;AACd;AAEO,SAASC,UAAQ,GAAG;AACzB,MAAI,QAAQ,qBAAqB,GAAG,KAAK,IAAI,KAAK,CAAC;AACnD,SAAO;AACT;AAEO,SAASC,UAAQ,GAAG;AACzB,MAAI,QAAQ,qBAAqB,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,CAAC;AAC3D,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,cAAc,cAAc,WAAW,SAAS;AACpE,IAAO,kBAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;ACFA,IAAI,OAAO;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AACT;AAEA,IAAI,SAAS;AAAA,EACX,GAAG,EAAE,KAAK,KAAQ,KAAK,KAAK;AAAA;AAAA,EAC5B,KAAK,EAAE,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK;AAAA;AAAA,EACxC,MAAM,EAAE,KAAK,GAAG,KAAK,MAAM,SAAS,KAAK;AAAA;AAAA,EACzC,OAAO,EAAE,KAAK,GAAG,KAAK,KAAK;AAAA;AAAA,EAC3B,MAAM,EAAE,KAAK,GAAG,KAAK,KAAK;AAAA;AAC5B;AAGO,SAASC,SAAO;AACrB,SAAO,KAAK,MAAM,EAAE,SAAQ,SAAU,GAAG;AACvC,QAAI,OAAO,KAAK,CAAC,MAAM,aAAa;AAClC,WAAK,CAAC,IAAI,OAAO,CAAC,EAAE;AAAA,IACtB,WAAW,OAAO,CAAC,EAAE,OAAO,MAAM,KAAK,CAAC,CAAC,GAAG;AAC1C,YAAM,IAAI,MAAM,8CAA8C,IAAI,QAAQ,KAAK,CAAC,CAAC;AAAA,IACnF,WAAW,OAAO,CAAC,EAAE,KAAK;AACxB,WAAK,CAAC,IAAI,WAAW,KAAK,CAAC,CAAC;AAAA,IAC9B;AACA,QAAI,OAAO,CAAC,EAAE,SAAS;AACrB,WAAK,CAAC,IAAI,KAAK,CAAC,IAAI;AAAA,IACtB;AAAA,EACF,GAAE,KAAK,IAAI,CAAC;AAEZ,MAAI,KAAK,IAAK,KAAK,IAAI,KAAK,IAAI,IAAI,OAAQ,IAAI,OAAO;AACrD,SAAK,OAAO,KAAK,OAAO,IAAI,KAAK,SAAS,KAAK;AAAA,EACjD,WAAW,KAAK,IAAI,KAAK,IAAI,IAAI,OAAO;AACtC,SAAK,OAAO,KAAK;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,KAAK;AACjB,SAAK,SAAS,KAAK,IAAI,KAAK,IAAI;AAChC,SAAK,SAAS,KAAK,IAAI,KAAK,IAAI;AAAA,EAClC;AAEA,OAAK,MAAM,KAAK,IAAI,KAAK;AAEzB,MAAI,KAAK,OAAO,KAAK,KAAK,MAAM,MAAM;AACpC,UAAM,IAAI,MAAM,gBAAgB;AAAA,EAClC;AAEA,OAAK,IAAI,IAAI,KAAK;AAClB,OAAK,KAAK,IAAI,KAAK;AACnB,OAAK,KAAK,IAAI,KAAK;AACnB,OAAK,SAAS,KAAK,IAAI,KAAK,KAAK;AACjC,OAAK,KAAK;AAEV,MAAI,QAAQ,KAAK;AACjB,MAAI,QAAQ,KAAK;AACjB,OAAK,KAAK,KAAK,IAAI,KAAK;AACxB,OAAK,KAAK,KAAK,IAAI,KAAK;AACxB,OAAK,KAAK,KAAK,IAAI,KAAK;AACxB,OAAK,KAAK,KAAK,IAAI,KAAK;AAC1B;AAEO,SAASC,UAAQ,GAAG;AACzB,IAAE,KAAK,KAAK;AACZ,MAAI,SAAS,KAAK,IAAI,EAAE,CAAC;AACzB,MAAI,SAAS,KAAK,IAAI,EAAE,CAAC;AACzB,MAAI,SAAS,KAAK,IAAI,EAAE,CAAC;AACzB,MAAI,GAAG;AACP,UAAQ,KAAK,MAAM;AAAA,IACjB,KAAK,KAAK;AACR,UAAI,KAAK,SAAS,SAAS,KAAK,SAAS,SAAS;AAClD;AAAA,IACF,KAAK,KAAK;AACR,UAAI,SAAS;AACb;AAAA,IACF,KAAK,KAAK;AACR,UAAI,CAAC;AACL;AAAA,IACF,KAAK,KAAK;AACR,UAAI;AACJ;AAAA,EACJ;AACA,MAAI,KAAK,OAAO,KAAK,IAAI;AACzB,MAAI,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;AAE7B,UAAQ,KAAK,MAAM;AAAA,IACjB,KAAK,KAAK;AACR,WAAK,KAAK,SAAS,SAAS,KAAK,SAAS,SAAS;AACnD;AAAA,IACF,KAAK,KAAK;AACR,WAAK;AACL;AAAA,IACF,KAAK,KAAK;AACR,WAAK,EAAE,SAAS;AAChB;AAAA,IACF,KAAK,KAAK;AACR,WAAK,SAAS;AACd;AAAA,EACJ;AAGA,MAAI,IAAI;AACR,OAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAC5B,OAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AACxC,OAAK,IAAI,KAAK,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK;AAC5C,MAAI,KAAK;AAET,IAAE,IAAI,IAAI,KAAK;AACf,IAAE,IAAI,IAAI,KAAK;AACf,SAAO;AACT;AAEO,SAASC,UAAQ,GAAG;AACzB,IAAE,KAAK,KAAK;AACZ,IAAE,KAAK,KAAK;AACZ,MAAI,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE;AAGzB,MAAI,IAAI,IAAI;AACZ,OAAK,KAAK,KAAK,MAAM,EAAE,IAAI,KAAK;AAChC,OAAK,KAAK,MAAM,EAAE,IAAI;AACtB,OAAK,KAAK,MAAM,EAAE,IAAI,KAAK,KAAK;AAChC,IAAE,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAC/B,IAAE,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAE/B,MAAI,KAAK,cAAM,EAAE,GAAG,EAAE,CAAC;AACvB,MAAI,KAAK,IAAI,EAAE,IAAI,OAAO;AACxB,MAAE,IAAI;AACN,MAAE,IAAI,EAAE;AAAA,EACV,OAAO;AACL,QAAI,MAAM;AACV,WAAO,IAAI,KAAK,KAAK,KAAK;AAC1B,YAAQ,KAAK,IAAI,KAAK,KAAK,IAAI,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK;AAC/D,WAAO,KAAK,KAAK,IAAI,OAAO,IAAI;AAChC,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK,KAAK;AACR,UAAE,IAAI,KAAK,KAAK,OAAO,KAAK,SAAS,EAAE,IAAI,OAAO,KAAK,SAAS,EAAE;AAClE,UAAE,KAAK,OAAO,KAAK,SAAS,KAAK,IAAI,EAAE,CAAC,KAAK;AAC7C,UAAE,KAAK,OAAO,KAAK;AACnB;AAAA,MACF,KAAK,KAAK;AACR,UAAE,IAAI,KAAK,KAAK,EAAE,IAAI,OAAO,EAAE;AAC/B,UAAE,IAAI,OAAO;AACb,UAAE,KAAK;AACP;AAAA,MACF,KAAK,KAAK;AACR,UAAE,IAAI,KAAK,KAAK,IAAI;AACpB,UAAE,IAAI,CAAC,EAAE;AACT;AAAA,MACF,KAAK,KAAK;AACR,UAAE,IAAI,CAAC,KAAK,KAAK,IAAI;AACrB;AAAA,IACJ;AACA,MAAE,IAAI,KAAK,MAAM,EAAE,GAAG,EAAE,CAAC;AAAA,EAC3B;AAEA,IAAE,IAAI,EAAE,IAAI,KAAK;AACjB,IAAE,IAAI,EAAE;AACR,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,sBAAsB,OAAO;AACjD,IAAO,gBAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;AC1KO,SAASC,SAAO;AACrB,OAAK,YAAa,KAAK,UAAU,MAAM,IAAI;AAC3C,OAAK,IAAI,OAAO,KAAK,CAAC;AACtB,OAAK,aAAa,KAAK,IAAI,KAAK;AAEhC,MAAI,KAAK,cAAc,KAAK,KAAK,aAAa,MAAM;AAClD,UAAM,IAAI,MAAM;AAAA,EAClB;AAEA,OAAK,WAAW,IAAM,KAAK;AAC3B,OAAK,IAAI,KAAK,WAAW,KAAK,WAAW;AAEzC,MAAI,KAAK,OAAO,GAAK;AACnB,QAAI,SAAS,IAAM,KAAK;AACxB,QAAI,UAAU,IAAI;AAElB,SAAK,WAAW,KAAK,KAAK,MAAM;AAChC,SAAK,YAAY;AACjB,SAAK,gBAAgB;AAErB,SAAK,QAAQ;AAAA,EACf,OAAO;AACL,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,gBAAgB;AAErB,SAAK,QAAQ;AAAA,EACf;AAEA,MAAI,CAAC,KAAK,OAAO;AACf,SAAK,QAAQ;AAAA,EACf;AACF;AAEA,SAASC,UAAQ,GAAG;AAClB,MAAI,MAAM,EAAE;AACZ,MAAI,MAAM,EAAE;AACZ,MAAI,KAAK,KAAK,KAAK;AACnB,QAAM,MAAM,KAAK;AAEjB,MAAI,KAAK,UAAU,WAAW;AAC5B,UAAM,KAAK,KAAK,KAAK,YAAY,KAAK,IAAI,GAAG,CAAC;AAC9C,QAAI,IAAI,KAAK,WAAW,cAAM,KAAK,WAAW,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,GAAG,CAAC;AAE1E,UAAM,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;AACtC,UAAM,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;AACtC,UAAM,IAAI,KAAK,IAAI,GAAG;AAEtB,SAAM,KAAK,WAAW,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK,gBAAiB,GAAK;AACpF,QAAE,IAAI,OAAO;AACb,QAAE,IAAI,OAAO;AACb,aAAO;AAAA,IACT;AAEA,UAAM,KAAK,WAAW;AACtB,QAAI,KAAK,WAAW;AAClB,QAAE,IAAI,KAAK,aAAa,KAAK,KAAK,MAAM,cAAM,KAAK,GAAG,CAAC;AACvD,QAAE,IAAI,KAAK,aAAa,KAAK,KAAK,MAAM,GAAG;AAAA,IAC7C,OAAO;AACL,QAAE,IAAI,KAAK,aAAa,KAAK,KAAK,MAAM,GAAG;AAC3C,QAAE,IAAI,KAAK,aAAa,KAAK,KAAK,MAAM,cAAM,KAAK,GAAG,CAAC;AAAA,IACzD;AAAA,EACF,WAAW,KAAK,UAAU,UAAU;AAClC,UAAM,KAAK,IAAI,GAAG;AAClB,UAAM,KAAK,IAAI,GAAG,IAAI;AACtB,UAAM,KAAK,IAAI,GAAG,IAAI;AACtB,UAAM,KAAK,IAAI,GAAG;AAClB,UAAM,KAAK,WAAW;AAEtB,QAAI,KAAK,WAAW;AAClB,QAAE,IAAI,KAAK,aAAa,KAAK,KAAK,MAAM,cAAM,KAAK,GAAG,CAAC;AACvD,QAAE,IAAI,KAAK,aAAa,KAAK,KAAK,MAAM,GAAG;AAAA,IAC7C,OAAO;AACL,QAAE,IAAI,KAAK,aAAa,KAAK,KAAK,MAAM,GAAG;AAC3C,QAAE,IAAI,KAAK,aAAa,KAAK,KAAK,MAAM,cAAM,KAAK,GAAG,CAAC;AAAA,IACzD;AAAA,EACF;AACA,IAAE,IAAI,EAAE,IAAI,KAAK;AACjB,IAAE,IAAI,EAAE,IAAI,KAAK;AACjB,SAAO;AACT;AAEA,SAASC,UAAQ,GAAG;AAClB,MAAI,MAAM;AACV,MAAI,MAAM;AACV,MAAI,MAAM;AACV,MAAI,GAAG,GAAG,KAAK;AAEf,IAAE,IAAI,EAAE,IAAI,KAAK;AACjB,IAAE,IAAI,EAAE,IAAI,KAAK;AAEjB,MAAI,KAAK,UAAU,WAAW;AAC5B,QAAI,KAAK,WAAW;AAClB,YAAM,KAAK,IAAI,EAAE,IAAI,KAAK,UAAU;AACpC,YAAM,KAAK,IAAI,EAAE,IAAI,KAAK,UAAU,IAAI,cAAM,GAAK,GAAG;AAAA,IACxD,OAAO;AACL,YAAM,KAAK,IAAI,EAAE,IAAI,KAAK,UAAU;AACpC,YAAM,KAAK,IAAI,EAAE,IAAI,KAAK,UAAU,IAAI,cAAM,GAAK,GAAG;AAAA,IACxD;AAEA,QAAI,OAAO,MAAM,KAAK;AACtB,QAAI,MAAM,MAAM,OAAO,OAAO,MAAM;AACpC,QAAI,IAAI,KAAK,WAAW;AACxB,UAAO,IAAI,IAAK,IAAI,IAAI,KAAK;AAE7B,QAAI,MAAM,GAAK;AACb,QAAE,IAAI,OAAO;AACb,QAAE,IAAI,OAAO;AACb,aAAO;AAAA,IACT;AAEA,SAAK,CAAC,IAAI,KAAK,KAAK,GAAG,MAAM,IAAM;AACnC,UAAM,KAAK,WAAW,IAAI;AAC1B,WAAO;AACP,WAAO;AAEP,MAAE,IAAI,KAAK,MAAM,KAAK,GAAG;AACzB,MAAE,IAAI,KAAK,KAAK,MAAM,KAAK,IAAI,EAAE,CAAC,IAAI,GAAG;AACzC,MAAE,IAAI,KAAK,KAAK,KAAK,gBAAgB,KAAK,IAAI,EAAE,CAAC,CAAC;AAAA,EACpD,WAAW,KAAK,UAAU,UAAU;AAClC,QAAI,KAAK,WAAW;AAClB,YAAM,KAAK,IAAI,EAAE,IAAI,KAAK,UAAU;AACpC,YAAM,KAAK,IAAI,EAAE,IAAI,KAAK,UAAU,IAAI,KAAK,KAAK,IAAM,MAAM,GAAG;AAAA,IACnE,OAAO;AACL,YAAM,KAAK,IAAI,EAAE,IAAI,KAAK,UAAU;AACpC,YAAM,KAAK,IAAI,EAAE,IAAI,KAAK,UAAU,IAAI,KAAK,KAAK,IAAM,MAAM,GAAG;AAAA,IACnE;AAEA,QAAI,MAAM,MAAM,MAAM,MAAM,MAAM;AAClC,QAAI,IAAI,KAAK,WAAW;AACxB,UAAO,IAAI,IAAK,IAAI,IAAI,KAAK;AAC7B,QAAI,MAAM,GAAK;AACb,QAAE,IAAI,OAAO;AACb,QAAE,IAAI,OAAO;AACb,aAAO;AAAA,IACT;AAEA,SAAK,CAAC,IAAI,KAAK,KAAK,GAAG,MAAM,IAAM;AACnC,UAAM,KAAK,WAAW,IAAI;AAC1B,WAAO;AACP,WAAO;AAEP,MAAE,IAAI,KAAK,MAAM,KAAK,GAAG;AACzB,MAAE,IAAI,KAAK,KAAK,MAAM,KAAK,IAAI,EAAE,CAAC,IAAI,GAAG;AAAA,EAC3C;AACA,IAAE,IAAI,EAAE,IAAI,KAAK;AACjB,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,gCAAgC,2BAA2B,MAAM;AACrF,IAAO,eAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;AC9IA,IAAI,KAAK;AAAT,IACE,KAAK;AADP,IAEE,KAAK;AAFP,IAGE,KAAK;AAHP,IAIE,IAAI,KAAK,KAAK,CAAC,IAAI;AAEd,SAASC,SAAO;AACrB,OAAK,KAAK;AACV,OAAK,QAAQ,KAAK,UAAU,SAAY,KAAK,QAAQ;AACvD;AAEO,SAASC,UAAQ,GAAG;AACzB,MAAI,MAAM,mBAAW,EAAE,IAAI,KAAK,KAAK;AACrC,MAAI,MAAM,EAAE;AACZ,MAAI,WAAW,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GACxC,aAAa,WAAW,UACxB,eAAe,aAAa,aAAa;AAC3C,IAAE,IAAI,MAAM,KAAK,IAAI,QAAQ,KACxB,KAAK,KAAK,IAAI,KAAK,aAAa,gBAAgB,IAAI,KAAK,IAAI,KAAK;AACvE,IAAE,IAAI,YAAY,KAAK,KAAK,aAAa,gBAAgB,KAAK,KAAK;AAEnE,IAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK;AAC1B,IAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK;AAC1B,SAAO;AACT;AAEO,SAASC,UAAQ,GAAG;AACzB,IAAE,KAAK,EAAE,IAAI,KAAK,MAAM,KAAK;AAC7B,IAAE,KAAK,EAAE,IAAI,KAAK,MAAM,KAAK;AAE7B,MAAI,MAAM,MACR,QAAQ,IACR,WAAW,EAAE,GACb,YAAY,cAAc,IAAI,KAAK,MAAM;AAE3C,OAAK,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC1B,iBAAa,WAAW;AACxB,mBAAe,aAAa,aAAa;AACzC,SAAK,YAAY,KAAK,KAAK,aAAa,gBAAgB,KAAK,KAAK,eAAe,EAAE;AACnF,UAAM,KAAK,IAAI,KAAK,aAAa,gBAAgB,IAAI,KAAK,IAAI,KAAK;AACnE,gBAAY,OAAO,KAAK;AACxB,QAAI,KAAK,IAAI,IAAI,IAAI,KAAK;AACxB;AAAA,IACF;AAAA,EACF;AACA,eAAa,WAAW;AACxB,iBAAe,aAAa,aAAa;AACzC,IAAE,IAAI,IAAI,EAAE,KAAK,KAAK,IAAI,KAAK,aAAa,gBAAgB,IAAI,KAAK,IAAI,KAAK,eAC1E,KAAK,IAAI,QAAQ;AACrB,IAAE,IAAI,KAAK,KAAK,KAAK,IAAI,QAAQ,IAAI,CAAC;AAEtC,IAAE,IAAI,mBAAW,EAAE,IAAI,KAAK,KAAK;AACjC,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,WAAW,eAAe,aAAa;AAC3D,IAAO,kBAAQ;AAAA,EACb,MAAMH;AAAA,EACN,SAASC;AAAA,EACT,SAASC;AAAA,EACT,OAAOC;AACT;;;AC1EA,IAAI,QAAQ;AAGL,SAASC,SAAO;AACrB,MAAI;AAEJ,OAAK,OAAO,KAAK;AACjB,MAAI,KAAK,IAAI,KAAK,IAAI,IAAI,OAAO;AAC/B,UAAM,IAAI,MAAM;AAAA,EAClB;AACA,MAAI,KAAK,IAAI;AACX,SAAK,KAAK,gBAAQ,KAAK,EAAE;AACzB,SAAK,KAAK;AAAA,MAAQ,KAAK;AAAA,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AAAA,MACxD,IAAI,KAAK,IAAI,KAAK,IAAI;AAAA,MAAG,KAAK;AAAA,IAAE;AAClC,SAAK,MAAM,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK;AACpE,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB,OAAO;AACL,QAAI,KAAK,IAAI,KAAK,IAAI,IAAI,SAAS,SAAS;AAC1C,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,WAAK,QAAQ,IAAI,KAAK,IAAI,KAAK,IAAI;AAAA,IACrC;AACA,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AACF;AAEA,SAAS,MAAM,GAAG;AAChB,MAAI,MAAM,mBAAW,EAAE,KAAK,KAAK,SAAS,EAAE;AAC5C,MAAI,MAAM,EAAE;AACZ,MAAI,IAAI,GAAG;AACX,OAAK,KAAK,MAAM,KAAK,KAAK,gBAAQ,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,EAAE;AACpF,MAAI,IAAI,OAAO,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC;AACjD,IAAE,IAAI,KAAK,KAAK,IAAI,CAAC;AACrB,IAAE,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC;AAEhC,IAAE,IAAI,KAAK,IAAI,EAAE,KAAK,KAAK,MAAM;AACjC,IAAE,IAAI,KAAK,IAAI,EAAE,KAAK,KAAK,MAAM;AACjC,SAAO;AACT;AAEA,SAAS,MAAM,GAAG;AAChB,IAAE,KAAK,EAAE,KAAK,KAAK,MAAM,MAAM,KAAK;AACpC,IAAE,KAAK,EAAE,KAAK,KAAK,MAAM,MAAM,KAAK;AAEpC,MAAI,GAAG,IAAI,KAAK;AAChB,OAAK,cAAM,EAAE,GAAG,EAAE,IAAI,KAAK,MAAM,EAAE,CAAC;AACpC,QAAM,oBAAY,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAC3D,OAAK,IAAI,KAAK,IAAI,GAAG,KAAK,SAAS;AACjC,QAAI,KAAK,IAAI,GAAG;AAChB,UAAM,KAAK,KAAK,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG;AAAA,EACjF,WAAW,KAAK,IAAI,IAAI,OAAO,KAAK,OAAO;AACzC,UAAM;AAAA,EACR,OAAO;AACL,UAAM,IAAI,MAAM;AAAA,EAClB;AACA,IAAE,IAAI,mBAAW,OAAO,KAAK,SAAS,EAAE;AACxC,IAAE,IAAI,mBAAW,GAAG;AACpB,SAAO;AACT;AAEA,SAAS,MAAM,GAAG;AAChB,MAAI,MAAM,mBAAW,EAAE,KAAK,KAAK,SAAS,EAAE;AAC5C,MAAI,MAAM,EAAE;AACZ,MAAI,GAAG;AACP,OAAK,KAAK,QAAQ,KAAK,OAAO;AAC9B,MAAI,KAAK,IAAI,EAAE,IAAI,OAAO;AACxB,MAAE,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,GAAG,IAAI,EAAE;AAChD,MAAE,IAAI,KAAK,QAAQ,KAAK,KAAK,IAAI,CAAC;AAAA,EACpC,OAAO;AACL,MAAE,IAAI,EAAE,IAAI;AAAA,EACd;AAEA,IAAE,IAAI,KAAK,IAAI,EAAE,KAAK,KAAK,MAAM;AACjC,IAAE,IAAI,KAAK,IAAI,EAAE,KAAK,KAAK,MAAM;AACjC,SAAO;AACT;AAEA,SAAS,MAAM,GAAG;AAChB,IAAE,KAAK,EAAE,KAAK,KAAK,MAAM,MAAM,KAAK;AACpC,IAAE,KAAK,EAAE,KAAK,KAAK,MAAM,MAAM,KAAK;AAEpC,MAAI,KAAK;AACT,MAAI,KAAK,cAAM,EAAE,GAAG,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;AAC1C,QAAM,KAAK,QAAQ,KAAK,OAAO;AAC/B,MAAI,KAAK,IAAI,GAAG,IAAI,SAAS;AAC3B,UAAM,IAAI,MAAM;AAAA,EAClB;AACA,MAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,KAAK,OAAO;AAC9C,UAAM;AAAA,EACR,OAAO;AACL,UAAM,KAAK,KAAK,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,KAAK,IAAI,GAAG;AAAA,EAChD;AACA,IAAE,IAAI,mBAAW,OAAO,KAAK,SAAS,EAAE;AACxC,IAAE,IAAI,mBAAW,GAAG;AACpB,SAAO;AACT;AAEO,IAAIC,UAAQ,CAAC,SAAS,yBAAyB;AACtD,IAAO,gBAAQ;AAAA,EACb,MAAMD;AAAA,EACN,OAAOC;AACT;;;AC1Fe,SAAR,cAAkBC,QAAO;AAC9B,EAAAA,OAAM,KAAK,YAAY,IAAI,aAAK;AAChC,EAAAA,OAAM,KAAK,YAAY,IAAI,cAAM;AACjC,EAAAA,OAAM,KAAK,YAAY,IAAI,WAAG;AAC9B,EAAAA,OAAM,KAAK,YAAY,IAAI,cAAM;AACjC,EAAAA,OAAM,KAAK,YAAY,IAAI,aAAK;AAChC,EAAAA,OAAM,KAAK,YAAY,IAAI,cAAM;AACjC,EAAAA,OAAM,KAAK,YAAY,IAAI,aAAK;AAChC,EAAAA,OAAM,KAAK,YAAY,IAAI,WAAG;AAC9B,EAAAA,OAAM,KAAK,YAAY,IAAI,cAAM;AACjC,EAAAA,OAAM,KAAK,YAAY,IAAI,YAAI;AAC/B,EAAAA,OAAM,KAAK,YAAY,IAAI,YAAI;AAC/B,EAAAA,OAAM,KAAK,YAAY,IAAI,WAAG;AAC9B,EAAAA,OAAM,KAAK,YAAY,IAAI,YAAI;AAC/B,EAAAA,OAAM,KAAK,YAAY,IAAI,WAAG;AAC9B,EAAAA,OAAM,KAAK,YAAY,IAAI,WAAG;AAC9B,EAAAA,OAAM,KAAK,YAAY,IAAI,YAAI;AAC/B,EAAAA,OAAM,KAAK,YAAY,IAAI,YAAI;AAC/B,EAAAA,OAAM,KAAK,YAAY,IAAI,YAAI;AAC/B,EAAAA,OAAM,KAAK,YAAY,IAAI,YAAI;AAC/B,EAAAA,OAAM,KAAK,YAAY,IAAI,YAAI;AAC/B,EAAAA,OAAM,KAAK,YAAY,IAAI,YAAI;AAC/B,EAAAA,OAAM,KAAK,YAAY,IAAI,aAAK;AAChC,EAAAA,OAAM,KAAK,YAAY,IAAI,YAAI;AAC/B,EAAAA,OAAM,KAAK,YAAY,IAAI,aAAK;AAChC,EAAAA,OAAM,KAAK,YAAY,IAAI,WAAG;AAC9B,EAAAA,OAAM,KAAK,YAAY,IAAI,aAAK;AAChC,EAAAA,OAAM,KAAK,YAAY,IAAI,eAAO;AAClC,EAAAA,OAAM,KAAK,YAAY,IAAI,aAAK;AAChC,EAAAA,OAAM,KAAK,YAAY,IAAI,YAAI;AAC/B,EAAAA,OAAM,KAAK,YAAY,IAAI,eAAO;AAClC,EAAAA,OAAM,KAAK,YAAY,IAAI,aAAK;AAClC;;;AC1CA,IAAMC,SAAQ,OAAO,OAAO,cAAM;AAAA,EAChC,cAAc;AAAA,EACd;AAAA,EACA,OAAO,IAAI,aAAK,OAAO;AAAA,EACvB;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AACX,CAAC;AACD,cAAoBA,MAAK;AACzB,IAAO,cAAQA;", "names": ["defs", "params", "unit", "D2R", "params", "init", "names", "init", "names", "names", "sphere", "datum", "nadgrid", "start", "Z", "inverse", "wgs84", "forward", "inverse", "inverse", "A", "M", "C1", "forward", "init", "forward", "inverse", "names", "init", "Z", "forward", "inverse", "names", "init", "names", "MAX_ITER", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "A", "inverse", "names", "init", "forward", "I", "inverse", "names", "init", "forward", "V", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "MAX_ITER", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "MAX_ITER", "init", "forward", "V", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "A", "init", "forward", "inverse", "M", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "start", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "forward", "inverse", "names", "init", "names", "proj4", "proj4"]}