import { EventBus } from './EventBus';

let isFullScreen = false;

class InputEvent extends EventBus {
  constructor() {
    super();
    this.keyCodes = [];
    document.addEventListener('keydown', (e) => {
      if (this.keyCodes.includes(e.keyCode)) {
        this.emit('keydown.' + e.keyCode);
      }
    });
    window.addEventListener('resize', (e) => {
      this.emit('resize', e);

      var _isFullScreen =
        document.fullScreen ||
        document.mozFullScreen ||
        document.webkitIsFullScreen;
      if (isFullScreen != _isFullScreen) {
        this.emit('fullScreenChange', isFullScreen);
      }
    });
  }
}

let inputEvent = new InputEvent();

export default inputEvent;
