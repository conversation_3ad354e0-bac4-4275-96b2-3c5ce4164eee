<script>
import {
  <PERSON><PERSON> as <PERSON>utton,
  Checkbox as ACheckbox,
  Dropdown as <PERSON><PERSON>down,
  <PERSON><PERSON> as AMenu,
  MenuItem as AMenuItem,
} from 'ant-design-vue';
import * as Cesium from 'cesium';

import core from '../utils/cesium/core';
import { GISMap as map } from '../utils/cesium/index.js';
import BaseLayerManager from '../utils/cesium/layers/BaseLayerManager.js';
import Fly from '../utils/cesium/mapTools/Fly.js';
import fileSelector from '../utils/fileSelector';
import readAsText from '../utils/readAsText';
import eb from '../ztu/eventbus.js';
import Box from './js/box.js';

let fly = null;
let baseLayerManager = null;
let dataSource = null;

export default {
  components: {
    AButton,
    ACheckbox,
    ADropdown,
    AMenu,
    AMenuItem,
  },
  props: {
    id: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      speed: 50, // 速度，单位米每秒
      loop: false, // 循环漫游
      jitter: false, // 随机跳动
      firstPersonPerspective: true, // 第一人视角
      // follow: true, //跟随
      distance: 200, // 视距
      pitch: 30, // 俯视角（degree）,平视为0度
      displayModel: true,
      displayPath: true,
      flying: false,
    };
  },
  mounted() {
    map.viewer.scene.globe.depthTestAgainstTerrain = false;
    baseLayerManager = baseLayerManager || new BaseLayerManager(map.viewer);
    eb.on('clearAll', () => {
      this.stop();
      this.clear();
    });
  },
  methods: {
    start() {
      if (fly) {
        this.stop();
      }
      fly =
        fly ||
        new Fly({
          viewer: map.viewer,
          callback: (eKey) => {
            if (eKey == 'stop') {
              fly = null;
              this.flying = false;
              let s = (Date.now() - this.startTime) / 1000;
              let m = Math.floor(s / 60);
              s = Number.parseInt(s % 60);
              let h = Math.floor(m / 60);
              m = m % 60;
              const d = Math.floor(h / 24);
              h = h % 24;
              const useTime =
                (d ? `${d}天` : '') +
                (h ? `${h}小时` : '') +
                (m ? `${m}分` : '') +
                (s ? `${s}秒` : '');
              Box.info('漫游', `漫游结束,用时:${useTime}`);
            } else if (eKey == 'empty') {
              fly = null;
              this.flying = false;
              Box.info('漫游', '漫游路径为空');
            }
          },
        });
      fly.setOptions({
        speed: this.speed,
        loop: this.loop,
        jitter: this.jitter,
        firstPersonPerspective: this.firstPersonPerspective,
        // follow: this.follow,
        distance: this.distance,
        pitch: this.pitch,
        displayModel: this.displayModel,
        displayPath: false,
      });
      const data = [];
      [
        ...baseLayerManager.drawer.entities,
        ...((dataSource && dataSource.entities.values) || []),
      ].forEach((entity) => {
        const json = core.entity2Json(entity);
        if (json.point) {
          data.push({
            point: json.position,
          });
        } else if (json.polyline) {
          data.push({
            polyline: json.polyline.positions,
          });
        } else if (json.polygon) {
          data.push({
            polygon: json.polygon.hierarchy.positions,
          });
        }
      });
      fly.start(data);
      this.startTime = new Date();
      this.flying = true;
    },
    stop() {
      fly && fly.stop();
    },
    drawPath() {
      baseLayerManager.drawer.drawEnd();
      baseLayerManager.drawer.drawStart(
        'polyline',
        {
          material: Cesium.Color.RED,
          disableDepthTestDistance: 5_000_000,
          clampToGround: true,
        },
        (entity) => {
          this.show();
        },
      );
    },
    importShp() {
      // localLayerManager = localLayerManager || new LocalLayerManager(map.viewer);
      baseLayerManager.addShapeLayer().then((ds) => {
        this.clear();
        dataSource = ds;
        this.show();
      });
    },
    importFile() {
      // localLayerManager = localLayerManager || new LocalLayerManager(map.viewer);
      fileSelector.openFile(
        (files) => {
          if (files.length > 0) {
            readAsText(files[0]).then((result) => {
              const json = JSON.parse(result);
              baseLayerManager.addEntityByJson(json);
              this.show();
            });
          }
        },
        { multiple: false, accept: '.json' },
      );
    },
    clear() {
      if (dataSource) map.viewer.dataSources.remove(dataSource);
      dataSource = null;
      baseLayerManager.drawer.clear();
    },
    show() {
      dataSource && (dataSource.show = true);
      baseLayerManager.drawer.show();
      this.displayPath = true;
    },
    hide() {
      console.log(dataSource);
      dataSource && (dataSource.show = false);
      baseLayerManager.drawer.hide();
      this.displayPath = false;
    },
    displayPathChange() {
      if (this.displayPath) this.show();
      else this.hide();
    },
  },
  unmount() {
    console.log('unmount');
  },
};
</script>

<template>
  <div class="fly-view-container">
    <div>
      <div class="form-item">
        <div class="form-item-label">速度(米/秒)</div>
        <div class="el-form-item-content">
          <input
            v-model="speed"
            :disabled="flying"
            min="1"
            step="1"
            style="width: 120px"
            type="number"
          />
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-label">视距(米)</div>
        <div class="el-form-item-content">
          <input
            v-model="distance"
            :disabled="flying"
            min="1"
            step="1"
            style="width: 120px"
            type="number"
          />
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-label">俯角(度)</div>
        <div class="el-form-item-content">
          <input
            v-model="pitch"
            :disabled="flying"
            max="80"
            min="10"
            step="1"
            style="width: 120px"
            type="number"
          />
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-label">循环漫游</div>
        <div class="el-form-item-content">
          <ACheckbox v-model:checked="loop" :disabled="flying" />
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-label">随机跳动</div>
        <div class="el-form-item-content">
          <ACheckbox v-model:checked="jitter" :disabled="flying" />
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-label">第一人视角</div>
        <div class="el-form-item-content">
          <ACheckbox
            v-model:checked="firstPersonPerspective"
            :disabled="flying"
          />
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-label">显示模型</div>
        <div class="el-form-item-content">
          <ACheckbox v-model:checked="displayModel" :disabled="flying" />
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-label">显示路径</div>
        <div
          class="form-item-content"
          style="
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
          "
        >
          <ACheckbox
            v-model:checked="displayPath"
            @change="displayPathChange"
          />
          <ADropdown :disabled="flying">
            <a style="font-size: 18px; color: #fff" @click.prevent.stop="">+</a>
            <template #overlay>
              <AMenu>
                <AMenuItem @click="drawPath"> 绘制漫游路线 </AMenuItem>
                <AMenuItem @click="importShp"> 导入SHP文件 </AMenuItem>
                <AMenuItem @click="importFile"> 导入绘图文件 </AMenuItem>
                <AMenuItem @click.stop="clear"> 清空 </AMenuItem>
              </AMenu>
            </template>
          </ADropdown>
        </div>
      </div>
    </div>
    <div class="footer">
      <AButton
        :disabled="flying"
        block
        style="color: #fff; background: transparent"
        type="primary"
        @click="start"
      >
        开始漫游
      </AButton>
      <AButton
        block
        style="margin-left: 10px; color: #fff; background: transparent"
        @click="stop"
      >
        停止漫游
      </AButton>
    </div>
  </div>
</template>

<style lang="less">
.fly-view-container {
  padding: 10px;
  width: 280px;
  input {
    height: 26px;
    margin: 4px;
    line-height: 22px;
    font-size: 14px;
    background: transparent;
    border: 1px solid rgba(40, 44, 52, 1);
    color: #fff;
    padding: 0 5px;
    outline: medium;
    border-radius: 2px;
  }
  textarea {
    margin: 4px;
    width: 200px;
    height: 150px;
    background: transparent;
    border: 1px solid rgba(40, 44, 52, 1);
    color: #fff;
    outline: medium;
    border-radius: 2px;
    resize: none;
    padding: 0 5px;
    line-height: 22px;
    font-size: 14px;
  }
  input:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    color: #fff;
    background: transparent;
    height: 26px;
    line-height: 26px;
    border: 1px solid rgba(40, 44, 52, 1);
    margin: 4px;
  }
  .ant-select-single .ant-select-selector .ant-select-selection-item,
  .ant-select-single .ant-select-selector .ant-select-selection-placeholder {
    line-height: 26px;
  }
  .vc-color-wrap.transparent {
    margin: 4px;
    height: 26px;
    border: 1px solid rgba(40, 44, 52, 1);
    margin-top: -1px;
    border-radius: 2px;
  }
  .form-item {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
  }

  .form-item-label {
    text-align: right;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: #fff;
    line-height: 40px;
    padding: 0 12px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100px;
  }

  .form-item-content {
    line-height: 40px;
    position: relative;
    font-size: 14px;
    align-items: center;
    flex-grow: 1;
  }
  .properties-btn {
    width: 26px;
    height: 26px;
    margin: 4px;
    border: 1px solid rgba(40, 44, 52, 1);
    color: #fff;
    cursor: pointer;
    text-align: center;
    line-height: 24px;
    display: inline-block;
    margin-right: 0;
  }
  .properties-btn:hover {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .footer {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    place-items: 10px;
    width: 100%;
    padding: 10px;
    border-top: 1px solid rgba(40, 44, 52, 0.8);
  }
}
</style>
