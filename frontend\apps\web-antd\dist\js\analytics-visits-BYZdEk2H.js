import{u as s,_ as o}from"./use-echarts-CfT8MSbz.js";import{d as i,r as n,o as c,j as p,b as m,k as f}from"../jse/index-index-DyHD_jbN.js";import"./bootstrap-5OPUVRWy.js";const h=i({__name:"analytics-visits",setup(l){const e=n(),{renderEcharts:a}=s(e);return c(()=>{a({grid:{bottom:0,containLabel:!0,left:"1%",right:"1%",top:"2 %"},series:[{barMaxWidth:80,data:[3e3,2e3,3333,5e3,3200,4200,3200,2100,3e3,5100,6e3,3200,4800],type:"bar"}],tooltip:{axisPointer:{lineStyle:{width:1}},trigger:"axis"},xAxis:{data:Array.from({length:12}).map((r,t)=>`${t+1}月`),type:"category"},yAxis:{max:8e3,splitNumber:4,type:"value"}})}),(r,t)=>(m(),p(f(o),{ref_key:"chartRef",ref:e},null,512))}});export{h as default};
