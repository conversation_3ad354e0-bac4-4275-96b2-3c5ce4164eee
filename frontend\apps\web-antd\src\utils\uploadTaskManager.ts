import { ref } from 'vue';
import { taskListEmitter } from '#/utils/taskEventBus';

export interface UploadTask {
  id: string;
  fileName: string;
  fileSize: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  dataType: string;
  fileFormat: string;
  createTime: number;
}

const STORAGE_KEY = 'UPLOAD_TASKS';


class UploadTaskManager {
  private tasks = ref<UploadTask[]>([]);
  private currentTaskIndex = ref(-1);
  private isProcessing = ref(false);

  constructor() {
    this.loadFromStorage();
  }

  // 从localStorage加载任务
  private loadFromStorage(): void {
    try {
      const storedTasks = localStorage.getItem(STORAGE_KEY);
      if (storedTasks) {
        this.tasks.value = JSON.parse(storedTasks);
      }
    } catch (error) {
      console.error('Failed to load tasks from storage:', error);
    }
  }

  // 保存任务到localStorage
  private saveToStorage(): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.tasks.value));
    } catch (error) {
      console.error('Failed to save tasks to storage:', error);
    }
  }

  // 添加任务
  addTask(files: FileList, dataType: string, fileFormat: string): void {
    Array.from(files).forEach(file => {
      const task: UploadTask = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        fileName: file.name,
        fileSize: file.size,
        status: 'pending',
        progress: 0,
        dataType,
        fileFormat,
        createTime: Date.now()
      };
      this.tasks.value.push(task);
    });
    this.saveToStorage();
    // 触发任务列表更新事件
    taskListEmitter.emit('taskListUpdated');
  }

  // 更新任务状态
  updateTaskStatus(taskId: string, status: UploadTask['status'], progress: number): void {
    const task = this.tasks.value.find(t => t.id === taskId);
    if (task) {
      task.status = status;
      task.progress = progress;
      this.saveToStorage();
      // 触发任务列表更新事件
      taskListEmitter.emit('taskListUpdated');
    }
  }

  // 获取所有任务
  getTasks(): UploadTask[] {
    return this.tasks.value;
  }

  // 获取待处理的任务
  getPendingTasks(): UploadTask[] {
    return this.tasks.value.filter(task => task.status === 'pending');
  }

  // 清除已完成的任务
  clearCompletedTasks(): void {
    this.tasks.value = this.tasks.value.filter(
      task => task.status !== 'completed' && task.status !== 'failed'
    );
    this.saveToStorage();
  }

  // 清除所有任务
  clearAllTasks(): void {
    this.tasks.value = [];
    this.currentTaskIndex.value = -1;
    this.isProcessing.value = false;
    localStorage.removeItem(STORAGE_KEY);
  }
}

export const uploadTaskManager = new UploadTaskManager();
