import * as Cesium from 'cesium'
import utils from '../../utils'
import readAsText from '../../readAsText'
import fileSelector from '../../fileSelector'
import {EventBus} from '../../common/EventBus'
import shp from './shp.js'
import Drawer from '../mapTools/Drawer'
import Box from '../../../components/js/box.js'
import core from '../core'

export default class BaseLayerManager extends EventBus {
	#viewer;
	constructor(viewer) {
		super();
		this.#viewer = viewer;
		this.drawer = new Drawer(this.#viewer);
		this.dataSources = [];
	}
	addShapeLayer() {
		let that = this;
		
		return new Promise((resolve,reject)=>{
			let addDataSource = (dataSource,title)=>{
				this.#viewer.dataSources.add(dataSource);
				//console.log(dataSource.entities)
				this.#viewer.zoomTo(dataSource)
				
				this.emit('addds',{
					title:title,
					dataSource:dataSource
				})
				resolve(dataSource);
			};
			let addGeoJsonDataSource = (data,title)=>{
				Cesium.GeoJsonDataSource.load(data, {
					clampToGround:true
				}).then((dataSource)=>{
					addDataSource(dataSource,title);
				})
			}
			let addKmlDataSource = (data,title)=>{
				Cesium.KmlDataSource.load(data, {}).then((dataSource)=>{
					addDataSource(dataSource,title);
				})
			}
			
			fileSelector.openFile((files)=>{
				let fs=[];
				for(let i=0;i<files.length;i++){
					let file = files[i];
					if(file.name.toLocaleLowerCase().indexOf('.json')>0){
						readAsText(file).then((result)=>{
							console.log(result)
							let json = JSON.parse(result);
							addGeoJsonDataSource(json,file.fileName);
						})
					}
					else if(file.name.toLocaleLowerCase().indexOf('.kmz')>0 || file.name.toLocaleLowerCase().indexOf('.kml')>0){
						readAsText(file).then((result)=>{
							console.log(result)
							let json = JSON.parse(result);
							addKmlDataSource(json,file.fileName);
						})
					}
					else
						fs.push(file)
				}
				if(!fs.length) return;
				shp.parseShpFiles(fs,'gb2312').then((result)=>{
					console.log(result)
					if(!result.crs){
						//result.crs = {"type":"name","properties":{"name":"urn:ogc:def:crs:EPSG::4490"}};
					}
					addGeoJsonDataSource(result,result.fileName);
				}).catch(err=>{
					reject(err)
				})
				/* shp.parseZipFiles(fs,(result)=>{
					console.log(result)
					if(!result.crs){
						//result.crs = {"type":"name","properties":{"name":"urn:ogc:def:crs:EPSG::4490"}};
					}
					addGeoJsonDataSource(result,result.fileName);
				}); */
			},{multiple:true,
				accept:".dbf,.prj,.shp,.cpg,.json",
			})
		})
	}
	addEntityByJson(json) {
		if(json instanceof Array){
			json.forEach(j=>{
				this.addEntityByJson(j);
			})
			//console.log(this.drawer.entities.length)
			this.#viewer.flyTo(this.drawer.entities)
			return;
		}
		let entity = core.json2Entity(json);
		if(entity)
		{
			this.drawer.drawEntity(entity);
			this.#viewer.zoomTo(entity);
		}
	}
	exportJson(){
		if(this.drawer.entities.length){
			let json = this.drawer.toJson();
			utils.saveAs(JSON.stringify(json),'export.json','.json');
		}
		else{
			Box.info('提示','绘图数据为空，无需导出')
		}
	}
}
