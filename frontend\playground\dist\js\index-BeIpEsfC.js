var l=(x,u,n)=>new Promise((g,i)=>{var f=s=>{try{a(n.next(s))}catch(d){i(d)}},m=s=>{try{a(n.throw(s))}catch(d){i(d)}},a=s=>s.done?g(s.value):Promise.resolve(s.value).then(f,m);a((n=n.apply(x,u)).next())});import{r as B,o as _,t as v,u as $,B as c,v as k}from"./bootstrap-DShsrVit.js";import{C as w}from"./index-B_b7xM74.js";import{_ as T}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as b,af as M,ag as N,ah as o,a3 as t,n as r,ap as A,ao as S,an as y}from"../jse/index-index-BMh_AyeW.js";const R={class:"text-primary mx-4"},q=b({__name:"index",setup(x){const u={admin:{password:"123456",username:"admin"},super:{password:"123456",username:"vben"},user:{password:"123456",username:"jack"}},{accessMode:n,toggleAccessMode:g}=B(),i=_(),f=v(),m=$();function a(p){return i.userRoles.includes(p)?"primary":"default"}function s(p){return l(this,null,function*(){if(i.userRoles.includes(p))return;const e=u[p];k(),e&&(yield f.authLogin(e,()=>l(this,null,function*(){m.go(0)})))})}function d(){return l(this,null,function*(){u.super&&(yield g(),k(),yield f.authLogin(u.super,()=>l(this,null,function*(){setTimeout(()=>{m.go(0)},150)})))})}return(p,e)=>(M(),N(t(T),{title:`${t(n)==="frontend"?"前端":"后端"}页面访问权限演示`,description:"切换不同的账号，观察左侧菜单变化。"},{default:o(()=>[r(t(w),{class:"mb-5",title:"权限模式"},{default:o(()=>[e[3]||(e[3]=A("span",{class:"font-semibold"},"当前权限模式:",-1)),A("span",R,S(t(n)==="frontend"?"前端权限控制":"后端权限控制"),1),r(t(c),{type:"primary",onClick:d},{default:o(()=>[y(" 切换为"+S(t(n)==="frontend"?"后端":"前端")+"权限模式 ",1)]),_:1})]),_:1}),r(t(w),{title:"账号切换"},{default:o(()=>[r(t(c),{type:a("super"),onClick:e[0]||(e[0]=C=>s("super"))},{default:o(()=>e[4]||(e[4]=[y(" 切换为 Super 账号 ")])),_:1},8,["type"]),r(t(c),{type:a("admin"),class:"mx-4",onClick:e[1]||(e[1]=C=>s("admin"))},{default:o(()=>e[5]||(e[5]=[y(" 切换为 Admin 账号 ")])),_:1},8,["type"]),r(t(c),{type:a("user"),onClick:e[2]||(e[2]=C=>s("user"))},{default:o(()=>e[6]||(e[6]=[y(" 切换为 User 账号 ")])),_:1},8,["type"])]),_:1})]),_:1},8,["title"]))}});export{q as default};
