var c=(f,n,e)=>new Promise((l,i)=>{var t=a=>{try{m(e.next(a))}catch(p){i(p)}},u=a=>{try{m(e.throw(a))}catch(p){i(p)}},m=a=>a.done?l(a.value):Promise.resolve(a.value).then(t,u);m((e=e.apply(f,n)).next())});import{B as k}from"./bootstrap-DShsrVit.js";import{C as y}from"./index-B_b7xM74.js";import{_ as x}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as _,af as w,ag as C,ah as r,a3 as o,ap as g,an as s,n as d}from"../jse/index-index-BMh_AyeW.js";import{u as h}from"./use-watermark-C6i-HLM1.js";const j=_({__name:"index",setup(f){const{destroyWatermark:n,updateWatermark:e}=h();function l(){return c(this,null,function*(){yield e({advancedStyle:{colorStops:[{color:"red",offset:0},{color:"blue",offset:1}],type:"linear"},content:"hello my watermark",globalAlpha:.5,gridLayoutOptions:{cols:2,gap:[20,20],matrix:[[1,0],[0,1]],rows:2},height:200,layout:"grid",rotate:22,width:200})})}return(i,t)=>(w(),C(o(x),{title:"水印"},{description:r(()=>t[1]||(t[1]=[g("div",{class:"text-foreground/80 mt-2"},[s(" 水印使用了 "),g("a",{class:"text-primary",href:"https://zhensherlock.github.io/watermark-js-plus/",target:"_blank"}," watermark-js-plus "),s(" 开源插件，详细配置可见插件配置。 ")],-1)])),default:r(()=>[d(o(y),{title:"使用"},{default:r(()=>[d(o(k),{class:"mr-2",type:"primary",onClick:t[0]||(t[0]=u=>l())},{default:r(()=>t[2]||(t[2]=[s(" 创建水印 ")])),_:1}),d(o(k),{danger:"",onClick:o(n)},{default:r(()=>t[3]||(t[3]=[s("移除水印")])),_:1},8,["onClick"])]),_:1})]),_:1}))}});export{j as default};
