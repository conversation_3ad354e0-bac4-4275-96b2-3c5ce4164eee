import{ao as a,k as e,z as o,I as r,l as t,ay as n,j as d}from"./chunks/framework.C8U7mBUf.js";const i=JSON.parse('{"title":"Roadmap","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/introduction/roadmap.md","filePath":"en/guide/introduction/roadmap.md"}');const s=a({name:"en/guide/introduction/roadmap.md"},[["render",function(a,i,s,m,l,p){const u=n("NolebaseGitContributors"),c=n("NolebaseGitChangelog");return d(),e("div",null,[i[0]||(i[0]=o("h1",{id:"roadmap",tabindex:"-1"},[r("Roadmap "),o("a",{class:"header-anchor",href:"#roadmap","aria-label":'Permalink to "Roadmap"'},"​")],-1)),i[1]||(i[1]=o("p",null,"TODO:",-1)),t(u),t(c)])}]]);export{i as __pageData,s as default};
