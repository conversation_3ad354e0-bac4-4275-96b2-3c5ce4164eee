import{u as s}from"./form-DnT3S1ma.js";import{by as m}from"./bootstrap-DShsrVit.js";import{C as r}from"./index-B_b7xM74.js";import{_ as i}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as u,af as d,ag as f,ah as a,a3 as t,n as l}from"../jse/index-index-BMh_AyeW.js";const N=u({__name:"query",setup(h){const[c]=s({collapsed:!1,commonConfig:{componentProps:{class:"w-full"}},handleSubmit:n,layout:"horizontal",schema:[{component:"Input",componentProps:{placeholder:"请输入用户名"},fieldName:"username",label:"字符串"},{component:"InputPassword",componentProps:{placeholder:"请输入密码"},fieldName:"password",label:"密码"},{component:"InputNumber",componentProps:{placeholder:"请输入"},fieldName:"number",label:"数字(带后缀)",suffix:()=>"¥"},{component:"Select",componentProps:{allowClear:!0,filterOption:!0,options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}],placeholder:"请选择",showSearch:!0},fieldName:"options",label:"下拉选"},{component:"DatePicker",fieldName:"datePicker",label:"日期选择框"}],showCollapseButton:!0,submitButtonOptions:{content:"查询"},wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"}),[p]=s({collapsed:!0,collapsedRows:2,commonConfig:{componentProps:{class:"w-full"}},handleSubmit:n,layout:"horizontal",schema:(()=>{const e=[];for(let o=0;o<14;o++)e.push({component:"Input",fieldName:`field${o}`,label:`字段${o}`});return e})(),showCollapseButton:!0,submitButtonOptions:{content:"查询"},wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"});function n(e){m.success({content:`form values: ${JSON.stringify(e)}`})}return(e,o)=>(d(),f(t(i),{description:"查询表单，常用语和表格组合使用，可进行收缩展开。",title:"表单组件"},{default:a(()=>[l(t(r),{class:"mb-5",title:"查询表单，默认展开"},{default:a(()=>[l(t(c))]),_:1}),l(t(r),{title:"查询表单，默认折叠，折叠时保留2行"},{default:a(()=>[l(t(p))]),_:1})]),_:1}))}});export{N as default};
