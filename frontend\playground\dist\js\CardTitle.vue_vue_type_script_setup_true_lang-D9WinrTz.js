import{a4 as r,af as t,am as n,ae as c,aZ as o,a3 as l,aX as p}from"../jse/index-index-BMh_AyeW.js";const u=r({__name:"Card",props:{class:{}},setup(s){const a=s;return(e,d)=>(t(),n("div",{class:o(l(p)("bg-card text-card-foreground border-border rounded-xl border",a.class))},[c(e.$slots,"default")],2))}}),f=r({__name:"CardContent",props:{class:{}},setup(s){const a=s;return(e,d)=>(t(),n("div",{class:o(l(p)("p-6 pt-0",a.class))},[c(e.$slots,"default")],2))}}),i=r({__name:"CardHeader",props:{class:{}},setup(s){const a=s;return(e,d)=>(t(),n("div",{class:o(l(p)("flex flex-col gap-y-1.5 p-5",a.class))},[c(e.$slots,"default")],2))}}),m=r({__name:"CardTitle",props:{class:{}},setup(s){const a=s;return(e,d)=>(t(),n("h3",{class:o(l(p)("font-semibold leading-none tracking-tight",a.class))},[c(e.$slots,"default")],2))}});export{m as _,i as a,f as b,u as c};
