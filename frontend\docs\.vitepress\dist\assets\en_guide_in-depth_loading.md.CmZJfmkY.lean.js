import{_ as i}from"./chunks/loading.DmNAR6La.js";import{ao as s,k as a,aP as t,z as l,l as n,ay as e,j as h}from"./chunks/framework.C8U7mBUf.js";const p=JSON.parse('{"title":"Global Loading","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/in-depth/loading.md","filePath":"en/guide/in-depth/loading.md"}'),k={class:"tip custom-block"};const d=s({name:"en/guide/in-depth/loading.md"},[["render",function(s,p,d,o,r,g){const E=e("NolebaseGitContributors"),c=e("NolebaseGitChangelog");return h(),a("div",null,[p[1]||(p[1]=t('<h1 id="global-loading" tabindex="-1">Global Loading <a class="header-anchor" href="#global-loading" aria-label="Permalink to &quot;Global Loading&quot;">​</a></h1><p>Global loading refers to the loading effect that appears when the page is refreshed, usually a spinning icon:</p><p><img src="'+i+'" alt="Global loading spinner"></p><h2 id="principle" tabindex="-1">Principle <a class="header-anchor" href="#principle" aria-label="Permalink to &quot;Principle&quot;">​</a></h2><p>Implemented by the <code>vite-plugin-inject-app-loading</code> plugin, the plugin injects a global <code>loading html</code> into each application.</p><h2 id="disable" tabindex="-1">Disable <a class="header-anchor" href="#disable" aria-label="Permalink to &quot;Disable&quot;">​</a></h2><p>If you do not need global loading, you can disable it in the <code>.env</code> file:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_INJECT_APP_LOADING</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">false</span></span></code></pre></div><h2 id="customization" tabindex="-1">Customization <a class="header-anchor" href="#customization" aria-label="Permalink to &quot;Customization&quot;">​</a></h2><p>If you want to customize the global loading, you can create a <code>loading.html</code> file in the application directory, at the same level as <code>index.html</code>. The plugin will automatically read and inject this HTML. You can define the style and animation of this HTML as you wish.</p>',10)),l("div",k,[p[0]||(p[0]=t('<p class="custom-block-title">TIP</p><ul><li>You can use the same syntax as in <code>index.html</code>, such as the <code>VITE_APP_TITLE</code> variable, to get the application&#39;s title.</li><li>You must ensure there is an element with <code>id=&quot;__app-loading__&quot;</code>.</li><li>Add a <code>hidden</code> class to the element with <code>id=&quot;__app-loading__&quot;</code>.</li><li>You must ensure there is a <code>style[data-app-loading=&quot;inject-css&quot;]</code> element.</li></ul><div class="language-html vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line highlighted"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">style</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> data-app-loading</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;inject-css&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  #__app-loading__.hidden</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    pointer-events</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">none</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line highlighted"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    visibility</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">hidden</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    opacity</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    transition</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">all</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 1</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">s</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> ease-out</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  /* ... */</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">style</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">div</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> id</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;__app-loading__&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  &lt;!-- ... --&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">div</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> class</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;title&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span><span style="--shiki-light:#B31D28;--shiki-light-font-style:italic;--shiki-dark:#FDAEB7;--shiki-dark-font-style:italic;">&lt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">%= VITE_APP_TITLE %&gt;&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">div</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">div</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div>',3)),n(E),n(c)])])}]]);export{p as __pageData,d as default};
