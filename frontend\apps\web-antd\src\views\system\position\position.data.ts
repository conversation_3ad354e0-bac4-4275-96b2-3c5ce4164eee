// 岗位类别选项
export const positionTypeOptions = [
  { label: '部门', value: 'department' },
  { label: '科室', value: 'office' },
];

// 状态选项
export const statusOptions = [
  { label: '全部', value: null },
  { label: '启用', value: 1 },
  { label: '停用', value: 0 },
];

// 表格列配置
export const columns = [
  {
    title: '岗位名称',
    dataIndex: 'name',
    field: 'name',
    width: 200,
  },
  {
    title: '岗位编码',
    dataIndex: 'code',
    field: 'code',
    width: 150,
  },
  {
    title: '岗位类别',
    dataIndex: 'type',
    field: 'type',
    width: 100,
    slots: { default: 'type' },
  },
  {
    title: '主管上级',
    dataIndex: 'parentPosition',
    field: 'parentPosition',
    width: 250,
    slots: { default: 'parentPosition' },
    formatter: ({ row }: any) => {
      if (!row.parentPosition) return '';
      const parentDept = row.parentPosition.department?.name || '';
      const parentName = row.parentPosition.name || '';
      return parentDept && parentName ? `${parentDept}/${parentName}` : '';
    },
  },
  {
    title: '排序',
    dataIndex: 'sortOrder',
    field: 'sortOrder',
    width: 80,
  },
  {
    title: '状态',
    dataIndex: 'status',
    field: 'status',
    width: 80,
    slots: { default: 'status' },
  },
  {
    title: '描述',
    dataIndex: 'description',
    field: 'description',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'createdTime',
    field: 'createdTime',
    width: 180,
  },
  {
    field: 'action',
    fixed: 'right' as const,
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

// 搜索表单配置
export const searchFormSchema = [
  {
    label: '岗位名称',
    fieldName: 'name',
    component: 'Input',
    componentProps: {
      placeholder: '请输入岗位名称',
    },
  },
  {
    label: '类别',
    fieldName: 'type',
    component: 'Select',
    componentProps: {
      options: positionTypeOptions,
      placeholder: '请选择类别',
    },
  },
  {
    label: '状态',
    fieldName: 'status',
    component: 'Select',
    componentProps: {
      options: statusOptions,
      placeholder: '请选择状态',
    },
  },
];

// 表单配置
export const formSchema = [
  {
    fieldName: 'departmentId',
    label: '归属机构',
    rules: 'required',
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择归属机构',
      treeDefaultExpandAll: true,
      showSearch: true,
      treeNodeFilterProp: 'title',
      onChange: (value: any, form: any) => {
        // 清空上级岗位的选择
        form.setFieldValue('parentId', undefined);
        // 重新加载上级岗位列表
        form.validate('parentId');
      },
    },
  },
  {
    fieldName: 'parentId',
    label: '主管上级',
    component: 'Select',
    componentProps: {
      placeholder: '请选择主管上级',
      allowClear: true,
      showSearch: true,
      optionFilterProp: 'label',
      fieldNames: {
        value: 'value',
        label: 'label',
      },
    },
  },
  {
    fieldName: 'name',
    label: '岗位名称',
    rules: 'required',
    component: 'Input',
    componentProps: {
      placeholder: '请输入岗位名称',
    },
  },
  {
    fieldName: 'code',
    label: '岗位编码',
    rules: 'required',
    component: 'Input',
    componentProps: {
      placeholder: '请输入岗位编码',
    },
  },

  {
    fieldName: 'type',
    label: '岗位类别',
    rules: 'required',
    component: 'Select',
    componentProps: {
      options: positionTypeOptions,
      placeholder: '请选择岗位类别',
    },
  },
  {
    fieldName: 'sortOrder',
    label: '排序',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入排序',
      min: 0,
    },
  },
  {
    fieldName: 'status',
    label: '状态',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '停用', value: 0 },
      ],
    },
    defaultValue: 1,
  },
  {
    fieldName: 'description',
    label: '岗位描述',
    component: 'Textarea',
    componentProps: {
      placeholder: '请输入岗位描述',
      rows: 3,
    },
  },
];

// 获取类别标签
export const getTypeLabel = (type: string) => {
  const option = positionTypeOptions.find(item => item.value === type);
  return option ? option.label : type;
};

// 获取状态标签
export const getStatusLabel = (status: number) => {
  return status === 1 ? '启用' : '停用';
};

// 构建树形选择数据
export const buildTreeSelectData = (data: any[], valueField = 'id', titleField = 'name', childrenField = 'children') => {
  return data.map(item => ({
    value: item[valueField],
    title: item[titleField],
    key: item[valueField],
    children: item[childrenField] ? buildTreeSelectData(item[childrenField], valueField, titleField, childrenField) : undefined,
  }));
};

// 构建树形结构
export const buildTree = (data: any[], parentId = 0) => {
  const tree: any[] = [];

  data.forEach(item => {
    if (Number(item.parentId) === Number(parentId)) {
      const children = buildTree(data, item.id);
      if (children.length > 0) {
        item.children = children;
      }
      tree.push(item);
    }
  });

  return tree;
};
