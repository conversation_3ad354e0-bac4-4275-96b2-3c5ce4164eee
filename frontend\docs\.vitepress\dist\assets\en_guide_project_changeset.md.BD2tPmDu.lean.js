import{ao as e,k as a,aP as s,l as t,ay as n,j as i}from"./chunks/framework.C8U7mBUf.js";const r=JSON.parse('{"title":"Changeset","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/project/changeset.md","filePath":"en/guide/project/changeset.md"}');const h=e({name:"en/guide/project/changeset.md"},[["render",function(e,r,h,o,l,d){const c=n("NolebaseGitContributors"),p=n("NolebaseGitChangelog");return i(),a("div",null,[r[0]||(r[0]=s('<h1 id="changeset" tabindex="-1">Changeset <a class="header-anchor" href="#changeset" aria-label="Permalink to &quot;Changeset&quot;">​</a></h1><p>The project has integrated <a href="https://github.com/changesets/changesets" target="_blank" rel="noreferrer">changeset</a> as a version management tool. Changeset is a version management tool that helps us better manage versions, generate changelogs, and automate releases.</p><p>For detailed usage, please refer to the official documentation. If you do not need it, you can ignore it directly.</p><h2 id="command-line" tabindex="-1">Command Line <a class="header-anchor" href="#command-line" aria-label="Permalink to &quot;Command Line&quot;">​</a></h2><p>The changeset command is already built into the project:</p><h3 id="interactively-add-changesets" tabindex="-1">Interactively Add Changesets <a class="header-anchor" href="#interactively-add-changesets" aria-label="Permalink to &quot;Interactively Add Changesets&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> run</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> changeset</span></span></code></pre></div><h3 id="uniformly-increment-version-numbers" tabindex="-1">Uniformly Increment Version Numbers <a class="header-anchor" href="#uniformly-increment-version-numbers" aria-label="Permalink to &quot;Uniformly Increment Version Numbers&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> run</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> version</span></span></code></pre></div>',9)),t(c),t(p)])}]]);export{r as __pageData,h as default};
