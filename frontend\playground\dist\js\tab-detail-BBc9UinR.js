import{b9 as r}from"./bootstrap-DShsrVit.js";import{_ as n}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as i,J as c,af as u,ag as m,ah as p,a3 as _,an as l,ao as f}from"../jse/index-index-BMh_AyeW.js";import{u as d}from"./use-tabs-C64_EnSy.js";const $=i({__name:"tab-detail",setup(x){const o=r(),{setTabTitle:s}=d(),a=c(()=>{var t,e;return(e=(t=o.params)==null?void 0:t.id)!=null?e:-1});return s(`No.${a.value} - 详情信息`),(t,e)=>(u(),m(_(n),{title:`标签页${a.value}详情页`},{description:p(()=>[l(f(a.value)+" - 详情页内容在此 ",1)]),_:1},8,["title"]))}});export{$ as default};
