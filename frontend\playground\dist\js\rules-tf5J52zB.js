var d=(c,p,o)=>new Promise((u,n)=>{var e=l=>{try{r(o.next(l))}catch(m){n(m)}},f=l=>{try{r(o.throw(l))}catch(m){n(m)}},r=l=>l.done?u(l.value):Promise.resolve(l.value).then(e,f);r((o=o.apply(c,p)).next())});import{u as N}from"./form-DnT3S1ma.js";import{s,by as h,B as b}from"./bootstrap-DShsrVit.js";import{C as x}from"./index-B_b7xM74.js";import{_ as g}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as k,af as C,ag as q,ah as t,a3 as a,n as i,an as P}from"../jse/index-index-BMh_AyeW.js";const _=k({__name:"rules",setup(c){const[p,o]=N({commonConfig:{componentProps:{class:"w-full"}},handleSubmit:u,layout:"horizontal",schema:[{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field1",label:"字段1",rules:"required"},{component:"Input",componentProps:{placeholder:"请输入"},defaultValue:"默认值",fieldName:"field2",label:"默认值(必填)",rules:"required"},{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field3",label:"默认值(非必填)",rules:s().default("默认值").optional()},{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field31",label:"自定义信息",rules:s().min(1,{message:"最少输入1个字符"})},{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field4",label:"邮箱",rules:s().email("请输入正确的邮箱")},{component:"InputNumber",componentProps:{placeholder:"请输入"},fieldName:"number",label:"数字",rules:"required"},{component:"Select",componentProps:{allowClear:!0,filterOption:!0,options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}],placeholder:"请选择",showSearch:!0},defaultValue:void 0,fieldName:"options",label:"下拉选",rules:"selectRequired"},{component:"RadioGroup",componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]},fieldName:"radioGroup",label:"单选组",rules:"selectRequired"},{component:"CheckboxGroup",componentProps:{name:"cname",options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]},fieldName:"checkboxGroup",label:"多选组",rules:"selectRequired"},{component:"Checkbox",fieldName:"checkbox",label:"",renderComponentContent:()=>({default:()=>["我已阅读并同意"]}),rules:"selectRequired"},{component:"DatePicker",defaultValue:void 0,fieldName:"datePicker",label:"日期选择框",rules:"selectRequired"},{component:"RangePicker",defaultValue:void 0,fieldName:"rangePicker",label:"区间选择框",rules:"selectRequired"},{component:"InputPassword",componentProps:{placeholder:"请输入"},fieldName:"password",label:"密码",rules:"required"},{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"input-blur",formFieldProps:{validateOnChange:!1,validateOnModelUpdate:!1},help:"blur时才会触发校验",label:"blur触发",rules:"required"},{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"input-async",label:"异步校验",rules:s().min(3,"用户名至少需要3个字符").refine(n=>d(this,null,function*(){return!(yield(r=>d(this,null,function*(){return yield new Promise(l=>setTimeout(l,1e3)),r==="existingUser"}))(n))}),{message:"用户名已存在"})}],wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"});function u(n){h.success({content:`form values: ${JSON.stringify(n)}`})}return(n,e)=>(C(),q(a(g),{description:"表单校验示例",title:"表单组件"},{default:t(()=>[i(a(x),{title:"基础组件校验示例"},{extra:t(()=>[i(a(b),{onClick:e[0]||(e[0]=()=>a(o).validate())},{default:t(()=>e[2]||(e[2]=[P("校验表单")])),_:1}),i(a(b),{class:"mx-2",onClick:e[1]||(e[1]=()=>a(o).resetValidate())},{default:t(()=>e[3]||(e[3]=[P(" 清空校验信息 ")])),_:1})]),default:t(()=>[i(a(p))]),_:1})]),_:1}))}});export{_ as default};
