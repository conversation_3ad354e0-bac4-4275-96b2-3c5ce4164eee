import{ao as i,k as e,aP as a,l as t,ay as s,j as o}from"./chunks/framework.C8U7mBUf.js";const n=JSON.parse('{"title":"Standards","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/project/standard.md","filePath":"en/guide/project/standard.md"}');const l=i({name:"en/guide/project/standard.md"},[["render",function(i,n,l,r,h,c){const d=s("NolebaseGitContributors"),p=s("NolebaseGitChangelog");return o(),e("div",null,[n[0]||(n[0]=a('<h1 id="standards" tabindex="-1">Standards <a class="header-anchor" href="#standards" aria-label="Permalink to &quot;Standards&quot;">​</a></h1><div class="tip custom-block"><p class="custom-block-title">Contributing Code</p><ul><li><p>If you want to contribute code to the project, please ensure your code complies with the project&#39;s coding standards.</p></li><li><p>If you are using <code>vscode</code>, you need to install the following plugins:</p><ul><li><a href="https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint" target="_blank" rel="noreferrer">ESLint</a> - Script code checking</li><li><a href="https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode" target="_blank" rel="noreferrer">Prettier</a> - Code formatting</li><li><a href="https://marketplace.visualstudio.com/items?itemName=streetsidesoftware.code-spell-checker" target="_blank" rel="noreferrer">Code Spell Checker</a> - Word syntax checking</li><li><a href="https://marketplace.visualstudio.com/items?itemName=stylelint.vscode-stylelint" target="_blank" rel="noreferrer">Stylelint</a> - CSS formatting</li></ul></li></ul></div><h2 id="purpose" tabindex="-1">Purpose <a class="header-anchor" href="#purpose" aria-label="Permalink to &quot;Purpose&quot;">​</a></h2><p>Students with basic engineering literacy always pay attention to coding standards, and code style checking (Code Linting, simply called Lint) is an important means to ensure the consistency of coding standards.</p><p>Following the corresponding coding standards has the following benefits:</p><ul><li>Lower bug error rate</li><li>Efficient development efficiency</li><li>Higher readability</li></ul><h2 id="tools" tabindex="-1">Tools <a class="header-anchor" href="#tools" aria-label="Permalink to &quot;Tools&quot;">​</a></h2><p>The project&#39;s configuration files are located in <code>internal/lint-configs</code>, where you can modify various lint configurations.</p><p>The project integrates the following code verification tools:</p><ul><li><a href="https://eslint.org/" target="_blank" rel="noreferrer">ESLint</a> for JavaScript code checking</li><li><a href="https://stylelint.io/" target="_blank" rel="noreferrer">Stylelint</a> for CSS style checking</li><li><a href="https://prettier.io/" target="_blank" rel="noreferrer">Prettier</a> for code formatting</li><li><a href="https://commitlint.js.org/" target="_blank" rel="noreferrer">Commitlint</a> for checking the standard of git commit messages</li><li><a href="https://publint.dev/" target="_blank" rel="noreferrer">Publint</a> for checking the standard of npm packages</li><li><a href="https://github.com/lint-staged/lint-staged" target="_blank" rel="noreferrer">Lint Staged</a> for running code verification before git commits</li><li><a href="https://cspell.org/" target="_blank" rel="noreferrer">Cspell</a> for checking spelling errors</li></ul><h2 id="eslint" tabindex="-1">ESLint <a class="header-anchor" href="#eslint" aria-label="Permalink to &quot;ESLint&quot;">​</a></h2><p>ESLint is a code standard and error checking tool used to identify and report syntax errors in TypeScript code.</p><h3 id="command" tabindex="-1">Command <a class="header-anchor" href="#command" aria-label="Permalink to &quot;Command&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> eslint</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> .</span></span></code></pre></div><h3 id="configuration" tabindex="-1">Configuration <a class="header-anchor" href="#configuration" aria-label="Permalink to &quot;Configuration&quot;">​</a></h3><p>The ESLint configuration file is <code>eslint.config.mjs</code>, with its core configuration located in the <code>internal/lint-configs/eslint-config</code> directory, which can be modified according to project needs.</p><h2 id="stylelint" tabindex="-1">Stylelint <a class="header-anchor" href="#stylelint" aria-label="Permalink to &quot;Stylelint&quot;">​</a></h2><p>Stylelint is used to check the style of CSS within the project. Coupled with the editor&#39;s auto-fix feature, it can effectively unify the CSS style within the project.</p><h3 id="command-1" tabindex="-1">Command <a class="header-anchor" href="#command-1" aria-label="Permalink to &quot;Command&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> stylelint</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;**/*.{vue,css,less.scss}&quot;</span></span></code></pre></div><h3 id="configuration-1" tabindex="-1">Configuration <a class="header-anchor" href="#configuration-1" aria-label="Permalink to &quot;Configuration&quot;">​</a></h3><p>The Stylelint configuration file is <code>stylelint.config.mjs</code>, with its core configuration located in the <code>internal/lint-configs/stylelint-config</code> directory, which can be modified according to project needs.</p><h2 id="prettier" tabindex="-1">Prettier <a class="header-anchor" href="#prettier" aria-label="Permalink to &quot;Prettier&quot;">​</a></h2><p>Prettier Can be used to unify project code style, consistent indentation, single and double quotes, trailing commas, and other styles.</p><h3 id="command-2" tabindex="-1">Command <a class="header-anchor" href="#command-2" aria-label="Permalink to &quot;Command&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> prettier</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> .</span></span></code></pre></div><h3 id="configuration-2" tabindex="-1">Configuration <a class="header-anchor" href="#configuration-2" aria-label="Permalink to &quot;Configuration&quot;">​</a></h3><p>The Prettier configuration file is <code>.prettier.mjs</code>, with its core configuration located in the <code>internal/lint-configs/prettier-config</code> directory, which can be modified according to project needs.</p><h2 id="commitlint" tabindex="-1">CommitLint <a class="header-anchor" href="#commitlint" aria-label="Permalink to &quot;CommitLint&quot;">​</a></h2><p>In a team, everyone&#39;s git commit messages can vary widely, making it difficult to ensure standardization without a mechanism. How can standardization be achieved? You might think of using git&#39;s hook mechanism to write shell scripts to implement this. Of course, this is possible, but actually, JavaScript has a great tool for implementing this template, which is commitlint (used for verifying the standard of git commit messages).</p><h3 id="configuration-3" tabindex="-1">Configuration <a class="header-anchor" href="#configuration-3" aria-label="Permalink to &quot;Configuration&quot;">​</a></h3><p>The CommitLint configuration file is <code>.commitlintrc.mjs</code>, with its core configuration located in the <code>internal/lint-configs/commitlint-config</code> directory, which can be modified according to project needs.</p><h3 id="git-commit-standards" tabindex="-1">Git Commit Standards <a class="header-anchor" href="#git-commit-standards" aria-label="Permalink to &quot;Git Commit Standards&quot;">​</a></h3><p>Refer to <a href="https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-changelog-angular" target="_blank" rel="noreferrer">Angular</a></p><ul><li><code>feat</code> Add new features</li><li><code>fix</code> Fix problems/BUGs</li><li><code>style</code> Code style changes that do not affect the outcome</li><li><code>perf</code> Optimization/performance improvement</li><li><code>refactor</code> Refactoring</li><li><code>revert</code> Revert changes</li><li><code>test</code> Related to tests</li><li><code>docs</code> Documentation/comments</li><li><code>chore</code> Dependency updates/scaffold configuration modifications, etc.</li><li><code>workflow</code> Workflow improvements</li><li><code>ci</code> Continuous integration</li><li><code>types</code> Type modifications</li></ul><h3 id="disabling-git-commit-standard-checks" tabindex="-1">Disabling Git Commit Standard Checks <a class="header-anchor" href="#disabling-git-commit-standard-checks" aria-label="Permalink to &quot;Disabling Git Commit Standard Checks&quot;">​</a></h3><p>If you want to disable Git commit standard checks, there are two ways:</p><div class="vp-code-group vp-adaptive-theme"><div class="tabs"><input type="radio" name="group-DlgP4" id="tab-jlQSHw6" checked><label data-title="Temporary disable" for="tab-jlQSHw6">Temporary disable</label><input type="radio" name="group-DlgP4" id="tab-XedlRiJ"><label data-title="Permanent closed" for="tab-XedlRiJ">Permanent closed</label></div><div class="blocks"><div class="language-bash vp-adaptive-theme active"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> commit</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> -m</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;feat: add home page&#39;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> --no-verify</span></span></code></pre></div><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark has-diff vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Comment out the following code in .husky/commit-msg to disable</span></span>\n<span class="line diff remove"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> exec</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> commitlint</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> --edit</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">$1</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;</span></span></code></pre></div></div></div><h2 id="publint" tabindex="-1">Publint <a class="header-anchor" href="#publint" aria-label="Permalink to &quot;Publint&quot;">​</a></h2><p>Publint is a tool for checking the standard of npm packages, which can check whether the package version conforms to the standard, whether it conforms to the standard ESM package specification, etc.</p><h3 id="command-3" tabindex="-1">Command <a class="header-anchor" href="#command-3" aria-label="Permalink to &quot;Command&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> publint</span></span></code></pre></div><h2 id="cspell" tabindex="-1">Cspell <a class="header-anchor" href="#cspell" aria-label="Permalink to &quot;Cspell&quot;">​</a></h2><p>Cspell is a tool for checking spelling errors, which can check for spelling errors in the code, avoiding bugs caused by spelling errors.</p><h3 id="command-4" tabindex="-1">Command <a class="header-anchor" href="#command-4" aria-label="Permalink to &quot;Command&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> cspell</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> lint</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> \\&quot;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">**</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">*</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">.ts</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\&quot;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  \\&quot;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">**</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/README.md</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\&quot;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> \\&quot;</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">.changeset/</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">*</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">.md</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\&quot;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> --no-progress</span></span></code></pre></div><h3 id="configuration-4" tabindex="-1">Configuration <a class="header-anchor" href="#configuration-4" aria-label="Permalink to &quot;Configuration&quot;">​</a></h3><p>The cspell configuration file is <code>cspell.json</code>, which can be modified according to project needs.</p><h2 id="git-hook" tabindex="-1">Git Hook <a class="header-anchor" href="#git-hook" aria-label="Permalink to &quot;Git Hook&quot;">​</a></h2><p>Git hooks are generally combined with various lints to check code style during git commits. If the check fails, the commit will not proceed. Developers need to modify and resubmit.</p><h3 id="husky" tabindex="-1">husky <a class="header-anchor" href="#husky" aria-label="Permalink to &quot;husky&quot;">​</a></h3><p>One issue is that the check will verify all code, but we only want to check the code we are committing. This is where husky comes in.</p><p>The most effective solution is to perform Lint checks locally before committing. A common practice is to use husky or pre-commit to perform a Lint check before local submission.</p><p>The project defines corresponding hooks inside <code>.husky</code>.</p><h4 id="how-to-disable-husky" tabindex="-1">How to Disable Husky <a class="header-anchor" href="#how-to-disable-husky" aria-label="Permalink to &quot;How to Disable Husky&quot;">​</a></h4><p>If you want to disable Husky, simply delete the .husky directory.</p><h3 id="lint-staged" tabindex="-1">lint-staged <a class="header-anchor" href="#lint-staged" aria-label="Permalink to &quot;lint-staged&quot;">​</a></h3><p>Used for automatically fixing style issues of committed files. Its configuration file is <code>.lintstagedrc.mjs</code>, which can be modified according to project needs.</p>',58)),t(d),t(p)])}]]);export{n as __pageData,l as default};
