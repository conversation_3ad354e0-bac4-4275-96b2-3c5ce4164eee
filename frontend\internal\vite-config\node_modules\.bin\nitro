#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/nitropack@2.11.11_@netlify+blobs@8.2.0_encoding@0.1.13/node_modules/nitropack/dist/cli/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/nitropack@2.11.11_@netlify+blobs@8.2.0_encoding@0.1.13/node_modules/nitropack/dist/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/nitropack@2.11.11_@netlify+blobs@8.2.0_encoding@0.1.13/node_modules/nitropack/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/nitropack@2.11.11_@netlify+blobs@8.2.0_encoding@0.1.13/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/nitropack@2.11.11_@netlify+blobs@8.2.0_encoding@0.1.13/node_modules/nitropack/dist/cli/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/nitropack@2.11.11_@netlify+blobs@8.2.0_encoding@0.1.13/node_modules/nitropack/dist/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/nitropack@2.11.11_@netlify+blobs@8.2.0_encoding@0.1.13/node_modules/nitropack/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/nitropack@2.11.11_@netlify+blobs@8.2.0_encoding@0.1.13/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nitropack/dist/cli/index.mjs" "$@"
else
  exec node  "$basedir/../nitropack/dist/cli/index.mjs" "$@"
fi
