var i=Object.defineProperty,s=(s,a,t)=>((s,a,t)=>a in s?i(s,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[a]=t)(s,"symbol"!=typeof a?a+"":a,t);import{u as a,a as t,b as l,c as h,d as e,e as k,f as n,g as E,h as d,Z as r,i as p,j as o,X as g,V as y,k as c,l as F,m as u,n as C}from"./chunks/theme.TDvSnEYR.js";import{g as D,S as B,u as m}from"./chunks/index.DyNOf6Q_.js";import{f as A,S as b,_ as f,r as v,w,j as x,y as O,m as V,l as _,u as S,ae as P,z as T,n as q,I as j,M as $,x as I,D as U,an as M,b as R,Z as L,h as N,O as z,k as W,F as G,C as J,aP as Z,ay as K}from"./chunks/framework.C8U7mBUf.js";const H={class:"flex-center"},Q=A({__name:"drawer",props:{drawerApi:{default:void 0},cancelText:{},class:{},closable:{type:Boolean},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},footer:{type:Boolean},footerClass:{},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},title:{},titleTooltip:{}},setup(i){var s,C;const B=i,m=D.getComponents(),A=b();f("DISMISSABLE_DRAWER_ID",A);const M=v(),{$t:R}=a(),{isMobile:L}=t(),N=null==(C=null==(s=B.drawerApi)?void 0:s.useStore)?void 0:C.call(s),{cancelText:z,class:W,closable:G,closeOnClickModal:J,closeOnPressEscape:Z,confirmLoading:K,confirmText:Q,contentClass:X,description:Y,footer:ii,footerClass:si,header:ai,headerClass:ti,loading:li,modal:hi,openAutoFocus:ei,showCancelButton:ki,showConfirmButton:ni,title:Ei,titleTooltip:di}=l(B,N);function ri(i){J.value||i.preventDefault()}function pi(i){Z.value||i.preventDefault()}function oi(i){const s=i.target,a=null==s?void 0:s.dataset.dismissableDrawer;J.value&&a===A||i.preventDefault()}function gi(i){ei.value||null==i||i.preventDefault()}function yi(i){i.preventDefault(),i.stopPropagation()}return w((()=>li.value),(i=>{i&&M.value&&M.value.scrollTo({top:0})})),(i,s)=>{var a;return x(),O(S(u),{modal:!1,open:null==(a=S(N))?void 0:a.isOpen,"onUpdate:open":s[2]||(s[2]=()=>{var s;return null==(s=i.drawerApi)?void 0:s.close()})},{default:V((()=>{var a;return[_(S(h),{class:P(S(e)("flex w-[520px] flex-col",S(W),{"!w-full":S(L)})),modal:S(hi),open:null==(a=S(N))?void 0:a.isOpen,onCloseAutoFocus:yi,onEscapeKeyDown:pi,onFocusOutside:yi,onInteractOutside:ri,onOpenAutoFocus:gi,onPointerDownOutside:oi},{default:V((()=>[S(ai)?(x(),O(S(k),{key:0,class:P(S(e)("!flex flex-row items-center justify-between border-b px-6 py-5",S(ti),{"px-4 py-3":S(G)}))},{default:V((()=>[T("div",null,[S(Ei)?(x(),O(S(n),{key:0,class:"text-left"},{default:V((()=>[q(i.$slots,"title",{},(()=>[j($(S(Ei))+" ",1),S(di)?(x(),O(S(E),{key:0,"trigger-class":"pb-1"},{default:V((()=>[j($(S(di)),1)])),_:1})):I("",!0)]))])),_:3})):I("",!0),S(Y)?(x(),O(S(d),{key:1,class:"mt-1 text-xs"},{default:V((()=>[q(i.$slots,"description",{},(()=>[j($(S(Y)),1)]))])),_:3})):I("",!0)]),S(Ei)&&S(Y)?I("",!0):(x(),O(S(r),{key:0},{default:V((()=>[S(Ei)?I("",!0):(x(),O(S(n),{key:0})),S(Y)?I("",!0):(x(),O(S(d),{key:1}))])),_:1})),T("div",H,[q(i.$slots,"extra"),S(G)?(x(),O(S(p),{key:0,"as-child":"",class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:V((()=>[_(S(o),null,{default:V((()=>[_(S(g),{class:"size-4"})])),_:1})])),_:1})):I("",!0)])])),_:3},8,["class"])):I("",!0),T("div",{ref_key:"wrapperRef",ref:M,class:P(S(e)("relative flex-1 overflow-y-auto p-3",S(X),{"overflow-hidden":S(li)}))},[S(li)?(x(),O(S(y),{key:0,class:"size-full",spinning:""})):I("",!0),q(i.$slots,"default")],2),S(ii)?(x(),O(S(c),{key:1,class:P(S(e)("w-full flex-row items-center justify-end border-t p-2 px-3",S(si)))},{default:V((()=>[q(i.$slots,"prepend-footer"),q(i.$slots,"footer",{},(()=>[S(ki)?(x(),O(U(S(m).DefaultButton||S(F)),{key:0,variant:"ghost",onClick:s[0]||(s[0]=()=>{var s;return null==(s=i.drawerApi)?void 0:s.onCancel()})},{default:V((()=>[q(i.$slots,"cancelText",{},(()=>[j($(S(z)||S(R)("cancel")),1)]))])),_:3})):I("",!0),S(ni)?(x(),O(U(S(m).PrimaryButton||S(F)),{key:1,loading:S(K),onClick:s[1]||(s[1]=()=>{var s;return null==(s=i.drawerApi)?void 0:s.onConfirm()})},{default:V((()=>[q(i.$slots,"confirmText",{},(()=>[j($(S(Q)||S(R)("confirm")),1)]))])),_:3},8,["loading"])):I("",!0)])),q(i.$slots,"append-footer")])),_:3},8,["class"])):I("",!0)])),_:3},8,["class","modal","open"])]})),_:3},8,["open"])}}});class X{constructor(i={}){s(this,"api"),s(this,"state"),s(this,"sharedData",{payload:{}}),s(this,"store");const{connectedComponent:a,onBeforeClose:t,onCancel:l,onConfirm:h,onOpenChange:e,...k}=i;this.store=new B({class:"",closable:!0,closeOnClickModal:!0,closeOnPressEscape:!0,confirmLoading:!1,contentClass:"",footer:!0,header:!0,isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,showCancelButton:!0,showConfirmButton:!0,title:"",...k},{onUpdate:()=>{var i,s,a;const t=this.store.state;(null==t?void 0:t.isOpen)===(null==(i=this.state)?void 0:i.isOpen)?this.state=t:(this.state=t,null==(a=(s=this.api).onOpenChange)||a.call(s,!!(null==t?void 0:t.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:t,onCancel:l,onConfirm:h,onOpenChange:e},C(this)}batchStore(i){this.store.batch(i)}close(){var i,s;((null==(s=(i=this.api).onBeforeClose)?void 0:s.call(i))??!0)&&this.store.setState((i=>({...i,isOpen:!1})))}getData(){var i;return(null==(i=this.sharedData)?void 0:i.payload)??{}}onCancel(){var i,s;this.api.onCancel?null==(s=(i=this.api).onCancel)||s.call(i):this.close()}onConfirm(){var i,s;null==(s=(i=this.api).onConfirm)||s.call(i)}open(){this.store.setState((i=>({...i,isOpen:!0})))}setData(i){this.sharedData.payload=i}setState(i){M(i)?this.store.setState(i):this.store.setState((s=>({...s,...i})))}}const Y=Symbol("VBEN_DRAWER_INJECT");function ii(i={}){var s;const{connectedComponent:a}=i;if(a){const s=R({}),t=A(((t,{attrs:l,slots:h})=>(f(Y,{extendApi(i){Object.setPrototypeOf(s,i)},options:i}),async function(i,s){var a;if(!s||0===Object.keys(s).length)return;await z();const t=null==(a=null==i?void 0:i.store)?void 0:a.state;if(!t)return;const l=new Set(Object.keys(t));for(const h of Object.keys(s))l.has(h)&&!["class"].includes(h)&&console.warn(`[Vben Drawer]: When 'connectedComponent' exists, do not set props or slots '${h}', which will increase complexity. If you need to modify the props of Drawer, please use useVbenDrawer or api.`)}(s,{...t,...l,...h}),()=>N(a,{...t,...l},h))),{inheritAttrs:!1,name:"VbenParentDrawer"});return[t,s]}const t=L(Y,{}),l={...t.options,...i};l.onOpenChange=s=>{var a,l,h;null==(a=i.onOpenChange)||a.call(i,s),null==(h=null==(l=t.options)?void 0:l.onOpenChange)||h.call(l,s)};const h=new X(l),e=h;e.useStore=i=>m(h.store,i);const k=A(((i,{attrs:s,slots:a})=>()=>N(Q,{...i,...s,drawerApi:e},a)),{inheritAttrs:!1,name:"VbenDrawer"});return null==(s=t.extendApi)||s.call(t,e),[k,e]}const si=A({__name:"index",setup(i){const[s,a]=ii();return(i,t)=>(x(),W("div",null,[_(S(F),{onClick:t[0]||(t[0]=()=>S(a).open())},{default:V((()=>t[1]||(t[1]=[j("Open")]))),_:1}),_(S(s),{class:"w-[600px]",title:"基础示例"},{default:V((()=>t[2]||(t[2]=[j(" drawer content ")]))),_:1})]))}}),ai=A({__name:"drawer",setup(i){const[s]=ii();return(i,a)=>(x(),O(S(s),{title:"组件抽离示例"},{default:V((()=>a[0]||(a[0]=[j(" extra drawer content ")]))),_:1}))}}),ti=A({__name:"index",setup(i){const[s,a]=ii({connectedComponent:ai});function t(){a.open()}return(i,a)=>(x(),W("div",null,[_(S(s)),_(S(F),{onClick:t},{default:V((()=>a[0]||(a[0]=[j("Open")]))),_:1})]))}}),li=A({__name:"drawer",setup(i){const s=v([]),[a,t]=ii({onCancel(){t.close()},onConfirm(){console.log("onConfirm")},onOpenChange(i){i&&l(10)}});function l(i){t.setState({loading:!0}),setTimeout((()=>{s.value=Array.from({length:i},((i,s)=>s+1)),t.setState({loading:!1})}),2e3)}return(i,t)=>(x(),O(S(a),{title:"自动计算高度"},{"prepend-footer":V((()=>[_(S(F),{type:"link",onClick:t[0]||(t[0]=i=>l(6))},{default:V((()=>t[1]||(t[1]=[j(" 点击更新数据 ")]))),_:1})])),default:V((()=>[(x(!0),W(G,null,J(s.value,(i=>(x(),W("div",{key:i,class:"even:bg-heavy bg-muted flex-center h-[220px] w-full"},$(i),1)))),128))])),_:1}))}}),hi=A({__name:"index",setup(i){const[s,a]=ii({connectedComponent:li});function t(){a.open()}return(i,a)=>(x(),W("div",null,[_(S(s)),_(S(F),{onClick:t},{default:V((()=>a[0]||(a[0]=[j("Open")]))),_:1})]))}}),ei={class:"flex-col-center"},ki=A({__name:"drawer",setup(i){const[s,a]=ii({onCancel(){a.close()},onConfirm(){console.info("onConfirm")},title:"动态修改配置示例"});return(i,t)=>(x(),O(S(s),null,{default:V((()=>[T("div",ei,[_(S(F),{class:"mb-3",type:"primary",onClick:t[0]||(t[0]=i=>{a.setState({title:"内部动态标题"})})},{default:V((()=>t[1]||(t[1]=[j(" 内部动态修改标题 ")]))),_:1})])])),_:1}))}}),ni=A({__name:"index",setup(i){const[s,a]=ii({connectedComponent:ki});function t(){a.open()}function l(){a.setState({title:"外部动态标题"}),a.open()}return(i,a)=>(x(),W("div",null,[_(S(s)),_(S(F),{onClick:t},{default:V((()=>a[0]||(a[0]=[j("Open")]))),_:1}),_(S(F),{class:"ml-2",type:"primary",onClick:l},{default:V((()=>a[1]||(a[1]=[j(" 从外部修改标题并打开 ")]))),_:1})]))}}),Ei={class:"flex-col-center"},di=A({__name:"drawer",setup(i){const s=v(),[a,t]=ii({onCancel(){t.close()},onConfirm(){console.info("onConfirm")},onOpenChange(i){i&&(s.value=t.getData())}});return(i,t)=>(x(),O(S(a),{title:"数据共享示例"},{default:V((()=>[T("div",Ei,"外部传递数据： "+$(s.value),1)])),_:1}))}}),ri=A({__name:"index",setup(i){const[s,a]=ii({connectedComponent:di});function t(){a.setData({content:"外部传递的数据 content",payload:"外部传递的数据 payload"}),a.open()}return(i,a)=>(x(),W("div",null,[_(S(s)),_(S(F),{onClick:t},{default:V((()=>a[0]||(a[0]=[j("Open")]))),_:1})]))}}),pi=JSON.parse('{"title":"Vben Drawer 抽屉","description":"","frontmatter":{"outline":"deep"},"headers":[],"relativePath":"components/common-ui/vben-drawer.md","filePath":"components/common-ui/vben-drawer.md"}'),oi={name:"components/common-ui/vben-drawer.md"},gi=Object.assign(oi,{setup:i=>(i,s)=>{const a=K("DemoPreview"),t=K("NolebaseGitContributors"),l=K("NolebaseGitChangelog");return x(),W("div",null,[s[9]||(s[9]=Z('<h1 id="vben-drawer-抽屉" tabindex="-1">Vben Drawer 抽屉 <a class="header-anchor" href="#vben-drawer-抽屉" aria-label="Permalink to &quot;Vben Drawer 抽屉&quot;">​</a></h1><p>框架提供的抽屉组件，支持<code>自动高度</code>、<code>loading</code>等功能。</p><blockquote><p>如果文档内没有参数说明，可以尝试在在线示例内寻找</p></blockquote><div class="info custom-block"><p class="custom-block-title">写在前面</p><p>如果你觉得现有组件的封装不够理想，或者不完全符合你的需求，大可以直接使用原生组件，亦或亲手封装一个适合的组件。框架提供的组件并非束缚，使用与否，完全取决于你的需求与自由。</p></div><div class="tip custom-block"><p class="custom-block-title">README</p><p>下方示例代码中的，存在一些国际化、主题色未适配问题，这些问题只在文档内会出现，实际使用并不会有这些问题，可忽略，不必纠结。</p></div><h2 id="基础用法" tabindex="-1">基础用法 <a class="header-anchor" href="#基础用法" aria-label="Permalink to &quot;基础用法&quot;">​</a></h2><p>使用 <code>useVbenDrawer</code> 创建最基础的模态框。</p>',7)),_(a,{files:"%5B%22index.vue%22%5D"},{"index.vue":V((()=>s[0]||(s[0]=[T("div",{class:"language-vue vp-adaptive-theme"},[T("button",{title:"Copy Code",class:"copy"}),T("span",{class:"lang"},"vue"),T("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[T("code",null,[T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenDrawer, VbenButton } "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '@vben/common-ui'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"drawerApi"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenDrawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"();")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"open"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"()"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">Open</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," class"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"w-[600px]"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," title"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"基础示例"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"> drawer content </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:V((()=>[_(si)])),_:1}),s[10]||(s[10]=T("h2",{id:"组件抽离",tabindex:"-1"},[j("组件抽离 "),T("a",{class:"header-anchor",href:"#组件抽离","aria-label":'Permalink to "组件抽离"'},"​")],-1)),s[11]||(s[11]=T("p",null,[j("Drawer 内的内容一般业务中，会比较复杂，所以我们可以将 drawer 内的内容抽离出来，也方便复用。通过 "),T("code",null,"connectedComponent"),j(" 参数，可以将内外组件进行连接，而不用其他任何操作。")],-1)),_(a,{files:"%5B%22index.vue%22%2C%22drawer.vue%22%5D"},{"index.vue":V((()=>s[1]||(s[1]=[T("div",{class:"language-vue vp-adaptive-theme"},[T("button",{title:"Copy Code",class:"copy"}),T("span",{class:"lang"},"vue"),T("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[T("code",null,[T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenDrawer, VbenButton } "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '@vben/common-ui'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ExtraDrawer "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," './drawer.vue'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"drawerApi"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenDrawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 连接抽离的组件")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  connectedComponent: ExtraDrawer,")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," open"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"open"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"();")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," />")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"open"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">Open</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),"drawer.vue":V((()=>s[2]||(s[2]=[T("div",{class:"language-vue vp-adaptive-theme"},[T("button",{title:"Copy Code",class:"copy"}),T("span",{class:"lang"},"vue"),T("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[T("code",null,[T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenDrawer } "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '@vben/common-ui'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenDrawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"();")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," title"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"组件抽离示例"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"> extra drawer content </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:V((()=>[_(ti)])),_:1}),s[12]||(s[12]=T("h2",{id:"自动计算高度",tabindex:"-1"},[j("自动计算高度 "),T("a",{class:"header-anchor",href:"#自动计算高度","aria-label":'Permalink to "自动计算高度"'},"​")],-1)),s[13]||(s[13]=T("p",null,[j("弹窗会自动计算内容高度，超过一定高度会出现滚动条，同时结合 "),T("code",null,"loading"),j(" 效果以及使用 "),T("code",null,"prepend-footer"),j(" 插槽。")],-1)),_(a,{files:"%5B%22index.vue%22%2C%22drawer.vue%22%5D"},{"index.vue":V((()=>s[3]||(s[3]=[T("div",{class:"language-vue vp-adaptive-theme"},[T("button",{title:"Copy Code",class:"copy"}),T("span",{class:"lang"},"vue"),T("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[T("code",null,[T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenDrawer, VbenButton } "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '@vben/common-ui'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ExtraDrawer "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," './drawer.vue'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"drawerApi"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenDrawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 连接抽离的组件")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  connectedComponent: ExtraDrawer,")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," open"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"open"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"();")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," />")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"open"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">Open</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),"drawer.vue":V((()=>s[4]||(s[4]=[T("div",{class:"language-vue vp-adaptive-theme"},[T("button",{title:"Copy Code",class:"copy"}),T("span",{class:"lang"},"vue"),T("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[T("code",null,[T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { ref } "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'vue'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenDrawer, VbenButton } "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '@vben/common-ui'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," list"),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}}," ="),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," ref"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"number"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"[]>([]);")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"drawerApi"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenDrawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"  onCancel"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"close"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"();")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"  onConfirm"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    console."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"log"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'onConfirm'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},");")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"  onOpenChange"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),T("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"isOpen"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    if"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," (isOpen) {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"      handleUpdate"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"10"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},");")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," handleUpdate"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),T("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"len"),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},":"),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," number"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ loading: "),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"true"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," });")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"  setTimeout"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"(() "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    list.value "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," Array."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"from"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ length: len }, ("),T("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"_v"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),T("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"k"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"=>"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," k "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"+"),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," 1"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},");")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ loading: "),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"false"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," });")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  }, "),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"2000"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},");")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," title"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"自动计算高度"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"      v-for"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"item "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"in"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," list"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"')]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      :"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"key"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"item"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"')]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"      class"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"even:bg-heavy bg-muted flex-center h-[220px] w-full"')]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    >")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      {{ item }}")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," #"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"prepend-footer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," type"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"link"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleUpdate"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"6"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},")"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        点击更新数据")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:V((()=>[_(hi)])),_:1}),s[14]||(s[14]=T("h2",{id:"使用-api",tabindex:"-1"},[j("使用 Api "),T("a",{class:"header-anchor",href:"#使用-api","aria-label":'Permalink to "使用 Api"'},"​")],-1)),s[15]||(s[15]=T("p",null,[j("通过 "),T("code",null,"drawerApi"),j(" 可以调用 drawer 的方法以及使用 "),T("code",null,"setState"),j(" 更新 drawer 的状态。")],-1)),_(a,{files:"%5B%22index.vue%22%2C%22drawer.vue%22%5D"},{"index.vue":V((()=>s[5]||(s[5]=[T("div",{class:"language-vue vp-adaptive-theme"},[T("button",{title:"Copy Code",class:"copy"}),T("span",{class:"lang"},"vue"),T("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[T("code",null,[T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenDrawer, VbenButton } "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '@vben/common-ui'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ExtraDrawer "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," './drawer.vue'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"drawerApi"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenDrawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 连接抽离的组件")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  connectedComponent: ExtraDrawer,")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," open"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"open"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"();")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," handleUpdateTitle"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ title: "),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'外部动态标题'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," });")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"open"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"();")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," />")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"open"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">Open</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," class"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ml-2"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," type"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"primary"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"handleUpdateTitle"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      从外部修改标题并打开")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),"drawer.vue":V((()=>s[6]||(s[6]=[T("div",{class:"language-vue vp-adaptive-theme"},[T("button",{title:"Copy Code",class:"copy"}),T("span",{class:"lang"},"vue"),T("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[T("code",null,[T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenDrawer, VbenButton } "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '@vben/common-ui'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"drawerApi"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenDrawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"  onCancel"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"close"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"();")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"  onConfirm"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    console."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"info"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'onConfirm'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},");")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  title: "),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'动态修改配置示例'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," handleUpdateTitle"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setState"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({ title: "),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'内部动态标题'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," });")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," class"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"flex-col-center"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," class"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"mb-3"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," type"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"primary"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"handleUpdateTitle"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"()"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"        内部动态修改标题")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:V((()=>[_(ni)])),_:1}),s[16]||(s[16]=T("h2",{id:"数据共享",tabindex:"-1"},[j("数据共享 "),T("a",{class:"header-anchor",href:"#数据共享","aria-label":'Permalink to "数据共享"'},"​")],-1)),s[17]||(s[17]=T("p",null,[j("如果你使用了 "),T("code",null,"connectedComponent"),j(" 参数，那么内外组件会共享数据，比如一些表单回填等操作。可以用 "),T("code",null,"drawerApi"),j(" 来获取数据和设置数据，配合 "),T("code",null,"onOpenChange"),j("，可以满足大部分的需求。")],-1)),_(a,{files:"%5B%22index.vue%22%2C%22drawer.vue%22%5D"},{"index.vue":V((()=>s[7]||(s[7]=[T("div",{class:"language-vue vp-adaptive-theme"},[T("button",{title:"Copy Code",class:"copy"}),T("span",{class:"lang"},"vue"),T("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[T("code",null,[T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenDrawer, VbenButton } "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '@vben/common-ui'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ExtraDrawer "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," './drawer.vue'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"drawerApi"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenDrawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6A737D","--shiki-dark":"#6A737D"}},"  // 连接抽离的组件")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  connectedComponent: ExtraDrawer,")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"function"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," open"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"setData"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    content: "),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'外部传递的数据 content'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    payload: "),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'外部传递的数据 payload'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},",")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  });")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"open"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"();")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"}")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," />")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," @"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"click"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"open"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">Open</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"VbenButton"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),"drawer.vue":V((()=>s[8]||(s[8]=[T("div",{class:"language-vue vp-adaptive-theme"},[T("button",{title:"Copy Code",class:"copy"}),T("span",{class:"lang"},"vue"),T("pre",{class:"shiki shiki-themes github-light github-dark vp-code",tabindex:"0"},[T("code",null,[T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," lang"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"ts"'),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," setup"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { ref } "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," 'vue'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"import"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," { useVbenDrawer } "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"from"),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}}," '@vben/common-ui'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},";")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," data"),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}}," ="),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," ref"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"();")]),j("\n"),T("span",{class:"line"}),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"const"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," ["),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"drawerApi"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"] "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," useVbenDrawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"({")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"  onCancel"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"close"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"();")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"  onConfirm"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"() {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    console."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"info"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},"'onConfirm'"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},");")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"  onOpenChange"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"("),T("span",{style:{"--shiki-light":"#E36209","--shiki-dark":"#FFAB70"}},"isOpen"),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},":"),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}}," boolean"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},") {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"    if"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," (isOpen) {")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"      data.value "),T("span",{style:{"--shiki-light":"#D73A49","--shiki-dark":"#F97583"}},"="),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}}," drawerApi."),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"getData"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}},"Record"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"string"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},", "),T("span",{style:{"--shiki-light":"#005CC5","--shiki-dark":"#79B8FF"}},"any"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">>();")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    }")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  },")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"});")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"script"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"<"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," title"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"数据共享示例"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"    <"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#6F42C1","--shiki-dark":"#B392F0"}}," class"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"="),T("span",{style:{"--shiki-light":"#032F62","--shiki-dark":"#9ECBFF"}},'"flex-col-center"'),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">外部传递数据： {{ data }}</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"div"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"  </"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"Drawer"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")]),j("\n"),T("span",{class:"line"},[T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},"</"),T("span",{style:{"--shiki-light":"#22863A","--shiki-dark":"#85E89D"}},"template"),T("span",{style:{"--shiki-light":"#24292E","--shiki-dark":"#E1E4E8"}},">")])])])],-1)]))),default:V((()=>[_(ri)])),_:1}),s[18]||(s[18]=Z('<div class="info custom-block"><p class="custom-block-title">注意</p><ul><li><code>VbenDrawer</code> 组件对与参数的处理优先级是 <code>slot</code> &gt; <code>props</code> &gt; <code>state</code>(通过api更新的状态以及useVbenDrawer参数)。如果你已经传入了 <code>slot</code> 或者 <code>props</code>，那么 <code>setState</code> 将不会生效，这种情况下你可以通过 <code>slot</code> 或者 <code>props</code> 来更新状态。</li><li>如果你使用到了 <code>connectedComponent</code> 参数，那么会存在 2 个<code>useVbenDrawer</code>, 此时，如果同时设置了相同的参数，那么以内部为准（也就是没有设置 connectedComponent 的代码）。比如 同时设置了 <code>onConfirm</code>，那么以内部的 <code>onConfirm</code> 为准。<code>onOpenChange</code>事件除外，内外都会触发。</li></ul></div><h2 id="api" tabindex="-1">API <a class="header-anchor" href="#api" aria-label="Permalink to &quot;API&quot;">​</a></h2><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// Drawer 为弹窗组件</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// drawerApi 为弹窗的方法</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">const</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> [</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">Drawer</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">drawerApi</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">] </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> useVbenDrawer</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">({</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 属性</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  // 事件</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span></code></pre></div><h3 id="props" tabindex="-1">Props <a class="header-anchor" href="#props" aria-label="Permalink to &quot;Props&quot;">​</a></h3><p>所有属性都可以传入 <code>useVbenDrawer</code> 的第一个参数中。</p><table tabindex="0"><thead><tr><th>属性名</th><th>描述</th><th>类型</th><th>默认值</th></tr></thead><tbody><tr><td>title</td><td>标题</td><td><code>string|slot</code></td><td>-</td></tr><tr><td>titleTooltip</td><td>标题提示信息</td><td><code>string|slot</code></td><td>-</td></tr><tr><td>description</td><td>描述信息</td><td><code>string|slot</code></td><td>-</td></tr><tr><td>isOpen</td><td>弹窗打开状态</td><td><code>boolean</code></td><td><code>false</code></td></tr><tr><td>loading</td><td>弹窗加载状态</td><td><code>boolean</code></td><td><code>false</code></td></tr><tr><td>closable</td><td>显示关闭按钮</td><td><code>boolean</code></td><td><code>true</code></td></tr><tr><td>modal</td><td>显示遮罩</td><td><code>boolean</code></td><td><code>true</code></td></tr><tr><td>header</td><td>显示header</td><td><code>boolean</code></td><td><code>true</code></td></tr><tr><td>footer</td><td>显示footer</td><td><code>boolean|slot</code></td><td><code>true</code></td></tr><tr><td>confirmLoading</td><td>确认按钮loading状态</td><td><code>boolean</code></td><td><code>false</code></td></tr><tr><td>closeOnClickModal</td><td>点击遮罩关闭弹窗</td><td><code>boolean</code></td><td><code>true</code></td></tr><tr><td>closeOnPressEscape</td><td>esc 关闭弹窗</td><td><code>boolean</code></td><td><code>true</code></td></tr><tr><td>confirmText</td><td>确认按钮文本</td><td><code>string|slot</code></td><td><code>确认</code></td></tr><tr><td>cancelText</td><td>取消按钮文本</td><td><code>string|slot</code></td><td><code>取消</code></td></tr><tr><td>showCancelButton</td><td>显示取消按钮</td><td><code>boolean</code></td><td><code>true</code></td></tr><tr><td>showConfirmButton</td><td>显示确认按钮文本</td><td><code>boolean</code></td><td><code>true</code></td></tr><tr><td>class</td><td>modal的class，宽度通过这个配置</td><td><code>string</code></td><td>-</td></tr><tr><td>contentClass</td><td>modal内容区域的class</td><td><code>string</code></td><td>-</td></tr><tr><td>footerClass</td><td>modal底部区域的class</td><td><code>string</code></td><td>-</td></tr><tr><td>headerClass</td><td>modal顶部区域的class</td><td><code>string</code></td><td>-</td></tr></tbody></table><h3 id="event" tabindex="-1">Event <a class="header-anchor" href="#event" aria-label="Permalink to &quot;Event&quot;">​</a></h3><p>以下事件，只有在 <code>useVbenDrawer({onCancel:()=&gt;{}})</code> 中传入才会生效。</p><table tabindex="0"><thead><tr><th>事件名</th><th>描述</th><th>类型</th></tr></thead><tbody><tr><td>onBeforeClose</td><td>关闭前触发，返回 <code>false</code>则禁止关闭</td><td><code>()=&gt;boolean</code></td></tr><tr><td>onCancel</td><td>点击取消按钮触发</td><td><code>()=&gt;void</code></td></tr><tr><td>onConfirm</td><td>点击确认按钮触发</td><td><code>()=&gt;void</code></td></tr><tr><td>onOpenChange</td><td>关闭或者打开弹窗时触发</td><td><code>(isOpen:boolean)=&gt;void</code></td></tr></tbody></table><h3 id="slots" tabindex="-1">Slots <a class="header-anchor" href="#slots" aria-label="Permalink to &quot;Slots&quot;">​</a></h3><p>除了上面的属性类型包含<code>slot</code>，还可以通过插槽来自定义弹窗的内容。</p><table tabindex="0"><thead><tr><th>插槽名</th><th>描述</th></tr></thead><tbody><tr><td>default</td><td>默认插槽 - 弹窗内容</td></tr><tr><td>prepend-footer</td><td>取消按钮左侧</td></tr><tr><td>append-footer</td><td>取消按钮右侧</td></tr></tbody></table><h3 id="modalapi" tabindex="-1">modalApi <a class="header-anchor" href="#modalapi" aria-label="Permalink to &quot;modalApi&quot;">​</a></h3><table tabindex="0"><thead><tr><th>事件名</th><th>描述</th><th>类型</th></tr></thead><tbody><tr><td>setState</td><td>动态设置弹窗状态属性</td><td><code>setState(props) | setState((prev)=&gt;(props))</code></td></tr><tr><td>open</td><td>打开弹窗</td><td><code>()=&gt;void</code></td></tr><tr><td>close</td><td>关闭弹窗</td><td><code>()=&gt;void</code></td></tr><tr><td>setData</td><td>设置共享数据</td><td><code>&lt;T&gt;(data:T)=&gt;void</code></td></tr><tr><td>getData</td><td>获取共享数据</td><td><code>&lt;T&gt;()=&gt;T</code></td></tr><tr><td>useStore</td><td>获取可响应式状态</td><td>-</td></tr></tbody></table>',14)),_(t),_(l)])}});export{pi as __pageData,gi as default};
