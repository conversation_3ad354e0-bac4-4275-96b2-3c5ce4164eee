var gl=Object.defineProperty,yl=Object.defineProperties;var wl=Object.getOwnPropertyDescriptors;var ct=Object.getOwnPropertySymbols;var na=Object.prototype.hasOwnProperty,sa=Object.prototype.propertyIsEnumerable;var oa=(l,o,t)=>o in l?gl(l,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[o]=t,pe=(l,o)=>{for(var t in o||(o={}))na.call(o,t)&&oa(l,t,o[t]);if(ct)for(var t of ct(o))sa.call(o,t)&&oa(l,t,o[t]);return l},Me=(l,o)=>yl(l,wl(o));var ke=(l,o)=>{var t={};for(var a in l)na.call(l,a)&&o.indexOf(a)<0&&(t[a]=l[a]);if(l!=null&&ct)for(var a of ct(l))o.indexOf(a)<0&&sa.call(l,a)&&(t[a]=l[a]);return t};var ne=(l,o,t)=>new Promise((a,s)=>{var n=i=>{try{u(t.next(i))}catch(h){s(h)}},r=i=>{try{u(t.throw(i))}catch(h){s(h)}},u=i=>i.done?a(i.value):Promise.resolve(i.value).then(n,r);u((t=t.apply(l,o)).next())});import{b8 as xl,g as ue,p as kl,aK as Sl,A as Xe,ch as _l,ci as Ml,x as Ee,cj as Cl,ck as $l,cl as Tl,cm as Vl,cn as Bl,co as Ll,cp as Pl,cq as zl,cr as El,cs as Il,ct as Ul,cu as Hl,cv as Al,cw as Wl,cx as Dl,d as Pe,bD as Je,cc as wt,cy as Lt,cz as xe,aI as $e,cA as Nl,cB as Ol,cC as Rl,cD as Kl,cE as Fl,cF as Gl,aR as qe,cG as jl,C as ql,h as Ye,u as et,b9 as Re,$ as b,bv as _e,cH as Xl,b2 as Yl,cI as ga,cJ as ya,c as wa,bC as Zl,cK as xa,s as ka,cg as rt,bR as tt,cL as Ql,cM as Jl,cN as eo,cO as to,cP as ao,cQ as lo,cR as oo,bI as no,cS as Sa,cT as so,ce as ro,aN as io,cU as uo,cV as co,cW as po,cX as fo,cf as xt,cY as Oe,bK as mo,cZ as ho}from"./bootstrap-DShsrVit.js";import{a4 as T,af as d,am as x,ae as L,aZ as A,a3 as e,aX as de,ag as k,ah as c,n as p,J as y,ac as ce,ai as Ce,aj as Ze,a7 as bt,O as F,bO as Pt,bP as bo,a5 as Ke,ar as oe,al as E,as as se,an as B,ao as _,F as G,ap as C,aq as ze,bI as vo,a$ as me,b0 as w,V as he,a1 as De,a6 as kt,bQ as go,M as _a,bR as pt,ak as Se,bS as yo,bl as wo,q as Ne,bA as zt,bT as Ma,bU as lt,bV as vt,bW as xo,bX as ft,W as it,bY as ko,Q as Ca,aw as Ie,bB as dt,bf as So,bZ as _o,b_ as Mo,bD as Co,a0 as U,b$ as ra,c0 as $o,ay as $a,c1 as ia,aG as To,av as Ta,c2 as Vo,c3 as Bo,b3 as gt,c4 as Lo,bb as da,x as St,az as Va,U as Ba,au as La,bv as Po,S as zo,c5 as Eo,bC as Io,bJ as Uo,ax as Pa,aP as ua,N as Ho,a2 as Ao}from"../jse/index-index-BMh_AyeW.js";import{_ as ot}from"./avatar.vue_vue_type_script_setup_true_lang-CyCVfWwM.js";import{B as Wo}from"./bell-D0icksiV.js";import{a as Et,_ as Do,C as No,E as Oo,b as Ro}from"./popover.vue_vue_type_script_setup_true_lang-CFXFI0jw.js";import{u as nt}from"./use-modal-B0smF4x0.js";import{_ as It,a as Ut,b as Ht,c as yt,d as Ko,e as za,f as Ea,M as Fo,S as Go,g as jo,h as qo,i as Xo}from"./theme-toggle.vue_vue_type_script_setup_true_lang-DzmrFWHD.js";import{_ as Be,M as Yo}from"./icon.vue_vue_type_script_setup_true_lang-BK5optdP.js";import{a as ut,u as Ia}from"./use-tabs-C64_EnSy.js";import{X as _t}from"./x-B-ntYT_e.js";import{u as Ua}from"./use-drawer-Qcdpj8Bl.js";import{_ as Zo,a as Qo,b as Jo}from"./TabsList.vue_vue_type_script_setup_true_lang-DfMjVjnv.js";import{S as en}from"./sun-ChDfqwZ7.js";import{R as At}from"./rotate-cw-B0JNpqtv.js";function Wt(l,o){for(const t of l){if(t.path===o)return t;const a=t.children&&Wt(t.children,o);if(a)return a}return null}function mt(l,o){var n;const t=Wt(l,o),a=(n=t==null?void 0:t.parents)==null?void 0:n[0],s=a?l.find(r=>r.path===a):void 0;return{findMenu:t,rootMenu:s,rootMenuPath:a}}const Dt=xl("core-lock",{actions:{lockScreen(l){this.isLockScreen=!0,this.lockScreenPassword=l},unlockScreen(){this.isLockScreen=!1,this.lockScreenPassword=void 0}},persist:{pick:["isLockScreen","lockScreenPassword"]},state:()=>({isLockScreen:!1,lockScreenPassword:void 0})});const tn=ue("ArrowDownIcon",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);const an=ue("ArrowLeftToLineIcon",[["path",{d:"M3 19V5",key:"rwsyhb"}],["path",{d:"m13 6-6 6 6 6",key:"1yhaz7"}],["path",{d:"M7 12h14",key:"uoisry"}]]);const ln=ue("ArrowRightLeftIcon",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]]);const on=ue("ArrowRightToLineIcon",[["path",{d:"M17 12H3",key:"8awo09"}],["path",{d:"m11 18 6-6-6-6",key:"8c2y43"}],["path",{d:"M21 5v14",key:"nzette"}]]);const nn=ue("ArrowUpToLineIcon",[["path",{d:"M5 3h14",key:"7usisc"}],["path",{d:"m18 13-6-6-6 6",key:"1kf1n9"}],["path",{d:"M12 7v14",key:"1akyts"}]]);const sn=ue("ArrowUpIcon",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);const rn=ue("CopyIcon",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);const dn=ue("CornerDownLeftIcon",[["polyline",{points:"9 10 4 15 9 20",key:"r3jprv"}],["path",{d:"M20 4v7a4 4 0 0 1-4 4H4",key:"6o5b7l"}]]);const un=ue("ExternalLinkIcon",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);const cn=ue("FoldHorizontalIcon",[["path",{d:"M2 12h6",key:"1wqiqv"}],["path",{d:"M22 12h-6",key:"1eg9hc"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 8v2",key:"1woqiv"}],["path",{d:"M12 14v2",key:"8jcxud"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m19 9-3 3 3 3",key:"12ol22"}],["path",{d:"m5 15 3-3-3-3",key:"1kdhjc"}]]);const Ha=ue("FullscreenIcon",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["rect",{width:"10",height:"8",x:"7",y:"8",rx:"1",key:"vys8me"}]]);const Aa=ue("LockKeyholeIcon",[["circle",{cx:"12",cy:"16",r:"1",key:"1au0dj"}],["rect",{x:"3",y:"10",width:"18",height:"12",rx:"2",key:"6s8ecr"}],["path",{d:"M7 10V7a5 5 0 0 1 10 0v3",key:"1pqi11"}]]);const pn=ue("LogOutIcon",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);const fn=ue("MailCheckIcon",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]]);const mn=ue("MaximizeIcon",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);const Wa=ue("Minimize2Icon",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);const hn=ue("MinimizeIcon",[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]]);const bn=ue("MinusIcon",[["path",{d:"M5 12h14",key:"1ays0h"}]]);const Da=ue("PinOffIcon",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M15 9.34V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H7.89",key:"znwnzq"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M9 9v1.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h11",key:"c9qhm2"}]]);const Mt=ue("PinIcon",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]]);const vn=ue("PlusIcon",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);const gn=ue("SearchXIcon",[["path",{d:"m13.5 8.5-5 5",key:"1cs55j"}],["path",{d:"m8.5 8.5 5 5",key:"a8mexj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);const ca=ue("SearchIcon",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);const Na=ue("SettingsIcon",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);const yn=ue("UserRoundPenIcon",[["path",{d:"M2 21a8 8 0 0 1 10.821-7.487",key:"1c8h7z"}],["path",{d:"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1817ys"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}]]),wn=kl("inline-flex items-center rounded-md border border-border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-accent hover:bg-accent text-primary-foreground shadow",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive-hover",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}}),xn=T({__name:"Badge",props:{class:{},variant:{}},setup(l){const o=l;return(t,a)=>(d(),x("div",{class:A(e(de)(e(wn)({variant:t.variant}),o.class))},[L(t.$slots,"default")],2))}}),kn=T({__name:"Breadcrumb",props:{class:{}},setup(l){const o=l;return(t,a)=>(d(),x("nav",{class:A(o.class),"aria-label":"breadcrumb",role:"navigation"},[L(t.$slots,"default")],2))}}),Sn=T({__name:"BreadcrumbItem",props:{class:{}},setup(l){const o=l;return(t,a)=>(d(),x("li",{class:A(e(de)("hover:text-foreground inline-flex items-center gap-1.5",o.class))},[L(t.$slots,"default")],2))}}),_n=T({__name:"BreadcrumbLink",props:{class:{},asChild:{type:Boolean},as:{default:"a"}},setup(l){const o=l;return(t,a)=>(d(),k(e(Sl),{as:t.as,"as-child":t.asChild,class:A(e(de)("hover:text-foreground transition-colors",o.class))},{default:c(()=>[L(t.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),Mn=T({__name:"BreadcrumbList",props:{class:{}},setup(l){const o=l;return(t,a)=>(d(),x("ol",{class:A(e(de)("text-muted-foreground flex flex-wrap items-center gap-1.5 break-words text-sm sm:gap-2.5",o.class))},[L(t.$slots,"default")],2))}}),Cn=T({__name:"BreadcrumbPage",props:{class:{}},setup(l){const o=l;return(t,a)=>(d(),x("span",{class:A(e(de)("text-foreground font-normal",o.class)),"aria-current":"page","aria-disabled":"true",role:"link"},[L(t.$slots,"default")],2))}}),$n=T({__name:"BreadcrumbSeparator",props:{class:{}},setup(l){const o=l;return(t,a)=>(d(),x("li",{class:A(e(de)("[&>svg]:size-3.5",o.class)),"aria-hidden":"true",role:"presentation"},[L(t.$slots,"default",{},()=>[p(e(Et))])],2))}}),Tn=T({__name:"DropdownMenuLabel",props:{class:{},inset:{type:Boolean},asChild:{type:Boolean},as:{}},setup(l){const o=l,t=y(()=>{const r=o,{class:s}=r;return ke(r,["class"])}),a=Xe(t);return(s,n)=>(d(),k(e(_l),ce(e(a),{class:e(de)("px-2 py-1.5 text-sm font-semibold",s.inset&&"pl-8",o.class)}),{default:c(()=>[L(s.$slots,"default")]),_:3},16,["class"]))}}),Bt=T({__name:"DropdownMenuSeparator",props:{class:{},asChild:{type:Boolean},as:{}},setup(l){const o=l,t=y(()=>{const n=o,{class:a}=n;return ke(n,["class"])});return(a,s)=>(d(),k(e(Ml),ce(t.value,{class:e(de)("bg-border -mx-1 my-1 h-px",o.class)}),null,16,["class"]))}}),pa=T({__name:"DropdownMenuShortcut",props:{class:{}},setup(l){const o=l;return(t,a)=>(d(),x("span",{class:A(e(de)("ml-auto text-xs tracking-widest opacity-60",o.class))},[L(t.$slots,"default")],2))}}),Vn=T({__name:"HoverCard",props:{defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(l,{emit:o}){const s=Ee(l,o);return(n,r)=>(d(),k(e(Cl),Ce(Ze(e(s))),{default:c(()=>[L(n.$slots,"default")]),_:3},16))}}),Bn=T({__name:"HoverCardContent",props:{class:{},forceMount:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},setup(l){const o=l,t=y(()=>{const r=o,{class:s}=r;return ke(r,["class"])}),a=Xe(t);return(s,n)=>(d(),k(e(Tl),null,{default:c(()=>[p(e($l),ce(e(a),{class:e(de)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-[1000] w-64 rounded-md border p-4 shadow-md outline-none",o.class)}),{default:c(()=>[L(s.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),Ln=T({__name:"HoverCardTrigger",props:{asChild:{type:Boolean},as:{}},setup(l){const o=l;return(t,a)=>(d(),k(e(Vl),Ce(Ze(o)),{default:c(()=>[L(t.$slots,"default")]),_:3},16))}}),Pn=T({__name:"NumberField",props:{class:{},defaultValue:{},modelValue:{},min:{},max:{},step:{},formatOptions:{},locale:{},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},asChild:{type:Boolean},as:{}},emits:["update:modelValue"],setup(l,{emit:o}){const t=l,a=o,s=y(()=>{const i=t,{class:r}=i;return ke(i,["class"])}),n=Ee(s,a);return(r,u)=>(d(),k(e(Bl),ce(e(n),{class:e(de)("grid gap-1.5",t.class)}),{default:c(()=>[L(r.$slots,"default")]),_:3},16,["class"]))}}),zn=T({__name:"NumberFieldContent",props:{class:{}},setup(l){const o=l;return(t,a)=>(d(),x("div",{class:A(e(de)("relative [&>[data-slot=input]]:has-[[data-slot=decrement]]:pl-5 [&>[data-slot=input]]:has-[[data-slot=increment]]:pr-5",o.class))},[L(t.$slots,"default")],2))}}),En=T({__name:"NumberFieldDecrement",props:{class:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(l){const o=l,t=y(()=>{const r=o,{class:s}=r;return ke(r,["class"])}),a=Xe(t);return(s,n)=>(d(),k(e(Ll),ce({"data-slot":"decrement"},e(a),{class:e(de)("absolute left-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",o.class)}),{default:c(()=>[L(s.$slots,"default",{},()=>[p(e(bn),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),In=T({__name:"NumberFieldIncrement",props:{class:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(l){const o=l,t=y(()=>{const r=o,{class:s}=r;return ke(r,["class"])}),a=Xe(t);return(s,n)=>(d(),k(e(Pl),ce({"data-slot":"increment"},e(a),{class:e(de)("absolute right-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",o.class)}),{default:c(()=>[L(s.$slots,"default",{},()=>[p(e(vn),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),Un=T({__name:"NumberFieldInput",setup(l){return(o,t)=>(d(),k(e(zl),{class:A(e(de)("border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent py-1 text-center text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50")),"data-slot":"input"},null,8,["class"]))}}),Oa=T({__name:"ScrollBar",props:{class:{},orientation:{default:"vertical"},forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(l){const o=l,t=y(()=>{const n=o,{class:a}=n;return ke(n,["class"])});return(a,s)=>(d(),k(e(Il),ce(t.value,{class:e(de)("flex touch-none select-none transition-colors",a.orientation==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-px",a.orientation==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-px",o.class)}),{default:c(()=>[p(e(El),{class:"bg-border relative flex-1 rounded-full"})]),_:1},16,["class"]))}}),Hn=T({__name:"ScrollArea",props:{class:{},onScroll:{type:Function,default:()=>{}},viewportProps:{},type:{},dir:{},scrollHideDelay:{},asChild:{type:Boolean},as:{}},setup(l){const o=l,t=y(()=>{const n=o,{class:a}=n;return ke(n,["class"])});return(a,s)=>(d(),k(e(Al),ce(t.value,{class:e(de)("relative overflow-hidden",o.class)}),{default:c(()=>[p(e(Ul),{"as-child":"",class:"h-full w-full rounded-[inherit]",onScroll:a.onScroll},{default:c(()=>[L(a.$slots,"default")]),_:3},8,["onScroll"]),p(Oa),p(e(Hl))]),_:3},16,["class"]))}}),An=T({__name:"Switch",props:{class:{},defaultChecked:{type:Boolean},checked:{type:Boolean},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},value:{},asChild:{type:Boolean},as:{}},emits:["update:checked"],setup(l,{emit:o}){const t=l,a=o,s=y(()=>{const i=t,{class:r}=i;return ke(i,["class"])}),n=Ee(s,a);return(r,u)=>(d(),k(e(Dl),ce(e(n),{class:e(de)("focus-visible:ring-ring focus-visible:ring-offset-background data-[state=checked]:bg-primary data-[state=unchecked]:bg-input peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t.class)}),{default:c(()=>[p(e(Wl),{class:A(e(de)("bg-background pointer-events-none block h-4 w-4 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"))},null,8,["class"])]),_:1},16,["class"]))}}),Wn=l=>{const o=bt(),t=bt(),a=F(!1),s=()=>{var u;o.value&&(a.value=o.value.scrollTop>=((u=l==null?void 0:l.visibilityHeight)!=null?u:0))},n=()=>{var u;(u=o.value)==null||u.scrollTo({behavior:"smooth",top:0})},r=Pt(s,300,!0);return bo(t,"scroll",r),Ke(()=>{var u;if(t.value=document,o.value=document.documentElement,l.target){if(o.value=(u=document.querySelector(l.target))!=null?u:void 0,!o.value)throw new Error(`target does not exist: ${l.target}`);t.value=o.value}s()}),{handleClick:n,visible:a}},Dn=T({name:"BackTop",__name:"back-top",props:{bottom:{default:20},isGroup:{type:Boolean,default:!1},right:{default:24},target:{default:""},visibilityHeight:{default:200}},setup(l){const o=l,t=y(()=>({bottom:`${o.bottom}px`,right:`${o.right}px`})),{handleClick:a,visible:s}=Wn(o);return(n,r)=>(d(),k(Je,{name:"fade-down"},{default:c(()=>[e(s)?(d(),k(e(Pe),{key:0,style:oe(t.value),class:"dark:bg-accent dark:hover:bg-heavy bg-background hover:bg-heavy data shadow-float fixed bottom-10 z-[1000] size-10 rounded-full duration-500",size:"icon",variant:"icon",onClick:e(a)},{default:c(()=>[p(e(nn),{class:"size-4"})]),_:1},8,["style","onClick"])):E("",!0)]),_:1}))}}),Nn={key:0},On={class:"flex-center"},Rn={class:"flex-center"},Kn=T({name:"Breadcrumb",__name:"breadcrumb",props:{breadcrumbs:{},showIcon:{type:Boolean,default:!1},styleType:{}},emits:["select"],setup(l,{emit:o}){const t=o;function a(s){s&&t("select",s)}return(s,n)=>(d(),k(e(kn),null,{default:c(()=>[p(e(Mn),null,{default:c(()=>[p(wt,{name:"breadcrumb-transition"},{default:c(()=>[(d(!0),x(G,null,se(s.breadcrumbs,(r,u)=>(d(),k(e(Sn),{key:`${r.path}-${r.title}-${u}`},{default:c(()=>{var i,h;return[(h=(i=r.items)==null?void 0:i.length)!=null&&h?(d(),x("div",Nn,[p(e(It),null,{default:c(()=>[p(e(Ut),{class:"flex items-center gap-1"},{default:c(()=>[s.showIcon?(d(),k(e(Be),{key:0,icon:r.icon,class:"size-5"},null,8,["icon"])):E("",!0),B(" "+_(r.title)+" ",1),p(e(Lt),{class:"size-4"})]),_:2},1024),p(e(Ht),{align:"start"},{default:c(()=>[(d(!0),x(G,null,se(r.items,m=>(d(),k(e(yt),{key:`sub-${m.path}`,onClick:xe(f=>a(m.path),["stop"])},{default:c(()=>[B(_(m.title),1)]),_:2},1032,["onClick"]))),128))]),_:2},1024)]),_:2},1024)])):u!==s.breadcrumbs.length-1?(d(),k(e(_n),{key:1,href:"javascript:void 0",onClick:xe(m=>a(r.path),["stop"])},{default:c(()=>[C("div",On,[s.showIcon?(d(),k(e(Be),{key:0,class:A([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):E("",!0),B(" "+_(r.title),1)])]),_:2},1032,["onClick"])):(d(),k(e(Cn),{key:2},{default:c(()=>[C("div",Rn,[s.showIcon?(d(),k(e(Be),{key:0,class:A([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):E("",!0),B(" "+_(r.title),1)])]),_:2},1024)),u<s.breadcrumbs.length-1&&!r.isHome?(d(),k(e($n),{key:3})):E("",!0)]}),_:2},1024))),128))]),_:1})]),_:1})]),_:1}))}}),Fn={class:"flex"},Gn=["onClick"],jn={class:"flex-center z-10 h-full"},qn=T({name:"Breadcrumb",__name:"breadcrumb-background",props:{breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(l,{emit:o}){const t=o;function a(s,n){!n||s===l.breadcrumbs.length-1||t("select",n)}return(s,n)=>(d(),x("ul",Fn,[p(wt,{name:"breadcrumb-transition"},{default:c(()=>[(d(!0),x(G,null,se(s.breadcrumbs,(r,u)=>(d(),x("li",{key:`${r.path}-${r.title}-${u}`},[C("a",{href:"javascript:void 0",onClick:xe(i=>a(u,r.path),["stop"])},[C("span",jn,[s.showIcon?(d(),k(e(Be),{key:0,icon:r.icon,class:"mr-1 size-4 flex-shrink-0"},null,8,["icon"])):E("",!0),C("span",{class:A({"text-foreground font-normal":u===s.breadcrumbs.length-1})},_(r.title),3)])],8,Gn)]))),128))]),_:1})]))}}),Xn=$e(qn,[["__scopeId","data-v-8cb920cd"]]),Yn=T({__name:"breadcrumb-view",props:{class:{},breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(l,{emit:o}){const s=Ee(l,o);return(n,r)=>(d(),x(G,null,[n.styleType==="normal"?(d(),k(Kn,Ce(ce({key:0},e(s))),null,16)):E("",!0),n.styleType==="background"?(d(),k(Xn,Ce(ce({key:1},e(s))),null,16)):E("",!0)],64))}}),Zn=T({__name:"ContextMenu",props:{dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(l,{emit:o}){const s=Ee(l,o);return(n,r)=>(d(),k(e(Nl),Ce(Ze(e(s))),{default:c(()=>[L(n.$slots,"default")]),_:3},16))}}),Qn=T({__name:"ContextMenuContent",props:{class:{},forceMount:{type:Boolean},loop:{type:Boolean},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},sticky:{},hideWhenDetached:{type:Boolean},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(l,{emit:o}){const t=l,a=o,s=y(()=>{const i=t,{class:r}=i;return ke(i,["class"])}),n=Ee(s,a);return(r,u)=>(d(),k(e(Rl),null,{default:c(()=>[p(e(Ol),ce(e(n),{class:e(de)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-[1000] min-w-32 overflow-hidden rounded-md border p-1 shadow-md",t.class)}),{default:c(()=>[L(r.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),Jn=T({__name:"ContextMenuItem",props:{class:{},inset:{type:Boolean},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(l,{emit:o}){const t=l,a=o,s=y(()=>{const i=t,{class:r}=i;return ke(i,["class"])}),n=Ee(s,a);return(r,u)=>(d(),k(e(Kl),ce(e(n),{class:e(de)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r.inset&&"pl-8",t.class)}),{default:c(()=>[L(r.$slots,"default")]),_:3},16,["class"]))}}),es=T({__name:"ContextMenuSeparator",props:{class:{},asChild:{type:Boolean},as:{}},setup(l){const o=l,t=y(()=>{const n=o,{class:a}=n;return ke(n,["class"])});return(a,s)=>(d(),k(e(Fl),ce(t.value,{class:e(de)("bg-border -mx-1 my-1 h-px",o.class)}),null,16,["class"]))}}),ts=T({__name:"ContextMenuShortcut",props:{class:{}},setup(l){const o=l;return(t,a)=>(d(),x("span",{class:A(e(de)("text-muted-foreground ml-auto text-xs tracking-widest",o.class))},[L(t.$slots,"default")],2))}}),as=T({__name:"ContextMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(l){const t=Xe(l);return(a,s)=>(d(),k(e(Gl),Ce(Ze(e(t))),{default:c(()=>[L(a.$slots,"default")]),_:3},16))}}),Ra=T({__name:"context-menu",props:{class:{},contentClass:{},contentProps:{},handlerData:{},itemClass:{},menus:{type:Function},dir:{},modal:{type:Boolean}},emits:["update:open"],setup(l,{emit:o}){const t=l,a=o,s=y(()=>{const v=t,{class:i,contentClass:h,contentProps:m,itemClass:f}=v;return ke(v,["class","contentClass","contentProps","itemClass"])}),n=Ee(s,a),r=y(()=>{var i;return(i=t.menus)==null?void 0:i.call(t,t.handlerData)});function u(i){var h;i.disabled||(h=i==null?void 0:i.handler)==null||h.call(i,t.handlerData)}return(i,h)=>(d(),k(e(Zn),Ce(Ze(e(n))),{default:c(()=>[p(e(as),{"as-child":""},{default:c(()=>[L(i.$slots,"default")]),_:3}),p(e(Qn),ce({class:i.contentClass},i.contentProps,{class:"side-content z-[1000]"}),{default:c(()=>[(d(!0),x(G,null,se(r.value,m=>(d(),x(G,{key:m.key},[p(e(Jn),{class:A([i.itemClass,"cursor-pointer"]),disabled:m.disabled,inset:m.inset||!m.icon,onClick:f=>u(m)},{default:c(()=>[m.icon?(d(),k(ze(m.icon),{key:0,class:"mr-2 size-4 text-lg"})):E("",!0),B(" "+_(m.text)+" ",1),m.shortcut?(d(),k(e(ts),{key:1},{default:c(()=>[B(_(m.shortcut),1)]),_:2},1024)):E("",!0)]),_:2},1032,["class","disabled","inset","onClick"]),m.separator?(d(),k(e(es),{key:0})):E("",!0)],64))),128))]),_:1},16,["class"])]),_:3},16))}}),ls=T({name:"DropdownMenu",__name:"dropdown-menu",props:{menus:{}},setup(l){const o=l;function t(a){var s;a.disabled||(s=a==null?void 0:a.handler)==null||s.call(a,o)}return(a,s)=>(d(),k(e(It),null,{default:c(()=>[p(e(Ut),{class:"flex h-full items-center gap-1"},{default:c(()=>[L(a.$slots,"default")]),_:3}),p(e(Ht),{align:"start"},{default:c(()=>[p(e(Ko),null,{default:c(()=>[(d(!0),x(G,null,se(a.menus,n=>(d(),x(G,{key:n.value},[p(e(yt),{disabled:n.disabled,class:"data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground text-foreground/80 mb-1 cursor-pointer",onClick:r=>t(n)},{default:c(()=>[n.icon?(d(),k(ze(n.icon),{key:0,class:"mr-2 size-4"})):E("",!0),B(" "+_(n.label),1)]),_:2},1032,["disabled","onClick"]),n.separator?(d(),k(e(Bt),{key:0,class:"bg-border"})):E("",!0)],64))),128))]),_:1})]),_:1})]),_:3}))}}),os=T({name:"FullScreen",__name:"full-screen",setup(l){const{isFullscreen:o,toggle:t}=vo();return o.value=!!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement),(a,s)=>(d(),k(e(qe),{onClick:e(t)},{default:c(()=>[e(o)?(d(),k(e(hn),{key:0,class:"text-foreground size-4"})):(d(),k(e(mn),{key:1,class:"text-foreground size-4"}))]),_:1},8,["onClick"]))}}),ns={class:"h-full cursor-pointer"},ss=T({__name:"hover-card",props:{class:{},contentClass:{},contentProps:{},defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(l,{emit:o}){const t=l,a=o,s=y(()=>{const m=t,{class:r,contentClass:u,contentProps:i}=m;return ke(m,["class","contentClass","contentProps"])}),n=Ee(s,a);return(r,u)=>(d(),k(e(Vn),Ce(Ze(e(n))),{default:c(()=>[p(e(Ln),{"as-child":"",class:"h-full"},{default:c(()=>[C("div",ns,[L(r.$slots,"trigger")])]),_:3}),p(e(Bn),ce({class:r.contentClass},r.contentProps,{class:"side-content z-[1000]"}),{default:c(()=>[L(r.$slots,"default")]),_:3},16,["class"])]),_:3},16))}}),rs=["href"],is={key:1,class:"text-foreground truncate text-nowrap font-semibold"},fa=T({name:"VbenLogo",__name:"logo",props:{collapsed:{type:Boolean,default:!1},href:{default:"javascript:void 0"},logoSize:{default:32},src:{default:""},text:{},theme:{default:"light"}},setup(l){return(o,t)=>(d(),x("div",{class:A([o.theme,"flex h-full items-center text-lg"])},[C("a",{class:A([o.$attrs.class,"flex h-full items-center gap-2 overflow-hidden px-3 text-lg leading-normal transition-all duration-500"]),href:o.href},[o.src?(d(),k(e(ot),{key:0,alt:o.text,src:o.src,class:"relative w-8 rounded-none bg-transparent"},null,8,["alt","src"])):E("",!0),o.collapsed?E("",!0):(d(),x("span",is,_(o.text),1))],10,rs)],2))}}),ds=T({__name:"scrollbar",props:{class:{default:""},horizontal:{type:Boolean,default:!1},scrollBarClass:{},shadow:{type:Boolean,default:!1},shadowBorder:{type:Boolean,default:!1},shadowBottom:{type:Boolean,default:!0},shadowLeft:{type:Boolean,default:!1},shadowRight:{type:Boolean,default:!1},shadowTop:{type:Boolean,default:!0}},emits:["scrollAt"],setup(l,{emit:o}){const t=l,a=o,s=F(!0),n=F(!1),r=F(!1),u=F(!0),i=y(()=>t.shadow&&t.shadowTop),h=y(()=>t.shadow&&t.shadowBottom),m=y(()=>t.shadow&&t.shadowLeft),f=y(()=>t.shadow&&t.shadowRight),g=y(()=>({"both-shadow":!u.value&&!n.value&&m.value&&f.value,"left-shadow":!u.value&&m.value,"right-shadow":!n.value&&f.value}));function v(V){var Y,Z,P,R,j,ie;const S=V.target,I=(Y=S==null?void 0:S.scrollTop)!=null?Y:0,N=(Z=S==null?void 0:S.scrollLeft)!=null?Z:0,K=(P=S==null?void 0:S.offsetHeight)!=null?P:0,X=(R=S==null?void 0:S.offsetWidth)!=null?R:0,H=(j=S==null?void 0:S.scrollHeight)!=null?j:0,D=(ie=S==null?void 0:S.scrollWidth)!=null?ie:0;s.value=I<=0,u.value=N<=0,r.value=I+K>=H,n.value=N+X>=D,a("scrollAt",{bottom:r.value,left:u.value,right:n.value,top:s.value})}return(V,S)=>(d(),k(e(Hn),{class:A([[e(de)(t.class),g.value],"vben-scrollbar relative"]),"on-scroll":v},{default:c(()=>[i.value?(d(),x("div",{key:0,class:A([{"opacity-100":!s.value,"border-border border-t":V.shadowBorder&&!s.value},"scrollbar-top-shadow pointer-events-none absolute top-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):E("",!0),L(V.$slots,"default",{},void 0,!0),h.value?(d(),x("div",{key:1,class:A([{"opacity-100":!s.value&&!r.value,"border-border border-b":V.shadowBorder&&!s.value&&!r.value},"scrollbar-bottom-shadow pointer-events-none absolute bottom-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):E("",!0),V.horizontal?(d(),k(e(Oa),{key:2,class:A(V.scrollBarClass),orientation:"horizontal"},null,8,["class"])):E("",!0)]),_:3},8,["class"]))}}),st=$e(ds,[["__scopeId","data-v-08015186"]]),us={class:"bg-background text-foreground inline-flex h-full w-full items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"},cs=T({__name:"tabs-indicator",props:{class:{},asChild:{type:Boolean},as:{}},setup(l){const o=l,t=y(()=>{const r=o,{class:s}=r;return ke(r,["class"])}),a=Xe(t);return(s,n)=>(d(),k(e(jl),ce(e(a),{class:e(de)("absolute bottom-0 left-0 z-10 h-full w-1/2 translate-x-[--radix-tabs-indicator-position] rounded-full px-0 py-1 pr-1 transition-[width,transform] duration-300",o.class)}),{default:c(()=>[C("div",us,[L(s.$slots,"default")])]),_:3},16,["class"]))}}),ps=T({__name:"segmented",props:me({defaultValue:{default:""},tabs:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=l,t=w(l,"modelValue"),a=y(()=>{var r;return o.defaultValue||((r=o.tabs[0])==null?void 0:r.value)}),s=y(()=>({"grid-template-columns":`repeat(${o.tabs.length}, minmax(0, 1fr))`})),n=y(()=>({width:`${(100/o.tabs.length).toFixed(0)}%`}));return(r,u)=>(d(),k(e(Jo),{modelValue:t.value,"onUpdate:modelValue":u[0]||(u[0]=i=>t.value=i),"default-value":a.value},{default:c(()=>[p(e(Zo),{style:oe(s.value),class:"bg-accent relative grid w-full"},{default:c(()=>[p(cs,{style:oe(n.value)},null,8,["style"]),(d(!0),x(G,null,se(r.tabs,i=>(d(),k(e(ql),{key:i.value,value:i.value,class:"z-20 inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium disabled:pointer-events-none disabled:opacity-50"},{default:c(()=>[B(_(i.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["style"]),(d(!0),x(G,null,se(r.tabs,i=>(d(),k(e(Qo),{key:i.value,value:i.value},{default:c(()=>[L(r.$slots,i.value)]),_:2},1032,["value"]))),128))]),_:3},8,["modelValue","default-value"]))}}),fs=T({name:"VbenSpinner",__name:"spinner",props:{class:{},minLoadingTime:{default:50},spinning:{type:Boolean}},setup(l){const o=l,t=F(!1),a=F(!0),s=F();he(()=>o.spinning,r=>{if(!r){t.value=!1,clearTimeout(s.value);return}s.value=setTimeout(()=>{t.value=!0,t.value&&(a.value=!0)},o.minLoadingTime)},{immediate:!0});function n(){t.value||(a.value=!1)}return(r,u)=>(d(),x("div",{class:A(e(de)("flex-center z-100 bg-overlay-content absolute left-0 top-0 size-full backdrop-blur-sm transition-all duration-500",{"invisible opacity-0":!t.value},o.class)),onTransitionend:n},u[0]||(u[0]=[C("div",{class:"loader before:bg-primary/50 after:bg-primary relative size-12 before:absolute before:left-0 before:top-[60px] before:h-[5px] before:w-12 before:rounded-[50%] before:content-[''] after:absolute after:left-0 after:top-0 after:h-full after:w-full after:rounded after:content-['']"},null,-1)]),34))}}),Ka=$e(fs,[["__scopeId","data-v-d370b2ef"]]);function Fa(){const{contentIsMaximize:l}=Ye();function o(){const t=l.value;De({header:{hidden:!t},sidebar:{hidden:!t}})}return{contentIsMaximize:l,toggleMaximize:o}}function Ga(){const l=et(),o=ut();function t(){return ne(this,null,function*(){yield o.refresh(l)})}return{refresh:t}}const ms=T({__name:"breadcrumb",props:{hideWhenOnlyOne:{type:Boolean},showHome:{type:Boolean,default:!1},showIcon:{type:Boolean,default:!1},type:{default:"normal"}},setup(l){const o=l,t=Re(),a=et(),s=y(()=>{const r=t.matched,u=[];for(const i of r){const{meta:h,path:m}=i,{hideChildrenInMenu:f,hideInBreadcrumb:g,icon:v,name:V,title:S}=h||{};g||f||!m||u.push({icon:v,path:m||t.path,title:S?b(S||V):""})}return o.showHome&&u.unshift({icon:"mdi:home-outline",isHome:!0,path:"/"}),o.hideWhenOnlyOne&&u.length===1?[]:u});function n(r){a.push(r)}return(r,u)=>(d(),k(e(Yn),{breadcrumbs:s.value,"show-icon":r.showIcon,"style-type":r.type,class:"ml-2",onSelect:n},null,8,["breadcrumbs","show-icon","style-type"]))}}),hs=T({name:"CheckUpdates",__name:"check-updates",props:{checkUpdatesInterval:{default:1},checkUpdateUrl:{default:"/"}},setup(l){const o=l;let t=!1;const a=F(""),s=F(""),n=F(),[r,u]=nt({closable:!1,closeOnPressEscape:!1,closeOnClickModal:!1,onConfirm(){s.value=a.value,window.location.reload()}});function i(){return ne(this,null,function*(){try{if(location.hostname==="localhost"||location.hostname==="127.0.0.1")return null;const V=yield fetch(o.checkUpdateUrl,{cache:"no-cache",method:"HEAD"});return V.headers.get("etag")||V.headers.get("last-modified")}catch(V){return console.error("Failed to fetch version tag"),null}})}function h(){return ne(this,null,function*(){const V=yield i();if(V){if(!s.value){s.value=V;return}s.value!==V&&V&&(clearInterval(n.value),m(V))}})}function m(V){a.value=V,u.open()}function f(){o.checkUpdatesInterval<=0||(n.value=setInterval(h,o.checkUpdatesInterval*60*1e3))}function g(){document.hidden?v():t||(t=!0,h().finally(()=>{t=!1,f()}))}function v(){clearInterval(n.value)}return Ke(()=>{document.addEventListener("visibilitychange",g)}),kt(()=>{v(),document.removeEventListener("visibilitychange",g)}),(V,S)=>(d(),k(e(r),{"cancel-text":e(b)("common.cancel"),"confirm-text":e(b)("common.refresh"),"fullscreen-button":!1,title:e(b)("ui.widgets.checkUpdatesTitle"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[B(_(e(b)("ui.widgets.checkUpdatesDescription")),1)]),_:1},8,["cancel-text","confirm-text","title"]))}}),bs={class:"!flex h-full justify-center px-2 sm:max-h-[450px]"},vs={key:0,class:"text-muted-foreground text-center"},gs={class:"mb-10 mt-6 text-xs"},ys={class:"text-foreground text-sm font-medium"},ws={key:1,class:"text-muted-foreground text-center"},xs={class:"my-10 text-xs"},ks={class:"w-full"},Ss={key:0,class:"text-muted-foreground mb-2 text-xs"},_s=["data-index","data-search-item"],Ms={class:"flex-1"},Cs=["onClick"],$s=T({name:"SearchPanel",__name:"search-panel",props:{keyword:{default:""},menus:{default:()=>[]}},emits:["close"],setup(l,{emit:o}){const t=l,a=o,s=et(),n=go(`__search-history-${location.hostname}__`,[]),r=F(-1),u=bt([]),i=F([]),h=Pt(m,200);function m(D){if(D=D.trim(),!D){i.value=[];return}const Y=H(D),Z=[];wo(u.value,P=>{var R;Y.test((R=P.name)==null?void 0:R.toLowerCase())&&Z.push(P)}),i.value=Z,Z.length>0&&(r.value=0),r.value=0}function f(){const D=document.querySelector(`[data-search-item="${r.value}"]`);D&&D.scrollIntoView({block:"nearest"})}function g(){return ne(this,null,function*(){if(i.value.length===0)return;const D=i.value,Y=r.value;if(D.length===0||Y<0)return;const Z=D[Y];Z&&(n.value.push(Z),S(),yield Ne(),zt(Z.path)?window.open(Z.path,"_blank"):s.push({path:Z.path,replace:!0}))})}function v(){i.value.length!==0&&(r.value--,r.value<0&&(r.value=i.value.length-1),f())}function V(){i.value.length!==0&&(r.value++,r.value>i.value.length-1&&(r.value=0),f())}function S(){i.value=[],a("close")}function I(D){var Z;const Y=(Z=D.target)==null?void 0:Z.dataset.index;r.value=Number(Y)}function N(D){t.keyword?i.value.splice(D,1):n.value.splice(D,1),r.value=Math.max(r.value-1,0),f()}const K=new Set(["$","(",")","*","+",".","?","[","\\","]","^","{","|","}"]);function X(D){return K.has(D)?`\\${D}`:D}function H(D){const Y=[...D].map(Z=>X(Z)).join(".*");return new RegExp(`.*${Y}.*`)}return he(()=>t.keyword,D=>{D?h(D):i.value=[...n.value]}),Ke(()=>{u.value=_a(t.menus,D=>Me(pe({},D),{name:b(D==null?void 0:D.name)})),n.value.length>0&&(i.value=n.value),pt("Enter",g),pt("ArrowUp",v),pt("ArrowDown",V),pt("Escape",S)}),(D,Y)=>(d(),k(e(st),null,{default:c(()=>[C("div",bs,[D.keyword&&i.value.length===0?(d(),x("div",vs,[p(e(gn),{class:"mx-auto mt-4 size-12"}),C("p",gs,[B(_(e(b)("ui.widgets.search.noResults"))+" ",1),C("span",ys,' "'+_(D.keyword)+'" ',1)])])):E("",!0),!D.keyword&&i.value.length===0?(d(),x("div",ws,[C("p",xs,_(e(b)("ui.widgets.search.noRecent")),1)])):E("",!0),Se(C("ul",ks,[e(n).length>0&&!D.keyword?(d(),x("li",Ss,_(e(b)("ui.widgets.search.recent")),1)):E("",!0),(d(!0),x(G,null,se(e(yo)(i.value,"path"),(Z,P)=>(d(),x("li",{key:Z.path,class:A([r.value===P?"active bg-primary text-primary-foreground":"","bg-accent flex-center group mb-3 w-full cursor-pointer rounded-lg px-4 py-4"]),"data-index":P,"data-search-item":P,onClick:g,onMouseenter:I},[p(e(Be),{icon:Z.icon,class:"mr-2 size-5 flex-shrink-0",fallback:""},null,8,["icon"]),C("span",Ms,_(Z.name),1),C("div",{class:"flex-center dark:hover:bg-accent hover:text-primary-foreground rounded-full p-1 hover:scale-110",onClick:xe(R=>N(P),["stop"])},[p(e(_t),{class:"size-4"})],8,Cs)],42,_s))),128))],512),[[_e,i.value.length>0]])])]),_:1}))}}),Ts={class:"flex items-center"},Vs=["placeholder"],Bs={class:"flex w-full justify-start text-xs"},Ls={class:"mr-2 flex items-center"},Ps={class:"mr-2 flex items-center"},zs={class:"flex items-center"},Es={class:"text-muted-foreground group-hover:text-foreground hidden text-xs duration-300 md:block"},Is={key:0,class:"bg-background border-foreground/60 text-muted-foreground group-hover:text-foreground relative hidden rounded-sm rounded-r-xl px-1.5 py-1 text-xs leading-none group-hover:opacity-100 md:block"},Us={key:1},Hs=T({name:"GlobalSearch",__name:"global-search",props:{enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]}},setup(l){const o=l,t=F(""),a=F(),[s,n]=nt({onCancel(){n.close()},onOpenChange(v){v||(t.value="")}}),r=n.useStore(v=>v.isOpen);function u(){n.close(),t.value=""}const i=Ma(),h=lt()?i["ctrl+k"]:i["cmd+k"];vt(h,()=>{o.enableShortcutKey&&n.open()}),vt(r,()=>{Ne(()=>{var v;(v=a.value)==null||v.focus()})});const m=v=>{var V;((V=v.key)==null?void 0:V.toLowerCase())==="k"&&(v.metaKey||v.ctrlKey)&&v.preventDefault()},f=()=>{o.enableShortcutKey?window.addEventListener("keydown",m):window.removeEventListener("keydown",m)},g=()=>{r.value?n.close():n.open()};return he(()=>o.enableShortcutKey,f),Ke(()=>{f(),kt(()=>{window.removeEventListener("keydown",m)})}),(v,V)=>(d(),x("div",null,[p(e(s),{"fullscreen-button":!1,class:"w-[600px]","header-class":"py-2 border-b"},{title:c(()=>[C("div",Ts,[p(e(ca),{class:"text-muted-foreground mr-2 size-4"}),Se(C("input",{ref_key:"searchInputRef",ref:a,"onUpdate:modelValue":V[0]||(V[0]=S=>t.value=S),placeholder:e(b)("ui.widgets.search.searchNavigate"),class:"ring-none placeholder:text-muted-foreground w-[80%] rounded-md border border-none bg-transparent p-2 pl-0 text-sm font-normal outline-none ring-0 ring-offset-transparent focus-visible:ring-transparent"},null,8,Vs),[[Xl,t.value]])])]),footer:c(()=>[C("div",Bs,[C("div",Ls,[p(e(dn),{class:"mr-1 size-3"}),B(" "+_(e(b)("ui.widgets.search.select")),1)]),C("div",Ps,[p(e(sn),{class:"mr-1 size-3"}),p(e(tn),{class:"mr-1 size-3"}),B(" "+_(e(b)("ui.widgets.search.navigate")),1)]),C("div",zs,[p(e(Yl),{class:"mr-1 size-3"}),B(" "+_(e(b)("ui.widgets.search.close")),1)])])]),default:c(()=>[p($s,{keyword:t.value,menus:v.menus,onClose:u},null,8,["keyword","menus"])]),_:1}),C("div",{class:"md:bg-accent group flex h-8 cursor-pointer items-center gap-3 rounded-2xl border-none bg-none px-2 py-0.5 outline-none",onClick:V[1]||(V[1]=S=>g())},[p(e(ca),{class:"text-muted-foreground group-hover:text-foreground size-4 group-hover:opacity-100"}),C("span",Es,_(e(b)("ui.widgets.search.title")),1),v.enableShortcutKey?(d(),x("span",Is,[B(_(e(lt)()?"Ctrl":"⌘")+" ",1),V[2]||(V[2]=C("kbd",null,"K",-1))])):(d(),x("span",Us))])]))}}),As={class:"bg-background fixed z-[2000] size-full"},Ws={class:"size-full"},Ds={class:"flex h-full justify-center px-[10%]"},Ns={class:"bg-accent flex-center relative mb-14 mr-20 h-4/5 w-2/5 flex-auto rounded-3xl text-center text-[260px]"},Os={class:"absolute left-4 top-4 text-xl font-semibold"},Rs={class:"bg-accent flex-center mb-14 h-4/5 w-2/5 flex-auto rounded-3xl text-center text-[260px]"},Ks=["onKeydown"],Fs={class:"flex-col-center mb-10 w-[300px]"},Gs={class:"enter-x mb-2 w-full items-center"},js={class:"enter-y absolute bottom-5 w-full text-center xl:text-xl 2xl:text-3xl"},qs={key:0,class:"enter-x mb-2 text-3xl"},Xs={class:"text-lg"},Ys={class:"text-3xl"},nu=T({name:"LockScreen",__name:"lock-screen",props:{avatar:{default:""}},emits:["toLogin"],setup(l){const{locale:o}=ga(),t=Dt(),a=xo(),s=ft(a,"A"),n=ft(a,"HH"),r=ft(a,"mm"),u=ft(a,"YYYY-MM-DD dddd",{locales:o.value}),i=F(!1),{lockScreenPassword:h}=ya(t),[m,{form:f,validate:g}]=wa(it({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:y(()=>[{component:"VbenInputPassword",componentProps:{placeholder:b("ui.widgets.lockScreen.placeholder")},fieldName:"password",label:b("authentication.password"),rules:ka().min(1,{message:b("authentication.passwordTip")})}]),showDefaultActions:!1})),v=y(()=>{var I;return(h==null?void 0:h.value)===((I=f==null?void 0:f.values)==null?void 0:I.password)});function V(){return ne(this,null,function*(){const{valid:I}=yield g();I&&(v.value?t.unlockScreen():f.setFieldError("password",b("authentication.passwordErrorTip")))})}function S(){i.value=!i.value}return Zl(),(I,N)=>(d(),x("div",As,[p(Je,{name:"slide-left"},{default:c(()=>[Se(C("div",Ws,[C("div",{class:"flex-col-center text-foreground/80 hover:text-foreground group my-4 cursor-pointer text-xl font-semibold",onClick:S},[p(e(Aa),{class:"size-5 transition-all duration-300 group-hover:scale-125"}),C("span",null,_(e(b)("ui.widgets.lockScreen.unlock")),1)]),C("div",Ds,[C("div",Ns,[C("span",Os,_(e(s)),1),B(" "+_(e(n)),1)]),C("div",Rs,_(e(r)),1)])],512),[[_e,!i.value]])]),_:1}),p(Je,{name:"slide-right"},{default:c(()=>[i.value?(d(),x("div",{key:0,class:"flex-center size-full",onKeydown:xa(xe(V,["prevent"]),["enter"])},[C("div",Fs,[p(e(ot),{src:I.avatar,class:"enter-x mb-6 size-20"},null,8,["src"]),C("div",Gs,[p(e(m))]),p(e(Pe),{class:"enter-x w-full",onClick:V},{default:c(()=>[B(_(e(b)("ui.widgets.lockScreen.entry")),1)]),_:1}),p(e(Pe),{class:"enter-x my-2 w-full",variant:"ghost",onClick:N[0]||(N[0]=K=>I.$emit("toLogin"))},{default:c(()=>[B(_(e(b)("ui.widgets.lockScreen.backToLogin")),1)]),_:1}),p(e(Pe),{class:"enter-x mr-2 w-full",variant:"ghost",onClick:S},{default:c(()=>[B(_(e(b)("common.back")),1)]),_:1})])],40,Ks)):E("",!0)]),_:1}),C("div",js,[i.value?(d(),x("div",qs,[B(_(e(n))+":"+_(e(r))+" ",1),C("span",Xs,_(e(s)),1)])):E("",!0),C("div",Ys,_(e(u)),1)])]))}}),Zs=["onKeydown"],Qs={class:"w-full"},Js={class:"ml-2 flex w-full flex-col items-center"},er={class:"text-foreground my-6 flex items-center font-medium"},tr=T({name:"LockScreenModal",__name:"lock-screen-modal",props:{avatar:{default:""},text:{default:""}},emits:["submit"],setup(l,{emit:o}){const t=o,[a,{resetForm:s,validate:n,getValues:r}]=wa(it({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:y(()=>[{component:"VbenInputPassword",componentProps:{placeholder:b("ui.widgets.lockScreen.placeholder")},fieldName:"lockScreenPassword",formFieldProps:{validateOnBlur:!1},label:b("authentication.password"),rules:ka().min(1,{message:b("ui.widgets.lockScreen.placeholder")})}]),showDefaultActions:!1})),[u]=nt({onConfirm(){i()},onOpenChange(h){h&&s()}});function i(){return ne(this,null,function*(){const{valid:h}=yield n(),m=yield r();h&&t("submit",m==null?void 0:m.lockScreenPassword)})}return(h,m)=>(d(),k(e(u),{footer:!1,"fullscreen-button":!1,title:e(b)("ui.widgets.lockScreen.title")},{default:c(()=>[C("div",{class:"mb-10 flex w-full flex-col items-center px-10",onKeydown:xa(xe(i,["prevent"]),["enter"])},[C("div",Qs,[C("div",Js,[p(e(ot),{src:h.avatar,class:"size-20","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["src"]),C("div",er,_(h.text),1)]),p(e(a)),p(e(Pe),{class:"mt-1 w-full",onClick:i},{default:c(()=>[B(_(e(b)("ui.widgets.lockScreen.screenButton")),1)]),_:1})])],40,Zs)]),_:1},8,["title"]))}}),ar={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},lr={class:"relative"},or={class:"flex items-center justify-between p-4 py-3"},nr={class:"text-foreground"},sr={class:"!flex max-h-[360px] w-full flex-col"},rr=["onClick"],ir={key:0,class:"bg-primary absolute right-2 top-2 h-2 w-2 rounded"},dr={class:"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full"},ur=["src"],cr={class:"flex flex-col gap-1 leading-none"},pr={class:"font-semibold"},fr={class:"text-muted-foreground my-1 line-clamp-2 text-xs"},mr={class:"text-muted-foreground line-clamp-2 text-xs"},hr={key:1,class:"flex-center text-muted-foreground min-h-[150px] w-full"},br={class:"border-border flex items-center justify-between border-t px-4 py-3"},vr=T({name:"NotificationPopup",__name:"notification",props:{dot:{type:Boolean,default:!1},notifications:{default:()=>[]}},emits:["clear","makeAll","read","viewAll"],setup(l,{emit:o}){const t=o,[a,s]=ko();function n(){a.value=!1}function r(){t("viewAll"),n()}function u(){t("makeAll")}function i(){t("clear")}function h(m){t("read",m)}return(m,f)=>(d(),k(e(Do),{open:e(a),"onUpdate:open":f[1]||(f[1]=g=>Ca(a)?a.value=g:null),"content-class":"relative right-2 w-[360px] p-0"},{trigger:c(()=>[C("div",{class:"flex-center mr-2 h-full",onClick:f[0]||(f[0]=xe(g=>e(s)(),["stop"]))},[p(e(qe),{class:"bell-button text-foreground relative"},{default:c(()=>[m.dot?(d(),x("span",ar)):E("",!0),p(e(Wo),{class:"size-4"})]),_:1})])]),default:c(()=>[C("div",lr,[C("div",or,[C("div",nr,_(e(b)("ui.widgets.notifications")),1),p(e(qe),{disabled:m.notifications.length<=0,tooltip:e(b)("ui.widgets.markAllAsRead"),onClick:u},{default:c(()=>[p(e(fn),{class:"size-4"})]),_:1},8,["disabled","tooltip"])]),m.notifications.length>0?(d(),k(e(st),{key:0},{default:c(()=>[C("ul",sr,[(d(!0),x(G,null,se(m.notifications,g=>(d(),x("li",{key:g.title,class:"hover:bg-accent border-border relative flex w-full cursor-pointer items-start gap-5 border-t px-3 py-3",onClick:v=>h(g)},[g.isRead?E("",!0):(d(),x("span",ir)),C("span",dr,[C("img",{src:g.avatar,class:"aspect-square h-full w-full object-cover",role:"img"},null,8,ur)]),C("div",cr,[C("p",pr,_(g.title),1),C("p",fr,_(g.message),1),C("p",mr,_(g.date),1)])],8,rr))),128))])]),_:1})):(d(),x("div",hr,_(e(b)("common.noData")),1)),C("div",br,[p(e(Pe),{disabled:m.notifications.length<=0,size:"sm",variant:"ghost",onClick:i},{default:c(()=>[B(_(e(b)("ui.widgets.clearNotifications")),1)]),_:1},8,["disabled"]),p(e(Pe),{size:"sm",onClick:r},{default:c(()=>[B(_(e(b)("ui.widgets.viewAll")),1)]),_:1})])])]),_:1},8,["open"]))}}),su=$e(vr,[["__scopeId","data-v-c5b60f1e"]]),gr={class:"flex flex-col py-4"},yr={class:"mb-3 font-semibold leading-none tracking-tight"},we=T({name:"PreferenceBlock",__name:"block",props:{title:{default:""}},setup(l){return(o,t)=>(d(),x("div",gr,[C("h3",yr,_(o.title),1),L(o.$slots,"default")]))}}),wr={class:"flex items-center text-sm"},xr={key:0,class:"ml-auto mr-2 text-xs opacity-60"},J=T({name:"PreferenceSwitchItem",__name:"switch-item",props:me({disabled:{type:Boolean,default:!1}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=w(l,"modelValue"),t=Ie();function a(){o.value=!o.value}return(s,n)=>(d(),x("div",{class:A([{"pointer-events-none opacity-50":s.disabled},"hover:bg-accent my-1 flex w-full items-center justify-between rounded-md px-2 py-2.5"]),onClick:a},[C("span",wr,[L(s.$slots,"default"),e(t).tip?(d(),k(e(tt),{key:0,side:"bottom"},{trigger:c(()=>[p(e(rt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[L(s.$slots,"tip")]),_:3})):E("",!0)]),s.$slots.shortcut?(d(),x("span",xr,[L(s.$slots,"shortcut")])):E("",!0),p(e(An),{checked:o.value,"onUpdate:checked":n[0]||(n[0]=r=>o.value=r),onClick:n[1]||(n[1]=xe(()=>{},["stop"]))},null,8,["checked"])],2))}}),kr={key:0,class:"mb-2 mt-3 flex justify-between gap-3 px-2"},Sr=["onClick"],_r=T({name:"PreferenceAnimation",__name:"animation",props:{transitionProgress:{type:Boolean,default:!1},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{}},emits:["update:transitionProgress","update:transitionName","update:transitionEnable","update:transitionLoading"],setup(l){const o=w(l,"transitionProgress"),t=w(l,"transitionName"),a=w(l,"transitionEnable"),s=w(l,"transitionLoading"),n=["fade","fade-slide","fade-up","fade-down"];function r(u){t.value=u}return(u,i)=>(d(),x(G,null,[p(J,{modelValue:o.value,"onUpdate:modelValue":i[0]||(i[0]=h=>o.value=h)},{default:c(()=>[B(_(e(b)("preferences.animation.progress")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:s.value,"onUpdate:modelValue":i[1]||(i[1]=h=>s.value=h)},{default:c(()=>[B(_(e(b)("preferences.animation.loading")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:a.value,"onUpdate:modelValue":i[2]||(i[2]=h=>a.value=h)},{default:c(()=>[B(_(e(b)("preferences.animation.transition")),1)]),_:1},8,["modelValue"]),a.value?(d(),x("div",kr,[(d(),x(G,null,se(n,h=>C("div",{key:h,class:A([{"outline-box-active":t.value===h},"outline-box p-2"]),onClick:m=>r(h)},[C("div",{class:A([`${h}-slow`,"bg-accent h-10 w-12 rounded-md"])},null,2)],10,Sr)),64))])):E("",!0)],64))}}),Mr={class:"flex items-center text-sm"},Ct=T({name:"PreferenceSelectItem",__name:"select-item",props:me({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=w(l,"modelValue"),t=Ie();return(a,s)=>(d(),x("div",{class:A([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":a.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[C("span",Mr,[L(a.$slots,"default"),e(t).tip?(d(),k(e(tt),{key:0,side:"bottom"},{trigger:c(()=>[p(e(rt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[L(a.$slots,"tip")]),_:3})):E("",!0)]),p(e(ao),{modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n)},{default:c(()=>[p(e(Ql),{class:"h-8 w-[165px]"},{default:c(()=>[p(e(Jl),{placeholder:a.placeholder},null,8,["placeholder"])]),_:1}),p(e(eo),null,{default:c(()=>[(d(!0),x(G,null,se(a.items,n=>(d(),k(e(to),{key:n.value,value:n.value},{default:c(()=>[B(_(n.label),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"])],2))}}),Cr=T({name:"PreferenceGeneralConfig",__name:"general",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{}},emits:["update:appLocale","update:appDynamicTitle","update:appWatermark","update:appEnableCheckUpdates"],setup(l){const o=w(l,"appLocale"),t=w(l,"appDynamicTitle"),a=w(l,"appWatermark"),s=w(l,"appEnableCheckUpdates");return(n,r)=>(d(),x(G,null,[p(Ct,{modelValue:o.value,"onUpdate:modelValue":r[0]||(r[0]=u=>o.value=u),items:e(lo)},{default:c(()=>[B(_(e(b)("preferences.language")),1)]),_:1},8,["modelValue","items"]),p(J,{modelValue:t.value,"onUpdate:modelValue":r[1]||(r[1]=u=>t.value=u)},{default:c(()=>[B(_(e(b)("preferences.dynamicTitle")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:a.value,"onUpdate:modelValue":r[2]||(r[2]=u=>a.value=u)},{default:c(()=>[B(_(e(b)("preferences.watermark")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:s.value,"onUpdate:modelValue":r[3]||(r[3]=u=>s.value=u)},{default:c(()=>[B(_(e(b)("preferences.checkUpdates")),1)]),_:1},8,["modelValue"])],64))}}),$r={class:"text-sm"},ja=T({name:"PreferenceToggleItem",__name:"toggle-item",props:me({disabled:{type:Boolean,default:!1},items:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=w(l,"modelValue");return(t,a)=>(d(),x("div",{class:A([{"pointer-events-none opacity-50":t.disabled},"hover:bg-accent flex w-full items-center justify-between rounded-md px-2 py-2"]),disabled:""},[C("span",$r,[L(t.$slots,"default")]),p(e(Ea),{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=s=>o.value=s),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(d(!0),x(G,null,se(t.items,s=>(d(),k(e(za),{key:s.value,value:s.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 rounded-sm"},{default:c(()=>[B(_(s.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])],2))}}),Tr=T({name:"PreferenceBreadcrumbConfig",__name:"breadcrumb",props:me({disabled:{type:Boolean}},{breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{}}),emits:["update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbStyleType","update:breadcrumbShowHome","update:breadcrumbHideOnlyOne"],setup(l){const o=l,t=w(l,"breadcrumbEnable"),a=w(l,"breadcrumbShowIcon"),s=w(l,"breadcrumbStyleType"),n=w(l,"breadcrumbShowHome"),r=w(l,"breadcrumbHideOnlyOne"),u=[{label:b("preferences.normal"),value:"normal"},{label:b("preferences.breadcrumb.background"),value:"background"}],i=y(()=>!t.value||o.disabled);return(h,m)=>(d(),x(G,null,[p(J,{modelValue:t.value,"onUpdate:modelValue":m[0]||(m[0]=f=>t.value=f),disabled:h.disabled},{default:c(()=>[B(_(e(b)("preferences.breadcrumb.enable")),1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:r.value,"onUpdate:modelValue":m[1]||(m[1]=f=>r.value=f),disabled:i.value},{default:c(()=>[B(_(e(b)("preferences.breadcrumb.hideOnlyOne")),1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:a.value,"onUpdate:modelValue":m[2]||(m[2]=f=>a.value=f),disabled:i.value},{default:c(()=>[B(_(e(b)("preferences.breadcrumb.icon")),1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:n.value,"onUpdate:modelValue":m[3]||(m[3]=f=>n.value=f),disabled:i.value||!a.value},{default:c(()=>[B(_(e(b)("preferences.breadcrumb.home")),1)]),_:1},8,["modelValue","disabled"]),p(ja,{modelValue:s.value,"onUpdate:modelValue":m[4]||(m[4]=f=>s.value=f),disabled:i.value,items:u},{default:c(()=>[B(_(e(b)("preferences.breadcrumb.style")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Vr={},Br={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Lr(l,o){return d(),x("svg",Br,o[0]||(o[0]=[dt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="53.60438" x="43.484" y="13.66705"></rect><path id="svg_14" d="m3.43932,15.53192c0,-1.08676 1.03344,-2 2.26323,-2l30.33036,0c1.22979,0 2.26323,0.91324 2.26323,2l0,17.24865c0,1.08676 -1.03344,2 -2.26323,2l-30.33036,0c-1.22979,0 -2.26323,-0.91324 -2.26323,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="95.02528" x="3.30419" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const qa=$e(Vr,[["render",Lr]]),Pr={},zr={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Er(l,o){return d(),x("svg",zr,o[0]||(o[0]=[dt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="41.98275" x="45.37589" y="13.53192"></rect><path id="svg_14" d="m16.4123,15.53192c0,-1.08676 0.74096,-2 1.62271,-2l21.74653,0c0.88175,0 1.62271,0.91324 1.62271,2l0,17.24865c0,1.08676 -0.74096,2 -1.62271,2l-21.74653,0c-0.88175,0 -1.62271,-0.91324 -1.62271,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="71.10636" x="16.54743" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const Ir=$e(Pr,[["render",Er]]),Ur={},Hr={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Ar(l,o){return d(),x("svg",Hr,o[0]||(o[0]=[C("g",null,[C("path",{id:"svg_1",d:"m0.13514,4.13514c0,-2.17352 1.82648,-4 4,-4l96,0c2.17352,0 4,1.82648 4,4l0,58c0,2.17352 -1.82648,4 -4,4l-96,0c-2.17352,0 -4,-1.82648 -4,-4l0,-58z",fill:"currentColor","fill-opacity":"0.02",opacity:"undefined",stroke:"null"}),C("rect",{id:"svg_13",fill:"currentColor","fill-opacity":"0.08",height:"26.57155",rx:"2",stroke:"null",width:"53.18333",x:"45.79979",y:"3.77232"}),C("path",{id:"svg_14",d:"m4.28142,5.96169c0,-1.37748 1.06465,-2.53502 2.33158,-2.53502l31.2463,0c1.26693,0 2.33158,1.15754 2.33158,2.53502l0,21.86282c0,1.37748 -1.06465,2.53502 -2.33158,2.53502l-31.2463,0c-1.26693,0 -2.33158,-1.15754 -2.33158,-2.53502l0,-21.86282z",fill:"currentColor","fill-opacity":"0.08",opacity:"undefined",stroke:"null"}),C("rect",{id:"svg_15",fill:"currentColor","fill-opacity":"0.08",height:"25.02247",rx:"2",stroke:"null",width:"94.39371",x:"4.56735",y:"34.92584"})],-1)]))}const Wr=$e(Ur,[["render",Ar]]),Dr={},Nr={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Or(l,o){return d(),x("svg",Nr,o[0]||(o[0]=[dt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect><rect id="svg_5" fill="currentColor" fill-opacity="0.08" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path></g>',1)]))}const Rr=$e(Dr,[["render",Or]]),Kr={},Fr={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Gr(l,o){return d(),x("svg",Fr,o[0]||(o[0]=[dt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect></g>',1)]))}const jr=$e(Kr,[["render",Gr]]),qr={},Xr={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Yr(l,o){return d(),x("svg",Xr,o[0]||(o[0]=[dt('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104"></rect><path id="svg_2" d="m-3.37838,3.61916a4.4919,4.02457 0 0 1 4.4919,-4.02457l26.35848,0l0,66.40541l-26.35848,0a4.4919,4.02457 0 0 1 -4.4919,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" width="17.66" x="4.906" y="23.884"></rect><rect id="svg_4" fill="#ffffff" height="9.706" rx="2" width="9.811" x="8.83" y="5.881"></rect><path id="svg_5" d="m4.906,35.833c0,-0.75801 0.63699,-1.395 1.395,-1.395l14.87,0c0.75801,0 1.395,0.63699 1.395,1.395l0,-0.001c0,0.75801 -0.63699,1.395 -1.395,1.395l-14.87,0c-0.75801,0 -1.395,-0.63699 -1.395,-1.395l0,0.001z" fill="#ffffff" opacity="undefined"></path><rect id="svg_6" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="44.992"></rect><rect id="svg_7" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="55.546"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="73.53879" x="28.97986" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="32.039" y="3.89903"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="3.49362"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="45.63141" x="56.05157" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="22.82978" x="29.38527" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="72.45771" x="28.97986" y="39.48203"></rect></g>',1)]))}const Zr=$e(qr,[["render",Yr]]),Qr=qa,Jr={class:"flex w-full gap-5"},ei=["onClick"],ti={class:"text-muted-foreground mt-2 text-center text-xs"},ai=T({name:"PreferenceLayoutContent",__name:"content",props:{modelValue:{default:"wide"},modelModifiers:{}},emits:["update:modelValue"],setup(l){const o=w(l,"modelValue"),t={compact:Ir,wide:Qr},a=y(()=>[{name:b("preferences.wide"),type:"wide"},{name:b("preferences.compact"),type:"compact"}]);function s(n){return n===o.value?["outline-box-active"]:[]}return(n,r)=>(d(),x("div",Jr,[(d(!0),x(G,null,se(a.value,u=>(d(),x("div",{key:u.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:i=>o.value=u.type},[C("div",{class:A([s(u.type),"outline-box flex-center"])},[(d(),k(ze(t[u.type])))],2),C("div",ti,_(u.name),1)],8,ei))),128))]))}}),li={class:"flex items-center text-sm"},at=T({name:"PreferenceSelectItem",__name:"input-item",props:me({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=w(l,"modelValue"),t=Ie();return(a,s)=>(d(),x("div",{class:A([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":a.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[C("span",li,[L(a.$slots,"default"),e(t).tip?(d(),k(e(tt),{key:0,side:"bottom"},{trigger:c(()=>[p(e(rt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[L(a.$slots,"tip")]),_:3})):E("",!0)]),p(e(oo),{modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n),class:"h-8 w-[165px]"},null,8,["modelValue"])],2))}}),oi=T({__name:"copyright",props:me({disabled:{type:Boolean}},{copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{}}),emits:["update:copyrightEnable","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:copyrightCompanyName","update:copyrightCompanySiteLink"],setup(l){const o=l,t=w(l,"copyrightEnable"),a=w(l,"copyrightDate"),s=w(l,"copyrightIcp"),n=w(l,"copyrightIcpLink"),r=w(l,"copyrightCompanyName"),u=w(l,"copyrightCompanySiteLink"),i=y(()=>o.disabled||!t.value);return(h,m)=>(d(),x(G,null,[p(J,{modelValue:t.value,"onUpdate:modelValue":m[0]||(m[0]=f=>t.value=f),disabled:h.disabled},{default:c(()=>[B(_(e(b)("preferences.copyright.enable")),1)]),_:1},8,["modelValue","disabled"]),p(at,{modelValue:r.value,"onUpdate:modelValue":m[1]||(m[1]=f=>r.value=f),disabled:i.value},{default:c(()=>[B(_(e(b)("preferences.copyright.companyName")),1)]),_:1},8,["modelValue","disabled"]),p(at,{modelValue:u.value,"onUpdate:modelValue":m[2]||(m[2]=f=>u.value=f),disabled:i.value},{default:c(()=>[B(_(e(b)("preferences.copyright.companySiteLink")),1)]),_:1},8,["modelValue","disabled"]),p(at,{modelValue:a.value,"onUpdate:modelValue":m[3]||(m[3]=f=>a.value=f),disabled:i.value},{default:c(()=>[B(_(e(b)("preferences.copyright.date")),1)]),_:1},8,["modelValue","disabled"]),p(at,{modelValue:s.value,"onUpdate:modelValue":m[4]||(m[4]=f=>s.value=f),disabled:i.value},{default:c(()=>[B(_(e(b)("preferences.copyright.icp")),1)]),_:1},8,["modelValue","disabled"]),p(at,{modelValue:n.value,"onUpdate:modelValue":m[5]||(m[5]=f=>n.value=f),disabled:i.value},{default:c(()=>[B(_(e(b)("preferences.copyright.icpLink")),1)]),_:1},8,["modelValue","disabled"])],64))}}),ni=T({__name:"footer",props:{footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{}},emits:["update:footerEnable","update:footerFixed"],setup(l){const o=w(l,"footerEnable"),t=w(l,"footerFixed");return(a,s)=>(d(),x(G,null,[p(J,{modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n)},{default:c(()=>[B(_(e(b)("preferences.footer.visible")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:t.value,"onUpdate:modelValue":s[1]||(s[1]=n=>t.value=n),disabled:!o.value},{default:c(()=>[B(_(e(b)("preferences.footer.fixed")),1)]),_:1},8,["modelValue","disabled"])],64))}}),si=T({__name:"header",props:me({disabled:{type:Boolean}},{headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{}}),emits:["update:headerEnable","update:headerMode"],setup(l){const o=w(l,"headerEnable"),t=w(l,"headerMode"),a=[{label:b("preferences.header.modeStatic"),value:"static"},{label:b("preferences.header.modeFixed"),value:"fixed"},{label:b("preferences.header.modeAuto"),value:"auto"},{label:b("preferences.header.modeAutoScroll"),value:"auto-scroll"}];return(s,n)=>(d(),x(G,null,[p(J,{modelValue:o.value,"onUpdate:modelValue":n[0]||(n[0]=r=>o.value=r),disabled:s.disabled},{default:c(()=>[B(_(e(b)("preferences.header.visible")),1)]),_:1},8,["modelValue","disabled"]),p(Ct,{modelValue:t.value,"onUpdate:modelValue":n[1]||(n[1]=r=>t.value=r),disabled:!o.value,items:a},{default:c(()=>[B(_(e(b)("preferences.mode")),1)]),_:1},8,["modelValue","disabled"])],64))}}),ri={class:"flex w-full flex-wrap gap-5"},ii=["onClick"],di={class:"text-muted-foreground flex-center hover:text-foreground mt-2 text-center text-xs"},ui=T({name:"PreferenceLayout",__name:"layout",props:{modelValue:{default:"sidebar-nav"},modelModifiers:{}},emits:["update:modelValue"],setup(l){const o=w(l,"modelValue"),t={"full-content":Wr,"header-nav":qa,"mixed-nav":Rr,"sidebar-mixed-nav":jr,"sidebar-nav":Zr},a=y(()=>[{name:b("preferences.vertical"),tip:b("preferences.verticalTip"),type:"sidebar-nav"},{name:b("preferences.twoColumn"),tip:b("preferences.twoColumnTip"),type:"sidebar-mixed-nav"},{name:b("preferences.horizontal"),tip:b("preferences.horizontalTip"),type:"header-nav"},{name:b("preferences.mixedMenu"),tip:b("preferences.mixedMenuTip"),type:"mixed-nav"},{name:b("preferences.fullContent"),tip:b("preferences.fullContentTip"),type:"full-content"}]);function s(n){return n===o.value?["outline-box-active"]:[]}return(n,r)=>(d(),x("div",ri,[(d(!0),x(G,null,se(a.value,u=>(d(),x("div",{key:u.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:i=>o.value=u.type},[C("div",{class:A([s(u.type),"outline-box flex-center"])},[(d(),k(ze(t[u.type])))],2),C("div",di,[B(_(u.name)+" ",1),u.tip?(d(),k(e(tt),{key:0,side:"bottom"},{trigger:c(()=>[p(e(rt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[B(" "+_(u.tip),1)]),_:2},1024)):E("",!0)])],8,ii))),128))]))}}),ci=T({name:"PreferenceNavigationConfig",__name:"navigation",props:me({disabled:{type:Boolean},disabledNavigationSplit:{type:Boolean}},{navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{}}),emits:["update:navigationStyleType","update:navigationSplit","update:navigationAccordion"],setup(l){const o=w(l,"navigationStyleType"),t=w(l,"navigationSplit"),a=w(l,"navigationAccordion"),s=[{label:b("preferences.rounded"),value:"rounded"},{label:b("preferences.plain"),value:"plain"}];return(n,r)=>(d(),x(G,null,[p(ja,{modelValue:o.value,"onUpdate:modelValue":r[0]||(r[0]=u=>o.value=u),disabled:n.disabled,items:s},{default:c(()=>[B(_(e(b)("preferences.navigationMenu.style")),1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:t.value,"onUpdate:modelValue":r[1]||(r[1]=u=>t.value=u),disabled:n.disabledNavigationSplit||n.disabled},{tip:c(()=>[B(_(e(b)("preferences.navigationMenu.splitTip")),1)]),default:c(()=>[B(_(e(b)("preferences.navigationMenu.split"))+" ",1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:a.value,"onUpdate:modelValue":r[2]||(r[2]=u=>a.value=u),disabled:n.disabled},{default:c(()=>[B(_(e(b)("preferences.navigationMenu.accordion")),1)]),_:1},8,["modelValue","disabled"])],64))}}),pi={class:"flex items-center text-sm"},fi=T({name:"PreferenceSelectItem",__name:"number-field-item",props:me({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(l){const o=w(l,"modelValue"),t=Ie();return(a,s)=>(d(),x("div",{class:A([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":a.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[C("span",pi,[L(a.$slots,"default"),e(t).tip?(d(),k(e(tt),{key:0,side:"bottom"},{trigger:c(()=>[p(e(rt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[L(a.$slots,"tip")]),_:3})):E("",!0)]),p(e(Pn),ce({modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n)},a.$attrs,{class:"w-[165px]"}),{default:c(()=>[p(e(zn),null,{default:c(()=>[p(e(En)),p(e(Un)),p(e(In))]),_:1})]),_:1},16,["modelValue"])],2))}}),mi=T({__name:"sidebar",props:me({disabled:{type:Boolean}},{sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{}}),emits:["update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsedShowTitle","update:sidebarCollapsed"],setup(l){const o=w(l,"sidebarEnable"),t=w(l,"sidebarWidth"),a=w(l,"sidebarCollapsedShowTitle"),s=w(l,"sidebarCollapsed");return(n,r)=>(d(),x(G,null,[p(J,{modelValue:o.value,"onUpdate:modelValue":r[0]||(r[0]=u=>o.value=u),disabled:n.disabled},{default:c(()=>[B(_(e(b)("preferences.sidebar.visible")),1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:s.value,"onUpdate:modelValue":r[1]||(r[1]=u=>s.value=u),disabled:!o.value||n.disabled},{default:c(()=>[B(_(e(b)("preferences.sidebar.collapsed")),1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:a.value,"onUpdate:modelValue":r[2]||(r[2]=u=>a.value=u),disabled:!o.value||n.disabled||!s.value},{default:c(()=>[B(_(e(b)("preferences.sidebar.collapsedShowTitle")),1)]),_:1},8,["modelValue","disabled"]),p(fi,{modelValue:t.value,"onUpdate:modelValue":r[3]||(r[3]=u=>t.value=u),disabled:!o.value||n.disabled,max:320,min:160,step:10},{default:c(()=>[B(_(e(b)("preferences.sidebar.width")),1)]),_:1},8,["modelValue","disabled"])],64))}}),hi=T({name:"PreferenceTabsConfig",__name:"tabbar",props:me({disabled:{type:Boolean}},{tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{}}),emits:["update:tabbarEnable","update:tabbarShowIcon","update:tabbarPersist","update:tabbarDraggable","update:tabbarStyleType","update:tabbarShowMore","update:tabbarShowMaximize"],setup(l){const o=w(l,"tabbarEnable"),t=w(l,"tabbarShowIcon"),a=w(l,"tabbarPersist"),s=w(l,"tabbarDraggable"),n=w(l,"tabbarStyleType"),r=w(l,"tabbarShowMore"),u=w(l,"tabbarShowMaximize"),i=y(()=>[{label:b("preferences.tabbar.styleType.chrome"),value:"chrome"},{label:b("preferences.tabbar.styleType.plain"),value:"plain"},{label:b("preferences.tabbar.styleType.card"),value:"card"},{label:b("preferences.tabbar.styleType.brisk"),value:"brisk"}]);return(h,m)=>(d(),x(G,null,[p(J,{modelValue:o.value,"onUpdate:modelValue":m[0]||(m[0]=f=>o.value=f),disabled:h.disabled},{default:c(()=>[B(_(e(b)("preferences.tabbar.enable")),1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:a.value,"onUpdate:modelValue":m[1]||(m[1]=f=>a.value=f),disabled:!o.value},{default:c(()=>[B(_(e(b)("preferences.tabbar.persist")),1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:s.value,"onUpdate:modelValue":m[2]||(m[2]=f=>s.value=f),disabled:!o.value},{default:c(()=>[B(_(e(b)("preferences.tabbar.draggable")),1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:t.value,"onUpdate:modelValue":m[3]||(m[3]=f=>t.value=f),disabled:!o.value},{default:c(()=>[B(_(e(b)("preferences.tabbar.icon")),1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:r.value,"onUpdate:modelValue":m[4]||(m[4]=f=>r.value=f),disabled:!o.value},{default:c(()=>[B(_(e(b)("preferences.tabbar.showMore")),1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:u.value,"onUpdate:modelValue":m[5]||(m[5]=f=>u.value=f),disabled:!o.value},{default:c(()=>[B(_(e(b)("preferences.tabbar.showMaximize")),1)]),_:1},8,["modelValue","disabled"]),p(Ct,{modelValue:n.value,"onUpdate:modelValue":m[6]||(m[6]=f=>n.value=f),items:i.value},{default:c(()=>[B(_(e(b)("preferences.tabbar.styleType.title")),1)]),_:1},8,["modelValue","items"])],64))}}),bi=T({name:"PreferenceInterfaceControl",__name:"widget",props:{widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:["update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:appPreferencesButtonPosition","update:widgetRefresh"],setup(l){const o=w(l,"widgetGlobalSearch"),t=w(l,"widgetFullscreen"),a=w(l,"widgetLanguageToggle"),s=w(l,"widgetNotification"),n=w(l,"widgetThemeToggle"),r=w(l,"widgetSidebarToggle"),u=w(l,"widgetLockScreen"),i=w(l,"appPreferencesButtonPosition"),h=w(l,"widgetRefresh"),m=y(()=>[{label:b("preferences.position.auto"),value:"auto"},{label:b("preferences.position.header"),value:"header"},{label:b("preferences.position.fixed"),value:"fixed"}]);return(f,g)=>(d(),x(G,null,[p(J,{modelValue:o.value,"onUpdate:modelValue":g[0]||(g[0]=v=>o.value=v)},{default:c(()=>[B(_(e(b)("preferences.widget.globalSearch")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:n.value,"onUpdate:modelValue":g[1]||(g[1]=v=>n.value=v)},{default:c(()=>[B(_(e(b)("preferences.widget.themeToggle")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:a.value,"onUpdate:modelValue":g[2]||(g[2]=v=>a.value=v)},{default:c(()=>[B(_(e(b)("preferences.widget.languageToggle")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:t.value,"onUpdate:modelValue":g[3]||(g[3]=v=>t.value=v)},{default:c(()=>[B(_(e(b)("preferences.widget.fullscreen")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:s.value,"onUpdate:modelValue":g[4]||(g[4]=v=>s.value=v)},{default:c(()=>[B(_(e(b)("preferences.widget.notification")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:u.value,"onUpdate:modelValue":g[5]||(g[5]=v=>u.value=v)},{default:c(()=>[B(_(e(b)("preferences.widget.lockScreen")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:r.value,"onUpdate:modelValue":g[6]||(g[6]=v=>r.value=v)},{default:c(()=>[B(_(e(b)("preferences.widget.sidebarToggle")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:h.value,"onUpdate:modelValue":g[7]||(g[7]=v=>h.value=v)},{default:c(()=>[B(_(e(b)("preferences.widget.refresh")),1)]),_:1},8,["modelValue"]),p(Ct,{modelValue:i.value,"onUpdate:modelValue":g[8]||(g[8]=v=>i.value=v),items:m.value},{default:c(()=>[B(_(e(b)("preferences.position.title")),1)]),_:1},8,["modelValue","items"])],64))}}),vi=T({name:"PreferenceGeneralConfig",__name:"global",props:{shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysLogout:{type:Boolean},shortcutKeysLogoutModifiers:{},shortcutKeysLockScreen:{type:Boolean},shortcutKeysLockScreenModifiers:{}},emits:["update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysLogout","update:shortcutKeysLockScreen"],setup(l){const o=w(l,"shortcutKeysEnable"),t=w(l,"shortcutKeysGlobalSearch"),a=w(l,"shortcutKeysLogout"),s=w(l,"shortcutKeysLockScreen"),n=y(()=>lt()?"Alt":"⌥");return(r,u)=>(d(),x(G,null,[p(J,{modelValue:o.value,"onUpdate:modelValue":u[0]||(u[0]=i=>o.value=i)},{default:c(()=>[B(_(e(b)("preferences.shortcutKeys.title")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:t.value,"onUpdate:modelValue":u[1]||(u[1]=i=>t.value=i),disabled:!o.value},{shortcut:c(()=>[B(_(e(lt)()?"Ctrl":"⌘")+" ",1),u[4]||(u[4]=C("kbd",null," K ",-1))]),default:c(()=>[B(_(e(b)("preferences.shortcutKeys.search"))+" ",1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:a.value,"onUpdate:modelValue":u[2]||(u[2]=i=>a.value=i),disabled:!o.value},{shortcut:c(()=>[B(_(n.value)+" Q ",1)]),default:c(()=>[B(_(e(b)("preferences.shortcutKeys.logout"))+" ",1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:s.value,"onUpdate:modelValue":u[3]||(u[3]=i=>s.value=i),disabled:!o.value},{shortcut:c(()=>[B(_(n.value)+" L ",1)]),default:c(()=>[B(_(e(b)("ui.widgets.lockScreen.title"))+" ",1)]),_:1},8,["modelValue","disabled"])],64))}}),gi={class:"flex w-full flex-wrap justify-between"},yi=["onClick"],wi={class:"flex-center relative size-5 rounded-sm"},xi=["value"],ki={class:"text-muted-foreground my-2 text-center text-xs"},Si=T({name:"PreferenceBuiltinTheme",__name:"builtin",props:me({isDark:{type:Boolean}},{modelValue:{default:"default"},modelModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{}}),emits:["update:modelValue","update:themeColorPrimary"],setup(l){const o=l,t=F(),a=w(l,"modelValue"),s=w(l,"themeColorPrimary"),n=y(()=>new So(s.value||"").toHexString()),r=y(()=>[..._o]);function u(f){switch(f){case"custom":return b("preferences.theme.builtin.custom");case"deep-blue":return b("preferences.theme.builtin.deepBlue");case"deep-green":return b("preferences.theme.builtin.deepGreen");case"default":return b("preferences.theme.builtin.default");case"gray":return b("preferences.theme.builtin.gray");case"green":return b("preferences.theme.builtin.green");case"neutral":return b("preferences.theme.builtin.neutral");case"orange":return b("preferences.theme.builtin.orange");case"pink":return b("preferences.theme.builtin.pink");case"rose":return b("preferences.theme.builtin.rose");case"sky-blue":return b("preferences.theme.builtin.skyBlue");case"slate":return b("preferences.theme.builtin.slate");case"violet":return b("preferences.theme.builtin.violet");case"yellow":return b("preferences.theme.builtin.yellow");case"zinc":return b("preferences.theme.builtin.zinc")}}function i(f){a.value=f.type;const g=o.isDark&&f.darkPrimaryColor||f.primaryColor;s.value=g||f.color}function h(f){const g=f.target;s.value=Mo(g.value)}function m(){var f,g,v;(v=(g=(f=t.value)==null?void 0:f[0])==null?void 0:g.click)==null||v.call(g)}return(f,g)=>(d(),x("div",gi,[(d(!0),x(G,null,se(r.value,v=>(d(),x("div",{key:v.type,class:"flex cursor-pointer flex-col",onClick:V=>i(v)},[C("div",{class:A([{"outline-box-active":v.type===a.value},"outline-box flex-center group cursor-pointer"])},[v.type!=="custom"?(d(),x("div",{key:0,style:oe({backgroundColor:v.color}),class:"mx-10 my-2 size-5 rounded-md"},null,4)):(d(),x("div",{key:1,class:"size-full px-10 py-2",onClick:xe(m,["stop"])},[C("div",wi,[p(e(yn),{class:"absolute z-10 size-5 opacity-60 group-hover:opacity-100"}),C("input",{ref_for:!0,ref_key:"colorInput",ref:t,value:n.value,class:"absolute inset-0 opacity-0",type:"color",onInput:h},null,40,xi)])]))],2),C("div",ki,_(u(v.type)),1)],8,yi))),128))]))}}),_i=T({name:"PreferenceColorMode",__name:"color-mode",props:{appColorWeakMode:{type:Boolean,default:!1},appColorWeakModeModifiers:{},appColorGrayMode:{type:Boolean,default:!1},appColorGrayModeModifiers:{}},emits:["update:appColorWeakMode","update:appColorGrayMode"],setup(l){const o=w(l,"appColorWeakMode"),t=w(l,"appColorGrayMode");return(a,s)=>(d(),x(G,null,[p(J,{modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n)},{default:c(()=>[B(_(e(b)("preferences.theme.weakMode")),1)]),_:1},8,["modelValue"]),p(J,{modelValue:t.value,"onUpdate:modelValue":s[1]||(s[1]=n=>t.value=n)},{default:c(()=>[B(_(e(b)("preferences.theme.grayMode")),1)]),_:1},8,["modelValue"])],64))}}),Mi=T({name:"PreferenceColorMode",__name:"radius",props:{themeRadius:{default:"0.5"},themeRadiusModifiers:{}},emits:["update:themeRadius"],setup(l){const o=w(l,"themeRadius"),t=[{label:"0",value:"0"},{label:"0.25",value:"0.25"},{label:"0.5",value:"0.5"},{label:"0.75",value:"0.75"},{label:"1",value:"1"}];return(a,s)=>(d(),k(e(Ea),{modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=n=>o.value=n),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(d(),x(G,null,se(t,n=>p(e(za),{key:n.value,value:n.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 w-16 rounded-sm"},{default:c(()=>[B(_(n.label),1)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"]))}}),Ci={class:"flex w-full flex-wrap justify-between"},$i=["onClick"],Ti={class:"text-muted-foreground mt-2 text-center text-xs"},Vi=T({name:"PreferenceTheme",__name:"theme",props:{modelValue:{default:"auto"},modelModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{}},emits:["update:modelValue","update:themeSemiDarkSidebar","update:themeSemiDarkHeader"],setup(l){const o=w(l,"modelValue"),t=w(l,"themeSemiDarkSidebar"),a=w(l,"themeSemiDarkHeader"),s=[{icon:en,name:"light"},{icon:Fo,name:"dark"},{icon:Go,name:"auto"}];function n(u){return u===o.value?["outline-box-active"]:[]}function r(u){switch(u){case"auto":return b("preferences.followSystem");case"dark":return b("preferences.theme.dark");case"light":return b("preferences.theme.light")}}return(u,i)=>(d(),x("div",Ci,[(d(),x(G,null,se(s,h=>C("div",{key:h.name,class:"flex cursor-pointer flex-col",onClick:m=>o.value=h.name},[C("div",{class:A([n(h.name),"outline-box flex-center py-4"])},[(d(),k(ze(h.icon),{class:"mx-9 size-5"}))],2),C("div",Ti,_(r(h.name)),1)],8,$i)),64)),p(J,{modelValue:t.value,"onUpdate:modelValue":i[0]||(i[0]=h=>t.value=h),disabled:o.value==="dark",class:"mt-6"},{default:c(()=>[B(_(e(b)("preferences.theme.darkSidebar")),1)]),_:1},8,["modelValue","disabled"]),p(J,{modelValue:a.value,"onUpdate:modelValue":i[1]||(i[1]=h=>a.value=h),disabled:o.value==="dark"},{default:c(()=>[B(_(e(b)("preferences.theme.darkHeader")),1)]),_:1},8,["modelValue","disabled"])]))}}),Bi={class:"flex items-center"},Li={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},Pi={class:"p-1"},zi=T({__name:"preferences-drawer",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appLayout:{},appLayoutModifiers:{},appColorGrayMode:{type:Boolean},appColorGrayModeModifiers:{},appColorWeakMode:{type:Boolean},appColorWeakModeModifiers:{},appContentCompact:{},appContentCompactModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},transitionProgress:{type:Boolean},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{},themeBuiltinType:{},themeBuiltinTypeModifiers:{},themeMode:{},themeModeModifiers:{},themeRadius:{},themeRadiusModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{},sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{},tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{},footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{},copyrightSettingShow:{type:Boolean},copyrightSettingShowModifiers:{},copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysGlobalLogout:{type:Boolean},shortcutKeysGlobalLogoutModifiers:{},shortcutKeysGlobalLockScreen:{type:Boolean},shortcutKeysGlobalLockScreenModifiers:{},widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:me(["clearPreferencesAndLogout"],["update:appLocale","update:appDynamicTitle","update:appLayout","update:appColorGrayMode","update:appColorWeakMode","update:appContentCompact","update:appWatermark","update:appEnableCheckUpdates","update:appPreferencesButtonPosition","update:transitionProgress","update:transitionName","update:transitionLoading","update:transitionEnable","update:themeColorPrimary","update:themeBuiltinType","update:themeMode","update:themeRadius","update:themeSemiDarkSidebar","update:themeSemiDarkHeader","update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsed","update:sidebarCollapsedShowTitle","update:headerEnable","update:headerMode","update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbShowHome","update:breadcrumbStyleType","update:breadcrumbHideOnlyOne","update:tabbarEnable","update:tabbarShowIcon","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarPersist","update:tabbarDraggable","update:tabbarStyleType","update:navigationStyleType","update:navigationSplit","update:navigationAccordion","update:footerEnable","update:footerFixed","update:copyrightSettingShow","update:copyrightEnable","update:copyrightCompanyName","update:copyrightCompanySiteLink","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysGlobalLogout","update:shortcutKeysGlobalLockScreen","update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:widgetRefresh"]),setup(l,{emit:o}){const t=o,a=no.getMessage(),s=w(l,"appLocale"),n=w(l,"appDynamicTitle"),r=w(l,"appLayout"),u=w(l,"appColorGrayMode"),i=w(l,"appColorWeakMode"),h=w(l,"appContentCompact"),m=w(l,"appWatermark"),f=w(l,"appEnableCheckUpdates"),g=w(l,"appPreferencesButtonPosition"),v=w(l,"transitionProgress"),V=w(l,"transitionName"),S=w(l,"transitionLoading"),I=w(l,"transitionEnable"),N=w(l,"themeColorPrimary"),K=w(l,"themeBuiltinType"),X=w(l,"themeMode"),H=w(l,"themeRadius"),D=w(l,"themeSemiDarkSidebar"),Y=w(l,"themeSemiDarkHeader"),Z=w(l,"sidebarEnable"),P=w(l,"sidebarWidth"),R=w(l,"sidebarCollapsed"),j=w(l,"sidebarCollapsedShowTitle"),ie=w(l,"headerEnable"),ye=w(l,"headerMode"),be=w(l,"breadcrumbEnable"),O=w(l,"breadcrumbShowIcon"),q=w(l,"breadcrumbShowHome"),ve=w(l,"breadcrumbStyleType"),ge=w(l,"breadcrumbHideOnlyOne"),Te=w(l,"tabbarEnable"),W=w(l,"tabbarShowIcon"),ee=w(l,"tabbarShowMore"),te=w(l,"tabbarShowMaximize"),ae=w(l,"tabbarPersist"),le=w(l,"tabbarDraggable"),fe=w(l,"tabbarStyleType"),Fe=w(l,"navigationStyleType"),Ae=w(l,"navigationSplit"),Ge=w(l,"navigationAccordion"),We=w(l,"footerEnable"),je=w(l,"footerFixed"),Ue=w(l,"copyrightSettingShow"),z=w(l,"copyrightEnable"),Q=w(l,"copyrightCompanyName"),re=w(l,"copyrightCompanySiteLink"),Le=w(l,"copyrightDate"),Ve=w(l,"copyrightIcp"),Rt=w(l,"copyrightIcpLink"),Kt=w(l,"shortcutKeysEnable"),Ft=w(l,"shortcutKeysGlobalSearch"),Gt=w(l,"shortcutKeysGlobalLogout"),jt=w(l,"shortcutKeysGlobalLockScreen"),qt=w(l,"widgetGlobalSearch"),Xt=w(l,"widgetFullscreen"),Yt=w(l,"widgetLanguageToggle"),Zt=w(l,"widgetNotification"),Qt=w(l,"widgetThemeToggle"),Jt=w(l,"widgetSidebarToggle"),ea=w(l,"widgetLockScreen"),ta=w(l,"widgetRefresh"),{diffPreference:Qe,isDark:sl,isFullContent:$t,isHeaderNav:rl,isMixedNav:aa,isSideMixedNav:il,isSideMode:dl,isSideNav:ul}=Ye(),{copy:cl}=Co({legacy:!0}),[pl]=Ua(),la=F("appearance"),fl=y(()=>[{label:b("preferences.appearance"),value:"appearance"},{label:b("preferences.layout"),value:"layout"},{label:b("preferences.shortcutKeys.title"),value:"shortcutKey"},{label:b("preferences.general"),value:"general"}]),ml=y(()=>!$t.value&&!aa.value&&!rl.value&&U.header.enable);function hl(){return ne(this,null,function*(){var Tt;yield cl(JSON.stringify(Qe.value,null,2)),(Tt=a.copyPreferencesSuccess)==null||Tt.call(a,b("preferences.copyPreferencesSuccessTitle"),b("preferences.copyPreferencesSuccess"))})}function bl(){return ne(this,null,function*(){ra(),$o(),t("clearPreferencesAndLogout")})}function vl(){return ne(this,null,function*(){Qe.value&&(ra(),yield Sa(U.app.locale))})}return(Tt,M)=>(d(),x("div",null,[p(e(pl),{description:e(b)("preferences.subtitle"),title:e(b)("preferences.title"),class:"sm:max-w-sm"},{extra:c(()=>[C("div",Bi,[p(e(qe),{disabled:!e(Qe),tooltip:e(b)("preferences.resetTip"),class:"relative"},{default:c(()=>[e(Qe)?(d(),x("span",Li)):E("",!0),p(e(At),{class:"size-4",onClick:vl})]),_:1},8,["disabled","tooltip"])])]),footer:c(()=>[p(e(Pe),{disabled:!e(Qe),class:"mx-4 w-full",size:"sm",variant:"default",onClick:hl},{default:c(()=>[p(e(rn),{class:"mr-2 size-3"}),B(" "+_(e(b)("preferences.copyPreferences")),1)]),_:1},8,["disabled"]),p(e(Pe),{disabled:!e(Qe),class:"mr-4 w-full",size:"sm",variant:"ghost",onClick:bl},{default:c(()=>[B(_(e(b)("preferences.clearAndLogout")),1)]),_:1},8,["disabled"])]),default:c(()=>[C("div",Pi,[p(e(ps),{modelValue:la.value,"onUpdate:modelValue":M[60]||(M[60]=$=>la.value=$),tabs:fl.value},{general:c(()=>[p(e(we),{title:e(b)("preferences.general")},{default:c(()=>[p(e(Cr),{"app-dynamic-title":n.value,"onUpdate:appDynamicTitle":M[0]||(M[0]=$=>n.value=$),"app-enable-check-updates":f.value,"onUpdate:appEnableCheckUpdates":M[1]||(M[1]=$=>f.value=$),"app-locale":s.value,"onUpdate:appLocale":M[2]||(M[2]=$=>s.value=$),"app-watermark":m.value,"onUpdate:appWatermark":M[3]||(M[3]=$=>m.value=$)},null,8,["app-dynamic-title","app-enable-check-updates","app-locale","app-watermark"])]),_:1},8,["title"]),p(e(we),{title:e(b)("preferences.animation.title")},{default:c(()=>[p(e(_r),{"transition-enable":I.value,"onUpdate:transitionEnable":M[4]||(M[4]=$=>I.value=$),"transition-loading":S.value,"onUpdate:transitionLoading":M[5]||(M[5]=$=>S.value=$),"transition-name":V.value,"onUpdate:transitionName":M[6]||(M[6]=$=>V.value=$),"transition-progress":v.value,"onUpdate:transitionProgress":M[7]||(M[7]=$=>v.value=$)},null,8,["transition-enable","transition-loading","transition-name","transition-progress"])]),_:1},8,["title"])]),appearance:c(()=>[p(e(we),{title:e(b)("preferences.theme.title")},{default:c(()=>[p(e(Vi),{modelValue:X.value,"onUpdate:modelValue":M[8]||(M[8]=$=>X.value=$),"theme-semi-dark-header":Y.value,"onUpdate:themeSemiDarkHeader":M[9]||(M[9]=$=>Y.value=$),"theme-semi-dark-sidebar":D.value,"onUpdate:themeSemiDarkSidebar":M[10]||(M[10]=$=>D.value=$)},null,8,["modelValue","theme-semi-dark-header","theme-semi-dark-sidebar"])]),_:1},8,["title"]),p(e(we),{title:e(b)("preferences.theme.builtin.title")},{default:c(()=>[p(e(Si),{modelValue:K.value,"onUpdate:modelValue":M[11]||(M[11]=$=>K.value=$),"theme-color-primary":N.value,"onUpdate:themeColorPrimary":M[12]||(M[12]=$=>N.value=$),"is-dark":e(sl)},null,8,["modelValue","theme-color-primary","is-dark"])]),_:1},8,["title"]),p(e(we),{title:e(b)("preferences.theme.radius")},{default:c(()=>[p(e(Mi),{modelValue:H.value,"onUpdate:modelValue":M[13]||(M[13]=$=>H.value=$)},null,8,["modelValue"])]),_:1},8,["title"]),p(e(we),{title:e(b)("preferences.other")},{default:c(()=>[p(e(_i),{"app-color-gray-mode":u.value,"onUpdate:appColorGrayMode":M[14]||(M[14]=$=>u.value=$),"app-color-weak-mode":i.value,"onUpdate:appColorWeakMode":M[15]||(M[15]=$=>i.value=$)},null,8,["app-color-gray-mode","app-color-weak-mode"])]),_:1},8,["title"])]),layout:c(()=>[p(e(we),{title:e(b)("preferences.layout")},{default:c(()=>[p(e(ui),{modelValue:r.value,"onUpdate:modelValue":M[16]||(M[16]=$=>r.value=$)},null,8,["modelValue"])]),_:1},8,["title"]),p(e(we),{title:e(b)("preferences.content")},{default:c(()=>[p(e(ai),{modelValue:h.value,"onUpdate:modelValue":M[17]||(M[17]=$=>h.value=$)},null,8,["modelValue"])]),_:1},8,["title"]),p(e(we),{title:e(b)("preferences.sidebar.title")},{default:c(()=>[p(e(mi),{"sidebar-collapsed":R.value,"onUpdate:sidebarCollapsed":M[18]||(M[18]=$=>R.value=$),"sidebar-collapsed-show-title":j.value,"onUpdate:sidebarCollapsedShowTitle":M[19]||(M[19]=$=>j.value=$),"sidebar-enable":Z.value,"onUpdate:sidebarEnable":M[20]||(M[20]=$=>Z.value=$),"sidebar-width":P.value,"onUpdate:sidebarWidth":M[21]||(M[21]=$=>P.value=$),disabled:!e(dl)},null,8,["sidebar-collapsed","sidebar-collapsed-show-title","sidebar-enable","sidebar-width","disabled"])]),_:1},8,["title"]),p(e(we),{title:e(b)("preferences.header.title")},{default:c(()=>[p(e(si),{"header-enable":ie.value,"onUpdate:headerEnable":M[22]||(M[22]=$=>ie.value=$),"header-mode":ye.value,"onUpdate:headerMode":M[23]||(M[23]=$=>ye.value=$),disabled:e($t)},null,8,["header-enable","header-mode","disabled"])]),_:1},8,["title"]),p(e(we),{title:e(b)("preferences.navigationMenu.title")},{default:c(()=>[p(e(ci),{"navigation-accordion":Ge.value,"onUpdate:navigationAccordion":M[24]||(M[24]=$=>Ge.value=$),"navigation-split":Ae.value,"onUpdate:navigationSplit":M[25]||(M[25]=$=>Ae.value=$),"navigation-style-type":Fe.value,"onUpdate:navigationStyleType":M[26]||(M[26]=$=>Fe.value=$),disabled:e($t),"disabled-navigation-split":!e(aa)},null,8,["navigation-accordion","navigation-split","navigation-style-type","disabled","disabled-navigation-split"])]),_:1},8,["title"]),p(e(we),{title:e(b)("preferences.breadcrumb.title")},{default:c(()=>[p(e(Tr),{"breadcrumb-enable":be.value,"onUpdate:breadcrumbEnable":M[27]||(M[27]=$=>be.value=$),"breadcrumb-hide-only-one":ge.value,"onUpdate:breadcrumbHideOnlyOne":M[28]||(M[28]=$=>ge.value=$),"breadcrumb-show-home":q.value,"onUpdate:breadcrumbShowHome":M[29]||(M[29]=$=>q.value=$),"breadcrumb-show-icon":O.value,"onUpdate:breadcrumbShowIcon":M[30]||(M[30]=$=>O.value=$),"breadcrumb-style-type":ve.value,"onUpdate:breadcrumbStyleType":M[31]||(M[31]=$=>ve.value=$),disabled:!ml.value||!(e(ul)||e(il))},null,8,["breadcrumb-enable","breadcrumb-hide-only-one","breadcrumb-show-home","breadcrumb-show-icon","breadcrumb-style-type","disabled"])]),_:1},8,["title"]),p(e(we),{title:e(b)("preferences.tabbar.title")},{default:c(()=>[p(e(hi),{"tabbar-draggable":le.value,"onUpdate:tabbarDraggable":M[32]||(M[32]=$=>le.value=$),"tabbar-enable":Te.value,"onUpdate:tabbarEnable":M[33]||(M[33]=$=>Te.value=$),"tabbar-persist":ae.value,"onUpdate:tabbarPersist":M[34]||(M[34]=$=>ae.value=$),"tabbar-show-icon":W.value,"onUpdate:tabbarShowIcon":M[35]||(M[35]=$=>W.value=$),"tabbar-show-maximize":te.value,"onUpdate:tabbarShowMaximize":M[36]||(M[36]=$=>te.value=$),"tabbar-show-more":ee.value,"onUpdate:tabbarShowMore":M[37]||(M[37]=$=>ee.value=$),"tabbar-style-type":fe.value,"onUpdate:tabbarStyleType":M[38]||(M[38]=$=>fe.value=$)},null,8,["tabbar-draggable","tabbar-enable","tabbar-persist","tabbar-show-icon","tabbar-show-maximize","tabbar-show-more","tabbar-style-type"])]),_:1},8,["title"]),p(e(we),{title:e(b)("preferences.widget.title")},{default:c(()=>[p(e(bi),{"app-preferences-button-position":g.value,"onUpdate:appPreferencesButtonPosition":M[39]||(M[39]=$=>g.value=$),"widget-fullscreen":Xt.value,"onUpdate:widgetFullscreen":M[40]||(M[40]=$=>Xt.value=$),"widget-global-search":qt.value,"onUpdate:widgetGlobalSearch":M[41]||(M[41]=$=>qt.value=$),"widget-language-toggle":Yt.value,"onUpdate:widgetLanguageToggle":M[42]||(M[42]=$=>Yt.value=$),"widget-lock-screen":ea.value,"onUpdate:widgetLockScreen":M[43]||(M[43]=$=>ea.value=$),"widget-notification":Zt.value,"onUpdate:widgetNotification":M[44]||(M[44]=$=>Zt.value=$),"widget-refresh":ta.value,"onUpdate:widgetRefresh":M[45]||(M[45]=$=>ta.value=$),"widget-sidebar-toggle":Jt.value,"onUpdate:widgetSidebarToggle":M[46]||(M[46]=$=>Jt.value=$),"widget-theme-toggle":Qt.value,"onUpdate:widgetThemeToggle":M[47]||(M[47]=$=>Qt.value=$)},null,8,["app-preferences-button-position","widget-fullscreen","widget-global-search","widget-language-toggle","widget-lock-screen","widget-notification","widget-refresh","widget-sidebar-toggle","widget-theme-toggle"])]),_:1},8,["title"]),p(e(we),{title:e(b)("preferences.footer.title")},{default:c(()=>[p(e(ni),{"footer-enable":We.value,"onUpdate:footerEnable":M[48]||(M[48]=$=>We.value=$),"footer-fixed":je.value,"onUpdate:footerFixed":M[49]||(M[49]=$=>je.value=$)},null,8,["footer-enable","footer-fixed"])]),_:1},8,["title"]),Ue.value?(d(),k(e(we),{key:0,title:e(b)("preferences.copyright.title")},{default:c(()=>[p(e(oi),{"copyright-company-name":Q.value,"onUpdate:copyrightCompanyName":M[50]||(M[50]=$=>Q.value=$),"copyright-company-site-link":re.value,"onUpdate:copyrightCompanySiteLink":M[51]||(M[51]=$=>re.value=$),"copyright-date":Le.value,"onUpdate:copyrightDate":M[52]||(M[52]=$=>Le.value=$),"copyright-enable":z.value,"onUpdate:copyrightEnable":M[53]||(M[53]=$=>z.value=$),"copyright-icp":Ve.value,"onUpdate:copyrightIcp":M[54]||(M[54]=$=>Ve.value=$),"copyright-icp-link":Rt.value,"onUpdate:copyrightIcpLink":M[55]||(M[55]=$=>Rt.value=$),disabled:!We.value},null,8,["copyright-company-name","copyright-company-site-link","copyright-date","copyright-enable","copyright-icp","copyright-icp-link","disabled"])]),_:1},8,["title"])):E("",!0)]),shortcutKey:c(()=>[p(e(we),{title:e(b)("preferences.shortcutKeys.global")},{default:c(()=>[p(e(vi),{"shortcut-keys-enable":Kt.value,"onUpdate:shortcutKeysEnable":M[56]||(M[56]=$=>Kt.value=$),"shortcut-keys-global-search":Ft.value,"onUpdate:shortcutKeysGlobalSearch":M[57]||(M[57]=$=>Ft.value=$),"shortcut-keys-lock-screen":jt.value,"onUpdate:shortcutKeysLockScreen":M[58]||(M[58]=$=>jt.value=$),"shortcut-keys-logout":Gt.value,"onUpdate:shortcutKeysLogout":M[59]||(M[59]=$=>Gt.value=$)},null,8,["shortcut-keys-enable","shortcut-keys-global-search","shortcut-keys-lock-screen","shortcut-keys-logout"])]),_:1},8,["title"])]),_:1},8,["modelValue","tabs"])])]),_:1},8,["description","title"])]))}}),Xa=T({__name:"preferences",setup(l){const[o,t]=Ua({connectedComponent:zi}),a=y(()=>{const n={};for(const[r,u]of Object.entries(U))for(const[i,h]of Object.entries(u))n[`${r}${ia(i)}`]=h;return n}),s=y(()=>{const n={};for(const[r,u]of Object.entries(U))if(typeof u=="object")for(const i of Object.keys(u))n[`update:${r}${ia(i)}`]=h=>{De({[r]:{[i]:h}}),r==="app"&&i==="locale"&&Sa(h)};else n[r]=u;return n});return(n,r)=>(d(),x("div",null,[p(e(o),ce(pe(pe({},n.$attrs),a.value),$a(s.value)),null,16),C("div",{onClick:r[0]||(r[0]=()=>e(t).open())},[L(n.$slots,"default",{},()=>[p(e(Pe),{title:e(b)("preferences.title"),class:"bg-primary flex-col-center size-10 cursor-pointer rounded-l-lg rounded-r-none border-none"},{default:c(()=>[p(e(Na),{class:"size-5"})]),_:1},8,["title"])])])]))}}),Ei=T({__name:"preferences-button",emits:["clearPreferencesAndLogout"],setup(l,{emit:o}){const t=o;function a(){t("clearPreferencesAndLogout")}return(s,n)=>(d(),k(Xa,{onClearPreferencesAndLogout:a},{default:c(()=>[p(e(qe),null,{default:c(()=>[p(e(Na),{class:"text-foreground size-4"})]),_:1})]),_:1}))}}),Ii={class:"hover:bg-accent ml-1 mr-2 cursor-pointer rounded-full p-1.5"},Ui={class:"hover:text-accent-foreground flex-center"},Hi={class:"ml-2 w-full"},Ai={key:0,class:"text-foreground mb-1 flex items-center text-sm font-medium"},Wi={class:"text-muted-foreground text-xs font-normal"},ru=T({name:"UserDropdown",__name:"user-dropdown",props:{avatar:{default:""},description:{default:""},enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]},tagText:{default:""},text:{default:""}},emits:["logout"],setup(l,{emit:o}){const t=l,a=o,s=F(!1),{globalLockScreenShortcutKey:n,globalLogoutShortcutKey:r}=Ye(),u=Dt(),[i,h]=nt({connectedComponent:tr}),[m,f]=nt({onConfirm(){X()}}),g=y(()=>lt()?"Alt":"⌥"),v=y(()=>t.enableShortcutKey&&r.value),V=y(()=>t.enableShortcutKey&&n.value),S=y(()=>t.enableShortcutKey&&U.shortcutKeys.enable);function I(){h.open()}function N(H){h.close(),u.lockScreen(H)}function K(){f.open(),s.value=!1}function X(){a("logout"),f.close()}if(S.value){const H=Ma();vt(H["Alt+KeyQ"],()=>{v.value&&K()}),vt(H["Alt+KeyL"],()=>{V.value&&I()})}return(H,D)=>(d(),x(G,null,[e(U).widget.lockScreen?(d(),k(e(i),{key:0,avatar:H.avatar,text:H.text,onSubmit:N},null,8,["avatar","text"])):E("",!0),p(e(m),{"cancel-text":e(b)("common.cancel"),"confirm-text":e(b)("common.confirm"),"fullscreen-button":!1,title:e(b)("common.prompt"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[B(_(e(b)("ui.widgets.logoutTip")),1)]),_:1},8,["cancel-text","confirm-text","title"]),p(e(It),null,{default:c(()=>[p(e(Ut),null,{default:c(()=>[C("div",Ii,[C("div",Ui,[p(e(ot),{alt:H.text,src:H.avatar,class:"size-8",dot:""},null,8,["alt","src"])])])]),_:1}),p(e(Ht),{class:"mr-2 min-w-[240px] p-0 pb-1"},{default:c(()=>{var Y;return[p(e(Tn),{class:"flex items-center p-3"},{default:c(()=>[p(e(ot),{alt:H.text,src:H.avatar,class:"size-12",dot:"","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["alt","src"]),C("div",Hi,[H.tagText||H.text||H.$slots.tagText?(d(),x("div",Ai,[B(_(H.text)+" ",1),L(H.$slots,"tagText",{},()=>[H.tagText?(d(),k(e(xn),{key:0,class:"ml-2 text-green-400"},{default:c(()=>[B(_(H.tagText),1)]),_:1})):E("",!0)])])):E("",!0),C("div",Wi,_(H.description),1)])]),_:3}),(Y=H.menus)!=null&&Y.length?(d(),k(e(Bt),{key:0})):E("",!0),e(U).widget.lockScreen?(d(),k(e(yt),{key:1,class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:I},{default:c(()=>[p(e(Aa),{class:"mr-2 size-4"}),B(" "+_(e(b)("ui.widgets.lockScreen.title"))+" ",1),V.value?(d(),k(e(pa),{key:0},{default:c(()=>[B(_(g.value)+" L ",1)]),_:1})):E("",!0)]),_:1})):E("",!0),e(U).widget.lockScreen?(d(),k(e(Bt),{key:2})):E("",!0),p(e(yt),{class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:K},{default:c(()=>[p(e(pn),{class:"mr-2 size-4"}),B(" "+_(e(b)("common.logout"))+" ",1),v.value?(d(),k(e(pa),{key:0},{default:c(()=>[B(_(g.value)+" Q ",1)]),_:1})):E("",!0)]),_:1})]}),_:3})]),_:3})],64))}}),Di=T({__name:"layout-content",props:{contentCompact:{},contentCompactWidth:{},padding:{},paddingBottom:{},paddingLeft:{},paddingRight:{},paddingTop:{}},setup(l){const o=l,{contentElement:t,overlayStyle:a}=so(),s=y(()=>{const{contentCompact:n,padding:r,paddingBottom:u,paddingLeft:i,paddingRight:h,paddingTop:m}=o,f=n==="compact"?{margin:"0 auto",width:`${o.contentCompactWidth}px`}:{};return Me(pe({},f),{flex:1,padding:`${r}px`,paddingBottom:`${u}px`,paddingLeft:`${i}px`,paddingRight:`${h}px`,paddingTop:`${m}px`})});return(n,r)=>(d(),x("main",{ref_key:"contentElement",ref:t,style:oe(s.value),class:"bg-background-deep relative"},[p(e(ro),{style:oe(e(a))},{default:c(()=>[L(n.$slots,"overlay")]),_:3},8,["style"]),L(n.$slots,"default")],4))}}),Ni=T({__name:"layout-footer",props:{fixed:{type:Boolean},height:{},show:{type:Boolean,default:!0},width:{},zIndex:{}},setup(l){const o=l,t=y(()=>{const{fixed:a,height:s,show:n,width:r,zIndex:u}=o;return{height:`${s}px`,marginBottom:n?"0":`-${s}px`,position:a?"fixed":"static",width:r,zIndex:u}});return(a,s)=>(d(),x("footer",{style:oe(t.value),class:"bg-background-deep bottom-0 w-full transition-all duration-200"},[L(a.$slots,"default")],4))}}),Oi=T({__name:"layout-header",props:{fullWidth:{type:Boolean},height:{},isMobile:{type:Boolean},show:{type:Boolean},sidebarWidth:{},theme:{},width:{},zIndex:{}},setup(l){const o=l,t=Ie(),a=y(()=>{const{fullWidth:n,height:r,show:u}=o,i=!u||!n?void 0:0;return{height:`${r}px`,marginTop:u?0:`-${r}px`,right:i}}),s=y(()=>({minWidth:`${o.isMobile?40:o.sidebarWidth}px`}));return(n,r)=>(d(),x("header",{class:A([n.theme,"border-border bg-header top-0 flex w-full flex-[0_0_auto] items-center border-b transition-[margin-top] duration-200"]),style:oe(a.value)},[e(t).logo?(d(),x("div",{key:0,style:oe(s.value)},[L(n.$slots,"logo")],4)):E("",!0),L(n.$slots,"toggle-button"),L(n.$slots,"default")],6))}}),ma=T({__name:"sidebar-collapse-button",props:{collapsed:{type:Boolean},collapsedModifiers:{}},emits:["update:collapsed"],setup(l){const o=w(l,"collapsed");function t(){o.value=!o.value}return(a,s)=>(d(),x("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 left-3 z-10 cursor-pointer rounded-sm p-1",onClick:xe(t,["stop"])},[o.value?(d(),k(e(io),{key:0,class:"size-4"})):(d(),k(e(No),{key:1,class:"size-4"}))]))}}),ha=T({__name:"sidebar-fixed-button",props:{expandOnHover:{type:Boolean},expandOnHoverModifiers:{}},emits:["update:expandOnHover"],setup(l){const o=w(l,"expandOnHover");function t(){o.value=!o.value}return(a,s)=>(d(),x("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 right-3 z-10 cursor-pointer rounded-sm p-[5px] transition-all duration-300",onClick:t},[o.value?(d(),k(e(Mt),{key:1,class:"size-3.5"})):(d(),k(e(Da),{key:0,class:"size-3.5"}))]))}}),Ri=T({__name:"layout-sidebar",props:me({collapseHeight:{default:42},collapseWidth:{default:48},domVisible:{type:Boolean,default:!0},extraWidth:{},fixedExtra:{type:Boolean,default:!1},headerHeight:{},isSidebarMixed:{type:Boolean,default:!1},marginTop:{default:0},mixedWidth:{default:70},paddingTop:{default:0},show:{type:Boolean,default:!0},showCollapseButton:{type:Boolean,default:!0},theme:{},width:{},zIndex:{default:0}},{collapse:{type:Boolean},collapseModifiers:{},extraCollapse:{type:Boolean},extraCollapseModifiers:{},expandOnHovering:{type:Boolean},expandOnHoveringModifiers:{},expandOnHover:{type:Boolean},expandOnHoverModifiers:{},extraVisible:{type:Boolean},extraVisibleModifiers:{}}),emits:me(["leave"],["update:collapse","update:extraCollapse","update:expandOnHovering","update:expandOnHover","update:extraVisible"]),setup(l,{emit:o}){const t=l,a=o,s=w(l,"collapse"),n=w(l,"extraCollapse"),r=w(l,"expandOnHovering"),u=w(l,"expandOnHover"),i=w(l,"extraVisible"),h=To(document.body),m=Ie(),f=bt(),g=y(()=>D(!0)),v=y(()=>{const{isSidebarMixed:P,marginTop:R,paddingTop:j,zIndex:ie}=t;return pe(Me(pe({"--scroll-shadow":"var(--sidebar)"},D(!1)),{height:`calc(100% - ${R}px)`,marginTop:`${R}px`,paddingTop:`${j}px`,zIndex:ie}),P&&i.value?{transition:"none"}:{})}),V=y(()=>{const{extraWidth:P,show:R,width:j,zIndex:ie}=t;return{left:`${j}px`,width:i.value&&R?`${P}px`:0,zIndex:ie}}),S=y(()=>{const{headerHeight:P}=t;return{height:`${P-1}px`}}),I=y(()=>{const{collapseWidth:P,fixedExtra:R,isSidebarMixed:j,mixedWidth:ie}=t;return j&&R?{width:`${s.value?P:ie}px`}:{}}),N=y(()=>{const{collapseHeight:P,headerHeight:R}=t;return pe({height:`calc(100% - ${R+P}px)`,paddingTop:"8px"},I.value)}),K=y(()=>{const{headerHeight:P,isSidebarMixed:R}=t;return pe(Me(pe({},R?{display:"flex",justifyContent:"center"}:{}),{height:`${P}px`}),I.value)}),X=y(()=>{const{collapseHeight:P,headerHeight:R}=t;return{height:`calc(100% - ${R+P}px)`}}),H=y(()=>({height:`${t.collapseHeight}px`}));Ta(()=>{i.value=t.fixedExtra?!0:i.value});function D(P){const{extraWidth:R,fixedExtra:j,isSidebarMixed:ie,show:ye,width:be}=t;let O=`${be+(ie&&j&&i.value?R:0)}px`;const{collapseWidth:q}=t;return P&&r.value&&!u.value&&(O=`${q}px`),Me(pe({},O==="0px"?{overflow:"hidden"}:{}),{flex:`0 0 ${O}`,marginLeft:ye?0:`-${O}`,maxWidth:O,minWidth:O,width:O})}function Y(P){(P==null?void 0:P.offsetX)<10||u.value||(r.value||(s.value=!1),t.isSidebarMixed&&(h.value=!0),r.value=!0)}function Z(){a("leave"),t.isSidebarMixed&&(h.value=!1),!u.value&&(r.value=!1,s.value=!0,i.value=!1)}return(P,R)=>(d(),x(G,null,[P.domVisible?(d(),x("div",{key:0,class:A([P.theme,"h-full transition-all duration-150"]),style:oe(g.value)},null,6)):E("",!0),C("aside",{class:A([[P.theme,{"bg-sidebar-deep":P.isSidebarMixed,"bg-sidebar border-border border-r":!P.isSidebarMixed}],"fixed left-0 top-0 h-full transition-all duration-150"]),style:oe(v.value),onMouseenter:Y,onMouseleave:Z},[!s.value&&!P.isSidebarMixed?(d(),k(e(ha),{key:0,"expand-on-hover":u.value,"onUpdate:expandOnHover":R[0]||(R[0]=j=>u.value=j)},null,8,["expand-on-hover"])):E("",!0),e(m).logo?(d(),x("div",{key:1,style:oe(K.value)},[L(P.$slots,"logo")],4)):E("",!0),p(e(st),{style:oe(N.value),shadow:"","shadow-border":""},{default:c(()=>[L(P.$slots,"default")]),_:3},8,["style"]),C("div",{style:oe(H.value)},null,4),P.showCollapseButton&&!P.isSidebarMixed?(d(),k(e(ma),{key:2,collapsed:s.value,"onUpdate:collapsed":R[1]||(R[1]=j=>s.value=j)},null,8,["collapsed"])):E("",!0),P.isSidebarMixed?(d(),x("div",{key:3,ref_key:"asideRef",ref:f,class:A([{"border-l":i.value},"border-border bg-sidebar fixed top-0 h-full overflow-hidden border-r transition-all duration-200"]),style:oe(V.value)},[P.isSidebarMixed&&u.value?(d(),k(e(ma),{key:0,collapsed:n.value,"onUpdate:collapsed":R[2]||(R[2]=j=>n.value=j)},null,8,["collapsed"])):E("",!0),n.value?E("",!0):(d(),k(e(ha),{key:1,"expand-on-hover":u.value,"onUpdate:expandOnHover":R[3]||(R[3]=j=>u.value=j)},null,8,["expand-on-hover"])),n.value?E("",!0):(d(),x("div",{key:2,style:oe(S.value),class:"pl-2"},[L(P.$slots,"extra-title")],4)),p(e(st),{style:oe(X.value),class:"border-border py-2",shadow:"","shadow-border":""},{default:c(()=>[L(P.$slots,"extra")]),_:3},8,["style"])],6)):E("",!0)],38)],64))}}),Ki=T({__name:"layout-tabbar",props:{height:{}},setup(l){const o=l,t=y(()=>{const{height:a}=o;return{height:`${a}px`}});return(a,s)=>(d(),x("section",{style:oe(t.value),class:"border-border bg-background flex w-full border-b transition-all"},[L(a.$slots,"default")],4))}});function Fi(l){const o=y(()=>l.isMobile?"sidebar-nav":l.layout),t=y(()=>o.value==="full-content"),a=y(()=>o.value==="sidebar-mixed-nav"),s=y(()=>o.value==="header-nav"),n=y(()=>o.value==="mixed-nav");return{currentLayout:o,isFullContent:t,isHeaderNav:s,isMixedNav:n,isSidebarMixedNav:a}}const Gi={class:"relative flex min-h-full w-full"},ji=T({name:"VbenLayout",__name:"vben-layout",props:me({contentCompact:{default:"wide"},contentCompactWidth:{default:1200},contentPadding:{default:0},contentPaddingBottom:{default:0},contentPaddingLeft:{default:0},contentPaddingRight:{default:0},contentPaddingTop:{default:0},footerEnable:{type:Boolean,default:!1},footerFixed:{type:Boolean,default:!0},footerHeight:{default:32},headerHeight:{default:50},headerHidden:{type:Boolean,default:!1},headerMode:{default:"fixed"},headerTheme:{},headerToggleSidebarButton:{type:Boolean,default:!0},headerVisible:{type:Boolean,default:!0},isMobile:{type:Boolean,default:!1},layout:{default:"sidebar-nav"},sidebarCollapse:{type:Boolean},sidebarCollapseShowTitle:{type:Boolean,default:!1},sidebarEnable:{type:Boolean},sidebarExtraCollapsedWidth:{default:60},sidebarHidden:{type:Boolean,default:!1},sidebarMixedWidth:{default:80},sidebarTheme:{default:"dark"},sidebarWidth:{default:180},sideCollapseWidth:{default:60},tabbarEnable:{type:Boolean,default:!0},tabbarHeight:{default:40},zIndex:{default:200}},{sidebarCollapse:{type:Boolean},sidebarCollapseModifiers:{},sidebarExtraVisible:{type:Boolean},sidebarExtraVisibleModifiers:{},sidebarExtraCollapse:{type:Boolean},sidebarExtraCollapseModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarEnable:{type:Boolean,default:!0},sidebarEnableModifiers:{}}),emits:me(["sideMouseLeave","toggleSidebar"],["update:sidebarCollapse","update:sidebarExtraVisible","update:sidebarExtraCollapse","update:sidebarExpandOnHover","update:sidebarEnable"]),setup(l,{emit:o}){const t=l,a=o,s=w(l,"sidebarCollapse"),n=w(l,"sidebarExtraVisible"),r=w(l,"sidebarExtraCollapse"),u=w(l,"sidebarExpandOnHover"),i=w(l,"sidebarEnable"),h=F(!1),m=F(!1),f=F(),{arrivedState:g,directions:v,isScrolling:V,y:S}=Vo(document),{setLayoutHeaderHeight:I}=uo(),{setLayoutFooterHeight:N}=co(),{y:K}=Bo({target:f,type:"client"}),{currentLayout:X,isFullContent:H,isHeaderNav:D,isMixedNav:Y,isSidebarMixedNav:Z}=Fi(t),P=y(()=>t.headerMode==="auto"),R=y(()=>{let z=0;return t.headerVisible&&!t.headerHidden&&(z+=t.headerHeight),t.tabbarEnable&&(z+=t.tabbarHeight),z}),j=y(()=>{const{sidebarCollapseShowTitle:z,sidebarMixedWidth:Q,sideCollapseWidth:re}=t;return z||Z.value?Q:re}),ie=y(()=>!D.value&&i.value),ye=y(()=>{const{headerHeight:z,isMobile:Q}=t;return Y.value&&!Q?z:0}),be=y(()=>{const{isMobile:z,sidebarHidden:Q,sidebarMixedWidth:re,sidebarWidth:Le}=t;let Ve=0;return Q||!ie.value||Q&&!Z.value&&!Y.value||(Z.value&&!z?Ve=re:s.value?Ve=z?0:j.value:Ve=Le),Ve}),O=y(()=>{const{sidebarExtraCollapsedWidth:z,sidebarWidth:Q}=t;return r.value?z:Q}),q=y(()=>X.value==="mixed-nav"||X.value==="sidebar-mixed-nav"||X.value==="sidebar-nav"),ve=y(()=>{const{headerMode:z}=t;return Y.value||z==="fixed"||z==="auto-scroll"||z==="auto"}),ge=y(()=>q.value&&i.value),Te=y(()=>!s.value&&t.isMobile),W=y(()=>{let z="100%",Q="unset";if(ve.value&&X.value!=="header-nav"&&X.value!=="mixed-nav"&&ge.value&&!t.isMobile)if(Z.value&&u.value&&n.value){const Le=s.value?j.value:t.sidebarMixedWidth,Ve=r.value?t.sidebarExtraCollapsedWidth:t.sidebarWidth;Q=`${Le+Ve}px`,z=`calc(100% - ${Q})`}else Q=h.value&&!u.value?`${j.value}px`:`${be.value}px`,z=`calc(100% - ${Q})`;return{sidebarAndExtraWidth:Q,width:z}}),ee=y(()=>{let z="",Q=0;if(!Y.value||t.sidebarHidden)z="100%";else if(i.value){const re=u.value?t.sidebarWidth:j.value;Q=s.value?j.value:re,z=`calc(100% - ${s.value?be.value:re}px)`}else z="100%";return{marginLeft:`${Q}px`,width:z}}),te=y(()=>{const z=ve.value,{footerEnable:Q,footerFixed:re,footerHeight:Le}=t;return{marginTop:z&&!H.value&&!m.value&&(!P.value||S.value<R.value)?`${R.value}px`:0,paddingBottom:`${Q&&re?Le:0}px`}}),ae=y(()=>{const{zIndex:z}=t,Q=Y.value?1:0;return z+Q}),le=y(()=>{const z=ve.value;return{height:H.value?"0":`${R.value}px`,left:Y.value?0:W.value.sidebarAndExtraWidth,position:z?"fixed":"static",top:m.value||H.value?`-${R.value}px`:0,width:W.value.width,"z-index":ae.value}}),fe=y(()=>{const{isMobile:z,zIndex:Q}=t;let re=z||q.value?1:-1;return Y.value&&(re+=1),Q+re}),Fe=y(()=>t.footerFixed?W.value.width:"100%"),Ae=y(()=>({zIndex:t.zIndex})),Ge=y(()=>t.isMobile||t.headerToggleSidebarButton&&q.value&&!Z.value&&!Y.value&&!t.isMobile),We=y(()=>!q.value||Y.value||t.isMobile);he(()=>t.isMobile,z=>{z&&(s.value=!0)},{immediate:!0}),he([()=>R.value,()=>H.value],([z])=>{I(H.value?0:z)},{immediate:!0}),he(()=>t.footerHeight,z=>{N(z)},{immediate:!0});{const z=()=>{K.value>R.value?m.value=!0:m.value=!1};he([()=>t.headerMode,()=>K.value],()=>{if(!P.value||Y.value||H.value){t.headerMode!=="auto-scroll"&&(m.value=!1);return}m.value=!0,z()},{immediate:!0})}{const z=Pt((Q,re,Le)=>{if(S.value<R.value){m.value=!1;return}if(Le){m.value=!1;return}Q?m.value=!1:re&&(m.value=!0)},300);he(()=>S.value,()=>{t.headerMode!=="auto-scroll"||Y.value||H.value||V.value&&z(v.top,v.bottom,g.top)})}function je(){s.value=!0}function Ue(){t.isMobile?s.value=!1:a("toggleSidebar")}return(z,Q)=>(d(),x("div",Gi,[ie.value?(d(),k(e(Ri),{key:0,collapse:s.value,"onUpdate:collapse":Q[0]||(Q[0]=re=>s.value=re),"expand-on-hover":u.value,"onUpdate:expandOnHover":Q[1]||(Q[1]=re=>u.value=re),"expand-on-hovering":h.value,"onUpdate:expandOnHovering":Q[2]||(Q[2]=re=>h.value=re),"extra-collapse":r.value,"onUpdate:extraCollapse":Q[3]||(Q[3]=re=>r.value=re),"extra-visible":n.value,"onUpdate:extraVisible":Q[4]||(Q[4]=re=>n.value=re),"collapse-width":j.value,"dom-visible":!z.isMobile,"extra-width":O.value,"fixed-extra":u.value,"header-height":e(Y)?0:z.headerHeight,"is-sidebar-mixed":e(Z),"margin-top":ye.value,"mixed-width":z.sidebarMixedWidth,show:ge.value,theme:z.sidebarTheme,width:be.value,"z-index":fe.value,onLeave:Q[5]||(Q[5]=()=>a("sideMouseLeave"))},gt({extra:c(()=>[L(z.$slots,"side-extra")]),"extra-title":c(()=>[L(z.$slots,"side-extra-title")]),default:c(()=>[e(Z)?L(z.$slots,"mixed-menu",{key:0}):L(z.$slots,"menu",{key:1})]),_:2},[q.value&&!e(Y)?{name:"logo",fn:c(()=>[L(z.$slots,"logo")]),key:"0"}:void 0]),1032,["collapse","expand-on-hover","expand-on-hovering","extra-collapse","extra-visible","collapse-width","dom-visible","extra-width","fixed-extra","header-height","is-sidebar-mixed","margin-top","mixed-width","show","theme","width","z-index"])):E("",!0),C("div",{ref_key:"contentRef",ref:f,class:"flex flex-1 flex-col overflow-hidden transition-all duration-300 ease-in"},[C("div",{class:A([[{"shadow-[0_16px_24px_hsl(var(--background))]":e(S)>20},e(po)],"overflow-hidden transition-all duration-200"]),style:oe(le.value)},[z.headerVisible?(d(),k(e(Oi),{key:0,"full-width":!q.value,height:z.headerHeight,"is-mobile":z.isMobile,show:!e(H)&&!z.headerHidden,"sidebar-width":z.sidebarWidth,theme:z.headerTheme,width:W.value.width,"z-index":ae.value},gt({"toggle-button":c(()=>[Ge.value?(d(),k(e(qe),{key:0,class:"my-0 ml-2 mr-1 rounded-md",onClick:Ue},{default:c(()=>[p(e(Yo),{class:"size-4"})]),_:1})):E("",!0)]),default:c(()=>[L(z.$slots,"header")]),_:2},[We.value?{name:"logo",fn:c(()=>[L(z.$slots,"logo")]),key:"0"}:void 0]),1032,["full-width","height","is-mobile","show","sidebar-width","theme","width","z-index"])):E("",!0),z.tabbarEnable?(d(),k(e(Ki),{key:1,height:z.tabbarHeight,style:oe(ee.value)},{default:c(()=>[L(z.$slots,"tabbar")]),_:3},8,["height","style"])):E("",!0)],6),p(e(Di),{"content-compact":z.contentCompact,"content-compact-width":z.contentCompactWidth,padding:z.contentPadding,"padding-bottom":z.contentPaddingBottom,"padding-left":z.contentPaddingLeft,"padding-right":z.contentPaddingRight,"padding-top":z.contentPaddingTop,style:oe(te.value),class:"transition-[margin-top] duration-200"},{overlay:c(()=>[L(z.$slots,"content-overlay")]),default:c(()=>[L(z.$slots,"content")]),_:3},8,["content-compact","content-compact-width","padding","padding-bottom","padding-left","padding-right","padding-top","style"]),z.footerEnable?(d(),k(e(Ni),{key:0,fixed:z.footerFixed,height:z.footerHeight,show:!e(H),width:Fe.value,"z-index":z.zIndex},{default:c(()=>[L(z.$slots,"footer")]),_:3},8,["fixed","height","show","width","z-index"])):E("",!0)],512),L(z.$slots,"extra"),Te.value?(d(),x("div",{key:1,style:oe(Ae.value),class:"bg-overlay fixed left-0 top-0 h-full w-full transition-[background-color] duration-200",onClick:je},null,4)):E("",!0)]))}}),qi={key:0,class:"relative size-full"},Xi=["src","onLoad"],Yi=T({name:"IFrameRouterView",__name:"iframe-router-view",setup(l){const o=F([]),t=ut(),a=Re(),s=y(()=>U.tabbar.enable),n=y(()=>s.value?t.getTabs.filter(g=>{var v;return!!((v=g.meta)!=null&&v.iframeSrc)}):a.meta.iframeSrc?[a]:[]),r=y(()=>new Set(n.value.map(g=>g.name))),u=y(()=>n.value.length>0);function i(g){return g.name===a.name}function h(g){const{meta:v,name:V}=g;return!V||!t.renderRouteView?!1:s.value?!(v!=null&&v.keepAlive)&&r.value.has(V)&&V!==a.name?!1:t.getTabs.some(S=>S.name===V):i(g)}function m(g){o.value[g]=!1}function f(g){const v=o.value[g];return v===void 0?!0:v}return(g,v)=>u.value?(d(!0),x(G,{key:0},se(n.value,(V,S)=>(d(),x(G,{key:V.fullPath},[h(V)?Se((d(),x("div",qi,[p(e(Ka),{spinning:f(S)},null,8,["spinning"]),C("iframe",{src:V.meta.iframeSrc,class:"size-full",onLoad:I=>m(S)},null,40,Xi)],512)),[[_e,i(V)]]):E("",!0)],64))),128)):E("",!0)}}),Zi={class:"relative h-full"},Qi=T({name:"LayoutContent",__name:"content",setup(l){const o=ut(),{keepAlive:t}=Ye(),{getCachedTabs:a,getExcludeCachedTabs:s,renderRouteView:n}=ya(o);function r(i){const{tabbar:h,transition:m}=U,f=m.name;if(!(!f||!m.enable))return!h.enable||!t,f}function u(i,h){var g;if(!i){console.error("Component view not found，please check the route configuration");return}const m=h.name;if(!m)return i;const f=(g=i==null?void 0:i.type)==null?void 0:g.name;return f||f===m||(i.type||(i.type={}),i.type.name=m),i}return(i,h)=>(d(),x("div",Zi,[p(e(Yi)),p(e(fo),null,{default:c(({Component:m,route:f})=>[p(Je,{name:r(f),appear:"",mode:"out-in"},{default:c(()=>[e(t)?(d(),k(Lo,{key:0,exclude:e(s),include:e(a)},[e(n)?Se((d(),k(ze(u(m,f)),{key:f.fullPath})),[[_e,!f.meta.iframeSrc]]):E("",!0)],1032,["exclude","include"])):e(n)?(d(),k(ze(m),{key:f.fullPath})):E("",!0)]),_:2},1032,["name"])]),_:1})]))}});function Ji(){const l=F(!1),o=F(0),t=et(),a=500,s=y(()=>U.transition.loading),n=()=>{if(!s.value)return;const r=performance.now()-o.value;r<a?setTimeout(()=>{l.value=!1},a-r):l.value=!1};return t.beforeEach(r=>(r.meta.loaded||!s.value||r.meta.iframeSrc||(o.value=performance.now(),l.value=!0),!0)),t.afterEach(r=>(r.meta.loaded||!s.value||r.meta.iframeSrc||n(),!0)),{spinning:l}}const ed=T({name:"LayoutContentSpinner",__name:"content-spinner",setup(l){const{spinning:o}=Ji();return(t,a)=>(d(),k(e(Ka),{spinning:e(o)},null,8,["spinning"]))}}),td={class:"flex-center text-muted-foreground relative h-full w-full text-xs"},ad=T({name:"LayoutFooter",__name:"footer",setup(l){return(o,t)=>(d(),x("div",td,[L(o.$slots,"default")]))}}),ld={class:"flex-center hidden lg:block"},od={class:"flex h-full min-w-0 flex-1 items-center"},nd={class:"flex h-full min-w-0 flex-shrink-0 items-center"},He=50,sd=T({name:"LayoutHeader",__name:"header",props:{theme:{default:"light"}},emits:["clearPreferencesAndLogout"],setup(l,{emit:o}){const t=o,a=xt(),{globalSearchShortcutKey:s,preferencesButtonPosition:n}=Ye(),r=Ie(),{refresh:u}=Ga(),i=y(()=>{const f=[{index:He+100,name:"user-dropdown"}];return U.widget.globalSearch&&f.push({index:He,name:"global-search"}),n.value.header&&f.push({index:He+10,name:"preferences"}),U.widget.themeToggle&&f.push({index:He+20,name:"theme-toggle"}),U.widget.languageToggle&&f.push({index:He+30,name:"language-toggle"}),U.widget.fullscreen&&f.push({index:He+40,name:"fullscreen"}),U.widget.notification&&f.push({index:He+50,name:"notification"}),Object.keys(r).forEach(g=>{const v=g.split("-");g.startsWith("header-right")&&f.push({index:Number(v[2]),name:g})}),f.sort((g,v)=>g.index-v.index)}),h=y(()=>{const f=[];return U.widget.refresh&&f.push({index:0,name:"refresh"}),Object.keys(r).forEach(g=>{const v=g.split("-");g.startsWith("header-left")&&f.push({index:Number(v[2]),name:g})}),f.sort((g,v)=>g.index-v.index)});function m(){t("clearPreferencesAndLogout")}return(f,g)=>(d(),x(G,null,[(d(!0),x(G,null,se(h.value.filter(v=>v.index<He),v=>L(f.$slots,v.name,{key:v.name},()=>[v.name==="refresh"?(d(),k(e(qe),{key:0,class:"my-0 mr-1 rounded-md",onClick:e(u)},{default:c(()=>[p(e(At),{class:"size-4"})]),_:1},8,["onClick"])):E("",!0)])),128)),C("div",ld,[L(f.$slots,"breadcrumb")]),(d(!0),x(G,null,se(h.value.filter(v=>v.index>He),v=>L(f.$slots,v.name,{key:v.name})),128)),C("div",od,[L(f.$slots,"menu")]),C("div",nd,[(d(!0),x(G,null,se(i.value,v=>L(f.$slots,v.name,{key:v.name},()=>[v.name==="global-search"?(d(),k(e(Hs),{key:0,"enable-shortcut-key":e(s),menus:e(a).accessMenus,class:"mr-1 sm:mr-4"},null,8,["enable-shortcut-key","menus"])):v.name==="preferences"?(d(),k(e(Ei),{key:1,class:"mr-1",onClearPreferencesAndLogout:m})):v.name==="theme-toggle"?(d(),k(e(jo),{key:2,class:"mr-1 mt-[2px]"})):v.name==="language-toggle"?(d(),k(e(qo),{key:3,class:"mr-1"})):v.name==="fullscreen"?(d(),k(e(os),{key:4,class:"mr-1"})):E("",!0)])),128))])],64))}}),rd=["onClick","onMouseenter"],id=T({name:"NormalMenu",__name:"normal-menu",props:{activePath:{default:""},collapse:{type:Boolean,default:!1},menus:{default:()=>[]},rounded:{type:Boolean},theme:{default:"dark"}},emits:["enter","select"],setup(l,{emit:o}){const t=l,a=o,{b:s,e:n,is:r}=Oe("normal-menu");function u(i){return t.activePath===i.path&&i.activeIcon||i.icon}return(i,h)=>(d(),x("ul",{class:A([[i.theme,e(s)(),e(r)("collapse",i.collapse),e(r)(i.theme,!0),e(r)("rounded",i.rounded)],"relative"])},[(d(!0),x(G,null,se(i.menus,m=>(d(),x("li",{key:m.path,class:A([e(n)("item"),e(r)("active",i.activePath===m.path)]),onClick:()=>a("select",m),onMouseenter:()=>a("enter",m)},[p(e(Be),{class:A(e(n)("icon")),icon:u(m),fallback:""},null,8,["class","icon"]),C("span",{class:A([e(n)("name"),"truncate"])},_(m.name),3)],42,rd))),128))],2))}}),dd=$e(id,[["__scopeId","data-v-8f493e90"]]);function Ya(l,o){var a,s;let t=l.parent;for(;t&&!o.includes((s=(a=t==null?void 0:t.type)==null?void 0:a.name)!=null?s:"");)t=t.parent;return t}const ht=l=>{const o=Array.isArray(l)?l:[l],t=[];return o.forEach(a=>{var s;Array.isArray(a)?t.push(...ht(a)):da(a)&&Array.isArray(a.children)?t.push(...ht(a.children)):(t.push(a),da(a)&&((s=a.component)!=null&&s.subTree)&&t.push(...ht(a.component.subTree)))}),t};function Za(){const l=St();if(!l)throw new Error("instance is required");const o=y(()=>{var n;let a=l.parent;const s=[l.props.path];for(;(a==null?void 0:a.type.name)!=="Menu";)a!=null&&a.props.path&&s.unshift(a.props.path),a=(n=a==null?void 0:a.parent)!=null?n:null;return s});return{parentMenu:y(()=>Ya(l,["Menu","SubMenu"])),parentPaths:o}}function Qa(l){return y(()=>{var t;return{"--menu-level":l?(t=l==null?void 0:l.level)!=null?t:1:0}})}const Ja=Symbol("menuContext");function ud(l){Va(Ja,l)}function el(l){const o=St();Va(`subMenu:${o==null?void 0:o.uid}`,l)}function Nt(){if(!St())throw new Error("instance is required");return Ba(Ja)}function tl(){const l=St();if(!l)throw new Error("instance is required");const o=Ya(l,["Menu","SubMenu"]);return Ba(`subMenu:${o==null?void 0:o.uid}`)}const cd=T({name:"CollapseTransition",__name:"collapse-transition",setup(l){const o=a=>{a.style.maxHeight="",a.style.overflow=a.dataset.oldOverflow,a.style.paddingTop=a.dataset.oldPaddingTop,a.style.paddingBottom=a.dataset.oldPaddingBottom},t={afterEnter(a){a.style.maxHeight="",a.style.overflow=a.dataset.oldOverflow},afterLeave(a){o(a)},beforeEnter(a){a.dataset||(a.dataset={}),a.dataset.oldPaddingTop=a.style.paddingTop,a.dataset.oldMarginTop=a.style.marginTop,a.dataset.oldPaddingBottom=a.style.paddingBottom,a.dataset.oldMarginBottom=a.style.marginBottom,a.style.height&&(a.dataset.elExistsHeight=a.style.height),a.style.maxHeight=0,a.style.paddingTop=0,a.style.marginTop=0,a.style.paddingBottom=0,a.style.marginBottom=0},beforeLeave(a){a.dataset||(a.dataset={}),a.dataset.oldPaddingTop=a.style.paddingTop,a.dataset.oldMarginTop=a.style.marginTop,a.dataset.oldPaddingBottom=a.style.paddingBottom,a.dataset.oldMarginBottom=a.style.marginBottom,a.dataset.oldOverflow=a.style.overflow,a.style.maxHeight=`${a.scrollHeight}px`,a.style.overflow="hidden"},enter(a){requestAnimationFrame(()=>{a.dataset.oldOverflow=a.style.overflow,a.dataset.elExistsHeight?a.style.maxHeight=a.dataset.elExistsHeight:a.scrollHeight===0?a.style.maxHeight=0:a.style.maxHeight=`${a.scrollHeight}px`,a.style.paddingTop=a.dataset.oldPaddingTop,a.style.paddingBottom=a.dataset.oldPaddingBottom,a.style.marginTop=a.dataset.oldMarginTop,a.style.marginBottom=a.dataset.oldMarginBottom,a.style.overflow="hidden"})},enterCancelled(a){o(a)},leave(a){a.scrollHeight!==0&&(a.style.maxHeight=0,a.style.paddingTop=0,a.style.paddingBottom=0,a.style.marginTop=0,a.style.marginBottom=0)},leaveCancelled(a){o(a)}};return(a,s)=>(d(),k(Je,ce({name:"collapse-transition"},$a(t)),{default:c(()=>[L(a.$slots,"default")]),_:3},16))}}),ba=T({name:"SubMenuContent",__name:"sub-menu-content",props:{isMenuMore:{type:Boolean,default:!1},isTopLevelMenuSubmenu:{type:Boolean},level:{default:0},activeIcon:{},disabled:{type:Boolean},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(l){const o=l,t=Nt(),{b:a,e:s,is:n}=Oe("sub-menu-content"),r=Oe("menu"),u=y(()=>t==null?void 0:t.openedMenus.includes(o.path)),i=y(()=>t.props.collapse),h=y(()=>o.level===1),m=y(()=>t.props.collapseShowTitle&&h.value&&i.value),f=y(()=>t==null?void 0:t.props.mode),g=y(()=>f.value==="horizontal"||!(h.value&&i.value)),v=y(()=>f.value==="vertical"&&h.value&&i.value&&!m.value),V=y(()=>f.value==="horizontal"&&!h.value||f.value==="vertical"&&i.value?Et:Lt),S=y(()=>u.value?{transform:"rotate(180deg)"}:{});return(I,N)=>(d(),x("div",{class:A([e(a)(),e(n)("collapse-show-title",m.value),e(n)("more",I.isMenuMore)])},[L(I.$slots,"default"),I.isMenuMore?E("",!0):(d(),k(e(Be),{key:0,class:A(e(r).e("icon")),icon:I.icon,fallback:""},null,8,["class","icon"])),v.value?E("",!0):(d(),x("div",{key:1,class:A([e(s)("title")])},[L(I.$slots,"title")],2)),I.isMenuMore?E("",!0):Se((d(),k(ze(V.value),{key:2,class:A([[e(s)("icon-arrow")],"size-4"]),style:oe(S.value)},null,8,["class","style"])),[[_e,g.value]])],2))}}),al=T({name:"SubMenu",__name:"sub-menu",props:{isSubMenuMore:{type:Boolean,default:!1},activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(l){var be;const o=l,{parentMenu:t,parentPaths:a}=Za(),{b:s,is:n}=Oe("sub-menu"),r=Oe("menu"),u=Nt(),i=tl(),h=Qa(i),m=F(!1),f=F({}),g=F({}),v=F(null);el({addSubMenu:Y,handleMouseleave:j,level:((be=i==null?void 0:i.level)!=null?be:0)+1,mouseInChild:m,removeSubMenu:Z});const V=y(()=>u==null?void 0:u.openedMenus.includes(o.path)),S=y(()=>{var O;return((O=t.value)==null?void 0:O.type.name)==="Menu"}),I=y(()=>{var O;return(O=u==null?void 0:u.props.mode)!=null?O:"vertical"}),N=y(()=>u==null?void 0:u.props.rounded),K=y(()=>{var O;return(O=i==null?void 0:i.level)!=null?O:0}),X=y(()=>K.value===1),H=y(()=>{const O=I.value==="horizontal",q=O&&X.value?"bottom":"right";return{collisionPadding:{top:20},side:q,sideOffset:O?5:10}}),D=y(()=>{let O=!1;return Object.values(f.value).forEach(q=>{q.active&&(O=!0)}),Object.values(g.value).forEach(q=>{q.active&&(O=!0)}),O});function Y(O){g.value[O.path]=O}function Z(O){Reflect.deleteProperty(g.value,O.path)}function P(){const O=u==null?void 0:u.props.mode;o.disabled||u!=null&&u.props.collapse&&O==="vertical"||O==="horizontal"||u==null||u.handleSubMenuClick({active:D.value,parentPaths:a.value,path:o.path})}function R(O,q=300){var ve,ge;if(O.type!=="focus"){if(!(u!=null&&u.props.collapse)&&(u==null?void 0:u.props.mode)==="vertical"||o.disabled){i&&(i.mouseInChild.value=!0);return}i&&(i.mouseInChild.value=!0),v.value&&window.clearTimeout(v.value),v.value=setTimeout(()=>{u==null||u.openMenu(o.path,a.value)},q),(ge=(ve=t.value)==null?void 0:ve.vnode.el)==null||ge.dispatchEvent(new MouseEvent("mouseenter"))}}function j(O=!1){var q;if(!(u!=null&&u.props.collapse)&&(u==null?void 0:u.props.mode)==="vertical"&&i){i.mouseInChild.value=!1;return}v.value&&window.clearTimeout(v.value),i&&(i.mouseInChild.value=!1),v.value=setTimeout(()=>{!m.value&&(u==null||u.closeMenu(o.path,a.value))},300),O&&((q=i==null?void 0:i.handleMouseleave)==null||q.call(i,!0))}const ie=y(()=>D.value&&o.activeIcon||o.icon),ye=it({active:D,parentPaths:a,path:o.path});return Ke(()=>{var O,q;(O=i==null?void 0:i.addSubMenu)==null||O.call(i,ye),(q=u==null?void 0:u.addSubMenu)==null||q.call(u,ye)}),La(()=>{var O,q;(O=i==null?void 0:i.removeSubMenu)==null||O.call(i,ye),(q=u==null?void 0:u.removeSubMenu)==null||q.call(u,ye)}),(O,q)=>(d(),x("li",{class:A([e(s)(),e(n)("opened",V.value),e(n)("active",D.value),e(n)("disabled",O.disabled)]),onFocus:R,onMouseenter:R,onMouseleave:q[3]||(q[3]=()=>j())},[e(u).isMenuPopup?(d(),k(e(ss),{key:0,"content-class":[e(u).theme,e(r).e("popup-container"),e(n)(e(u).theme,!0),V.value?"":"hidden"],"content-props":H.value,open:!0,"open-delay":0},{trigger:c(()=>[p(ba,{class:A(e(n)("active",D.value)),icon:ie.value,"is-menu-more":O.isSubMenuMore,"is-top-level-menu-submenu":S.value,level:K.value,path:O.path,onClick:xe(P,["stop"])},{title:c(()=>[L(O.$slots,"title")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"])]),default:c(()=>[C("div",{class:A([e(r).is(I.value,!0),e(r).e("popup")]),onFocus:q[0]||(q[0]=ve=>R(ve,100)),onMouseenter:q[1]||(q[1]=ve=>R(ve,100)),onMouseleave:q[2]||(q[2]=()=>j(!0))},[C("ul",{class:A([e(r).b(),e(n)("rounded",N.value)]),style:oe(e(h))},[L(O.$slots,"default")],6)],34)]),_:3},8,["content-class","content-props"])):(d(),x(G,{key:1},[p(ba,{class:A(e(n)("active",D.value)),icon:ie.value,"is-menu-more":O.isSubMenuMore,"is-top-level-menu-submenu":S.value,level:K.value,path:O.path,onClick:xe(P,["stop"])},{title:c(()=>[L(O.$slots,"title")]),default:c(()=>[L(O.$slots,"content")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"]),p(cd,null,{default:c(()=>[Se(C("ul",{class:A([e(r).b(),e(n)("rounded",N.value)]),style:oe(e(h))},[L(O.$slots,"default")],6),[[_e,V.value]])]),_:3})],64))],34))}}),pd=T({name:"Menu",__name:"menu",props:{accordion:{type:Boolean,default:!0},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{default:"vertical"},rounded:{type:Boolean,default:!0},theme:{default:"dark"}},emits:["close","open","select"],setup(l,{emit:o}){const t=l,a=o,{b:s,is:n}=Oe("menu"),r=Qa(),u=Ie(),i=F(),h=F(-1),m=F(t.defaultOpeneds&&!t.collapse?[...t.defaultOpeneds]:[]),f=F(t.defaultActive),g=F({}),v=F({}),V=F(!1),S=y(()=>t.mode==="horizontal"||t.mode==="vertical"&&t.collapse),I=y(()=>{var le,fe;const W=(fe=(le=u.default)==null?void 0:le.call(u))!=null?fe:[],ee=ht(W),te=h.value===-1?ee:ee.slice(0,h.value),ae=h.value===-1?[]:ee.slice(h.value);return{showSlotMore:ae.length>0,slotDefault:te,slotMore:ae}});he(()=>t.collapse,W=>{W&&(m.value=[])}),he(g.value,P),he(()=>t.defaultActive,(W="")=>{g.value[W]||(f.value=""),R(W)});let N;Ta(()=>{t.mode==="horizontal"?N=Po(i,Y).stop:N==null||N()}),ud(it({activePath:f,addMenuItem:q,addSubMenu:ve,closeMenu:be,handleMenuItemClick:j,handleSubMenuClick:ie,isMenuPopup:S,openedMenus:m,openMenu:O,props:t,removeMenuItem:Te,removeSubMenu:ge,subMenus:v,theme:zo(t,"theme"),items:g})),el({addSubMenu:ve,level:1,mouseInChild:V,removeSubMenu:ge});function K(W){const ee=getComputedStyle(W),te=Number.parseInt(ee.marginLeft,10),ae=Number.parseInt(ee.marginRight,10);return W.offsetWidth+te+ae||0}function X(){var Ge,We,je;if(!i.value)return-1;const W=[...(We=(Ge=i.value)==null?void 0:Ge.childNodes)!=null?We:[]].filter(Ue=>Ue.nodeName!=="#comment"&&(Ue.nodeName!=="#text"||Ue.nodeValue)),ee=46,te=getComputedStyle(i==null?void 0:i.value),ae=Number.parseInt(te.paddingLeft,10),le=Number.parseInt(te.paddingRight,10),fe=((je=i.value)==null?void 0:je.clientWidth)-ae-le;let Fe=0,Ae=0;return W.forEach((Ue,z)=>{Fe+=K(Ue),Fe<=fe-ee&&(Ae=z+1)}),Ae===W.length?-1:Ae}function H(W,ee=33.34){let te;return()=>{te&&clearTimeout(te),te=setTimeout(()=>{W()},ee)}}let D=!0;function Y(){if(h.value===X())return;const W=()=>{h.value=-1,Ne(()=>{h.value=X()})};W(),D?W():H(W)(),D=!1}function Z(){const W=f.value&&g.value[f.value];return!W||t.mode==="horizontal"||t.collapse?[]:W.parentPaths}function P(){Z().forEach(ee=>{const te=v.value[ee];te&&O(ee,te.parentPaths)})}function R(W){const ee=g.value,te=ee[W]||f.value&&ee[f.value]||ee[t.defaultActive||""];f.value=te?te.path:W}function j(W){const{collapse:ee,mode:te}=t;(te==="horizontal"||ee)&&(m.value=[]);const{parentPaths:ae,path:le}=W;!le||!ae||(zt(le)||(f.value=le),a("select",le,ae))}function ie({parentPaths:W,path:ee}){m.value.includes(ee)?be(ee,W):O(ee,W)}function ye(W){const ee=m.value.indexOf(W);ee!==-1&&m.value.splice(ee,1)}function be(W,ee){var te,ae;t.accordion&&(m.value=(ae=(te=v.value[W])==null?void 0:te.parentPaths)!=null?ae:[]),ye(W),a("close",W,ee)}function O(W,ee){if(!m.value.includes(W)){if(t.accordion){const te=Z();te.includes(W)&&(ee=te),m.value=m.value.filter(ae=>ee.includes(ae))}m.value.push(W),a("open",W,ee)}}function q(W){g.value[W.path]=W}function ve(W){v.value[W.path]=W}function ge(W){Reflect.deleteProperty(v.value,W.path)}function Te(W){Reflect.deleteProperty(g.value,W.path)}return(W,ee)=>(d(),x("ul",{ref_key:"menu",ref:i,class:A([W.theme,e(s)(),e(n)(W.mode,!0),e(n)(W.theme,!0),e(n)("rounded",W.rounded),e(n)("collapse",W.collapse)]),style:oe(e(r)),role:"menu"},[W.mode==="horizontal"&&I.value.showSlotMore?(d(),x(G,{key:0},[(d(!0),x(G,null,se(I.value.slotDefault,te=>(d(),k(ze(te),{key:te.key}))),128)),p(al,{"is-sub-menu-more":"",path:"sub-menu-more"},{title:c(()=>[p(e(Oo),{class:"size-4"})]),default:c(()=>[(d(!0),x(G,null,se(I.value.slotMore,te=>(d(),k(ze(te),{key:te.key}))),128))]),_:1})],64)):L(W.$slots,"default",{key:1})],6))}}),fd={class:"relative mr-1 flex size-1.5"},md=T({__name:"menu-badge-dot",props:{dotClass:{default:""},dotStyle:{default:()=>({})}},setup(l){return(o,t)=>(d(),x("span",fd,[C("span",{class:A([o.dotClass,"absolute inline-flex h-full w-full animate-ping rounded-full opacity-75"]),style:oe(o.dotStyle)},null,6),C("span",{class:A([o.dotClass,"relative inline-flex size-1.5 rounded-full"]),style:oe(o.dotStyle)},null,6)]))}}),ll=T({__name:"menu-badge",props:{hasChildren:{type:Boolean},badge:{},badgeType:{},badgeVariants:{}},setup(l){const o=l,t={default:"bg-green-500",destructive:"bg-destructive",primary:"bg-primary",success:"bg-green-500",warning:"bg-yellow-500"},a=y(()=>o.badgeType==="dot"),s=y(()=>{const{badgeVariants:r}=o;return r?t[r]||r:t.default}),n=y(()=>s.value&&Eo(s.value)?{backgroundColor:s.value}:{});return(r,u)=>a.value||r.badge?(d(),x("span",{key:0,class:A([r.$attrs.class,"absolute"])},[a.value?(d(),k(md,{key:0,"dot-class":s.value,"dot-style":n.value},null,8,["dot-class","dot-style"])):(d(),x("div",{key:1,class:A([s.value,"text-primary-foreground flex-center rounded-xl px-1.5 py-0.5 text-[10px]"]),style:oe(n.value)},_(r.badge),7))],2)):E("",!0)}}),hd=T({name:"MenuItem",__name:"menu-item",props:{activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},emits:["click"],setup(l,{emit:o}){const t=l,a=o,s=Ie(),{b:n,e:r,is:u}=Oe("menu-item"),i=Oe("menu"),h=Nt(),m=tl(),{parentMenu:f,parentPaths:g}=Za(),v=y(()=>t.path===(h==null?void 0:h.activePath)),V=y(()=>v.value&&t.activeIcon||t.icon),S=y(()=>{var H;return((H=f.value)==null?void 0:H.type.name)==="Menu"}),I=y(()=>{var H;return((H=h.props)==null?void 0:H.collapseShowTitle)&&S.value&&h.props.collapse}),N=y(()=>{var H;return h.props.mode==="vertical"&&S.value&&((H=h.props)==null?void 0:H.collapse)&&s.title}),K=it({active:v,parentPaths:g.value,path:t.path||""});function X(){var H;t.disabled||((H=h==null?void 0:h.handleMenuItemClick)==null||H.call(h,{parentPaths:g.value,path:t.path}),a("click",K))}return Ke(()=>{var H,D;(H=m==null?void 0:m.addSubMenu)==null||H.call(m,K),(D=h==null?void 0:h.addMenuItem)==null||D.call(h,K)}),La(()=>{var H,D;(H=m==null?void 0:m.removeSubMenu)==null||H.call(m,K),(D=h==null?void 0:h.removeMenuItem)==null||D.call(h,K)}),(H,D)=>(d(),x("li",{class:A([e(h).theme,e(n)(),e(u)("active",v.value),e(u)("disabled",H.disabled),e(u)("collapse-show-title",I.value)]),role:"menuitem",onClick:xe(X,["stop"])},[N.value?(d(),k(e(tt),{key:0,"content-class":[e(h).theme],side:"right"},{trigger:c(()=>[C("div",{class:A([e(i).be("tooltip","trigger")])},[p(e(Be),{class:A(e(i).e("icon")),icon:V.value,fallback:""},null,8,["class","icon"]),L(H.$slots,"default"),I.value?(d(),x("span",{key:0,class:A(e(i).e("name"))},[L(H.$slots,"title")],2)):E("",!0)],2)]),default:c(()=>[L(H.$slots,"title")]),_:3},8,["content-class"])):E("",!0),Se(C("div",{class:A([e(r)("content")])},[e(h).props.mode!=="horizontal"?(d(),k(e(ll),ce({key:0,class:"right-2"},t),null,16)):E("",!0),p(e(Be),{class:A(e(i).e("icon")),icon:V.value},null,8,["class","icon"]),L(H.$slots,"default"),L(H.$slots,"title")],2),[[_e,!N.value]])],2))}}),ol=T({name:"SubMenuUi",__name:"sub-menu",props:{menu:{}},setup(l){const o=l,t=y(()=>{const{menu:a}=o;return Reflect.has(a,"children")&&!!a.children&&a.children.length>0});return(a,s)=>t.value?(d(),k(e(al),{key:`${a.menu.path}_sub`,"active-icon":a.menu.activeIcon,icon:a.menu.icon,path:a.menu.path},{content:c(()=>[p(e(ll),{badge:a.menu.badge,"badge-type":a.menu.badgeType,"badge-variants":a.menu.badgeVariants,class:"right-6"},null,8,["badge","badge-type","badge-variants"])]),title:c(()=>[C("span",null,_(a.menu.name),1)]),default:c(()=>[(d(!0),x(G,null,se(a.menu.children||[],n=>(d(),k(ol,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},8,["active-icon","icon","path"])):(d(),k(e(hd),{key:a.menu.path,"active-icon":a.menu.activeIcon,badge:a.menu.badge,"badge-type":a.menu.badgeType,"badge-variants":a.menu.badgeVariants,icon:a.menu.icon,path:a.menu.path},{title:c(()=>[C("span",null,_(a.menu.name),1)]),_:1},8,["active-icon","badge","badge-type","badge-variants","icon","path"]))}}),nl=T({name:"MenuView",__name:"menu",props:{menus:{},accordion:{type:Boolean},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},theme:{}},setup(l){const t=Xe(l);return(a,s)=>(d(),k(e(pd),Ce(Ze(e(t))),{default:c(()=>[(d(!0),x(G,null,se(a.menus,n=>(d(),k(ol,{key:n.path,menu:n},null,8,["menu"]))),128))]),_:1},16))}});function Ot(){const l=et(),o=l.getRoutes(),t=new Map;return o.forEach(s=>{t.set(s.path,s)}),{navigation:s=>ne(this,null,function*(){var i;const n=t.get(s),{openInNewWindow:r=!1,query:u={}}=(i=n==null?void 0:n.meta)!=null?i:{};zt(s)?Io(s,{target:"_blank"}):r?Uo(s):yield l.push({path:s,query:u})})}}const bd=T({__name:"extra-menu",props:{collapse:{type:Boolean},menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},theme:{}},setup(l){const o=Re(),{navigation:t}=Ot();function a(s){return ne(this,null,function*(){yield t(s)})}return(s,n)=>{var r;return d(),k(e(nl),{accordion:s.accordion,collapse:s.collapse,"default-active":((r=e(o).meta)==null?void 0:r.activePath)||e(o).path,menus:s.menus,rounded:s.rounded,theme:s.theme,mode:"vertical",onSelect:a},null,8,["accordion","collapse","default-active","menus","rounded","theme"])}}}),va=T({__name:"menu",props:{menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapse:{type:Boolean},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},theme:{}},emits:["select"],setup(l,{emit:o}){const t=l,a=o;function s(n){a("select",n,t.mode)}return(n,r)=>(d(),k(e(nl),{accordion:n.accordion,collapse:n.collapse,"collapse-show-title":n.collapseShowTitle,"default-active":n.defaultActive,menus:n.menus,mode:n.mode,rounded:n.rounded,theme:n.theme,onSelect:s},null,8,["accordion","collapse","collapse-show-title","default-active","menus","mode","rounded","theme"]))}}),vd=T({__name:"mixed-menu",props:{activePath:{},collapse:{type:Boolean},menus:{},rounded:{type:Boolean},theme:{}},emits:["defaultSelect","enter","select"],setup(l,{emit:o}){const t=l,a=o,s=Re();return Pa(()=>{const n=Wt(t.menus||[],s.path);if(n){const r=(t.menus||[]).find(u=>{var i;return u.path===((i=n.parents)==null?void 0:i[0])});a("defaultSelect",n,r)}}),(n,r)=>(d(),k(e(dd),{"active-path":n.activePath,collapse:n.collapse,menus:n.menus,rounded:n.rounded,theme:n.theme,onEnter:r[0]||(r[0]=u=>a("enter",u)),onSelect:r[1]||(r[1]=u=>a("select",u))},null,8,["active-path","collapse","menus","rounded","theme"]))}});function gd(){const l=xt(),{navigation:o}=Ot(),t=y(()=>l.accessMenus),a=Re(),s=F([]),n=F(!1),r=F(""),u=f=>ne(this,null,function*(){var v,V,S;s.value=(v=f==null?void 0:f.children)!=null?v:[],r.value=(S=(V=f.parents)==null?void 0:V[0])!=null?S:f.path;const g=s.value.length>0;n.value=g,g||(yield o(f.path))}),i=(f,g)=>{var v,V,S;s.value=(v=g==null?void 0:g.children)!=null?v:[],r.value=(S=(V=f.parents)==null?void 0:V[0])!=null?S:f.path,U.sidebar.expandOnHover&&(n.value=s.value.length>0)},h=()=>{var V,S;if(U.sidebar.expandOnHover)return;n.value=!1;const{findMenu:f,rootMenu:g,rootMenuPath:v}=mt(t.value,a.path);r.value=(V=v!=null?v:f==null?void 0:f.path)!=null?V:"",s.value=(S=g==null?void 0:g.children)!=null?S:[]},m=f=>{var g,v,V;if(!U.sidebar.expandOnHover){const{findMenu:S}=mt(t.value,f.path);s.value=(g=S==null?void 0:S.children)!=null?g:[],r.value=(V=(v=f.parents)==null?void 0:v[0])!=null?V:f.path,n.value=s.value.length>0}};return he(()=>a.path,f=>{var I,N,K;const g=((I=a.meta)==null?void 0:I.activePath)||f,{findMenu:v,rootMenu:V,rootMenuPath:S}=mt(t.value,g);r.value=(N=S!=null?S:v==null?void 0:v.path)!=null?N:"",s.value=(K=V==null?void 0:V.children)!=null?K:[]},{immediate:!0}),{extraActiveMenu:r,extraMenus:s,handleDefaultSelect:i,handleMenuMouseEnter:m,handleMixedMenuSelect:u,handleSideMouseLeave:h,sidebarExtraVisible:n}}function yd(){const{navigation:l}=Ot(),o=xt(),t=Re(),a=F([]),s=F(""),{isMixedNav:n}=Ye(),r=y(()=>U.navigation.split&&n.value),u=y(()=>{const S=U.sidebar.enable;return r.value?S&&a.value.length>0:S}),i=y(()=>o.accessMenus),h=y(()=>r.value?i.value.map(S=>Me(pe({},S),{children:[]})):i.value),m=y(()=>r.value?a.value:i.value),f=y(()=>{var S,I;return(I=(S=t==null?void 0:t.meta)==null?void 0:S.activePath)!=null?I:t.path}),g=y(()=>r.value?s.value:t.path),v=(S,I)=>{var K,X;if(!r.value||I==="vertical"){l(S);return}const N=i.value.find(H=>H.path===S);s.value=(K=N==null?void 0:N.path)!=null?K:"",a.value=(X=N==null?void 0:N.children)!=null?X:[],a.value.length===0&&l(S)};function V(S=t.path){var N,K;let{rootMenu:I}=mt(i.value,S);I||(I=i.value.find(X=>X.path===S)),s.value=(N=I==null?void 0:I.path)!=null?N:"",a.value=(K=I==null?void 0:I.children)!=null?K:[]}return he(()=>t.path,S=>{var N,K;const I=(K=(N=t==null?void 0:t.meta)==null?void 0:N.activePath)!=null?K:S;V(I)},{immediate:!0}),Pa(()=>{var S;V(((S=t.meta)==null?void 0:S.activePath)||t.path)}),{handleMenuSelect:v,headerActive:g,headerMenus:h,sidebarActive:f,sidebarMenus:m,sidebarVisible:u}}const wd={class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold"},xd=T({__name:"tool-more",props:{menus:{}},setup(l){return(o,t)=>(d(),k(e(ls),{menus:o.menus,modal:!1},{default:c(()=>[C("div",wd,[p(e(Lt),{class:"size-4"})])]),_:1},8,["menus"]))}}),kd=T({__name:"tool-screen",props:{screen:{type:Boolean},screenModifiers:{}},emits:["update:screen"],setup(l){const o=w(l,"screen");function t(){o.value=!o.value}return(a,s)=>(d(),x("div",{class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold",onClick:t},[o.value?(d(),k(e(Wa),{key:0,class:"size-4"})):(d(),k(e(Ha),{key:1,class:"size-4"}))]))}}),Sd=["data-index","onClick"],_d={class:"relative flex size-full items-center"},Md={class:"absolute right-1.5 top-1/2 z-[3] translate-y-[-50%] overflow-hidden"},Cd={class:"text-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mx-3 mr-4 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pr-3 transition-all duration-300"},$d={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},Td=T({name:"VbenTabs",inheritAttrs:!1,__name:"tabs",props:me({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{},maxWidth:{},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]}},{active:{},activeModifiers:{}}),emits:me(["close","unpin"],["update:active"]),setup(l,{emit:o}){const t=l,a=o,s=w(l,"active"),n=y(()=>({brisk:{content:"h-full after:content-['']  after:absolute after:bottom-0 after:left-0 after:w-full after:h-[1.5px] after:bg-primary after:scale-x-0 after:transition-[transform] after:ease-out after:duration-300 hover:after:scale-x-100 after:origin-left [&.is-active]:after:scale-x-100 [&:not(:first-child)]:border-l last:border-r last:border-r border-border"},card:{content:"h-[calc(100%-6px)] rounded-md ml-2 border border-border  transition-all"},plain:{content:"h-full [&:not(:first-child)]:border-l last:border-r border-border"}})[t.styleType||"plain"]||{content:""}),r=y(()=>t.tabs.map(u=>{const{fullPath:i,meta:h,name:m,path:f}=u||{},{affixTab:g,icon:v,newTabTitle:V,tabClosable:S,title:I}=h||{};return{affixTab:!!g,closable:Reflect.has(h,"tabClosable")?!!S:!0,fullPath:i,icon:v,key:i||f,meta:h,name:m,path:f,title:V||I||m}}));return(u,i)=>(d(),x("div",{class:A([u.contentClass,"relative !flex h-full w-max items-center overflow-hidden pr-6"])},[p(wt,{name:"slide-left"},{default:c(()=>[(d(!0),x(G,null,se(r.value,(h,m)=>(d(),x("div",{key:h.key,class:A([[{"is-active dark:bg-accent bg-primary/15":h.key===s.value,draggable:!h.affixTab,"affix-tab":h.affixTab},n.value.content],"tab-item [&:not(.is-active)]:hover:bg-accent translate-all group relative flex cursor-pointer select-none"]),"data-index":m,"data-tab-item":"true",onClick:f=>s.value=h.key},[p(e(Ra),{"handler-data":h,menus:u.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[C("div",_d,[C("div",Md,[Se(p(e(_t),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground dark:group-[.is-active]:text-accent-foreground group-[.is-active]:text-primary size-3 cursor-pointer rounded-full transition-all",onClick:xe(()=>a("close",h.key),["stop"])},null,8,["onClick"]),[[_e,!h.affixTab&&r.value.length>1&&h.closable]]),Se(p(e(Mt),{class:"hover:bg-accent hover:stroke-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:xe(()=>a("unpin",h),["stop"])},null,8,["onClick"]),[[_e,h.affixTab&&r.value.length>1&&h.closable]])]),C("div",Cd,[u.showIcon?(d(),k(e(Be),{key:0,icon:h.icon,class:"mr-2 flex size-4 items-center overflow-hidden",fallback:""},null,8,["icon"])):E("",!0),C("span",$d,_(h.title),1)])])]),_:2},1032,["handler-data","menus"])],10,Sd))),128))]),_:1})],2))}}),Vd=["data-active-tab","data-index","onClick"],Bd={class:"relative size-full px-1"},Ld={key:0,class:"tabs-chrome__divider bg-border absolute left-[var(--gap)] top-1/2 z-0 h-4 w-[1px] translate-y-[-50%] transition-all"},Pd={class:"tabs-chrome__extra absolute right-[var(--gap)] top-1/2 z-[3] size-4 translate-y-[-50%]"},zd={class:"tabs-chrome__item-main group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground text-accent-foreground z-[2] mx-[calc(var(--gap)*2)] my-0 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pl-2 pr-4 duration-150"},Ed={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},Id=T({name:"VbenTabsChrome",inheritAttrs:!1,__name:"tabs",props:me({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{default:7},maxWidth:{},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]}},{active:{},activeModifiers:{}}),emits:me(["close","unpin"],["update:active"]),setup(l,{emit:o}){const t=l,a=o,s=w(l,"active"),n=F(),r=F(),u=y(()=>{const{gap:h}=t;return{"--gap":`${h}px`}}),i=y(()=>t.tabs.map(h=>{const{fullPath:m,meta:f,name:g,path:v}=h||{},{affixTab:V,icon:S,newTabTitle:I,tabClosable:N,title:K}=f||{};return{affixTab:!!V,closable:Reflect.has(f,"tabClosable")?!!N:!0,fullPath:m,icon:S,key:m||v,meta:f,name:g,path:v,title:I||K||g}}));return(h,m)=>(d(),x("div",{ref_key:"contentRef",ref:n,class:A([h.contentClass,"tabs-chrome !flex h-full w-max overflow-y-hidden pr-6"]),style:oe(u.value)},[p(wt,{name:"slide-left"},{default:c(()=>[(d(!0),x(G,null,se(i.value,(f,g)=>(d(),x("div",{key:f.key,ref_for:!0,ref_key:"tabRef",ref:r,class:A([[{"is-active":f.key===s.value,draggable:!f.affixTab,"affix-tab":f.affixTab}],"tabs-chrome__item draggable translate-all group relative -mr-3 flex h-full select-none items-center"]),"data-active-tab":s.value,"data-index":g,"data-tab-item":"true",onClick:v=>s.value=f.key},[p(e(Ra),{"handler-data":f,menus:h.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[C("div",Bd,[g!==0&&f.key!==s.value?(d(),x("div",Ld)):E("",!0),m[0]||(m[0]=C("div",{class:"tabs-chrome__background absolute z-[-1] size-full px-[calc(var(--gap)-1px)] py-0 transition-opacity duration-150"},[C("div",{class:"tabs-chrome__background-content group-[.is-active]:bg-primary/15 dark:group-[.is-active]:bg-accent h-full rounded-tl-[var(--gap)] rounded-tr-[var(--gap)] duration-150"}),C("svg",{class:"tabs-chrome__background-before group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 left-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[C("path",{d:"M 0 7 A 7 7 0 0 0 7 0 L 7 7 Z"})]),C("svg",{class:"tabs-chrome__background-after group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 right-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[C("path",{d:"M 0 0 A 7 7 0 0 0 7 7 L 0 7 Z"})])],-1)),C("div",Pd,[Se(p(e(_t),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[2px] size-3 cursor-pointer rounded-full transition-all",onClick:xe(()=>a("close",f.key),["stop"])},null,8,["onClick"]),[[_e,!f.affixTab&&i.value.length>1&&f.closable]]),Se(p(e(Mt),{class:"hover:text-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:xe(()=>a("unpin",f),["stop"])},null,8,["onClick"]),[[_e,f.affixTab&&i.value.length>1&&f.closable]])]),C("div",zd,[h.showIcon?(d(),k(e(Be),{key:0,icon:f.icon,class:"mr-1 flex size-4 items-center overflow-hidden"},null,8,["icon"])):E("",!0),C("span",Ed,_(f.title),1)])])]),_:2},1032,["handler-data","menus"])],10,Vd))),128))]),_:1})],6))}}),Ud=$e(Id,[["__scopeId","data-v-bae60330"]]);function Vt(l){const o="group";return l.classList.contains(o)?l:l.closest(`.${o}`)}function Hd(l,o){const t=F(null);function a(){return ne(this,null,function*(){var i;yield Ne();const n=(i=document.querySelectorAll(`.${l.contentClass}`))==null?void 0:i[0];if(!n){console.warn("Element not found for sortable initialization");return}const r=()=>ne(this,null,function*(){var h;n.style.cursor="default",(h=n.querySelector(".draggable"))==null||h.classList.remove("dragging")}),{initializeSortable:u}=ho(n,{filter:(h,m)=>{const f=Vt(m);return!(f==null?void 0:f.classList.contains("draggable"))||!l.draggable},onEnd(h){const{newIndex:m,oldIndex:f}=h,{srcElement:g}=h.originalEvent;if(!g){r();return}const v=Vt(g);if(!v){r();return}if(!v.classList.contains("draggable")){r();return}f!==void 0&&m!==void 0&&!Number.isNaN(f)&&!Number.isNaN(m)&&f!==m&&o("sortTabs",f,m),r()},onMove(h){const m=Vt(h.related);if(m!=null&&m.classList.contains("draggable")&&l.draggable){const f=h.dragged.classList.contains("affix-tab"),g=h.related.classList.contains("affix-tab");return f===g}else return!1},onStart:()=>{var h;n.style.cursor="grabbing",(h=n.querySelector(".draggable"))==null||h.classList.add("dragging")}});t.value=yield u()})}function s(){return ne(this,null,function*(){const{isMobile:n}=mo();n.value||(yield Ne(),a())})}Ke(s),he(()=>l.styleType,()=>{var n;(n=t.value)==null||n.destroy(),s()}),kt(()=>{var n;(n=t.value)==null||n.destroy()})}function Ad(l){let o=null,t=null,a=0;const s=F(null),n=F(null),r=F(!1),u=F(!0),i=F(!1);function h(){var K;const S=(K=s.value)==null?void 0:K.$el;if(!S||!n.value)return{};const I=S.clientWidth,N=n.value.clientWidth;return{scrollbarWidth:I,scrollViewWidth:N}}function m(S,I=150){var X;const{scrollbarWidth:N,scrollViewWidth:K}=h();!N||!K||N>K||(X=n.value)==null||X.scrollBy({behavior:"smooth",left:S==="left"?-(N-I):+(N-I)})}function f(){return ne(this,null,function*(){var N,K;yield Ne();const S=(N=s.value)==null?void 0:N.$el;if(!S)return;const I=S==null?void 0:S.querySelector("div[data-radix-scroll-area-viewport]");n.value=I,v(),yield Ne(),g(),o==null||o.disconnect(),o=new ResizeObserver(ua(X=>{v(),g()},100)),o.observe(I),a=((K=l.tabs)==null?void 0:K.length)||0,t==null||t.disconnect(),t=new MutationObserver(()=>{const X=I.querySelectorAll('div[data-tab-item="true"]').length;X>a&&g(),X!==a&&(v(),a=X)}),t.observe(I,{attributes:!1,childList:!0,subtree:!0})})}function g(){return ne(this,null,function*(){if(!n.value)return;yield Ne();const S=n.value,{scrollbarWidth:I}=h(),{scrollWidth:N}=S;I>=N||requestAnimationFrame(()=>{const K=S==null?void 0:S.querySelector(".is-active");K==null||K.scrollIntoView({behavior:"smooth",inline:"start"})})})}function v(){return ne(this,null,function*(){if(!n.value)return;const{scrollbarWidth:S}=h();r.value=n.value.scrollWidth>S})}const V=ua(({left:S,right:I})=>{u.value=S,i.value=I},100);return he(()=>l.active,()=>ne(this,null,function*(){g()}),{flush:"post"}),he(()=>l.styleType,()=>{f()}),Ke(f),kt(()=>{o==null||o.disconnect(),t==null||t.disconnect(),o=null,t=null}),{handleScrollAt:V,initScrollbar:f,scrollbarRef:s,scrollDirection:m,scrollIsAtLeft:u,scrollIsAtRight:i,showScrollButton:r}}const Wd={class:"flex h-full flex-1 overflow-hidden"},Dd=T({name:"TabsView",__name:"tabs-view",props:{active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{},draggable:{type:Boolean,default:!0},gap:{},maxWidth:{},minWidth:{},showIcon:{type:Boolean},styleType:{default:"chrome"},tabs:{}},emits:["close","sortTabs","unpin"],setup(l,{emit:o}){const t=l,a=o,s=Ee(t,a),{handleScrollAt:n,scrollbarRef:r,scrollDirection:u,scrollIsAtLeft:i,scrollIsAtRight:h,showScrollButton:m}=Ad(t);return Hd(t,a),(f,g)=>(d(),x("div",Wd,[Se(C("span",{class:A([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(i),"pointer-events-none opacity-30":e(i)},"border-r px-2"]),onClick:g[0]||(g[0]=v=>e(u)("left"))},[p(e(Ro),{class:"size-4 h-full"})],2),[[_e,e(m)]]),C("div",{class:A([{"pt-[3px]":f.styleType==="chrome"},"size-full flex-1 overflow-hidden"])},[p(e(st),{ref_key:"scrollbarRef",ref:r,"shadow-bottom":!1,"shadow-top":!1,class:"h-full",horizontal:"","scroll-bar-class":"z-10 hidden ",shadow:"","shadow-left":"","shadow-right":"",onScrollAt:e(n)},{default:c(()=>[f.styleType==="chrome"?(d(),k(e(Ud),Ce(ce({key:0},pe(pe(pe({},e(s)),f.$attrs),f.$props))),null,16)):(d(),k(e(Td),Ce(ce({key:1},pe(pe(pe({},e(s)),f.$attrs),f.$props))),null,16))]),_:1},8,["onScrollAt"])],2),Se(C("span",{class:A([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(h),"pointer-events-none opacity-30":e(h)},"hover:bg-muted text-muted-foreground cursor-pointer border-l px-2"]),onClick:g[1]||(g[1]=v=>e(u)("right"))},[p(e(Et),{class:"size-4 h-full"})],2),[[_e,e(m)]])]))}});function Nd(){const l=et(),o=Re(),t=xt(),a=ut(),{contentIsMaximize:s,toggleMaximize:n}=Fa(),{closeAllTabs:r,closeCurrentTab:u,closeLeftTabs:i,closeOtherTabs:h,closeRightTabs:m,closeTabByKey:f,getTabDisableState:g,openTabInNewWindow:v,refreshTab:V,toggleTabPin:S}=Ia(),I=y(()=>o.fullPath),{locale:N}=ga(),K=F();he([()=>a.getTabs,()=>a.updateTime,()=>N.value],([P])=>{K.value=P.map(R=>Y(R))});const X=()=>{const P=Ho(l.getRoutes(),R=>{var j;return!!((j=R.meta)!=null&&j.affixTab)});a.setAffixTabs(P)},H=P=>{l.push(P)},D=P=>ne(this,null,function*(){yield f(P)});function Y(P){var R;return Me(pe({},P),{meta:Me(pe({},P==null?void 0:P.meta),{title:b((R=P==null?void 0:P.meta)==null?void 0:R.title)})})}return he(()=>t.accessMenus,()=>{X()},{immediate:!0}),he(()=>o.path,()=>{var R,j;const P=(j=(R=o.matched)==null?void 0:R[o.matched.length-1])==null?void 0:j.meta;a.addTab(Me(pe({},o),{meta:P||o.meta}))},{immediate:!0}),{createContextMenus:P=>{var ge,Te;const{disabledCloseAll:R,disabledCloseCurrent:j,disabledCloseLeft:ie,disabledCloseOther:ye,disabledCloseRight:be,disabledRefresh:O}=g(P),q=(Te=(ge=P==null?void 0:P.meta)==null?void 0:ge.affixTab)!=null?Te:!1;return[{disabled:j,handler:()=>ne(this,null,function*(){yield u(P)}),icon:_t,key:"close",text:b("preferences.tabbar.contextMenu.close")},{handler:()=>ne(this,null,function*(){yield S(P)}),icon:q?Da:Mt,key:"affix",text:q?b("preferences.tabbar.contextMenu.unpin"):b("preferences.tabbar.contextMenu.pin")},{handler:()=>ne(this,null,function*(){s.value||(yield l.push(P.fullPath)),n()}),icon:s.value?Wa:Ha,key:s.value?"restore-maximize":"maximize",text:s.value?b("preferences.tabbar.contextMenu.restoreMaximize"):b("preferences.tabbar.contextMenu.maximize")},{disabled:O,handler:V,icon:At,key:"reload",text:b("preferences.tabbar.contextMenu.reload")},{handler:()=>ne(this,null,function*(){yield v(P)}),icon:un,key:"open-in-new-window",separator:!0,text:b("preferences.tabbar.contextMenu.openInNewWindow")},{disabled:ie,handler:()=>ne(this,null,function*(){yield i(P)}),icon:an,key:"close-left",text:b("preferences.tabbar.contextMenu.closeLeft")},{disabled:be,handler:()=>ne(this,null,function*(){yield m(P)}),icon:on,key:"close-right",separator:!0,text:b("preferences.tabbar.contextMenu.closeRight")},{disabled:ye,handler:()=>ne(this,null,function*(){yield h(P)}),icon:cn,key:"close-other",text:b("preferences.tabbar.contextMenu.closeOther")},{disabled:R,handler:r,icon:ln,key:"close-all",text:b("preferences.tabbar.contextMenu.closeAll")}]},currentActive:I,currentTabs:K,handleClick:H,handleClose:D}}const Od={class:"flex-center h-full"},Rd=T({name:"LayoutTabbar",__name:"tabbar",props:{showIcon:{type:Boolean},theme:{}},setup(l){const o=Re(),t=ut(),{contentIsMaximize:a,toggleMaximize:s}=Fa(),{unpinTab:n}=Ia(),{createContextMenus:r,currentActive:u,currentTabs:i,handleClick:h,handleClose:m}=Nd(),f=y(()=>{const g=t.getTabByPath(u.value);return r(g).map(V=>Me(pe({},V),{label:V.text,value:V.key}))});return U.tabbar.persist||t.closeOtherTabs(o),(g,v)=>(d(),x(G,null,[p(e(Dd),{active:e(u),class:A(g.theme),"context-menus":e(r),draggable:e(U).tabbar.draggable,"show-icon":g.showIcon,"style-type":e(U).tabbar.styleType,tabs:e(i),onClose:e(m),onSortTabs:e(t).sortTabs,onUnpin:e(n),"onUpdate:active":e(h)},null,8,["active","class","context-menus","draggable","show-icon","style-type","tabs","onClose","onSortTabs","onUnpin","onUpdate:active"]),C("div",Od,[e(U).tabbar.showMore?(d(),k(e(xd),{key:0,menus:f.value},null,8,["menus"])):E("",!0),e(U).tabbar.showMaximize?(d(),k(e(kd),{key:1,screen:e(a),onChange:e(s),"onUpdate:screen":e(s)},null,8,["screen","onChange","onUpdate:screen"])):E("",!0)])],64))}}),iu=T({name:"BasicLayout",__name:"layout",emits:["clearPreferencesAndLogout"],setup(l,{emit:o}){const t=o,{isDark:a,isHeaderNav:s,isMixedNav:n,isMobile:r,isSideMixedNav:u,layout:i,preferencesButtonPosition:h,sidebarCollapsed:m,theme:f}=Ye(),g=Dt(),{refresh:v}=Ga(),V=y(()=>a.value||U.theme.semiDarkSidebar?"dark":"light"),S=y(()=>a.value||U.theme.semiDarkHeader?"dark":"light"),I=y(()=>{const{collapsedShowTitle:ae}=U.sidebar,le=[];return ae&&m.value&&!n.value&&le.push("mx-auto"),u.value&&le.push("flex-center"),le.join(" ")}),N=y(()=>U.navigation.styleType==="rounded"),K=y(()=>r.value&&m.value?!0:s.value||n.value?!1:m.value||u.value),X=y(()=>!r.value&&(s.value||n.value)),{extraActiveMenu:H,extraMenus:D,handleDefaultSelect:Y,handleMenuMouseEnter:Z,handleMixedMenuSelect:P,handleSideMouseLeave:R,sidebarExtraVisible:j}=gd(),{handleMenuSelect:ie,headerActive:ye,headerMenus:be,sidebarActive:O,sidebarMenus:q,sidebarVisible:ve}=yd();function ge(ae){return _a(ae,le=>Me(pe({},Ao(le)),{name:b(le.name)}))}function Te(){De({sidebar:{hidden:!U.sidebar.hidden}})}function W(){t("clearPreferencesAndLogout")}he(()=>U.app.layout,ae=>ne(this,null,function*(){ae==="sidebar-mixed-nav"&&U.sidebar.hidden&&De({sidebar:{hidden:!1}})})),he(()=>U.app.locale,v);const ee=Ie(),te=y(()=>Object.keys(ee).filter(ae=>ae.startsWith("header-")));return(ae,le)=>(d(),k(e(ji),{"sidebar-extra-visible":e(j),"onUpdate:sidebarExtraVisible":le[0]||(le[0]=fe=>Ca(j)?j.value=fe:null),"content-compact":e(U).app.contentCompact,"footer-enable":e(U).footer.enable,"footer-fixed":e(U).footer.fixed,"header-hidden":e(U).header.hidden,"header-mode":e(U).header.mode,"header-theme":S.value,"header-toggle-sidebar-button":e(U).widget.sidebarToggle,"header-visible":e(U).header.enable,"is-mobile":e(U).app.isMobile,layout:e(i),"sidebar-collapse":e(U).sidebar.collapsed,"sidebar-collapse-show-title":e(U).sidebar.collapsedShowTitle,"sidebar-enable":e(ve),"sidebar-expand-on-hover":e(U).sidebar.expandOnHover,"sidebar-extra-collapse":e(U).sidebar.extraCollapse,"sidebar-hidden":e(U).sidebar.hidden,"sidebar-theme":V.value,"sidebar-width":e(U).sidebar.width,"tabbar-enable":e(U).tabbar.enable,"tabbar-height":e(U).tabbar.height,onSideMouseLeave:e(R),onToggleSidebar:Te,"onUpdate:sidebarCollapse":le[1]||(le[1]=fe=>e(De)({sidebar:{collapsed:fe}})),"onUpdate:sidebarEnable":le[2]||(le[2]=fe=>e(De)({sidebar:{enable:fe}})),"onUpdate:sidebarExpandOnHover":le[3]||(le[3]=fe=>e(De)({sidebar:{expandOnHover:fe}})),"onUpdate:sidebarExtraCollapse":le[4]||(le[4]=fe=>e(De)({sidebar:{extraCollapse:fe}}))},gt({logo:c(()=>[e(U).logo.enable?(d(),k(e(fa),{key:0,class:A(I.value),collapsed:K.value,src:e(U).logo.source,text:e(U).app.name,theme:X.value?S.value:e(f)},null,8,["class","collapsed","src","text","theme"])):E("",!0)]),header:c(()=>[p(e(sd),{theme:e(f),onClearPreferencesAndLogout:W},gt({"user-dropdown":c(()=>[L(ae.$slots,"user-dropdown")]),notification:c(()=>[L(ae.$slots,"notification")]),_:2},[!X.value&&e(U).breadcrumb.enable?{name:"breadcrumb",fn:c(()=>[p(e(ms),{"hide-when-only-one":e(U).breadcrumb.hideOnlyOne,"show-home":e(U).breadcrumb.showHome,"show-icon":e(U).breadcrumb.showIcon,type:e(U).breadcrumb.styleType},null,8,["hide-when-only-one","show-home","show-icon","type"])]),key:"0"}:void 0,X.value?{name:"menu",fn:c(()=>[p(e(va),{"default-active":e(ye),menus:ge(e(be)),rounded:N.value,theme:S.value,class:"w-full",mode:"horizontal",onSelect:e(ie)},null,8,["default-active","menus","rounded","theme","onSelect"])]),key:"1"}:void 0,se(te.value,fe=>({name:fe,fn:c(()=>[L(ae.$slots,fe)])}))]),1032,["theme"])]),menu:c(()=>[p(e(va),{accordion:e(U).navigation.accordion,collapse:e(U).sidebar.collapsed,"collapse-show-title":e(U).sidebar.collapsedShowTitle,"default-active":e(O),menus:ge(e(q)),rounded:N.value,theme:V.value,mode:"vertical",onSelect:e(ie)},null,8,["accordion","collapse","collapse-show-title","default-active","menus","rounded","theme","onSelect"])]),"mixed-menu":c(()=>[p(e(vd),{"active-path":e(H),menus:ge(e(be)),rounded:N.value,theme:V.value,onDefaultSelect:e(Y),onEnter:e(Z),onSelect:e(P)},null,8,["active-path","menus","rounded","theme","onDefaultSelect","onEnter","onSelect"])]),"side-extra":c(()=>[p(e(bd),{accordion:e(U).navigation.accordion,collapse:e(U).sidebar.extraCollapse,menus:ge(e(D)),rounded:N.value,theme:V.value},null,8,["accordion","collapse","menus","rounded","theme"])]),"side-extra-title":c(()=>[e(U).logo.enable?(d(),k(e(fa),{key:0,text:e(U).app.name,theme:e(f)},null,8,["text","theme"])):E("",!0)]),tabbar:c(()=>[e(U).tabbar.enable?(d(),k(e(Rd),{key:0,"show-icon":e(U).tabbar.showIcon,theme:e(f)},null,8,["show-icon","theme"])):E("",!0)]),content:c(()=>[p(e(Qi))]),extra:c(()=>[L(ae.$slots,"extra"),e(U).app.enableCheckUpdates?(d(),k(e(hs),{key:0,"check-updates-interval":e(U).app.checkUpdatesInterval},null,8,["check-updates-interval"])):E("",!0),e(U).widget.lockScreen?(d(),k(Je,{key:1,name:"slide-up"},{default:c(()=>[e(g).isLockScreen?L(ae.$slots,"lock-screen",{key:0}):E("",!0)]),_:3})):E("",!0),e(h).fixed?(d(),k(e(Xa),{key:2,class:"z-100 fixed bottom-20 right-0",onClearPreferencesAndLogout:W})):E("",!0),p(e(Dn))]),_:2},[e(U).transition.loading?{name:"content-overlay",fn:c(()=>[p(e(ed))]),key:"0"}:void 0,e(U).footer.enable?{name:"footer",fn:c(()=>[p(e(ad),null,{default:c(()=>[e(U).copyright.enable?(d(),k(e(Xo),Ce(ce({key:0},e(U).copyright)),null,16)):E("",!0)]),_:1})]),key:"1"}:void 0]),1032,["sidebar-extra-visible","content-compact","footer-enable","footer-fixed","header-hidden","header-mode","header-theme","header-toggle-sidebar-button","header-visible","is-mobile","layout","sidebar-collapse","sidebar-collapse-show-title","sidebar-enable","sidebar-expand-on-hover","sidebar-extra-collapse","sidebar-hidden","sidebar-theme","sidebar-width","tabbar-enable","tabbar-height","onSideMouseLeave"]))}});export{su as N,ru as _,nu as a,iu as b,Yi as c,ms as d,hs as e,Hs as f,tr as g,Xa as h,Ei as i};
