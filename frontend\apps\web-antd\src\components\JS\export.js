function saveJSON(data, filename) {
  if (!data) {
    alert('保存的数据为空');
    return;
  }
  if (!filename) filename = 'json.json';
  if (typeof data === 'object') {
    data = JSON.stringify(data, undefined, 4);
  }
  const a = document.createElement('a');
  const blob = new Blob([data], { type: 'text/json' });
  const e = document.createEvent('MouseEvents');
  a.download = filename;
  a.href = window.URL.createObjectURL(blob);
  a.dataset.downloadurl = ['text/json', a.download, a.href].join(':');
  e.initMouseEvent(
    'click',
    true,
    false,
    window,
    0,
    0,
    0,
    0,
    0,
    false,
    false,
    false,
    false,
    0,
    null,
  );
  a.dispatchEvent(e);
}
export default saveJSON;
export function clipCanvas(canvas, { x, y, width, height }) {
  return new Promise((resolve, reject) => {
    canvas.toBlob((blob) => {
      const a = new FileReader();
      a.addEventListener('load', (e) => {
        const img = new Image();
        img.addEventListener('load', () => {
          const canvas1 = document.createElement('canvas');
          canvas1.width = width;
          canvas1.height = height;
          canvas1.getContext('2d').drawImage(img, -x, -y);
          resolve(canvas1);
        });
        img.src = e.target.result;
      });
      a.readAsDataURL(blob);
    });
  });
}
