import{ao as e,k as r,aP as a,l as t,ay as o,j as l}from"./chunks/framework.C8U7mBUf.js";const s=JSON.parse('{"title":"社区交流","description":"","frontmatter":{},"headers":[],"relativePath":"commercial/community.md","filePath":"commercial/community.md"}');const c=e({name:"commercial/community.md"},[["render",function(e,s,c,i,m,n){const h=o("NolebaseGitContributors"),p=o("NolebaseGitChangelog");return l(),r("div",null,[s[0]||(s[0]=a('<h1 id="社区交流" tabindex="-1">社区交流 <a class="header-anchor" href="#社区交流" aria-label="Permalink to &quot;社区交流&quot;">​</a></h1><p>社区交流群主要是为了方便大家交流，提问，解答问题，分享经验等。偏自助方式，如果你有问题，可以通过以下方式加入社区交流群：</p><ul><li><a href="https://pd.qq.com/s/16p8lvvob" target="_blank" rel="noreferrer">QQ频道</a>：推荐！！！主要提供问题解答，分享经验等。</li><li>QQ群：<a href="https://qm.qq.com/q/MEmHoCLbG0" target="_blank" rel="noreferrer">大群</a>，<a href="https://qm.qq.com/q/YacMHPYAMu" target="_blank" rel="noreferrer">1群</a>、<a href="https://qm.qq.com/q/ajVKZvFICk" target="_blank" rel="noreferrer">2群</a>、<a href="https://qm.qq.com/q/36zdwThP2E" target="_blank" rel="noreferrer">3群</a>，<a href="https://qm.qq.com/q/sCzSlm3504" target="_blank" rel="noreferrer">4群</a>，主要使用者的交流群。</li><li><a href="https://discord.com/invite/VU62jTecad" target="_blank" rel="noreferrer">Discord</a>: 主要提供问题解答，分享经验等。</li></ul><div class="tip custom-block"><p class="custom-block-title">TIP</p><p>免费QQ群人数上限200，将会不定期清理。推荐加入QQ频道进行交流</p></div><h2 id="微信群" tabindex="-1">微信群 <a class="header-anchor" href="#微信群" aria-label="Permalink to &quot;微信群&quot;">​</a></h2><p>作者主要通过微信群提供帮助，如果你有问题，可以通过以下方式加入微信群。</p><p>通过微信联系作者，注明加群来意：</p><div class="tip custom-block"><p class="custom-block-title">TIP</p><p>因为微信群人数有限制，加微信群前，你可以通过<a href="./../sponsor/personal.html">赞助</a>任意金额，主动发送截图给作者，备注<code>加入微信群</code>即可。</p></div><img src="https://unpkg.com/@vbenjs/static-source@0.1.7/source/wechat.jpg" style="width:300px;">',9)),t(h),t(p)])}]]);export{s as __pageData,c as default};
