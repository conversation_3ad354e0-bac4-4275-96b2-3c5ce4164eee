var v=(p,u,o)=>new Promise((r,t)=>{var l=n=>{try{c(o.next(n))}catch(m){t(m)}},i=n=>{try{c(o.throw(n))}catch(m){t(m)}},c=n=>n.done?r(n.value):Promise.resolve(n.value).then(l,i);c((o=o.apply(p,u)).next())});import{c as k,u as y,$ as e,T as V,d as B,s as g,f as N}from"./bootstrap-DShsrVit.js";import{a4 as S,W as x,J as _,af as $,am as A,n as b,ah as w,ae as T,an as h,ao as d,a3 as s,aZ as F,ap as P,O as R,ag as I,h as C}from"../jse/index-index-BMh_AyeW.js";const L={class:"mt-4 text-center text-sm"},q=S({name:"RegisterForm",__name:"register",props:{formSchema:{default:()=>[]},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(p,{expose:u,emit:o}){const r=p,t=o,[l,i]=k(x({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:_(()=>r.formSchema),showDefaultActions:!1})),c=y();function n(){return v(this,null,function*(){const{valid:a}=yield i.validate(),f=yield i.getValues();a&&t("submit",f)})}function m(){c.push(r.loginPath)}return u({getFormApi:()=>i}),(a,f)=>($(),A("div",null,[b(V,null,{desc:w(()=>[T(a.$slots,"subTitle",{},()=>[h(d(a.subTitle||s(e)("authentication.signUpSubtitle")),1)])]),default:w(()=>[T(a.$slots,"title",{},()=>[h(d(a.title||s(e)("authentication.createAnAccount"))+" 🚀 ",1)])]),_:3}),b(s(l)),b(s(B),{class:F([{"cursor-wait":a.loading},"mt-2 w-full"]),loading:a.loading,"aria-label":"register",onClick:n},{default:w(()=>[T(a.$slots,"submitButtonText",{},()=>[h(d(a.submitButtonText||s(e)("authentication.signUp")),1)])]),_:3},8,["class","loading"]),P("div",L,[h(d(s(e)("authentication.alreadyHaveAccount"))+" ",1),P("span",{class:"vben-link text-sm font-normal",onClick:f[0]||(f[0]=D=>m())},d(s(e)("authentication.goToLogin")),1)])]))}}),H=S({name:"Register",__name:"register",setup(p){const u=R(!1),o=_(()=>[{component:"VbenInput",componentProps:{placeholder:e("authentication.usernameTip")},fieldName:"username",label:e("authentication.username"),rules:g().min(1,{message:e("authentication.usernameTip")})},{component:"VbenInputPassword",componentProps:{passwordStrength:!0,placeholder:e("authentication.password")},fieldName:"password",label:e("authentication.password"),renderComponentContent(){return{strengthText:()=>e("authentication.passwordStrength")}},rules:g().min(1,{message:e("authentication.passwordTip")})},{component:"VbenInputPassword",componentProps:{placeholder:e("authentication.confirmPassword")},dependencies:{rules(t){const{password:l}=t;return g({required_error:e("authentication.passwordTip")}).min(1,{message:e("authentication.passwordTip")}).refine(i=>i===l,{message:e("authentication.confirmPasswordTip")})},triggerFields:["password"]},fieldName:"confirmPassword",label:e("authentication.confirmPassword")},{component:"VbenCheckbox",fieldName:"agreePolicy",renderComponentContent:()=>({default:()=>C("span",[e("authentication.agree"),C("a",{class:"vben-link ml-1 ",href:""},`${e("authentication.privacyPolicy")} & ${e("authentication.terms")}`)])}),rules:N().refine(t=>!!t,{message:e("authentication.agreeTip")})}]);function r(t){console.log("register submit:",t)}return(t,l)=>($(),I(s(q),{"form-schema":o.value,loading:u.value,onSubmit:r},null,8,["form-schema","loading"]))}});export{H as default};
