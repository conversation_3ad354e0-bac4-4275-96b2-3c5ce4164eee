import * as Cesium from 'cesium'
import {
	h,reactive,render
} from 'vue'
import {
	eventbus
} from '../../../ztu/eventbus'
import fileSelector from '../../fileSelector.js'
import Box from '../../../components/JS/box'
import core from "../core"
import shp from './shp'
import {Tree} from 'ant-design-vue';
import readAsText from '../../readAsText'

export default class LocalViewer extends eventbus {
	constructor(options){
		super();
		this.viewer = options.viewer;
		this.localLayers = [];
		this.treeData = reactive([{title:'本地图层',
				key:'local_layer',
				children:[]}]);
		this.checkedKeys = reactive([]);
		this.selectedKeys = reactive([]);
		if(options.el){
			this.mount(options.el);
		}
		this.on('change',(localLayers)=>{
			let nodes = localLayers.map(a=>{
				return {
					title: a.title,
					key:a.key
				}
			});
			
			this.treeData[0].children = nodes;
			
			this.checkedKeys.splice(0,this.checkedKeys.length);
			localLayers.filter(a=>a.show).map(a=>a.key).forEach(a=>{
				this.checkedKeys.push(a)
			});
			console.log(this.treeData,this.checkedKeys)
		})
	}
	
	mount(el){
		if(typeof el === 'string'){
			el = document.getElementById(el);
		}
		if(!(el instanceof HTMLElement)){
			return console.log('POIViewer绑定节点无效')
		}
		//let checkedKeys = [];
		let node = h(Tree,{
			selectable:true,
			blockNode:true,
			checkable:true,
			//defaultExpandAll:true,
			checkedKeys: this.checkedKeys,
			selectedKeys:this.selectedKeys,
			//'onUpdate:checkedKeys': (value) => this.checkedKeys = value,
			onCheck:(keys)=>this.show(keys),
			onSelect:(keys)=>this.showAndZoom(keys[0]),
			treeData:this.treeData,
			style:{
				margin:'0px',
				marginTop:0
			}
		},{
			title:({title, key})=>{
				if(key=='local_layer'){
					return h('div',{
						style:'display:flex;align-items: center;justify-content: space-between;user-select: none;'
					},[
						h('span',null,title),
						h('span',{
							style:'cursor: pointer;padding:0 4px;margin-right:-4px;',
							title:'打开SHP文件',
							onClick:()=>this.loadShpFile()
						},[
							h('span',{
								class:"iconfont icon-jia"
							})
						])
					]);
				}
				else {
					return h('div',{
						style:'display:flex;align-items: center;justify-content: space-between;user-select: none;'
					},[
						h('span',null,title),
						h('span',{
							style:'cursor: pointer;padding:0 4px;margin-right:-4px;',
							title:'删除',
							onClick:()=>this.remove(key)
						},[
							h('span',{
								class:"iconfont icon-act_qingkong"
							})
						])
					]);
				}
			}
		});
		render(node,el);
	}
	loadShpFile(){
		fileSelector.openFile((files) => {
			shp.parseShpFiles(files, 'gb2312').then((result) => {
				if (!result.crs) {
					//result.crs = {"type":"name","properties":{"name":"urn:ogc:def:crs:EPSG::4490"}};
				}
				console.log(result)
				this.loadJson(result, result.fileName);
			}).catch(err => {
				//reject(err)
				console.log(err)
			})
		}, {
			multiple: true,
			accept: ".dbf,.prj,.shp,.cpg,",
		})
	}
	loadJsonFile(){
		fileSelector.openFile((files) => {
			readAsText(files[0]).then((result) => {
				console.log(result)
				let json = JSON.parse(result);
				this.loadJson(json, file.fileName);
			})
		}, {
			multiple: false,
			accept: ".json",
		})
	}
	loadKMLFile(){
		fileSelector.openFile((files) => {
			readAsText(files[0]).then((result) => {
				console.log(result)
				let json = JSON.parse(result);
				this.loadKML(json, file.fileName);
			})
		}, {
			multiple: false,
			accept: ".kml,.kmz",
		})
	}
	loadJson(data,layerName){
		Cesium.GeoJsonDataSource.load(data, {
			clampToGround: true
		}).then((dataSource) => {
			dataSource.name = 'local_layer';
			let es = dataSource.entities.values.map(entity=>{
				return core.json2Entity(core.entity2Json(entity))
			})
			dataSource.entities.removeAll();
			es.forEach(entity=>{
				//entity.polygon.classificationType = Cesium.ClassificationType.CESIUM_3D_TILE;
				dataSource.entities.add(entity);
			})
			/* dataSource.entities.values.forEach(entity=>{
				if(entity.polygon){
					//entity.polygon.arcType=Cesium.ArcType.GEODESIC;
					entity.polygon.arcType = undefined;
					entity.polygon.classificationType = Cesium.ClassificationType.BOTH;
					entity.polygon.heightReference = Cesium.HeightReference.CLAMP_TO_GROUND;
				}
			}) */
			this.loadDataSource(dataSource,layerName);
		})
	}
	loadKML(data,layerName){
		Cesium.KmlDataSource.load(data, {
			clampToGround: true
		}).then((dataSource) => {
			dataSource.name = 'local_layer';
			this.loadDataSource(dataSource,layerName);
		})
	}
	loadDataSource(dataSource,layerName){
		this.viewer.dataSources.add(dataSource);
		this.localLayers.push({
			title:layerName,
			show:dataSource.show,
			key:'local_layer_'+Math.random(),
			dataSource:dataSource,
		});
		this.viewer.flyTo(dataSource, {
			offset: new Cesium.HeadingPitchRange(this.viewer.camera.heading, this.viewer.camera.pitch,
				0)
		})
		this.emit('change', this.localLayers);
	}
	getByName(key){
		return this.localLayers.filter(a=>a.key == key);
	}
	remove(key){
		let localLayers = this.getByName(key);
		if(localLayers && localLayers instanceof Array && localLayers.length>0){
			Box.confirm('删除','确定要删除吗?',()=>{
				localLayers.forEach(localLayer=>{
					this.viewer.dataSources.remove(localLayer.dataSource);
				})
				let keys = localLayers.map(a=>a.key);
				this.localLayers = this.localLayers.filter(a=>!keys.includes(a.key));
				this.emit('change',this.localLayers);
			});
		}
		
	}
	show(keys){
		console.log(keys)
		this.localLayers.forEach(a=>{
			console.log(a)
			a.show = keys.includes(a.key);
			a.dataSource.show = a.show;
			this.emit('change',this.localLayers);
		})
	}
	showAndZoom(key){
		console.log(key)
		let localLayers = this.getByName(key);
		if(localLayers && localLayers instanceof Array && localLayers.length==1){
			let localLayer = localLayers[0];
			if(!localLayer)return;
			localLayer.show = true;
			localLayer.dataSource.show = true;
			this.viewer.flyTo(localLayer.dataSource, {
				offset: new Cesium.HeadingPitchRange(this.viewer.camera.heading, this.viewer.camera.pitch,
					0)
			})
			this.emit('change',this.localLayers);
		}
	}
}

export let localViewer = null;
export function initLocalViewer(options) {
	localViewer = new LocalViewer(options);
	return localViewer;
}
