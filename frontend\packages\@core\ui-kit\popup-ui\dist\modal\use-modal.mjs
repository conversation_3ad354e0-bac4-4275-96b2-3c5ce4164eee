import { defineComponent, h, inject, nextTick, provide, reactive } from "vue";
import { useStore } from "@vben-core/shared/store";
import VbenModal from "./modal.vue";
import { ModalApi } from "./modal-api.mjs";
const USER_MODAL_INJECT_KEY = Symbol("VBEN_MODAL_INJECT");
export function useVbenModal(options = {}) {
  const { connectedComponent } = options;
  if (connectedComponent) {
    const extendedApi2 = reactive({});
    const Modal2 = defineComponent(
      (props, { attrs, slots }) => {
        provide(USER_MODAL_INJECT_KEY, {
          extendApi(api2) {
            Object.setPrototypeOf(extendedApi2, api2);
          },
          options
        });
        checkProps(extendedApi2, {
          ...props,
          ...attrs,
          ...slots
        });
        return () => h(
          connectedComponent,
          {
            ...props,
            ...attrs
          },
          slots
        );
      },
      {
        inheritAttrs: false,
        name: "VbenParentModal"
      }
    );
    return [Modal2, extendedApi2];
  }
  const injectData = inject(USER_MODAL_INJECT_KEY, {});
  const mergedOptions = {
    ...injectData.options,
    ...options
  };
  mergedOptions.onOpenChange = (isOpen) => {
    options.onOpenChange?.(isOpen);
    injectData.options?.onOpenChange?.(isOpen);
  };
  const api = new ModalApi(mergedOptions);
  const extendedApi = api;
  extendedApi.useStore = (selector) => {
    return useStore(api.store, selector);
  };
  const Modal = defineComponent(
    (props, { attrs, slots }) => {
      return () => h(
        VbenModal,
        {
          ...props,
          ...attrs,
          modalApi: extendedApi
        },
        slots
      );
    },
    {
      inheritAttrs: false,
      name: "VbenModal"
    }
  );
  injectData.extendApi?.(extendedApi);
  return [Modal, extendedApi];
}
async function checkProps(api, attrs) {
  if (!attrs || Object.keys(attrs).length === 0) {
    return;
  }
  await nextTick();
  const state = api?.store?.state;
  if (!state) {
    return;
  }
  const stateKeys = new Set(Object.keys(state));
  for (const attr of Object.keys(attrs)) {
    if (stateKeys.has(attr) && !["class"].includes(attr)) {
      console.warn(
        `[Vben Modal]: When 'connectedComponent' exists, do not set props or slots '${attr}', which will increase complexity. If you need to modify the props of Modal, please use useVbenModal or api.`
      );
    }
  }
}
