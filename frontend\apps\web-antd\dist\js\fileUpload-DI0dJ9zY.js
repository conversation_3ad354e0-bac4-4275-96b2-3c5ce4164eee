var E=Object.defineProperty;var b=(s,e)=>(e=Symbol[s])?e:Symbol.for("Symbol."+s),C=s=>{throw TypeError(s)};var N=(s,e,t)=>e in s?E(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var k=(s,e,t)=>N(s,typeof e!="symbol"?e+"":e,t),F=(s,e,t)=>e.has(s)||C("Cannot "+t);var u=(s,e,t)=>(F(s,e,"read from private field"),t?t.call(s):e.get(s)),y=(s,e,t)=>e.has(s)?C("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(s):e.set(s,t),f=(s,e,t,o)=>(F(s,e,"write to private field"),o?o.call(s,t):e.set(s,t),t);var S=(s,e,t,o)=>({set _(n){f(s,e,n,t)},get _(){return u(s,e,o)}});var d=(s,e,t)=>new Promise((o,n)=>{var i=l=>{try{a(t.next(l))}catch(c){n(c)}},r=l=>{try{a(t.throw(l))}catch(c){n(c)}},a=l=>l.done?o(l.value):Promise.resolve(l.value).then(i,r);a((t=t.apply(s,e)).next())});var v=(s,e,t)=>(e=s[b("asyncIterator")])?e.call(s):(s=s[b("iterator")](),e={},t=(o,n)=>(n=s[o])&&(e[o]=i=>new Promise((r,a,l)=>(i=n.call(s,i),l=i.done,Promise.resolve(i.value).then(c=>r({value:c,done:l}),a)))),t("next"),t("return"),e);import{r as x,e as $,g as T}from"./bootstrap-5OPUVRWy.js";class M{constructor(e){k(this,"value");k(this,"next");this.value=e}}var h,p,g;class L{constructor(){y(this,h);y(this,p);y(this,g);this.clear()}enqueue(e){const t=new M(e);u(this,h)?(u(this,p).next=t,f(this,p,t)):(f(this,h,t),f(this,p,t)),S(this,g)._++}dequeue(){const e=u(this,h);if(e)return f(this,h,u(this,h).next),S(this,g)._--,e.value}peek(){if(u(this,h))return u(this,h).value}clear(){f(this,h,void 0),f(this,p,void 0),f(this,g,0)}get size(){return u(this,g)}*[Symbol.iterator](){let e=u(this,h);for(;e;)yield e.value,e=e.next}*drain(){for(;u(this,h);)yield this.dequeue()}}h=new WeakMap,p=new WeakMap,g=new WeakMap;function A(s){z(s);const e=new L;let t=0;const o=()=>{t<s&&e.size>0&&(e.dequeue()(),t++)},n=()=>{t--,o()},i=(l,c,m)=>d(null,null,function*(){const P=d(null,null,function*(){return l(...m)});c(P);try{yield P}catch(Y){}n()}),r=(l,c,m)=>{new Promise(P=>{e.enqueue(P)}).then(i.bind(void 0,l,c,m)),d(null,null,function*(){yield Promise.resolve(),t<s&&o()})},a=(l,...c)=>new Promise(m=>{r(l,m,c)});return Object.defineProperties(a,{activeCount:{get:()=>t},pendingCount:{get:()=>e.size},clearQueue:{value(){e.clear()}},concurrency:{get:()=>s,set(l){z(l),s=l,queueMicrotask(()=>{for(;t<s&&e.size>0;)o()})}}}),a}function z(s){if(!((Number.isInteger(s)||s===Number.POSITIVE_INFINITY)&&s>0))throw new TypeError("Expected `concurrency` to be a number from 1 and up")}const D="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";let O=(s=21)=>{let e="",t=crypto.getRandomValues(new Uint8Array(s|=0));for(;s--;)e+=D[t[s]&63];return e};const{apiURL:U}=T(),I=s=>s?`Bearer ${s}`:null;let w=!1,R=class{constructor(e,t="osgb",o="osgb",n=5*1024*1024){this.folder=e,this.chunkSize=n,this.dataType=t,this.fileFormat=o,this.identifier=null,this.aborted=!1,this.completed=!1}abort(){this.aborted=!0}createChunks(e){const t=[];let o=0;for(;o<e.size;)t.push(e.slice(o,o+this.chunkSize)),o+=this.chunkSize;return t}getFolderStructure(){return d(this,null,function*(){const e=[];try{for(var t=v(this.folder.files),o,n,i;o=!(n=yield t.next()).done;o=!1){const r=n.value;e.push({relativePath:r.webkitRelativePath,fileName:r.name,isDirectory:!1,size:r.size||0})}}catch(n){i=[n]}finally{try{o&&(n=t.return)&&(yield n.call(t))}finally{if(i)throw i[0]}}return e})}initUpload(e){return d(this,null,function*(){return yield x.post(`/file-manage/folder/structure/${this.dataType}/${this.fileFormat}`,e)})}start(){return d(this,null,function*(){try{yield this.uploadFiles()}catch(e){throw console.error("Upload failed:",e),e}})}uploadChunk(e,t,o,n,i){return d(this,null,function*(){if(w)return;const r=new FormData;r.append("file",o||new Blob([],{type:"application/octet-stream"})),r.append("fileId",e),r.append("folderId",this.identifier),r.append("relativePath",t.webkitRelativePath),r.append("chunkNumber",n),r.append("totalChunks",i),r.append("chunkSize",o?o.size:0),r.append("totalSize",t.size),r.append("fileName",t.name);const a=new Headers;a.append("Authorization",I($().accessToken));const l=yield fetch(`${U}/file-manage/file/chunk/${this.dataType}`,{method:"POST",body:r,headers:a});if(!l.ok)throw new Error(`Chunk upload failed: ${l.statusText}`)})}getRandomInt(e,t){return e=Math.ceil(e),t=Math.floor(t),Math.floor(Math.random()*(t-e+1))+e}uploadFile(e){return d(this,null,function*(){if(w)return;const t=this.createChunks(e),o=[];let n=O();if(t.length>0)for(let i=0;i<t.length&&!this.aborted;i++)o.push(this.uploadChunk(n,e,t[i],i+1,t.length));else o.push(this.uploadChunk(n,e,void 0,1,1));yield Promise.all(o)})}uploadFiles(){return d(this,null,function*(){w=!1;const e=A(1),t=[];try{for(var o=v(this.folder.files),n,i,r;n=!(i=yield o.next()).done;n=!1){const a=i.value;a.isDirectory||t.push(e(()=>this.uploadFile(a)))}}catch(i){r=[i]}finally{try{n&&(i=o.return)&&(yield i.call(o))}finally{if(r)throw r[0]}}yield Promise.all(t)})}},B=class{constructor(e,t,o){this.identifier=e,this.onProgress=o,this.onConnect=t}checkProgress(){try{const e=$();let t=I(e.accessToken);this.eventSource=new EventSource(`${U}/file-manage/folder/progress/${this.identifier}?token=${t}`),this.eventSource.addEventListener("connect",o=>{console.log("收到连接确认消息:",o.data),console.log("事件ID:",o.lastEventId),this.onConnect&&this.onConnect()}),this.eventSource.addEventListener("progress",o=>{console.log(o.data);const n=JSON.parse(o.data);this.onProgress&&this.onProgress(n)}),this.eventSource.addEventListener("complete",o=>{console.log("Complete:",o.data),this.onProgress&&this.onProgress({folderProgress:100,chunkProgress:100}),this.stop()})}catch(e){console.error("Failed to fetch progress:",e)}}start(){this.checkProgress()}stop(){this.eventSource&&(this.eventSource.close(),this.eventSource=null)}};function X(){w=!0}function Z(s,e,t,o,n){return d(this,null,function*(){const i=s,r=new R(i,t,o);try{const a=yield r.getFolderStructure();r.identifier=yield r.initUpload(a),new B(r.identifier,()=>{r.start()},c=>{c.folderProgress>=0?J(c.folderProgress):c.fileProgress>=0&&Q(c.fileProgress),c.folderProgress===100&&!r.completed&&(r.completed=!0,e&&e(r.identifier))}).start()}catch(a){console.error("Upload failed:",a),n&&n(a)}})}function J(s){const e=document.querySelector("#folderProgress");e&&(e.textContent=`${s}%`)}function Q(s){const e=document.querySelector("#folderProgressArea"),t=document.querySelector("#fileProgress");e&&(e.style.visibility="visible"),t&&(t.style.width=`${s}%`,t.textContent=`${s}%`)}const{apiURL:q}=T();class V{constructor(e,t="osgb",o="osgb",n=5*1024*1024){this.file=e,this.chunkSize=n,this.dataType=t,this.fileFormat=o;let i=new Date().getTime();this.identifier=i,this.aborted=!1}abort(){this.aborted=!0}createChunks(e){const t=[];let o=0;for(;o<e.size;)t.push(e.slice(o,o+this.chunkSize)),o+=this.chunkSize;return t}start(){return d(this,null,function*(){try{yield this.uploadFile(this.file)}catch(e){throw console.error("Upload failed:",e),e}})}uploadChunk(e,t,o,n,i){return d(this,null,function*(){const r=new FormData;r.append("file",o),r.append("fileId",e),r.append("relativePath",t.name),r.append("chunkNumber",n),r.append("totalChunks",i),r.append("chunkSize",o.size),r.append("totalSize",t.size),r.append("fileName",t.name);const a=yield fetch(`${q}/file-manage/file/chunk/${this.dataType}`,{method:"POST",body:r});if(!a.ok)throw new Error(`Chunk upload failed: ${a.statusText}`)})}uploadFile(e){return d(this,null,function*(){const t=this.createChunks(e),o=[];let n=this.identifier;for(let i=0;i<t.length&&!this.aborted;i++)o.push(this.uploadChunk(n,e,t[i],i+1,t.length));yield Promise.all(o)})}}class j{constructor(e,t,o){this.identifier=e,this.onProgress=o,this.onConnect=t}checkProgress(){return d(this,null,function*(){try{this.eventSource=new EventSource(`${q}/file-manage/file/progress/${this.identifier}`),this.eventSource.addEventListener("connect",e=>{console.log("收到连接确认消息:",e.data),console.log("事件ID:",e.lastEventId),this.onConnect&&this.onConnect()}),this.eventSource.addEventListener("progress",e=>{console.log(e.data);const t=JSON.parse(e.data);this.onProgress&&this.onProgress(t)}),this.eventSource.addEventListener("complete",e=>{console.log("Complete:",e.data),this.onProgress&&this.onProgress({fileProgress:100,chunkProgress:100}),this.stop()})}catch(e){console.error("Failed to fetch progress:",e)}})}start(){this.checkProgress()}stop(){this.eventSource&&(this.eventSource.close(),this.eventSource=null)}}function ee(s,e,t,o){return d(this,null,function*(){const n=s.files[0],i=new V(n,t,filetype);try{new j(i.identifier,()=>{i.start()},a=>{console.log("-------progress"+JSON.stringify(a)),H(a.fileProgress),a.fileProgress===100&&e&&e(i.identifier)}).start()}catch(r){console.error("Upload failed:",r)}})}function H(s){const e=document.querySelector("#folderProgressArea"),t=document.querySelector("#fileProgress");e&&(e.style.visibility="hidden"),t&&(t.style.width=`${s}%`,t.textContent=`${s}%`)}export{ee as a,X as s,Z as u};
