var t,e,s=t=>{throw TypeError(t)},i=(t,e,i)=>e.has(t)||s("Cannot "+i),n=(t,e,s)=>(i(t,e,"read from private field"),s?s.call(t):e.get(t)),r=(t,e,i)=>e.has(t)?s("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,i),a=(t,e,s,n)=>(i(t,e,"write to private field"),n?n.call(t,s):e.set(t,s),s);import{r as o,w as h,au as c,Q as l}from"./framework.C8U7mBUf.js";t=new WeakMap,e=new WeakMap;const u=new class{constructor(){r(this,t,{}),r(this,e,{})}defineMessage({copyPreferencesSuccess:t}){a(this,e,{copyPreferencesSuccess:t})}getComponents(){return n(this,t)}getMessage(){return n(this,e)}setComponents(e){a(this,t,e)}};class f{constructor(t,e){this.listeners=new Set,this._batching=!1,this._flushing=0,this.subscribe=t=>{var e,s;this.listeners.add(t);const i=null==(s=null==(e=this.options)?void 0:e.onSubscribe)?void 0:s.call(e,t,this);return()=>{this.listeners.delete(t),null==i||i()}},this.setState=t=>{var e,s,i;const n=this.state;this.state=(null==(e=this.options)?void 0:e.updateFn)?this.options.updateFn(n)(t):t(n),null==(i=null==(s=this.options)?void 0:s.onUpdate)||i.call(s),this._flush()},this._flush=()=>{if(this._batching)return;const t=++this._flushing;this.listeners.forEach((e=>{this._flushing===t&&e()}))},this.batch=t=>{if(this._batching)return t();this._batching=!0,t(),this._batching=!1,this._flush()},this.state=t,this.options=e}}function p(t,e=t=>t){const s=o(e(t.state));return h((()=>t),((t,i,n)=>{const r=t.subscribe((()=>{const i=e(t.state);(function(t,e){if(Object.is(t,e))return!0;if("object"!=typeof t||null===t||"object"!=typeof e||null===e)return!1;if(t instanceof Map&&e instanceof Map){if(t.size!==e.size)return!1;for(const[s,i]of t)if(!e.has(s)||!Object.is(i,e.get(s)))return!1;return!0}if(t instanceof Set&&e instanceof Set){if(t.size!==e.size)return!1;for(const s of t)if(!e.has(s))return!1;return!0}const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!1;for(let i=0;i<s.length;i++)if(!Object.prototype.hasOwnProperty.call(e,s[i])||!Object.is(t[s[i]],e[s[i]]))return!1;return!0})(c(s.value),i)||(s.value=i)}));n((()=>{r()}))}),{immediate:!0}),l(s)}export{f as S,u as g,p as u};
