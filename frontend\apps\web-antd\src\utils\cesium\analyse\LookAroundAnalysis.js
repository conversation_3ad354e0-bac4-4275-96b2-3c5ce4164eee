import * as Cesium from 'cesium';

import Math3d from '../core/Math3d';
import Primitive from '../core/Primitive';
/**
 * 环视分析
 * @param {*} params
 */
function LookAroundAnalysis(params) {
  if (!params && !params.center && !params.radius && params.that) {
    alert('没有获取到分析参数');
    return false;
  }

  const $this = this;
  let lines = [];
  let polygon = null;
  const that = params.that;
  this._viewer = that._viewer;
  this._math3d = new Math3d(this._viewer);
  this._primitive = new Primitive(this._viewer);
  this._pointsKSYResult = [];
  if (!that._viewer.scene.globe.depthTestAgainstTerrain) {
    alert('请开启深度检测');
    return false;
  }

  const viewHeight = params.viewHeight || 10;
  const spaceAngle = params.spaceAngle || 10;
  // var cartographicCenter = Cesium.Cartographic.fromCartesian(params.center);
  const center84 = that._base.transformCartesianToWGS84(params.center);
  center84.alt = center84.alt + viewHeight;
  const center = that._base.transformWGS84ToCartesian(center84);
  // 分析
  // try {
  const ab = params.radius;
  const eopt = {};
  eopt.semiMinorAxis = ab;
  eopt.semiMajorAxis = ab;
  eopt.rotation = 0;
  eopt.center = center;
  // eopt.granularity = Math.PI / 45.0; //间隔
  eopt.granularity = (2 * Math.PI) / Math.ceil(360 / spaceAngle); // 间隔
  const ellipse = $this._math3d.computeEllipseEdgePositions(eopt); // 范围当前椭圆位置的数组
  console.log(ellipse, eopt.granularity, spaceAngle);
  const positions = [];
  for (let i = 0; i < ellipse.outerPositions.length; i += 3) {
    // 逐条计算可视域
    const cartesian = new Cesium.Cartesian3(
      ellipse.outerPositions[i],
      ellipse.outerPositions[i + 1],
      ellipse.outerPositions[i + 2],
    );
    const results = this._math3d.getIntersectObj(center, cartesian); // 碰撞检测
    const arrowPositions = [center, cartesian];
    // console.log(results)
    // if(results.length>0)
    // console.log(params.radius,Cesium.Cartesian3.distance(params.center,results[0].position))
    // console.log(intersectPosition)
    if (results.length > 0) {
      const dis1 = Cesium.Cartesian3.distance(cartesian, results[0].position);
      const dis2 = Cesium.Cartesian3.distance(center, results[0].position);

      const bVisibility = dis1 + dis2 > params.radius + 0.01;
      if (!bVisibility) {
        arrowPositions[1] = results[0].position;
        const unArrowPositions = [results[0].position, cartesian];
        lines.push(
          that._analysisLayer.entities.add({
            polyline: {
              positions: unArrowPositions,
              width: 3,
              arcType: Cesium.ArcType.NONE,
              material: Cesium.Color.RED,
              depthFailMaterial: new Cesium.PolylineDashMaterialProperty({
                color: Cesium.Color.RED,
              }),
            },
          }),
        );
      }
    }
    // 通视线
    lines.push(
      that._analysisLayer.entities.add({
        polyline: {
          positions: arrowPositions,
          width: 3,
          arcType: Cesium.ArcType.NONE,
          material: Cesium.Color.GREEN,
        },
      }),
    );
    positions.push(arrowPositions[1]);
    /*	
			let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
			let deltaRadian = 0.00005 * Math.PI / 180.0; //Cesium.Math.RADIANS_PER_DEGREE 
			let cartographicArr = $this._math3d.computeInterpolateLineCartographic(cartographicCenter, cartographic,
			deltaRadian);
			
			$this._math3d.computeCartographicPointsTerrainData(cartographicArr,
				function(terrainData) {
					if (terrainData.length > 0) {
						let preVisible = true;
						let cartesiansLine = [];
						let colors = [];
						for (let j = 1; j < terrainData.length; j++) {
							//逐点计算可见性
							let visible = true; //该点可见性
							if (j > 1) {
								let cartographicCenterHV = new Cesium.Cartographic(terrainData[0].longitude,
									terrainData[0].latitude, terrainData[0].height + viewHeight);
								if (preVisible) {
									//   
									let curPoint = $this._math3d.computeInterpolateIndexLineHeightCartographic(
										cartographicCenterHV, terrainData[j], j, j - 1);
									if (curPoint.height >= terrainData[j - 1].height) {
										preVisible = true;
										visible = true;
									} else {
										preVisible = false;
										visible = false;
									}
								} else {
									//插值到当前
									let curPointArr = $this._math3d.computeInterpolateIndexLineHeightCartographic(
										cartographicCenterHV, terrainData[j], j, j - 1);
									for (let k = 0; k < curPointArr.length; k++) {
										if (curPointArr[k].height >= terrainData[k].height) {
											preVisible = true;
											visible = true;
										} else {
											preVisible = false;
											visible = false;
											break;
										}
									}
								}
							}
							let cartesianTemp = Cesium.Cartesian3.fromRadians(terrainData[j].longitude, terrainData[
								j].latitude, terrainData[j].height + 1);
							cartesiansLine.push(cartesianTemp);
							//绘制点
							if (visible) {
								colors.push(0);
								colors.push(0);
								colors.push(1);
								colors.push(1);
							} else {
								colors.push(1);
								colors.push(0);
								colors.push(0);
								colors.push(1);
							}
						}
						console.log(colors);
						//绘制结果
						$this._pointsKSYResult.push(new JCesium.PointsPrimitive({
							'viewer': that._viewer,
							'Cartesians': cartesiansLine,
							'Colors': colors
						}));
					} else {
						alert("高程异常！");
					}
				}); */
  }
  // let poss = that._base.transformCartesianArrayToWGS84Array(positions).map
  // that._math2d.getBezierPoints2d(positions.map(a=>[a.x,a.y]))
  polygon = that._analysisLayer.entities.add({
    polygon: {
      hierarchy: { positions },
      // height: center84.alt,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      classificationType: Cesium.ClassificationType.BOTH,
      outline: true,
      outlineWidth: 3,
      // fill: false,
      arcType: Cesium.ArcType.RHUMB,
      material: Cesium.Color.GREEN.withAlpha(0.5),
    },
  });
  // } catch (error) {
  //	console.log(error);
  // }

  LookAroundAnalysis.prototype.remove = function () {
    /* if($this._pointsKSYResult && $this._pointsKSYResult.length>0){
			$this._pointsKSYResult.forEach(a=>{
				a.remove();
			});
			$this._pointsKSYResult = [];
		} */
    lines.forEach((line) => {
      that._analysisLayer.entities.remove(line);
    });
    lines = [];
    if (polygon) {
      that._analysisLayer.entities.remove(polygon);
      polygon = null;
    }
  };
}
export default LookAroundAnalysis;
