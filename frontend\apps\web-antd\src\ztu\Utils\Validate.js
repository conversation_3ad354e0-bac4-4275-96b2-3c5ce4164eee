/**
 * 是否为空
 * @param {*} input 
 * @example
 if(!empty(params)) { 如果非空则进行相应的处理! }
 */
function empty(input) {
  return (
    typeof input == 'undefined' ||
    !input ||
    /^\s*(null|0|undefined|false|no)\s*$/i.test(input) ||
    (input instanceof Array && !input.length) ||
    (typeof input == 'object' &&
      (!Object.keys(input).length || ('length' in input && !input.length)))
  );
}

function isPhoneNumber(phone) {
  if (empty(phone)) {
    return false;
  }
  return /^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(phone);
}

/**
 * 手机号脱敏
 * @param {String} telephone
 * @example hiddenPhone('18912345678') //189****5678
 */
function hiddenPhone(telephone) {
  if (isPhoneNumber(telephone)) {
    const prefix = telephone.slice(0, 3);
    const suffix = telephone.slice(7, 11);
    return prefix + '****' + suffix;
  } else {
    return '';
  }
}
/**
 * 银行卡号脱敏 身份证
 * @param {String} carNo 卡号
 * @param {Number} start 头部保留位数
 * @param {Number} end 尾部保留位数
 * @example hiddenCarNo('358912198812252356',4,4) //3589 ******** 2356
 */
function hiddenCarNo(carNo, start = 0, end = 0) {
  if (!empty(carNo)) {
    if (start == 0) {
      var num = '***************';
      var end = carNo.slice(carNo.length - end, carNo.length);
      return num + ' ' + end;
    } else {
      var first = carNo.slice(0, start);
      var end = carNo.slice(carNo.length - end, carNo.length);
      var center = ' ******** ';
      // for (var i = 0; i < carNo.length - start; i++) {
      //     center += '*'
      // }

      return first + ' ' + center + ' ' + end;
    }
  }
  return '';
}
/**
 * 是否是小数（钱币值）
 * @param {String} input
 */
function isMoney(input) {
  if (empty(input)) return false;
  if (typeof input !== 'string') return false;
  var pattern = /^\d+(\.\d+)?$/;
  var result = pattern.exec(input); //match 是匹配的意思   用正则表达式来匹配
  if (result) {
    return false;
  } else {
    return true;
  }
}

export default {
  empty,
  isPhoneNumber,
  hiddenPhone,
  hiddenCarNo,
  isMoney,
};
