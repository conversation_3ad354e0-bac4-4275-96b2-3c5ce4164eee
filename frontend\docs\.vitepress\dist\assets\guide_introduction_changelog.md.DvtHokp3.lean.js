import{ao as a,k as e,z as t,I as n,l as o,ay as r,j as i}from"./chunks/framework.C8U7mBUf.js";const s=JSON.parse('{"title":"更新日志","description":"","frontmatter":{},"headers":[],"relativePath":"guide/introduction/changelog.md","filePath":"guide/introduction/changelog.md"}');const l=a({name:"guide/introduction/changelog.md"},[["render",function(a,s,l,d,c,u){const h=r("NolebaseGitContributors"),g=r("NolebaseGitChangelog");return i(),e("div",null,[s[0]||(s[0]=t("h1",{id:"更新日志",tabindex:"-1"},[n("更新日志 "),t("a",{class:"header-anchor",href:"#更新日志","aria-label":'Permalink to "更新日志"'},"​")],-1)),s[1]||(s[1]=t("p",null,"TODO",-1)),o(h),o(g)])}]]);export{s as __pageData,l as default};
