import{d as s,c as a,z as t,j as c,b as r,k as e,$ as o}from"../jse/index-index-DyHD_jbN.js";import{A as i}from"./authentication-Cmx7T77G.js";import"./bootstrap-5OPUVRWy.js";import"./theme-toggle.vue_vue_type_script_setup_true_lang-pVzKzmJu.js";const h=s({__name:"auth",setup(u){const p=a(()=>t.app.name),n=a(()=>t.logo.source);return(l,m)=>(r(),c(e(i),{"app-name":p.value,logo:n.value,"page-description":e(o)("authentication.pageDesc"),"page-title":e(o)("authentication.pageTitle")},null,8,["app-name","logo","page-description","page-title"]))}});export{h as default};
