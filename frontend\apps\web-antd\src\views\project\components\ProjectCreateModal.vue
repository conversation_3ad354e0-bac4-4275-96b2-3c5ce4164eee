<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { save } from '#/views/project/project.api';
import { message } from 'ant-design-vue';

// 声明Emits
const emit = defineEmits(['register', 'success']);

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm() {},
  onOpenChange(isOpen) {
    if (isOpen) {
      formApi.resetForm();
    }
  },
});

const state = modalApi.useStore();
const isUpdate = ref(false);

const formSchema = [
  {
    field: 'projectCode',
    label: '项目编号',
    component: 'Input',
    required: true,
  },
  {
    field: 'projectName',
    label: '项目名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'projectType',
    label: '项目类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '外业项目', value: '外业项目' },
      ],
    },
    required: true,
  },
  {
    field: 'commissionUnit',
    label: '委托单位',
    component: 'Input',
    required: true,
  },
  {
    field: 'belongDept',
    label: '归属部门',
    component: 'Input',
    required: true,
  },
  {
    field: 'workingParticipants',
    label: '作业参与部门',
    component: 'Select',
    componentProps: {
      options: [
        { label: '智慧国土部', value: '智慧国土部' },
      ],
    },
    required: true,
  },
  {
    field: 'projectRegion',
    label: '项目地区',
    component: 'Cascader',
    componentProps: {
      options: [],
      placeholder: '省-市-县',
    },
    required: true,
  },
  {
    field: 'projectManager',
    label: '项目负责人',
    component: 'Input',
    required: true,
  },
  {
    field: 'startDate',
    label: '开工日期',
    component: 'DatePicker',
    required: true,
  },
  {
    field: 'endDate',
    label: '完工日期',
    component: 'DatePicker',
    required: true,
  },
  {
    field: 'isFinished',
    label: '是否已经回款',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    required: true,
  },
  {
    field: 'projectContent',
    label: '项目内容',
    component: 'InputTextArea',
    componentProps: {
      rows: 4,
      placeholder: '请输入项目工作内容和工作要求等描述',
    },
    required: true,
  },
];

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-2/8',
  },
  handleSubmit,
  handleReset,
  layout: 'horizontal',
  schema: formSchema,
  wrapperClass: 'grid-cols-1',
  submitButtonOptions:{
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  }
});

async function handleReset() {
  await formApi.resetForm();
}

// 表单提交事件
async function handleSubmit(values) {
  try {
    await save(values);
    message.success('保存成功');
    modalApi.close();
    emit('success');
  } catch (error) {
    message.error('保存失败');
  }
}

// 设置标题
const title = computed(() => (unref(isUpdate) ? '编辑项目' : '新建项目'));
</script>

<template>
  <Modal :title="title">
    <Form />
  </Modal>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';
</style>
