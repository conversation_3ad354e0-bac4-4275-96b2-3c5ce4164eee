var D=Object.getOwnPropertySymbols;var R=Object.prototype.hasOwnProperty,F=Object.prototype.propertyIsEnumerable;var x=(o,t)=>{var e={};for(var s in o)R.call(o,s)&&t.indexOf(s)<0&&(e[s]=o[s]);if(o!=null&&D)for(var s of D(o))t.indexOf(s)<0&&F.call(o,s)&&(e[s]=o[s]);return e};var B=(o,t,e)=>new Promise((s,r)=>{var n=d=>{try{u(e.next(d))}catch(h){r(h)}},l=d=>{try{u(e.throw(d))}catch(h){r(h)}},u=d=>d.done?s(d.value):Promise.resolve(d.value).then(n,l);u((e=e.apply(o,t)).next())});import{d as f,j as m,b as i,k as a,O as j,P as O,q as c,g as _,c as v,s as g,I as k,J as M,a9 as H,a2 as W,L,M as N,a as y,F as A,D as E,h as S,e as w,v as G,p as I,t as b,z as U,Z as P,ab as Z,f as p,i as J,$ as z}from"../jse/index-index-DyHD_jbN.js";import{c as $,E as V,Z as K,$ as X,a0 as Y,a1 as Q,O as C,a2 as ee,a3 as ae,p as te,a4 as se,a5 as oe,a6 as ne,A as le,f as re,a7 as de,a as ie,D as ce,a8 as ue}from"./bootstrap-5OPUVRWy.js";const pe=$("languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]]);const me=$("moon-star",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9",key:"4ay0iu"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}]]);const fe=$("sun-moon",[["path",{d:"M12 8a2.83 2.83 0 0 0 4 4 4 4 0 1 1-4-4",key:"1fu5g2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.9 4.9 1.4 1.4",key:"b9915j"}],["path",{d:"m17.7 17.7 1.4 1.4",key:"qc3ed3"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.3 17.7-1.4 1.4",key:"5gca6"}],["path",{d:"m19.1 4.9-1.4 1.4",key:"wpu9u6"}]]);const he=$("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),ge=f({__name:"DropdownMenu",props:{defaultOpen:{type:Boolean},open:{type:Boolean},dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(o,{emit:t}){const r=V(o,t);return(n,l)=>(i(),m(a(K),j(O(a(r))),{default:c(()=>[_(n.$slots,"default")]),_:3},16))}}),ye=f({__name:"DropdownMenuContent",props:{class:{},forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(o,{emit:t}){const e=o,s=t,r=v(()=>{const d=e,{class:l}=d;return x(d,["class"])}),n=V(r,s);return(l,u)=>(i(),m(a(X),null,{default:c(()=>[g(a(Y),k(a(n),{class:a(M)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-[1000] min-w-32 overflow-hidden rounded-md border p-1 shadow-md",e.class)}),{default:c(()=>[_(l.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),_e=f({__name:"DropdownMenuGroup",props:{asChild:{type:Boolean},as:{}},setup(o){const t=o;return(e,s)=>(i(),m(a(Q),j(O(t)),{default:c(()=>[_(e.$slots,"default")]),_:3},16))}}),ve=f({__name:"DropdownMenuItem",props:{class:{},inset:{type:Boolean},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},setup(o){const t=o,e=v(()=>{const l=t,{class:r}=l;return x(l,["class"])}),s=C(e);return(r,n)=>(i(),m(a(ee),k(a(s),{class:a(M)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r.inset&&"pl-8",t.class)}),{default:c(()=>[_(r.$slots,"default")]),_:3},16,["class"]))}}),ke=f({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const e=C(o);return(s,r)=>(i(),m(a(ae),k({class:"outline-none"},a(e)),{default:c(()=>[_(s.$slots,"default")]),_:3},16))}}),xe=te("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground",{defaultVariants:{size:"default",variant:"default"},variants:{size:{default:"h-9 px-3",lg:"h-10 px-3",sm:"h-8 px-2"},variant:{default:"bg-transparent",outline:"border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground"}}}),be=f({__name:"ToggleGroup",props:{class:{},size:{},variant:{},rovingFocus:{type:Boolean},disabled:{type:Boolean},orientation:{},dir:{},loop:{type:Boolean},asChild:{type:Boolean},as:{},type:{},modelValue:{},defaultValue:{}},emits:["update:modelValue"],setup(o,{emit:t}){const e=o,s=t;H("toggleGroup",{size:e.size,variant:e.variant});const r=v(()=>{const d=e,{class:l}=d;return x(d,["class"])}),n=V(r,s);return(l,u)=>(i(),m(a(se),k(a(n),{class:a(M)("flex items-center justify-center gap-1",e.class)}),{default:c(()=>[_(l.$slots,"default")]),_:3},16,["class"]))}}),we=f({__name:"ToggleGroupItem",props:{class:{},size:{},variant:{},value:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const t=o,e=W("toggleGroup"),s=v(()=>{const h=t,{class:n,size:l,variant:u}=h;return x(h,["class","size","variant"])}),r=C(s);return(n,l)=>{var u,d;return i(),m(a(oe),k(a(r),{class:a(M)(a(xe)({variant:((u=a(e))==null?void 0:u.variant)||n.variant,size:((d=a(e))==null?void 0:d.size)||n.size}),t.class)}),{default:c(()=>[_(n.$slots,"default")]),_:3},16,["class"])}}}),Me=f({name:"DropdownRadioMenu",__name:"dropdown-radio-menu",props:L({menus:{}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const t=N(o,"modelValue");function e(s){t.value=s}return(s,r)=>(i(),m(a(ge),null,{default:c(()=>[g(a(ke),{"as-child":"",class:"flex items-center gap-1"},{default:c(()=>[_(s.$slots,"default")]),_:3}),g(a(ye),{align:"start"},{default:c(()=>[g(a(_e),null,{default:c(()=>[(i(!0),y(A,null,E(s.menus,n=>(i(),m(a(ve),{key:n.key,class:S([n.value===t.value?"bg-accent text-accent-foreground":"","data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground text-foreground/80 mb-1 cursor-pointer"]),onClick:l=>e(n.value)},{default:c(()=>[n.icon?(i(),m(I(n.icon),{key:0,class:"mr-2 size-4"})):w("",!0),n.icon?w("",!0):(i(),y("span",{key:1,class:S([n.value===t.value?"bg-foreground":"","mr-2 size-1.5 rounded-full"])},null,2)),G(" "+b(n.label),1)]),_:2},1032,["class","onClick"]))),128))]),_:1})]),_:1})]),_:3}))}}),$e={class:"text-md flex-center"},Be=["href"],ze=["href"],je=f({name:"Copyright",__name:"copyright",props:{companyName:{default:"Vben Admin"},companySiteLink:{default:""},date:{default:"2024"},icp:{default:""},icpLink:{default:""}},setup(o){return(t,e)=>(i(),y("div",$e,[t.icp?(i(),y("a",{key:0,href:t.icpLink||"javascript:void(0)",class:"hover:text-primary-hover mx-1",target:"_blank"},b(t.icp),9,Be)):w("",!0),G(" Copyright © "+b(t.date)+" ",1),t.companyName?(i(),y("a",{key:1,href:t.companySiteLink||"javascript:void(0)",class:"hover:text-primary-hover mx-1",target:"_blank"},b(t.companyName),9,ze)):w("",!0)]))}}),Oe=f({name:"LanguageToggle",__name:"language-toggle",setup(o){function t(e){return B(this,null,function*(){const s=e;P({app:{locale:s}}),yield Z(s)})}return(e,s)=>(i(),y("div",null,[g(a(Me),{menus:a(ne),"model-value":a(U).app.locale,"onUpdate:modelValue":t},{default:c(()=>[g(a(le),null,{default:c(()=>[g(a(pe),{class:"text-foreground size-4"})]),_:1})]),_:1},8,["menus","model-value"])]))}}),Pe=f({name:"ThemeToggleButton",__name:"theme-button",props:L({type:{default:"normal"}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const t=o,e=N(o,"modelValue"),s=v(()=>e.value?"light":"dark"),r=v(()=>t.type==="normal"?{variant:"heavy"}:{class:"rounded-full",size:"icon",style:{padding:"7px"},variant:"icon"});function n(l){if(!(document.startViewTransition&&!window.matchMedia("(prefers-reduced-motion: reduce)").matches)||!l){e.value=!e.value;return}const d=l.clientX,h=l.clientY,q=Math.hypot(Math.max(d,innerWidth-d),Math.max(h,innerHeight-h));document.startViewTransition(()=>B(null,null,function*(){e.value=!e.value,yield J()})).ready.then(()=>{const T=[`circle(0px at ${d}px ${h}px)`,`circle(${q}px at ${d}px ${h}px)`];document.documentElement.animate({clipPath:e.value?[...T].reverse():T},{duration:450,easing:"ease-in",pseudoElement:e.value?"::view-transition-old(root)":"::view-transition-new(root)"})})}return(l,u)=>(i(),m(a(re),k({"aria-label":s.value,class:[[`is-${s.value}`],"theme-toggle cursor-pointer border-none bg-none"],"aria-live":"polite"},r.value,{onClick:de(n,["stop"])}),{default:c(()=>u[0]||(u[0]=[p("svg",{"aria-hidden":"true",height:"24",viewBox:"0 0 24 24",width:"24"},[p("mask",{id:"theme-toggle-moon",class:"theme-toggle__moon",fill:"hsl(var(--foreground)/80%)",stroke:"none"},[p("rect",{fill:"white",height:"100%",width:"100%",x:"0",y:"0"}),p("circle",{cx:"40",cy:"8",fill:"black",r:"11"})]),p("circle",{id:"sun",class:"theme-toggle__sun",cx:"12",cy:"12",mask:"url(#theme-toggle-moon)",r:"11"}),p("g",{class:"theme-toggle__sun-beams"},[p("line",{x1:"12",x2:"12",y1:"1",y2:"3"}),p("line",{x1:"12",x2:"12",y1:"21",y2:"23"}),p("line",{x1:"4.22",x2:"5.64",y1:"4.22",y2:"5.64"}),p("line",{x1:"18.36",x2:"19.78",y1:"18.36",y2:"19.78"}),p("line",{x1:"1",x2:"3",y1:"12",y2:"12"}),p("line",{x1:"21",x2:"23",y1:"12",y2:"12"}),p("line",{x1:"4.22",x2:"5.64",y1:"19.78",y2:"18.36"}),p("line",{x1:"18.36",x2:"19.78",y1:"5.64",y2:"4.22"})])],-1)])),_:1},16,["aria-label","class"]))}}),Ve=ie(Pe,[["__scopeId","data-v-8e18cb5a"]]),Le=f({name:"ThemeToggle",__name:"theme-toggle",props:{shouldOnHover:{type:Boolean,default:!1}},setup(o){function t(r){P({theme:{mode:r?"dark":"light"}})}const{isDark:e}=ce(),s=[{icon:he,name:"light",title:z("preferences.theme.light")},{icon:me,name:"dark",title:z("preferences.theme.dark")},{icon:fe,name:"auto",title:z("preferences.followSystem")}];return(r,n)=>(i(),y("div",null,[g(a(ue),{disabled:!r.shouldOnHover,side:"bottom"},{trigger:c(()=>[g(Ve,{"model-value":a(e),type:"icon","onUpdate:modelValue":t},null,8,["model-value"])]),default:c(()=>[g(a(be),{"model-value":a(U).theme.mode,class:"gap-2",type:"single",variant:"outline","onUpdate:modelValue":n[0]||(n[0]=l=>a(P)({theme:{mode:l}}))},{default:c(()=>[(i(),y(A,null,E(s,l=>g(a(we),{key:l.name,value:l.name},{default:c(()=>[(i(),m(I(l.icon),{class:"size-5"}))]),_:2},1032,["value"])),64))]),_:1},8,["model-value"])]),_:1},8,["disabled"])]))}});export{me as M,he as S,Oe as _,Le as a,Me as b,je as c,ge as d,ke as e,ye as f,ve as g,_e as h,be as i,we as j,fe as k};
