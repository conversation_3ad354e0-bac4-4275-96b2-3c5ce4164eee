const e="Preferences",o="Customize Preferences & Preview in Real Time",t="Data has changed, click to reset",n="Reset Preferences",a="Preferences reset successfully",i="Appearance",l="Layout",r="Content",s="Other",c="Wide",d="Fixed",u="Follow System",b="Vertical",p="Side vertical menu mode",m="Horizontal",g="Horizontal menu mode, all menus displayed at the top",h="Two Column",S="Vertical Two Column Menu Mode",y="Mixed Menu",T="Vertical & Horizontal Menu Co-exists",w="Full Content",f="Only display content body, hide all menus",C="Normal",k="Plain",M="Rounded",P="Copy Preferences",x="Copy successful",L="Copy successful, please override in `src/preferences.ts` under app",B="Clear Cache & Logout",v="Mode",z="General",E="Language",H="Dynamic Title",N="Watermark",A="Periodic update check",D={title:"Preferences Postion",header:"Header",auto:"Auto",fixed:"Fixed"},F={title:"Sidebar",width:"Width",visible:"Show Sidebar",collapsed:"Collpase Menu",collapsedShowTitle:"Show Menu Title"},R={title:"Tabbar",enable:"Enable Tab Bar",icon:"Show Tabbar Icon",showMore:"Show More Button",showMaximize:"Show Maximize Button",persist:"Persist Tabs",draggable:"Enable Draggable Sort",styleType:{title:"Tabs Style",chrome:"Chrome",card:"Card",plain:"Plain",brisk:"Brisk"},contextMenu:{reload:"Reload",close:"Close",pin:"Pin",unpin:"Unpin",closeLeft:"Close Left Tabs",closeRight:"Close Right Tabs",closeOther:"Close Other Tabs",closeAll:"Close All Tabs",openInNewWindow:"Open in New Window",maximize:"Maximize",restoreMaximize:"Restore"}},G={title:"Navigation Menu",style:"Navigation Menu Style",accordion:"Sidebar Accordion Menu",split:"Navigation Menu Separation",splitTip:"When enabled, the sidebar displays the top bar's submenu"},O={title:"Breadcrumb",home:"Show Home Button",enable:"Enable Breadcrumb",icon:"Show Breadcrumb Icon",background:"background",style:"Breadcrumb Style",hideOnlyOne:"Hidden when only one"},W={title:"Animation",loading:"Page Loading",transition:"Page Transition",progress:"Page Progress"},I={title:"Theme",radius:"Radius",light:"Light",dark:"Dark",darkSidebar:"Semi Dark Sidebar",darkHeader:"Semi Dark Header",weakMode:"Weak Mode",grayMode:"Gray Mode",builtin:{title:"Built-in",default:"Default",violet:"Violet",pink:"Pink",rose:"Rose",skyBlue:"Sky Blue",deepBlue:"Deep Blue",green:"Green",deepGreen:"Deep Green",orange:"Orange",yellow:"Yellow",zinc:"Zinc",neutral:"Neutral",slate:"Slate",gray:"Gray",custom:"Custom"}},V={title:"Header",visible:"Show Header",modeStatic:"Static",modeFixed:"Fixed",modeAuto:"Auto hide & Show",modeAutoScroll:"Scroll to Hide & Show"},K={title:"Footer",visible:"Show Footer",fixed:"Fixed at Bottom"},U={title:"Copyright",enable:"Enable Copyright",companyName:"Company Name",companySiteLink:"Company Site Link",date:"Date",icp:"ICP License Number",icpLink:"ICP Site Link"},Y={title:"Shortcut Keys",global:"Global",search:"Global Search",logout:"Logout",preferences:"Preferences"},Z={title:"Widget",globalSearch:"Enable Global Search",fullscreen:"Enable Fullscreen",themeToggle:"Enable Theme Toggle",languageToggle:"Enable Language Toggle",notification:"Enable Notification",sidebarToggle:"Enable Sidebar Toggle",lockScreen:"Enable Lock Screen",refresh:"Enable Refresh"},j={title:e,subtitle:o,resetTip:t,resetTitle:n,resetSuccess:a,appearance:i,layout:l,content:r,other:s,wide:c,compact:d,followSystem:u,vertical:b,verticalTip:p,horizontal:m,horizontalTip:g,twoColumn:h,twoColumnTip:S,mixedMenu:y,mixedMenuTip:T,fullContent:w,fullContentTip:f,normal:C,plain:k,rounded:M,copyPreferences:P,copyPreferencesSuccessTitle:x,copyPreferencesSuccess:L,clearAndLogout:B,mode:v,general:z,language:E,dynamicTitle:H,watermark:N,checkUpdates:A,position:D,sidebar:F,tabbar:R,navigationMenu:G,breadcrumb:O,animation:W,theme:I,header:V,footer:K,copyright:U,shortcutKeys:Y,widget:Z};export{W as animation,i as appearance,O as breadcrumb,A as checkUpdates,B as clearAndLogout,d as compact,r as content,P as copyPreferences,L as copyPreferencesSuccess,x as copyPreferencesSuccessTitle,U as copyright,j as default,H as dynamicTitle,u as followSystem,K as footer,w as fullContent,f as fullContentTip,z as general,V as header,m as horizontal,g as horizontalTip,E as language,l as layout,y as mixedMenu,T as mixedMenuTip,v as mode,G as navigationMenu,C as normal,s as other,k as plain,D as position,a as resetSuccess,t as resetTip,n as resetTitle,M as rounded,Y as shortcutKeys,F as sidebar,o as subtitle,R as tabbar,I as theme,e as title,h as twoColumn,S as twoColumnTip,b as vertical,p as verticalTip,N as watermark,c as wide,Z as widget};
