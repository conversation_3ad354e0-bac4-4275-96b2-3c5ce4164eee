var W=Object.defineProperty;var M=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,V=Object.prototype.propertyIsEnumerable;var L=(o,a,e)=>a in o?W(o,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[a]=e,g=(o,a)=>{for(var e in a||(a={}))N.call(a,e)&&L(o,e,a[e]);if(M)for(var e of M(a))V.call(a,e)&&L(o,e,a[e]);return o};var m=(o,a,e)=>new Promise((s,t)=>{var c=r=>{try{v(e.next(r))}catch(d){t(d)}},h=r=>{try{v(e.throw(r))}catch(d){t(d)}},v=r=>r.done?s(r.value):Promise.resolve(r.value).then(c,h);v((e=e.apply(o,a)).next())});import{c as I,J as O,l as U,m as q,e as j,n as z,o as D,_ as G}from"./bootstrap-5OPUVRWy.js";import{K as H,r as b,i as J,_ as F,d as S,L as K,M as Q,w as T,a as X,b as E,s as u,q as p,k as n,g as Y,c as k,$,N as x,z as A,j as Z,E as ee,V as R}from"../jse/index-index-DyHD_jbN.js";import{e as ae,N as te,i as oe,_ as se}from"./layout.vue_vue_type_script_setup_true_lang-CXvwR802.js";import{u as ne}from"./use-modal-uChFuhJy.js";import{_ as re}from"./icon.vue_vue_type_script_setup_true_lang-CgLK7NiC.js";import"./theme-toggle.vue_vue_type_script_setup_true_lang-pVzKzmJu.js";import"./loading-DzjUKA94.js";import"./rotate-cw-lLmdvVrn.js";const f=b(),w=b({advancedStyle:{colorStops:[{color:"gray",offset:0},{color:"gray",offset:1}],type:"linear"},content:"",contentType:"multi-line-text",globalAlpha:.25,gridLayoutOptions:{cols:2,gap:[20,20],matrix:[[1,0],[0,1]],rows:2},height:200,layout:"grid",rotate:30,width:160});function ie(){function o(s){return m(this,null,function*(){var c;const{Watermark:t}=yield F(()=>m(null,null,function*(){const{Watermark:h}=yield import("./index.esm-DCgfxJEb.js");return{Watermark:h}}),[]);w.value=g(g({},w.value),s),f.value=new t(w.value),yield(c=f.value)==null?void 0:c.create()})}function a(s){return m(this,null,function*(){var t;f.value?(yield J(),yield(t=f.value)==null?void 0:t.changeOptions(g(g({},w.value),s))):yield o(s)})}function e(){var s;(s=f.value)==null||s.destroy()}return H(()=>{e()}),{destroyWatermark:e,updateWatermark:a,watermark:f}}const le=I("book-open-text",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M16 12h2",key:"7q9ll5"}],["path",{d:"M16 8h2",key:"msurwy"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}],["path",{d:"M6 12h2",key:"32wvfc"}],["path",{d:"M6 8h2",key:"30oboj"}]]),ue=S({name:"LoginExpiredModal",__name:"login-expired-modal",props:K({avatar:{default:""},codeLoginPath:{},forgetPasswordPath:{},loading:{type:Boolean},qrCodeLoginPath:{},registerPath:{},showCodeLogin:{type:Boolean},showForgetPassword:{type:Boolean},showQrcodeLogin:{type:Boolean},showRegister:{type:Boolean},showRememberMe:{type:Boolean},showThirdPartyLogin:{type:Boolean},subTitle:{},title:{},submitButtonText:{}},{open:{type:Boolean},openModifiers:{}}),emits:["update:open"],setup(o){const a=Q(o,"open"),[e,s]=ne();return T(()=>a.value,t=>{s.setState({isOpen:t})}),(t,c)=>(E(),X("div",null,[u(n(e),{closable:!1,"close-on-click-modal":!1,"close-on-press-escape":!1,footer:!1,"fullscreen-button":!1,header:!1,class:"border-none px-10 py-6 text-center shadow-xl sm:w-[600px] sm:rounded-2xl md:h-[unset]"},{default:p(()=>[u(n(re),{src:t.avatar,class:"mx-auto mb-6 size-20"},null,8,["src"]),u(n(O),{"show-forget-password":!1,"show-register":!1,"show-remember-me":!1,"sub-title":t.$t("authentication.loginAgainSubTitle"),title:t.$t("authentication.loginAgainTitle")},{default:p(()=>[Y(t.$slots,"default")]),_:3},8,["sub-title","title"])]),_:3})]))}}),we=S({__name:"basic",setup(o){const a=b([{avatar:"https://avatar.vercel.sh/vercel.svg?text=VB",date:"3小时前",isRead:!0,message:"描述信息描述信息描述信息",title:"收到了 14 份新周报"},{avatar:"https://avatar.vercel.sh/1",date:"刚刚",isRead:!1,message:"描述信息描述信息描述信息",title:"朱偏右 回复了你"},{avatar:"https://avatar.vercel.sh/1",date:"2024-01-01",isRead:!1,message:"描述信息描述信息描述信息",title:"曲丽丽 评论了你"},{avatar:"https://avatar.vercel.sh/satori",date:"1天前",isRead:!1,message:"描述信息描述信息描述信息",title:"代办提醒"}]),e=U(),s=q(),t=j(),{destroyWatermark:c,updateWatermark:h}=ie(),v=k(()=>a.value.some(i=>!i.isRead)),r=k(()=>[{handler:()=>{x(ee,{target:"_blank"})},icon:le,text:$("ui.widgets.document")},{handler:()=>{x(R,{target:"_blank"})},icon:z,text:"GitHub"},{handler:()=>{x(`${R}/issues`,{target:"_blank"})},icon:D,text:$("ui.widgets.qa")}]),d=k(()=>{var i,l;return(l=(i=e.userInfo)==null?void 0:i.avatar)!=null?l:A.app.defaultAvatar});function y(){return m(this,null,function*(){yield s.logout(!1)})}function P(){a.value=[]}function C(){a.value.forEach(i=>i.isRead=!0)}return T(()=>A.app.watermark,i=>m(null,null,function*(){var l;i?yield h({content:`${(l=e.userInfo)==null?void 0:l.username}`}):c()}),{immediate:!0}),(i,l)=>(E(),Z(n(se),{onClearPreferencesAndLogout:y},{"user-dropdown":p(()=>{var _,B;return[u(n(oe),{avatar:d.value,menus:r.value,text:(_=n(e).userInfo)==null?void 0:_.username,description:(B=n(e).userInfo)==null?void 0:B.realName,"tag-text":"",onLogout:y},null,8,["avatar","menus","text","description"])]}),notification:p(()=>[u(n(te),{dot:v.value,notifications:a.value,onClear:P,onMakeAll:C},null,8,["dot","notifications"])]),extra:p(()=>[u(n(ue),{open:n(t).loginExpired,"onUpdate:open":l[0]||(l[0]=_=>n(t).loginExpired=_),avatar:d.value},{default:p(()=>[u(G)]),_:1},8,["open","avatar"])]),"lock-screen":p(()=>[u(n(ae),{avatar:d.value,onToLogin:y},null,8,["avatar"])]),_:1}))}});export{we as default};
