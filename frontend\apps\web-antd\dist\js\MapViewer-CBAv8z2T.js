var b=Object.defineProperty,_=Object.defineProperties;var I=Object.getOwnPropertyDescriptors;var C=Object.getOwnPropertySymbols;var T=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable;var S=(i,e,t)=>e in i?b(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t,w=(i,e)=>{for(var t in e||(e={}))T.call(e,t)&&S(i,t,e[t]);if(C)for(var t of C(e))M.call(e,t)&&S(i,t,e[t]);return i},v=(i,e)=>_(i,I(e));var d=(i,e,t)=>new Promise((r,a)=>{var s=n=>{try{o(t.next(n))}catch(c){a(c)}},l=n=>{try{o(t.throw(n))}catch(c){a(c)}},o=n=>n.done?r(n.value):Promise.resolve(n.value).then(s,l);o((t=t.apply(i,e)).next())});import{G as GISMap,d as deepMerge,b as core,e as axios,l as list2Tree,c as capp,L as LayerInfo,f as deepEquals,B as Box,C as CesiumMap,z as ztu,a as appInit,i as initMap}from"./index-D4Q7xmlJ.js";import{a as _export_sfc,q as render,u as useRouter}from"./bootstrap-5OPUVRWy.js";import{a as createElementBlock,b as openBlock,F as Fragment,D as renderList,f as createBaseVNode,t as toDisplayString,C as h,o as onMounted,w as watch}from"../jse/index-index-DyHD_jbN.js";import{e as eventbus,a as eb,i as initShapeViewer}from"./index-BYvs451p.js";import"./Base-xeJpkIWP.js";const _sfc_main$1={props:{p:{type:String,default:"{}"}},data(){return{ps:[]}},mounted(){console.log(this.p);const i=JSON.parse(this.p),e=[];for(const t in i)e.push([t,i[t]]);console.log(this.p,i,e),this.ps=e}},_hoisted_1$1={class:"properties-box"},_hoisted_2={class:"field"},_hoisted_3={class:"value"};function _sfc_render(i,e,t,r,a,s){return openBlock(),createElementBlock("div",_hoisted_1$1,[(openBlock(!0),createElementBlock(Fragment,null,renderList(a.ps,(l,o)=>(openBlock(),createElementBlock("div",{key:o,class:"property"},[createBaseVNode("div",_hoisted_2,toDisplayString(l[0]),1),createBaseVNode("div",_hoisted_3,toDisplayString(l[1]),1)]))),128))])}const PropertiesBox=_export_sfc(_sfc_main$1,[["render",_sfc_render],["__scopeId","data-v-b02054ff"]]),LayerDefaultOptions={geoserver:{wms:{url:"http://localhost:9999/geoserver/wms",layers:"test:layer",parameters:{service:"WMS",format:"image/png",transparent:!0,format_options:"dpi:300",srs:"EPSG:4326"}},wmts:{url:"http://localhost:9999/geoserver/gwc/service/wmts/rest/test:layer/{style}/{TileMatrixSetID}/{TileMatrixSetID}:{TileMatrix}/{TileRow}/{TileColumn}?format:image/png",layer:"test:layer",style:"",format:"image/png",tileMatrixSetID:"EPSG:900913"},tms:{url:"http://localhost:9999/geoserver/gwc/service/tms/1.0.0/test:layer@EPSG：900913@png/{z} /{x}/{reverseY}",tilingScheme:new Cesium.GeographicTilingScheme,fileExtension:"png",minimumLevel:0,maximumLevel:19},geojson:{options:{clampToGround:!0},url:"http://fzzt.fzjhdn.com:18086/geoserver/anxi_xiaoban/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=anxi_xiaoban%3Aanxilinban&maxFeatures=50&outputFormat=application%2Fjson"},kml:{}},arcgis:{mapserver:{url:"http://localhost:9999/arcgis/mapserver",usePreCachedTilesIfAvailable:!1}},bing:{url:"https://dev.virtualearth.net/",key:"AmXdbd8UeUJtaRSn7yVwyXgQlBBUqliLbHpgn2c76DfuHwAXfRrgS5qwfHU6Rhm8",mapStyle:Cesium.BingMapsStyle.AERIAL},"3dtiles":{url:"http://fzzt.fzjhdn.com:19003/keshan3DTile/tileset.json",skipLevelOfDetail:!0,baseScreenSpaceError:1024,skipLevels:1,skipScreenSpaceErrorFactor:16},terrain:{provider:"",url:"",requestVertexNormals:!0,requestWaterMask:!0}},LayerStyle={"#":i=>{}};class layerService extends eventbus{constructor(i){super(),this.viewer=i||GISMap.viewer;const e=this;this._allowPicking=!0,this.infoBox=null,this.layers=[],this.highLightLayer=null,this.data=[],this.dataCache={},this._select={feature:null,material:null},this._hover={feature:null,material:null},this.geoserver={loadWMS(r,a){r=deepMerge(LayerDefaultOptions.geoserver.wms,r),r.layers="wuyishan:bbbababaaa";const s=new Cesium.WebMapServiceImageryProvider(r);return e.viewer.scene.imageryLayers.addImageryProvider(s,Math.min(a,e.viewer.scene.imageryLayers.length))},loadWMTS(r,a){console.log(r),r=deepMerge(LayerDefaultOptions.geoserver.wmts,r);const s=new Cesium.WebMapTileServiceImageryProvider(r);return e.viewer.scene.imageryLayers.addImageryProvider(s,Math.min(a,e.viewer.scene.imageryLayers.length))},loadTMS(r,a){r=deepMerge(LayerDefaultOptions.geoserver.tms,r);const s=new Cesium.UrlTemplateImageryProvider(r);return e.viewer.scene.imageryLayers.addImageryProvider(s,Math.min(a,e.viewer.scene.imageryLayers.length))},loadGeoJson(r){const a=e.viewer;return new Promise((s,l)=>{r=typeof r=="string"&&{url:r}||r,r=deepMerge(LayerDefaultOptions.geoserver.geojson,r),Cesium.GeoJsonDataSource.load(r.url,r.options||{}).then(o=>{const n=o.entities.values.map(c=>core.json2Entity(core.entity2Json(c)));o.entities.removeAll(),n.forEach(c=>{o.entities.add(c)}),a.dataSources.add(o),s(o),e.setLayerStyle(o,r.style)})})},loadKML(){const r=e.viewer;return new Promise((a,s)=>{options=typeof options=="string"&&{url:options}||options,options=deepMerge(LayerDefaultOptions.geoserver.kml,options),Cesium.KmlDataSource.load(options.url,options.options||{}).then(l=>{r.dataSources.add(l),a(l),e.setLayerStyle(l,options.style)})})}},this.arcgisserver={checkToken(r){return d(this,null,function*(){const a=yield axios.get(`${r.url.trim("/")}/${r.layers}?f=json${r.token?`&token=${r.token}`:""}`);return a&&a.data.error==null})},loadWMS(r,a){r=deepMerge(LayerDefaultOptions.arcgis.mapserver,r);const s=new Cesium.ArcGisMapServerImageryProvider(r);return e.viewer.scene.imageryLayers.addImageryProvider(s,Math.min(a,e.viewer.scene.imageryLayers.length))},loadGeoJson(r,a){return r=deepMerge(LayerDefaultOptions.arcgis.geojson,r),r.url=`${r.url.trim("/")}/{layers}/query?where=1%3D1&inSR=4326&spatialRel=esriSpatialRelIntersects&outFields=*&returnGeometry=true&returnTrueCurves=false&outSR=4326&returnIdsOnly=false&returnCountOnly=false&returnZ=false&returnM=false&returnDistinctValues=false&returnExtentsOnly=false&f=geojson{token}`,r.url=r.url.replace("{token}",r.token?`&token=${encodeURIComponent(r.token)}`:""),r.url=r.url.replace("{layers}",r.layers),e.geoserver.loadGeoJson(r)}},this.init()}getData(){return this.data}getLayer(i){return i?typeof i=="string"?this.layers.find(e=>e.key==i):i:null}getLayerProperty(i){const e=this.getLayer(i);if(e&&e.refLayer instanceof Cesium.ImageryLayer)return{alpha:e.refLayer.alpha,brightness:e.refLayer.brightness,contrast:e.refLayer.contrast,hue:e.refLayer.hue,saturation:e.refLayer.saturation};if(e&&e.refLayer instanceof Cesium.Cesium3DTileset){if(e.refLayer.style&&e.refLayer.style.style&&e.refLayer.style.style.color){const t=e.refLayer.style.style.color.replaceAll(/[ a-z'()]/gi,"").split(",");return e.refLayer.style.brightness,{alpha:Number.parseFloat(t[3]),brightness:e.refLayer.style.brightness,red:e.refLayer.style.red,green:e.refLayer.style.green,blue:e.refLayer.style.blue}}return{alpha:1,brightness:1,red:255,green:255,blue:255}}}getSplitLayers(){return this.layers.filter(i=>i.refLayer instanceof Cesium.ImageryLayer||i.refLayer instanceof Cesium.Cesium3DTileset).map(i=>({key:i.key,title:i.title}))}getTreeData(){return list2Tree(this.data)}hide(i){const e=this.getLayer(i);!e||!e.show||(e.type=="terrain"?(this.unloadTerrain(),e.refLayer=!1):e.refLayer.show=!1,e.show=!1,this.emit("showChanged",this.showKeys))}hideAll(){this.layers.filter(i=>i.show&&i.type).forEach(i=>{this.hide(i)})}init(){new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas).setInputAction(e=>{this.pick(e.position).then(r=>{r&&r.properties&&this.emit("pick",r)}).catch(r=>{})},Cesium.ScreenSpaceEventType.LEFT_CLICK),this.on("pick",e=>{this.showInfoBox(e)}),eb.on("draw",()=>{console.log("draw"),this.allowPicking=!1}),eb.on("drawEnd",()=>{console.log("drawEnd"),this.allowPicking=!0})}isSelected(i){return!!(i&&this._select.feature&&i===this._select.feature)}load3DTiles(i,e){i=deepMerge(LayerDefaultOptions["3dtiles"],i);const t=new Cesium.Cesium3DTileset(i);return this.viewer.scene.primitives.add(t)}loadBingMap(i,e){i=deepMerge(LayerDefaultOptions.bing,i);let t=new Cesium.BingMapsImageryProvider(i);return this.viewer.scene.imageryLayers.addImageryProvider(t,Math.min(e,this.viewer.scene.imageryLayers.length))}loadTerrain(i,e){return d(this,null,function*(){return i.url&&(i.provider=i.provider||"CesiumTerrainProvider"),i=deepMerge(LayerDefaultOptions.terrain,i),i.provider?this.viewer.terrainProvider=new Cesium[i.provider](i):this.viewer.terrainProvider=Cesium.createWorldTerrain(i),this.viewer.scene.globe.depthTestAgainstTerrain=!0,!0})}loadData(i){this.data!==i&&(this.data=i,this.reload(),this.layers=[],this.loadLayer(i))}loadHighLightLayer(i){return d(this,null,function*(){this.unloadHighLightLayer(),this.highLightLayer=i,yield this.showAndZoomToLayer(i),i&&i.refLayer&&i.refLayer.brightness!=null,i&&this.hide(i.key)})}loadLayer(i,e){if(Array.isArray(i)){i.forEach((r,a)=>{r.type&&this.loadLayer(r,a)});return}let t=capp.layers[i.key];if(t)t=t.clone(),t.showIndex=e,this.layers.push(t);else{let r=new LayerInfo(i);r.showIndex=this.layers.length,this.layers.push(r)}}pick(i){return new Promise((e,t)=>{if(!this._allowPicking)return;let r=this.viewer.scene.drillPick(i);const a=this.viewer.scene.pick(i);let s=null;const l={properties:null,layerKey:null,x:i.x,y:i.y};if(console.log(r,a),r&&r.length>0&&(r=r.filter(y=>a&&a.id&&a.id===y.id&&y.id.unclickable!=!0)),console.log(r),r&&r.length>0){const y=r[0].id;if(y){const p=y.owner||y.entityCollection&&y.entityCollection.owner;if(console.log(p),p){const m=this.layers.find(L=>L.refLayer===p);m?l.layerKey=m.key:p.name==this.HIGHLIGHT&&(l.layerKey=this.highLightLayer?this.highLightLayer.key:""),this.select=y}y.properties instanceof Cesium.PropertyBag?y.properties.propertyNames.forEach(m=>{l.properties=l.properties||{},l.properties[m]=y.properties[m]._value}):l.properties=y.properties,e(l)}return}this.select=null;const o=core.getCatesian3FromPX(this.viewer,i),n=Cesium.Cartographic.fromCartesian(o);if(!n)return;let c=new Cesium.Cartesian2;const u=this.viewer.camera.positionCartographic.height,g=core.getMapLevel(u);s=this.layers.filter(y=>y.show&&y.refLayer instanceof Cesium.ImageryLayer),this.highLightLayer&&s.unshift(this.highLightLayer);const f=(y=0)=>{if(y>=s.length){t();return}const p=s[y].refLayer._imageryProvider;if(p&&p.ready){c=p.tilingScheme.positionToTileXY(n,g,c);const m=p.pickFeatures(c.x,c.y,g,n.longitude,n.latitude);m&&m.then(L=>{L.length>0?e(v(w({},l),{properties:L[0].properties,layerKey:s[y].key})):f(y+1)})}};f()})}reload(){this.layers.forEach(i=>{this.unLoadLayer(i.key)})}setLayerProperty(i,e){const t=this.getLayer(i);t&&t.refLayer instanceof Cesium.ImageryLayer?(typeof e.alpha=="number"&&(t.refLayer.alpha=e.alpha),typeof e.brightness=="number"&&(t.refLayer.brightness=e.brightness),typeof e.contrast=="number"&&(t.refLayer.contrast=e.contrast),typeof e.hue=="number"&&(t.refLayer.hue=e.hue),typeof e.saturation=="number"&&(t.refLayer.saturation=e.saturation)):t&&t.refLayer instanceof Cesium.Cesium3DTileset&&typeof e.alpha=="number"&&(t.refLayer.style=new Cesium.Cesium3DTileStyle({color:`color('rgba(${e.brightness*e.red},
					  ${e.brightness*e.green},
					  ${e.brightness*e.blue},${e.alpha})')`}),t.refLayer.style.brightness=e.brightness,t.refLayer.style.red=e.red,t.refLayer.style.green=e.green,t.refLayer.style.blue=e.blue)}setLayerStyle(dataSource,style){if(dataSource instanceof Cesium.DataSource&&style){if(typeof style=="string")dataSource.entities.values.forEach(i=>{LayerStyle[style](i)});else if(style.type=="function"){var entities=dataSource.entities.values;for(const i of entities)style.run(i)}else if(style.type=="functionString"){var entities=dataSource.entities.values;for(const entity of entities)eval(style.run)(entity,Cesium)}}}show(i){return d(this,null,function*(){if(!i)return;if(Array.isArray(i))return i.forEach(a=>{this.show(a)});const e=this.getLayer(i);if(console.log("................getlayer",e),!e)return;if(e.show||e.loading)return e;if(e.refLayer)return e.refLayer.show=!0,e.show=!0,this.emit("showChanged",this.showKeys),e;e.loading=!0;const t=yield e.getCapabilities();let r=null;switch(e.type.toLocaleLowerCase().startsWith("arcgis:")&&!e.isStaticLayer&&!(yield this.arcgisserver.checkToken(e.data))&&(yield e.getData(!0)),console.log(e.type),e.type.toLocaleLowerCase()){case"3dtiles":case"3d tiles":case"cesiumlab:3d tiles":{r=this.load3DTiles(e.data,e.showIndex||0),r&&(r.show=e.show=!0,e.refLayer=r);break}case"arcgis:geojson":{r=yield this.arcgisserver.loadGeoJson(e.data,e.showIndex||0),r&&(r.show=e.show=!0,e.refLayer=r,e.refLayer.name=e.key);break}case"arcgis:mapserver":{e.data.usePreCachedTilesIfAvailable=t.isRasterLayer,r=this.arcgisserver.loadWMS(e.data,e.showIndex||0),r&&(r.show=e.show=!0,e.refLayer=r);break}case"arcgis:tms":{r=this.geoserver.loadTMS(v(w({},e.data),{url:`${e.data.url}/tile/{z}/{y}/{x}`}),e.showIndex||0),r&&(r.show=e.show=!0,e.refLayer=r);break}case"arcgis:wmts":{r=this.geoserver.loadWMTS(v(w({},e.data),{url:`${e.data.url}/wmts`}),e.showIndex||0),r&&(r.show=e.show=!0,e.refLayer=r);break}case"bing":{r=this.loadBingMap(e.data,e.showIndex||0),r&&(r.show=e.show=!0,e.refLayer=r);break}case"geojson":case"geoserver:geojson":{r=yield this.geoserver.loadGeoJson(e.data,e.showIndex||0),r&&(r.show=e.show=!0,e.refLayer=r,e.refLayer.name=e.key);break}case"geoserver:kml":case"kml":{r=yield this.geoserver.loadKML(e.data,e.showIndex||0),r&&(r.show=e.show=!0,e.refLayer=r,e.refLayer.name=e.key);break}case"geoserver:tms":case"tms":{r=this.geoserver.loadTMS(e.data,e.showIndex||0),r&&(r.show=e.show=!0,e.refLayer=r);break}case"geoserver:wms":case"wms":{r=this.geoserver.loadWMS(e.data,e.showIndex||0),r&&(r.show=e.show=!0,e.refLayer=r);break}case"geoserver:wmts":case"wmts":{r=this.geoserver.loadWMTS(e.data,e.showIndex||0),r&&(r.show=e.show=!0,e.refLayer=r);break}case"terrain":{r=this.loadTerrain(e.data,e.showIndex||0),r&&(e.refLayer=r,e.show=!0);break}}return console.log(e),e.loading=!1,this.highLightLayer==e?this.showAndZoomToLayer(e):this.getLayer(e.key)!==e&&this.unLoadLayer(e),this.emit("showChanged",this.showKeys),e})}showAndZoomToLayer(i,e){return d(this,null,function*(){e?this.dataCache[i]=e:e=this.dataCache[i];const t=yield this.show(i);t&&t.refLayer&&!["arcgis:wmts","bing","geoserver:wmts","terrain","wmts"].includes(t.type)&&(t.refLayer instanceof Cesium.ImageryLayer?t.refLayer.getViewableRectangle().then(r=>{e&&e.mapServerBox&&(r.east=Cesium.Math.toRadians(e.mapServerBox[2]),r.west=Cesium.Math.toRadians(e.mapServerBox[0]),r.north=Cesium.Math.toRadians(e.mapServerBox[3]),r.south=Cesium.Math.toRadians(e.mapServerBox[1])),deepEquals(r,Cesium.Rectangle.MAX_VALUE)||this.viewer.camera.flyTo({destination:r})}):t.refLayer instanceof Cesium.GeoJsonDataSource?this.viewer.flyTo(t.refLayer):t.refLayer instanceof Cesium.Cesium3DTileset&&this.viewer.flyTo(t.refLayer))})}showInfoBox(i){return d(this,null,function*(){i.x=i.x||400,i.y=i.y||100;let e={};if(i.layerKey){const t=this.getLayer(i.layerKey);if(!t)return;let r=(yield t.getFieldList())||[];r=r.filter(s=>s.allowDisplay);const a={};for(const s in i.properties)a[s.toLocaleLowerCase()]=i.properties[s];r.forEach(s=>{s.cnName?e[s.cnName]=a[s.name.toLocaleLowerCase()]:e[s.name]=a[s.name.toLocaleLowerCase()]})}else e=i.properties||{};e=JSON.stringify(e),e!="{}"&&(this.infoBox=Box.open({style:{left:`${i.x+1}px`,top:`${i.y}px`},title:"属性",beforeClose:()=>{this.infoBox=null}},h(PropertiesBox,{p:e}),this.infoBox,"boxAnchor"))})}sortByKeys(i){i.forEach((e,t)=>{const r=this.layers.findIndex(a=>a.key==e);if(r!==-1){const a=this.layers[r];a.showIndex=t,this.layers.splice(r,1),this.layers.push(a)}}),this.sortByShowIndex()}sortByShowIndex(){this.layers.filter(i=>i.refLayer instanceof Cesium.ImageryLayer).forEach(i=>{this.viewer.imageryLayers.lowerToBottom(i.refLayer)}),this.highLightLayer&&this.highLightLayer.refLayer&&this.highLightLayer.refLayer instanceof Cesium.ImageryLayer&&this.viewer.imageryLayers.raiseToTop(this.highLightLayer.refLayer)}unloadHighLightLayer(){this.highLightLayer&&this.highLightLayer.refLayer&&(this.unLoadLayer(this.highLightLayer),this.highLightLayer=null);const i=this.HIGHLIGHT,e=this.viewer.dataSources.getByName(i);e.length>0&&this.viewer.dataSources.remove(e[0],!0)}unLoadLayer(i){const e=this.getLayer(i);if(!(!e||!e.refLayer))switch(this.hide(e),e.type.toLocaleLowerCase()){case"3dtiles":case"3d tiles":case"cesiumlab:3d tiles":{this.viewer.scene.primitives.remove(e.refLayer),e.refLayer=null;break}case"arcgis:mapserver":case"bing":case"geoserver:tms":case"geoserver:wms":case"geoserver:wmts":case"tms":case"wms":case"wmts":{this.viewer.scene.imageryLayers.remove(e.refLayer),e.refLayer=null;break}case"geojson":case"geoserver:geojson":case"geoserver:kml":case"kml":{this.viewer.dataSources.remove(e.refLayer),e.refLayer=null;break}}}unloadTerrain(){this.viewer.terrainProvider=new Cesium.EllipsoidTerrainProvider({}),this.viewer.scene.globe.depthTestAgainstTerrain=!1}set allowPicking(i){this._allowPicking=i}get HIGHLIGHT(){return"___HIGHLIGHT"}set highLightEntities(i){}get highLightEntities(){}set hover(i){if(i&&this._hover.feature&&i===this._hover.feature)return;const e=Cesium.Color.YELLOW.withAlpha(.5);let t=null;if(this._hover.feature){t=this._hover.feature;const r=this._hover.material;t.point?(t.point.color=r.color,t.point.outlineColor=r.outlineColor):t.polyline?t.polyline.material=r:t.polygon?t.polygon.material=r:t.rectangle?t.rectangle.material=r:t.ellipse&&(t.ellipse.material=r)}i&&(t=this._hover.feature=i,t.point?(this._hover.material={color:t.point.color,outlineColor:t.point.outlineColor},t.point.color=e,t.point.outlineColor=e):t.polyline?(this._hover.material=t.polyline.material,t.polyline.material=e):t.polygon?(this._hover.material=t.polygon.material,t.polygon.material=e):t.rectangle?(this._hover.material=t.rectangle.material,t.rectangle.material=e):t.ellipse?(this._hover.material=t.ellipse.material,t.ellipse.material=e):(this._hover.feature=null,this._hover.material=null))}set select(i){if(i&&this._select.feature&&i===this._select.feature)return;const e=Cesium.Color.DARKORANGE.withAlpha(.5);let t=null;if(this._select.feature){t=this._select.feature;const r=this._select.material;t.point?(t.point.color=r.color,t.point.outlineColor=r.outlineColor):t.polyline?t.polyline.material=r:t.polygon?t.polygon.material=r:t.rectangle?t.rectangle.material=r:t.ellipse&&(t.ellipse.material=r)}i?(t=this._select.feature=i,t.point?(this._select.material={color:t.point.color,outlineColor:t.point.outlineColor},t.point.color=e,t.point.outlineColor=e):t.polyline?(this._select.material=t.polyline.material,t.polyline.material=e):t.polygon?(this._select.material=t.polygon.material,t.polygon.material=e):t.rectangle?(this._select.material=t.rectangle.material,t.rectangle.material=e):t.ellipse?(this._select.material=t.ellipse.material,t.ellipse.material=e):(this._select.feature=null,this._select.material=null)):(this._select.feature=null,this._select.material=null)}get showKeys(){return this.layers.filter(i=>i.show).map(i=>i.key)}get showLeftKeys(){return this.layers.filter(i=>i.show&&(i.refLayer instanceof Cesium.ImageryLayer||i.refLayer instanceof Cesium.Cesium3DTileset)&&i.refLayer.splitDirection!=Cesium.SplitDirection.RIGHT).map(i=>i.key)}get showRightKeys(){return this.layers.filter(i=>i.show&&(i.refLayer instanceof Cesium.ImageryLayer||i.refLayer instanceof Cesium.Cesium3DTileset)&&i.refLayer.splitDirection!=Cesium.SplitDirection.LEFT).map(i=>i.key)}}class SplitMapManager{constructor(e){this.viewer=e,this.splitViewers=[],this.changeEvent=null,this.mode=""}split(e,t){t=w({layout:[[0,1]],synchronization:!1,callback:!1,render:null,controler:null},t);let r=this;this.rootNode=document.getElementById(e).parentNode,this.rootNode.style.position=this.rootNode.style.position||"relative",this.restore(),this.mode="split",console.log("split"),this.rowNumber=t.layout.length,this.columnNumber=t.layout[0].length,this.splitNumber=-1;for(let s=0;s<t.layout.length;s++)for(let l=0;l<t.layout[s].length;l++)this.splitNumber=Math.max(t.layout[s][l],this.splitNumber);if(this.splitNumber++,this.splitNumber<0)return;this.rectangles=[];for(let s=0;s<this.splitNumber;s++)this.rectangles.push([-1,-1,-1,-1]);for(let s=0;s<t.layout.length;s++)for(let l=0;l<t.layout[s].length;l++){let o=t.layout[s][l];if(o<0)return;let n=this.rectangles[o];n[0]=n[0]==-1?l:Math.min(n[0],l),n[1]=n[1]==-1?s:Math.min(n[1],s),n[2]=n[2]==-1?l:Math.max(n[2],l),n[3]=n[3]==-1?s:Math.max(n[3],s)}deepEquals(this.rectangles[0],[-1,-1,-1,-1])&&(this.rectangles[0]=[0,0,0,0]),this.rectangles=this.rectangles.filter(s=>!deepEquals(s,[-1,-1,-1,-1])).map(s=>[s[0]*100/this.columnNumber+"%",s[1]*100/this.rowNumber+"%",(s[2]-s[0]+1)*100/this.columnNumber+"%",(s[3]-s[1]+1)*100/this.rowNumber+"%"]);let a=this.rootNode.children[0];a.style.position="absolute",a.style.left=this.rectangles[0][0],a.style.top=this.rectangles[0][1],a.style.width=this.rectangles[0][2],a.style.height=this.rectangles[0][3],this.splitViewers=[{viewer:this.viewer,synchronization:!0}],this.rectangles.forEach((s,l)=>{if(l==0)return;let o=document.createElement("div");o.style.position="absolute",o.id="cesium_container_"+l,o.style.left=this.rectangles[l][0],o.style.top=this.rectangles[l][1],o.style.width=this.rectangles[l][2],o.style.height=this.rectangles[l][3],this.rootNode.appendChild(o);let n=new CesiumMap(o.id).viewer;this.splitViewers.push({viewer:n,synchronization:!!t.synchronization}),t.render&&render(t.render(l,n),o)}),t.controler&&Box.open({style:{}},h(t.controler,{data:this})),this.splitViewers.forEach(s=>{let l=s.viewer;l.camera.percentageChanged=.01,l.scene.camera.changed.addEventListener(()=>{r.cameraSynchronization(s)})}),t.synchronization&&r.cameraSynchronization(this.splitViewers[0]),t.callback&&callback(r.splitViewers)}setSynchronization(e,t){let r=this.splitViewers[e];r.synchronization=t,this.cameraSynchronization(this.splitViewers[0])}cameraSynchronization(e){if(!e.synchronization)return;let t=e.viewer,a=t.scene.globe.ellipsoid.cartesianToCartographic(t.camera.position),s={position:{longitude:Cesium.Math.toDegrees(a.longitude),latitude:Cesium.Math.toDegrees(a.latitude),height:a.height},orientation:{heading:t.camera.heading,pitch:t.camera.pitch,roll:t.camera.roll}};this.splitViewers.forEach(o=>{o.viewer!==t&&o.synchronization&&l(o.viewer)});function l(o){o.camera.setView({destination:Cesium.Cartesian3.fromDegrees(s.position.longitude,s.position.latitude,s.position.height),orientation:s.orientation})}}curtain(e,t){t=w({style:""},t);let r=this;this.restore(),this.mode="curtain";const a=this.slider=document.createElement("div");document.getElementById(e).appendChild(a),a.style=t.style||"position: absolute;left: 50%;top: 0px;background-color: rgba(40,44,52,0.8);width: 5px;height: 100%;z-index: 99;cursor:ew-resize;",this.viewer.scene.splitPosition=a.offsetLeft/a.parentElement.offsetWidth;const s=this.sliderHandler=new Cesium.ScreenSpaceEventHandler(a);let l=!1,o=n=>{if(!l)return;const c=n.endPosition.x,u=(a.offsetLeft+c)/a.parentElement.offsetWidth;a.style.left=`${100*u}%`,r.viewer.scene.splitPosition=u};s.setInputAction(function(){l=!0},Cesium.ScreenSpaceEventType.LEFT_DOWN),s.setInputAction(function(){l=!0},Cesium.ScreenSpaceEventType.PINCH_START),s.setInputAction(o,Cesium.ScreenSpaceEventType.MOUSE_MOVE),s.setInputAction(o,Cesium.ScreenSpaceEventType.PINCH_MOVE),s.setInputAction(function(){l=!1},Cesium.ScreenSpaceEventType.LEFT_UP),s.setInputAction(function(){l=!1},Cesium.ScreenSpaceEventType.PINCH_END)}restore(){if(this.mode){if(this.mode="",this.rootNode&&this.rootNode.children&&this.rootNode.children.length){let e=this.rootNode.children;for(;e.length>1;)this.rootNode.removeChild(e[1]);let t=e[0];t.style.position="absolute",t.style.left="0",t.style.top="0",t.style.width="100%",t.style.height="100%"}this.splitViewers&&this.splitViewers.length&&(this.splitViewers.forEach((e,t)=>{if(t==0)return;let r=e.viewer;r&&!r.isDestroyed()&&r.destroy()}),this.splitViewers=[]),this.changeEvent&&console.log(this.changeEvent),this.slider&&(this.slider.parentElement.removeChild(this.slider),this.slider=null),this.sliderHandler&&!this.sliderHandler.isDestroyed()&&(this.sliderHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN),this.sliderHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP),this.sliderHandler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE),this.sliderHandler.removeInputAction(Cesium.ScreenSpaceEventType.PINCH_START),this.sliderHandler.removeInputAction(Cesium.ScreenSpaceEventType.PINCH_END),this.sliderHandler.removeInputAction(Cesium.ScreenSpaceEventType.PINCH_MOVE),this.sliderHandler.destroy(),this.sliderHandler=null),ztu.global.GISLayers.getSplitLayers().forEach(e=>{e=ztu.global.GISLayers.getLayer(e.key),e.refLayer.splitDirection=Cesium.SplitDirection.NONE}),Box.closeAll()}}}const _hoisted_1={id:"cesiumContainer"},_sfc_main={__name:"MapViewer",props:{visibleLayers:Array},emits:["viewMapLoaded"],setup(i,{emit:e}){const t=i,r=e,a=useRouter();onMounted(()=>d(null,null,function*(){capp||appInit(a),yield capp.initLogin({}),s()})),watch(()=>t.visibleLayers,l=>{console.log("New Layer Selected:",l)});const s=()=>d(null,null,function*(){initMap("viewDiv",l=>d(null,null,function*(){ztu.global.GISLayers=new layerService(l),ztu.global.SplitMap=new SplitMapManager(l),initShapeViewer({viewer:l,drawer:null,useLocalStorage:!0,data:null}),l.scene.morphStart.addEventListener((c,u,g,f)=>{c._morphCancelled=!0,console.log(c,u,g,f)});const o=ztu.global.GISLayers;if(o.data.length===0&&capp){const c=capp.layersOrigin;o.loadData(c);const u=capp.chkLayers,g=c.filter(f=>u.includes(f.key)).map(f=>f.key);o.show(g),o.on("reloadlayer",()=>{u.value=[]}),o.on("showChanged",f=>{u.value=f})}const n={longitude:118.089425,latitude:26.079353,height:3e6};l.camera.setView({destination:Cesium.Cartesian3.fromDegrees(n.longitude,n.latitude,n.height)}),r("viewMapLoaded",!0)}))});return(l,o)=>(openBlock(),createElementBlock("div",_hoisted_1,o[0]||(o[0]=[createBaseVNode("div",{id:"viewDiv"},null,-1)])))}},MapViewer=_export_sfc(_sfc_main,[["__scopeId","data-v-cac934c7"]]),MapViewer$1=Object.freeze(Object.defineProperty({__proto__:null,default:MapViewer},Symbol.toStringTag,{value:"Module"}));export{MapViewer as M,MapViewer$1 as a,layerService as l};
