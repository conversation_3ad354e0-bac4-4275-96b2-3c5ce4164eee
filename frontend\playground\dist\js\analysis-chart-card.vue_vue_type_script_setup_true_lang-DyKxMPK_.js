import{_ as n,a as l,b as r,c as _}from"./CardTitle.vue_vue_type_script_setup_true_lang-D9WinrTz.js";import{a4 as o,af as c,ag as f,ah as a,n as s,a3 as e,an as i,ao as m,ae as u}from"../jse/index-index-BMh_AyeW.js";const x=o({name:"AnalysisChartCard",__name:"analysis-chart-card",props:{title:{}},setup(d){return(t,p)=>(c(),f(e(_),null,{default:a(()=>[s(e(l),null,{default:a(()=>[s(e(n),{class:"text-xl"},{default:a(()=>[i(m(t.title),1)]),_:1})]),_:1}),s(e(r),null,{default:a(()=>[u(t.$slots,"default")]),_:3})]),_:3}))}});export{x as _};
