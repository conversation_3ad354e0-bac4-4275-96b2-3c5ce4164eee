var f=(i,p,a)=>new Promise((c,n)=>{var o=t=>{try{s(a.next(t))}catch(e){n(e)}},u=t=>{try{s(a.throw(t))}catch(e){n(e)}},s=t=>t.done?c(t.value):Promise.resolve(t.value).then(o,u);s((a=a.apply(i,p)).next())});import{u as _}from"./vxe-table-CZ9gPHn5.js";import"./entity.data-u4HDUExc.js";import{c as y}from"./entity.api-CPgpBrqe.js";import{d as h,r as g,w as b,o as x,a as C,b as I,s as v,k as D}from"../jse/index-index-DyHD_jbN.js";import{a as G}from"./bootstrap-5OPUVRWy.js";import"./loading-DzjUKA94.js";import"./form-DdFfsSWf.js";const V={class:"app-container"},k=h({__name:"EntityLayerData",props:["columns","layerId"],emits:["handleCancel"],setup(i,{emit:p}){const a=i;let c=g([]),n=g([]),o=a.layerId;b(()=>a.layerId,(e,m)=>{console.log(`count changed from ${m} to ${e}`),o=e,console.log(e),t.reload()});const u={columns:n.value,data:c.value,height:"320px",pagerConfig:{enabled:!0},proxyConfig:{ajax:{query:(M,N)=>f(null,[M,N],function*({page:e},m){if(!o)return t.setGridOptions({columns:[]}),{total:0,items:[]};const r=yield y(o,{page:{current:e.currentPage,size:e.pageSize,searchCount:!0}});if(e.currentPage==1&&r.columns){let d=[];for(let l=0;l<r.columns.length;l++)d.push({title:r.columns[l],field:r.columns[l],width:120});t.setGridOptions({columns:d})}return{total:r.pageValues.total,items:r.pageValues.records}})}},sortConfig:{multiple:!0}},[s,t]=_({gridOptions:u});return x(()=>{o=a.layerId}),(e,m)=>(I(),C("view",V,[v(D(s))]))}}),j=G(k,[["__scopeId","data-v-be0f6f66"]]);export{j as default};
