{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/column/index.js"], "sourcesContent": ["import { VxeUI } from '../ui';\nimport VxeColumnComponent from '../table/src/column';\nexport const VxeColumn = Object.assign({}, VxeColumnComponent, {\n    install(app) {\n        app.component(VxeColumnComponent.name, VxeColumnComponent);\n        // 兼容旧用法\n        app.component('VxeTableColumn', VxeColumnComponent);\n    }\n});\nif (VxeUI.dynamicApp) {\n    VxeUI.dynamicApp.component(VxeColumnComponent.name, VxeColumnComponent);\n    // 兼容旧用法\n    VxeUI.dynamicApp.component('VxeTableColumn', VxeColumnComponent);\n}\nVxeUI.component(VxeColumnComponent);\nexport const Column = VxeColumn;\nexport default VxeColumn;\n"], "mappings": ";;;;;;;;AAEO,IAAM,YAAY,OAAO,OAAO,CAAC,GAAG,gBAAoB;AAAA,EAC3D,QAAQ,KAAK;AACT,QAAI,UAAU,eAAmB,MAAM,cAAkB;AAEzD,QAAI,UAAU,kBAAkB,cAAkB;AAAA,EACtD;AACJ,CAAC;AACD,IAAI,MAAM,YAAY;AAClB,QAAM,WAAW,UAAU,eAAmB,MAAM,cAAkB;AAEtE,QAAM,WAAW,UAAU,kBAAkB,cAAkB;AACnE;AACA,MAAM,UAAU,cAAkB;AAC3B,IAAM,SAAS;AACtB,IAAOA,kBAAQ;", "names": ["column_default"]}