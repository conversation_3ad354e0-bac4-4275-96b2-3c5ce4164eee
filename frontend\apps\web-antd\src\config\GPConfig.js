export default {
  buffer:
    'http://fzzt.fzjhdn.com:6080/arcgis/rest/services/GPServices/bufferModel/GPServer/bufferModel/execute',
  erase:
    'http://fzzt.fzjhdn.com:6080/arcgis/rest/services/GPServices/eraseModel/GPServer/eraseModel/execute',
  intersect:
    'http://fzzt.fzjhdn.com:6080/arcgis/rest/services/GPServices/intersectModel/GPServer/intersectModel/execute',
  symmetrical:
    'http://fzzt.fzjhdn.com:6080/arcgis/rest/services/GPServices/symmetricalModel/GPServer/symmetricalModel/execute',
  union:
    'http://fzzt.fzjhdn.com:6080/arcgis/rest/services/GPServices/unionModel/GPServer/unionModel/execute',
  update:
    'http://fzzt.fzjhdn.com:6080/arcgis/rest/services/GPServices/updateModel/GPServer/updateModel/execute',
  identity:
    'http://fzzt.fzjhdn.com:6080/arcgis/rest/services/GPServices/identityModel/GPServer/identityModel/execute',
  pointdistance:
    'http://fzzt.fzjhdn.com:6080/arcgis/rest/services/GPServices/pointDistanceModel/GPServer/pointDistanceModel/execute',
  squareneighbor:
    'http://fzzt.fzjhdn.com:6080/arcgis/rest/services/GPServices/polygonNeighborsModel/GPServer/polygonNeighborsModel',
};
