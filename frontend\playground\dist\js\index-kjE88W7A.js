import{_ as r}from"./infinite-queries.vue_vue_type_script_setup_true_lang-D78LvLVP.js";import{_ as s}from"./paginated-queries.vue_vue_type_script_setup_true_lang-B9vAIu01.js";import{_ as i}from"./query-retries.vue_vue_type_script_setup_true_lang-Dy38lU2o.js";import{C as o}from"./index-B_b7xM74.js";import{_}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as m,af as f,ag as c,ah as t,a3 as e,ap as p,n as a}from"../jse/index-index-BMh_AyeW.js";import"./useBaseQuery-6AyvSVSW.js";import"./bootstrap-DShsrVit.js";import"./useQuery-CNjS4j1-.js";const n={class:"grid grid-cols-1 gap-4 md:grid-cols-2"},w=m({__name:"index",setup(d){return(l,u)=>(f(),c(e(_),{title:"Vue Query示例"},{default:t(()=>[p("div",n,[a(e(o),{title:"分页查询"},{default:t(()=>[a(s)]),_:1}),a(e(o),{title:"无限滚动"},{default:t(()=>[a(r,{class:"h-[300px] overflow-auto"})]),_:1}),a(e(o),{title:"错误重试"},{default:t(()=>[a(i)]),_:1})])]),_:1}))}});export{w as default};
