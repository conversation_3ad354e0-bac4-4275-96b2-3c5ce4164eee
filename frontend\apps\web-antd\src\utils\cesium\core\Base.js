import * as Cesium from 'cesium';
import JCesium from './JCesium';
/**
 * 基础模块
 * @param {*} viewer
 */
function Base(viewer) {
  if (viewer) {
    this._viewer = viewer;
    this._installBaiduImageryProvider();
    this._installGooGleImageryProvider();
    this._installAmapImageryProvider();
    this._installTencentImageryProvider();
    this._installTdtImageryProvider();
  }
}
Base.prototype = {
  //相机定位
  setView: function (options) {
    if (this._viewer && options && options.position) {
      if (options.distance) {
        //距离

        var pos1 = new Cesium.Cartesian3(0, options.distance, opt.distance);
        options.position = Cesium.Cartesian3.add(
          options.position,
          pos1,
          new Cesium.Cartesian3(),
        );
      }
      this._viewer.scene.camera.setView({
        destination: options.position,
        orientation: options.orientation || {
          heading: Cesium.Math.toRadians(90.0),
          pitch: Cesium.Math.toRadians(90.0),
          roll: Cesium.Math.toRadians(0.0),
        },
      });
    }
  },
  //相机飞行
  flyTo: function (options) {
    if (this._viewer && options && options.position) {
      if (options.distance) {
        //距离
        var pos1 = new Cesium.Cartesian3(0, options.distance, options.distance);
        options.position = Cesium.Cartesian3.add(
          options.position,
          pos1,
          new Cesium.Cartesian3(),
        );
      }
      this._viewer.scene.camera.flyTo({
        destination: options.position,
        orientation: options.orientation || {
          heading: Cesium.Math.toRadians(90.0),
          pitch: Cesium.Math.toRadians(90.0),
          roll: Cesium.Math.toRadians(0.0),
        },
        // pitchAdjustHeight: 500,
        easingFunction:
          options.easingFunction || Cesium.EasingFunction.LINEAR_NONE,
        duration: options.duration || 3,
        complete: options.callback,
      });
    }
  },
  //坐标转换 笛卡尔转84
  transformCartesianToWGS84: function (cartesian) {
    if (this._viewer && cartesian) {
      var ellipsoid = Cesium.Ellipsoid.WGS84;
      var cartographic = ellipsoid.cartesianToCartographic(cartesian);
      return {
        lng: Cesium.Math.toDegrees(cartographic.longitude),
        lat: Cesium.Math.toDegrees(cartographic.latitude),
        alt: cartographic.height,
      };
    }
  },
  //坐标数组转换 笛卡尔转84
  transformWGS84ArrayToCartesianArray: function (WSG84Arr, alt) {
    if (this._viewer && WSG84Arr) {
      var $this = this;
      return WSG84Arr
        ? WSG84Arr.map(function (item) {
            return $this.transformWGS84ToCartesian(item, alt);
          })
        : [];
    }
  },
  //坐标转换 84转笛卡尔
  transformWGS84ToCartesian: function (position, alt) {
    if (this._viewer) {
      return position
        ? Cesium.Cartesian3.fromDegrees(
            position.lng || position.lon,
            position.lat,
            (position.alt = alt || position.alt),
            Cesium.Ellipsoid.WGS84,
          )
        : Cesium.Cartesian3.ZERO;
    }
  },
  //坐标数组转换 84转笛卡尔
  transformCartesianArrayToWGS84Array: function (cartesianArr) {
    if (this._viewer) {
      var $this = this;
      return cartesianArr
        ? cartesianArr.map(function (item) {
            return $this.transformCartesianToWGS84(item);
          })
        : [];
    }
  },
  /**
	 * 相机绕点旋转
	 * @param viewer
	 *  let options = {
	        lng: 117.1423291616,
	        lat: 39.0645831633,
	        height: 15.8,
	        heading: 0.0,
	        pitch: 0.0,
	        roll: 0.0
	    };
	    viewer.clock.stopTime = viewer.clock.startTime 
	*/
  setCameraEotateHeading(options) {
    if (options) {
      let viewer = this._viewer;
      let position = Cesium.Cartesian3.fromDegrees(
        options.lng,
        options.lat,
        options.height,
      );
      // 相机看点的角度，如果大于0那么则是从地底往上看，所以要为负值，这里取-30度
      let pitch = Cesium.Math.toRadians(-30);
      // 给定飞行一周所需时间，比如10s, 那么每秒转动度数
      let angle = 360 / 30;
      // 给定相机距离点多少距离飞行，这里取值为5000m
      let distance = 5000;
      let startTime = Cesium.JulianDate.fromDate(new Date());
      viewer.clock.startTime = startTime.clone(); // 开始时间
      viewer.clock.currentTime = startTime.clone(); // 当前时间
      viewer.clock.clockRange = Cesium.ClockRange.CLAMPED; // 行为方式
      viewer.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK; // 时钟设置为当前系统时间; 忽略所有其他设置。
      //相机的当前heading
      let initialHeading = viewer.camera.heading;
      let Exection = function TimeExecution() {
        // 当前已经过去的时间，单位s
        let delTime = Cesium.JulianDate.secondsDifference(
          viewer.clock.currentTime,
          viewer.clock.startTime,
        );
        let heading = Cesium.Math.toRadians(delTime * angle) + initialHeading;
        viewer.scene.camera.setView({
          destination: position, // 点的坐标
          orientation: {
            heading: heading,
            pitch: pitch,
          },
        });
        viewer.scene.camera.moveBackward(distance);

        if (
          Cesium.JulianDate.compare(
            viewer.clock.currentTime,
            viewer.clock.stopTime,
          ) >= 0
        ) {
          viewer.clock.onTick.removeEventListener(Exection);
        }
      };
      viewer.clock.onTick.addEventListener(Exection);
    }
  },
  /**
   *
   * @param {*} position
   * 84坐标转制图坐标
   */
  transformWGS84ToCartographic: function (position) {
    return position
      ? Cesium.Cartographic.fromDegrees(
          position.lng || position.lon,
          position.lat,
          position.alt,
        )
      : Cesium.Cartographic.ZERO;
  },
  // 拾取位置点
  getCatesian3FromPX: function (px) {
    let viewer = this._viewer;
    if (viewer && px) {
      var picks = viewer.scene.drillPick(px);
      var cartesian = null;
      var isOn3dtiles = false,
        isOnTerrain = false;
      // drillPick
      for (let i in picks) {
        let pick = picks[i];

        if (
          (pick && pick.primitive instanceof Cesium.Cesium3DTileFeature) ||
          (pick && pick.primitive instanceof Cesium.Cesium3DTileset) ||
          (pick && pick.primitive instanceof Cesium.Model)
        ) {
          //模型上拾取
          isOn3dtiles = true;
        }
        // 3dtilset
        if (isOn3dtiles) {
          viewer.scene.pick(px); // pick
          cartesian = viewer.scene.pickPosition(px);
          if (cartesian) {
            let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
            if (cartographic.height < 0) cartographic.height = 0;
            let lon = Cesium.Math.toDegrees(cartographic.longitude),
              lat = Cesium.Math.toDegrees(cartographic.latitude),
              height = cartographic.height;
            cartesian = this.transformWGS84ToCartesian({
              lng: lon,
              lat: lat,
              alt: height,
            });
          }
        }
      }
      // 地形
      let boolTerrain =
        viewer.terrainProvider instanceof Cesium.EllipsoidTerrainProvider;
      // Terrain
      if (!isOn3dtiles && !boolTerrain) {
        var ray = viewer.scene.camera.getPickRay(px);
        if (!ray) return null;
        cartesian = viewer.scene.globe.pick(ray, viewer.scene);
        isOnTerrain = true;
      }
      // 地球
      if (!isOn3dtiles && !isOnTerrain && boolTerrain) {
        cartesian = viewer.scene.camera.pickEllipsoid(
          px,
          viewer.scene.globe.ellipsoid,
        );
      }
      if (cartesian) {
        let position = this.transformCartesianToWGS84(cartesian);
        if (position.alt < 0) {
          cartesian = this.transformWGS84ToCartesian(position, 0.1);
        }
        return cartesian;
      }
      return false;
    }
  },
  //获取相机位置
  getCameraPosition: function () {
    if (this._viewer) {
      var result = this._viewer.scene.camera.pickEllipsoid(
        new Cesium.Cartesian2(
          this._viewer.canvas.clientWidth / 2,
          this._viewer.canvas.clientHeight / 2,
        ),
      );
      if (result) {
        var curPosition =
            Cesium.Ellipsoid.WGS84.cartesianToCartographic(result),
          lon = (curPosition.longitude * 180) / Math.PI,
          lat = (curPosition.latitude * 180) / Math.PI;

        var direction = this._viewer.camera._direction,
          x = Cesium.Math.toDegrees(direction.x),
          y = Cesium.Math.toDegrees(direction.y),
          z = Cesium.Math.toDegrees(direction.z),
          height = this._viewer.camera.positionCartographic.height,
          heading = Cesium.Math.toDegrees(this._viewer.camera.heading),
          pitch = Cesium.Math.toDegrees(this._viewer.camera.pitch),
          roll = Cesium.Math.toDegrees(this._viewer.camera.roll);

        var rectangle = this._viewer.camera.computeViewRectangle(),
          west = (rectangle.west / Math.PI) * 180,
          north = (rectangle.north / Math.PI) * 180,
          east = (rectangle.east / Math.PI) * 180,
          south = (rectangle.south / Math.PI) * 180,
          centerx = (west + east) / 2,
          cnetery = (north + south) / 2;

        return {
          lon: lon,
          lat: lat,
          height: height,
          heading: heading,
          pitch: pitch,
          roll: roll,
          position: this._viewer.camera.position,
          center: {
            x: centerx,
            y: cnetery,
          },
          direction: new Cesium.Cartesian3(x, y, z),
        };
      }
    }
  },
  //修改相机状态
  updateCameraState: function (flag) {
    this._viewer.scene._screenSpaceCameraController.enableRotate = flag;
    this._viewer.scene._screenSpaceCameraController.enableTranslate = flag;
    this._viewer.scene._screenSpaceCameraController.enableZoom = flag;
    this._viewer.scene._screenSpaceCameraController.enableTilt = flag;
    this._viewer.scene._screenSpaceCameraController.enableLook = flag;
  },
  //鼠标事件注册
  bindHandelEvent: function (
    _mouseClickHandler,
    _mouseMoveHandler,
    _mouseDbClickHandler,
  ) {
    if (this._viewer) {
      var _handlers = new Cesium.ScreenSpaceEventHandler(this._viewer.canvas);
      _handlers.setInputAction(function (movement) {
        _mouseClickHandler && _mouseClickHandler(movement, _handlers);
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

      _handlers.setInputAction(function (movement) {
        _mouseMoveHandler && _mouseMoveHandler(movement, _handlers);
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

      _handlers.setInputAction(function (movement) {
        _mouseDbClickHandler && _mouseDbClickHandler(movement, _handlers);
      }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK);
    }
  },
  //获取鼠标信息
  getHandelPosition: function (callback) {
    if (this._viewer) {
      var _handler = new Cesium.ScreenSpaceEventHandler(
          this._viewer.scene.canvas,
        ),
        $this = this;
      _handler.setInputAction(function (movement) {
        var cartesian = $this._viewer.scene.camera.pickEllipsoid(
          movement.endPosition,
          $this._viewer.scene.globe.ellipsoid,
        );

        if (typeof callback === 'function') {
          callback($this.transformCartesianToWGS84(cartesian), _handler);
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }
  },
  //保存当前场景png
  saveSceneImages: function () {
    if (this._viewer) {
      function dataURLtoBlob(dataurl) {
        var arr = dataurl.split(','),
          mime = arr[0].match(/:(.*?);/)[1],
          bstr = atob(arr[1]),
          n = bstr.length,
          u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], {
          type: mime,
        });
      }

      var canvas = this._viewer.scene.canvas;
      var image = canvas
        .toDataURL('image/png')
        .replace('image/png', 'image/octet-stream');
      var link = document.createElement('a');
      var strDataURI = image.substr(22, image.length);
      var blob = dataURLtoBlob(image);
      var objurl = URL.createObjectURL(blob);
      link.download = 'scene.png';
      link.href = objurl;
      link.click();
    }
  },
  /**
   * amap
   */
  _installAmapImageryProvider: function () {
    const IMG_URL =
      'https://webst{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}';
    const ELEC_URL =
      'http://webrd{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=7&x={x}&y={y}&z={z}';

    function AmapImageryProvider(options) {
      options['url'] = options.style === 'img' ? IMG_URL : ELEC_URL;
      if (!options.subdomains) {
        options['subdomains'] = ['01', '02', '03', '04'];
      }
      return new Cesium.UrlTemplateImageryProvider(options);
    }

    JCesium.AmapImageryProvider = AmapImageryProvider;
  },
  /**
   * 天地图
   */
  _installTdtImageryProvider: function () {
    const MAP_URL =
      'http://t{s}.tianditu.gov.cn/{layer}_c/wmts?service=WMTS&version=1.0.0&request=GetTile&tilematrix={TileMatrix}&layer={layer}&style={style}&tilerow={TileRow}&tilecol={TileCol}&tilematrixset={TileMatrixSet}&format=tiles&tk={key}';

    function TdtImageryProvider(options) {
      return new Cesium.UrlTemplateImageryProvider({
        url: MAP_URL.replace(/\{layer\}/g, options.style || 'vec').replace(
          /\{key\}/g,
          options.key || '',
        ),
        style: 'default',
        format: 'tiles',
        tileMatrixSetID: 'c',
        subdomains: [...Array(6).keys()].map((item) => (item + 1).toString()),
        tileMatrixLabels: [...Array(18).keys()].map((item) =>
          (item + 1).toString(),
        ),
        tilingScheme: new Cesium.GeographicTilingScheme(),
        maximumLevel: 18,
      });
    }

    JCesium.TdtImageryProvider = TdtImageryProvider;
  },
  /**
   * 腾讯
   */
  _installTencentImageryProvider: function () {
    const ELEC_URL =
      'https://rt{s}.map.gtimg.com/tile?z={z}&x={x}&y={reverseY}&styleid=1000&scene=0&version=347';

    function TencentImageryProvider(options) {
      options['url'] = ELEC_URL;
      if (!options.subdomains) {
        options['subdomains'] = ['0', '1', '2'];
      }
      return new Cesium.UrlTemplateImageryProvider(options);
    }

    JCesium.TencentImageryProvider = TencentImageryProvider;
  },
  /**
   * google
   */
  _installGooGleImageryProvider: function () {
    //标注 影像 地形三种
    const ELEC_URL =
      'http://mt{s}.google.cn/vt/lyrs=m@207000000&hl=zh-CN&gl=CN&src=app&x={x}&y={y}&z={z}&s=Galile';
    const IMG_URL =
      'http://mt{s}.google.cn/vt/lyrs=s&hl=zh-CN&x={x}&y={y}&z={z}&s=Gali';
    const TER_URL =
      'http://mt{s}.google.cn/vt/lyrs=t@131,r@227000000&hl=zh-CN&gl=cn&x={x}&y={y}&z={z}&s=Galile';

    function GoogleImageryProvider(options) {
      options['url'] =
        options.style === 'img'
          ? IMG_URL
          : options.style === 'ter'
            ? TER_URL
            : ELEC_URL;
      if (!options.subdomains) {
        options['subdomains'] = ['1', '2', '3', '4', '5'];
      }
      return new Cesium.UrlTemplateImageryProvider(options);
    }
    JCesium.GoogleImageryProvider = GoogleImageryProvider;
  },
  /**
   * 百度影像拓展
   */
  _installBaiduImageryProvider: function () {
    var TEMP_MAP_URL =
      'http://api{s}.map.bdimg.com/customimage/tile?&x={x}&y={y}&z={z}&scale=1&customid={style}';

    function BaiduImageryProvider(options) {
      TEMP_MAP_URL = options.temp_url || TEMP_MAP_URL;

      this._url = TEMP_MAP_URL;
      this._tileWidth = 256;
      this._tileHeight = 256;
      this._maximumLevel = 18;
      this._minimumLevel = 1;
      this._tilingScheme = new Cesium.WebMercatorTilingScheme({
        rectangleSouthwestInMeters: new Cesium.Cartesian2(-33554054, -33746824),
        rectangleNortheastInMeters: new Cesium.Cartesian2(33554054, 33746824),
      });
      this._rectangle = this._tilingScheme.rectangle;
      this._credit = undefined;
      this._style = options.style || 'normal';
    }

    Object.defineProperties(BaiduImageryProvider.prototype, {
      url: {
        get: function () {
          return this._url;
        },
      },
      token: {
        get: function () {
          return this._token;
        },
      },
      tileWidth: {
        get: function () {
          if (!this.ready) {
            throw new Cesium.DeveloperError(
              'tileWidth must not be called before the imagery provider is ready.',
            );
          }
          return this._tileWidth;
        },
      },
      tileHeight: {
        get: function () {
          if (!this.ready) {
            throw new Cesium.DeveloperError(
              'tileHeight must not be called before the imagery provider is ready.',
            );
          }
          return this._tileHeight;
        },
      },
      maximumLevel: {
        get: function () {
          if (!this.ready) {
            throw new Cesium.DeveloperError(
              'tileHeight must not be called before the imagery provider is ready.',
            );
          }
          return this._tileHeight;
        },
      },
      minimumLevel: {
        get: function () {
          if (!this.ready) {
            throw new Cesium.DeveloperError(
              'minimumLevel must not be called before the imagery provider is ready.',
            );
          }
          return 0;
        },
      },
      tilingScheme: {
        get: function () {
          if (!this.ready) {
            throw new Cesium.DeveloperError(
              'tilingScheme must not be called before the imagery provider is ready.',
            );
          }
          return this._tilingScheme;
        },
      },

      rectangle: {
        get: function () {
          if (!this.ready) {
            throw new Cesium.DeveloperError(
              'rectangle must not be called before the imagery provider is ready.',
            );
          }
          return this._rectangle;
        },
      },

      ready: {
        get: function () {
          return !!this._url;
        },
      },

      credit: {
        get: function () {
          return this._credit;
        },
      },
    });

    BaiduImageryProvider.prototype.getTileCredits = function (x, y, level) {};

    BaiduImageryProvider.prototype.requestImage = function (x, y, level) {
      if (!this.ready) {
        throw new Cesium.DeveloperError(
          'requestImage must not be called before the imagery provider is ready.',
        );
      }
      var xTiles = this._tilingScheme.getNumberOfXTilesAtLevel(level);
      var yTiles = this._tilingScheme.getNumberOfYTilesAtLevel(level);
      var url = this._url
        .replace('{x}', x - xTiles / 2)
        .replace('{y}', yTiles / 2 - y - 1)
        .replace('{z}', level)
        .replace('{s}', 1)
        .replace('{style}', this._style);
      return Cesium.ImageryProvider.loadImage(this, url);
    };

    JCesium.BaiduImageryProvider = BaiduImageryProvider;
  },
};
export default Base;
