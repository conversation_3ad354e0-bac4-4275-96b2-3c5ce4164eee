import{ao as s,k as a,aP as i,l as e,ay as t,j as n}from"./chunks/framework.C8U7mBUf.js";const l=JSON.parse('{"title":"PROJECT UPDATE","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/other/project-update.md","filePath":"en/guide/other/project-update.md"}');const o=s({name:"en/guide/other/project-update.md"},[["render",function(s,l,o,h,p,d){const c=t("NolebaseGitContributors"),r=t("NolebaseGitChangelog");return n(),a("div",null,[l[0]||(l[0]=i('<h1 id="project-update" tabindex="-1">PROJECT UPDATE <a class="header-anchor" href="#project-update" aria-label="Permalink to &quot;PROJECT UPDATE&quot;">​</a></h1><h2 id="why-can-t-it-be-updated-like-a-npm-plugin" tabindex="-1">Why Can&#39;t It Be Updated Like a npm Plugin <a class="header-anchor" href="#why-can-t-it-be-updated-like-a-npm-plugin" aria-label="Permalink to &quot;Why Can&#39;t It Be Updated Like a npm Plugin&quot;">​</a></h2><p>Because the project is a complete project template, not a plugin or a package, it cannot be updated like a plugin. After you use the code, you will develop it further based on business needs, and you need to manually merge and upgrade.</p><h2 id="what-should-i-do" tabindex="-1">What Should I Do <a class="header-anchor" href="#what-should-i-do" aria-label="Permalink to &quot;What Should I Do&quot;">​</a></h2><p>The project is managed using a <code>Monorepo</code> approach and has abstracted some of the more core code, such as <code>packages/@core</code>, <code>packages/effects</code>. As long as the business code has not modified this part of the code, you can directly pull the latest code and then merge it into your branch. You only need to handle some conflicts simply. Other folders will only make some minor adjustments, which will not affect the business code.</p><div class="tip custom-block"><p class="custom-block-title">Recommendation</p><p>It is recommended to follow the repository updates actively and merge them; do not accumulate over a long time, Otherwise, it will lead to too many merge conflicts and increase the difficulty of merging.</p></div><h2 id="updating-code-using-git" tabindex="-1">Updating Code Using Git <a class="header-anchor" href="#updating-code-using-git" aria-label="Permalink to &quot;Updating Code Using Git&quot;">​</a></h2><ol><li>Clone the code</li></ol><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> clone</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> https://github.com/vbenjs/vue-vben-admin.git</span></span></code></pre></div><ol start="2"><li>Add your company&#39;s git source address</li></ol><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># up is the source name, can be set arbitrarily</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># gitUrl is the latest open-source code</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> remote</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> add</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> up</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> gitUrl</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span></code></pre></div><ol start="3"><li>Push the code to your company&#39;s git</li></ol><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Push the code to your company</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># main is the branch name, adjust according to your situation</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> push</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> up</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> main</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Sync the company&#39;s code</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># main is the branch name, adjust according to your situation</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> pull</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> up</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> main</span></span></code></pre></div><ol start="4"><li>How to sync the latest open-source code</li></ol><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> pull</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> origin</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> main</span></span></code></pre></div><div class="tip custom-block"><p class="custom-block-title">Tip</p><p>When syncing the code, conflicts may occur. Just resolve the conflicts.</p></div>',16)),e(c),e(r)])}]]);export{l as __pageData,o as default};
