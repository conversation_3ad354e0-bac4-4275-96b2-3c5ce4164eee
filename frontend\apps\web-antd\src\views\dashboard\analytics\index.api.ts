import { requestClient } from '#/api/request';

enum Api {
  info = '/statistic/data-type/data-size',
  barDatas = '/statistic/data-create-month/data-size',
  pieDatas = '/statistic/data-region/category/data-size',
  ringDatas = '/statistic/data-year/data-size',
}

/**
 * 按行政区划统计接口
 * @param params
 */
export const getBarDatas = (params) => {
  return requestClient.get(Api.barDatas, {
    params,
  });
};


/**
 * 按月统计数据接口
 * @param params
 */
export const getInfo = (params) => {
  return requestClient.get(Api.info, {
    params,
  });
};


/**
 * 年度统计接口
 * @param params
 */
export const getPieDatas = (params) => {
  return requestClient.get(Api.pieDatas, {
    params,
  });
};

/**
 * 年份统计统计接口
 * @param params
 */
export const getRingDatas = (params) => {
  return requestClient.get(Api.ringDatas, {
    params,
  });
};

