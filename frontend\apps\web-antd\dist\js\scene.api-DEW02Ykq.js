var c=Object.defineProperty;var r=Object.getOwnPropertySymbols;var g=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable;var o=(e,t,s)=>t in e?c(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,n=(e,t)=>{for(var s in t||(t={}))g.call(t,s)&&o(e,s,t[s]);if(r)for(var s of r(t))u.call(t,s)&&o(e,s,t[s]);return e};import{r as a}from"./bootstrap-5OPUVRWy.js";const p=e=>a.post("/geo-scene/add",n({},e)),$=(e,t)=>a.put(`/geo-scene/update/${t}`,n({},e)),i=e=>a.put("/geo-scene/update-file",n({},e)),y=e=>a.post("/geo-scene/page",n({},e)),m=(e,t,s)=>a.delete(`/geo-scene/${e}/${t}`,{}).then(()=>{s()}),f=(e,t)=>a.get(`/geo-scene/detail/${e}/${t}`,{}),L=(e,t)=>a.get(`/geo-scene/layers/${e}/${t}`,{}),b=(e,t)=>a.get(`/geo-scene/layer/metadata/${e}/${t}`,{});export{L as a,b,m as d,$ as e,f as g,y as l,p as s,i as u};
