var c=(i,s,a)=>new Promise((l,t)=>{var r=o=>{try{e(a.next(o))}catch(m){t(m)}},n=o=>{try{e(a.throw(o))}catch(m){t(m)}},e=o=>o.done?l(o.value):Promise.resolve(o.value).then(r,n);e((a=a.apply(i,s)).next())});import{u}from"./form-DnT3S1ma.js";import{by as f}from"./bootstrap-DShsrVit.js";import{a4 as d,af as b,ag as h,ah as _,a3 as p,n as C}from"../jse/index-index-BMh_AyeW.js";import{u as N}from"./use-modal-B0smF4x0.js";const V=d({name:"FormModelDemo",__name:"form-modal-demo",setup(i){function s(n){f.info(JSON.stringify(n))}const[a,l]=u({handleSubmit:s,schema:[{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field1",label:"字段1",rules:"required"},{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field2",label:"字段2",rules:"required"},{component:"Select",componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}],placeholder:"请输入"},fieldName:"field3",label:"字段3",rules:"required"}],showDefaultActions:!1}),[t,r]=N({fullscreenButton:!1,onCancel(){r.close()},onConfirm:()=>c(this,null,function*(){yield l.validateAndSubmitForm()}),onOpenChange(n){if(n){const{values:e}=r.getData();e&&l.setValues(e)}},title:"内嵌表单示例"});return(n,e)=>(b(),h(p(t),null,{default:_(()=>[C(p(a))]),_:1}))}});export{V as _};
