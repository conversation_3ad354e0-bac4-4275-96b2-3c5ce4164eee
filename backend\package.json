{"name": "system-manage-backend", "version": "1.0.0", "description": "System Management Backend API", "author": "", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "debug": "nodemon --config nodemon-debug.json", "debug:brk": "node --inspect-brk=0.0.0.0:9229 -r ts-node/register -r tsconfig-paths/register src/main.ts", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- -d src/data-source.ts migration:generate", "migration:create": "npm run typeorm -- -d src/data-source.ts migration:create", "migration:run": "npm run typeorm -- -d src/data-source.ts migration:run", "migration:revert": "npm run typeorm -- -d src/data-source.ts migration:revert", "migration:show": "npm run typeorm -- -d src/data-source.ts migration:show"}, "dependencies": {"@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "mysql2": "^3.14.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "typeorm": "^0.3.25"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/testing": "^11.1.5", "@types/bcryptjs": "^2.4.6", "@types/node": "^24.1.0", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}