<script>
import {
  Button as AButton,
  Select as ASelect,
  SelectOption as ASelectOption,
} from 'ant-design-vue';
import { ColorPicker } from 'vue3-colorpicker';

import core from '../../../../../utils/cesium/core';
import Utils from '../../../../../utils/utils.js';
import ztu from '../../../../../ztu';
import { shapeViewer } from '../index.js';

import 'vue3-colorpicker/style.css';

let targetEntity = null;

export default {
  components: {
    AButton,
    ColorPicker,
    ASelect,
    ASelectOption,
  },
  props: {
    entity: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      type: '',
      fontSize: '',
      fontFamily: '',
      data: {},
      label: {},
      properties: [],
    };
  },
  mounted() {
    // console.log(shapeViewer)
    targetEntity = shapeViewer.selectEntity;
    this.cleanSelect();
    this.type = targetEntity.name;
    const json = core.entity2Json(targetEntity);
    this.data = json[this.type] || {};
    this.label = json.label || {
      fillColor: 'rgba(255,255,255,1)',
      font: '14px sans-serif',
      text: '',
    };
    const idx = this.label.font.indexOf(' ');
    this.fontSize = Number.parseInt(
      this.label.font.slice(0, Math.max(0, idx)) || 14,
    );
    this.fontFamily = this.label.font.slice(idx + 1);
    const properties = json.properties || {};
    const desc = [];
    for (const p in properties) {
      desc.push([p, properties[p]]);
    }
    this.properties = desc;
  },
  methods: {
    cleanSelect() {
      if (ztu.global.GISLayers.isSelected(targetEntity)) {
        ztu.global.GISLayers.select = null;
      }
    },
    save() {
      this.cleanSelect();
      const json = {};
      if (this.type != 'label') json[this.type] = this.data;
      this.label.font = `${this.fontSize}px ${this.fontFamily}`;
      json.label = this.label;
      const desc = {};
      this.properties.forEach((d, index) => {
        desc[d[0]] = d[1];
      });
      json.properties = desc;
      shapeViewer.save(json);
      // core.json2Entity(json,this.entity);

      // let savejson = ztu.global.MyLayerService.drawer.toJson()
      // console.log("...........json",savejson)
      // localStorage.setItem("nowentity",JSON.stringify(savejson))

      this.$parent.$parent.close();
      // ztu.global.MyLayerService.rebuildData()
    },
    exportFile() {
      this.save();
      const data = JSON.stringify(core.entity2Json(targetEntity));
      console.log(targetEntity, data);
      Utils.saveAs(data, `${this.type}.json`, '.json');
      this.$parent.$parent.close();
    },
  },
};
</script>

<template>
  <div class="shape-setting-container">
    <div>
      <div class="form-item">
        <div class="form-item-label" style="display: inline-flex">文本</div>
        <div class="el-form-item-content">
          <input v-model="label.text" style="width: 140px" />
          <ColorPicker
            v-model:pure-color="label.fillColor"
            format="rgb"
            shape="square"
          />
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-label" style="display: inline-flex">字体</div>
        <div class="el-form-item-content">
          <input
            v-model="fontSize"
            min="8"
            step="1"
            style="width: 60px"
            type="number"
          />
          <ASelect v-model:value="fontFamily" style="width: 130px">
            <ASelectOption value="sans-serif">sans-serif</ASelectOption>
            <ASelectOption value="宋体">宋体</ASelectOption>
            <ASelectOption value="楷体">楷体</ASelectOption>
            <ASelectOption value="黑体">黑体</ASelectOption>
          </ASelect>
        </div>
      </div>
      <div v-if="type == 'polyline'" class="form-item">
        <div class="form-item-label">边框</div>
        <div class="el-form-item-content">
          <input
            v-model="data.width"
            min="1"
            step="1"
            style="width: 60px"
            type="number"
          />
          <ColorPicker
            v-model:pure-color="data.material"
            format="rgb"
            shape="square"
          />
        </div>
      </div>
      <div
        v-if="['polygon', 'rectangle', 'ellipse'].includes(type)"
        class="form-item"
      >
        <div class="form-item-label">填充</div>
        <div class="el-form-item-content">
          <ColorPicker
            v-model:pure-color="data.material"
            format="rgb"
            shape="square"
          />
        </div>
      </div>
      <div v-if="['billboard', 'model'].includes(type)" class="form-item">
        <div class="form-item-label">缩放</div>
        <div class="el-form-item-content">
          <input
            v-model="data.scale"
            min="0.01"
            step="0.01"
            style="width: 60px"
            type="number"
          />
        </div>
      </div>
      <div v-if="type == 'billboard'" class="form-item">
        <div class="form-item-label">图标</div>
        <div class="el-form-item-content">
          <div class="markerIconBox"></div>
        </div>
      </div>
      <div v-if="type == 'model'" class="form-item">
        <div class="form-item-label">模型</div>
        <div class="el-form-item-content">
          <div class="markerIconBox"></div>
        </div>
      </div>
      <div class="form-item" style="align-items: start">
        <div class="form-item-label">属性</div>
        <div ref="properties" class="el-form-item-content">
          <div>
            <input
              style="
                width: 70px;
                background-color: cornflowerblue;
                border-color: aliceblue;
              "
              type="text"
              value="属性名称"
            />
            <input
              style="
                width: 90px;
                background-color: cornflowerblue;
                border-color: aliceblue;
              "
              type="text"
              value="属性值"
            />
          </div>
          <div v-for="(p, index) in properties" :key="index">
            <input v-model="p[0]" style="width: 70px" type="text" />
            <input v-model="p[1]" style="width: 90px" type="text" />
            <div class="properties-btn" @click="properties.splice(index, 1)">
              -
            </div>
          </div>
          <div class="properties-btn" @click="properties.push([,])">+</div>
        </div>
      </div>
    </div>
    <div class="footer">
      <AButton type="default" @click="exportFile">导出</AButton>
      <AButton type="primary" @click="save">保存</AButton>
    </div>
  </div>
</template>

<style lang="less">
.shape-setting-container {
  padding: 10px;
  input {
    height: 26px;
    margin: 4px;
    line-height: 22px;
    font-size: 14px;
    background: transparent;
    border: 1px solid #d9d9d9;
    color: #fff;
    padding: 0 5px;
    outline: medium;
    border-radius: 2px;
  }
  textarea {
    margin: 4px;
    width: 200px;
    height: 150px;
    background: transparent;
    border: 1px solid #d9d9d9;
    color: #fff;
    outline: medium;
    border-radius: 2px;
    resize: none;
    padding: 0 5px;
    line-height: 22px;
    font-size: 14px;
  }
  input:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    color: #fff;
    background: transparent;
    height: 26px;
    line-height: 26px;
    border: 1px solid #d9d9d9;
    margin: 4px;
  }
  .ant-select-single .ant-select-selector .ant-select-selection-item,
  .ant-select-single .ant-select-selector .ant-select-selection-placeholder {
    line-height: 26px;
  }
  .vc-color-wrap.transparent {
    margin: 4px;
    height: 26px;
    border: 1px solid #d9d9d9;
    margin-top: -1px;
    border-radius: 2px;
  }
  .form-item {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
  }

  .form-item-label {
    text-align: right;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: #fff;
    line-height: 40px;
    padding: 0 12px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }

  .form-item-content {
    line-height: 40px;
    position: relative;
    font-size: 14px;
    align-items: center;
  }
  .properties-btn {
    width: 26px;
    height: 26px;
    margin: 4px;
    border: 1px solid #d9d9d9;
    color: #fff;
    cursor: pointer;
    text-align: center;
    line-height: 24px;
    display: inline-block;
    margin-right: 0;
  }
  .properties-btn:hover {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  .footer {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    place-items: 10px;
    width: 100%;
    padding: 10px;
    border-top: 1px solid rgba(40, 44, 52, 0.8);

    .ant-btn {
      margin: 0 10px;
      color: rgba(0, 0, 0, 0.85);
      border-color: #d9d9d9;
      background: rgba(255, 255, 255, 0.3);
    }

    .ant-btn:hover,
    .ant-btn:focus,
    .ant-btn:active {
      color: rgba(0, 0, 0, 1);
    }

    .ant-btn-primary:hover,
    .ant-btn-primary:focus,
    .ant-btn-primary:active {
      color: #fff;
      border-color: #1890ff;
      background: #1890ff;
    }
  }
}
</style>
