var s=(d,o,e)=>new Promise((i,a)=>{var p=t=>{try{r(e.next(t))}catch(n){a(n)}},c=t=>{try{r(e.throw(t))}catch(n){a(n)}},r=t=>t.done?i(t.value):Promise.resolve(t.value).then(p,c);r((e=e.apply(d,o)).next())});import{u}from"./vxe-table-a0ubJ4nQ.js";import{B as g}from"./bootstrap-DShsrVit.js";import{g as h}from"./table-eRKxsFfH.js";import{_}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as x,af as w,ag as y,ah as l,a3 as f,n as m,an as C}from"../jse/index-index-BMh_AyeW.js";import"./empty-icon-Crs2MoG4.js";import"./loading-Cqdke3S1.js";import"./form-DnT3S1ma.js";const z=x({__name:"fixed",setup(d){const o={columns:[{fixed:"left",title:"序号",type:"seq",width:50},{field:"category",title:"Category",width:300},{field:"color",title:"Color",width:300},{field:"productName",title:"Product Name",width:300},{field:"price",title:"Price",width:300},{field:"releaseDate",formatter:"formatDateTime",title:"DateTime",width:500},{field:"action",fixed:"right",slots:{default:"action"},title:"操作",width:120}],height:"auto",pagerConfig:{},proxyConfig:{ajax:{query:a=>s(this,[a],function*({page:i}){return yield h({page:i.currentPage,pageSize:i.pageSize})})}},rowConfig:{isHover:!0}},[e]=u({gridOptions:o});return(i,a)=>(w(),y(f(_),{"auto-content-height":""},{default:l(()=>[m(f(e),null,{action:l(()=>[m(f(g),{type:"link"},{default:l(()=>a[0]||(a[0]=[C("编辑")])),_:1})]),_:1})]),_:1}))}});export{z as default};
