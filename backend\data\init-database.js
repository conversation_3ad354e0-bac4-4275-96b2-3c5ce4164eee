const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '123456',
  multipleStatements: true
};

// 解析SQL语句的函数
function parseSqlStatements(sqlContent) {
  const statements = [];
  let currentStatement = '';
  let inString = false;
  let stringChar = '';
  let inComment = false;
  let inMultiLineComment = false;

  for (let i = 0; i < sqlContent.length; i++) {
    const char = sqlContent[i];
    const nextChar = sqlContent[i + 1];

    // 处理多行注释
    if (!inString && char === '/' && nextChar === '*') {
      inMultiLineComment = true;
      i++; // 跳过下一个字符
      continue;
    }

    if (inMultiLineComment && char === '*' && nextChar === '/') {
      inMultiLineComment = false;
      i++; // 跳过下一个字符
      continue;
    }

    if (inMultiLineComment) {
      continue;
    }

    // 处理单行注释
    if (!inString && char === '-' && nextChar === '-') {
      inComment = true;
      continue;
    }

    if (inComment && char === '\n') {
      inComment = false;
      currentStatement += char;
      continue;
    }

    if (inComment) {
      continue;
    }

    // 处理字符串
    if (!inString && (char === '"' || char === "'")) {
      inString = true;
      stringChar = char;
      currentStatement += char;
      continue;
    }

    if (inString && char === stringChar) {
      // 检查是否是转义字符
      if (sqlContent[i - 1] !== '\\') {
        inString = false;
        stringChar = '';
      }
      currentStatement += char;
      continue;
    }

    // 处理分号
    if (!inString && char === ';') {
      const statement = currentStatement.trim();
      if (statement && !statement.startsWith('--') && !statement.startsWith('/*')) {
        statements.push(statement);
      }
      currentStatement = '';
      continue;
    }

    currentStatement += char;
  }

  // 添加最后一个语句（如果有的话）
  const lastStatement = currentStatement.trim();
  if (lastStatement && !lastStatement.startsWith('--') && !lastStatement.startsWith('/*')) {
    statements.push(lastStatement);
  }

  return statements;
}

async function initDatabase() {
  let connection;
  
  try {
    console.log('🚀 开始初始化数据库...');
    
    // 连接到MySQL服务器（不指定数据库）
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 成功连接到MySQL服务器');
    
    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'database-init.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    console.log('✅ 成功读取数据库初始化脚本');
    
    // 执行SQL脚本
    console.log('📝 开始执行数据库初始化脚本...');

    try {
      // 使用multipleStatements执行整个SQL文件
      await connection.query(sqlContent);
      console.log('✅ 数据库初始化脚本执行完成');
    } catch (error) {
      // 如果multipleStatements失败，尝试逐条执行
      console.log('⚠️  批量执行失败，尝试逐条执行SQL语句...');

      const sqlStatements = parseSqlStatements(sqlContent);

      for (let i = 0; i < sqlStatements.length; i++) {
        const statement = sqlStatements[i].trim();
        if (statement) {
          try {
            await connection.execute(statement);

            // 显示执行进度
            if (statement.toUpperCase().includes('CREATE DATABASE')) {
              console.log('  ✅ 数据库创建成功');
            } else if (statement.toUpperCase().includes('CREATE TABLE')) {
              const tableName = statement.match(/CREATE TABLE\s+(\w+)/i)?.[1];
              console.log(`  ✅ 创建表: ${tableName}`);
            } else if (statement.toUpperCase().includes('INSERT INTO')) {
              const tableName = statement.match(/INSERT INTO\s+(\w+)/i)?.[1];
              if (tableName) {
                console.log(`  ✅ 插入数据到: ${tableName}`);
              }
            }
          } catch (execError) {
            // 忽略一些可能的警告
            if (execError.message.includes('database exists') ||
                execError.message.includes('table exists') ||
                execError.code === 'ER_DB_CREATE_EXISTS' ||
                execError.code === 'ER_TABLE_EXISTS_ERROR') {
              console.log(`  ⚠️  ${execError.message}`);
            } else {
              console.error(`❌ 执行SQL语句失败: ${statement.substring(0, 100)}...`);
              console.error(`错误信息: ${execError.message}`);
              throw execError;
            }
          }
        }
      }
      console.log('✅ 数据库初始化脚本执行完成');
    }

    console.log('✅ 数据库初始化脚本执行完成');
    
    // 验证数据库和表是否创建成功
    console.log('🔍 验证数据库初始化结果...');
    
    // 切换到目标数据库
    await connection.execute('USE system_manage');
    
    // 检查表是否创建成功
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('📋 已创建的表：');
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });
    
    // 检查初始数据
    const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [roles] = await connection.execute('SELECT COUNT(*) as count FROM roles');
    const [applications] = await connection.execute('SELECT COUNT(*) as count FROM applications');
    
    console.log('📊 初始数据统计：');
    console.log(`  - 用户数量: ${users[0].count}`);
    console.log(`  - 角色数量: ${roles[0].count}`);
    console.log(`  - 应用数量: ${applications[0].count}`);
    
    // 显示默认管理员账号信息
    const [adminUser] = await connection.execute(
      'SELECT username, real_name FROM users WHERE username = "admin"'
    );
    
    if (adminUser.length > 0) {
      console.log('👤 默认管理员账号信息：');
      console.log(`  - 用户名: ${adminUser[0].username}`);
      console.log(`  - 真实姓名: ${adminUser[0].real_name}`);
      console.log(`  - 默认密码: admin123`);
    }
    
    console.log('🎉 数据库初始化完成！');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败：', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 请检查数据库连接配置（用户名、密码）');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 请确保MySQL服务已启动');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('💡 数据库不存在，将尝试创建');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 检查是否提供了数据库配置
function checkConfig() {
  console.log('🔧 数据库配置：');
  console.log(`  - 主机: ${dbConfig.host}`);
  console.log(`  - 端口: ${dbConfig.port}`);
  console.log(`  - 用户: ${dbConfig.user}`);
  console.log(`  - 密码: ${dbConfig.password ? '***' : '(未设置)'}`);
  console.log('');
  
  if (!dbConfig.password) {
    console.warn('⚠️  警告: 未设置数据库密码，如果MySQL需要密码请设置 DB_PASSWORD 环境变量');
    console.log('');
  }
}

// 主函数
async function main() {
  console.log('='.repeat(50));
  console.log('    系统管理数据库初始化工具');
  console.log('='.repeat(50));
  console.log('');
  
  checkConfig();
  await initDatabase();
}

// 运行初始化
main().catch(console.error);
