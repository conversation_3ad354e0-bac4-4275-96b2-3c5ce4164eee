{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/number-input/src/util.js"], "sourcesContent": ["import XEUtils from 'xe-utils';\nexport function handleNumber(val) {\n    return XEUtils.isString(val) ? val.replace(/[^0-9e.-]/g, '') : val;\n}\nexport function toFloatValueFixed(inputValue, digitsValue) {\n    if (/^-/.test('' + inputValue)) {\n        return XEUtils.toFixed(XEUtils.ceil(inputValue, digitsValue), digitsValue);\n    }\n    return XEUtils.toFixed(XEUtils.floor(inputValue, digitsValue), digitsValue);\n}\n"], "mappings": ";;;;;;;;AAAA,sBAAoB;AACb,SAAS,aAAa,KAAK;AAC9B,SAAO,gBAAAA,QAAQ,SAAS,GAAG,IAAI,IAAI,QAAQ,cAAc,EAAE,IAAI;AACnE;AACO,SAAS,kBAAkB,YAAY,aAAa;AACvD,MAAI,KAAK,KAAK,KAAK,UAAU,GAAG;AAC5B,WAAO,gBAAAA,QAAQ,QAAQ,gBAAAA,QAAQ,KAAK,YAAY,WAAW,GAAG,WAAW;AAAA,EAC7E;AACA,SAAO,gBAAAA,QAAQ,QAAQ,gBAAAA,QAAQ,MAAM,YAAY,WAAW,GAAG,WAAW;AAC9E;", "names": ["XEUtils"]}