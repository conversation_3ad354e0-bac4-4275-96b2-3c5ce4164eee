import{by as o}from"./bootstrap-DShsrVit.js";import{a4 as t,af as s,ag as r,ah as i,a3 as p,an as l}from"../jse/index-index-BMh_AyeW.js";import{u as m}from"./use-modal-B0smF4x0.js";const C=t({__name:"base-demo",setup(f){const[n,a]=m({onCancel(){a.close()},onClosed(){o.info("onClosed：关闭动画结束")},onConfirm(){o.info("onConfirm")},onOpened(){o.info("onOpened：打开动画结束")}});return(d,e)=>(s(),r(p(n),{class:"w-[600px]",title:"基础弹窗示例","title-tooltip":"标题提示内容"},{default:i(()=>e[0]||(e[0]=[l(" base demo ")])),_:1}))}});export{C as _};
