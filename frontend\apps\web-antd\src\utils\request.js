import axios from 'axios'

import {baseURL} from "../config"
import Box from '../components/JS/box.js'
import {useRouter} from "vue-router"
import {capp} from '../utils/app'
// let loadingInstance = {
//     open() {
//         store.dispatch('app/setRequestLoading', true);
//     },
//     close() {
//         store.dispatch('app/setRequestLoading', false);
//     }
// }

/**
 * @description 处理code异常
 * @param {*} code
 * @param {*} msg
 */
// const handleCode = (code, msg) => {
//     const store = useStore();
//     switch (code) {
//         case 400:
//             message.info(msg)
//             break;
//         case 401:
//             router.push("/passport/login");
//             message.error('登录失效,请重新登录')
//             store.dispatch("user/logout");
//             break
//         // case 403:
//         //   break
//         case 500:
//             message.error(msg || '服务端错误')
//             break
//         default:
//             message.error(msg || `后端接口${code}异常`)
//             break
//     }
// }

/**
 * @description axios初始化
 */
const instance = axios.create({
    // baseURL: baseURL,
    timeout: 60000,
    headers: {
        'Content-Type': 'application/json;charset=UTF-8',
    },
})

const  ErrorCodeEnum = {
  
    // 40001: '用户名或者密码错误!',
    // 40002: '该用户不唯一,请联系管理员!',
    // 40003: '该账号已被禁用,请联系管理员！',
    '40004': '无效的令牌，请重新登陆！',
    '40005': '登陆过期，请重新登陆！',
    '40006': '登陆过期，请重新登陆！',//令牌获取失败
    '40007': '用户未登陆，请前往登陆！',
    '40008': '登陆过期，请重新登陆！',//令牌检查错误
    '40009': '用户密码已更新，请重新登录！',//令牌检查错误
    
}

/**
 * @description axios请求拦截器
 */
instance.interceptors.request.use(
    (config) => {
        let token = localStorage.getItem('user/token');
        if(token){
            
            config.headers['token'] = token;
        }
        
        return config
    },
    (error) => {
        return Promise.reject(error)
    }
)

/**
 * @description axios响应拦截器
 */
instance.interceptors.response.use(
    (response) => {
        // if (loadingInstance) loadingInstance.close()
		console.log(response)
        const {data, config} = response
        const {code, message} = data
        // 是否操作正常
        if (code==200) {
            return data.data;
        }
        // else  if(ErrorCodeEnum[""+code]) {
        //    // useRouter().replace({name:'login'})
        //    capp.router.replace({name:'login'});
        // }
         else {
            Box.info('Error',message || 'Error');
            return Promise.reject(
                'vue-admin-beautiful请求异常拦截:' +
                JSON.stringify({url: config.url, code, message}) || 'Error'
            )
        }
    },
    (error) => {
        // if (loadingInstance) loadingInstance.close()
        let {response, message: msg = ""} = error
        if (response && response.data) {
            const {status, data} = response
            //handleCode(status, data.msg || msg)
            return Promise.reject(error)
        } else {
            if (msg === 'Network Error') {
                msg = '后端接口连接异常'
            } else if (msg.includes('timeout')) {
                msg = '后端接口请求超时'
            } else if (msg.includes('Request failed with status code')) {
                const code = msg.substr(msg.length - 3)
                msg = '后端接口' + code + '异常'
            }
            Box.info('Error',msg);
            //message.error(msg)
            // message.error(msg || `后端接口未知异常`)
            return Promise.reject(error)
        }
    }
)

export default instance
