import * as Cesium from 'cesium';
import core from '../core';
import Drawer from './Drawer';

export default class Fly {
  constructor(options) {
    this.setOptions(options);
    console.log(options);
    this._viewer = options.viewer;
    this._callback = options.callback;
    this._base = new core.Base(this._viewer);
    this.flying = false;
    this.entity = null;
    this.paths = [];
    this.position = null; //new Cesium.Cartesian3();
    this.entityHeading = 0;
    this.drawer = new Drawer(this._viewer);
  }
  setOptions(options) {
    this.options = {
      ...{
        speed: 50, //速度，单位米每秒
        loop: false, //循环漫游
        jitter: false, //随机跳动
        firstPersonPerspective: false, //第一人视角
        follow: true, //跟随
        distance: 200, //视距
        pitch: 30, //俯视角（degree）,平视为0度
        model: {
          uri: '/assets/models/CesiumMilkTruck.glb', //"/assets/models/Cesium_Man.glb",
          color: Cesium.Color.GAINSBORO,
          colorBlendAmount: 0.4,
          scale: 1,
        },
        displayModel: false,
        displayPath: false,
      },
      ...options,
    };
  }
  start(data) {
    this.flying && this.stop();
    if (data) {
      this.data = data;
      let length = this.data.length;
      if (!length) {
        if (this._callback) {
          setTimeout(() => {
            this._callback('empty');
          });
        }
        return;
      }
      this._viewer.scene.globe.depthTestAgainstTerrain = true;
      this.entity = this._viewer.entities.add({
        position: new Cesium.CallbackProperty((time, result) => {
          return this._viewer.scene.clampToHeight(this.position, [this.entity]);
        }, false),
        orientation: new Cesium.CallbackProperty((time, result) => {
          return Cesium.Transforms.headingPitchRollQuaternion(
            this.position,
            new Cesium.HeadingPitchRoll(this.entityHeading - Math.PI / 2, 0, 0),
          );
        }, false),
        model: this.options.model,
      });
      this.entity.viewFrom = new Cesium.Cartesian3(
        0,
        this.options.distance * Math.cos((this.options.pitch / 180) * Math.PI),
        this.options.distance * Math.sin((this.options.pitch / 180) * Math.PI),
      );
      this._viewer.trackedEntity = this.entity;
      this.entity.model.scale = this.options.displayModel
        ? this.options.model.scale
        : 0;

      let mapcenter =
        core.getMapCenter(this._viewer) || core.getDefaultViewCenter();
      this.position = Cesium.Cartesian3.fromDegrees(
        mapcenter.lng,
        mapcenter.lat,
        this.options.distance,
      );

      let loop = async (index = 0) => {
        if (!this.flying) return;
        if (index >= length) {
          if (this.options.loop && index > 0) {
            index = 0;
          } else {
            return this.stop();
          }
        }
        let item = null;
        if (this.options.jitter) {
          let i = Math.floor(Math.random() * length);
          item = this.data[i];
        } else {
          item = this.data[index];
        }
        if (item.point) {
          if (item.point.x == undefined) {
            item.point = core.transformWGS84ToCartesian(item.point);
          }
          this.position = new Cesium.Cartesian3(
            item.point.x,
            item.point.y,
            item.point.z,
          );
          await new Promise((resolve) => setTimeout(resolve, 1000));
          await this.flyForPoint(item.point);
          await loop(index + 1);
        } else if (item.polyline) {
          if (!item.polyline.length) return loop(index + 1);
          if (item.polyline[0].x == undefined) {
            item.polyline = core.transformWGS84ArrayToCartesianArray(
              item.polyline,
            );
          }
          this.position = new Cesium.Cartesian3(
            item.polyline[0].x,
            item.polyline[0].y,
            item.polyline[0].z,
          );
          await new Promise((resolve) => setTimeout(resolve, 1000));
          await this.flyForPolyline(item.polyline);
          await loop(index + 1);
        } else if (item.polygon) {
          if (!item.polygon.length) return loop(index + 1);
          if (item.polygon[0].x == undefined) {
            item.polygon = core.transformWGS84ArrayToCartesianArray(
              item.polygon,
            );
          }
          this.position = new Cesium.Cartesian3(
            item.polygon[0].x,
            item.polygon[0].y,
            item.polygon[0].z,
          );
          await new Promise((resolve) => setTimeout(resolve, 1000));
          await this.forForPolygon(item.polygon);
          await loop(index + 1);
        }
      };
      this.flying = true;
      loop();
    }
  }
  stop() {
    this.flying = false;
    if (this.entity) {
      this._viewer.trackedEntity = undefined;
      this._viewer.entities.remove(this.entity);
      this.entity = null;
    }
    this.drawer.clear();
    let boolTerrain =
      this._viewer.terrainProvider instanceof Cesium.EllipsoidTerrainProvider;
    this._viewer.scene.globe.depthTestAgainstTerrain = boolTerrain;
    if (this._callback) this._callback('stop');
  }
  flyForPoint(point) {
    return new Promise((resolve, reject) => {
      if (point) {
        let that = this;
        let viewer = this._viewer;
        let position = point;
        this.position = position;
        // 相机看点的角度，如果大于0那么则是从地底往上看，所以要为负值，这里取-30度
        let pitch = Cesium.Math.toRadians(this.options.pitch * -1); //Cesium.Math.toRadians(-30);
        // 给定相机距离点多少距离飞行，这里取值为5000m
        let distance = this.options.distance;
        // 给定飞行一周所需时间，比如10s, 那么每秒转动度数
        let l = Math.abs(distance * Math.cos(pitch * -1));
        let angle = (this.options.speed / (Math.PI * 2 * l)) * 360; //360 / 30;
        let startTime = Cesium.JulianDate.fromDate(new Date());
        let stopTime = Cesium.JulianDate.addSeconds(
          startTime,
          Math.ceil(360 / angle),
          new Cesium.JulianDate(),
        );
        viewer.clock.startTime = startTime.clone(); // 开始时间
        viewer.clock.currentTime = startTime.clone(); // 当前时间
        viewer.clock.stopTime = stopTime.clone();
        viewer.clock.clockRange = Cesium.ClockRange.CLAMPED; // 行为方式
        viewer.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK; // 时钟设置为当前系统时间; 忽略所有其他设置。

        let Exection = function TimeExecution() {
          let heading = viewer.camera.heading;
          // 当前已经过去的时间，单位s
          let delTime = Cesium.JulianDate.secondsDifference(
            viewer.clock.currentTime,
            viewer.clock.startTime,
          );
          let headingChangeValue = Cesium.Math.toRadians(delTime * angle);
          viewer.clock.startTime = viewer.clock.currentTime.clone();

          that.entityHeading = heading + headingChangeValue;

          viewer.camera.rotateLeft(headingChangeValue);
          if (
            Cesium.JulianDate.compare(
              viewer.clock.currentTime,
              viewer.clock.stopTime,
            ) >= 0 ||
            !that.flying
          ) {
            viewer.clock.onTick.removeEventListener(Exection);
            resolve();
          }
        };
        viewer.clock.onTick.addEventListener(Exection);
      }
    });
  }
  rorate(angle) {
    return new Promise((resolve, reject) => {
      let that = this;
      let viewer = this._viewer;
      let startTime = Cesium.JulianDate.fromDate(new Date());
      if (angle > Math.PI) angle = angle - 2 * Math.PI;
      let stopTime = Cesium.JulianDate.addSeconds(
        startTime,
        Math.abs(angle / Math.PI),
        new Cesium.JulianDate(),
      );
      viewer.clock.startTime = startTime.clone(); // 开始时间
      viewer.clock.currentTime = startTime.clone(); // 当前时间
      viewer.clock.stopTime = stopTime.clone();
      viewer.clock.clockRange = Cesium.ClockRange.CLAMPED; // 行为方式
      viewer.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK; // 时钟设置为当前系统时间; 忽略所有其他设置。

      let Exection = function TimeExecution() {
        let heading = viewer.camera.heading;
        // 当前已经过去的时间，单位s
        let delTime = Cesium.JulianDate.secondsDifference(
          viewer.clock.currentTime,
          viewer.clock.startTime,
        );
        let delTime2 = Cesium.JulianDate.secondsDifference(
          viewer.clock.stopTime,
          viewer.clock.startTime,
        );
        delTime = Math.min(delTime, delTime2);
        viewer.clock.startTime = viewer.clock.currentTime;
        if (angle > 0) viewer.camera.rotateRight(delTime * Math.PI);
        else viewer.camera.rotateLeft(delTime * Math.PI);
        if (
          Cesium.JulianDate.compare(
            viewer.clock.currentTime,
            viewer.clock.stopTime,
          ) >= 0 ||
          !that.flying
        ) {
          viewer.clock.onTick.removeEventListener(Exection);
          resolve();
        }
      };
      viewer.clock.onTick.addEventListener(Exection);
    });
  }
  flyForTwoPoint(start, end) {
    return new Promise(async (resolve, reject) => {
      if (start && end) {
        let that = this;
        let viewer = this._viewer;
        let geodesic = new Cesium.EllipsoidGeodesic();
        let pitch = Cesium.Math.toRadians(this.options.pitch * -1);
        if (!start.x) {
          start = core.transformWGS84ToCartesian(start);
          end = core.transformWGS84ToCartesian(end);
        }
        let startCartographic = Cesium.Cartographic.fromCartesian(start);
        let endCartographic = Cesium.Cartographic.fromCartesian(end);
        geodesic.setEndPoints(startCartographic, endCartographic);
        let seconds =
          Cesium.Cartesian3.distance(start, end) / this.options.speed;
        this.entityHeading = (() => {
          //以a点为原点建立局部坐标系（东方向为x轴,北方向为y轴,垂直于地面为z轴），得到一个局部坐标到世界坐标转换的变换矩阵
          var localToWorld_Matrix =
            Cesium.Transforms.eastNorthUpToFixedFrame(start);
          //求世界坐标到局部坐标的变换矩阵
          var worldToLocal_Matrix = Cesium.Matrix4.inverse(
            localToWorld_Matrix,
            new Cesium.Matrix4(),
          );
          //a点在局部坐标的位置，其实就是局部坐标原点
          var localPosition_A = Cesium.Matrix4.multiplyByPoint(
            worldToLocal_Matrix,
            start,
            new Cesium.Cartesian3(),
          );
          //B点在以A点为原点的局部的坐标位置
          var localPosition_B = Cesium.Matrix4.multiplyByPoint(
            worldToLocal_Matrix,
            end,
            new Cesium.Cartesian3(),
          );
          //弧度
          console.log(localPosition_B, localPosition_A);
          return Math.atan2(
            localPosition_B.x - localPosition_A.x,
            localPosition_B.y - localPosition_A.y,
          );
        })();
        this.position = start;

        // if (that.options.firstPersonPerspective) {
        // 	viewer.camera.rotateRight(viewer.camera.heading - this.entityHeading);
        // }
        if (that.options.firstPersonPerspective) {
          //viewer.camera.rotateRight(viewer.camera.heading - this.entityHeading);
          await this.rorate(viewer.camera.heading - this.entityHeading);
        }
        let startTime = Cesium.JulianDate.fromDate(new Date());
        let stopTime = Cesium.JulianDate.addSeconds(
          startTime,
          seconds,
          new Cesium.JulianDate(),
        );
        viewer.clock.startTime = startTime.clone(); // 开始时间
        viewer.clock.currentTime = startTime.clone(); // 当前时间
        viewer.clock.stopTime = stopTime.clone();
        viewer.clock.clockRange = Cesium.ClockRange.CLAMPED; // 行为方式
        viewer.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK; // 时钟设置为当前系统时间; 忽略所有其他设置。
        let Exection = function TimeExecution() {
          let heading = viewer.camera.heading;
          // 当前已经过去的时间，单位s
          let delTime = Cesium.JulianDate.secondsDifference(
            viewer.clock.currentTime,
            viewer.clock.startTime,
          );
          let delTime2 = Cesium.JulianDate.secondsDifference(
            viewer.clock.stopTime,
            viewer.clock.startTime,
          );
          delTime = Math.min(delTime, delTime2);
          let midpointCartographic = geodesic.interpolateUsingSurfaceDistance(
            delTime * that.options.speed,
          );
          let position = Cesium.Cartesian3.fromRadians(
            midpointCartographic.longitude,
            midpointCartographic.latitude,
            midpointCartographic.height,
          );
          // let pc = viewer.camera.positionCartographic;
          // let l = Cesium.Cartesian3.distance(that.position, Cesium.Cartesian3.fromRadians(pc
          // 	.longitude, pc.latitude, pc.height));
          that.position = position;
          if (
            Cesium.JulianDate.compare(
              viewer.clock.currentTime,
              viewer.clock.stopTime,
            ) >= 0 ||
            !that.flying
          ) {
            viewer.clock.onTick.removeEventListener(Exection);
            resolve();
          }
        };
        viewer.clock.onTick.addEventListener(Exection);
      }
    });
  }
  flyForPolyline(polyline) {
    return new Promise((resolve, reject) => {
      if (polyline && polyline.length) {
        let that = this;
        let viewer = this._viewer;
        let positions = polyline;
        let line = null;
        if (positions[0].x == undefined) {
          positions = positions.map((a) => {
            return core.transformWGS84ToCartesian(a);
          });
        }
        if (this.options.displayPath) {
          line = this.drawer.drawLine(
            positions,
            core.getPolylineCenter(positions),
            {
              material: Cesium.Color.RED,
            },
          );
        }
        let geodesic = new Cesium.EllipsoidGeodesic();
        let loop = (index = 0) => {
          if (index >= positions.length - 1) {
            this.drawer.remove(line);
            return resolve();
          }
          if (!that.flying) {
            this.drawer.remove(line);
            return resolve();
          }
          let start = positions[index];
          let end = positions[index + 1];

          this.flyForTwoPoint(start, end).then(() => {
            loop(index + 1);
          });
        };
        loop();
      }
    });
  }
  forForPolygon(positions) {
    return this.flyForPolyline(positions);
  }
}
