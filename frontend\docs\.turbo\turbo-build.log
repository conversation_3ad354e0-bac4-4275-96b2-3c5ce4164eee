
> @vben/docs@5.4.6 build E:\work\git\gisbase-datamanage-fed\docs
> vitepress build


  [32m[1mvitepress[22m v1.5.0[39m

- building client + server bundles...
i [36m@nolebase/vitepress-plugin-git-changelog[39m[90m:[39m Prepare to gather git logs...
i [36m@nolebase/vitepress-plugin-git-changelog[39m[90m:[39m Done. [90m(3932ms)[39m
i [36m@nolebase/vitepress-plugin-git-changelog[39m[90m:[39m Prepare to gather git logs...
ZIP file created: E:\work\git\gisbase-datamanage-fed\docs\.vitepress\dist.zip (22 total bytes)
Folder has been zipped to: E:\work\git\gisbase-datamanage-fed\docs\.vitepress\dist.zip
[32m✓[0m building client + server bundles...
ZIP file created: E:\work\git\gisbase-datamanage-fed\docs\.vitepress\dist.zip (22 total bytes)
Folder has been zipped to: E:\work\git\gisbase-datamanage-fed\docs\.vitepress\dist.zip
- rendering pages...
[32m✓[0m rendering pages...
build complete in 494.50s.
