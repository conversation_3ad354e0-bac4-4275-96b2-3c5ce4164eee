import{ao as a,k as e,aP as s,l as t,ay as i,j as n}from"./chunks/framework.C8U7mBUf.js";const h=JSON.parse('{"title":"Changeset","description":"","frontmatter":{},"headers":[],"relativePath":"guide/project/changeset.md","filePath":"guide/project/changeset.md"}');const r=a({name:"guide/project/changeset.md"},[["render",function(a,h,r,l,o,p){const c=i("NolebaseGitContributors"),d=i("NolebaseGitChangelog");return n(),e("div",null,[h[0]||(h[0]=s('<h1 id="changeset" tabindex="-1">Changeset <a class="header-anchor" href="#changeset" aria-label="Permalink to &quot;Changeset&quot;">​</a></h1><p>项目内置了 <a href="https://github.com/changesets/changesets" target="_blank" rel="noreferrer">changeset</a> 作为版本管理工具。Changeset 是一个版本管理工具，它可以帮助我们更好的管理版本，生成 changelog，以及自动发布。</p><p>详细使用方式可查看官方文档，这里不再阐述。如果你不需要它，可以直接忽略。</p><h2 id="命令行" tabindex="-1">命令行 <a class="header-anchor" href="#命令行" aria-label="Permalink to &quot;命令行&quot;">​</a></h2><p>changeset 命令在项目中已经内置：</p><h3 id="交互式填写变更集" tabindex="-1">交互式填写变更集 <a class="header-anchor" href="#交互式填写变更集" aria-label="Permalink to &quot;交互式填写变更集&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> run</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> changeset</span></span></code></pre></div><h3 id="统一提升版本号" tabindex="-1">统一提升版本号 <a class="header-anchor" href="#统一提升版本号" aria-label="Permalink to &quot;统一提升版本号&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> run</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> version</span></span></code></pre></div>',9)),t(c),t(d)])}]]);export{h as __pageData,r as default};
