var K=Object.defineProperty;var N=Object.getOwnPropertySymbols;var Q=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable;var U=(l,a,e)=>a in l?K(l,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):l[a]=e,E=(l,a)=>{for(var e in a||(a={}))Q.call(a,e)&&U(l,e,a[e]);if(N)for(var e of N(a))W.call(a,e)&&U(l,e,a[e]);return l};var v=(l,a,e)=>new Promise((C,B)=>{var x=r=>{try{i(e.next(r))}catch(b){B(b)}},n=r=>{try{i(e.throw(r))}catch(b){B(b)}},i=r=>r.done?C(r.value):Promise.resolve(r.value).then(x,n);i((e=e.apply(l,a)).next())});import{v as X,a as Y}from"./bootstrap-5OPUVRWy.js";import{u as A}from"./form-DdFfsSWf.js";import{f as Z}from"./entity.data-u4HDUExc.js";import{e as ee,s as te,u as se}from"./entity.api-CPgpBrqe.js";import{s as M,a as D}from"./toast-CQjPPeQ1.js";import{u as ae,a as oe,s as le}from"./fileUpload-DI0dJ9zY.js";import{u as ie}from"./scene.data-BMXeOdST.js";import{s as re}from"./alert-DJKWbMfG.js";import{d as ne,r as m,c as de,k as w,j as k,b as h,q as j,f,e as I,a as P,H as ue,v as ce,I as fe}from"../jse/index-index-DyHD_jbN.js";import{u as pe}from"./use-modal-uChFuhJy.js";import"./loading-DzjUKA94.js";const me={style:{"padding-left":"20px","padding-right":"20px","padding-top":"20px"}},be=["directory","webkitdirectory"],ge={key:0,for:"fileInput",onclick:"triggerFileInput",class:"custom-label bg-gray-200 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400"},ye={key:1,for:"fileInput",onclick:"triggerFileInput",class:"custom-label bg-primary-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400"},ve={key:2,style:{width:"900px"}},he=ne({__name:"EntityCreateModal",emits:["register","success"],setup(l,{emit:a}){const e=a;let C=null;const B=()=>{F.value=1,c.value=!1,d.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}}),y.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}})},[x,n]=pe({closeOnClickModal:!1,onCancel(){n.close()},onClosed(){B()},onBeforeClose(){return c.value?(re("提示","关闭窗口将停止上传，是否确定关闭窗口？",()=>{le(),c.value=!1,n.close(),e("success")}),!1):!0},onConfirm(){},onOpenChange(t){if(t){d.resetForm();const s=n.getData(),o=n.useStore();console.log(s),i.value=o.value&&o.value.update,r.value=o.value&&o.value.upload,b=o.value&&o.value.category,g=o.value&&o.value.row&&o.value.row.dataType;let p={};i.value&&(p=E({},o.value.row)),C=p.id,p.update=i.value,p.upload=r.value,d.setValues(p)}}});n.useStore(),m({id:"",datasetName:""});let i=m(!1),r=m(!1),b="",g="";const[z,d]=A({commonConfig:{componentProps:{class:"w-full"},labelClass:"w-2/8"},handleReset:T,handleSubmit:L,layout:"horizontal",schema:Z,wrapperClass:"grid-cols-1",submitButtonOptions:{content:"下一步"}}),[R,y]=A({commonConfig:{componentProps:{class:"w-full"},labelClass:"w-2/8"},handleSubmit:H,handleReset:T,layout:"horizontal",schema:ie,wrapperClass:"grid-cols-1",resetButtonOptions:{show:!1}});let u=null;m([]);const O=m("0"),c=m(!1),F=m(1);let V="";const q=de(()=>w(i)?"编辑":"新增");function T(t){return v(this,null,function*(){let s=t.id;yield d.resetForm(),yield d.setFieldValue("id",s),yield d.setFieldValue("dataType",g)})}function H(t){return v(this,null,function*(){if(c.value=!0,y.setState({commonConfig:{disabled:!0},submitButtonOptions:{disabled:!0},resetButtonOptions:{disabled:!0}}),u.files==null||u.files.length==0){S&&S();return}let s=p=>v(null,null,function*(){try{M("文件上传成功，解析文件中..."),yield se({id:V,fileId:p,dataType:t.dataType}),S&&S()}catch(we){_&&_()}}),o=t.fileFormat;O.value==="0"?yield ae(u,s,g,o,_):yield oe(u,s,g)})}function L(t){return v(this,null,function*(){try{t.category=b,g=t.dataType,d.setState({commonConfig:{disabled:!0},submitButtonOptions:{disabled:!0},resetButtonOptions:{disabled:!0}}),M("操作处理中...");try{let s=i.value?yield ee(t,C):yield te(t);D("创建数据集完成，请上传数据!"),G(s)}catch(s){$()}}finally{}})}const S=()=>{c.value=!1,y.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}}),n.close(),D("操作成功！"),e("success")},_=()=>{c.value=!1,y.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}}),n.close(),e("success")},$=()=>{d.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}})},G=t=>v(null,null,function*(){V=t,F.value=2,d.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}}),e("success"),yield y.setFieldValue("dataType",g)}),J=t=>{console.log(t),u=t.target;const s=document.getElementById("fileName");s.textContent=u.files.length>0?"已选"+u.files.length+"个文件":"没有选择文件",y.setFieldValue("fileUpload",u.files)};return(t,s)=>(h(),k(w(x),fe(t.$attrs,{footer:!1,title:q.value,class:"w-[800px]","destroy-on-close":!0,maskClosable:!1}),{default:j(()=>[f("div",me,[F.value===1?(h(),k(w(z),{key:0})):I("",!0),F.value===2?(h(),k(w(R),{key:1},{fileUpload:j(o=>[ue(f("input",{class:"hidden",id:"fileInput",type:"file",directory:O.value==="0",webkitdirectory:O.value==="0",onChange:J},null,40,be),[[X,!w(i)]]),c.value?(h(),P("label",ge," 选择文件 ")):(h(),P("label",ye," 选择文件 ")),s[0]||(s[0]=f("span",{id:"fileName",class:"ml-4"},"没有选择文件",-1))]),_:1})):I("",!0),c.value?(h(),P("view",ve,s[1]||(s[1]=[f("div",{id:"folderProgressArea",class:"folderProgressArea"},[ce(" 文件夹总进度："),f("div",{id:"folderProgress",class:"folderProgress"},"0%")],-1),f("div",{style:{display:"flex","flex-direction":"column","justify-content":"flex-start","align-items":"flex-start"}},[f("div",{style:{"margin-bottom":"5px"}},"当前文件进度："),f("div",{id:"fileProgress",class:"fileProgress bg-primary text-white"})],-1)]))):I("",!0)])]),_:1},16,["title"]))}}),Ne=Y(he,[["__scopeId","data-v-2874f8c2"]]);export{Ne as default};
