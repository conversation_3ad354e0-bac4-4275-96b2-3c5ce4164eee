import{ao as a,k as e,z as t,I as o,l as n,ay as r,j as i}from"./chunks/framework.C8U7mBUf.js";const s=JSON.parse('{"title":"Layout","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/in-depth/layout.md","filePath":"en/guide/in-depth/layout.md"}');const l=a({name:"en/guide/in-depth/layout.md"},[["render",function(a,s,l,d,u,h){const m=r("NolebaseGitContributors"),y=r("NolebaseGitChangelog");return i(),e("div",null,[s[0]||(s[0]=t("h1",{id:"layout",tabindex:"-1"},[o("Layout "),t("a",{class:"header-anchor",href:"#layout","aria-label":'Permalink to "Layout"'},"​")],-1)),n(m),n(y)])}]]);export{s as __pageData,l as default};
