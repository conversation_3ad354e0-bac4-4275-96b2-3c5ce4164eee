import{ao as a,k as i,aP as t,l as e,ay as s,j as n}from"./chunks/framework.C8U7mBUf.js";const l=JSON.parse('{"title":"Tailwind CSS","description":"","frontmatter":{},"headers":[],"relativePath":"guide/project/tailwindcss.md","filePath":"guide/project/tailwindcss.md"}');const d=a({name:"guide/project/tailwindcss.md"},[["render",function(a,l,d,o,c,r){const w=s("NolebaseGitContributors"),h=s("NolebaseGitChangelog");return n(),i("div",null,[l[0]||(l[0]=t('<h1 id="tailwind-css" tabindex="-1">Tailwind CSS <a class="header-anchor" href="#tailwind-css" aria-label="Permalink to &quot;Tailwind CSS&quot;">​</a></h1><p><a href="https://tailwindcss.com/" target="_blank" rel="noreferrer">Tailwind CSS</a> 是一个实用性优先的CSS框架，用于快速构建自定义设计。</p><h2 id="配置" tabindex="-1">配置 <a class="header-anchor" href="#配置" aria-label="Permalink to &quot;配置&quot;">​</a></h2><p>项目的配置文件位于 <code>internal/tailwind-config</code> 下，你可以在这里修改 Tailwind CSS 的配置。</p><div class="tip custom-block"><p class="custom-block-title">包使用 tailwindcss 的限制</p><p>当前只有对应的包下面存在 <code>tailwind.config.mjs</code> 文件才会启用 tailwindcss 的编译，否则不会启用 tailwindcss。如果你是纯粹的 SDK 包，不需要使用 tailwindcss，可以不用创建 <code>tailwind.config.mjs</code> 文件。</p></div>',5)),e(w),e(h)])}]]);export{l as __pageData,d as default};
