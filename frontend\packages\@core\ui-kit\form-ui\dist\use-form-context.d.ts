import type { FormActions, VbenFormProps } from './types';
import { type ComputedRef } from 'vue';
export declare const injectFormProps: <T extends [VbenFormProps<import("./types").BaseFormComponentType> | ComputedRef<VbenFormProps<import("./types").BaseFormComponentType>>, FormActions] | null | undefined = [VbenFormProps<import("./types").BaseFormComponentType> | ComputedRef<VbenFormProps<import("./types").BaseFormComponentType>>, FormActions]>(fallback?: T | undefined) => T extends null ? [VbenFormProps<import("./types").BaseFormComponentType> | ComputedRef<VbenFormProps<import("./types").BaseFormComponentType>>, FormActions] | null : [VbenFormProps<import("./types").BaseFormComponentType> | ComputedRef<VbenFormProps<import("./types").BaseFormComponentType>>, FormActions], provideFormProps: (contextValue: [VbenFormProps<import("./types").BaseFormComponentType> | ComputedRef<VbenFormProps<import("./types").BaseFormComponentType>>, FormActions]) => [VbenFormProps<import("./types").BaseFormComponentType> | ComputedRef<VbenFormProps<import("./types").BaseFormComponentType>>, FormActions];
export declare function useFormInitial(props: ComputedRef<VbenFormProps> | VbenFormProps): {
    delegatedSlots: ComputedRef<string[]>;
    form: import("vee-validate").FormContext<{
        [x: string]: any;
    }, {
        [x: string]: any;
    }>;
};
