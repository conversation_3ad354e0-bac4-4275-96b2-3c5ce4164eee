import type { DrawerApiOptions, DrawerState } from './drawer';
import { Store } from '@vben-core/shared/store';
export declare class DrawerApi {
    private api;
    private state;
    sharedData: Record<'payload', any>;
    store: Store<DrawerState>;
    constructor(options?: DrawerApiOptions);
    batchStore(cb: () => void): void;
    /**
     * 关闭弹窗
     */
    close(): void;
    getData<T extends object = Record<string, any>>(): T;
    /**
     * 取消操作
     */
    onCancel(): void;
    /**
     * 确认操作
     */
    onConfirm(): void;
    open(): void;
    setData<T>(payload: T): void;
    setState(stateOrFn: ((prev: DrawerState) => Partial<DrawerState>) | Partial<DrawerState>): void;
}
