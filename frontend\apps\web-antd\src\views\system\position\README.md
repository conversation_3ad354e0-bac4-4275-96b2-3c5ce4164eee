# 岗位管理功能

## 功能概述

岗位管理模块提供了完整的岗位管理功能，支持树形结构的岗位层级管理。

## 主要功能

### 1. 岗位信息管理
- **岗位名称**: 岗位的名称
- **岗位编码**: 唯一的岗位编码
- **上级岗位**: 支持树形层级结构
- **归属部门**: 岗位所属的组织部门
- **岗位类别**: 部门或科室
- **排序**: 支持排序调整
- **状态**: 启用/停用状态控制
- **描述**: 岗位描述信息

### 2. 组织筛选侧边栏
- **组织树形结构**: 左侧显示完整的组织架构树
- **组织筛选**: 点击组织节点筛选对应的岗位
- **搜索功能**: 支持组织名称搜索
- **全部组织**: 支持查看所有岗位
- **响应式设计**: 适配不同屏幕尺寸

### 3. 树形结构显示
- 支持树形结构展示岗位层级
- 可展开/收起子岗位
- 支持无限层级嵌套

### 4. 基本操作
- **新增岗位**: 创建新的岗位
- **编辑岗位**: 修改岗位信息
- **删除岗位**: 删除岗位（需确保无子岗位）
- **添加子岗位**: 为现有岗位添加下级岗位
- **搜索过滤**: 支持按名称、编码、类别、状态等条件搜索

## 文件结构

```
position/
├── index.vue                    # 主页面
├── position.api.ts              # API接口
├── position.data.ts             # 数据配置
├── components/
│   └── PositionModal.vue       # 新增/编辑弹窗
└── README.md                   # 说明文档

注：OrganizationSidebar.vue 组织筛选侧边栏已移动到
../organization/components/OrganizationSidebar.vue
```

## 使用说明

### 1. 访问路径
系统管理 -> 岗位管理 (`/system/position`)

### 2. 新增岗位
1. 点击"新增岗位"按钮
2. 填写岗位信息
3. 选择上级岗位（可选）
4. 选择归属部门（必选）
5. 设置类别、排序等信息
6. 点击确定保存

### 3. 编辑岗位
1. 在岗位列表中点击"编辑"按钮
2. 修改岗位信息
3. 点击确定保存

### 4. 添加子岗位
1. 在岗位列表中点击"添加子岗位"按钮
2. 系统会自动设置上级岗位
3. 填写其他信息并保存

### 5. 组织筛选
1. 在左侧组织树中点击任意组织节点
2. 右侧岗位列表会自动筛选显示该组织下的岗位
3. 点击"全部组织"查看所有岗位
4. 使用搜索框快速查找组织

### 6. 删除岗位
1. 在岗位列表中点击"删除"按钮
2. 确认删除操作
3. 注意：有子岗位的岗位无法删除

## 数据验证

### 必填字段
- 岗位名称
- 岗位编码
- 归属部门
- 岗位类别

### 唯一性约束
- 岗位编码必须唯一

### 业务规则
- 不能将自己设置为上级岗位
- 不能将子岗位设置为上级岗位（防止循环引用）
- 有子岗位的岗位无法删除
- 必须选择归属部门

## API接口

### 前端接口
- `getPositionTree()` - 获取岗位树形结构
- `createPosition()` - 创建岗位
- `updatePosition()` - 更新岗位
- `deletePosition()` - 删除岗位

### 后端接口
- `GET /positions/tree` - 获取岗位树形结构
- `POST /positions` - 创建岗位
- `PUT /positions/:id` - 更新岗位
- `DELETE /positions/:id` - 删除岗位

## 注意事项

1. 岗位必须归属于某个组织部门
2. 岗位编码在系统中必须唯一
3. 删除岗位前需确保没有子岗位
4. 岗位的层级关系不能形成循环引用
5. 岗位状态影响相关业务功能的可用性
