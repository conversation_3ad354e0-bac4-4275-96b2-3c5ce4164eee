var K=Object.defineProperty;var p=Object.getOwnPropertySymbols;var F=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable;var C=(t,e,s)=>e in t?K(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,j=(t,e)=>{for(var s in e||(e={}))F.call(e,s)&&C(t,s,e[s]);if(p)for(var s of p(e))N.call(e,s)&&C(t,s,e[s]);return t};var A=(t,e)=>{var s={};for(var a in t)F.call(t,a)&&e.indexOf(a)<0&&(s[a]=t[a]);if(t!=null&&p)for(var a of p(t))e.indexOf(a)<0&&N.call(t,a)&&(s[a]=t[a]);return s};import{A as Q,C as R,S as U,i as W,j as Y,k as Z}from"./bootstrap-DShsrVit.js";import{_ as aa}from"./analytics-trends.vue_vue_type_script_setup_true_lang-CKMGijT1.js";import{_ as ea}from"./analytics-visits.vue_vue_type_script_setup_true_lang-CCiA4jVb.js";import{_ as ta}from"./analytics-visits-data.vue_vue_type_script_setup_true_lang-BfqXe14_.js";import{_ as sa}from"./analytics-visits-sales.vue_vue_type_script_setup_true_lang-B4X0k3Y7.js";import{_ as la}from"./analytics-visits-source.vue_vue_type_script_setup_true_lang-Bu3KC_d3.js";import{_ as na,a as oa,b as ra,c as ia}from"./CardTitle.vue_vue_type_script_setup_true_lang-D9WinrTz.js";import{a4 as m,J as w,af as i,ag as v,ah as r,ae as z,ac as ua,a3 as l,aX as ca,O as I,bx as E,av as da,V as fa,a5 as ma,by as _a,am as d,ao as b,ar as pa,bz as va,n,F as $,as as y,an as D,ap as O}from"../jse/index-index-BMh_AyeW.js";import{_ as ba}from"./icon.vue_vue_type_script_setup_true_lang-BK5optdP.js";import{_ as ga}from"./CardFooter.vue_vue_type_script_setup_true_lang-BLjMzuQQ.js";import{_ as xa,a as ha,b as $a}from"./TabsList.vue_vue_type_script_setup_true_lang-DfMjVjnv.js";import{_ as h}from"./analysis-chart-card.vue_vue_type_script_setup_true_lang-DyKxMPK_.js";import"./use-echarts-DwgLoGZI.js";const ya=m({__name:"TabsTrigger",props:{class:{},value:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(t){const e=t,s=w(()=>{const _=e,{class:u}=_;return A(_,["class"])}),a=Q(s);return(u,o)=>(i(),v(l(R),ua(l(a),{class:l(ca)("ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow",e.class)}),{default:r(()=>[z(u.$slots,"default")]),_:3},16,["class"]))}}),P=m({name:"CountToAnimator",__name:"count-to-animator",props:{autoplay:{type:Boolean,default:!0},color:{default:""},decimal:{default:"."},decimals:{default:0},duration:{default:1500},endVal:{default:2021},prefix:{default:""},separator:{default:","},startVal:{default:0},suffix:{default:""},transition:{default:"linear"},useEasing:{type:Boolean,default:!0}},emits:["onStarted","onFinished"],setup(t,{expose:e,emit:s}){const a=t,u=s,o=I(a.startVal),_=I(!1);let V=E(o);const J=w(()=>M(l(V)));da(()=>{o.value=a.startVal}),fa([()=>a.startVal,()=>a.endVal],()=>{a.autoplay&&T()}),ma(()=>{a.autoplay&&T()});function T(){k(),o.value=a.endVal}function L(){o.value=a.startVal,k()}function k(){V=E(o,j({disabled:_,duration:a.duration,onFinished:()=>u("onFinished"),onStarted:()=>u("onStarted")},a.useEasing?{transition:_a[a.transition]}:{}))}function M(c){if(!c&&c!==0)return"";const{decimal:S,decimals:X,prefix:q,separator:g,suffix:G}=a;c=Number(c).toFixed(X),c+="";const x=c.split(".");let f=x[0];const H=x.length>1?S+x[1]:"",B=/(\d+)(\d{3})/;if(g&&!va(g)&&f)for(;B.test(f);)f=f.replace(B,`$1${g}$2`);return q+f+H+G}return e({reset:L}),(c,S)=>(i(),d("span",{style:pa({color:c.color})},b(J.value),5))}}),wa={class:"card-box w-full px-4 pb-5 pt-3"},Va=m({name:"AnalysisChartsTabs",__name:"analysis-charts-tabs",props:{tabs:{default:()=>[]}},setup(t){const e=t,s=w(()=>{var a,u;return(u=(a=e.tabs)==null?void 0:a[0])==null?void 0:u.value});return(a,u)=>(i(),d("div",wa,[n(l($a),{"default-value":s.value},{default:r(()=>[n(l(xa),null,{default:r(()=>[(i(!0),d($,null,y(a.tabs,o=>(i(),v(l(ya),{key:o.label,value:o.value},{default:r(()=>[D(b(o.label),1)]),_:2},1032,["value"]))),128))]),_:1}),(i(!0),d($,null,y(a.tabs,o=>(i(),v(l(ha),{key:o.label,value:o.value,class:"pt-4"},{default:r(()=>[z(a.$slots,o.value)]),_:2},1032,["value"]))),128))]),_:3},8,["default-value"])]))}}),Ta={class:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4"},ka=m({name:"AnalysisOverview",__name:"analysis-overview",props:{items:{default:()=>[]}},setup(t){return(e,s)=>(i(),d("div",Ta,[(i(!0),d($,null,y(e.items,a=>(i(),v(l(ia),{key:a.title,title:a.title,class:"w-full"},{default:r(()=>[n(l(oa),null,{default:r(()=>[n(l(na),{class:"text-xl"},{default:r(()=>[D(b(a.title),1)]),_:2},1024)]),_:2},1024),n(l(ra),{class:"flex items-center justify-between"},{default:r(()=>[n(l(P),{"end-val":a.value,"start-val":1,class:"text-xl",prefix:""},null,8,["end-val"]),n(l(ba),{icon:a.icon,class:"size-8 flex-shrink-0"},null,8,["icon"])]),_:2},1024),n(l(ga),{class:"justify-between"},{default:r(()=>[O("span",null,b(a.totalTitle),1),n(l(P),{"end-val":a.totalValue,"start-val":1,prefix:""},null,8,["end-val"])]),_:2},1024)]),_:2},1032,["title"]))),128))]))}}),Sa={class:"p-5"},Ba={class:"mt-5 w-full md:flex"},Xa=m({__name:"index",setup(t){const e=[{icon:U,title:"用户量",totalTitle:"总用户量",totalValue:12e4,value:2e3},{icon:W,title:"访问量",totalTitle:"总访问量",totalValue:5e5,value:2e4},{icon:Y,title:"下载量",totalTitle:"总下载量",totalValue:12e4,value:8e3},{icon:Z,title:"使用量",totalTitle:"总使用量",totalValue:5e4,value:5e3}],s=[{label:"流量趋势",value:"trends"},{label:"月访问量",value:"visits"}];return(a,u)=>(i(),d("div",Sa,[n(l(ka),{items:e}),n(l(Va),{tabs:s,class:"mt-5"},{trends:r(()=>[n(aa)]),visits:r(()=>[n(ea)]),_:1}),O("div",Ba,[n(l(h),{class:"mt-5 md:mr-4 md:mt-0 md:w-1/3",title:"访问数量"},{default:r(()=>[n(ta)]),_:1}),n(l(h),{class:"mt-5 md:mr-4 md:mt-0 md:w-1/3",title:"访问来源"},{default:r(()=>[n(la)]),_:1}),n(l(h),{class:"mt-5 md:mt-0 md:w-1/3",title:"访问来源"},{default:r(()=>[n(sa)]),_:1})])]))}});export{Xa as default};
