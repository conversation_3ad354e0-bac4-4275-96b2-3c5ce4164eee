<script lang="ts" setup>
import { computed, ref, unref, watch } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { formSchema } from '../organization.data';
import {
  createOrganization,
  updateOrganization,
  getOrganizationTree,
  getRoleList
} from '../organization.api';
import { message } from 'ant-design-vue';
import { showLoading, showSuccess } from '#/utils/toast.js';
import { buildTreeSelectData } from '../organization.data';

// 声明Emits
const emit = defineEmits(['register', 'success']);

const isUpdate = ref(false);
const organizationId = ref<number | null>(null);
const organizationTreeData = ref([]);
const roleOptions = ref([]);

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onClosed() {
    formApi.resetForm();
    isUpdate.value = false;
    organizationId.value = null;
  },
  onConfirm() {},
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      loadOrganizationTree();
      loadRoleList();

      const state = modalApi.useStore().value;
      if (state?.row) {
        // 编辑模式
        isUpdate.value = true;
        organizationId.value = state.row.id;
        formApi.setValues({
          ...state.row,
          parentId: state.row.parentId === 0 ? undefined : state.row.parentId,
        });
      } else {
        // 新增模式
        isUpdate.value = false;
        organizationId.value = null;
        if (state?.parentId) {
          formApi.setFieldValue('parentId', state.parentId);
        }
      }
    }
  },
});

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'text-right pr-4',
    labelWidth: 100,
  },
  // 提交函数
  handleSubmit,
  handleReset,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: formSchema,
  wrapperClass: 'grid-cols-1',
  submitButtonOptions: {
    content: '确定',
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  },
  resetButtonOptions: {
    content: '取消',
  },
});

// 设置标题
const title = computed(() => (unref(isUpdate) ? '编辑组织' : '新增组织'));

// 加载组织树形数据
async function loadOrganizationTree() {
  try {
    const result = await getOrganizationTree();
    const treeData = buildTreeSelectData(result || []);
    organizationTreeData.value = result;

    // 更新表单中的上级机构选项
    formApi.updateSchema([
      {
        fieldName: 'parentId',
        componentProps: {
          treeData: treeData,
        },
      },
    ]);
  } catch (error) {
    console.error('加载组织树形数据失败:', error);
  }
}

// 加载角色列表
async function loadRoleList() {
  try {
    const result = await getRoleList();
    roleOptions.value = (result || []).map(role => ({
      label: role.name,
      value: role.id,
    }));

    // 更新表单中的默认角色选项
    formApi.updateSchema([
      {
        fieldName: 'defaultRoleId',
        componentProps: {
          options: roleOptions.value,
        },
      },
    ]);
  } catch (error) {
    console.error('加载角色列表失败:', error);
  }
}

async function handleReset() {
  modalApi.close();
}

// 表单提交事件
async function handleSubmit(values) {
  try {
    showLoading('操作处理中...');

    if (isUpdate.value && organizationId.value) {
      await updateOrganization(organizationId.value, values);
      showSuccess('更新成功！');
    } else {
      await createOrganization(values);
      showSuccess('创建成功！');
    }

    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('操作失败:', error);
    message.error(error.message || '操作失败');
  }
}

// 打开新增弹窗
function openCreateModal(parentId?: number) {
  isUpdate.value = false;
  organizationId.value = null;
  modalApi.open();

  if (parentId) {
    formApi.setFieldValue('parentId', parentId);
  }
}

// 打开编辑弹窗
function openEditModal(record: any) {
  isUpdate.value = true;
  organizationId.value = record.id;
  modalApi.open();

  // 设置表单值
  formApi.setValues({
    ...record,
    parentId: record.parentId === 0 ? undefined : record.parentId,
  });
}

// 暴露方法给父组件
defineExpose({
  openCreateModal,
  openEditModal,
});
</script>

<template>
  <Modal
    v-bind="$attrs"
    :footer="false"
    :title="title"
    class="w-[600px]"
    :destroy-on-close="true"
    :maskClosable="false"
  >
    <div style="padding: 20px">
      <Form />
    </div>
  </Modal>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';
</style>
