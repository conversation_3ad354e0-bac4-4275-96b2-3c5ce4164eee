var b=(v,g,l)=>new Promise((n,s)=>{var c=o=>{try{u(l.next(o))}catch(a){s(a)}},e=o=>{try{u(l.throw(o))}catch(a){s(a)}},u=o=>o.done?n(o.value):Promise.resolve(o.value).then(c,e);u((l=l.apply(v,g)).next())});import{u as V}from"./entity.api-CPgpBrqe.js";import{s as B,a as N}from"./toast-CQjPPeQ1.js";import{u as U,a as T,s as E}from"./fileUpload-DI0dJ9zY.js";import{u as M}from"./scene.data-BMXeOdST.js";import{u as A}from"./form-DdFfsSWf.js";import{s as j}from"./alert-DJKWbMfG.js";import{d as q,r as y,j as z,b as m,q as k,f as i,s as D,a as h,e as L,k as F,v as G}from"../jse/index-index-DyHD_jbN.js";import{u as H}from"./use-modal-uChFuhJy.js";import{a as J}from"./bootstrap-5OPUVRWy.js";import"./loading-DzjUKA94.js";const K={style:{"padding-left":"20px","padding-right":"20px","padding-top":"20px"}},Q=["directory","webkitdirectory"],R={key:0,for:"fileInput",onclick:"triggerFileInput",class:"custom-label bg-gray-200 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400"},W={key:1,for:"fileInput",onclick:"triggerFileInput",class:"custom-label bg-primary-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400"},X={key:0,style:{width:"900px"}},Y=q({__name:"EntityUploadModal",emits:["handleCancel","success"],setup(v,{emit:g}){const l=g;let n=null,s=null;y([]);const c=y("0"),e=y(!1),u=()=>{d.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}})},[o,a]=H({closeOnClickModal:!1,onCancel(){a.close()},onClosed(){u()},onBeforeClose(){return e.value?(j("提示","关闭窗口将停止上传，是否确定关闭窗口？",()=>{E(),e.value=!1,a.close()}),!1):!0},onConfirm(){},onOpenChange(r){r&&(n=a.useStore().value.row,d.setFieldValue("dataType",n.dataType),e.value=!1)}}),[O,d]=A({commonConfig:{componentProps:{class:"w-full"},labelClass:"w-2/8"},handleSubmit:C,layout:"horizontal",schema:M,wrapperClass:"grid-cols-1",resetButtonOptions:{show:!1}});function C(r){return b(this,null,function*(){try{B("操作处理中..."),e.value=!0,d.setState({commonConfig:{disabled:!0},submitButtonOptions:{disabled:!0},resetButtonOptions:{disabled:!0}});let t=n.id,f=n.dataType,x=P=>b(null,null,function*(){try{B("文件上传成功，解析文件中..."),yield V({id:t,fileId:P,dataType:f}),w&&w()}catch(Z){p&&p()}}),_=r.fileFormat;c.value==="0"?yield U(s,x,f,_,p):yield T(s,x,f,_)}catch(t){p&&p()}finally{}})}const w=()=>{e.value=!1,d.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}}),a.close(),N("操作成功！"),l("success")},p=()=>{e.value=!1,d.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}}),a.close(),l("success")},S=()=>{l("onCancel",{})},I=r=>{if(e.value)return;console.log(r),s=r.target;const t=document.getElementById("fileName");t.textContent=s.files.length>0?"已选"+s.files.length+"个文件":"没有选择文件",d.setFieldValue("fileUpload",s.files)};return(r,t)=>(m(),z(F(o),{title:"上传文件",class:"w-[600px]",onCancel:S,onOk:C,maskClosable:!1,footer:!1},{default:k(()=>[i("div",K,[D(F(O),null,{fileUpload:k(f=>[i("input",{class:"hidden",id:"fileInput",type:"file",directory:c.value==="0",webkitdirectory:c.value==="0",onChange:I},null,40,Q),e.value?(m(),h("label",R," 选择文件 ")):(m(),h("label",W," 选择文件 ")),t[0]||(t[0]=i("span",{id:"fileName",class:"ml-4"},"没有选择文件",-1))]),_:1}),e.value?(m(),h("view",X,t[1]||(t[1]=[i("div",{id:"folderProgressArea",class:"folderProgressArea"},[G(" 文件夹总进度："),i("div",{id:"folderProgress",class:"folderProgress"},"0%")],-1),i("div",{style:{display:"flex","flex-direction":"column","justify-content":"flex-start","align-items":"flex-start"}},[i("div",{style:{"margin-bottom":"5px"}},"当前文件进度："),i("div",{id:"fileProgress",class:"fileProgress bg-primary text-white"})],-1)]))):L("",!0)])]),_:1}))}}),ce=J(Y,[["__scopeId","data-v-87b81a34"]]);export{ce as default};
