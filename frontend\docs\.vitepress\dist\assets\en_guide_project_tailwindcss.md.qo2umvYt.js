import{ao as i,k as e,aP as a,l as n,ay as t,j as o}from"./chunks/framework.C8U7mBUf.js";const s=JSON.parse('{"title":"Tailwind CSS","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/project/tailwindcss.md","filePath":"en/guide/project/tailwindcss.md"}');const r=i({name:"en/guide/project/tailwindcss.md"},[["render",function(i,s,r,l,d,c){const u=t("NolebaseGitContributors"),f=t("NolebaseGitChangelog");return o(),e("div",null,[s[0]||(s[0]=a('<h1 id="tailwind-css" tabindex="-1">Tailwind CSS <a class="header-anchor" href="#tailwind-css" aria-label="Permalink to &quot;Tailwind CSS&quot;">​</a></h1><p><a href="https://tailwindcss.com/" target="_blank" rel="noreferrer">Tailwind CSS</a> is a utility-first CSS framework for quickly building custom designs.</p><h2 id="configuration" tabindex="-1">Configuration <a class="header-anchor" href="#configuration" aria-label="Permalink to &quot;Configuration&quot;">​</a></h2><p>The project&#39;s configuration file is located in <code>internal/tailwind-config</code>, where you can modify the Tailwind CSS configuration.</p><div class="tip custom-block"><p class="custom-block-title">Restrictions on using tailwindcss in packages</p><p>Tailwind CSS compilation will only be enabled if there is a <code>tailwind.config.mjs</code> file present in the corresponding package. Otherwise, Tailwind CSS will not be enabled. If you have a pure SDK package that does not require Tailwind CSS, you do not need to create a <code>tailwind.config.mjs</code> file.</p></div>',5)),n(u),n(f)])}]]);export{s as __pageData,r as default};
