# 用户管理功能

## 功能概述

用户管理模块提供了完整的用户管理功能，支持用户信息维护、状态控制、批量操作等。

## 主要功能

### 1. 用户信息管理
- **工号**: 用户的工号（可选）
- **用户名**: 唯一的用户名
- **姓名**: 用户的真实姓名
- **手机号**: 联系电话
- **邮箱**: 电子邮箱地址
- **任职部门**: 用户所属的组织部门
- **岗位**: 用户的岗位信息
- **状态**: 启用/禁用状态控制

### 2. 组织筛选侧边栏
- **组织树形结构**: 左侧显示完整的组织架构树
- **组织筛选**: 点击组织节点筛选对应的用户
- **搜索功能**: 支持组织名称搜索
- **全部组织**: 支持查看所有用户
- **响应式设计**: 适配不同屏幕尺寸

### 3. 用户列表管理
- **多条件筛选**: 支持按用户名、姓名、状态、岗位、部门等条件筛选
- **表格展示**: 清晰展示用户信息
- **状态切换**: 快速启用/禁用用户账户
- **分页显示**: 支持大量用户数据的分页展示

### 4. 基本操作
- **新增用户**: 创建新的用户账户
- **编辑用户**: 修改用户信息
- **删除用户**: 删除用户账户
- **重置密码**: 重置用户密码为默认值
- **状态控制**: 启用/禁用用户账户

### 5. 批量操作
- **批量删除**: 批量删除选中的用户
- **批量启用**: 批量启用选中的用户
- **批量禁用**: 批量禁用选中的用户
- **批量重置密码**: 批量重置选中用户的密码
- **批量权限分配**: 批量分配用户角色（待实现）

### 6. 岗位管理
- **岗位列表**: 在用户编辑对话框中管理用户的岗位信息
- **添加岗位**: 为用户添加新的岗位
- **删除岗位**: 移除用户的岗位信息
- **岗位信息**: 显示岗位名称、编码、归属部门等信息

## 文件结构

```
user/
├── index.vue                    # 主页面
├── user.api.ts                  # API接口
├── user.data.ts                 # 数据配置
├── components/
│   └── UserModal.vue           # 新增/编辑弹窗
└── README.md                   # 说明文档
```

## 使用说明

### 1. 访问路径
系统管理 -> 用户管理 (`/system/user`)

### 2. 新增用户
1. 点击"新增用户"按钮
2. 填写用户基础信息
3. 选择任职部门和岗位
4. 在岗位信息区域添加用户的岗位
5. 点击确定保存

### 3. 编辑用户
1. 在用户列表中点击"编辑"按钮
2. 修改用户信息
3. 管理用户的岗位信息
4. 点击确定保存

### 4. 批量操作
1. 选中需要操作的用户（使用复选框）
2. 点击对应的批量操作按钮
3. 确认操作

### 5. 组织筛选
1. 在左侧组织树中点击任意组织节点
2. 右侧用户列表会自动筛选显示该组织下的用户
3. 点击"全部组织"查看所有用户
4. 使用搜索框快速查找组织

### 6. 重置密码
1. 在用户列表中点击"重置密码"按钮
2. 确认重置操作
3. 密码将重置为默认值：123456

## 数据验证

### 必填字段
- 用户名
- 姓名
- 密码（仅新增时）

### 唯一性约束
- 用户名必须唯一
- 工号必须唯一（如果填写）

### 业务规则
- 用户名不能重复
- 工号不能重复
- 密码长度不能少于6位
- 邮箱格式必须正确

## API接口

### 前端接口
- `getUserList()` - 获取用户列表
- `createUser()` - 创建用户
- `updateUser()` - 更新用户
- `deleteUser()` - 删除用户
- `batchDeleteUsers()` - 批量删除用户
- `resetUserPassword()` - 重置用户密码
- `updateUserStatus()` - 更新用户状态
- `batchUpdateStatus()` - 批量更新用户状态

### 后端接口
- `GET /users` - 获取用户列表
- `POST /users` - 创建用户
- `PUT /users/:id` - 更新用户
- `DELETE /users/:id` - 删除用户
- `DELETE /users/batch` - 批量删除用户
- `POST /users/:id/reset-password` - 重置用户密码
- `PATCH /users/:id/status` - 更新用户状态
- `PUT /users/batch-status` - 批量更新用户状态

## 注意事项

1. 用户必须归属于某个组织部门
2. 用户名在系统中必须唯一
3. 工号在系统中必须唯一（如果填写）
4. 删除用户前需确保没有关联的业务数据
5. 用户状态影响登录和相关业务功能的可用性
6. 重置密码后用户需要重新登录
7. 岗位信息管理支持一个用户拥有多个岗位

## 技术特性

- **响应式设计**: 适配不同屏幕尺寸
- **组件化开发**: 模块化的组件结构
- **类型安全**: 使用TypeScript确保类型安全
- **数据验证**: 前后端双重数据验证
- **用户体验**: 友好的交互反馈和加载状态
- **权限控制**: 基于角色的权限管理（待完善）
