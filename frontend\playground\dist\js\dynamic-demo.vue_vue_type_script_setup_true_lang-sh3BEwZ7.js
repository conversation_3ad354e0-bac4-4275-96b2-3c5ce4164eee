var g=Object.defineProperty,b=Object.defineProperties;var x=Object.getOwnPropertyDescriptors;var r=Object.getOwnPropertySymbols;var B=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var i=(a,t,e)=>t in a?g(a,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[t]=e,u=(a,t)=>{for(var e in t||(t={}))B.call(t,e)&&i(a,e,t[e]);if(r)for(var e of r(t))k.call(t,e)&&i(a,e,t[e]);return a},f=(a,t)=>b(a,x(t));import{by as S,B as c}from"./bootstrap-DShsrVit.js";import{a4 as V,af as N,ag as T,ah as l,a3 as o,ap as v,n as d,an as m,ao as M}from"../jse/index-index-BMh_AyeW.js";import{u as $}from"./use-modal-B0smF4x0.js";const w={class:"flex-col-center"},h=V({__name:"dynamic-demo",setup(a){const[t,e]=$({draggable:!0,onCancel(){e.close()},onConfirm(){S.info("onConfirm")},title:"动态修改配置示例"}),p=e.useStore();function _(){e.setState({title:"内部动态标题"})}function y(){e.setState(s=>f(u({},s),{fullscreen:!s.fullscreen}))}return(s,n)=>(N(),T(o(t),null,{default:l(()=>[v("div",w,[d(o(c),{class:"mb-3",type:"primary",onClick:n[0]||(n[0]=C=>_())},{default:l(()=>n[2]||(n[2]=[m(" 内部动态修改标题 ")])),_:1}),d(o(c),{class:"mb-3",type:"primary",onClick:n[1]||(n[1]=C=>y())},{default:l(()=>[m(M(o(p).fullscreen?"退出全屏":"打开全屏"),1)]),_:1})])]),_:1}))}});export{h as _};
