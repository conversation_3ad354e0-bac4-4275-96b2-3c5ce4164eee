var h=(f,d,e)=>new Promise((i,u)=>{var p=a=>{try{r(e.next(a))}catch(n){u(n)}},m=a=>{try{r(e.throw(a))}catch(n){u(n)}},r=a=>a.done?i(a.value):Promise.resolve(a.value).then(p,m);r((e=e.apply(f,d)).next())});import{B as k,bd as D}from"./bootstrap-DShsrVit.js";import{u as N}from"./useQuery-CNjS4j1-.js";import{a4 as V,O as q,af as t,am as o,ap as c,n as g,ah as x,an as B,a3 as s,ao as _,F as C,as as F,al as j}from"../jse/index-index-BMh_AyeW.js";const w={class:"flex gap-4"},z={class:"p-4"},E={key:0},I={key:1},L={key:2},P=10,K=V({__name:"paginated-queries",setup(f){const d=v=>h(this,null,function*(){return(yield fetch(`https://dummyjson.com/products?limit=${P}&skip=${(v.value-1)*P}`)).json()}),e=q(1),{data:i,error:u,isError:p,isPending:m,isPlaceholderData:r}=N({placeholderData:D,queryFn:()=>d(e),queryKey:["products",e]}),a=()=>{e.value=Math.max(e.value-1,1)},n=()=>{r.value||(e.value=e.value+1)};return(v,l)=>(t(),o(C,null,[c("div",w,[g(s(k),{size:"small",onClick:a},{default:x(()=>l[0]||(l[0]=[B("上一页")])),_:1}),c("p",null,"当前页: "+_(e.value),1),g(s(k),{size:"small",onClick:n},{default:x(()=>l[1]||(l[1]=[B("下一页")])),_:1})]),c("div",z,[s(m)?(t(),o("div",E,"加载中...")):s(p)?(t(),o("div",I,"出错了: "+_(s(u)),1)):s(i)?(t(),o("div",L,[c("ul",null,[(t(!0),o(C,null,F(s(i).products,y=>(t(),o("li",{key:y.id},_(y.title),1))),128))])])):j("",!0)])],64))}});export{K as _};
