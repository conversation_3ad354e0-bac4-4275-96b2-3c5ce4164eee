import{_ as L,b as P,c as k,d as B,a as T,e as b,f as w,N as A,g as y,h as S,i as V}from"./layout.vue_vue_type_script_setup_true_lang-CXvwR802.js";import{A as C,_ as F,a as I}from"./authentication-Cmx7T77G.js";import{_ as O,a as U}from"./theme-toggle.vue_vue_type_script_setup_true_lang-pVzKzmJu.js";import{a as r}from"./bootstrap-5OPUVRWy.js";import{r as o,a as t,b as s}from"../jse/index-index-DyHD_jbN.js";import"./icon.vue_vue_type_script_setup_true_lang-CgLK7NiC.js";import"./use-modal-uChFuhJy.js";import"./loading-DzjUKA94.js";import"./rotate-cw-lLmdvVrn.js";const a=o(!1);function _(){function e(){a.value=!0}return{handleOpenPreference:e,openPreferences:a}}const n={};function c(e,i){return s(),t("div")}const d=r(n,[["render",c]]);export{C as AuthPageLayout,F as AuthenticationColorToggle,I as AuthenticationLayoutToggle,L as BasicLayout,P as Breadcrumb,k as CheckUpdates,B as GlobalSearch,T as IFrameRouterView,d as IFrameView,O as LanguageToggle,b as LockScreen,w as LockScreenModal,A as Notification,y as Preferences,S as PreferencesButton,U as ThemeToggle,V as UserDropdown,_ as useOpenPreferences};
