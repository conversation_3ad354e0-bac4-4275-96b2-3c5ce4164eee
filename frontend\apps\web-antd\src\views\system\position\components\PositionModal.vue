<script lang="ts" setup>
import { computed, ref, unref, watch } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { formSchema } from '../position.data';
import {
  createPosition,
  updatePosition,
  getPositionList,
} from '../position.api';
import { getOrganizationTree } from '../../organization/organization.api';
import { message } from 'ant-design-vue';
import { showLoading, showSuccess } from '#/utils/toast.js';
import { buildTreeSelectData } from '../position.data';

// 声明Emits
const emit = defineEmits(['register', 'success']);

const isUpdate = ref(false);
const positionId = ref<number | null>(null);
const positionTreeData = ref([]);
const organizationTreeData = ref([]);

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onClosed() {
    formApi.resetForm();
    isUpdate.value = false;
    positionId.value = null;
    // 恢复部门选择字段为可编辑
    formApi.updateSchema([
      {
        fieldName: 'departmentId',
        componentProps: {
          disabled: false,
        },
      },
    ]);
  },
  onConfirm() {},
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      // 先加载组织树
      loadOrganizationTree();
      // 再加载岗位列表
      loadPositionTree();

      const state = modalApi.useStore().value;
      if (state?.row) {
        // 编辑模式
        isUpdate.value = true;
        positionId.value = state.row.id;
        formApi.setValues({
          ...state.row,
          parentId: state.row.parentId === 0 ? undefined : state.row.parentId,
        });
        // 编辑模式下不允许修改归属部门
        formApi.updateSchema([
          {
            fieldName: 'departmentId',
            componentProps: {
              disabled: true,
            },
          },
        ]);
      } else {
        // 新增模式
        isUpdate.value = false;
        positionId.value = null;
        if (state?.parentId) {
          formApi.setFieldValue('parentId', state.parentId);
        }
        if (state?.departmentId) {
          // 预填归属部门并禁用字段
          formApi.setFieldValue('departmentId', state.departmentId);
          formApi.updateSchema([
            {
              fieldName: 'departmentId',
              componentProps: {
                disabled: true,
              },
            },
          ]);
        } else {
          // 无预设部门则保持可编辑
          formApi.updateSchema([
            {
              fieldName: 'departmentId',
              componentProps: {
                disabled: false,
              },
            },
          ]);
        }
      }
    }
  },
});

const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'text-right pr-4',
    labelWidth: 100,
  },
  // 提交函数
  handleSubmit,
  handleReset,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: formSchema,
  wrapperClass: 'grid-cols-1',
  submitButtonOptions: {
    content: '确定',
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  },
  resetButtonOptions: {
    content: '取消',
  },
});

// 计算弹窗标题
const title = computed(() => {
  return isUpdate.value ? '编辑岗位' : '新增岗位';
});

// 加载组织树形数据
async function loadOrganizationTree() {
  try {
    const data = await getOrganizationTree();
    const options = buildTreeSelectData(data || []);
    organizationTreeData.value = options;

    // 更新表单中的归属机构选项
    formApi.updateSchema([
      {
        fieldName: 'departmentId',
        componentProps: {
          treeData: organizationTreeData.value,
        },
      },
    ]);
  } catch (error) {
    console.error('加载组织树形数据失败:', error);
  }
}

// 加载岗位列表数据
async function loadPositionTree() {
  try {
    const state = modalApi.useStore().value;
    const currentDepartmentId = state?.departmentId || state?.row?.departmentId;

    if (!currentDepartmentId) {
      positionTreeData.value = [];
      formApi.updateSchema([
        {
          fieldName: 'parentId',
          componentProps: {
            options: [],
          },
        },
      ]);
      return;
    }

    const data = await getPositionList({
      // departmentId: currentDepartmentId,
      status: 1, // 只获取启用状态的岗位
    });

    // 转换为选择项列表，过滤掉自己（编辑时）
    const options = (data || [])
      .map((item: any) => ({
        label: item.name,
        value: item.id,
      }));

    positionTreeData.value = options;

    // 更新表单中的上级岗位选项
    formApi.updateSchema([
      {
        fieldName: 'parentId',
        componentProps: {
          options: positionTreeData.value,
        },
      },
    ]);
  } catch (error) {
    console.error('加载岗位树形数据失败:', error);
  }
}


// 表单重置事件
async function handleReset() {
  modalApi.close();
}

// 表单提交事件
async function handleSubmit(values) {
  try {
    showLoading('操作处理中...');

    if (isUpdate.value && positionId.value) {
      await updatePosition(positionId.value, values);
      showSuccess('更新成功！');
    } else {
      await createPosition(values);
      showSuccess('创建成功！');
    }

    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('操作失败:', error);
    // message.error(error.message || '操作失败');
  }
}

// 打开新增弹窗
function openCreateModal(parentId?: number) {
  isUpdate.value = false;
  positionId.value = null;
  modalApi.open();

  if (parentId) {
    formApi.setFieldValue('parentId', parentId);
  }
}

// 打开编辑弹窗
function openEditModal(record: any) {
  isUpdate.value = true;
  positionId.value = record.id;
  modalApi.open();

  // 设置表单值
  formApi.setValues({
    ...record,
    parentId: record.parentId === 0 ? undefined : record.parentId,
  });
}

// 暴露方法给父组件
defineExpose({
  openCreateModal,
  openEditModal,
});
</script>

<template>
  <Modal
    v-bind="$attrs"
    :footer="false"
    :title="title"
    class="w-[600px]"
    :destroy-on-close="true"
    :maskClosable="false"
  >
    <div style="padding: 20px">
      <Form />
    </div>
  </Modal>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';
</style>
