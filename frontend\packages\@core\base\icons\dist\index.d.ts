import * as vue from 'vue';
export { ArrowDown, ArrowLeft, ArrowLeftToLine, ArrowRightLeft, ArrowRightToLine, ArrowUp, ArrowUpToLine, Bell, BookOpenText, Check, ChevronDown, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, CircleHelp, Copy, CornerDownLeft, Ellipsis, Expand, ExternalLink, Eye, EyeOff, FoldHorizontal, Fullscreen, Github, Grip, Menu as IconDefault, Info, InspectionPanel, Languages, LoaderCircle, LockKeyhole, LogOut, MailCheck, Maximize, ArrowRightFromLine as MdiMenuClose, ArrowLeftFromLine as MdiMenuOpen, Menu, Minimize, Minimize2, MoonStar, Palette, PanelLeft, PanelRight, Pin, PinOff, RotateCw, Search, SearchX, Settings, Shrink, Sun, SunMoon, SwatchBook, UserRoundPen, X } from 'lucide-vue-next';
export { Icon as IconifyIcon, IconifyIcon as IconifyIconStructure, addCollection, addIcon, listIcons } from '@iconify/vue';

declare function createIconifyIcon(icon: string): vue.DefineComponent<{}, () => vue.VNode<vue.RendererNode, vue.RendererElement, {
    [key: string]: any;
}>, {}, {}, {}, vue.ComponentOptionsMixin, vue.ComponentOptionsMixin, {}, string, vue.PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, vue.ComponentProvideOptions, true, {}, any>;

export { createIconifyIcon };
