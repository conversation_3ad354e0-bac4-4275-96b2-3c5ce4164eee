import { arcgisToGeoJSON } from '@esri/arcgis-to-geojson-utils';
import * as Cesium from 'cesium';

import * as api from '../../../api/index.js';
import GPConfig from '../../../config/GPConfig';
import { executeTask } from './GPExecute';

export default class BufferAnalysis {
  constructor(params) {
    this.viewer = params.that._viewer;
    this.gp = params.gp || GPConfig.buffer;
  }
  async execute(options) {
    this.remove();
    const viewer = this.viewer;
    if (options.positions) {
      const positions = options.positions.map((p) => [p.lng, p.lat]);
      options.filter = JSON.stringify({
        objectIdFieldName: 'objectid',
        globalIdFieldName: '',
        geometryType: 'esriGeometryPolygon',
        spatialReference: {
          wkid: 4326,
          latestWkid: 4326,
        },
        fields: [],
        features: [
          {
            geometry: {
              rings: [positions],
            },
          },
        ],
      });
    } else if (options.filter) {
      options.filter = JSON.stringify({
        objectIdFieldName: 'objectid',
        globalIdFieldName: '',
        geometryType: 'esriGeometryPolygon',
        spatialReference: {
          wkid: 4326,
          latestWkid: 4326,
        },
        fields: [],
        features: options.filter,
      });
    }

    options = {
      layerInfo: null,
      distance: 10,
      ...options,
    };
    const gpUrls = await api.getGpServiceToken({
      gpName: 'buffer',
      layerIdList: [options.layerInfo.key],
    });
    const token1 = gpUrls.layers[0].slice(
      gpUrls.layers[0].lastIndexOf('=') + 1,
    );
    // let url = (options.layerInfo.data.url.trim('/') + '/').replace(/\/mapserver\//ig, '/FeatureServer/') + options.layerInfo.data.layers;
    const url = `${options.layerInfo.data.url.trim('/')}/${
      options.layerInfo.data.layers
    }`;
    const params = {
      inputFeature: JSON.stringify({
        url,
        token: token1,
      }),
      filterFeature: options.filter,
      distance: JSON.stringify({
        distance: options.distance,
        units: 'esriMeters',
      }),
      'env:outSR': 4326,
      'env:processSR': 4326,
      f: 'json',
    };
    const res = await executeTask(this.gp, params);
    console.log(res);
    const result = res.results[0].value;
    // delete result.crs
    // delete result.exceededTransferLimit
    const json = arcgisToGeoJSON(result);
    console.log(json);
    this.dataSource = await Cesium.GeoJsonDataSource.load(json, {
      clampToGround: true,
    });
    this.dataSource.name = options.layerInfo.key;
    viewer.dataSources.add(this.dataSource);
    // viewer.flyTo(this.dataSource)
  }
  remove() {
    const viewer = this.viewer;
    if (this.dataSource) {
      viewer.dataSources.remove(this.dataSource);
    }
  }
}
