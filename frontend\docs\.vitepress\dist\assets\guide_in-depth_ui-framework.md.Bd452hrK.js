import{ao as e,k as o,aP as a,l as d,ay as i,j as l}from"./chunks/framework.C8U7mBUf.js";const c=JSON.parse('{"title":"组件库切换","description":"","frontmatter":{},"headers":[],"relativePath":"guide/in-depth/ui-framework.md","filePath":"guide/in-depth/ui-framework.md"}');const t=e({name:"guide/in-depth/ui-framework.md"},[["render",function(e,c,t,n,r,s){const p=i("NolebaseGitContributors"),u=i("NolebaseGitChangelog");return l(),o("div",null,[c[0]||(c[0]=a('<h1 id="组件库切换" tabindex="-1">组件库切换 <a class="header-anchor" href="#组件库切换" aria-label="Permalink to &quot;组件库切换&quot;">​</a></h1><p><code>Vue Admin</code> 支持你自由选择组件库，目前演示站点的默认组件库是 <code>Ant Design Vue</code>，与旧版本保持一致。同时框架还内置了 <code>Element Plus</code> 版本和 <code>Naive UI</code> 版本，你可以根据自己的喜好选择。</p><h2 id="新增组件库应用" tabindex="-1">新增组件库应用 <a class="header-anchor" href="#新增组件库应用" aria-label="Permalink to &quot;新增组件库应用&quot;">​</a></h2><p>如果你想用其他别的组件库，你只需要按一下步骤进行操作：</p><ol><li>在<code>apps</code>内创建一个新的文件夹，例如<code>apps/web-xxx</code>。</li><li>更改<code>apps/web-xxx/package.json</code>的<code>name</code>字段为<code>web-xxx</code>。</li><li>移除其他组件库依赖及代码，并用你的组件库进行替换相应逻辑，需要改动的地方不多。</li><li>调整<code>locales</code>内的语言文件。</li><li>调整 <code>app.vue</code> 内的组件。</li><li>自行适配组件库的主题，与 <code>Vben Admin</code> 契合。</li><li>调整 <code>.env</code> 内的应用名</li><li>在大仓根目录增加 <code>dev:xxx</code> 脚本</li><li>执行 <code>pnpm install</code> 安装依赖</li></ol>',5)),d(p),d(u)])}]]);export{c as __pageData,t as default};
