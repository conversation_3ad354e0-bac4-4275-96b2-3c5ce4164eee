import request from '../query/arcgisRequest';

export function executeTask(url, params) {
  return request({
    url,
    method: 'post',
    params,
  });
}

export function executeJob(url, params, change) {
  return new Promise(async (resolve, reject) => {
    let job = await request({
      url: `${url.trim('/')}/submitJob`,
      method: 'post',
      params,
    });

    const getJobStatus = async () => {
      if (!job) return;

      if (job.jobStatus == 'esriJobSucceeded') {
        // http://<job-url>/results/<param-name>
        resolve(
          await request({
            url: `${url.trim('/')}/jobs/${job.jobId}/${
              job.results.result.paramUrl
            }`,
            method: 'get',
            params: { f: 'json' },
          }),
        );
      } else if (job.jobStatus == 'esriJobFailed') {
        if (change) change('failed');
      } else if (job.jobStatus == 'esriJobCancelled') {
        if (change) change('cancelled');
      } else {
        job = await request({
          url: `${url.trim('/')}/jobs/${job.jobId}`,
          method: 'get',
          params: { f: 'json' },
        });
        await new Promise((resolve) => setTimeout(resolve, 3000));
        getJobStatus();
      }
    };
    getJobStatus();
  });

  // http://<gp-task-url>/cancel
}
