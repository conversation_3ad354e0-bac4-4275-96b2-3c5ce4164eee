import{u as k}from"./form-DnT3S1ma.js";import{by as p,B as i,bS as C}from"./bootstrap-DShsrVit.js";import{C as B}from"./index-B_b7xM74.js";import{_ as w}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as $,af as g,ag as v,ah as o,a3 as e,n,an as u}from"../jse/index-index-BMh_AyeW.js";const P=$({__name:"api",setup(A){const[b,l]=k({commonConfig:{componentProps:{class:"w-full"}},handleSubmit:S,layout:"horizontal",schema:[{component:"Input",componentProps:{placeholder:"请输入用户名"},fieldName:"field1",label:"field1"},{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"field2",label:"field2"},{component:"Select",componentProps:{allowClear:!0,filterOption:!0,options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}],placeholder:"请选择",showSearch:!0},fieldName:"fieldOptions",label:"下拉选"}],wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"});function S(r){p.success({content:`form values: ${JSON.stringify(r)}`})}function a(r){switch(r){case"batchAddSchema":{l.setState(t=>{var f;const s=(f=t==null?void 0:t.schema)!=null?f:[],d=[];for(let m=0;m<3;m++)d.push({component:"Input",componentProps:{placeholder:"请输入"},fieldName:`field${m}${Date.now()}`,label:"field+"});return{schema:[...s,...d]}});break}case"batchDeleteSchema":{l.setState(t=>{var d;return{schema:((d=t==null?void 0:t.schema)!=null?d:[]).slice(0,-3)}});break}case"disabled":{l.setState({commonConfig:{disabled:!0}});break}case"hiddenAction":{l.setState({showDefaultActions:!1});break}case"hiddenResetButton":{l.setState({resetButtonOptions:{show:!1}});break}case"hiddenSubmitButton":{l.setState({submitButtonOptions:{show:!1}});break}case"labelWidth":{l.setState({commonConfig:{labelWidth:150}});break}case"resetDisabled":{l.setState({commonConfig:{disabled:!1}});break}case"resetLabelWidth":{l.setState({commonConfig:{labelWidth:100}});break}case"showAction":{l.setState({showDefaultActions:!0});break}case"showResetButton":{l.setState({resetButtonOptions:{show:!0}});break}case"showSubmitButton":{l.setState({submitButtonOptions:{show:!0}});break}case"updateActionAlign":{l.setState({actionWrapperClass:"text-center"});break}case"updateResetButton":{l.setState({resetButtonOptions:{disabled:!0}});break}case"updateSchema":{l.updateSchema([{componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"},{label:"选项3",value:"3"}]},fieldName:"fieldOptions"}]),p.success("字段 `fieldOptions` 下拉选项更新成功。");break}case"updateSubmitButton":{l.setState({submitButtonOptions:{loading:!0}});break}}}return(r,t)=>(g(),v(e(w),{description:"表单组件api操作示例。",title:"表单组件"},{default:o(()=>[n(e(C),{class:"mb-5 flex-wrap"},{default:o(()=>[n(e(i),{onClick:t[0]||(t[0]=s=>a("updateSchema"))},{default:o(()=>t[16]||(t[16]=[u("updateSchema")])),_:1}),n(e(i),{onClick:t[1]||(t[1]=s=>a("labelWidth"))},{default:o(()=>t[17]||(t[17]=[u("更改labelWidth")])),_:1}),n(e(i),{onClick:t[2]||(t[2]=s=>a("resetLabelWidth"))},{default:o(()=>t[18]||(t[18]=[u("还原labelWidth")])),_:1}),n(e(i),{onClick:t[3]||(t[3]=s=>a("disabled"))},{default:o(()=>t[19]||(t[19]=[u("禁用表单")])),_:1}),n(e(i),{onClick:t[4]||(t[4]=s=>a("resetDisabled"))},{default:o(()=>t[20]||(t[20]=[u("解除禁用")])),_:1}),n(e(i),{onClick:t[5]||(t[5]=s=>a("hiddenAction"))},{default:o(()=>t[21]||(t[21]=[u("隐藏操作按钮")])),_:1}),n(e(i),{onClick:t[6]||(t[6]=s=>a("showAction"))},{default:o(()=>t[22]||(t[22]=[u("显示操作按钮")])),_:1}),n(e(i),{onClick:t[7]||(t[7]=s=>a("hiddenResetButton"))},{default:o(()=>t[23]||(t[23]=[u("隐藏重置按钮")])),_:1}),n(e(i),{onClick:t[8]||(t[8]=s=>a("showResetButton"))},{default:o(()=>t[24]||(t[24]=[u("显示重置按钮")])),_:1}),n(e(i),{onClick:t[9]||(t[9]=s=>a("hiddenSubmitButton"))},{default:o(()=>t[25]||(t[25]=[u("隐藏提交按钮")])),_:1}),n(e(i),{onClick:t[10]||(t[10]=s=>a("showSubmitButton"))},{default:o(()=>t[26]||(t[26]=[u("显示提交按钮")])),_:1}),n(e(i),{onClick:t[11]||(t[11]=s=>a("updateResetButton"))},{default:o(()=>t[27]||(t[27]=[u("修改重置按钮")])),_:1}),n(e(i),{onClick:t[12]||(t[12]=s=>a("updateSubmitButton"))},{default:o(()=>t[28]||(t[28]=[u("修改提交按钮")])),_:1}),n(e(i),{onClick:t[13]||(t[13]=s=>a("updateActionAlign"))},{default:o(()=>t[29]||(t[29]=[u(" 调整操作按钮位置 ")])),_:1}),n(e(i),{onClick:t[14]||(t[14]=s=>a("batchAddSchema"))},{default:o(()=>t[30]||(t[30]=[u(" 批量添加表单项 ")])),_:1}),n(e(i),{onClick:t[15]||(t[15]=s=>a("batchDeleteSchema"))},{default:o(()=>t[31]||(t[31]=[u(" 批量删除表单项 ")])),_:1})]),_:1}),n(e(B),{title:"操作示例"},{default:o(()=>[n(e(b))]),_:1})]),_:1}))}});export{P as default};
