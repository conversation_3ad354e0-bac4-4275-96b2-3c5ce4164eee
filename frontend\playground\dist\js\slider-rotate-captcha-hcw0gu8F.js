var j=Object.defineProperty;var D=Object.getOwnPropertySymbols;var U=Object.prototype.hasOwnProperty,Z=Object.prototype.propertyIsEnumerable;var w=(o,s,r)=>s in o?j(o,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[s]=r,$=(o,s)=>{for(var r in s||(s={}))U.call(s,r)&&w(o,r,s[r]);if(D)for(var r of D(s))Z.call(s,r)&&w(o,r,s[r]);return o};import{$ as _,bz as A,o as J,by as X}from"./bootstrap-DShsrVit.js";import{a4 as C,a$ as M,b1 as q,W as G,b0 as H,V as K,J as u,af as f,am as T,ap as g,aZ as V,ar as k,ao as B,al as z,a3 as c,n as b,b3 as Q,as as Y,ah as R,ae as ee,ai as te,aj as ae,b2 as se,ag as re,a0 as oe}from"../jse/index-index-BMh_AyeW.js";import{C as ne}from"./index-B_b7xM74.js";import{_ as ie}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";const le={class:"relative flex flex-col items-center"},ce=["src"],de={class:"absolute bottom-3 left-0 z-10 block h-7 w-full text-center text-xs leading-[30px] text-white"},ue={key:1,class:"bg-black/30"},me=C({__name:"index",props:M({diffDegree:{default:20},imageSize:{default:260},imageWrapperStyle:{},maxDegree:{default:300},minDegree:{default:120},src:{default:""},defaultTip:{default:""}},{modelValue:{type:Boolean,default:!1},modelModifiers:{}}),emits:M(["success"],["update:modelValue"]),setup(o,{expose:s,emit:r}){const i=o,d=r,p=q("slideBarRef"),e=G({currentRotate:0,dragging:!1,endTime:0,imgStyle:{},isPassing:!1,randomRotate:0,showTip:!1,startTime:0,toOrigin:!1}),h=H(o,"modelValue");K(()=>e.isPassing,t=>{if(t){const{endTime:a,startTime:n}=e,l=(a-n)/1e3;d("success",{isPassing:t,time:l.toFixed(1)})}h.value=t});const P=u(()=>{const{imageSize:t,imageWrapperStyle:a}=i;return $({height:`${t}px`,width:`${t}px`},a)}),F=u(()=>{const{maxDegree:t,minDegree:a}=i;return a>t&&console.warn("minDegree should not be greater than maxDegree"),a===t?Math.floor(1+Math.random()*1)/10+1:1});function O(){e.startTime=Date.now()}function E(t){e.dragging=!0;const{imageSize:a,maxDegree:n}=i,{moveX:l}=t,m=a;if(m===0)return;const y=Math.ceil(l/m*1.5*n*c(F));e.currentRotate=y,v(e.randomRotate-y)}function S(){const{maxDegree:t,minDegree:a}=i,n=Math.floor(a+Math.random()*(t-a));e.randomRotate=n,v(n)}function I(){const{currentRotate:t,randomRotate:a}=e,{diffDegree:n}=i;Math.abs(a-t)>=(n||20)?(v(a),e.toOrigin=!0,se(()=>{e.toOrigin=!1,e.showTip=!0},300)):W(),e.showTip=!0,e.dragging=!1}function v(t){e.imgStyle={transform:`rotateZ(${t}deg)`}}function W(){e.isPassing=!0,e.endTime=Date.now()}function x(){e.showTip=!1;const t=c(p);t&&(e.isPassing=!1,t.resume(),S())}const L=u(()=>e.toOrigin?["transition-transform duration-300"]:[]),N=u(()=>e.isPassing?_("ui.captcha.sliderRotateSuccessTip",[((e.endTime-e.startTime)/1e3).toFixed(1)]):_("ui.captcha.sliderRotateFailTip"));return s({resume:x}),(t,a)=>(f(),T("div",le,[g("div",{style:k(P.value),class:"border-border relative cursor-pointer overflow-hidden rounded-full border shadow-md"},[g("img",{class:V([L.value,"w-full rounded-full"]),src:t.src,style:k(e.imgStyle),alt:"verify",onClick:x,onLoad:S},null,46,ce),g("div",de,[e.showTip?(f(),T("div",{key:0,class:V({"bg-success/80":e.isPassing,"bg-destructive/80":!e.isPassing})},B(N.value),3)):z("",!0),e.dragging?z("",!0):(f(),T("div",ue,B(t.defaultTip||c(_)("ui.captcha.sliderRotateDefaultTip")),1))])],4),b(A,{ref_key:"slideBarRef",ref:p,modelValue:h.value,"onUpdate:modelValue":a[0]||(a[0]=n=>h.value=n),class:"mt-5","is-slot":"",onEnd:I,onMove:E,onStart:O},Q({_:2},[Y(t.$slots,(n,l)=>({name:l,fn:R(m=>[ee(t.$slots,l,te(ae(m)))])}))]),1032,["modelValue"])]))}}),fe={class:"flex items-center justify-center p-4"},Te=C({__name:"slider-rotate-captcha",setup(o){const s=J();function r(){X.success("success!")}const i=u(()=>{var d;return((d=s.userInfo)==null?void 0:d.avatar)||oe.app.defaultAvatar});return(d,p)=>(f(),re(c(ie),{description:"用于前端简单的拖动校验场景",title:"滑块旋转校验"},{default:R(()=>[b(c(ne),{class:"mb-5",title:"基本示例"},{default:R(()=>[g("div",fe,[b(c(me),{src:i.value,onSuccess:r},null,8,["src"])])]),_:1})]),_:1}))}});export{Te as default};
