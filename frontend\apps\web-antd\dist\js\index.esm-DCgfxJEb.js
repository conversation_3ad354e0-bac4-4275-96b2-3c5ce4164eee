function N(r,t){t===void 0&&(t={});var e=t.insertAt;if(typeof document!="undefined"){var i=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css",e==="top"&&i.firstChild?i.insertBefore(a,i.firstChild):i.appendChild(a),a.styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r))}}var F="@keyframes watermark{0%{background-position:0 0}25%{background-position:100% 100%}50%{background-position:100% 0}75%{background-position:0 100%}to{background-position:0 0}}@keyframes watermark-horizontal{0%{background-position-x:0}to{background-position-x:100%}}@keyframes watermark-vertical{0%{background-position-y:0}to{background-position-y:100%}}";N(F);var R=function(r,t){return R=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(e[a]=i[a])},R(r,t)};function X(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");R(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var k=function(){return k=Object.assign||function(t){for(var e,i=1,a=arguments.length;i<a;i++){e=arguments[i];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}return t},k.apply(this,arguments)};function S(r,t,e,i){function a(n){return n instanceof e?n:new e(function(o){o(n)})}return new(e||(e=Promise))(function(n,o){function l(c){try{s(i.next(c))}catch(h){o(h)}}function d(c){try{s(i.throw(c))}catch(h){o(h)}}function s(c){c.done?n(c.value):a(c.value).then(l,d)}s((i=i.apply(r,t||[])).next())})}function b(r,t){var e={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},i,a,n,o=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return o.next=l(0),o.throw=l(1),o.return=l(2),typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function l(s){return function(c){return d([s,c])}}function d(s){if(i)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(e=0)),e;)try{if(i=1,a&&(n=s[0]&2?a.return:s[0]?a.throw||((n=a.return)&&n.call(a),0):a.next)&&!(n=n.call(a,s[1])).done)return n;switch(a=0,n&&(s=[s[0]&2,n.value]),s[0]){case 0:case 1:n=s;break;case 4:return e.label++,{value:s[1],done:!1};case 5:e.label++,a=s[1],s=[0];continue;case 7:s=e.ops.pop(),e.trys.pop();continue;default:if(n=e.trys,!(n=n.length>0&&n[n.length-1])&&(s[0]===6||s[0]===2)){e=0;continue}if(s[0]===3&&(!n||s[1]>n[0]&&s[1]<n[3])){e.label=s[1];break}if(s[0]===6&&e.label<n[1]){e.label=n[1],n=s;break}if(n&&e.label<n[2]){e.label=n[2],e.ops.push(s);break}n[2]&&e.ops.pop(),e.trys.pop();continue}s=t.call(r,e)}catch(c){s=[6,c],a=0}finally{i=n=0}if(s[0]&5)throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}}var q=function(r){return r.toDataURL("image/png",1)},V=function(r){return typeof r=="function"},x=function(r){return r===void 0},Y=function(r){return typeof r=="string"},z=function(r,t,e){t===void 0&&(t={}),e===void 0&&(e="http://www.w3.org/2000/svg");var i=document.createElementNS(e,r);for(var a in t)i.setAttribute(a,t[a]);return i},K=function(r,t,e){for(var i=[],a="",n="",o=0,l=t.length;o<l;o++){if(n=t.charAt(o),n===`
`){i.push(a),a="";continue}a+=n,r.measureText(a).width>e&&(i.push(a.substring(0,a.length-1)),a="",o--)}return i.push(a),i},U=function(r,t){return S(void 0,void 0,void 0,function(){var e,i,a,n,o,l,d,s,c;return b(this,function(h){switch(h.label){case 0:return e=z("svg",{xmlns:"http://www.w3.org/2000/svg"}),i=document.createElement("div"),i.setAttribute("xmlns","http://www.w3.org/1999/xhtml"),i.style.cssText=`
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font: `.concat(r.font,`;
  color: `).concat(t.fontColor,`;
`),i.innerHTML="<div class='rich-text-content'>".concat(t.content,"</div>"),document.body.appendChild(i),[4,$(i)];case 1:return h.sent(),a=(c=i.querySelector(".rich-text-content"))===null||c===void 0?void 0:c.getBoundingClientRect(),n=a==null?void 0:a.width,o=a==null?void 0:a.height,document.body.removeChild(i),l=t.richTextWidth||n||t.width,d=t.richTextHeight||o||t.height,e.setAttribute("width",l.toString()),e.setAttribute("height",d.toString()),s=z("foreignObject",{width:l.toString(),height:d.toString()}),s.appendChild(i),e.appendChild(s),[2,{element:e,width:l,height:d}]}})})};function $(r){return S(this,void 0,void 0,function(){var t,e,i,a,n;return b(this,function(o){switch(o.label){case 0:t=r.querySelectorAll("img"),e=function(l){var d,s,c,h,v;return b(this,function(u){switch(u.label){case 0:if(d=l.getAttribute("src"),!d)return[3,6];u.label=1;case 1:return u.trys.push([1,5,,6]),[4,fetch(d)];case 2:return s=u.sent(),[4,s.blob()];case 3:return c=u.sent(),[4,new Promise(function(m,p){var f=new FileReader;f.onloadend=function(){return m(f.result)},f.onerror=p,f.readAsDataURL(c)})];case 4:return h=u.sent(),Y(h)&&l.setAttribute("src",h),[3,6];case 5:return v=u.sent(),console.error("Error converting ".concat(d," to base64:"),v),[3,6];case 6:return[2]}})},i=0,a=Array.from(t),o.label=1;case 1:return i<a.length?(n=a[i],[5,e(n)]):[3,4];case 2:o.sent(),o.label=3;case 3:return i++,[3,1];case 4:return[2]}})})}var J=function(r){var t=r.outerHTML.replace(/<(img|br|input|hr|embed)(.*?)>/g,"<$1$2/>").replace(/\n/g,"").replace(/\t/g,"").replace(/#/g,"%23");return"data:image/svg+xml;charset=utf-8,".concat(t)},y=function(r,t){return x(r)?t:r},B=function(r,t,e){t===void 0&&(t=void 0),e===void 0&&(e=void 0);var i=new Image;return i.setAttribute("crossOrigin","anonymous"),!x(t)&&(i.width=t),!x(e)&&(i.height=e),i.src=r,new Promise(function(a){i.onload=function(){a(i)}})},Q=function(r,t,e){return Array.from({length:r},function(){return new Array(t).fill(e)})},Z=function(r,t){if(!r)return"";var e=Math.random()*6+2,i=Math.random()*2+2;switch(t){case"repeat":return"animation: 200s linear 0s infinite alternate watermark !important;";case"repeat-x":return"animation: ".concat(e,"s ease-in 0s infinite alternate watermark-vertical !important;'");case"repeat-y":return"animation: ".concat(i,"s ease-out 0s infinite alternate watermark-horizontal !important;'");case"no-repeat":return"animation: ".concat(e,"s ease-in 0s infinite alternate watermark-horizontal, ").concat(i,"s ease-out 0s infinite alternate watermark-vertical !important;");default:return""}},G={width:300,height:300,rotate:45,layout:"default",auxiliaryLine:!1,translatePlacement:"middle",contentType:"text",content:"hello watermark-js-plus",textType:"fill",imageWidth:0,imageHeight:0,lineHeight:30,zIndex:2147483647,backgroundPosition:"0 0",backgroundRepeat:"repeat",fontSize:"20px",fontFamily:"sans-serif",fontStyle:"",fontVariant:"",fontColor:"#000",fontWeight:"normal",filter:"none",letterSpacing:"0px",wordSpacing:"0px",globalAlpha:.5,mode:"default",mutationObserve:!0,monitorProtection:!1,movable:!1,parent:"body",onSuccess:function(){},onBeforeDestroy:function(){},onDestroyed:function(){},onObserveError:function(){}},tt=function(r,t,e){var i=r.getContext("2d");if(i===null)throw new Error("get context error");i.font="".concat(t.fontStyle," ").concat(t.fontVariant," ").concat(t.fontWeight," ").concat(t.fontSize," ").concat(t.fontFamily),i.filter=t.filter,i.letterSpacing=t.letterSpacing,i.wordSpacing=t.wordSpacing,t!=null&&t.rotate&&(t.rotate=(360-t.rotate%360)*(Math.PI/180)),x(e.textRowMaxWidth)&&(t.textRowMaxWidth=t.width);var a={image:{rect:{width:t.imageWidth,height:t.imageHeight},position:{x:0,y:0}},textLine:{data:[],yOffsetValue:0},advancedStyleParams:{linear:{x0:0,x1:0},radial:{x0:0,y0:0,r0:0,x1:0,y1:0,r1:0},conic:{x:0,y:0,startAngle:0},pattern:{}}};switch(t.contentType){case"text":a.textLine.data=[t.content];break;case"multi-line-text":a.textLine.data=K(i,t.content,t.textRowMaxWidth);break}var n=t.width/2,o=t.height/2,l="middle",d="center";switch(!x(e==null?void 0:e.translateX)&&!x(e==null?void 0:e.translateY)?(n=e==null?void 0:e.translateX,o=e==null?void 0:e.translateY,l="top",d="left"):(a.advancedStyleParams.linear.x0=-t.width/2,a.advancedStyleParams.linear.x1=t.width/2,a.advancedStyleParams.radial.r0=0,a.advancedStyleParams.radial.r1=t.width/2),e.translatePlacement){case"top":n=t.width/2,o=0,l="top",a.advancedStyleParams.linear.x0=-t.width/2,a.advancedStyleParams.linear.x1=t.width/2,a.advancedStyleParams.radial.y0=a.textLine.data.length*t.lineHeight/2,a.advancedStyleParams.radial.y1=a.textLine.data.length*t.lineHeight/2,a.advancedStyleParams.conic.y=a.textLine.data.length*t.lineHeight/2;break;case"top-start":n=0,o=0,l="top",d="start",a.advancedStyleParams.linear.x0=0,a.advancedStyleParams.linear.x1=t.width,a.advancedStyleParams.radial.x0=t.width/2,a.advancedStyleParams.radial.y0=a.textLine.data.length*t.lineHeight/2,a.advancedStyleParams.radial.x1=t.width/2,a.advancedStyleParams.radial.y1=a.textLine.data.length*t.lineHeight/2,a.advancedStyleParams.conic.x=t.width/2,a.advancedStyleParams.conic.y=a.textLine.data.length*t.lineHeight/2;break;case"top-end":n=t.width,o=0,l="top",d="end",a.advancedStyleParams.linear.x0=0,a.advancedStyleParams.linear.x1=-t.width,a.advancedStyleParams.radial.x0=-t.width/2,a.advancedStyleParams.radial.y0=a.textLine.data.length*t.lineHeight/2,a.advancedStyleParams.radial.x1=-t.width/2,a.advancedStyleParams.radial.y1=a.textLine.data.length*t.lineHeight/2,a.advancedStyleParams.conic.x=-t.width/2,a.advancedStyleParams.conic.y=a.textLine.data.length*t.lineHeight/2;break;case"bottom":n=t.width/2,o=t.height,l="bottom",a.advancedStyleParams.linear.x0=-t.width/2,a.advancedStyleParams.linear.x1=t.width/2,a.advancedStyleParams.radial.y0=-a.textLine.data.length*t.lineHeight/2,a.advancedStyleParams.radial.y1=-a.textLine.data.length*t.lineHeight/2,a.advancedStyleParams.conic.x=0,a.advancedStyleParams.conic.y=-a.textLine.data.length*t.lineHeight/2;break;case"bottom-start":n=0,o=t.height,l="bottom",d="start",a.advancedStyleParams.linear.x0=0,a.advancedStyleParams.linear.x1=t.width,a.advancedStyleParams.radial.x0=t.width/2,a.advancedStyleParams.radial.y0=-a.textLine.data.length*t.lineHeight/2,a.advancedStyleParams.radial.x1=t.width/2,a.advancedStyleParams.radial.y1=-a.textLine.data.length*t.lineHeight/2,a.advancedStyleParams.conic.x=t.width/2,a.advancedStyleParams.conic.y=-a.textLine.data.length*t.lineHeight/2;break;case"bottom-end":n=t.width,o=t.height,l="bottom",d="end",a.advancedStyleParams.linear.x0=0,a.advancedStyleParams.linear.x1=-t.width,a.advancedStyleParams.radial.x0=-t.width/2,a.advancedStyleParams.radial.y0=-a.textLine.data.length*t.lineHeight/2,a.advancedStyleParams.radial.x1=-t.width/2,a.advancedStyleParams.radial.y1=-a.textLine.data.length*t.lineHeight/2,a.advancedStyleParams.conic.x=-t.width/2,a.advancedStyleParams.conic.y=-a.textLine.data.length*t.lineHeight/2;break;case"left":n=0,o=t.height/2,d="start",a.advancedStyleParams.linear.x0=0,a.advancedStyleParams.linear.x1=t.width,a.advancedStyleParams.radial.x0=t.width/2,a.advancedStyleParams.radial.x1=t.width/2,a.advancedStyleParams.conic.x=t.width/2,a.advancedStyleParams.conic.y=0;break;case"right":n=t.width,o=t.height/2,d="end",a.advancedStyleParams.linear.x0=0,a.advancedStyleParams.linear.x1=-t.width,a.advancedStyleParams.radial.x0=-t.width/2,a.advancedStyleParams.radial.x1=-t.width/2,a.advancedStyleParams.conic.x=-t.width/2,a.advancedStyleParams.conic.y=0;break}if(t.translateX=n,t.translateY=o,x(e==null?void 0:e.textBaseline)&&(t.textBaseline=l),x(e==null?void 0:e.textAlign)&&(t.textAlign=d),["text","multi-line-text"].includes(t.contentType))switch(t.textBaseline){case"middle":a.textLine.yOffsetValue=(a.textLine.data.length-1)*t.lineHeight/2;break;case"bottom":case"alphabetic":case"ideographic":a.textLine.yOffsetValue=(a.textLine.data.length-1)*t.lineHeight+(t.lineHeight-parseInt(t.fontSize))/2;break;case"top":case"hanging":a.textLine.yOffsetValue=-t.lineHeight/2+parseInt(t.fontSize)/2;break}return a},I=function(r){typeof window!="undefined"&&r&&(Object.defineProperty(window,"MutationObserver",{writable:!1,configurable:!1}),Object.defineProperty(window,"requestAnimationFrame",{writable:!1,configurable:!1}))},C=function(){function r(t,e){this.props=t,this.options=e,this.canvas=r.createCanvas(this.options.width,this.options.height),this.recommendOptions=tt(this.canvas,this.options,this.props)}return r.createCanvas=function(t,e){var i,a=window.devicePixelRatio||1,n=document.createElement("canvas");return n.width=t*a,n.height=e*a,n.style.width="".concat(t,"px"),n.style.height="".concat(e,"px"),(i=n.getContext("2d"))===null||i===void 0||i.setTransform(a,0,0,a,0,0),n},r.clearCanvas=function(t){var e=t.getContext("2d");if(e===null)throw new Error("get context error");e.restore(),e.resetTransform(),e.clearRect(0,0,t.width,t.height);var i=window.devicePixelRatio||1;e.setTransform(i,0,0,i,0,0)},r.prototype.getCanvas=function(){return this.canvas},r.prototype.clear=function(){r.clearCanvas(this.canvas)},r.prototype.draw=function(){var t=this,e=this.canvas.getContext("2d");if(e===null)throw new Error("get context error");return this.options.auxiliaryLine&&(e.beginPath(),e.rect(0,0,this.options.width,this.options.height),e.lineWidth=1,e.strokeStyle="#000",e.stroke(),e.closePath(),e.beginPath(),e.rect(this.options.translateX,this.options.translateY,1,1),e.lineWidth=1,e.strokeStyle="#f00",e.stroke(),e.closePath()),this.setStyle(e),e.save(),e.translate(this.options.translateX,this.options.translateY),e.rotate(this.options.rotate),new Promise(function(i){switch(t.options.contentType){case"text":t.drawText(e,i);break;case"image":t.drawImage(e,i);break;case"multi-line-text":t.drawMultiLineText(e,i);break;case"rich-text":t.drawRichText(e,i);break}})},r.prototype.setStyle=function(t){var e,i="fillStyle";this.options.textType==="stroke"&&(i="strokeStyle");var a=this.options.fontColor;if(!((e=this.options)===null||e===void 0)&&e.advancedStyle)switch(this.options.advancedStyle.type){case"linear":a=this.createLinearGradient(t);break;case"radial":a=this.createRadialGradient(t);break;case"conic":a=this.createConicGradient(t);break;case"pattern":a=this.createPattern(t);break}t[i]&&a&&(t[i]=a),this.options.textAlign&&(t.textAlign=this.options.textAlign),this.options.textBaseline&&(t.textBaseline=this.options.textBaseline),t.globalAlpha=this.options.globalAlpha,this.options.shadowStyle&&(t.shadowBlur=y(this.options.shadowStyle.shadowBlur,0),t.shadowColor=y(this.options.shadowStyle.shadowColor,"#00000000"),t.shadowOffsetX=y(this.options.shadowStyle.shadowOffsetX,0),t.shadowOffsetY=y(this.options.shadowStyle.shadowOffsetY,0)),V(this.options.extraDrawFunc)&&this.options.extraDrawFunc(t)},r.prototype.createLinearGradient=function(t){var e,i,a,n,o,l,d,s,c,h,v,u,m,p,f,w=t.createLinearGradient(y((a=(i=(e=this.options.advancedStyle)===null||e===void 0?void 0:e.params)===null||i===void 0?void 0:i.linear)===null||a===void 0?void 0:a.x0,this.recommendOptions.advancedStyleParams.linear.x0),y((l=(o=(n=this.options.advancedStyle)===null||n===void 0?void 0:n.params)===null||o===void 0?void 0:o.linear)===null||l===void 0?void 0:l.y0,0),y((c=(s=(d=this.options.advancedStyle)===null||d===void 0?void 0:d.params)===null||s===void 0?void 0:s.linear)===null||c===void 0?void 0:c.x1,this.recommendOptions.advancedStyleParams.linear.x1),y((u=(v=(h=this.options.advancedStyle)===null||h===void 0?void 0:h.params)===null||v===void 0?void 0:v.linear)===null||u===void 0?void 0:u.y1,0));return(f=(p=(m=this.options)===null||m===void 0?void 0:m.advancedStyle)===null||p===void 0?void 0:p.colorStops)===null||f===void 0||f.forEach(function(g){w.addColorStop(g.offset,g.color)}),w},r.prototype.createConicGradient=function(t){var e,i,a,n,o,l,d,s,c,h,v,u,m,p,f,w=t.createConicGradient(y((n=(a=(i=(e=this.options)===null||e===void 0?void 0:e.advancedStyle)===null||i===void 0?void 0:i.params)===null||a===void 0?void 0:a.conic)===null||n===void 0?void 0:n.startAngle,0),y((s=(d=(l=(o=this.options)===null||o===void 0?void 0:o.advancedStyle)===null||l===void 0?void 0:l.params)===null||d===void 0?void 0:d.conic)===null||s===void 0?void 0:s.x,this.recommendOptions.advancedStyleParams.conic.x),y((u=(v=(h=(c=this.options)===null||c===void 0?void 0:c.advancedStyle)===null||h===void 0?void 0:h.params)===null||v===void 0?void 0:v.conic)===null||u===void 0?void 0:u.y,this.recommendOptions.advancedStyleParams.conic.y));return(f=(p=(m=this.options)===null||m===void 0?void 0:m.advancedStyle)===null||p===void 0?void 0:p.colorStops)===null||f===void 0||f.forEach(function(g){w.addColorStop(g.offset,g.color)}),w},r.prototype.createRadialGradient=function(t){var e,i,a,n,o,l,d,s,c,h,v,u,m,p,f,w,g,_,P,O,E,L,T,D,A,W,H,j=t.createRadialGradient(y((n=(a=(i=(e=this.options)===null||e===void 0?void 0:e.advancedStyle)===null||i===void 0?void 0:i.params)===null||a===void 0?void 0:a.radial)===null||n===void 0?void 0:n.x0,this.recommendOptions.advancedStyleParams.radial.x0),y((s=(d=(l=(o=this.options)===null||o===void 0?void 0:o.advancedStyle)===null||l===void 0?void 0:l.params)===null||d===void 0?void 0:d.radial)===null||s===void 0?void 0:s.y0,this.recommendOptions.advancedStyleParams.radial.y0),y((u=(v=(h=(c=this.options)===null||c===void 0?void 0:c.advancedStyle)===null||h===void 0?void 0:h.params)===null||v===void 0?void 0:v.radial)===null||u===void 0?void 0:u.r0,this.recommendOptions.advancedStyleParams.radial.r0),y((w=(f=(p=(m=this.options)===null||m===void 0?void 0:m.advancedStyle)===null||p===void 0?void 0:p.params)===null||f===void 0?void 0:f.radial)===null||w===void 0?void 0:w.x1,this.recommendOptions.advancedStyleParams.radial.x1),y((O=(P=(_=(g=this.options)===null||g===void 0?void 0:g.advancedStyle)===null||_===void 0?void 0:_.params)===null||P===void 0?void 0:P.radial)===null||O===void 0?void 0:O.y1,this.recommendOptions.advancedStyleParams.radial.y1),y((D=(T=(L=(E=this.options)===null||E===void 0?void 0:E.advancedStyle)===null||L===void 0?void 0:L.params)===null||T===void 0?void 0:T.radial)===null||D===void 0?void 0:D.r1,this.recommendOptions.advancedStyleParams.radial.r1));return(H=(W=(A=this.options)===null||A===void 0?void 0:A.advancedStyle)===null||W===void 0?void 0:W.colorStops)===null||H===void 0||H.forEach(function(M){j.addColorStop(M.offset,M.color)}),j},r.prototype.createPattern=function(t){var e,i,a,n,o,l,d,s;return t.createPattern((n=(a=(i=(e=this.options)===null||e===void 0?void 0:e.advancedStyle)===null||i===void 0?void 0:i.params)===null||a===void 0?void 0:a.pattern)===null||n===void 0?void 0:n.image,((s=(d=(l=(o=this.options)===null||o===void 0?void 0:o.advancedStyle)===null||l===void 0?void 0:l.params)===null||d===void 0?void 0:d.pattern)===null||s===void 0?void 0:s.repetition)||"")},r.prototype.setText=function(t,e){var i="fillText";this.options.textType==="stroke"&&(i="strokeText"),t[i]&&t[i](e.text,e.x,e.y,e.maxWidth)},r.prototype.drawText=function(t,e){this.setText(t,{text:this.options.content,x:0,y:0-this.recommendOptions.textLine.yOffsetValue,maxWidth:this.options.textRowMaxWidth||this.options.width}),e(t.canvas)},r.prototype.drawImage=function(t,e){var i=this;B(this.options.image).then(function(a){var n=i.getImageRect(a),o=n.width,l=n.height,d=i.getDrawImagePosition(o,l);t.drawImage(a,d.x,d.y,o,l),e(t.canvas)})},r.prototype.drawMultiLineText=function(t,e){var i=this,a=this.recommendOptions.textLine.data,n=this.recommendOptions.textLine.yOffsetValue;a.forEach(function(o,l){i.setText(t,{text:o,x:0,y:i.options.lineHeight*l-n,maxWidth:i.options.textRowMaxWidth||i.options.width})}),e(t.canvas)},r.prototype.drawRichText=function(t,e){return S(this,void 0,void 0,function(){var i,a=this;return b(this,function(n){switch(n.label){case 0:return[4,U(t,this.options)];case 1:return i=n.sent(),B(J(i.element),i.width,i.height).then(function(o){var l=a.getDrawImagePosition(o.width,o.height);t.drawImage(o,l.x,l.y,o.width,o.height),e(t.canvas)}),[2]}})})},r.prototype.getImageRect=function(t){var e={width:this.options.imageWidth||0,height:this.options.imageHeight||0};switch(!0){case(e.width!==0&&e.height===0):e.height=e.width*t.height/t.width;break;case(e.width===0&&e.height!==0):e.width=e.height*t.width/t.height;break;case(e.width===0&&e.height===0):e.width=t.width,e.height=t.height;break}return e},r.prototype.getDrawImagePosition=function(t,e){var i,a,n={x:-t/2,y:-e/2};switch(this.options.translatePlacement){case"top":n.x=-t/2,n.y=0;break;case"top-start":n.x=0,n.y=0;break;case"top-end":n.x=-t,n.y=0;break;case"bottom":n.x=-t/2,n.y=-e;break;case"bottom-start":n.x=0,n.y=-e;break;case"bottom-end":n.x=-t,n.y=-e;break;case"left":n.x=0,n.y=-e/2;break;case"right":n.x=-t,n.y=-e/2;break}return!x((i=this.props)===null||i===void 0?void 0:i.translateX)&&(n.x=0),!x((a=this.props)===null||a===void 0?void 0:a.translateY)&&(n.y=0),n},r}(),et=function(){function r(t,e){var i,a,n,o;this.options=t,this.partialWidth=this.options.width,this.partialHeight=this.options.height,this.rows=((i=this.options.gridLayoutOptions)===null||i===void 0?void 0:i.rows)||1,this.cols=((a=this.options.gridLayoutOptions)===null||a===void 0?void 0:a.cols)||1,this.matrix=((n=this.options.gridLayoutOptions)===null||n===void 0?void 0:n.matrix)||Q(this.rows,this.cols,1),this.gap=((o=this.options.gridLayoutOptions)===null||o===void 0?void 0:o.gap)||[0,0],this.partialCanvas=e}return r.prototype.draw=function(){var t,e,i,a,n,o,l,d,s=C.createCanvas(((t=this.options.gridLayoutOptions)===null||t===void 0?void 0:t.width)||this.partialWidth*this.cols+this.gap[0]*this.cols,((e=this.options.gridLayoutOptions)===null||e===void 0?void 0:e.height)||this.partialHeight*this.rows+this.gap[1]*this.rows),c=s.getContext("2d");!((i=this.options.gridLayoutOptions)===null||i===void 0)&&i.backgroundImage&&(c==null||c.drawImage((a=this.options.gridLayoutOptions)===null||a===void 0?void 0:a.backgroundImage,0,0,(n=this.options.gridLayoutOptions)===null||n===void 0?void 0:n.width,(o=this.options.gridLayoutOptions)===null||o===void 0?void 0:o.height));for(var h=0;h<this.rows;h++)for(var v=0;v<this.cols;v++)!((d=(l=this.matrix)===null||l===void 0?void 0:l[h])===null||d===void 0)&&d[v]&&(c==null||c.drawImage(this.partialCanvas,this.partialWidth*v+this.gap[0]*v,this.partialHeight*h+this.gap[1]*h,this.partialWidth,this.partialHeight));return s},r}(),at=function(r,t){switch(r.layout){case"grid":return new et(r,t).draw();default:return t}},it=function(r){var t,e,i;switch(r.layout){case"grid":{var a=((t=r.gridLayoutOptions)===null||t===void 0?void 0:t.cols)||1,n=((e=r.gridLayoutOptions)===null||e===void 0?void 0:e.rows)||1,o=((i=r.gridLayoutOptions)===null||i===void 0?void 0:i.gap)||[0,0];return[r.width*a+o[0]*a,r.height*n+o[1]*n]}default:return[r.width,r.height]}},nt=function(){function r(t){t===void 0&&(t={}),this.parentElement=document.body,this.isCreating=!1,this.props=t,this.options=k(k({},G),t),this.changeParentElement(this.options.parent),this.watermarkCanvas=new C(this.props,this.options),I(this.options.monitorProtection)}return r.prototype.changeOptions=function(){return S(this,arguments,void 0,function(t,e,i){return t===void 0&&(t={}),e===void 0&&(e="overwrite"),i===void 0&&(i=!0),b(this,function(a){switch(a.label){case 0:return this.initConfigData(t,e),I(this.options.monitorProtection),i?(this.remove(),[4,this.create()]):[3,2];case 1:a.sent(),a.label=2;case 2:return[2]}})})},r.prototype.create=function(){return S(this,void 0,void 0,function(){var t,e,i,a,n,o,l,d,s,c,h,v;return b(this,function(u){switch(u.label){case 0:return this.isCreating?[2]:(this.isCreating=!0,this.validateUnique()?this.validateContent()?(t=x(this.watermarkDom),[4,(o=this.watermarkCanvas)===null||o===void 0?void 0:o.draw()]):(this.isCreating=!1,[2]):(this.isCreating=!1,[2]));case 1:if(u.sent(),this.layoutCanvas=at(this.options,(l=this.watermarkCanvas)===null||l===void 0?void 0:l.getCanvas()),e=q(this.layoutCanvas),(d=this.watermarkCanvas)===null||d===void 0||d.clear(),this.watermarkDom=document.createElement("div"),i=document.createElement("div"),this.watermarkDom.__WATERMARK__="watermark",this.watermarkDom.__WATERMARK__INSTANCE__=this,a=this.checkParentElementType(),this.watermarkDom.style.cssText=`
      z-index:`.concat(this.options.zIndex,`!important;display:block!important;visibility:visible!important;transform:none!important;scale:none!important;
      `).concat(a==="custom"?"top:0!important;bottom:0!important;left:0!important;right:0!important;height:100%!important;pointer-events:none!important;position:absolute!important;":"position:relative!important;",`
    `),n=it(this.options),i.style.cssText=`
      display:block!important;visibility:visible!important;pointer-events:none;top:0;bottom:0;left:0;right:0;transform:none!important;scale:none!important;
      position:`.concat(a==="root"?"fixed":"absolute",`!important;-webkit-print-color-adjust:exact!important;width:100%!important;height:100%!important;
      z-index:`).concat(this.options.zIndex,"!important;background-image:url(").concat(e,")!important;background-repeat:").concat(this.options.backgroundRepeat,`!important;
      background-size:`).concat(n[0],"px ").concat(n[1],"px!important;background-position:").concat(this.options.backgroundPosition,`;
      `).concat(Z(this.options.movable,this.options.backgroundRepeat),`
    `),this.watermarkDom.appendChild(i),this.parentElement.appendChild(this.watermarkDom),this.options.mutationObserve)try{this.bindMutationObserve()}catch(m){(c=(s=this.options).onObserveError)===null||c===void 0||c.call(s)}return t&&((v=(h=this.options).onSuccess)===null||v===void 0||v.call(h)),this.isCreating=!1,[2]}})})},r.prototype.destroy=function(){this.remove(),this.watermarkDom=void 0},r.prototype.check=function(){return S(this,void 0,void 0,function(){return b(this,function(t){return[2,this.parentElement.contains(this.watermarkDom)]})})},r.prototype.remove=function(){var t,e,i,a,n,o,l,d;(e=(t=this.options).onBeforeDestroy)===null||e===void 0||e.call(t),(i=this.observer)===null||i===void 0||i.disconnect(),(a=this.parentObserve)===null||a===void 0||a.disconnect(),this.unbindCheckWatermarkElementEvent(),(o=(n=this.watermarkDom)===null||n===void 0?void 0:n.parentNode)===null||o===void 0||o.removeChild(this.watermarkDom),(d=(l=this.options).onDestroyed)===null||d===void 0||d.call(l)},r.prototype.initConfigData=function(t,e){var i=this;e===void 0&&(e="overwrite"),e==="append"?Object.keys(t).forEach(function(a){i.props&&(i.props[a]=t[a])}):this.props=t,this.options=k(k({},G),this.props),this.changeParentElement(this.options.parent),this.watermarkCanvas=new C(this.props,this.options)},r.prototype.changeParentElement=function(t){if(typeof t=="string"){var e=document.querySelector(t);e&&(this.parentElement=e)}else this.parentElement=t;this.parentElement||console.error("[WatermarkJsPlus]: please pass a valid parent element.")},r.prototype.validateUnique=function(){var t=!0;return Array.from(this.parentElement.childNodes).forEach(function(e){t&&Object.hasOwnProperty.call(e,"__WATERMARK__")&&(t=!1)}),t},r.prototype.validateContent=function(){switch(this.options.contentType){case"image":return Object.hasOwnProperty.call(this.options,"image");case"multi-line-text":case"rich-text":case"text":return this.options.content.length>0}},r.prototype.checkParentElementType=function(){return["html","body"].includes(this.parentElement.tagName.toLocaleLowerCase())?"root":"custom"},r.prototype.checkWatermarkElement=function(){return S(this,void 0,void 0,function(){return b(this,function(t){switch(t.label){case 0:return this.parentElement.contains(this.watermarkDom)?[3,2]:(this.remove(),[4,this.create()]);case 1:t.sent(),t.label=2;case 2:return this.bindCheckWatermarkElementEvent(),[2]}})})},r.prototype.bindMutationObserve=function(){var t=this;this.watermarkDom&&(this.bindCheckWatermarkElementEvent(),this.observer=new MutationObserver(function(e){return S(t,void 0,void 0,function(){return b(this,function(i){switch(i.label){case 0:return e.length>0?(this.remove(),[4,this.create()]):[3,2];case 1:i.sent(),i.label=2;case 2:return[2]}})})}),this.observer.observe(this.watermarkDom,{attributes:!0,childList:!0,subtree:!0,characterData:!0}),this.parentObserve=new MutationObserver(function(e){return S(t,void 0,void 0,function(){var i,a,n,o;return b(this,function(l){switch(l.label){case 0:i=0,a=e,l.label=1;case 1:return i<a.length?(n=a[i],(n==null?void 0:n.target)===this.watermarkDom||((o=n==null?void 0:n.removedNodes)===null||o===void 0?void 0:o[0])===this.watermarkDom||n.type==="childList"&&n.target===this.parentElement&&n.target.lastChild!==this.watermarkDom?(this.remove(),[4,this.create()]):[3,3]):[3,4];case 2:l.sent(),l.label=3;case 3:return i++,[3,1];case 4:return[2]}})})}),this.parentObserve.observe(this.parentElement,{attributes:!0,childList:!0,subtree:!0,characterData:!0}))},r.prototype.bindCheckWatermarkElementEvent=function(){this.unbindCheckWatermarkElementEvent(),this.checkWatermarkElementRequestID=requestAnimationFrame(this.checkWatermarkElement.bind(this))},r.prototype.unbindCheckWatermarkElementEvent=function(){x(this.checkWatermarkElementRequestID)||cancelAnimationFrame(this.checkWatermarkElementRequestID)},r}();(function(r){X(t,r);function t(e){e===void 0&&(e={});var i={globalAlpha:.005,mode:"blind"};return r.call(this,k(k({},e),i))||this}return t.prototype.changeOptions=function(){return S(this,arguments,void 0,function(e,i,a){return e===void 0&&(e={}),i===void 0&&(i="overwrite"),a===void 0&&(a=!0),b(this,function(n){switch(n.label){case 0:return e.globalAlpha=.005,e.mode="blind",this.initConfigData(e,i),I(this.options.monitorProtection),a?(this.remove(),[4,this.create()]):[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}})})},t.decode=function(e){var i=e.url,a=i===void 0?"":i,n=e.fillColor,o=n===void 0?"#000":n,l=e.compositeOperation,d=l===void 0?"color-burn":l,s=e.mode,c=s===void 0?"canvas":s,h=e.compositeTimes,v=h===void 0?3:h,u=e.onSuccess;if(a&&c==="canvas"){var m=new Image;m.src=a,m.addEventListener("load",function(){var p=m.width,f=m.height,w=C.createCanvas(p,f),g=w.getContext("2d");if(!g)throw new Error("get context error");g.drawImage(m,0,0,p,f),g.globalCompositeOperation=d,g.fillStyle=o;for(var _=0;_<v;_++)g.fillRect(0,0,p,f);var P=q(w);V(u)&&(u==null||u(P))})}},t})(nt);export{nt as Watermark};
