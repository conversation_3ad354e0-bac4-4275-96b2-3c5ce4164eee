import core from '../core';
import request from './arcgisRequest';
import * as Cesium from 'cesium';
import X2js from 'x2js';
import { GISMap as map } from '../index';

export default class GeoServerQuery {
  constructor(layerInfo) {
    this.layerInfo = layerInfo;
    this.layers = layerInfo.data.layers || '0';
    this.url =
      layerInfo.data.url.substring(0, layerInfo.data.url.lastIndexOf('/') + 1) +
        'ows' || ''; //"http://fzzt.fzjhdn.com:18086/geoserver/test/ows";
    this.token = layerInfo.data.token || '';
    this.resultOffset = 0;
    this.resultRecordCount = 1000;
    this.outFields = '*';
    this.where = '1=1';
    this.queryByDistance = 0;
  }
  buildParams(params) {
    let defaultData = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      // maxFeatures: '',
      // startIndex: '',
      outputFormat: 'application/json', //encodeURI('text/xml; subtype=gml/2.1.2'),
      // callback : 'getIdentifyroadGrid',//查询的回调函数,jsonp调用
    };
    return {
      ...defaultData,
      ...params,
      ...(() => {
        return this.token
          ? {
              token: this.token,
            }
          : {};
      })(),
    };
  }
  /*
	与arcgis的完整灵活的统计相比，geoserver的统计有些不一样。其wps服务的aggregate功能差异如下：1、可以设置多个分组值；2一次聚合查询只能设置一个统
	计变量；3、每个统计变量可以设置多个统计量（包括Count, Average, Max, Median, Min, StdDev, and Sum），具体看geoserver用户手册-
	GeoServer 2.23.x User Manual » Services » Web Processing Service (WPS) » Process Cookbook » GeoServer processes
	*巨坑geoserver,输出为text/xml时输入的function顺序和输出的顺序不一样，换成application/json格式输出，搞了一整天，没整明白中文乱码问题(json输出倒是
		按照指定的顺序输出)
	*/
  getRealUrl(url) {
    return (
      url.substring(0, url.indexOf('com') + 4) +
      '1' +
      url.substring(url.indexOf('com') + 4)
    );
  }
  async statistics(options) {
    let promises = [];
    let op = {};
    let oparr = [];
    options.statList.forEach((stat) => {
      op = {
        exp: stat.exp,
        field: stat.field,
        groupfield: options.groupfield || null,
      };
      oparr.push(op);
      promises.push(this.statistic(op));
    });
    return Promise.all(promises)
      .then((result) => {
        console.log('geoserver return:', result);
        let datalist = [];
        result.forEach((res, index) => {
          let x2js1 = new X2js();
          let el = x2js1.xml2js(res); // el的格式：{AggregationResults:{GroupByResult:{object-array:[{},[]]}}}或groupfield:null为{AggregationResults:{}}
          // if(el.ExecuteResponse)
          el =
            (el.AggregationResults.GroupByResult &&
              el.AggregationResults.GroupByResult['object-array']) ??
            el?.AggregationResults;
          let constradata = (obj) => {
            let arr = [];
            let json = {};
            for (let key in obj) {
              if (obj[key] instanceof Array) arr = obj[key];
              else arr.push(obj[key]);
            }

            let ifexited = datalist.filter((a) => {
              return a[oparr[index].groupfield] == arr[0];
            });
            if (ifexited.length > 0) {
              let a = datalist.indexOf(ifexited[0]);
              datalist[a][oparr[index].field + '_' + oparr[index].exp] = arr[1];
            } else {
              json[oparr[index].groupfield] = arr[0];
              json[oparr[index].field + '_' + oparr[index].exp] = arr[1];
              datalist.push(json);
            }
          };
          let constradata2 = (obj) => {
            let key = oparr[index].field + '_' + oparr[index].exp;
            let json = {};
            json[key] = el[oparr[index].exp];
            datalist.push(json);
          };

          if (el instanceof Array) {
            for (let i = 0; i < el.length; i++) {
              let res = el[i];
              constradata(res);
            }
          } else if (el instanceof Object) {
            constradata2(el);
          }
        });
        console.log('统计结果', datalist);
        return datalist;
      })
      .catch((error) => {
        console.log('geoserver统计出错了', error);
      });
  }
  /*
	option{exp:xxx,field:yyy,groupfield:zzz}
	exp:统计方法;field:统计字段;groupfield:分组字段
	*/
  async statistic(option) {
    // console.log("单统计参数:", option);
    // 统计方法
    let functions = option?.exp;
    // 统计字段
    let aggregationAttribute = option?.field;
    let singlePass = false;
    //分组字段
    let groupByAttributes = option.groupfield
      ? `<wps:Input>
		<ows:Identifier>groupByAttributes</ows:Identifier>
		<wps:Data>
		  <wps:LiteralData>${option.groupfield}</wps:LiteralData>
		</wps:Data>
  		</wps:Input>`
      : null;

    let param = this.buildParams({
      typeName: this.layers,
      cql_filter: this.str2Ascll16(this.where) || '',
      outputFormat: 'text%2Fxml%3B%20subtype%3Dgml%2F2.1.2',
    });
    // console.log(param);
    let href = this.getRealUrl(this.url) + '?'; //这里的href是真实地址，不是代理地址
    for (let i in param) {
      href += '' + i + '=' + param[i] + '&amp;';
    }
    href = href.substring(0, href.length - 6);

    let aggregateXml = `<?xml version="1.0" encoding="UTF-8"?>
		<wps:Execute version="1.0.0" service="WPS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns="http://www.opengis.net/wps/1.0.0" xmlns:wfs="http://www.opengis.net/wfs" xmlns:wps="http://www.opengis.net/wps/1.0.0"
		xmlns:ows="http://www.opengis.net/ows/1.1" xmlns:gml="http://www.opengis.net/gml" xmlns:ogc="http://www.opengis.net/ogc"
		xmlns:wcs="http://www.opengis.net/wcs/1.1.1" xmlns:xlink="http://www.w3.org/1999/xlink"
		xsi:schemaLocation="http://www.opengis.net/wps/1.0.0 http://schemas.opengis.net/wps/1.0.0/wpsAll.xsd">
  		<ows:Identifier>gs:Aggregate</ows:Identifier>
  		<wps:DataInputs>
    		<wps:Input>
      			<ows:Identifier>features</ows:Identifier>
      			<wps:Reference mimeType="text/xml; subtype=wfs-collection/1.0" xlink:href="${href}" method="GET"/>
    		</wps:Input>
			<wps:Input>
				<ows:Identifier>aggregationAttribute</ows:Identifier>
				<wps:Data>
			  		<wps:LiteralData>${aggregationAttribute}</wps:LiteralData>
				</wps:Data>
	  		</wps:Input>
			<wps:Input>
			  	<ows:Identifier>function</ows:Identifier>
			  	<wps:Data>
					<wps:LiteralData>${functions}</wps:LiteralData>
			  	</wps:Data>
			</wps:Input>
    		<wps:Input>
      			<ows:Identifier>singlePass</ows:Identifier>
      			<wps:Data>
        			<wps:LiteralData>${singlePass}</wps:LiteralData>
      			</wps:Data>
    		</wps:Input>
    		${groupByAttributes}
  		</wps:DataInputs>
  		<wps:ResponseForm>
    		<wps:RawDataOutput mimeType="text/xml">
      			<ows:Identifier>result</ows:Identifier>
    		</wps:RawDataOutput>
  		</wps:ResponseForm>
	</wps:Execute>`;
    return new Promise((resolve, reject) => {
      resolve(
        request({
          url: this.url,
          method: 'post',
          data: aggregateXml,
        }),
      );
    });
  }
  // 	--点构造方式
  // POINT(106.00,36.00)

  // --线构造方式
  // LINESTRING((106.00 36.00,106.00 36.10))

  // --面构造方式
  // POLYGON((-90 40, -90 45, -60 45, -60 40, -90 40))
  setGeometriy(entity) {
    //esriGeometryPoint | esriGeometryMultipoint | esriGeometryPolyline | esriGeometryPolygon | esriGeometryEnvelope
    let geometry = [];
    let geometryType = '';
    //console.log(entity)
    let trans = (position) => {
      var ellipsoid = Cesium.Ellipsoid.WGS84;
      var cartographic = ellipsoid.cartesianToCartographic(
        new Cesium.Cartesian3(position.x, position.y, position.z),
      );
      return {
        x: Cesium.Math.toDegrees(cartographic.longitude),
        y: Cesium.Math.toDegrees(cartographic.latitude),
      };
    };

    let loop = (entity) => {
      entity = core.entity2Json(entity);
      //点
      if (entity.point && (!geometryType || geometryType == 'Point')) {
        entity.position = trans(entity.position);
        geometryType = 'Point';
        // geometry.push(entity.position.x, entity.position.y);
        geometry = `${entity.position.x} ${entity.position.y}`;
      }
      //线
      else if (
        entity.polyline &&
        (!geometryType || geometryType == 'Polyline')
      ) {
        geometryType = 'Polyline';
        geometry.push(
          entity.polyline.positions.map((a) => {
            a = trans(a);
            return `${a.x} ${a.y}`;
          }),
        );
        geometry = geometry.join(',');
      }
      //面
      else if (entity.polygon && (!geometryType || geometryType == 'Polygon')) {
        geometryType = 'Polygon';
        geometry = entity.polygon.hierarchy.positions.map((a) => {
          a = trans(a);
          return `${a.x} ${a.y}`;
        });
        geometry.push(geometry[0]);
        geometry = geometry.join(',');
      }
      //矩形
      else if (
        entity.rectangle &&
        (!geometryType || geometryType == 'Rectangle')
      ) {
        geometryType = 'Rectangle';
        let xmin = (entity.rectangle.coordinates.west / Math.PI) * 180;
        let ymin = (entity.rectangle.coordinates.south / Math.PI) * 180;
        let xmax = (entity.rectangle.coordinates.east / Math.PI) * 180;
        let ymax = (entity.rectangle.coordinates.north / Math.PI) * 180;
        geometry = [
          `${xmin} ${ymin}`,
          `${xmax} ${ymin}`,
          `${xmax} ${ymax}`,
          `${xmin} ${ymax}`,
          `${xmin} ${ymin}`,
        ];
        geometry = geometry.join(',');
      }
      //圆
      else if (entity.ellipse && (!geometryType || geometryType == 'Ellipse')) {
        geometryType = 'Ellipse';
        //entity.position = trans(entity.position)
        let b = entity.position;
        let a = new Cesium.Cartesian3(
          b.x + entity.ellipse.semiMinorAxis,
          b.y,
          b.z,
        );
        let positions = [];
        [...Array(360)].forEach((_, i) => {
          positions.push(core.rotatedPointByAngle(a, b, i));
        });
        geometry = positions.map((a) => {
          a = trans(a);
          return `${a.x} ${a.y}`;
        });
        geometry.push(geometry[0]);
        geometry = geometry.join(',');
      }
    };
    if (entity instanceof Array) {
      entity.forEach((a) => {
        loop(a);
      });
    } else if (entity) {
      loop(entity);
    }
    // else {
    // 	this.geometryType = 'Envelope';
    // 	this.geometry = ''
    // 	return;
    // }
    // let spatialReference = {
    // 	spatialReference: {
    // 		wkid: 4326
    // 	}
    // };
    if (geometryType == 'Point') {
      if (geometry.length == 1) {
        let p = geometry.shift();
        geometry = `POINT(${geometry})`;
      }
    } else if (geometryType == 'Polyline') {
      geometry = `LINESTRING((${geometry}))`;
    } else if (
      geometryType == 'Polygon' ||
      geometryType == 'Rectangle' ||
      geometryType == 'Ellipse'
    ) {
      geometryType = 'Polygon';
      geometry = `POLYGON((${geometry}))`;
    }
    if (geometryType) {
      this.geometryType = geometryType;
      this.geometry = geometry; //JSON.stringify(geometry)
    }
  }
  // 	OGC wfs空间查询
  // 空间查询种类
  // <Intersects>- 测试两个几何是否相交
  // <Disjoint>- 测试两个几何是否不相交
  // <Contains>- 测试几何是否包含另一个几何
  // <Within>- 测试几何是否在另一个之内
  // <Touches>- 测试两个几何体是否接触
  // <Crosses>- 测试两个几何图形是否交叉
  // <Overlaps>- 测试两个几何图形是否重叠
  // <Equals>- 测试两个几何是否在拓扑上相等
  setSpatialRel(spatialRel) {
    let spatialRels = {
      intersects: 'Intersects',
      contains: 'Contains',
      crosses: 'Crosses',
      // "envelope-intersects": "esriSpatialRelEnvelopeIntersects",
      overlaps: 'Overlaps',
      touches: 'Touches',
      within: 'Within',
      'disjoint-intersects': 'Disjoint',
    };
    this.spatialRel = spatialRels[spatialRel] || 'Intersects';
    this.setWhere();
  }
  setWhere() {
    if (!this.geometry) return;
    this.where = this.spatialRel + '( the_geom , ' + this.geometry + ')'; //！！！！这里需要注意，要素的空间字段有的为geom,有的为the_geom
  }
  async queryById() {
    let viewer = map.viewer;
    let result = await request({
      url: this.url.trim(),
      method: 'get',
      params: this.buildParams({
        typeName: this.layers,
        cql_filter: this.str2Unicode(this.where) || '',
      }),
    });
    let hightLightDataSources = viewer.dataSources.getByName('___HIGHLIGHT');
    hightLightDataSources.forEach((ds) => {
      viewer.dataSources.remove(ds, true);
    });
    let dataSource = await Cesium.GeoJsonDataSource.load(result, {
      clampToGround: true,
    });
    dataSource.name = '___HIGHLIGHT';
    viewer.dataSources.add(dataSource);
    viewer.flyTo(dataSource, {
      offset: new Cesium.HeadingPitchRange(
        viewer.camera.heading,
        viewer.camera.pitch,
        0,
      ),
    });
    return dataSource;
  }
  async showList() {
    let result = await request({
      url: this.url.trim(),
      method: 'get',
      params: this.buildParams({
        typeName: this.layers,
        cql_filter: this.str2Unicode(this.where) || '',
        startIndex: this.resultOffset,
        maxFeatures: this.resultRecordCount,
        propertyName: this.outFields || '*',
      }),
    });
    return {
      list: result.features ? result.features.map((a) => a.properties) : [],
      count: result.totalFeatures || 0,
    };
  }
  str2Unicode(str) {
    if (!str) return;
    let es = [];
    for (let i = 0; i < str.length; i++)
      es[i] = ('00' + str.charCodeAt(i).toString(16)).slice(-4);
    return '\\u' + es.join('\\u');
  }
  str2Ascll16(str) {
    if (!str) return;
    let es = [];
    for (let i = 0; i < str.length; i++)
      es[i] = ('00' + str.charCodeAt(i).toString(16)).slice(-4);
    return '%5Cu' + es.join('%5Cu');
  }
}
