<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { Page } from '@vben/common-ui';
import { onMounted, ref, onUnmounted } from 'vue';

import { useVbenModal, VbenButton, type VbenFormProps } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import SceneDetialModal from '#/views/dataManage/scene/components/SceneDetialModal.vue';
import SceneCreateModal from '#/views/dataManage/scene/components/SceneCreateModal.vue';
import SceneUpdateModal from '#/views/dataManage/scene/components/SceneUpdateModal.vue';
import { deleteOne, list } from '#/views/dataManage/scene/scene.api';
import {
  columns,
  dataTypeOptions,
  getDataTypeValuesByCategory,
  searchFormSchema
} from '#/views/dataManage/scene/scene.data';
import {showConform} from '#/utils/alert.js'
import SceneUploadModal from "#/views/dataManage/scene/components/SceneUploadModal.vue";
import { getUploadStatusLabel } from "#/views/dataManage/entity/entity.data";
import { getCategoryFromLastPath,getLastPathSegment } from "#/views/common/util";
import { message } from 'ant-design-vue';
import { isTaskUploading } from '#/utils/upload-state';
import emitter from '#/utils/mitt';


let lastPath = getLastPathSegment();
let category = ref(getCategoryFromLastPath(lastPath));

const [CreateModal, createModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: SceneCreateModal,
  fullscreenButton:false,
  destroyOnClose:true,
  key: 'uniqueModalKey',
  onClosed:()=>{
    gridApi.query();
  }
});

const [UpdateModal, updateModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: SceneUpdateModal,
  fullscreenButton:false,
  destroyOnClose:true,
  onClosed:()=>{
    gridApi.query();
  }
});


const [DetialModal, detailModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: SceneDetialModal,
  fullscreenButton:false,
  destroyOnClose:true,
  onClosed:()=>{
  }
});

const [UploadModal, uploadModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: SceneUploadModal,
  fullscreenButton:false,
  destroyOnClose:true,
  onClosed:()=>{
  }
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: searchFormSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  },
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeGridProps = {
  columns,
  height: 'auto',
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let search = { ...formValues};
        let regionCode = search.regionCode;
        if(regionCode){
          let regionProvinceCode = regionCode[0];
          let regionCityCode = regionCode[1];
          let regionCountyCode = regionCode[2];

          search.regionProvinceCode = regionProvinceCode;
          search.regionCityCode = regionCityCode;
          search.regionCountyCode = regionCountyCode;

          delete search.regionCode;
        }
        const data = await list({
          page : {
            current: page.currentPage,
            size: page.pageSize,
            searchCount:true
          },
          condition : {
            ...search,
            category:category.value
          }
        });
        return {
          total: data.total,
          items:data.records,
        };
      },
    },
  },
  rowConfig: {
    isHover: true,
  },
};

const [Grid,gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const currentRow = ref(null);

// Reset form
const onReset = () => {};

/**
 * 新增事件
 */
const handleAdd = () => {
  createModalApi.setState({ upload:false,update:false,category:category});
  createModalApi.open();
};

/**
 * 编辑事件
 */
function handleEdit(row) {
  updateModalApi.setState({ row ,upload:false,update:true,category:category});
  updateModalApi.open();
}

const onCancelDetialModal = () => {
  detailModalApi.close();
};

const onCancelUploadModal = () => {
  uploadModalApi.close();
};

const onUploadSuccess = () =>{
  onCancelUploadModal();
  onUpdate();
}

/**
 * 删除事件
 */
async function handleDelete(record) {
  showConform('提示','确定要删除？',()=>{
     deleteOne(record.dataType,record.id, onUpdate);
  })
}

/**
 * 查看事件
 */
const handleView = (row) => {
  currentRow.value = row;
  detailModalApi.setState({ row});
  detailModalApi.open();
};

/**
 * 上传事件
 */
const handleUploadView = (row) => {
  currentRow.value = row;

  // 检查任务是否正在上传
  if (isTaskUploading(row.id)) {
    message.warning('该任务已经在上传中，请稍后再试');
    return;
  }

  uploadModalApi.setState({ row });
  uploadModalApi.open();
};

const onUpdate = ()=>{
  gridApi.reload();
}

// Fetch data on component mounted
onMounted(() => {
  emitter.on('uploadSuccess', onUploadSuccess);
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  emitter.off('uploadSuccess', onUploadSuccess);
});
</script>

<template>
  <Page auto-content-height>
    <!--引用表格-->
      <Grid>
        <!--插槽:table标题-->
        <template #toolbar-tools>
          <VbenButton
            pre-icon="ant-design:plus-outlined"
            type="primary"
            @click="handleAdd"
          >
            新建数据集
          </VbenButton>
        </template>
        <template #dataSituation="{ row }">
          <view>{{row.dataSituation === 1?'正式数据' : row.dataSituation == null ? '' : '过程数据'}}</view>
        </template>
        <template #area="{ row }">
          <view>{{row.area && row.area.toFixed(2)}}</view>
        </template>
        <template #regionName="{ row }">
          <view>{{row.regionProvinceName}}{{row.regionCityName}}{{row.regionCountyName}}</view>
        </template>
        <template #upload-status="{ row }">
          <view>{{getUploadStatusLabel(row)}}</view>
        </template>
        <!--操作栏-->
        <template #action="{ row }">
          <div class="actionBar">
            <a-button
              class="actionButton"
              type="link"
              @click="handleEdit(row)"
            >
              编辑
            </a-button>
            <a-button
              class="actionButton"
              type="link"
              @click="handleDelete(row)"
            >
              删除
            </a-button>
            <a-button
              class="actionButton"
              type="link"
              @click="handleView(row)"
            >
              查看
            </a-button>
            <a-button
              v-show="row.uploadStatus === 0 || row.uploadStatus == 3"
              class="actionButton"
              type="link"
              @click="handleUploadView(row)"
            >
              上传
            </a-button>
          </div>

        </template>
      </Grid>

    <CreateModal
      :category="category"
    />
    <UpdateModal
      :row="currentRow"
      :category="category"
    />
    <DetialModal
      :row="currentRow"
      @on-cancel="onCancelDetialModal"
    />
    <UploadModal
      :row="currentRow"
      :category="category"
      @on-cancel="onCancelUploadModal"
    />
  </Page>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';

.actionButton {
  padding: 6px;
  color:hsl(var(--primary));
}

.actionButton:hover {
  color:hsl(var(--primary));
}

.actionBar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}



</style>
