@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\eslint@9.26.0_jiti@2.4.2\node_modules\eslint\bin\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\eslint@9.26.0_jiti@2.4.2\node_modules\eslint\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\eslint@9.26.0_jiti@2.4.2\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\eslint@9.26.0_jiti@2.4.2\node_modules\eslint\bin\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\eslint@9.26.0_jiti@2.4.2\node_modules\eslint\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\eslint@9.26.0_jiti@2.4.2\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\eslint\bin\eslint.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\eslint\bin\eslint.js" %*
)
