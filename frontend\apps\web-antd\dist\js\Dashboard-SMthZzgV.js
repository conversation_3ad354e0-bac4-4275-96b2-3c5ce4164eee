var Ge=Object.defineProperty,Oe=Object.defineProperties;var Ve=Object.getOwnPropertyDescriptors;var Ie=Object.getOwnPropertySymbols;var Ne=Object.prototype.hasOwnProperty,ze=Object.prototype.propertyIsEnumerable;var Se=(r,e,s)=>e in r?Ge(r,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[e]=s,ue=(r,e)=>{for(var s in e||(e={}))Ne.call(e,s)&&Se(r,s,e[s]);if(Ie)for(var s of Ie(e))ze.call(e,s)&&Se(r,s,e[s]);return r},pe=(r,e)=>Oe(r,Ve(e));var V=(r,e,s)=>new Promise((l,n)=>{var c=b=>{try{a(s.next(b))}catch(A){n(A)}},u=b=>{try{a(s.throw(b))}catch(A){n(A)}},a=b=>b.done?l(b.value):Promise.resolve(b.value).then(c,u);a((s=s.apply(r,e)).next())});import{a as q,a9 as Fe,a7 as Pe,aa as Be,ab as Ke,ac as Me,ad as Ye,u as Re,ae as $e,af as He,ag as We,ah as Ue,ai as Je,aj as Ze,ak as je,al as qe,am as Xe,an as et}from"./bootstrap-5OPUVRWy.js";import{u as tt}from"./vxe-table-CZ9gPHn5.js";import{V as Ee,S as st,m as ee,l as Te,g as at,q as nt,a as it,b as ot}from"./SpaceQuery-BLePLjaE.js";import{G as ge,b as ke,g as fe,B as Z,z as M,h as Le,s as lt,H as rt,c as Ae}from"./index-D4Q7xmlJ.js";import{x as g,a as E,b as k,f as o,s as i,n as j,e as S,t as W,F as ve,D as Qe,C as $,q as d,j as z,r as v,v as P,K as ct,k as N,o as dt}from"../jse/index-index-DyHD_jbN.js";import{a as xe,s as De}from"./index-BYvs451p.js";import"./Base-xeJpkIWP.js";import{l as ut,M as pt}from"./MapViewer-CBAv8z2T.js";import{b as yt,s as ht}from"./toast-CQjPPeQ1.js";import"./loading-DzjUKA94.js";import"./form-DdFfsSWf.js";import"./util-BadkgFi3.js";import"./scene.data-BMXeOdST.js";import"./entity.data-u4HDUExc.js";const mt="data:image/png;base64,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************************************+vzzIApS2BYYvMAMSTPaJO2bZ1m/61vT1DGbbIKCBe7AEY/VBca+npZcghi4wCYt0egDEXjGsvQ8onGy1/oYRMwAgkXcsIINbtkfFZRlcnGGzk4XPy3RYZAcSqPQBjsMd3Nrc8Pum2SC8gFu3Bs4zOnj25mVVQuizSC8g3Q/edPMuY3MMnbc4aKF0W6QXEwk8YAMaknqy8GQHFwufku6Z8ewDZfXvFQz7lHq20eQtTw823WZ4AYcpWqecu3uzOlyGXALL6J9QAY3EPXrC7XS9DNn/hdY9B5KGQDNJXLEzZrkh53z5WD+Rft36UoQcQOSl5BqK5AIZmuva2vQqU5v7e3OCcrXzyTOPb+3iWYa/zrjwiTVDki/3kFqtp6QVktkVqfkim6cRY2XUCGlPDzbdXkmAvINJ2xmCdZxmu+7H6wc+aGm6evTrObASQEUh4lqHet0LtYGRqeGg8OwpIKyRM2Ybqt0tPpnVqeMrdyQxAJKVHBw8YS/tS6J0t7WuzAHleETmB40/AOP5CV42T25LA8VkU2blKX9MAZEtS7JQENBIAEI1U2WaYBAAkTCk5EY0EAEQjVbYZJgEACVNKTkQjAQDRSJVthkkAQMKUkhPRSABANFJlm2ESAJAwpeRENBIAEI1U2WaYBAAkTCk5EY0EAEQjVbYZJoH/AZ4A7Njl1oPrAAAAAElFTkSuQmCC",gt={components:{CompassNeedle:Fe},data(){return{angle:0,iconCompass:mt}},mounted(){const r=this;fe(e=>{e.viewer.scene.postRender.addEventListener(function(){this.angle=Cesium.Math.toDegrees(e.viewer.camera.heading)*-1},r)})},methods:{lookNorth(r){r=r||0;const e=ge.viewer,s=e.camera.positionCartographic.clone().height,l=ke.getMapCenter(e),n=Cesium.Math.toDegrees(e.camera.pitch),c=ke.getCameraPositionByMapCenter({lng:l.lng,lat:l.lat,alt:s},r,n);e.camera.flyTo({destination:Cesium.Cartesian3.fromDegrees(c.lng,c.lat,s),orientation:{heading:Cesium.Math.toRadians(r),pitch:e.camera.pitch,roll:0}})}}};function kt(r,e,s,l,n,c){const u=g("CompassNeedle");return k(),E("div",{style:j(`transform:rotate(${n.angle}deg)`),class:"compass-viewer"},[o("div",{class:"north",onClick:e[0]||(e[0]=a=>c.lookNorth(0))},[i(u,{class:"size-12"})]),o("span",{style:j([{transform:`rotate(${n.angle*-1}deg)`},{top:"35px",right:"0"}]),class:"direction","data-title":"东",onClick:e[1]||(e[1]=a=>c.lookNorth(90))},"东",4),o("span",{style:j([{transform:`rotate(${n.angle*-1}deg)`},{bottom:"0",left:"35px"}]),class:"direction","data-title":"南",onClick:e[2]||(e[2]=a=>c.lookNorth(180))},"南",4),o("span",{style:j([{transform:`rotate(${n.angle*-1}deg)`},{top:"35px",left:"0"}]),class:"direction","data-title":"西",onClick:e[3]||(e[3]=a=>c.lookNorth(-90))},"西",4),o("span",{style:j([{transform:`rotate(${n.angle*-1}deg)`},{top:"0",left:"35px"}]),class:"direction","data-title":"北",onClick:e[4]||(e[4]=a=>c.lookNorth(0))},"北",4)],4)}const ft=q(gt,[["render",kt],["__scopeId","data-v-7f03cc62"]]),vt={data(){return{barWidth:0,distance:0,displayType:0,distances:[1,2,3,5,10,20,30,50,100,200,300,500,1e3,2e3,3e3,5e3,1e4,2e4,3e4,5e4,1e5,2e5,3e5,5e5,1e6]}},computed:{distanceLabel(){return this.distance?this.displayType==1?`1:${this.distance}`:this.distance>=1e3?`${this.distance/1e3}km`:this.distance>=1?`${this.distance}m`:"":""}},mounted(){const r=this;fe(e=>{const s=e.viewer.scene;s.postRender.addEventListener(function(){const l=new Cesium.EllipsoidGeodesic,n=s.canvas.clientWidth,c=s.canvas.clientHeight,u=s.camera.getPickRay(new Cesium.Cartesian2(n/2|0,c-1)),a=s.camera.getPickRay(new Cesium.Cartesian2(1+n/2|0,c-1)),{globe:b}=s,A=b.pick(u,s),L=b.pick(a,s);if(!Cesium.defined(A)||!Cesium.defined(L)){this.barWidth=void 0,this.distance=void 0;return}const R=b.ellipsoid.cartesianToCartographic(A),G=b.ellipsoid.cartesianToCartographic(L);l.setEndPoints(R,G);const F=l.surfaceDistance,U=100;let O;const H=this.distances;for(let D=H.length-1;!Cesium.defined(O)&&D>=0;--D)H[D]/F<U&&(O=H[D]);this.barWidth=O/F,this.distance=O},r)})},methods:{setDistanceLegend(r){const e=ge.viewer,s=e.camera.positionCartographic.clone(),l=r/this.distance;l<1?e.camera.moveForward(s.height*(1-l)):l>1&&e.camera.moveBackward(s.height*(l-1))}}},Ct={class:"distance-label"},_t={class:"distance-select"},bt=["onClick"];function At(r,e,s,l,n,c){return c.distanceLabel?(k(),E("div",{key:0,class:"distance-legend",onClick:e[0]||(e[0]=u=>n.displayType=(n.displayType+1)%2)},[o("div",Ct,"1 : "+W(c.distanceLabel),1),o("div",{style:j({width:`${n.barWidth}px`}),class:"bar"},null,4),o("div",_t,[(k(!0),E(ve,null,Qe(n.distances,u=>(k(),E("div",{key:u,class:"select-item",onClick:Pe(a=>c.setDistanceLegend(u),["stop"])}," 1:"+W(u>1e3?`${u/1e3}km`:`${u}m`),9,bt))),128))])])):S("",!0)}const wt=q(vt,[["render",At],["__scopeId","data-v-ce5c85e0"]]);let K=null;const Lt={data(){return{lng:0,lat:0,h:0,ch:0,type:0}},computed:{longitude(){if(this.type==0)return this.lng;const r=Math.floor(this.lng);let e=(this.lng-r)*60,s=(e-Math.floor(e))*60;return e=`0${Math.floor(e)}`.slice(-2),s=`0${s.toFixed(2)}`.slice(-5),`${r}°${e}′${s}″`},latitude(){if(this.type==0)return this.lat;const r=Math.floor(this.lat);let e=(this.lat-r)*60,s=(e-Math.floor(e))*60;return e=`0${Math.floor(e)}`.slice(-2),s=`0${s.toFixed(2)}`.slice(-5),`${r}°${e}′${s}″`},height(){return this.h>1e3?`${(this.h/1e3).toFixed(2)}km`:`${this.h}m`},cameraHeight(){return this.ch>1e3?`${(this.ch/1e3).toFixed(2)}km`:`${this.ch}m`}},mounted(){fe(r=>{const e=r.viewer;K=new Cesium.ScreenSpaceEventHandler(e.scene.canvas),K.setInputAction(s=>{const l=ke.getCatesian3FromPX(e,s.endPosition);if(!l)return!1;const n=ke.transformCartesianToWGS84(l);this.lng=n.lng.toFixed(6),this.lat=n.lat.toFixed(6),this.h=n.alt.toFixed(2),this.ch=e.camera.positionCartographic.clone().height.toFixed(2)},Cesium.ScreenSpaceEventType.MOUSE_MOVE),e.scene.camera.changed.addEventListener(()=>{this.ch=e.camera.positionCartographic.clone().height.toFixed(2)}),this.ch=e.camera.positionCartographic.clone().height.toFixed(2),Ee.on("getlocation",this.pickLocation)})},unmounted(){Ee.off("getlocation",this.pickLocation),this.pickLocationEnd(),K&&!K.isDestroyed()&&(K.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE),K=K&&K.destroy()),K=null},methods:{copy(e){var e=e||`经度:${this.longitude} 纬度:${this.latitude} 高度:${this.height}`;const s=document.createElement("input");document.body.append(s),s.setAttribute("value",e),s.select(),document.execCommand("copy"),document.execCommand("copy")&&(document.execCommand("copy"),console.log(e),alert(`${e}
已复制好，可贴粘。`)),s.remove()},pickLocation(){const r=ge.viewer,e=this;let s=null;K.setInputAction(l=>{const n=`经度:${this.longitude} 纬度:${this.latitude} 高度:${this.height}`;s=Z.open({title:"拾取坐标",beforeClose:()=>{e.pickLocationEnd()}},[$("div",{style:{color:"#fff",padding:"10px"}},[$("div",{},`经度:${this.longitude}`),$("div",{},`纬度:${this.latitude}`),$("div",{},`高度:${this.height}`)]),$("div",{style:{"text-align":"center","border-top":"1px solid rgba(40, 44, 52, 0.8)",padding:"5px"}},[$("button",{style:{padding:"2px 20px"},onclick:()=>{this.copy(n),Z.close(s)}},"复制")])],s)},Cesium.ScreenSpaceEventType.LEFT_CLICK),K.setInputAction(l=>{e.pickLocationEnd()},Cesium.ScreenSpaceEventType.RIGHT_CLICK),r._element.style.cursor="crosshair",r.enableCursorStyle=!0},pickLocationEnd(){if(!K)return;const r=ge.viewer;K.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK),K.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK),r._element.style.cursor="default",r.enableCursorStyle=!1}}};function xt(r,e,s,l,n,c){return k(),E("div",{class:"location-container",onClick:e[0]||(e[0]=u=>n.type=(n.type+1)%2)},[o("span",null,"经度:"+W(c.longitude),1),o("span",null,"纬度:"+W(c.latitude),1),o("span",null,"高度:"+W(c.height),1),o("span",null,"相机高度:"+W(c.cameraHeight),1)])}const It=q(Lt,[["render",xt],["__scopeId","data-v-7515e60f"]]);Cesium.VerticalOrigin.BOTTOM,Cesium.HorizontalOrigin.CENTER,new Cesium.Cartesian3(0,.05,0),Cesium.HeightReference.CLAMP_TO_GROUND,new Cesium.DistanceDisplayCondition(0,3e5),Cesium.VerticalOrigin.BASELINE,Cesium.HorizontalOrigin.CENTER,new Cesium.Cartesian2(0,-34),new Cesium.Cartesian3(0,.01,0),Cesium.HeightReference.CLAMP_TO_GROUND,new Cesium.DistanceDisplayCondition(0,1e5),Cesium.Color.BLACK,Cesium.Color.WHITE,Cesium.LabelStyle.FILL_AND_OUTLINE;const St={components:{ATree:Me,ATabs:Ke,ATabPane:Be},data(){return{activeKey:"left",checkedKeys:[],checkedKeys2:[]}},mounted(){this.GISLayers=M.global.GISLayers,this.SplitMap=M.global.SplitMap,this.layers=this.GISLayers.getSplitLayers(),this.checkchange(),this.SplitMap.curtain("viewDiv"),this.GISLayers.on("showChanged",()=>{this.layers=this.GISLayers.getSplitLayers(),this.checkchange()})},methods:{change(r){this.activeKey=r},layerCheck(r,{checked:e,checkedNodes:s,node:l,event:n}){const c=l.key,u=this.GISLayers.getLayer(c),a=u.refLayer;e&&this.activeKey=="left"?(a.splitDirection=u.show?Cesium.SplitDirection.NONE:Cesium.SplitDirection.LEFT,u.show||this.GISLayers.showAndZoomToLayer(l.key)):e&&this.activeKey=="right"?(a.splitDirection=u.show?Cesium.SplitDirection.NONE:Cesium.SplitDirection.RIGHT,u.show||this.GISLayers.showAndZoomToLayer(l.key)):this.activeKey=="left"?a.splitDirection==Cesium.SplitDirection.NONE?a.splitDirection=Cesium.SplitDirection.RIGHT:this.GISLayers.hide(c):this.activeKey=="right"&&(a.splitDirection==Cesium.SplitDirection.NONE?a.splitDirection=Cesium.SplitDirection.LEFT:this.GISLayers.hide(c)),this.checkchange()},checkchange(){this.checkedKeys=this.GISLayers.showLeftKeys,this.checkedKeys2=this.GISLayers.showRightKeys,console.log(this.checkedKeys,this.checkedKeys2)}}},Et={class:"curtain-view"},Tt={style:{"max-height":"300px","margin-top":"-15px",overflow:"hidden auto"}},Dt={style:{"max-height":"300px","margin-top":"-15px",overflow:"hidden auto"}};function Pt(r,e,s,l,n,c){const u=g("ATree"),a=g("ATabPane"),b=g("ATabs");return k(),E("div",Et,[i(b,{modelValue:n.activeKey,"onUpdate:modelValue":e[2]||(e[2]=A=>n.activeKey=A),onChange:c.change},{default:d(()=>[i(a,{key:"left",tab:"左帘"},{default:d(()=>[o("div",Tt,[i(u,{"checked-keys":n.checkedKeys,"onUpdate:checkedKeys":e[0]||(e[0]=A=>n.checkedKeys=A),"tree-data":r.layers,checkable:"",onCheck:c.layerCheck},null,8,["checked-keys","tree-data","onCheck"])])]),_:1}),i(a,{key:"right","force-render":"",tab:"右帘"},{default:d(()=>[o("div",Dt,[i(u,{"checked-keys":n.checkedKeys2,"onUpdate:checkedKeys":e[1]||(e[1]=A=>n.checkedKeys2=A),"tree-data":r.layers,checkable:"",onCheck:c.layerCheck},null,8,["checked-keys","tree-data","onCheck"])])]),_:1})]),_:1},8,["modelValue","onChange"])])}const Bt=q(St,[["render",Pt]]),Kt={components:{ATree:Me,ATabs:Ke,ATabPane:Be,ASwitch:Ye},data(){return{activeKey:0,checkedKeys:[],screenslist:[],layers:[],GISLayers:[],checkedkeysArray:[]}},mounted(){this.GISLayers.push(M.global.GISLayers);const r=this.GISLayers[0].getData();this.layers=this.GISLayers[0].getTreeData();const e=M.global.SplitMap.splitViewers.length;this.checkedkeysArray=[[]],this.GISLayers[0].on("showChanged",l=>{this.checkedkeysArray[0]=l,this.change(this.activeKey)}),this.checkedKeys=this.checkedkeysArray[0]=this.GISLayers[0].layers.filter(l=>l.show).map(l=>l.key);const s=[{title:"主屏幕",key:0}];console.log(e);for(let l=1;l<e;l++){const n=M.global.SplitMap.splitViewers[l],c=new ut(n.viewer);this.GISLayers.push(c),c.loadData(r),s.push({title:`屏幕${l+1}`,key:l,tongbu:n.synchronization}),this.checkedkeysArray.push(this.checkedKeys),c.show(this.checkedKeys)}console.log(s),this.screenslist=s},methods:{change(r){this.activeKey=r,this.layers=this.GISLayers[Number.parseInt(r)].getTreeData(),this.checkedKeys=this.checkedkeysArray[Number.parseInt(r)]},tongbu(r){const e=this.screenslist[this.activeKey];e.tongbu=r,console.log(this.screenslist),M.global.SplitMap.setSynchronization(e.key,r)},layerCheck(r,{checked:e,checkedNodes:s,node:l,event:n}){const c=Number.parseInt(this.activeKey);if(console.log("........node",e,l),e&&l.data.type)this.GISLayers[c].showAndZoomToLayer(l.key);else if(e){const u=Le(l);this.GISLayers[c].show(u)}else l.data.type?this.GISLayers[c].hide(l.key):Le(l).forEach(u=>{this.GISLayers[c].hide(u)});this.checkedkeysArray[c]=r}}},Mt={class:"multiscreen-view"},Rt={class:"setting"};function Qt(r,e,s,l,n,c){const u=g("ATree"),a=g("ASwitch"),b=g("ATabPane"),A=g("ATabs");return k(),E("div",Mt,[i(A,{modelValue:n.activeKey,"onUpdate:modelValue":e[1]||(e[1]=L=>n.activeKey=L),style:{width:"280px"},onChange:c.change},{default:d(()=>[(k(!0),E(ve,null,Qe(n.screenslist,L=>(k(),z(b,{key:L.key,tab:L.title},{default:d(()=>[o("div",Rt,[i(u,{"checked-keys":n.checkedKeys,"onUpdate:checkedKeys":e[0]||(e[0]=R=>n.checkedKeys=R),"tree-data":n.layers,checkable:"","default-expand-all":"",onCheck:c.layerCheck},null,8,["checked-keys","tree-data","onCheck"])]),o("div",null,[L.key>0?(k(),z(a,{key:0,checked:L.tongbu,"onUpdate:checked":R=>L.tongbu=R,"checked-children":"同步","un-checked-children":"不同步",onChange:c.tongbu},null,8,["checked","onUpdate:checked","onChange"])):S("",!0)])]),_:2},1032,["tab"]))),128))]),_:1},8,["modelValue","onChange"])])}const we=q(Kt,[["render",Qt]]),Gt={class:"wander"},Ot={class:"layerHeader"},Vt={style:{display:"flex","align-items":"center","justify-content":"center"}},Nt={style:{display:"flex","align-items":"center"}},zt={class:"layerContent"},Ft={key:0},Yt={style:{display:"flex",width:"100%",margin:"10px 5px"}},$t={class:"resultList",style:{margin:"10px 5px",background:"#031126"}},Ht={key:1},Wt={class:"resultList",style:{margin:"10px 5px",background:"#031126"}},Ut={__name:"wander",setup(r){const e=v("one"),s=v(!1),l=v(""),n=v([{name:"飞行路线1"},{name:"飞行路线2"},{name:"飞行路线3"},{name:"飞行路线4"},{name:"飞行路线5"},{name:"飞行路线6"}]),c=v([{name:"点1"},{name:"点2"},{name:"点3"},{name:"点4"},{name:"点5"},{name:"点6"}]);return(u,a)=>{const b=g("CaretLeftOutlined"),A=g("CaretRightOutlined"),L=g("a-button"),R=g("a-list-item-meta"),G=g("a-list-item"),F=g("a-list"),U=g("a-input"),O=g("a-select-option"),H=g("a-select"),D=g("a-input-group"),te=g("a-tab-pane"),le=g("a-tabs");return k(),E("div",Gt,[o("div",Ot,[o("div",Vt,[o("div",Nt,[i(b,{style:{position:"relative","margin-right":"-7px","font-size":"36px",color:"#1b3157"}}),a[6]||(a[6]=o("span",{style:{display:"inline-block",height:"28px",padding:"5px 13px","font-size":"16px","font-weight":"bold","line-height":"18px",color:"#aad5f2",background:"#1b3157"}}," 地图漫游 ",-1)),i(A,{style:{position:"relative","margin-left":"-7px","font-size":"40px",color:"#1b3157"}})])])]),o("div",zt,[s.value?(k(),E("div",Ht,[i(L,{size:"small",style:{color:"white"},type:"text",onClick:a[1]||(a[1]=T=>s.value=!1)},{default:d(()=>a[10]||(a[10]=[P(" 《 返回 ")])),_:1}),a[18]||(a[18]=o("br",null,null,-1)),a[19]||(a[19]=o("br",null,null,-1)),i(U,{value:l.value,"onUpdate:value":a[2]||(a[2]=T=>l.value=T),"addon-before":"路线名称：",placeholder:"请输入"},null,8,["value"]),i(D,{class:"selectGroup",compact:""},{default:d(()=>[a[14]||(a[14]=o("div",{style:{width:"25%",height:"32px",color:"whitesmoke","text-align":"center",background:"rgb(32 73 117 / 50%)"}}," 视角 ",-1)),i(H,{ref:"select",value:e.value,"onUpdate:value":a[3]||(a[3]=T=>e.value=T),style:{width:"75%"}},{default:d(()=>[i(O,{value:"one"},{default:d(()=>a[11]||(a[11]=[P("第一人称视角")])),_:1}),i(O,{value:"two"},{default:d(()=>a[12]||(a[12]=[P("相机跟随")])),_:1}),i(O,{value:"three"},{default:d(()=>a[13]||(a[13]=[P("上帝视角")])),_:1})]),_:1},8,["value"])]),_:1}),i(le,{"active-key":u.activeKey,"onUpdate:activeKey":a[5]||(a[5]=T=>u.activeKey=T),"tab-bar-style":{color:"darkgray"}},{default:d(()=>[i(te,{key:"1",tab:"自定义绘制"},{default:d(()=>[o("div",Wt,[i(F,{"data-source":c.value,class:"listStyle","item-layout":"horizontal"},{renderItem:d(({item:T})=>[i(G,null,{actions:d(()=>a[15]||(a[15]=[o("div",null,[o("a",null,"定位")],-1),o("div",{style:{color:"red"}},[o("a",null,"删除")],-1)])),default:d(()=>[i(R,{style:{cursor:"pointer"}},{title:d(()=>[o("span",null,W(T.name),1)]),_:2},1024)]),_:2},1024)]),_:1},8,["data-source"])])]),_:1}),i(te,{key:"2",tab:"导入"},{default:d(()=>[i(U,{value:l.value,"onUpdate:value":a[4]||(a[4]=T=>l.value=T),"addon-before":"选择文件：",type:"file"},null,8,["value"])]),_:1})]),_:1},8,["active-key"]),a[20]||(a[20]=o("br",null,null,-1)),a[21]||(a[21]=o("br",null,null,-1)),i(L,{size:"small",type:"primary"},{default:d(()=>a[16]||(a[16]=[P("飞行播放")])),_:1}),i(L,{danger:"",size:"small",type:"primary"},{default:d(()=>a[17]||(a[17]=[P("确定添加")])),_:1})])):(k(),E("div",Ft,[o("div",Yt,[i(L,{size:"small",type:"primary",onClick:a[0]||(a[0]=T=>s.value=!0)},{default:d(()=>a[7]||(a[7]=[P(" 新增路线 ")])),_:1}),i(L,{danger:"",size:"small",type:"primary"},{default:d(()=>a[8]||(a[8]=[P("清空路线")])),_:1})]),o("div",$t,[i(F,{"data-source":n.value,class:"listStyle","item-layout":"horizontal"},{renderItem:d(({item:T})=>[i(G,null,{actions:d(()=>a[9]||(a[9]=[o("div",null,[o("a",null,"飞行")],-1),o("div",null,[o("a",null,"编辑")],-1),o("div",null,[o("a",{style:{color:"red"}},"删除")],-1)])),default:d(()=>[i(R,{style:{cursor:"pointer"}},{title:d(()=>[o("span",null,W(T.name),1)]),_:2},1024)]),_:2},1024)]),_:1},8,["data-source"])])]))])])}}},Jt=q(Ut,[["__scopeId","data-v-203f69fa"]]),Zt={class:"tools"};const jt={class:"text"},qt={class:"maptools bg-white dark:bg-black"},Xt={key:0,class:"iconfont",style:{"font-size":"14px"}},es={key:1,class:"iconfont",style:{"font-size":"14px"}},ts={__name:"mapTools",props:["checkOptLayers"],setup(r){const e=v(!1);v(!1);const s=v(!1);v([]);const l=v(3);Re();const n=v(null);v(null),v(null);const c=v(!1),u=r,b=(f=>getComputedStyle(document.documentElement).getPropertyValue(f))("--primary"),{h1:A,s1:L,l1:R}=lt(b),G=rt(A,L,R);console.log(G);const F=()=>{ee.fullMap()},U=()=>{document.fullscreenElement?document.exitFullscreen():document.documentElement.requestFullscreen(),e.value=!e.value},O=()=>{ee.zoomIn()},H=()=>{ee.zoomOut()},D=()=>{l.value=ee.morph(),console.log("...........scenemode",l)},te=()=>{ee.fushi()},le=f=>{ee.changeMap(f.key)},T=()=>{c.value=!c.value},se=(f,C,Y,re,ce)=>V(null,null,function*(){try{const de=yield Promise.resolve(Y),t=new Cesium.ImageryLayer(de);return t.alpha=Cesium.defaultValue(re,.5),t.show=Cesium.defaultValue(ce,!0),t.name=C,f.viewer.imageryLayers.add(t),Cesium.knockout.track(t,["alpha","show","name"]),t}catch(de){console.error(`There was an error while creating ${C}. ${de}`)}}),Ce=()=>{xe.emit("clearData",{})};let ae=null,ne=null;const _e=()=>{fe(f=>V(null,null,function*(){if(ae&&ne){f.viewer.imageryLayers.remove(ae),f.viewer.imageryLayers.remove(ne),ae=null,ne=null;return}ae=yield se(f,"Grid",new Cesium.GridImageryProvider,1,!0),ne=yield se(f,"Tile Coordinates",new Cesium.TileCoordinatesImageryProvider,1,!0)}))};ct(()=>{Z.closeAll()});const ye=f=>{switch(f.key){case"curtain":{Z.open({title:"卷帘对比",beforeClose:()=>{M.global.SplitMap.restore()}},$(Bt));break}case"doublescreen":{M.global.SplitMap.split("viewDiv",{layout:[[0,1]],synchronization:!0}),Z.open({title:"双屏对比",beforeClose:()=>{M.global.SplitMap.restore()}},$(we));break}case"fourscreen":{M.global.SplitMap.split("viewDiv",{layout:[[0,0,1,1],[0,0,1,1],[2,2,3,3],[2,2,3,3]],synchronization:!0}),Z.open({title:"四屏对比",beforeClose:()=>{M.global.SplitMap.restore()}},$(we));break}case"threescreen":{M.global.SplitMap.split("viewDiv",{layout:[[0,1,2],[0,1,2],[0,1,2]],synchronization:!0}),Z.open({title:"三屏对比",beforeClose:()=>{M.global.SplitMap.restore()}},$(we));break}}},he=f=>{ee.onStartSpaceQuery(f)},me=()=>{c.value=!1};return(f,C)=>{const Y=g("a-menu-item"),re=g("a-menu"),ce=g("DownOutlined"),de=g("a-dropdown"),t=g("a-flex"),p=g("a-card"),m=g("a-tooltip");return k(),E(ve,null,[o("view",Zt,[i(p,{bodyStyle:{padding:"10px"}},{default:d(()=>[i(t,{justify:"space-between"},{default:d(()=>[(f.$chkFuns("功能树.工具栏.底图"),S("",!0)),f.$chkFuns("功能树.工具栏.分屏")?(k(),E("view",{key:1,ref_key:"multiScreenEl",ref:n,class:"item"},[i(de,{"get-popup-container":()=>n.value},{overlay:d(()=>[i(re,{class:"",onClick:ye},{default:d(()=>[i(Y,{key:"curtain"},{default:d(()=>C[11]||(C[11]=[P(" 卷帘对比 ")])),_:1}),i(Y,{key:"doublescreen"},{default:d(()=>C[12]||(C[12]=[P(" 双屏对比 ")])),_:1}),i(Y,{key:"threescreen"},{default:d(()=>C[13]||(C[13]=[P(" 三屏对比 ")])),_:1}),i(Y,{key:"fourscreen"},{default:d(()=>C[14]||(C[14]=[P(" 四屏对比 ")])),_:1})]),_:1})]),default:d(()=>[o("div",null,[i(N($e),{class:"size-8"}),o("div",jt,[C[15]||(C[15]=P(" 分屏 ")),i(ce)])])]),_:1},8,["get-popup-container"])],512)):S("",!0),o("view",{class:"item",onClick:T},[i(N(He),{class:"size-8",style:j({color:c.value?N(G):"#000000"})},null,8,["style"]),C[16]||(C[16]=o("div",{class:"text"},"数据分发",-1))])]),_:1})]),_:1})]),o("div",qt,[f.$chkFuns("功能树.侧边栏.全图/起始位置")?(k(),z(m,{key:0,placement:"left",title:"全图/起始位置"},{default:d(()=>[o("a",{class:"item",onClick:F},[i(N(We),{class:"size-6"})])]),_:1})):S("",!0),f.$chkFuns("功能树.侧边栏.全屏/退出全屏")?(k(),z(m,{key:1,placement:"left",title:"全屏/退出全屏"},{default:d(()=>[o("a",{class:"item",onClick:C[0]||(C[0]=y=>U())},[i(N(Ue),{class:"size-6"})])]),_:1})):S("",!0),f.$chkFuns("功能树.侧边栏.放大")?(k(),z(m,{key:2,placement:"left",title:"放大"},{default:d(()=>[o("a",{class:"item",onClick:O},[i(N(Je),{class:"size-6"})])]),_:1})):S("",!0),f.$chkFuns("功能树.侧边栏.缩小")?(k(),z(m,{key:3,placement:"left",title:"缩小"},{default:d(()=>[o("a",{class:"item",onClick:H},[i(N(Ze),{class:"size-6"})])]),_:1})):S("",!0),f.$chkFuns("功能树.侧边栏.俯视")?(k(),z(m,{key:4,placement:"left",title:"俯视"},{default:d(()=>[o("a",{class:"item",onClick:C[1]||(C[1]=y=>te())},[i(N(je),{class:"size-6"})])]),_:1})):S("",!0),f.$chkFuns("功能树.侧边栏.2D/3D")?(k(),z(m,{key:5,placement:"left",title:"2D/3D"},{default:d(()=>[o("a",{class:"item",onClick:D},[l.value==2?(k(),E("span",Xt,"2D")):S("",!0),l.value==3?(k(),E("span",es,"3D")):S("",!0)])]),_:1})):S("",!0),f.$chkFuns("功能树.侧边栏.网格")?(k(),z(m,{key:6,placement:"left",title:"网格"},{default:d(()=>[o("a",{class:"item",onClick:C[2]||(C[2]=y=>_e())},[i(N(qe),{class:"size-6"})])]),_:1})):S("",!0),f.$chkFuns("功能树.侧边栏.网格")?(k(),z(m,{key:7,placement:"left",title:"清除"},{default:d(()=>[o("a",{class:"item",onClick:C[3]||(C[3]=y=>Ce())},[i(N(Xe),{class:"size-6"})])]),_:1})):S("",!0)]),s.value?(k(),z(Jt,{key:0,class:"wander"})):S("",!0),i(st,{checkOptLayers:u.checkOptLayers,visible:c.value,onOnCLoseSpaceQuery:me,onStartQuery:he},null,8,["checkOptLayers","visible"])],64)}}},ss=q(ts,[["__scopeId","data-v-61ce5a3f"]]),as={class:"layerContent"},ns={style:{display:"flex","align-items":"center","justify-content":"space-between","user-select":"none"}},is=["onClick"],os={key:0,class:"iconfont icon-toumingdu1",style:{"font-size":"18px",color:"#fff"}},ls={class:"query-content"},rs={class:"query-tools"},cs={class:"query-result"},ds={key:1,style:{position:"absolute",bottom:"0",left:"0","z-index":"1",width:"100%",height:"30px",background:"rgb(40 44 52 / 70%)"}},us={style:{display:"flex","align-items":"stretch",height:"30px","margin-left":"50px","border-top":"1px solid rgb(40 44 52 / 80%)"}},ps={__name:"Dashboard",setup(r){v({token:{colorPrimary:"#1890ff"}});const e=v(!1);let s=null;const l=v([{title:"场景图",key:"scene",checkable:!1,layerType:1,index:0,children:[{title:"地形级",key:"scene-terrain",checkable:!1,index:0,layerType:1,children:[]},{title:"城市级",checkable:!1,key:"scene-city",index:1,layerType:1,children:[]},{title:"部件级",key:"scene-part",checkable:!1,index:2,layerType:1,children:[]}]},{title:"实体图",checkable:!1,key:"entity",layerType:2,index:1,children:[{title:"地形级",checkable:!1,index:0,layerType:2,key:"entity-terrain",children:[]},{title:"城市级",checkable:!1,index:1,layerType:2,key:"entity-city",children:[]},{title:"部件级",checkable:!1,index:2,layerType:2,key:"entity-part",children:[]}]},{title:"底图",checkable:!1,key:"baseMap",children:[]}]),n=v([]),c=v([]),u=v([]),a=v(""),b=v("");Re();const A=v(!0),L=v([]),R=v("layer"),G=v(!1);let F=null;const U={columns:Te,checkboxConfig:{highlight:!0,labelField:"layerName",checkRowKeys:n.value},layerQueryColumns:Te,showHeader:!1,pagerConfig:{enabled:!0,layouts:["PrevPage","JumpNumber","NextPage","FullJump"]},height:"600px",proxyConfig:{ajax:{query:(m,y)=>V(null,[m,y],function*({page:t},p){let w=F;if(G.value&&(w=at(De),F=w),!w.coordinates||w.coordinates.length===0)return{total:0,items:[]};const x={page:{current:t.currentPage,size:t.pageSize,searchCount:!0},condition:{coordinates:w.coordinates}},B=yield nt(x);let I=B==null?void 0:B.total,X=B.records;return{total:I,items:X}})}},rowConfig:{isHover:!0,keyField:"layerId"}},O={checkboxChange(m){return V(this,arguments,function*({row:t,checked:p}){if(console.log("选中状态改变:",t,"是否选中:",p),t.key=t.layerId,p){c.value.push(t),n.value&&(n.value.checked?n.value.checked.push(t.key):n.value.push(t.key)),yield he(t);return}if(n.value){let y=n.value.checked?n.value.checked:n.value,w=y.indexOf(t.key);w>-1&&y.splice(w,1)}me(c.value,t),s.unLoadLayer(t.key)})}},[H,D]=tt({gridOptions:U,gridEvents:O,gridClass:"custom-table-class"}),te=()=>{le(),G.value=!0,De.add("polygon")},le=()=>{xe.emit("captureEnd",{target:"tool"}),xe.emit("clearAll",{target:"tool"}),G.value=!1},T=()=>{if(!G.value){yt("请先绘制查询区域！");return}D.reload(),se()},se=()=>{!n.value&&!n.value.checked||setTimeout(()=>{if(!D||!D.grid)return;D.grid.setAllCheckboxRow&&D.grid.setAllCheckboxRow(!1);let t=n.value.checked?n.value.checked:n.value;D.grid.setCheckboxRowKey&&D.grid.setCheckboxRowKey(t,!0)},100)},Ce=()=>{A.value=!A.value},ae=t=>V(null,null,function*(){e.value=!0,s=M.global.GISLayers;let p=s.getTreeData(),m=l.value[l.value.length-1];m.children=m.children.concat(p[0].children),n.value=Ae.chkLayers}),ne=t=>V(null,null,function*(){const{index:p,key:m,title:y,parent:w,layerType:x}=t;if(t.children&&t.children.length===0){w.node.index;let B=x,I=t.collectionId,Q=(yield _e(B,y,I)).map(_=>pe(ue({},_),{checkable:_.isLeaf}));Q&&Q.length>0&&(t.children=t.children.concat(Q),t.dataRef.children=Q)}}),_e=(t,p,m)=>V(null,null,function*(){let y=yield ot(t,p,m),w=[];return y&&y.forEach(x=>{m!=null&&m.length>0?w.push(pe(ue({},x),{title:x.layerName,key:x.layerId,layerType:t,isLeaf:!0})):w.push(pe(ue({},x),{title:x.collectionName,key:x.collectionId,layerType:t,isLeaf:!1,children:[]}))}),w}),ye=t=>V(null,null,function*(){yield Ae.addLayerDatas([{id:t.layerId,name:t.title,type:t.mapServerType,url:t.layerUrl}]),s.showAndZoomToLayer(t.key,t)}),he=t=>V(null,null,function*(){if(!t.releaseStatus){const m=ht("图层发布中...");let y=yield it(t.dataType,t.layerId);y&&y.layerId&&(t.releaseStatus=1,t.layerUrl=y.layerUrl,t.layerId=y.layerId,t.mapServerType=y.mapServerType,t.url=y.layerUrl,t.mapServerBox=y.mapServerBox,m(),yield ye(t));return}yield ye(t)}),me=(t,p)=>{for(var m=0,y=0;m<t.length;m++)t[m].layerId!=p.layerId&&(t[y++]=t[m]);t.length>0&&(t.length-=1)},f=(x,B)=>V(null,[x,B],function*(t,{checked:p,checkedNodes:m,node:y,event:w}){if(Ae.chkLayers=t,p){if(y.data&&y.data.type){s.showAndZoomToLayer(y.key,y);return}c.value.push(y.dataRef),yield he(y.dataRef),se();return}me(c.value,y.dataRef),s.unLoadLayer(y.key),se()}),C=t=>{const p=t.node.key,m=t.dragNode.key,y=t.node.pos.split("-"),w=t.dropPosition-Number(y[y.length-1]),x=(_,J,ie)=>{_.forEach((oe,be)=>{if(oe.key===J)return ie(oe,be,_);if(oe.children)return x(oe.children,J,ie)})},B=JSON.parse(JSON.stringify(l.value));let I,X,Q;if(x(B,m,(_,J,ie)=>{X=ie,ie.splice(J,1),I=_}),!t.dropToGap)x(B,p,_=>{_.children=_.children||[],_.children.unshift(I),Q=_.children});else if((t.node.children||[]).length>0&&t.node.expanded&&w===1)x(B,p,_=>{_.children=_.children||[],_.children.unshift(I),Q=_.children});else{let _=[],J=0;x(B,p,(ie,oe,be)=>{_=be,J=oe}),Q=_,w===-1?_.splice(J,0,I):_.splice(J+1,0,I)}if(X==Q){const _=Le(B);s.sortByKeys(_),l.value=B}};dt(()=>V(null,null,function*(){b.value=`${document.documentElement.clientHeight-34}px`,window.addEventListener("resize",()=>{setTimeout(()=>{b.value=`${document.documentElement.clientHeight-34}px`},300)},!1)}));let Y=null;function re(t){const p=s.getLayerProperty(t);p&&(Y=Z.open({title:"图层配置",style:{left:"unset",right:"100px",top:"150px",width:"300px"},beforeClose:()=>{Y=null}},h(layerPropsSetting,pe(ue({},p),{save:m=>{s.setLayerProperty(t,m)}})),Y))}const ce=(t,p)=>{const m=u.value.indexOf(p.key);m>-1?u.value.splice(m,1):u.value.push(p.key)};return(t,p)=>{const m=g("a-tree"),y=g("a-tab-pane"),w=g("a-button"),x=g("a-tabs"),B=g("a-card");return k(),E(ve,null,[i(N(et),{class:"size-8 foldMenu",onClick:Ce}),A.value?(k(),z(B,{key:0,class:"layerTree",bodyStyle:{padding:"0px"}},{default:d(()=>[i(x,{activeKey:R.value,"onUpdate:activeKey":p[2]||(p[2]=I=>R.value=I),class:"custom-tabs",centered:""},{default:d(()=>[i(y,{key:"layer",tab:"图层列表"},{default:d(()=>[o("div",as,[i(m,{"checked-keys":n.value,"onUpdate:checkedKeys":p[0]||(p[0]=I=>n.value=I),"expanded-keys":u.value,"onUpdate:expandedKeys":p[1]||(p[1]=I=>u.value=I),selectable:!1,"tree-data":l.value,checkable:!0,checkStrictly:"","block-node":"",virtual:"","default-expand-all":"","load-data":ne,onCheck:f,onDrop:C,onClick:ce},{title:d(({title:I,key:X,data:Q})=>[o("div",ns,[o("span",{style:j({color:a.value&&I.indexOf(a.value)>-1?"red":"unset"})},W(I),5),o("span",{style:{cursor:"pointer"},onClick:Pe(_=>re(X),["stop"])},[Q.data&&Q.data.type&&Q.data.type!="terrain"?(k(),E("span",os)):S("",!0)],8,is)])]),_:1},8,["checked-keys","expanded-keys","tree-data"])])]),_:1}),i(y,{key:"query",tab:"图层查询"},{default:d(()=>[o("div",ls,[o("div",rs,[i(w,{onClick:te},{default:d(()=>p[3]||(p[3]=[P(" 绘制范围 ")])),_:1}),i(w,{onClick:le},{default:d(()=>p[4]||(p[4]=[P(" 清除选区 ")])),_:1}),i(w,{onClick:T},{default:d(()=>p[5]||(p[5]=[P(" 查询 ")])),_:1})]),o("div",cs,[i(N(H))])])]),_:1})]),_:1},8,["activeKey"])]),_:1})):S("",!0),i(pt,{"visible-layers":L.value,onViewMapLoaded:ae},null,8,["visible-layers"]),e.value?(k(),E("div",ds,[o("div",us,[i(wt,{style:{bottom:"0","padding-top":"2px","margin-left":"20px"}}),i(It,{style:{bottom:"0",left:"200px"}})])])):S("",!0),i(ss,{checkOptLayers:c.value},null,8,["checkOptLayers"]),i(ft,{style:{top:"110px",right:"20px",bottom:"unset",left:"unset"}})],64)}}},Ss=q(ps,[["__scopeId","data-v-f8830e93"]]);export{Ss as default};
