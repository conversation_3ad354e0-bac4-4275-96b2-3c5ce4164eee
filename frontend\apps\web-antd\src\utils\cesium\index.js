import * as Cesium from 'cesium';

import { capp } from '../app';
import { mapInitOptions } from './config';
import Hotkey from './core/Hotkey';
import KeepView from './mapTools/keepView';

export default class CesiumMap {
  constructor(viewDiv, callback) {
    this.initOptions = {
      animation: false, // 控制场景动画的播放速度控件
      baseLayerPicker: false, // 底图切换控件
      fullscreenButton: false, // 全屏控件
      geocoder: false, // 地理位置查询定位控件
      homeButton: false, // 默认相机位置控件
      timeline: false, // 时间滚动条控件
      infoBox: false, // 是否显示信息框
      sceneModePicker: false, // 是否显示3D/2D选择器
      selectionIndicator: false, // 点击点绿色弹出 是否显示选取指示器组件
      sceneMode: Cesium.SceneMode.SCENE3D, // 设定3维地图的默认场景模式:Cesium.SceneMode.SCENE2D、Cesium.SceneMode.SCENE3D、Cesium.SceneMode.MORPHING
      navigationHelpButton: false, // 默认的相机控制提示控件
      scene3DOnly: false, // 每个几何实例仅以3D渲染以节省GPU内存
      navigationInstructionsInitiallyVisible: false,
      showRenderLoopErrors: false, // 是否显示渲染错误
      orderIndependentTranslucency: false, // 设置背景透明
      // terrainProvider: Cesium.createWorldTerrain({
      //     requestVertexNormals: true,
      //     requestWaterMask: true
      // }),
      mapMode2D: Cesium.MapMode2D.ROTATE, // 二维旋转视角
      contextOptions: {
        // 截图配置
        webgl: {
          alpha: true,
          depth: true,
          stencil: true,
          antialias: true,
          premultipliedAlpha: true,
          // 通过canvas.toDataURL()实现截图需要将该项设置为true
          preserveDrawingBuffer: true,
          failIfMajorPerformanceCaveat: true,
        },
      },
      imageryProvider: false,

      //  //影像底图
      //  url: "http://t{s}.tianditu.com/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=76620c5b63143f80f875dffb556b57ef",
      //  subdomains: ['0','1','2','3','4','5','6','7'],
      //  layer: "tdtImgLayer",
      //  style: "default",
      //  format: "image/jpeg",
      //  tileMatrixSetID: "GoogleMapsCompatible",//使用谷歌的瓦片切片方式
      //  show: true
      // })
    };
    // this.layers = []
    this.init(viewDiv, callback);
  }

  async init(viewDiv, callback) {
    // 测试token
    // Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.Le6XuBlhPd9ZiDDRfoFOEvziBkjmvar8TetNYCL1aRc';
    // 正式token
    Cesium.Ion.defaultAccessToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI3MmE1NGFlNC1hZDNkLTRhMjEtODY2OC05ZjQzZDVmMzZiNjciLCJpZCI6MjQxNTUzLCJpYXQiOjE3MjYyOTU2OTV9.9c-7Dhe21dqbq4HjLyV8wceZSShZVcstL9yDPmUpILw';

    // 设置默认范围
    const rectangle =
      (capp && capp.extent) ||
      (mapInitOptions.extent
        ? Cesium.Rectangle.fromDegrees(
            mapInitOptions.extent[0],
            mapInitOptions.extent[1],
            mapInitOptions.extent[2],
            mapInitOptions.extent[3],
          )
        : Cesium.Rectangle.fromDegrees(-180, -90, 180, 90));
    Cesium.Camera.DEFAULT_VIEW_FACTOR = 0;
    Cesium.Camera.DEFAULT_VIEW_RECTANGLE = rectangle;
    try {
      document.getElementById(viewDiv).innerHTML = '';
      // this.viewer = new Cesium.Viewer(viewDiv, {
      //   terrainProvider: await Cesium.CesiumTerrainProvider.fromIonAssetId(1),
      //   ...this.initOptions
      // });
      this.viewer = new Cesium.Viewer(viewDiv, this.initOptions);
    } catch {
      this.initOptions.contextOptions = {};
      document.getElementById(viewDiv).innerHTML = '';
      this.viewer = new Cesium.Viewer(viewDiv, this.initOptions);
    }
    this.keepViewer = new KeepView(this.viewer);
    this.hotkey = new Hotkey({
      viewer: this.viewer,
    });
    this.viewer._cesiumWidget._creditContainer.style.display = 'none';
    callback && callback(this.viewer);
    // 屏蔽Cesium的默认双击追踪选中entity行为
    this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
      Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK,
    );
    // cesium默认右键为放大缩小，此处给zoomEventTypes设置新值
    this.viewer.scene.screenSpaceCameraController.zoomEventTypes = [
      Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK,
    ];
    // earthsdk默认右键为改变视角，此处禁止。
    this.viewer.scene.screenSpaceCameraController.lookEventTypes = [];
    // this.viewer.imageryLayers.removeAll();
    this.viewer.scene.globe.depthTestAgainstTerrain = false;

    // 抗锯齿
    this.viewer.scene.postProcessStages.fxaa.enabled = false;
    // 星空背景透明
    this.viewer.scene.skyBox.show = false;
    this.viewer.scene.backgroundColor = new Cesium.Color(0, 0, 0, 0);

    console.log('开始画点线面');

    const viewer = this.viewer;
  }
}

export let GISMap = null;
// export let GISMapside = null

export function getGisMap(callback) {
  const timer = setInterval(() => {
    if (GISMap) {
      callback && callback(GISMap);
      clearInterval(timer);
    }
  }, 1000);
}

export function initMap(viewDiv, callback) {
  GISMap = new CesiumMap(viewDiv, callback);
  return GISMap;
}
