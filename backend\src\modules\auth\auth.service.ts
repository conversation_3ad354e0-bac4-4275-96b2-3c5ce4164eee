import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '@/modules/users/users.service';
import { User } from '@/modules/users/entities/user.entity';
import { LoginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  async validateUser(account: string, password: string): Promise<User | null> {
    // 支持用户名或手机号登录
    let user = await this.usersService.findByUsername(account);
    if (!user) {
      // 如果按用户名找不到，尝试按手机号查找
      user = await this.usersService.findByPhone(account);
    }

    if (!user) {
      return null;
    }

    if (user.status !== 1) {
      throw new UnauthorizedException('用户已被禁用');
    }

    const isPasswordValid = await this.usersService.validatePassword(user, password);
    if (!isPasswordValid) {
      return null;
    }

    return user;
  }

  async login(loginDto: LoginDto) {
    // 忽略captcha参数，不进行验证码验证
    const user = await this.validateUser(loginDto.account, loginDto.password);
    if (!user) {
      throw new UnauthorizedException('账号或密码错误');
    }

    // 更新最后登录时间
    await this.usersService.updateLastLoginTime(user.id);

    // 构建JWT payload，包含更多信息
    const payload = {
      accountId: user.id.toString(),
      roleId: user.roles?.[0]?.id?.toString() || '',
      appId: '1584770581682618369', // 固定应用ID
      name: user.realName,
      roleType: '1', // 默认角色类型
      userId: user.id.toString(),
      account: loginDto.account
    };

    const token = this.jwtService.sign(payload);

    return {
      roleType: 1,
      name: user.realName,
      token,
      account: loginDto.account,
    };
  }

  async logout(_user: User) {
    // 这里可以实现token黑名单等逻辑
    return { message: '退出成功' };
  }

  async getProfile(userId: number) {
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new UnauthorizedException('用户不存在');
    }

    return {
      id: user.id,
      username: user.username,
      realName: user.realName,
      email: user.email,
      phone: user.phone,
      avatar: user.avatar,
      department: user.department,
      position: user.position,
      roles: user.roles,
    };
  }

  async changePassword(userId: number, changePasswordDto: ChangePasswordDto) {
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new UnauthorizedException('用户不存在');
    }

    // 验证原密码
    const isOldPasswordValid = await this.usersService.validatePassword(user, changePasswordDto.oldPassword);
    if (!isOldPasswordValid) {
      throw new BadRequestException('原密码错误');
    }

    // 检查新密码是否与原密码相同
    if (changePasswordDto.oldPassword === changePasswordDto.newPassword) {
      throw new BadRequestException('新密码不能与原密码相同');
    }

    // 更新密码
    await this.usersService.updatePassword(userId, changePasswordDto.newPassword);

    return { message: '密码修改成功' };
  }
}
