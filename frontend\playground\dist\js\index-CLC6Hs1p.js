var M=Object.defineProperty,W=Object.defineProperties;var w=Object.getOwnPropertyDescriptors;var y=Object.getOwnPropertySymbols;var B=Object.prototype.hasOwnProperty,T=Object.prototype.propertyIsEnumerable;var _=(a,t,e)=>t in a?M(a,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[t]=e,b=(a,t)=>{for(var e in t||(t={}))B.call(t,e)&&_(a,e,t[e]);if(y)for(var e of y(t))T.call(t,e)&&_(a,e,t[e]);return a},V=(a,t)=>W(a,w(t));import{bR as E,aI as A}from"./bootstrap-DShsrVit.js";import{a4 as k,J as L,O as c,av as z,af as g,am as N,n as s,ah as i,ap as p,ae as v,ac as O,a3 as o,ag as F,an as n,ao as x}from"../jse/index-index-BMh_AyeW.js";import{C as r}from"./index-B_b7xM74.js";import{_ as j}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";const q=k({__name:"ellipsis-text",props:{expand:{type:Boolean,default:!1},line:{default:1},maxWidth:{default:"100%"},placement:{default:"top"},tooltip:{type:Boolean,default:!0},tooltipBackgroundColor:{default:""},tooltipColor:{default:""},tooltipFontSize:{default:14},tooltipMaxWidth:{default:void 0},tooltipOverlayStyle:{default:()=>({textAlign:"justify"})}},emits:["expandChange"],setup(a,{emit:t}){const e=a,u=t,C=L(()=>typeof e.maxWidth=="number"?`${e.maxWidth}px`:e.maxWidth),m=c(),d=c(!1),h=c();z(()=>{var l;e.tooltip&&m.value&&(h.value=(l=e.tooltipMaxWidth)!=null?l:m.value.offsetWidth+24)},{flush:"post"});function $(){d.value=!d.value,u("expandChange",d.value)}function S(){e.expand&&$()}return(l,R)=>(g(),N("div",null,[s(o(E),{"content-style":V(b({},l.tooltipOverlayStyle),{maxWidth:`${h.value}px`,fontSize:`${l.tooltipFontSize}px`,color:l.tooltipColor,backgroundColor:l.tooltipBackgroundColor}),disabled:!e.tooltip||d.value,side:l.placement},{trigger:i(()=>[p("div",O({ref_key:"ellipsis",ref:m,class:[{"!cursor-pointer":l.expand,"block truncate":l.line===1,[l.$style.ellipsisMultiLine]:l.line>1},"cursor-text overflow-hidden"],style:{"-webkit-line-clamp":d.value?"":l.line,"max-width":C.value},onClick:S},l.$attrs),[v(l.$slots,"default")],16)]),default:i(()=>[v(l.$slots,"tooltip",{},()=>[v(l.$slots,"default")])]),_:3},8,["content-style","disabled","side"])]))}}),D="_ellipsisMultiLine_fwtmq_2",I={ellipsisMultiLine:D},J={$style:I},f=A(q,[["__cssModules",J]]),P="Vben Admin 是一个基于 Vue3.0、Vite、 TypeScript 的后台解决方案，目标是为开发中大型项目提供开箱即用的解决方案。包括二次封装组件、utils、hooks、动态菜单、权限校验、多主题配置、按钮级别权限控制等功能。项目会使用前端较新的技术栈，可以作为项目的启动模版，以帮助你快速搭建企业级中后台产品原型。也可以作为一个示例，用于学习 vue3、vite、ts 等主流技术。该项目会持续跟进最新技术，并将其应用在项目中。Vben Admin 是一个基于 Vue3.0、Vite、 TypeScript 的后台解决方案，目标是为开发中大型项目提供开箱即用的解决方案。包括二次封装组件、utils、hooks、动态菜单、权限校验、多主题配置、按钮级别权限控制等功能。项目会使用前端较新的技术栈，可以作为项目的启动模版，以帮助你快速搭建企业级中后台产品原型。也可以作为一个示例，用于学习 vue3、vite、ts 等主流技术。该项目会持续跟进最新技术，并将其应用在项目中。Vben Admin 是一个基于 Vue3.0、Vite、 TypeScript 的后台解决方案，目标是为开发中大型项目提供开箱即用的解决方案。包括二次封装组件、utils、hooks、动态菜单、权限校验、多主题配置、按钮级别权限控制等功能。项目会使用前端较新的技术栈，可以作为项目的启动模版，以帮助你快速搭建企业级中后台产品原型。也可以作为一个示例，用于学习 vue3、vite、ts 等主流技术。该项目会持续跟进最新技术，并将其应用在项目中。Vben Admin 是一个基于 Vue3.0、Vite、 TypeScript 的后台解决方案，目标是为开发中大型项目提供开箱即用的解决方案。包括二次封装组件、utils、hooks、动态菜单、权限校验、多主题配置、按钮级别权限控制等功能。项目会使用前端较新的技术栈，可以作为项目的启动模版，以帮助你快速搭建企业级中后台产品原型。也可以作为一个示例，用于学习 vue3、vite、ts 等主流技术。该项目会持续跟进最新技术，并将其应用在项目中。",X=k({__name:"index",setup(a){const t=c(P);return(e,u)=>(g(),F(o(j),{description:"用于多行文本省略，支持点击展开和自定义内容。",title:"文本省略组件示例"},{default:i(()=>[s(o(r),{class:"mb-4",title:"基本使用"},{default:i(()=>[s(o(f),{"max-width":500},{default:i(()=>[n(x(t.value),1)]),_:1})]),_:1}),s(o(r),{class:"mb-4",title:"多行省略"},{default:i(()=>[s(o(f),{line:2},{default:i(()=>[n(x(t.value),1)]),_:1})]),_:1}),s(o(r),{class:"mb-4",title:"点击展开"},{default:i(()=>[s(o(f),{line:3,expand:""},{default:i(()=>[n(x(t.value),1)]),_:1})]),_:1}),s(o(r),{class:"mb-4",title:"自定义内容"},{default:i(()=>[s(o(f),{"max-width":240},{tooltip:i(()=>u[0]||(u[0]=[p("div",{style:{"text-align":"center"}},[n(" 《秦皇岛》"),p("br"),n("住在我心里孤独的"),p("br"),n("孤独的海怪 痛苦之王"),p("br"),n("开始厌倦 深海的光 停滞的海浪 ")],-1)])),default:i(()=>[u[1]||(u[1]=n(" 住在我心里孤独的 孤独的海怪 痛苦之王 开始厌倦 深海的光 停滞的海浪 "))]),_:1})]),_:1})]),_:1}))}});export{X as default};
