import pcasCode from '#/assets/pcas-code.json';

export interface RegionOption {
  label: string;
  value: string;
  children?: RegionOption[];
}

export class RegionUtils {
  private static provinces: RegionOption[] = [];

  // 初始化省市区数据
  static initRegionData(): RegionOption[] {
    if (this.provinces.length === 0) {
      this.provinces = pcasCode.map(province => ({
        label: province.name,
        value: province.code,
        children: province.children?.map(city => ({
          label: city.name,
          value: city.code,
          children: city.children?.map(area => ({
            label: area.name,
            value: area.code
          }))
        }))
      }));
    }
    return this.provinces;
  }


  static getProvinces() {
    const provinces = this.initRegionData();
    return provinces;
  }



  // 根据区域代码获取完整名称
  static getRegionNameByCode(code: string): string {
    if (!code) return '';
    let provinces = this.getProvinces();
    const province = provinces.find(p => code.startsWith(p.value));
    if (!province) return '';

    const city = province.children?.find(c => code.startsWith(c.value));
    if (!city) return province.label;

    const area = city.children?.find(a => code === a.value);
    if (!area) return `${province.label}${city.label}`;

    return {province:province.label,city:city.label,area:area.label};
  }

  // 获取默认的省份代码（福建省）
  static getDefaultProvinceCode(): string[] {
    return ['35'];
  }
}
