import{ao as s,k as i,aP as a,l as e,ay as t,j as n}from"./chunks/framework.C8U7mBUf.js";const l=JSON.parse('{"title":"Quick Start","description":"","frontmatter":{"outline":"deep"},"headers":[],"relativePath":"en/guide/introduction/quick-start.md","filePath":"en/guide/introduction/quick-start.md"}');const h=s({name:"en/guide/introduction/quick-start.md"},[["render",function(s,l,h,p,r,o){const d=t("NolebaseGitContributors"),c=t("NolebaseGitChangelog");return n(),i("div",null,[l[0]||(l[0]=a('<h1 id="quick-start" tabindex="-1">Quick Start <a class="header-anchor" href="#quick-start" aria-label="Permalink to &quot;Quick Start {#quick-start}&quot;">​</a></h1><h2 id="prerequisites" tabindex="-1">Prerequisites <a class="header-anchor" href="#prerequisites" aria-label="Permalink to &quot;Prerequisites&quot;">​</a></h2><div class="info custom-block"><p class="custom-block-title">Environment Requirements</p><p>Before starting the project, ensure that your environment meets the following requirements:</p><ul><li><a href="https://nodejs.org/en" target="_blank" rel="noreferrer">Node.js</a> version 20.15.0 or above. It is recommended to use <a href="https://github.com/Schniz/fnm" target="_blank" rel="noreferrer">fnm</a>, <a href="https://github.com/nvm-sh/nvm" target="_blank" rel="noreferrer">nvm</a>, or directly use <a href="https://pnpm.io/cli/env" target="_blank" rel="noreferrer">pnpm</a> for version management.</li><li><a href="https://git-scm.com/" target="_blank" rel="noreferrer">Git</a> any version.</li></ul><p>To verify if your environment meets the above requirements, you can check the versions using the following commands:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Ensure the correct node LTS version is displayed</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">node</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> -v</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Ensure the correct git version is displayed</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> -v</span></span></code></pre></div></div><h2 id="starting-the-project" tabindex="-1">Starting the Project <a class="header-anchor" href="#starting-the-project" aria-label="Permalink to &quot;Starting the Project&quot;">​</a></h2><h3 id="obtain-the-source-code" tabindex="-1">Obtain the Source Code <a class="header-anchor" href="#obtain-the-source-code" aria-label="Permalink to &quot;Obtain the Source Code&quot;">​</a></h3><div class="vp-code-group vp-adaptive-theme"><div class="tabs"><input type="radio" name="group-E0vd0" id="tab-zzW42Wn" checked><label data-title="GitHub" for="tab-zzW42Wn">GitHub</label><input type="radio" name="group-E0vd0" id="tab-mkiK8kK"><label data-title="Gitee" for="tab-mkiK8kK">Gitee</label></div><div class="blocks"><div class="language-bash vp-adaptive-theme active"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Clone the code</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> clone</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> https://github.com/vbenjs/vue-vben-admin.git</span></span></code></pre></div><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Clone the code</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># The Gitee repository may not have the latest code</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> clone</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> https://gitee.com/annsion/vue-vben-admin.git</span></span></code></pre></div></div></div><div class="danger custom-block"><p class="custom-block-title">Caution</p><p>Ensure that the directory where you store the code and all its parent directories do not contain Chinese, Korean, Japanese characters, or spaces, as this may cause errors when installing dependencies and starting the project.</p></div><h3 id="install-dependencies" tabindex="-1">Install Dependencies <a class="header-anchor" href="#install-dependencies" aria-label="Permalink to &quot;Install Dependencies&quot;">​</a></h3><p>Open a terminal in your code directory and execute the following commands:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Enter the project directory</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">cd</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vue-vben-admin</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Enable the project-specified version of pnpm</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">corepack</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> enable</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Install dependencies</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> install</span></span></code></pre></div><div class="tip custom-block"><p class="custom-block-title">Note</p><p>The project only supports using <code>pnpm</code> for installing dependencies. By default, <code>corepack</code> will be used to install the specified version of <code>pnpm</code>.</p></div><h3 id="run-the-project" tabindex="-1">Run the Project <a class="header-anchor" href="#run-the-project" aria-label="Permalink to &quot;Run the Project&quot;">​</a></h3><p>Execute the following command to run the project:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Start the project</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> dev</span></span></code></pre></div><p>You will see an output similar to the following, allowing you to select the project you want to run:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">◆</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">  Select</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> the</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> app</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> you</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> need</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> to</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> run</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> [dev]:</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">  ●</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> @vben/web-antd</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">  ○</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> @vben/web-ele</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">  ○</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> @vben/web-naive</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">  ○</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> @vben/docs</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">│</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">  ○</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> @vben/playground</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">└</span></span></code></pre></div><p>Now, you can visit <code>http://localhost:5555</code> in your browser to view the project.</p>',17)),e(d),e(c)])}]]);export{l as __pageData,h as default};
