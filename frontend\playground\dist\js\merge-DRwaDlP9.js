var K=(i,e,t)=>new Promise((n,r)=>{var o=s=>{try{l(t.next(s))}catch(c){r(c)}},m=s=>{try{l(t.throw(s))}catch(c){r(c)}},l=s=>s.done?n(s.value):Promise.resolve(s.value).then(o,m);l((t=t.apply(i,e)).next())});import{bT as k,a3 as d,X as A,W as z,an as ii,ao as P,U as u,bU as ri,a9 as X,aq as ei,bV as si,Z as li,aj as ai,ag as ci,ah as di,ai as pi,al as mi,av as gi,bW as ui,ay as $i,at as hi,ap as fi,bX as Si,bY as bi,by as G,bw as vi,B as Ci,bv as U}from"./bootstrap-DShsrVit.js";import{u as q}from"./form-DnT3S1ma.js";import{J as M,a7 as Ii,a5 as yi,a6 as xi,a4 as R,n as p,O as J,af as wi,ag as zi,ah as _,a3 as T,an as Ti,ap as Y,ak as Q}from"../jse/index-index-BMh_AyeW.js";import{C as Xi}from"./index-B_b7xM74.js";import{_ as Di}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";const Pi=i=>({xs:`(max-width: ${i.screenXSMax}px)`,sm:`(min-width: ${i.screenSM}px)`,md:`(min-width: ${i.screenMD}px)`,lg:`(min-width: ${i.screenLG}px)`,xl:`(min-width: ${i.screenXL}px)`,xxl:`(min-width: ${i.screenXXL}px)`,xxxl:`{min-width: ${i.screenXXXL}px}`});function Wi(){const[,i]=k();return M(()=>{const e=Pi(i.value),t=new Map;let n=-1,r={};return{matchHandlers:{},dispatch(o){return r=o,t.forEach(m=>m(r)),t.size>=1},subscribe(o){return t.size||this.register(),n+=1,t.set(n,o),o(r),n},unsubscribe(o){t.delete(o),t.size||this.unregister()},unregister(){Object.keys(e).forEach(o=>{const m=e[o],l=this.matchHandlers[m];l==null||l.mql.removeListener(l==null?void 0:l.listener)}),t.clear()},register(){Object.keys(e).forEach(o=>{const m=e[o],l=c=>{let{matches:f}=c;this.dispatch(d(d({},r),{[o]:f}))},s=window.matchMedia(m);s.addListener(l),this.matchHandlers[m]={mql:s,listener:l},l(s)})},responsiveMap:e}})}function Hi(){const i=Ii({});let e=null;const t=Wi();return yi(()=>{e=t.value.subscribe(n=>{i.value=n})}),xi(()=>{t.value.unsubscribe(e)}),i}function Z(i){return typeof i=="string"}function Bi(){}const ti=()=>({prefixCls:String,itemWidth:String,active:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},status:P(),iconPrefix:String,icon:u.any,adjustMarginRight:String,stepNumber:Number,stepIndex:Number,description:u.any,title:u.any,subTitle:u.any,progressDot:ri(u.oneOfType([u.looseBool,u.func])),tailContent:u.any,icons:u.shape({finish:u.any,error:u.any}).loose,onClick:X(),onStepClick:X(),stepIcon:X(),itemRender:X(),__legacy:ei()}),ni=R({compatConfig:{MODE:3},name:"Step",inheritAttrs:!1,props:ti(),setup(i,e){let{slots:t,emit:n,attrs:r}=e;const o=l=>{n("click",l),n("stepClick",i.stepIndex)},m=l=>{let{icon:s,title:c,description:f}=l;const{prefixCls:a,stepNumber:C,status:$,iconPrefix:y,icons:g,progressDot:I=t.progressDot,stepIcon:w=t.stepIcon}=i;let h;const b=A(`${a}-icon`,`${y}icon`,{[`${y}icon-${s}`]:s&&Z(s),[`${y}icon-check`]:!s&&$==="finish"&&(g&&!g.finish||!g),[`${y}icon-cross`]:!s&&$==="error"&&(g&&!g.error||!g)}),S=p("span",{class:`${a}-icon-dot`},null);return I?typeof I=="function"?h=p("span",{class:`${a}-icon`},[I({iconDot:S,index:C-1,status:$,title:c,description:f,prefixCls:a})]):h=p("span",{class:`${a}-icon`},[S]):s&&!Z(s)?h=p("span",{class:`${a}-icon`},[s]):g&&g.finish&&$==="finish"?h=p("span",{class:`${a}-icon`},[g.finish]):g&&g.error&&$==="error"?h=p("span",{class:`${a}-icon`},[g.error]):s||$==="finish"||$==="error"?h=p("span",{class:b},null):h=p("span",{class:`${a}-icon`},[C]),w&&(h=w({index:C-1,status:$,title:c,description:f,node:h})),h};return()=>{var l,s,c,f;const{prefixCls:a,itemWidth:C,active:$,status:y="wait",tailContent:g,adjustMarginRight:I,disabled:w,title:h=(l=t.title)===null||l===void 0?void 0:l.call(t),description:b=(s=t.description)===null||s===void 0?void 0:s.call(t),subTitle:S=(c=t.subTitle)===null||c===void 0?void 0:c.call(t),icon:v=(f=t.icon)===null||f===void 0?void 0:f.call(t),onClick:x,onStepClick:D}=i,W=y||"wait",F=A(`${a}-item`,`${a}-item-${W}`,{[`${a}-item-custom`]:v,[`${a}-item-active`]:$,[`${a}-item-disabled`]:w===!0}),H={};C&&(H.width=C),I&&(H.marginRight=I);const B={onClick:x||Bi};D&&!w&&(B.role="button",B.tabindex=0,B.onClick=o);const L=p("div",z(z({},ii(r,["__legacy"])),{},{class:[F,r.class],style:[r.style,H]}),[p("div",z(z({},B),{},{class:`${a}-item-container`}),[p("div",{class:`${a}-item-tail`},[g]),p("div",{class:`${a}-item-icon`},[m({icon:v,title:h,description:b})]),p("div",{class:`${a}-item-content`},[p("div",{class:`${a}-item-title`},[h,S&&p("div",{title:typeof S=="string"?S:void 0,class:`${a}-item-subtitle`},[S])]),b&&p("div",{class:`${a}-item-description`},[b])])])]);return i.itemRender?i.itemRender(L):L}}});var Mi=function(i,e){var t={};for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&e.indexOf(n)<0&&(t[n]=i[n]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(i);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(i,n[r])&&(t[n[r]]=i[n[r]]);return t};const Ni=R({compatConfig:{MODE:3},name:"Steps",props:{type:u.string.def("default"),prefixCls:u.string.def("vc-steps"),iconPrefix:u.string.def("vc"),direction:u.string.def("horizontal"),labelPlacement:u.string.def("horizontal"),status:P("process"),size:u.string.def(""),progressDot:u.oneOfType([u.looseBool,u.func]).def(void 0),initial:u.number.def(0),current:u.number.def(0),items:u.array.def(()=>[]),icons:u.shape({finish:u.any,error:u.any}).loose,stepIcon:X(),isInline:u.looseBool,itemRender:X()},emits:["change"],setup(i,e){let{slots:t,emit:n}=e;const r=l=>{const{current:s}=i;s!==l&&n("change",l)},o=(l,s,c)=>{const{prefixCls:f,iconPrefix:a,status:C,current:$,initial:y,icons:g,stepIcon:I=t.stepIcon,isInline:w,itemRender:h,progressDot:b=t.progressDot}=i,S=w||b,v=d(d({},l),{class:""}),x=y+s,D={active:x===$,stepNumber:x+1,stepIndex:x,key:x,prefixCls:f,iconPrefix:a,progressDot:S,stepIcon:I,icons:g,onStepClick:r};return C==="error"&&s===$-1&&(v.class=`${f}-next-error`),v.status||(x===$?v.status=C:x<$?v.status="finish":v.status="wait"),w&&(v.icon=void 0,v.subTitle=void 0),c?c(d(d({},v),D)):(h&&(v.itemRender=W=>h(v,W)),p(ni,z(z(z({},v),D),{},{__legacy:!1}),null))},m=(l,s)=>o(d({},l.props),s,c=>li(l,c));return()=>{var l;const{prefixCls:s,direction:c,type:f,labelPlacement:a,iconPrefix:C,status:$,size:y,current:g,progressDot:I=t.progressDot,initial:w,icons:h,items:b,isInline:S,itemRender:v}=i,x=Mi(i,["prefixCls","direction","type","labelPlacement","iconPrefix","status","size","current","progressDot","initial","icons","items","isInline","itemRender"]),D=f==="navigation",W=S||I,F=S?"horizontal":c,H=S?void 0:y,B=W?"vertical":a,L=A(s,`${s}-${c}`,{[`${s}-${H}`]:H,[`${s}-label-${B}`]:F==="horizontal",[`${s}-dot`]:!!W,[`${s}-navigation`]:D,[`${s}-inline`]:S});return p("div",z({class:L},x),[b.filter(j=>j).map((j,oi)=>o(j,oi)),si((l=t.default)===null||l===void 0?void 0:l.call(t)).map(m)])}}}),_i=i=>{const{componentCls:e,stepsIconCustomTop:t,stepsIconCustomSize:n,stepsIconCustomFontSize:r}=i;return{[`${e}-item-custom`]:{[`> ${e}-item-container > ${e}-item-icon`]:{height:"auto",background:"none",border:0,[`> ${e}-icon`]:{top:t,width:n,height:n,fontSize:r,lineHeight:`${n}px`}}},[`&:not(${e}-vertical)`]:{[`${e}-item-custom`]:{[`${e}-item-icon`]:{width:"auto",background:"none"}}}}},Oi=i=>{const{componentCls:e,stepsIconSize:t,lineHeight:n,stepsSmallIconSize:r}=i;return{[`&${e}-label-vertical`]:{[`${e}-item`]:{overflow:"visible","&-tail":{marginInlineStart:t/2+i.controlHeightLG,padding:`${i.paddingXXS}px ${i.paddingLG}px`},"&-content":{display:"block",width:(t/2+i.controlHeightLG)*2,marginTop:i.marginSM,textAlign:"center"},"&-icon":{display:"inline-block",marginInlineStart:i.controlHeightLG},"&-title":{paddingInlineEnd:0,paddingInlineStart:0,"&::after":{display:"none"}},"&-subtitle":{display:"block",marginBottom:i.marginXXS,marginInlineStart:0,lineHeight:n}},[`&${e}-small:not(${e}-dot)`]:{[`${e}-item`]:{"&-icon":{marginInlineStart:i.controlHeightLG+(t-r)/2}}}}}},Ri=i=>{const{componentCls:e,stepsNavContentMaxWidth:t,stepsNavArrowColor:n,stepsNavActiveColor:r,motionDurationSlow:o}=i;return{[`&${e}-navigation`]:{paddingTop:i.paddingSM,[`&${e}-small`]:{[`${e}-item`]:{"&-container":{marginInlineStart:-i.marginSM}}},[`${e}-item`]:{overflow:"visible",textAlign:"center","&-container":{display:"inline-block",height:"100%",marginInlineStart:-i.margin,paddingBottom:i.paddingSM,textAlign:"start",transition:`opacity ${o}`,[`${e}-item-content`]:{maxWidth:t},[`${e}-item-title`]:d(d({maxWidth:"100%",paddingInlineEnd:0},ai),{"&::after":{display:"none"}})},[`&:not(${e}-item-active)`]:{[`${e}-item-container[role='button']`]:{cursor:"pointer","&:hover":{opacity:.85}}},"&:last-child":{flex:1,"&::after":{display:"none"}},"&::after":{position:"absolute",top:`calc(50% - ${i.paddingSM/2}px)`,insetInlineStart:"100%",display:"inline-block",width:i.fontSizeIcon,height:i.fontSizeIcon,borderTop:`${i.lineWidth}px ${i.lineType} ${n}`,borderBottom:"none",borderInlineStart:"none",borderInlineEnd:`${i.lineWidth}px ${i.lineType} ${n}`,transform:"translateY(-50%) translateX(-50%) rotate(45deg)",content:'""'},"&::before":{position:"absolute",bottom:0,insetInlineStart:"50%",display:"inline-block",width:0,height:i.lineWidthBold,backgroundColor:r,transition:`width ${o}, inset-inline-start ${o}`,transitionTimingFunction:"ease-out",content:'""'}},[`${e}-item${e}-item-active::before`]:{insetInlineStart:0,width:"100%"}},[`&${e}-navigation${e}-vertical`]:{[`> ${e}-item`]:{marginInlineEnd:0,"&::before":{display:"none"},[`&${e}-item-active::before`]:{top:0,insetInlineEnd:0,insetInlineStart:"unset",display:"block",width:i.lineWidth*3,height:`calc(100% - ${i.marginLG}px)`},"&::after":{position:"relative",insetInlineStart:"50%",display:"block",width:i.controlHeight*.25,height:i.controlHeight*.25,marginBottom:i.marginXS,textAlign:"center",transform:"translateY(-50%) translateX(-50%) rotate(135deg)"},[`> ${e}-item-container > ${e}-item-tail`]:{visibility:"hidden"}}},[`&${e}-navigation${e}-horizontal`]:{[`> ${e}-item > ${e}-item-container > ${e}-item-tail`]:{visibility:"hidden"}}}},Li=i=>{const{antCls:e,componentCls:t}=i;return{[`&${t}-with-progress`]:{[`${t}-item`]:{paddingTop:i.paddingXXS,[`&-process ${t}-item-container ${t}-item-icon ${t}-icon`]:{color:i.processIconColor}},[`&${t}-vertical > ${t}-item `]:{paddingInlineStart:i.paddingXXS,[`> ${t}-item-container > ${t}-item-tail`]:{top:i.marginXXS,insetInlineStart:i.stepsIconSize/2-i.lineWidth+i.paddingXXS}},[`&, &${t}-small`]:{[`&${t}-horizontal ${t}-item:first-child`]:{paddingBottom:i.paddingXXS,paddingInlineStart:i.paddingXXS}},[`&${t}-small${t}-vertical > ${t}-item > ${t}-item-container > ${t}-item-tail`]:{insetInlineStart:i.stepsSmallIconSize/2-i.lineWidth+i.paddingXXS},[`&${t}-label-vertical`]:{[`${t}-item ${t}-item-tail`]:{top:i.margin-2*i.lineWidth}},[`${t}-item-icon`]:{position:"relative",[`${e}-progress`]:{position:"absolute",insetBlockStart:(i.stepsIconSize-i.stepsProgressSize-i.lineWidth*2)/2,insetInlineStart:(i.stepsIconSize-i.stepsProgressSize-i.lineWidth*2)/2}}}}},Ei=i=>{const{componentCls:e,descriptionWidth:t,lineHeight:n,stepsCurrentDotSize:r,stepsDotSize:o,motionDurationSlow:m}=i;return{[`&${e}-dot, &${e}-dot${e}-small`]:{[`${e}-item`]:{"&-title":{lineHeight:n},"&-tail":{top:Math.floor((i.stepsDotSize-i.lineWidth*3)/2),width:"100%",marginTop:0,marginBottom:0,marginInline:`${t/2}px 0`,padding:0,"&::after":{width:`calc(100% - ${i.marginSM*2}px)`,height:i.lineWidth*3,marginInlineStart:i.marginSM}},"&-icon":{width:o,height:o,marginInlineStart:(i.descriptionWidth-o)/2,paddingInlineEnd:0,lineHeight:`${o}px`,background:"transparent",border:0,[`${e}-icon-dot`]:{position:"relative",float:"left",width:"100%",height:"100%",borderRadius:100,transition:`all ${m}`,"&::after":{position:"absolute",top:-i.marginSM,insetInlineStart:(o-i.controlHeightLG*1.5)/2,width:i.controlHeightLG*1.5,height:i.controlHeight,background:"transparent",content:'""'}}},"&-content":{width:t},[`&-process ${e}-item-icon`]:{position:"relative",top:(o-r)/2,width:r,height:r,lineHeight:`${r}px`,background:"none",marginInlineStart:(i.descriptionWidth-r)/2},[`&-process ${e}-icon`]:{[`&:first-child ${e}-icon-dot`]:{insetInlineStart:0}}}},[`&${e}-vertical${e}-dot`]:{[`${e}-item-icon`]:{marginTop:(i.controlHeight-o)/2,marginInlineStart:0,background:"none"},[`${e}-item-process ${e}-item-icon`]:{marginTop:(i.controlHeight-r)/2,top:0,insetInlineStart:(o-r)/2,marginInlineStart:0},[`${e}-item > ${e}-item-container > ${e}-item-tail`]:{top:(i.controlHeight-o)/2,insetInlineStart:0,margin:0,padding:`${o+i.paddingXS}px 0 ${i.paddingXS}px`,"&::after":{marginInlineStart:(o-i.lineWidth)/2}},[`&${e}-small`]:{[`${e}-item-icon`]:{marginTop:(i.controlHeightSM-o)/2},[`${e}-item-process ${e}-item-icon`]:{marginTop:(i.controlHeightSM-r)/2},[`${e}-item > ${e}-item-container > ${e}-item-tail`]:{top:(i.controlHeightSM-o)/2}},[`${e}-item:first-child ${e}-icon-dot`]:{insetInlineStart:0},[`${e}-item-content`]:{width:"inherit"}}}},Ai=i=>{const{componentCls:e}=i;return{[`&${e}-rtl`]:{direction:"rtl",[`${e}-item`]:{"&-subtitle":{float:"left"}},[`&${e}-navigation`]:{[`${e}-item::after`]:{transform:"rotate(-45deg)"}},[`&${e}-vertical`]:{[`> ${e}-item`]:{"&::after":{transform:"rotate(225deg)"},[`${e}-item-icon`]:{float:"right"}}},[`&${e}-dot`]:{[`${e}-item-icon ${e}-icon-dot, &${e}-small ${e}-item-icon ${e}-icon-dot`]:{float:"right"}}}}},Fi=i=>{const{componentCls:e,stepsSmallIconSize:t,fontSizeSM:n,fontSize:r,colorTextDescription:o}=i;return{[`&${e}-small`]:{[`&${e}-horizontal:not(${e}-label-vertical) ${e}-item`]:{paddingInlineStart:i.paddingSM,"&:first-child":{paddingInlineStart:0}},[`${e}-item-icon`]:{width:t,height:t,marginTop:0,marginBottom:0,marginInline:`0 ${i.marginXS}px`,fontSize:n,lineHeight:`${t}px`,textAlign:"center",borderRadius:t},[`${e}-item-title`]:{paddingInlineEnd:i.paddingSM,fontSize:r,lineHeight:`${t}px`,"&::after":{top:t/2}},[`${e}-item-description`]:{color:o,fontSize:r},[`${e}-item-tail`]:{top:t/2-i.paddingXXS},[`${e}-item-custom ${e}-item-icon`]:{width:"inherit",height:"inherit",lineHeight:"inherit",background:"none",border:0,borderRadius:0,[`> ${e}-icon`]:{fontSize:t,lineHeight:`${t}px`,transform:"none"}}}}},ji=i=>{const{componentCls:e,stepsSmallIconSize:t,stepsIconSize:n}=i;return{[`&${e}-vertical`]:{display:"flex",flexDirection:"column",[`> ${e}-item`]:{display:"block",flex:"1 0 auto",paddingInlineStart:0,overflow:"visible",[`${e}-item-icon`]:{float:"left",marginInlineEnd:i.margin},[`${e}-item-content`]:{display:"block",minHeight:i.controlHeight*1.5,overflow:"hidden"},[`${e}-item-title`]:{lineHeight:`${n}px`},[`${e}-item-description`]:{paddingBottom:i.paddingSM}},[`> ${e}-item > ${e}-item-container > ${e}-item-tail`]:{position:"absolute",top:0,insetInlineStart:i.stepsIconSize/2-i.lineWidth,width:i.lineWidth,height:"100%",padding:`${n+i.marginXXS*1.5}px 0 ${i.marginXXS*1.5}px`,"&::after":{width:i.lineWidth,height:"100%"}},[`> ${e}-item:not(:last-child) > ${e}-item-container > ${e}-item-tail`]:{display:"block"},[` > ${e}-item > ${e}-item-container > ${e}-item-content > ${e}-item-title`]:{"&::after":{display:"none"}},[`&${e}-small ${e}-item-container`]:{[`${e}-item-tail`]:{position:"absolute",top:0,insetInlineStart:i.stepsSmallIconSize/2-i.lineWidth,padding:`${t+i.marginXXS*1.5}px 0 ${i.marginXXS*1.5}px`},[`${e}-item-title`]:{lineHeight:`${t}px`}}}}},Gi=i=>{const{componentCls:e,inlineDotSize:t,inlineTitleColor:n,inlineTailColor:r}=i,o=i.paddingXS+i.lineWidth,m={[`${e}-item-container ${e}-item-content ${e}-item-title`]:{color:n}};return{[`&${e}-inline`]:{width:"auto",display:"inline-flex",[`${e}-item`]:{flex:"none","&-container":{padding:`${o}px ${i.paddingXXS}px 0`,margin:`0 ${i.marginXXS/2}px`,borderRadius:i.borderRadiusSM,cursor:"pointer",transition:`background-color ${i.motionDurationMid}`,"&:hover":{background:i.controlItemBgHover},"&[role='button']:hover":{opacity:1}},"&-icon":{width:t,height:t,marginInlineStart:`calc(50% - ${t/2}px)`,[`> ${e}-icon`]:{top:0},[`${e}-icon-dot`]:{borderRadius:i.fontSizeSM/4}},"&-content":{width:"auto",marginTop:i.marginXS-i.lineWidth},"&-title":{color:n,fontSize:i.fontSizeSM,lineHeight:i.lineHeightSM,fontWeight:"normal",marginBottom:i.marginXXS/2},"&-description":{display:"none"},"&-tail":{marginInlineStart:0,top:o+t/2,transform:"translateY(-50%)","&:after":{width:"100%",height:i.lineWidth,borderRadius:0,marginInlineStart:0,background:r}},[`&:first-child ${e}-item-tail`]:{width:"50%",marginInlineStart:"50%"},[`&:last-child ${e}-item-tail`]:{display:"block",width:"50%"},"&-wait":d({[`${e}-item-icon ${e}-icon ${e}-icon-dot`]:{backgroundColor:i.colorBorderBg,border:`${i.lineWidth}px ${i.lineType} ${r}`}},m),"&-finish":d({[`${e}-item-tail::after`]:{backgroundColor:r},[`${e}-item-icon ${e}-icon ${e}-icon-dot`]:{backgroundColor:r,border:`${i.lineWidth}px ${i.lineType} ${r}`}},m),"&-error":m,"&-active, &-process":d({[`${e}-item-icon`]:{width:t,height:t,marginInlineStart:`calc(50% - ${t/2}px)`,top:0}},m),[`&:not(${e}-item-active) > ${e}-item-container[role='button']:hover`]:{[`${e}-item-title`]:{color:n}}}}}};var N;(function(i){i.wait="wait",i.process="process",i.finish="finish",i.error="error"})(N||(N={}));const E=(i,e)=>{const t=`${e.componentCls}-item`,n=`${i}IconColor`,r=`${i}TitleColor`,o=`${i}DescriptionColor`,m=`${i}TailColor`,l=`${i}IconBgColor`,s=`${i}IconBorderColor`,c=`${i}DotColor`;return{[`${t}-${i} ${t}-icon`]:{backgroundColor:e[l],borderColor:e[s],[`> ${e.componentCls}-icon`]:{color:e[n],[`${e.componentCls}-icon-dot`]:{background:e[c]}}},[`${t}-${i}${t}-custom ${t}-icon`]:{[`> ${e.componentCls}-icon`]:{color:e[c]}},[`${t}-${i} > ${t}-container > ${t}-content > ${t}-title`]:{color:e[r],"&::after":{backgroundColor:e[m]}},[`${t}-${i} > ${t}-container > ${t}-content > ${t}-description`]:{color:e[o]},[`${t}-${i} > ${t}-container > ${t}-tail::after`]:{backgroundColor:e[m]}}},Vi=i=>{const{componentCls:e,motionDurationSlow:t}=i,n=`${e}-item`;return d(d(d(d(d(d({[n]:{position:"relative",display:"inline-block",flex:1,overflow:"hidden",verticalAlign:"top","&:last-child":{flex:"none",[`> ${n}-container > ${n}-tail, > ${n}-container >  ${n}-content > ${n}-title::after`]:{display:"none"}}},[`${n}-container`]:{outline:"none"},[`${n}-icon, ${n}-content`]:{display:"inline-block",verticalAlign:"top"},[`${n}-icon`]:{width:i.stepsIconSize,height:i.stepsIconSize,marginTop:0,marginBottom:0,marginInlineStart:0,marginInlineEnd:i.marginXS,fontSize:i.stepsIconFontSize,fontFamily:i.fontFamily,lineHeight:`${i.stepsIconSize}px`,textAlign:"center",borderRadius:i.stepsIconSize,border:`${i.lineWidth}px ${i.lineType} transparent`,transition:`background-color ${t}, border-color ${t}`,[`${e}-icon`]:{position:"relative",top:i.stepsIconTop,color:i.colorPrimary,lineHeight:1}},[`${n}-tail`]:{position:"absolute",top:i.stepsIconSize/2-i.paddingXXS,insetInlineStart:0,width:"100%","&::after":{display:"inline-block",width:"100%",height:i.lineWidth,background:i.colorSplit,borderRadius:i.lineWidth,transition:`background ${t}`,content:'""'}},[`${n}-title`]:{position:"relative",display:"inline-block",paddingInlineEnd:i.padding,color:i.colorText,fontSize:i.fontSizeLG,lineHeight:`${i.stepsTitleLineHeight}px`,"&::after":{position:"absolute",top:i.stepsTitleLineHeight/2,insetInlineStart:"100%",display:"block",width:9999,height:i.lineWidth,background:i.processTailColor,content:'""'}},[`${n}-subtitle`]:{display:"inline",marginInlineStart:i.marginXS,color:i.colorTextDescription,fontWeight:"normal",fontSize:i.fontSize},[`${n}-description`]:{color:i.colorTextDescription,fontSize:i.fontSize}},E(N.wait,i)),E(N.process,i)),{[`${n}-process > ${n}-container > ${n}-title`]:{fontWeight:i.fontWeightStrong}}),E(N.finish,i)),E(N.error,i)),{[`${n}${e}-next-error > ${e}-item-title::after`]:{background:i.colorError},[`${n}-disabled`]:{cursor:"not-allowed"}})},Ki=i=>{const{componentCls:e,motionDurationSlow:t}=i;return{[`& ${e}-item`]:{[`&:not(${e}-item-active)`]:{[`& > ${e}-item-container[role='button']`]:{cursor:"pointer",[`${e}-item`]:{[`&-title, &-subtitle, &-description, &-icon ${e}-icon`]:{transition:`color ${t}`}},"&:hover":{[`${e}-item`]:{"&-title, &-subtitle, &-description":{color:i.colorPrimary}}}},[`&:not(${e}-item-process)`]:{[`& > ${e}-item-container[role='button']:hover`]:{[`${e}-item`]:{"&-icon":{borderColor:i.colorPrimary,[`${e}-icon`]:{color:i.colorPrimary}}}}}}},[`&${e}-horizontal:not(${e}-label-vertical)`]:{[`${e}-item`]:{paddingInlineStart:i.padding,whiteSpace:"nowrap","&:first-child":{paddingInlineStart:0},[`&:last-child ${e}-item-title`]:{paddingInlineEnd:0},"&-tail":{display:"none"},"&-description":{maxWidth:i.descriptionWidth,whiteSpace:"normal"}}}}},Ui=i=>{const{componentCls:e}=i;return{[e]:d(d(d(d(d(d(d(d(d(d(d(d(d({},pi(i)),{display:"flex",width:"100%",fontSize:0,textAlign:"initial"}),Vi(i)),Ki(i)),_i(i)),Fi(i)),ji(i)),Oi(i)),Ei(i)),Ri(i)),Ai(i)),Li(i)),Gi(i))}},qi=ci("Steps",i=>{const{wireframe:e,colorTextDisabled:t,fontSizeHeading3:n,fontSize:r,controlHeight:o,controlHeightLG:m,colorTextLightSolid:l,colorText:s,colorPrimary:c,colorTextLabel:f,colorTextDescription:a,colorTextQuaternary:C,colorFillContent:$,controlItemBgActive:y,colorError:g,colorBgContainer:I,colorBorderSecondary:w}=i,h=i.controlHeight,b=i.colorSplit,S=di(i,{processTailColor:b,stepsNavArrowColor:t,stepsIconSize:h,stepsIconCustomSize:h,stepsIconCustomTop:0,stepsIconCustomFontSize:m/2,stepsIconTop:-.5,stepsIconFontSize:r,stepsTitleLineHeight:o,stepsSmallIconSize:n,stepsDotSize:o/4,stepsCurrentDotSize:m/4,stepsNavContentMaxWidth:"auto",processIconColor:l,processTitleColor:s,processDescriptionColor:s,processIconBgColor:c,processIconBorderColor:c,processDotColor:c,waitIconColor:e?t:f,waitTitleColor:a,waitDescriptionColor:a,waitTailColor:b,waitIconBgColor:e?I:$,waitIconBorderColor:e?t:"transparent",waitDotColor:t,finishIconColor:c,finishTitleColor:s,finishDescriptionColor:a,finishTailColor:c,finishIconBgColor:e?I:y,finishIconBorderColor:e?c:y,finishDotColor:c,errorIconColor:l,errorTitleColor:g,errorDescriptionColor:g,errorTailColor:b,errorIconBgColor:g,errorIconBorderColor:g,errorDotColor:g,stepsNavActiveColor:c,stepsProgressSize:m,inlineDotSize:6,inlineTitleColor:C,inlineTailColor:w});return[Ui(S)]},{descriptionWidth:140}),Ji=()=>({prefixCls:String,iconPrefix:String,current:Number,initial:Number,percent:Number,responsive:ei(),items:hi(),labelPlacement:P(),status:P(),size:P(),direction:P(),progressDot:fi([Boolean,Function]),type:P(),onChange:X(),"onUpdate:current":X()}),V=R({compatConfig:{MODE:3},name:"ASteps",inheritAttrs:!1,props:mi(Ji(),{current:0,responsive:!0,labelPlacement:"horizontal"}),slots:Object,setup(i,e){let{attrs:t,slots:n,emit:r}=e;const{prefixCls:o,direction:m,configProvider:l}=gi("steps",i),[s,c]=qi(o),[,f]=k(),a=Hi(),C=M(()=>i.responsive&&a.value.xs?"vertical":i.direction),$=M(()=>l.getPrefixCls("",i.iconPrefix)),y=b=>{r("update:current",b),r("change",b)},g=M(()=>i.type==="inline"),I=M(()=>g.value?void 0:i.percent),w=b=>{let{node:S,status:v}=b;if(v==="process"&&i.percent!==void 0){const x=i.size==="small"?f.value.controlHeight:f.value.controlHeightLG;return p("div",{class:`${o.value}-progress-icon`},[p(Si,{type:"circle",percent:I.value,size:x,strokeWidth:4,format:()=>null},null),S])}return S},h=M(()=>({finish:p(ui,{class:`${o.value}-finish-icon`},null),error:p($i,{class:`${o.value}-error-icon`},null)}));return()=>{const b=A({[`${o.value}-rtl`]:m.value==="rtl",[`${o.value}-with-progress`]:I.value!==void 0},t.class,c.value),S=(v,x)=>v.description?p(bi,{title:v.description},{default:()=>[x]}):x;return s(p(Ni,z(z(z({icons:h.value},t),ii(i,["percent","responsive"])),{},{items:i.items,direction:C.value,prefixCls:o.value,iconPrefix:$.value,class:b,onChange:y,isInline:g.value,itemRender:g.value?S:void 0}),d({stepIcon:w},n)))}}}),O=R(d(d({compatConfig:{MODE:3}},ni),{name:"AStep",props:ti()})),Yi=d(V,{Step:O,install:i=>(i.component(V.name,V),i.component(O.name,O),i)}),Qi={class:"mx-auto max-w-lg"},Zi={class:"p-20"},re=R({__name:"merge",setup(i){const e=J(0);function t(a){G.success({content:`form1 values: ${JSON.stringify(a)}`}),e.value=1}function n(){e.value=0}function r(a){G.success({content:`form2 values: ${JSON.stringify(a)}`})}const[o,m]=q({commonConfig:{componentProps:{class:"w-full"}},handleSubmit:t,layout:"horizontal",resetButtonOptions:{show:!1},schema:[{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"formFirst",label:"表单1字段",rules:"required"}],submitButtonOptions:{content:"下一步"},wrapperClass:"grid-cols-1 md:grid-cols-1 lg:grid-cols-1"}),[l,s]=q({commonConfig:{componentProps:{class:"w-full"}},handleReset:n,handleSubmit:r,layout:"horizontal",resetButtonOptions:{content:"上一步"},schema:[{component:"Input",componentProps:{placeholder:"请输入"},fieldName:"formSecond",label:"表单2字段",rules:"required"}],wrapperClass:"grid-cols-1 md:grid-cols-1 lg:grid-cols-1"}),c=J(!0);function f(){return K(this,null,function*(){const a=yield m.merge(s).submitAllForm(c.value);G.success({content:`merged form values: ${JSON.stringify(a)}`})})}return(a,C)=>(wi(),zi(T(Di),{description:"表单组件合并示例：在某些场景下，例如分步表单，需要合并多个表单并统一提交。默认情况下，使用 Object.assign 规则合并表单。如果需要特殊处理数据，可以传入 false。",title:"表单组件"},{default:_(()=>[p(T(Xi),{title:"基础示例"},{extra:_(()=>[p(T(vi),{checked:c.value,"onUpdate:checked":C[0]||(C[0]=$=>c.value=$),"checked-children":"开启字段合并",class:"mr-4","un-checked-children":"关闭字段合并"},null,8,["checked"]),p(T(Ci),{type:"primary",onClick:f},{default:_(()=>C[1]||(C[1]=[Ti("合并提交")])),_:1})]),default:_(()=>[Y("div",Qi,[p(T(Yi),{current:e.value,class:"steps"},{default:_(()=>[p(T(O),{title:"表单1"}),p(T(O),{title:"表单2"})]),_:1},8,["current"]),Y("div",Zi,[Q(p(T(o),null,null,512),[[U,e.value===0]]),Q(p(T(l),null,null,512),[[U,e.value===1]])])])]),_:1})]),_:1}))}});export{re as default};
