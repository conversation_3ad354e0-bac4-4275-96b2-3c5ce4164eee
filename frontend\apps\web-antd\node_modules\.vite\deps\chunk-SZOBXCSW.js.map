{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/grid/src/grid.js", "../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/grid/index.js"], "sourcesContent": ["import { defineComponent, h, ref, computed, provide, reactive, onUnmounted, watch, nextTick, onMounted } from 'vue';\nimport XEUtils from 'xe-utils';\nimport { getLastZIndex, nextZIndex, isEnableConf } from '../../ui/src/utils';\nimport { getOffsetHeight, getPaddingTopBottomSize, getDomNode, toCssUnit } from '../../ui/src/dom';\nimport { VxeUI } from '../../ui';\nimport VxeTableComponent from '../../table/src/table';\nimport VxeToolbarComponent from '../../toolbar/src/toolbar';\nimport tableComponentProps from '../../table/src/props';\nimport tableComponentEmits from '../../table/src/emits';\nimport { getSlotVNs } from '../../ui/src/vn';\nimport { errLog } from '../../ui/src/log';\nconst { getConfig, getI18n, commands, hooks, useFns, createEvent, globalEvents, GLOBAL_EVENT_KEYS, renderEmptyElement } = VxeUI;\nconst tableComponentPropKeys = Object.keys(tableComponentProps);\nconst tableComponentMethodKeys = ['clearAll', 'syncData', 'updateData', 'loadData', 'reloadData', 'reloadRow', 'loadColumn', 'reloadColumn', 'getRowNode', 'getColumnNode', 'getRowIndex', 'getVTRowIndex', 'getVMRowIndex', 'getColumnIndex', 'getVTColumnIndex', 'getVMColumnIndex', 'setRow', 'createData', 'createRow', 'revertData', 'clearData', 'isRemoveByRow', 'isInsertByRow', 'isUpdateByRow', 'getColumns', 'getColumnById', 'getColumnByField', 'getTableColumn', 'getFullColumns', 'getData', 'getCheckboxRecords', 'getParentRow', 'getTreeParentRow', 'getRowSeq', 'getRowById', 'getRowid', 'getTableData', 'getFullData', 'setColumnFixed', 'clearColumnFixed', 'setColumnWidth', 'getColumnWidth', 'setRowHeightConf', 'getRowHeightConf', 'setRowHeight', 'getRowHeight', 'hideColumn', 'showColumn', 'resetColumn', 'refreshColumn', 'refreshScroll', 'recalculate', 'closeTooltip', 'isAllCheckboxChecked', 'isAllCheckboxIndeterminate', 'getCheckboxIndeterminateRecords', 'setCheckboxRow', 'setCheckboxRowKey', 'isCheckedByCheckboxRow', 'isCheckedByCheckboxRowKey', 'isIndeterminateByCheckboxRow', 'isIndeterminateByCheckboxRowKey', 'toggleCheckboxRow', 'setAllCheckboxRow', 'getRadioReserveRecord', 'clearRadioReserve', 'getCheckboxReserveRecords', 'clearCheckboxReserve', 'toggleAllCheckboxRow', 'clearCheckboxRow', 'setCurrentRow', 'isCheckedByRadioRow', 'isCheckedByRadioRowKey', 'setRadioRow', 'setRadioRowKey', 'clearCurrentRow', 'clearRadioRow', 'getCurrentRecord', 'getRadioRecord', 'getCurrentColumn', 'setCurrentColumn', 'clearCurrentColumn', 'setPendingRow', 'togglePendingRow', 'hasPendingByRow', 'isPendingByRow', 'getPendingRecords', 'clearPendingRow', 'sort', 'setSort', 'clearSort', 'isSort', 'getSortColumns', 'closeFilter', 'isFilter', 'isActiveFilterByColumn', 'isRowExpandLoaded', 'clearRowExpandLoaded', 'reloadRowExpand', 'reloadRowExpand', 'toggleRowExpand', 'setAllRowExpand', 'setRowExpand', 'isExpandByRow', 'isRowExpandByRow', 'clearRowExpand', 'clearRowExpandReserve', 'getRowExpandRecords', 'getTreeExpandRecords', 'isTreeExpandLoaded', 'clearTreeExpandLoaded', 'reloadTreeExpand', 'reloadTreeChilds', 'toggleTreeExpand', 'setAllTreeExpand', 'setTreeExpand', 'isTreeExpandByRow', 'clearTreeExpand', 'clearTreeExpandReserve', 'getScroll', 'scrollTo', 'scrollToRow', 'scrollToColumn', 'clearScroll', 'updateFooter', 'updateStatus', 'setMergeCells', 'removeInsertRow', 'removeMergeCells', 'getMergeCells', 'clearMergeCells', 'setMergeFooterItems', 'removeMergeFooterItems', 'getMergeFooterItems', 'clearMergeFooterItems', 'getCustomStoreData', 'setRowGroupExpand', 'setAllRowGroupExpand', 'clearRowGroupExpand', 'isRowGroupExpandByRow', 'isRowGroupRecord', 'setRowGroups', 'clearRowGroups', 'openTooltip', 'moveColumnTo', 'moveRowTo', 'getCellLabel', 'getCellElement', 'focus', 'blur', 'connect'];\nconst gridComponentEmits = [\n    ...tableComponentEmits,\n    'page-change',\n    'form-submit',\n    'form-submit-invalid',\n    'form-reset',\n    'form-collapse',\n    'form-toggle-collapse',\n    'proxy-query',\n    'proxy-delete',\n    'proxy-save',\n    'toolbar-button-click',\n    'toolbar-tool-click',\n    'zoom'\n];\nexport default defineComponent({\n    name: 'VxeGrid',\n    props: Object.assign(Object.assign({}, tableComponentProps), { layouts: Array, columns: Array, pagerConfig: Object, proxyConfig: Object, toolbarConfig: Object, formConfig: Object, zoomConfig: Object, size: {\n            type: String,\n            default: () => getConfig().grid.size || getConfig().size\n        } }),\n    emits: gridComponentEmits,\n    setup(props, context) {\n        var _a;\n        const { slots, emit } = context;\n        const xID = XEUtils.uniqueId();\n        // 使用已安装的组件，如果未安装则不渲染\n        const VxeUIFormComponent = VxeUI.getComponent('VxeForm');\n        const VxeUIPagerComponent = VxeUI.getComponent('VxePager');\n        const defaultLayouts = [['Form'], ['Toolbar', 'Top', 'Table', 'Bottom', 'Pager']];\n        const { computeSize } = useFns.useSize(props);\n        const reactData = reactive({\n            tableLoading: false,\n            proxyInited: false,\n            isZMax: false,\n            tableData: [],\n            filterData: [],\n            formData: {},\n            sortData: [],\n            tZindex: 0,\n            tablePage: {\n                total: 0,\n                pageSize: ((_a = getConfig().pager) === null || _a === void 0 ? void 0 : _a.pageSize) || 10,\n                currentPage: 1\n            }\n        });\n        const refElem = ref();\n        const refTable = ref();\n        const refForm = ref();\n        const refToolbar = ref();\n        const refPager = ref();\n        const refFormWrapper = ref();\n        const refToolbarWrapper = ref();\n        const refTopWrapper = ref();\n        const refBottomWrapper = ref();\n        const refPagerWrapper = ref();\n        const extendTableMethods = (methodKeys) => {\n            const funcs = {};\n            methodKeys.forEach(name => {\n                funcs[name] = (...args) => {\n                    const $xeTable = refTable.value;\n                    if ($xeTable && $xeTable[name]) {\n                        return $xeTable[name](...args);\n                    }\n                };\n            });\n            return funcs;\n        };\n        const gridExtendTableMethods = extendTableMethods(tableComponentMethodKeys);\n        tableComponentMethodKeys.forEach(name => {\n            gridExtendTableMethods[name] = (...args) => {\n                const $xeTable = refTable.value;\n                if ($xeTable && $xeTable[name]) {\n                    return $xeTable && $xeTable[name](...args);\n                }\n            };\n        });\n        const computeProxyOpts = computed(() => {\n            return XEUtils.merge({}, XEUtils.clone(getConfig().grid.proxyConfig, true), props.proxyConfig);\n        });\n        const computeIsRespMsg = computed(() => {\n            const proxyOpts = computeProxyOpts.value;\n            return XEUtils.isBoolean(proxyOpts.message) ? proxyOpts.message : proxyOpts.showResponseMsg;\n        });\n        const computeIsActiveMsg = computed(() => {\n            const proxyOpts = computeProxyOpts.value;\n            return proxyOpts.showActiveMsg;\n        });\n        const computePagerOpts = computed(() => {\n            return Object.assign({}, getConfig().grid.pagerConfig, props.pagerConfig);\n        });\n        const computeFormOpts = computed(() => {\n            return Object.assign({}, getConfig().grid.formConfig, props.formConfig);\n        });\n        const computeToolbarOpts = computed(() => {\n            return Object.assign({}, getConfig().grid.toolbarConfig, props.toolbarConfig);\n        });\n        const computeZoomOpts = computed(() => {\n            return Object.assign({}, getConfig().grid.zoomConfig, props.zoomConfig);\n        });\n        const computeStyles = computed(() => {\n            const { height, maxHeight } = props;\n            const { isZMax, tZindex } = reactData;\n            const stys = {};\n            if (isZMax) {\n                stys.zIndex = tZindex;\n            }\n            else {\n                if (height) {\n                    stys.height = height === 'auto' || height === '100%' ? '100%' : toCssUnit(height);\n                }\n                if (maxHeight) {\n                    stys.maxHeight = maxHeight === 'auto' || maxHeight === '100%' ? '100%' : toCssUnit(maxHeight);\n                }\n            }\n            return stys;\n        });\n        const computeTableExtendProps = computed(() => {\n            const rest = {};\n            const gridProps = props;\n            tableComponentPropKeys.forEach((key) => {\n                rest[key] = gridProps[key];\n            });\n            return rest;\n        });\n        const computeTableProps = computed(() => {\n            const { seqConfig, pagerConfig, loading, editConfig, proxyConfig } = props;\n            const { isZMax, tableLoading, tablePage } = reactData;\n            const tableExtendProps = computeTableExtendProps.value;\n            const proxyOpts = computeProxyOpts.value;\n            const pagerOpts = computePagerOpts.value;\n            const tProps = Object.assign({}, tableExtendProps);\n            if (isZMax) {\n                if (tableExtendProps.maxHeight) {\n                    tProps.maxHeight = '100%';\n                }\n                else {\n                    tProps.height = '100%';\n                }\n            }\n            if (proxyConfig && isEnableConf(proxyOpts)) {\n                tProps.loading = loading || tableLoading;\n                if (pagerConfig && proxyOpts.seq && isEnableConf(pagerOpts)) {\n                    tProps.seqConfig = Object.assign({}, seqConfig, { startIndex: (tablePage.currentPage - 1) * tablePage.pageSize });\n                }\n            }\n            if (editConfig) {\n                tProps.editConfig = Object.assign({}, editConfig);\n            }\n            return tProps;\n        });\n        const computeCurrLayoutConf = computed(() => {\n            const { layouts } = props;\n            let confs = [];\n            if (layouts && layouts.length) {\n                confs = layouts;\n            }\n            else {\n                confs = getConfig().grid.layouts || defaultLayouts;\n            }\n            let headKeys = [];\n            let bodyKeys = [];\n            let footKeys = [];\n            if (confs.length) {\n                if (XEUtils.isArray(confs[0])) {\n                    headKeys = confs[0];\n                    bodyKeys = (confs[1] || []);\n                    footKeys = (confs[2] || []);\n                }\n                else {\n                    bodyKeys = confs;\n                }\n            }\n            return {\n                headKeys,\n                bodyKeys,\n                footKeys\n            };\n        });\n        const computePageConfFlag = computed(() => {\n            const pagerOpts = computePagerOpts.value;\n            return `${pagerOpts.currentPage}${pagerOpts.pageSize}`;\n        });\n        const refMaps = {\n            refElem,\n            refTable,\n            refForm,\n            refToolbar,\n            refPager\n        };\n        const computeMaps = {\n            computeProxyOpts,\n            computePagerOpts,\n            computeFormOpts,\n            computeToolbarOpts,\n            computeZoomOpts\n        };\n        const $xeGrid = {\n            xID,\n            props: props,\n            context,\n            reactData,\n            getRefMaps: () => refMaps,\n            getComputeMaps: () => computeMaps\n        };\n        const initToolbar = () => {\n            const toolbarOpts = computeToolbarOpts.value;\n            if (props.toolbarConfig && isEnableConf(toolbarOpts)) {\n                nextTick(() => {\n                    const $xeTable = refTable.value;\n                    const $xeToolbar = refToolbar.value;\n                    if ($xeTable && $xeToolbar) {\n                        $xeTable.connect($xeToolbar);\n                    }\n                });\n            }\n        };\n        const getFormData = () => {\n            const { proxyConfig } = props;\n            const { formData } = reactData;\n            const proxyOpts = computeProxyOpts.value;\n            const formOpts = computeFormOpts.value;\n            return proxyConfig && isEnableConf(proxyOpts) && proxyOpts.form ? formData : formOpts.data;\n        };\n        const initPages = () => {\n            const { tablePage } = reactData;\n            const { pagerConfig } = props;\n            const pagerOpts = computePagerOpts.value;\n            const { currentPage, pageSize } = pagerOpts;\n            if (pagerConfig && isEnableConf(pagerOpts)) {\n                if (currentPage) {\n                    tablePage.currentPage = currentPage;\n                }\n                if (pageSize) {\n                    tablePage.pageSize = pageSize;\n                }\n            }\n        };\n        const triggerPendingEvent = (code) => {\n            const isActiveMsg = computeIsActiveMsg.value;\n            const $xeTable = refTable.value;\n            const selectRecords = $xeTable.getCheckboxRecords();\n            if (selectRecords.length) {\n                $xeTable.togglePendingRow(selectRecords);\n                gridExtendTableMethods.clearCheckboxRow();\n            }\n            else {\n                if (isActiveMsg) {\n                    if (VxeUI.modal) {\n                        VxeUI.modal.message({ id: code, content: getI18n('vxe.grid.selectOneRecord'), status: 'warning' });\n                    }\n                }\n            }\n        };\n        const getRespMsg = (rest, defaultMsg) => {\n            const proxyOpts = computeProxyOpts.value;\n            const resConfigs = proxyOpts.response || proxyOpts.props || {};\n            const messageProp = resConfigs.message;\n            let msg;\n            if (rest && messageProp) {\n                msg = XEUtils.isFunction(messageProp) ? messageProp({ data: rest, $grid: $xeGrid }) : XEUtils.get(rest, messageProp);\n            }\n            return msg || getI18n(defaultMsg);\n        };\n        const handleDeleteRow = (code, alertKey, callback) => {\n            const isActiveMsg = computeIsActiveMsg.value;\n            const selectRecords = gridExtendTableMethods.getCheckboxRecords();\n            if (isActiveMsg) {\n                if (selectRecords.length) {\n                    if (VxeUI.modal) {\n                        return VxeUI.modal.confirm({ id: `cfm_${code}`, content: getI18n(alertKey), escClosable: true }).then((type) => {\n                            if (type === 'confirm') {\n                                return callback();\n                            }\n                        });\n                    }\n                }\n                else {\n                    if (VxeUI.modal) {\n                        VxeUI.modal.message({ id: `msg_${code}`, content: getI18n('vxe.grid.selectOneRecord'), status: 'warning' });\n                    }\n                }\n            }\n            else {\n                if (selectRecords.length) {\n                    callback();\n                }\n            }\n            return Promise.resolve();\n        };\n        const pageChangeEvent = (params) => {\n            const { proxyConfig } = props;\n            const { tablePage } = reactData;\n            const { $event, currentPage, pageSize } = params;\n            const proxyOpts = computeProxyOpts.value;\n            tablePage.currentPage = currentPage;\n            tablePage.pageSize = pageSize;\n            $xeGrid.dispatchEvent('page-change', params, $event);\n            if (proxyConfig && isEnableConf(proxyOpts)) {\n                $xeGrid.commitProxy('query').then((rest) => {\n                    $xeGrid.dispatchEvent('proxy-query', rest, $event);\n                });\n            }\n        };\n        const sortChangeEvent = (params) => {\n            const $xeTable = refTable.value;\n            const { proxyConfig } = props;\n            const { computeSortOpts } = $xeTable.getComputeMaps();\n            const proxyOpts = computeProxyOpts.value;\n            const sortOpts = computeSortOpts.value;\n            // 如果是服务端排序\n            if (sortOpts.remote) {\n                reactData.sortData = params.sortList;\n                if (proxyConfig && isEnableConf(proxyOpts)) {\n                    reactData.tablePage.currentPage = 1;\n                    gridMethods.commitProxy('query').then((rest) => {\n                        gridMethods.dispatchEvent('proxy-query', rest, params.$event);\n                    });\n                }\n            }\n            gridMethods.dispatchEvent('sort-change', params, params.$event);\n        };\n        const filterChangeEvent = (params) => {\n            const $xeTable = refTable.value;\n            const { proxyConfig } = props;\n            const { computeFilterOpts } = $xeTable.getComputeMaps();\n            const proxyOpts = computeProxyOpts.value;\n            const filterOpts = computeFilterOpts.value;\n            // 如果是服务端过滤\n            if (filterOpts.remote) {\n                reactData.filterData = params.filterList;\n                if (proxyConfig && isEnableConf(proxyOpts)) {\n                    reactData.tablePage.currentPage = 1;\n                    gridMethods.commitProxy('query').then((rest) => {\n                        gridMethods.dispatchEvent('proxy-query', rest, params.$event);\n                    });\n                }\n            }\n            gridMethods.dispatchEvent('filter-change', params, params.$event);\n        };\n        const submitFormEvent = (params) => {\n            const { proxyConfig } = props;\n            const proxyOpts = computeProxyOpts.value;\n            if (reactData.tableLoading) {\n                return;\n            }\n            if (proxyConfig && isEnableConf(proxyOpts)) {\n                gridMethods.commitProxy('reload').then((rest) => {\n                    gridMethods.dispatchEvent('proxy-query', Object.assign(Object.assign({}, rest), { isReload: true }), params.$event);\n                });\n            }\n            gridMethods.dispatchEvent('form-submit', params, params.$event);\n        };\n        const resetFormEvent = (params) => {\n            const { proxyConfig } = props;\n            const { $event } = params;\n            const proxyOpts = computeProxyOpts.value;\n            const $xeTable = refTable.value;\n            if (proxyConfig && isEnableConf(proxyOpts)) {\n                $xeTable.clearScroll();\n                gridMethods.commitProxy('reload').then((rest) => {\n                    gridMethods.dispatchEvent('proxy-query', Object.assign(Object.assign({}, rest), { isReload: true }), $event);\n                });\n            }\n            gridMethods.dispatchEvent('form-reset', params, $event);\n        };\n        const submitInvalidEvent = (params) => {\n            gridMethods.dispatchEvent('form-submit-invalid', params, params.$event);\n        };\n        const collapseEvent = (params) => {\n            const { $event } = params;\n            gridMethods.dispatchEvent('form-toggle-collapse', params, $event);\n            gridMethods.dispatchEvent('form-collapse', params, $event);\n        };\n        const handleZoom = (isMax) => {\n            const { isZMax } = reactData;\n            if (isMax ? !isZMax : isZMax) {\n                reactData.isZMax = !isZMax;\n                if (reactData.tZindex < getLastZIndex()) {\n                    reactData.tZindex = nextZIndex();\n                }\n            }\n            return nextTick()\n                .then(() => gridExtendTableMethods.recalculate(true))\n                .then(() => {\n                setTimeout(() => gridExtendTableMethods.recalculate(true), 15);\n                return reactData.isZMax;\n            });\n        };\n        const getFuncSlot = (optSlots, slotKey) => {\n            const funcSlot = optSlots[slotKey];\n            if (funcSlot) {\n                if (XEUtils.isString(funcSlot)) {\n                    if (slots[funcSlot]) {\n                        return slots[funcSlot];\n                    }\n                    else {\n                        errLog('vxe.error.notSlot', [funcSlot]);\n                    }\n                }\n                else {\n                    return funcSlot;\n                }\n            }\n            return null;\n        };\n        const getConfigSlot = (slotConfigs) => {\n            const slotConf = {};\n            XEUtils.objectMap(slotConfigs, (slotFunc, slotKey) => {\n                if (slotFunc) {\n                    if (XEUtils.isString(slotFunc)) {\n                        if (slots[slotFunc]) {\n                            slotConf[slotKey] = slots[slotFunc];\n                        }\n                        else {\n                            errLog('vxe.error.notSlot', [slotFunc]);\n                        }\n                    }\n                    else {\n                        slotConf[slotKey] = slotFunc;\n                    }\n                }\n            });\n            return slotConf;\n        };\n        /**\n         * 渲染表单\n         */\n        const renderForm = () => {\n            const { formConfig, proxyConfig } = props;\n            const { formData } = reactData;\n            const proxyOpts = computeProxyOpts.value;\n            const formOpts = computeFormOpts.value;\n            if ((formConfig && isEnableConf(formOpts)) || slots.form) {\n                let slotVNs = [];\n                if (slots.form) {\n                    slotVNs = slots.form({ $grid: $xeGrid });\n                }\n                else {\n                    if (formOpts.items) {\n                        const formSlots = {};\n                        if (!formOpts.inited) {\n                            formOpts.inited = true;\n                            const beforeItem = proxyOpts.beforeItem;\n                            if (proxyOpts && beforeItem) {\n                                formOpts.items.forEach((item) => {\n                                    beforeItem({ $grid: $xeGrid, item });\n                                });\n                            }\n                        }\n                        // 处理插槽\n                        formOpts.items.forEach((item) => {\n                            XEUtils.each(item.slots, (func) => {\n                                if (!XEUtils.isFunction(func)) {\n                                    if (slots[func]) {\n                                        formSlots[func] = slots[func];\n                                    }\n                                }\n                            });\n                        });\n                        if (VxeUIFormComponent) {\n                            slotVNs.push(h(VxeUIFormComponent, Object.assign(Object.assign({ ref: refForm }, Object.assign({}, formOpts, {\n                                data: proxyConfig && isEnableConf(proxyOpts) && proxyOpts.form ? formData : formOpts.data\n                            })), { onSubmit: submitFormEvent, onReset: resetFormEvent, onSubmitInvalid: submitInvalidEvent, onCollapse: collapseEvent }), formSlots));\n                        }\n                    }\n                }\n                return h('div', {\n                    ref: refFormWrapper,\n                    key: 'form',\n                    class: 'vxe-grid--form-wrapper'\n                }, slotVNs);\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        /**\n         * 渲染工具栏\n         */\n        const renderToolbar = () => {\n            const { toolbarConfig } = props;\n            const toolbarOpts = computeToolbarOpts.value;\n            if ((toolbarConfig && isEnableConf(toolbarOpts)) || slots.toolbar) {\n                let slotVNs = [];\n                if (slots.toolbar) {\n                    slotVNs = slots.toolbar({ $grid: $xeGrid });\n                }\n                else {\n                    const toolbarOptSlots = toolbarOpts.slots;\n                    let buttonsSlot;\n                    let toolsSlot;\n                    const toolbarSlots = {};\n                    if (toolbarOptSlots) {\n                        buttonsSlot = getFuncSlot(toolbarOptSlots, 'buttons');\n                        toolsSlot = getFuncSlot(toolbarOptSlots, 'tools');\n                        if (buttonsSlot) {\n                            toolbarSlots.buttons = buttonsSlot;\n                        }\n                        if (toolsSlot) {\n                            toolbarSlots.tools = toolsSlot;\n                        }\n                    }\n                    slotVNs.push(h(VxeToolbarComponent, Object.assign(Object.assign({ ref: refToolbar }, toolbarOpts), { slots: undefined }), toolbarSlots));\n                }\n                return h('div', {\n                    ref: refToolbarWrapper,\n                    key: 'toolbar',\n                    class: 'vxe-grid--toolbar-wrapper'\n                }, slotVNs);\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        /**\n         * 渲染表格顶部区域\n         */\n        const renderTop = () => {\n            if (slots.top) {\n                return h('div', {\n                    ref: refTopWrapper,\n                    key: 'top',\n                    class: 'vxe-grid--top-wrapper'\n                }, slots.top({ $grid: $xeGrid }));\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        const renderTableLeft = () => {\n            const leftSlot = slots.left;\n            if (leftSlot) {\n                return h('div', {\n                    class: 'vxe-grid--left-wrapper'\n                }, leftSlot({ $grid: $xeGrid }));\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        const renderTableRight = () => {\n            const rightSlot = slots.right;\n            if (rightSlot) {\n                return h('div', {\n                    class: 'vxe-grid--right-wrapper'\n                }, rightSlot({ $grid: $xeGrid }));\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        /**\n         * 渲染表格\n         */\n        const renderTable = () => {\n            const { proxyConfig } = props;\n            const tableProps = computeTableProps.value;\n            const proxyOpts = computeProxyOpts.value;\n            const tableOns = Object.assign({}, tableCompEvents);\n            const emptySlot = slots.empty;\n            const loadingSlot = slots.loading;\n            const rowDragIconSlot = slots.rowDragIcon || slots['row-drag-icon'];\n            const columnDragIconSlot = slots.columnDragIcon || slots['column-drag-icon'];\n            if (proxyConfig && isEnableConf(proxyOpts)) {\n                if (proxyOpts.sort) {\n                    tableOns.onSortChange = sortChangeEvent;\n                }\n                if (proxyOpts.filter) {\n                    tableOns.onFilterChange = filterChangeEvent;\n                }\n            }\n            const slotObj = {};\n            if (emptySlot) {\n                slotObj.empty = emptySlot;\n            }\n            if (loadingSlot) {\n                slotObj.loading = loadingSlot;\n            }\n            if (rowDragIconSlot) {\n                slotObj.rowDragIcon = rowDragIconSlot;\n            }\n            if (columnDragIconSlot) {\n                slotObj.columnDragIcon = columnDragIconSlot;\n            }\n            return h('div', {\n                class: 'vxe-grid--table-wrapper'\n            }, [\n                h(VxeTableComponent, Object.assign(Object.assign({ ref: refTable }, tableProps), tableOns), slotObj)\n            ]);\n        };\n        /**\n         * 渲染表格底部区域\n         */\n        const renderBottom = () => {\n            if (slots.bottom) {\n                return h('div', {\n                    ref: refBottomWrapper,\n                    key: 'bottom',\n                    class: 'vxe-grid--bottom-wrapper'\n                }, slots.bottom({ $grid: $xeGrid }));\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        /**\n         * 渲染分页\n         */\n        const renderPager = () => {\n            const { proxyConfig, pagerConfig } = props;\n            const proxyOpts = computeProxyOpts.value;\n            const pagerOpts = computePagerOpts.value;\n            const pagerSlot = slots.pager;\n            if ((pagerConfig && isEnableConf(pagerOpts)) || slots.pager) {\n                return h('div', {\n                    ref: refPagerWrapper,\n                    key: 'pager',\n                    class: 'vxe-grid--pager-wrapper'\n                }, pagerSlot\n                    ? pagerSlot({ $grid: $xeGrid })\n                    : [\n                        VxeUIPagerComponent\n                            ? h(VxeUIPagerComponent, Object.assign(Object.assign(Object.assign({ ref: refPager }, pagerOpts), (proxyConfig && isEnableConf(proxyOpts) ? reactData.tablePage : {})), { onPageChange: pageChangeEvent }), getConfigSlot(pagerOpts.slots))\n                            : renderEmptyElement($xeGrid)\n                    ]);\n            }\n            return renderEmptyElement($xeGrid);\n        };\n        const renderChildLayout = (layoutKeys) => {\n            const childVNs = [];\n            layoutKeys.forEach(key => {\n                switch (key) {\n                    case 'Form':\n                        childVNs.push(renderForm());\n                        break;\n                    case 'Toolbar':\n                        childVNs.push(renderToolbar());\n                        break;\n                    case 'Top':\n                        childVNs.push(renderTop());\n                        break;\n                    case 'Table':\n                        childVNs.push(h('div', {\n                            key: 'table',\n                            class: 'vxe-grid--table-container'\n                        }, [\n                            renderTableLeft(),\n                            renderTable(),\n                            renderTableRight()\n                        ]));\n                        break;\n                    case 'Bottom':\n                        childVNs.push(renderBottom());\n                        break;\n                    case 'Pager':\n                        childVNs.push(renderPager());\n                        break;\n                    default:\n                        errLog('vxe.error.notProp', [`layouts -> ${key}`]);\n                        break;\n                }\n            });\n            return childVNs;\n        };\n        const renderLayout = () => {\n            const currLayoutConf = computeCurrLayoutConf.value;\n            const { headKeys, bodyKeys, footKeys } = currLayoutConf;\n            const asideLeftSlot = slots.asideLeft || slots['aside-left'];\n            const asideRightSlot = slots.asideRight || slots['aside-right'];\n            return [\n                h('div', {\n                    class: 'vxe-grid--layout-header-wrapper'\n                }, renderChildLayout(headKeys)),\n                h('div', {\n                    class: 'vxe-grid--layout-body-wrapper'\n                }, [\n                    asideLeftSlot\n                        ? h('div', {\n                            class: 'vxe-grid--layout-aside-left-wrapper'\n                        }, asideLeftSlot({}))\n                        : renderEmptyElement($xeGrid),\n                    h('div', {\n                        class: 'vxe-grid--layout-body-content-wrapper'\n                    }, renderChildLayout(bodyKeys)),\n                    asideRightSlot\n                        ? h('div', {\n                            class: 'vxe-grid--layout-aside-right-wrapper'\n                        }, asideRightSlot({}))\n                        : renderEmptyElement($xeGrid)\n                ]),\n                h('div', {\n                    class: 'vxe-grid--layout-footer-wrapper'\n                }, renderChildLayout(footKeys))\n            ];\n        };\n        const tableCompEvents = {};\n        tableComponentEmits.forEach(name => {\n            const type = XEUtils.camelCase(`on-${name}`);\n            tableCompEvents[type] = (...args) => emit(name, ...args);\n        });\n        const initProxy = () => {\n            const { proxyConfig, formConfig } = props;\n            const { proxyInited } = reactData;\n            const proxyOpts = computeProxyOpts.value;\n            const formOpts = computeFormOpts.value;\n            if (proxyConfig && isEnableConf(proxyOpts)) {\n                if (formConfig && isEnableConf(formOpts) && proxyOpts.form && formOpts.items) {\n                    const fData = {};\n                    formOpts.items.forEach(item => {\n                        const { field, itemRender } = item;\n                        if (field) {\n                            let itemValue = null;\n                            if (itemRender) {\n                                const { defaultValue } = itemRender;\n                                if (XEUtils.isFunction(defaultValue)) {\n                                    itemValue = defaultValue({ item });\n                                }\n                                else if (!XEUtils.isUndefined(defaultValue)) {\n                                    itemValue = defaultValue;\n                                }\n                            }\n                            fData[field] = itemValue;\n                        }\n                    });\n                    reactData.formData = fData;\n                }\n                if (!proxyInited) {\n                    reactData.proxyInited = true;\n                    if (proxyOpts.autoLoad !== false) {\n                        nextTick().then(() => gridMethods.commitProxy('_init')).then((rest) => {\n                            gridMethods.dispatchEvent('proxy-query', Object.assign(Object.assign({}, rest), { isInited: true }), new Event('init'));\n                        });\n                    }\n                }\n            }\n        };\n        const handleGlobalKeydownEvent = (evnt) => {\n            const zoomOpts = computeZoomOpts.value;\n            const isEsc = globalEvents.hasKey(evnt, GLOBAL_EVENT_KEYS.ESCAPE);\n            if (isEsc && reactData.isZMax && zoomOpts.escRestore !== false) {\n                gridPrivateMethods.triggerZoomEvent(evnt);\n            }\n        };\n        const dispatchEvent = (type, params, evnt) => {\n            emit(type, createEvent(evnt, { $grid: $xeGrid }, params));\n        };\n        const gridMethods = {\n            dispatchEvent,\n            getEl() {\n                return refElem.value;\n            },\n            /**\n             * 提交指令，支持 code 或 button\n             * @param {String/Object} code 字符串或对象\n             */\n            commitProxy(proxyTarget, ...args) {\n                const { toolbarConfig, pagerConfig, editRules, validConfig } = props;\n                const { tablePage } = reactData;\n                const isActiveMsg = computeIsActiveMsg.value;\n                const isRespMsg = computeIsRespMsg.value;\n                const proxyOpts = computeProxyOpts.value;\n                const pagerOpts = computePagerOpts.value;\n                const toolbarOpts = computeToolbarOpts.value;\n                const { beforeQuery, afterQuery, beforeDelete, afterDelete, beforeSave, afterSave, ajax = {} } = proxyOpts;\n                const resConfigs = proxyOpts.response || proxyOpts.props || {};\n                const $xeTable = refTable.value;\n                const formData = getFormData();\n                let button = null;\n                let code = null;\n                if (XEUtils.isString(proxyTarget)) {\n                    const { buttons } = toolbarOpts;\n                    const matchObj = toolbarConfig && isEnableConf(toolbarOpts) && buttons ? XEUtils.findTree(buttons, (item) => item.code === proxyTarget, { children: 'dropdowns' }) : null;\n                    button = matchObj ? matchObj.item : null;\n                    code = proxyTarget;\n                }\n                else {\n                    button = proxyTarget;\n                    code = button.code;\n                }\n                const btnParams = button ? button.params : null;\n                switch (code) {\n                    case 'insert':\n                        return $xeTable.insert({});\n                    case 'insert_edit':\n                        return $xeTable.insert({}).then(({ row }) => $xeTable.setEditRow(row));\n                    // 已废弃\n                    case 'insert_actived':\n                        return $xeTable.insert({}).then(({ row }) => $xeTable.setEditRow(row));\n                    // 已废弃\n                    case 'mark_cancel':\n                        triggerPendingEvent(code);\n                        break;\n                    case 'remove':\n                        return handleDeleteRow(code, 'vxe.grid.removeSelectRecord', () => $xeTable.removeCheckboxRow());\n                    case 'import':\n                        $xeTable.importData(btnParams);\n                        break;\n                    case 'open_import':\n                        $xeTable.openImport(btnParams);\n                        break;\n                    case 'export':\n                        $xeTable.exportData(btnParams);\n                        break;\n                    case 'open_export':\n                        $xeTable.openExport(btnParams);\n                        break;\n                    case 'reset_custom':\n                        return $xeTable.resetCustom(true);\n                    case '_init':\n                    case 'reload':\n                    case 'query': {\n                        const ajaxMethods = ajax.query;\n                        const querySuccessMethods = ajax.querySuccess;\n                        const queryErrorMethods = ajax.queryError;\n                        if (ajaxMethods) {\n                            const isInited = code === '_init';\n                            const isReload = code === 'reload';\n                            if (!isInited && reactData.tableLoading) {\n                                return nextTick();\n                            }\n                            let sortList = [];\n                            let filterList = [];\n                            let pageParams = {};\n                            if (pagerConfig) {\n                                if (isInited || isReload) {\n                                    tablePage.currentPage = 1;\n                                }\n                                if (isEnableConf(pagerOpts)) {\n                                    pageParams = Object.assign({}, tablePage);\n                                }\n                            }\n                            if (isInited) {\n                                let defaultSort = null;\n                                if ($xeTable) {\n                                    const { computeSortOpts } = $xeTable.getComputeMaps();\n                                    const sortOpts = computeSortOpts.value;\n                                    defaultSort = sortOpts.defaultSort;\n                                }\n                                // 如果使用默认排序\n                                if (defaultSort) {\n                                    if (!XEUtils.isArray(defaultSort)) {\n                                        defaultSort = [defaultSort];\n                                    }\n                                    sortList = defaultSort.map((item) => {\n                                        return {\n                                            field: item.field,\n                                            property: item.field,\n                                            order: item.order\n                                        };\n                                    });\n                                }\n                                if ($xeTable) {\n                                    filterList = $xeTable.getCheckedFilters();\n                                }\n                            }\n                            else {\n                                if ($xeTable) {\n                                    if (isReload) {\n                                        $xeTable.clearAll();\n                                    }\n                                    else {\n                                        sortList = $xeTable.getSortColumns();\n                                        filterList = $xeTable.getCheckedFilters();\n                                    }\n                                }\n                            }\n                            const commitParams = {\n                                code,\n                                button,\n                                isInited,\n                                isReload,\n                                $grid: $xeGrid,\n                                page: pageParams,\n                                sort: sortList.length ? sortList[0] : {},\n                                sorts: sortList,\n                                filters: filterList,\n                                form: formData,\n                                options: ajaxMethods\n                            };\n                            reactData.sortData = sortList;\n                            reactData.filterData = filterList;\n                            reactData.tableLoading = true;\n                            return Promise.resolve((beforeQuery || ajaxMethods)(commitParams, ...args))\n                                .then(rest => {\n                                let tableData = [];\n                                reactData.tableLoading = false;\n                                if (rest) {\n                                    if (pagerConfig && isEnableConf(pagerOpts)) {\n                                        const totalProp = resConfigs.total;\n                                        const total = (XEUtils.isFunction(totalProp) ? totalProp({ data: rest, $grid: $xeGrid }) : XEUtils.get(rest, totalProp || 'page.total')) || 0;\n                                        tablePage.total = XEUtils.toNumber(total);\n                                        const resultProp = resConfigs.result;\n                                        tableData = (XEUtils.isFunction(resultProp) ? resultProp({ data: rest, $grid: $xeGrid }) : XEUtils.get(rest, resultProp || 'result')) || [];\n                                        // 检验当前页码，不能超出当前最大页数\n                                        const pageCount = Math.max(Math.ceil(total / tablePage.pageSize), 1);\n                                        if (tablePage.currentPage > pageCount) {\n                                            tablePage.currentPage = pageCount;\n                                        }\n                                    }\n                                    else {\n                                        const listProp = resConfigs.list;\n                                        tableData = (listProp ? (XEUtils.isFunction(listProp) ? listProp({ data: rest, $grid: $xeGrid }) : XEUtils.get(rest, listProp)) : rest) || [];\n                                    }\n                                }\n                                if ($xeTable) {\n                                    $xeTable.loadData(tableData);\n                                }\n                                else {\n                                    nextTick(() => {\n                                        if ($xeTable) {\n                                            $xeTable.loadData(tableData);\n                                        }\n                                    });\n                                }\n                                if (afterQuery) {\n                                    afterQuery(commitParams, ...args);\n                                }\n                                if (querySuccessMethods) {\n                                    querySuccessMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));\n                                }\n                                return { status: true };\n                            }).catch((rest) => {\n                                reactData.tableLoading = false;\n                                if (queryErrorMethods) {\n                                    queryErrorMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));\n                                }\n                                return { status: false };\n                            });\n                        }\n                        else {\n                            errLog('vxe.error.notFunc', ['proxy-config.ajax.query']);\n                        }\n                        break;\n                    }\n                    case 'delete': {\n                        const ajaxMethods = ajax.delete;\n                        const deleteSuccessMethods = ajax.deleteSuccess;\n                        const deleteErrorMethods = ajax.deleteError;\n                        if (ajaxMethods) {\n                            const selectRecords = gridExtendTableMethods.getCheckboxRecords();\n                            const removeRecords = selectRecords.filter(row => !$xeTable.isInsertByRow(row));\n                            const body = { removeRecords };\n                            const commitParams = { $grid: $xeGrid, code, button, body, form: formData, options: ajaxMethods };\n                            if (selectRecords.length) {\n                                return handleDeleteRow(code, 'vxe.grid.deleteSelectRecord', () => {\n                                    if (!removeRecords.length) {\n                                        return $xeTable.remove(selectRecords);\n                                    }\n                                    reactData.tableLoading = true;\n                                    return Promise.resolve((beforeDelete || ajaxMethods)(commitParams, ...args))\n                                        .then(rest => {\n                                        reactData.tableLoading = false;\n                                        $xeTable.setPendingRow(removeRecords, false);\n                                        if (isRespMsg) {\n                                            if (VxeUI.modal) {\n                                                VxeUI.modal.message({ content: getRespMsg(rest, 'vxe.grid.delSuccess'), status: 'success' });\n                                            }\n                                        }\n                                        if (afterDelete) {\n                                            afterDelete(commitParams, ...args);\n                                        }\n                                        else {\n                                            gridMethods.commitProxy('query');\n                                        }\n                                        if (deleteSuccessMethods) {\n                                            deleteSuccessMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));\n                                        }\n                                        return { status: true };\n                                    })\n                                        .catch(rest => {\n                                        reactData.tableLoading = false;\n                                        if (isRespMsg) {\n                                            if (VxeUI.modal) {\n                                                VxeUI.modal.message({ id: code, content: getRespMsg(rest, 'vxe.grid.operError'), status: 'error' });\n                                            }\n                                        }\n                                        if (deleteErrorMethods) {\n                                            deleteErrorMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));\n                                        }\n                                        return { status: false };\n                                    });\n                                });\n                            }\n                            else {\n                                if (isActiveMsg) {\n                                    if (VxeUI.modal) {\n                                        VxeUI.modal.message({ id: code, content: getI18n('vxe.grid.selectOneRecord'), status: 'warning' });\n                                    }\n                                }\n                            }\n                        }\n                        else {\n                            errLog('vxe.error.notFunc', ['proxy-config.ajax.delete']);\n                        }\n                        break;\n                    }\n                    case 'save': {\n                        const ajaxMethods = ajax.save;\n                        const saveSuccessMethods = ajax.saveSuccess;\n                        const saveErrorMethods = ajax.saveError;\n                        if (ajaxMethods) {\n                            const body = $xeTable.getRecordset();\n                            const { insertRecords, removeRecords, updateRecords, pendingRecords } = body;\n                            const commitParams = { $grid: $xeGrid, code, button, body, form: formData, options: ajaxMethods };\n                            // 排除掉新增且标记为删除的数据\n                            if (insertRecords.length) {\n                                body.pendingRecords = pendingRecords.filter((row) => $xeTable.findRowIndexOf(insertRecords, row) === -1);\n                            }\n                            // 排除已标记为删除的数据\n                            if (pendingRecords.length) {\n                                body.insertRecords = insertRecords.filter((row) => $xeTable.findRowIndexOf(pendingRecords, row) === -1);\n                            }\n                            let restPromise = Promise.resolve();\n                            if (editRules) {\n                                // 只校验新增和修改的数据\n                                restPromise = $xeTable[validConfig && validConfig.msgMode === 'full' ? 'fullValidate' : 'validate'](body.insertRecords.concat(updateRecords));\n                            }\n                            return restPromise.then((errMap) => {\n                                if (errMap) {\n                                    // 如果校验不通过\n                                    return;\n                                }\n                                if (body.insertRecords.length || removeRecords.length || updateRecords.length || body.pendingRecords.length) {\n                                    reactData.tableLoading = true;\n                                    return Promise.resolve((beforeSave || ajaxMethods)(commitParams, ...args))\n                                        .then(rest => {\n                                        reactData.tableLoading = false;\n                                        $xeTable.clearPendingRow();\n                                        if (isRespMsg) {\n                                            if (VxeUI.modal) {\n                                                VxeUI.modal.message({ content: getRespMsg(rest, 'vxe.grid.saveSuccess'), status: 'success' });\n                                            }\n                                        }\n                                        if (afterSave) {\n                                            afterSave(commitParams, ...args);\n                                        }\n                                        else {\n                                            gridMethods.commitProxy('query');\n                                        }\n                                        if (saveSuccessMethods) {\n                                            saveSuccessMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));\n                                        }\n                                        return { status: true };\n                                    })\n                                        .catch(rest => {\n                                        reactData.tableLoading = false;\n                                        if (isRespMsg) {\n                                            if (VxeUI.modal) {\n                                                VxeUI.modal.message({ id: code, content: getRespMsg(rest, 'vxe.grid.operError'), status: 'error' });\n                                            }\n                                        }\n                                        if (saveErrorMethods) {\n                                            saveErrorMethods(Object.assign(Object.assign({}, commitParams), { response: rest }));\n                                        }\n                                        return { status: false };\n                                    });\n                                }\n                                else {\n                                    if (isActiveMsg) {\n                                        if (VxeUI.modal) {\n                                            VxeUI.modal.message({ id: code, content: getI18n('vxe.grid.dataUnchanged'), status: 'info' });\n                                        }\n                                    }\n                                }\n                            });\n                        }\n                        else {\n                            errLog('vxe.error.notFunc', ['proxy-config.ajax.save']);\n                        }\n                        break;\n                    }\n                    default: {\n                        const gCommandOpts = commands.get(code);\n                        if (gCommandOpts) {\n                            const tCommandMethod = gCommandOpts.tableCommandMethod || gCommandOpts.commandMethod;\n                            if (tCommandMethod) {\n                                tCommandMethod({ code, button, $grid: $xeGrid, $table: $xeTable }, ...args);\n                            }\n                            else {\n                                errLog('vxe.error.notCommands', [code]);\n                            }\n                        }\n                    }\n                }\n                return nextTick();\n            },\n            zoom() {\n                if (reactData.isZMax) {\n                    return gridMethods.revert();\n                }\n                return gridMethods.maximize();\n            },\n            isMaximized() {\n                return reactData.isZMax;\n            },\n            maximize() {\n                return handleZoom(true);\n            },\n            revert() {\n                return handleZoom();\n            },\n            getFormData,\n            getFormItems(itemIndex) {\n                const formOpts = computeFormOpts.value;\n                const { formConfig } = props;\n                const { items } = formOpts;\n                const itemList = [];\n                XEUtils.eachTree(formConfig && isEnableConf(formOpts) && items ? items : [], item => {\n                    itemList.push(item);\n                }, { children: 'children' });\n                return XEUtils.isUndefined(itemIndex) ? itemList : itemList[itemIndex];\n            },\n            getProxyInfo() {\n                const $xeTable = refTable.value;\n                if (props.proxyConfig) {\n                    const { sortData } = reactData;\n                    return {\n                        data: $xeTable ? $xeTable.getFullData() : [],\n                        filter: reactData.filterData,\n                        form: getFormData(),\n                        sort: sortData.length ? sortData[0] : {},\n                        sorts: sortData,\n                        pager: reactData.tablePage,\n                        pendingRecords: $xeTable ? $xeTable.getPendingRecords() : []\n                    };\n                }\n                return null;\n            }\n            // setProxyInfo (options) {\n            //   if (props.proxyConfig && options) {\n            //     const { pager, form } = options\n            //     const proxyOpts = computeProxyOpts.value\n            //     if (pager) {\n            //       if (pager.currentPage) {\n            //         reactData.tablePage.currentPage = Number(pager.currentPage)\n            //       }\n            //       if (pager.pageSize) {\n            //         reactData.tablePage.pageSize = Number(pager.pageSize)\n            //       }\n            //     }\n            //     if (proxyOpts.form && form) {\n            //       Object.assign(reactData.formData, form)\n            //     }\n            //   }\n            //   return nextTick()\n            // }\n        };\n        // 检查插槽\n        if (process.env.NODE_ENV === 'development') {\n            gridMethods.loadColumn = (columns) => {\n                const $xeTable = refTable.value;\n                XEUtils.eachTree(columns, (column) => {\n                    if (column.slots) {\n                        XEUtils.each(column.slots, (func) => {\n                            if (!XEUtils.isFunction(func)) {\n                                if (!slots[func]) {\n                                    errLog('vxe.error.notSlot', [func]);\n                                }\n                            }\n                        });\n                    }\n                });\n                if ($xeTable) {\n                    return $xeTable.loadColumn(columns);\n                }\n                return nextTick();\n            };\n            gridMethods.reloadColumn = (columns) => {\n                gridExtendTableMethods.clearAll();\n                return gridMethods.loadColumn(columns);\n            };\n        }\n        const gridPrivateMethods = {\n            extendTableMethods,\n            callSlot(slotFunc, params) {\n                if (slotFunc) {\n                    if (XEUtils.isString(slotFunc)) {\n                        slotFunc = slots[slotFunc] || null;\n                    }\n                    if (XEUtils.isFunction(slotFunc)) {\n                        return getSlotVNs(slotFunc(params));\n                    }\n                }\n                return [];\n            },\n            /**\n             * 获取需要排除的高度\n             */\n            getExcludeHeight() {\n                const { isZMax } = reactData;\n                const el = refElem.value;\n                if (el) {\n                    const formWrapper = refFormWrapper.value;\n                    const toolbarWrapper = refToolbarWrapper.value;\n                    const topWrapper = refTopWrapper.value;\n                    const bottomWrapper = refBottomWrapper.value;\n                    const pagerWrapper = refPagerWrapper.value;\n                    const parentEl = el.parentElement;\n                    const parentPaddingSize = isZMax ? 0 : (parentEl ? getPaddingTopBottomSize(parentEl) : 0);\n                    return parentPaddingSize + getPaddingTopBottomSize(el) + getOffsetHeight(formWrapper) + getOffsetHeight(toolbarWrapper) + getOffsetHeight(topWrapper) + getOffsetHeight(bottomWrapper) + getOffsetHeight(pagerWrapper);\n                }\n                return 0;\n            },\n            getParentHeight() {\n                const el = refElem.value;\n                if (el) {\n                    const parentEl = el.parentElement;\n                    return (reactData.isZMax ? getDomNode().visibleHeight : (parentEl ? XEUtils.toNumber(getComputedStyle(parentEl).height) : 0)) - gridPrivateMethods.getExcludeHeight();\n                }\n                return 0;\n            },\n            triggerToolbarCommitEvent(params, evnt) {\n                const { code } = params;\n                return gridMethods.commitProxy(params, evnt).then((rest) => {\n                    if (code && rest && rest.status && ['query', 'reload', 'delete', 'save'].includes(code)) {\n                        gridMethods.dispatchEvent(code === 'delete' || code === 'save' ? `proxy-${code}` : 'proxy-query', Object.assign(Object.assign({}, rest), { isReload: code === 'reload' }), evnt);\n                    }\n                });\n            },\n            triggerToolbarBtnEvent(button, evnt) {\n                gridPrivateMethods.triggerToolbarCommitEvent(button, evnt);\n                gridMethods.dispatchEvent('toolbar-button-click', { code: button.code, button }, evnt);\n            },\n            triggerToolbarTolEvent(tool, evnt) {\n                gridPrivateMethods.triggerToolbarCommitEvent(tool, evnt);\n                gridMethods.dispatchEvent('toolbar-tool-click', { code: tool.code, tool }, evnt);\n            },\n            triggerZoomEvent(evnt) {\n                gridMethods.zoom();\n                gridMethods.dispatchEvent('zoom', { type: reactData.isZMax ? 'max' : 'revert' }, evnt);\n            }\n        };\n        Object.assign($xeGrid, gridExtendTableMethods, gridMethods, gridPrivateMethods);\n        const columnFlag = ref(0);\n        watch(() => props.columns ? props.columns.length : -1, () => {\n            columnFlag.value++;\n        });\n        watch(() => props.columns, () => {\n            columnFlag.value++;\n        });\n        watch(columnFlag, () => {\n            nextTick(() => $xeGrid.loadColumn(props.columns || []));\n        });\n        watch(() => props.toolbarConfig, () => {\n            initToolbar();\n        });\n        watch(computePageConfFlag, () => {\n            initPages();\n        });\n        watch(() => props.proxyConfig, () => {\n            initProxy();\n        });\n        hooks.forEach((options) => {\n            const { setupGrid } = options;\n            if (setupGrid) {\n                const hookRest = setupGrid($xeGrid);\n                if (hookRest && XEUtils.isObject(hookRest)) {\n                    Object.assign($xeGrid, hookRest);\n                }\n            }\n        });\n        initPages();\n        onMounted(() => {\n            nextTick(() => {\n                const { columns } = props;\n                if (props.formConfig) {\n                    if (!VxeUIFormComponent) {\n                        errLog('vxe.error.reqComp', ['vxe-form']);\n                    }\n                }\n                if (props.pagerConfig) {\n                    if (!VxeUIPagerComponent) {\n                        errLog('vxe.error.reqComp', ['vxe-pager']);\n                    }\n                }\n                // const { data, columns, proxyConfig } = props\n                // const proxyOpts = computeProxyOpts.value\n                // const formOpts = computeFormOpts.value\n                // if (isEnableConf(proxyConfig) && (data || (proxyOpts.form && formOpts.data))) {\n                //   errLog('vxe.error.errConflicts', ['grid.data', 'grid.proxy-config'])\n                // }\n                //   if (proxyOpts.props) {\n                //     warnLog('vxe.error.delProp', ['proxy-config.props', 'proxy-config.response'])\n                //   }\n                if (columns && columns.length) {\n                    $xeGrid.loadColumn(columns);\n                }\n                initToolbar();\n                initProxy();\n            });\n            globalEvents.on($xeGrid, 'keydown', handleGlobalKeydownEvent);\n        });\n        onUnmounted(() => {\n            globalEvents.off($xeGrid, 'keydown');\n        });\n        const renderVN = () => {\n            const vSize = computeSize.value;\n            const styles = computeStyles.value;\n            return h('div', {\n                ref: refElem,\n                class: ['vxe-grid', {\n                        [`size--${vSize}`]: vSize,\n                        'is--animat': !!props.animat,\n                        'is--round': props.round,\n                        'is--maximize': reactData.isZMax,\n                        'is--loading': props.loading || reactData.tableLoading\n                    }],\n                style: styles\n            }, renderLayout());\n        };\n        $xeGrid.renderVN = renderVN;\n        provide('$xeGrid', $xeGrid);\n        return $xeGrid;\n    },\n    render() {\n        return this.renderVN();\n    }\n});\n", "import { VxeUI } from '../ui';\nimport VxeGridComponent from './src/grid';\nexport const VxeGrid = Object.assign({}, VxeGridComponent, {\n    install(app) {\n        app.component(VxeGridComponent.name, VxeGridComponent);\n    }\n});\nif (VxeUI.dynamicApp) {\n    VxeUI.dynamicApp.component(VxeGridComponent.name, VxeGridComponent);\n}\nVxeUI.component(VxeGridComponent);\nexport const Grid = VxeGrid;\nexport default VxeGrid;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,sBAAoB;AAUpB,IAAM,EAAE,WAAW,SAAS,UAAU,OAAO,QAAQ,aAAa,cAAc,mBAAmB,mBAAmB,IAAI;AAC1H,IAAM,yBAAyB,OAAO,KAAK,aAAmB;AAC9D,IAAM,2BAA2B,CAAC,YAAY,YAAY,cAAc,YAAY,cAAc,aAAa,cAAc,gBAAgB,cAAc,iBAAiB,eAAe,iBAAiB,iBAAiB,kBAAkB,oBAAoB,oBAAoB,UAAU,cAAc,aAAa,cAAc,aAAa,iBAAiB,iBAAiB,iBAAiB,cAAc,iBAAiB,oBAAoB,kBAAkB,kBAAkB,WAAW,sBAAsB,gBAAgB,oBAAoB,aAAa,cAAc,YAAY,gBAAgB,eAAe,kBAAkB,oBAAoB,kBAAkB,kBAAkB,oBAAoB,oBAAoB,gBAAgB,gBAAgB,cAAc,cAAc,eAAe,iBAAiB,iBAAiB,eAAe,gBAAgB,wBAAwB,8BAA8B,mCAAmC,kBAAkB,qBAAqB,0BAA0B,6BAA6B,gCAAgC,mCAAmC,qBAAqB,qBAAqB,yBAAyB,qBAAqB,6BAA6B,wBAAwB,wBAAwB,oBAAoB,iBAAiB,uBAAuB,0BAA0B,eAAe,kBAAkB,mBAAmB,iBAAiB,oBAAoB,kBAAkB,oBAAoB,oBAAoB,sBAAsB,iBAAiB,oBAAoB,mBAAmB,kBAAkB,qBAAqB,mBAAmB,QAAQ,WAAW,aAAa,UAAU,kBAAkB,eAAe,YAAY,0BAA0B,qBAAqB,wBAAwB,mBAAmB,mBAAmB,mBAAmB,mBAAmB,gBAAgB,iBAAiB,oBAAoB,kBAAkB,yBAAyB,uBAAuB,wBAAwB,sBAAsB,yBAAyB,oBAAoB,oBAAoB,oBAAoB,oBAAoB,iBAAiB,qBAAqB,mBAAmB,0BAA0B,aAAa,YAAY,eAAe,kBAAkB,eAAe,gBAAgB,gBAAgB,iBAAiB,mBAAmB,oBAAoB,iBAAiB,mBAAmB,uBAAuB,0BAA0B,uBAAuB,yBAAyB,sBAAsB,qBAAqB,wBAAwB,uBAAuB,yBAAyB,oBAAoB,gBAAgB,kBAAkB,eAAe,gBAAgB,aAAa,gBAAgB,kBAAkB,SAAS,QAAQ,SAAS;AAC5vF,IAAM,qBAAqB;AAAA,EACvB,GAAG;AAAA,EACH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAO,eAAQ,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,aAAmB,GAAG,EAAE,SAAS,OAAO,SAAS,OAAO,aAAa,QAAQ,aAAa,QAAQ,eAAe,QAAQ,YAAY,QAAQ,YAAY,QAAQ,MAAM;AAAA,IACtM,MAAM;AAAA,IACN,SAAS,MAAM,UAAU,EAAE,KAAK,QAAQ,UAAU,EAAE;AAAA,EACxD,EAAE,CAAC;AAAA,EACP,OAAO;AAAA,EACP,MAAM,OAAO,SAAS;AAClB,QAAI;AACJ,UAAM,EAAE,OAAO,KAAK,IAAI;AACxB,UAAM,MAAM,gBAAAA,QAAQ,SAAS;AAE7B,UAAM,qBAAqB,MAAM,aAAa,SAAS;AACvD,UAAM,sBAAsB,MAAM,aAAa,UAAU;AACzD,UAAM,iBAAiB,CAAC,CAAC,MAAM,GAAG,CAAC,WAAW,OAAO,SAAS,UAAU,OAAO,CAAC;AAChF,UAAM,EAAE,YAAY,IAAI,OAAO,QAAQ,KAAK;AAC5C,UAAM,YAAY,SAAS;AAAA,MACvB,cAAc;AAAA,MACd,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,WAAW,CAAC;AAAA,MACZ,YAAY,CAAC;AAAA,MACb,UAAU,CAAC;AAAA,MACX,UAAU,CAAC;AAAA,MACX,SAAS;AAAA,MACT,WAAW;AAAA,QACP,OAAO;AAAA,QACP,YAAY,KAAK,UAAU,EAAE,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AAAA,QACzF,aAAa;AAAA,MACjB;AAAA,IACJ,CAAC;AACD,UAAM,UAAU,IAAI;AACpB,UAAM,WAAW,IAAI;AACrB,UAAM,UAAU,IAAI;AACpB,UAAM,aAAa,IAAI;AACvB,UAAM,WAAW,IAAI;AACrB,UAAM,iBAAiB,IAAI;AAC3B,UAAM,oBAAoB,IAAI;AAC9B,UAAM,gBAAgB,IAAI;AAC1B,UAAM,mBAAmB,IAAI;AAC7B,UAAM,kBAAkB,IAAI;AAC5B,UAAM,qBAAqB,CAAC,eAAe;AACvC,YAAM,QAAQ,CAAC;AACf,iBAAW,QAAQ,UAAQ;AACvB,cAAM,IAAI,IAAI,IAAI,SAAS;AACvB,gBAAM,WAAW,SAAS;AAC1B,cAAI,YAAY,SAAS,IAAI,GAAG;AAC5B,mBAAO,SAAS,IAAI,EAAE,GAAG,IAAI;AAAA,UACjC;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,yBAAyB,mBAAmB,wBAAwB;AAC1E,6BAAyB,QAAQ,UAAQ;AACrC,6BAAuB,IAAI,IAAI,IAAI,SAAS;AACxC,cAAM,WAAW,SAAS;AAC1B,YAAI,YAAY,SAAS,IAAI,GAAG;AAC5B,iBAAO,YAAY,SAAS,IAAI,EAAE,GAAG,IAAI;AAAA,QAC7C;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACpC,aAAO,gBAAAA,QAAQ,MAAM,CAAC,GAAG,gBAAAA,QAAQ,MAAM,UAAU,EAAE,KAAK,aAAa,IAAI,GAAG,MAAM,WAAW;AAAA,IACjG,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACpC,YAAM,YAAY,iBAAiB;AACnC,aAAO,gBAAAA,QAAQ,UAAU,UAAU,OAAO,IAAI,UAAU,UAAU,UAAU;AAAA,IAChF,CAAC;AACD,UAAM,qBAAqB,SAAS,MAAM;AACtC,YAAM,YAAY,iBAAiB;AACnC,aAAO,UAAU;AAAA,IACrB,CAAC;AACD,UAAM,mBAAmB,SAAS,MAAM;AACpC,aAAO,OAAO,OAAO,CAAC,GAAG,UAAU,EAAE,KAAK,aAAa,MAAM,WAAW;AAAA,IAC5E,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM;AACnC,aAAO,OAAO,OAAO,CAAC,GAAG,UAAU,EAAE,KAAK,YAAY,MAAM,UAAU;AAAA,IAC1E,CAAC;AACD,UAAM,qBAAqB,SAAS,MAAM;AACtC,aAAO,OAAO,OAAO,CAAC,GAAG,UAAU,EAAE,KAAK,eAAe,MAAM,aAAa;AAAA,IAChF,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM;AACnC,aAAO,OAAO,OAAO,CAAC,GAAG,UAAU,EAAE,KAAK,YAAY,MAAM,UAAU;AAAA,IAC1E,CAAC;AACD,UAAM,gBAAgB,SAAS,MAAM;AACjC,YAAM,EAAE,QAAQ,UAAU,IAAI;AAC9B,YAAM,EAAE,QAAQ,QAAQ,IAAI;AAC5B,YAAM,OAAO,CAAC;AACd,UAAI,QAAQ;AACR,aAAK,SAAS;AAAA,MAClB,OACK;AACD,YAAI,QAAQ;AACR,eAAK,SAAS,WAAW,UAAU,WAAW,SAAS,SAAS,UAAU,MAAM;AAAA,QACpF;AACA,YAAI,WAAW;AACX,eAAK,YAAY,cAAc,UAAU,cAAc,SAAS,SAAS,UAAU,SAAS;AAAA,QAChG;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,0BAA0B,SAAS,MAAM;AAC3C,YAAM,OAAO,CAAC;AACd,YAAM,YAAY;AAClB,6BAAuB,QAAQ,CAAC,QAAQ;AACpC,aAAK,GAAG,IAAI,UAAU,GAAG;AAAA,MAC7B,CAAC;AACD,aAAO;AAAA,IACX,CAAC;AACD,UAAM,oBAAoB,SAAS,MAAM;AACrC,YAAM,EAAE,WAAW,aAAa,SAAS,YAAY,YAAY,IAAI;AACrE,YAAM,EAAE,QAAQ,cAAc,UAAU,IAAI;AAC5C,YAAM,mBAAmB,wBAAwB;AACjD,YAAM,YAAY,iBAAiB;AACnC,YAAM,YAAY,iBAAiB;AACnC,YAAM,SAAS,OAAO,OAAO,CAAC,GAAG,gBAAgB;AACjD,UAAI,QAAQ;AACR,YAAI,iBAAiB,WAAW;AAC5B,iBAAO,YAAY;AAAA,QACvB,OACK;AACD,iBAAO,SAAS;AAAA,QACpB;AAAA,MACJ;AACA,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,eAAO,UAAU,WAAW;AAC5B,YAAI,eAAe,UAAU,OAAO,aAAa,SAAS,GAAG;AACzD,iBAAO,YAAY,OAAO,OAAO,CAAC,GAAG,WAAW,EAAE,aAAa,UAAU,cAAc,KAAK,UAAU,SAAS,CAAC;AAAA,QACpH;AAAA,MACJ;AACA,UAAI,YAAY;AACZ,eAAO,aAAa,OAAO,OAAO,CAAC,GAAG,UAAU;AAAA,MACpD;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,wBAAwB,SAAS,MAAM;AACzC,YAAM,EAAE,QAAQ,IAAI;AACpB,UAAI,QAAQ,CAAC;AACb,UAAI,WAAW,QAAQ,QAAQ;AAC3B,gBAAQ;AAAA,MACZ,OACK;AACD,gBAAQ,UAAU,EAAE,KAAK,WAAW;AAAA,MACxC;AACA,UAAI,WAAW,CAAC;AAChB,UAAI,WAAW,CAAC;AAChB,UAAI,WAAW,CAAC;AAChB,UAAI,MAAM,QAAQ;AACd,YAAI,gBAAAA,QAAQ,QAAQ,MAAM,CAAC,CAAC,GAAG;AAC3B,qBAAW,MAAM,CAAC;AAClB,qBAAY,MAAM,CAAC,KAAK,CAAC;AACzB,qBAAY,MAAM,CAAC,KAAK,CAAC;AAAA,QAC7B,OACK;AACD,qBAAW;AAAA,QACf;AAAA,MACJ;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,UAAM,sBAAsB,SAAS,MAAM;AACvC,YAAM,YAAY,iBAAiB;AACnC,aAAO,GAAG,UAAU,WAAW,GAAG,UAAU,QAAQ;AAAA,IACxD,CAAC;AACD,UAAM,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,UAAM,cAAc;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,UAAM,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,MAAM;AAAA,MAClB,gBAAgB,MAAM;AAAA,IAC1B;AACA,UAAM,cAAc,MAAM;AACtB,YAAM,cAAc,mBAAmB;AACvC,UAAI,MAAM,iBAAiB,aAAa,WAAW,GAAG;AAClD,iBAAS,MAAM;AACX,gBAAM,WAAW,SAAS;AAC1B,gBAAM,aAAa,WAAW;AAC9B,cAAI,YAAY,YAAY;AACxB,qBAAS,QAAQ,UAAU;AAAA,UAC/B;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,UAAM,cAAc,MAAM;AACtB,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,SAAS,IAAI;AACrB,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,gBAAgB;AACjC,aAAO,eAAe,aAAa,SAAS,KAAK,UAAU,OAAO,WAAW,SAAS;AAAA,IAC1F;AACA,UAAM,YAAY,MAAM;AACpB,YAAM,EAAE,UAAU,IAAI;AACtB,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,YAAY,iBAAiB;AACnC,YAAM,EAAE,aAAa,SAAS,IAAI;AAClC,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,YAAI,aAAa;AACb,oBAAU,cAAc;AAAA,QAC5B;AACA,YAAI,UAAU;AACV,oBAAU,WAAW;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,sBAAsB,CAAC,SAAS;AAClC,YAAM,cAAc,mBAAmB;AACvC,YAAM,WAAW,SAAS;AAC1B,YAAM,gBAAgB,SAAS,mBAAmB;AAClD,UAAI,cAAc,QAAQ;AACtB,iBAAS,iBAAiB,aAAa;AACvC,+BAAuB,iBAAiB;AAAA,MAC5C,OACK;AACD,YAAI,aAAa;AACb,cAAI,MAAM,OAAO;AACb,kBAAM,MAAM,QAAQ,EAAE,IAAI,MAAM,SAAS,QAAQ,0BAA0B,GAAG,QAAQ,UAAU,CAAC;AAAA,UACrG;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,aAAa,CAAC,MAAM,eAAe;AACrC,YAAM,YAAY,iBAAiB;AACnC,YAAM,aAAa,UAAU,YAAY,UAAU,SAAS,CAAC;AAC7D,YAAM,cAAc,WAAW;AAC/B,UAAI;AACJ,UAAI,QAAQ,aAAa;AACrB,cAAM,gBAAAA,QAAQ,WAAW,WAAW,IAAI,YAAY,EAAE,MAAM,MAAM,OAAO,QAAQ,CAAC,IAAI,gBAAAA,QAAQ,IAAI,MAAM,WAAW;AAAA,MACvH;AACA,aAAO,OAAO,QAAQ,UAAU;AAAA,IACpC;AACA,UAAM,kBAAkB,CAAC,MAAM,UAAU,aAAa;AAClD,YAAM,cAAc,mBAAmB;AACvC,YAAM,gBAAgB,uBAAuB,mBAAmB;AAChE,UAAI,aAAa;AACb,YAAI,cAAc,QAAQ;AACtB,cAAI,MAAM,OAAO;AACb,mBAAO,MAAM,MAAM,QAAQ,EAAE,IAAI,OAAO,IAAI,IAAI,SAAS,QAAQ,QAAQ,GAAG,aAAa,KAAK,CAAC,EAAE,KAAK,CAAC,SAAS;AAC5G,kBAAI,SAAS,WAAW;AACpB,uBAAO,SAAS;AAAA,cACpB;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ,OACK;AACD,cAAI,MAAM,OAAO;AACb,kBAAM,MAAM,QAAQ,EAAE,IAAI,OAAO,IAAI,IAAI,SAAS,QAAQ,0BAA0B,GAAG,QAAQ,UAAU,CAAC;AAAA,UAC9G;AAAA,QACJ;AAAA,MACJ,OACK;AACD,YAAI,cAAc,QAAQ;AACtB,mBAAS;AAAA,QACb;AAAA,MACJ;AACA,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AACA,UAAM,kBAAkB,CAAC,WAAW;AAChC,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,UAAU,IAAI;AACtB,YAAM,EAAE,QAAQ,aAAa,SAAS,IAAI;AAC1C,YAAM,YAAY,iBAAiB;AACnC,gBAAU,cAAc;AACxB,gBAAU,WAAW;AACrB,cAAQ,cAAc,eAAe,QAAQ,MAAM;AACnD,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,gBAAQ,YAAY,OAAO,EAAE,KAAK,CAAC,SAAS;AACxC,kBAAQ,cAAc,eAAe,MAAM,MAAM;AAAA,QACrD,CAAC;AAAA,MACL;AAAA,IACJ;AACA,UAAM,kBAAkB,CAAC,WAAW;AAChC,YAAM,WAAW,SAAS;AAC1B,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,gBAAgB,IAAI,SAAS,eAAe;AACpD,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,gBAAgB;AAEjC,UAAI,SAAS,QAAQ;AACjB,kBAAU,WAAW,OAAO;AAC5B,YAAI,eAAe,aAAa,SAAS,GAAG;AACxC,oBAAU,UAAU,cAAc;AAClC,sBAAY,YAAY,OAAO,EAAE,KAAK,CAAC,SAAS;AAC5C,wBAAY,cAAc,eAAe,MAAM,OAAO,MAAM;AAAA,UAChE,CAAC;AAAA,QACL;AAAA,MACJ;AACA,kBAAY,cAAc,eAAe,QAAQ,OAAO,MAAM;AAAA,IAClE;AACA,UAAM,oBAAoB,CAAC,WAAW;AAClC,YAAM,WAAW,SAAS;AAC1B,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,kBAAkB,IAAI,SAAS,eAAe;AACtD,YAAM,YAAY,iBAAiB;AACnC,YAAM,aAAa,kBAAkB;AAErC,UAAI,WAAW,QAAQ;AACnB,kBAAU,aAAa,OAAO;AAC9B,YAAI,eAAe,aAAa,SAAS,GAAG;AACxC,oBAAU,UAAU,cAAc;AAClC,sBAAY,YAAY,OAAO,EAAE,KAAK,CAAC,SAAS;AAC5C,wBAAY,cAAc,eAAe,MAAM,OAAO,MAAM;AAAA,UAChE,CAAC;AAAA,QACL;AAAA,MACJ;AACA,kBAAY,cAAc,iBAAiB,QAAQ,OAAO,MAAM;AAAA,IACpE;AACA,UAAM,kBAAkB,CAAC,WAAW;AAChC,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,YAAY,iBAAiB;AACnC,UAAI,UAAU,cAAc;AACxB;AAAA,MACJ;AACA,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,oBAAY,YAAY,QAAQ,EAAE,KAAK,CAAC,SAAS;AAC7C,sBAAY,cAAc,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,UAAU,KAAK,CAAC,GAAG,OAAO,MAAM;AAAA,QACtH,CAAC;AAAA,MACL;AACA,kBAAY,cAAc,eAAe,QAAQ,OAAO,MAAM;AAAA,IAClE;AACA,UAAM,iBAAiB,CAAC,WAAW;AAC/B,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,EAAE,OAAO,IAAI;AACnB,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,SAAS;AAC1B,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,iBAAS,YAAY;AACrB,oBAAY,YAAY,QAAQ,EAAE,KAAK,CAAC,SAAS;AAC7C,sBAAY,cAAc,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,UAAU,KAAK,CAAC,GAAG,MAAM;AAAA,QAC/G,CAAC;AAAA,MACL;AACA,kBAAY,cAAc,cAAc,QAAQ,MAAM;AAAA,IAC1D;AACA,UAAM,qBAAqB,CAAC,WAAW;AACnC,kBAAY,cAAc,uBAAuB,QAAQ,OAAO,MAAM;AAAA,IAC1E;AACA,UAAM,gBAAgB,CAAC,WAAW;AAC9B,YAAM,EAAE,OAAO,IAAI;AACnB,kBAAY,cAAc,wBAAwB,QAAQ,MAAM;AAChE,kBAAY,cAAc,iBAAiB,QAAQ,MAAM;AAAA,IAC7D;AACA,UAAM,aAAa,CAAC,UAAU;AAC1B,YAAM,EAAE,OAAO,IAAI;AACnB,UAAI,QAAQ,CAAC,SAAS,QAAQ;AAC1B,kBAAU,SAAS,CAAC;AACpB,YAAI,UAAU,UAAU,cAAc,GAAG;AACrC,oBAAU,UAAU,WAAW;AAAA,QACnC;AAAA,MACJ;AACA,aAAO,SAAS,EACX,KAAK,MAAM,uBAAuB,YAAY,IAAI,CAAC,EACnD,KAAK,MAAM;AACZ,mBAAW,MAAM,uBAAuB,YAAY,IAAI,GAAG,EAAE;AAC7D,eAAO,UAAU;AAAA,MACrB,CAAC;AAAA,IACL;AACA,UAAM,cAAc,CAAC,UAAU,YAAY;AACvC,YAAM,WAAW,SAAS,OAAO;AACjC,UAAI,UAAU;AACV,YAAI,gBAAAA,QAAQ,SAAS,QAAQ,GAAG;AAC5B,cAAI,MAAM,QAAQ,GAAG;AACjB,mBAAO,MAAM,QAAQ;AAAA,UACzB,OACK;AACD,mBAAO,qBAAqB,CAAC,QAAQ,CAAC;AAAA,UAC1C;AAAA,QACJ,OACK;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,UAAM,gBAAgB,CAAC,gBAAgB;AACnC,YAAM,WAAW,CAAC;AAClB,sBAAAA,QAAQ,UAAU,aAAa,CAAC,UAAU,YAAY;AAClD,YAAI,UAAU;AACV,cAAI,gBAAAA,QAAQ,SAAS,QAAQ,GAAG;AAC5B,gBAAI,MAAM,QAAQ,GAAG;AACjB,uBAAS,OAAO,IAAI,MAAM,QAAQ;AAAA,YACtC,OACK;AACD,qBAAO,qBAAqB,CAAC,QAAQ,CAAC;AAAA,YAC1C;AAAA,UACJ,OACK;AACD,qBAAS,OAAO,IAAI;AAAA,UACxB;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AAIA,UAAM,aAAa,MAAM;AACrB,YAAM,EAAE,YAAY,YAAY,IAAI;AACpC,YAAM,EAAE,SAAS,IAAI;AACrB,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,gBAAgB;AACjC,UAAK,cAAc,aAAa,QAAQ,KAAM,MAAM,MAAM;AACtD,YAAI,UAAU,CAAC;AACf,YAAI,MAAM,MAAM;AACZ,oBAAU,MAAM,KAAK,EAAE,OAAO,QAAQ,CAAC;AAAA,QAC3C,OACK;AACD,cAAI,SAAS,OAAO;AAChB,kBAAM,YAAY,CAAC;AACnB,gBAAI,CAAC,SAAS,QAAQ;AAClB,uBAAS,SAAS;AAClB,oBAAM,aAAa,UAAU;AAC7B,kBAAI,aAAa,YAAY;AACzB,yBAAS,MAAM,QAAQ,CAAC,SAAS;AAC7B,6BAAW,EAAE,OAAO,SAAS,KAAK,CAAC;AAAA,gBACvC,CAAC;AAAA,cACL;AAAA,YACJ;AAEA,qBAAS,MAAM,QAAQ,CAAC,SAAS;AAC7B,8BAAAA,QAAQ,KAAK,KAAK,OAAO,CAAC,SAAS;AAC/B,oBAAI,CAAC,gBAAAA,QAAQ,WAAW,IAAI,GAAG;AAC3B,sBAAI,MAAM,IAAI,GAAG;AACb,8BAAU,IAAI,IAAI,MAAM,IAAI;AAAA,kBAChC;AAAA,gBACJ;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AACD,gBAAI,oBAAoB;AACpB,sBAAQ,KAAK,EAAE,oBAAoB,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,QAAQ,GAAG,OAAO,OAAO,CAAC,GAAG,UAAU;AAAA,gBACzG,MAAM,eAAe,aAAa,SAAS,KAAK,UAAU,OAAO,WAAW,SAAS;AAAA,cACzF,CAAC,CAAC,GAAG,EAAE,UAAU,iBAAiB,SAAS,gBAAgB,iBAAiB,oBAAoB,YAAY,cAAc,CAAC,GAAG,SAAS,CAAC;AAAA,YAC5I;AAAA,UACJ;AAAA,QACJ;AACA,eAAO,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,QACX,GAAG,OAAO;AAAA,MACd;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AAIA,UAAM,gBAAgB,MAAM;AACxB,YAAM,EAAE,cAAc,IAAI;AAC1B,YAAM,cAAc,mBAAmB;AACvC,UAAK,iBAAiB,aAAa,WAAW,KAAM,MAAM,SAAS;AAC/D,YAAI,UAAU,CAAC;AACf,YAAI,MAAM,SAAS;AACf,oBAAU,MAAM,QAAQ,EAAE,OAAO,QAAQ,CAAC;AAAA,QAC9C,OACK;AACD,gBAAM,kBAAkB,YAAY;AACpC,cAAI;AACJ,cAAI;AACJ,gBAAM,eAAe,CAAC;AACtB,cAAI,iBAAiB;AACjB,0BAAc,YAAY,iBAAiB,SAAS;AACpD,wBAAY,YAAY,iBAAiB,OAAO;AAChD,gBAAI,aAAa;AACb,2BAAa,UAAU;AAAA,YAC3B;AACA,gBAAI,WAAW;AACX,2BAAa,QAAQ;AAAA,YACzB;AAAA,UACJ;AACA,kBAAQ,KAAK,EAAE,iBAAqB,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,WAAW,GAAG,WAAW,GAAG,EAAE,OAAO,OAAU,CAAC,GAAG,YAAY,CAAC;AAAA,QAC3I;AACA,eAAO,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,QACX,GAAG,OAAO;AAAA,MACd;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AAIA,UAAM,YAAY,MAAM;AACpB,UAAI,MAAM,KAAK;AACX,eAAO,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,QACX,GAAG,MAAM,IAAI,EAAE,OAAO,QAAQ,CAAC,CAAC;AAAA,MACpC;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AACA,UAAM,kBAAkB,MAAM;AAC1B,YAAM,WAAW,MAAM;AACvB,UAAI,UAAU;AACV,eAAO,EAAE,OAAO;AAAA,UACZ,OAAO;AAAA,QACX,GAAG,SAAS,EAAE,OAAO,QAAQ,CAAC,CAAC;AAAA,MACnC;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AACA,UAAM,mBAAmB,MAAM;AAC3B,YAAM,YAAY,MAAM;AACxB,UAAI,WAAW;AACX,eAAO,EAAE,OAAO;AAAA,UACZ,OAAO;AAAA,QACX,GAAG,UAAU,EAAE,OAAO,QAAQ,CAAC,CAAC;AAAA,MACpC;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AAIA,UAAM,cAAc,MAAM;AACtB,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,aAAa,kBAAkB;AACrC,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,OAAO,OAAO,CAAC,GAAG,eAAe;AAClD,YAAM,YAAY,MAAM;AACxB,YAAM,cAAc,MAAM;AAC1B,YAAM,kBAAkB,MAAM,eAAe,MAAM,eAAe;AAClE,YAAM,qBAAqB,MAAM,kBAAkB,MAAM,kBAAkB;AAC3E,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,YAAI,UAAU,MAAM;AAChB,mBAAS,eAAe;AAAA,QAC5B;AACA,YAAI,UAAU,QAAQ;AAClB,mBAAS,iBAAiB;AAAA,QAC9B;AAAA,MACJ;AACA,YAAM,UAAU,CAAC;AACjB,UAAI,WAAW;AACX,gBAAQ,QAAQ;AAAA,MACpB;AACA,UAAI,aAAa;AACb,gBAAQ,UAAU;AAAA,MACtB;AACA,UAAI,iBAAiB;AACjB,gBAAQ,cAAc;AAAA,MAC1B;AACA,UAAI,oBAAoB;AACpB,gBAAQ,iBAAiB;AAAA,MAC7B;AACA,aAAO,EAAE,OAAO;AAAA,QACZ,OAAO;AAAA,MACX,GAAG;AAAA,QACC,EAAE,eAAmB,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,SAAS,GAAG,UAAU,GAAG,QAAQ,GAAG,OAAO;AAAA,MACvG,CAAC;AAAA,IACL;AAIA,UAAM,eAAe,MAAM;AACvB,UAAI,MAAM,QAAQ;AACd,eAAO,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,QACX,GAAG,MAAM,OAAO,EAAE,OAAO,QAAQ,CAAC,CAAC;AAAA,MACvC;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AAIA,UAAM,cAAc,MAAM;AACtB,YAAM,EAAE,aAAa,YAAY,IAAI;AACrC,YAAM,YAAY,iBAAiB;AACnC,YAAM,YAAY,iBAAiB;AACnC,YAAM,YAAY,MAAM;AACxB,UAAK,eAAe,aAAa,SAAS,KAAM,MAAM,OAAO;AACzD,eAAO,EAAE,OAAO;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,QACX,GAAG,YACG,UAAU,EAAE,OAAO,QAAQ,CAAC,IAC5B;AAAA,UACE,sBACM,EAAE,qBAAqB,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,EAAE,KAAK,SAAS,GAAG,SAAS,GAAI,eAAe,aAAa,SAAS,IAAI,UAAU,YAAY,CAAC,CAAE,GAAG,EAAE,cAAc,gBAAgB,CAAC,GAAG,cAAc,UAAU,KAAK,CAAC,IACxO,mBAAmB,OAAO;AAAA,QACpC,CAAC;AAAA,MACT;AACA,aAAO,mBAAmB,OAAO;AAAA,IACrC;AACA,UAAM,oBAAoB,CAAC,eAAe;AACtC,YAAM,WAAW,CAAC;AAClB,iBAAW,QAAQ,SAAO;AACtB,gBAAQ,KAAK;AAAA,UACT,KAAK;AACD,qBAAS,KAAK,WAAW,CAAC;AAC1B;AAAA,UACJ,KAAK;AACD,qBAAS,KAAK,cAAc,CAAC;AAC7B;AAAA,UACJ,KAAK;AACD,qBAAS,KAAK,UAAU,CAAC;AACzB;AAAA,UACJ,KAAK;AACD,qBAAS,KAAK,EAAE,OAAO;AAAA,cACnB,KAAK;AAAA,cACL,OAAO;AAAA,YACX,GAAG;AAAA,cACC,gBAAgB;AAAA,cAChB,YAAY;AAAA,cACZ,iBAAiB;AAAA,YACrB,CAAC,CAAC;AACF;AAAA,UACJ,KAAK;AACD,qBAAS,KAAK,aAAa,CAAC;AAC5B;AAAA,UACJ,KAAK;AACD,qBAAS,KAAK,YAAY,CAAC;AAC3B;AAAA,UACJ;AACI,mBAAO,qBAAqB,CAAC,cAAc,GAAG,EAAE,CAAC;AACjD;AAAA,QACR;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,eAAe,MAAM;AACvB,YAAM,iBAAiB,sBAAsB;AAC7C,YAAM,EAAE,UAAU,UAAU,SAAS,IAAI;AACzC,YAAM,gBAAgB,MAAM,aAAa,MAAM,YAAY;AAC3D,YAAM,iBAAiB,MAAM,cAAc,MAAM,aAAa;AAC9D,aAAO;AAAA,QACH,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG,kBAAkB,QAAQ,CAAC;AAAA,QAC9B,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG;AAAA,UACC,gBACM,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,GAAG,cAAc,CAAC,CAAC,CAAC,IAClB,mBAAmB,OAAO;AAAA,UAChC,EAAE,OAAO;AAAA,YACL,OAAO;AAAA,UACX,GAAG,kBAAkB,QAAQ,CAAC;AAAA,UAC9B,iBACM,EAAE,OAAO;AAAA,YACP,OAAO;AAAA,UACX,GAAG,eAAe,CAAC,CAAC,CAAC,IACnB,mBAAmB,OAAO;AAAA,QACpC,CAAC;AAAA,QACD,EAAE,OAAO;AAAA,UACL,OAAO;AAAA,QACX,GAAG,kBAAkB,QAAQ,CAAC;AAAA,MAClC;AAAA,IACJ;AACA,UAAM,kBAAkB,CAAC;AACzB,kBAAoB,QAAQ,UAAQ;AAChC,YAAM,OAAO,gBAAAA,QAAQ,UAAU,MAAM,IAAI,EAAE;AAC3C,sBAAgB,IAAI,IAAI,IAAI,SAAS,KAAK,MAAM,GAAG,IAAI;AAAA,IAC3D,CAAC;AACD,UAAM,YAAY,MAAM;AACpB,YAAM,EAAE,aAAa,WAAW,IAAI;AACpC,YAAM,EAAE,YAAY,IAAI;AACxB,YAAM,YAAY,iBAAiB;AACnC,YAAM,WAAW,gBAAgB;AACjC,UAAI,eAAe,aAAa,SAAS,GAAG;AACxC,YAAI,cAAc,aAAa,QAAQ,KAAK,UAAU,QAAQ,SAAS,OAAO;AAC1E,gBAAM,QAAQ,CAAC;AACf,mBAAS,MAAM,QAAQ,UAAQ;AAC3B,kBAAM,EAAE,OAAO,WAAW,IAAI;AAC9B,gBAAI,OAAO;AACP,kBAAI,YAAY;AAChB,kBAAI,YAAY;AACZ,sBAAM,EAAE,aAAa,IAAI;AACzB,oBAAI,gBAAAA,QAAQ,WAAW,YAAY,GAAG;AAClC,8BAAY,aAAa,EAAE,KAAK,CAAC;AAAA,gBACrC,WACS,CAAC,gBAAAA,QAAQ,YAAY,YAAY,GAAG;AACzC,8BAAY;AAAA,gBAChB;AAAA,cACJ;AACA,oBAAM,KAAK,IAAI;AAAA,YACnB;AAAA,UACJ,CAAC;AACD,oBAAU,WAAW;AAAA,QACzB;AACA,YAAI,CAAC,aAAa;AACd,oBAAU,cAAc;AACxB,cAAI,UAAU,aAAa,OAAO;AAC9B,qBAAS,EAAE,KAAK,MAAM,YAAY,YAAY,OAAO,CAAC,EAAE,KAAK,CAAC,SAAS;AACnE,0BAAY,cAAc,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,UAAU,KAAK,CAAC,GAAG,IAAI,MAAM,MAAM,CAAC;AAAA,YAC1H,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,2BAA2B,CAAC,SAAS;AACvC,YAAM,WAAW,gBAAgB;AACjC,YAAM,QAAQ,aAAa,OAAO,MAAM,kBAAkB,MAAM;AAChE,UAAI,SAAS,UAAU,UAAU,SAAS,eAAe,OAAO;AAC5D,2BAAmB,iBAAiB,IAAI;AAAA,MAC5C;AAAA,IACJ;AACA,UAAM,gBAAgB,CAAC,MAAM,QAAQ,SAAS;AAC1C,WAAK,MAAM,YAAY,MAAM,EAAE,OAAO,QAAQ,GAAG,MAAM,CAAC;AAAA,IAC5D;AACA,UAAM,cAAc;AAAA,MAChB;AAAA,MACA,QAAQ;AACJ,eAAO,QAAQ;AAAA,MACnB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA,YAAY,gBAAgB,MAAM;AAC9B,cAAM,EAAE,eAAe,aAAa,WAAW,YAAY,IAAI;AAC/D,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,cAAc,mBAAmB;AACvC,cAAM,YAAY,iBAAiB;AACnC,cAAM,YAAY,iBAAiB;AACnC,cAAM,YAAY,iBAAiB;AACnC,cAAM,cAAc,mBAAmB;AACvC,cAAM,EAAE,aAAa,YAAY,cAAc,aAAa,YAAY,WAAW,OAAO,CAAC,EAAE,IAAI;AACjG,cAAM,aAAa,UAAU,YAAY,UAAU,SAAS,CAAC;AAC7D,cAAM,WAAW,SAAS;AAC1B,cAAM,WAAW,YAAY;AAC7B,YAAI,SAAS;AACb,YAAI,OAAO;AACX,YAAI,gBAAAA,QAAQ,SAAS,WAAW,GAAG;AAC/B,gBAAM,EAAE,QAAQ,IAAI;AACpB,gBAAM,WAAW,iBAAiB,aAAa,WAAW,KAAK,UAAU,gBAAAA,QAAQ,SAAS,SAAS,CAAC,SAAS,KAAK,SAAS,aAAa,EAAE,UAAU,YAAY,CAAC,IAAI;AACrK,mBAAS,WAAW,SAAS,OAAO;AACpC,iBAAO;AAAA,QACX,OACK;AACD,mBAAS;AACT,iBAAO,OAAO;AAAA,QAClB;AACA,cAAM,YAAY,SAAS,OAAO,SAAS;AAC3C,gBAAQ,MAAM;AAAA,UACV,KAAK;AACD,mBAAO,SAAS,OAAO,CAAC,CAAC;AAAA,UAC7B,KAAK;AACD,mBAAO,SAAS,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,MAAM,SAAS,WAAW,GAAG,CAAC;AAAA;AAAA,UAEzE,KAAK;AACD,mBAAO,SAAS,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,MAAM,SAAS,WAAW,GAAG,CAAC;AAAA;AAAA,UAEzE,KAAK;AACD,gCAAoB,IAAI;AACxB;AAAA,UACJ,KAAK;AACD,mBAAO,gBAAgB,MAAM,+BAA+B,MAAM,SAAS,kBAAkB,CAAC;AAAA,UAClG,KAAK;AACD,qBAAS,WAAW,SAAS;AAC7B;AAAA,UACJ,KAAK;AACD,qBAAS,WAAW,SAAS;AAC7B;AAAA,UACJ,KAAK;AACD,qBAAS,WAAW,SAAS;AAC7B;AAAA,UACJ,KAAK;AACD,qBAAS,WAAW,SAAS;AAC7B;AAAA,UACJ,KAAK;AACD,mBAAO,SAAS,YAAY,IAAI;AAAA,UACpC,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,SAAS;AACV,kBAAM,cAAc,KAAK;AACzB,kBAAM,sBAAsB,KAAK;AACjC,kBAAM,oBAAoB,KAAK;AAC/B,gBAAI,aAAa;AACb,oBAAM,WAAW,SAAS;AAC1B,oBAAM,WAAW,SAAS;AAC1B,kBAAI,CAAC,YAAY,UAAU,cAAc;AACrC,uBAAO,SAAS;AAAA,cACpB;AACA,kBAAI,WAAW,CAAC;AAChB,kBAAI,aAAa,CAAC;AAClB,kBAAI,aAAa,CAAC;AAClB,kBAAI,aAAa;AACb,oBAAI,YAAY,UAAU;AACtB,4BAAU,cAAc;AAAA,gBAC5B;AACA,oBAAI,aAAa,SAAS,GAAG;AACzB,+BAAa,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,gBAC5C;AAAA,cACJ;AACA,kBAAI,UAAU;AACV,oBAAI,cAAc;AAClB,oBAAI,UAAU;AACV,wBAAM,EAAE,gBAAgB,IAAI,SAAS,eAAe;AACpD,wBAAM,WAAW,gBAAgB;AACjC,gCAAc,SAAS;AAAA,gBAC3B;AAEA,oBAAI,aAAa;AACb,sBAAI,CAAC,gBAAAA,QAAQ,QAAQ,WAAW,GAAG;AAC/B,kCAAc,CAAC,WAAW;AAAA,kBAC9B;AACA,6BAAW,YAAY,IAAI,CAAC,SAAS;AACjC,2BAAO;AAAA,sBACH,OAAO,KAAK;AAAA,sBACZ,UAAU,KAAK;AAAA,sBACf,OAAO,KAAK;AAAA,oBAChB;AAAA,kBACJ,CAAC;AAAA,gBACL;AACA,oBAAI,UAAU;AACV,+BAAa,SAAS,kBAAkB;AAAA,gBAC5C;AAAA,cACJ,OACK;AACD,oBAAI,UAAU;AACV,sBAAI,UAAU;AACV,6BAAS,SAAS;AAAA,kBACtB,OACK;AACD,+BAAW,SAAS,eAAe;AACnC,iCAAa,SAAS,kBAAkB;AAAA,kBAC5C;AAAA,gBACJ;AAAA,cACJ;AACA,oBAAM,eAAe;AAAA,gBACjB;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,MAAM,SAAS,SAAS,SAAS,CAAC,IAAI,CAAC;AAAA,gBACvC,OAAO;AAAA,gBACP,SAAS;AAAA,gBACT,MAAM;AAAA,gBACN,SAAS;AAAA,cACb;AACA,wBAAU,WAAW;AACrB,wBAAU,aAAa;AACvB,wBAAU,eAAe;AACzB,qBAAO,QAAQ,SAAS,eAAe,aAAa,cAAc,GAAG,IAAI,CAAC,EACrE,KAAK,UAAQ;AACd,oBAAI,YAAY,CAAC;AACjB,0BAAU,eAAe;AACzB,oBAAI,MAAM;AACN,sBAAI,eAAe,aAAa,SAAS,GAAG;AACxC,0BAAM,YAAY,WAAW;AAC7B,0BAAM,SAAS,gBAAAA,QAAQ,WAAW,SAAS,IAAI,UAAU,EAAE,MAAM,MAAM,OAAO,QAAQ,CAAC,IAAI,gBAAAA,QAAQ,IAAI,MAAM,aAAa,YAAY,MAAM;AAC5I,8BAAU,QAAQ,gBAAAA,QAAQ,SAAS,KAAK;AACxC,0BAAM,aAAa,WAAW;AAC9B,iCAAa,gBAAAA,QAAQ,WAAW,UAAU,IAAI,WAAW,EAAE,MAAM,MAAM,OAAO,QAAQ,CAAC,IAAI,gBAAAA,QAAQ,IAAI,MAAM,cAAc,QAAQ,MAAM,CAAC;AAE1I,0BAAM,YAAY,KAAK,IAAI,KAAK,KAAK,QAAQ,UAAU,QAAQ,GAAG,CAAC;AACnE,wBAAI,UAAU,cAAc,WAAW;AACnC,gCAAU,cAAc;AAAA,oBAC5B;AAAA,kBACJ,OACK;AACD,0BAAM,WAAW,WAAW;AAC5B,iCAAa,WAAY,gBAAAA,QAAQ,WAAW,QAAQ,IAAI,SAAS,EAAE,MAAM,MAAM,OAAO,QAAQ,CAAC,IAAI,gBAAAA,QAAQ,IAAI,MAAM,QAAQ,IAAK,SAAS,CAAC;AAAA,kBAChJ;AAAA,gBACJ;AACA,oBAAI,UAAU;AACV,2BAAS,SAAS,SAAS;AAAA,gBAC/B,OACK;AACD,2BAAS,MAAM;AACX,wBAAI,UAAU;AACV,+BAAS,SAAS,SAAS;AAAA,oBAC/B;AAAA,kBACJ,CAAC;AAAA,gBACL;AACA,oBAAI,YAAY;AACZ,6BAAW,cAAc,GAAG,IAAI;AAAA,gBACpC;AACA,oBAAI,qBAAqB;AACrB,sCAAoB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,gBAC1F;AACA,uBAAO,EAAE,QAAQ,KAAK;AAAA,cAC1B,CAAC,EAAE,MAAM,CAAC,SAAS;AACf,0BAAU,eAAe;AACzB,oBAAI,mBAAmB;AACnB,oCAAkB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,gBACxF;AACA,uBAAO,EAAE,QAAQ,MAAM;AAAA,cAC3B,CAAC;AAAA,YACL,OACK;AACD,qBAAO,qBAAqB,CAAC,yBAAyB,CAAC;AAAA,YAC3D;AACA;AAAA,UACJ;AAAA,UACA,KAAK,UAAU;AACX,kBAAM,cAAc,KAAK;AACzB,kBAAM,uBAAuB,KAAK;AAClC,kBAAM,qBAAqB,KAAK;AAChC,gBAAI,aAAa;AACb,oBAAM,gBAAgB,uBAAuB,mBAAmB;AAChE,oBAAM,gBAAgB,cAAc,OAAO,SAAO,CAAC,SAAS,cAAc,GAAG,CAAC;AAC9E,oBAAM,OAAO,EAAE,cAAc;AAC7B,oBAAM,eAAe,EAAE,OAAO,SAAS,MAAM,QAAQ,MAAM,MAAM,UAAU,SAAS,YAAY;AAChG,kBAAI,cAAc,QAAQ;AACtB,uBAAO,gBAAgB,MAAM,+BAA+B,MAAM;AAC9D,sBAAI,CAAC,cAAc,QAAQ;AACvB,2BAAO,SAAS,OAAO,aAAa;AAAA,kBACxC;AACA,4BAAU,eAAe;AACzB,yBAAO,QAAQ,SAAS,gBAAgB,aAAa,cAAc,GAAG,IAAI,CAAC,EACtE,KAAK,UAAQ;AACd,8BAAU,eAAe;AACzB,6BAAS,cAAc,eAAe,KAAK;AAC3C,wBAAI,WAAW;AACX,0BAAI,MAAM,OAAO;AACb,8BAAM,MAAM,QAAQ,EAAE,SAAS,WAAW,MAAM,qBAAqB,GAAG,QAAQ,UAAU,CAAC;AAAA,sBAC/F;AAAA,oBACJ;AACA,wBAAI,aAAa;AACb,kCAAY,cAAc,GAAG,IAAI;AAAA,oBACrC,OACK;AACD,kCAAY,YAAY,OAAO;AAAA,oBACnC;AACA,wBAAI,sBAAsB;AACtB,2CAAqB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,oBAC3F;AACA,2BAAO,EAAE,QAAQ,KAAK;AAAA,kBAC1B,CAAC,EACI,MAAM,UAAQ;AACf,8BAAU,eAAe;AACzB,wBAAI,WAAW;AACX,0BAAI,MAAM,OAAO;AACb,8BAAM,MAAM,QAAQ,EAAE,IAAI,MAAM,SAAS,WAAW,MAAM,oBAAoB,GAAG,QAAQ,QAAQ,CAAC;AAAA,sBACtG;AAAA,oBACJ;AACA,wBAAI,oBAAoB;AACpB,yCAAmB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,oBACzF;AACA,2BAAO,EAAE,QAAQ,MAAM;AAAA,kBAC3B,CAAC;AAAA,gBACL,CAAC;AAAA,cACL,OACK;AACD,oBAAI,aAAa;AACb,sBAAI,MAAM,OAAO;AACb,0BAAM,MAAM,QAAQ,EAAE,IAAI,MAAM,SAAS,QAAQ,0BAA0B,GAAG,QAAQ,UAAU,CAAC;AAAA,kBACrG;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,OACK;AACD,qBAAO,qBAAqB,CAAC,0BAA0B,CAAC;AAAA,YAC5D;AACA;AAAA,UACJ;AAAA,UACA,KAAK,QAAQ;AACT,kBAAM,cAAc,KAAK;AACzB,kBAAM,qBAAqB,KAAK;AAChC,kBAAM,mBAAmB,KAAK;AAC9B,gBAAI,aAAa;AACb,oBAAM,OAAO,SAAS,aAAa;AACnC,oBAAM,EAAE,eAAe,eAAe,eAAe,eAAe,IAAI;AACxE,oBAAM,eAAe,EAAE,OAAO,SAAS,MAAM,QAAQ,MAAM,MAAM,UAAU,SAAS,YAAY;AAEhG,kBAAI,cAAc,QAAQ;AACtB,qBAAK,iBAAiB,eAAe,OAAO,CAAC,QAAQ,SAAS,eAAe,eAAe,GAAG,MAAM,EAAE;AAAA,cAC3G;AAEA,kBAAI,eAAe,QAAQ;AACvB,qBAAK,gBAAgB,cAAc,OAAO,CAAC,QAAQ,SAAS,eAAe,gBAAgB,GAAG,MAAM,EAAE;AAAA,cAC1G;AACA,kBAAI,cAAc,QAAQ,QAAQ;AAClC,kBAAI,WAAW;AAEX,8BAAc,SAAS,eAAe,YAAY,YAAY,SAAS,iBAAiB,UAAU,EAAE,KAAK,cAAc,OAAO,aAAa,CAAC;AAAA,cAChJ;AACA,qBAAO,YAAY,KAAK,CAAC,WAAW;AAChC,oBAAI,QAAQ;AAER;AAAA,gBACJ;AACA,oBAAI,KAAK,cAAc,UAAU,cAAc,UAAU,cAAc,UAAU,KAAK,eAAe,QAAQ;AACzG,4BAAU,eAAe;AACzB,yBAAO,QAAQ,SAAS,cAAc,aAAa,cAAc,GAAG,IAAI,CAAC,EACpE,KAAK,UAAQ;AACd,8BAAU,eAAe;AACzB,6BAAS,gBAAgB;AACzB,wBAAI,WAAW;AACX,0BAAI,MAAM,OAAO;AACb,8BAAM,MAAM,QAAQ,EAAE,SAAS,WAAW,MAAM,sBAAsB,GAAG,QAAQ,UAAU,CAAC;AAAA,sBAChG;AAAA,oBACJ;AACA,wBAAI,WAAW;AACX,gCAAU,cAAc,GAAG,IAAI;AAAA,oBACnC,OACK;AACD,kCAAY,YAAY,OAAO;AAAA,oBACnC;AACA,wBAAI,oBAAoB;AACpB,yCAAmB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,oBACzF;AACA,2BAAO,EAAE,QAAQ,KAAK;AAAA,kBAC1B,CAAC,EACI,MAAM,UAAQ;AACf,8BAAU,eAAe;AACzB,wBAAI,WAAW;AACX,0BAAI,MAAM,OAAO;AACb,8BAAM,MAAM,QAAQ,EAAE,IAAI,MAAM,SAAS,WAAW,MAAM,oBAAoB,GAAG,QAAQ,QAAQ,CAAC;AAAA,sBACtG;AAAA,oBACJ;AACA,wBAAI,kBAAkB;AAClB,uCAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,KAAK,CAAC,CAAC;AAAA,oBACvF;AACA,2BAAO,EAAE,QAAQ,MAAM;AAAA,kBAC3B,CAAC;AAAA,gBACL,OACK;AACD,sBAAI,aAAa;AACb,wBAAI,MAAM,OAAO;AACb,4BAAM,MAAM,QAAQ,EAAE,IAAI,MAAM,SAAS,QAAQ,wBAAwB,GAAG,QAAQ,OAAO,CAAC;AAAA,oBAChG;AAAA,kBACJ;AAAA,gBACJ;AAAA,cACJ,CAAC;AAAA,YACL,OACK;AACD,qBAAO,qBAAqB,CAAC,wBAAwB,CAAC;AAAA,YAC1D;AACA;AAAA,UACJ;AAAA,UACA,SAAS;AACL,kBAAM,eAAe,SAAS,IAAI,IAAI;AACtC,gBAAI,cAAc;AACd,oBAAM,iBAAiB,aAAa,sBAAsB,aAAa;AACvE,kBAAI,gBAAgB;AAChB,+BAAe,EAAE,MAAM,QAAQ,OAAO,SAAS,QAAQ,SAAS,GAAG,GAAG,IAAI;AAAA,cAC9E,OACK;AACD,uBAAO,yBAAyB,CAAC,IAAI,CAAC;AAAA,cAC1C;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,OAAO;AACH,YAAI,UAAU,QAAQ;AAClB,iBAAO,YAAY,OAAO;AAAA,QAC9B;AACA,eAAO,YAAY,SAAS;AAAA,MAChC;AAAA,MACA,cAAc;AACV,eAAO,UAAU;AAAA,MACrB;AAAA,MACA,WAAW;AACP,eAAO,WAAW,IAAI;AAAA,MAC1B;AAAA,MACA,SAAS;AACL,eAAO,WAAW;AAAA,MACtB;AAAA,MACA;AAAA,MACA,aAAa,WAAW;AACpB,cAAM,WAAW,gBAAgB;AACjC,cAAM,EAAE,WAAW,IAAI;AACvB,cAAM,EAAE,MAAM,IAAI;AAClB,cAAM,WAAW,CAAC;AAClB,wBAAAA,QAAQ,SAAS,cAAc,aAAa,QAAQ,KAAK,QAAQ,QAAQ,CAAC,GAAG,UAAQ;AACjF,mBAAS,KAAK,IAAI;AAAA,QACtB,GAAG,EAAE,UAAU,WAAW,CAAC;AAC3B,eAAO,gBAAAA,QAAQ,YAAY,SAAS,IAAI,WAAW,SAAS,SAAS;AAAA,MACzE;AAAA,MACA,eAAe;AACX,cAAM,WAAW,SAAS;AAC1B,YAAI,MAAM,aAAa;AACnB,gBAAM,EAAE,SAAS,IAAI;AACrB,iBAAO;AAAA,YACH,MAAM,WAAW,SAAS,YAAY,IAAI,CAAC;AAAA,YAC3C,QAAQ,UAAU;AAAA,YAClB,MAAM,YAAY;AAAA,YAClB,MAAM,SAAS,SAAS,SAAS,CAAC,IAAI,CAAC;AAAA,YACvC,OAAO;AAAA,YACP,OAAO,UAAU;AAAA,YACjB,gBAAgB,WAAW,SAAS,kBAAkB,IAAI,CAAC;AAAA,UAC/D;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAmBJ;AAEA,QAAI,MAAwC;AACxC,kBAAY,aAAa,CAAC,YAAY;AAClC,cAAM,WAAW,SAAS;AAC1B,wBAAAA,QAAQ,SAAS,SAAS,CAAC,WAAW;AAClC,cAAI,OAAO,OAAO;AACd,4BAAAA,QAAQ,KAAK,OAAO,OAAO,CAAC,SAAS;AACjC,kBAAI,CAAC,gBAAAA,QAAQ,WAAW,IAAI,GAAG;AAC3B,oBAAI,CAAC,MAAM,IAAI,GAAG;AACd,yBAAO,qBAAqB,CAAC,IAAI,CAAC;AAAA,gBACtC;AAAA,cACJ;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AACD,YAAI,UAAU;AACV,iBAAO,SAAS,WAAW,OAAO;AAAA,QACtC;AACA,eAAO,SAAS;AAAA,MACpB;AACA,kBAAY,eAAe,CAAC,YAAY;AACpC,+BAAuB,SAAS;AAChC,eAAO,YAAY,WAAW,OAAO;AAAA,MACzC;AAAA,IACJ;AACA,UAAM,qBAAqB;AAAA,MACvB;AAAA,MACA,SAAS,UAAU,QAAQ;AACvB,YAAI,UAAU;AACV,cAAI,gBAAAA,QAAQ,SAAS,QAAQ,GAAG;AAC5B,uBAAW,MAAM,QAAQ,KAAK;AAAA,UAClC;AACA,cAAI,gBAAAA,QAAQ,WAAW,QAAQ,GAAG;AAC9B,mBAAO,WAAW,SAAS,MAAM,CAAC;AAAA,UACtC;AAAA,QACJ;AACA,eAAO,CAAC;AAAA,MACZ;AAAA;AAAA;AAAA;AAAA,MAIA,mBAAmB;AACf,cAAM,EAAE,OAAO,IAAI;AACnB,cAAM,KAAK,QAAQ;AACnB,YAAI,IAAI;AACJ,gBAAM,cAAc,eAAe;AACnC,gBAAM,iBAAiB,kBAAkB;AACzC,gBAAM,aAAa,cAAc;AACjC,gBAAM,gBAAgB,iBAAiB;AACvC,gBAAM,eAAe,gBAAgB;AACrC,gBAAM,WAAW,GAAG;AACpB,gBAAM,oBAAoB,SAAS,IAAK,WAAW,wBAAwB,QAAQ,IAAI;AACvF,iBAAO,oBAAoB,wBAAwB,EAAE,IAAI,gBAAgB,WAAW,IAAI,gBAAgB,cAAc,IAAI,gBAAgB,UAAU,IAAI,gBAAgB,aAAa,IAAI,gBAAgB,YAAY;AAAA,QACzN;AACA,eAAO;AAAA,MACX;AAAA,MACA,kBAAkB;AACd,cAAM,KAAK,QAAQ;AACnB,YAAI,IAAI;AACJ,gBAAM,WAAW,GAAG;AACpB,kBAAQ,UAAU,SAAS,WAAW,EAAE,gBAAiB,WAAW,gBAAAA,QAAQ,SAAS,iBAAiB,QAAQ,EAAE,MAAM,IAAI,KAAM,mBAAmB,iBAAiB;AAAA,QACxK;AACA,eAAO;AAAA,MACX;AAAA,MACA,0BAA0B,QAAQ,MAAM;AACpC,cAAM,EAAE,KAAK,IAAI;AACjB,eAAO,YAAY,YAAY,QAAQ,IAAI,EAAE,KAAK,CAAC,SAAS;AACxD,cAAI,QAAQ,QAAQ,KAAK,UAAU,CAAC,SAAS,UAAU,UAAU,MAAM,EAAE,SAAS,IAAI,GAAG;AACrF,wBAAY,cAAc,SAAS,YAAY,SAAS,SAAS,SAAS,IAAI,KAAK,eAAe,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,UAAU,SAAS,SAAS,CAAC,GAAG,IAAI;AAAA,UACnL;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACA,uBAAuB,QAAQ,MAAM;AACjC,2BAAmB,0BAA0B,QAAQ,IAAI;AACzD,oBAAY,cAAc,wBAAwB,EAAE,MAAM,OAAO,MAAM,OAAO,GAAG,IAAI;AAAA,MACzF;AAAA,MACA,uBAAuB,MAAM,MAAM;AAC/B,2BAAmB,0BAA0B,MAAM,IAAI;AACvD,oBAAY,cAAc,sBAAsB,EAAE,MAAM,KAAK,MAAM,KAAK,GAAG,IAAI;AAAA,MACnF;AAAA,MACA,iBAAiB,MAAM;AACnB,oBAAY,KAAK;AACjB,oBAAY,cAAc,QAAQ,EAAE,MAAM,UAAU,SAAS,QAAQ,SAAS,GAAG,IAAI;AAAA,MACzF;AAAA,IACJ;AACA,WAAO,OAAO,SAAS,wBAAwB,aAAa,kBAAkB;AAC9E,UAAM,aAAa,IAAI,CAAC;AACxB,UAAM,MAAM,MAAM,UAAU,MAAM,QAAQ,SAAS,IAAI,MAAM;AACzD,iBAAW;AAAA,IACf,CAAC;AACD,UAAM,MAAM,MAAM,SAAS,MAAM;AAC7B,iBAAW;AAAA,IACf,CAAC;AACD,UAAM,YAAY,MAAM;AACpB,eAAS,MAAM,QAAQ,WAAW,MAAM,WAAW,CAAC,CAAC,CAAC;AAAA,IAC1D,CAAC;AACD,UAAM,MAAM,MAAM,eAAe,MAAM;AACnC,kBAAY;AAAA,IAChB,CAAC;AACD,UAAM,qBAAqB,MAAM;AAC7B,gBAAU;AAAA,IACd,CAAC;AACD,UAAM,MAAM,MAAM,aAAa,MAAM;AACjC,gBAAU;AAAA,IACd,CAAC;AACD,UAAM,QAAQ,CAAC,YAAY;AACvB,YAAM,EAAE,UAAU,IAAI;AACtB,UAAI,WAAW;AACX,cAAM,WAAW,UAAU,OAAO;AAClC,YAAI,YAAY,gBAAAA,QAAQ,SAAS,QAAQ,GAAG;AACxC,iBAAO,OAAO,SAAS,QAAQ;AAAA,QACnC;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,cAAU;AACV,cAAU,MAAM;AACZ,eAAS,MAAM;AACX,cAAM,EAAE,QAAQ,IAAI;AACpB,YAAI,MAAM,YAAY;AAClB,cAAI,CAAC,oBAAoB;AACrB,mBAAO,qBAAqB,CAAC,UAAU,CAAC;AAAA,UAC5C;AAAA,QACJ;AACA,YAAI,MAAM,aAAa;AACnB,cAAI,CAAC,qBAAqB;AACtB,mBAAO,qBAAqB,CAAC,WAAW,CAAC;AAAA,UAC7C;AAAA,QACJ;AAUA,YAAI,WAAW,QAAQ,QAAQ;AAC3B,kBAAQ,WAAW,OAAO;AAAA,QAC9B;AACA,oBAAY;AACZ,kBAAU;AAAA,MACd,CAAC;AACD,mBAAa,GAAG,SAAS,WAAW,wBAAwB;AAAA,IAChE,CAAC;AACD,gBAAY,MAAM;AACd,mBAAa,IAAI,SAAS,SAAS;AAAA,IACvC,CAAC;AACD,UAAM,WAAW,MAAM;AACnB,YAAM,QAAQ,YAAY;AAC1B,YAAM,SAAS,cAAc;AAC7B,aAAO,EAAE,OAAO;AAAA,QACZ,KAAK;AAAA,QACL,OAAO,CAAC,YAAY;AAAA,UACZ,CAAC,SAAS,KAAK,EAAE,GAAG;AAAA,UACpB,cAAc,CAAC,CAAC,MAAM;AAAA,UACtB,aAAa,MAAM;AAAA,UACnB,gBAAgB,UAAU;AAAA,UAC1B,eAAe,MAAM,WAAW,UAAU;AAAA,QAC9C,CAAC;AAAA,QACL,OAAO;AAAA,MACX,GAAG,aAAa,CAAC;AAAA,IACrB;AACA,YAAQ,WAAW;AACnB,YAAQ,WAAW,OAAO;AAC1B,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO,KAAK,SAAS;AAAA,EACzB;AACJ,CAAC;;;ACtyCM,IAAM,UAAU,OAAO,OAAO,CAAC,GAAG,cAAkB;AAAA,EACvD,QAAQ,KAAK;AACT,QAAI,UAAU,aAAiB,MAAM,YAAgB;AAAA,EACzD;AACJ,CAAC;AACD,IAAI,MAAM,YAAY;AAClB,QAAM,WAAW,UAAU,aAAiB,MAAM,YAAgB;AACtE;AACA,MAAM,UAAU,YAAgB;AACzB,IAAM,OAAO;AACpB,IAAOC,gBAAQ;", "names": ["XEUtils", "grid_default"]}