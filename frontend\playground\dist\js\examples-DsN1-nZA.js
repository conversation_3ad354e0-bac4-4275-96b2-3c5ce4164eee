const e="Examples",t={title:"Modal"},a={title:"Drawer"},i={title:"EllipsisText"},l={title:"Form",basic:"Basic Form",query:"Query Form",rules:"Form Rules",dynamic:"Dynamic Form",custom:"Custom Component",api:"Api",merge:"Merge Form"},o={title:"Vxe Table",basic:"Basic Table",remote:"Remote Load",tree:"Tree Table",fixed:"Fixed Header/Column",virtual:"Virtual Scroll",editCell:"Edit Cell",editRow:"Edit Row","custom-cell":"Custom Cell",form:"Form Table"},c={title:"Captcha",pointSelection:"Point Selection Captcha",sliderCaptcha:"Slider Captcha",sliderRotateCaptcha:"Rotate Captcha",captchaCardTitle:"Please complete the security verification",pageDescription:"Verify user identity by clicking on specific locations in the image.",pageTitle:"Captcha Component Example",basic:"Basic Usage",titlePlaceholder:"Captcha Title Text",captchaImageUrlPlaceholder:"Captcha Image (supports img tag src attribute value)",hintImage:"Hint Image",hintText:"Hint Text",hintImagePlaceholder:"Hint Image (supports img tag src attribute value)",hintTextPlaceholder:"Hint Text",showConfirm:"Show Confirm",hideConfirm:"Hide Confirm",widthPlaceholder:"Captcha Image Width Default 300px",heightPlaceholder:"Captcha Image Height Default 220px",paddingXPlaceholder:"Horizontal Padding Default 12px",paddingYPlaceholder:"Vertical Padding Default 16px",index:"Index:",timestamp:"Timestamp:",x:"x:",y:"y:"},r={title:e,modal:t,drawer:a,ellipsis:i,form:l,vxeTable:o,captcha:c};export{c as captcha,r as default,a as drawer,i as ellipsis,l as form,t as modal,e as title,o as vxeTable};
