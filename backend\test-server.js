const http = require('http');

const server = http.createServer((req, res) => {
  if (req.method === 'POST' && req.url === '/api/system/login') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('收到登录请求:', data);
        
        // 简单的验证逻辑
        if (data.account === 'admin' && data.password === 'admin123') {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            code: "00000",
            data: {
              roleType: 1,
              name: "系统管理员",
              token: "test-token-123",
              account: data.account
            },
            currentTime: new Date().toLocaleString('zh-CN')
          }));
        } else {
          res.writeHead(401, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            message: "账号或密码错误",
            error: "Unauthorized",
            statusCode: 401
          }));
        }
      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Invalid JSON' }));
      }
    });
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Not Found' }));
  }
});

const port = 3000;
server.listen(port, () => {
  console.log(`测试服务器运行在: http://localhost:${port}`);
  console.log('可以测试登录接口: POST /api/system/login');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
