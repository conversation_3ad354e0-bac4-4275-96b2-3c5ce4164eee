var U=(t,c,o)=>new Promise((s,d)=>{var p=y=>{try{x(o.next(y))}catch(S){d(S)}},f=y=>{try{x(o.throw(y))}catch(S){d(S)}},x=y=>y.done?s(y.value):Promise.resolve(y.value).then(p,f);x((o=o.apply(t,c)).next())});import{_ as z,u as B}from"./use-echarts-CfT8MSbz.js";import{t as A,a as $,r as C,l as H}from"./bootstrap-5OPUVRWy.js";import{d as T,r as _,y as j,W as O,x as w,j as I,b as v,o as N,a as F,s as u,q as m,f as b,k as L,t as E,F as P,D as K}from"../jse/index-index-DyHD_jbN.js";const M=T({name:"Bar",components:{EchartsUI:z},props:{chartData:{type:Object,default:()=>{}},option:{type:Object,default:()=>({})},width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"},seriesColor:{type:String,default:"#1890ff"}},setup(t){const c=_(),{renderEcharts:o}=B(c),s=j({tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{show:!0,backgroundColor:"#333"}}},grid:{top:"5%"},legend:{show:!0,bottom:0},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[{name:"bar",type:"bar",data:[],color:t.seriesColor}]});O(()=>{t.chartData&&d()});function d(){if(t.option&&Object.assign(s,A(t.option)),t.chartData.seriesData)s.series=t.chartData.seriesData&&t.chartData.seriesData.map(p=>({name:p.name,type:"bar",data:p.data})),s.xAxis.data=t.chartData.xAxisData,s.xAxis.axisLabel=t.chartData.xAxisLabel&&t.chartData.xAxisLabel,o(s);else{const p=t.chartData.map(x=>x.value),f=t.chartData.map(x=>x.name);s.series[0].data=p,s.series[0].color=t.seriesColor,s.xAxis.data=f,o(s)}}return{chartRef:c}}});function q(t,c,o,s,d,p){const f=w("EchartsUI");return v(),I(f,{ref:"chartRef"},null,512)}const W=$(M,[["render",q]]),J=T({name:"Bar",components:{EchartsUI:z},props:{chartData:{type:Object,default:()=>{}},option:{type:Object,default:()=>({})},width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"},seriesColor:{type:String,default:"#1890ff"}},setup(t){const c=_(),{renderEcharts:o}=B(c),s=j({tooltip:{formatter:"{b} ({c})"},legend:{show:!0,bottom:0},grid:{bottom:"10%"},series:[{type:"pie",radius:["40%","70%"],center:["50%","40%"],data:[],labelLine:{show:!0},label:{show:!0,formatter:`{b} 
 ({d}%)`,color:"#B1B9D3"}}]});O(()=>{t.chartData&&d()});function d(){t.option&&Object.assign(s,A(t.option)),s.series[0].data=t.chartData,o(s)}return{chartRef:c}}});function Q(t,c,o,s,d,p){const f=w("EchartsUI");return v(),I(f,{ref:"chartRef"},null,512)}const X=$(J,[["render",Q]]),Z=t=>C.get("/statistic/data-create-month/data-size",{params:t}),tt=t=>C.get("/statistic/data-type/data-size",{params:t}),at=t=>C.get("/statistic/data-region/data-size",{params:t}),et=t=>C.get("/statistic/data-year/data-size",{params:t}),st=T({name:"SCharts",components:{EchartsUI:z},props:{option:{type:Object,default:()=>({})},width:{type:String,default:"100%"},height:{type:String,default:"calc(100vh - 78px)"}},setup(t){const c=_(),{renderEcharts:o}=B(c),s=j({});O(()=>{d()});function d(){t.option&&Object.assign(s,A(t.option)),o(s)}return{chartRef:c}}});function rt(t,c,o,s,d,p){const f=w("EchartsUI");return v(),I(f,{ref:"chartRef"},null,512)}const nt=$(st,[["render",rt]]),ot="/png/icon_head_default-CEmUpiwH.png",ct={class:"pageHead"},it={class:"imageLabelArea"},lt={class:"headLeftText"},dt={style:{"margin-bottom":"10px"}},ht={class:"staticsArea"},ut={style:{"margin-top":"15px"}},pt=T({__name:"index",setup(t){const c=_([]),o=_({xAxisData:[],seriesData:[]}),s=_({}),d=_([]);_([]),_([]),_("xxx");function p(e){return e>=1048576?`${(e/1048576).toFixed(2)} TB`:e>=1024?`${(e/1024).toFixed(2)} GB`:`${e.toFixed(2)} MB`}const f=(e,a)=>{let r=0;a&&a.forEach(i=>{(e.fileFormat.length==0||e.fileFormat.includes(i.dataType))&&i.dataSize&&(r+=i.dataSize)}),e.value=p(r)},x=e=>{let a=[{label:"数据总量",value:"120G",color:"#00BFFF",fileFormat:[]},{label:"三维数据",value:"120G",color:"#32CD32",fileFormat:["osgb","tiles"]},{label:"栅格数据",value:"120G",color:"#FF4500",fileFormat:["dom","dem"]},{label:"矢量数据",value:"120G",color:"#f89d76",fileFormat:["vector"]}];a.forEach(r=>{f(r,e)}),c.value=a},y=()=>{const e=new Date,a=e.getFullYear(),r=(e.getMonth()+1).toString().padStart(2,"0"),i=e.getDate().toString().padStart(2,"0");return`${a}年${r}月${i}日`},S=()=>{const a=new Date().getHours();return a<12?"早上好":a<18?"下午好":"晚上好"},R=H(),k=e=>{let a={};e&&e.forEach(l=>{a[l.dataType]==null&&(a[l.dataType]={months:[],datas:[]}),a[l.dataType].months.push(l.month),a[l.dataType].datas.push(l.dataSize?l.dataSize:0)});let r=Object.entries(a);const[i,D]=r[0];let g=[];r&&r.forEach(l=>{g.push({name:l[0],data:l[1].datas,color:"#00BFFF"})});let n={xAxisData:D.months,seriesData:g};o.value=n},G=e=>{let a={};e&&e.forEach(h=>{a[h.dataType]==null&&(a[h.dataType]={regions:[],datas:[]}),a[h.dataType].regions.push(h.regionName),a[h.dataType].datas.push(h.dataSize?h.dataSize:0)});let r=Object.entries(a);const[i,D]=r[0];let g=[],n=D.regions;r&&r.forEach(h=>{g.push({name:h[0],type:"bar",stack:"total",label:{show:!1},emphasis:{focus:"series"},data:h[1].datas})});let l={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value"},yAxis:{type:"category",data:n},series:g};s.value=l},Y=e=>{let a={};e&&e.forEach(n=>{a[n.dataYear]==null&&(a[n.dataYear]={dataTypes:[],dataSize:0}),a[n.dataYear].dataTypes.push(n.dataType),a[n.dataYear].dataSize=a[n.dataYear].dataSize+(n.dataSize?n.dataSize:0)});let r=Object.entries(a);const[i,D]=r[0];let g=[];r&&r.forEach(n=>{g.push({name:n[0],value:n[1].dataSize})}),d.value=g},V=()=>U(null,null,function*(){let e=yield tt();x(e);let a=yield Z();k(a);let r=yield at();G(r);let i=yield et();Y(i)});return N(()=>{V()}),(e,a)=>{const r=w("a-image"),i=w("a-card"),D=w("a-col"),g=w("a-row");return v(),F("div",null,[u(i,{style:{margin:"20px 20px 0px 20px"}},{default:m(()=>{var n;return[b("view",ct,[b("view",it,[u(r,{class:"imageLabel",preview:!1,src:L(ot),width:"80px",height:"80px"},null,8,["src"]),b("view",lt,[b("h2",dt,E(S())+", "+E((n=L(R).userInfo)==null?void 0:n.realName),1),b("p",null,E(y()),1)])]),b("view",ht,[(v(!0),F(P,null,K(c.value,(l,h)=>(v(),F("view",{key:h,class:"headLeftText"},[b("view",null,E(l.label),1),b("h2",ut,E(l.value),1)]))),128))])])]}),_:1}),u(g,{class:"chartArea"},{default:m(()=>[u(D,{span:8},{default:m(()=>[u(i,{size:"small",title:"数据量统计",class:"chartCard"},{default:m(()=>[u(W,{"chart-data":o.value,height:"300px",width:"350px"},null,8,["chart-data"])]),_:1})]),_:1}),u(D,{span:8},{default:m(()=>[u(i,{size:"small",title:"数据范围统计",class:"chartCard"},{default:m(()=>[u(nt,{option:s.value,height:"300px",width:"350px"},null,8,["option"])]),_:1})]),_:1}),u(D,{span:8},{default:m(()=>[u(i,{size:"small",title:"数据建设年份统计",class:"chartCard"},{default:m(()=>[u(X,{"chart-data":d.value,height:"300px",width:"350px"},null,8,["chart-data"])]),_:1})]),_:1})]),_:1})])}}}),yt=$(pt,[["__scopeId","data-v-cbe55059"]]);export{yt as default};
