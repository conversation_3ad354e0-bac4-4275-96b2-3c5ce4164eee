<script>
import { Button as AButton, Select as ASelect } from 'ant-design-vue';
import * as Cesium from 'cesium';
import { ColorPicker } from 'vue3-colorpicker';

import * as api from '../api/index';
import ShapeConfig from '../utils/cesium/config/ShapeConfig';
import core from '../utils/cesium/core';
import { GISMap } from '../utils/cesium/index';
import Drawer from '../utils/cesium/mapTools/Drawer';
import ztu from '../ztu';

import 'vue3-colorpicker/style.css';

let MyLayerService = null;
export default {
  components: {
    AButton,
    ColorPicker,
    ASelect,
  },
  props: {
    entity: {
      type: Object,
      default: null,
    },
    type: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      // 图片配置
      imgsgroup: [
        {
          id: '',
          resUrl: '',
          name: '',
        },
      ],
      billboard: {
        id: null,
        name: '',
        type: 'billboard',
        tagtype: '',
        content: '',
        fontsize: 14,
        fontcolor: 'rgb(255,255,255)',
        resId: null,
      },
      typegroup: [
        {
          value: '',
          label: '',
        },
      ],
      position: null,
      ismove: false,
    };
  },
  mounted() {
    MyLayerService = ztu.global.MyLayerService;
    console.log('POIEntity', this.entity);
    // 获取图标列表
    api.getPoiList().then((res) => {
      this.imgsgroup =
        res &&
        res.map((a) => {
          return {
            id: a.id,
            resUrl: a.resUrl,
            name: a.name,
          };
        });
      console.log('图标列表', this.imgsgroup);
    });

    // 获取自己创建过的图标类型列表
    api.getUserVisibleTypeList().then((res) => {
      this.typegroup =
        res &&
        res.map((a) => {
          return {
            value: a,
            label: a,
          };
        });
      console.log('类型列表', this.typegroup);
    });
    // 获取初始兴趣点文本信息
    if (this.entity.label) {
      this.billboard.name = this.entity._label.text._value || '';
      this.billboard.fontsize = Number.parseInt(
        this.entity.label.font._value || 14,
      );
      this.billboard.fontcolor =
        this.entity.label.fillColor._value.toCssColorString() ||
        'rgb(255,255,255)';
    }
    // 获取初始兴趣点备注
    if (this.entity.description) {
      this.billboard.content = this.entity.description._value || '';
    }
    this.billboard.id = this.entity.objectId;
    console.log('this.billboard.id', this.billboard.id);
    this.billboard.tagtype = this.entity.tagType;
    this.billboard.resId = this.entity.resId;
  },
  methods: {
    // 生成实体
    ok() {
      // 文字
      const textOptions = {
        text: this.billboard.name,
        font: `${this.billboard.fontsize}px sans-serif`,
        fillColor: Cesium.Color.fromCssColorString(this.billboard.fontcolor),
        pixelOffset: new Cesium.Cartesian2(0, -34),
      };
      this.entity.label = new Cesium.LabelGraphics({
        ...ShapeConfig.textStyle,
        ...textOptions,
      });
      // 内容
      if (this.billboard.content) {
        this.entity.description = this.billboard.content;
      }
      // if(!this.ismove)
      // {

      // 	let loca = this.entity.position._value
      // 	this.position = core.transformCartesianToWGS84(loca)
      // }
      console.log('.........ok', this.entity);
    },
    // 保存至后台
    async saveas() {
      this.ok();
      console.log('.........saveas', this.entity);
      if (!this.ismove) {
        const loca = this.entity.position._value;
        this.position = core.transformCartesianToWGS84(loca);
      }
      // 编辑兴趣点
      const poiparams = {
        id: this.billboard.id,
        latitude: this.position.lat || 0,
        longitude: this.position.lng || 0,
        name: this.billboard.name || '',
        nameColor: this.billboard.fontcolor || 'rgb(255,255,255)',
        nameSize: this.billboard.fontsize || 14,
        remark: this.billboard.content || '',
        resId: this.billboard.resId,
        tagType: this.billboard.tagtype || '其它',
      };
      console.log('.........saveas', poiparams);
      await api.editLayerTag(poiparams);
      await MyLayerService.loadPOITypes();
      await MyLayerService.loadPOIs(this.entity.tagType, true);
      if (this.entity.tagType != poiparams.tagType) {
        await MyLayerService.loadPOIs(poiparams.tagType, true);
      }
      this.$parent.$parent.close();
    },
    // 获取标注点图片
    getimg(e) {
      console.log('e', e.target);
      if (this.imgsgroup) {
        this.imgsgroup.forEach((item) => {
          if (item.id == e.target.id) {
            this.entity.billboard._image._value = item.resUrl;
            this.billboard.resId = e.target.id;
          }
        });
      } else {
        alert('当前无可用图标！');
      }
    },
    // POI分类选项
    handleChange(e) {
      this.billboard.tagtype = e;
    },
    onSearch(value) {
      const foundIt = this.typegroup.some((option) => {
        return option.value.toString() === value.toString();
      });
      if (!foundIt && !!value) {
        this.deleteSearchAdd(value);
        const params = { label: value, value, searchAdd: true };
        this.typegroup.push(params);
      } else {
        this.handleChange(value);
      }
    },
    deleteSearchAdd(value = '') {
      const indexes = [];
      this.typegroup.forEach((option, index) => {
        if (
          option.searchAdd &&
          (option.value ?? '').toString() !== value.toString()
        ) {
          indexes.push(index);
        }
      });
      // 翻转删除数组中的项
      for (const index of indexes.reverse()) {
        this.typegroup.splice(index, 1);
      }
    },
    // 移动POI点
    movePOI() {
      let d = null;
      const viewer = GISMap.viewer;
      d = new Drawer(viewer);
      d.dragEntity();
      d.drawStart('marker', null, (entity) => {
        console.log('......entity', entity);
        const newentity = entity;
        const loca = newentity.position.getValue();
        if (loca) {
          this.entity.position._value = loca;
          this.ismove = true;
          this.position = core.transformCartesianToWGS84(loca);
          viewer.entities.remove(newentity);
        }
      });
    },
    deletePOI() {
      api.deleteLayerTag({ id: this.billboard.id }).then((res) => {
        MyLayerService.deletePOI(this.billboard.id);
        this.$parent.$parent.close();
      });
    },
  },
};
</script>

<template>
  <div class="shape-setting-container">
    <div v-if="type == 'billboard'" class="marker">
      <div class="form-item">
        <div class="form-item-label" style="display: inline-flex">名称</div>
        <div
          class="el-form-item-content"
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 210px;
          "
        >
          <input v-model="billboard.name" style="width: 80px" />
          <input
            v-model="billboard.fontsize"
            min="7"
            style="width: 50px"
            type="number"
          />
          <ColorPicker
            v-model:pure-color="billboard.fontcolor"
            format="rgb"
            shape="circle"
            style="z-index: 100000"
          />
        </div>
      </div>
      <div class="form-item" style="height: 35px">
        <div class="form-item-label">类型</div>
        <div class="el-form-item-content">
          <ASelect
            v-model:value="billboard.tagtype"
            show-search
            style="
              width: 207px;
              margin-left: 3px;
              color: #fff;
              background: transparent;
            "
            @change="handleChange"
            @search="onSearch"
          >
            <a-select-option
              v-for="item in typegroup"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </ASelect>
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-label">图标</div>
        <div class="el-form-item-content" @click="getimg">
          <div class="markerIconBox">
            <img
              v-for="item in imgsgroup"
              :id="item.id"
              :key="item.id"
              :alt="item.name"
              :src="item.resUrl"
              class="img"
              style="margin: 3px 5px"
            />
          </div>
        </div>
      </div>
      <div class="form-item">
        <div class="form-item-label">备注</div>
        <div class="el-form-item-content">
          <textarea v-model="billboard.content"></textarea>
        </div>
      </div>
      <div style="margin: 5px 0 5px 25px">
        <AButton
          block
          style="
            display: block;
            width: 83%;
            margin: 0 auto;
            color: #fff;
            background: transparent;
          "
          type="primary"
          @click.stop="movePOI"
        >
          移动位置
        </AButton>
      </div>
    </div>
    <div class="footer">
      <AButton type="default" @click="saveas">保存</AButton>
      <AButton type="primary" @click="ok">预览</AButton>
      <AButton type="primary" @click="deletePOI">删除</AButton>
    </div>
  </div>
</template>

<style lang="less" scoped>
.shape-setting-container {
  width: 100%;
  .marker {
    padding: 10px;
    input {
      height: 26px;
      margin: 7px;
      line-height: 22px;
      font-size: 14px;
      background: rgba(40, 44, 52, 0.5);
      border: 1px solid rgba(40, 44, 52, 0.8);
      color: #fff;
    }
    textarea {
      background: rgba(40, 44, 52, 0.5);
      border: 1px solid rgba(40, 44, 52, 0.8);
      color: #fff;
      margin-left: 7px;
      height: 100%;
      width: 210px;
    }
    .form-item {
      margin-bottom: 5px;
    }
    .form-item-label {
      text-align: right;
      vertical-align: middle;
      float: left;
      font-size: 14px;
      color: #fff;
      line-height: 1.5715;
      padding: 0 12px 0 0;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
    }
    .form-item-content {
      line-height: 22px;
      position: relative;
      font-size: 14px;
      height: 50px;
      width: 210px;
    }
  }
  .footer {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    place-items: 10px;
    width: 100%;
    padding: 10px;
    border-top: 1px solid rgba(40, 44, 52, 0.8);
    .ant-btn {
      margin: 0 10px;
      color: rgba(0, 0, 0, 0.85);
      border-color: #d9d9d9;
      background: rgba(255, 255, 255, 0.3);
    }
    .ant-btn:hover,
    .ant-btn:focus,
    .ant-btn:active {
      color: rgba(0, 0, 0, 1);
    }
    .ant-btn-primary:hover,
    .ant-btn-primary:focus,
    .ant-btn-primary:active {
      color: #fff;
      border-color: #1890ff;
      background: #1890ff;
    }
  }
  .img {
    height: 30px;
    width: 30px;
    margin-right: 10px;
    margin-bottom: 5px;
  }
  .markerIconBox {
    width: 210px;
    margin-left: 7px;
    line-height: 35px;
    font-size: 14px;
    background: rgba(40, 44, 52, 0.5);
    border: 1px solid rgba(40, 44, 52, 0.8);
    color: #fff;
    max-height: 80px;
    overflow-y: scroll;
  }

  .markerIconBox::-webkit-scrollbar {
    width: 7px;
  }
  .markerIconBox::-webkit-scrollbar-thumb {
    background-color: rgba(30, 128, 255, 0.8);
    border-radius: 10%;
  }
  .colorui {
    margin: 5px 5px 10px 48px !important;
    line-height: 22px;
    z-index: 100000 !important;
  }
  :deep(.vc-color-wrap) {
    margin-top: 6px;
  }
  :deep(.ant-select-selection-item) {
    background: transparent;
    border: 0px solid #f0f0f0;
  }
  :deep(.shape-setting-container) {
    color: #fff;
    background: transparent;
    height: 26px;
    line-height: 26px;
    border: 1px solid #282c34;
    margin: 7px;
  }
}
</style>
