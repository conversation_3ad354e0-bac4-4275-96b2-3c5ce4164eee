import * as Cesium from 'cesium'
import {
	h,
	render
} from 'vue'
import CesiumMap from '../index'
import {
	deepEquals
} from '../../utils'
import Box from '../../../components/js/box.js'
import ztu from '../../../ztu'
import {capp} from '../../app.js'

export default class SplitMapManager {
	constructor(viewer) {
		//this.splitNumber = 0;
		this.viewer = viewer;
		this.splitViewers = [];
		this.changeEvent = null;
		this.mode = '';
	}
	/**
	 * 分屏
	 * @param {String} container 主屏Id
	 * @param {Object} options 
	 * @example new SplitMap().split('viewDiv',{
		 layout:[[0,1]],
		 synchronization:true,
		 callback:false,
		 render:null,//render(viewer){return h()}
		 controler:null,
	 })
	 */
	split(container, options) {
		options = {
			layout: [
				[0, 1]
			],
			synchronization: false,
			callback: false,
			render: null, //render(viewer){return h()}
			controler: null,
			...options
		}
		let that = this;
		this.rootNode = document.getElementById(container).parentNode;
		this.rootNode.style.position = this.rootNode.style.position || 'relative';
		this.restore();
		this.mode = 'split';
		console.log('split')
		this.rowNumber = options.layout.length;
		this.columnNumber = options.layout[0].length;
		/**
		 * 分屏数
		 */
		this.splitNumber = -1;
		for (let y = 0; y < options.layout.length; y++) {
			for (let x = 0; x < options.layout[y].length; x++) {
				this.splitNumber = Math.max(options.layout[y][x], this.splitNumber);
			}
		}
		this.splitNumber++;
		if (this.splitNumber < 0) return;
		/**
		 * 分屏位置及大小
		 */
		this.rectangles = [];
		for (let i = 0; i < this.splitNumber; i++) this.rectangles.push([-1, -1, -1, -1]);

		for (let y = 0; y < options.layout.length; y++) {
			for (let x = 0; x < options.layout[y].length; x++) {
				let n = options.layout[y][x];
				if (n < 0) return;
				let r = this.rectangles[n];
				r[0] = r[0] == -1 ? x : Math.min(r[0], x);
				r[1] = r[1] == -1 ? y : Math.min(r[1], y);
				r[2] = r[2] == -1 ? x : Math.max(r[2], x);
				r[3] = r[3] == -1 ? y : Math.max(r[3], y);
			}
		}
		if (deepEquals(this.rectangles[0], [-1, -1, -1, -1])) {
			this.rectangles[0] = [0, 0, 0, 0]; //隐藏主屏
		}

		this.rectangles = this.rectangles.filter(a => !deepEquals(a, [-1, -1, -1, -1])).map(a => {
			return [
				a[0] * 100 / this.columnNumber + '%', //left
				a[1] * 100 / this.rowNumber + '%', //top
				(a[2] - a[0] + 1) * 100 / this.columnNumber + '%', //width
				(a[3] - a[1] + 1) * 100 / this.rowNumber + '%' //height
			];
		});

		//主屏调整
		let node = this.rootNode.children[0];
		node.style.position = 'absolute';
		node.style.left = this.rectangles[0][0];
		node.style.top = this.rectangles[0][1];
		node.style.width = this.rectangles[0][2];
		node.style.height = this.rectangles[0][3];
		//生成分屏
		this.splitViewers = [{
			viewer:this.viewer,
			synchronization:true
		}];
		this.rectangles.forEach((rectangle, index) => {
			if (index == 0) return;
			let node = document.createElement('div');
			node.style.position = 'absolute';
			node.id = 'cesium_container_' + index;
			node.style.left = this.rectangles[index][0];
			node.style.top = this.rectangles[index][1];
			node.style.width = this.rectangles[index][2];
			node.style.height = this.rectangles[index][3];
			this.rootNode.appendChild(node);
			//创建cesium
			let v = new CesiumMap(node.id).viewer;
			this.splitViewers.push({
				viewer:v,
				synchronization:!!options.synchronization
			});
			//外部组件
			options.render && render(options.render(index,v), node);
		})
		//生成控制面板
		if (options.controler) {
			Box.open({
				//hasHeader:false,
				style: {
					//width:'100px',
				}
			}, h(options.controler, {
				data: this
			}))
		}

		//同步
		this.splitViewers.forEach(item  => {
			let viewer = item.viewer;
			viewer.camera.percentageChanged = 0.01;
			viewer.scene.camera.changed.addEventListener(() => {
				that.cameraSynchronization(item);
			});
		});
		options.synchronization && that.cameraSynchronization(this.splitViewers[0]);

		
		if (options.callback) {
			callback(that.splitViewers);
		}
	}
	setSynchronization(viewIndex,synchronization){
		let source = this.splitViewers[viewIndex];
		source.synchronization = synchronization;
		this.cameraSynchronization(this.splitViewers[0]);
	}
	cameraSynchronization(source) {
		if(!source.synchronization) return;
		let viewer = source.viewer;
		let ellipsoid = viewer.scene.globe.ellipsoid;
		let cartographic = ellipsoid.cartesianToCartographic(viewer.camera.position);
		let options = {
			position: {
				longitude: Cesium.Math.toDegrees(cartographic.longitude),
				latitude: Cesium.Math.toDegrees(cartographic.latitude),
				height: cartographic.height
			},
			orientation: {
				heading: viewer.camera.heading,
				pitch: viewer.camera.pitch,
				roll: viewer.camera.roll
			}
		}
		this.splitViewers.forEach(item  => {
			if (item.viewer !== viewer && item.synchronization)
				setView(item.viewer)
		})
	
		function setView(viewer) {
			viewer.camera.setView({
				destination: Cesium.Cartesian3.fromDegrees(options.position.longitude, options.position
					.latitude, options.position.height),
				orientation: options.orientation,
			})
		}
	}
	/**
	 * 卷帘
	 * @param {String} sliderId 滑块Id
	 * @param {*} options {
		 style:'',
	 }
	 */
	curtain(container, options) {
		options = {
			//vertical: false,
			style:'',
			...options
		}
		let that = this;
		this.restore();
		this.mode = 'curtain';
		const slider = this.slider = document.createElement('div');
		document.getElementById(container).appendChild(slider);
		slider.style = options.style || 
				"position: absolute;left: 50%;top: 0px;background-color: rgba(40,44,52,0.8);width: 5px;height: 100%;z-index: 99;cursor:ew-resize;";

		this.viewer.scene.splitPosition = slider.offsetLeft / slider.parentElement.offsetWidth;
		const handler = this.sliderHandler = new Cesium.ScreenSpaceEventHandler(slider);
		let moveActive = false;

		let move = (movement)=> {
			if (!moveActive) {
				return;
			}
			const relativeOffset = movement.endPosition.x;
			const splitPosition =
				(slider.offsetLeft + relativeOffset) /
				slider.parentElement.offsetWidth;
			slider.style.left = `${100.0 * splitPosition}%`;
			that.viewer.scene.splitPosition = splitPosition;
		}

		handler.setInputAction(function() {
			moveActive = true;
		}, Cesium.ScreenSpaceEventType.LEFT_DOWN);
		handler.setInputAction(function() {
			moveActive = true;
		}, Cesium.ScreenSpaceEventType.PINCH_START);

		handler.setInputAction(move, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
		handler.setInputAction(move, Cesium.ScreenSpaceEventType.PINCH_MOVE);

		handler.setInputAction(function() {
			moveActive = false;
		}, Cesium.ScreenSpaceEventType.LEFT_UP);
		handler.setInputAction(function() {
			moveActive = false;
		}, Cesium.ScreenSpaceEventType.PINCH_END);
	}
	restore() {
		if(!this.mode) return;
		this.mode='';
		if (this.rootNode && this.rootNode.children && this.rootNode.children.length) {
			let children = this.rootNode.children;
			//删除分屏
			while (children.length > 1) {
				this.rootNode.removeChild(children[1]);
			}
			//主屏还原
			let node = children[0];
			node.style.position = 'absolute';
			node.style.left = '0';
			node.style.top = '0';
			node.style.width = '100%';
			node.style.height = '100%';
		}
		if (this.splitViewers && this.splitViewers.length) {
			this.splitViewers.forEach((item,index) => {
				if(index==0) return;
				let viewer = item.viewer;
				//释放viewer
				if (viewer && !viewer.isDestroyed())
					viewer.destroy();
			})
			this.splitViewers = [];
		}
		if (this.changeEvent) {
			console.log(this.changeEvent)
		}
		if(this.slider){
			//this.slider.style.display = 'none';
			this.slider.parentElement.removeChild(this.slider);
			this.slider = null;
		}
		if (this.sliderHandler && !this.sliderHandler.isDestroyed()){
			this.sliderHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
			this.sliderHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
			this.sliderHandler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
			this.sliderHandler.removeInputAction(Cesium.ScreenSpaceEventType.PINCH_START);
			this.sliderHandler.removeInputAction(Cesium.ScreenSpaceEventType.PINCH_END);
			this.sliderHandler.removeInputAction(Cesium.ScreenSpaceEventType.PINCH_MOVE);
			this.sliderHandler.destroy();
			this.sliderHandler = null;
			
		}
		//console.log(mapLayers,mapLayers.getSplitLayers)
		ztu.global.GISLayers.getSplitLayers().forEach(layer=>{
			layer = ztu.global.GISLayers.getLayer(layer.key);
			layer.refLayer.splitDirection = Cesium.SplitDirection.NONE;
		})
		Box.closeAll();
	}
}
