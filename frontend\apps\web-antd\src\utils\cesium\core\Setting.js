//js构造viewer对象时候隐藏
var viewer = new Cesium.Viewer("cesiumContainer", {
	animation: false, //隐藏掉时钟 图中6
	timeline: false, //隐藏时间轴 图中7
	fullscreenButton: false, //隐藏全屏按钮 图中8
	geocoder: false, //隐藏搜索按钮 图中1
	homeButton: false, //隐藏home按钮 图中2
	navigationHelpButton: false, //隐藏帮助按钮 图中5
	sceneModePicker: false, //隐藏切换二三维按钮 图中3
	baseLayerPicker: false, //隐藏切换底图按钮 图中4
	creditContainer: document.createElement("div"), //隐藏logo  图中9
});
//2.通过viewer对象来隐藏div的方式
viewer.animation._container.style.display = "none"; //隐藏掉时钟 图中6
viewer.timeline.container.style.display = "none"; //隐藏时间轴 图中7
viewer.fullscreenButton.container.style.display = "none"; //隐藏全屏按钮 图中8
viewer.geocoder.container.style.display = "none"; //隐藏搜索按钮 图中1
viewer.homeButton.container.style.display = "none"; //隐藏home按钮 图中2
viewer.navigationHelpButton.container.style.display = "none"; //隐藏帮助按钮 图中5
viewer.sceneModePicker.container.style.display = "none"; //隐藏切换二三维按钮 图中3
viewer.baseLayerPicker.container.style.display = "none"; //隐藏切换底图按钮 图中4
viewer._cesiumWidget._creditContainer.style.display = "none"; //隐藏logo 图中9
//二，修改cesium场景中鼠标的默认操作
//Cesium里面鼠标的操作主要在ScreenSpaceCameraController.js里面
viewer.scene.screenSpaceCameraController.enableInputs //设置为true的话，场景里可以旋转，拖拽等。设置为false将禁用所有鼠标的输入事件
viewer.scene.screenSpaceCameraController.enableTranslate //是否可以拖动地图,只在2d和2.5d场景里生效
viewer.scene.screenSpaceCameraController.enableZoom //是否可以缩放
viewer.scene.screenSpaceCameraController.enableRotate //是否可以旋转地图,只在2d和2.5d场景里生效
viewer.scene.screenSpaceCameraController.enableTilt //是否可以倾斜地图,只在3d和2.5d场景生效
viewer.scene.screenSpaceCameraController.enableLook //是否允许使用自由外观,只改变相机的朝向，不改变相机位置
viewer.scene.screenSpaceCameraController.inertiaSpin //旋转惯性
viewer.scene.screenSpaceCameraController.inertiaTranslate //平移惯性
viewer.scene.screenSpaceCameraController.inertiaZoom //缩放惯性
viewer.scene.screenSpaceCameraController.bounceAnimationTime //切换2d,2.5d，3d模式之间的时间间隔，默认3s
viewer.scene.screenSpaceCameraController.minimumZoomDistance //相机离地表的最低高度，默认1米，比如设置为-100米的情况下相机将钻入地下
viewer.scene.screenSpaceCameraController.maximumZoomDistance //相机离地表的最大高度，默认为无穷大
viewer.scene.screenSpaceCameraController.translateEventTypes = Cesium.CameraEventType
	.LEFT_DRAG //移动场景的事件，默认是鼠标按住左键拖拽地图,可自定义移动场景的鼠标事件，2d和2.5d有效
viewer.scene.screenSpaceCameraController.zoomEventTypes = [
	Cesium.CameraEventType.RIGHT_DRAG,
	Cesium.CameraEventType.WHEEL,
	Cesium.CameraEventType.PINCH,
]
//鼠标缩放事件，传入的是一个数组,默认鼠标右键拖拽，鼠标滚轮滚动，两个手指滚动笔记本触控区都可以触发场景缩放效果
viewer.scene.screenSpaceCameraController.rotateEventTypes = Cesium.CameraEventType
	.LEFT_DRAG //旋转场景，默认是左键拖拽，只在2.5d和3d场景生效
viewer.scene.screenSpaceCameraController.tiltEventTypes = [
	Cesium.CameraEventType.MIDDLE_DRAG,
	Cesium.CameraEventType.PINCH,
	{
		eventType: Cesium.CameraEventType.LEFT_DRAG,
		modifier: Cesium.KeyboardEventModifier.CTRL,
	},
	{
		eventType: Cesium.CameraEventType.RIGHT_DRAG,
		modifier: Cesium.KeyboardEventModifier.CTRL,
	},
]
//场景倾斜事件，默认是鼠标滚轮下按拖拽，按CTRL+左键拖拽，按CTRL+右键拖拽都可以使场景倾斜，建议将场景倾斜改成右键拖拽使用起来更方便一点，比如
viewer.scene.screenSpaceCameraController.tiltEventTypes = Cesium.CameraEventType.RIGHT_DRAG
viewer.scene.screenSpaceCameraController.lookEventTypes = {
	eventType: CameraEventType.LEFT_DRAG,
	modifier: KeyboardEventModifier.SHIFT,
}
//相机位置不变，改变相机方向进行自由观看，默认是按住SHIFTT键+左键拖拽
viewer.scene.screenSpaceCameraController.enableCollisionDetection //是否开启碰撞检测，默认是开启
