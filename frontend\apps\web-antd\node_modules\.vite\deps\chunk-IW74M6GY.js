import {
  button_default
} from "./chunk-PZMLOBLO.js";
import {
  dynamicApp
} from "./chunk-VRANVM3Q.js";
import {
  VxeUI
} from "./chunk-DULHHPCE.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/button/index.js
var VxeButton = Object.assign({}, button_default, {
  install(app) {
    app.component(button_default.name, button_default);
  }
});
dynamicApp.use(VxeButton);
VxeUI.component(button_default);
var Button = VxeButton;
var button_default2 = VxeButton;

export {
  VxeButton,
  Button,
  button_default2 as button_default
};
//# sourceMappingURL=chunk-IW74M6GY.js.map
