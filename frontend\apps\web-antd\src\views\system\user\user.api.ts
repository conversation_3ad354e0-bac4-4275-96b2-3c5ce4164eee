import { requestClient } from '#/api/request';

enum Api {
  list = '/users',
  create = '/users',
  update = '/users',
  delete = '/users',
  detail = '/users',
  batchDelete = '/users/batch',
  resetPassword = '/users/reset-password',
  assignRoles = '/users/assign-roles',
  batchAssignRoles = '/users/batch-assign-roles',
}

/**
 * 获取用户列表
 * @param params 查询参数
 */
export const getUserList = (params?: any) => {
  return requestClient.get(Api.list, { params });
};

/**
 * 创建用户
 * @param data 用户数据
 */
export const createUser = (data: any) => {
  return requestClient.post(Api.create, data);
};

/**
 * 更新用户
 * @param id 用户ID
 * @param data 用户数据
 */
export const updateUser = (id: number, data: any) => {
  return requestClient.put(`${Api.update}/${id}`, data);
};

/**
 * 删除用户
 * @param id 用户ID
 */
export const deleteUser = (id: number) => {
  return requestClient.delete(`${Api.delete}/${id}`);
};

/**
 * 获取用户详情
 * @param id 用户ID
 */
export const getUserDetail = (id: number) => {
  return requestClient.get(`${Api.detail}/${id}`);
};

/**
 * 批量删除用户
 * @param ids 用户ID数组
 */
export const batchDeleteUsers = (ids: number[]) => {
  return requestClient.delete(Api.batchDelete, { data: { ids } });
};

/**
 * 重置用户密码
 * @param id 用户ID
 * @param password 新密码
 */
export const resetUserPassword = (id: number, password: string) => {
  return requestClient.put(`${Api.resetPassword}/${id}`, { password });
};

/**
 * 批量重置用户密码
 * @param ids 用户ID数组
 * @param password 新密码
 */
export const batchResetPassword = (ids: number[], password: string) => {
  return requestClient.put(`${Api.resetPassword}/batch`, { ids, password });
};

/**
 * 分配用户角色
 * @param id 用户ID
 * @param roleIds 角色ID数组
 */
export const assignUserRoles = (id: number, roleIds: number[]) => {
  return requestClient.put(`${Api.assignRoles}/${id}`, { roleIds });
};

/**
 * 批量分配用户角色
 * @param ids 用户ID数组
 * @param roleIds 角色ID数组
 */
export const batchAssignRoles = (ids: number[], roleIds: number[]) => {
  return requestClient.put(Api.batchAssignRoles, { userIds: ids, roleIds });
};

/**
 * 更新用户状态
 * @param id 用户ID
 * @param status 状态：1启用，0禁用
 */
export const updateUserStatus = (id: number, status: number) => {
  return requestClient.put(`${Api.update}/${id}`, { status });
};

/**
 * 批量更新用户状态
 * @param ids 用户ID数组
 * @param status 状态：1启用，0禁用
 */
export const batchUpdateStatus = (ids: number[], status: number) => {
  return requestClient.put(`${Api.update}/batch-status`, { ids, status });
};
