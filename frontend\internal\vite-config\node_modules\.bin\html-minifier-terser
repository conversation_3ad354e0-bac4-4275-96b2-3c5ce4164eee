#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/html-minifier-terser@7.2.0/node_modules/html-minifier-terser/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/html-minifier-terser@7.2.0/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/html-minifier-terser@7.2.0/node_modules/html-minifier-terser/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/html-minifier-terser@7.2.0/node_modules:/mnt/e/work/git/system-manage-fed/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../html-minifier-terser/cli.js" "$@"
else
  exec node  "$basedir/../html-minifier-terser/cli.js" "$@"
fi
