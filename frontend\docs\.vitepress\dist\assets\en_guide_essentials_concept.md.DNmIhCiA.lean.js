import{ao as a,k as s,aP as e,l as i,ay as t,j as n}from"./chunks/framework.C8U7mBUf.js";const p=JSON.parse('{"title":"Basic Concepts","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/essentials/concept.md","filePath":"en/guide/essentials/concept.md"}');const o=a({name:"en/guide/essentials/concept.md"},[["render",function(a,p,o,l,c,h){const r=t("NolebaseGitContributors"),d=t("NolebaseGitChangelog");return n(),s("div",null,[p[0]||(p[0]=e('<h1 id="basic-concepts" tabindex="-1">Basic Concepts <a class="header-anchor" href="#basic-concepts" aria-label="Permalink to &quot;Basic Concepts&quot;">​</a></h1><p>In the new version, the entire project has been restructured. Now, we will introduce some basic concepts to help you better understand the entire document. Please make sure to read this section first.</p><h2 id="monorepo" tabindex="-1">Monorepo <a class="header-anchor" href="#monorepo" aria-label="Permalink to &quot;Monorepo&quot;">​</a></h2><p>Monorepo refers to the repository of the entire project, which includes all code, packages, applications, standards, documentation, configurations, etc., that is, the entire content of a <code>Monorepo</code> directory.</p><h2 id="applications" tabindex="-1">Applications <a class="header-anchor" href="#applications" aria-label="Permalink to &quot;Applications&quot;">​</a></h2><p>Applications refer to a complete project; a project can contain multiple applications, which can reuse the code, packages, standards, etc., within the monorepo. Applications are placed in the <code>apps</code> directory. Each application is independent and can be run, built, tested, and deployed separately; it can also include different component libraries, etc.</p><div class="tip custom-block"><p class="custom-block-title">TIP</p><p>Applications are not limited to front-end applications; they can also be back-end applications, mobile applications, etc. For example, <code>apps/backend-mock</code> is a back-end service.</p></div><h2 id="packages" tabindex="-1">Packages <a class="header-anchor" href="#packages" aria-label="Permalink to &quot;Packages&quot;">​</a></h2><p>A package refers to an independent module, which can be a component, a tool, a library, etc. Packages can be referenced by multiple applications or other packages. Packages are placed in the <code>packages</code> directory.</p><p>You can consider these packages as independent <code>npm</code> packages, and they are used in the same way as <code>npm</code> packages.</p><h3 id="package-import" tabindex="-1">Package Import <a class="header-anchor" href="#package-import" aria-label="Permalink to &quot;Package Import&quot;">​</a></h3><p>Importing a package in <code>package.json</code>:</p><div class="language-json vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;dependencies&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>\n<span class="line highlighted"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;@vben/utils&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;workspace:*&quot;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><h3 id="package-usage" tabindex="-1">Package Usage <a class="header-anchor" href="#package-usage" aria-label="Permalink to &quot;Package Usage&quot;">​</a></h3><p>Importing a package in the code:</p><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { isString } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;@vben/utils&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span></code></pre></div><h2 id="aliases" tabindex="-1">Aliases <a class="header-anchor" href="#aliases" aria-label="Permalink to &quot;Aliases&quot;">​</a></h2><p>In the project, you can see some paths starting with <code>#</code>, such as <code>#/api</code>, <code>#/views</code>. These paths are aliases, used for quickly locating a certain directory. They are not implemented through <code>vite</code>&#39;s <code>alias</code>, but through the principle of <a href="https://nodejs.org/api/packages.html#subpath-imports" target="_blank" rel="noreferrer">subpath imports</a> in <code>Node.js</code> itself. You only need to configure the <code>imports</code> field in <code>package.json</code>.</p><div class="language-json vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;imports&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>\n<span class="line highlighted"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;#/*&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;./src/*&quot;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p>To make these aliases recognizable by the IDE, we also need to configure them in <code>tsconfig.json</code>:</p><div class="language-json vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;compilerOptions&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;baseUrl&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;.&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;paths&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>\n<span class="line highlighted"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;#/*&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;src/*&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">]</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p>This way, you can use aliases in your code.</p>',22)),i(r),i(d)])}]]);export{p as __pageData,o as default};
