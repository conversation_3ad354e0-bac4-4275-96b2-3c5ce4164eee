import * as Cesium from 'cesium';

import { GISMap as map } from '../index';
import {
  arcgisToGeoJSON,
  geojsonToArcGIS,
} from '@esri/arcgis-to-geojson-utils';
import { h } from 'vue';
import core from '../core';
import request from './arcgisRequest';

const HIGHTLIGHT_LAYER_NAME = '___HIGHLIGHT';
const STATISTICS_FUNCTION_LIST = [
  {
    label: '记录数',
    value: 'count',
  },
  {
    label: '总和',
    value: 'sum',
  },
  {
    label: '平均数',
    value: 'avg',
  },
  {
    label: '最小值',
    value: 'min',
  },
  {
    label: '最大值',
    value: 'max',
  },
  {
    label: '标准差',
    value: 'stddev',
  },
  {
    label: '方差',
    value: 'var',
  },
];
class ArcGisQuery {
  constructor(layerInfo) {
    this.url = layerInfo.data.url || '';
    this.layers = layerInfo.data.layers || '0';
    this.token = layerInfo.data.token || '';
  }
  buildParams(params) {
    let defaultData = {
      where: '1=1',
      text: '',
      objectIds: '',
      time: '',
      geometry: '',
      geometryType: 'esriGeometryEnvelope',
      inSR: '4326',
      spatialRel: 'esriSpatialRelIntersects',
      relationParam: '',
      outFields: '*',
      returnGeometry: false,
      returnTrueCurves: false,
      maxAllowableOffset: '',
      geometryPrecision: '',
      outSR: '4326',
      returnIdsOnly: false,
      returnCountOnly: false,
      orderByFields: '',
      groupByFieldsForStatistics: '',
      outStatistics: '',
      returnZ: false,
      returnM: false,
      gdbVersion: '',
      returnDistinctValues: false,
      resultOffset: '',
      resultRecordCount: '',
      queryByDistance: '',
      returnExtentsOnly: false,
      datumTransformation: '',
      parameterValues: '',
      rangeValues: '',
      f: 'json',
    };
    return {
      ...defaultData,
      ...params,
      ...(() => {
        return this.token
          ? {
              token: this.token,
            }
          : {};
      })(),
    };
  }
  async buildRequest(params) {
    return await request({
      url: `${this.url.trim('/')}/${encodeURIComponent(this.layers)}/query`,
      method: 'get',
      params: this.buildParams(params),
    });
  }
  getSpatialRel(spatialRel) {
    let spatialRels = {
      intersects: 'esriSpatialRelIntersects',
      contains: 'esriSpatialRelContains',
      crosses: 'esriSpatialRelCrosses',
      'envelope-intersects': 'esriSpatialRelEnvelopeIntersects',
      overlaps: 'esriSpatialRelOverlaps',
      touches: 'esriSpatialRelTouches',
      within: 'esriSpatialRelWithin',
      'disjoint-intersects': 'esriSpatialRelIndexIntersects',
    };
    if (spatialRel && typeof spatialRel === 'string')
      return spatialRels[spatialRel] || 'esriSpatialRelIntersects';
    return 'esriSpatialRelIntersects';
  }
  getGeometry(geojson) {
    let geometry = [];
    let geometryType = '';
    geojson = geojson || [];
    geojson.forEach((a) => {
      if (!geometryType) geometryType = a.geometry.type;
      else if (geometryType != a.geometry.type) return;
      geometry.push(a.geometry.coordinates);
    });
    if (geometryType) {
      geometryType = 'esriGeometry' + geometryType;
    } else {
      geometryType = 'esriGeometryEnvelope';
      geometry = '';
    }
    let spatialReference = {
      spatialReference: {
        wkid: 4326,
      },
    };
    if (geometryType == 'esriGeometryPoint') {
      if (geometry.length == 1) {
        let p = geometry.shift();
        geometry = {
          x: p[0],
          y: p[1],
          ...spatialReference,
        };
      } else {
        geometryType = 'esriGeometryMultipoint';
        geometry = {
          points: geometry,
          ...spatialReference,
        };
      }
    } else if (geometryType == 'esriGeometryPolyline') {
      geometry = {
        paths: geometry,
        ...spatialReference,
      };
    } else if (geometryType == 'esriGeometryPolygon') {
      geometry = {
        rings: geometry,
        ...spatialReference,
      };
    }
    return {
      geometryType: geometryType,
      geometry: geometry ? JSON.stringify(geometry) : '',
    };
  }
  async query(params) {
    let geometry = this.getGeometry(params.geometry);
    let spatialRel = this.getSpatialRel(params.spatialRel);
    let data = await this.buildRequest({
      where: params.where || '1=1',
      returnGeometry: params.returnGeometry || false,
      geometry: geometry.geometry,
      geometryType: geometry.geometryType,
      outFields: params.outFields || '*',
      spatialRel: spatialRel,
      resultOffset: params.resultOffset || '',
      resultRecordCount: params.resultRecordCount || '',
      queryByDistance: params.queryByDistance || '',
      returnDistinctValues: params.returnDistinctValues || false,
    });
    return arcgisToGeoJSON(data);
  }
  async queryCount(params) {
    let geometry = this.getGeometry(params.geometry);
    let spatialRel = this.getSpatialRel(params.spatialRel);
    let data = await this.buildRequest({
      where: params.where || '1=1',
      geometry: geometry.geometry,
      geometryType: geometry.geometryType,
      spatialRel: spatialRel,
      returnCountOnly: true,
      queryByDistance: params.queryByDistance || '',
    });
    return (data && data.count) || 0;
  }
  async basicStatistics(params) {
    if (!params || !params.statisticField) {
      throw new Error('统计项不明确');
    }
    let statItems = [];
    let outStatistics = STATISTICS_FUNCTION_LIST.map((a) => {
      return {
        onStatisticField: params.statisticField,
        outStatisticFieldName: a.label,
        statisticType: a.value,
      };
    });
    outStatistics = outStatistics.length ? JSON.stringify(outStatistics) : '';
    let geometry = this.getGeometry(params.geometry);
    let spatialRel = this.getSpatialRel(params.spatialRel);
    let data = await this.buildRequest({
      where: params.where || '1=1',
      geometry: geometry.geometry,
      geometryType: geometry.geometryType,
      spatialRel: spatialRel,
      queryByDistance: params.queryByDistance || '',
      outStatistics: outStatistics,
    });
    let attributes =
      data && data.features ? data.features.map((a) => a.attributes) : [];
    if (attributes.length) return attributes[0];
    return null;
  }
  async customizeStatistics(params) {
    let groupByFieldsForStatistics = params.groupByFields || '';
    let outStatistics = (params.outStatistics || [])
      .filter((a) => a.field && a.exp)
      .map((a) => {
        return {
          onStatisticField: a.field,
          outStatisticFieldName: `${a.field}_${a.exp}`,
          statisticType: a.exp,
        };
      });
    outStatistics = outStatistics.length ? JSON.stringify(outStatistics) : '';
    let data = null;
    if (params.groupByBetweens && params.groupByBetweens.length > 0) {
      let result = [];
      for (let index = 0; index < params.groupByBetweens.length; index++) {
        let item = params.groupByBetweens[index];
        let where = `(${params.where || '1=1'})`;
        where += ` and ${groupByFieldsForStatistics} <= ${item.value2}`;
        where += ` and ${groupByFieldsForStatistics} > ${item.value1}`;
        data = await this.buildRequest({
          where: params.where || '1=1',
          geometry: geometry.geometry,
          geometryType: geometry.geometryType,
          spatialRel: spatialRel,
          queryByDistance: params.queryByDistance || '',
          outStatistics: outStatistics,
          groupByFieldsForStatistics: groupByFieldsForStatistics,
        });
        (data && data.features
          ? data.features.map((a) => a.attributes)
          : []
        ).forEach((item2) => {
          item2 &&
            (item2[groupByFieldsForStatistics] =
              item.label || `${item.value1}-${item.value2}`);
          result.push(item2 || {});
        });
      }
      return result;
    } else {
      data = await this.buildRequest({
        where: params.where || '1=1',
        geometry: geometry.geometry,
        geometryType: geometry.geometryType,
        spatialRel: spatialRel,
        queryByDistance: params.queryByDistance || '',
        outStatistics: outStatistics,
        groupByFieldsForStatistics: groupByFieldsForStatistics,
      });
      return data && data.features
        ? data.features.map((a) => a.attributes)
        : [];
    }
  }
  async queryCapabilities() {
    let data = await request({
      url: `${this.url.trim('/')}/${this.layers}`,
      method: 'get',
      params: {
        f: 'pjson',
        ...(() => {
          return this.token ? { token: this.token } : {};
        })(),
      },
    });
    debugger;
    let result = data && {
      useStandardizedQueries: data.useStandardizedQueries || false,
      supportsPagination:
        (data.supportsAdvancedQueries &&
          data.advancedQueryCapabilities.supportsPagination) ||
        false,
      maxRecordCount: data.maxRecordCount || 0,
      supportsStatistics: data.supportsStatistics || false,
      supportsAdvancedQueries: data.supportsAdvancedQueries || false,
      fields: (data.fields || []).map((a) => {
        return {
          name: a.name,
          cnName: a.alias,
          allowQuery: a.type == 'esriFieldTypeString',
          allowDisplay: a.type != 'esriFieldTypeGeometry',
        };
      }),
      extent: {
        ...(data.extent || data.fullExtent),
        CRS: 'EPSG:' + (data.extent || data.fullExtent).spatialReference.wkid,
      },
    };
    return result;
  }
}
class GeoServerQuery {
  constructor(layerInfo) {
    this.url = layerInfo.data.url || '';
    this.layers = layerInfo.data.layers || '0';
    this.token = layerInfo.data.token || '';
  }
  buildParams(params) {
    let defaultData = {
      service: 'WFS',
      version: '1.0.0',
      request: 'GetFeature',
      maxFeatures: '',
      startIndex: '',
      outputFormat: 'application/json',
      // callback : 'getIdentifyroadGrid',//查询的回调函数,jsonp调用
    };
    return {
      ...defaultData,
      ...params,
      ...(() => {
        return this.token
          ? {
              token: this.token,
            }
          : {};
      })(),
    };
  }
  async buildRequest(params) {
    return await request({
      url: this.url.trim(),
      method: 'GET',
      params: this.buildParams(params),
    });
  }
  async query(params) {
    let data = await this.buildRequest(params);
    return data;
  }
}

export default class MapAdvancedQuery {
  constructor(options) {
    this.viewer = options.viewer;
  }
  buildQuery(layerInfo) {
    if (layerInfo.type.startsWith('arcgis:')) {
      return new ArcGisQuery(layerInfo);
    } else if (layerInfo.type.startsWith('geoserver:')) {
      return new GeoServerQuery(layerInfo);
    }
    throw new Error('未知的图层服务类型');
  }
  addVisitRecord(layerInfo, funInfo) {
    this.emit('visit', {
      layerInfo,
      funInfo,
    });
  }
  async queryCapabilities(layerInfo) {
    let queryHelper = this.buildQuery(layerInfo);
    return await queryHelper.queryCapabilities();
  }
  str2Unicode(str) {
    if (!str) return;
    let es = [];
    for (let i = 0; i < str.length; i++)
      es[i] = ('00' + str.charCodeAt(i).toString(16)).slice(-4);
    return '\\u' + es.join('\\u');
  }
  async query(layerInfo, options) {
    if (!layerInfo) {
      throw new Error('图层信息不可为空');
    }
    options = options || {};
    let queryHelper = this.buildQuery(layerInfo);
    let params = null;
    if (layerInfo.type.startsWith('arcgis:')) {
      params = {
        where: options.where,
        geometry: this.toGeoJson(options.entities),
        returnGeometry: options.show,
        outFields: options.outFields,
        spatialRel: options.spatialRel,
        resultOffset: options.resultOffset,
        resultRecordCount: options.resultRecordCount,
        queryByDistance: options.queryByDistance,
        returnDistinctValues: options.returnDistinctValues,
      };
    } else if (layerInfo.type.startsWith('geoserver:')) {
      params = {
        typeName: queryHelper.layers,
        cql_filter: this.str2Unicode(options.where) || '',
        startIndex: options.resultOffset,
        maxFeatures: options.resultRecordCount,
        propertyName: options.propertyName || '*',
      };
    }
    let data = await queryHelper.query(params);
    console.log(data);
    if (options.show) {
      let hightLightDataSources = this.viewer.dataSources.getByName(
        HIGHTLIGHT_LAYER_NAME,
      );
      hightLightDataSources.forEach((ds) => {
        this.viewer.dataSources.remove(ds, true);
      });
      let dataSource = await Cesium.GeoJsonDataSource.load(data, {
        clampToGround: true,
      });
      dataSource.name = HIGHTLIGHT_LAYER_NAME;
      this.viewer.dataSources.add(dataSource);
      this.viewer.flyTo(dataSource, {
        offset: new Cesium.HeadingPitchRange(
          this.viewer.camera.heading,
          this.viewer.camera.pitch,
          0,
        ),
      });
      return dataSource;
    } else {
      let result = data ? data.features.map((a) => a.properties) : [];
      if (data && data.totalFeatures) {
        result.push({ totalFeatures: data.totalFeatures });
      }
      return result;
    }
  }
  async queryDistinct(layerInfo, options) {
    options = options || {};
    if (layerInfo.type.startsWith('arcgis:')) {
      return await this.query(layerInfo, {
        where: options.where,
        entities: options.entities,
        show: options.show,
        outFields: options.outFields,
        spatialRel: options.spatialRel,
        queryByDistance: options.queryByDistance,
        returnDistinctValues: true,
      });
    } else if (layerInfo.type.startsWith('geoserver:')) {
      return await this.query(layerInfo, {
        propertyName: options.outFields,
        where: '1=1',
        resultOffset: 0,
        resultRecordCount: 1000,
      });
    }
  }
  async queryCount(layerInfo, options) {
    let queryHelper = this.buildQuery(layerInfo);
    options = options || {};
    let params = {
      where: options.where,
      geometry: this.toGeoJson(options.entities),
      spatialRel: options.spatialRel,
      queryByDistance: options.queryByDistance,
    };
    let count = await queryHelper.queryCount(params);
    return count;
  }
  async basicStatistics(layerInfo, options) {
    if (!options || !options.statisticField) {
      throw new Error('统计项不明确');
    }
    let queryHelper = this.buildQuery(layerInfo);
    let params = {
      where: options.where,
      geometry: this.toGeoJson(options.entities),
      spatialRel: options.spatialRel,
      queryByDistance: options.queryByDistance,
      statisticField: options.statisticField,
    };
    return await queryHelper.basicStatistics(params);
  }
  async customizeStatistics(layerInfo, options) {
    let queryHelper = this.buildQuery(layerInfo);
    let params = {
      where: options.where,
      geometry: this.toGeoJson(options.entities),
      spatialRel: options.spatialRel,
      queryByDistance: options.queryByDistance,
      groupByFields: options.groupByFields,
      outStatistics: options.outStatistics,
      groupByBetweens: options.groupByBetweens,
    };
    let dataList = await queryHelper.basicStatistics(params);
    return dataList;
  }
  toGeoJson(entities) {
    if (!entities) return null;
    let json = {
      type: 'FeatureCollection',
      features: [],
    };
    let features = json.features;
    /* features.push({
			type:"Feature",
			geometry:{
				type:"Polygon",
				coordinates:[]
			}
		}) */
    let trans = (position) => {
      var ellipsoid = Cesium.Ellipsoid.WGS84;
      var cartographic = ellipsoid.cartesianToCartographic(
        new Cesium.Cartesian3(position.x, position.y, position.z),
      );
      //console.log(position,cartographic)
      return {
        x: Cesium.Math.toDegrees(cartographic.longitude),
        y: Cesium.Math.toDegrees(cartographic.latitude),
      };
    };
    entities.forEach((item) => {
      let entity = core.entity2Json(item);

      let coordinates = [];
      if (entity.point) {
        features.push({
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: ((a) => {
              a = trans(a);
              return [a.x, a.y];
            })(entity.position),
          },
        });
      } else if (entity.polyline) {
        features.push({
          type: 'Feature',
          geometry: {
            type: 'Polyline',
            coordinates: [
              entity.polyline.positions.map((a) => {
                a = trans(a);
                return [a.x, a.y];
              }),
            ],
          },
        });
      } else if (entity.polygon) {
        features.push({
          type: 'Feature',
          geometry: {
            type: 'Polygon',
            coordinates: [
              entity.polygon.hierarchy.positions.map((a) => {
                a = trans(a);
                return [a.x, a.y];
              }),
            ],
          },
        });
      } else if (entity.rectangle) {
        let extent = {
          xmin: (entity.rectangle.coordinates.west / Math.PI) * 180,
          ymin: (entity.rectangle.coordinates.south / Math.PI) * 180,
          xmax: (entity.rectangle.coordinates.east / Math.PI) * 180,
          ymax: (entity.rectangle.coordinates.north / Math.PI) * 180,
        };
        features.push({
          type: 'Feature',
          geometry: {
            type: 'Polygon',
            coordinates: [
              [
                [extent.xmin, extent.ymin],
                [extent.xmin, extent.ymax],
                [extent.xmax, extent.ymax],
                [extent.xmax, extent.ymin],
              ],
            ],
          },
        });
      } else if (entity.ellipse) {
        let b = entity.position;
        let a = new Cesium.Cartesian3(
          b.x + entity.ellipse.semiMinorAxis,
          b.y,
          b.z,
        );
        let positions = [];
        [...Array(360)].forEach((_, i) => {
          positions.push(core.rotatedPointByAngle(a, b, i));
        });

        features.push({
          type: 'Feature',
          geometry: {
            type: 'Polygon',
            coordinates: [
              positions.map((a) => {
                a = trans(a);
                return [a.x, a.y];
              }),
            ],
          },
        });
      }
    });
    return json;
  }
}
