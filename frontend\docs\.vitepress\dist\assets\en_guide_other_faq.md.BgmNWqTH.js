import{ao as s,k as i,z as a,I as e,aP as t,l as n,ay as l,j as h}from"./chunks/framework.C8U7mBUf.js";const o=JSON.parse('{"title":"Frequently Asked Questions #","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/other/faq.md","filePath":"en/guide/other/faq.md"}');const p=s({name:"en/guide/other/faq.md"},[["render",function(s,o,p,r,d,k){const c=l("NolebaseGitContributors"),g=l("NolebaseGitChangelog");return h(),i("div",null,[o[0]||(o[0]=a("h1",{faq:"",id:"frequently-asked-questions",tabindex:"-1"},[e("Frequently Asked Questions # "),a("a",{class:"header-anchor",href:"#frequently-asked-questions","aria-label":'Permalink to "Frequently Asked Questions #{faq}"'},"​")],-1)),o[1]||(o[1]=t('<div class="tip custom-block"><p class="custom-block-title">Listed are some common questions</p><p>If you have a question, you can first look here. If not found, you can search or submit your question on <a href="https://github.com/vbenjs/vue-vben-admin/issues" target="_blank" rel="noreferrer">GitHub Issue</a>, or if it&#39;s a discussion-type question, you can go to <a href="https://github.com/vbenjs/vue-vben-admin/discussions" target="_blank" rel="noreferrer">GitHub Discussions</a></p></div><h2 id="instructions" tabindex="-1">Instructions <a class="header-anchor" href="#instructions" aria-label="Permalink to &quot;Instructions&quot;">​</a></h2><p>If you encounter a problem, you can start looking from the following aspects:</p><ol><li>Search the corresponding module&#39;s GitHub repository <a href="https://github.com/vbenjs/vue-vben-admin/issues" target="_blank" rel="noreferrer">issue</a></li><li>Search for the problem on <a href="https://www.google.com" target="_blank" rel="noreferrer">Google</a></li><li>Search for the problem on <a href="https://www.baidu.com" target="_blank" rel="noreferrer">Baidu</a></li><li>If you can&#39;t find the issue in the list below, you can ask in <a href="https://github.com/vbenjs/vue-vben-admin/issues" target="_blank" rel="noreferrer">issues</a></li><li>If it&#39;s not a problem type and needs discussion, please go to <a href="https://github.com/vbenjs/vue-vben-admin/discussions" target="_blank" rel="noreferrer">discussions</a> to discuss</li></ol><h2 id="dependency-issues" tabindex="-1">Dependency Issues <a class="header-anchor" href="#dependency-issues" aria-label="Permalink to &quot;Dependency Issues&quot;">​</a></h2><p>In a <code>Monorepo</code> project, it is necessary to develop the habit of executing <code>pnpm install</code> every time you <code>git pull</code> the code, as new dependency packages are often added. The project has already configured automatic execution of <code>pnpm install</code> in <code>.husky/git-merge</code>, but sometimes there might be issues. If it does not execute automatically, it is recommended to execute it manually once.</p><h2 id="about-cache-update-issues" tabindex="-1">About Cache Update Issues <a class="header-anchor" href="#about-cache-update-issues" aria-label="Permalink to &quot;About Cache Update Issues&quot;">​</a></h2><p>The project configuration is by default cached in <code>localStorage</code>, so some configurations may not change after a version update.</p><p>The solution is to modify the <code>version</code> number in <code>package.json</code> each time the code is updated. This is because the key for <code>localStorage</code> is based on the version number. Therefore, after an update, the configurations from a previous version will become invalid. Simply re-login to apply the new settings.</p><h2 id="about-modifying-configuration-files" tabindex="-1">About Modifying Configuration Files <a class="header-anchor" href="#about-modifying-configuration-files" aria-label="Permalink to &quot;About Modifying Configuration Files&quot;">​</a></h2><p>When modifying environment files such as <code>.env</code> or the <code>vite.config.ts</code> file, Vite will automatically restart the service.</p><p>There&#39;s a chance that automatic restarts may encounter issues. Simply rerunning the project can resolve these problems.</p><h2 id="errors-when-running-locally" tabindex="-1">Errors When Running Locally <a class="header-anchor" href="#errors-when-running-locally" aria-label="Permalink to &quot;Errors When Running Locally&quot;">​</a></h2><p>Since Vite does not transform code locally and the code uses relatively new syntax such as optional chaining, local development requires using a higher version of the browser (<code>Chrome 90+</code>).</p><h2 id="blank-page-after-switching-pages" tabindex="-1">Blank Page After Switching Pages <a class="header-anchor" href="#blank-page-after-switching-pages" aria-label="Permalink to &quot;Blank Page After Switching Pages&quot;">​</a></h2><p>This issue occurs because route switching animations are enabled, and the corresponding page component has multiple root nodes. Adding a <code>&lt;div&gt;&lt;/div&gt;</code> at the outermost layer of the page can solve this problem.</p><p><strong>Incorrect Example</strong></p><div class="language-vue vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">vue</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  &lt;!-- Annotations are also considered a node --&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;text h1&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;text h2&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><p><strong>正确示例</strong></p><div class="language-vue vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">vue</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">div</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;text h1&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    &lt;</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;text h2&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">h2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  &lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">div</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/</span><span style="--shiki-light:#22863A;--shiki-dark:#85E89D;">template</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&gt;</span></span></code></pre></div><div class="tip custom-block"><p class="custom-block-title">Tip</p><ul><li>If you want to use multiple root tags, you can disable route switching animations.</li><li>Root comment nodes under <code>template</code> are also counted as a node.</li></ul></div><h2 id="my-code-works-locally-but-not-when-packaged" tabindex="-1">My code works locally but not when packaged <a class="header-anchor" href="#my-code-works-locally-but-not-when-packaged" aria-label="Permalink to &quot;My code works locally but not when packaged&quot;">​</a></h2><p>The reason for this issue could be one of the following. You can check these reasons, and if there are other possibilities, feel free to add them:</p><ul><li>The variable <code>ctx</code> was used, which is not exposed in the instance type. The Vue official documentation also advises against using this property as it is intended for internal use only.</li></ul><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { getCurrentInstance } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;vue&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">getCurrentInstance</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">().ctx.xxxx;</span></span></code></pre></div><h2 id="dependency-installation-issues" tabindex="-1">Dependency Installation Issues <a class="header-anchor" href="#dependency-installation-issues" aria-label="Permalink to &quot;Dependency Installation Issues&quot;">​</a></h2><ul><li>If you cannot install dependencies or the startup reports an error, you can try executing <code>pnpm run reinstall</code>.</li><li>If you cannot install dependencies or encounter errors, you can try switching to a mobile hotspot for installing dependencies.</li><li>If that still doesn&#39;t work, you can configure a domestic mirror for installation.</li><li>You can also create a <code>.npmrc</code> file in the project root directory with the following content:</li></ul><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># .npmrc</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">registry</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> =</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> https://registry.npmmirror.com/</span></span></code></pre></div><h2 id="package-file-too-large" tabindex="-1">Package File Too Large <a class="header-anchor" href="#package-file-too-large" aria-label="Permalink to &quot;Package File Too Large&quot;">​</a></h2><ul><li><p>First, the full version will be larger because it includes many library files. You can use the simplified version for development.</p></li><li><p>Secondly, it is recommended to enable gzip, which can reduce the size to about 1/3 of the original. Gzip can be enabled directly by the server. If so, the frontend does not need to build <code>.gz</code> format files. If the frontend has built <code>.gz</code> files, for example, with nginx, you need to enable the <code>gzip_static: on</code> option.</p></li><li><p>While enabling gzip, you can also enable <code>brotli</code> for better compression than gzip. Both can coexist.</p></li></ul><p><strong>Note</strong></p><ul><li><p>gzip_static: This module requires additional installation in nginx, as the default nginx does not include this module.</p></li><li><p>Enabling <code>brotli</code> also requires additional nginx module installation.</p></li></ul><h2 id="runtime-errors" tabindex="-1">Runtime Errors <a class="header-anchor" href="#runtime-errors" aria-label="Permalink to &quot;Runtime Errors&quot;">​</a></h2><p>If you encounter errors similar to the following, please check that the full project path (including all parent paths) does not contain Chinese, Japanese, or Korean characters. Otherwise, you will encounter a 404 error for the path, leading to the following issue:</p><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">[vite] Failed to resolve </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">module</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> import</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;ant-design-vue/dist/antd.css-vben-adminode_modulesant-design-vuedistantd.css&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">. (</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">imported</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;"> by</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> /@/</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">setup</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">/</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">ant</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">-</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">design</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">-</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">vue</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">/</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">info</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">.</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">ts</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span></span></code></pre></div><h2 id="console-route-warning-issue" tabindex="-1">Console Route Warning Issue <a class="header-anchor" href="#console-route-warning-issue" aria-label="Permalink to &quot;Console Route Warning Issue&quot;">​</a></h2><p>If you see the following warning in the console, and the page <code>can open normally</code>, you can ignore this warning.</p><p>Future versions of <code>vue-router</code> may provide an option to disable this warning.</p><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">[Vue Router warn]: No match found for location </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">with</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> path </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;xxxx&quot;</span></span></code></pre></div><h2 id="startup-error" tabindex="-1">Startup Error <a class="header-anchor" href="#startup-error" aria-label="Permalink to &quot;Startup Error&quot;">​</a></h2><p>If you encounter the following error message, please check if your nodejs version meets the requirements.</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">TypeError:</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> str.matchAll</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> is</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> not</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> a</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> function</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">at</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Object.extractor</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> (vue-vben-admin-main</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\n</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ode_modules@purge-icons</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\c</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ore</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\d</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ist</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\i</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ndex.js:146:27)</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">at</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Extract</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> (vue-vben-admin-main</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\n</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ode_modules@purge-icons</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\c</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ore</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\d</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ist</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\i</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">ndex.js:173:54)</span></span></code></pre></div><h2 id="nginx-deployment" tabindex="-1">nginx Deployment <a class="header-anchor" href="#nginx-deployment" aria-label="Permalink to &quot;nginx Deployment&quot;">​</a></h2><p>After deploying to <code>nginx</code>，you might encounter the following error:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">Failed</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> to</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> load</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> module</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> script:</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Expected</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> a</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> JavaScript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> module</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> script</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> but</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> the</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> server</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> responded</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> with</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> a</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> MIME</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> type</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> of</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;application/octet-stream&quot;.</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Strict</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> MIME</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> type</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> checking</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> is</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> enforced</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> for</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> module</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> scripts</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> per</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> HTML</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> spec.</span></span></code></pre></div><p>Solution 1:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">http</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    #If there is such a configuration, it needs to be commented out</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    #include       mime.types;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    types</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">      application/javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> js</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> mjs</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p>Solution 2：</p><p>Open the <code>mime.types</code> file under <code>nginx</code> and change <code>application/javascript js;</code> to <code>application/javascript js mjs;</code></p>',49)),n(c),n(g)])}]]);export{o as __pageData,p as default};
