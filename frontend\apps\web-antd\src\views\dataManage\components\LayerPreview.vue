<script setup>
import { onMounted, watch } from 'vue';

import * as Cesium from 'cesium';

import { initMap } from '#/utils/cesium';

import { useRouter } from 'vue-router';

import { appInit, capp } from '#/utils/app.js';

// Props: Receive selected layers from parent (App.vue)
const props = defineProps({
  layerUrl: String,
});
const emits = defineEmits(['viewMapLoaded']);
const router = useRouter();
let viewer;
let layers;
onMounted(async () => {
  if (!capp) {
    appInit(router);
  }
  init();
});

watch(() => props.layerUrl, (newValue, oldValue) => {
  if(newValue) {
    loadLayer(newValue);
  }
});

const loadLayer = async (url) => {
  // 安全地加载3DTiles
  try {
    if(url == null || url.length === 0) {
      return;
    }
    viewer.scene.globe.show = false;
    viewer.scene.screenSpaceCameraController.enableRotate = true;
    viewer.scene.screenSpaceCameraController.enableTranslate = true;
    viewer.scene.screenSpaceCameraController.enableZoom = true;
    viewer.scene.backgroundColor = Cesium.Color.TRANSPARENT;
    //移除星空背景
    viewer.scene.sun.show = false;
    viewer.scene.moon.show = false;
    viewer.scene.skyBox.show = false;
    const tileset = new Cesium.Cesium3DTileset({
      url: url, // 替换为实际路径

      // 添加更多配置以提高兼容性
      modelMatrix: Cesium.Matrix4.IDENTITY,

      // 优化加载选项
      maximumScreenSpaceError: 16,
      maximumNumberOfLoadedTiles: 100
    });

    // 添加到场景并处理潜在错误
    viewer.scene.primitives.removeAll();
    viewer.scene.primitives.add(tileset);

    // 监听图层加载
    tileset.readyPromise && tileset.readyPromise.then(function(tileset) {
      if(!tileset) {
        return;
      }
      // 自动飞行到图层
      const boundingSphere = tileset.boundingSphere;
      // zoomTo方法会自动调整相机以完整显示模型
      viewer.zoomTo(tileset, new Cesium.HeadingPitchRange(
        0,      // heading：水平角度
        -0.5,   // pitch：俯仰角
        boundingSphere.radius * 2.0 // 距离：根据模型大小调整
      ));

      console.log('3DTiles加载成功');
    }).catch(e=>{
      console.log(e);
    })

  } catch (error) {
    console.error('加载3DTiles时发生错误', error);
  }

}


const init = async () => {
  // const viewer = new Cesium.Viewer('viewDiv', { infoBox: false });

  const map = initMap('viewDiv', async (vr) => {
    viewer = vr;
    viewer.scene.morphStart.addEventListener((e, e1, e2, e3) => {
      e._morphCancelled = true;
      console.log(e, e1, e2, e3);
    });

    if(props.layerUrl && props.layerUrl.length > 0) {
      loadLayer(props.layerUrl);
    }
    // viewer.zoomTo(circle);
    emits('viewMapLoaded', true);
  });
};
</script>

<template>
  <div id="cesiumContainer">
    <div id="viewDiv"></div>
  </div>
</template>

<style scoped>
#cesiumContainer {
  position: relative;
  width: 100%;
  height: 250px;

  #viewDiv {
    width: 100%;
    height: 100%;

    .cesium-infoBox {
      position: absolute;
      top: 100px !important;
      right: 500px !important;
      display: block;
      width: 300px !important;
      max-width: 480px;
      color: #edffff;
      background: rgb(38 38 38 / 95%);
      border: 1px solid #444;
      border-right: none;
      border-top-left-radius: 7px;
      border-bottom-left-radius: 7px;
      box-shadow: 0 0 10px 1px #000;
      transform: translate(100%, 0);
    }
  }

  :deep(.cesium-viewer) {
    width: 100%;
    height: 100%;
  }

  :deep(.cesium-viewer-cesiumWidgetContainer) {
    width: 100%;
    height: 100%;
  }

  :deep(.cesium-widget) {
    width: 100%;
    height: 100%;
  }

  :deep(canvas) {
    width: 100%;
    height: 100%;
  }
}
</style>
