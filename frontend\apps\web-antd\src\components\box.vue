<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    mask: {
      type: Boolean,
      default: false,
    },
    maskClosable: {
      type: Boolean,
      default: false,
    },
    hasHeader: {
      type: Boolean,
      default: true,
    },
    hasFooter: {
      type: Boolean,
      default: false,
    },
    moverect: {
      type: Object,
      default: {
        left: 0,
        top: 0,
        bottom: 0,
        right: 0,
      },
    },
    close: {
      type: Function,
    },
  },
  data() {
    return {
      isMinimize: false,
    };
  },
  updated() {
    console.log(this.$refs.box);
    if (this.left != undefined) {
      this.$refs.box.style.left = this.left;
      this.$refs.box.style.top = this.top;
      this.$refs.box.style.right = '';
      this.$refs.box.style.transform = '';
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.cleanTransform(this.$refs.box);
    });
  },
  methods: {
    cleanTransform(div) {
      if (div.style.transform) {
        // console.log(div.offsetLeft,div.offsetTop)
        let matrix = window.getComputedStyle(div).transform || '';
        matrix = matrix.replaceAll(/ |matrix\(|\)/gi, '').split(/\(|,/);
        div.style.transform = '';
        div.style.left = `${div.offsetLeft + Number(matrix[4])}px`;
        div.style.top = `${div.offsetTop + Number(matrix[5])}px`;
        // console.log(div.offsetLeft,div.offsetTop,matrix)
        // console.log(div.offsetLeft,div.offsetTop,div.style.transform)
      }
    },
    composedPath(e) {
      // 存在则直接return
      if (e.path) {
        return e.path;
      }
      // 不存在则遍历target节点
      let target = e.target;
      e.path = [];
      while (target.parentNode !== null) {
        e.path.push(target);
        target = target.parentNode;
      }
      // 最后补上document和window
      e.path.push(document, window);
      return e.path;
    },
    headerMouseDown(e) {
      const that = this;
      // console.log(e)
      const ev = e || window.event;
      let div = this.composedPath(e);
      div = div[1];
      let x = ev.clientX; // - div.offsetLeft;
      let y = ev.clientY; // - div.offsetTop;

      div.style.left = `${div.offsetLeft}px`;
      div.style.top = `${div.offsetTop}px`;

      this.cleanTransform(div);

      div.style.right = null;
      div.style.bottom = null;
      const w = window.innerWidth;
      const h = window.innerHeight;
      // let gl = parseInt(div.style.marginLeft || 0);
      // let gt = parseInt(div.style.marginTop || 0)
      const mm = (e) => {
        const ev = e || window.event;
        const xx = ev.clientX;
        const yy = ev.clientY;

        let l = div.offsetLeft + xx - x;
        let t = div.offsetTop + yy - y;
        l = Math.max(l, this.moverect.left);
        l = Math.min(l, w - this.moverect.right - div.offsetWidth);
        t = Math.max(t, this.moverect.top);
        t = Math.min(t, h - this.moverect.bottom - div.offsetHeight);

        that.left = div.style.left = `${l}px`;
        that.top = div.style.top = `${t}px`;

        x = xx;
        y = yy;
      };
      document.addEventListener('mousemove', mm);
      const mu = (e) => {
        document.removeEventListener('mousemove', mm);
        document.removeEventListener('mouseup', mu);
      };
      document.addEventListener('mouseup', mu);
    },
  },
};
</script>

<template>
  <view
    ref="box"
    :class="{ minimize: isMinimize }"
    class="box-container"
    style="
      position: fixed;
      display: flex;
      flex-direction: column;
      width: 320px;
      height: auto;
      z-index: 999;
    "
  >
    <view class="bg-white dark:bg-black">
      <view v-show="mask"  @click="maskClosable && close()"></view>
      <view
        v-if="hasHeader"
        ref="boxDiv"
        :title="title"
        class="headerBox"
        style="cursor: move"
        @mousedown.stop="headerMouseDown"
      >
        <slot name="header">
          <p v-show="title" class="headerText">{{ title }}</p>
          <i
            v-if="!isMinimize"
            class="headerIcon"
            style="transform: rotate(0deg)"
            title="最小化"
            @click.stop="isMinimize = !isMinimize"
            @mousedown.stop=""
            >-</i>
          <i
            v-else
            class="headerIcon"
            title="还原"
            @click.stop="isMinimize = !isMinimize"
            @mousedown.stop=""
            >-</i>
          <i
            class="headerIcon"
            title="关闭"
            @click.stop="close"
            @mousedown.stop=""
            >+</i>
        </slot>
      </view>
      <transition mode="out-in" name="zoom">
        <div v-show="!isMinimize" class="content">
          <slot></slot>
        </div>
      </transition>
      <div v-if="hasFooter" class="footer">
        <slot name="footer"></slot>
      </div>
    </view>
  </view>
</template>

<style scoped lang="less">
.box-container {
  position: absolute;
  z-index: 1000;
  &.minimize {
    max-width: 100px !important;
  }
  .mask {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -1;
  }
  .headerBox {
    width: 100%;
    height: 36px;
    line-height: 20px;
    font-size: 16px;
    font-weight: 600;
    padding: 8px;
    user-select: none;
    display: flex;
    flex-direction: row;
    justify-content: center;
    .headerText {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin: 0;
      pointer-events: none;
      flex-grow: 1;
    }
    .headerIcon {
      width: 22px;
      height: 22px;
      line-height: 20px;
      cursor: pointer;
      font-size: 30px;
      text-align: center;
      transform: rotate(-45deg);
    }
  }
  .content {
    word-wrap: break-word;
    word-break: break-all;
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
