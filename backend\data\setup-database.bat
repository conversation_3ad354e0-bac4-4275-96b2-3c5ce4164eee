@echo off
chcp 65001 >nul
title 系统管理平台 - 数据库一键安装

echo ================================================
echo        系统管理平台数据库一键安装工具
echo ================================================
echo.

REM 检查Node.js
echo 🔍 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Node.js，请先安装Node.js
    echo 💡 下载地址: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js已安装: 
node --version

echo.
echo 📦 安装数据库依赖包...
cd ..
if exist "package.json" (
    npm install
) else (
    npm install mysql2
)

if %errorlevel% neq 0 (
    echo ❌ 依赖包安装失败
    pause
    exit /b 1
)

cd data
echo ✅ 依赖包安装完成
echo.

REM 数据库配置
echo 🔧 数据库配置
echo ================
echo.
echo 请输入数据库连接信息（直接回车使用默认值）:
echo.

set /p db_host="数据库主机 [localhost]: "
if "%db_host%"=="" set db_host=localhost

set /p db_port="数据库端口 [3306]: "
if "%db_port%"=="" set db_port=3306

set /p db_user="数据库用户名 [root]: "
if "%db_user%"=="" set db_user=root

set /p db_pass="数据库密码: "

echo.
echo 📋 配置信息确认:
echo    主机: %db_host%
echo    端口: %db_port%
echo    用户: %db_user%
echo    密码: %db_pass%
echo.

set /p confirm="确认配置信息是否正确？(y/N): "
if /i not "%confirm%"=="y" (
    echo 取消安装
    pause
    exit /b 0
)

REM 设置环境变量
set DB_HOST=%db_host%
set DB_PORT=%db_port%
set DB_USERNAME=%db_user%
set DB_PASSWORD=%db_pass%

echo.
echo 🔍 测试数据库连接...
node test-db-connection.js
if %errorlevel% neq 0 (
    echo.
    echo ❌ 数据库连接测试失败，请检查配置后重试
    pause
    exit /b 1
)

echo.
echo 🚀 开始初始化数据库...
echo.

REM 首先尝试标准版本
echo 📝 尝试标准初始化方式...
node init-database.js

REM 如果标准版本失败，尝试简化版本
if %errorlevel% neq 0 (
    echo.
    echo ⚠️  标准初始化失败，尝试简化版本...
    echo.
    node init-database-simple.js
)

if %errorlevel% equ 0 (
    echo.
    echo 🎉 数据库初始化完成！
    echo.
    echo 📝 重要信息:
    echo    - 数据库名称: system_manage
    echo    - 默认管理员用户名: admin
    echo    - 默认管理员密码: admin123
    echo    - 请及时修改默认密码！
    echo.
    echo 🔗 下一步:
    echo    1. 更新后端.env文件中的数据库配置
    echo    2. 启动后端服务
    echo    3. 使用admin/admin123登录系统
    echo.
) else (
    echo.
    echo ❌ 数据库初始化失败，请查看错误信息
    echo.
)

echo 按任意键退出...
pause >nul
