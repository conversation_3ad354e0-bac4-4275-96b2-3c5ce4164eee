var g=(f,u,t)=>new Promise((d,l)=>{var c=e=>{try{n(t.next(e))}catch(s){l(s)}},h=e=>{try{n(t.throw(e))}catch(s){l(s)}},n=e=>e.done?d(e.value):Promise.resolve(e.value).then(c,h);n((t=t.apply(f,u)).next())});import{d as y,r as p,u as m,c as $,o as b,a as o,b as a,e as r,f as k,g as i,t as v,n as C,h as H,i as R}from"../jse/index-index-DyHD_jbN.js";const B={class:"relative"},S={key:0,class:"mb-2 flex text-lg font-semibold"},T={key:0,class:"text-muted-foreground"},z={key:0,class:"absolute bottom-4 right-4"},w=y({name:"<PERSON>",__name:"page",props:{title:{default:""},description:{default:""},contentClass:{default:""},autoContentHeight:{type:Boolean,default:!1}},setup(f){const u=p(0),t=p(0),d=p(!1),l=m("headerRef"),c=m("footerRef"),h=$(()=>f.autoContentHeight?{height:d.value?`calc(var(--vben-content-height) - ${u.value}px - ${t.value}px)`:"0"}:{});function n(){return g(this,null,function*(){var e,s;f.autoContentHeight&&(yield R(),u.value=((e=l.value)==null?void 0:e.offsetHeight)||0,t.value=((s=c.value)==null?void 0:s.offsetHeight)||0,setTimeout(()=>{d.value=!0},30))})}return b(()=>{n()}),(e,s)=>(a(),o("div",B,[e.description||e.$slots.description||e.title||e.$slots.title||e.$slots.extra?(a(),o("div",{key:0,ref_key:"headerRef",ref:l,class:"bg-card relative px-6 py-4"},[i(e.$slots,"title",{},()=>[e.title?(a(),o("div",S,v(e.title),1)):r("",!0)]),i(e.$slots,"description",{},()=>[e.description?(a(),o("p",T,v(e.description),1)):r("",!0)]),e.$slots.extra?(a(),o("div",z,[i(e.$slots,"extra")])):r("",!0)],512)):r("",!0),k("div",{class:H([e.contentClass,"h-full p-4"]),style:C(h.value)},[i(e.$slots,"default")],6),e.$slots.footer?(a(),o("div",{key:1,ref_key:"footerRef",ref:c,class:"bg-card align-center absolute bottom-0 left-0 right-0 flex px-6 py-4"},[i(e.$slots,"footer")],512)):r("",!0)]))}});export{w as _};
