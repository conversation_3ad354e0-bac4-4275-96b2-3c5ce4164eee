# 组织管理功能

## 功能概述

组织管理模块提供了完整的组织架构管理功能，支持树形结构的部门组织架构管理。

## 主要功能

### 1. 组织信息管理
- **机构名称**: 组织的名称
- **机构编码**: 唯一的组织编码
- **上级机构**: 支持树形层级结构
- **类别**: 部门或科室
- **排序**: 支持排序调整
- **默认角色**: 为组织设置默认角色
- **状态**: 启用/停用状态控制
- **描述**: 组织描述信息

### 2. 树形结构显示
- 支持树形结构展示组织架构
- 可展开/收起子组织
- 支持无限层级嵌套

### 3. 基本操作
- **新增组织**: 创建新的组织
- **编辑组织**: 修改组织信息
- **删除组织**: 删除组织（需确保无子组织）
- **添加子组织**: 为现有组织添加下级组织
- **搜索过滤**: 支持按名称、编码、类别、状态等条件搜索

### 4. 可复用组件
- **OrganizationSidebar.vue**: 组织筛选侧边栏组件
  - 支持树形结构显示组织架构
  - 提供搜索和筛选功能
  - 可在其他模块中复用（如岗位管理）
  - 使用vben主题系统，支持明暗主题切换

## 文件结构

```
organization/
├── index.vue                    # 主页面
├── organization.api.ts          # API接口
├── organization.data.ts         # 数据配置
├── components/
│   ├── OrganizationModal.vue   # 新增/编辑弹窗
│   └── OrganizationSidebar.vue # 组织筛选侧边栏（可复用组件）
└── README.md                   # 说明文档
```

## 使用说明

### 1. 访问路径
系统管理 -> 组织管理 (`/system/organization`)

### 2. 新增组织
1. 点击"新增组织"按钮
2. 填写组织信息
3. 选择上级机构（可选）
4. 设置类别、排序等信息
5. 点击确定保存

### 3. 编辑组织
1. 在组织列表中点击"编辑"按钮
2. 修改组织信息
3. 点击确定保存

### 4. 添加子组织
1. 在组织列表中点击"添加子组织"按钮
2. 系统会自动设置上级机构
3. 填写其他信息并保存

### 5. 删除组织
1. 在组织列表中点击"删除"按钮
2. 确认删除操作
3. 注意：有子组织的组织无法删除

## 数据验证

### 必填字段
- 机构名称
- 机构编码
- 类别

### 唯一性约束
- 机构编码必须唯一

### 业务规则
- 不能将自己设置为上级机构
- 不能将子机构设置为上级机构（防止循环引用）
- 有子机构的组织无法删除

## API接口

### 前端接口
- `getOrganizationTree()` - 获取组织树形结构
- `createOrganization()` - 创建组织
- `updateOrganization()` - 更新组织
- `deleteOrganization()` - 删除组织
- `getRoleList()` - 获取角色列表

### 后端接口
- `GET /organizations/tree` - 获取组织树形结构
- `POST /organizations` - 创建组织
- `PUT /organizations/:id` - 更新组织
- `DELETE /organizations/:id` - 删除组织
- `GET /roles/list` - 获取角色列表

## 注意事项

1. 确保后端服务正常运行
2. 需要有相应的权限才能进行增删改操作
3. 删除操作不可恢复，请谨慎操作
4. 组织编码一旦创建不建议修改，可能影响关联数据

## 扩展功能

后续可以考虑添加的功能：
- 批量导入组织数据
- 组织架构图可视化
- 组织人员统计
- 组织权限继承
- 组织历史变更记录
