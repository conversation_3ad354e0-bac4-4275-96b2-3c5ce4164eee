import { MigrationInterface, Query<PERSON>un<PERSON> } from "typeorm";

export class UserFieldsUpdate1753953278247 implements MigrationInterface {
    name = 'UserFieldsUpdate1753953278247'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`code\` ON \`organizations\``);
        await queryRunner.query(`DROP INDEX \`idx_code\` ON \`organizations\``);
        await queryRunner.query(`DROP INDEX \`idx_parent_id\` ON \`organizations\``);
        await queryRunner.query(`DROP INDEX \`idx_sort_order\` ON \`organizations\``);
        await queryRunner.query(`DROP INDEX \`idx_status\` ON \`organizations\``);
        await queryRunner.query(`DROP INDEX \`code\` ON \`positions\``);
        await queryRunner.query(`DROP INDEX \`idx_code\` ON \`positions\``);
        await queryRunner.query(`DROP INDEX \`idx_department_id\` ON \`positions\``);
        await queryRunner.query(`DROP INDEX \`idx_parent_id\` ON \`positions\``);
        await queryRunner.query(`DROP INDEX \`idx_sort_order\` ON \`positions\``);
        await queryRunner.query(`DROP INDEX \`idx_status\` ON \`positions\``);
        await queryRunner.query(`DROP INDEX \`employee_id\` ON \`users\``);
        await queryRunner.query(`DROP INDEX \`idx_department_id\` ON \`users\``);
        await queryRunner.query(`DROP INDEX \`idx_position_id\` ON \`users\``);
        await queryRunner.query(`DROP INDEX \`idx_status\` ON \`users\``);
        await queryRunner.query(`DROP INDEX \`idx_username\` ON \`users\``);
        await queryRunner.query(`DROP INDEX \`username\` ON \`users\``);
        await queryRunner.query(`DROP INDEX \`code\` ON \`roles\``);
        await queryRunner.query(`DROP INDEX \`idx_code\` ON \`roles\``);
        await queryRunner.query(`DROP INDEX \`idx_status\` ON \`roles\``);
        await queryRunner.query(`DROP INDEX \`idx_application_id\` ON \`functions\``);
        await queryRunner.query(`DROP INDEX \`idx_parent_id\` ON \`functions\``);
        await queryRunner.query(`DROP INDEX \`idx_sort_order\` ON \`functions\``);
        await queryRunner.query(`DROP INDEX \`idx_status\` ON \`functions\``);
        await queryRunner.query(`DROP INDEX \`idx_type\` ON \`functions\``);
        await queryRunner.query(`DROP INDEX \`app_key\` ON \`applications\``);
        await queryRunner.query(`DROP INDEX \`idx_app_key\` ON \`applications\``);
        await queryRunner.query(`DROP INDEX \`idx_status\` ON \`applications\``);
        await queryRunner.query(`DROP INDEX \`idx_status\` ON \`dict_types\``);
        await queryRunner.query(`DROP INDEX \`idx_type\` ON \`dict_types\``);
        await queryRunner.query(`DROP INDEX \`type\` ON \`dict_types\``);
        await queryRunner.query(`DROP INDEX \`idx_status\` ON \`user_groups\``);
        await queryRunner.query(`DROP INDEX \`idx_role_id\` ON \`user_roles\``);
        await queryRunner.query(`DROP INDEX \`idx_user_id\` ON \`user_roles\``);
        await queryRunner.query(`DROP INDEX \`uk_user_role\` ON \`user_roles\``);
        await queryRunner.query(`DROP INDEX \`idx_function_id\` ON \`role_functions\``);
        await queryRunner.query(`DROP INDEX \`idx_role_id\` ON \`role_functions\``);
        await queryRunner.query(`DROP INDEX \`uk_role_function\` ON \`role_functions\``);
        await queryRunner.query(`DROP INDEX \`idx_user_group_id\` ON \`user_group_members\``);
        await queryRunner.query(`DROP INDEX \`idx_user_id\` ON \`user_group_members\``);
        await queryRunner.query(`DROP INDEX \`uk_group_user\` ON \`user_group_members\``);
        await queryRunner.query(`DROP INDEX \`idx_role_id\` ON \`user_group_roles\``);
        await queryRunner.query(`DROP INDEX \`idx_user_group_id\` ON \`user_group_roles\``);
        await queryRunner.query(`DROP INDEX \`uk_group_role\` ON \`user_group_roles\``);
        await queryRunner.query(`CREATE TABLE \`dict_data\` (\`id\` bigint NOT NULL AUTO_INCREMENT, \`created_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6), \`updated_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`created_by\` bigint NULL COMMENT '创建人', \`updated_by\` bigint NULL COMMENT '更新人', \`dict_type\` varchar(100) NOT NULL COMMENT '字典类型', \`dict_label\` varchar(100) NOT NULL COMMENT '字典标签', \`dict_value\` varchar(100) NOT NULL COMMENT '字典键值', \`parent_id\` bigint NOT NULL COMMENT '父级ID，支持层级结构' DEFAULT '0', \`sort_order\` int NOT NULL COMMENT '排序' DEFAULT '0', \`status\` tinyint NOT NULL COMMENT '状态：1启用，0停用' DEFAULT '1', \`is_default\` tinyint NOT NULL COMMENT '是否默认：1是，0否' DEFAULT '0', \`remark\` text NULL COMMENT '备注', INDEX \`IDX_177fb3a4ab1c3a6f3675184ac4\` (\`sort_order\`), INDEX \`IDX_7e863f7db933218a6b0e94550f\` (\`status\`), INDEX \`IDX_10c16805bbf33b59353fc2ce5f\` (\`parent_id\`), INDEX \`IDX_6e5788e1a69660020df3035ae5\` (\`dict_type\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`sys_config\` (\`id\` bigint NOT NULL AUTO_INCREMENT, \`created_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6), \`updated_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`created_by\` bigint NULL COMMENT '创建人', \`updated_by\` bigint NULL COMMENT '更新人', \`config_name\` varchar(100) NOT NULL COMMENT '参数名称', \`config_key\` varchar(100) NOT NULL COMMENT '参数键名', \`config_value\` text NULL COMMENT '参数键值', \`config_type\` enum ('Y', 'N') NOT NULL COMMENT '系统内置：Y是，N否' DEFAULT 'N', \`remark\` text NULL COMMENT '备注', INDEX \`IDX_7a354be8daf75e6cfa525746d6\` (\`config_type\`), INDEX \`IDX_018d62dc9e7496d36994292299\` (\`config_key\`), UNIQUE INDEX \`IDX_018d62dc9e7496d36994292299\` (\`config_key\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` DROP COLUMN \`created_by\``);
        await queryRunner.query(`ALTER TABLE \`user_roles\` DROP COLUMN \`created_time\``);
        await queryRunner.query(`ALTER TABLE \`user_roles\` CHANGE \`id\` \`id\` bigint NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` DROP PRIMARY KEY`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` DROP COLUMN \`id\``);
        await queryRunner.query(`ALTER TABLE \`role_functions\` DROP COLUMN \`created_by\``);
        await queryRunner.query(`ALTER TABLE \`role_functions\` DROP COLUMN \`created_time\``);
        await queryRunner.query(`ALTER TABLE \`role_functions\` CHANGE \`id\` \`id\` bigint NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` DROP PRIMARY KEY`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` DROP COLUMN \`id\``);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` DROP COLUMN \`created_by\``);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` DROP COLUMN \`created_time\``);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` CHANGE \`id\` \`id\` bigint NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` DROP PRIMARY KEY`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` DROP COLUMN \`id\``);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` DROP COLUMN \`created_by\``);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` DROP COLUMN \`created_time\``);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` CHANGE \`id\` \`id\` bigint NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` DROP PRIMARY KEY`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` DROP COLUMN \`id\``);
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`gender\` varchar(10) NULL COMMENT '性别'`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`user_group_ids\` json NULL COMMENT '用户组ID列表'`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`management_level\` int NULL COMMENT '管理级别'`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`technical_level\` int NULL COMMENT '技术级别'`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` ADD PRIMARY KEY (\`user_id\`, \`role_id\`)`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` ADD PRIMARY KEY (\`role_id\`, \`function_id\`)`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` ADD PRIMARY KEY (\`user_group_id\`, \`user_id\`)`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` ADD PRIMARY KEY (\`user_group_id\`, \`role_id\`)`);
        await queryRunner.query(`ALTER TABLE \`organizations\` CHANGE \`created_time\` \`created_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`organizations\` CHANGE \`updated_time\` \`updated_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`organizations\` CHANGE \`parent_id\` \`parent_id\` bigint NOT NULL COMMENT '上级机构ID，0为根节点' DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`organizations\` CHANGE \`type\` \`type\` enum ('department', 'office') NOT NULL COMMENT '类别：department部门，office科室' DEFAULT 'department'`);
        await queryRunner.query(`ALTER TABLE \`organizations\` CHANGE \`sort_order\` \`sort_order\` int NOT NULL COMMENT '排序' DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`organizations\` CHANGE \`status\` \`status\` tinyint NOT NULL COMMENT '状态：1启用，0停用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`positions\` CHANGE \`created_time\` \`created_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`positions\` CHANGE \`updated_time\` \`updated_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`positions\` CHANGE \`parent_id\` \`parent_id\` bigint NOT NULL COMMENT '主管上级岗位ID' DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`positions\` CHANGE \`type\` \`type\` enum ('department', 'office') NOT NULL COMMENT '类别：department部门，office科室' DEFAULT 'department'`);
        await queryRunner.query(`ALTER TABLE \`positions\` CHANGE \`sort_order\` \`sort_order\` int NOT NULL COMMENT '排序' DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`positions\` CHANGE \`status\` \`status\` tinyint NOT NULL COMMENT '状态：1启用，0停用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`created_time\` \`created_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`updated_time\` \`updated_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD UNIQUE INDEX \`IDX_9760615d88ed518196bb79ea03\` (\`employee_id\`)`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD UNIQUE INDEX \`IDX_fe0bb3f6520ee0469504521e71\` (\`username\`)`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`status\` \`status\` tinyint NOT NULL COMMENT '状态：1启用，0禁用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`created_time\` \`created_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`updated_time\` \`updated_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`status\` \`status\` tinyint NOT NULL COMMENT '状态：1启用，0禁用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`functions\` CHANGE \`created_time\` \`created_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`functions\` CHANGE \`updated_time\` \`updated_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`functions\` CHANGE \`parent_id\` \`parent_id\` bigint NOT NULL COMMENT '上级功能ID，0为根节点' DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`functions\` CHANGE \`type\` \`type\` enum ('directory', 'menu', 'button') NOT NULL COMMENT '功能类型：directory目录，menu菜单，button按钮' DEFAULT 'menu'`);
        await queryRunner.query(`ALTER TABLE \`functions\` CHANGE \`sort_order\` \`sort_order\` int NOT NULL COMMENT '排序' DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`functions\` CHANGE \`status\` \`status\` tinyint NOT NULL COMMENT '状态：1启用，0停用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`applications\` CHANGE \`created_time\` \`created_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`applications\` CHANGE \`updated_time\` \`updated_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`applications\` CHANGE \`status\` \`status\` tinyint NOT NULL COMMENT '状态：1启用，0禁用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`dict_types\` CHANGE \`created_time\` \`created_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`dict_types\` CHANGE \`updated_time\` \`updated_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`dict_types\` ADD UNIQUE INDEX \`IDX_64375a3699bad63fb4203d00cd\` (\`type\`)`);
        await queryRunner.query(`ALTER TABLE \`dict_types\` CHANGE \`status\` \`status\` tinyint NOT NULL COMMENT '状态：1启用，0停用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`user_groups\` CHANGE \`created_time\` \`created_time\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`user_groups\` CHANGE \`updated_time\` \`updated_time\` datetime(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`);
        await queryRunner.query(`ALTER TABLE \`user_groups\` CHANGE \`status\` \`status\` tinyint NOT NULL COMMENT '状态：1启用，0禁用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` CHANGE \`user_id\` \`user_id\` bigint NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` CHANGE \`role_id\` \`role_id\` bigint NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` CHANGE \`role_id\` \`role_id\` bigint NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` CHANGE \`function_id\` \`function_id\` bigint NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` CHANGE \`user_group_id\` \`user_group_id\` bigint NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` CHANGE \`user_id\` \`user_id\` bigint NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` CHANGE \`user_group_id\` \`user_group_id\` bigint NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` CHANGE \`role_id\` \`role_id\` bigint NOT NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_5dcfcb058264e920fa2bb7f32e\` ON \`organizations\` (\`sort_order\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_f3770f157bd77d83ab022e92fc\` ON \`organizations\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_7e27c3b62c681fbe3e2322535f\` ON \`organizations\` (\`code\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_f3a7c9411eaa5f9cbc5363de33\` ON \`organizations\` (\`parent_id\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_7e27c3b62c681fbe3e2322535f\` ON \`organizations\` (\`code\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_c0920a61c84fb8c17842cf527b\` ON \`positions\` (\`sort_order\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_fac15950ad2aefd486b6931cbe\` ON \`positions\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_e21258bdc3692b44960c623940\` ON \`positions\` (\`code\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_e413c6578fcdae9a8fd673c5bc\` ON \`positions\` (\`department_id\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_31e5697233ecaf96a5f5b01a0f\` ON \`positions\` (\`parent_id\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_e21258bdc3692b44960c623940\` ON \`positions\` (\`code\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_3676155292d72c67cd4e090514\` ON \`users\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_8e29a9d2f1fa57ebf1a4ce1735\` ON \`users\` (\`position_id\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_0921d1972cf861d568f5271cd8\` ON \`users\` (\`department_id\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_14958a120176d4e1e8be423977\` ON \`roles\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_f6d54f95c31b73fb1bdd8e91d0\` ON \`roles\` (\`code\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_f6d54f95c31b73fb1bdd8e91d0\` ON \`roles\` (\`code\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_51bb49a3b4603a3cfa0e88edb4\` ON \`functions\` (\`sort_order\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_329445c5feb028e74ae963fbc6\` ON \`functions\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_2ae54b68555815d1e812d631bf\` ON \`functions\` (\`type\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_d4b0f31651aa947e8f1bcae4ad\` ON \`functions\` (\`parent_id\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_0014ccfd80ba5cea5c05df3733\` ON \`functions\` (\`application_id\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_8ee114cee92e995a9e75c05cfb\` ON \`applications\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_3bb0304751dae010ebe2641eb6\` ON \`applications\` (\`app_key\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_3bb0304751dae010ebe2641eb6\` ON \`applications\` (\`app_key\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_4b539333c284a32488c31cbd0e\` ON \`dict_types\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_8c76701d24d9835b018e68d392\` ON \`user_groups\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_87b8888186ca9769c960e92687\` ON \`user_roles\` (\`user_id\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_b23c65e50a758245a33ee35fda\` ON \`user_roles\` (\`role_id\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_a8ef32a3bd2ce3ccfe09be45e7\` ON \`role_functions\` (\`role_id\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_28290190d276b0b430a6b3c837\` ON \`role_functions\` (\`function_id\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_fa1e18bf09e5adf0ff5d6a6ba9\` ON \`user_group_members\` (\`user_group_id\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_32ba1d353e99d14130486cd34c\` ON \`user_group_members\` (\`user_id\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_b3250bfa310624bfe0ca2f6d79\` ON \`user_group_roles\` (\`user_group_id\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_b179ae9c469fe567fd61c5536c\` ON \`user_group_roles\` (\`role_id\`)`);
        await queryRunner.query(`ALTER TABLE \`organizations\` ADD CONSTRAINT \`FK_f3a7c9411eaa5f9cbc5363de331\` FOREIGN KEY (\`parent_id\`) REFERENCES \`organizations\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`organizations\` ADD CONSTRAINT \`FK_d6d03bec242159fd21b7a6227e4\` FOREIGN KEY (\`default_role_id\`) REFERENCES \`roles\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`positions\` ADD CONSTRAINT \`FK_31e5697233ecaf96a5f5b01a0fd\` FOREIGN KEY (\`parent_id\`) REFERENCES \`positions\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`positions\` ADD CONSTRAINT \`FK_e413c6578fcdae9a8fd673c5bc7\` FOREIGN KEY (\`department_id\`) REFERENCES \`organizations\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD CONSTRAINT \`FK_0921d1972cf861d568f5271cd85\` FOREIGN KEY (\`department_id\`) REFERENCES \`organizations\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD CONSTRAINT \`FK_8e29a9d2f1fa57ebf1a4ce17353\` FOREIGN KEY (\`position_id\`) REFERENCES \`positions\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`functions\` ADD CONSTRAINT \`FK_0014ccfd80ba5cea5c05df37331\` FOREIGN KEY (\`application_id\`) REFERENCES \`applications\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`functions\` ADD CONSTRAINT \`FK_d4b0f31651aa947e8f1bcae4adf\` FOREIGN KEY (\`parent_id\`) REFERENCES \`functions\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`dict_data\` ADD CONSTRAINT \`FK_10c16805bbf33b59353fc2ce5ff\` FOREIGN KEY (\`parent_id\`) REFERENCES \`dict_data\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` ADD CONSTRAINT \`FK_87b8888186ca9769c960e926870\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` ADD CONSTRAINT \`FK_b23c65e50a758245a33ee35fda1\` FOREIGN KEY (\`role_id\`) REFERENCES \`roles\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` ADD CONSTRAINT \`FK_a8ef32a3bd2ce3ccfe09be45e76\` FOREIGN KEY (\`role_id\`) REFERENCES \`roles\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` ADD CONSTRAINT \`FK_28290190d276b0b430a6b3c8370\` FOREIGN KEY (\`function_id\`) REFERENCES \`functions\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` ADD CONSTRAINT \`FK_fa1e18bf09e5adf0ff5d6a6ba95\` FOREIGN KEY (\`user_group_id\`) REFERENCES \`user_groups\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` ADD CONSTRAINT \`FK_32ba1d353e99d14130486cd34cf\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` ADD CONSTRAINT \`FK_b3250bfa310624bfe0ca2f6d79c\` FOREIGN KEY (\`user_group_id\`) REFERENCES \`user_groups\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` ADD CONSTRAINT \`FK_b179ae9c469fe567fd61c5536c2\` FOREIGN KEY (\`role_id\`) REFERENCES \`roles\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` DROP FOREIGN KEY \`FK_b179ae9c469fe567fd61c5536c2\``);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` DROP FOREIGN KEY \`FK_b3250bfa310624bfe0ca2f6d79c\``);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` DROP FOREIGN KEY \`FK_32ba1d353e99d14130486cd34cf\``);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` DROP FOREIGN KEY \`FK_fa1e18bf09e5adf0ff5d6a6ba95\``);
        await queryRunner.query(`ALTER TABLE \`role_functions\` DROP FOREIGN KEY \`FK_28290190d276b0b430a6b3c8370\``);
        await queryRunner.query(`ALTER TABLE \`role_functions\` DROP FOREIGN KEY \`FK_a8ef32a3bd2ce3ccfe09be45e76\``);
        await queryRunner.query(`ALTER TABLE \`user_roles\` DROP FOREIGN KEY \`FK_b23c65e50a758245a33ee35fda1\``);
        await queryRunner.query(`ALTER TABLE \`user_roles\` DROP FOREIGN KEY \`FK_87b8888186ca9769c960e926870\``);
        await queryRunner.query(`ALTER TABLE \`dict_data\` DROP FOREIGN KEY \`FK_10c16805bbf33b59353fc2ce5ff\``);
        await queryRunner.query(`ALTER TABLE \`functions\` DROP FOREIGN KEY \`FK_d4b0f31651aa947e8f1bcae4adf\``);
        await queryRunner.query(`ALTER TABLE \`functions\` DROP FOREIGN KEY \`FK_0014ccfd80ba5cea5c05df37331\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP FOREIGN KEY \`FK_8e29a9d2f1fa57ebf1a4ce17353\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP FOREIGN KEY \`FK_0921d1972cf861d568f5271cd85\``);
        await queryRunner.query(`ALTER TABLE \`positions\` DROP FOREIGN KEY \`FK_e413c6578fcdae9a8fd673c5bc7\``);
        await queryRunner.query(`ALTER TABLE \`positions\` DROP FOREIGN KEY \`FK_31e5697233ecaf96a5f5b01a0fd\``);
        await queryRunner.query(`ALTER TABLE \`organizations\` DROP FOREIGN KEY \`FK_d6d03bec242159fd21b7a6227e4\``);
        await queryRunner.query(`ALTER TABLE \`organizations\` DROP FOREIGN KEY \`FK_f3a7c9411eaa5f9cbc5363de331\``);
        await queryRunner.query(`DROP INDEX \`IDX_b179ae9c469fe567fd61c5536c\` ON \`user_group_roles\``);
        await queryRunner.query(`DROP INDEX \`IDX_b3250bfa310624bfe0ca2f6d79\` ON \`user_group_roles\``);
        await queryRunner.query(`DROP INDEX \`IDX_32ba1d353e99d14130486cd34c\` ON \`user_group_members\``);
        await queryRunner.query(`DROP INDEX \`IDX_fa1e18bf09e5adf0ff5d6a6ba9\` ON \`user_group_members\``);
        await queryRunner.query(`DROP INDEX \`IDX_28290190d276b0b430a6b3c837\` ON \`role_functions\``);
        await queryRunner.query(`DROP INDEX \`IDX_a8ef32a3bd2ce3ccfe09be45e7\` ON \`role_functions\``);
        await queryRunner.query(`DROP INDEX \`IDX_b23c65e50a758245a33ee35fda\` ON \`user_roles\``);
        await queryRunner.query(`DROP INDEX \`IDX_87b8888186ca9769c960e92687\` ON \`user_roles\``);
        await queryRunner.query(`DROP INDEX \`IDX_8c76701d24d9835b018e68d392\` ON \`user_groups\``);
        await queryRunner.query(`DROP INDEX \`IDX_4b539333c284a32488c31cbd0e\` ON \`dict_types\``);
        await queryRunner.query(`DROP INDEX \`IDX_3bb0304751dae010ebe2641eb6\` ON \`applications\``);
        await queryRunner.query(`DROP INDEX \`IDX_3bb0304751dae010ebe2641eb6\` ON \`applications\``);
        await queryRunner.query(`DROP INDEX \`IDX_8ee114cee92e995a9e75c05cfb\` ON \`applications\``);
        await queryRunner.query(`DROP INDEX \`IDX_0014ccfd80ba5cea5c05df3733\` ON \`functions\``);
        await queryRunner.query(`DROP INDEX \`IDX_d4b0f31651aa947e8f1bcae4ad\` ON \`functions\``);
        await queryRunner.query(`DROP INDEX \`IDX_2ae54b68555815d1e812d631bf\` ON \`functions\``);
        await queryRunner.query(`DROP INDEX \`IDX_329445c5feb028e74ae963fbc6\` ON \`functions\``);
        await queryRunner.query(`DROP INDEX \`IDX_51bb49a3b4603a3cfa0e88edb4\` ON \`functions\``);
        await queryRunner.query(`DROP INDEX \`IDX_f6d54f95c31b73fb1bdd8e91d0\` ON \`roles\``);
        await queryRunner.query(`DROP INDEX \`IDX_f6d54f95c31b73fb1bdd8e91d0\` ON \`roles\``);
        await queryRunner.query(`DROP INDEX \`IDX_14958a120176d4e1e8be423977\` ON \`roles\``);
        await queryRunner.query(`DROP INDEX \`IDX_0921d1972cf861d568f5271cd8\` ON \`users\``);
        await queryRunner.query(`DROP INDEX \`IDX_8e29a9d2f1fa57ebf1a4ce1735\` ON \`users\``);
        await queryRunner.query(`DROP INDEX \`IDX_3676155292d72c67cd4e090514\` ON \`users\``);
        await queryRunner.query(`DROP INDEX \`IDX_e21258bdc3692b44960c623940\` ON \`positions\``);
        await queryRunner.query(`DROP INDEX \`IDX_31e5697233ecaf96a5f5b01a0f\` ON \`positions\``);
        await queryRunner.query(`DROP INDEX \`IDX_e413c6578fcdae9a8fd673c5bc\` ON \`positions\``);
        await queryRunner.query(`DROP INDEX \`IDX_e21258bdc3692b44960c623940\` ON \`positions\``);
        await queryRunner.query(`DROP INDEX \`IDX_fac15950ad2aefd486b6931cbe\` ON \`positions\``);
        await queryRunner.query(`DROP INDEX \`IDX_c0920a61c84fb8c17842cf527b\` ON \`positions\``);
        await queryRunner.query(`DROP INDEX \`IDX_7e27c3b62c681fbe3e2322535f\` ON \`organizations\``);
        await queryRunner.query(`DROP INDEX \`IDX_f3a7c9411eaa5f9cbc5363de33\` ON \`organizations\``);
        await queryRunner.query(`DROP INDEX \`IDX_7e27c3b62c681fbe3e2322535f\` ON \`organizations\``);
        await queryRunner.query(`DROP INDEX \`IDX_f3770f157bd77d83ab022e92fc\` ON \`organizations\``);
        await queryRunner.query(`DROP INDEX \`IDX_5dcfcb058264e920fa2bb7f32e\` ON \`organizations\``);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` CHANGE \`role_id\` \`role_id\` bigint NOT NULL COMMENT '角色ID'`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` CHANGE \`user_group_id\` \`user_group_id\` bigint NOT NULL COMMENT '用户组ID'`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` CHANGE \`user_id\` \`user_id\` bigint NOT NULL COMMENT '用户ID'`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` CHANGE \`user_group_id\` \`user_group_id\` bigint NOT NULL COMMENT '用户组ID'`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` CHANGE \`function_id\` \`function_id\` bigint NOT NULL COMMENT '功能ID'`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` CHANGE \`role_id\` \`role_id\` bigint NOT NULL COMMENT '角色ID'`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` CHANGE \`role_id\` \`role_id\` bigint NOT NULL COMMENT '角色ID'`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` CHANGE \`user_id\` \`user_id\` bigint NOT NULL COMMENT '用户ID'`);
        await queryRunner.query(`ALTER TABLE \`user_groups\` CHANGE \`status\` \`status\` tinyint NULL COMMENT '状态：1启用，0禁用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`user_groups\` CHANGE \`updated_time\` \`updated_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`user_groups\` CHANGE \`created_time\` \`created_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`dict_types\` CHANGE \`status\` \`status\` tinyint NULL COMMENT '状态：1启用，0停用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`dict_types\` DROP INDEX \`IDX_64375a3699bad63fb4203d00cd\``);
        await queryRunner.query(`ALTER TABLE \`dict_types\` CHANGE \`updated_time\` \`updated_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`dict_types\` CHANGE \`created_time\` \`created_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`applications\` CHANGE \`status\` \`status\` tinyint NULL COMMENT '状态：1启用，0禁用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`applications\` CHANGE \`updated_time\` \`updated_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`applications\` CHANGE \`created_time\` \`created_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`functions\` CHANGE \`status\` \`status\` tinyint NULL COMMENT '状态：1启用，0停用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`functions\` CHANGE \`sort_order\` \`sort_order\` int NULL COMMENT '排序' DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`functions\` CHANGE \`type\` \`type\` enum ('directory', 'menu', 'button') NULL COMMENT '功能类型：directory目录，menu菜单，button按钮' DEFAULT 'menu'`);
        await queryRunner.query(`ALTER TABLE \`functions\` CHANGE \`parent_id\` \`parent_id\` bigint NULL COMMENT '上级功能ID，0为根节点' DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`functions\` CHANGE \`updated_time\` \`updated_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`functions\` CHANGE \`created_time\` \`created_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`status\` \`status\` tinyint NULL COMMENT '状态：1启用，0禁用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`updated_time\` \`updated_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`created_time\` \`created_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`status\` \`status\` tinyint NULL COMMENT '状态：1启用，0禁用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`users\` DROP INDEX \`IDX_fe0bb3f6520ee0469504521e71\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP INDEX \`IDX_9760615d88ed518196bb79ea03\``);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`updated_time\` \`updated_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`created_time\` \`created_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`positions\` CHANGE \`status\` \`status\` tinyint NULL COMMENT '状态：1启用，0停用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`positions\` CHANGE \`sort_order\` \`sort_order\` int NULL COMMENT '排序' DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`positions\` CHANGE \`type\` \`type\` enum ('department', 'office') NULL COMMENT '类别：department部门，office科室' DEFAULT 'department'`);
        await queryRunner.query(`ALTER TABLE \`positions\` CHANGE \`parent_id\` \`parent_id\` bigint NULL COMMENT '主管上级岗位ID' DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`positions\` CHANGE \`updated_time\` \`updated_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`positions\` CHANGE \`created_time\` \`created_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`organizations\` CHANGE \`status\` \`status\` tinyint NULL COMMENT '状态：1启用，0停用' DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE \`organizations\` CHANGE \`sort_order\` \`sort_order\` int NULL COMMENT '排序' DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`organizations\` CHANGE \`type\` \`type\` enum ('department', 'office') NULL COMMENT '类别：department部门，office科室' DEFAULT 'department'`);
        await queryRunner.query(`ALTER TABLE \`organizations\` CHANGE \`parent_id\` \`parent_id\` bigint NULL COMMENT '上级机构ID，0为根节点' DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`organizations\` CHANGE \`updated_time\` \`updated_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`organizations\` CHANGE \`created_time\` \`created_time\` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` DROP PRIMARY KEY`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` DROP PRIMARY KEY`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` DROP PRIMARY KEY`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` DROP PRIMARY KEY`);
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`technical_level\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`management_level\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`user_group_ids\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`gender\``);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` ADD \`id\` bigint NOT NULL AUTO_INCREMENT`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` ADD PRIMARY KEY (\`id\`)`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` CHANGE \`id\` \`id\` bigint NOT NULL AUTO_INCREMENT`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` ADD \`created_time\` datetime NULL DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`user_group_roles\` ADD \`created_by\` bigint NULL COMMENT '创建人'`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` ADD \`id\` bigint NOT NULL AUTO_INCREMENT`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` ADD PRIMARY KEY (\`id\`)`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` CHANGE \`id\` \`id\` bigint NOT NULL AUTO_INCREMENT`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` ADD \`created_time\` datetime NULL DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`user_group_members\` ADD \`created_by\` bigint NULL COMMENT '创建人'`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` ADD \`id\` bigint NOT NULL AUTO_INCREMENT`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` ADD PRIMARY KEY (\`id\`)`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` CHANGE \`id\` \`id\` bigint NOT NULL AUTO_INCREMENT`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` ADD \`created_time\` datetime NULL DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`role_functions\` ADD \`created_by\` bigint NULL COMMENT '创建人'`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` ADD \`id\` bigint NOT NULL AUTO_INCREMENT`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` ADD PRIMARY KEY (\`id\`)`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` CHANGE \`id\` \`id\` bigint NOT NULL AUTO_INCREMENT`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` ADD \`created_time\` datetime NULL DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE \`user_roles\` ADD \`created_by\` bigint NULL COMMENT '创建人'`);
        await queryRunner.query(`DROP INDEX \`IDX_018d62dc9e7496d36994292299\` ON \`sys_config\``);
        await queryRunner.query(`DROP INDEX \`IDX_018d62dc9e7496d36994292299\` ON \`sys_config\``);
        await queryRunner.query(`DROP INDEX \`IDX_7a354be8daf75e6cfa525746d6\` ON \`sys_config\``);
        await queryRunner.query(`DROP TABLE \`sys_config\``);
        await queryRunner.query(`DROP INDEX \`IDX_6e5788e1a69660020df3035ae5\` ON \`dict_data\``);
        await queryRunner.query(`DROP INDEX \`IDX_10c16805bbf33b59353fc2ce5f\` ON \`dict_data\``);
        await queryRunner.query(`DROP INDEX \`IDX_7e863f7db933218a6b0e94550f\` ON \`dict_data\``);
        await queryRunner.query(`DROP INDEX \`IDX_177fb3a4ab1c3a6f3675184ac4\` ON \`dict_data\``);
        await queryRunner.query(`DROP TABLE \`dict_data\``);
        await queryRunner.query(`CREATE UNIQUE INDEX \`uk_group_role\` ON \`user_group_roles\` (\`user_group_id\`, \`role_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_user_group_id\` ON \`user_group_roles\` (\`user_group_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_role_id\` ON \`user_group_roles\` (\`role_id\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`uk_group_user\` ON \`user_group_members\` (\`user_group_id\`, \`user_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_user_id\` ON \`user_group_members\` (\`user_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_user_group_id\` ON \`user_group_members\` (\`user_group_id\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`uk_role_function\` ON \`role_functions\` (\`role_id\`, \`function_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_role_id\` ON \`role_functions\` (\`role_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_function_id\` ON \`role_functions\` (\`function_id\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`uk_user_role\` ON \`user_roles\` (\`user_id\`, \`role_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_user_id\` ON \`user_roles\` (\`user_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_role_id\` ON \`user_roles\` (\`role_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_status\` ON \`user_groups\` (\`status\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`type\` ON \`dict_types\` (\`type\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_type\` ON \`dict_types\` (\`type\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_status\` ON \`dict_types\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_status\` ON \`applications\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_app_key\` ON \`applications\` (\`app_key\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`app_key\` ON \`applications\` (\`app_key\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_type\` ON \`functions\` (\`type\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_status\` ON \`functions\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_sort_order\` ON \`functions\` (\`sort_order\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_parent_id\` ON \`functions\` (\`parent_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_application_id\` ON \`functions\` (\`application_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_status\` ON \`roles\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_code\` ON \`roles\` (\`code\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`code\` ON \`roles\` (\`code\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`username\` ON \`users\` (\`username\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_username\` ON \`users\` (\`username\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_status\` ON \`users\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_position_id\` ON \`users\` (\`position_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_department_id\` ON \`users\` (\`department_id\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`employee_id\` ON \`users\` (\`employee_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_status\` ON \`positions\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_sort_order\` ON \`positions\` (\`sort_order\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_parent_id\` ON \`positions\` (\`parent_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_department_id\` ON \`positions\` (\`department_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_code\` ON \`positions\` (\`code\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`code\` ON \`positions\` (\`code\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_status\` ON \`organizations\` (\`status\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_sort_order\` ON \`organizations\` (\`sort_order\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_parent_id\` ON \`organizations\` (\`parent_id\`)`);
        await queryRunner.query(`CREATE INDEX \`idx_code\` ON \`organizations\` (\`code\`)`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`code\` ON \`organizations\` (\`code\`)`);
    }

}
