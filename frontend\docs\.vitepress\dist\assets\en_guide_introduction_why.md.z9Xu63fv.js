import{ao as e,k as o,z as a,I as r,l as t,ay as n,j as i}from"./chunks/framework.C8U7mBUf.js";const s=JSON.parse('{"title":"Why Choose Us?","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/introduction/why.md","filePath":"en/guide/introduction/why.md"}');const d=e({name:"en/guide/introduction/why.md"},[["render",function(e,s,d,l,h,u){const m=n("NolebaseGitContributors"),c=n("NolebaseGitChangelog");return i(),o("div",null,[s[0]||(s[0]=a("h1",{id:"why-choose-us",tabindex:"-1"},[r("Why Choose Us? "),a("a",{class:"header-anchor",href:"#why-choose-us","aria-label":'Permalink to "Why Choose Us?"'},"​")],-1)),s[1]||(s[1]=a("p",null,"First of all, we do not compare ourselves with other frameworks. We believe that every framework has its own characteristics and is suited for different scenarios. Our goal is to provide a simple and easy-to-use framework that allows developers to get started quickly and focus on developing business logic. Therefore, we will continue to improve and optimize our framework to offer a better experience.",-1)),s[2]||(s[2]=a("h2",{id:"framework-history",tabindex:"-1"},[r("Framework History "),a("a",{class:"header-anchor",href:"#framework-history","aria-label":'Permalink to "Framework History"'},"​")],-1)),s[3]||(s[3]=a("p",null,[r("Starting from Vue Vben Admin version 1.x, the framework has undergone numerous iterations and optimizations. From initially using "),a("code",null,"Vite 0.x"),r(" when there were no ready-made plugins available, we developed many custom plugins to bridge the gap between Webpack and Vite. Although many of these have since been replaced, our original intention has remained the same: to provide a simple and easy-to-use framework.")],-1)),s[4]||(s[4]=a("p",null,"Although the community maintained the project for a period, we have always closely monitored the development of Vue Vben Admin. We have witnessed many developers use Vben Admin and provide valuable suggestions and feedback. We are very grateful for everyone's support and contributions, which continue to drive us to improve Vben Admin. In the new version, we have continuously collected user feedback, started anew, and optimized the framework to provide a better user experience. Our goal is to enable developers to get started quickly and focus on developing business logic.",-1)),t(m),t(c)])}]]);export{s as __pageData,d as default};
