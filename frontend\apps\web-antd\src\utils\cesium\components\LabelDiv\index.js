/**
 * @descripion:
 * @param {Viewer} viewer
 * @param {Cartesian2} position
 * @param {String} title
 * @param {String} id
 * @return {*}
 */
import * as Cesium from 'cesium';
import { h, render } from 'vue';
import Label from './label.vue';

export default class LabelDiv {
  constructor(val) {
    this.viewer = val.viewer;
    this.position = val.position;
    this.vmInstance = h(Label, {
      style: val.style || '',
      html: val.html || '',
      text: val.text || '',
      toolbox: val.toolbox || null,
    });
    let mountNode = document.createElement('div');
    //render函数的作用就是将Notice组件的虚拟DOM转换成真实DOM并插入到mountNode元素里
    render(this.vmInstance, mountNode);
    //render(this.vmInstance, val.viewer.cesiumWidget.container);
    val.viewer.cesiumWidget.container.appendChild(this.vmInstance.el); //将字符串模板生成的内容添加到DOM上
    //this.vmInstance.$el = this.vmInstance.el;
    if (val.el) {
      let content = this.vmInstance.el.getElementsByClassName(
        'divlabel-container-content',
      );
      console.log(content);
      content && content.length && content[0].appendChild(val.el);
    }
    this.addPostRender();
  }

  //添加场景事件
  addPostRender() {
    this.viewer.scene.postRender.addEventListener(this.postRender, this);
  }

  //场景渲染事件 实时更新窗口的位置 使其与笛卡尔坐标一致
  postRender() {
    if (!this.vmInstance.el || !this.vmInstance.el.style) return;
    const canvasHeight = this.viewer.scene.canvas.height;
    const windowPosition = new Cesium.Cartesian2();
    Cesium.SceneTransforms.wgs84ToWindowCoordinates(
      this.viewer.scene,
      this.position,
      windowPosition,
    );
    this.vmInstance.el.style.bottom = canvasHeight - windowPosition.y + 'px';
    const elWidth = this.vmInstance.el.offsetWidth;
    this.vmInstance.el.style.left = windowPosition.x + 'px';
    if (this.viewer.scene.mode == Cesium.SceneMode.SCENE3D) {
      const camerPosition = this.viewer.camera.position;
      let height =
        this.viewer.scene.globe.ellipsoid.cartesianToCartographic(
          camerPosition,
        ).height;
      height += this.viewer.scene.globe.ellipsoid.maximumRadius;
      //console.log(camerPosition, this.position,this.viewer.camera.positionCartographic.height,height)
      if (
        !(Cesium.Cartesian3.distance(camerPosition, this.position) > height) &&
        this.viewer.camera.positionCartographic.height < 50000000
      ) {
        this.vmInstance.el.style.display = 'block';
      } else {
        this.vmInstance.el.style.display = 'none';
      }
    } else {
      if (this.viewer.camera.positionCartographic.height < 50000000) {
        this.vmInstance.el.style.display = 'block';
      } else {
        this.vmInstance.el.style.display = 'none';
      }
    }
  }
  destroy() {
    this.viewer.scene.postRender.removeEventListener(this.postRender, this);
    console.log('destroy');
    this.viewer.cesiumWidget.container.removeChild(this.vmInstance.el);
  }
}

/*
import LabelDiv from '#/scripts/LabelDiv' //根据自己的目录引入JS类文件

let val = {
	viewer:viewer,//map 的viewer。根据实际项目填写
	position:[Number(res.data.center_point.lng), Number(res.data.center_point.lat)],//经纬度，根据实际项目填写
	height:0,
	title:res.data.center_text,
	id:'210201025'+Number(res.data.center_point.lng)//ID 必须唯一
}
new LabelDiv(val) */
