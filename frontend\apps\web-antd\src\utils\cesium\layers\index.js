import { h } from 'vue';

import axios from 'axios';
import * as Cesium from 'cesium';

import Box from '../../../components/JS/box';
import PropertiesBox from '../../../components/propertiesbox.vue';
import { deepEquals, deepMerge, list2Tree } from '../../../utils/utils';
import eb, { eventbus } from '../../../ztu/eventbus';
import { capp, LayerInfo } from "../../app";
import LayerDefaultOptions from '../config/LayerDefaultOptions';
import LayerStyle from '../config/LayerStyle';
import core from '../core';
import { GISMap } from '../index';

export class layerService extends eventbus {
  constructor(viewer) {
    super();
    this.viewer = viewer || GISMap.viewer;
    const that = this;
    /**
		 * 图层数组
		 * {
			 type:'geoserver:wfs',
			 key:'test:layer',
			 title:'道路',
			 isLoaded:false,
			 show:false,
			 showIndex:0,
			 refLayer:layer,
			 refData:{}
		 }
		 */
    this._allowPicking = true;
    this.infoBox = null;
    this.layers = [];
    this.highLightLayer = null;
    this.data = [];
    this.dataCache = {};
    this._select = {
      feature: null,
      material: null,
    };
    this._hover = {
      feature: null,
      material: null,
    };
    this.geoserver = {
      /*
			options:{
				url:'http://localhost:9999/geoserver/wms',
				layers:'test:layer'
			}
			*/
      loadWMS(options, index) {
        options = deepMerge(LayerDefaultOptions.geoserver.wms, options);
        options.layers = 'wuyishan:bbbababaaa';
        // console.log(LayerDefaultOptions.geoserver.wms, options)
        const layer = new Cesium.WebMapServiceImageryProvider(options);
        return that.viewer.scene.imageryLayers.addImageryProvider(
          layer,
          Math.min(index, that.viewer.scene.imageryLayers.length),
        );
      },
      /*
			options:{
				url:'http://localhost:9999/geoserver/gwc/service/wmts/rest/test:layer/{style}/{TileMatrixSetID}/{TileMatrixSetID}:{TileMatrix}/{TileRow}/{TileColumn}?format:image/png',
				layer:'test:layer'
			}
			*/
      loadWMTS(options, index) {
        console.log(options);
        options = deepMerge(LayerDefaultOptions.geoserver.wmts, options);
        const layer = new Cesium.WebMapTileServiceImageryProvider(options);
        const addLayer = that.viewer.scene.imageryLayers.addImageryProvider(
          layer,
          Math.min(index, that.viewer.scene.imageryLayers.length),
        );
        // 将天地图图层提升到最上层
        // that.viewer.imageryLayers.lowerToBottom(addLayer);
        return addLayer;
      },
      /*
			url:'http://localhost:9999/geoserver/gwc/service/tms/1.0.0/test:layer@EPSG：900913@png/{z} /{x}/{reverseY}
			*/
      loadTMS(options, index) {
        options = deepMerge(LayerDefaultOptions.geoserver.tms, options);
        const layer = new Cesium.UrlTemplateImageryProvider(options);
        return that.viewer.scene.imageryLayers.addImageryProvider(
          layer,
          Math.min(index, that.viewer.scene.imageryLayers.length),
        );
      },
      /*
			url:'http://fzzt.fzjhdn.com:18086/geoserver/anxi_xiaoban/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=anxi_xiaoban%3Aanxilinban&maxFeatures=50&outputFormat=application%2Fjson
			*/
      loadGeoJson(options) {
        const viewer = that.viewer;
        return new Promise((resolve, reject) => {
          options =
            (typeof options === 'string' && {
              url: options,
            }) ||
            options;
          options = deepMerge(LayerDefaultOptions.geoserver.geojson, options);
          Cesium.GeoJsonDataSource.load(
            options.url,
            options.options || {},
          ).then((dataSource) => {
            const es = dataSource.entities.values.map((entity) => {
              return core.json2Entity(core.entity2Json(entity));
            });
            dataSource.entities.removeAll();
            es.forEach((entity) => {
              // entity.polygon.classificationType = Cesium.ClassificationType.CESIUM_3D_TILE;
              dataSource.entities.add(entity);
            });
            viewer.dataSources.add(dataSource);
            resolve(dataSource);
            that.setLayerStyle(dataSource, options.style);
            // viewer.zoomTo(dataSource);
          });
        });
      },
      loadKML() {
        const viewer = that.viewer;
        return new Promise((resolve, reject) => {
          options =
            (typeof options === 'string' && {
              url: options,
            }) ||
            options;
          options = deepMerge(LayerDefaultOptions.geoserver.kml, options);
          Cesium.KmlDataSource.load(options.url, options.options || {}).then(
            (kmlData) => {
              viewer.dataSources.add(kmlData);
              resolve(kmlData);
              that.setLayerStyle(kmlData, options.style);
              // viewer.zoomTo(kmlData);
              /* let entities = kmlData.entities.values;
						for (let entity of entities) {
							entity.polyline.show = true;
							entity.polyline.material = new Cesium.PolylineGlowMaterialProperty({
								glowPower:0.5,
								color:Cesium.Color.BLUE
							})
						} */
            },
          );
        });
      },
    };
    this.arcgisserver = {
      async checkToken(data) {
        const cap = await axios.get(
          `${data.url.trim('/')}/${data.layers}?f=json${data.token ? `&token=${data.token}` : ''}`,
        );
        return cap && cap.data.error == undefined;
      },
      loadWMS(options, index) {
        options = deepMerge(LayerDefaultOptions.arcgis.mapserver, options);
        // console.log(LayerDefaultOptions.arcgis.wms, options)
        // if(options.token) options.token = options.token+'11'
        const layer = new Cesium.ArcGisMapServerImageryProvider(options);
        /* layer.errorEvent.addEventListener((err)=>{
					//console.log(err)
				}) */
        // 验证token是否有效

        return that.viewer.scene.imageryLayers.addImageryProvider(
          layer,
          Math.min(index, that.viewer.scene.imageryLayers.length),
        );
      },
      loadGeoJson(options, index) {
        options = deepMerge(LayerDefaultOptions.arcgis.geojson, options);
        // console.log(LayerDefaultOptions.arcgis.wms, options)
        options.url = `${options.url.trim(
          '/',
        )}/{layers}/query?where=1%3D1&inSR=4326&spatialRel=esriSpatialRelIntersects&outFields=*&returnGeometry=true&returnTrueCurves=false&outSR=4326&returnIdsOnly=false&returnCountOnly=false&returnZ=false&returnM=false&returnDistinctValues=false&returnExtentsOnly=false&f=geojson{token}`;
        options.url = options.url.replace(
          '{token}',
          options.token ? `&token=${encodeURIComponent(options.token)}` : '',
        );
        options.url = options.url.replace('{layers}', options.layers);
        return that.geoserver.loadGeoJson(options);
      },
    };
    this.init();
  }
  getData() {
    return this.data;
  }
  getLayer(key) {
    if (!key) return null;
    if (typeof key === 'string') {
      return this.layers.find((a) => a.key == key);
    }
    return key;
  }
  getLayerProperty(key) {
    const layer = this.getLayer(key);
    if (layer && layer.refLayer instanceof Cesium.ImageryLayer) {
      return {
        alpha: layer.refLayer.alpha,
        brightness: layer.refLayer.brightness,
        contrast: layer.refLayer.contrast,
        hue: layer.refLayer.hue,
        saturation: layer.refLayer.saturation,
      };
    } else if (layer && layer.refLayer instanceof Cesium.Cesium3DTileset) {
      if (
        layer.refLayer.style &&
        layer.refLayer.style.style &&
        layer.refLayer.style.style.color
      ) {
        const data = layer.refLayer.style.style.color
          .replaceAll(/[ a-z'()]/gi, '')
          .split(',');
        const brightness = layer.refLayer.style.brightness;
        return {
          alpha: Number.parseFloat(data[3]),
          brightness: layer.refLayer.style.brightness,
          red: layer.refLayer.style.red,
          green: layer.refLayer.style.green,
          blue: layer.refLayer.style.blue,
        };
      }
      return {
        alpha: 1,
        brightness: 1,
        red: 255,
        green: 255,
        blue: 255,
      };
    }
  }
  getSplitLayers() {
    return this.layers
      .filter(
        (layer) =>
          layer.refLayer instanceof Cesium.ImageryLayer ||
          layer.refLayer instanceof Cesium.Cesium3DTileset,
      )
      .map((a) => {
        return {
          key: a.key,
          title: a.title,
        };
      });
  }
  getTreeData() {
    return list2Tree(this.data);
  }
  hide(key) {
    const layer = this.getLayer(key);
    if (!layer || !layer.show) return;
    if (layer.type == 'terrain') {
      this.unloadTerrain();
      layer.refLayer = false;
    } else layer.refLayer.show = false;
    layer.show = false;
    this.emit('showChanged', this.showKeys);
  }
  hideAll() {
    this.layers
      .filter((a) => a.show && a.type)
      .forEach((layer) => {
        this.hide(layer);
      });
  }
  init() {
    const handler = new Cesium.ScreenSpaceEventHandler(
      this.viewer.scene.canvas,
    );
    handler.setInputAction((movement) => {
      const result = this.pick(movement.position);
      result
        .then((res) => {
          if (res && res.properties) {
            // console.log(res.properties)
            this.emit('pick', res);
          }
        })
        .catch((error) => {
          // //console.log(err)
        });
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    this.on('pick', (res) => {
      this.showInfoBox(res);
    });
    eb.on('draw', () => {
      console.log('draw');
      this.allowPicking = false;
    });
    eb.on('drawEnd', () => {
      console.log('drawEnd');
      this.allowPicking = true;
    });
  }
  isSelected(feature) {
    if (feature && this._select.feature && feature === this._select.feature)
      return true;
    return false;
  }
  load3DTiles(options, index) {
    // options.url = options.url + '/'+ options.signName +'/'+ options.layers
    options = deepMerge(LayerDefaultOptions['3dtiles'], options);
    const layer = new Cesium.Cesium3DTileset(options);
    return this.viewer.scene.primitives.add(layer);
  }

  loadBingMap(options, index) {
    options = deepMerge(LayerDefaultOptions['bing'], options);
    let layer = new Cesium.BingMapsImageryProvider(options);
    return this.viewer.scene.imageryLayers.addImageryProvider(
      layer,
      Math.min(index, this.viewer.scene.imageryLayers.length),
    );
  }

  async  loadTerrain(options, index) {
    if (options.url)
      options.provider = options.provider || 'CesiumTerrainProvider';
    options = deepMerge(LayerDefaultOptions.terrain, options);
    // console.log(options)
    if (options.provider) {
      this.viewer.terrainProvider = new Cesium[options.provider](options);
    } else {
      this.viewer.terrainProvider = Cesium.createWorldTerrain(options);
    }
    this.viewer.scene.globe.depthTestAgainstTerrain = true; // 是否检查深度
    return true;
  }

  loadData(data) {
    if (this.data === data) return;
    this.data = data;
    this.reload();
    this.layers = [];
    this.loadLayer(data);
  }
  async loadHighLightLayer(layer) {
    this.unloadHighLightLayer();
    this.highLightLayer = layer;
    await this.showAndZoomToLayer(layer);
    if (layer && layer.refLayer && layer.refLayer.brightness != undefined) {
      // layer.refLayer.brightness = 1.5;
    }
    if (layer) {
      this.hide(layer.key);
    }
  }
  /**
	 * 载入图层配置
	 * @param {object | Array} options
	 * @example loadLayer([{
		 type:'geoserver:wfs',
		 key:'test:layer',
		 title:'道路',
		 data:{
		 	url:''
		 	style:''
		 }
	 }])
	 */
  loadLayer(options, index) {
    if (Array.isArray(options)) {
      options.forEach((o, index) => {
        if (o.type) this.loadLayer(o, index);
      });
      return;
    }

    // let r = options.data && options.data.rectangle;
    // r && (options.data.rectangle = Cesium.Rectangle.fromDegrees(r[0], r[1], r[2], r[3]));

    /* this.layers.push({
			type: options.type ? options.type.toLowerCase() : null,
			key: options.key,
			title: options.title,
			fullTitle:options.fullTitle,
			show: false,
			showIndex: index,
			refLayer: null,
			refData: options.data
		}); */
    let layer = capp.layers[options.key];
    if (layer) {
      layer = layer.clone();
      layer.showIndex = index;
      this.layers.push(layer);
    }else {
      let layerInfo =  new LayerInfo(options);
      layerInfo.showIndex = this.layers.length;
      this.layers.push(layerInfo);
    }
  }

  pick(px) {
    // console.log(px,this._allowPicking)
    return new Promise((resolve, reject) => {
      // console.log('ssssssssssssss')
      if (!this._allowPicking) return;
      // var pick = this.viewer.scene.pick(px);
      // console.log(this.viewer.scene.drillPick(px))
      // this.viewer.scene.globe.depthTestAgainstTerrain = true;
      let picks = this.viewer.scene.drillPick(px);
      const pick = this.viewer.scene.pick(px);
      // let pick2 = this.viewer.scene.pickPosition(px);
      // this.viewer.scene.globe.depthTestAgainstTerrain = false;
      let layers = null;
      const res = {
        properties: null,
        layerKey: null,
        x: px.x,
        y: px.y,
      };
      console.log(picks, pick);
      if (picks && picks.length > 0) {
        picks = picks.filter(
          (a) =>
            pick && pick.id && pick.id === a.id && a.id.unclickable != true,
        );
      }
      console.log(picks);
      if (picks && picks.length > 0) {
        const entity = picks[0].id;
        if (entity) {
          const owner =
            entity.owner ||
            (entity.entityCollection && entity.entityCollection.owner);
          console.log(owner);
          if (owner) {
            const layer = this.layers.find((a) => a.refLayer === owner);
            if (layer) {
              res.layerKey = layer.key;
            } else if (owner.name == this.HIGHLIGHT) {
              res.layerKey = this.highLightLayer ? this.highLightLayer.key : '';
            }
            /* else if(owner.name=='poi_layer'){

							console.log(entity.description)
							res.properties = {
								'名称':entity.name,
								'类型':entity.tagType,
								'备注':entity.description?entity.description._value:''
							}
							return resolve(res)
						} */
            this.select = entity;
          }

          if (entity.properties instanceof Cesium.PropertyBag) {
            entity.properties.propertyNames.forEach((p) => {
              res.properties = res.properties || {};
              res.properties[p] = entity.properties[p]._value;
            });
          } else {
            res.properties = entity.properties;
          }

          resolve(res);
        }
        return;
      }
      this.select = null;
      const cartesian = core.getCatesian3FromPX(this.viewer, px);
      const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
      if (!cartographic) return;
      let xy = new Cesium.Cartesian2();
      const height = this.viewer.camera.positionCartographic.height;
      const level = core.getMapLevel(height);

      layers = this.layers.filter(
        (a) => a.show && a.refLayer instanceof Cesium.ImageryLayer,
      );
      if (this.highLightLayer) {
        layers.unshift(this.highLightLayer);
      }
      const loop = (i = 0) => {
        if (i >= layers.length) {
          reject();
          return;
        }
        const imageryProvider = layers[i].refLayer._imageryProvider;
        if (imageryProvider && imageryProvider.ready) {
          xy = imageryProvider.tilingScheme.positionToTileXY(
            cartographic,
            level,
            xy,
          );
          const promise = imageryProvider.pickFeatures(
            xy.x,
            xy.y,
            level,
            cartographic.longitude,
            cartographic.latitude,
          );
          promise &&
            promise.then((r) => {
              if (r.length > 0)
                resolve({
                  ...res,

                  properties: r[0].properties,
                  layerKey: layers[i].key,
                });
              else {
                loop(i + 1);
              }
            });
        }
      };
      loop();
    });
  }
  reload() {
    this.layers.forEach((a) => {
      this.unLoadLayer(a.key);
    });
  }
  setLayerProperty(key, options) {
    const layer = this.getLayer(key);
    // console.log(layer, key, options, options.alpha instanceof Number)
    if (layer && layer.refLayer instanceof Cesium.ImageryLayer) {
      if (typeof options.alpha === 'number') {
        // console.log(options.alpha)
        layer.refLayer.alpha = options.alpha;
      }
      if (typeof options.brightness === 'number') {
        layer.refLayer.brightness = options.brightness;
      }
      if (typeof options.contrast === 'number') {
        layer.refLayer.contrast = options.contrast;
      }
      if (typeof options.hue === 'number') {
        layer.refLayer.hue = options.hue;
      }
      if (typeof options.saturation === 'number') {
        layer.refLayer.saturation = options.saturation;
      }
    } else if (
      layer &&
      layer.refLayer instanceof Cesium.Cesium3DTileset &&
      typeof options.alpha === 'number'
    ) {
      layer.refLayer.style = new Cesium.Cesium3DTileStyle({
        color: `color('rgba(${options.brightness * options.red},
					  ${options.brightness * options.green},
					  ${options.brightness * options.blue},${options.alpha})')`,
      });
      layer.refLayer.style.brightness = options.brightness;
      layer.refLayer.style.red = options.red;
      layer.refLayer.style.green = options.green;
      layer.refLayer.style.blue = options.blue;
    }
  }
  setLayerStyle(dataSource, style) {
    if (!(dataSource instanceof Cesium.DataSource)) return;
    if (style) {
      // 通过样式表加载样式
      if (typeof style === 'string') {
        dataSource.entities.values.forEach((entity) => {
          LayerStyle[style](entity);
        });
      } else if (style.type == 'function') {
        var entities = dataSource.entities.values;
        for (const entity of entities) {
          // eval(options.style)(entity,Cesium);
          style.run(entity);
        }
      } else if (style.type == 'functionString') {
        var entities = dataSource.entities.values;
        for (const entity of entities) {
          eval(style.run)(entity, Cesium);
        }
      }
    }
  }
  async show(key) {
    if (!key) return;
    if (Array.isArray(key)) {
      return key.forEach((key) => {
        this.show(key);
      });
    }
    const layer = this.getLayer(key);
    console.log('................getlayer', layer);
    if (!layer) return;
    if (layer.show || layer.loading) return layer;

    if (layer.refLayer) {
      layer.refLayer.show = true;
      layer.show = true;
      this.emit('showChanged', this.showKeys);
      return layer;
    }
    layer.loading = true;
    const cap = await layer.getCapabilities();
    let refLayer = null;
    // console.log(layer)
    if (
      layer.type.toLocaleLowerCase().startsWith('arcgis:') &&
      !layer.isStaticLayer &&
      !(await this.arcgisserver.checkToken(layer.data))
    ) {
      // token验证失败
      await layer.getData(true);
    }
    console.log(layer.type);
    switch (layer.type.toLocaleLowerCase()) {
      case '3dtiles':
      case '3d tiles':
      case 'cesiumlab:3d tiles': {
        // console.log("cesiumlab:3d tiles")
        refLayer = this.load3DTiles(layer.data, layer.showIndex || 0);
        if (refLayer) {
          refLayer.show = layer.show = true;
          layer.refLayer = refLayer;
        }
        break;
      }
      case 'arcgis:geojson': {
        refLayer = await this.arcgisserver.loadGeoJson(
          layer.data,
          layer.showIndex || 0,
        );
        if (refLayer) {
          refLayer.show = layer.show = true;
          layer.refLayer = refLayer;
          layer.refLayer.name = layer.key;
        }
        break;
      }
      case 'arcgis:mapserver': {
        // console.log(layer)
        /* if(cap.dynamics){
					layer.data.usePreCachedTilesIfAvailable = true;
				} */
        layer.data.usePreCachedTilesIfAvailable = cap.isRasterLayer;
        refLayer = this.arcgisserver.loadWMS(layer.data, layer.showIndex || 0);
        if (refLayer) {
          refLayer.show = layer.show = true;
          layer.refLayer = refLayer;
        }
        break;
      }
      case 'arcgis:tms': {
        refLayer = this.geoserver.loadTMS(
          {
            ...layer.data,
            url: `${layer.data.url}/tile/{z}/{y}/{x}`,
          },
          layer.showIndex || 0,
        );
        if (refLayer) {
          refLayer.show = layer.show = true;
          layer.refLayer = refLayer;
        }
        break;
      }
      case 'arcgis:wmts': {
        refLayer = this.geoserver.loadWMTS(
          {
            ...layer.data,
            url: `${layer.data.url}/wmts`,
          },
          layer.showIndex || 0,
        );
        if (refLayer) {
          refLayer.show = layer.show = true;
          layer.refLayer = refLayer;
        }
        break;
      }
      case 'bing': {
        refLayer = this.loadBingMap(layer.data, layer.showIndex || 0);
        if (refLayer) {
          refLayer.show = layer.show = true;
          layer.refLayer = refLayer;
        }
        break;
      }
      case 'geojson':
      case 'geoserver:geojson': {
        refLayer = await this.geoserver.loadGeoJson(
          layer.data,
          layer.showIndex || 0,
        );
        if (refLayer) {
          refLayer.show = layer.show = true;
          layer.refLayer = refLayer;
          layer.refLayer.name = layer.key;
        }
        break;
      }
      case 'geoserver:kml':
      case 'kml': {
        {
        }
        refLayer = await this.geoserver.loadKML(
          layer.data,
          layer.showIndex || 0,
        );
        if (refLayer) {
          refLayer.show = layer.show = true;
          layer.refLayer = refLayer;
          layer.refLayer.name = layer.key;
        }
        break;
      }
      case 'geoserver:tms':
      case 'tms': {
        {
        }
        refLayer = this.geoserver.loadTMS(layer.data, layer.showIndex || 0);
        if (refLayer) {
          refLayer.show = layer.show = true;
          layer.refLayer = refLayer;
        }
        break;
      }
      case 'geoserver:wms':
      case 'wms': {
        {
        }
        refLayer = this.geoserver.loadWMS(layer.data, layer.showIndex || 0);
        if (refLayer) {
          refLayer.show = layer.show = true;
          layer.refLayer = refLayer;
        }
        break;
      }
      case 'geoserver:wmts':
      case 'wmts': {
        {
        }
        refLayer = this.geoserver.loadWMTS(layer.data, layer.showIndex || 0);
        if (refLayer) {
          refLayer.show = layer.show = true;
          layer.refLayer = refLayer;
        }
        break;
      }
      case 'terrain': {
        refLayer = this.loadTerrain(layer.data, layer.showIndex || 0);
        if (refLayer) {
          layer.refLayer = refLayer;
          layer.show = true;
        }
        break;
      }
      default: {
        break;
      }
    }
    console.log(layer);
    layer.loading = false;
    if (this.highLightLayer == layer) {
      this.showAndZoomToLayer(layer);
    } else if (this.getLayer(layer.key) !== layer) {
      this.unLoadLayer(layer);
    }
    // this.sortByShowIndex();
    this.emit('showChanged', this.showKeys);
    return layer;
  }
  async showAndZoomToLayer(key,data) {
    // console.log(this.layers)
    if(data) {
      this.dataCache[key] = data;
    }else {
      data = this.dataCache[key];
    }
    const layer = await this.show(key);
    if (
      layer &&
      layer.refLayer &&
      !['arcgis:wmts', 'bing', 'geoserver:wmts', 'terrain', 'wmts'].includes(
        layer.type,
      )
    ) {
      if (layer.refLayer instanceof Cesium.ImageryLayer) {
        layer.refLayer.getViewableRectangle().then((rectangle) => {
          // console.log(rectangle)
          //bbox=118.92630333092933,25.83528300367192,119.40114908545837,26.2313672604423
         if(data && data.mapServerBox) {
           rectangle.east =  Cesium.Math.toRadians(data.mapServerBox[2]);
           rectangle.west = Cesium.Math.toRadians(data.mapServerBox[0]);
           rectangle.north = Cesium.Math.toRadians(data.mapServerBox[3]);
           rectangle.south =  Cesium.Math.toRadians(data.mapServerBox[1]);
         }

          if (!deepEquals(rectangle, Cesium.Rectangle.MAX_VALUE)) {
            this.viewer.camera.flyTo({
              destination: rectangle,
            });
          }
        });
      } else if (layer.refLayer instanceof Cesium.GeoJsonDataSource) {
        this.viewer.flyTo(layer.refLayer);
      } else if (layer.refLayer instanceof Cesium.Cesium3DTileset) {
        this.viewer.flyTo(layer.refLayer);
      }
    }
  }
  async showInfoBox(res) {
    res.x = res.x || 400;
    res.y = res.y || 100;
    let properties = {};
    if (res.layerKey) {
      // 字段表映射
      const layer = this.getLayer(res.layerKey);
      if (!layer) return;
      let flist = (await layer.getFieldList()) || [];
      flist = flist.filter((a) => a.allowDisplay);
      const _properties = {};
      // 属性名改小写
      for (const p in res.properties) {
        _properties[p.toLocaleLowerCase()] = res.properties[p];
      }
      flist.forEach((a) => {
        if (a.cnName)
          properties[a.cnName] = _properties[a.name.toLocaleLowerCase()];
        else properties[a.name] = _properties[a.name.toLocaleLowerCase()];
      });
    } else {
      properties = res.properties || {};
    }
    properties = JSON.stringify(properties);
    if (properties == '{}') return;
    this.infoBox = Box.open(
      {
        style: { left: `${res.x + 1}px`, top: `${res.y}px` },
        title: '属性',
        beforeClose: () => {
          this.infoBox = null;
        },
      },
      h(PropertiesBox, {
        p: properties,
      }),
      this.infoBox,
      'boxAnchor',
    );
  }
  /**
   * 调整顺序
   * @param {Array<string>} Key值数组
   */
  sortByKeys(keys) {
    keys.forEach((key, index) => {
      const idx = this.layers.findIndex((a) => a.key == key);
      if (idx !== -1) {
        const layer = this.layers[idx];
        layer.showIndex = index;
        this.layers.splice(idx, 1);
        this.layers.push(layer);
      }
    });
    this.sortByShowIndex();
  }
  sortByShowIndex() {
    this.layers
      .filter((a) => a.refLayer instanceof Cesium.ImageryLayer)
      .forEach((layer) => {
        this.viewer.imageryLayers.lowerToBottom(layer.refLayer);
      });
    // this.viewer.imageryLayers.length && console.log(this.viewer.imageryLayers.get(0))
    if (
      this.highLightLayer &&
      this.highLightLayer.refLayer &&
      this.highLightLayer.refLayer instanceof Cesium.ImageryLayer
    ) {
      this.viewer.imageryLayers.raiseToTop(this.highLightLayer.refLayer);
    }
  }
  unloadHighLightLayer() {
    if (this.highLightLayer && this.highLightLayer.refLayer) {
      this.unLoadLayer(this.highLightLayer);
      this.highLightLayer = null;
    }
    const name = this.HIGHLIGHT;
    const dss = this.viewer.dataSources.getByName(name);
    if (dss.length > 0) {
      this.viewer.dataSources.remove(dss[0], true);
    }
  }
  unLoadLayer(key) {
    const layer = this.getLayer(key);
    if (!layer || !layer.refLayer) return;
    this.hide(layer);
    switch (layer.type.toLocaleLowerCase()) {
      case '3dtiles':
      case '3d tiles':
      case 'cesiumlab:3d tiles': {
        this.viewer.scene.primitives.remove(layer.refLayer);
        layer.refLayer = null;
        break;
      }
      case 'arcgis:mapserver':
      case 'bing':
      case 'geoserver:tms':
      case 'geoserver:wms':
      case 'geoserver:wmts':
      case 'tms':
      case 'wms':
      case 'wmts': {
        {
        }
        this.viewer.scene.imageryLayers.remove(layer.refLayer);
        layer.refLayer = null;
        break;
      }
      case 'geojson':
      case 'geoserver:geojson':
      case 'geoserver:kml':
      case 'kml': {
        {
        }
        this.viewer.dataSources.remove(layer.refLayer);
        layer.refLayer = null;
        break;
      }
      // 隐藏时已卸载地形
      /* case 'terrain':
				this.unloadTerrain();
				layer.refLayer = false;
				break; */
      default: {
        break;
      }
    }
  }
  unloadTerrain() {
    this.viewer.terrainProvider = new Cesium.EllipsoidTerrainProvider({});
    this.viewer.scene.globe.depthTestAgainstTerrain = false; // 是否检查深度
  }
  set allowPicking(value) {
    this._allowPicking = value;
  }
  get HIGHLIGHT() {
    return '___HIGHLIGHT';
  }
  set highLightEntities(value) {}
  get highLightEntities() {}
  set hover(feature) {
    if (feature && this._hover.feature && feature === this._hover.feature)
      return;
    const hcolor = Cesium.Color.YELLOW.withAlpha(0.5);
    // 取消原来的
    let f = null;
    if (this._hover.feature) {
      f = this._hover.feature;
      const m = this._hover.material;
      if (f.point) {
        f.point.color = m.color;
        f.point.outlineColor = m.outlineColor;
      } else if (f.polyline) {
        f.polyline.material = m;
      } else if (f.polygon) {
        f.polygon.material = m;
      } else if (f.rectangle) {
        f.rectangle.material = m;
      } else if (f.ellipse) {
        f.ellipse.material = m;
      }
    }
    // 着色
    if (feature) {
      f = this._hover.feature = feature;
      if (f.point) {
        this._hover.material = {
          color: f.point.color,
          outlineColor: f.point.outlineColor,
        };
        f.point.color = hcolor;
        f.point.outlineColor = hcolor;
      } else if (f.polyline) {
        this._hover.material = f.polyline.material;
        f.polyline.material = hcolor;
      } else if (f.polygon) {
        this._hover.material = f.polygon.material;
        f.polygon.material = hcolor;
      } else if (f.rectangle) {
        this._hover.material = f.rectangle.material;
        f.rectangle.material = hcolor;
      } else if (f.ellipse) {
        this._hover.material = f.ellipse.material;
        f.ellipse.material = hcolor;
      } else {
        this._hover.feature = null;
        this._hover.material = null;
      }
    }
  }
  set select(feature) {
    if (feature && this._select.feature && feature === this._select.feature)
      return;
    const hcolor = Cesium.Color.DARKORANGE.withAlpha(0.5);
    // 取消原来的
    let f = null;
    if (this._select.feature) {
      f = this._select.feature;
      const m = this._select.material;
      if (f.point) {
        f.point.color = m.color;
        f.point.outlineColor = m.outlineColor;
      } else if (f.polyline) {
        f.polyline.material = m;
      } else if (f.polygon) {
        f.polygon.material = m;
      } else if (f.rectangle) {
        f.rectangle.material = m;
      } else if (f.ellipse) {
        f.ellipse.material = m;
      }
    }
    // 着色
    if (feature) {
      f = this._select.feature = feature;
      if (f.point) {
        this._select.material = {
          color: f.point.color,
          outlineColor: f.point.outlineColor,
        };
        f.point.color = hcolor;
        f.point.outlineColor = hcolor;
      } else if (f.polyline) {
        this._select.material = f.polyline.material;
        f.polyline.material = hcolor;
      } else if (f.polygon) {
        this._select.material = f.polygon.material;
        f.polygon.material = hcolor;
      } else if (f.rectangle) {
        this._select.material = f.rectangle.material;
        f.rectangle.material = hcolor;
      } else if (f.ellipse) {
        this._select.material = f.ellipse.material;
        f.ellipse.material = hcolor;
      } else {
        this._select.feature = null;
        this._select.material = null;
      }
      // console.log(this._select)
    } else {
      this._select.feature = null;
      this._select.material = null;
    }
  }
  get showKeys() {
    return this.layers.filter((layer) => layer.show).map((layer) => layer.key);
  }
  get showLeftKeys() {
    return this.layers
      .filter(
        (layer) =>
          layer.show &&
          (layer.refLayer instanceof Cesium.ImageryLayer ||
            layer.refLayer instanceof Cesium.Cesium3DTileset) &&
          layer.refLayer.splitDirection != Cesium.SplitDirection.RIGHT,
      )
      .map((layer) => layer.key);
  }
  get showRightKeys() {
    return this.layers
      .filter(
        (layer) =>
          layer.show &&
          (layer.refLayer instanceof Cesium.ImageryLayer ||
            layer.refLayer instanceof Cesium.Cesium3DTileset) &&
          layer.refLayer.splitDirection != Cesium.SplitDirection.LEFT,
      )
      .map((layer) => layer.key);
  }
}
export let GISLayers = null;
export function initLayers(viewer) {
  return new Promise((resolve, reject) => {
    GISLayers = new layerService(viewer);
    GISLayers.init();
    resolve(GISLayers);
  });
}
