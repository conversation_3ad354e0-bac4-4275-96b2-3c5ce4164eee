var e=Object.defineProperty,t=(t,n,a)=>((t,n,a)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[n]=a)(t,"symbol"!=typeof n?n+"":n,a);import{f as n,h as a,Z as r,A as s,B as i,c as o,w as l,F as c,R as u,a5 as d,X as f,r as m,s as p,l as h,aq as _,aN as v,au as g,an as y,aW as b,K as k,u as w,aX as x,aj as O,j as C,k as T,n as S,y as L,m as F,I as N,M as E,p as I,D as P,x as R,z as j,ae as Z,N as A,aV as $,a6 as M,a7 as D,O as V,am as W,W as z,E as U,G as B,aO as H,C as q,ad as Y,at as K,q as G,aY as J,H as X}from"./framework.C8U7mBUf.js";import{u as Q,O as ee,P as te,Q as ne,R as ae,U as re,W as se,l as ie,J as oe,n as le,H as ce,Y as ue,$ as de,a0 as fe,a1 as me,d as pe,F as he,a2 as _e,a3 as ve,a4 as ge,a5 as ye,g as be,a6 as ke,a7 as we,a8 as xe,a9 as Oe,aa as Ce,ab as Te,ac as Se,ad as Le,ae as Fe,af as Ne,ag as Ee,ah as Ie,ai as Pe,aj as Re,ak as je,B as Ze,al as Ae,am as $e,an as Me,ao as De,ap as Ve,S as We,M as ze,aq as Ue,ar as Be,I as He,as as qe,at as Ye,au as Ke,av as Ge,aw as Je,ax as Xe}from"./theme.TDvSnEYR.js";import{g as Qe,S as et,u as tt}from"./index.DyNOf6Q_.js";
/*!
  * shared v10.0.5
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */const nt="undefined"!=typeof window,at=(e,t=!1)=>t?Symbol.for(e):Symbol(e),rt=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),st=e=>"number"==typeof e&&isFinite(e),it=e=>"[object RegExp]"===wt(e),ot=e=>xt(e)&&0===Object.keys(e).length,lt=Object.assign,ct=Object.create,ut=(e=null)=>ct(e);let dt;const ft=()=>dt||(dt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:ut());function mt(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const pt=Object.prototype.hasOwnProperty;function ht(e,t){return pt.call(e,t)}const _t=Array.isArray,vt=e=>"function"==typeof e,gt=e=>"string"==typeof e,yt=e=>"boolean"==typeof e,bt=e=>null!==e&&"object"==typeof e,kt=Object.prototype.toString,wt=e=>kt.call(e),xt=e=>"[object Object]"===wt(e);function Ot(e,t=""){return e.reduce(((e,n,a)=>0===a?e+n:e+t+n),"")}function Ct(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const Tt=e=>!bt(e)||_t(e);function St(e,t){if(Tt(e)||Tt(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach((a=>{"__proto__"!==a&&(bt(e[a])&&!bt(t[a])&&(t[a]=Array.isArray(e[a])?[]:ut()),Tt(t[a])||Tt(e[a])?t[a]=e[a]:n.push({src:e[a],des:t[a]}))}))}}
/*!
  * message-compiler v10.0.5
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function Lt(e,t,n){return{start:e,end:t}}const Ft=1,Nt=2,Et=3,It=4,Pt=5,Rt=6,jt=7,Zt=8,At=9,$t=10,Mt=11,Dt=12,Vt=13,Wt=14;function zt(e,t,n={}){const{domain:a,messages:r,args:s}=n,i=new SyntaxError(String(e));return i.code=e,t&&(i.location=t),i.domain=a,i}function Ut(e){throw e}const Bt=" ",Ht="\n",qt=String.fromCharCode(8232),Yt=String.fromCharCode(8233);function Kt(e){const t=e;let n=0,a=1,r=1,s=0;const i=e=>"\r"===t[e]&&t[e+1]===Ht,o=e=>t[e]===Yt,l=e=>t[e]===qt,c=e=>i(e)||(e=>t[e]===Ht)(e)||o(e)||l(e),u=e=>i(e)||o(e)||l(e)?Ht:t[e];function d(){return s=0,c(n)&&(a++,r=0),i(n)&&n++,n++,r++,t[n]}return{index:()=>n,line:()=>a,column:()=>r,peekOffset:()=>s,charAt:u,currentChar:()=>u(n),currentPeek:()=>u(n+s),next:d,peek:function(){return i(n+s)&&s++,s++,t[n+s]},reset:function(){n=0,a=1,r=1,s=0},resetPeek:function(e=0){s=e},skipToPeek:function(){const e=n+s;for(;e!==n;)d();s=0}}}const Gt=void 0;function Jt(e,t={}){const n=!1!==t.location,a=Kt(e),r=()=>a.index(),s=()=>{return e=a.line(),t=a.column(),n=a.index(),{line:e,column:t,offset:n};var e,t,n},i=s(),o=r(),l={currentType:13,offset:o,startLoc:i,endLoc:i,lastType:13,lastOffset:o,lastStartLoc:i,lastEndLoc:i,braceNest:0,inLinked:!1,text:""},c=()=>l,{onError:u}=t;function d(e,t,a,...r){const s=c();if(t.column+=a,t.offset+=a,u){const a=zt(e,n?Lt(s.startLoc,t):null,{domain:"tokenizer",args:r});u(a)}}function f(e,t,a){e.endLoc=s(),e.currentType=t;const r={type:t};return n&&(r.loc=Lt(e.startLoc,e.endLoc)),null!=a&&(r.value=a),r}const m=e=>f(e,13);function p(e,t){return e.currentChar()===t?(e.next(),t):(d(Ft,s(),0,t),"")}function h(e){let t="";for(;e.currentPeek()===Bt||e.currentPeek()===Ht;)t+=e.currentPeek(),e.peek();return t}function _(e){const t=h(e);return e.skipToPeek(),t}function v(e){if(e===Gt)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function g(e,t){const{currentType:n}=t;if(2!==n)return!1;h(e);const a=function(e){if(e===Gt)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),a}function y(e){h(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function b(e,t=!0){const n=(t=!1,a="")=>{const r=e.currentPeek();return"{"===r?t:"@"!==r&&r?"|"===r?!(a===Bt||a===Ht):r===Bt?(e.peek(),n(!0,Bt)):r!==Ht||(e.peek(),n(!0,Ht)):t},a=n();return t&&e.resetPeek(),a}function k(e,t){const n=e.currentChar();return n===Gt?Gt:t(n)?(e.next(),n):null}function w(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function x(e){return k(e,w)}function O(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function C(e){return k(e,O)}function T(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function S(e){return k(e,T)}function L(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function F(e){return k(e,L)}function N(e){let t="",n="";for(;t=S(e);)n+=t;return n}function E(e){return"'"!==e&&e!==Ht}function I(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return P(e,t,4);case"U":return P(e,t,6);default:return d(It,s(),0,t),""}}function P(e,t,n){p(e,t);let a="";for(let r=0;r<n;r++){const n=F(e);if(!n){d(Pt,s(),0,`\\${t}${a}${e.currentChar()}`);break}a+=n}return`\\${t}${a}`}function R(e){return"{"!==e&&"}"!==e&&e!==Bt&&e!==Ht}function j(e){_(e);const t=p(e,"|");return _(e),t}function Z(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&d(At,s(),0),e.next(),n=f(t,2,"{"),_(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&d(Zt,s(),0),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&_(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&d(jt,s(),0),n=A(e,t)||m(t),t.braceNest=0,n;default:{let a=!0,r=!0,i=!0;if(y(e))return t.braceNest>0&&d(jt,s(),0),n=f(t,1,j(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(4===t.currentType||5===t.currentType||6===t.currentType))return d(jt,s(),0),t.braceNest=0,$(e,t);if(a=function(e,t){const{currentType:n}=t;if(2!==n)return!1;h(e);const a=v(e.currentPeek());return e.resetPeek(),a}(e,t))return n=f(t,4,function(e){_(e);let t="",n="";for(;t=C(e);)n+=t;return e.currentChar()===Gt&&d(jt,s(),0),n}(e)),_(e),n;if(r=g(e,t))return n=f(t,5,function(e){_(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${N(e)}`):t+=N(e),e.currentChar()===Gt&&d(jt,s(),0),t}(e)),_(e),n;if(i=function(e,t){const{currentType:n}=t;if(2!==n)return!1;h(e);const a="'"===e.currentPeek();return e.resetPeek(),a}(e,t))return n=f(t,6,function(e){_(e),p(e,"'");let t="",n="";for(;t=k(e,E);)n+="\\"===t?I(e):t;const a=e.currentChar();return a===Ht||a===Gt?(d(Et,s(),0),a===Ht&&(e.next(),p(e,"'")),n):(p(e,"'"),n)}(e)),_(e),n;if(!a&&!r&&!i)return n=f(t,12,function(e){_(e);let t="",n="";for(;t=k(e,R);)n+=t;return n}(e)),d(Nt,s(),0,n.value),_(e),n;break}}return n}function A(e,t){const{currentType:n}=t;let a=null;const r=e.currentChar();switch(7!==n&&8!==n&&11!==n&&9!==n||r!==Ht&&r!==Bt||d($t,s(),0),r){case"@":return e.next(),a=f(t,7,"@"),t.inLinked=!0,a;case".":return _(e),e.next(),f(t,8,".");case":":return _(e),e.next(),f(t,9,":");default:return y(e)?(a=f(t,1,j(e)),t.braceNest=0,t.inLinked=!1,a):function(e,t){const{currentType:n}=t;if(7!==n)return!1;h(e);const a="."===e.currentPeek();return e.resetPeek(),a}(e,t)||function(e,t){const{currentType:n}=t;if(7!==n&&11!==n)return!1;h(e);const a=":"===e.currentPeek();return e.resetPeek(),a}(e,t)?(_(e),A(e,t)):function(e,t){const{currentType:n}=t;if(8!==n)return!1;h(e);const a=v(e.currentPeek());return e.resetPeek(),a}(e,t)?(_(e),f(t,11,function(e){let t="",n="";for(;t=x(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(9!==n)return!1;const a=()=>{const t=e.currentPeek();return"{"===t?v(e.peek()):!("@"===t||"|"===t||":"===t||"."===t||t===Bt||!t)&&(t===Ht?(e.peek(),a()):b(e,!1))},r=a();return e.resetPeek(),r}(e,t)?(_(e),"{"===r?Z(e,t)||a:f(t,10,function(e){const t=n=>{const a=e.currentChar();return"{"!==a&&"@"!==a&&"|"!==a&&"("!==a&&")"!==a&&a?a===Bt?n:(n+=a,e.next(),t(n)):n};return t("")}(e))):(7===n&&d($t,s(),0),t.braceNest=0,t.inLinked=!1,$(e,t))}}function $(e,t){let n={type:13};if(t.braceNest>0)return Z(e,t)||m(t);if(t.inLinked)return A(e,t)||m(t);switch(e.currentChar()){case"{":return Z(e,t)||m(t);case"}":return d(Rt,s(),0),e.next(),f(t,3,"}");case"@":return A(e,t)||m(t);default:if(y(e))return n=f(t,1,j(e)),t.braceNest=0,t.inLinked=!1,n;if(b(e))return f(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if(n===Bt||n===Ht)if(b(e))t+=n,e.next();else{if(y(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:i}=l;return l.lastType=e,l.lastOffset=t,l.lastStartLoc=n,l.lastEndLoc=i,l.offset=r(),l.startLoc=s(),a.currentChar()===Gt?f(l,13):$(a,l)},currentOffset:r,currentPosition:s,context:c}}const Xt=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Qt(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function en(e={}){const t=!1!==e.location,{onError:n}=e;function a(e,a,r,s,...i){const o=e.currentPosition();if(o.offset+=s,o.column+=s,n){const e=zt(a,t?Lt(r,o):null,{domain:"parser",args:i});n(e)}}function r(e,n,a){const r={type:e};return t&&(r.start=n,r.end=n,r.loc={start:a,end:a}),r}function s(e,n,a,r){t&&(e.end=n,e.loc&&(e.loc.end=a))}function i(e,t){const n=e.context(),a=r(3,n.offset,n.startLoc);return a.value=t,s(a,e.currentOffset(),e.currentPosition()),a}function o(e,t){const n=e.context(),{lastOffset:a,lastStartLoc:i}=n,o=r(5,a,i);return o.index=parseInt(t,10),e.nextToken(),s(o,e.currentOffset(),e.currentPosition()),o}function l(e,t){const n=e.context(),{lastOffset:a,lastStartLoc:i}=n,o=r(4,a,i);return o.key=t,e.nextToken(),s(o,e.currentOffset(),e.currentPosition()),o}function c(e,t){const n=e.context(),{lastOffset:a,lastStartLoc:i}=n,o=r(9,a,i);return o.value=t.replace(Xt,Qt),e.nextToken(),s(o,e.currentOffset(),e.currentPosition()),o}function u(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let i=e.nextToken();if(8===i.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:i,lastStartLoc:o}=n,l=r(8,i,o);return 11!==t.type?(a(e,Dt,n.lastStartLoc,0),l.value="",s(l,i,o),{nextConsumeToken:t,node:l}):(null==t.value&&a(e,Wt,n.lastStartLoc,0,tn(t)),l.value=t.value||"",s(l,e.currentOffset(),e.currentPosition()),{node:l})}(e);n.modifier=t.node,i=t.nextConsumeToken||e.nextToken()}switch(9!==i.type&&a(e,Wt,t.lastStartLoc,0,tn(i)),i=e.nextToken(),2===i.type&&(i=e.nextToken()),i.type){case 10:null==i.value&&a(e,Wt,t.lastStartLoc,0,tn(i)),n.key=function(e,t){const n=e.context(),a=r(7,n.offset,n.startLoc);return a.value=t,s(a,e.currentOffset(),e.currentPosition()),a}(e,i.value||"");break;case 4:null==i.value&&a(e,Wt,t.lastStartLoc,0,tn(i)),n.key=l(e,i.value||"");break;case 5:null==i.value&&a(e,Wt,t.lastStartLoc,0,tn(i)),n.key=o(e,i.value||"");break;case 6:null==i.value&&a(e,Wt,t.lastStartLoc,0,tn(i)),n.key=c(e,i.value||"");break;default:{a(e,Vt,t.lastStartLoc,0);const o=e.context(),l=r(7,o.offset,o.startLoc);return l.value="",s(l,o.offset,o.startLoc),n.key=l,s(n,o.offset,o.startLoc),{nextConsumeToken:i,node:n}}}return s(n,e.currentOffset(),e.currentPosition()),{node:n}}function d(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let d=null;do{const r=d||e.nextToken();switch(d=null,r.type){case 0:null==r.value&&a(e,Wt,t.lastStartLoc,0,tn(r)),n.items.push(i(e,r.value||""));break;case 5:null==r.value&&a(e,Wt,t.lastStartLoc,0,tn(r)),n.items.push(o(e,r.value||""));break;case 4:null==r.value&&a(e,Wt,t.lastStartLoc,0,tn(r)),n.items.push(l(e,r.value||""));break;case 6:null==r.value&&a(e,Wt,t.lastStartLoc,0,tn(r)),n.items.push(c(e,r.value||""));break;case 7:{const t=u(e);n.items.push(t.node),d=t.nextConsumeToken||null;break}}}while(13!==t.currentType&&1!==t.currentType);return s(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function f(e){const t=e.context(),{offset:n,startLoc:i}=t,o=d(e);return 13===t.currentType?o:function(e,t,n,i){const o=e.context();let l=0===i.items.length;const c=r(1,t,n);c.cases=[],c.cases.push(i);do{const t=d(e);l||(l=0===t.items.length),c.cases.push(t)}while(13!==o.currentType);return l&&a(e,Mt,n,0),s(c,e.currentOffset(),e.currentPosition()),c}(e,n,i,o)}return{parse:function(n){const i=Jt(n,lt({},e)),o=i.context(),l=r(0,o.offset,o.startLoc);return t&&l.loc&&(l.loc.source=n),l.body=f(i),e.onCacheKey&&(l.cacheKey=e.onCacheKey(n)),13!==o.currentType&&a(i,Wt,o.lastStartLoc,0,n[o.offset]||""),s(l,i.currentOffset(),i.currentPosition()),l}}}function tn(e){if(13===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function nn(e,t){for(let n=0;n<e.length;n++)an(e[n],t)}function an(e,t){switch(e.type){case 1:nn(e.cases,t),t.helper("plural");break;case 2:nn(e.items,t);break;case 6:an(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function rn(e,t={}){const n=function(e){const t={ast:e,helpers:new Set};return{context:()=>t,helper:e=>(t.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&an(e.body,n);const a=n.context();e.helpers=Array.from(a.helpers)}function sn(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const a=e.items[n];if(3!==a.type&&9!==a.type)break;if(null==a.value)break;t.push(a.value)}if(t.length===e.items.length){e.static=Ot(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function on(e){switch(e.t=e.type,e.type){case 0:{const t=e;on(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)on(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)on(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;on(t.key),t.k=t.key,delete t.key,t.modifier&&(on(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function ln(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?ln(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:a}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(a());const r=t.cases.length;for(let n=0;n<r&&(ln(e,t.cases[n]),n!==r-1);n++)e.push(", ");e.deindent(a()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:a}=e;e.push(`${n("normalize")}([`),e.indent(a());const r=t.items.length;for(let s=0;s<r&&(ln(e,t.items[s]),s!==r-1);s++)e.push(", ");e.deindent(a()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),ln(e,t.key),t.modifier?(e.push(", "),ln(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}function cn(e,t={}){const n=lt({},t),a=!!n.jit,r=!!n.minify,s=null==n.optimize||n.optimize,i=en(n).parse(e);return a?(s&&function(e){const t=e.body;2===t.type?sn(t):t.cases.forEach((e=>sn(e)))}(i),r&&on(i),{ast:i,code:""}):(rn(i,n),((e,t={})=>{const n=gt(t.mode)?t.mode:"normal",a=gt(t.filename)?t.filename:"message.intl",r=!!t.sourceMap,s=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",i=t.needIndent?t.needIndent:"arrow"!==n,o=e.helpers||[],l=function(e,t){const{sourceMap:n,filename:a,breakLineCode:r,needIndent:s}=t,i=!1!==t.location,o={filename:a,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:s,indentLevel:0};function l(e,t){o.code+=e}function c(e,t=!0){const n=t?r:"";l(s?n+"  ".repeat(e):n)}return i&&e.loc&&(o.source=e.loc.source),{context:()=>o,push:l,indent:function(e=!0){const t=++o.indentLevel;e&&c(t)},deindent:function(e=!0){const t=--o.indentLevel;e&&c(t)},newline:function(){c(o.indentLevel)},helper:e=>`_${e}`,needIndent:()=>o.needIndent}}(e,{mode:n,filename:a,sourceMap:r,breakLineCode:s,needIndent:i});l.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),l.indent(i),o.length>0&&(l.push(`const { ${Ot(o.map((e=>`${e}: _${e}`)),", ")} } = ctx`),l.newline()),l.push("return "),ln(l,e),l.deindent(i),l.push("}"),delete e.helpers;const{code:c,map:u}=l.context();return{ast:e,code:c,map:u?u.toJSON():void 0}})(i,n))}
/*!
  * core-base v10.0.5
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function un(e){return t=>function(e,t){const n=(a=t,xn(a,dn));var a;if(null==n)throw On(0);if(1===gn(n)){const t=function(e){return xn(e,fn,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,mn(e,n)]),[]))}return mn(e,n)}(t,e)}const dn=["b","body"];const fn=["c","cases"];function mn(e,t){const n=function(e){return xn(e,pn)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return xn(e,hn,[])}(t).reduce(((t,n)=>[...t,_n(e,n)]),[]);return e.normalize(n)}}const pn=["s","static"];const hn=["i","items"];function _n(e,t){const n=gn(t);switch(n){case 3:case 9:case 7:case 8:return bn(t,n);case 4:{const a=t;if(ht(a,"k")&&a.k)return e.interpolate(e.named(a.k));if(ht(a,"key")&&a.key)return e.interpolate(e.named(a.key));throw On(n)}case 5:{const a=t;if(ht(a,"i")&&st(a.i))return e.interpolate(e.list(a.i));if(ht(a,"index")&&st(a.index))return e.interpolate(e.list(a.index));throw On(n)}case 6:{const n=t,a=function(e){return xn(e,kn)}(n),r=function(e){const t=xn(e,wn);if(t)return t;throw On(6)}(n);return e.linked(_n(e,r),a?_n(e,a):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const vn=["t","type"];function gn(e){return xn(e,vn)}const yn=["v","value"];function bn(e,t){const n=xn(e,yn);if(n)return n;throw On(t)}const kn=["m","modifier"];const wn=["k","key"];function xn(e,t,n){for(let a=0;a<t.length;a++){const n=t[a];if(ht(e,n)&&null!=e[n])return e[n]}return n}function On(e){return new Error(`unhandled node type: ${e}`)}const Cn=e=>e;let Tn=ut();function Sn(e){return bt(e)&&0===gn(e)&&(ht(e,"b")||ht(e,"body"))}let Ln=null;const Fn=Nn("function:translate");function Nn(e){return t=>Ln&&Ln.emit(e,t)}const En=17,In=18,Pn=19,Rn=21,jn=22,Zn=23;function An(e){return zt(e,null,void 0)}function $n(e,t){return null!=t.locale?Dn(t.locale):Dn(e.locale)}let Mn;function Dn(e){if(gt(e))return e;if(vt(e)){if(e.resolvedOnce&&null!=Mn)return Mn;if("Function"===e.constructor.name){const n=e();if(bt(t=n)&&vt(t.then)&&vt(t.catch))throw An(Rn);return Mn=n}throw An(jn)}throw An(Zn);var t}function Vn(e,t,n){return[...new Set([n,..._t(t)?t:bt(t)?Object.keys(t):gt(t)?[t]:[n]])]}function Wn(e,t,n){const a=gt(n)?n:Xn,r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let s=r.__localeChainCache.get(a);if(!s){s=[];let e=[n];for(;_t(e);)e=zn(s,e,t);const i=_t(t)||!xt(t)?t:t.default?t.default:null;e=gt(i)?[i]:i,_t(e)&&zn(s,e,!1),r.__localeChainCache.set(a,s)}return s}function zn(e,t,n){let a=!0;for(let r=0;r<t.length&&yt(a);r++){const s=t[r];gt(s)&&(a=Un(e,t[r],n))}return a}function Un(e,t,n){let a;const r=t.split("-");do{a=Bn(e,r.join("-"),n),r.splice(-1,1)}while(r.length&&!0===a);return a}function Bn(e,t,n){let a=!1;if(!e.includes(t)&&(a=!0,t)){a="!"!==t[t.length-1];const r=t.replace(/!/g,"");e.push(r),(_t(n)||xt(n))&&n[r]&&(a=n[r])}return a}const Hn=[];Hn[0]={w:[0],i:[3,0],"[":[4],o:[7]},Hn[1]={w:[1],".":[2],"[":[4],o:[7]},Hn[2]={w:[2],i:[3,0],0:[3,0]},Hn[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},Hn[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},Hn[5]={"'":[4,0],o:8,l:[5,0]},Hn[6]={'"':[4,0],o:8,l:[6,0]};const qn=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Yn(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function Kn(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,qn.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const Gn=new Map;function Jn(e,t){return bt(e)?e[t]:null}const Xn="en-US",Qn=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let ea,ta,na;let aa=null;const ra=()=>aa;let sa=null;const ia=e=>{sa=e};let oa=0;function la(e={}){const t=vt(e.onWarn)?e.onWarn:Ct,n=gt(e.version)?e.version:"10.0.5",a=gt(e.locale)||vt(e.locale)?e.locale:Xn,r=vt(a)?Xn:a,s=_t(e.fallbackLocale)||xt(e.fallbackLocale)||gt(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:r,i=xt(e.messages)?e.messages:ca(r),o=xt(e.datetimeFormats)?e.datetimeFormats:ca(r),l=xt(e.numberFormats)?e.numberFormats:ca(r),c=lt(ut(),e.modifiers,{upper:(e,t)=>"text"===t&&gt(e)?e.toUpperCase():"vnode"===t&&bt(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&gt(e)?e.toLowerCase():"vnode"===t&&bt(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&gt(e)?Qn(e):"vnode"===t&&bt(e)&&"__v_isVNode"in e?Qn(e.children):e}),u=e.pluralRules||ut(),d=vt(e.missing)?e.missing:null,f=!yt(e.missingWarn)&&!it(e.missingWarn)||e.missingWarn,m=!yt(e.fallbackWarn)&&!it(e.fallbackWarn)||e.fallbackWarn,p=!!e.fallbackFormat,h=!!e.unresolving,_=vt(e.postTranslation)?e.postTranslation:null,v=xt(e.processor)?e.processor:null,g=!yt(e.warnHtmlMessage)||e.warnHtmlMessage,y=!!e.escapeParameter,b=vt(e.messageCompiler)?e.messageCompiler:ea,k=vt(e.messageResolver)?e.messageResolver:ta||Jn,w=vt(e.localeFallbacker)?e.localeFallbacker:na||Vn,x=bt(e.fallbackContext)?e.fallbackContext:void 0,O=e,C=bt(O.__datetimeFormatters)?O.__datetimeFormatters:new Map,T=bt(O.__numberFormatters)?O.__numberFormatters:new Map,S=bt(O.__meta)?O.__meta:{};oa++;const L={version:n,cid:oa,locale:a,fallbackLocale:s,messages:i,modifiers:c,pluralRules:u,missing:d,missingWarn:f,fallbackWarn:m,fallbackFormat:p,unresolving:h,postTranslation:_,processor:v,warnHtmlMessage:g,escapeParameter:y,messageCompiler:b,messageResolver:k,localeFallbacker:w,fallbackContext:x,onWarn:t,__meta:S};return L.datetimeFormats=o,L.numberFormats=l,L.__datetimeFormatters=C,L.__numberFormatters=T,__INTLIFY_PROD_DEVTOOLS__&&function(e,t,n){Ln&&Ln.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}(L,n,S),L}const ca=e=>({[e]:ut()});function ua(e,t,n,a,r){const{missing:s,onWarn:i}=e;if(null!==s){const a=s(e,n,t,r);return gt(a)?a:t}return t}function da(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function fa(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let s=n+1;s<t.length;s++)if(a=e,r=t[s],a!==r&&a.split("-")[0]===r.split("-")[0])return!0;var a,r;return!1}function ma(e,...t){const{datetimeFormats:n,unresolving:a,fallbackLocale:r,onWarn:s,localeFallbacker:i}=e,{__datetimeFormatters:o}=e,[l,c,u,d]=ha(...t);yt(u.missingWarn)?u.missingWarn:e.missingWarn;yt(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const f=!!u.part,m=$n(e,u),p=i(e,r,m);if(!gt(l)||""===l)return new Intl.DateTimeFormat(m,d).format(c);let h,_={},v=null;for(let b=0;b<p.length&&(h=p[b],_=n[h]||{},v=_[l],!xt(v));b++)ua(e,l,h,0,"datetime format");if(!xt(v)||!gt(h))return a?-1:l;let g=`${h}__${l}`;ot(d)||(g=`${g}__${JSON.stringify(d)}`);let y=o.get(g);return y||(y=new Intl.DateTimeFormat(h,lt({},v,d)),o.set(g,y)),f?y.formatToParts(c):y.format(c)}const pa=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function ha(...e){const[t,n,a,r]=e,s=ut();let i,o=ut();if(gt(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw An(Pn);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();i=new Date(n);try{i.toISOString()}catch{throw An(Pn)}}else if("[object Date]"===wt(t)){if(isNaN(t.getTime()))throw An(In);i=t}else{if(!st(t))throw An(En);i=t}return gt(n)?s.key=n:xt(n)&&Object.keys(n).forEach((e=>{pa.includes(e)?o[e]=n[e]:s[e]=n[e]})),gt(a)?s.locale=a:xt(a)&&(o=a),xt(r)&&(o=r),[s.key||"",i,s,o]}function _a(e,t,n){const a=e;for(const r in n){const e=`${t}__${r}`;a.__datetimeFormatters.has(e)&&a.__datetimeFormatters.delete(e)}}function va(e,...t){const{numberFormats:n,unresolving:a,fallbackLocale:r,onWarn:s,localeFallbacker:i}=e,{__numberFormatters:o}=e,[l,c,u,d]=ya(...t);yt(u.missingWarn)?u.missingWarn:e.missingWarn;yt(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const f=!!u.part,m=$n(e,u),p=i(e,r,m);if(!gt(l)||""===l)return new Intl.NumberFormat(m,d).format(c);let h,_={},v=null;for(let b=0;b<p.length&&(h=p[b],_=n[h]||{},v=_[l],!xt(v));b++)ua(e,l,h,0,"number format");if(!xt(v)||!gt(h))return a?-1:l;let g=`${h}__${l}`;ot(d)||(g=`${g}__${JSON.stringify(d)}`);let y=o.get(g);return y||(y=new Intl.NumberFormat(h,lt({},v,d)),o.set(g,y)),f?y.formatToParts(c):y.format(c)}const ga=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function ya(...e){const[t,n,a,r]=e,s=ut();let i=ut();if(!st(t))throw An(En);const o=t;return gt(n)?s.key=n:xt(n)&&Object.keys(n).forEach((e=>{ga.includes(e)?i[e]=n[e]:s[e]=n[e]})),gt(a)?s.locale=a:xt(a)&&(i=a),xt(r)&&(i=r),[s.key||"",o,s,i]}function ba(e,t,n){const a=e;for(const r in n){const e=`${t}__${r}`;a.__numberFormatters.has(e)&&a.__numberFormatters.delete(e)}}const ka=e=>e,wa=e=>"",xa=e=>0===e.length?"":Ot(e),Oa=e=>null==e?"":_t(e)||xt(e)&&e.toString===kt?JSON.stringify(e,null,2):String(e);function Ca(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function Ta(e={}){const t=e.locale,n=function(e){const t=st(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(st(e.named.count)||st(e.named.n))?st(e.named.count)?e.named.count:st(e.named.n)?e.named.n:t:t}(e),a=bt(e.pluralRules)&&gt(t)&&vt(e.pluralRules[t])?e.pluralRules[t]:Ca,r=bt(e.pluralRules)&&gt(t)&&vt(e.pluralRules[t])?Ca:void 0,s=e.list||[],i=e.named||ut();st(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,i);function o(t,n){const a=vt(e.messages)?e.messages(t,!!n):!!bt(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):wa)}const l=xt(e.processor)&&vt(e.processor.normalize)?e.processor.normalize:xa,c=xt(e.processor)&&vt(e.processor.interpolate)?e.processor.interpolate:Oa,u={list:e=>s[e],named:e=>i[e],plural:e=>e[a(n,e.length,r)],linked:(t,...n)=>{const[a,r]=n;let s="text",i="";1===n.length?bt(a)?(i=a.modifier||i,s=a.type||s):gt(a)&&(i=a||i):2===n.length&&(gt(a)&&(i=a||i),gt(r)&&(s=r||s));const l=o(t,!0)(u),c="vnode"===s&&_t(l)&&i?l[0]:l;return i?(d=i,e.modifiers?e.modifiers[d]:ka)(c,s):c;var d},message:o,type:xt(e.processor)&&gt(e.processor.type)?e.processor.type:"text",interpolate:c,normalize:l,values:lt(ut(),s,i)};return u}const Sa=()=>"",La=e=>vt(e);function Fa(e,...t){const{fallbackFormat:n,postTranslation:a,unresolving:r,messageCompiler:s,fallbackLocale:i,messages:o}=e,[l,c]=Ia(...t),u=yt(c.missingWarn)?c.missingWarn:e.missingWarn,d=yt(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,f=yt(c.escapeParameter)?c.escapeParameter:e.escapeParameter,m=!!c.resolvedMessage,p=gt(c.default)||yt(c.default)?yt(c.default)?s?l:()=>l:c.default:n?s?l:()=>l:null,h=n||null!=p&&(gt(p)||vt(p)),_=$n(e,c);f&&function(e){_t(e.list)?e.list=e.list.map((e=>gt(e)?mt(e):e)):bt(e.named)&&Object.keys(e.named).forEach((t=>{gt(e.named[t])&&(e.named[t]=mt(e.named[t]))}))}(c);let[v,g,y]=m?[l,_,o[_]||ut()]:Na(e,l,_,i,d,u),b=v,k=l;if(m||gt(b)||Sn(b)||La(b)||h&&(b=p,k=b),!(m||(gt(b)||Sn(b)||La(b))&&gt(g)))return r?-1:l;let w=!1;const x=La(b)?b:Ea(e,l,g,b,k,(()=>{w=!0}));if(w)return b;const O=function(e,t,n,a){const{modifiers:r,pluralRules:s,messageResolver:i,fallbackLocale:o,fallbackWarn:l,missingWarn:c,fallbackContext:u}=e,d=(a,r)=>{let s=i(n,a);if(null==s&&(u||r)){const[,,n]=Na(u||e,a,t,o,l,c);s=i(n,a)}if(gt(s)||Sn(s)){let n=!1;const r=Ea(e,a,t,s,a,(()=>{n=!0}));return n?Sa:r}return La(s)?s:Sa},f={locale:t,modifiers:r,pluralRules:s,messages:d};e.processor&&(f.processor=e.processor);a.list&&(f.list=a.list);a.named&&(f.named=a.named);st(a.plural)&&(f.pluralIndex=a.plural);return f}(e,g,y,c),C=function(e,t,n){const a=t(n);return a}(0,x,Ta(O)),T=a?a(C,l):C;if(__INTLIFY_PROD_DEVTOOLS__){const t={timestamp:Date.now(),key:gt(l)?l:La(b)?b.key:"",locale:g||(La(b)?b.locale:""),format:gt(b)?b:La(b)?b.source:"",message:T};t.meta=lt({},e.__meta,ra()||{}),Fn(t)}return T}function Na(e,t,n,a,r,s){const{messages:i,onWarn:o,messageResolver:l,localeFallbacker:c}=e,u=c(e,a,n);let d,f=ut(),m=null;for(let p=0;p<u.length&&(d=u[p],f=i[d]||ut(),null===(m=l(f,t))&&(m=f[t]),!(gt(m)||Sn(m)||La(m)));p++)if(!fa(d,u)){const n=ua(e,t,d,0,"translate");n!==t&&(m=n)}return[m,d,f]}function Ea(e,t,n,a,r,s){const{messageCompiler:i,warnHtmlMessage:o}=e;if(La(a)){const e=a;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==i){const e=()=>a;return e.locale=n,e.key=t,e}const l=i(a,function(e,t,n,a,r,s){return{locale:t,key:n,warnHtmlMessage:r,onError:e=>{throw s&&s(e),e},onCacheKey:e=>((e,t,n)=>rt({l:e,k:t,s:n}))(t,n,e)}}(0,n,r,0,o,s));return l.locale=n,l.key=t,l.source=a,l}function Ia(...e){const[t,n,a]=e,r=ut();if(!(gt(t)||st(t)||La(t)||Sn(t)))throw An(En);const s=st(t)?String(t):(La(t),t);return st(n)?r.plural=n:gt(n)?r.default=n:xt(n)&&!ot(n)?r.named=n:_t(n)&&(r.list=n),st(a)?r.plural=a:gt(a)?r.default=a:xt(a)&&lt(r,a),[s,r]}"boolean"!=typeof __INTLIFY_PROD_DEVTOOLS__&&(ft().__INTLIFY_PROD_DEVTOOLS__=!1),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(ft().__INTLIFY_DROP_MESSAGE_COMPILER__=!1);const Pa=24,Ra=25,ja=26,Za=27,Aa=28,$a=29,Ma=31,Da=32;function Va(e,...t){return zt(e,null,void 0)}const Wa=at("__translateVNode"),za=at("__datetimeParts"),Ua=at("__numberParts"),Ba=at("__setPluralRules"),Ha=at("__injectWithOption"),qa=at("__dispose");function Ya(e){if(!bt(e))return e;for(const t in e)if(ht(e,t))if(t.includes(".")){const n=t.split("."),a=n.length-1;let r=e,s=!1;for(let e=0;e<a;e++){if(n[e]in r||(r[n[e]]=ut()),!bt(r[n[e]])){s=!0;break}r=r[n[e]]}s||(r[n[a]]=e[t],delete e[t]),bt(r[n[a]])&&Ya(r[n[a]])}else bt(e[t])&&Ya(e[t]);return e}function Ka(e,t){const{messages:n,__i18n:a,messageResolver:r,flatJson:s}=t,i=xt(n)?n:_t(a)?ut():{[e]:ut()};if(_t(a)&&a.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(i[t]=i[t]||ut(),St(n,i[t])):St(n,i)}else gt(e)&&St(JSON.parse(e),i)})),null==r&&s)for(const o in i)ht(i,o)&&Ya(i[o]);return i}function Ga(e){return e.type}function Ja(e,t,n){let a=bt(t.messages)?t.messages:ut();"__i18nGlobal"in n&&(a=Ka(e.locale.value,{messages:a,__i18n:n.__i18nGlobal}));const r=Object.keys(a);if(r.length&&r.forEach((t=>{e.mergeLocaleMessage(t,a[t])})),bt(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(bt(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}function Xa(e){return h(_,null,e,0)}const Qa=()=>[],er=()=>!1;let tr=0;function nr(e){return(t,n,a,r)=>e(n,a,u()||void 0,r)}function ar(e={}){const{__root:t,__injectWithOption:n}=e,a=void 0===t,r=e.flatJson,s=nt?m:p;let i=!yt(e.inheritLocale)||e.inheritLocale;const c=s(t&&i?t.locale.value:gt(e.locale)?e.locale:Xn),u=s(t&&i?t.fallbackLocale.value:gt(e.fallbackLocale)||_t(e.fallbackLocale)||xt(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:c.value),d=s(Ka(c.value,e)),f=s(xt(e.datetimeFormats)?e.datetimeFormats:{[c.value]:{}}),h=s(xt(e.numberFormats)?e.numberFormats:{[c.value]:{}});let _=t?t.missingWarn:!yt(e.missingWarn)&&!it(e.missingWarn)||e.missingWarn,v=t?t.fallbackWarn:!yt(e.fallbackWarn)&&!it(e.fallbackWarn)||e.fallbackWarn,g=t?t.fallbackRoot:!yt(e.fallbackRoot)||e.fallbackRoot,y=!!e.fallbackFormat,b=vt(e.missing)?e.missing:null,k=vt(e.missing)?nr(e.missing):null,w=vt(e.postTranslation)?e.postTranslation:null,x=t?t.warnHtmlMessage:!yt(e.warnHtmlMessage)||e.warnHtmlMessage,O=!!e.escapeParameter;const C=t?t.modifiers:xt(e.modifiers)?e.modifiers:{};let T,S=e.pluralRules||t&&t.pluralRules;T=(()=>{a&&ia(null);const t={version:"10.0.5",locale:c.value,fallbackLocale:u.value,messages:d.value,modifiers:C,pluralRules:S,missing:null===k?void 0:k,missingWarn:_,fallbackWarn:v,fallbackFormat:y,unresolving:!0,postTranslation:null===w?void 0:w,warnHtmlMessage:x,escapeParameter:O,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=f.value,t.numberFormats=h.value,t.__datetimeFormatters=xt(T)?T.__datetimeFormatters:void 0,t.__numberFormatters=xt(T)?T.__numberFormatters:void 0;const n=la(t);return a&&ia(n),n})(),da(T,c.value,u.value);const L=o({get:()=>c.value,set:e=>{c.value=e,T.locale=c.value}}),F=o({get:()=>u.value,set:e=>{u.value=e,T.fallbackLocale=u.value,da(T,c.value,e)}}),N=o((()=>d.value)),E=o((()=>f.value)),I=o((()=>h.value));const P=(e,n,r,s,i,o)=>{let l;c.value,u.value,d.value,f.value,h.value;try{__INTLIFY_PROD_DEVTOOLS__,a||(T.fallbackContext=t?sa:void 0),l=e(T)}finally{__INTLIFY_PROD_DEVTOOLS__,a||(T.fallbackContext=void 0)}if("translate exists"!==r&&st(l)&&-1===l||"translate exists"===r&&!l){const[e,a]=n();return t&&g?s(t):i(e)}if(o(l))return l;throw Va(Pa)};function R(...e){return P((t=>Reflect.apply(Fa,null,[t,...e])),(()=>Ia(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>gt(e)))}const j={normalize:function(e){return e.map((e=>gt(e)||st(e)||yt(e)?Xa(String(e)):e))},interpolate:e=>e,type:"vnode"};function Z(e){return d.value[e]||{}}tr++,t&&nt&&(l(t.locale,(e=>{i&&(c.value=e,T.locale=e,da(T,c.value,u.value))})),l(t.fallbackLocale,(e=>{i&&(u.value=e,T.fallbackLocale=e,da(T,c.value,u.value))})));const A={id:tr,locale:L,fallbackLocale:F,get inheritLocale(){return i},set inheritLocale(e){i=e,e&&t&&(c.value=t.locale.value,u.value=t.fallbackLocale.value,da(T,c.value,u.value))},get availableLocales(){return Object.keys(d.value).sort()},messages:N,get modifiers(){return C},get pluralRules(){return S||{}},get isGlobal(){return a},get missingWarn(){return _},set missingWarn(e){_=e,T.missingWarn=_},get fallbackWarn(){return v},set fallbackWarn(e){v=e,T.fallbackWarn=v},get fallbackRoot(){return g},set fallbackRoot(e){g=e},get fallbackFormat(){return y},set fallbackFormat(e){y=e,T.fallbackFormat=y},get warnHtmlMessage(){return x},set warnHtmlMessage(e){x=e,T.warnHtmlMessage=e},get escapeParameter(){return O},set escapeParameter(e){O=e,T.escapeParameter=e},t:R,getLocaleMessage:Z,setLocaleMessage:function(e,t){if(r){const n={[e]:t};for(const e in n)ht(n,e)&&Ya(n[e]);t=n[e]}d.value[e]=t,T.messages=d.value},mergeLocaleMessage:function(e,t){d.value[e]=d.value[e]||{};const n={[e]:t};if(r)for(const a in n)ht(n,a)&&Ya(n[a]);St(t=n[e],d.value[e]),T.messages=d.value},getPostTranslationHandler:function(){return vt(w)?w:null},setPostTranslationHandler:function(e){w=e,T.postTranslation=e},getMissingHandler:function(){return b},setMissingHandler:function(e){null!==e&&(k=nr(e)),b=e,T.missing=k},[Ba]:function(e){S=e,T.pluralRules=S}};return A.datetimeFormats=E,A.numberFormats=I,A.rt=function(...e){const[t,n,a]=e;if(a&&!bt(a))throw Va(Ra);return R(t,n,lt({resolvedMessage:!0},a||{}))},A.te=function(e,t){return P((()=>{if(!e)return!1;const n=Z(gt(t)?t:c.value),a=T.messageResolver(n,e);return Sn(a)||La(a)||gt(a)}),(()=>[e]),"translate exists",(n=>Reflect.apply(n.te,n,[e,t])),er,(e=>yt(e)))},A.tm=function(e){const n=function(e){let t=null;const n=Wn(T,u.value,c.value);for(let a=0;a<n.length;a++){const r=d.value[n[a]]||{},s=T.messageResolver(r,e);if(null!=s){t=s;break}}return t}(e);return null!=n?n:t&&t.tm(e)||{}},A.d=function(...e){return P((t=>Reflect.apply(ma,null,[t,...e])),(()=>ha(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>""),(e=>gt(e)))},A.n=function(...e){return P((t=>Reflect.apply(va,null,[t,...e])),(()=>ya(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>""),(e=>gt(e)))},A.getDateTimeFormat=function(e){return f.value[e]||{}},A.setDateTimeFormat=function(e,t){f.value[e]=t,T.datetimeFormats=f.value,_a(T,e,t)},A.mergeDateTimeFormat=function(e,t){f.value[e]=lt(f.value[e]||{},t),T.datetimeFormats=f.value,_a(T,e,t)},A.getNumberFormat=function(e){return h.value[e]||{}},A.setNumberFormat=function(e,t){h.value[e]=t,T.numberFormats=h.value,ba(T,e,t)},A.mergeNumberFormat=function(e,t){h.value[e]=lt(h.value[e]||{},t),T.numberFormats=h.value,ba(T,e,t)},A[Ha]=n,A[Wa]=function(...e){return P((t=>{let n;const a=t;try{a.processor=j,n=Reflect.apply(Fa,null,[a,...e])}finally{a.processor=null}return n}),(()=>Ia(...e)),"translate",(t=>t[Wa](...e)),(e=>[Xa(e)]),(e=>_t(e)))},A[za]=function(...e){return P((t=>Reflect.apply(ma,null,[t,...e])),(()=>ha(...e)),"datetime format",(t=>t[za](...e)),Qa,(e=>gt(e)||_t(e)))},A[Ua]=function(...e){return P((t=>Reflect.apply(va,null,[t,...e])),(()=>ya(...e)),"number format",(t=>t[Ua](...e)),Qa,(e=>gt(e)||_t(e)))},A}function rr(e={}){const t=ar(function(e){const t=gt(e.locale)?e.locale:Xn,n=gt(e.fallbackLocale)||_t(e.fallbackLocale)||xt(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,a=vt(e.missing)?e.missing:void 0,r=!yt(e.silentTranslationWarn)&&!it(e.silentTranslationWarn)||!e.silentTranslationWarn,s=!yt(e.silentFallbackWarn)&&!it(e.silentFallbackWarn)||!e.silentFallbackWarn,i=!yt(e.fallbackRoot)||e.fallbackRoot,o=!!e.formatFallbackMessages,l=xt(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=vt(e.postTranslation)?e.postTranslation:void 0,d=!gt(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,f=!!e.escapeParameterHtml,m=!yt(e.sync)||e.sync;let p=e.messages;if(xt(e.sharedMessages)){const t=e.sharedMessages;p=Object.keys(t).reduce(((e,n)=>{const a=e[n]||(e[n]={});return lt(a,t[n]),e}),p||{})}const{__i18n:h,__root:_,__injectWithOption:v}=e,g=e.datetimeFormats,y=e.numberFormats;return{locale:t,fallbackLocale:n,messages:p,flatJson:e.flatJson,datetimeFormats:g,numberFormats:y,missing:a,missingWarn:r,fallbackWarn:s,fallbackRoot:i,fallbackFormat:o,modifiers:l,pluralRules:c,postTranslation:u,warnHtmlMessage:d,escapeParameter:f,messageResolver:e.messageResolver,inheritLocale:m,__i18n:h,__root:_,__injectWithOption:v}}(e)),{__extender:n}=e,a={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return yt(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=yt(e)?!e:e},get silentFallbackWarn(){return yt(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=yt(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t:(...e)=>Reflect.apply(t.t,t,[...e]),rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[n,a,r]=e,s={plural:1};let i=null,o=null;if(!gt(n))throw Va(Ra);const l=n;return gt(a)?s.locale=a:st(a)?s.plural=a:_t(a)?i=a:xt(a)&&(o=a),gt(r)?s.locale=r:_t(r)?i=r:xt(r)&&(o=r),Reflect.apply(t.t,t,[l,i||o||{},s])},te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)}};return a.__extender=n,a}function sr(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Ba](t.pluralizationRules||e.pluralizationRules);const n=Ka(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}const ir={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function or(){return c}const lr=n({name:"i18n-t",props:lt({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>st(e)||!isNaN(e)}},ir),setup(e,t){const{slots:n,attrs:r}=t,s=e.i18n||_r({useScope:e.scope,__useComponent:!0});return()=>{const i=Object.keys(n).filter((e=>"_"!==e)),o=ut();e.locale&&(o.locale=e.locale),void 0!==e.plural&&(o.plural=gt(e.plural)?+e.plural:e.plural);const l=function({slots:e},t){if(1===t.length&&"default"===t[0])return(e.default?e.default():[]).reduce(((e,t)=>[...e,...t.type===c?t.children:[t]]),[]);return t.reduce(((t,n)=>{const a=e[n];return a&&(t[n]=a()),t}),ut())}(t,i),u=s[Wa](e.keypath,l,o),d=lt(ut(),r),f=gt(e.tag)||bt(e.tag)?e.tag:or();return a(f,d,u)}}});function cr(e,t,n,r){const{slots:s,attrs:i}=t;return()=>{const t={part:!0};let o=ut();e.locale&&(t.locale=e.locale),gt(e.format)?t.key=e.format:bt(e.format)&&(gt(e.format.key)&&(t.key=e.format.key),o=Object.keys(e.format).reduce(((t,a)=>n.includes(a)?lt(ut(),t,{[a]:e.format[a]}):t),ut()));const l=r(e.value,t,o);let c=[t.key];_t(l)?c=l.map(((e,t)=>{const n=s[e.type],a=n?n({[e.type]:e.value,index:t,parts:l}):[e.value];var r;return _t(r=a)&&!gt(r[0])&&(a[0].key=`${e.type}-${t}`),a})):gt(l)&&(c=[l]);const u=lt(ut(),i),d=gt(e.tag)||bt(e.tag)?e.tag:or();return a(d,u,c)}}const ur=n({name:"i18n-n",props:lt({value:{type:Number,required:!0},format:{type:[String,Object]}},ir),setup(e,t){const n=e.i18n||_r({useScope:e.scope,__useComponent:!0});return cr(e,t,ga,((...e)=>n[Ua](...e)))}}),dr=n({name:"i18n-d",props:lt({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},ir),setup(e,t){const n=e.i18n||_r({useScope:e.scope,__useComponent:!0});return cr(e,t,pa,((...e)=>n[za](...e)))}});function fr(e){if(gt(e))return{path:e};if(xt(e)){if(!("path"in e))throw Va(Aa);return e}throw Va($a)}function mr(e){const{path:t,locale:n,args:a,choice:r,plural:s}=e,i={},o=a||{};return gt(n)&&(i.locale=n),st(r)&&(i.plural=r),st(s)&&(i.plural=s),[t,o,i]}function pr(e,t,...n){const a=xt(n[0])?n[0]:{};(!yt(a.globalInstall)||a.globalInstall)&&([lr.name,"I18nT"].forEach((t=>e.component(t,lr))),[ur.name,"I18nN"].forEach((t=>e.component(t,ur))),[dr.name,"I18nD"].forEach((t=>e.component(t,dr)))),e.directive("t",function(e){const t=t=>{const{instance:n,value:a}=t;if(!n||!n.$)throw Va(Da);const r=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const a=n.__getInstance(t);return null!=a?a.__composer:e.global.__composer}}(e,n.$),s=fr(a);return[Reflect.apply(r.t,r,[...mr(s)]),r]};return{created:(n,a)=>{const[r,s]=t(a);nt&&e.global===s&&(n.__i18nWatcher=l(s.locale,(()=>{a.instance&&a.instance.$forceUpdate()}))),n.__composer=s,n.textContent=r},unmounted:e=>{nt&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,a=fr(t);e.textContent=Reflect.apply(n.t,n,[...mr(a)])}},getSSRProps:e=>{const[n]=t(e);return{textContent:n}}}}(t))}const hr=at("global-vue-i18n");function _r(e={}){const t=u();if(null==t)throw Va(ja);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Va(Za);const n=function(e){const t=r(e.isCE?hr:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Va(e.isCE?Ma:Da);return t}(t),a=function(e){return"composition"===e.mode?e.global:e.global.__composer}(n),o=Ga(t),l=function(e,t){return ot(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,o);if("global"===l)return Ja(a,e,o),a;if("parent"===l){let r=function(e,t,n=!1){let a=null;const r=t.root;let s=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,n);for(;null!=s;){const t=e;if("composition"===e.mode)a=t.__getInstance(s);else if(__VUE_I18N_LEGACY_API__){const e=t.__getInstance(s);null!=e&&(a=e.__composer,n&&a&&!a[Ha]&&(a=null))}if(null!=a)break;if(r===s)break;s=s.parent}return a}(n,t,e.__useComponent);return null==r&&(r=a),r}const c=n;let d=c.__getInstance(t);if(null==d){const n=lt({},e);"__i18n"in o&&(n.__i18n=o.__i18n),a&&(n.__root=a),d=ar(n),c.__composerExtend&&(d[qa]=c.__composerExtend(d)),function(e,t,n){s((()=>{}),t),i((()=>{const a=n;e.__deleteInstance(t);const r=a[qa];r&&(r(),delete a[qa])}),t)}(c,t,d),c.__setInstance(t,d)}return d}const vr=["locale","fallbackLocale","availableLocales"],gr=["t","rt","d","n","tm","te"];var yr;if("boolean"!=typeof __VUE_I18N_FULL_INSTALL__&&(ft().__VUE_I18N_FULL_INSTALL__=!0),"boolean"!=typeof __VUE_I18N_LEGACY_API__&&(ft().__VUE_I18N_LEGACY_API__=!0),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(ft().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),"boolean"!=typeof __INTLIFY_PROD_DEVTOOLS__&&(ft().__INTLIFY_PROD_DEVTOOLS__=!1),ea=function(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&gt(e)){!yt(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||Cn)(e),a=Tn[n];if(a)return a;const{ast:r,detectError:s}=function(e,t={}){let n=!1;const a=t.onError||Ut;return t.onError=e=>{n=!0,a(e)},{...cn(e,t),detectError:n}}(e,{...t,location:!1,jit:!0}),i=un(r);return s?i:Tn[n]=i}{const t=e.cacheKey;if(t){const n=Tn[t];return n||(Tn[t]=un(e))}return un(e)}},ta=function(e,t){if(!bt(e))return null;let n=Gn.get(t);if(n||(n=function(e){const t=[];let n,a,r,s,i,o,l,c=-1,u=0,d=0;const f=[];function m(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,r="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===a?a=r:a+=r},f[1]=()=>{void 0!==a&&(t.push(a),a=void 0)},f[2]=()=>{f[0](),d++},f[3]=()=>{if(d>0)d--,u=4,f[0]();else{if(d=0,void 0===a)return!1;if(a=Kn(a),!1===a)return!1;f[1]()}};null!==u;)if(c++,n=e[c],"\\"!==n||!m()){if(s=Yn(n),l=Hn[u],i=l[s]||l.l||8,8===i)return;if(u=i[0],void 0!==i[1]&&(o=f[i[1]],o&&(r=n,!1===o())))return;if(7===u)return t}}(t),n&&Gn.set(t,n)),!n)return null;const a=n.length;let r=e,s=0;for(;s<a;){const e=r[n[s]];if(void 0===e)return null;if(vt(r))return null;r=e,s++}return r},na=Wn,__INTLIFY_PROD_DEVTOOLS__){const e=ft();e.__INTLIFY__=!0,yr=e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__,Ln=yr}const br=function(e={}){const t=__VUE_I18N_LEGACY_API__&&yt(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,n=!yt(e.globalInjection)||e.globalInjection,a=new Map,[r,s]=function(e,t){const n=d(),a=__VUE_I18N_LEGACY_API__&&t?n.run((()=>rr(e))):n.run((()=>ar(e)));if(null==a)throw Va(Da);return[n,a]}(e,t),i=at(""),o={get mode(){return __VUE_I18N_LEGACY_API__&&t?"legacy":"composition"},async install(e,...a){if(e.__VUE_I18N_SYMBOL__=i,e.provide(e.__VUE_I18N_SYMBOL__,o),xt(a[0])){const e=a[0];o.__composerExtend=e.__composerExtend,o.__vueI18nExtend=e.__vueI18nExtend}let r=null;!t&&n&&(r=function(e,t){const n=Object.create(null);vr.forEach((e=>{const a=Object.getOwnPropertyDescriptor(t,e);if(!a)throw Va(Da);const r=f(a.value)?{get:()=>a.value.value,set(e){a.value.value=e}}:{get:()=>a.get&&a.get()};Object.defineProperty(n,e,r)})),e.config.globalProperties.$i18n=n,gr.forEach((n=>{const a=Object.getOwnPropertyDescriptor(t,n);if(!a||!a.value)throw Va(Da);Object.defineProperty(e.config.globalProperties,`$${n}`,a)}));const a=()=>{delete e.config.globalProperties.$i18n,gr.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))};return a}(e,o.global)),__VUE_I18N_FULL_INSTALL__&&pr(e,o,...a),__VUE_I18N_LEGACY_API__&&t&&e.mixin(function(e,t,n){return{beforeCreate(){const a=u();if(!a)throw Va(Da);const r=this.$options;if(r.i18n){const a=r.i18n;if(r.__i18n&&(a.__i18n=r.__i18n),a.__root=t,this===this.$root)this.$i18n=sr(e,a);else{a.__injectWithOption=!0,a.__extender=n.__vueI18nExtend,this.$i18n=rr(a);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(r.__i18n)if(this===this.$root)this.$i18n=sr(e,r);else{this.$i18n=rr({__i18n:r.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;r.__i18nGlobal&&Ja(t,r,r),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),n.__setInstance(a,this.$i18n)},mounted(){},unmounted(){const e=u();if(!e)throw Va(Da);const t=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,t.__disposer&&(t.__disposer(),delete t.__disposer,delete t.__extender),n.__deleteInstance(e),delete this.$i18n}}}(s,s.__composer,o));const l=e.unmount;e.unmount=()=>{r&&r(),o.dispose(),l()}},get global(){return s},dispose(){r.stop()},__instances:a,__getInstance:function(e){return a.get(e)||null},__setInstance:function(e,t){a.set(e,t)},__deleteInstance:function(e){a.delete(e)}};return o}({globalInjection:!0,legacy:!1,locale:"",messages:{}}),kr=Object.assign({"./langs/en-US/authentication.json":()=>v((()=>import("./authentication.ovDtJ5Ec.js")),[]),"./langs/en-US/common.json":()=>v((()=>import("./common.DY4aBKEw.js")),[]),"./langs/en-US/preferences.json":()=>v((()=>import("./preferences.DyLVdY3r.js")),[]),"./langs/en-US/ui.json":()=>v((()=>import("./ui.B5s_TNmb.js")),[]),"./langs/zh-CN/authentication.json":()=>v((()=>import("./authentication.CGe1jrJ7.js")),[]),"./langs/zh-CN/common.json":()=>v((()=>import("./common.0VnrOQfJ.js")),[]),"./langs/zh-CN/preferences.json":()=>v((()=>import("./preferences.DzPNUNFS.js")),[]),"./langs/zh-CN/ui.json":()=>v((()=>import("./ui.B2izUYAl.js")),[])});Q(),function(e,t){const n={},a={};for(const r in t){const a=r.match(e);if(a){const[e,s,i]=a;s&&i&&(n[s]||(n[s]={}),t[r]&&(n[s][i]=t[r]))}}for(const[r,s]of Object.entries(n))a[r]=async()=>{var e;const t={};for(const[n,a]of Object.entries(s))t[n]=null==(e=await a())?void 0:e.default;return{default:t}}}(/\.\/langs\/([^/]+)\/(.*)\.json$/,kr);const wr=br.global.t,xr="modelValue",Or={},Cr={DefaultButton:a(ie,{size:"sm",variant:"outline"}),PrimaryButton:a(ie,{size:"sm",variant:"default"}),VbenCheckbox:ee,VbenInput:te,VbenInputPassword:ne,VbenPinInput:ae,VbenSelect:re},Tr={VbenCheckbox:"checked"};class Sr{constructor(e={}){t(this,"latestSubmissionValues",null),t(this,"prevState",null),t(this,"form",{}),t(this,"isMounted",!1),t(this,"state",null),t(this,"stateHandler"),t(this,"store");const{...n}=e,a={actionWrapperClass:"",collapsed:!1,collapsedRows:1,collapseTriggerResize:!1,commonConfig:{},handleReset:void 0,handleSubmit:void 0,handleValuesChange:void 0,layout:"horizontal",resetButtonOptions:{},schema:[],showCollapseButton:!1,showDefaultActions:!0,submitButtonOptions:{},submitOnEnter:!1,wrapperClass:"grid-cols-1"};this.store=new et({...a,...n},{onUpdate:()=>{this.prevState=this.state,this.state=this.store.state,this.updateState()}}),this.state=this.store.state,this.stateHandler=new oe,le(this)}async getForm(){var e;if(this.isMounted||await this.stateHandler.waitForCondition(),!(null==(e=this.form)?void 0:e.meta))throw new Error("<VbenForm /> is not mounted");return this.form}updateState(){var e,t,n;const a=(null==(e=this.state)?void 0:e.schema)??[],r=(null==(t=this.prevState)?void 0:t.schema)??[];if(a.length<r.length){const e=new Set(a.map((e=>e.fieldName))),t=r.filter((t=>!e.has(t.fieldName)));for(const a of t)null==(n=this.form)||n.setFieldValue(a.fieldName,void 0)}}batchStore(e){this.store.batch(e)}getLatestSubmissionValues(){return this.latestSubmissionValues||{}}getState(){return this.state}async getValues(){return(await this.getForm()).values}merge(e){const t=[this,e],n=new Proxy(e,{get:(e,a)=>"merge"===a?e=>(t.push(e),n):"submitAllForm"===a?async(e=!0)=>{try{const n=await Promise.all(t.map((async e=>{const t=await e.getForm();if(!(await e.validate()).valid)return;return g(t.values||{})})));if(e){return Object.assign({},...n)}return n}catch(n){console.error("Validation error:",n)}}:e[a]});return n}mount(e){this.isMounted||(Object.assign(this.form,e),this.stateHandler.setConditionTrue(),this.setLatestSubmissionValues({...g(this.form.values)}),this.isMounted=!0)}async removeSchemaByFields(e){var t;const n=new Set(e),a=((null==(t=this.state)?void 0:t.schema)??[]).filter((e=>n.has(e.fieldName)));this.setState({schema:a})}async resetForm(e,t){return(await this.getForm()).resetForm(e,t)}async resetValidate(){const e=await this.getForm();Object.keys(e.errors.value).forEach((t=>{e.setFieldError(t,void 0)}))}async setFieldValue(e,t,n){(await this.getForm()).setFieldValue(e,t,n)}setLatestSubmissionValues(e){this.latestSubmissionValues={...g(e)}}setState(e){y(e)?this.store.setState((t=>ce(e(t),t))):this.store.setState((t=>ce(e,t)))}async setValues(e,t=!0,n=!1){var a,r;const s=await this.getForm();if(!t)return void s.setValues(e,n);const i=(null==(r=null==(a=this.state)?void 0:a.schema)?void 0:r.map((e=>e.fieldName)))??[],o=b(e,i);s.setValues(o,n)}async submitForm(e){var t,n;null==e||e.preventDefault(),null==e||e.stopPropagation();const a=await this.getForm();await a.submitForm();const r=g(a.values||{});return await(null==(n=null==(t=this.state)?void 0:t.handleSubmit)?void 0:n.call(t,r)),r}unmount(){var e,t;null==(t=null==(e=this.form)?void 0:e.resetForm)||t.call(e),this.latestSubmissionValues=null,this.isMounted=!1,this.stateHandler.reset()}updateSchema(e){var t;const n=[...e];if(!n.every((e=>Reflect.has(e,"fieldName")&&e.fieldName)))return void console.error("All items in the schema array must have a valid `fieldName` property to be updated");const a=[...(null==(t=this.state)?void 0:t.schema)??[]],r={};n.forEach((e=>{e.fieldName&&(r[e.fieldName]=e)})),a.forEach(((e,t)=>{const n=r[e.fieldName];n&&(a[t]=ce(n,e))})),this.setState({schema:a})}async validate(e){const t=await this.getForm(),n=await t.validate(e);return Object.keys((null==n?void 0:n.errors)??{}).length>0&&console.error("validate error",null==n?void 0:n.errors),n}async validateAndSubmitForm(){const e=await this.getForm(),{valid:t}=await e.validate();if(t)return await this.submitForm()}}var Lr,Fr,Nr;(Fr=Lr||(Lr={})).assertEqual=e=>e,Fr.assertIs=function(e){},Fr.assertNever=function(e){throw new Error},Fr.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},Fr.getValidEnumValues=e=>{const t=Fr.objectKeys(e).filter((t=>"number"!=typeof e[e[t]])),n={};for(const a of t)n[a]=e[a];return Fr.objectValues(n)},Fr.objectValues=e=>Fr.objectKeys(e).map((function(t){return e[t]})),Fr.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},Fr.find=(e,t)=>{for(const n of e)if(t(n))return n},Fr.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&isFinite(e)&&Math.floor(e)===e,Fr.joinValues=function(e,t=" | "){return e.map((e=>"string"==typeof e?`'${e}'`:e)).join(t)},Fr.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t,(Nr||(Nr={})).mergeShapes=(e,t)=>({...e,...t});const Er=Lr.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Ir=e=>{switch(typeof e){case"undefined":return Er.undefined;case"string":return Er.string;case"number":return isNaN(e)?Er.nan:Er.number;case"boolean":return Er.boolean;case"function":return Er.function;case"bigint":return Er.bigint;case"symbol":return Er.symbol;case"object":return Array.isArray(e)?Er.array:null===e?Er.null:e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch?Er.promise:"undefined"!=typeof Map&&e instanceof Map?Er.map:"undefined"!=typeof Set&&e instanceof Set?Er.set:"undefined"!=typeof Date&&e instanceof Date?Er.date:Er.object;default:return Er.unknown}},Pr=Lr.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class Rr extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(e){return e.message},n={_errors:[]},a=e=>{for(const r of e.issues)if("invalid_union"===r.code)r.unionErrors.map(a);else if("invalid_return_type"===r.code)a(r.returnTypeError);else if("invalid_arguments"===r.code)a(r.argumentsError);else if(0===r.path.length)n._errors.push(t(r));else{let e=n,a=0;for(;a<r.path.length;){const n=r.path[a];a===r.path.length-1?(e[n]=e[n]||{_errors:[]},e[n]._errors.push(t(r))):e[n]=e[n]||{_errors:[]},e=e[n],a++}}};return a(this),n}static assert(e){if(!(e instanceof Rr))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,Lr.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){const t={},n=[];for(const a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):n.push(e(a));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}Rr.create=e=>new Rr(e);const jr=(e,t)=>{let n;switch(e.code){case Pr.invalid_type:n=e.received===Er.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case Pr.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,Lr.jsonStringifyReplacer)}`;break;case Pr.unrecognized_keys:n=`Unrecognized key(s) in object: ${Lr.joinValues(e.keys,", ")}`;break;case Pr.invalid_union:n="Invalid input";break;case Pr.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${Lr.joinValues(e.options)}`;break;case Pr.invalid_enum_value:n=`Invalid enum value. Expected ${Lr.joinValues(e.options)}, received '${e.received}'`;break;case Pr.invalid_arguments:n="Invalid function arguments";break;case Pr.invalid_return_type:n="Invalid function return type";break;case Pr.invalid_date:n="Invalid date";break;case Pr.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:Lr.assertNever(e.validation):n="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case Pr.too_small:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case Pr.too_big:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case Pr.custom:n="Invalid input";break;case Pr.invalid_intersection_types:n="Intersection results could not be merged";break;case Pr.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case Pr.not_finite:n="Number must be finite";break;default:n=t.defaultError,Lr.assertNever(e)}return{message:n}};let Zr=jr;function Ar(){return Zr}const $r=e=>{const{data:t,path:n,errorMaps:a,issueData:r}=e,s=[...n,...r.path||[]],i={...r,path:s};if(void 0!==r.message)return{...r,path:s,message:r.message};let o="";const l=a.filter((e=>!!e)).slice().reverse();for(const c of l)o=c(i,{data:t,defaultError:o}).message;return{...r,path:s,message:o}};function Mr(e,t){const n=Ar(),a=$r({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===jr?void 0:jr].filter((e=>!!e))});e.common.issues.push(a)}class Dr{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const a of t){if("aborted"===a.status)return Vr;"dirty"===a.status&&e.dirty(),n.push(a.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const a of t){const e=await a.key,t=await a.value;n.push({key:e,value:t})}return Dr.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const a of t){const{key:t,value:r}=a;if("aborted"===t.status)return Vr;if("aborted"===r.status)return Vr;"dirty"===t.status&&e.dirty(),"dirty"===r.status&&e.dirty(),"__proto__"===t.value||void 0===r.value&&!a.alwaysSet||(n[t.value]=r.value)}return{status:e.value,value:n}}}const Vr=Object.freeze({status:"aborted"}),Wr=e=>({status:"dirty",value:e}),zr=e=>({status:"valid",value:e}),Ur=e=>"aborted"===e.status,Br=e=>"dirty"===e.status,Hr=e=>"valid"===e.status,qr=e=>"undefined"!=typeof Promise&&e instanceof Promise;function Yr(e,t,n,a){if("function"==typeof t||!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t.get(e)}function Kr(e,t,n,a,r){if("function"==typeof t||!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,n),n}var Gr,Jr,Xr,Qr;"function"==typeof SuppressedError&&SuppressedError,(Jr=Gr||(Gr={})).errToObj=e=>"string"==typeof e?{message:e}:e||{},Jr.toString=e=>"string"==typeof e?e:null==e?void 0:e.message;class es{constructor(e,t,n,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=a}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const ts=(e,t)=>{if(Hr(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new Rr(e.common.issues);return this._error=t,this._error}}};function ns(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:a,description:r}=e;if(t&&(n||a))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(t)return{errorMap:t,description:r};return{errorMap:(t,r)=>{var s,i;const{message:o}=e;return"invalid_enum_value"===t.code?{message:null!=o?o:r.defaultError}:void 0===r.data?{message:null!==(s=null!=o?o:a)&&void 0!==s?s:r.defaultError}:"invalid_type"!==t.code?{message:r.defaultError}:{message:null!==(i=null!=o?o:n)&&void 0!==i?i:r.defaultError}},description:r}}class as{get description(){return this._def.description}_getType(e){return Ir(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:Ir(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new Dr,ctx:{common:e.parent.common,data:e.data,parsedType:Ir(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(qr(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){var n;const a={common:{issues:[],async:null!==(n=null==t?void 0:t.async)&&void 0!==n&&n,contextualErrorMap:null==t?void 0:t.errorMap},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ir(e)},r=this._parseSync({data:e,path:a.path,parent:a});return ts(a,r)}"~validate"(e){var t,n;const a={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ir(e)};if(!this["~standard"].async)try{const t=this._parseSync({data:e,path:[],parent:a});return Hr(t)?{value:t.value}:{issues:a.common.issues}}catch(r){(null===(n=null===(t=null==r?void 0:r.message)||void 0===t?void 0:t.toLowerCase())||void 0===n?void 0:n.includes("encountered"))&&(this["~standard"].async=!0),a.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:a}).then((e=>Hr(e)?{value:e.value}:{issues:a.common.issues}))}async parseAsync(e,t){const n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){const n={common:{issues:[],contextualErrorMap:null==t?void 0:t.errorMap,async:!0},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ir(e)},a=this._parse({data:e,path:n.path,parent:n}),r=await(qr(a)?a:Promise.resolve(a));return ts(n,r)}refine(e,t){const n=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement(((t,a)=>{const r=e(t),s=()=>a.addIssue({code:Pr.custom,...n(t)});return"undefined"!=typeof Promise&&r instanceof Promise?r.then((e=>!!e||(s(),!1))):!!r||(s(),!1)}))}refinement(e,t){return this._refinement(((n,a)=>!!e(n)||(a.addIssue("function"==typeof t?t(n,a):t),!1)))}_refinement(e){return new ai({schema:this,typeName:hi.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return ri.create(this,this._def)}nullable(){return si.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return $s.create(this)}promise(){return ni.create(this,this._def)}or(e){return Vs.create([this,e],this._def)}and(e){return Bs.create(this,e,this._def)}transform(e){return new ai({...ns(this._def),schema:this,typeName:hi.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"==typeof e?e:()=>e;return new ii({...ns(this._def),innerType:this,defaultValue:t,typeName:hi.ZodDefault})}brand(){return new ui({typeName:hi.ZodBranded,type:this,...ns(this._def)})}catch(e){const t="function"==typeof e?e:()=>e;return new oi({...ns(this._def),innerType:this,catchValue:t,typeName:hi.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return di.create(this,e)}readonly(){return fi.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const rs=/^c[^\s-]{8,}$/i,ss=/^[0-9a-z]+$/,is=/^[0-9A-HJKMNP-TV-Z]{26}$/i,os=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,ls=/^[a-z0-9_-]{21}$/i,cs=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,us=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,ds=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let fs;const ms=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ps=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,hs=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,_s=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,vs=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,gs=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ys="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",bs=new RegExp(`^${ys}$`);function ks(e){let t="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`),t}function ws(e){let t=`${ys}T${ks(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function xs(e,t){if(!cs.test(e))return!1;try{const[n]=e.split("."),a=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),r=JSON.parse(atob(a));return"object"==typeof r&&null!==r&&(!(!r.typ||!r.alg)&&(!t||r.alg===t))}catch(n){return!1}}function Os(e,t){return!("v4"!==t&&t||!ps.test(e))||!("v6"!==t&&t||!_s.test(e))}class Cs extends as{_parse(e){this._def.coerce&&(e.data=String(e.data));if(this._getType(e)!==Er.string){const t=this._getOrReturnCtx(e);return Mr(t,{code:Pr.invalid_type,expected:Er.string,received:t.parsedType}),Vr}const t=new Dr;let n;for(const i of this._def.checks)if("min"===i.kind)e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),Mr(n,{code:Pr.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if("max"===i.kind)e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),Mr(n,{code:Pr.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if("length"===i.kind){const a=e.data.length>i.value,r=e.data.length<i.value;(a||r)&&(n=this._getOrReturnCtx(e,n),a?Mr(n,{code:Pr.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):r&&Mr(n,{code:Pr.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if("email"===i.kind)ds.test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"email",code:Pr.invalid_string,message:i.message}),t.dirty());else if("emoji"===i.kind)fs||(fs=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),fs.test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"emoji",code:Pr.invalid_string,message:i.message}),t.dirty());else if("uuid"===i.kind)os.test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"uuid",code:Pr.invalid_string,message:i.message}),t.dirty());else if("nanoid"===i.kind)ls.test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"nanoid",code:Pr.invalid_string,message:i.message}),t.dirty());else if("cuid"===i.kind)rs.test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"cuid",code:Pr.invalid_string,message:i.message}),t.dirty());else if("cuid2"===i.kind)ss.test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"cuid2",code:Pr.invalid_string,message:i.message}),t.dirty());else if("ulid"===i.kind)is.test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"ulid",code:Pr.invalid_string,message:i.message}),t.dirty());else if("url"===i.kind)try{new URL(e.data)}catch(s){n=this._getOrReturnCtx(e,n),Mr(n,{validation:"url",code:Pr.invalid_string,message:i.message}),t.dirty()}else if("regex"===i.kind){i.regex.lastIndex=0;i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"regex",code:Pr.invalid_string,message:i.message}),t.dirty())}else if("trim"===i.kind)e.data=e.data.trim();else if("includes"===i.kind)e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),Mr(n,{code:Pr.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty());else if("toLowerCase"===i.kind)e.data=e.data.toLowerCase();else if("toUpperCase"===i.kind)e.data=e.data.toUpperCase();else if("startsWith"===i.kind)e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),Mr(n,{code:Pr.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty());else if("endsWith"===i.kind)e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),Mr(n,{code:Pr.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty());else if("datetime"===i.kind){ws(i).test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{code:Pr.invalid_string,validation:"datetime",message:i.message}),t.dirty())}else if("date"===i.kind){bs.test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{code:Pr.invalid_string,validation:"date",message:i.message}),t.dirty())}else if("time"===i.kind){new RegExp(`^${ks(i)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{code:Pr.invalid_string,validation:"time",message:i.message}),t.dirty())}else"duration"===i.kind?us.test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"duration",code:Pr.invalid_string,message:i.message}),t.dirty()):"ip"===i.kind?(a=e.data,("v4"!==(r=i.version)&&r||!ms.test(a))&&("v6"!==r&&r||!hs.test(a))&&(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"ip",code:Pr.invalid_string,message:i.message}),t.dirty())):"jwt"===i.kind?xs(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"jwt",code:Pr.invalid_string,message:i.message}),t.dirty()):"cidr"===i.kind?Os(e.data,i.version)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"cidr",code:Pr.invalid_string,message:i.message}),t.dirty()):"base64"===i.kind?vs.test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"base64",code:Pr.invalid_string,message:i.message}),t.dirty()):"base64url"===i.kind?gs.test(e.data)||(n=this._getOrReturnCtx(e,n),Mr(n,{validation:"base64url",code:Pr.invalid_string,message:i.message}),t.dirty()):Lr.assertNever(i);var a,r;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement((t=>e.test(t)),{validation:t,code:Pr.invalid_string,...Gr.errToObj(n)})}_addCheck(e){return new Cs({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...Gr.errToObj(e)})}url(e){return this._addCheck({kind:"url",...Gr.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...Gr.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...Gr.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...Gr.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...Gr.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...Gr.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...Gr.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...Gr.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...Gr.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...Gr.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...Gr.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...Gr.errToObj(e)})}datetime(e){var t,n;return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t&&t,local:null!==(n=null==e?void 0:e.local)&&void 0!==n&&n,...Gr.errToObj(null==e?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,...Gr.errToObj(null==e?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...Gr.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...Gr.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:null==t?void 0:t.position,...Gr.errToObj(null==t?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...Gr.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...Gr.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...Gr.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...Gr.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...Gr.errToObj(t)})}nonempty(e){return this.min(1,Gr.errToObj(e))}trim(){return new Cs({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Cs({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Cs({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find((e=>"datetime"===e.kind))}get isDate(){return!!this._def.checks.find((e=>"date"===e.kind))}get isTime(){return!!this._def.checks.find((e=>"time"===e.kind))}get isDuration(){return!!this._def.checks.find((e=>"duration"===e.kind))}get isEmail(){return!!this._def.checks.find((e=>"email"===e.kind))}get isURL(){return!!this._def.checks.find((e=>"url"===e.kind))}get isEmoji(){return!!this._def.checks.find((e=>"emoji"===e.kind))}get isUUID(){return!!this._def.checks.find((e=>"uuid"===e.kind))}get isNANOID(){return!!this._def.checks.find((e=>"nanoid"===e.kind))}get isCUID(){return!!this._def.checks.find((e=>"cuid"===e.kind))}get isCUID2(){return!!this._def.checks.find((e=>"cuid2"===e.kind))}get isULID(){return!!this._def.checks.find((e=>"ulid"===e.kind))}get isIP(){return!!this._def.checks.find((e=>"ip"===e.kind))}get isCIDR(){return!!this._def.checks.find((e=>"cidr"===e.kind))}get isBase64(){return!!this._def.checks.find((e=>"base64"===e.kind))}get isBase64url(){return!!this._def.checks.find((e=>"base64url"===e.kind))}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function Ts(e,t){const n=(e.toString().split(".")[1]||"").length,a=(t.toString().split(".")[1]||"").length,r=n>a?n:a;return parseInt(e.toFixed(r).replace(".",""))%parseInt(t.toFixed(r).replace(".",""))/Math.pow(10,r)}Cs.create=e=>{var t;return new Cs({checks:[],typeName:hi.ZodString,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...ns(e)})};class Ss extends as{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){this._def.coerce&&(e.data=Number(e.data));if(this._getType(e)!==Er.number){const t=this._getOrReturnCtx(e);return Mr(t,{code:Pr.invalid_type,expected:Er.number,received:t.parsedType}),Vr}let t;const n=new Dr;for(const a of this._def.checks)if("int"===a.kind)Lr.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),Mr(t,{code:Pr.invalid_type,expected:"integer",received:"float",message:a.message}),n.dirty());else if("min"===a.kind){(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),Mr(t,{code:Pr.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty())}else if("max"===a.kind){(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),Mr(t,{code:Pr.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty())}else"multipleOf"===a.kind?0!==Ts(e.data,a.value)&&(t=this._getOrReturnCtx(e,t),Mr(t,{code:Pr.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):"finite"===a.kind?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),Mr(t,{code:Pr.not_finite,message:a.message}),n.dirty()):Lr.assertNever(a);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,Gr.toString(t))}gt(e,t){return this.setLimit("min",e,!1,Gr.toString(t))}lte(e,t){return this.setLimit("max",e,!0,Gr.toString(t))}lt(e,t){return this.setLimit("max",e,!1,Gr.toString(t))}setLimit(e,t,n,a){return new Ss({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:Gr.toString(a)}]})}_addCheck(e){return new Ss({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:Gr.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:Gr.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:Gr.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:Gr.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:Gr.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:Gr.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:Gr.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:Gr.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:Gr.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find((e=>"int"===e.kind||"multipleOf"===e.kind&&Lr.isInteger(e.value)))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if("finite"===n.kind||"int"===n.kind||"multipleOf"===n.kind)return!0;"min"===n.kind?(null===t||n.value>t)&&(t=n.value):"max"===n.kind&&(null===e||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Ss.create=e=>new Ss({checks:[],typeName:hi.ZodNumber,coerce:(null==e?void 0:e.coerce)||!1,...ns(e)});class Ls extends as{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch(a){return this._getInvalidInput(e)}if(this._getType(e)!==Er.bigint)return this._getInvalidInput(e);let t;const n=new Dr;for(const r of this._def.checks)if("min"===r.kind){(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),Mr(t,{code:Pr.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty())}else if("max"===r.kind){(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),Mr(t,{code:Pr.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty())}else"multipleOf"===r.kind?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),Mr(t,{code:Pr.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):Lr.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return Mr(t,{code:Pr.invalid_type,expected:Er.bigint,received:t.parsedType}),Vr}gte(e,t){return this.setLimit("min",e,!0,Gr.toString(t))}gt(e,t){return this.setLimit("min",e,!1,Gr.toString(t))}lte(e,t){return this.setLimit("max",e,!0,Gr.toString(t))}lt(e,t){return this.setLimit("max",e,!1,Gr.toString(t))}setLimit(e,t,n,a){return new Ls({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:Gr.toString(a)}]})}_addCheck(e){return new Ls({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:Gr.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:Gr.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:Gr.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:Gr.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:Gr.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}Ls.create=e=>{var t;return new Ls({checks:[],typeName:hi.ZodBigInt,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...ns(e)})};class Fs extends as{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));if(this._getType(e)!==Er.boolean){const t=this._getOrReturnCtx(e);return Mr(t,{code:Pr.invalid_type,expected:Er.boolean,received:t.parsedType}),Vr}return zr(e.data)}}Fs.create=e=>new Fs({typeName:hi.ZodBoolean,coerce:(null==e?void 0:e.coerce)||!1,...ns(e)});class Ns extends as{_parse(e){this._def.coerce&&(e.data=new Date(e.data));if(this._getType(e)!==Er.date){const t=this._getOrReturnCtx(e);return Mr(t,{code:Pr.invalid_type,expected:Er.date,received:t.parsedType}),Vr}if(isNaN(e.data.getTime())){return Mr(this._getOrReturnCtx(e),{code:Pr.invalid_date}),Vr}const t=new Dr;let n;for(const a of this._def.checks)"min"===a.kind?e.data.getTime()<a.value&&(n=this._getOrReturnCtx(e,n),Mr(n,{code:Pr.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),t.dirty()):"max"===a.kind?e.data.getTime()>a.value&&(n=this._getOrReturnCtx(e,n),Mr(n,{code:Pr.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),t.dirty()):Lr.assertNever(a);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new Ns({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:Gr.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:Gr.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}Ns.create=e=>new Ns({checks:[],coerce:(null==e?void 0:e.coerce)||!1,typeName:hi.ZodDate,...ns(e)});class Es extends as{_parse(e){if(this._getType(e)!==Er.symbol){const t=this._getOrReturnCtx(e);return Mr(t,{code:Pr.invalid_type,expected:Er.symbol,received:t.parsedType}),Vr}return zr(e.data)}}Es.create=e=>new Es({typeName:hi.ZodSymbol,...ns(e)});class Is extends as{_parse(e){if(this._getType(e)!==Er.undefined){const t=this._getOrReturnCtx(e);return Mr(t,{code:Pr.invalid_type,expected:Er.undefined,received:t.parsedType}),Vr}return zr(e.data)}}Is.create=e=>new Is({typeName:hi.ZodUndefined,...ns(e)});class Ps extends as{_parse(e){if(this._getType(e)!==Er.null){const t=this._getOrReturnCtx(e);return Mr(t,{code:Pr.invalid_type,expected:Er.null,received:t.parsedType}),Vr}return zr(e.data)}}Ps.create=e=>new Ps({typeName:hi.ZodNull,...ns(e)});class Rs extends as{constructor(){super(...arguments),this._any=!0}_parse(e){return zr(e.data)}}Rs.create=e=>new Rs({typeName:hi.ZodAny,...ns(e)});class js extends as{constructor(){super(...arguments),this._unknown=!0}_parse(e){return zr(e.data)}}js.create=e=>new js({typeName:hi.ZodUnknown,...ns(e)});class Zs extends as{_parse(e){const t=this._getOrReturnCtx(e);return Mr(t,{code:Pr.invalid_type,expected:Er.never,received:t.parsedType}),Vr}}Zs.create=e=>new Zs({typeName:hi.ZodNever,...ns(e)});class As extends as{_parse(e){if(this._getType(e)!==Er.undefined){const t=this._getOrReturnCtx(e);return Mr(t,{code:Pr.invalid_type,expected:Er.void,received:t.parsedType}),Vr}return zr(e.data)}}As.create=e=>new As({typeName:hi.ZodVoid,...ns(e)});class $s extends as{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),a=this._def;if(t.parsedType!==Er.array)return Mr(t,{code:Pr.invalid_type,expected:Er.array,received:t.parsedType}),Vr;if(null!==a.exactLength){const e=t.data.length>a.exactLength.value,r=t.data.length<a.exactLength.value;(e||r)&&(Mr(t,{code:e?Pr.too_big:Pr.too_small,minimum:r?a.exactLength.value:void 0,maximum:e?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),n.dirty())}if(null!==a.minLength&&t.data.length<a.minLength.value&&(Mr(t,{code:Pr.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),n.dirty()),null!==a.maxLength&&t.data.length>a.maxLength.value&&(Mr(t,{code:Pr.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map(((e,n)=>a.type._parseAsync(new es(t,e,t.path,n))))).then((e=>Dr.mergeArray(n,e)));const r=[...t.data].map(((e,n)=>a.type._parseSync(new es(t,e,t.path,n))));return Dr.mergeArray(n,r)}get element(){return this._def.type}min(e,t){return new $s({...this._def,minLength:{value:e,message:Gr.toString(t)}})}max(e,t){return new $s({...this._def,maxLength:{value:e,message:Gr.toString(t)}})}length(e,t){return new $s({...this._def,exactLength:{value:e,message:Gr.toString(t)}})}nonempty(e){return this.min(1,e)}}function Ms(e){if(e instanceof Ds){const t={};for(const n in e.shape){const a=e.shape[n];t[n]=ri.create(Ms(a))}return new Ds({...e._def,shape:()=>t})}return e instanceof $s?new $s({...e._def,type:Ms(e.element)}):e instanceof ri?ri.create(Ms(e.unwrap())):e instanceof si?si.create(Ms(e.unwrap())):e instanceof Hs?Hs.create(e.items.map((e=>Ms(e)))):e}$s.create=(e,t)=>new $s({type:e,minLength:null,maxLength:null,exactLength:null,typeName:hi.ZodArray,...ns(t)});class Ds extends as{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const e=this._def.shape(),t=Lr.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==Er.object){const t=this._getOrReturnCtx(e);return Mr(t,{code:Pr.invalid_type,expected:Er.object,received:t.parsedType}),Vr}const{status:t,ctx:n}=this._processInputParams(e),{shape:a,keys:r}=this._getCached(),s=[];if(!(this._def.catchall instanceof Zs&&"strip"===this._def.unknownKeys))for(const o in n.data)r.includes(o)||s.push(o);const i=[];for(const o of r){const e=a[o],t=n.data[o];i.push({key:{status:"valid",value:o},value:e._parse(new es(n,t,n.path,o)),alwaysSet:o in n.data})}if(this._def.catchall instanceof Zs){const e=this._def.unknownKeys;if("passthrough"===e)for(const t of s)i.push({key:{status:"valid",value:t},value:{status:"valid",value:n.data[t]}});else if("strict"===e)s.length>0&&(Mr(n,{code:Pr.unrecognized_keys,keys:s}),t.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of s){const a=n.data[t];i.push({key:{status:"valid",value:t},value:e._parse(new es(n,a,n.path,t)),alwaysSet:t in n.data})}}return n.common.async?Promise.resolve().then((async()=>{const e=[];for(const t of i){const n=await t.key,a=await t.value;e.push({key:n,value:a,alwaysSet:t.alwaysSet})}return e})).then((e=>Dr.mergeObjectSync(t,e))):Dr.mergeObjectSync(t,i)}get shape(){return this._def.shape()}strict(e){return Gr.errToObj,new Ds({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,n)=>{var a,r,s,i;const o=null!==(s=null===(r=(a=this._def).errorMap)||void 0===r?void 0:r.call(a,t,n).message)&&void 0!==s?s:n.defaultError;return"unrecognized_keys"===t.code?{message:null!==(i=Gr.errToObj(e).message)&&void 0!==i?i:o}:{message:o}}}:{}})}strip(){return new Ds({...this._def,unknownKeys:"strip"})}passthrough(){return new Ds({...this._def,unknownKeys:"passthrough"})}extend(e){return new Ds({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new Ds({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:hi.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new Ds({...this._def,catchall:e})}pick(e){const t={};return Lr.objectKeys(e).forEach((n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])})),new Ds({...this._def,shape:()=>t})}omit(e){const t={};return Lr.objectKeys(this.shape).forEach((n=>{e[n]||(t[n]=this.shape[n])})),new Ds({...this._def,shape:()=>t})}deepPartial(){return Ms(this)}partial(e){const t={};return Lr.objectKeys(this.shape).forEach((n=>{const a=this.shape[n];e&&!e[n]?t[n]=a:t[n]=a.optional()})),new Ds({...this._def,shape:()=>t})}required(e){const t={};return Lr.objectKeys(this.shape).forEach((n=>{if(e&&!e[n])t[n]=this.shape[n];else{let e=this.shape[n];for(;e instanceof ri;)e=e._def.innerType;t[n]=e}})),new Ds({...this._def,shape:()=>t})}keyof(){return Qs(Lr.objectKeys(this.shape))}}Ds.create=(e,t)=>new Ds({shape:()=>e,unknownKeys:"strip",catchall:Zs.create(),typeName:hi.ZodObject,...ns(t)}),Ds.strictCreate=(e,t)=>new Ds({shape:()=>e,unknownKeys:"strict",catchall:Zs.create(),typeName:hi.ZodObject,...ns(t)}),Ds.lazycreate=(e,t)=>new Ds({shape:e,unknownKeys:"strip",catchall:Zs.create(),typeName:hi.ZodObject,...ns(t)});class Vs extends as{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map((async e=>{const n={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:n}),ctx:n}}))).then((function(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const a of e)if("dirty"===a.result.status)return t.common.issues.push(...a.ctx.common.issues),a.result;const n=e.map((e=>new Rr(e.ctx.common.issues)));return Mr(t,{code:Pr.invalid_union,unionErrors:n}),Vr}));{let e;const a=[];for(const s of n){const n={...t,common:{...t.common,issues:[]},parent:null},r=s._parseSync({data:t.data,path:t.path,parent:n});if("valid"===r.status)return r;"dirty"!==r.status||e||(e={result:r,ctx:n}),n.common.issues.length&&a.push(n.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const r=a.map((e=>new Rr(e)));return Mr(t,{code:Pr.invalid_union,unionErrors:r}),Vr}}get options(){return this._def.options}}Vs.create=(e,t)=>new Vs({options:e,typeName:hi.ZodUnion,...ns(t)});const Ws=e=>e instanceof Js?Ws(e.schema):e instanceof ai?Ws(e.innerType()):e instanceof Xs?[e.value]:e instanceof ei?e.options:e instanceof ti?Lr.objectValues(e.enum):e instanceof ii?Ws(e._def.innerType):e instanceof Is?[void 0]:e instanceof Ps?[null]:e instanceof ri?[void 0,...Ws(e.unwrap())]:e instanceof si?[null,...Ws(e.unwrap())]:e instanceof ui||e instanceof fi?Ws(e.unwrap()):e instanceof oi?Ws(e._def.innerType):[];class zs extends as{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==Er.object)return Mr(t,{code:Pr.invalid_type,expected:Er.object,received:t.parsedType}),Vr;const n=this.discriminator,a=t.data[n],r=this.optionsMap.get(a);return r?t.common.async?r._parseAsync({data:t.data,path:t.path,parent:t}):r._parseSync({data:t.data,path:t.path,parent:t}):(Mr(t,{code:Pr.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),Vr)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const a=new Map;for(const r of t){const t=Ws(r.shape[e]);if(!t.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const n of t){if(a.has(n))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(n)}`);a.set(n,r)}}return new zs({typeName:hi.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...ns(n)})}}function Us(e,t){const n=Ir(e),a=Ir(t);if(e===t)return{valid:!0,data:e};if(n===Er.object&&a===Er.object){const n=Lr.objectKeys(t),a=Lr.objectKeys(e).filter((e=>-1!==n.indexOf(e))),r={...e,...t};for(const s of a){const n=Us(e[s],t[s]);if(!n.valid)return{valid:!1};r[s]=n.data}return{valid:!0,data:r}}if(n===Er.array&&a===Er.array){if(e.length!==t.length)return{valid:!1};const n=[];for(let a=0;a<e.length;a++){const r=Us(e[a],t[a]);if(!r.valid)return{valid:!1};n.push(r.data)}return{valid:!0,data:n}}return n===Er.date&&a===Er.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class Bs extends as{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),a=(e,a)=>{if(Ur(e)||Ur(a))return Vr;const r=Us(e.value,a.value);return r.valid?((Br(e)||Br(a))&&t.dirty(),{status:t.value,value:r.data}):(Mr(n,{code:Pr.invalid_intersection_types}),Vr)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then((([e,t])=>a(e,t))):a(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Bs.create=(e,t,n)=>new Bs({left:e,right:t,typeName:hi.ZodIntersection,...ns(n)});class Hs extends as{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==Er.array)return Mr(n,{code:Pr.invalid_type,expected:Er.array,received:n.parsedType}),Vr;if(n.data.length<this._def.items.length)return Mr(n,{code:Pr.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),Vr;!this._def.rest&&n.data.length>this._def.items.length&&(Mr(n,{code:Pr.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const a=[...n.data].map(((e,t)=>{const a=this._def.items[t]||this._def.rest;return a?a._parse(new es(n,e,n.path,t)):null})).filter((e=>!!e));return n.common.async?Promise.all(a).then((e=>Dr.mergeArray(t,e))):Dr.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new Hs({...this._def,rest:e})}}Hs.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Hs({items:e,typeName:hi.ZodTuple,rest:null,...ns(t)})};class qs extends as{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==Er.object)return Mr(n,{code:Pr.invalid_type,expected:Er.object,received:n.parsedType}),Vr;const a=[],r=this._def.keyType,s=this._def.valueType;for(const i in n.data)a.push({key:r._parse(new es(n,i,n.path,i)),value:s._parse(new es(n,n.data[i],n.path,i)),alwaysSet:i in n.data});return n.common.async?Dr.mergeObjectAsync(t,a):Dr.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,n){return new qs(t instanceof as?{keyType:e,valueType:t,typeName:hi.ZodRecord,...ns(n)}:{keyType:Cs.create(),valueType:e,typeName:hi.ZodRecord,...ns(t)})}}class Ys extends as{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==Er.map)return Mr(n,{code:Pr.invalid_type,expected:Er.map,received:n.parsedType}),Vr;const a=this._def.keyType,r=this._def.valueType,s=[...n.data.entries()].map((([e,t],s)=>({key:a._parse(new es(n,e,n.path,[s,"key"])),value:r._parse(new es(n,t,n.path,[s,"value"]))})));if(n.common.async){const e=new Map;return Promise.resolve().then((async()=>{for(const n of s){const a=await n.key,r=await n.value;if("aborted"===a.status||"aborted"===r.status)return Vr;"dirty"!==a.status&&"dirty"!==r.status||t.dirty(),e.set(a.value,r.value)}return{status:t.value,value:e}}))}{const e=new Map;for(const n of s){const a=n.key,r=n.value;if("aborted"===a.status||"aborted"===r.status)return Vr;"dirty"!==a.status&&"dirty"!==r.status||t.dirty(),e.set(a.value,r.value)}return{status:t.value,value:e}}}}Ys.create=(e,t,n)=>new Ys({valueType:t,keyType:e,typeName:hi.ZodMap,...ns(n)});class Ks extends as{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==Er.set)return Mr(n,{code:Pr.invalid_type,expected:Er.set,received:n.parsedType}),Vr;const a=this._def;null!==a.minSize&&n.data.size<a.minSize.value&&(Mr(n,{code:Pr.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),null!==a.maxSize&&n.data.size>a.maxSize.value&&(Mr(n,{code:Pr.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());const r=this._def.valueType;function s(e){const n=new Set;for(const a of e){if("aborted"===a.status)return Vr;"dirty"===a.status&&t.dirty(),n.add(a.value)}return{status:t.value,value:n}}const i=[...n.data.values()].map(((e,t)=>r._parse(new es(n,e,n.path,t))));return n.common.async?Promise.all(i).then((e=>s(e))):s(i)}min(e,t){return new Ks({...this._def,minSize:{value:e,message:Gr.toString(t)}})}max(e,t){return new Ks({...this._def,maxSize:{value:e,message:Gr.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}Ks.create=(e,t)=>new Ks({valueType:e,minSize:null,maxSize:null,typeName:hi.ZodSet,...ns(t)});class Gs extends as{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==Er.function)return Mr(t,{code:Pr.invalid_type,expected:Er.function,received:t.parsedType}),Vr;function n(e,n){return $r({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,Ar(),jr].filter((e=>!!e)),issueData:{code:Pr.invalid_arguments,argumentsError:n}})}function a(e,n){return $r({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,Ar(),jr].filter((e=>!!e)),issueData:{code:Pr.invalid_return_type,returnTypeError:n}})}const r={errorMap:t.common.contextualErrorMap},s=t.data;if(this._def.returns instanceof ni){const e=this;return zr((async function(...t){const i=new Rr([]),o=await e._def.args.parseAsync(t,r).catch((e=>{throw i.addIssue(n(t,e)),i})),l=await Reflect.apply(s,this,o);return await e._def.returns._def.type.parseAsync(l,r).catch((e=>{throw i.addIssue(a(l,e)),i}))}))}{const e=this;return zr((function(...t){const i=e._def.args.safeParse(t,r);if(!i.success)throw new Rr([n(t,i.error)]);const o=Reflect.apply(s,this,i.data),l=e._def.returns.safeParse(o,r);if(!l.success)throw new Rr([a(o,l.error)]);return l.data}))}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new Gs({...this._def,args:Hs.create(e).rest(js.create())})}returns(e){return new Gs({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new Gs({args:e||Hs.create([]).rest(js.create()),returns:t||js.create(),typeName:hi.ZodFunction,...ns(n)})}}class Js extends as{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}Js.create=(e,t)=>new Js({getter:e,typeName:hi.ZodLazy,...ns(t)});class Xs extends as{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return Mr(t,{received:t.data,code:Pr.invalid_literal,expected:this._def.value}),Vr}return{status:"valid",value:e.data}}get value(){return this._def.value}}function Qs(e,t){return new ei({values:e,typeName:hi.ZodEnum,...ns(t)})}Xs.create=(e,t)=>new Xs({value:e,typeName:hi.ZodLiteral,...ns(t)});class ei extends as{constructor(){super(...arguments),Xr.set(this,void 0)}_parse(e){if("string"!=typeof e.data){const t=this._getOrReturnCtx(e),n=this._def.values;return Mr(t,{expected:Lr.joinValues(n),received:t.parsedType,code:Pr.invalid_type}),Vr}if(Yr(this,Xr)||Kr(this,Xr,new Set(this._def.values)),!Yr(this,Xr).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return Mr(t,{received:t.data,code:Pr.invalid_enum_value,options:n}),Vr}return zr(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return ei.create(e,{...this._def,...t})}exclude(e,t=this._def){return ei.create(this.options.filter((t=>!e.includes(t))),{...this._def,...t})}}Xr=new WeakMap,ei.create=Qs;class ti extends as{constructor(){super(...arguments),Qr.set(this,void 0)}_parse(e){const t=Lr.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==Er.string&&n.parsedType!==Er.number){const e=Lr.objectValues(t);return Mr(n,{expected:Lr.joinValues(e),received:n.parsedType,code:Pr.invalid_type}),Vr}if(Yr(this,Qr)||Kr(this,Qr,new Set(Lr.getValidEnumValues(this._def.values))),!Yr(this,Qr).has(e.data)){const e=Lr.objectValues(t);return Mr(n,{received:n.data,code:Pr.invalid_enum_value,options:e}),Vr}return zr(e.data)}get enum(){return this._def.values}}Qr=new WeakMap,ti.create=(e,t)=>new ti({values:e,typeName:hi.ZodNativeEnum,...ns(t)});class ni extends as{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==Er.promise&&!1===t.common.async)return Mr(t,{code:Pr.invalid_type,expected:Er.promise,received:t.parsedType}),Vr;const n=t.parsedType===Er.promise?t.data:Promise.resolve(t.data);return zr(n.then((e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap}))))}}ni.create=(e,t)=>new ni({type:e,typeName:hi.ZodPromise,...ns(t)});class ai extends as{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===hi.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),a=this._def.effect||null,r={addIssue:e=>{Mr(n,e),e.fatal?t.abort():t.dirty()},get path(){return n.path}};if(r.addIssue=r.addIssue.bind(r),"preprocess"===a.type){const e=a.transform(n.data,r);if(n.common.async)return Promise.resolve(e).then((async e=>{if("aborted"===t.value)return Vr;const a=await this._def.schema._parseAsync({data:e,path:n.path,parent:n});return"aborted"===a.status?Vr:"dirty"===a.status||"dirty"===t.value?Wr(a.value):a}));{if("aborted"===t.value)return Vr;const a=this._def.schema._parseSync({data:e,path:n.path,parent:n});return"aborted"===a.status?Vr:"dirty"===a.status||"dirty"===t.value?Wr(a.value):a}}if("refinement"===a.type){const e=e=>{const t=a.refinement(e,r);if(n.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===n.common.async){const a=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===a.status?Vr:("dirty"===a.status&&t.dirty(),e(a.value),{status:t.value,value:a.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then((n=>"aborted"===n.status?Vr:("dirty"===n.status&&t.dirty(),e(n.value).then((()=>({status:t.value,value:n.value}))))))}if("transform"===a.type){if(!1===n.common.async){const e=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Hr(e))return e;const s=a.transform(e.value,r);if(s instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then((e=>Hr(e)?Promise.resolve(a.transform(e.value,r)).then((e=>({status:t.value,value:e}))):e))}Lr.assertNever(a)}}ai.create=(e,t,n)=>new ai({schema:e,typeName:hi.ZodEffects,effect:t,...ns(n)}),ai.createWithPreprocess=(e,t,n)=>new ai({schema:t,effect:{type:"preprocess",transform:e},typeName:hi.ZodEffects,...ns(n)});class ri extends as{_parse(e){return this._getType(e)===Er.undefined?zr(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ri.create=(e,t)=>new ri({innerType:e,typeName:hi.ZodOptional,...ns(t)});class si extends as{_parse(e){return this._getType(e)===Er.null?zr(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}si.create=(e,t)=>new si({innerType:e,typeName:hi.ZodNullable,...ns(t)});class ii extends as{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===Er.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}ii.create=(e,t)=>new ii({innerType:e,typeName:hi.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...ns(t)});class oi extends as{_parse(e){const{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return qr(a)?a.then((e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new Rr(n.common.issues)},input:n.data})}))):{status:"valid",value:"valid"===a.status?a.value:this._def.catchValue({get error(){return new Rr(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}oi.create=(e,t)=>new oi({innerType:e,typeName:hi.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...ns(t)});class li extends as{_parse(e){if(this._getType(e)!==Er.nan){const t=this._getOrReturnCtx(e);return Mr(t,{code:Pr.invalid_type,expected:Er.nan,received:t.parsedType}),Vr}return{status:"valid",value:e.data}}}li.create=e=>new li({typeName:hi.ZodNaN,...ns(e)});const ci=Symbol("zod_brand");class ui extends as{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class di extends as{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async){return(async()=>{const e=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?Vr:"dirty"===e.status?(t.dirty(),Wr(e.value)):this._def.out._parseAsync({data:e.value,path:n.path,parent:n})})()}{const e=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?Vr:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:n.path,parent:n})}}static create(e,t){return new di({in:e,out:t,typeName:hi.ZodPipeline})}}class fi extends as{_parse(e){const t=this._def.innerType._parse(e),n=e=>(Hr(e)&&(e.value=Object.freeze(e.value)),e);return qr(t)?t.then((e=>n(e))):n(t)}unwrap(){return this._def.innerType}}function mi(e,t={},n){return e?Rs.create().superRefine(((a,r)=>{var s,i;if(!e(a)){const e="function"==typeof t?t(a):"string"==typeof t?{message:t}:t,o=null===(i=null!==(s=e.fatal)&&void 0!==s?s:n)||void 0===i||i,l="string"==typeof e?{message:e}:e;r.addIssue({code:"custom",...l,fatal:o})}})):Rs.create()}fi.create=(e,t)=>new fi({innerType:e,typeName:hi.ZodReadonly,...ns(t)});const pi={object:Ds.lazycreate};var hi,_i;(_i=hi||(hi={})).ZodString="ZodString",_i.ZodNumber="ZodNumber",_i.ZodNaN="ZodNaN",_i.ZodBigInt="ZodBigInt",_i.ZodBoolean="ZodBoolean",_i.ZodDate="ZodDate",_i.ZodSymbol="ZodSymbol",_i.ZodUndefined="ZodUndefined",_i.ZodNull="ZodNull",_i.ZodAny="ZodAny",_i.ZodUnknown="ZodUnknown",_i.ZodNever="ZodNever",_i.ZodVoid="ZodVoid",_i.ZodArray="ZodArray",_i.ZodObject="ZodObject",_i.ZodUnion="ZodUnion",_i.ZodDiscriminatedUnion="ZodDiscriminatedUnion",_i.ZodIntersection="ZodIntersection",_i.ZodTuple="ZodTuple",_i.ZodRecord="ZodRecord",_i.ZodMap="ZodMap",_i.ZodSet="ZodSet",_i.ZodFunction="ZodFunction",_i.ZodLazy="ZodLazy",_i.ZodLiteral="ZodLiteral",_i.ZodEnum="ZodEnum",_i.ZodEffects="ZodEffects",_i.ZodNativeEnum="ZodNativeEnum",_i.ZodOptional="ZodOptional",_i.ZodNullable="ZodNullable",_i.ZodDefault="ZodDefault",_i.ZodCatch="ZodCatch",_i.ZodPromise="ZodPromise",_i.ZodBranded="ZodBranded",_i.ZodPipeline="ZodPipeline",_i.ZodReadonly="ZodReadonly";const vi=Cs.create,gi=Ss.create,yi=li.create,bi=Ls.create,ki=Fs.create,wi=Ns.create,xi=Es.create,Oi=Is.create,Ci=Ps.create,Ti=Rs.create,Si=js.create,Li=Zs.create,Fi=As.create,Ni=$s.create,Ei=Ds.create,Ii=Ds.strictCreate,Pi=Vs.create,Ri=zs.create,ji=Bs.create,Zi=Hs.create,Ai=qs.create,$i=Ys.create,Mi=Ks.create,Di=Gs.create,Vi=Js.create,Wi=Xs.create,zi=ei.create,Ui=ti.create,Bi=ni.create,Hi=ai.create,qi=ri.create,Yi=si.create,Ki=ai.createWithPreprocess,Gi=di.create,Ji={string:e=>Cs.create({...e,coerce:!0}),number:e=>Ss.create({...e,coerce:!0}),boolean:e=>Fs.create({...e,coerce:!0}),bigint:e=>Ls.create({...e,coerce:!0}),date:e=>Ns.create({...e,coerce:!0})},Xi=Vr;var Qi=Object.freeze({__proto__:null,defaultErrorMap:jr,setErrorMap:function(e){Zr=e},getErrorMap:Ar,makeIssue:$r,EMPTY_PATH:[],addIssueToContext:Mr,ParseStatus:Dr,INVALID:Vr,DIRTY:Wr,OK:zr,isAborted:Ur,isDirty:Br,isValid:Hr,isAsync:qr,get util(){return Lr},get objectUtil(){return Nr},ZodParsedType:Er,getParsedType:Ir,ZodType:as,datetimeRegex:ws,ZodString:Cs,ZodNumber:Ss,ZodBigInt:Ls,ZodBoolean:Fs,ZodDate:Ns,ZodSymbol:Es,ZodUndefined:Is,ZodNull:Ps,ZodAny:Rs,ZodUnknown:js,ZodNever:Zs,ZodVoid:As,ZodArray:$s,ZodObject:Ds,ZodUnion:Vs,ZodDiscriminatedUnion:zs,ZodIntersection:Bs,ZodTuple:Hs,ZodRecord:qs,ZodMap:Ys,ZodSet:Ks,ZodFunction:Gs,ZodLazy:Js,ZodLiteral:Xs,ZodEnum:ei,ZodNativeEnum:ti,ZodPromise:ni,ZodEffects:ai,ZodTransformer:ai,ZodOptional:ri,ZodNullable:si,ZodDefault:ii,ZodCatch:oi,ZodNaN:li,BRAND:ci,ZodBranded:ui,ZodPipeline:di,ZodReadonly:fi,custom:mi,Schema:as,ZodSchema:as,late:pi,get ZodFirstPartyTypeKind(){return hi},coerce:Ji,any:Ti,array:Ni,bigint:bi,boolean:ki,date:wi,discriminatedUnion:Ri,effect:Hi,enum:zi,function:Di,instanceof:(e,t={message:`Input not instance of ${e.name}`})=>mi((t=>t instanceof e),t),intersection:ji,lazy:Vi,literal:Wi,map:$i,nan:yi,nativeEnum:Ui,never:Li,null:Ci,nullable:Yi,number:gi,object:Ei,oboolean:()=>ki().optional(),onumber:()=>gi().optional(),optional:qi,ostring:()=>vi().optional(),pipeline:Gi,preprocess:Ki,promise:Bi,record:Ai,set:Mi,strictObject:Ii,string:vi,symbol:xi,transformer:Hi,tuple:Zi,undefined:Oi,union:Pi,unknown:Si,void:Fi,NEVER:Xi,ZodIssueCode:Pr,quotelessJson:e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:Rr});const eo=(e,t)=>e.constructor.name===t.name,to=new Map;function no(e){const t=e.constructor.name;if(to.has(t))return to.get(t)(e);console.warn("getSchemaDefaultForField: Unhandled type",e.constructor.name)}function ao(e){if(eo(e,Qi.ZodRecord))return{};if(eo(e,Qi.ZodEffects))return ao(e._def.schema);if(eo(e,Qi.ZodIntersection))return{...ao(e._def.left),...ao(e._def.right)};if(eo(e,Qi.ZodUnion)){for(const t of e._def.options)if(eo(t,Qi.ZodObject))return ao(t);return console.warn("getSchemaDefaultObject: No object found in union, returning empty object"),{}}return eo(e,Qi.ZodObject)?Object.fromEntries(Object.entries(e.shape).map((([e,t])=>[e,no(t)])).filter((e=>void 0!==e[1]))):(console.warn(`getSchemaDefaultObject: Expected object schema, got ${e.constructor.name}`),{})}to.set(Qi.ZodBoolean.name,(()=>!1)),to.set(Qi.ZodNumber.name,(()=>0)),to.set(Qi.ZodString.name,(()=>"")),to.set(Qi.ZodArray.name,(()=>[])),to.set(Qi.ZodRecord.name,(()=>({}))),to.set(Qi.ZodDefault.name,(e=>e._def.defaultValue())),to.set(Qi.ZodEffects.name,(e=>no(e._def.schema))),to.set(Qi.ZodOptional.name,(e=>eo(e._def.innerType,Qi.ZodDefault)?e._def.innerType._def.defaultValue():void 0)),to.set(Qi.ZodTuple.name,(e=>{const t=[];for(const n of e._def.items)t.push(no(n));return t})),to.set(Qi.ZodEffects.name,(e=>no(e._def.schema))),to.set(Qi.ZodUnion.name,(e=>no(e._def.options[0]))),to.set(Qi.ZodObject.name,(e=>ao(e))),to.set(Qi.ZodRecord.name,(e=>ao(e))),to.set(Qi.ZodIntersection.name,(e=>ao(e)));const[ro,so]=ue("VbenFormProps");function io(e){var t;const n=k(),a=function(){const t={},n={};(w(e).schema||[]).forEach((e=>{Reflect.has(e,"defaultValue")?t[e.fieldName]=e.defaultValue:e.rules&&!x(e.rules)&&(n[e.fieldName]=e.rules)}));const a=(r=Ei(n),ao(r));var r;return{...t,...a}}(),r=de({...(null==(t=Object.keys(a))?void 0:t.length)?{initialValues:a}:{}});return{delegatedSlots:o((()=>{const e=[];for(const t of Object.keys(n))"default"!==t&&e.push(t);return e})),form:r}}const oo=n({__name:"form-actions",props:{modelValue:{default:!1},modelModifiers:{}},emits:["update:modelValue"],setup(e,{expose:t}){const{$t:n}=Q(),[a,r]=ro(),s=O(e,"modelValue"),i=o((()=>({content:`${n.value("reset")}`,show:!0,...w(a).resetButtonOptions}))),c=o((()=>({content:`${n.value("submit")}`,show:!0,...w(a).submitButtonOptions}))),u=o((()=>w(a).actionWrapperClass?{}:{"grid-column":"-2 / -1",marginLeft:"auto"}));async function d(e){var t,n;null==e||e.preventDefault(),null==e||e.stopPropagation();const{valid:s}=await r.validate();if(!s)return;const i=function(e){const t=w(a).fieldMappingTime;if(!t||!Array.isArray(t))return e;return t.forEach((([t,[n,a],r="YYYY-MM-DD"])=>{if(!e[t])return void delete e[t];const[s,i]=e[t],[o,l]=Array.isArray(r)?r:[r,r];e[n]=s?he(s,o):void 0,e[a]=i?he(i,l):void 0,delete e[t]})),e}(g(r.values));await(null==(n=(t=w(a)).handleSubmit)?void 0:n.call(t,i))}async function f(e){var t;null==e||e.preventDefault(),null==e||e.stopPropagation();const n=w(a),s=g(r.values);n.fieldMappingTime&&n.fieldMappingTime.forEach((([e,[t,n]])=>{delete s[t],delete s[n]})),y(n.handleReset)?await(null==(t=n.handleReset)?void 0:t.call(n,s)):r.resetForm()}return l((()=>s.value),(()=>{w(a).collapseTriggerResize&&fe()})),t({handleReset:f,handleSubmit:d}),(e,t)=>(C(),T("div",{class:Z(w(pe)("col-span-full w-full pb-6 text-right",w(a).actionWrapperClass)),style:A(u.value)},[S(e.$slots,"reset-before"),i.value.show?(C(),L(P(w(Cr).DefaultButton),I({key:0,class:"mr-3",type:"button",onClick:f},i.value),{default:F((()=>[N(E(i.value.content),1)])),_:1},16)):R("",!0),S(e.$slots,"submit-before"),c.value.show?(C(),L(P(w(Cr).PrimaryButton),I({key:1,type:"button",onClick:d},c.value),{default:F((()=>[N(E(c.value.content),1)])),_:1},16)):R("",!0),S(e.$slots,"expand-before"),w(a).showCollapseButton?(C(),L(w(me),{key:2,"model-value":s.value,"onUpdate:modelValue":t[0]||(t[0]=e=>s.value=e),class:"ml-2"},{default:F((()=>[j("span",null,E(s.value?w(n)("expand"):w(n)("collapse")),1)])),_:1},8,["model-value"])):R("",!0),S(e.$slots,"expand-after")],6))}}),[lo,co]=ue("FormRenderProps");
/**
  * vee-validate v4.15.0
  * (c) 2024 Abdelrahman Awad
  * @license MIT
  */
const uo=e=>null!==e&&!!e&&"object"==typeof e&&!Array.isArray(e);function fo(e){return Number(e)>=0}function mo(e){if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function po(e,t){return Object.keys(t).forEach((n=>{if(mo(t[n])&&mo(e[n]))return e[n]||(e[n]={}),void po(e[n],t[n]);e[n]=t[n]})),e}function ho(e,t){return{__type:"VVTypedSchema",async parse(n){const a=await e.safeParseAsync(n,t);if(a.success)return{value:a.data,errors:[]};const r={};return _o(a.error.issues,r),{errors:Object.values(r)}},cast(t){try{return e.parse(t)}catch(n){const a=vo(e);return uo(a)&&uo(t)?po(a,t):t}},describe(t){try{if(!t)return{required:!e.isOptional(),exists:!0};const n=function(e,t){if(!bo(t))return null;if(_e(e))return t.shape[ve(e)];const n=(e||"").split(/\.|\[(\d+)\]/).filter(Boolean);let a=t;for(let r=0;r<=n.length;r++){const e=n[r];if(!e||!a)return a;bo(a)?a=a.shape[e]||null:fo(e)&&yo(a)&&(a=a._def.type)}return null}(t,e);return n?{required:!n.isOptional(),exists:!0}:{required:!1,exists:!1}}catch(n){return{required:!1,exists:!1}}}}}function _o(e,t){e.forEach((e=>{const n=function(e){const t=e.split(".");if(!t.length)return"";let n=String(t[0]);for(let a=1;a<t.length;a++)fo(t[a])?n+=`[${t[a]}]`:n+=`.${t[a]}`;return n}(e.path.join("."));("invalid_union"!==e.code||(_o(e.unionErrors.flatMap((e=>e.issues)),t),n))&&(t[n]||(t[n]={errors:[],path:n}),t[n].errors.push(e.message))}))}function vo(e){if(e instanceof Ds)return Object.fromEntries(Object.entries(e.shape).map((([e,t])=>t instanceof ii?[e,t._def.defaultValue()]:t instanceof Ds?[e,vo(t)]:[e,void 0])))}function go(e){return e._def.typeName}function yo(e){return go(e)===hi.ZodArray}function bo(e){return go(e)===hi.ZodObject}function ko(e){const t=ge(),n=lo().form;if(!t)throw new Error("useDependencies should be used within <VbenForm>");const a=m(!0),r=m(!1),s=m(!0),i=m(!1),c=m({}),u=m(),d=o((()=>{var n;return((null==(n=e())?void 0:n.triggerFields)??[]).map((e=>t.value[e]))}));return l([d,e],(async([e,o])=>{var l;if(!o||!(null==(l=null==o?void 0:o.triggerFields)?void 0:l.length))return;r.value=!1,a.value=!0,s.value=!0,i.value=!1,u.value=void 0,c.value={};const{componentProps:d,disabled:f,if:m,required:p,rules:h,show:_,trigger:v}=o,g=t.value;if(y(m)){if(a.value=!!(await m(g,n)),!a.value)return}else if(ye(m)&&(a.value=m,!a.value))return;if(y(_)){if(s.value=!!(await _(g,n)),!s.value)return}else if(ye(_)&&(s.value=_,!s.value))return;y(d)&&(c.value=await d(g,n)),y(h)&&(u.value=await h(g,n)),y(f)?r.value=!!(await f(g,n)):ye(f)&&(r.value=f),y(p)&&(i.value=!!(await p(g,n))),y(v)&&await v(g,n)}),{deep:!0,immediate:!0}),{dynamicComponentProps:c,dynamicRules:u,isDisabled:r,isIf:a,isRequired:i,isShow:s}}const wo={key:0,class:"text-destructive mr-[2px]"},xo=n({__name:"form-label",props:{class:{},help:{},required:{type:Boolean}},setup(e){const t=e;return(e,n)=>(C(),L(w(ke),{class:Z(w(pe)("flex items-center",t.class))},{default:F((()=>[e.required?(C(),T("span",wo,"*")):R("",!0),S(e.$slots,"default"),e.help?(C(),L(w(be),{key:1,"trigger-class":"size-3.5 ml-1"},{default:F((()=>[N(E(e.help),1)])),_:1})):R("",!0)])),_:3},8,["class"]))}});function Oo(e){return!e||x(e)?null:"innerType"in e._def?Oo(e._def.innerType):"schema"in e._def?Oo(e._def.schema):e}function Co(e){if(!e||x(e))return;const t=e;return"ZodDefault"===t._def.typeName?t._def.defaultValue():"innerType"in t._def?Co(t._def.innerType):"schema"in t._def?Co(t._def.schema):void 0}function To(e){return!(!e||!W(e))&&(Reflect.has(e,"target")&&Reflect.has(e,"stopPropagation"))}const So={key:0,class:"ml-1"},Lo=n({__name:"form-field",props:{commonComponentProps:{},component:{},componentProps:{type:Function},defaultValue:{},dependencies:{},description:{},fieldName:{},help:{},label:{},renderComponentContent:{type:Function},rules:{},suffix:{type:[Function,String]},controlClass:{},disabled:{type:Boolean},disabledOnChangeListener:{type:Boolean},emptyStateValue:{},formFieldProps:{},formItemClass:{},hideLabel:{type:Boolean},hideRequiredMark:{type:Boolean},labelClass:{},labelWidth:{},wrapperClass:{}},setup(e){const{componentBindEventMap:t,componentMap:n,isVertical:a}=(()=>{const e=lo(),t=o((()=>"vertical"===e.layout)),n=o((()=>e.componentMap));return{componentBindEventMap:o((()=>e.componentBindEventMap)),componentMap:n,isVertical:t}})(),r=lo(),s=ge(),i=we(e.fieldName),c=$("fieldComponentRef"),u=r.form,d=o((()=>{var e;return(null==(e=i.value)?void 0:e.length)>0})),f=o((()=>{const t=x(e.component)?n.value[e.component]:e.component;return t||console.warn(`Component ${e.component} is not registered`),t})),{dynamicComponentProps:m,dynamicRules:p,isDisabled:_,isIf:v,isRequired:g,isShow:b}=ko((()=>e.dependencies)),k=o((()=>{var t;return(null==(t=e.labelClass)?void 0:t.includes("w-"))||a.value?{}:{width:`${e.labelWidth}px`}})),O=o((()=>p.value||e.rules)),M=o((()=>v.value&&b.value)),D=o((()=>{var e,t,n,a,r,s;if(!M.value)return!1;if(!O.value)return g.value;if(g.value)return!0;if(x(O.value))return["required","selectRequired"].includes(O.value);let i=null==(t=null==(e=null==O?void 0:O.value)?void 0:e.isOptional)?void 0:t.call(e);if("ZodDefault"===(null==(a=null==(n=null==O?void 0:O.value)?void 0:n._def)?void 0:a.typeName)){const e=null==(r=null==O?void 0:O.value)?void 0:r._def.innerType;e&&(i=null==(s=e.isOptional)?void 0:s.call(e))}return!i})),G=o((()=>{var e;if(!M.value)return null;let t=O.value;if(!t)return g.value?"required":null;if(x(t))return t;if(!!D.value){const n=null==(e=null==t?void 0:t.unwrap)?void 0:e.call(t);n&&(t=n)}return ho(t)})),J=o((()=>{const t=y(e.componentProps)?e.componentProps(s.value,u):e.componentProps;return{...e.commonComponentProps,...t,...m.value}}));l((()=>{var e;return null==(e=J.value)?void 0:e.autofocus}),(e=>{!0===e&&V((()=>{var e,t;c.value&&y(c.value.focus)&&document.activeElement!==c.value&&(null==(t=null==(e=c.value)?void 0:e.focus)||t.call(e))}))}),{immediate:!0});const X=o((()=>{var t;return _.value||e.disabled||(null==(t=J.value)?void 0:t.disabled)})),Q=o((()=>y(e.renderComponentContent)?e.renderComponentContent(s.value,u):{})),ee=o((()=>Object.keys(Q.value))),te=o((()=>{const t=G.value;return{keepValue:!0,label:e.label,...t?{rules:t}:{},...e.formFieldProps}}));function ne(n){const a=function(n){var a,r;const s=n.componentField.modelValue,i=n.componentField["onUpdate:modelValue"],o=x(e.component)?null==(a=t.value)?void 0:a[e.component]:null;let l=s;return s&&W(s)&&o&&(l=To(s)?null==(r=null==s?void 0:s.target)?void 0:r[o]:s),o?{[`onUpdate:${o}`]:i,[o]:void 0===l?e.emptyStateValue:l,onChange:e.disabledOnChangeListener?void 0:e=>{var t,a;const r=To(e),s=null==(t=null==n?void 0:n.componentField)?void 0:t.onChange;return r?null==s?void 0:s((null==(a=null==e?void 0:e.target)?void 0:a[o])??e):null==s?void 0:s(e)},onInput:()=>{}}:{}}(n);return{...n.componentField,...J.value,...a}}return(e,t)=>w(v)?(C(),L(w(Le),I({key:0},te.value,{name:e.fieldName}),{default:F((t=>[z(h(w(xe),I({class:[{"form-valid-error":d.value,"flex-col":w(a),"flex-row items-center":!w(a)},"flex pb-6"]},e.$attrs),{default:F((()=>[e.hideLabel?R("",!0):(C(),L(xo,{key:0,class:Z(w(pe)("flex leading-6",{"mr-2 flex-shrink-0 justify-end":!w(a),"mb-1 flex-row":w(a)},e.labelClass)),help:e.help,required:D.value&&!e.hideRequiredMark,style:A(k.value)},{default:F((()=>[N(E(e.label),1)])),_:1},8,["class","help","required","style"])),j("div",{class:Z(w(pe)("relative flex w-full items-center",e.wrapperClass))},[h(w(Oe),{class:Z(w(pe)(e.controlClass))},{default:F((()=>[S(e.$slots,"default",U(B({...t,...ne(t),disabled:X.value,isInValid:d.value})),(()=>[(C(),L(P(f.value),I({ref_key:"fieldComponentRef",ref:c,class:{"border-destructive focus:border-destructive hover:border-destructive/80 focus:shadow-[0_0_0_2px_rgba(255,38,5,0.06)]":d.value}},ne(t),{disabled:X.value}),H({_:2},[q(ee.value,(e=>({name:e,fn:F((()=>[h(w(Ce),I({content:Q.value[e]},t),null,16,["content"])]))})))]),1040,["class","disabled"]))]))])),_:2},1032,["class"]),e.suffix?(C(),T("div",So,[h(w(Ce),{content:e.suffix},null,8,["content"])])):R("",!0),e.description?(C(),L(w(Te),{key:1},{default:F((()=>[h(w(Ce),{content:e.description},null,8,["content"])])),_:1})):R("",!0),h(Y,{name:"slide-up"},{default:F((()=>[h(w(Se),{class:"absolute -bottom-[22px]"})])),_:1})],2)])),_:2},1040,["class"]),[[K,w(b)]])])),_:3},16,["name"])):R("",!0)}}),Fo=n({__name:"form",props:{globalCommonConfig:{default:()=>({})},collapsed:{type:Boolean},collapsedRows:{default:1},collapseTriggerResize:{type:Boolean},commonConfig:{default:()=>({})},componentBindEventMap:{},componentMap:{},form:{},layout:{},schema:{},showCollapseButton:{type:Boolean,default:!1},wrapperClass:{default:"grid-cols-1 sm:grid-cols-2 md:grid-cols-3"}},emits:["submit"],setup(e,{emit:t}){const n=e,a=t;co(n);const{isCalculated:r,keepFormItemIndex:i,wrapperRef:u}=function(e){const t=$("wrapperRef"),n=m({}),a=m(!1),r=M(D),i=o((()=>{const t=e.collapsedRows??1,a=n.value;let r=0;for(let e=1;e<=t;e++)r+=(null==a?void 0:a[e])??0;return r-1||1}));async function c(){if(!e.showCollapseButton)return;if(await V(),!t.value)return;const r=[...t.value.children],s=t.value,i=window.getComputedStyle(s).getPropertyValue("grid-template-rows").split(" "),o=null==s?void 0:s.getBoundingClientRect();r.forEach((t=>{const r=t.getBoundingClientRect().top-o.top;let s=0,l=0;for(const[e,n]of i.entries())if(l+=Number.parseFloat(n),r<l){s=e+1;break}s>((null==e?void 0:e.collapsedRows)??1)||(n.value[s]=(n.value[s]??0)+1,a.value=!0)}))}return l([()=>e.showCollapseButton,()=>r.active().value,()=>{var t;return null==(t=e.schema)?void 0:t.length}],(async([e])=>{e&&(await V(),n.value={},a.value=!1,await c())})),s((()=>{c()})),{isCalculated:a,keepFormItemIndex:i,wrapperRef:t}}(n),d=o((()=>{var e;const t=[];return null==(e=n.schema)||e.forEach((e=>{const{fieldName:n}=e,a=e.rules;let r="";a&&!x(a)&&(r=a._def.typeName);const s=Oo(a);t.push({default:Co(a),fieldName:n,required:!["ZodNullable","ZodOptional"].includes(r),rules:s})})),t})),f=o((()=>n.form?"form":Fe)),p=o((()=>n.form?{onSubmit:n.form.handleSubmit((e=>a("submit",e)))}:{onSubmit:e=>a("submit",e)})),h=o((()=>n.collapsed&&r.value)),_=o((()=>{const{componentProps:e={},controlClass:t="",disabled:a,disabledOnChangeListener:r=!1,emptyStateValue:s,formFieldProps:o={},formItemClass:l="",hideLabel:c=!1,hideRequiredMark:u=!1,labelClass:d="",labelWidth:f=100,wrapperClass:m=""}=ce(n.commonConfig,n.globalCommonConfig);return(n.schema||[]).map(((p,_)=>{const v=i.value,g=!!(n.showCollapseButton&&h.value&&v)&&v<=_;return{disabled:a,disabledOnChangeListener:r,emptyStateValue:s,hideLabel:c,hideRequiredMark:u,labelWidth:f,wrapperClass:m,...p,commonComponentProps:e,componentProps:p.componentProps,controlClass:pe(t,p.controlClass),formFieldProps:{...o,...p.formFieldProps},formItemClass:pe("flex-shrink-0",{hidden:g},l,p.formItemClass),labelClass:pe(d,p.labelClass)}}))}));return(e,t)=>(C(),L(P(f.value),U(B(p.value)),{default:F((()=>[j("div",{ref_key:"wrapperRef",ref:u,class:Z([e.wrapperClass,"grid"])},[(C(!0),T(c,null,q(_.value,(t=>(C(),L(Lo,I({key:t.fieldName,ref_for:!0},t,{class:t.formItemClass,rules:t.rules}),{default:F((n=>[S(e.$slots,t.fieldName,I({ref_for:!0},n))])),_:2},1040,["class","rules"])))),128)),S(e.$slots,"default",{shapes:d.value})],2)])),_:3},16))}}),No=n({__name:"vben-use-form",props:{formApi:{},actionWrapperClass:{},fieldMappingTime:{},handleReset:{type:Function},handleSubmit:{type:Function},handleValuesChange:{type:Function},resetButtonOptions:{},showDefaultActions:{type:Boolean},submitButtonOptions:{},submitOnEnter:{type:Boolean},collapsed:{type:Boolean},collapsedRows:{},collapseTriggerResize:{type:Boolean},commonConfig:{},layout:{},schema:{},showCollapseButton:{type:Boolean},wrapperClass:{}},setup(e){var t,n,a,r;const s=e,i=$("formActionsRef"),o=null==(n=null==(t=s.formApi)?void 0:t.useStore)?void 0:n.call(t),l=Ne(s,o),{delegatedSlots:c,form:u}=io(l);so([l,u]),null==(r=null==(a=s.formApi)?void 0:a.mount)||r.call(a,u);const d=e=>{var t;null==(t=s.formApi)||t.setState({collapsed:!!e})};function f(e){var t,n;e.target instanceof HTMLTextAreaElement||(e.preventDefault(),o.value.submitOnEnter&&i.value&&(null==(n=null==(t=i.value)?void 0:t.handleSubmit)||n.call(t)))}return(e,t)=>(C(),L(w(Fo),I({onKeydown:G(f,["enter"])},w(l),{collapsed:w(o).collapsed,"component-bind-event-map":w(Tr),"component-map":w(Cr),form:w(u),"global-common-config":w(Or)}),H({default:F((t=>[S(e.$slots,"default",U(B(t)),(()=>[w(l).showDefaultActions?(C(),L(oo,{key:0,ref_key:"formActionsRef",ref:i,"model-value":w(o).collapsed,"onUpdate:modelValue":d},{"reset-before":F((t=>[S(e.$slots,"reset-before",U(B(t)))])),"submit-before":F((t=>[S(e.$slots,"submit-before",U(B(t)))])),"expand-before":F((t=>[S(e.$slots,"expand-before",U(B(t)))])),"expand-after":F((t=>[S(e.$slots,"expand-after",U(B(t)))])),_:3},8,["model-value"])):R("",!0)]))])),_:2},[q(w(c),(t=>({name:t,fn:F((n=>[S(e.$slots,t,U(B(n)))]))})))]),1040,["collapsed","component-bind-event-map","component-map","form","global-common-config"]))}});const Eo=(e,t)=>(n,{attrs:r,slots:s})=>{const i=(null==n?void 0:n.placeholder)||wr(`ui.placeholder.${t}`);return a(e,{...n,...r,placeholder:i},s)};!async function(){const e={AutoComplete:Ie,Checkbox:Pe,CheckboxGroup:Re,DatePicker:je,DefaultButton:(e,{attrs:t,slots:n})=>a(Ze,{...e,attrs:t,type:"default"},n),Divider:Ae,Input:Eo(He,"input"),InputNumber:Eo(qe,"input"),InputPassword:Eo(Ye,"input"),Mentions:Eo(Ke,"input"),PrimaryButton:(e,{attrs:t,slots:n})=>a(Ze,{...e,attrs:t,type:"primary"},n),Radio:$e,RadioGroup:Me,RangePicker:De,Rate:Ve,Select:Eo(Ge,"select"),Space:We,Switch:ze,Textarea:Eo(Je,"input"),TimePicker:Ue,TreeSelect:Eo(Xe,"select"),Upload:Be};Qe.setComponents(e),Qe.defineMessage({copyPreferencesSuccess:(e,t)=>{Ee.success({description:t,message:e,placement:"bottomRight"})}})}(),function(e){const{config:t,defineRules:n}=e,{disabledOnChangeListener:a=!1,emptyStateValue:r}=t||{};if(Object.assign(Or,{disabledOnChangeListener:a,emptyStateValue:r}),n)for(const l of Object.keys(n))se(l,n[l]);const s=(null==t?void 0:t.baseModelPropName)??xr,i=null==t?void 0:t.modelPropNameMap,o=Qe.getComponents();for(const l of Object.keys(o)){const e=l;Cr[e]=o[l],s!==xr&&(Tr[e]=s),i&&i[e]&&(Tr[e]=i[e])}}({config:{baseModelPropName:"value",disabledOnChangeListener:!0,emptyStateValue:null,modelPropNameMap:{Checkbox:"checked",Radio:"checked",Switch:"checked",Upload:"fileList"}},defineRules:{required:(e,t,n)=>null!=e&&0!==e.length||wr("ui.formRules.required",[n.label]),selectRequired:(e,t,n)=>null!=e||wr("ui.formRules.selectRequired",[n.label])}});const Io=function(e){const t=J(e),r=new Sr(e),s=r;s.useStore=e=>tt(r.store,e);const i=n(((e,{attrs:t,slots:n})=>(X((()=>{r.unmount()})),r.setState({...e,...t}),()=>a(No,{...e,...t,formApi:s},n))),{inheritAttrs:!1,name:"VbenUseForm"});return t&&l((()=>e.schema),(()=>{r.setState({schema:e.schema})}),{immediate:!0}),[i,s]};export{wr as $,vi as s,Io as u};
