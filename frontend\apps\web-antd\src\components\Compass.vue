<script>
import * as Cesium from 'cesium';

import iconCompass from '../assets/images/icon_compass.png';
import { getGisMap, GISMap } from '../utils/cesium';
import core from '../utils/cesium/core';
import { CompassNeedle} from '@vben/icons';
export default {
  components: {CompassNeedle },
  data() {
    return {
      angle: 0,
      iconCompass,
    };
  },
  mounted() {
    const that = this;
    getGisMap((GISMap) => {
      const scene = GISMap.viewer.scene;
      scene.postRender.addEventListener(function () {
        this.angle = Cesium.Math.toDegrees(GISMap.viewer.camera.heading) * -1;
      }, that);
    });
  },
  methods: {
    lookNorth(angle) {
      angle = angle || 0;
      const viewer = GISMap.viewer;
      const height = viewer.camera.positionCartographic.clone().height;
      const mapcenter = core.getMapCenter(viewer);
      const pitch = Cesium.Math.toDegrees(viewer.camera.pitch);
      const target = core.getCameraPositionByMapCenter(
        { lng: mapcenter.lng, lat: mapcenter.lat, alt: height },
        angle,
        pitch,
      );
      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          target.lng,
          target.lat,
          height,
        ),
        orientation: {
          heading: Cesium.Math.toRadians(angle),
          pitch: viewer.camera.pitch,
          roll: 0,
        },
      });
    },
  },
};
</script>

<template>
  <div :style="`transform:rotate(${angle}deg)`" class="compass-viewer">
    <div class="north" @click="lookNorth(0)">
      <CompassNeedle class="size-12" />
    </div>
    <span
      :style="{ transform: `rotate(${angle * -1}deg)` }"
      class="direction"
      data-title="东"
      style="top: 35px; right: 0"
      @click="lookNorth(90)"
      >东</span>
    <span
      :style="{ transform: `rotate(${angle * -1}deg)` }"
      class="direction"
      data-title="南"
      style="bottom: 0; left: 35px"
      @click="lookNorth(180)"
      >南</span>
    <span
      :style="{ transform: `rotate(${angle * -1}deg)` }"
      class="direction"
      data-title="西"
      style="top: 35px; left: 0"
      @click="lookNorth(-90)"
      >西</span>
    <span
      :style="{ transform: `rotate(${angle * -1}deg)` }"
      class="direction"
      data-title="北"
      style="top: 0; left: 35px"
      @click="lookNorth(0)"
      >北</span>
  </div>
</template>

<style scoped lang="less">
.northImage {
  width: 50px;
  height: 50px;
}
.compass-viewer {
  position: absolute;
  right: 10px;
  width: 90px;
  height: 90px;
  border-radius: 100%;
  .north {
    position: absolute;
    left: 10px;
    top: 10px;
    width: 50px;
    height: 50px;
    border-radius: 100%;
    text-align: center;
    padding: 10px;
    cursor: pointer;
    .iconfont {
      color: #333;
      font-size: 30px;
      line-height: 30px;
      -webkit-text-stroke: 1px #fff;
    }
    &:hover .iconfont {
      color: #1890ff;
    }
  }
  .direction {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 100%;
    text-align: center;
    display: block;
    font-size: 12px;
    line-height: 20px;
    color: #333;
    display: none;
    cursor: pointer;
    -webkit-text-stroke: 2px #fff;
    &::after {
      content: attr(data-title);
      position: absolute;
      left: 0;
      top: 0;
      width: 20px;
      height: 20px;
      border-radius: 100%;
      text-align: center;
      display: block;
      font-size: 12px;
      line-height: 20px;
      pointer-events: none;
      -webkit-text-stroke: 0;
    }
    &:hover {
      color: #1890ff;
    }
  }
  &:hover,
  &:active {
    .direction {
      display: block;
    }
  }
}
</style>
