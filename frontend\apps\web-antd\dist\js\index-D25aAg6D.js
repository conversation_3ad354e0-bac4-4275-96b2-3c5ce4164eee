var G=Object.defineProperty,H=Object.defineProperties;var T=Object.getOwnPropertyDescriptors;var U=Object.getOwnPropertySymbols;var I=Object.prototype.hasOwnProperty,K=Object.prototype.propertyIsEnumerable;var V=(n,e,t)=>e in n?G(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,w=(n,e)=>{for(var t in e||(e={}))I.call(e,t)&&V(n,t,e[t]);if(U)for(var t of U(e))K.call(e,t)&&V(n,t,e[t]);return n},A=(n,e)=>H(n,T(e));var h=(n,e,t)=>new Promise((S,u)=>{var _=s=>{try{p(t.next(s))}catch(m){u(m)}},C=s=>{try{p(t.throw(s))}catch(m){u(m)}},p=s=>s.done?S(s.value):Promise.resolve(s.value).then(_,C);p((t=t.apply(n,e)).next())});import{v as R,f as J,a as Q}from"./bootstrap-5OPUVRWy.js";import{u as W}from"./vxe-table-CZ9gPHn5.js";import X from"./SceneDetialModal-CMSsmrBj.js";import Y from"./SceneCreateModal-QtF1Ihbe.js";import Z from"./SceneUpdateModal-Bymu8ibZ.js";import{l as tt,d as et}from"./scene.api-DEW02Ykq.js";import{c as ot,s as nt}from"./scene.data-BMXeOdST.js";import{s as at}from"./alert-DJKWbMfG.js";import st from"./SceneUploadModal-pbSscZPJ.js";import{g as lt}from"./entity.data-u4HDUExc.js";import{g as rt,b as it}from"./util-BadkgFi3.js";import{_ as dt}from"./page.vue_vue_type_script_setup_true_lang-D2AkmoUI.js";import{d as ct,r as ut,o as pt,x as mt,j as ft,b as Ct,q as r,s as i,k as d,f as x,H as yt,v as f,t as D}from"../jse/index-index-DyHD_jbN.js";import{u as g}from"./use-modal-uChFuhJy.js";import"./loading-DzjUKA94.js";import"./form-DdFfsSWf.js";import"./LayerPreview-B-iME7wH.js";import"./index-D4Q7xmlJ.js";import"./Base-xeJpkIWP.js";/* empty css                                                                     */import"./toast-CQjPPeQ1.js";import"./fileUpload-DI0dJ9zY.js";const gt={class:"actionBar"},St=ct({__name:"index",setup(n){let e=rt(),t=it(e);const[S,u]=g({connectedComponent:Y,fullscreenButton:!1,destroyOnClose:!0,key:"uniqueModalKey",onClosed:()=>{v.query()}}),[_,C]=g({connectedComponent:Z,fullscreenButton:!1,destroyOnClose:!0,onClosed:()=>{v.query()}}),[p,s]=g({connectedComponent:X,fullscreenButton:!1,destroyOnClose:!0,onClosed:()=>{}}),[m,k]=g({connectedComponent:st,fullscreenButton:!1,destroyOnClose:!0,onClosed:()=>{}}),$={collapsed:!1,schema:nt,showCollapseButton:!0,submitButtonOptions:{content:"查询"},submitOnEnter:!1},q={columns:ot,height:"auto",pagerConfig:{},proxyConfig:{ajax:{query:(y,l)=>h(null,[y,l],function*({page:o},a){const c=yield tt({page:{current:o.currentPage,size:o.pageSize,searchCount:!0},condition:A(w({},a),{category:t})});return{total:c.total,items:c.records}})}},rowConfig:{isHover:!0}},[P,v]=W({formOptions:$,gridOptions:q}),M=ut(null),F=()=>{u.setState({upload:!1,update:!1,category:t}),u.open()};function L(o){C.setState({row:o,upload:!1,update:!0,category:t}),C.open()}const N=()=>{s.close()},O=()=>{k.close()},B=()=>{O(),b()};function j(o){return h(this,null,function*(){at("提示","确定要删除？",()=>{et(o.dataType,o.id,b)})})}const z=o=>{M.value=o,s.setState({row:o}),s.open()},E=o=>{M.value=o,k.setState({row:o}),k.open()},b=()=>{v.reload()};return pt(()=>{}),(o,a)=>{const y=mt("a-button");return Ct(),ft(d(dt),{"auto-content-height":""},{default:r(()=>[i(d(P),null,{"toolbar-tools":r(()=>[i(d(J),{"pre-icon":"ant-design:plus-outlined",type:"primary",onClick:F},{default:r(()=>a[0]||(a[0]=[f(" 新建数据集 ")])),_:1})]),area:r(({row:l})=>[x("view",null,D(l.area&&l.area.toFixed(2)),1)]),"upload-status":r(({row:l})=>[x("view",null,D(d(lt)(l)),1)]),action:r(({row:l})=>[x("div",gt,[i(y,{class:"actionButton",type:"link",onClick:c=>L(l)},{default:r(()=>a[1]||(a[1]=[f(" 编辑 ")])),_:2},1032,["onClick"]),i(y,{class:"actionButton",type:"link",onClick:c=>j(l)},{default:r(()=>a[2]||(a[2]=[f(" 删除 ")])),_:2},1032,["onClick"]),i(y,{class:"actionButton",type:"link",onClick:c=>z(l)},{default:r(()=>a[3]||(a[3]=[f(" 查看 ")])),_:2},1032,["onClick"]),yt(i(y,{class:"actionButton",type:"link",onClick:c=>E(l)},{default:r(()=>a[4]||(a[4]=[f(" 上传 ")])),_:2},1032,["onClick"]),[[R,l.uploadStatus===0||l.uploadStatus==3]])])]),_:1}),i(d(S),{onSuccess:B}),i(d(_),{onSuccess:B}),i(d(p),{onOnCancel:N}),i(d(m),{row:M.value,onOnCancel:O,onSuccess:B},null,8,["row"])]),_:1})}}}),Gt=Q(St,[["__scopeId","data-v-c711a088"]]);export{Gt as default};
