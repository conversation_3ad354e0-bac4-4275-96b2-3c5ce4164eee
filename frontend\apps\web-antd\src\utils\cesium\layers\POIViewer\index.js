import * as Cesium from 'cesium'
import {
	createVNode,
	h,reactive,render
} from 'vue'
import {
	eventbus
} from '../../../../ztu/eventbus'
import Drawer from '../../mapTools/Drawer'
import Box from '../../../../components/JS/box'
import POISetting from './components/POISetting.vue'
import SearchPOI from './components/SearchPOI.vue'
import core from "../../core"
import ShapeConfig from './Config'
import {Tree} from 'ant-design-vue'; 
import * as api from '../../../../api'
import {oppositeColor,hexToRgb,rgbToHex} from '../../../utils.js'

/**
 * 兴趣点视图
 * @event change 类型变更或显隐时
 * @event dataChange 兴趣点变更
 * @event select 选中兴趣点时
 */
export default class POIViewer extends eventbus {
	constructor(options) {
		super();
		this.viewer = options.viewer;
		
		this._poiTypes = null;
		this._poiLayers = [];
		this._selectedPOI = null;
		this._editEntity = null;
		this._poiImages = null;
		this.treeData = reactive([{title:'我的兴趣点',
				key:'poi_layer',
				children:[]}]);
		this.checkedKeys = reactive([]);
		this.selectedKeys = reactive([]);
		this.loadTypes();
		this.loadPOIImages();
		this.on('change',(poiLayers)=>{
			let nodes = poiLayers.map(a=>{
				return {
					title:a.type,
					key:a.type,
					show:a.dataSource.show
				}
			});
			console.log(nodes)
			this.treeData[0].children = nodes;
			this.checkedKeys.splice(0,this.checkedKeys.length);
			console.log(this.checkedKeys)
			nodes.filter(a=>a.show).map(a=>a.key).forEach(a=>{
				this.checkedKeys.push(a)
			});
			console.log(this.checkedKeys)
		});
		if(options.el){
			this.mount(options.el);
		}
	}
	get poiTypes() {
		return this._poiTypes;
	}
	get poiLayers() {
		return this._poiLayers;
	}
	get poiImages(){
		return this._poiImages || [];
	}
	set selectedPOI(poi) {
		this._selectedPOI = poi;
		this.emit('select', this._selectedPOI);
	}
	get selectedPOI(){
		return this._selectedPOI;
	}
	get editEntity(){
		return this._editEntity;
	}
	mount(el){
		if(typeof el === 'string'){
			el = document.getElementById(el);
		}
		if(!(el instanceof HTMLElement)){
			return console.log('POIViewer绑定节点无效')
		}
		//let checkedKeys = [];
		let node = h(Tree,{
			selectable:true,
			blockNode:true,
			checkable:true,
			//defaultExpandAll:true,
			checkedKeys: this.checkedKeys,
			selectedKeys:this.selectedKeys,
			//'onUpdate:checkedKeys': (value) => this.checkedKeys = value,
			onCheck:(keys)=>this.show(keys),
			onSelect:(keys)=>this.showAndZoom(keys[0]),
			treeData:this.treeData,
			style:{
				margin:'0px',
				marginTop:0
			}
		},{
			title:({title, key,show})=>{
				return show?(
					h('div',{
						style:'display:flex;align-items: center;justify-content: space-between;user-select: none;'
					},[
						h('span',null,title),
						h('span',{
							style:'cursor: pointer;',
							onClick:(e)=>{
								//e.preventDefault();
								e.stopPropagation();
								this.showSearchBox(key);
							}
						},[
							h('span',{
								class:"iconfont icon-Search",
								style:"color:#fff;font-size:14px;padding:0 4px;"
							})
						])
					]))
					:h('span',null,title);
			}
		});
		render(node,el);
	}
	async loadPOIImages(){
		let res = await api.getPoiList();
		this._poiImages = res && res.map( a=> {
			return {
				id:a.id,
				resUrl:a.resUrl,
				name:a.name
			}
		});
	}
	async loadTypes() {
		this._poiTypes = await api.getUserVisibleTypeList();
		this._poiTypes.sort();
		this._poiTypes.forEach((type, index) => {
			let idx = this._poiLayers.findIndex(a => a.type == type);
			if (idx == -1) {
				let ds = new Cesium.CustomDataSource();
				ds.name = "poi_layer";
				ds.show = false;
				this.viewer.dataSources.add(ds);
				this._poiLayers.push({
					type: type,
					dataSource: ds,
					isLoaded: false,
					refData: null
				})
			}
		})
		this.emit('change', this.poiLayers);
	}
	async loadPOIs(poiType, reload = false) {
		let idx = this._poiLayers.findIndex(a => a.type == poiType);
		if (idx == -1) return;
		let poiLayer = this._poiLayers[idx];
		let ds = poiLayer.dataSource;
		if (!reload && poiLayer.isLoaded) return;
		ds.entities.removeAll();
		poiLayer.refData = await api.getLayerTag({
			tagType: poiType
		});
		poiLayer.refData && poiLayer.refData.forEach(item => {
			let entityjson = {
				position: Cesium.Cartesian3.fromDegrees(item.longitude, item.latitude,0.01),
				billboard: {
					image: item.resUrl,
				},
				label: {
					fillColor: item.nameColor || "",
					font: (item.nameSize || 14) + 'px sans-serif',
					text: item.name,
				},
				refData: item,
				properties: {
					'名称': item.name,
					'类型': item.tagType,
					'备注': item.remark
				}
			}
			this.addByJson(ds, entityjson)
		})
		poiLayer.isLoaded = true;
		if (reload) {
			//poids.show=true;
			poiLayer.dataSource.show = true;
			this.emit('change', this.poiLayers)
			this.emit('dataChange', poiLayer);
		}
	}
	addByJson(ds, json) {
		let entity = core.json2Entity(json,null,ShapeConfig);
		if (entity) {
			let color = json.refData.nameColor;
			color = rgbToHex(color);
			color = oppositeColor(color);
			color = hexToRgb(color);
			entity.label.outlineColor = Cesium.Color.fromCssColorString(color);
			entity.label.outlineWidth = 2;
			ds.entities.add(entity);
			entity.refData = json.refData;
		}
	}
	query(key, poiType) {
		let poiLayer = this._poiLayers.find(a => a.type == poiType);
		if (!poiLayer || !poiLayer.isLoaded || !poiLayer.refData) return [];
		if (!key) return poiLayer.refData;
		return poiLayer.refData.filter(a => (a.name + '').indexOf(key) > -1);
	}
	getPOI(id) {
		let entity = null;
		let poiLayer = null;
		for (let i = 0; i < this._poiLayers.length; i++) {
			entity = this._poiLayers[i].dataSource.entities.values.find(a => a.refData && a.refData.id == id);
			if (entity) {
				poiLayer = this._poiLayers[i];
				break;
			}
		}
		if (!entity) return null;
		return {
			poiLayer,
			poi: entity
		}
	}
	remove(id) {
		let {
			poiLayer,
			poi
		} = this.getPOI(id);
		if (poi) {
			Box.confirm('删除兴趣点', '确定删除兴趣点?', async () => {
				await api.deleteLayerTag({id});
				poiLayer.dataSource.entities.remove(poi);
				await this.loadPOIs(poiLayer.type, true);
				this.emit('dataChange', poiLayer);
			})
		}
	}
	showEditor(id) {
		//this.restore();
		let {
			poiLayer,
			poi
		} = this.getPOI(id);
		if (!poi) return;
		if(this.editor) Box.close(this.editor);
		this.selectedPOI = poi;
		this._editEntity = poi;
		Box.closeAll()
		this.editor = Box.open({
			moverect: {
				left: 50,
				top: 64,
				right: 0,
				bottom: 0
			},
			style: {
				right: '100px',
				top: '140px',
				left: "unset"
			},
			title: '属性',
			beforeClose: () => {
				this.restore();
			}
		}, [h(POISetting)])
	}
	moveStart(id) {
		return new Promise((resolve, reject) => {
			let {
				poiLayer,
				poi
			} = this.getPOI(id);
			if (!poi) return;
			let row = poi.refData;
			let d = new Drawer(this.viewer);
			d.drawStart('marker', null, (newentity) => {
				let loca = newentity.position.getValue()
				d.remove(newentity);
				loca && (poi.position._value = loca);
				loca = core.transformCartesianToWGS84(loca)

				let poiparams = {
					id: id,
					latitude: loca.lat || 0,
					longitude: loca.lng || 0,
					name: row.name,
					nameColor: row.nameColor,
					nameSize: row.nameSize,
					remark: row.remark,
					resId: row.resId,
					tagType: row.tagType,
				}
				d = null;
				resolve(poiparams);
			});
		})
	}
	async save(params) {
		//this.review(params);
		console.log(params)
		let id = params.id;
		if (id) {
			let {
				poiLayer,
				poi
			} = this.getPOI(id);
			await api.editLayerTag(params);
			if (poi.refData.tagType!=params.tagType)
				await this.loadPOIs(poi.refData.tagType, true);
			this._editEntity = null;
		} else {
			await api.createLayerTag(params);
		}
		await this.loadTypes();
		await this.loadPOIs(params.tagType, true);
		
		this.editor && Box.close(this.editor)
	}
	review(params,entity) {
		let id = params.id;
		let poi = entity || this.getPOI(id).poi;
		//let {
		//	poi
		//} = this.getPOI(id);
		if (!poi) return;
		poi.label = new Cesium.LabelGraphics({
			...(ShapeConfig.textStyle),
			text: params.name,
			font: params.nameSize+'px sans-serif',
			fillColor:Cesium.Color.fromCssColorString(params.nameColor),
			pixelOffset : new Cesium.Cartesian2(0, -34)
		})
		let color = params.nameColor;
		color = rgbToHex(color);
		color = oppositeColor(color);
		color = hexToRgb(color);
		poi.label.outlineColor = Cesium.Color.fromCssColorString(color);
		poi.label.outlineWidth = 2;
		
		if(params.resUrl){
			poi.billboard.image=params.resUrl;
		}
		else{
			let poiImage = this.poiImages.find(a=>a.id == params.resId);
			poi.billboard.image=poiImage?poiImage.resUrl:'';
		}
		poi.properties = {
			'名称': params.name,
			'类型': params.tagType,
			'备注': params.remark
		};
	}
	restore() {
		if(this._editEntity 
			&& this._editEntity.refData 
			&& this._editEntity.refData.id>0){
			let item = this._editEntity.refData;
			this.review(item);
		}
		else if(this._editEntity){
			this.viewer.entities.remove(this._editEntity);
		}
		this._editEntity = null;
		this.editor = null;
	}
	showSearchBox(poiType) {
		let style = {
			width: "550px"
		}
		if (!this.searchPoiBox) {
			style = {
				...style,
				left: '50%',
				top: '50%',
				transform: 'translate3d(-50%, -50%, 0)'
			}
		}
		this.searchPoiBox = Box.open({
			title: '兴趣点搜索 - ' + poiType,
			beforeClose: () => {
				this.searchPoiBox = null;
			},
			style: style
		}, h(SearchPOI, {
			poiType: poiType,

		}), this.searchPoiBox)
	}
	add(options) {
		let d = new Drawer(this.viewer);
		d.drawStart('marker', {
			...(ShapeConfig['marker' + 'Style']),
			...options
		}, (entity)=>{
			if(this.editor) Box.close(this.editor);
			this._editEntity = entity;
			this.selectedPOI = entity;
			this.editor = Box.open({
				moverect: {
					left: 50,
					top: 64,
					right: 0,
					bottom: 0
				},
				style: {
					right: '100px',
					top: '140px',
					left: "unset"
				},
				title: '属性',
				beforeClose: () => {
					this.restore();
				}
			}, h(POISetting));
		})
	}
	show(poiTypes){
		this.poiLayers.forEach(poiLayer=>{
			poiLayer.dataSource.show = poiTypes.includes(poiLayer.type);
			if(poiLayer.dataSource.show && !poiLayer.isLoaded){
				this.loadPOIs(poiLayer.type);
			}
		})
		this.emit('change',this.poiLayers);
	}
	async showAndZoom(poiType){
		if(!poiType) return;
		let poiLayer = this.poiLayers.find(a=>a.type == poiType);
		if(poiLayer){
			poiLayer.dataSource.show = true;
			if(poiLayer.dataSource.show && !poiLayer.isLoaded){
				await this.loadPOIs(poiLayer.type);
			}
			this.emit('change',this.poiLayers);
			let positions = poiLayer.dataSource.entities.values.map(a=>a.position._value);
			//console.log(rect,x,y)
			this.viewer.camera.flyTo({
				destination:core.computeExtent(positions,1)
			});
		}
	}
}
export let poiViewer = null;
export function initPOIViewer(options) {
	poiViewer = new POIViewer(options);
	return poiViewer;
}
