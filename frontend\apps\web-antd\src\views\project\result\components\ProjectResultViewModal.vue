<script setup>
import { computed, ref, unref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { getResultDetail } from '#/views/project/project.api';
import { message } from 'ant-design-vue';
import { RegionUtils } from '#/utils/regionUtils';
import { formSchema } from '#/views/project/result/result.data.js';

// 声明Emits
const emit = defineEmits(['register', 'success']);

let id = null;
let reginCodeValue = null;

const projectNatureMap = {
  0: '内外业均有',
  1: '外业项目',
  2: '内业项目',
};

const operationDeptMap = {
  '001': '智慧国土部',
  '002': '遥感与航测部',
  '003': '工程部',
  '004': '海洋与制图部',
  '005': '生态修复设计部',
  '006': '国土空间规划院',
  '007': '研发部',
};

const organizationalAcceptanceMap = {
  0: '未验收',
  1: '已验收',
};

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  footer: null,
  onCancel() {
    modalApi.close();
  },
  onClosed() {
    clearData();
  },
  onOpenChange: async (isOpen) => {
    if (isOpen) {
      const data = modalApi.getData();
      if (data && data.row) {
        let rowData = { ...data.row };
        rowData = await getResultDetail(rowData.id);
        let regionCode = [
          parseInt(rowData.regionProvinceCode),
          parseInt(rowData.regionCityCode),
          parseInt(rowData.regionCountyCode),
        ];
        let regionName = `${rowData.regionProvinceName || ''} ${rowData.regionCityName || ''} ${rowData.regionCountyName || ''}`.trim();
        rowData.regionCode = regionName;
        reginCodeValue = regionCode;

        // 处理项目性质
        if (typeof rowData.projectNature === 'number') {
          rowData.projectNature = projectNatureMap[rowData.projectNature];
        }

        // 处理 organizationalAcceptance
        if (typeof rowData.organizationalAcceptance === 'number') {
          rowData.organizationalAcceptance = organizationalAcceptanceMap[rowData.organizationalAcceptance];
        }

        // 处理 operationDept
        if (rowData.operationDept && operationDeptMap[rowData.operationDept]) {
          rowData.operationDept = operationDeptMap[rowData.operationDept];
        }

        delete rowData.regionProvinceCode;
        delete rowData.regionCityCode;
        delete rowData.regionCountyCode;
        delete rowData.regionProvinceName;
        delete rowData.regionCityName;
        delete rowData.regionCountyName;

        id = data.row.id;
        formModel.value = rowData; // 将数据赋值给 formModel
      }
    } else {
      // 在Modal关闭时重新启用表单项，以便下次打开时可以编辑
      // formApi.setProps({ disabled: false }); // 移除此行，确保表单始终禁用
    }
  },
});

const state = modalApi.useStore();
const formModel = ref({}); // 修改为对象，用于存储详情数据

// 设置标题
const title = computed(() => '查看项目成果');

// 清除数据函数
function clearData() {
  id = null;
  formModel.value = {}; // 清空 formModel
}
</script>

<template>
  <Modal v-bind="$attrs" :footer="false" :title="title" class="w-[1400px]" :destroy-on-close="true" :maskClosable="false">
    <div class="p-6">
      <div class="grid grid-cols-3 gap-y-6 gap-x-4">
        <div v-for="item in formSchema" :key="item.fieldName" class="flex items-start py-2">
          <div class="w-32 text-right pr-4 font-bold">{{ item.label }}:</div>
          <div class="flex-1">{{ formModel[item.fieldName] }}</div>
        </div>
      </div>
      <div class="flex justify-end mt-4 space-x-2">
        <a-button @click="modalApi.close()">关闭</a-button>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
@import '#/styles/dark-antd.less';
/* 可以在这里添加样式 */
</style>