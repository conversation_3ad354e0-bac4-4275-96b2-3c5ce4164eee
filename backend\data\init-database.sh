#!/bin/bash

echo "================================================"
echo "           系统管理数据库初始化工具"
echo "================================================"
echo ""

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js，请先安装Node.js"
    echo "💡 下载地址: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js版本: $(node --version)"

# 检查mysql2包是否安装
if [ ! -d "../node_modules/mysql2" ]; then
    echo "📦 正在安装mysql2依赖包..."
    cd ..
    npm install mysql2
    cd data
    if [ $? -ne 0 ]; then
        echo "❌ 依赖包安装失败"
        exit 1
    fi
fi

# 检查数据库初始化脚本是否存在
if [ ! -f "database-init.sql" ]; then
    echo "❌ 错误: 未找到database-init.sql文件"
    exit 1
fi

# 提示用户配置数据库连接
echo "🔧 请确保已正确配置数据库连接信息："
echo "   方式1: 设置环境变量 DB_HOST, DB_PORT, DB_USERNAME, DB_PASSWORD"
echo "   方式2: 修改 init-database.js 中的默认配置"
echo ""

read -p "是否继续执行数据库初始化？(y/N): " confirm
if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "取消初始化"
    exit 0
fi

echo ""
echo "🚀 开始执行数据库初始化..."
echo ""

# 运行初始化脚本
node init-database.js

echo ""
echo "初始化完成！"
