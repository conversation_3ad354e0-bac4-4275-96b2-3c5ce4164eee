var _=Object.defineProperty,C=Object.defineProperties;var I=Object.getOwnPropertyDescriptors;var m=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var u=(n,t,i)=>t in n?_(n,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):n[t]=i,p=(n,t)=>{for(var i in t||(t={}))V.call(t,i)&&u(n,i,t[i]);if(m)for(var i of m(t))k.call(t,i)&&u(n,i,t[i]);return n},h=(n,t)=>C(n,I(t));import{u as b}from"./form-DnT3S1ma.js";import{by as x,B as s}from"./bootstrap-DShsrVit.js";import{C as g}from"./index-B_b7xM74.js";import{_ as y}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as B,af as v,ag as D,ah as d,a3 as a,n as r,an as f}from"../jse/index-index-BMh_AyeW.js";const T=B({__name:"dynamic",setup(n){const[t,i]=b({handleSubmit:c,schema:[{component:"Input",defaultValue:"hidden value",dependencies:{show:!1,triggerFields:["field1Switch"]},fieldName:"hiddenField",label:"隐藏字段"},{component:"Switch",defaultValue:!0,fieldName:"field1Switch",help:"通过Dom控制销毁",label:"显示字段1"},{component:"Switch",defaultValue:!0,fieldName:"field2Switch",help:"通过css控制隐藏",label:"显示字段2"},{component:"Switch",fieldName:"field3Switch",label:"禁用字段3"},{component:"Switch",fieldName:"field4Switch",label:"字段4必填"},{component:"Input",dependencies:{if(e){return!!e.field1Switch},triggerFields:["field1Switch"]},fieldName:"field1",label:"字段1"},{component:"Input",dependencies:{show(e){return!!e.field2Switch},triggerFields:["field2Switch"]},fieldName:"field2",label:"字段2"},{component:"Input",dependencies:{disabled(e){return!!e.field3Switch},triggerFields:["field3Switch"]},fieldName:"field3",label:"字段3"},{component:"Input",dependencies:{required(e){return!!e.field4Switch},triggerFields:["field4Switch"]},fieldName:"field4",label:"字段4"},{component:"Input",dependencies:{rules(e){return e.field1==="123"?"required":null},triggerFields:["field1"]},fieldName:"field5",help:"当字段1的值为`123`时，必填",label:"动态rules"},{component:"Select",componentProps:{allowClear:!0,class:"w-full",filterOption:!0,options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}],placeholder:"请选择",showSearch:!0},dependencies:{componentProps(e){return e.field2==="123"?{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"},{label:"选项3",value:"3"}]}:{}},triggerFields:["field2"]},fieldName:"field6",help:"当字段2的值为`123`时，更改下拉选项",label:"动态配置"},{component:"Input",fieldName:"field7",label:"字段7"}],wrapperClass:"grid-cols-1 md:grid-cols-3 lg:grid-cols-4"}),[S]=b({handleSubmit:c,schema:[{component:"Input",fieldName:"field1",label:"字段1"},{component:"Input",componentProps:{disabled:!0},dependencies:{trigger(e,l){l.setFieldValue("field2",e.field1)},triggerFields:["field1"]},fieldName:"field2",label:"字段2"}],wrapperClass:"grid-cols-1 md:grid-cols-3 lg:grid-cols-4"});function c(e){x.success({content:`form values: ${JSON.stringify(e)}`})}function w(){i.setState(e=>{var l;return{schema:(l=e.schema)==null?void 0:l.filter(o=>o.fieldName!=="field7")}})}function N(){i.setState(e=>{var l;return{schema:[...(l=e==null?void 0:e.schema)!=null?l:[],{component:"Input",fieldName:`field${Date.now()}`,label:"字段+"}]}})}function F(){i.setState(e=>{var l;return{schema:(l=e.schema)==null?void 0:l.map(o=>o.fieldName==="field3"?h(p({},o),{label:"字段3-修改"}):o)}})}return(e,l)=>(v(),D(a(y),{description:"表单组件动态联动示例，包含了常用的场景。增删改，本质上是修改schema，你也可以通过 `setState` 动态修改schema。",title:"表单组件"},{default:d(()=>[r(a(g),{title:"表单动态联动示例"},{extra:d(()=>[r(a(s),{class:"mr-2",onClick:F},{default:d(()=>l[0]||(l[0]=[f("修改字段3")])),_:1}),r(a(s),{class:"mr-2",onClick:w},{default:d(()=>l[1]||(l[1]=[f("删除字段7")])),_:1}),r(a(s),{onClick:N},{default:d(()=>l[2]||(l[2]=[f("添加字段")])),_:1})]),default:d(()=>[r(a(t))]),_:1}),r(a(g),{class:"mt-5",title:"字段同步，字段1数据与字段2数据同步"},{default:d(()=>[r(a(S))]),_:1})]),_:1}))}});export{T as default};
