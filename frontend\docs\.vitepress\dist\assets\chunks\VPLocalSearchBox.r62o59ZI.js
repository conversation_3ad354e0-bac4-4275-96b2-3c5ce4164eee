var e=Object.defineProperty,t=(t,n,o)=>((t,n,o)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[n]=o)(t,"symbol"!=typeof n?n+"":n,o);import{aN as n,r as o,c as i,ba as s,aQ as r,bb as a,w as l,bc as c,f as u,s as d,bd as h,be as f,bf as p,J as m,bg as v,A as g,O as b,aJ as y,bh as w,bi as x,a8 as _,aL as S,H as E,j as I,y as k,z as O,v as N,u as T,W as F,ah as C,X as R,k as M,ae as A,x as L,C as D,F as j,I as z,M as P,T as V,a4 as $,aS as W,b0 as J,b6 as G,bj as B,ao as q}from"./framework.C8U7mBUf.js";import{az as K,aA as U}from"./theme.TDvSnEYR.js";const H={root:()=>n((()=>import("./@localSearchIndexroot.BeTP2oRz.js")),[]),en:()=>n((()=>import("./@localSearchIndexen.B-Ci-VkA.js")),[])};
/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/var Q=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],Y=Q.join(","),X="undefined"==typeof Element,Z=X?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,ee=!X&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},te=function e(t,n){var o;void 0===n&&(n=!0);var i=null==t||null===(o=t.getAttribute)||void 0===o?void 0:o.call(t,"inert");return""===i||"true"===i||n&&t&&e(t.parentNode)},ne=function(e,t,n){if(te(e))return[];var o=Array.prototype.slice.apply(e.querySelectorAll(Y));return t&&Z.call(e,Y)&&o.unshift(e),o=o.filter(n)},oe=function e(t,n,o){for(var i=[],s=Array.from(t);s.length;){var r=s.shift();if(!te(r,!1))if("SLOT"===r.tagName){var a=r.assignedElements(),l=e(a.length?a:r.children,!0,o);o.flatten?i.push.apply(i,l):i.push({scopeParent:r,candidates:l})}else{Z.call(r,Y)&&o.filter(r)&&(n||!t.includes(r))&&i.push(r);var c=r.shadowRoot||"function"==typeof o.getShadowRoot&&o.getShadowRoot(r),u=!te(c,!1)&&(!o.shadowRootFilter||o.shadowRootFilter(r));if(c&&u){var d=e(!0===c?r.children:c.children,!0,o);o.flatten?i.push.apply(i,d):i.push({scopeParent:r,candidates:d})}else s.unshift.apply(s,r.children)}}return i},ie=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},se=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!ie(e)?0:e.tabIndex},re=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},ae=function(e){return"INPUT"===e.tagName},le=function(e){return function(e){return ae(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||ee(e),o=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=o(window.CSS.escape(e.name));else try{t=o(e.name)}catch(s){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",s.message),!1}var i=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!i||i===e}(e)},ce=function(e){var t=e.getBoundingClientRect(),n=t.width,o=t.height;return 0===n&&0===o},ue=function(e,t){var n=t.displayCheck,o=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var i=Z.call(e,"details>summary:first-of-type")?e.parentElement:e;if(Z.call(i,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return ce(e)}else{if("function"==typeof o){for(var s=e;e;){var r=e.parentElement,a=ee(e);if(r&&!r.shadowRoot&&!0===o(r))return ce(e);e=e.assignedSlot?e.assignedSlot:r||a===e.ownerDocument?r:a.host}e=s}if(function(e){var t,n,o,i,s=e&&ee(e),r=null===(t=s)||void 0===t?void 0:t.host,a=!1;if(s&&s!==e)for(a=!!(null!==(n=r)&&void 0!==n&&null!==(o=n.ownerDocument)&&void 0!==o&&o.contains(r)||null!=e&&null!==(i=e.ownerDocument)&&void 0!==i&&i.contains(e));!a&&r;){var l,c,u;a=!(null===(c=r=null===(l=s=ee(r))||void 0===l?void 0:l.host)||void 0===c||null===(u=c.ownerDocument)||void 0===u||!u.contains(r))}return a}(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},de=function(e,t){return!(t.disabled||te(t)||function(e){return ae(e)&&"hidden"===e.type}(t)||ue(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var o=t.children.item(n);if("LEGEND"===o.tagName)return!!Z.call(t,"fieldset[disabled] *")||!o.contains(e)}return!0}t=t.parentElement}return!1}(t))},he=function(e,t){return!(le(t)||se(t)<0||!de(e,t))},fe=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},pe=function e(t){var n=[],o=[];return t.forEach((function(t,i){var s=!!t.scopeParent,r=s?t.scopeParent:t,a=function(e,t){var n=se(e);return n<0&&t&&!ie(e)?0:n}(r,s),l=s?e(t.candidates):r;0===a?s?n.push.apply(n,l):n.push(r):o.push({documentOrder:i,tabIndex:a,item:t,isScope:s,content:l})})),o.sort(re).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},me=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==Z.call(e,Y)&&he(t,e)},ve=Q.concat("iframe").join(","),ge=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==Z.call(e,ve)&&de(t,e)};
/*!
* focus-trap 7.6.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/
function be(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function ye(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function we(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function xe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?we(Object(n),!0).forEach((function(t){ye(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):we(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function _e(e){return function(e){if(Array.isArray(e))return be(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return be(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?be(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Se=function(e,t){if(e.length>0){var n=e[e.length-1];n!==t&&n._setPausedState(!0)}var o=e.indexOf(t);-1===o||e.splice(o,1),e.push(t)},Ee=function(e,t){var n=e.indexOf(t);-1!==n&&e.splice(n,1),e.length>0&&!e[e.length-1]._isManuallyPaused()&&e[e.length-1]._setPausedState(!1)},Ie=function(e){return"Tab"===(null==e?void 0:e.key)||9===(null==e?void 0:e.keyCode)},ke=function(e){return Ie(e)&&!e.shiftKey},Oe=function(e){return Ie(e)&&e.shiftKey},Ne=function(e){return setTimeout(e,0)},Te=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return"function"==typeof e?e.apply(void 0,n):e},Fe=function(e){return e.target.shadowRoot&&"function"==typeof e.composedPath?e.composedPath()[0]:e.target},Ce=[],Re=function(e,t){var n,o=(null==t?void 0:t.document)||document,i=(null==t?void 0:t.trapStack)||Ce,s=xe({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:ke,isKeyBackward:Oe},t),r={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,manuallyPaused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},a=function(e,t,n){return e&&void 0!==e[t]?e[t]:s[n||t]},l=function(e,t){var n="function"==typeof(null==t?void 0:t.composedPath)?t.composedPath():void 0;return r.containerGroups.findIndex((function(t){var o=t.container,i=t.tabbableNodes;return o.contains(e)||(null==n?void 0:n.includes(o))||i.find((function(t){return t===e}))}))},c=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.hasFallback,i=void 0!==n&&n,r=t.params,a=void 0===r?[]:r,l=s[e];if("function"==typeof l&&(l=l.apply(void 0,_e(a))),!0===l&&(l=void 0),!l){if(void 0===l||!1===l)return l;throw new Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var c=l;if("string"==typeof l){try{c=o.querySelector(l)}catch(u){throw new Error("`".concat(e,'` appears to be an invalid selector; error="').concat(u.message,'"'))}if(!c&&!i)throw new Error("`".concat(e,"` as selector refers to no known node"))}return c},u=function(){var e=c("initialFocus",{hasFallback:!0});if(!1===e)return!1;if(void 0===e||e&&!ge(e,s.tabbableOptions))if(l(o.activeElement)>=0)e=o.activeElement;else{var t=r.tabbableGroups[0];e=t&&t.firstTabbableNode||c("fallbackFocus")}else null===e&&(e=c("fallbackFocus"));if(!e)throw new Error("Your focus-trap needs to have at least one focusable element");return e},d=function(){if(r.containerGroups=r.containers.map((function(e){var t=function(e,t){var n;return n=(t=t||{}).getShadowRoot?oe([e],t.includeContainer,{filter:he.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:fe}):ne(e,t.includeContainer,he.bind(null,t)),pe(n)}(e,s.tabbableOptions),n=function(e,t){return(t=t||{}).getShadowRoot?oe([e],t.includeContainer,{filter:de.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):ne(e,t.includeContainer,de.bind(null,t))}(e,s.tabbableOptions),o=t.length>0?t[0]:void 0,i=t.length>0?t[t.length-1]:void 0,r=n.find((function(e){return me(e)})),a=n.slice().reverse().find((function(e){return me(e)})),l=!!t.find((function(e){return se(e)>0}));return{container:e,tabbableNodes:t,focusableNodes:n,posTabIndexesFound:l,firstTabbableNode:o,lastTabbableNode:i,firstDomTabbableNode:r,lastDomTabbableNode:a,nextTabbableNode:function(e){var o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=t.indexOf(e);return i<0?o?n.slice(n.indexOf(e)+1).find((function(e){return me(e)})):n.slice(0,n.indexOf(e)).reverse().find((function(e){return me(e)})):t[i+(o?1:-1)]}}})),r.tabbableGroups=r.containerGroups.filter((function(e){return e.tabbableNodes.length>0})),r.tabbableGroups.length<=0&&!c("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(r.containerGroups.find((function(e){return e.posTabIndexesFound}))&&r.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},h=function(e){var t=e.activeElement;if(t)return t.shadowRoot&&null!==t.shadowRoot.activeElement?h(t.shadowRoot):t},f=function(e){!1!==e&&e!==h(document)&&(e&&e.focus?(e.focus({preventScroll:!!s.preventScroll}),r.mostRecentlyFocusedNode=e,function(e){return e.tagName&&"input"===e.tagName.toLowerCase()&&"function"==typeof e.select}(e)&&e.select()):f(u()))},p=function(e){var t=c("setReturnFocus",{params:[e]});return t||!1!==t&&e},m=function(e){var t=e.target,n=e.event,o=e.isBackward,i=void 0!==o&&o;t=t||Fe(n),d();var a=null;if(r.tabbableGroups.length>0){var u=l(t,n),h=u>=0?r.containerGroups[u]:void 0;if(u<0)a=i?r.tabbableGroups[r.tabbableGroups.length-1].lastTabbableNode:r.tabbableGroups[0].firstTabbableNode;else if(i){var f=r.tabbableGroups.findIndex((function(e){var n=e.firstTabbableNode;return t===n}));if(f<0&&(h.container===t||ge(t,s.tabbableOptions)&&!me(t,s.tabbableOptions)&&!h.nextTabbableNode(t,!1))&&(f=u),f>=0){var p=0===f?r.tabbableGroups.length-1:f-1,m=r.tabbableGroups[p];a=se(t)>=0?m.lastTabbableNode:m.lastDomTabbableNode}else Ie(n)||(a=h.nextTabbableNode(t,!1))}else{var v=r.tabbableGroups.findIndex((function(e){var n=e.lastTabbableNode;return t===n}));if(v<0&&(h.container===t||ge(t,s.tabbableOptions)&&!me(t,s.tabbableOptions)&&!h.nextTabbableNode(t))&&(v=u),v>=0){var g=v===r.tabbableGroups.length-1?0:v+1,b=r.tabbableGroups[g];a=se(t)>=0?b.firstTabbableNode:b.firstDomTabbableNode}else Ie(n)||(a=h.nextTabbableNode(t))}}else a=c("fallbackFocus");return a},v=function(e){var t=Fe(e);l(t,e)>=0||(Te(s.clickOutsideDeactivates,e)?n.deactivate({returnFocus:s.returnFocusOnDeactivate}):Te(s.allowOutsideClick,e)||e.preventDefault())},g=function(e){var t=Fe(e),n=l(t,e)>=0;if(n||t instanceof Document)n&&(r.mostRecentlyFocusedNode=t);else{var o;e.stopImmediatePropagation();var i=!0;if(r.mostRecentlyFocusedNode)if(se(r.mostRecentlyFocusedNode)>0){var a=l(r.mostRecentlyFocusedNode),c=r.containerGroups[a].tabbableNodes;if(c.length>0){var d=c.findIndex((function(e){return e===r.mostRecentlyFocusedNode}));d>=0&&(s.isKeyForward(r.recentNavEvent)?d+1<c.length&&(o=c[d+1],i=!1):d-1>=0&&(o=c[d-1],i=!1))}}else r.containerGroups.some((function(e){return e.tabbableNodes.some((function(e){return se(e)>0}))}))||(i=!1);else i=!1;i&&(o=m({target:r.mostRecentlyFocusedNode,isBackward:s.isKeyBackward(r.recentNavEvent)})),f(o||(r.mostRecentlyFocusedNode||u()))}r.recentNavEvent=void 0},b=function(e){(s.isKeyForward(e)||s.isKeyBackward(e))&&function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];r.recentNavEvent=e;var n=m({event:e,isBackward:t});n&&(Ie(e)&&e.preventDefault(),f(n))}(e,s.isKeyBackward(e))},y=function(e){var t;"Escape"!==(null==(t=e)?void 0:t.key)&&"Esc"!==(null==t?void 0:t.key)&&27!==(null==t?void 0:t.keyCode)||!1===Te(s.escapeDeactivates,e)||(e.preventDefault(),n.deactivate())},w=function(e){var t=Fe(e);l(t,e)>=0||Te(s.clickOutsideDeactivates,e)||Te(s.allowOutsideClick,e)||(e.preventDefault(),e.stopImmediatePropagation())},x=function(){if(r.active)return Se(i,n),r.delayInitialFocusTimer=s.delayInitialFocus?Ne((function(){f(u())})):f(u()),o.addEventListener("focusin",g,!0),o.addEventListener("mousedown",v,{capture:!0,passive:!1}),o.addEventListener("touchstart",v,{capture:!0,passive:!1}),o.addEventListener("click",w,{capture:!0,passive:!1}),o.addEventListener("keydown",b,{capture:!0,passive:!1}),o.addEventListener("keydown",y),n},_=function(){if(r.active)return o.removeEventListener("focusin",g,!0),o.removeEventListener("mousedown",v,!0),o.removeEventListener("touchstart",v,!0),o.removeEventListener("click",w,!0),o.removeEventListener("keydown",b,!0),o.removeEventListener("keydown",y),n},S="undefined"!=typeof window&&"MutationObserver"in window?new MutationObserver((function(e){e.some((function(e){return Array.from(e.removedNodes).some((function(e){return e===r.mostRecentlyFocusedNode}))}))&&f(u())})):void 0,E=function(){S&&(S.disconnect(),r.active&&!r.paused&&r.containers.map((function(e){S.observe(e,{subtree:!0,childList:!0})})))};return n={get active(){return r.active},get paused(){return r.paused},activate:function(e){if(r.active)return this;var t=a(e,"onActivate"),n=a(e,"onPostActivate"),i=a(e,"checkCanFocusTrap");i||d(),r.active=!0,r.paused=!1,r.nodeFocusedBeforeActivation=o.activeElement,null==t||t();var s=function(){i&&d(),x(),E(),null==n||n()};return i?(i(r.containers.concat()).then(s,s),this):(s(),this)},deactivate:function(e){if(!r.active)return this;var t=xe({onDeactivate:s.onDeactivate,onPostDeactivate:s.onPostDeactivate,checkCanReturnFocus:s.checkCanReturnFocus},e);clearTimeout(r.delayInitialFocusTimer),r.delayInitialFocusTimer=void 0,_(),r.active=!1,r.paused=!1,E(),Ee(i,n);var o=a(t,"onDeactivate"),l=a(t,"onPostDeactivate"),c=a(t,"checkCanReturnFocus"),u=a(t,"returnFocus","returnFocusOnDeactivate");null==o||o();var d=function(){Ne((function(){u&&f(p(r.nodeFocusedBeforeActivation)),null==l||l()}))};return u&&c?(c(p(r.nodeFocusedBeforeActivation)).then(d,d),this):(d(),this)},pause:function(e){return r.active?(r.manuallyPaused=!0,this._setPausedState(!0,e)):this},unpause:function(e){return r.active?(r.manuallyPaused=!1,i[i.length-1]!==this?this:this._setPausedState(!1,e)):this},updateContainerElements:function(e){var t=[].concat(e).filter(Boolean);return r.containers=t.map((function(e){return"string"==typeof e?o.querySelector(e):e})),r.active&&d(),E(),this}},Object.defineProperties(n,{_isManuallyPaused:{value:function(){return r.manuallyPaused}},_setPausedState:{value:function(e,t){if(r.paused===e)return this;if(r.paused=e,e){var n=a(t,"onPause"),o=a(t,"onPostPause");null==n||n(),_(),E(),null==o||o()}else{var i=a(t,"onUnpause"),s=a(t,"onPostUnpause");null==i||i(),d(),x(),E(),null==s||s()}return this}}}),n.updateContainerElements(e),n};class Me{constructor(e,t=!0,n=[],o=5e3){this.ctx=e,this.iframes=t,this.exclude=n,this.iframesTimeout=o}static matches(e,t){const n="string"==typeof t?[t]:t,o=e.matches||e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector;if(o){let t=!1;return n.every((n=>!o.call(e,n)||(t=!0,!1))),t}return!1}getContexts(){let e,t=[];return e=void 0!==this.ctx&&this.ctx?NodeList.prototype.isPrototypeOf(this.ctx)?Array.prototype.slice.call(this.ctx):Array.isArray(this.ctx)?this.ctx:"string"==typeof this.ctx?Array.prototype.slice.call(document.querySelectorAll(this.ctx)):[this.ctx]:[],e.forEach((e=>{const n=t.filter((t=>t.contains(e))).length>0;-1!==t.indexOf(e)||n||t.push(e)})),t}getIframeContents(e,t,n=()=>{}){let o;try{const t=e.contentWindow;if(o=t.document,!t||!o)throw new Error("iframe inaccessible")}catch(i){n()}o&&t(o)}isIframeBlank(e){const t="about:blank",n=e.getAttribute("src").trim();return e.contentWindow.location.href===t&&n!==t&&n}observeIframeLoad(e,t,n){let o=!1,i=null;const s=()=>{if(!o){o=!0,clearTimeout(i);try{this.isIframeBlank(e)||(e.removeEventListener("load",s),this.getIframeContents(e,t,n))}catch(r){n()}}};e.addEventListener("load",s),i=setTimeout(s,this.iframesTimeout)}onIframeReady(e,t,n){try{"complete"===e.contentWindow.document.readyState?this.isIframeBlank(e)?this.observeIframeLoad(e,t,n):this.getIframeContents(e,t,n):this.observeIframeLoad(e,t,n)}catch(o){n()}}waitForIframes(e,t){let n=0;this.forEachIframe(e,(()=>!0),(e=>{n++,this.waitForIframes(e.querySelector("html"),(()=>{--n||t()}))}),(e=>{e||t()}))}forEachIframe(e,t,n,o=()=>{}){let i=e.querySelectorAll("iframe"),s=i.length,r=0;i=Array.prototype.slice.call(i);const a=()=>{--s<=0&&o(r)};s||a(),i.forEach((e=>{Me.matches(e,this.exclude)?a():this.onIframeReady(e,(o=>{t(e)&&(r++,n(o)),a()}),a)}))}createIterator(e,t,n){return document.createNodeIterator(e,t,n,!1)}createInstanceOnIframe(e){return new Me(e.querySelector("html"),this.iframes)}compareNodeIframe(e,t,n){if(e.compareDocumentPosition(n)&Node.DOCUMENT_POSITION_PRECEDING){if(null===t)return!0;if(t.compareDocumentPosition(n)&Node.DOCUMENT_POSITION_FOLLOWING)return!0}return!1}getIteratorNode(e){const t=e.previousNode();let n;return n=(null===t||e.nextNode())&&e.nextNode(),{prevNode:t,node:n}}checkIframeFilter(e,t,n,o){let i=!1,s=!1;return o.forEach(((e,t)=>{e.val===n&&(i=t,s=e.handled)})),this.compareNodeIframe(e,t,n)?(!1!==i||s?!1===i||s||(o[i].handled=!0):o.push({val:n,handled:!0}),!0):(!1===i&&o.push({val:n,handled:!1}),!1)}handleOpenIframes(e,t,n,o){e.forEach((e=>{e.handled||this.getIframeContents(e.val,(e=>{this.createInstanceOnIframe(e).forEachNode(t,n,o)}))}))}iterateThroughNodes(e,t,n,o,i){const s=this.createIterator(t,e,o);let r,a,l=[],c=[],u=()=>(({prevNode:a,node:r}=this.getIteratorNode(s)),r);for(;u();)this.iframes&&this.forEachIframe(t,(e=>this.checkIframeFilter(r,a,e,l)),(t=>{this.createInstanceOnIframe(t).forEachNode(e,(e=>c.push(e)),o)})),c.push(r);c.forEach((e=>{n(e)})),this.iframes&&this.handleOpenIframes(l,e,n,o),i()}forEachNode(e,t,n,o=()=>{}){const i=this.getContexts();let s=i.length;s||o(),i.forEach((i=>{const r=()=>{this.iterateThroughNodes(e,i,t,n,(()=>{--s<=0&&o()}))};this.iframes?this.waitForIframes(i,r):r()}))}}let Ae=class{constructor(e){this.ctx=e,this.ie=!1;const t=window.navigator.userAgent;(t.indexOf("MSIE")>-1||t.indexOf("Trident")>-1)&&(this.ie=!0)}set opt(e){this._opt=Object.assign({},{element:"",className:"",exclude:[],iframes:!1,iframesTimeout:5e3,separateWordSearch:!0,diacritics:!0,synonyms:{},accuracy:"partially",acrossElements:!1,caseSensitive:!1,ignoreJoiners:!1,ignoreGroups:0,ignorePunctuation:[],wildcards:"disabled",each:()=>{},noMatch:()=>{},filter:()=>!0,done:()=>{},debug:!1,log:window.console},e)}get opt(){return this._opt}get iterator(){return new Me(this.ctx,this.opt.iframes,this.opt.exclude,this.opt.iframesTimeout)}log(e,t="debug"){const n=this.opt.log;this.opt.debug&&"object"==typeof n&&"function"==typeof n[t]&&n[t](`mark.js: ${e}`)}escapeStr(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}createRegExp(e){return"disabled"!==this.opt.wildcards&&(e=this.setupWildcardsRegExp(e)),e=this.escapeStr(e),Object.keys(this.opt.synonyms).length&&(e=this.createSynonymsRegExp(e)),(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.setupIgnoreJoinersRegExp(e)),this.opt.diacritics&&(e=this.createDiacriticsRegExp(e)),e=this.createMergedBlanksRegExp(e),(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.createJoinersRegExp(e)),"disabled"!==this.opt.wildcards&&(e=this.createWildcardsRegExp(e)),e=this.createAccuracyRegExp(e)}createSynonymsRegExp(e){const t=this.opt.synonyms,n=this.opt.caseSensitive?"":"i",o=this.opt.ignoreJoiners||this.opt.ignorePunctuation.length?"\0":"";for(let i in t)if(t.hasOwnProperty(i)){const s=t[i],r="disabled"!==this.opt.wildcards?this.setupWildcardsRegExp(i):this.escapeStr(i),a="disabled"!==this.opt.wildcards?this.setupWildcardsRegExp(s):this.escapeStr(s);""!==r&&""!==a&&(e=e.replace(new RegExp(`(${this.escapeStr(r)}|${this.escapeStr(a)})`,`gm${n}`),o+`(${this.processSynomyms(r)}|${this.processSynomyms(a)})`+o))}return e}processSynomyms(e){return(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.setupIgnoreJoinersRegExp(e)),e}setupWildcardsRegExp(e){return(e=e.replace(/(?:\\)*\?/g,(e=>"\\"===e.charAt(0)?"?":""))).replace(/(?:\\)*\*/g,(e=>"\\"===e.charAt(0)?"*":""))}createWildcardsRegExp(e){let t="withSpaces"===this.opt.wildcards;return e.replace(/\u0001/g,t?"[\\S\\s]?":"\\S?").replace(/\u0002/g,t?"[\\S\\s]*?":"\\S*")}setupIgnoreJoinersRegExp(e){return e.replace(/[^(|)\\]/g,((e,t,n)=>{let o=n.charAt(t+1);return/[(|)\\]/.test(o)||""===o?e:e+"\0"}))}createJoinersRegExp(e){let t=[];const n=this.opt.ignorePunctuation;return Array.isArray(n)&&n.length&&t.push(this.escapeStr(n.join(""))),this.opt.ignoreJoiners&&t.push("\\u00ad\\u200b\\u200c\\u200d"),t.length?e.split(/\u0000+/).join(`[${t.join("")}]*`):e}createDiacriticsRegExp(e){const t=this.opt.caseSensitive?"":"i",n=this.opt.caseSensitive?["aàáảãạăằắẳẵặâầấẩẫậäåāą","AÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ","cçćč","CÇĆČ","dđď","DĐĎ","eèéẻẽẹêềếểễệëěēę","EÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ","iìíỉĩịîïī","IÌÍỈĨỊÎÏĪ","lł","LŁ","nñňń","NÑŇŃ","oòóỏõọôồốổỗộơởỡớờợöøō","OÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ","rř","RŘ","sšśșş","SŠŚȘŞ","tťțţ","TŤȚŢ","uùúủũụưừứửữựûüůū","UÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ","yýỳỷỹỵÿ","YÝỲỶỸỴŸ","zžżź","ZŽŻŹ"]:["aàáảãạăằắẳẵặâầấẩẫậäåāąAÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ","cçćčCÇĆČ","dđďDĐĎ","eèéẻẽẹêềếểễệëěēęEÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ","iìíỉĩịîïīIÌÍỈĨỊÎÏĪ","lłLŁ","nñňńNÑŇŃ","oòóỏõọôồốổỗộơởỡớờợöøōOÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ","rřRŘ","sšśșşSŠŚȘŞ","tťțţTŤȚŢ","uùúủũụưừứửữựûüůūUÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ","yýỳỷỹỵÿYÝỲỶỸỴŸ","zžżźZŽŻŹ"];let o=[];return e.split("").forEach((i=>{n.every((n=>{if(-1!==n.indexOf(i)){if(o.indexOf(n)>-1)return!1;e=e.replace(new RegExp(`[${n}]`,`gm${t}`),`[${n}]`),o.push(n)}return!0}))})),e}createMergedBlanksRegExp(e){return e.replace(/[\s]+/gim,"[\\s]+")}createAccuracyRegExp(e){let t=this.opt.accuracy,n="string"==typeof t?t:t.value,o="string"==typeof t?[]:t.limiters,i="";switch(o.forEach((e=>{i+=`|${this.escapeStr(e)}`})),n){case"partially":default:return`()(${e})`;case"complementary":return i="\\s"+(i||this.escapeStr("!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~¡¿")),`()([^${i}]*${e}[^${i}]*)`;case"exactly":return`(^|\\s${i})(${e})(?=$|\\s${i})`}}getSeparatedKeywords(e){let t=[];return e.forEach((e=>{this.opt.separateWordSearch?e.split(" ").forEach((e=>{e.trim()&&-1===t.indexOf(e)&&t.push(e)})):e.trim()&&-1===t.indexOf(e)&&t.push(e)})),{keywords:t.sort(((e,t)=>t.length-e.length)),length:t.length}}isNumeric(e){return Number(parseFloat(e))==e}checkRanges(e){if(!Array.isArray(e)||"[object Object]"!==Object.prototype.toString.call(e[0]))return this.log("markRanges() will only accept an array of objects"),this.opt.noMatch(e),[];const t=[];let n=0;return e.sort(((e,t)=>e.start-t.start)).forEach((e=>{let{start:o,end:i,valid:s}=this.callNoMatchOnInvalidRanges(e,n);s&&(e.start=o,e.length=i-o,t.push(e),n=i)})),t}callNoMatchOnInvalidRanges(e,t){let n,o,i=!1;return e&&void 0!==e.start?(n=parseInt(e.start,10),o=n+parseInt(e.length,10),this.isNumeric(e.start)&&this.isNumeric(e.length)&&o-t>0&&o-n>0?i=!0:(this.log(`Ignoring invalid or overlapping range: ${JSON.stringify(e)}`),this.opt.noMatch(e))):(this.log(`Ignoring invalid range: ${JSON.stringify(e)}`),this.opt.noMatch(e)),{start:n,end:o,valid:i}}checkWhitespaceRanges(e,t,n){let o,i=!0,s=n.length,r=t-s,a=parseInt(e.start,10)-r;return a=a>s?s:a,o=a+parseInt(e.length,10),o>s&&(o=s,this.log(`End range automatically set to the max value of ${s}`)),a<0||o-a<0||a>s||o>s?(i=!1,this.log(`Invalid range: ${JSON.stringify(e)}`),this.opt.noMatch(e)):""===n.substring(a,o).replace(/\s+/g,"")&&(i=!1,this.log("Skipping whitespace only range: "+JSON.stringify(e)),this.opt.noMatch(e)),{start:a,end:o,valid:i}}getTextNodes(e){let t="",n=[];this.iterator.forEachNode(NodeFilter.SHOW_TEXT,(e=>{n.push({start:t.length,end:(t+=e.textContent).length,node:e})}),(e=>this.matchesExclude(e.parentNode)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT),(()=>{e({value:t,nodes:n})}))}matchesExclude(e){return Me.matches(e,this.opt.exclude.concat(["script","style","title","head","html"]))}wrapRangeInTextNode(e,t,n){const o=this.opt.element?this.opt.element:"mark",i=e.splitText(t),s=i.splitText(n-t);let r=document.createElement(o);return r.setAttribute("data-markjs","true"),this.opt.className&&r.setAttribute("class",this.opt.className),r.textContent=i.textContent,i.parentNode.replaceChild(r,i),s}wrapRangeInMappedTextNode(e,t,n,o,i){e.nodes.every(((s,r)=>{const a=e.nodes[r+1];if(void 0===a||a.start>t){if(!o(s.node))return!1;const a=t-s.start,l=(n>s.end?s.end:n)-s.start,c=e.value.substr(0,s.start),u=e.value.substr(l+s.start);if(s.node=this.wrapRangeInTextNode(s.node,a,l),e.value=c+u,e.nodes.forEach(((t,n)=>{n>=r&&(e.nodes[n].start>0&&n!==r&&(e.nodes[n].start-=l),e.nodes[n].end-=l)})),n-=l,i(s.node.previousSibling,s.start),!(n>s.end))return!1;t=s.end}return!0}))}wrapMatches(e,t,n,o,i){const s=0===t?0:t+1;this.getTextNodes((t=>{t.nodes.forEach((t=>{let i;for(t=t.node;null!==(i=e.exec(t.textContent))&&""!==i[s];){if(!n(i[s],t))continue;let r=i.index;if(0!==s)for(let e=1;e<s;e++)r+=i[e].length;t=this.wrapRangeInTextNode(t,r,r+i[s].length),o(t.previousSibling),e.lastIndex=0}})),i()}))}wrapMatchesAcrossElements(e,t,n,o,i){const s=0===t?0:t+1;this.getTextNodes((t=>{let r;for(;null!==(r=e.exec(t.value))&&""!==r[s];){let i=r.index;if(0!==s)for(let e=1;e<s;e++)i+=r[e].length;const a=i+r[s].length;this.wrapRangeInMappedTextNode(t,i,a,(e=>n(r[s],e)),((t,n)=>{e.lastIndex=n,o(t)}))}i()}))}wrapRangeFromIndex(e,t,n,o){this.getTextNodes((i=>{const s=i.value.length;e.forEach(((e,o)=>{let{start:r,end:a,valid:l}=this.checkWhitespaceRanges(e,s,i.value);l&&this.wrapRangeInMappedTextNode(i,r,a,(n=>t(n,e,i.value.substring(r,a),o)),(t=>{n(t,e)}))})),o()}))}unwrapMatches(e){const t=e.parentNode;let n=document.createDocumentFragment();for(;e.firstChild;)n.appendChild(e.removeChild(e.firstChild));t.replaceChild(n,e),this.ie?this.normalizeTextNode(t):t.normalize()}normalizeTextNode(e){if(e){if(3===e.nodeType)for(;e.nextSibling&&3===e.nextSibling.nodeType;)e.nodeValue+=e.nextSibling.nodeValue,e.parentNode.removeChild(e.nextSibling);else this.normalizeTextNode(e.firstChild);this.normalizeTextNode(e.nextSibling)}}markRegExp(e,t){this.opt=t,this.log(`Searching with expression "${e}"`);let n=0,o="wrapMatches";this.opt.acrossElements&&(o="wrapMatchesAcrossElements"),this[o](e,this.opt.ignoreGroups,((e,t)=>this.opt.filter(t,e,n)),(e=>{n++,this.opt.each(e)}),(()=>{0===n&&this.opt.noMatch(e),this.opt.done(n)}))}mark(e,t){this.opt=t;let n=0,o="wrapMatches";const{keywords:i,length:s}=this.getSeparatedKeywords("string"==typeof e?[e]:e),r=this.opt.caseSensitive?"":"i",a=e=>{let t=new RegExp(this.createRegExp(e),`gm${r}`),l=0;this.log(`Searching with expression "${t}"`),this[o](t,1,((t,o)=>this.opt.filter(o,e,n,l)),(e=>{l++,n++,this.opt.each(e)}),(()=>{0===l&&this.opt.noMatch(e),i[s-1]===e?this.opt.done(n):a(i[i.indexOf(e)+1])}))};this.opt.acrossElements&&(o="wrapMatchesAcrossElements"),0===s?this.opt.done(n):a(i[0])}markRanges(e,t){this.opt=t;let n=0,o=this.checkRanges(e);o&&o.length?(this.log("Starting to mark with the following ranges: "+JSON.stringify(o)),this.wrapRangeFromIndex(o,((e,t,n,o)=>this.opt.filter(e,t,n,o)),((e,t)=>{n++,this.opt.each(e,t)}),(()=>{this.opt.done(n)}))):this.opt.done(n)}unmark(e){this.opt=e;let t=this.opt.element?this.opt.element:"*";t+="[data-markjs]",this.opt.className&&(t+=`.${this.opt.className}`),this.log(`Removal selector "${t}"`),this.iterator.forEachNode(NodeFilter.SHOW_ELEMENT,(e=>{this.unwrapMatches(e)}),(e=>{const n=Me.matches(e,t),o=this.matchesExclude(e);return!n||o?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT}),this.opt.done)}};function Le(e){const t=new Ae(e);return this.mark=(e,n)=>(t.mark(e,n),this),this.markRegExp=(e,n)=>(t.markRegExp(e,n),this),this.markRanges=(e,n)=>(t.markRanges(e,n),this),this.unmark=e=>(t.unmark(e),this),this}function De(e,t,n,o){return new(n||(n=Promise))((function(t,i){function s(e){try{a(o.next(e))}catch(t){i(t)}}function r(e){try{a(o.throw(e))}catch(t){i(t)}}function a(e){var o;e.done?t(e.value):(o=e.value,o instanceof n?o:new n((function(e){e(o)}))).then(s,r)}a((o=o.apply(e,[])).next())}))}"function"==typeof SuppressedError&&SuppressedError;const je="KEYS",ze="VALUES",Pe="";class Ve{constructor(e,t){const n=e._tree,o=Array.from(n.keys());this.set=e,this._type=t,this._path=o.length>0?[{node:n,keys:o}]:[]}next(){const e=this.dive();return this.backtrack(),e}dive(){if(0===this._path.length)return{done:!0,value:void 0};const{node:e,keys:t}=$e(this._path);if($e(t)===Pe)return{done:!1,value:this.result()};const n=e.get($e(t));return this._path.push({node:n,keys:Array.from(n.keys())}),this.dive()}backtrack(){if(0===this._path.length)return;const e=$e(this._path).keys;e.pop(),e.length>0||(this._path.pop(),this.backtrack())}key(){return this.set._prefix+this._path.map((({keys:e})=>$e(e))).filter((e=>e!==Pe)).join("")}value(){return $e(this._path).node.get(Pe)}result(){switch(this._type){case ze:return this.value();case je:return this.key();default:return[this.key(),this.value()]}}[Symbol.iterator](){return this}}const $e=e=>e[e.length-1],We=(e,t,n,o,i,s,r,a)=>{const l=s*r;e:for(const c of e.keys())if(c===Pe){const t=i[l-1];t<=n&&o.set(a,[e.get(c),t])}else{let l=s;for(let e=0;e<c.length;++e,++l){const o=c[e],s=r*l,a=s-r;let u=i[s];const d=Math.max(0,l-n-1),h=Math.min(r-1,l+n);for(let e=d;e<h;++e){const n=o!==t[e],r=i[a+e]+ +n,l=i[a+e+1]+1,c=i[s+e]+1,d=i[s+e+1]=Math.min(r,l,c);d<u&&(u=d)}if(u>n)continue e}We(e.get(c),t,n,o,i,l,r,a+c)}};class Je{constructor(e=new Map,t=""){this._size=void 0,this._tree=e,this._prefix=t}atPrefix(e){if(!e.startsWith(this._prefix))throw new Error("Mismatched prefix");const[t,n]=Ge(this._tree,e.slice(this._prefix.length));if(void 0===t){const[t,o]=Qe(n);for(const n of t.keys())if(n!==Pe&&n.startsWith(o)){const i=new Map;return i.set(n.slice(o.length),t.get(n)),new Je(i,e)}}return new Je(t,e)}clear(){this._size=void 0,this._tree.clear()}delete(e){return this._size=void 0,Ke(this._tree,e)}entries(){return new Ve(this,"ENTRIES")}forEach(e){for(const[t,n]of this)e(t,n,this)}fuzzyGet(e,t){return((e,t,n)=>{const o=new Map;if(void 0===t)return o;const i=t.length+1,s=i+n,r=new Uint8Array(s*i).fill(n+1);for(let a=0;a<i;++a)r[a]=a;for(let a=1;a<s;++a)r[a*i]=a;return We(e,t,n,o,r,1,i,""),o})(this._tree,e,t)}get(e){const t=Be(this._tree,e);return void 0!==t?t.get(Pe):void 0}has(e){const t=Be(this._tree,e);return void 0!==t&&t.has(Pe)}keys(){return new Ve(this,je)}set(e,t){if("string"!=typeof e)throw new Error("key must be a string");this._size=void 0;return qe(this._tree,e).set(Pe,t),this}get size(){if(this._size)return this._size;this._size=0;const e=this.entries();for(;!e.next().done;)this._size+=1;return this._size}update(e,t){if("string"!=typeof e)throw new Error("key must be a string");this._size=void 0;const n=qe(this._tree,e);return n.set(Pe,t(n.get(Pe))),this}fetch(e,t){if("string"!=typeof e)throw new Error("key must be a string");this._size=void 0;const n=qe(this._tree,e);let o=n.get(Pe);return void 0===o&&n.set(Pe,o=t()),o}values(){return new Ve(this,ze)}[Symbol.iterator](){return this.entries()}static from(e){const t=new Je;for(const[n,o]of e)t.set(n,o);return t}static fromObject(e){return Je.from(Object.entries(e))}}const Ge=(e,t,n=[])=>{if(0===t.length||null==e)return[e,n];for(const o of e.keys())if(o!==Pe&&t.startsWith(o))return n.push([e,o]),Ge(e.get(o),t.slice(o.length),n);return n.push([e,t]),Ge(void 0,"",n)},Be=(e,t)=>{if(0===t.length||null==e)return e;for(const n of e.keys())if(n!==Pe&&t.startsWith(n))return Be(e.get(n),t.slice(n.length))},qe=(e,t)=>{const n=t.length;e:for(let o=0;e&&o<n;){for(const s of e.keys())if(s!==Pe&&t[o]===s[0]){const i=Math.min(n-o,s.length);let r=1;for(;r<i&&t[o+r]===s[r];)++r;const a=e.get(s);if(r===s.length)e=a;else{const n=new Map;n.set(s.slice(r),a),e.set(t.slice(o,o+r),n),e.delete(s),e=n}o+=r;continue e}const i=new Map;return e.set(t.slice(o),i),i}return e},Ke=(e,t)=>{const[n,o]=Ge(e,t);if(void 0!==n)if(n.delete(Pe),0===n.size)Ue(o);else if(1===n.size){const[e,t]=n.entries().next().value;He(o,e,t)}},Ue=e=>{if(0===e.length)return;const[t,n]=Qe(e);if(t.delete(n),0===t.size)Ue(e.slice(0,-1));else if(1===t.size){const[n,o]=t.entries().next().value;n!==Pe&&He(e.slice(0,-1),n,o)}},He=(e,t,n)=>{if(0===e.length)return;const[o,i]=Qe(e);o.set(i+t,n),o.delete(i)},Qe=e=>e[e.length-1],Ye="or",Xe="and",Ze="and_not";class et{constructor(e){if(null==(null==e?void 0:e.fields))throw new Error('MiniSearch: option "fields" must be provided');const t=null==e.autoVacuum||!0===e.autoVacuum?ut:e.autoVacuum;this._options=Object.assign(Object.assign(Object.assign({},st),e),{autoVacuum:t,searchOptions:Object.assign(Object.assign({},rt),e.searchOptions||{}),autoSuggestOptions:Object.assign(Object.assign({},at),e.autoSuggestOptions||{})}),this._index=new Je,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldIds={},this._fieldLength=new Map,this._avgFieldLength=[],this._nextId=0,this._storedFields=new Map,this._dirtCount=0,this._currentVacuum=null,this._enqueuedVacuum=null,this._enqueuedVacuumConditions=ct,this.addFields(this._options.fields)}add(e){const{extractField:t,tokenize:n,processTerm:o,fields:i,idField:s}=this._options,r=t(e,s);if(null==r)throw new Error(`MiniSearch: document does not have ID field "${s}"`);if(this._idToShortId.has(r))throw new Error(`MiniSearch: duplicate ID ${r}`);const a=this.addDocumentId(r);this.saveStoredFields(a,e);for(const l of i){const i=t(e,l);if(null==i)continue;const s=n(i.toString(),l),r=this._fieldIds[l],c=new Set(s).size;this.addFieldLength(a,r,this._documentCount-1,c);for(const e of s){const t=o(e,l);if(Array.isArray(t))for(const e of t)this.addTerm(r,a,e);else t&&this.addTerm(r,a,t)}}}addAll(e){for(const t of e)this.add(t)}addAllAsync(e,t={}){const{chunkSize:n=10}=t,o={chunk:[],promise:Promise.resolve()},{chunk:i,promise:s}=e.reduce((({chunk:e,promise:t},o,i)=>(e.push(o),(i+1)%n==0?{chunk:[],promise:t.then((()=>new Promise((e=>setTimeout(e,0))))).then((()=>this.addAll(e)))}:{chunk:e,promise:t})),o);return s.then((()=>this.addAll(i)))}remove(e){const{tokenize:t,processTerm:n,extractField:o,fields:i,idField:s}=this._options,r=o(e,s);if(null==r)throw new Error(`MiniSearch: document does not have ID field "${s}"`);const a=this._idToShortId.get(r);if(null==a)throw new Error(`MiniSearch: cannot remove document with ID ${r}: it is not in the index`);for(const l of i){const i=o(e,l);if(null==i)continue;const s=t(i.toString(),l),r=this._fieldIds[l],c=new Set(s).size;this.removeFieldLength(a,r,this._documentCount,c);for(const e of s){const t=n(e,l);if(Array.isArray(t))for(const e of t)this.removeTerm(r,a,e);else t&&this.removeTerm(r,a,t)}}this._storedFields.delete(a),this._documentIds.delete(a),this._idToShortId.delete(r),this._fieldLength.delete(a),this._documentCount-=1}removeAll(e){if(e)for(const t of e)this.remove(t);else{if(arguments.length>0)throw new Error("Expected documents to be present. Omit the argument to remove all documents.");this._index=new Je,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldLength=new Map,this._avgFieldLength=[],this._storedFields=new Map,this._nextId=0}}discard(e){const t=this._idToShortId.get(e);if(null==t)throw new Error(`MiniSearch: cannot discard document with ID ${e}: it is not in the index`);this._idToShortId.delete(e),this._documentIds.delete(t),this._storedFields.delete(t),(this._fieldLength.get(t)||[]).forEach(((e,n)=>{this.removeFieldLength(t,n,this._documentCount,e)})),this._fieldLength.delete(t),this._documentCount-=1,this._dirtCount+=1,this.maybeAutoVacuum()}maybeAutoVacuum(){if(!1===this._options.autoVacuum)return;const{minDirtFactor:e,minDirtCount:t,batchSize:n,batchWait:o}=this._options.autoVacuum;this.conditionalVacuum({batchSize:n,batchWait:o},{minDirtCount:t,minDirtFactor:e})}discardAll(e){const t=this._options.autoVacuum;try{this._options.autoVacuum=!1;for(const t of e)this.discard(t)}finally{this._options.autoVacuum=t}this.maybeAutoVacuum()}replace(e){const{idField:t,extractField:n}=this._options,o=n(e,t);this.discard(o),this.add(e)}vacuum(e={}){return this.conditionalVacuum(e)}conditionalVacuum(e,t){return this._currentVacuum?(this._enqueuedVacuumConditions=this._enqueuedVacuumConditions&&t,null!=this._enqueuedVacuum||(this._enqueuedVacuum=this._currentVacuum.then((()=>{const t=this._enqueuedVacuumConditions;return this._enqueuedVacuumConditions=ct,this.performVacuuming(e,t)}))),this._enqueuedVacuum):!1===this.vacuumConditionsMet(t)?Promise.resolve():(this._currentVacuum=this.performVacuuming(e),this._currentVacuum)}performVacuuming(e,t){return De(this,0,void 0,(function*(){const n=this._dirtCount;if(this.vacuumConditionsMet(t)){const t=e.batchSize||lt.batchSize,o=e.batchWait||lt.batchWait;let i=1;for(const[e,n]of this._index){for(const[e,t]of n)for(const[o]of t)this._documentIds.has(o)||(t.size<=1?n.delete(e):t.delete(o));0===this._index.get(e).size&&this._index.delete(e),i%t==0&&(yield new Promise((e=>setTimeout(e,o)))),i+=1}this._dirtCount-=n}yield null,this._currentVacuum=this._enqueuedVacuum,this._enqueuedVacuum=null}))}vacuumConditionsMet(e){if(null==e)return!0;let{minDirtCount:t,minDirtFactor:n}=e;return t=t||ut.minDirtCount,n=n||ut.minDirtFactor,this.dirtCount>=t&&this.dirtFactor>=n}get isVacuuming(){return null!=this._currentVacuum}get dirtCount(){return this._dirtCount}get dirtFactor(){return this._dirtCount/(1+this._documentCount+this._dirtCount)}has(e){return this._idToShortId.has(e)}getStoredFields(e){const t=this._idToShortId.get(e);if(null!=t)return this._storedFields.get(t)}search(e,t={}){const{searchOptions:n}=this._options,o=Object.assign(Object.assign({},n),t),i=this.executeQuery(e,t),s=[];for(const[r,{score:a,terms:l,match:c}]of i){const e=l.length||1,t={id:this._documentIds.get(r),score:a*e,terms:Object.keys(c),queryTerms:l,match:c};Object.assign(t,this._storedFields.get(r)),(null==o.filter||o.filter(t))&&s.push(t)}return e===et.wildcard&&null==o.boostDocument||s.sort(ft),s}autoSuggest(e,t={}){t=Object.assign(Object.assign({},this._options.autoSuggestOptions),t);const n=new Map;for(const{score:i,terms:s}of this.search(e,t)){const e=s.join(" "),t=n.get(e);null!=t?(t.score+=i,t.count+=1):n.set(e,{score:i,terms:s,count:1})}const o=[];for(const[i,{score:s,terms:r,count:a}]of n)o.push({suggestion:i,terms:r,score:s/a});return o.sort(ft),o}get documentCount(){return this._documentCount}get termCount(){return this._index.size}static loadJSON(e,t){if(null==t)throw new Error("MiniSearch: loadJSON should be given the same options used when serializing the index");return this.loadJS(JSON.parse(e),t)}static loadJSONAsync(e,t){return De(this,0,void 0,(function*(){if(null==t)throw new Error("MiniSearch: loadJSON should be given the same options used when serializing the index");return this.loadJSAsync(JSON.parse(e),t)}))}static getDefault(e){if(st.hasOwnProperty(e))return tt(st,e);throw new Error(`MiniSearch: unknown option "${e}"`)}static loadJS(e,t){const{index:n,documentIds:o,fieldLength:i,storedFields:s,serializationVersion:r}=e,a=this.instantiateMiniSearch(e,t);a._documentIds=mt(o),a._fieldLength=mt(i),a._storedFields=mt(s);for(const[l,c]of a._documentIds)a._idToShortId.set(c,l);for(const[l,c]of n){const e=new Map;for(const t of Object.keys(c)){let n=c[t];1===r&&(n=n.ds),e.set(parseInt(t,10),mt(n))}a._index.set(l,e)}return a}static loadJSAsync(e,t){return De(this,0,void 0,(function*(){const{index:n,documentIds:o,fieldLength:i,storedFields:s,serializationVersion:r}=e,a=this.instantiateMiniSearch(e,t);a._documentIds=yield vt(o),a._fieldLength=yield vt(i),a._storedFields=yield vt(s);for(const[e,t]of a._documentIds)a._idToShortId.set(t,e);let l=0;for(const[e,t]of n){const n=new Map;for(const e of Object.keys(t)){let o=t[e];1===r&&(o=o.ds),n.set(parseInt(e,10),yield vt(o))}++l%1e3==0&&(yield gt(0)),a._index.set(e,n)}return a}))}static instantiateMiniSearch(e,t){const{documentCount:n,nextId:o,fieldIds:i,averageFieldLength:s,dirtCount:r,serializationVersion:a}=e;if(1!==a&&2!==a)throw new Error("MiniSearch: cannot deserialize an index created with an incompatible version");const l=new et(t);return l._documentCount=n,l._nextId=o,l._idToShortId=new Map,l._fieldIds=i,l._avgFieldLength=s,l._dirtCount=r||0,l._index=new Je,l}executeQuery(e,t={}){if(e===et.wildcard)return this.executeWildcardQuery(t);if("string"!=typeof e){const n=Object.assign(Object.assign(Object.assign({},t),e),{queries:void 0}),o=e.queries.map((e=>this.executeQuery(e,n)));return this.combineResults(o,n.combineWith)}const{tokenize:n,processTerm:o,searchOptions:i}=this._options,s=Object.assign(Object.assign({tokenize:n,processTerm:o},i),t),{tokenize:r,processTerm:a}=s,l=r(e).flatMap((e=>a(e))).filter((e=>!!e)).map(it(s)).map((e=>this.executeQuerySpec(e,s)));return this.combineResults(l,s.combineWith)}executeQuerySpec(e,t){const n=Object.assign(Object.assign({},this._options.searchOptions),t),o=(n.fields||this._options.fields).reduce(((e,t)=>Object.assign(Object.assign({},e),{[t]:tt(n.boost,t)||1})),{}),{boostDocument:i,weights:s,maxFuzzy:r,bm25:a}=n,{fuzzy:l,prefix:c}=Object.assign(Object.assign({},rt.weights),s),u=this._index.get(e.term),d=this.termResults(e.term,e.term,1,e.termBoost,u,o,i,a);let h,f;if(e.prefix&&(h=this._index.atPrefix(e.term)),e.fuzzy){const t=!0===e.fuzzy?.2:e.fuzzy,n=t<1?Math.min(r,Math.round(e.term.length*t)):t;n&&(f=this._index.fuzzyGet(e.term,n))}if(h)for(const[p,m]of h){const t=p.length-e.term.length;if(!t)continue;null==f||f.delete(p);const n=c*p.length/(p.length+.3*t);this.termResults(e.term,p,n,e.termBoost,m,o,i,a,d)}if(f)for(const p of f.keys()){const[t,n]=f.get(p);if(!n)continue;const s=l*p.length/(p.length+n);this.termResults(e.term,p,s,e.termBoost,t,o,i,a,d)}return d}executeWildcardQuery(e){const t=new Map,n=Object.assign(Object.assign({},this._options.searchOptions),e);for(const[o,i]of this._documentIds){const e=n.boostDocument?n.boostDocument(i,"",this._storedFields.get(o)):1;t.set(o,{score:e,terms:[],match:{}})}return t}combineResults(e,t=Ye){if(0===e.length)return new Map;const n=t.toLowerCase(),o=nt[n];if(!o)throw new Error(`Invalid combination operator: ${t}`);return e.reduce(o)||new Map}toJSON(){const e=[];for(const[t,n]of this._index){const o={};for(const[e,t]of n)o[e]=Object.fromEntries(t);e.push([t,o])}return{documentCount:this._documentCount,nextId:this._nextId,documentIds:Object.fromEntries(this._documentIds),fieldIds:this._fieldIds,fieldLength:Object.fromEntries(this._fieldLength),averageFieldLength:this._avgFieldLength,storedFields:Object.fromEntries(this._storedFields),dirtCount:this._dirtCount,index:e,serializationVersion:2}}termResults(e,t,n,o,i,s,r,a,l=new Map){if(null==i)return l;for(const c of Object.keys(s)){const u=s[c],d=this._fieldIds[c],h=i.get(d);if(null==h)continue;let f=h.size;const p=this._avgFieldLength[d];for(const i of h.keys()){if(!this._documentIds.has(i)){this.removeTerm(d,i,t),f-=1;continue}const s=r?r(this._documentIds.get(i),t,this._storedFields.get(i)):1;if(!s)continue;const m=h.get(i),v=this._fieldLength.get(i)[d],g=n*o*u*s*ot(m,f,this._documentCount,v,p,a),b=l.get(i);if(b){b.score+=g,dt(b.terms,e);const n=tt(b.match,t);n?n.push(c):b.match[t]=[c]}else l.set(i,{score:g,terms:[e],match:{[t]:[c]}})}}return l}addTerm(e,t,n){const o=this._index.fetch(n,pt);let i=o.get(e);if(null==i)i=new Map,i.set(t,1),o.set(e,i);else{const e=i.get(t);i.set(t,(e||0)+1)}}removeTerm(e,t,n){if(!this._index.has(n))return void this.warnDocumentChanged(t,e,n);const o=this._index.fetch(n,pt),i=o.get(e);null==i||null==i.get(t)?this.warnDocumentChanged(t,e,n):i.get(t)<=1?i.size<=1?o.delete(e):i.delete(t):i.set(t,i.get(t)-1),0===this._index.get(n).size&&this._index.delete(n)}warnDocumentChanged(e,t,n){for(const o of Object.keys(this._fieldIds))if(this._fieldIds[o]===t)return void this._options.logger("warn",`MiniSearch: document with ID ${this._documentIds.get(e)} has changed before removal: term "${n}" was not present in field "${o}". Removing a document after it has changed can corrupt the index!`,"version_conflict")}addDocumentId(e){const t=this._nextId;return this._idToShortId.set(e,t),this._documentIds.set(t,e),this._documentCount+=1,this._nextId+=1,t}addFields(e){for(let t=0;t<e.length;t++)this._fieldIds[e[t]]=t}addFieldLength(e,t,n,o){let i=this._fieldLength.get(e);null==i&&this._fieldLength.set(e,i=[]),i[t]=o;const s=(this._avgFieldLength[t]||0)*n+o;this._avgFieldLength[t]=s/(n+1)}removeFieldLength(e,t,n,o){if(1===n)return void(this._avgFieldLength[t]=0);const i=this._avgFieldLength[t]*n-o;this._avgFieldLength[t]=i/(n-1)}saveStoredFields(e,t){const{storeFields:n,extractField:o}=this._options;if(null==n||0===n.length)return;let i=this._storedFields.get(e);null==i&&this._storedFields.set(e,i={});for(const s of n){const e=o(t,s);void 0!==e&&(i[s]=e)}}}et.wildcard=Symbol("*");const tt=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0,nt={[Ye]:(e,t)=>{for(const n of t.keys()){const o=e.get(n);if(null==o)e.set(n,t.get(n));else{const{score:e,terms:i,match:s}=t.get(n);o.score=o.score+e,o.match=Object.assign(o.match,s),ht(o.terms,i)}}return e},[Xe]:(e,t)=>{const n=new Map;for(const o of t.keys()){const i=e.get(o);if(null==i)continue;const{score:s,terms:r,match:a}=t.get(o);ht(i.terms,r),n.set(o,{score:i.score+s,terms:i.terms,match:Object.assign(i.match,a)})}return n},[Ze]:(e,t)=>{for(const n of t.keys())e.delete(n);return e}},ot=(e,t,n,o,i,s)=>{const{k:r,b:a,d:l}=s;return Math.log(1+(n-t+.5)/(t+.5))*(l+e*(r+1)/(e+r*(1-a+a*o/i)))},it=e=>(t,n,o)=>({term:t,fuzzy:"function"==typeof e.fuzzy?e.fuzzy(t,n,o):e.fuzzy||!1,prefix:"function"==typeof e.prefix?e.prefix(t,n,o):!0===e.prefix,termBoost:"function"==typeof e.boostTerm?e.boostTerm(t,n,o):1}),st={idField:"id",extractField:(e,t)=>e[t],tokenize:e=>e.split(bt),processTerm:e=>e.toLowerCase(),fields:void 0,searchOptions:void 0,storeFields:[],logger:(e,t)=>{"function"==typeof(null===console||void 0===console?void 0:console[e])&&console[e](t)},autoVacuum:!0},rt={combineWith:Ye,prefix:!1,fuzzy:!1,maxFuzzy:6,boost:{},weights:{fuzzy:.45,prefix:.375},bm25:{k:1.2,b:.7,d:.5}},at={combineWith:"and",prefix:(e,t,n)=>t===n.length-1},lt={batchSize:1e3,batchWait:10},ct={minDirtFactor:.1,minDirtCount:20},ut=Object.assign(Object.assign({},lt),ct),dt=(e,t)=>{e.includes(t)||e.push(t)},ht=(e,t)=>{for(const n of t)e.includes(n)||e.push(n)},ft=({score:e},{score:t})=>t-e,pt=()=>new Map,mt=e=>{const t=new Map;for(const n of Object.keys(e))t.set(parseInt(n,10),e[n]);return t},vt=e=>De(void 0,0,void 0,(function*(){const t=new Map;let n=0;for(const o of Object.keys(e))t.set(parseInt(o,10),e[o]),++n%1e3==0&&(yield gt(0));return t})),gt=e=>new Promise((t=>setTimeout(t,e))),bt=/[\n\r\p{Z}\p{P}]+/u;class yt{constructor(e=10){t(this,"max"),t(this,"cache"),this.max=e,this.cache=new Map}get(e){let t=this.cache.get(e);return void 0!==t&&(this.cache.delete(e),this.cache.set(e,t)),t}set(e,t){this.cache.has(e)?this.cache.delete(e):this.cache.size===this.max&&this.cache.delete(this.first()),this.cache.set(e,t)}first(){return this.cache.keys().next().value}clear(){this.cache.clear()}}const wt=["aria-owns"],xt={class:"shell"},_t=["title"],St={class:"search-actions before"},Et=["title"],It=["aria-activedescendant","aria-controls","placeholder"],kt={class:"search-actions"},Ot=["title"],Nt=["disabled","title"],Tt=["id","role","aria-labelledby"],Ft=["id","aria-selected"],Ct=["href","aria-label","onMouseenter","onFocusin","data-index"],Rt={class:"titles"},Mt=["innerHTML"],At={class:"title main"},Lt=["innerHTML"],Dt={key:0,class:"excerpt-wrapper"},jt={key:0,class:"excerpt",inert:""},zt=["innerHTML"],Pt={key:0,class:"no-results"},Vt={class:"search-keyboard-shortcuts"},$t=["aria-label"],Wt=["aria-label"],Jt=["aria-label"],Gt=["aria-label"],Bt=q(u({__name:"VPLocalSearchBox",emits:["close"],setup(e,{emit:t}){var n,u;const q=t,Q=d(),Y=d(),X=d(H),Z=K(),{activate:ee}=function(e,t={}){let n;const{immediate:u,...d}=t,h=o(!1),f=o(!1),p=e=>n&&n.activate(e),m=e=>n&&n.deactivate(e),v=i((()=>{const t=s(e);return(Array.isArray(t)?t:[t]).map((e=>{const t=s(e);return"string"==typeof t?t:r(t)})).filter(a)}));return l(v,(e=>{e.length&&(n=Re(e,{...d,onActivate(){h.value=!0,t.onActivate&&t.onActivate()},onDeactivate(){h.value=!1,t.onDeactivate&&t.onDeactivate()}}),u&&p())}),{flush:"post"}),c((()=>m())),{hasFocus:h,isPaused:f,activate:p,deactivate:m,pause:()=>{n&&(n.pause(),f.value=!0)},unpause:()=>{n&&(n.unpause(),f.value=!1)}}}(Q,{immediate:!0,allowOutsideClick:!0,clickOutsideDeactivates:!0,escapeDeactivates:!0}),{localeIndex:te,theme:ne}=Z,oe=h((async()=>{var e,t,n,o,i,s,r,a,l;return $(et.loadJSON(null==(n=await(null==(t=(e=X.value)[te.value])?void 0:t.call(e)))?void 0:n.default,{fields:["title","titles","text"],storeFields:["title","titles"],searchOptions:{fuzzy:.2,prefix:!0,boost:{title:4,text:2,titles:1},..."local"===(null==(o=ne.value.search)?void 0:o.provider)&&(null==(s=null==(i=ne.value.search.options)?void 0:i.miniSearch)?void 0:s.searchOptions)},..."local"===(null==(r=ne.value.search)?void 0:r.provider)&&(null==(l=null==(a=ne.value.search.options)?void 0:a.miniSearch)?void 0:l.options)}))})),ie=i((()=>{var e,t;return"local"===(null==(e=ne.value.search)?void 0:e.provider)&&!0===(null==(t=ne.value.search.options)?void 0:t.disableQueryPersistence)})).value?o(""):f("vitepress:local-search-filter",""),se=p("vitepress:local-search-detailed-list","local"===(null==(n=ne.value.search)?void 0:n.provider)&&!0===(null==(u=ne.value.search.options)?void 0:u.detailedView)),re=i((()=>{var e,t,n;return"local"===(null==(e=ne.value.search)?void 0:e.provider)&&(!0===(null==(t=ne.value.search.options)?void 0:t.disableDetailedView)||!1===(null==(n=ne.value.search.options)?void 0:n.detailedView))})),ae=i((()=>{var e,t,n,o,i,s,r;const a=(null==(e=ne.value.search)?void 0:e.options)??ne.value.algolia;return(null==(i=null==(o=null==(n=null==(t=null==a?void 0:a.locales)?void 0:t[te.value])?void 0:n.translations)?void 0:o.button)?void 0:i.buttonText)||(null==(r=null==(s=null==a?void 0:a.translations)?void 0:s.button)?void 0:r.buttonText)||"Search"}));m((()=>{re.value&&(se.value=!1)}));const le=d([]),ce=o(!1);l(ie,(()=>{ce.value=!1}));const ue=h((async()=>{if(Y.value)return $(new Le(Y.value))}),null),de=new yt(16);v((()=>[oe.value,ie.value,se.value]),(async([e,t,n],o,i)=>{var s,r,a,l;(null==o?void 0:o[0])!==e&&de.clear();let c=!1;if(i((()=>{c=!0})),!e)return;le.value=e.search(t).slice(0,16),ce.value=!0;const u=n?await Promise.all(le.value.map((e=>async function(e){const t=G(e.slice(0,e.indexOf("#")));try{if(!t)throw new Error(`Cannot find file for id: ${e}`);return{id:e,mod:await import(t)}}catch(n){return console.error(n),{id:e,mod:{}}}}(e.id)))):[];if(c)return;for(const{id:f,mod:p}of u){const e=f.slice(0,f.indexOf("#"));let t=de.get(e);if(t)continue;t=new Map,de.set(e,t);const n=p.default??p;if((null==n?void 0:n.render)||(null==n?void 0:n.setup)){const e=W(n);e.config.warnHandler=()=>{},e.provide(J,Z),Object.defineProperties(e.config.globalProperties,{$frontmatter:{get:()=>Z.frontmatter.value},$params:{get:()=>Z.page.value.params}});const o=document.createElement("div");e.mount(o);o.querySelectorAll("h1, h2, h3, h4, h5, h6").forEach((e=>{var n;const o=null==(n=e.querySelector("a"))?void 0:n.getAttribute("href"),i=(null==o?void 0:o.startsWith("#"))&&o.slice(1);if(!i)return;let s="";for(;(e=e.nextElementSibling)&&!/^h[1-6]$/i.test(e.tagName);)s+=e.outerHTML;t.set(i,s)})),e.unmount()}if(c)return}const d=new Set;if(le.value=le.value.map((e=>{const[t,n]=e.id.split("#"),o=de.get(t),i=(null==o?void 0:o.get(n))??"";for(const s in e.match)d.add(s);return{...e,text:i}})),await b(),c)return;await new Promise((e=>{var t;null==(t=ue.value)||t.unmark({done:()=>{var t;null==(t=ue.value)||t.markRegExp(function(e){return new RegExp([...e].sort(((e,t)=>t.length-e.length)).map((e=>`(${B(e)})`)).join("|"),"gi")}(d),{done:e})}})}));const h=(null==(s=Q.value)?void 0:s.querySelectorAll(".result .excerpt"))??[];for(const f of h)null==(r=f.querySelector('mark[data-markjs="true"]'))||r.scrollIntoView({block:"center"});null==(l=null==(a=Y.value)?void 0:a.firstElementChild)||l.scrollIntoView({block:"start"})}),{debounce:200,immediate:!0});const he=o(),fe=i((()=>{var e;return(null==(e=ie.value)?void 0:e.length)<=0}));function pe(e=!0){var t,n;null==(t=he.value)||t.focus(),e&&(null==(n=he.value)||n.select())}g((()=>{pe()}));const me=o(-1),ve=o(!0);function ge(){b((()=>{const e=document.querySelector(".result.selected");null==e||e.scrollIntoView({block:"nearest"})}))}l(le,(e=>{me.value=e.length?0:-1,ge()})),y("ArrowUp",(e=>{e.preventDefault(),me.value--,me.value<0&&(me.value=le.value.length-1),ve.value=!0,ge()})),y("ArrowDown",(e=>{e.preventDefault(),me.value++,me.value>=le.value.length&&(me.value=0),ve.value=!0,ge()}));const be=w();y("Enter",(e=>{if(e.isComposing)return;if(e.target instanceof HTMLButtonElement&&"submit"!==e.target.type)return;const t=le.value[me.value];e.target instanceof HTMLInputElement&&!t?e.preventDefault():t&&(be.go(t.id),q("close"))})),y("Escape",(()=>{q("close")}));const ye=U({modal:{displayDetails:"Display detailed list",resetButtonTitle:"Reset search",backButtonTitle:"Close search",noResultsText:"No results for",footer:{selectText:"to select",selectKeyAriaLabel:"enter",navigateText:"to navigate",navigateUpKeyAriaLabel:"up arrow",navigateDownKeyAriaLabel:"down arrow",closeText:"to close",closeKeyAriaLabel:"escape"}}});g((()=>{window.history.pushState(null,"",null)})),x("popstate",(e=>{e.preventDefault(),q("close")}));const we=_(S?document.body:null);function xe(){ie.value="",b().then((()=>pe(!1)))}function _e(e){var t;if(!ve.value)return;const n=null==(t=e.target)?void 0:t.closest(".result"),o=Number.parseInt(null==n?void 0:n.dataset.index);o>=0&&o!==me.value&&(me.value=o),ve.value=!1}return g((()=>{b((()=>{we.value=!0,b().then((()=>ee()))}))})),E((()=>{we.value=!1})),(e,t)=>{var n,o,i,s,r;return I(),k(V,{to:"body"},[O("div",{ref_key:"el",ref:Q,role:"button","aria-owns":(null==(n=le.value)?void 0:n.length)?"localsearch-list":void 0,"aria-expanded":"true","aria-haspopup":"listbox","aria-labelledby":"localsearch-label",class:"VPLocalSearchBox"},[O("div",{class:"backdrop",onClick:t[0]||(t[0]=t=>e.$emit("close"))}),O("div",xt,[O("form",{class:"search-bar",onPointerup:t[4]||(t[4]=e=>{"mouse"===e.pointerType&&pe()}),onSubmit:t[5]||(t[5]=N((()=>{}),["prevent"]))},[O("label",{title:ae.value,id:"localsearch-label",for:"localsearch-input"},t[7]||(t[7]=[O("span",{"aria-hidden":"true",class:"vpi-search search-icon local-search-icon"},null,-1)]),8,_t),O("div",St,[O("button",{class:"back-button",title:T(ye)("modal.backButtonTitle"),onClick:t[1]||(t[1]=t=>e.$emit("close"))},t[8]||(t[8]=[O("span",{class:"vpi-arrow-left local-search-icon"},null,-1)]),8,Et)]),F(O("input",{ref_key:"searchInput",ref:he,"onUpdate:modelValue":t[2]||(t[2]=e=>R(ie)?ie.value=e:null),"aria-activedescendant":me.value>-1?"localsearch-item-"+me.value:void 0,"aria-autocomplete":"both","aria-controls":(null==(o=le.value)?void 0:o.length)?"localsearch-list":void 0,"aria-labelledby":"localsearch-label",autocapitalize:"off",autocomplete:"off",autocorrect:"off",class:"search-input",id:"localsearch-input",enterkeyhint:"go",maxlength:"64",placeholder:ae.value,spellcheck:"false",type:"search"},null,8,It),[[C,T(ie)]]),O("div",kt,[re.value?L("",!0):(I(),M("button",{key:0,class:A(["toggle-layout-button",{"detailed-list":T(se)}]),type:"button",title:T(ye)("modal.displayDetails"),onClick:t[3]||(t[3]=e=>me.value>-1&&(se.value=!T(se)))},t[9]||(t[9]=[O("span",{class:"vpi-layout-list local-search-icon"},null,-1)]),10,Ot)),O("button",{class:"clear-button",type:"reset",disabled:fe.value,title:T(ye)("modal.resetButtonTitle"),onClick:xe},t[10]||(t[10]=[O("span",{class:"vpi-delete local-search-icon"},null,-1)]),8,Nt)])],32),O("ul",{ref_key:"resultsEl",ref:Y,id:(null==(i=le.value)?void 0:i.length)?"localsearch-list":void 0,role:(null==(s=le.value)?void 0:s.length)?"listbox":void 0,"aria-labelledby":(null==(r=le.value)?void 0:r.length)?"localsearch-label":void 0,class:"results",onMousemove:_e},[(I(!0),M(j,null,D(le.value,((n,o)=>(I(),M("li",{key:n.id,id:"localsearch-item-"+o,"aria-selected":me.value===o?"true":"false",role:"option"},[O("a",{href:n.id,class:A(["result",{selected:me.value===o}]),"aria-label":[...n.titles,n.title].join(" > "),onMouseenter:e=>!ve.value&&(me.value=o),onFocusin:e=>me.value=o,onClick:t[6]||(t[6]=t=>e.$emit("close")),"data-index":o},[O("div",null,[O("div",Rt,[t[12]||(t[12]=O("span",{class:"title-icon"},"#",-1)),(I(!0),M(j,null,D(n.titles,((e,n)=>(I(),M("span",{key:n,class:"title"},[O("span",{class:"text",innerHTML:e},null,8,Mt),t[11]||(t[11]=O("span",{class:"vpi-chevron-right local-search-icon"},null,-1))])))),128)),O("span",At,[O("span",{class:"text",innerHTML:n.title},null,8,Lt)])]),T(se)?(I(),M("div",Dt,[n.text?(I(),M("div",jt,[O("div",{class:"vp-doc",innerHTML:n.text},null,8,zt)])):L("",!0),t[13]||(t[13]=O("div",{class:"excerpt-gradient-bottom"},null,-1)),t[14]||(t[14]=O("div",{class:"excerpt-gradient-top"},null,-1))])):L("",!0)])],42,Ct)],8,Ft)))),128)),T(ie)&&!le.value.length&&ce.value?(I(),M("li",Pt,[z(P(T(ye)("modal.noResultsText"))+' "',1),O("strong",null,P(T(ie)),1),t[15]||(t[15]=z('" '))])):L("",!0)],40,Tt),O("div",Vt,[O("span",null,[O("kbd",{"aria-label":T(ye)("modal.footer.navigateUpKeyAriaLabel")},t[16]||(t[16]=[O("span",{class:"vpi-arrow-up navigate-icon"},null,-1)]),8,$t),O("kbd",{"aria-label":T(ye)("modal.footer.navigateDownKeyAriaLabel")},t[17]||(t[17]=[O("span",{class:"vpi-arrow-down navigate-icon"},null,-1)]),8,Wt),z(" "+P(T(ye)("modal.footer.navigateText")),1)]),O("span",null,[O("kbd",{"aria-label":T(ye)("modal.footer.selectKeyAriaLabel")},t[18]||(t[18]=[O("span",{class:"vpi-corner-down-left navigate-icon"},null,-1)]),8,Jt),z(" "+P(T(ye)("modal.footer.selectText")),1)]),O("span",null,[O("kbd",{"aria-label":T(ye)("modal.footer.closeKeyAriaLabel")},"esc",8,Gt),z(" "+P(T(ye)("modal.footer.closeText")),1)])])])],8,wt)])}}}),[["__scopeId","data-v-ba7a2eb0"]]);export{Bt as default};
