export default class events {
  constructor(view, callback) {
    this.view = view;
    this.registerEvent(callback);
  }

  registerEvent(callback) {
    this.clickEvent(callback);
  }

  clickEvent(callback) {
    this.view.on('click', async (event) => {
      console.log(this.view.scale);
      console.log(this.view.zoom);
      console.log(this.view.extent);
      console.log(event.mapPoint.longitude, event.mapPoint.latitude);
      const res = await this.view.hitTest(event);
      if (res.results.length && res.results.length > 0) {
        let graphic = res.results.filter((result) => {
          return result.graphic.layer;
        })[0].graphic;
        callback && callback({ eventType: 'click', data: res });
        console.log(graphic.attributes);
      }
    });
  }
}
