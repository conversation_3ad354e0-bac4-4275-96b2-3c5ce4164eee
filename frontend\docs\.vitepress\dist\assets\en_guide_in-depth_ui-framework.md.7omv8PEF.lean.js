import{ao as e,k as o,aP as i,l as a,ay as r,j as t}from"./chunks/framework.C8U7mBUf.js";const d=JSON.parse('{"title":"UI Framework Switching","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/in-depth/ui-framework.md","filePath":"en/guide/in-depth/ui-framework.md"}');const n=e({name:"en/guide/in-depth/ui-framework.md"},[["render",function(e,d,n,c,s,l){const h=r("NolebaseGitContributors"),m=r("NolebaseGitChangelog");return t(),o("div",null,[d[0]||(d[0]=i('<h1 id="ui-framework-switching" tabindex="-1">UI Framework Switching <a class="header-anchor" href="#ui-framework-switching" aria-label="Permalink to &quot;UI Framework Switching&quot;">​</a></h1><p><code>Vue Admin</code> supports your freedom to choose the UI framework. The default UI framework for the demo site is <code>Ant Design Vue</code>, consistent with the older version. The framework also has built-in versions for <code>Element Plus</code> and <code>Naive UI</code>, allowing you to choose according to your preference.</p><h2 id="adding-a-new-ui-framework" tabindex="-1">Adding a New UI Framework <a class="header-anchor" href="#adding-a-new-ui-framework" aria-label="Permalink to &quot;Adding a New UI Framework&quot;">​</a></h2><p>If you want to use a different UI framework, you only need to follow these steps:</p><ol><li>Create a new folder inside <code>apps</code>, for example, <code>apps/web-xxx</code>.</li><li>Change the <code>name</code> field in <code>apps/web-xxx/package.json</code> to <code>web-xxx</code>.</li><li>Remove dependencies and code from other UI frameworks and replace them with your chosen UI framework&#39;s logic, which requires minimal changes.</li><li>Adjust the language files within <code>locales</code>.</li><li>Adjust the components in <code>app.vue</code>.</li><li>Adapt the theme of the UI framework to match <code>Vben Admin</code>.</li><li>Adjust the application name in <code>.env</code>.</li><li>Add a <code>dev:xxx</code> script in the root directory of the repository.</li><li>Run <code>pnpm install</code> to install dependencies.</li></ol>',5)),a(h),a(m)])}]]);export{d as __pageData,n as default};
