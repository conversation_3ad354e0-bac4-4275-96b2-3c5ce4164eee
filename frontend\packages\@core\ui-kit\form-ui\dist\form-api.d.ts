import type { Recordable } from '@vben-core/typings';
import type { FormState, GenericObject, ResetFormOpts, ValidationOptions } from 'vee-validate';
import type { FormActions, FormSchema, VbenFormProps } from './types';
import { Store } from '@vben-core/shared/store';
import { StateHandler } from '@vben-core/shared/utils';
export declare class FormApi {
    private latestSubmissionValues;
    private prevState;
    form: FormActions;
    isMounted: boolean;
    state: null | VbenFormProps;
    stateHandler: StateHandler;
    store: Store<VbenFormProps>;
    constructor(options?: VbenFormProps);
    private getForm;
    private updateState;
    batchStore(cb: () => void): void;
    getLatestSubmissionValues(): Recordable<any>;
    getState(): VbenFormProps<import("./types").BaseFormComponentType> | null;
    getValues(): Promise<GenericObject>;
    merge(formApi: FormApi): any;
    mount(formActions: FormActions): void;
    /**
     * 根据字段名移除表单项
     * @param fields
     */
    removeSchemaByFields(fields: string[]): Promise<void>;
    /**
     * 重置表单
     */
    resetForm(state?: Partial<FormState<GenericObject>> | undefined, opts?: Partial<ResetFormOpts>): Promise<void>;
    resetValidate(): Promise<void>;
    setFieldValue(field: string, value: any, shouldValidate?: boolean): Promise<void>;
    setLatestSubmissionValues(values: null | Recordable<any>): void;
    setState(stateOrFn: ((prev: VbenFormProps) => Partial<VbenFormProps>) | Partial<VbenFormProps>): void;
    /**
     * 设置表单值
     * @param fields record
     * @param filterFields 过滤不在schema中定义的字段 默认为true
     * @param shouldValidate
     */
    setValues(fields: Record<string, any>, filterFields?: boolean, shouldValidate?: boolean): Promise<void>;
    submitForm(e?: Event): Promise<GenericObject>;
    unmount(): void;
    updateSchema(schema: Partial<FormSchema>[]): void;
    validate(opts?: Partial<ValidationOptions>): Promise<import("vee-validate").FormValidationResult<GenericObject, GenericObject>>;
    validateAndSubmitForm(): Promise<GenericObject | undefined>;
}
