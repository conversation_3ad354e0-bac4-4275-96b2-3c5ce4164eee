@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\sass@1.87.0\node_modules\sass\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\sass@1.87.0\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\sass@1.87.0\node_modules\sass\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\sass@1.87.0\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\sass\sass.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\sass\sass.js" %*
)
