import type { ModalApiOptions, ModalState } from './modal';
import { Store } from '@vben-core/shared/store';
export declare class ModalApi {
    private api;
    private state;
    sharedData: Record<'payload', any>;
    store: Store<ModalState>;
    constructor(options?: ModalApiOptions);
    batchStore(cb: () => void): void;
    /**
     * 关闭弹窗
     */
    close(): void;
    getData<T extends object = Record<string, any>>(): T;
    /**
     * 取消操作
     */
    onCancel(): void;
    /**
     * 弹窗关闭动画播放完毕后的回调
     */
    onClosed(): void;
    /**
     * 确认操作
     */
    onConfirm(): void;
    /**
     * 弹窗打开动画播放完毕后的回调
     */
    onOpened(): void;
    open(): void;
    setData<T>(payload: T): void;
    setState(stateOrFn: ((prev: ModalState) => Partial<ModalState>) | Partial<ModalState>): void;
}
