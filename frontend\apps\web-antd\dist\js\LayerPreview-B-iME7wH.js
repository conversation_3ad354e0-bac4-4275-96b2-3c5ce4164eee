var t=(l,m,a)=>new Promise((u,i)=>{var o=e=>{try{n(a.next(e))}catch(r){i(r)}},c=e=>{try{n(a.throw(e))}catch(r){i(r)}},n=e=>e.done?u(e.value):Promise.resolve(e.value).then(o,c);n((a=a.apply(l,m)).next())});import{c as f,a as v,i as y}from"./index-D4Q7xmlJ.js";import{a as h,u as C}from"./bootstrap-5OPUVRWy.js";/* empty css                                                                     */import{o as w,w as g,a as _,b as S,f as T}from"../jse/index-index-DyHD_jbN.js";import"./Base-xeJpkIWP.js";const b={id:"cesiumContainer"},x={__name:"LayerPreview",props:{layerUrl:String},emits:["viewMapLoaded"],setup(l,{emit:m}){const a=l,u=m,i=C();let o;w(()=>t(null,null,function*(){f||v(i),n()})),g(()=>a.layerUrl,(e,r)=>{e&&c(e)});const c=e=>t(null,null,function*(){try{if(e==null||e.length===0)return;o.scene.globe.show=!1,o.scene.screenSpaceCameraController.enableRotate=!0,o.scene.screenSpaceCameraController.enableTranslate=!0,o.scene.screenSpaceCameraController.enableZoom=!0,o.scene.backgroundColor=Cesium.Color.TRANSPARENT,o.scene.sun.show=!1,o.scene.moon.show=!1,o.scene.skyBox.show=!1;const r=new Cesium.Cesium3DTileset({url:e,modelMatrix:Cesium.Matrix4.IDENTITY,maximumScreenSpaceError:16,maximumNumberOfLoadedTiles:100});o.scene.primitives.removeAll(),o.scene.primitives.add(r),r.readyPromise&&r.readyPromise.then(function(s){if(!s)return;const d=s.boundingSphere;o.zoomTo(s,new Cesium.HeadingPitchRange(0,-.5,d.radius*2)),console.log("3DTiles加载成功")}).catch(s=>{console.log(s)})}catch(r){console.error("加载3DTiles时发生错误",r)}}),n=()=>t(null,null,function*(){y("viewDiv",e=>t(null,null,function*(){o=e,o.scene.morphStart.addEventListener((r,s,d,p)=>{r._morphCancelled=!0,console.log(r,s,d,p)}),a.layerUrl&&a.layerUrl.length>0&&c(a.layerUrl),u("viewMapLoaded",!0)}))});return(e,r)=>(S(),_("div",b,r[0]||(r[0]=[T("div",{id:"viewDiv"},null,-1)])))}},R=h(x,[["__scopeId","data-v-ecc8efa1"]]);export{R as default};
