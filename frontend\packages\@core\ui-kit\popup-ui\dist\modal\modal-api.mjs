import { Store } from "@vben-core/shared/store";
import { bindMethods, isFunction } from "@vben-core/shared/utils";
export class ModalApi {
  api;
  // private prevState!: ModalState;
  state;
  // 共享数据
  sharedData = {
    payload: {}
  };
  store;
  constructor(options = {}) {
    const {
      connectedComponent: _,
      onBeforeClose,
      onCancel,
      onClosed,
      onConfirm,
      onOpenChange,
      onOpened,
      ...storeState
    } = options;
    const defaultState = {
      bordered: true,
      centered: false,
      class: "",
      closeOnClickModal: true,
      closeOnPressEscape: true,
      confirmLoading: false,
      contentClass: "",
      draggable: false,
      footer: true,
      footerClass: "",
      fullscreen: false,
      fullscreenButton: true,
      header: true,
      headerClass: "",
      isOpen: false,
      loading: false,
      modal: true,
      openAutoFocus: false,
      showCancelButton: true,
      showConfirmButton: true,
      title: ""
    };
    this.store = new Store(
      {
        ...defaultState,
        ...storeState
      },
      {
        onUpdate: () => {
          const state = this.store.state;
          if (state?.isOpen === this.state?.isOpen) {
            this.state = state;
          } else {
            this.state = state;
            this.api.onOpenChange?.(!!state?.isOpen);
          }
        }
      }
    );
    this.state = this.store.state;
    this.api = {
      onBeforeClose,
      onCancel,
      onClosed,
      onConfirm,
      onOpenChange,
      onOpened
    };
    bindMethods(this);
  }
  // 如果需要多次更新状态，可以使用 batch 方法
  batchStore(cb) {
    this.store.batch(cb);
  }
  /**
   * 关闭弹窗
   */
  close() {
    const allowClose = this.api.onBeforeClose?.() ?? true;
    if (allowClose) {
      this.store.setState((prev) => ({ ...prev, isOpen: false }));
    }
  }
  getData() {
    return this.sharedData?.payload ?? {};
  }
  /**
   * 取消操作
   */
  onCancel() {
    if (this.api.onCancel) {
      this.api.onCancel?.();
    } else {
      this.close();
    }
  }
  /**
   * 弹窗关闭动画播放完毕后的回调
   */
  onClosed() {
    if (!this.state.isOpen) {
      this.api.onClosed?.();
    }
  }
  /**
   * 确认操作
   */
  onConfirm() {
    this.api.onConfirm?.();
  }
  /**
   * 弹窗打开动画播放完毕后的回调
   */
  onOpened() {
    if (this.state.isOpen) {
      this.api.onOpened?.();
    }
  }
  open() {
    this.store.setState((prev) => ({ ...prev, isOpen: true }));
  }
  setData(payload) {
    this.sharedData.payload = payload;
  }
  setState(stateOrFn) {
    if (isFunction(stateOrFn)) {
      this.store.setState(stateOrFn);
    } else {
      this.store.setState((prev) => ({ ...prev, ...stateOrFn }));
    }
  }
}
