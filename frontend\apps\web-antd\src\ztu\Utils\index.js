import validate from './Validate';
import clickThrottle from './clickThrottle';
import './Array';
import './Date';

let ____one_ = [];

let utils = {
  validate: validate,
  /**
   * 本地存储
   */
  localStorage: {
    /**
     * 存储数据
     */
    setItem: (key, value) => {
      // 将数组、对象类型的数据转换为 JSON 格式字符串进行存储
      if (typeof value === 'object') {
        value = JSON.stringify(value);
      }
      window.localStorage.setItem(key, value);
    },
    /**
     * 获取数据
     */
    getItem: (key) => {
      const data = window.localStorage.getItem(key);
      try {
        if (!data) {
          return '';
        }
        return JSON.parse(data);
      } catch (err) {
        return data;
      }
    },
    /**
     * 删除数据
     */
    removeItem: (key) => {
      window.localStorage.removeItem(key);
    },
  },
  one: (fun, guid) => {
    let start = new Date().getTime();
    console.log(
      ____one_,
      ____one_.findIndex((a) => a == guid),
    );
    if (____one_.findIndex((a) => a == guid) > -1) return;
    ____one_.push(guid);
    fun();
    let f = ____one_.findIndex((a) => a == guid);
    if (f > -1) {
      ____one_.splice(f, 1);
    }

    console.log(new Date().timeDiff(start));
  },
  clickThrottle: clickThrottle,
};

export default utils;
