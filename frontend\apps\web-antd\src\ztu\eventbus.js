export class eventbus {
  constructor() {
    this.__handlers = {};
  }
  emit(type, param) {
    //判断是否存在该事件类型
    if (this.__handlers[type] instanceof Array) {
      var handlers = this.__handlers[type];
      //在同一个事件类型下的可能存在多种处理事件，找出本次需要处理的事件
      for (var i = 0; i < handlers.length; i++) {
        //执行触发
        handlers[i].handler(param);
        if (handlers[i].once) {
          handlers.splice(i, 1);
          i--;
        }
      }
    }
  }
  on(type, handler) {
    if (typeof this.__handlers[type] == 'undefined') {
      this.__handlers[type] = [];
    }
    //将处理事件push到事件处理数组里面
    this.__handlers[type].push({
      handler: handler,
    });
  }
  once(type, handler) {
    if (typeof this.__handlers[type] == 'undefined') {
      this.__handlers[type] = [];
    }
    //将处理事件push到事件处理数组里面
    this.__handlers[type].push({
      handler: handler,
      once: true,
    });
  }
  off(type, handler) {
    //判断是否存在该事件类型
    if (this.__handlers[type] instanceof Array) {
      var handlers = this.__handlers[type];
      //在同一个事件类型下的可能存在多种处理事件
      for (var i = 0; i < handlers.length; i++) {
        //找出本次需要处理的事件下标
        if (handlers[i].handler == handler) {
          break;
        }
      }
      //从事件处理数组里面删除
      handlers.splice(i, 1);
    }
  }
  clear(type) {
    if (type) {
      if (this.__handlers[type] instanceof Array) {
        this.__handlers[type] = [];
      }
    } else {
      this.__handlers = {};
    }
  }
}
let eb = new eventbus();
export default eb;
