var Lo=Object.defineProperty,Eo=Object.defineProperties;var zo=Object.getOwnPropertyDescriptors;var ht=Object.getOwnPropertySymbols;var ua=Object.prototype.hasOwnProperty,ca=Object.prototype.propertyIsEnumerable;var zt=(o,a,t)=>a in o?Lo(o,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[a]=t,J=(o,a)=>{for(var t in a||(a={}))ua.call(a,t)&&zt(o,t,a[t]);if(ht)for(var t of ht(a))ca.call(a,t)&&zt(o,t,a[t]);return o},ke=(o,a)=>Eo(o,zo(a));var me=(o,a)=>{var t={};for(var l in o)ua.call(o,l)&&a.indexOf(l)<0&&(t[l]=o[l]);if(o!=null&&ht)for(var l of ht(o))a.indexOf(l)<0&&ca.call(o,l)&&(t[l]=o[l]);return t};var nt=(o,a,t)=>zt(o,typeof a!="symbol"?a+"":a,t);var K=(o,a,t)=>new Promise((l,n)=>{var s=d=>{try{u(t.next(d))}catch(h){n(h)}},r=d=>{try{u(t.throw(d))}catch(h){n(h)}},u=d=>d.done?l(d.value):Promise.resolve(d.value).then(s,r);u((t=t.apply(o,a)).next())});import{D as Qe,aO as Ma,u as et,aP as We,c as ue,p as $a,aQ as Io,O as tt,aR as Uo,aS as Ao,E as Te,aT as Ho,aU as Oo,aV as Do,aW as Ro,aX as Wo,aY as No,aZ as Fo,a_ as Ko,a$ as jo,b0 as Go,b1 as qo,b2 as Yo,b3 as Xo,b4 as Jo,b5 as Zo,b6 as Qo,b7 as el,F as tl,X as al,I as ol,y as Ze,G as ll,L as sl,N as nl,b8 as rl,b9 as il,ba as dl,bb as ul,bc as cl,f as Pe,bd as St,be as At,a7 as Ce,a as Ee,bf as pl,bg as fl,bh as ml,bi as hl,bj as bl,bk as vl,A as je,bl as gl,bm as yl,Q as Ba,U as wl,V as xl,W as kl,Y as _l,v as Be,bn as Sl,aL as Cl,bo as Va,b as Pa,j as La,bp as Ea,o as pt,a8 as ot,bq as Tl,br as Ml,bs as $l,bt as Bl,bu as Vl,a6 as Pl,bv as Ll,J as El,bw as zl,bx as Il,e as Ct}from"./bootstrap-5OPUVRWy.js";import{Z as Ke,aq as Ul,ar as Al,as as za,at as Hl,d as T,a as x,b as i,h as O,k as e,J as ne,g as B,j as y,q as c,s as f,c as w,I as ie,O as _e,P as Ve,a1 as Ia,a2 as Tt,e as E,ad as wt,r as q,au as Ht,av as Ol,o as qe,n as de,F as Y,D as ce,v as L,t as S,f as C,p as Le,aw as Dl,L as xe,M as k,w as we,a5 as Rl,a6 as Wl,a7 as Ua,a8 as Nl,a9 as Mt,aa as Fl,T as Kl,y as lt,C as pa,i as Re,$ as b,K as $t,ak as jl,ax as Aa,ay as bt,H as $e,az as Gl,aA as ql,R as Ot,aB as Ha,aC as it,ah as xt,aD as Oa,aE as Yl,aF as vt,aG as Xl,aH as Da,ao as Ae,l as ft,aI as Jl,aJ as Zl,aK as Ql,ai as es,z as D,aL as fa,aM as ts,ab as Ra,aN as ma,aO as Wa,aP as as,aQ as os,W as Na,aR as ls,aS as ss,aT as ns,aU as rs,a0 as kt,aV as is,X as ds,aW as Ge,aX as ha,ag as Bt,a3 as Fa,aY as us,aZ as cs,a_ as ps,N as fs,a$ as Ka,b0 as ms,aj as ba,b1 as hs,b2 as bs}from"../jse/index-index-DyHD_jbN.js";import{a as Ie,_ as dt,M as vs}from"./icon.vue_vue_type_script_setup_true_lang-CgLK7NiC.js";import{d as Dt,e as Rt,f as Wt,g as _t,h as gs,i as ja,j as Ga,S as ys,M as ws,k as xs,a as ks,_ as _s,c as Ss}from"./theme-toggle.vue_vue_type_script_setup_true_lang-pVzKzmJu.js";import{X as mt,u as ut}from"./use-modal-uChFuhJy.js";import{V as Cs}from"./loading-DzjUKA94.js";import{R as Nt}from"./rotate-cw-lLmdvVrn.js";function qa(){const{contentIsMaximize:o}=Qe();function a(){const t=o.value;Ke({header:{hidden:!t},sidebar:{hidden:!t}})}return{contentIsMaximize:o,toggleMaximize:a}}function Ft(o,a){for(const t of o){if(t.path===a)return t;const l=t.children&&Ft(t.children,a);if(l)return l}return null}function gt(o,a){var s;const t=Ft(o,a),l=(s=t==null?void 0:t.parents)==null?void 0:s[0],n=l?o.find(r=>r.path===l):void 0;return{findMenu:t,rootMenu:n,rootMenuPath:l}}const Kt=Ma("core-lock",{actions:{lockScreen(o){this.isLockScreen=!0,this.lockScreenPassword=o},unlockScreen(){this.isLockScreen=!1,this.lockScreenPassword=void 0}},persist:{pick:["isLockScreen","lockScreenPassword"]},state:()=>({isLockScreen:!1,lockScreenPassword:void 0})}),st=Ma("core-tabbar",{actions:{_bulkCloseByPaths(o){return K(this,null,function*(){this.tabs=this.tabs.filter(a=>!o.includes(pe(a))),this.updateCacheTabs()})},_close(o){const{fullPath:a}=o;if(Oe(o))return;const t=this.tabs.findIndex(l=>l.fullPath===a);t!==-1&&this.tabs.splice(t,1)},_goToDefaultTab(o){return K(this,null,function*(){if(this.getTabs.length<=0)return;const a=this.getTabs[0];a&&(yield this._goToTab(a,o))})},_goToTab(o,a){return K(this,null,function*(){const{params:t,path:l,query:n}=o,s={params:t||{},path:l,query:n||{}};yield a.replace(s)})},addTab(o){var l,n;const a=Ts(o);if(!Ms(a))return;const t=this.tabs.findIndex(s=>pe(s)===pe(o));if(t===-1){const s=(n=(l=o==null?void 0:o.meta)==null?void 0:l.maxNumOfOpenTab)!=null?n:-1;if(s>0&&this.tabs.filter(r=>r.name===o.name).length>=s){const r=this.tabs.findIndex(u=>u.name===o.name);r!==-1&&this.tabs.splice(r,1)}this.tabs.push(a)}else{const s=Hl(this.tabs)[t],r=ke(J(J({},s),a),{meta:J(J({},s==null?void 0:s.meta),a.meta)});if(s){const u=s.meta;Reflect.has(u,"affixTab")&&(r.meta.affixTab=u.affixTab),Reflect.has(u,"newTabTitle")&&(r.meta.newTabTitle=u.newTabTitle)}this.tabs.splice(t,1,r)}this.updateCacheTabs()},closeAllTabs(o){return K(this,null,function*(){const a=this.tabs.filter(t=>Oe(t));this.tabs=a.length>0?a:[...this.tabs].splice(0,1),yield this._goToDefaultTab(o),this.updateCacheTabs()})},closeLeftTabs(o){return K(this,null,function*(){const a=this.tabs.findIndex(n=>pe(n)===pe(o));if(a<1)return;const t=this.tabs.slice(0,a),l=[];for(const n of t)Oe(n)||l.push(pe(n));yield this._bulkCloseByPaths(l)})},closeOtherTabs(o){return K(this,null,function*(){const a=this.tabs.map(l=>pe(l)),t=[];for(const l of a)if(l!==o.fullPath){const n=this.tabs.find(s=>pe(s)===l);if(!n)continue;Oe(n)||t.push(pe(n))}yield this._bulkCloseByPaths(t)})},closeRightTabs(o){return K(this,null,function*(){const a=this.tabs.findIndex(t=>pe(t)===pe(o));if(a!==-1&&a<this.tabs.length-1){const t=this.tabs.slice(a+1),l=[];for(const n of t)Oe(n)||l.push(pe(n));yield this._bulkCloseByPaths(l)}})},closeTab(o,a){return K(this,null,function*(){const{currentRoute:t}=a;if(pe(t.value)!==pe(o)){this._close(o),this.updateCacheTabs();return}const l=this.getTabs.findIndex(r=>pe(r)===pe(t.value)),n=this.getTabs[l-1],s=this.getTabs[l+1];s?(this._close(o),yield this._goToTab(s,a)):n?(this._close(o),yield this._goToTab(n,a)):console.error("Failed to close the tab; only one tab remains open.")})},closeTabByKey(o,a){return K(this,null,function*(){const t=decodeURIComponent(o),l=this.tabs.findIndex(s=>pe(s)===t);if(l===-1)return;const n=this.tabs[l];n&&(yield this.closeTab(n,a))})},getTabByPath(o){return this.getTabs.find(a=>pe(a)===o)},openTabInNewWindow(o){return K(this,null,function*(){za(o.fullPath||o.path)})},pinTab(o){return K(this,null,function*(){var n;const a=this.tabs.findIndex(s=>pe(s)===pe(o));if(a!==-1){const s=this.tabs[a];o.meta.affixTab=!0,o.meta.title=(n=s==null?void 0:s.meta)==null?void 0:n.title,this.tabs.splice(a,1,o)}const l=this.tabs.filter(s=>Oe(s)).findIndex(s=>pe(s)===pe(o));yield this.sortTabs(a,l)})},refresh(o){return K(this,null,function*(){const{currentRoute:a}=o,{name:t}=a.value;this.excludeCachedTabs.add(t),this.renderRouteView=!1,Ul(),yield new Promise(l=>setTimeout(l,200)),this.excludeCachedTabs.delete(t),this.renderRouteView=!0,Al()})},resetTabTitle(o){return K(this,null,function*(){var t;if((t=o==null?void 0:o.meta)!=null&&t.newTabTitle)return;const a=this.tabs.find(l=>pe(l)===pe(o));a&&(a.meta.newTabTitle=void 0,yield this.updateCacheTabs())})},setAffixTabs(o){for(const a of o)a.meta.affixTab=!0,this.addTab($s(a))},setTabTitle(o,a){return K(this,null,function*(){const t=this.tabs.find(l=>pe(l)===pe(o));t&&(t.meta.newTabTitle=a,yield this.updateCacheTabs())})},setUpdateTime(){this.updateTime=Date.now()},sortTabs(o,a){return K(this,null,function*(){const t=this.tabs[o];t&&(this.tabs.splice(o,1),this.tabs.splice(a,0,t),this.dragEndIndex=this.dragEndIndex+1)})},toggleTabPin(o){return K(this,null,function*(){var t,l;yield((l=(t=o==null?void 0:o.meta)==null?void 0:t.affixTab)!=null?l:!1)?this.unpinTab(o):this.pinTab(o)})},unpinTab(o){return K(this,null,function*(){var n;const a=this.tabs.findIndex(s=>pe(s)===pe(o));if(a!==-1){const s=this.tabs[a];o.meta.affixTab=!1,o.meta.title=(n=s==null?void 0:s.meta)==null?void 0:n.title,this.tabs.splice(a,1,o)}const l=this.tabs.filter(s=>Oe(s)).length;yield this.sortTabs(a,l)})},updateCacheTabs(){return K(this,null,function*(){var a;const o=new Set;for(const t of this.tabs){if(!((a=t.meta)==null?void 0:a.keepAlive))continue;(t.matched||[]).forEach((s,r)=>{r>0&&o.add(s.name)});const n=t.name;o.add(n)}this.cachedTabs=o})}},getters:{affixTabs(){return this.tabs.filter(a=>Oe(a)).sort((a,t)=>{var s,r,u,d;const l=(r=(s=a.meta)==null?void 0:s.affixTabOrder)!=null?r:0,n=(d=(u=t.meta)==null?void 0:u.affixTabOrder)!=null?d:0;return l-n})},getCachedTabs(){return[...this.cachedTabs]},getExcludeCachedTabs(){return[...this.excludeCachedTabs]},getTabs(){const o=this.tabs.filter(a=>!Oe(a));return[...this.affixTabs,...o].filter(Boolean)}},persist:[{pick:["tabs"],storage:sessionStorage}],state:()=>({cachedTabs:new Set,dragEndIndex:0,excludeCachedTabs:new Set,renderRouteView:!0,tabs:[],updateTime:Date.now()})});function Ts(o){if(!o)return o;const n=o,{matched:a,meta:t}=n,l=me(n,["matched","meta"]);return ke(J({},l),{matched:a?a.map(s=>({meta:s.meta,name:s.name,path:s.path})):void 0,meta:ke(J({},t),{newTabTitle:t.newTabTitle})})}function Oe(o){var a,t;return(t=(a=o==null?void 0:o.meta)==null?void 0:a.affixTab)!=null?t:!1}function Ms(o){var t;const a=(t=o==null?void 0:o.matched)!=null?t:[];return!o.meta.hideInTab&&a.every(l=>!l.meta.hideInTab)}function pe(o){return decodeURIComponent(o.fullPath||o.path)}function $s(o){return{meta:o.meta,name:o.name,path:o.path}}function Ya(){const o=et(),a=st();function t(){return K(this,null,function*(){yield a.refresh(o)})}return{refresh:t}}function Xa(){const o=et(),a=We(),t=st();function l(I){return K(this,null,function*(){yield t.closeLeftTabs(I||a)})}function n(){return K(this,null,function*(){yield t.closeAllTabs(o)})}function s(I){return K(this,null,function*(){yield t.closeRightTabs(I||a)})}function r(I){return K(this,null,function*(){yield t.closeOtherTabs(I||a)})}function u(I){return K(this,null,function*(){yield t.closeTab(I||a,o)})}function d(I){return K(this,null,function*(){yield t.pinTab(I||a)})}function h(I){return K(this,null,function*(){yield t.unpinTab(I||a)})}function m(I){return K(this,null,function*(){yield t.toggleTabPin(I||a)})}function p(){return K(this,null,function*(){yield t.refresh(o)})}function g(I){return K(this,null,function*(){yield t.openTabInNewWindow(I||a)})}function v(I){return K(this,null,function*(){yield t.closeTabByKey(I,o)})}function V(I){return K(this,null,function*(){t.setUpdateTime(),yield t.setTabTitle(a,I)})}function _(){return K(this,null,function*(){t.setUpdateTime(),yield t.resetTabTitle(a)})}function U(I=a){var ve;const N=t.getTabs,Z=t.affixTabs,H=N.findIndex(be=>be.path===I.path),R=N.length<=1,{meta:X}=I,Q=(ve=X==null?void 0:X.affixTab)!=null?ve:!1,z=a.path===I.path,F=H===0||H-Z.length<=0||!z,j=!z||H===N.length-1,re=R||!z||N.length-Z.length<=1;return{disabledCloseAll:R,disabledCloseCurrent:!!Q||R,disabledCloseLeft:F,disabledCloseOther:re,disabledCloseRight:j,disabledRefresh:!z}}return{closeAllTabs:n,closeCurrentTab:u,closeLeftTabs:l,closeOtherTabs:r,closeRightTabs:s,closeTabByKey:v,getTabDisableState:U,openTabInNewWindow:g,pinTab:d,refreshTab:p,resetTabTitle:_,setTabTitle:V,toggleTabPin:m,unpinTab:h}}const Bs=ue("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);const Vs=ue("arrow-left-to-line",[["path",{d:"M3 19V5",key:"rwsyhb"}],["path",{d:"m13 6-6 6 6 6",key:"1yhaz7"}],["path",{d:"M7 12h14",key:"uoisry"}]]);const Ps=ue("arrow-right-left",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]]);const Ls=ue("arrow-right-to-line",[["path",{d:"M17 12H3",key:"8awo09"}],["path",{d:"m11 18 6-6-6-6",key:"8c2y43"}],["path",{d:"M21 5v14",key:"nzette"}]]);const Es=ue("arrow-up-to-line",[["path",{d:"M5 3h14",key:"7usisc"}],["path",{d:"m18 13-6-6-6 6",key:"1kf1n9"}],["path",{d:"M12 7v14",key:"1akyts"}]]);const zs=ue("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);const Is=ue("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);const Us=ue("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);const jt=ue("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);const As=ue("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]]);const Hs=ue("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);const Os=ue("corner-down-left",[["polyline",{points:"9 10 4 15 9 20",key:"r3jprv"}],["path",{d:"M20 4v7a4 4 0 0 1-4 4H4",key:"6o5b7l"}]]);const Ds=ue("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);const Rs=ue("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);const Ws=ue("fold-horizontal",[["path",{d:"M2 12h6",key:"1wqiqv"}],["path",{d:"M22 12h-6",key:"1eg9hc"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 8v2",key:"1woqiv"}],["path",{d:"M12 14v2",key:"8jcxud"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m19 9-3 3 3 3",key:"12ol22"}],["path",{d:"m5 15 3-3-3-3",key:"1kdhjc"}]]);const Ja=ue("fullscreen",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}],["rect",{width:"10",height:"8",x:"7",y:"8",rx:"1",key:"vys8me"}]]);const Za=ue("lock-keyhole",[["circle",{cx:"12",cy:"16",r:"1",key:"1au0dj"}],["rect",{x:"3",y:"10",width:"18",height:"12",rx:"2",key:"6s8ecr"}],["path",{d:"M7 10V7a5 5 0 0 1 10 0v3",key:"1pqi11"}]]);const Ns=ue("log-out",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);const Fs=ue("mail-check",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]]);const Ks=ue("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);const Qa=ue("minimize-2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);const js=ue("minimize",[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]]);const Gs=ue("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);const eo=ue("pin-off",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M15 9.34V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H7.89",key:"znwnzq"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M9 9v1.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h11",key:"c9qhm2"}]]);const Vt=ue("pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]]);const qs=ue("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);const Ys=ue("search-x",[["path",{d:"m13.5 8.5-5 5",key:"1cs55j"}],["path",{d:"m8.5 8.5 5 5",key:"a8mexj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);const va=ue("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);const to=ue("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);const Xs=ue("user-round-pen",[["path",{d:"M2 21a8 8 0 0 1 10.821-7.487",key:"1c8h7z"}],["path",{d:"M21.378 16.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1817ys"}],["circle",{cx:"10",cy:"8",r:"5",key:"o932ke"}]]),Js=$a("inline-flex items-center rounded-md border border-border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-accent hover:bg-accent text-primary-foreground shadow",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive-hover",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}}),Zs=T({__name:"Badge",props:{class:{},variant:{}},setup(o){const a=o;return(t,l)=>(i(),x("div",{class:O(e(ne)(e(Js)({variant:t.variant}),a.class))},[B(t.$slots,"default")],2))}}),Qs=T({__name:"Breadcrumb",props:{class:{}},setup(o){const a=o;return(t,l)=>(i(),x("nav",{class:O(a.class),"aria-label":"breadcrumb",role:"navigation"},[B(t.$slots,"default")],2))}}),en=T({__name:"BreadcrumbItem",props:{class:{}},setup(o){const a=o;return(t,l)=>(i(),x("li",{class:O(e(ne)("hover:text-foreground inline-flex items-center gap-1.5",a.class))},[B(t.$slots,"default")],2))}}),tn=T({__name:"BreadcrumbLink",props:{class:{},asChild:{type:Boolean},as:{default:"a"}},setup(o){const a=o;return(t,l)=>(i(),y(e(Io),{as:t.as,"as-child":t.asChild,class:O(e(ne)("hover:text-foreground transition-colors",a.class))},{default:c(()=>[B(t.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),an=T({__name:"BreadcrumbList",props:{class:{}},setup(o){const a=o;return(t,l)=>(i(),x("ol",{class:O(e(ne)("text-muted-foreground flex flex-wrap items-center gap-1.5 break-words text-sm sm:gap-2.5",a.class))},[B(t.$slots,"default")],2))}}),on=T({__name:"BreadcrumbPage",props:{class:{}},setup(o){const a=o;return(t,l)=>(i(),x("span",{class:O(e(ne)("text-foreground font-normal",a.class)),"aria-current":"page","aria-disabled":"true",role:"link"},[B(t.$slots,"default")],2))}}),ln=T({__name:"BreadcrumbSeparator",props:{class:{}},setup(o){const a=o;return(t,l)=>(i(),x("li",{class:O(e(ne)("[&>svg]:size-3.5",a.class)),"aria-hidden":"true",role:"presentation"},[B(t.$slots,"default",{},()=>[f(e(jt))])],2))}}),sn=T({__name:"DropdownMenuLabel",props:{class:{},inset:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const r=a,{class:n}=r;return me(r,["class"])}),l=tt(t);return(n,s)=>(i(),y(e(Uo),ie(e(l),{class:e(ne)("px-2 py-1.5 text-sm font-semibold",n.inset&&"pl-8",a.class)}),{default:c(()=>[B(n.$slots,"default")]),_:3},16,["class"]))}}),Ut=T({__name:"DropdownMenuSeparator",props:{class:{},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const s=a,{class:l}=s;return me(s,["class"])});return(l,n)=>(i(),y(e(Ao),ie(t.value,{class:e(ne)("bg-border -mx-1 my-1 h-px",a.class)}),null,16,["class"]))}}),ga=T({__name:"DropdownMenuShortcut",props:{class:{}},setup(o){const a=o;return(t,l)=>(i(),x("span",{class:O(e(ne)("ml-auto text-xs tracking-widest opacity-60",a.class))},[B(t.$slots,"default")],2))}}),nn=T({__name:"HoverCard",props:{defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(o,{emit:a}){const n=Te(o,a);return(s,r)=>(i(),y(e(Ho),_e(Ve(e(n))),{default:c(()=>[B(s.$slots,"default")]),_:3},16))}}),rn=T({__name:"HoverCardContent",props:{class:{},forceMount:{type:Boolean},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const r=a,{class:n}=r;return me(r,["class"])}),l=tt(t);return(n,s)=>(i(),y(e(Oo),null,{default:c(()=>[f(e(Do),ie(e(l),{class:e(ne)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-[1000] w-64 rounded-md border p-4 shadow-md outline-none",a.class)}),{default:c(()=>[B(n.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),dn=T({__name:"HoverCardTrigger",props:{asChild:{type:Boolean},as:{}},setup(o){const a=o;return(t,l)=>(i(),y(e(Ro),_e(Ve(a)),{default:c(()=>[B(t.$slots,"default")]),_:3},16))}}),un=T({__name:"NumberField",props:{class:{},defaultValue:{},modelValue:{},min:{},max:{},step:{},formatOptions:{},locale:{},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},asChild:{type:Boolean},as:{}},emits:["update:modelValue"],setup(o,{emit:a}){const t=o,l=a,n=w(()=>{const d=t,{class:r}=d;return me(d,["class"])}),s=Te(n,l);return(r,u)=>(i(),y(e(Wo),ie(e(s),{class:e(ne)("grid gap-1.5",t.class)}),{default:c(()=>[B(r.$slots,"default")]),_:3},16,["class"]))}}),cn=T({__name:"NumberFieldContent",props:{class:{}},setup(o){const a=o;return(t,l)=>(i(),x("div",{class:O(e(ne)("relative [&>[data-slot=input]]:has-[[data-slot=decrement]]:pl-5 [&>[data-slot=input]]:has-[[data-slot=increment]]:pr-5",a.class))},[B(t.$slots,"default")],2))}}),pn=T({__name:"NumberFieldDecrement",props:{class:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const r=a,{class:n}=r;return me(r,["class"])}),l=tt(t);return(n,s)=>(i(),y(e(No),ie({"data-slot":"decrement"},e(l),{class:e(ne)("absolute left-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",a.class)}),{default:c(()=>[B(n.$slots,"default",{},()=>[f(e(Gs),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),fn=T({__name:"NumberFieldIncrement",props:{class:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const r=a,{class:n}=r;return me(r,["class"])}),l=tt(t);return(n,s)=>(i(),y(e(Fo),ie({"data-slot":"increment"},e(l),{class:e(ne)("absolute right-0 top-1/2 -translate-y-1/2 p-3 disabled:cursor-not-allowed disabled:opacity-20",a.class)}),{default:c(()=>[B(n.$slots,"default",{},()=>[f(e(qs),{class:"h-4 w-4"})])]),_:3},16,["class"]))}}),mn=T({__name:"NumberFieldInput",setup(o){return(a,t)=>(i(),y(e(Ko),{class:O(e(ne)("border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent py-1 text-center text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50")),"data-slot":"input"},null,8,["class"]))}}),hn=T({__name:"Popover",props:{defaultOpen:{type:Boolean},open:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(o,{emit:a}){const n=Te(o,a);return(s,r)=>(i(),y(e(jo),_e(Ve(e(n))),{default:c(()=>[B(s.$slots,"default")]),_:3},16))}}),bn=T({inheritAttrs:!1,__name:"PopoverContent",props:{class:{},forceMount:{type:Boolean},trapFocus:{type:Boolean},side:{},sideOffset:{default:4},align:{default:"center"},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},updatePositionStrategy:{},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{},disableOutsidePointerEvents:{type:Boolean}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(o,{emit:a}){const t=o,l=a,n=w(()=>{const d=t,{class:r}=d;return me(d,["class"])}),s=Te(n,l);return(r,u)=>(i(),y(e(Go),null,{default:c(()=>[f(e(qo),ie(J(J({},e(s)),r.$attrs),{class:e(ne)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border w-72 rounded-md border p-4 shadow-md outline-none",t.class)}),{default:c(()=>[B(r.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),vn=T({__name:"PopoverTrigger",props:{asChild:{type:Boolean},as:{}},setup(o){const a=o;return(t,l)=>(i(),y(e(Yo),_e(Ve(a)),{default:c(()=>[B(t.$slots,"default")]),_:3},16))}}),ao=T({__name:"ScrollBar",props:{class:{},orientation:{default:"vertical"},forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const s=a,{class:l}=s;return me(s,["class"])});return(l,n)=>(i(),y(e(Xo),ie(t.value,{class:e(ne)("flex touch-none select-none transition-colors",l.orientation==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-px",l.orientation==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-px",a.class)}),{default:c(()=>[f(e(Jo),{class:"bg-border relative flex-1 rounded-full"})]),_:1},16,["class"]))}}),gn=T({__name:"ScrollArea",props:{class:{},onScroll:{type:Function,default:()=>{}},viewportProps:{},type:{},dir:{},scrollHideDelay:{},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const s=a,{class:l}=s;return me(s,["class"])});return(l,n)=>(i(),y(e(Zo),ie(t.value,{class:e(ne)("relative overflow-hidden",a.class)}),{default:c(()=>[f(e(Qo),{"as-child":"",class:"h-full w-full rounded-[inherit]",onScroll:l.onScroll},{default:c(()=>[B(l.$slots,"default")]),_:3},8,["onScroll"]),f(ao),f(e(el))]),_:3},16,["class"]))}}),yn=$a("fixed z-[1000] bg-background shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500 border-border",{defaultVariants:{side:"right"},variants:{side:{bottom:"inset-x-0 bottom-0 border-t border-border data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left ",right:"inset-y-0 right-0 h-full w-3/4 border-l  data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right",top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top"}}}),wn=T({__name:"Sheet",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(o,{emit:a}){const n=Te(o,a);return(s,r)=>(i(),y(e(tl),_e(Ve(e(n))),{default:c(()=>[B(s.$slots,"default")]),_:3},16))}}),xn=T({__name:"SheetClose",props:{asChild:{type:Boolean},as:{}},setup(o){const a=o;return(t,l)=>(i(),y(e(al),_e(Ve(a)),{default:c(()=>[B(t.$slots,"default")]),_:3},16))}}),kn=["data-dismissable-drawer"],_n=T({__name:"SheetOverlay",setup(o){Ia();const a=Tt("DISMISSABLE_DRAWER_ID");return(t,l)=>(i(),x("div",{"data-dismissable-drawer":e(a),class:"bg-overlay fixed inset-0 z-[1000]"},null,8,kn))}}),Sn=T({inheritAttrs:!1,__name:"SheetContent",props:{class:{},modal:{type:Boolean},open:{type:Boolean},side:{},forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(o,{emit:a}){const t=o,l=a,n=w(()=>{const p=t,{class:r,modal:u,open:d,side:h}=p;return me(p,["class","modal","open","side"])}),s=Te(n,l);return(r,u)=>(i(),y(e(ol),null,{default:c(()=>[f(Ze,{name:"fade"},{default:c(()=>[r.open&&r.modal?(i(),y(_n,{key:0})):E("",!0)]),_:1}),f(e(ll),ie({class:e(ne)(e(yn)({side:r.side}),"z-[1000]",t.class)},J(J({},e(s)),r.$attrs)),{default:c(()=>[B(r.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),ya=T({__name:"SheetDescription",props:{class:{},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const s=a,{class:l}=s;return me(s,["class"])});return(l,n)=>(i(),y(e(sl),ie({class:e(ne)("text-muted-foreground text-sm",a.class)},t.value),{default:c(()=>[B(l.$slots,"default")]),_:3},16,["class"]))}}),Cn=T({__name:"SheetFooter",props:{class:{}},setup(o){const a=o;return(t,l)=>(i(),x("div",{class:O(e(ne)("flex flex-col-reverse sm:flex-row sm:justify-end sm:gap-x-2",a.class))},[B(t.$slots,"default")],2))}}),Tn=T({__name:"SheetHeader",props:{class:{}},setup(o){const a=o;return(t,l)=>(i(),x("div",{class:O(e(ne)("flex flex-col text-center sm:text-left",a.class))},[B(t.$slots,"default")],2))}}),wa=T({__name:"SheetTitle",props:{class:{},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const s=a,{class:l}=s;return me(s,["class"])});return(l,n)=>(i(),y(e(nl),ie({class:e(ne)("text-foreground font-medium",a.class)},t.value),{default:c(()=>[B(l.$slots,"default")]),_:3},16,["class"]))}}),Mn=T({__name:"Switch",props:{class:{},defaultChecked:{type:Boolean},checked:{type:Boolean},disabled:{type:Boolean},required:{type:Boolean},name:{},id:{},value:{},asChild:{type:Boolean},as:{}},emits:["update:checked"],setup(o,{emit:a}){const t=o,l=a,n=w(()=>{const d=t,{class:r}=d;return me(d,["class"])}),s=Te(n,l);return(r,u)=>(i(),y(e(il),ie(e(s),{class:e(ne)("focus-visible:ring-ring focus-visible:ring-offset-background data-[state=checked]:bg-primary data-[state=unchecked]:bg-input peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t.class)}),{default:c(()=>[f(e(rl),{class:O(e(ne)("bg-background pointer-events-none block h-4 w-4 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"))},null,8,["class"])]),_:1},16,["class"]))}}),$n=T({__name:"Tabs",props:{defaultValue:{},orientation:{},dir:{},activationMode:{},modelValue:{},asChild:{type:Boolean},as:{}},emits:["update:modelValue"],setup(o,{emit:a}){const n=Te(o,a);return(s,r)=>(i(),y(e(dl),_e(Ve(e(n))),{default:c(()=>[B(s.$slots,"default")]),_:3},16))}}),Bn=T({__name:"TabsContent",props:{class:{},value:{},forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const s=a,{class:l}=s;return me(s,["class"])});return(l,n)=>(i(),y(e(ul),ie({class:e(ne)("ring-offset-background focus-visible:ring-ring mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",a.class)},t.value),{default:c(()=>[B(l.$slots,"default")]),_:3},16,["class"]))}}),Vn=T({__name:"TabsList",props:{class:{},loop:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const s=a,{class:l}=s;return me(s,["class"])});return(l,n)=>(i(),y(e(cl),ie(t.value,{class:e(ne)("bg-muted text-muted-foreground inline-flex h-9 items-center justify-center rounded-lg p-1",a.class)}),{default:c(()=>[B(l.$slots,"default")]),_:3},16,["class"]))}}),Pn=o=>{const a=wt(),t=wt(),l=q(!1),n=()=>{var u;a.value&&(l.value=a.value.scrollTop>=((u=o==null?void 0:o.visibilityHeight)!=null?u:0))},s=()=>{var u;(u=a.value)==null||u.scrollTo({behavior:"smooth",top:0})},r=Ht(n,300,!0);return Ol(t,"scroll",r),qe(()=>{var u;if(t.value=document,a.value=document.documentElement,o.target){if(a.value=(u=document.querySelector(o.target))!=null?u:void 0,!a.value)throw new Error(`target does not exist: ${o.target}`);t.value=a.value}n()}),{handleClick:s,visible:l}},Ln=T({name:"BackTop",__name:"back-top",props:{bottom:{default:20},isGroup:{type:Boolean,default:!1},right:{default:24},target:{default:""},visibilityHeight:{default:200}},setup(o){const a=o,t=w(()=>({bottom:`${a.bottom}px`,right:`${a.right}px`})),{handleClick:l,visible:n}=Pn(a);return(s,r)=>(i(),y(Ze,{name:"fade-down"},{default:c(()=>[e(n)?(i(),y(e(Pe),{key:0,style:de(t.value),class:"dark:bg-accent dark:hover:bg-heavy bg-background hover:bg-heavy data shadow-float fixed bottom-10 z-[1000] size-10 rounded-full duration-500",size:"icon",variant:"icon",onClick:e(l)},{default:c(()=>[f(e(Es),{class:"size-4"})]),_:1},8,["style","onClick"])):E("",!0)]),_:1}))}}),En={key:0},zn={class:"flex-center"},In={class:"flex-center"},Un=T({name:"Breadcrumb",__name:"breadcrumb",props:{breadcrumbs:{},showIcon:{type:Boolean,default:!1},styleType:{}},emits:["select"],setup(o,{emit:a}){const t=a;function l(n){n&&t("select",n)}return(n,s)=>(i(),y(e(Qs),null,{default:c(()=>[f(e(an),null,{default:c(()=>[f(St,{name:"breadcrumb-transition"},{default:c(()=>[(i(!0),x(Y,null,ce(n.breadcrumbs,(r,u)=>(i(),y(e(en),{key:`${r.path}-${r.title}-${u}`},{default:c(()=>{var d,h;return[(h=(d=r.items)==null?void 0:d.length)!=null&&h?(i(),x("div",En,[f(e(Dt),null,{default:c(()=>[f(e(Rt),{class:"flex items-center gap-1"},{default:c(()=>[n.showIcon?(i(),y(e(Ie),{key:0,icon:r.icon,class:"size-5"},null,8,["icon"])):E("",!0),L(" "+S(r.title)+" ",1),f(e(At),{class:"size-4"})]),_:2},1024),f(e(Wt),{align:"start"},{default:c(()=>[(i(!0),x(Y,null,ce(r.items,m=>(i(),y(e(_t),{key:`sub-${m.path}`,onClick:Ce(p=>l(m.path),["stop"])},{default:c(()=>[L(S(m.title),1)]),_:2},1032,["onClick"]))),128))]),_:2},1024)]),_:2},1024)])):u!==n.breadcrumbs.length-1?(i(),y(e(tn),{key:1,href:"javascript:void 0",onClick:Ce(m=>l(r.path),["stop"])},{default:c(()=>[C("div",zn,[n.showIcon?(i(),y(e(Ie),{key:0,class:O([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):E("",!0),L(" "+S(r.title),1)])]),_:2},1032,["onClick"])):(i(),y(e(on),{key:2},{default:c(()=>[C("div",In,[n.showIcon?(i(),y(e(Ie),{key:0,class:O([{"size-5":r.isHome},"mr-1 size-4"]),icon:r.icon},null,8,["class","icon"])):E("",!0),L(" "+S(r.title),1)])]),_:2},1024)),u<n.breadcrumbs.length-1&&!r.isHome?(i(),y(e(ln),{key:3})):E("",!0)]}),_:2},1024))),128))]),_:1})]),_:1})]),_:1}))}}),An={class:"flex"},Hn=["onClick"],On={class:"flex-center z-10 h-full"},Dn=T({name:"Breadcrumb",__name:"breadcrumb-background",props:{breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(o,{emit:a}){const t=a;function l(n,s){!s||n===o.breadcrumbs.length-1||t("select",s)}return(n,s)=>(i(),x("ul",An,[f(St,{name:"breadcrumb-transition"},{default:c(()=>[(i(!0),x(Y,null,ce(n.breadcrumbs,(r,u)=>(i(),x("li",{key:`${r.path}-${r.title}-${u}`},[C("a",{href:"javascript:void 0",onClick:Ce(d=>l(u,r.path),["stop"])},[C("span",On,[n.showIcon?(i(),y(e(Ie),{key:0,icon:r.icon,class:"mr-1 size-4 flex-shrink-0"},null,8,["icon"])):E("",!0),C("span",{class:O({"text-foreground font-normal":u===n.breadcrumbs.length-1})},S(r.title),3)])],8,Hn)]))),128))]),_:1})]))}}),Rn=Ee(Dn,[["__scopeId","data-v-da1498bb"]]),Wn=T({__name:"breadcrumb-view",props:{class:{},breadcrumbs:{},showIcon:{type:Boolean},styleType:{}},emits:["select"],setup(o,{emit:a}){const n=Te(o,a);return(s,r)=>(i(),x(Y,null,[s.styleType==="normal"?(i(),y(Un,_e(ie({key:0},e(n))),null,16)):E("",!0),s.styleType==="background"?(i(),y(Rn,_e(ie({key:1},e(n))),null,16)):E("",!0)],64))}}),Nn=T({__name:"ContextMenu",props:{dir:{},modal:{type:Boolean,default:!1}},emits:["update:open"],setup(o,{emit:a}){const n=Te(o,a);return(s,r)=>(i(),y(e(pl),_e(Ve(e(n))),{default:c(()=>[B(s.$slots,"default")]),_:3},16))}}),Fn=T({__name:"ContextMenuContent",props:{class:{},forceMount:{type:Boolean},loop:{type:Boolean},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},sticky:{},hideWhenDetached:{type:Boolean},prioritizePosition:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(o,{emit:a}){const t=o,l=a,n=w(()=>{const d=t,{class:r}=d;return me(d,["class"])}),s=Te(n,l);return(r,u)=>(i(),y(e(fl),null,{default:c(()=>[f(e(ml),ie(e(s),{class:e(ne)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 border-border z-[1000] min-w-32 overflow-hidden rounded-md border p-1 shadow-md",t.class)}),{default:c(()=>[B(r.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),Kn=T({__name:"ContextMenuItem",props:{class:{},inset:{type:Boolean},disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{}},emits:["select"],setup(o,{emit:a}){const t=o,l=a,n=w(()=>{const d=t,{class:r}=d;return me(d,["class"])}),s=Te(n,l);return(r,u)=>(i(),y(e(hl),ie(e(s),{class:e(ne)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r.inset&&"pl-8",t.class)}),{default:c(()=>[B(r.$slots,"default")]),_:3},16,["class"]))}}),jn=T({__name:"ContextMenuSeparator",props:{class:{},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const s=a,{class:l}=s;return me(s,["class"])});return(l,n)=>(i(),y(e(bl),ie(t.value,{class:e(ne)("bg-border -mx-1 my-1 h-px",a.class)}),null,16,["class"]))}}),Gn=T({__name:"ContextMenuShortcut",props:{class:{}},setup(o){const a=o;return(t,l)=>(i(),x("span",{class:O(e(ne)("text-muted-foreground ml-auto text-xs tracking-widest",a.class))},[B(t.$slots,"default")],2))}}),qn=T({__name:"ContextMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const t=tt(o);return(l,n)=>(i(),y(e(vl),_e(Ve(e(t))),{default:c(()=>[B(l.$slots,"default")]),_:3},16))}}),oo=T({__name:"context-menu",props:{class:{},contentClass:{},contentProps:{},handlerData:{},itemClass:{},menus:{type:Function},dir:{},modal:{type:Boolean}},emits:["update:open"],setup(o,{emit:a}){const t=o,l=a,n=w(()=>{const v=t,{class:d,contentClass:h,contentProps:m,itemClass:p}=v;return me(v,["class","contentClass","contentProps","itemClass"])}),s=Te(n,l),r=w(()=>{var d;return(d=t.menus)==null?void 0:d.call(t,t.handlerData)});function u(d){var h;d.disabled||(h=d==null?void 0:d.handler)==null||h.call(d,t.handlerData)}return(d,h)=>(i(),y(e(Nn),_e(Ve(e(s))),{default:c(()=>[f(e(qn),{"as-child":""},{default:c(()=>[B(d.$slots,"default")]),_:3}),f(e(Fn),ie({class:d.contentClass},d.contentProps,{class:"side-content z-[1000]"}),{default:c(()=>[(i(!0),x(Y,null,ce(r.value,m=>(i(),x(Y,{key:m.key},[f(e(Kn),{class:O([d.itemClass,"cursor-pointer"]),disabled:m.disabled,inset:m.inset||!m.icon,onClick:p=>u(m)},{default:c(()=>[m.icon?(i(),y(Le(m.icon),{key:0,class:"mr-2 size-4 text-lg"})):E("",!0),L(" "+S(m.text)+" ",1),m.shortcut?(i(),y(e(Gn),{key:1},{default:c(()=>[L(S(m.shortcut),1)]),_:2},1024)):E("",!0)]),_:2},1032,["class","disabled","inset","onClick"]),m.separator?(i(),y(e(jn),{key:0})):E("",!0)],64))),128))]),_:1},16,["class"])]),_:3},16))}}),Yn=T({name:"DropdownMenu",__name:"dropdown-menu",props:{menus:{}},setup(o){const a=o;function t(l){var n;l.disabled||(n=l==null?void 0:l.handler)==null||n.call(l,a)}return(l,n)=>(i(),y(e(Dt),null,{default:c(()=>[f(e(Rt),{class:"flex h-full items-center gap-1"},{default:c(()=>[B(l.$slots,"default")]),_:3}),f(e(Wt),{align:"start"},{default:c(()=>[f(e(gs),null,{default:c(()=>[(i(!0),x(Y,null,ce(l.menus,s=>(i(),x(Y,{key:s.value},[f(e(_t),{disabled:s.disabled,class:"data-[state=checked]:bg-accent data-[state=checked]:text-accent-foreground text-foreground/80 mb-1 cursor-pointer",onClick:r=>t(s)},{default:c(()=>[s.icon?(i(),y(Le(s.icon),{key:0,class:"mr-2 size-4"})):E("",!0),L(" "+S(s.label),1)]),_:2},1032,["disabled","onClick"]),s.separator?(i(),y(e(Ut),{key:0,class:"bg-border"})):E("",!0)],64))),128))]),_:1})]),_:1})]),_:3}))}}),Xn=T({name:"FullScreen",__name:"full-screen",setup(o){const{isFullscreen:a,toggle:t}=Dl();return a.value=!!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement),(l,n)=>(i(),y(e(je),{onClick:e(t)},{default:c(()=>[e(a)?(i(),y(e(js),{key:0,class:"text-foreground size-4"})):(i(),y(e(Ks),{key:1,class:"text-foreground size-4"}))]),_:1},8,["onClick"]))}}),Jn={class:"h-full cursor-pointer"},Zn=T({__name:"hover-card",props:{class:{},contentClass:{},contentProps:{},defaultOpen:{type:Boolean},open:{type:Boolean},openDelay:{},closeDelay:{}},emits:["update:open"],setup(o,{emit:a}){const t=o,l=a,n=w(()=>{const m=t,{class:r,contentClass:u,contentProps:d}=m;return me(m,["class","contentClass","contentProps"])}),s=Te(n,l);return(r,u)=>(i(),y(e(nn),_e(Ve(e(s))),{default:c(()=>[f(e(dn),{"as-child":"",class:"h-full"},{default:c(()=>[C("div",Jn,[B(r.$slots,"trigger")])]),_:3}),f(e(rn),ie({class:r.contentClass},r.contentProps,{class:"side-content z-[1000]"}),{default:c(()=>[B(r.$slots,"default")]),_:3},16,["class"])]),_:3},16))}}),Qn=["href"],er={key:1,class:"text-foreground truncate text-nowrap font-semibold"},xa=T({name:"VbenLogo",__name:"logo",props:{collapsed:{type:Boolean,default:!1},href:{default:"javascript:void 0"},logoSize:{default:32},src:{default:""},text:{},theme:{default:"light"}},setup(o){return(a,t)=>(i(),x("div",{class:O([a.theme,"flex h-full items-center text-lg"])},[C("a",{class:O([a.$attrs.class,"flex h-full items-center gap-2 overflow-hidden px-3 text-lg leading-normal transition-all duration-500"]),href:a.href},[a.src?(i(),y(e(dt),{key:0,alt:a.text,src:a.src,class:"relative w-8 rounded-none bg-transparent"},null,8,["alt","src"])):E("",!0),a.collapsed?E("",!0):(i(),x("span",er,S(a.text),1))],10,Qn)],2))}}),tr=T({__name:"popover",props:{class:{},contentClass:{},contentProps:{},defaultOpen:{type:Boolean},open:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(o,{emit:a}){const t=o,l=a,n=w(()=>{const m=t,{class:r,contentClass:u,contentProps:d}=m;return me(m,["class","contentClass","contentProps"])}),s=Te(n,l);return(r,u)=>(i(),y(e(hn),_e(Ve(e(s))),{default:c(()=>[f(e(vn),null,{default:c(()=>[B(r.$slots,"trigger"),f(e(bn),ie({class:[r.contentClass,"side-content z-[1000]"]},r.contentProps),{default:c(()=>[B(r.$slots,"default")]),_:3},16,["class"])]),_:3})]),_:3},16))}}),ar=T({__name:"scrollbar",props:{class:{default:""},horizontal:{type:Boolean,default:!1},scrollBarClass:{},shadow:{type:Boolean,default:!1},shadowBorder:{type:Boolean,default:!1},shadowBottom:{type:Boolean,default:!0},shadowLeft:{type:Boolean,default:!1},shadowRight:{type:Boolean,default:!1},shadowTop:{type:Boolean,default:!0}},emits:["scrollAt"],setup(o,{emit:a}){const t=o,l=a,n=q(!0),s=q(!1),r=q(!1),u=q(!0),d=w(()=>t.shadow&&t.shadowTop),h=w(()=>t.shadow&&t.shadowBottom),m=w(()=>t.shadow&&t.shadowLeft),p=w(()=>t.shadow&&t.shadowRight),g=w(()=>({"both-shadow":!u.value&&!s.value&&m.value&&p.value,"left-shadow":!u.value&&m.value,"right-shadow":!s.value&&p.value}));function v(V){var X,Q,z,F,j,re;const _=V.target,U=(X=_==null?void 0:_.scrollTop)!=null?X:0,I=(Q=_==null?void 0:_.scrollLeft)!=null?Q:0,N=(z=_==null?void 0:_.offsetHeight)!=null?z:0,Z=(F=_==null?void 0:_.offsetWidth)!=null?F:0,H=(j=_==null?void 0:_.scrollHeight)!=null?j:0,R=(re=_==null?void 0:_.scrollWidth)!=null?re:0;n.value=U<=0,u.value=I<=0,r.value=U+N>=H,s.value=I+Z>=R,l("scrollAt",{bottom:r.value,left:u.value,right:s.value,top:n.value})}return(V,_)=>(i(),y(e(gn),{class:O([[e(ne)(t.class),g.value],"vben-scrollbar relative"]),"on-scroll":v},{default:c(()=>[d.value?(i(),x("div",{key:0,class:O([{"opacity-100":!n.value,"border-border border-t":V.shadowBorder&&!n.value},"scrollbar-top-shadow pointer-events-none absolute top-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):E("",!0),B(V.$slots,"default",{},void 0,!0),h.value?(i(),x("div",{key:1,class:O([{"opacity-100":!n.value&&!r.value,"border-border border-b":V.shadowBorder&&!n.value&&!r.value},"scrollbar-bottom-shadow pointer-events-none absolute bottom-0 z-10 h-12 w-full opacity-0 transition-opacity duration-300 ease-in-out will-change-[opacity]"])},null,2)):E("",!0),V.horizontal?(i(),y(e(ao),{key:2,class:O(V.scrollBarClass),orientation:"horizontal"},null,8,["class"])):E("",!0)]),_:3},8,["class"]))}}),ct=Ee(ar,[["__scopeId","data-v-964c3c92"]]),or={class:"bg-background text-foreground inline-flex h-full w-full items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"},lr=T({__name:"tabs-indicator",props:{class:{},asChild:{type:Boolean},as:{}},setup(o){const a=o,t=w(()=>{const r=a,{class:n}=r;return me(r,["class"])}),l=tt(t);return(n,s)=>(i(),y(e(gl),ie(e(l),{class:e(ne)("absolute bottom-0 left-0 z-10 h-full w-1/2 translate-x-[--radix-tabs-indicator-position] rounded-full px-0 py-1 pr-1 transition-[width,transform] duration-300",a.class)}),{default:c(()=>[C("div",or,[B(n.$slots,"default")])]),_:3},16,["class"]))}}),sr=T({__name:"segmented",props:xe({defaultValue:{default:""},tabs:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const a=o,t=k(o,"modelValue"),l=w(()=>{var r;return a.defaultValue||((r=a.tabs[0])==null?void 0:r.value)}),n=w(()=>({"grid-template-columns":`repeat(${a.tabs.length}, minmax(0, 1fr))`})),s=w(()=>({width:`${(100/a.tabs.length).toFixed(0)}%`}));return(r,u)=>(i(),y(e($n),{modelValue:t.value,"onUpdate:modelValue":u[0]||(u[0]=d=>t.value=d),"default-value":l.value},{default:c(()=>[f(e(Vn),{style:de(n.value),class:"bg-accent relative grid w-full"},{default:c(()=>[f(lr,{style:de(s.value)},null,8,["style"]),(i(!0),x(Y,null,ce(r.tabs,d=>(i(),y(e(yl),{key:d.value,value:d.value,class:"z-20 inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium disabled:pointer-events-none disabled:opacity-50"},{default:c(()=>[L(S(d.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["style"]),(i(!0),x(Y,null,ce(r.tabs,d=>(i(),y(e(Bn),{key:d.value,value:d.value},{default:c(()=>[B(r.$slots,d.value)]),_:2},1032,["value"]))),128))]),_:3},8,["modelValue","default-value"]))}}),nr=T({name:"VbenSpinner",__name:"spinner",props:{class:{},minLoadingTime:{default:50},spinning:{type:Boolean}},setup(o){const a=o,t=q(!1),l=q(!0),n=q();we(()=>a.spinning,r=>{if(!r){t.value=!1,clearTimeout(n.value);return}n.value=setTimeout(()=>{t.value=!0,t.value&&(l.value=!0)},a.minLoadingTime)},{immediate:!0});function s(){t.value||(l.value=!1)}return(r,u)=>(i(),x("div",{class:O(e(ne)("flex-center z-100 bg-overlay-content absolute left-0 top-0 size-full backdrop-blur-sm transition-all duration-500",{"invisible opacity-0":!t.value},a.class)),onTransitionend:s},u[0]||(u[0]=[C("div",{class:"loader before:bg-primary/50 after:bg-primary relative size-12 before:absolute before:left-0 before:top-[60px] before:h-[5px] before:w-12 before:rounded-[50%] before:content-[''] after:absolute after:left-0 after:top-0 after:h-full after:w-full after:rounded after:content-['']"},null,-1)]),34))}}),lo=Ee(nr,[["__scopeId","data-v-e8795525"]]),rr={class:"flex-center"},ir=T({__name:"drawer",props:{drawerApi:{default:void 0},cancelText:{},class:{},closable:{type:Boolean},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},footer:{type:Boolean},footerClass:{},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},title:{},titleTooltip:{}},setup(o){var he,Me;const a=o,t=Ba.getComponents(),l=Rl();Mt("DISMISSABLE_DRAWER_ID",l);const n=q(),{$t:s}=Wl(),{isMobile:r}=Ua(),u=(Me=(he=a.drawerApi)==null?void 0:he.useStore)==null?void 0:Me.call(he),{cancelText:d,class:h,closable:m,closeOnClickModal:p,closeOnPressEscape:g,confirmLoading:v,confirmText:V,contentClass:_,description:U,footer:I,footerClass:N,header:Z,headerClass:H,loading:R,modal:X,openAutoFocus:Q,showCancelButton:z,showConfirmButton:F,title:j,titleTooltip:re}=Nl(a,u);we(()=>R.value,P=>{P&&n.value&&n.value.scrollTo({top:0})});function ve(P){p.value||P.preventDefault()}function be(P){g.value||P.preventDefault()}function W(P){const G=P.target,te=G==null?void 0:G.dataset.dismissableDrawer;(!p.value||te!==l)&&P.preventDefault()}function ee(P){Q.value||P==null||P.preventDefault()}function ge(P){P.preventDefault(),P.stopPropagation()}return(P,G)=>{var te;return i(),y(e(wn),{modal:!1,open:(te=e(u))==null?void 0:te.isOpen,"onUpdate:open":G[2]||(G[2]=()=>{var ae;return(ae=P.drawerApi)==null?void 0:ae.close()})},{default:c(()=>{var ae;return[f(e(Sn),{class:O(e(ne)("flex w-[520px] flex-col",e(h),{"!w-full":e(r)})),modal:e(X),open:(ae=e(u))==null?void 0:ae.isOpen,onCloseAutoFocus:ge,onEscapeKeyDown:be,onFocusOutside:ge,onInteractOutside:ve,onOpenAutoFocus:ee,onPointerDownOutside:W},{default:c(()=>[e(Z)?(i(),y(e(Tn),{key:0,class:O(e(ne)("!flex flex-row items-center justify-between border-b px-6 py-5",e(H),{"px-4 py-3":e(m)}))},{default:c(()=>[C("div",null,[e(j)?(i(),y(e(wa),{key:0,class:"text-left"},{default:c(()=>[B(P.$slots,"title",{},()=>[L(S(e(j))+" ",1),e(re)?(i(),y(e(wl),{key:0,"trigger-class":"pb-1"},{default:c(()=>[L(S(e(re)),1)]),_:1})):E("",!0)])]),_:3})):E("",!0),e(U)?(i(),y(e(ya),{key:1,class:"mt-1 text-xs"},{default:c(()=>[B(P.$slots,"description",{},()=>[L(S(e(U)),1)])]),_:3})):E("",!0)]),!e(j)||!e(U)?(i(),y(e(xl),{key:0},{default:c(()=>[e(j)?E("",!0):(i(),y(e(wa),{key:0})),e(U)?E("",!0):(i(),y(e(ya),{key:1}))]),_:1})):E("",!0),C("div",rr,[B(P.$slots,"extra"),e(m)?(i(),y(e(xn),{key:0,"as-child":"",class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:c(()=>[f(e(je),null,{default:c(()=>[f(e(mt),{class:"size-4"})]),_:1})]),_:1})):E("",!0)])]),_:3},8,["class"])):E("",!0),C("div",{ref_key:"wrapperRef",ref:n,class:O(e(ne)("relative flex-1 overflow-y-auto p-3",e(_),{"overflow-hidden":e(R)}))},[e(R)?(i(),y(e(Cs),{key:0,class:"size-full",spinning:""})):E("",!0),B(P.$slots,"default")],2),e(I)?(i(),y(e(Cn),{key:1,class:O(e(ne)("w-full flex-row items-center justify-end border-t p-2 px-3",e(N)))},{default:c(()=>[B(P.$slots,"prepend-footer"),B(P.$slots,"footer",{},()=>[e(z)?(i(),y(Le(e(t).DefaultButton||e(Pe)),{key:0,variant:"ghost",onClick:G[0]||(G[0]=()=>{var se;return(se=P.drawerApi)==null?void 0:se.onCancel()})},{default:c(()=>[B(P.$slots,"cancelText",{},()=>[L(S(e(d)||e(s)("cancel")),1)])]),_:3})):E("",!0),e(F)?(i(),y(Le(e(t).PrimaryButton||e(Pe)),{key:1,loading:e(v),onClick:G[1]||(G[1]=()=>{var se;return(se=P.drawerApi)==null?void 0:se.onConfirm()})},{default:c(()=>[B(P.$slots,"confirmText",{},()=>[L(S(e(V)||e(s)("confirm")),1)])]),_:3},8,["loading"])):E("",!0)]),B(P.$slots,"append-footer")]),_:3},8,["class"])):E("",!0)]),_:3},8,["class","modal","open"])]}),_:3},8,["open"])}}});class dr{constructor(a={}){nt(this,"api");nt(this,"state");nt(this,"sharedData",{payload:{}});nt(this,"store");const h=a,{connectedComponent:t,onBeforeClose:l,onCancel:n,onConfirm:s,onOpenChange:r}=h,u=me(h,["connectedComponent","onBeforeClose","onCancel","onConfirm","onOpenChange"]),d={class:"",closable:!0,closeOnClickModal:!0,closeOnPressEscape:!0,confirmLoading:!1,contentClass:"",footer:!0,header:!0,isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,showCancelButton:!0,showConfirmButton:!0,title:""};this.store=new kl(J(J({},d),u),{onUpdate:()=>{var p,g,v;const m=this.store.state;(m==null?void 0:m.isOpen)===((p=this.state)==null?void 0:p.isOpen)?this.state=m:(this.state=m,(v=(g=this.api).onOpenChange)==null||v.call(g,!!(m!=null&&m.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:l,onCancel:n,onConfirm:s,onOpenChange:r},Fl(this)}batchStore(a){this.store.batch(a)}close(){var t,l,n;((n=(l=(t=this.api).onBeforeClose)==null?void 0:l.call(t))!=null?n:!0)&&this.store.setState(s=>ke(J({},s),{isOpen:!1}))}getData(){var a,t;return(t=(a=this.sharedData)==null?void 0:a.payload)!=null?t:{}}onCancel(){var a,t;this.api.onCancel?(t=(a=this.api).onCancel)==null||t.call(a):this.close()}onConfirm(){var a,t;(t=(a=this.api).onConfirm)==null||t.call(a)}open(){this.store.setState(a=>ke(J({},a),{isOpen:!0}))}setData(a){this.sharedData.payload=a}setState(a){Kl(a)?this.store.setState(a):this.store.setState(t=>J(J({},t),a))}}const ka=Symbol("VBEN_DRAWER_INJECT");function so(o={}){var u;const{connectedComponent:a}=o;if(a){const d=lt({});return[T((m,{attrs:p,slots:g})=>(Mt(ka,{extendApi(v){Object.setPrototypeOf(d,v)},options:o}),ur(d,J(J(J({},m),p),g)),()=>pa(a,J(J({},m),p),g)),{inheritAttrs:!1,name:"VbenParentDrawer"}),d]}const t=Tt(ka,{}),l=J(J({},t.options),o);l.onOpenChange=d=>{var h,m,p;(h=o.onOpenChange)==null||h.call(o,d),(p=(m=t.options)==null?void 0:m.onOpenChange)==null||p.call(m,d)};const n=new dr(l),s=n;s.useStore=d=>_l(n.store,d);const r=T((d,{attrs:h,slots:m})=>()=>pa(ir,ke(J(J({},d),h),{drawerApi:s}),m),{inheritAttrs:!1,name:"VbenDrawer"});return(u=t.extendApi)==null||u.call(t,s),[r,s]}function ur(o,a){return K(this,null,function*(){var n;if(!a||Object.keys(a).length===0)return;yield Re();const t=(n=o==null?void 0:o.store)==null?void 0:n.state;if(!t)return;const l=new Set(Object.keys(t));for(const s of Object.keys(a))l.has(s)&&!["class"].includes(s)&&console.warn(`[Vben Drawer]: When 'connectedComponent' exists, do not set props or slots '${s}', which will increase complexity. If you need to modify the props of Drawer, please use useVbenDrawer or api.`)})}const cr=T({__name:"breadcrumb",props:{hideWhenOnlyOne:{type:Boolean},showHome:{type:Boolean,default:!1},showIcon:{type:Boolean,default:!1},type:{default:"normal"}},setup(o){const a=o,t=We(),l=et(),n=w(()=>{const r=t.matched,u=[];for(const d of r){const{meta:h,path:m}=d,{hideChildrenInMenu:p,hideInBreadcrumb:g,icon:v,name:V,title:_}=h||{};g||p||!m||u.push({icon:v,path:m||t.path,title:_?b(_||V):""})}return a.showHome&&u.unshift({icon:"mdi:home-outline",isHome:!0,path:"/"}),a.hideWhenOnlyOne&&u.length===1?[]:u});function s(r){l.push(r)}return(r,u)=>(i(),y(e(Wn),{breadcrumbs:n.value,"show-icon":r.showIcon,"style-type":r.type,class:"ml-2",onSelect:s},null,8,["breadcrumbs","show-icon","style-type"]))}}),pr=T({name:"CheckUpdates",__name:"check-updates",props:{checkUpdatesInterval:{default:1},checkUpdateUrl:{default:"/"}},setup(o){const a=o;let t=!1;const l=q(""),n=q(""),s=q(),[r,u]=ut({closable:!1,closeOnPressEscape:!1,closeOnClickModal:!1,onConfirm(){n.value=l.value,window.location.reload()}});function d(){return K(this,null,function*(){try{if(location.hostname==="localhost"||location.hostname==="127.0.0.1")return null;const V=yield fetch(a.checkUpdateUrl,{cache:"no-cache",method:"HEAD"});return V.headers.get("etag")||V.headers.get("last-modified")}catch(V){return console.error("Failed to fetch version tag"),null}})}function h(){return K(this,null,function*(){const V=yield d();if(V){if(!n.value){n.value=V;return}n.value!==V&&V&&(clearInterval(s.value),m(V))}})}function m(V){l.value=V,u.open()}function p(){a.checkUpdatesInterval<=0||(s.value=setInterval(h,a.checkUpdatesInterval*60*1e3))}function g(){document.hidden?v():t||(t=!0,h().finally(()=>{t=!1,p()}))}function v(){clearInterval(s.value)}return qe(()=>{document.addEventListener("visibilitychange",g)}),$t(()=>{v(),document.removeEventListener("visibilitychange",g)}),(V,_)=>(i(),y(e(r),{"cancel-text":e(b)("common.cancel"),"confirm-text":e(b)("common.refresh"),"fullscreen-button":!1,title:e(b)("ui.widgets.checkUpdatesTitle"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[L(S(e(b)("ui.widgets.checkUpdatesDescription")),1)]),_:1},8,["cancel-text","confirm-text","title"]))}}),fr={class:"!flex h-full justify-center px-2 sm:max-h-[450px]"},mr={key:0,class:"text-muted-foreground text-center"},hr={class:"mb-10 mt-6 text-xs"},br={class:"text-foreground text-sm font-medium"},vr={key:1,class:"text-muted-foreground text-center"},gr={class:"my-10 text-xs"},yr={class:"w-full"},wr={key:0,class:"text-muted-foreground mb-2 text-xs"},xr=["data-index","data-search-item"],kr={class:"flex-1"},_r=["onClick"],Sr=T({name:"SearchPanel",__name:"search-panel",props:{keyword:{default:""},menus:{default:()=>[]}},emits:["close"],setup(o,{emit:a}){const t=o,l=a,n=et(),s=jl(`__search-history-${location.hostname}__`,[]),r=q(-1),u=wt([]),d=q([]),h=Ht(m,200);function m(R){if(R=R.trim(),!R){d.value=[];return}const X=H(R),Q=[];ql(u.value,z=>{var F;X.test((F=z.name)==null?void 0:F.toLowerCase())&&Q.push(z)}),d.value=Q,Q.length>0&&(r.value=0),r.value=0}function p(){const R=document.querySelector(`[data-search-item="${r.value}"]`);R&&R.scrollIntoView({block:"nearest"})}function g(){return K(this,null,function*(){if(d.value.length===0)return;const R=d.value,X=r.value;if(R.length===0||X<0)return;const Q=R[X];Q&&(s.value.push(Q),_(),yield Re(),Ot(Q.path)?window.open(Q.path,"_blank"):n.push({path:Q.path,replace:!0}))})}function v(){d.value.length!==0&&(r.value--,r.value<0&&(r.value=d.value.length-1),p())}function V(){d.value.length!==0&&(r.value++,r.value>d.value.length-1&&(r.value=0),p())}function _(){d.value=[],l("close")}function U(R){var Q;const X=(Q=R.target)==null?void 0:Q.dataset.index;r.value=Number(X)}function I(R){t.keyword?d.value.splice(R,1):s.value.splice(R,1),r.value=Math.max(r.value-1,0),p()}const N=new Set(["$","(",")","*","+",".","?","[","\\","]","^","{","|","}"]);function Z(R){return N.has(R)?`\\${R}`:R}function H(R){const X=[...R].map(Q=>Z(Q)).join(".*");return new RegExp(`.*${X}.*`)}return we(()=>t.keyword,R=>{R?h(R):d.value=[...s.value]}),qe(()=>{u.value=Aa(t.menus,R=>ke(J({},R),{name:b(R==null?void 0:R.name)})),s.value.length>0&&(d.value=s.value),bt("Enter",g),bt("ArrowUp",v),bt("ArrowDown",V),bt("Escape",_)}),(R,X)=>(i(),y(e(ct),null,{default:c(()=>[C("div",fr,[R.keyword&&d.value.length===0?(i(),x("div",mr,[f(e(Ys),{class:"mx-auto mt-4 size-12"}),C("p",hr,[L(S(e(b)("ui.widgets.search.noResults"))+" ",1),C("span",br,' "'+S(R.keyword)+'" ',1)])])):E("",!0),!R.keyword&&d.value.length===0?(i(),x("div",vr,[C("p",gr,S(e(b)("ui.widgets.search.noRecent")),1)])):E("",!0),$e(C("ul",yr,[e(s).length>0&&!R.keyword?(i(),x("li",wr,S(e(b)("ui.widgets.search.recent")),1)):E("",!0),(i(!0),x(Y,null,ce(e(Gl)(d.value,"path"),(Q,z)=>(i(),x("li",{key:Q.path,class:O([r.value===z?"active bg-primary text-primary-foreground":"","bg-accent flex-center group mb-3 w-full cursor-pointer rounded-lg px-4 py-4"]),"data-index":z,"data-search-item":z,onClick:g,onMouseenter:U},[f(e(Ie),{icon:Q.icon,class:"mr-2 size-5 flex-shrink-0",fallback:""},null,8,["icon"]),C("span",kr,S(Q.name),1),C("div",{class:"flex-center dark:hover:bg-accent hover:text-primary-foreground rounded-full p-1 hover:scale-110",onClick:Ce(F=>I(z),["stop"])},[f(e(mt),{class:"size-4"})],8,_r)],42,xr))),128))],512),[[Be,d.value.length>0]])])]),_:1}))}}),Cr={class:"flex items-center"},Tr=["placeholder"],Mr={class:"flex w-full justify-start text-xs"},$r={class:"mr-2 flex items-center"},Br={class:"mr-2 flex items-center"},Vr={class:"flex items-center"},Pr={class:"text-muted-foreground group-hover:text-foreground hidden text-xs duration-300 md:block"},Lr={key:0,class:"bg-background border-foreground/60 text-muted-foreground group-hover:text-foreground relative hidden rounded-sm rounded-r-xl px-1.5 py-1 text-xs leading-none group-hover:opacity-100 md:block"},Er={key:1},zr=T({name:"GlobalSearch",__name:"global-search",props:{enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]}},setup(o){const a=o,t=q(""),l=q(),[n,s]=ut({onCancel(){s.close()},onOpenChange(v){v||(t.value="")}}),r=s.useStore(v=>v.isOpen);function u(){s.close(),t.value=""}const d=Ha(),h=it()?d["ctrl+k"]:d["cmd+k"];xt(h,()=>{a.enableShortcutKey&&s.open()}),xt(r,()=>{Re(()=>{var v;(v=l.value)==null||v.focus()})});const m=v=>{var V;((V=v.key)==null?void 0:V.toLowerCase())==="k"&&(v.metaKey||v.ctrlKey)&&v.preventDefault()},p=()=>{a.enableShortcutKey?window.addEventListener("keydown",m):window.removeEventListener("keydown",m)},g=()=>{r.value?s.close():s.open()};return we(()=>a.enableShortcutKey,p),qe(()=>{p(),$t(()=>{window.removeEventListener("keydown",m)})}),(v,V)=>(i(),x("div",null,[f(e(n),{"fullscreen-button":!1,class:"w-[600px]","header-class":"py-2 border-b"},{title:c(()=>[C("div",Cr,[f(e(va),{class:"text-muted-foreground mr-2 size-4"}),$e(C("input",{ref_key:"searchInputRef",ref:l,"onUpdate:modelValue":V[0]||(V[0]=_=>t.value=_),placeholder:e(b)("ui.widgets.search.searchNavigate"),class:"ring-none placeholder:text-muted-foreground w-[80%] rounded-md border border-none bg-transparent p-2 pl-0 text-sm font-normal outline-none ring-0 ring-offset-transparent focus-visible:ring-transparent"},null,8,Tr),[[Cl,t.value]])])]),footer:c(()=>[C("div",Mr,[C("div",$r,[f(e(Os),{class:"mr-1 size-3"}),L(" "+S(e(b)("ui.widgets.search.select")),1)]),C("div",Br,[f(e(zs),{class:"mr-1 size-3"}),f(e(Bs),{class:"mr-1 size-3"}),L(" "+S(e(b)("ui.widgets.search.navigate")),1)]),C("div",Vr,[f(e(Sl),{class:"mr-1 size-3"}),L(" "+S(e(b)("ui.widgets.search.close")),1)])])]),default:c(()=>[f(Sr,{keyword:t.value,menus:v.menus,onClose:u},null,8,["keyword","menus"])]),_:1}),C("div",{class:"md:bg-accent group flex h-8 cursor-pointer items-center gap-3 rounded-2xl border-none bg-none px-2 py-0.5 outline-none",onClick:V[1]||(V[1]=_=>g())},[f(e(va),{class:"text-muted-foreground group-hover:text-foreground size-4 group-hover:opacity-100"}),C("span",Pr,S(e(b)("ui.widgets.search.title")),1),v.enableShortcutKey?(i(),x("span",Lr,[L(S(e(it)()?"Ctrl":"⌘")+" ",1),V[2]||(V[2]=C("kbd",null,"K",-1))])):(i(),x("span",Er))])]))}}),Ir={class:"bg-background fixed z-[2000] size-full"},Ur={class:"size-full"},Ar={class:"flex h-full justify-center px-[10%]"},Hr={class:"bg-accent flex-center relative mb-14 mr-20 h-4/5 w-2/5 flex-auto rounded-3xl text-center text-[260px]"},Or={class:"absolute left-4 top-4 text-xl font-semibold"},Dr={class:"bg-accent flex-center mb-14 h-4/5 w-2/5 flex-auto rounded-3xl text-center text-[260px]"},Rr=["onKeydown"],Wr={class:"flex-col-center mb-10 w-[300px]"},Nr={class:"enter-x mb-2 w-full items-center"},Fr={class:"enter-y absolute bottom-5 w-full text-center xl:text-xl 2xl:text-3xl"},Kr={key:0,class:"enter-x mb-2 text-3xl"},jr={class:"text-lg"},Gr={class:"text-3xl"},Yu=T({name:"LockScreen",__name:"lock-screen",props:{avatar:{default:""}},emits:["toLogin"],setup(o){const{locale:a}=Oa(),t=Kt(),l=Yl(),n=vt(l,"A"),s=vt(l,"HH"),r=vt(l,"mm"),u=vt(l,"YYYY-MM-DD dddd",{locales:a.value}),d=q(!1),{lockScreenPassword:h}=Va(t),[m,{form:p,validate:g}]=Pa(lt({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:w(()=>[{component:"VbenInputPassword",componentProps:{placeholder:b("ui.widgets.lockScreen.placeholder")},fieldName:"password",label:b("authentication.password"),rules:La().min(1,{message:b("authentication.passwordTip")})}]),showDefaultActions:!1})),v=w(()=>{var U;return(h==null?void 0:h.value)===((U=p==null?void 0:p.values)==null?void 0:U.password)});function V(){return K(this,null,function*(){const{valid:U}=yield g();U&&(v.value?t.unlockScreen():p.setFieldError("password",b("authentication.passwordErrorTip")))})}function _(){d.value=!d.value}return Ia(),(U,I)=>(i(),x("div",Ir,[f(Ze,{name:"slide-left"},{default:c(()=>[$e(C("div",Ur,[C("div",{class:"flex-col-center text-foreground/80 hover:text-foreground group my-4 cursor-pointer text-xl font-semibold",onClick:_},[f(e(Za),{class:"size-5 transition-all duration-300 group-hover:scale-125"}),C("span",null,S(e(b)("ui.widgets.lockScreen.unlock")),1)]),C("div",Ar,[C("div",Hr,[C("span",Or,S(e(n)),1),L(" "+S(e(s)),1)]),C("div",Dr,S(e(r)),1)])],512),[[Be,!d.value]])]),_:1}),f(Ze,{name:"slide-right"},{default:c(()=>[d.value?(i(),x("div",{key:0,class:"flex-center size-full",onKeydown:Ea(Ce(V,["prevent"]),["enter"])},[C("div",Wr,[f(e(dt),{src:U.avatar,class:"enter-x mb-6 size-20"},null,8,["src"]),C("div",Nr,[f(e(m))]),f(e(Pe),{class:"enter-x w-full",onClick:V},{default:c(()=>[L(S(e(b)("ui.widgets.lockScreen.entry")),1)]),_:1}),f(e(Pe),{class:"enter-x my-2 w-full",variant:"ghost",onClick:I[0]||(I[0]=N=>U.$emit("toLogin"))},{default:c(()=>[L(S(e(b)("ui.widgets.lockScreen.backToLogin")),1)]),_:1}),f(e(Pe),{class:"enter-x mr-2 w-full",variant:"ghost",onClick:_},{default:c(()=>[L(S(e(b)("common.back")),1)]),_:1})])],40,Rr)):E("",!0)]),_:1}),C("div",Fr,[d.value?(i(),x("div",Kr,[L(S(e(s))+":"+S(e(r))+" ",1),C("span",jr,S(e(n)),1)])):E("",!0),C("div",Gr,S(e(u)),1)])]))}}),qr=["onKeydown"],Yr={class:"w-full"},Xr={class:"ml-2 flex w-full flex-col items-center"},Jr={class:"text-foreground my-6 flex items-center font-medium"},Zr=T({name:"LockScreenModal",__name:"lock-screen-modal",props:{avatar:{default:""},text:{default:""}},emits:["submit"],setup(o,{emit:a}){const t=a,[l,{resetForm:n,validate:s,getValues:r}]=Pa(lt({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:w(()=>[{component:"VbenInputPassword",componentProps:{placeholder:b("ui.widgets.lockScreen.placeholder")},fieldName:"lockScreenPassword",formFieldProps:{validateOnBlur:!1},label:b("authentication.password"),rules:La().min(1,{message:b("ui.widgets.lockScreen.placeholder")})}]),showDefaultActions:!1})),[u]=ut({onConfirm(){d()},onOpenChange(h){h&&n()}});function d(){return K(this,null,function*(){const{valid:h}=yield s(),m=yield r();h&&t("submit",m==null?void 0:m.lockScreenPassword)})}return(h,m)=>(i(),y(e(u),{footer:!1,"fullscreen-button":!1,title:e(b)("ui.widgets.lockScreen.title")},{default:c(()=>[C("div",{class:"mb-10 flex w-full flex-col items-center px-10",onKeydown:Ea(Ce(d,["prevent"]),["enter"])},[C("div",Yr,[C("div",Xr,[f(e(dt),{src:h.avatar,class:"size-20","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["src"]),C("div",Jr,S(h.text),1)]),f(e(l)),f(e(Pe),{class:"mt-1 w-full",onClick:d},{default:c(()=>[L(S(e(b)("ui.widgets.lockScreen.screenButton")),1)]),_:1})])],40,qr)]),_:1},8,["title"]))}}),Qr={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},ei={class:"relative"},ti={class:"flex items-center justify-between p-4 py-3"},ai={class:"text-foreground"},oi={class:"!flex max-h-[360px] w-full flex-col"},li=["onClick"],si={key:0,class:"bg-primary absolute right-2 top-2 h-2 w-2 rounded"},ni={class:"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full"},ri=["src"],ii={class:"flex flex-col gap-1 leading-none"},di={class:"font-semibold"},ui={class:"text-muted-foreground my-1 line-clamp-2 text-xs"},ci={class:"text-muted-foreground line-clamp-2 text-xs"},pi={key:1,class:"flex-center text-muted-foreground min-h-[150px] w-full"},fi={class:"border-border flex items-center justify-between border-t px-4 py-3"},mi=T({name:"NotificationPopup",__name:"notification",props:{dot:{type:Boolean,default:!1},notifications:{default:()=>[]}},emits:["clear","makeAll","read","viewAll"],setup(o,{emit:a}){const t=a,[l,n]=Xl();function s(){l.value=!1}function r(){t("viewAll"),s()}function u(){t("makeAll")}function d(){t("clear")}function h(m){t("read",m)}return(m,p)=>(i(),y(e(tr),{open:e(l),"onUpdate:open":p[1]||(p[1]=g=>Da(l)?l.value=g:null),"content-class":"relative right-2 w-[360px] p-0"},{trigger:c(()=>[C("div",{class:"flex-center mr-2 h-full",onClick:p[0]||(p[0]=Ce(g=>e(n)(),["stop"]))},[f(e(je),{class:"bell-button text-foreground relative"},{default:c(()=>[m.dot?(i(),x("span",Qr)):E("",!0),f(e(Is),{class:"size-4"})]),_:1})])]),default:c(()=>[C("div",ei,[C("div",ti,[C("div",ai,S(e(b)("ui.widgets.notifications")),1),f(e(je),{disabled:m.notifications.length<=0,tooltip:e(b)("ui.widgets.markAllAsRead"),onClick:u},{default:c(()=>[f(e(Fs),{class:"size-4"})]),_:1},8,["disabled","tooltip"])]),m.notifications.length>0?(i(),y(e(ct),{key:0},{default:c(()=>[C("ul",oi,[(i(!0),x(Y,null,ce(m.notifications,g=>(i(),x("li",{key:g.title,class:"hover:bg-accent border-border relative flex w-full cursor-pointer items-start gap-5 border-t px-3 py-3",onClick:v=>h(g)},[g.isRead?E("",!0):(i(),x("span",si)),C("span",ni,[C("img",{src:g.avatar,class:"aspect-square h-full w-full object-cover",role:"img"},null,8,ri)]),C("div",ii,[C("p",di,S(g.title),1),C("p",ui,S(g.message),1),C("p",ci,S(g.date),1)])],8,li))),128))])]),_:1})):(i(),x("div",pi,S(e(b)("common.noData")),1)),C("div",fi,[f(e(Pe),{disabled:m.notifications.length<=0,size:"sm",variant:"ghost",onClick:d},{default:c(()=>[L(S(e(b)("ui.widgets.clearNotifications")),1)]),_:1},8,["disabled"]),f(e(Pe),{size:"sm",onClick:r},{default:c(()=>[L(S(e(b)("ui.widgets.viewAll")),1)]),_:1})])])]),_:1},8,["open"]))}}),Xu=Ee(mi,[["__scopeId","data-v-59bbc927"]]),hi={class:"flex flex-col py-4"},bi={class:"mb-3 font-semibold leading-none tracking-tight"},Se=T({name:"PreferenceBlock",__name:"block",props:{title:{default:""}},setup(o){return(a,t)=>(i(),x("div",hi,[C("h3",bi,S(a.title),1),B(a.$slots,"default")]))}}),vi={class:"flex items-center text-sm"},gi={key:0,class:"ml-auto mr-2 text-xs opacity-60"},le=T({name:"PreferenceSwitchItem",__name:"switch-item",props:xe({disabled:{type:Boolean,default:!1}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const a=k(o,"modelValue"),t=Ae();function l(){a.value=!a.value}return(n,s)=>(i(),x("div",{class:O([{"pointer-events-none opacity-50":n.disabled},"hover:bg-accent my-1 flex w-full items-center justify-between rounded-md px-2 py-2.5"]),onClick:l},[C("span",vi,[B(n.$slots,"default"),e(t).tip?(i(),y(e(ot),{key:0,side:"bottom"},{trigger:c(()=>[f(e(pt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[B(n.$slots,"tip")]),_:3})):E("",!0)]),n.$slots.shortcut?(i(),x("span",gi,[B(n.$slots,"shortcut")])):E("",!0),f(e(Mn),{checked:a.value,"onUpdate:checked":s[0]||(s[0]=r=>a.value=r),onClick:s[1]||(s[1]=Ce(()=>{},["stop"]))},null,8,["checked"])],2))}}),yi={key:0,class:"mb-2 mt-3 flex justify-between gap-3 px-2"},wi=["onClick"],xi=T({name:"PreferenceAnimation",__name:"animation",props:{transitionProgress:{type:Boolean,default:!1},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{}},emits:["update:transitionProgress","update:transitionName","update:transitionEnable","update:transitionLoading"],setup(o){const a=k(o,"transitionProgress"),t=k(o,"transitionName"),l=k(o,"transitionEnable"),n=k(o,"transitionLoading"),s=["fade","fade-slide","fade-up","fade-down"];function r(u){t.value=u}return(u,d)=>(i(),x(Y,null,[f(le,{modelValue:a.value,"onUpdate:modelValue":d[0]||(d[0]=h=>a.value=h)},{default:c(()=>[L(S(e(b)("preferences.animation.progress")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:n.value,"onUpdate:modelValue":d[1]||(d[1]=h=>n.value=h)},{default:c(()=>[L(S(e(b)("preferences.animation.loading")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:l.value,"onUpdate:modelValue":d[2]||(d[2]=h=>l.value=h)},{default:c(()=>[L(S(e(b)("preferences.animation.transition")),1)]),_:1},8,["modelValue"]),l.value?(i(),x("div",yi,[(i(),x(Y,null,ce(s,h=>C("div",{key:h,class:O([{"outline-box-active":t.value===h},"outline-box p-2"]),onClick:m=>r(h)},[C("div",{class:O([`${h}-slow`,"bg-accent h-10 w-12 rounded-md"])},null,2)],10,wi)),64))])):E("",!0)],64))}}),ki={class:"flex items-center text-sm"},Pt=T({name:"PreferenceSelectItem",__name:"select-item",props:xe({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const a=k(o,"modelValue"),t=Ae();return(l,n)=>(i(),x("div",{class:O([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":l.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[C("span",ki,[B(l.$slots,"default"),e(t).tip?(i(),y(e(ot),{key:0,side:"bottom"},{trigger:c(()=>[f(e(pt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[B(l.$slots,"tip")]),_:3})):E("",!0)]),f(e(Tl),{modelValue:a.value,"onUpdate:modelValue":n[0]||(n[0]=s=>a.value=s)},{default:c(()=>[f(e(Ml),{class:"h-8 w-[165px]"},{default:c(()=>[f(e($l),{placeholder:l.placeholder},null,8,["placeholder"])]),_:1}),f(e(Bl),null,{default:c(()=>[(i(!0),x(Y,null,ce(l.items,s=>(i(),y(e(Vl),{key:s.value,value:s.value},{default:c(()=>[L(S(s.label),1)]),_:2},1032,["value"]))),128))]),_:1})]),_:1},8,["modelValue"])],2))}}),_i=T({name:"PreferenceGeneralConfig",__name:"general",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{}},emits:["update:appLocale","update:appDynamicTitle","update:appWatermark","update:appEnableCheckUpdates"],setup(o){const a=k(o,"appLocale"),t=k(o,"appDynamicTitle"),l=k(o,"appWatermark"),n=k(o,"appEnableCheckUpdates");return(s,r)=>(i(),x(Y,null,[f(Pt,{modelValue:a.value,"onUpdate:modelValue":r[0]||(r[0]=u=>a.value=u),items:e(Pl)},{default:c(()=>[L(S(e(b)("preferences.language")),1)]),_:1},8,["modelValue","items"]),f(le,{modelValue:t.value,"onUpdate:modelValue":r[1]||(r[1]=u=>t.value=u)},{default:c(()=>[L(S(e(b)("preferences.dynamicTitle")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:l.value,"onUpdate:modelValue":r[2]||(r[2]=u=>l.value=u)},{default:c(()=>[L(S(e(b)("preferences.watermark")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:n.value,"onUpdate:modelValue":r[3]||(r[3]=u=>n.value=u)},{default:c(()=>[L(S(e(b)("preferences.checkUpdates")),1)]),_:1},8,["modelValue"])],64))}}),Si={class:"text-sm"},no=T({name:"PreferenceToggleItem",__name:"toggle-item",props:xe({disabled:{type:Boolean,default:!1},items:{default:()=>[]}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const a=k(o,"modelValue");return(t,l)=>(i(),x("div",{class:O([{"pointer-events-none opacity-50":t.disabled},"hover:bg-accent flex w-full items-center justify-between rounded-md px-2 py-2"]),disabled:""},[C("span",Si,[B(t.$slots,"default")]),f(e(ja),{modelValue:a.value,"onUpdate:modelValue":l[0]||(l[0]=n=>a.value=n),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(i(!0),x(Y,null,ce(t.items,n=>(i(),y(e(Ga),{key:n.value,value:n.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 rounded-sm"},{default:c(()=>[L(S(n.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])],2))}}),Ci=T({name:"PreferenceBreadcrumbConfig",__name:"breadcrumb",props:xe({disabled:{type:Boolean}},{breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{}}),emits:["update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbStyleType","update:breadcrumbShowHome","update:breadcrumbHideOnlyOne"],setup(o){const a=o,t=k(o,"breadcrumbEnable"),l=k(o,"breadcrumbShowIcon"),n=k(o,"breadcrumbStyleType"),s=k(o,"breadcrumbShowHome"),r=k(o,"breadcrumbHideOnlyOne"),u=[{label:b("preferences.normal"),value:"normal"},{label:b("preferences.breadcrumb.background"),value:"background"}],d=w(()=>!t.value||a.disabled);return(h,m)=>(i(),x(Y,null,[f(le,{modelValue:t.value,"onUpdate:modelValue":m[0]||(m[0]=p=>t.value=p),disabled:h.disabled},{default:c(()=>[L(S(e(b)("preferences.breadcrumb.enable")),1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:r.value,"onUpdate:modelValue":m[1]||(m[1]=p=>r.value=p),disabled:d.value},{default:c(()=>[L(S(e(b)("preferences.breadcrumb.hideOnlyOne")),1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:l.value,"onUpdate:modelValue":m[2]||(m[2]=p=>l.value=p),disabled:d.value},{default:c(()=>[L(S(e(b)("preferences.breadcrumb.icon")),1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:s.value,"onUpdate:modelValue":m[3]||(m[3]=p=>s.value=p),disabled:d.value||!l.value},{default:c(()=>[L(S(e(b)("preferences.breadcrumb.home")),1)]),_:1},8,["modelValue","disabled"]),f(no,{modelValue:n.value,"onUpdate:modelValue":m[4]||(m[4]=p=>n.value=p),disabled:d.value,items:u},{default:c(()=>[L(S(e(b)("preferences.breadcrumb.style")),1)]),_:1},8,["modelValue","disabled"])],64))}}),Ti={},Mi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function $i(o,a){return i(),x("svg",Mi,a[0]||(a[0]=[ft('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="53.60438" x="43.484" y="13.66705"></rect><path id="svg_14" d="m3.43932,15.53192c0,-1.08676 1.03344,-2 2.26323,-2l30.33036,0c1.22979,0 2.26323,0.91324 2.26323,2l0,17.24865c0,1.08676 -1.03344,2 -2.26323,2l-30.33036,0c-1.22979,0 -2.26323,-0.91324 -2.26323,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="95.02528" x="3.30419" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const ro=Ee(Ti,[["render",$i]]),Bi={},Vi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Pi(o,a){return i(),x("svg",Vi,a[0]||(a[0]=[ft('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="41.98275" x="45.37589" y="13.53192"></rect><path id="svg_14" d="m16.4123,15.53192c0,-1.08676 0.74096,-2 1.62271,-2l21.74653,0c0.88175,0 1.62271,0.91324 1.62271,2l0,17.24865c0,1.08676 -0.74096,2 -1.62271,2l-21.74653,0c-0.88175,0 -1.62271,-0.91324 -1.62271,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="71.10636" x="16.54743" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect></g>',1)]))}const Li=Ee(Bi,[["render",Pi]]),Ei={},zi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Ii(o,a){return i(),x("svg",zi,a[0]||(a[0]=[C("g",null,[C("path",{id:"svg_1",d:"m0.13514,4.13514c0,-2.17352 1.82648,-4 4,-4l96,0c2.17352,0 4,1.82648 4,4l0,58c0,2.17352 -1.82648,4 -4,4l-96,0c-2.17352,0 -4,-1.82648 -4,-4l0,-58z",fill:"currentColor","fill-opacity":"0.02",opacity:"undefined",stroke:"null"}),C("rect",{id:"svg_13",fill:"currentColor","fill-opacity":"0.08",height:"26.57155",rx:"2",stroke:"null",width:"53.18333",x:"45.79979",y:"3.77232"}),C("path",{id:"svg_14",d:"m4.28142,5.96169c0,-1.37748 1.06465,-2.53502 2.33158,-2.53502l31.2463,0c1.26693,0 2.33158,1.15754 2.33158,2.53502l0,21.86282c0,1.37748 -1.06465,2.53502 -2.33158,2.53502l-31.2463,0c-1.26693,0 -2.33158,-1.15754 -2.33158,-2.53502l0,-21.86282z",fill:"currentColor","fill-opacity":"0.08",opacity:"undefined",stroke:"null"}),C("rect",{id:"svg_15",fill:"currentColor","fill-opacity":"0.08",height:"25.02247",rx:"2",stroke:"null",width:"94.39371",x:"4.56735",y:"34.92584"})],-1)]))}const Ui=Ee(Ei,[["render",Ii]]),Ai={},Hi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Oi(o,a){return i(),x("svg",Hi,a[0]||(a[0]=[ft('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><rect id="svg_8" fill="hsl(var(--primary))" height="9.07027" stroke="null" width="104.07934" x="-0.07419" y="-0.05773"></rect><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="15.58168" y="3.20832"></rect><path id="svg_12" d="m98.19822,2.872c0,-0.54338 0.45662,-1 1,-1l1.925,0c0.54338,0 1,0.45662 1,1l0,2.4c0,0.54338 -0.45662,1 -1,1l-1.925,0c-0.54338,0 -1,-0.45662 -1,-1l0,-2.4z" fill="#ffffff" opacity="undefined" stroke="null"></path><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="44.13071" x="53.37873" y="13.45652"></rect><path id="svg_14" d="m19.4393,15.74245c0,-1.08676 0.79001,-2 1.73013,-2l23.18605,0c0.94011,0 1.73013,0.91324 1.73013,2l0,17.24865c0,1.08676 -0.79001,2 -1.73013,2l-23.18605,0c-0.94011,0 -1.73013,-0.91324 -1.73013,-2l0,-17.24865z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="78.39372" x="19.93575" y="39.34689"></rect><rect id="svg_21" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="28.14924" y="3.07319"></rect><rect id="svg_22" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="41.25735" y="3.20832"></rect><rect id="svg_23" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="7.52486" x="54.23033" y="3.07319"></rect><rect id="svg_4" fill="#ffffff" height="7.13843" rx="2" stroke="null" width="7.78397" x="1.5327" y="0.881"></rect><rect id="svg_5" fill="currentColor" fill-opacity="0.08" height="56.81191" stroke="null" width="15.44642" x="-0.06423" y="9.03113"></rect><path id="svg_2" d="m2.38669,15.38074c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_6" d="m2.38669,28.43336c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_7" d="m2.17616,41.27545c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path><path id="svg_9" d="m2.17616,54.32806c0,-0.20384 0.27195,-0.37513 0.59557,-0.37513l7.98149,0c0.32362,0 0.59557,0.17129 0.59557,0.37513l0,3.23525c0,0.20384 -0.27195,0.37513 -0.59557,0.37513l-7.98149,0c-0.32362,0 -0.59557,-0.17129 -0.59557,-0.37513l0,-3.23525z" fill="currentColor" fill-opacity="0.08" opacity="undefined" stroke="null"></path></g>',1)]))}const Di=Ee(Ai,[["render",Oi]]),Ri={},Wi={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Ni(o,a){return i(),x("svg",Wi,a[0]||(a[0]=[ft('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104" x="0.13514" y="0.13514"></rect><path id="svg_2" d="m-3.37838,3.7543a1.93401,4.02457 0 0 1 1.93401,-4.02457l11.3488,0l0,66.40541l-11.3488,0a1.93401,4.02457 0 0 1 -1.93401,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="15.46086"></rect><rect id="svg_4" fill="#ffffff" height="7.67897" rx="2" stroke="null" width="8.18938" x="0.58676" y="1.42154"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="75.91967" x="25.38277" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="27.91529" y="3.69284"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.78868" y="3.69981"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="42.9287" x="58.75427" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="28.36894" x="26.14342" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="75.09493" x="26.34264" y="39.68822"></rect><rect id="svg_5" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.79832" y="28.39462"></rect><rect id="svg_6" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="41.80156"></rect><rect id="svg_7" fill="#e5e5e5" height="2.789" rx="1.395" stroke="null" width="5.47439" x="1.64059" y="55.36623"></rect><rect id="svg_16" fill="currentColor" fill-opacity="0.08" height="65.72065" stroke="null" width="12.49265" x="9.85477" y="-0.02618"></rect></g>',1)]))}const Fi=Ee(Ri,[["render",Ni]]),Ki={},ji={class:"custom-radio-image",fill:"none",height:"66",width:"104",xmlns:"http://www.w3.org/2000/svg"};function Gi(o,a){return i(),x("svg",ji,a[0]||(a[0]=[ft('<g><rect id="svg_1" fill="currentColor" fill-opacity="0.02" height="66" rx="4" stroke="null" width="104"></rect><path id="svg_2" d="m-3.37838,3.61916a4.4919,4.02457 0 0 1 4.4919,-4.02457l26.35848,0l0,66.40541l-26.35848,0a4.4919,4.02457 0 0 1 -4.4919,-4.02457l0,-58.35627z" fill="hsl(var(--primary))" stroke="null"></path><rect id="svg_3" fill="#e5e5e5" height="2.789" rx="1.395" width="17.66" x="4.906" y="23.884"></rect><rect id="svg_4" fill="#ffffff" height="9.706" rx="2" width="9.811" x="8.83" y="5.881"></rect><path id="svg_5" d="m4.906,35.833c0,-0.75801 0.63699,-1.395 1.395,-1.395l14.87,0c0.75801,0 1.395,0.63699 1.395,1.395l0,-0.001c0,0.75801 -0.63699,1.395 -1.395,1.395l-14.87,0c-0.75801,0 -1.395,-0.63699 -1.395,-1.395l0,0.001z" fill="#ffffff" opacity="undefined"></path><rect id="svg_6" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="44.992"></rect><rect id="svg_7" fill="#ffffff" height="2.789" rx="1.395" width="17.66" x="4.906" y="55.546"></rect><rect id="svg_8" fill="currentColor" fill-opacity="0.08" height="9.07027" rx="2" stroke="null" width="73.53879" x="28.97986" y="1.42876"></rect><rect id="svg_9" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="32.039" y="3.89903"></rect><rect id="svg_10" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="80.75054" y="3.62876"></rect><rect id="svg_11" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="87.58249" y="3.49362"></rect><rect id="svg_12" fill="#b2b2b2" height="4.4" rx="1" stroke="null" width="3.925" x="94.6847" y="3.62876"></rect><rect id="svg_13" fill="currentColor" fill-opacity="0.08" height="21.51892" rx="2" stroke="null" width="45.63141" x="56.05157" y="14.613"></rect><rect id="svg_14" fill="currentColor" fill-opacity="0.08" height="20.97838" rx="2" stroke="null" width="22.82978" x="29.38527" y="14.613"></rect><rect id="svg_15" fill="currentColor" fill-opacity="0.08" height="21.65405" rx="2" stroke="null" width="72.45771" x="28.97986" y="39.48203"></rect></g>',1)]))}const qi=Ee(Ki,[["render",Gi]]),Yi=ro,Xi={class:"flex w-full gap-5"},Ji=["onClick"],Zi={class:"text-muted-foreground mt-2 text-center text-xs"},Qi=T({name:"PreferenceLayoutContent",__name:"content",props:{modelValue:{default:"wide"},modelModifiers:{}},emits:["update:modelValue"],setup(o){const a=k(o,"modelValue"),t={compact:Li,wide:Yi},l=w(()=>[{name:b("preferences.wide"),type:"wide"},{name:b("preferences.compact"),type:"compact"}]);function n(s){return s===a.value?["outline-box-active"]:[]}return(s,r)=>(i(),x("div",Xi,[(i(!0),x(Y,null,ce(l.value,u=>(i(),x("div",{key:u.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:d=>a.value=u.type},[C("div",{class:O([n(u.type),"outline-box flex-center"])},[(i(),y(Le(t[u.type])))],2),C("div",Zi,S(u.name),1)],8,Ji))),128))]))}}),ed={class:"flex items-center text-sm"},rt=T({name:"PreferenceSelectItem",__name:"input-item",props:xe({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const a=k(o,"modelValue"),t=Ae();return(l,n)=>(i(),x("div",{class:O([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":l.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[C("span",ed,[B(l.$slots,"default"),e(t).tip?(i(),y(e(ot),{key:0,side:"bottom"},{trigger:c(()=>[f(e(pt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[B(l.$slots,"tip")]),_:3})):E("",!0)]),f(e(Ll),{modelValue:a.value,"onUpdate:modelValue":n[0]||(n[0]=s=>a.value=s),class:"h-8 w-[165px]"},null,8,["modelValue"])],2))}}),td=T({__name:"copyright",props:xe({disabled:{type:Boolean}},{copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{}}),emits:["update:copyrightEnable","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:copyrightCompanyName","update:copyrightCompanySiteLink"],setup(o){const a=o,t=k(o,"copyrightEnable"),l=k(o,"copyrightDate"),n=k(o,"copyrightIcp"),s=k(o,"copyrightIcpLink"),r=k(o,"copyrightCompanyName"),u=k(o,"copyrightCompanySiteLink"),d=w(()=>a.disabled||!t.value);return(h,m)=>(i(),x(Y,null,[f(le,{modelValue:t.value,"onUpdate:modelValue":m[0]||(m[0]=p=>t.value=p),disabled:h.disabled},{default:c(()=>[L(S(e(b)("preferences.copyright.enable")),1)]),_:1},8,["modelValue","disabled"]),f(rt,{modelValue:r.value,"onUpdate:modelValue":m[1]||(m[1]=p=>r.value=p),disabled:d.value},{default:c(()=>[L(S(e(b)("preferences.copyright.companyName")),1)]),_:1},8,["modelValue","disabled"]),f(rt,{modelValue:u.value,"onUpdate:modelValue":m[2]||(m[2]=p=>u.value=p),disabled:d.value},{default:c(()=>[L(S(e(b)("preferences.copyright.companySiteLink")),1)]),_:1},8,["modelValue","disabled"]),f(rt,{modelValue:l.value,"onUpdate:modelValue":m[3]||(m[3]=p=>l.value=p),disabled:d.value},{default:c(()=>[L(S(e(b)("preferences.copyright.date")),1)]),_:1},8,["modelValue","disabled"]),f(rt,{modelValue:n.value,"onUpdate:modelValue":m[4]||(m[4]=p=>n.value=p),disabled:d.value},{default:c(()=>[L(S(e(b)("preferences.copyright.icp")),1)]),_:1},8,["modelValue","disabled"]),f(rt,{modelValue:s.value,"onUpdate:modelValue":m[5]||(m[5]=p=>s.value=p),disabled:d.value},{default:c(()=>[L(S(e(b)("preferences.copyright.icpLink")),1)]),_:1},8,["modelValue","disabled"])],64))}}),ad=T({__name:"footer",props:{footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{}},emits:["update:footerEnable","update:footerFixed"],setup(o){const a=k(o,"footerEnable"),t=k(o,"footerFixed");return(l,n)=>(i(),x(Y,null,[f(le,{modelValue:a.value,"onUpdate:modelValue":n[0]||(n[0]=s=>a.value=s)},{default:c(()=>[L(S(e(b)("preferences.footer.visible")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:t.value,"onUpdate:modelValue":n[1]||(n[1]=s=>t.value=s),disabled:!a.value},{default:c(()=>[L(S(e(b)("preferences.footer.fixed")),1)]),_:1},8,["modelValue","disabled"])],64))}}),od=T({__name:"header",props:xe({disabled:{type:Boolean}},{headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{}}),emits:["update:headerEnable","update:headerMode"],setup(o){const a=k(o,"headerEnable"),t=k(o,"headerMode"),l=[{label:b("preferences.header.modeStatic"),value:"static"},{label:b("preferences.header.modeFixed"),value:"fixed"},{label:b("preferences.header.modeAuto"),value:"auto"},{label:b("preferences.header.modeAutoScroll"),value:"auto-scroll"}];return(n,s)=>(i(),x(Y,null,[f(le,{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=r=>a.value=r),disabled:n.disabled},{default:c(()=>[L(S(e(b)("preferences.header.visible")),1)]),_:1},8,["modelValue","disabled"]),f(Pt,{modelValue:t.value,"onUpdate:modelValue":s[1]||(s[1]=r=>t.value=r),disabled:!a.value,items:l},{default:c(()=>[L(S(e(b)("preferences.mode")),1)]),_:1},8,["modelValue","disabled"])],64))}}),ld={class:"flex w-full flex-wrap gap-5"},sd=["onClick"],nd={class:"text-muted-foreground flex-center hover:text-foreground mt-2 text-center text-xs"},rd=T({name:"PreferenceLayout",__name:"layout",props:{modelValue:{default:"sidebar-nav"},modelModifiers:{}},emits:["update:modelValue"],setup(o){const a=k(o,"modelValue"),t={"full-content":Ui,"header-nav":ro,"mixed-nav":Di,"sidebar-mixed-nav":Fi,"sidebar-nav":qi},l=w(()=>[{name:b("preferences.vertical"),tip:b("preferences.verticalTip"),type:"sidebar-nav"},{name:b("preferences.twoColumn"),tip:b("preferences.twoColumnTip"),type:"sidebar-mixed-nav"},{name:b("preferences.horizontal"),tip:b("preferences.horizontalTip"),type:"header-nav"},{name:b("preferences.mixedMenu"),tip:b("preferences.mixedMenuTip"),type:"mixed-nav"},{name:b("preferences.fullContent"),tip:b("preferences.fullContentTip"),type:"full-content"}]);function n(s){return s===a.value?["outline-box-active"]:[]}return(s,r)=>(i(),x("div",ld,[(i(!0),x(Y,null,ce(l.value,u=>(i(),x("div",{key:u.name,class:"flex w-[100px] cursor-pointer flex-col",onClick:d=>a.value=u.type},[C("div",{class:O([n(u.type),"outline-box flex-center"])},[(i(),y(Le(t[u.type])))],2),C("div",nd,[L(S(u.name)+" ",1),u.tip?(i(),y(e(ot),{key:0,side:"bottom"},{trigger:c(()=>[f(e(pt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[L(" "+S(u.tip),1)]),_:2},1024)):E("",!0)])],8,sd))),128))]))}}),id=T({name:"PreferenceNavigationConfig",__name:"navigation",props:xe({disabled:{type:Boolean},disabledNavigationSplit:{type:Boolean}},{navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{}}),emits:["update:navigationStyleType","update:navigationSplit","update:navigationAccordion"],setup(o){const a=k(o,"navigationStyleType"),t=k(o,"navigationSplit"),l=k(o,"navigationAccordion"),n=[{label:b("preferences.rounded"),value:"rounded"},{label:b("preferences.plain"),value:"plain"}];return(s,r)=>(i(),x(Y,null,[f(no,{modelValue:a.value,"onUpdate:modelValue":r[0]||(r[0]=u=>a.value=u),disabled:s.disabled,items:n},{default:c(()=>[L(S(e(b)("preferences.navigationMenu.style")),1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:t.value,"onUpdate:modelValue":r[1]||(r[1]=u=>t.value=u),disabled:s.disabledNavigationSplit||s.disabled},{tip:c(()=>[L(S(e(b)("preferences.navigationMenu.splitTip")),1)]),default:c(()=>[L(S(e(b)("preferences.navigationMenu.split"))+" ",1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:l.value,"onUpdate:modelValue":r[2]||(r[2]=u=>l.value=u),disabled:s.disabled},{default:c(()=>[L(S(e(b)("preferences.navigationMenu.accordion")),1)]),_:1},8,["modelValue","disabled"])],64))}}),dd={class:"flex items-center text-sm"},ud=T({name:"PreferenceSelectItem",__name:"number-field-item",props:xe({disabled:{type:Boolean,default:!1},items:{default:()=>[]},placeholder:{default:""}},{modelValue:{},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const a=k(o,"modelValue"),t=Ae();return(l,n)=>(i(),x("div",{class:O([{"hover:bg-accent":!e(t).tip,"pointer-events-none opacity-50":l.disabled},"my-1 flex w-full items-center justify-between rounded-md px-2 py-1"])},[C("span",dd,[B(l.$slots,"default"),e(t).tip?(i(),y(e(ot),{key:0,side:"bottom"},{trigger:c(()=>[f(e(pt),{class:"ml-1 size-3 cursor-help"})]),default:c(()=>[B(l.$slots,"tip")]),_:3})):E("",!0)]),f(e(un),ie({modelValue:a.value,"onUpdate:modelValue":n[0]||(n[0]=s=>a.value=s)},l.$attrs,{class:"w-[165px]"}),{default:c(()=>[f(e(cn),null,{default:c(()=>[f(e(pn)),f(e(mn)),f(e(fn))]),_:1})]),_:1},16,["modelValue"])],2))}}),cd=T({__name:"sidebar",props:xe({disabled:{type:Boolean}},{sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{}}),emits:["update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsedShowTitle","update:sidebarCollapsed"],setup(o){const a=k(o,"sidebarEnable"),t=k(o,"sidebarWidth"),l=k(o,"sidebarCollapsedShowTitle"),n=k(o,"sidebarCollapsed");return(s,r)=>(i(),x(Y,null,[f(le,{modelValue:a.value,"onUpdate:modelValue":r[0]||(r[0]=u=>a.value=u),disabled:s.disabled},{default:c(()=>[L(S(e(b)("preferences.sidebar.visible")),1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:n.value,"onUpdate:modelValue":r[1]||(r[1]=u=>n.value=u),disabled:!a.value||s.disabled},{default:c(()=>[L(S(e(b)("preferences.sidebar.collapsed")),1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:l.value,"onUpdate:modelValue":r[2]||(r[2]=u=>l.value=u),disabled:!a.value||s.disabled||!n.value},{default:c(()=>[L(S(e(b)("preferences.sidebar.collapsedShowTitle")),1)]),_:1},8,["modelValue","disabled"]),f(ud,{modelValue:t.value,"onUpdate:modelValue":r[3]||(r[3]=u=>t.value=u),disabled:!a.value||s.disabled,max:320,min:160,step:10},{default:c(()=>[L(S(e(b)("preferences.sidebar.width")),1)]),_:1},8,["modelValue","disabled"])],64))}}),pd=T({name:"PreferenceTabsConfig",__name:"tabbar",props:xe({disabled:{type:Boolean}},{tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{}}),emits:["update:tabbarEnable","update:tabbarShowIcon","update:tabbarPersist","update:tabbarDraggable","update:tabbarStyleType","update:tabbarShowMore","update:tabbarShowMaximize"],setup(o){const a=k(o,"tabbarEnable"),t=k(o,"tabbarShowIcon"),l=k(o,"tabbarPersist"),n=k(o,"tabbarDraggable"),s=k(o,"tabbarStyleType"),r=k(o,"tabbarShowMore"),u=k(o,"tabbarShowMaximize"),d=w(()=>[{label:b("preferences.tabbar.styleType.chrome"),value:"chrome"},{label:b("preferences.tabbar.styleType.plain"),value:"plain"},{label:b("preferences.tabbar.styleType.card"),value:"card"},{label:b("preferences.tabbar.styleType.brisk"),value:"brisk"}]);return(h,m)=>(i(),x(Y,null,[f(le,{modelValue:a.value,"onUpdate:modelValue":m[0]||(m[0]=p=>a.value=p),disabled:h.disabled},{default:c(()=>[L(S(e(b)("preferences.tabbar.enable")),1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:l.value,"onUpdate:modelValue":m[1]||(m[1]=p=>l.value=p),disabled:!a.value},{default:c(()=>[L(S(e(b)("preferences.tabbar.persist")),1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:n.value,"onUpdate:modelValue":m[2]||(m[2]=p=>n.value=p),disabled:!a.value},{default:c(()=>[L(S(e(b)("preferences.tabbar.draggable")),1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:t.value,"onUpdate:modelValue":m[3]||(m[3]=p=>t.value=p),disabled:!a.value},{default:c(()=>[L(S(e(b)("preferences.tabbar.icon")),1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:r.value,"onUpdate:modelValue":m[4]||(m[4]=p=>r.value=p),disabled:!a.value},{default:c(()=>[L(S(e(b)("preferences.tabbar.showMore")),1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:u.value,"onUpdate:modelValue":m[5]||(m[5]=p=>u.value=p),disabled:!a.value},{default:c(()=>[L(S(e(b)("preferences.tabbar.showMaximize")),1)]),_:1},8,["modelValue","disabled"]),f(Pt,{modelValue:s.value,"onUpdate:modelValue":m[6]||(m[6]=p=>s.value=p),items:d.value},{default:c(()=>[L(S(e(b)("preferences.tabbar.styleType.title")),1)]),_:1},8,["modelValue","items"])],64))}}),fd=T({name:"PreferenceInterfaceControl",__name:"widget",props:{widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:["update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:appPreferencesButtonPosition","update:widgetRefresh"],setup(o){const a=k(o,"widgetGlobalSearch"),t=k(o,"widgetFullscreen"),l=k(o,"widgetLanguageToggle"),n=k(o,"widgetNotification"),s=k(o,"widgetThemeToggle"),r=k(o,"widgetSidebarToggle"),u=k(o,"widgetLockScreen"),d=k(o,"appPreferencesButtonPosition"),h=k(o,"widgetRefresh"),m=w(()=>[{label:b("preferences.position.auto"),value:"auto"},{label:b("preferences.position.header"),value:"header"},{label:b("preferences.position.fixed"),value:"fixed"}]);return(p,g)=>(i(),x(Y,null,[f(le,{modelValue:a.value,"onUpdate:modelValue":g[0]||(g[0]=v=>a.value=v)},{default:c(()=>[L(S(e(b)("preferences.widget.globalSearch")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:s.value,"onUpdate:modelValue":g[1]||(g[1]=v=>s.value=v)},{default:c(()=>[L(S(e(b)("preferences.widget.themeToggle")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:l.value,"onUpdate:modelValue":g[2]||(g[2]=v=>l.value=v)},{default:c(()=>[L(S(e(b)("preferences.widget.languageToggle")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:t.value,"onUpdate:modelValue":g[3]||(g[3]=v=>t.value=v)},{default:c(()=>[L(S(e(b)("preferences.widget.fullscreen")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:n.value,"onUpdate:modelValue":g[4]||(g[4]=v=>n.value=v)},{default:c(()=>[L(S(e(b)("preferences.widget.notification")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:u.value,"onUpdate:modelValue":g[5]||(g[5]=v=>u.value=v)},{default:c(()=>[L(S(e(b)("preferences.widget.lockScreen")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:r.value,"onUpdate:modelValue":g[6]||(g[6]=v=>r.value=v)},{default:c(()=>[L(S(e(b)("preferences.widget.sidebarToggle")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:h.value,"onUpdate:modelValue":g[7]||(g[7]=v=>h.value=v)},{default:c(()=>[L(S(e(b)("preferences.widget.refresh")),1)]),_:1},8,["modelValue"]),f(Pt,{modelValue:d.value,"onUpdate:modelValue":g[8]||(g[8]=v=>d.value=v),items:m.value},{default:c(()=>[L(S(e(b)("preferences.position.title")),1)]),_:1},8,["modelValue","items"])],64))}}),md=T({name:"PreferenceGeneralConfig",__name:"global",props:{shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysLogout:{type:Boolean},shortcutKeysLogoutModifiers:{},shortcutKeysLockScreen:{type:Boolean},shortcutKeysLockScreenModifiers:{}},emits:["update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysLogout","update:shortcutKeysLockScreen"],setup(o){const a=k(o,"shortcutKeysEnable"),t=k(o,"shortcutKeysGlobalSearch"),l=k(o,"shortcutKeysLogout"),n=k(o,"shortcutKeysLockScreen"),s=w(()=>it()?"Alt":"⌥");return(r,u)=>(i(),x(Y,null,[f(le,{modelValue:a.value,"onUpdate:modelValue":u[0]||(u[0]=d=>a.value=d)},{default:c(()=>[L(S(e(b)("preferences.shortcutKeys.title")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:t.value,"onUpdate:modelValue":u[1]||(u[1]=d=>t.value=d),disabled:!a.value},{shortcut:c(()=>[L(S(e(it)()?"Ctrl":"⌘")+" ",1),u[4]||(u[4]=C("kbd",null," K ",-1))]),default:c(()=>[L(S(e(b)("preferences.shortcutKeys.search"))+" ",1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:l.value,"onUpdate:modelValue":u[2]||(u[2]=d=>l.value=d),disabled:!a.value},{shortcut:c(()=>[L(S(s.value)+" Q ",1)]),default:c(()=>[L(S(e(b)("preferences.shortcutKeys.logout"))+" ",1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:n.value,"onUpdate:modelValue":u[3]||(u[3]=d=>n.value=d),disabled:!a.value},{shortcut:c(()=>[L(S(s.value)+" L ",1)]),default:c(()=>[L(S(e(b)("ui.widgets.lockScreen.title"))+" ",1)]),_:1},8,["modelValue","disabled"])],64))}}),hd={class:"flex w-full flex-wrap justify-between"},bd=["onClick"],vd={class:"flex-center relative size-5 rounded-sm"},gd=["value"],yd={class:"text-muted-foreground my-2 text-center text-xs"},wd=T({name:"PreferenceBuiltinTheme",__name:"builtin",props:xe({isDark:{type:Boolean}},{modelValue:{default:"default"},modelModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{}}),emits:["update:modelValue","update:themeColorPrimary"],setup(o){const a=o,t=q(),l=k(o,"modelValue"),n=k(o,"themeColorPrimary"),s=w(()=>new Jl(n.value||"").toHexString()),r=w(()=>[...Zl]);function u(p){switch(p){case"custom":return b("preferences.theme.builtin.custom");case"deep-blue":return b("preferences.theme.builtin.deepBlue");case"deep-green":return b("preferences.theme.builtin.deepGreen");case"default":return b("preferences.theme.builtin.default");case"gray":return b("preferences.theme.builtin.gray");case"green":return b("preferences.theme.builtin.green");case"neutral":return b("preferences.theme.builtin.neutral");case"orange":return b("preferences.theme.builtin.orange");case"pink":return b("preferences.theme.builtin.pink");case"rose":return b("preferences.theme.builtin.rose");case"sky-blue":return b("preferences.theme.builtin.skyBlue");case"slate":return b("preferences.theme.builtin.slate");case"violet":return b("preferences.theme.builtin.violet");case"yellow":return b("preferences.theme.builtin.yellow");case"zinc":return b("preferences.theme.builtin.zinc")}}function d(p){l.value=p.type;const g=a.isDark&&p.darkPrimaryColor||p.primaryColor;n.value=g||p.color}function h(p){const g=p.target;n.value=Ql(g.value)}function m(){var p,g,v;(v=(g=(p=t.value)==null?void 0:p[0])==null?void 0:g.click)==null||v.call(g)}return(p,g)=>(i(),x("div",hd,[(i(!0),x(Y,null,ce(r.value,v=>(i(),x("div",{key:v.type,class:"flex cursor-pointer flex-col",onClick:V=>d(v)},[C("div",{class:O([{"outline-box-active":v.type===l.value},"outline-box flex-center group cursor-pointer"])},[v.type!=="custom"?(i(),x("div",{key:0,style:de({backgroundColor:v.color}),class:"mx-10 my-2 size-5 rounded-md"},null,4)):(i(),x("div",{key:1,class:"size-full px-10 py-2",onClick:Ce(m,["stop"])},[C("div",vd,[f(e(Xs),{class:"absolute z-10 size-5 opacity-60 group-hover:opacity-100"}),C("input",{ref_for:!0,ref_key:"colorInput",ref:t,value:s.value,class:"absolute inset-0 opacity-0",type:"color",onInput:h},null,40,gd)])]))],2),C("div",yd,S(u(v.type)),1)],8,bd))),128))]))}}),xd=T({name:"PreferenceColorMode",__name:"color-mode",props:{appColorWeakMode:{type:Boolean,default:!1},appColorWeakModeModifiers:{},appColorGrayMode:{type:Boolean,default:!1},appColorGrayModeModifiers:{}},emits:["update:appColorWeakMode","update:appColorGrayMode"],setup(o){const a=k(o,"appColorWeakMode"),t=k(o,"appColorGrayMode");return(l,n)=>(i(),x(Y,null,[f(le,{modelValue:a.value,"onUpdate:modelValue":n[0]||(n[0]=s=>a.value=s)},{default:c(()=>[L(S(e(b)("preferences.theme.weakMode")),1)]),_:1},8,["modelValue"]),f(le,{modelValue:t.value,"onUpdate:modelValue":n[1]||(n[1]=s=>t.value=s)},{default:c(()=>[L(S(e(b)("preferences.theme.grayMode")),1)]),_:1},8,["modelValue"])],64))}}),kd=T({name:"PreferenceColorMode",__name:"radius",props:{themeRadius:{default:"0.5"},themeRadiusModifiers:{}},emits:["update:themeRadius"],setup(o){const a=k(o,"themeRadius"),t=[{label:"0",value:"0"},{label:"0.25",value:"0.25"},{label:"0.5",value:"0.5"},{label:"0.75",value:"0.75"},{label:"1",value:"1"}];return(l,n)=>(i(),y(e(ja),{modelValue:a.value,"onUpdate:modelValue":n[0]||(n[0]=s=>a.value=s),class:"gap-2",size:"sm",type:"single",variant:"outline"},{default:c(()=>[(i(),x(Y,null,ce(t,s=>f(e(Ga),{key:s.value,value:s.value,class:"data-[state=on]:bg-primary data-[state=on]:text-primary-foreground h-7 w-16 rounded-sm"},{default:c(()=>[L(S(s.label),1)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"]))}}),_d={class:"flex w-full flex-wrap justify-between"},Sd=["onClick"],Cd={class:"text-muted-foreground mt-2 text-center text-xs"},Td=T({name:"PreferenceTheme",__name:"theme",props:{modelValue:{default:"auto"},modelModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{}},emits:["update:modelValue","update:themeSemiDarkSidebar","update:themeSemiDarkHeader"],setup(o){const a=k(o,"modelValue"),t=k(o,"themeSemiDarkSidebar"),l=k(o,"themeSemiDarkHeader"),n=[{icon:ys,name:"light"},{icon:ws,name:"dark"},{icon:xs,name:"auto"}];function s(u){return u===a.value?["outline-box-active"]:[]}function r(u){switch(u){case"auto":return b("preferences.followSystem");case"dark":return b("preferences.theme.dark");case"light":return b("preferences.theme.light")}}return(u,d)=>(i(),x("div",_d,[(i(),x(Y,null,ce(n,h=>C("div",{key:h.name,class:"flex cursor-pointer flex-col",onClick:m=>a.value=h.name},[C("div",{class:O([s(h.name),"outline-box flex-center py-4"])},[(i(),y(Le(h.icon),{class:"mx-9 size-5"}))],2),C("div",Cd,S(r(h.name)),1)],8,Sd)),64)),f(le,{modelValue:t.value,"onUpdate:modelValue":d[0]||(d[0]=h=>t.value=h),disabled:a.value==="dark",class:"mt-6"},{default:c(()=>[L(S(e(b)("preferences.theme.darkSidebar")),1)]),_:1},8,["modelValue","disabled"]),f(le,{modelValue:l.value,"onUpdate:modelValue":d[1]||(d[1]=h=>l.value=h),disabled:a.value==="dark"},{default:c(()=>[L(S(e(b)("preferences.theme.darkHeader")),1)]),_:1},8,["modelValue","disabled"])]))}}),Md={class:"flex items-center"},$d={key:0,class:"bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"},Bd={class:"p-1"},Vd=T({__name:"preferences-drawer",props:{appLocale:{},appLocaleModifiers:{},appDynamicTitle:{type:Boolean},appDynamicTitleModifiers:{},appLayout:{},appLayoutModifiers:{},appColorGrayMode:{type:Boolean},appColorGrayModeModifiers:{},appColorWeakMode:{type:Boolean},appColorWeakModeModifiers:{},appContentCompact:{},appContentCompactModifiers:{},appWatermark:{type:Boolean},appWatermarkModifiers:{},appEnableCheckUpdates:{type:Boolean},appEnableCheckUpdatesModifiers:{},appPreferencesButtonPosition:{},appPreferencesButtonPositionModifiers:{},transitionProgress:{type:Boolean},transitionProgressModifiers:{},transitionName:{},transitionNameModifiers:{},transitionLoading:{type:Boolean},transitionLoadingModifiers:{},transitionEnable:{type:Boolean},transitionEnableModifiers:{},themeColorPrimary:{},themeColorPrimaryModifiers:{},themeBuiltinType:{},themeBuiltinTypeModifiers:{},themeMode:{},themeModeModifiers:{},themeRadius:{},themeRadiusModifiers:{},themeSemiDarkSidebar:{type:Boolean},themeSemiDarkSidebarModifiers:{},themeSemiDarkHeader:{type:Boolean},themeSemiDarkHeaderModifiers:{},sidebarEnable:{type:Boolean},sidebarEnableModifiers:{},sidebarWidth:{},sidebarWidthModifiers:{},sidebarCollapsed:{type:Boolean},sidebarCollapsedModifiers:{},sidebarCollapsedShowTitle:{type:Boolean},sidebarCollapsedShowTitleModifiers:{},headerEnable:{type:Boolean},headerEnableModifiers:{},headerMode:{},headerModeModifiers:{},breadcrumbEnable:{type:Boolean},breadcrumbEnableModifiers:{},breadcrumbShowIcon:{type:Boolean},breadcrumbShowIconModifiers:{},breadcrumbShowHome:{type:Boolean},breadcrumbShowHomeModifiers:{},breadcrumbStyleType:{},breadcrumbStyleTypeModifiers:{},breadcrumbHideOnlyOne:{type:Boolean},breadcrumbHideOnlyOneModifiers:{},tabbarEnable:{type:Boolean},tabbarEnableModifiers:{},tabbarShowIcon:{type:Boolean},tabbarShowIconModifiers:{},tabbarShowMore:{type:Boolean},tabbarShowMoreModifiers:{},tabbarShowMaximize:{type:Boolean},tabbarShowMaximizeModifiers:{},tabbarPersist:{type:Boolean},tabbarPersistModifiers:{},tabbarDraggable:{type:Boolean},tabbarDraggableModifiers:{},tabbarStyleType:{},tabbarStyleTypeModifiers:{},navigationStyleType:{},navigationStyleTypeModifiers:{},navigationSplit:{type:Boolean},navigationSplitModifiers:{},navigationAccordion:{type:Boolean},navigationAccordionModifiers:{},footerEnable:{type:Boolean},footerEnableModifiers:{},footerFixed:{type:Boolean},footerFixedModifiers:{},copyrightSettingShow:{type:Boolean},copyrightSettingShowModifiers:{},copyrightEnable:{type:Boolean},copyrightEnableModifiers:{},copyrightCompanyName:{},copyrightCompanyNameModifiers:{},copyrightCompanySiteLink:{},copyrightCompanySiteLinkModifiers:{},copyrightDate:{},copyrightDateModifiers:{},copyrightIcp:{},copyrightIcpModifiers:{},copyrightIcpLink:{},copyrightIcpLinkModifiers:{},shortcutKeysEnable:{type:Boolean},shortcutKeysEnableModifiers:{},shortcutKeysGlobalSearch:{type:Boolean},shortcutKeysGlobalSearchModifiers:{},shortcutKeysGlobalLogout:{type:Boolean},shortcutKeysGlobalLogoutModifiers:{},shortcutKeysGlobalLockScreen:{type:Boolean},shortcutKeysGlobalLockScreenModifiers:{},widgetGlobalSearch:{type:Boolean},widgetGlobalSearchModifiers:{},widgetFullscreen:{type:Boolean},widgetFullscreenModifiers:{},widgetLanguageToggle:{type:Boolean},widgetLanguageToggleModifiers:{},widgetNotification:{type:Boolean},widgetNotificationModifiers:{},widgetThemeToggle:{type:Boolean},widgetThemeToggleModifiers:{},widgetSidebarToggle:{type:Boolean},widgetSidebarToggleModifiers:{},widgetLockScreen:{type:Boolean},widgetLockScreenModifiers:{},widgetRefresh:{type:Boolean},widgetRefreshModifiers:{}},emits:xe(["clearPreferencesAndLogout"],["update:appLocale","update:appDynamicTitle","update:appLayout","update:appColorGrayMode","update:appColorWeakMode","update:appContentCompact","update:appWatermark","update:appEnableCheckUpdates","update:appPreferencesButtonPosition","update:transitionProgress","update:transitionName","update:transitionLoading","update:transitionEnable","update:themeColorPrimary","update:themeBuiltinType","update:themeMode","update:themeRadius","update:themeSemiDarkSidebar","update:themeSemiDarkHeader","update:sidebarEnable","update:sidebarWidth","update:sidebarCollapsed","update:sidebarCollapsedShowTitle","update:headerEnable","update:headerMode","update:breadcrumbEnable","update:breadcrumbShowIcon","update:breadcrumbShowHome","update:breadcrumbStyleType","update:breadcrumbHideOnlyOne","update:tabbarEnable","update:tabbarShowIcon","update:tabbarShowMore","update:tabbarShowMaximize","update:tabbarPersist","update:tabbarDraggable","update:tabbarStyleType","update:navigationStyleType","update:navigationSplit","update:navigationAccordion","update:footerEnable","update:footerFixed","update:copyrightSettingShow","update:copyrightEnable","update:copyrightCompanyName","update:copyrightCompanySiteLink","update:copyrightDate","update:copyrightIcp","update:copyrightIcpLink","update:shortcutKeysEnable","update:shortcutKeysGlobalSearch","update:shortcutKeysGlobalLogout","update:shortcutKeysGlobalLockScreen","update:widgetGlobalSearch","update:widgetFullscreen","update:widgetLanguageToggle","update:widgetNotification","update:widgetThemeToggle","update:widgetSidebarToggle","update:widgetLockScreen","update:widgetRefresh"]),setup(o,{emit:a}){const t=a,l=Ba.getMessage(),n=k(o,"appLocale"),s=k(o,"appDynamicTitle"),r=k(o,"appLayout"),u=k(o,"appColorGrayMode"),d=k(o,"appColorWeakMode"),h=k(o,"appContentCompact"),m=k(o,"appWatermark"),p=k(o,"appEnableCheckUpdates"),g=k(o,"appPreferencesButtonPosition"),v=k(o,"transitionProgress"),V=k(o,"transitionName"),_=k(o,"transitionLoading"),U=k(o,"transitionEnable"),I=k(o,"themeColorPrimary"),N=k(o,"themeBuiltinType"),Z=k(o,"themeMode"),H=k(o,"themeRadius"),R=k(o,"themeSemiDarkSidebar"),X=k(o,"themeSemiDarkHeader"),Q=k(o,"sidebarEnable"),z=k(o,"sidebarWidth"),F=k(o,"sidebarCollapsed"),j=k(o,"sidebarCollapsedShowTitle"),re=k(o,"headerEnable"),ve=k(o,"headerMode"),be=k(o,"breadcrumbEnable"),W=k(o,"breadcrumbShowIcon"),ee=k(o,"breadcrumbShowHome"),ge=k(o,"breadcrumbStyleType"),he=k(o,"breadcrumbHideOnlyOne"),Me=k(o,"tabbarEnable"),P=k(o,"tabbarShowIcon"),G=k(o,"tabbarShowMore"),te=k(o,"tabbarShowMaximize"),ae=k(o,"tabbarPersist"),se=k(o,"tabbarDraggable"),ye=k(o,"tabbarStyleType"),Ye=k(o,"navigationStyleType"),Ne=k(o,"navigationSplit"),Xe=k(o,"navigationAccordion"),Fe=k(o,"footerEnable"),Je=k(o,"footerFixed"),He=k(o,"copyrightSettingShow"),A=k(o,"copyrightEnable"),oe=k(o,"copyrightCompanyName"),fe=k(o,"copyrightCompanySiteLink"),Ue=k(o,"copyrightDate"),ze=k(o,"copyrightIcp"),Yt=k(o,"copyrightIcpLink"),Xt=k(o,"shortcutKeysEnable"),Jt=k(o,"shortcutKeysGlobalSearch"),Zt=k(o,"shortcutKeysGlobalLogout"),Qt=k(o,"shortcutKeysGlobalLockScreen"),ea=k(o,"widgetGlobalSearch"),ta=k(o,"widgetFullscreen"),aa=k(o,"widgetLanguageToggle"),oa=k(o,"widgetNotification"),la=k(o,"widgetThemeToggle"),sa=k(o,"widgetSidebarToggle"),na=k(o,"widgetLockScreen"),ra=k(o,"widgetRefresh"),{diffPreference:at,isDark:wo,isFullContent:Lt,isHeaderNav:xo,isMixedNav:ia,isSideMixedNav:ko,isSideMode:_o,isSideNav:So}=Qe(),{copy:Co}=es({legacy:!0}),[To]=so(),da=q("appearance"),Mo=w(()=>[{label:b("preferences.appearance"),value:"appearance"},{label:b("preferences.layout"),value:"layout"},{label:b("preferences.shortcutKeys.title"),value:"shortcutKey"},{label:b("preferences.general"),value:"general"}]),$o=w(()=>!Lt.value&&!ia.value&&!xo.value&&D.header.enable);function Bo(){return K(this,null,function*(){var Et;yield Co(JSON.stringify(at.value,null,2)),(Et=l.copyPreferencesSuccess)==null||Et.call(l,b("preferences.copyPreferencesSuccessTitle"),b("preferences.copyPreferencesSuccess"))})}function Vo(){return K(this,null,function*(){fa(),ts(),t("clearPreferencesAndLogout")})}function Po(){return K(this,null,function*(){at.value&&(fa(),yield Ra(D.app.locale))})}return(Et,M)=>(i(),x("div",null,[f(e(To),{description:e(b)("preferences.subtitle"),title:e(b)("preferences.title"),class:"sm:max-w-sm"},{extra:c(()=>[C("div",Md,[f(e(je),{disabled:!e(at),tooltip:e(b)("preferences.resetTip"),class:"relative"},{default:c(()=>[e(at)?(i(),x("span",$d)):E("",!0),f(e(Nt),{class:"size-4",onClick:Po})]),_:1},8,["disabled","tooltip"])])]),footer:c(()=>[f(e(Pe),{disabled:!e(at),class:"mx-4 w-full",size:"sm",variant:"default",onClick:Bo},{default:c(()=>[f(e(Hs),{class:"mr-2 size-3"}),L(" "+S(e(b)("preferences.copyPreferences")),1)]),_:1},8,["disabled"]),f(e(Pe),{disabled:!e(at),class:"mr-4 w-full",size:"sm",variant:"ghost",onClick:Vo},{default:c(()=>[L(S(e(b)("preferences.clearAndLogout")),1)]),_:1},8,["disabled"])]),default:c(()=>[C("div",Bd,[f(e(sr),{modelValue:da.value,"onUpdate:modelValue":M[60]||(M[60]=$=>da.value=$),tabs:Mo.value},{general:c(()=>[f(e(Se),{title:e(b)("preferences.general")},{default:c(()=>[f(e(_i),{"app-dynamic-title":s.value,"onUpdate:appDynamicTitle":M[0]||(M[0]=$=>s.value=$),"app-enable-check-updates":p.value,"onUpdate:appEnableCheckUpdates":M[1]||(M[1]=$=>p.value=$),"app-locale":n.value,"onUpdate:appLocale":M[2]||(M[2]=$=>n.value=$),"app-watermark":m.value,"onUpdate:appWatermark":M[3]||(M[3]=$=>m.value=$)},null,8,["app-dynamic-title","app-enable-check-updates","app-locale","app-watermark"])]),_:1},8,["title"]),f(e(Se),{title:e(b)("preferences.animation.title")},{default:c(()=>[f(e(xi),{"transition-enable":U.value,"onUpdate:transitionEnable":M[4]||(M[4]=$=>U.value=$),"transition-loading":_.value,"onUpdate:transitionLoading":M[5]||(M[5]=$=>_.value=$),"transition-name":V.value,"onUpdate:transitionName":M[6]||(M[6]=$=>V.value=$),"transition-progress":v.value,"onUpdate:transitionProgress":M[7]||(M[7]=$=>v.value=$)},null,8,["transition-enable","transition-loading","transition-name","transition-progress"])]),_:1},8,["title"])]),appearance:c(()=>[f(e(Se),{title:e(b)("preferences.theme.title")},{default:c(()=>[f(e(Td),{modelValue:Z.value,"onUpdate:modelValue":M[8]||(M[8]=$=>Z.value=$),"theme-semi-dark-header":X.value,"onUpdate:themeSemiDarkHeader":M[9]||(M[9]=$=>X.value=$),"theme-semi-dark-sidebar":R.value,"onUpdate:themeSemiDarkSidebar":M[10]||(M[10]=$=>R.value=$)},null,8,["modelValue","theme-semi-dark-header","theme-semi-dark-sidebar"])]),_:1},8,["title"]),f(e(Se),{title:e(b)("preferences.theme.builtin.title")},{default:c(()=>[f(e(wd),{modelValue:N.value,"onUpdate:modelValue":M[11]||(M[11]=$=>N.value=$),"theme-color-primary":I.value,"onUpdate:themeColorPrimary":M[12]||(M[12]=$=>I.value=$),"is-dark":e(wo)},null,8,["modelValue","theme-color-primary","is-dark"])]),_:1},8,["title"]),f(e(Se),{title:e(b)("preferences.theme.radius")},{default:c(()=>[f(e(kd),{modelValue:H.value,"onUpdate:modelValue":M[13]||(M[13]=$=>H.value=$)},null,8,["modelValue"])]),_:1},8,["title"]),f(e(Se),{title:e(b)("preferences.other")},{default:c(()=>[f(e(xd),{"app-color-gray-mode":u.value,"onUpdate:appColorGrayMode":M[14]||(M[14]=$=>u.value=$),"app-color-weak-mode":d.value,"onUpdate:appColorWeakMode":M[15]||(M[15]=$=>d.value=$)},null,8,["app-color-gray-mode","app-color-weak-mode"])]),_:1},8,["title"])]),layout:c(()=>[f(e(Se),{title:e(b)("preferences.layout")},{default:c(()=>[f(e(rd),{modelValue:r.value,"onUpdate:modelValue":M[16]||(M[16]=$=>r.value=$)},null,8,["modelValue"])]),_:1},8,["title"]),f(e(Se),{title:e(b)("preferences.content")},{default:c(()=>[f(e(Qi),{modelValue:h.value,"onUpdate:modelValue":M[17]||(M[17]=$=>h.value=$)},null,8,["modelValue"])]),_:1},8,["title"]),f(e(Se),{title:e(b)("preferences.sidebar.title")},{default:c(()=>[f(e(cd),{"sidebar-collapsed":F.value,"onUpdate:sidebarCollapsed":M[18]||(M[18]=$=>F.value=$),"sidebar-collapsed-show-title":j.value,"onUpdate:sidebarCollapsedShowTitle":M[19]||(M[19]=$=>j.value=$),"sidebar-enable":Q.value,"onUpdate:sidebarEnable":M[20]||(M[20]=$=>Q.value=$),"sidebar-width":z.value,"onUpdate:sidebarWidth":M[21]||(M[21]=$=>z.value=$),disabled:!e(_o)},null,8,["sidebar-collapsed","sidebar-collapsed-show-title","sidebar-enable","sidebar-width","disabled"])]),_:1},8,["title"]),f(e(Se),{title:e(b)("preferences.header.title")},{default:c(()=>[f(e(od),{"header-enable":re.value,"onUpdate:headerEnable":M[22]||(M[22]=$=>re.value=$),"header-mode":ve.value,"onUpdate:headerMode":M[23]||(M[23]=$=>ve.value=$),disabled:e(Lt)},null,8,["header-enable","header-mode","disabled"])]),_:1},8,["title"]),f(e(Se),{title:e(b)("preferences.navigationMenu.title")},{default:c(()=>[f(e(id),{"navigation-accordion":Xe.value,"onUpdate:navigationAccordion":M[24]||(M[24]=$=>Xe.value=$),"navigation-split":Ne.value,"onUpdate:navigationSplit":M[25]||(M[25]=$=>Ne.value=$),"navigation-style-type":Ye.value,"onUpdate:navigationStyleType":M[26]||(M[26]=$=>Ye.value=$),disabled:e(Lt),"disabled-navigation-split":!e(ia)},null,8,["navigation-accordion","navigation-split","navigation-style-type","disabled","disabled-navigation-split"])]),_:1},8,["title"]),f(e(Se),{title:e(b)("preferences.breadcrumb.title")},{default:c(()=>[f(e(Ci),{"breadcrumb-enable":be.value,"onUpdate:breadcrumbEnable":M[27]||(M[27]=$=>be.value=$),"breadcrumb-hide-only-one":he.value,"onUpdate:breadcrumbHideOnlyOne":M[28]||(M[28]=$=>he.value=$),"breadcrumb-show-home":ee.value,"onUpdate:breadcrumbShowHome":M[29]||(M[29]=$=>ee.value=$),"breadcrumb-show-icon":W.value,"onUpdate:breadcrumbShowIcon":M[30]||(M[30]=$=>W.value=$),"breadcrumb-style-type":ge.value,"onUpdate:breadcrumbStyleType":M[31]||(M[31]=$=>ge.value=$),disabled:!$o.value||!(e(So)||e(ko))},null,8,["breadcrumb-enable","breadcrumb-hide-only-one","breadcrumb-show-home","breadcrumb-show-icon","breadcrumb-style-type","disabled"])]),_:1},8,["title"]),f(e(Se),{title:e(b)("preferences.tabbar.title")},{default:c(()=>[f(e(pd),{"tabbar-draggable":se.value,"onUpdate:tabbarDraggable":M[32]||(M[32]=$=>se.value=$),"tabbar-enable":Me.value,"onUpdate:tabbarEnable":M[33]||(M[33]=$=>Me.value=$),"tabbar-persist":ae.value,"onUpdate:tabbarPersist":M[34]||(M[34]=$=>ae.value=$),"tabbar-show-icon":P.value,"onUpdate:tabbarShowIcon":M[35]||(M[35]=$=>P.value=$),"tabbar-show-maximize":te.value,"onUpdate:tabbarShowMaximize":M[36]||(M[36]=$=>te.value=$),"tabbar-show-more":G.value,"onUpdate:tabbarShowMore":M[37]||(M[37]=$=>G.value=$),"tabbar-style-type":ye.value,"onUpdate:tabbarStyleType":M[38]||(M[38]=$=>ye.value=$)},null,8,["tabbar-draggable","tabbar-enable","tabbar-persist","tabbar-show-icon","tabbar-show-maximize","tabbar-show-more","tabbar-style-type"])]),_:1},8,["title"]),f(e(Se),{title:e(b)("preferences.widget.title")},{default:c(()=>[f(e(fd),{"app-preferences-button-position":g.value,"onUpdate:appPreferencesButtonPosition":M[39]||(M[39]=$=>g.value=$),"widget-fullscreen":ta.value,"onUpdate:widgetFullscreen":M[40]||(M[40]=$=>ta.value=$),"widget-global-search":ea.value,"onUpdate:widgetGlobalSearch":M[41]||(M[41]=$=>ea.value=$),"widget-language-toggle":aa.value,"onUpdate:widgetLanguageToggle":M[42]||(M[42]=$=>aa.value=$),"widget-lock-screen":na.value,"onUpdate:widgetLockScreen":M[43]||(M[43]=$=>na.value=$),"widget-notification":oa.value,"onUpdate:widgetNotification":M[44]||(M[44]=$=>oa.value=$),"widget-refresh":ra.value,"onUpdate:widgetRefresh":M[45]||(M[45]=$=>ra.value=$),"widget-sidebar-toggle":sa.value,"onUpdate:widgetSidebarToggle":M[46]||(M[46]=$=>sa.value=$),"widget-theme-toggle":la.value,"onUpdate:widgetThemeToggle":M[47]||(M[47]=$=>la.value=$)},null,8,["app-preferences-button-position","widget-fullscreen","widget-global-search","widget-language-toggle","widget-lock-screen","widget-notification","widget-refresh","widget-sidebar-toggle","widget-theme-toggle"])]),_:1},8,["title"]),f(e(Se),{title:e(b)("preferences.footer.title")},{default:c(()=>[f(e(ad),{"footer-enable":Fe.value,"onUpdate:footerEnable":M[48]||(M[48]=$=>Fe.value=$),"footer-fixed":Je.value,"onUpdate:footerFixed":M[49]||(M[49]=$=>Je.value=$)},null,8,["footer-enable","footer-fixed"])]),_:1},8,["title"]),He.value?(i(),y(e(Se),{key:0,title:e(b)("preferences.copyright.title")},{default:c(()=>[f(e(td),{"copyright-company-name":oe.value,"onUpdate:copyrightCompanyName":M[50]||(M[50]=$=>oe.value=$),"copyright-company-site-link":fe.value,"onUpdate:copyrightCompanySiteLink":M[51]||(M[51]=$=>fe.value=$),"copyright-date":Ue.value,"onUpdate:copyrightDate":M[52]||(M[52]=$=>Ue.value=$),"copyright-enable":A.value,"onUpdate:copyrightEnable":M[53]||(M[53]=$=>A.value=$),"copyright-icp":ze.value,"onUpdate:copyrightIcp":M[54]||(M[54]=$=>ze.value=$),"copyright-icp-link":Yt.value,"onUpdate:copyrightIcpLink":M[55]||(M[55]=$=>Yt.value=$),disabled:!Fe.value},null,8,["copyright-company-name","copyright-company-site-link","copyright-date","copyright-enable","copyright-icp","copyright-icp-link","disabled"])]),_:1},8,["title"])):E("",!0)]),shortcutKey:c(()=>[f(e(Se),{title:e(b)("preferences.shortcutKeys.global")},{default:c(()=>[f(e(md),{"shortcut-keys-enable":Xt.value,"onUpdate:shortcutKeysEnable":M[56]||(M[56]=$=>Xt.value=$),"shortcut-keys-global-search":Jt.value,"onUpdate:shortcutKeysGlobalSearch":M[57]||(M[57]=$=>Jt.value=$),"shortcut-keys-lock-screen":Qt.value,"onUpdate:shortcutKeysLockScreen":M[58]||(M[58]=$=>Qt.value=$),"shortcut-keys-logout":Zt.value,"onUpdate:shortcutKeysLogout":M[59]||(M[59]=$=>Zt.value=$)},null,8,["shortcut-keys-enable","shortcut-keys-global-search","shortcut-keys-lock-screen","shortcut-keys-logout"])]),_:1},8,["title"])]),_:1},8,["modelValue","tabs"])])]),_:1},8,["description","title"])]))}}),io=T({__name:"preferences",setup(o){const[a,t]=so({connectedComponent:Vd}),l=w(()=>{const s={};for(const[r,u]of Object.entries(D))for(const[d,h]of Object.entries(u))s[`${r}${ma(d)}`]=h;return s}),n=w(()=>{const s={};for(const[r,u]of Object.entries(D))if(typeof u=="object")for(const d of Object.keys(u))s[`update:${r}${ma(d)}`]=h=>{Ke({[r]:{[d]:h}}),r==="app"&&d==="locale"&&Ra(h)};else s[r]=u;return s});return(s,r)=>(i(),x("div",null,[f(e(a),ie(J(J({},s.$attrs),l.value),Wa(n.value)),null,16),C("div",{onClick:r[0]||(r[0]=()=>e(t).open())},[B(s.$slots,"default",{},()=>[f(e(Pe),{title:e(b)("preferences.title"),class:"bg-primary flex-col-center size-10 cursor-pointer rounded-l-lg rounded-r-none border-none"},{default:c(()=>[f(e(to),{class:"size-5"})]),_:1},8,["title"])])])]))}}),Pd=T({__name:"preferences-button",emits:["clearPreferencesAndLogout"],setup(o,{emit:a}){const t=a;function l(){t("clearPreferencesAndLogout")}return(n,s)=>(i(),y(io,{onClearPreferencesAndLogout:l},{default:c(()=>[f(e(je),null,{default:c(()=>[f(e(to),{class:"text-foreground size-4"})]),_:1})]),_:1}))}}),Ld={class:"hover:bg-accent ml-1 mr-2 cursor-pointer rounded-full p-1.5"},Ed={class:"hover:text-accent-foreground flex-center"},zd={class:"ml-2 w-full"},Id={key:0,class:"text-foreground mb-1 flex items-center text-sm font-medium"},Ud={class:"text-muted-foreground text-xs font-normal"},Ju=T({name:"UserDropdown",__name:"user-dropdown",props:{avatar:{default:""},description:{default:""},enableShortcutKey:{type:Boolean,default:!0},menus:{default:()=>[]},tagText:{default:""},text:{default:""}},emits:["logout"],setup(o,{emit:a}){const t=o,l=a,n=q(!1),{globalLockScreenShortcutKey:s,globalLogoutShortcutKey:r}=Qe(),u=Kt(),[d,h]=ut({connectedComponent:Zr}),[m,p]=ut({onConfirm(){Z()}}),g=w(()=>it()?"Alt":"⌥"),v=w(()=>t.enableShortcutKey&&r.value),V=w(()=>t.enableShortcutKey&&s.value),_=w(()=>t.enableShortcutKey&&D.shortcutKeys.enable);function U(){h.open()}function I(H){h.close(),u.lockScreen(H)}function N(){p.open(),n.value=!1}function Z(){l("logout"),p.close()}if(_.value){const H=Ha();xt(H["Alt+KeyQ"],()=>{v.value&&N()}),xt(H["Alt+KeyL"],()=>{V.value&&U()})}return(H,R)=>(i(),x(Y,null,[e(D).widget.lockScreen?(i(),y(e(d),{key:0,avatar:H.avatar,text:H.text,onSubmit:I},null,8,["avatar","text"])):E("",!0),f(e(m),{"cancel-text":e(b)("common.cancel"),"confirm-text":e(b)("common.confirm"),"fullscreen-button":!1,title:e(b)("common.prompt"),centered:"","content-class":"px-8 min-h-10","footer-class":"border-none mb-3 mr-3","header-class":"border-none"},{default:c(()=>[L(S(e(b)("ui.widgets.logoutTip")),1)]),_:1},8,["cancel-text","confirm-text","title"]),f(e(Dt),null,{default:c(()=>[f(e(Rt),null,{default:c(()=>[C("div",Ld,[C("div",Ed,[f(e(dt),{alt:H.text,src:H.avatar,class:"size-8",dot:""},null,8,["alt","src"])])])]),_:1}),f(e(Wt),{class:"mr-2 min-w-[240px] p-0 pb-1"},{default:c(()=>{var X;return[f(e(sn),{class:"flex items-center p-3"},{default:c(()=>[f(e(dt),{alt:H.text,src:H.avatar,class:"size-12",dot:"","dot-class":"bottom-0 right-1 border-2 size-4 bg-green-500"},null,8,["alt","src"]),C("div",zd,[H.tagText||H.text||H.$slots.tagText?(i(),x("div",Id,[L(S(H.text)+" ",1),B(H.$slots,"tagText",{},()=>[H.tagText?(i(),y(e(Zs),{key:0,class:"ml-2 text-green-400"},{default:c(()=>[L(S(H.tagText),1)]),_:1})):E("",!0)])])):E("",!0),C("div",Ud,S(H.description),1)])]),_:3}),(X=H.menus)!=null&&X.length?(i(),y(e(Ut),{key:0})):E("",!0),e(D).widget.lockScreen?(i(),y(e(_t),{key:1,class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:U},{default:c(()=>[f(e(Za),{class:"mr-2 size-4"}),L(" "+S(e(b)("ui.widgets.lockScreen.title"))+" ",1),V.value?(i(),y(e(ga),{key:0},{default:c(()=>[L(S(g.value)+" L ",1)]),_:1})):E("",!0)]),_:1})):E("",!0),e(D).widget.lockScreen?(i(),y(e(Ut),{key:2})):E("",!0),f(e(_t),{class:"mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8",onClick:N},{default:c(()=>[f(e(Ns),{class:"mr-2 size-4"}),L(" "+S(e(b)("common.logout"))+" ",1),v.value?(i(),y(e(ga),{key:0},{default:c(()=>[L(S(g.value)+" Q ",1)]),_:1})):E("",!0)]),_:1})]}),_:3})]),_:3})],64))}}),Ad=T({__name:"layout-content",props:{contentCompact:{},contentCompactWidth:{},padding:{},paddingBottom:{},paddingLeft:{},paddingRight:{},paddingTop:{}},setup(o){const a=o,{contentElement:t,overlayStyle:l}=as(),n=w(()=>{const{contentCompact:s,padding:r,paddingBottom:u,paddingLeft:d,paddingRight:h,paddingTop:m}=a,p=s==="compact"?{margin:"0 auto",width:`${a.contentCompactWidth}px`}:{};return ke(J({},p),{flex:1,padding:`${r}px`,paddingBottom:`${u}px`,paddingLeft:`${d}px`,paddingRight:`${h}px`,paddingTop:`${m}px`})});return(s,r)=>(i(),x("main",{ref_key:"contentElement",ref:t,style:de(n.value),class:"bg-background-deep relative"},[f(e(El),{style:de(e(l))},{default:c(()=>[B(s.$slots,"overlay")]),_:3},8,["style"]),B(s.$slots,"default")],4))}}),Hd=T({__name:"layout-footer",props:{fixed:{type:Boolean},height:{},show:{type:Boolean,default:!0},width:{},zIndex:{}},setup(o){const a=o,t=w(()=>{const{fixed:l,height:n,show:s,width:r,zIndex:u}=a;return{height:`${n}px`,marginBottom:s?"0":`-${n}px`,position:l?"fixed":"static",width:r,zIndex:u}});return(l,n)=>(i(),x("footer",{style:de(t.value),class:"bg-background-deep bottom-0 w-full transition-all duration-200"},[B(l.$slots,"default")],4))}}),Od=T({__name:"layout-header",props:{fullWidth:{type:Boolean},height:{},isMobile:{type:Boolean},show:{type:Boolean},sidebarWidth:{},theme:{},width:{},zIndex:{}},setup(o){const a=o,t=Ae(),l=w(()=>{const{fullWidth:s,height:r,show:u}=a,d=!u||!s?void 0:0;return{height:`${r}px`,marginTop:u?0:`-${r}px`,right:d}}),n=w(()=>({minWidth:`${a.isMobile?40:a.sidebarWidth}px`}));return(s,r)=>(i(),x("header",{class:O([s.theme,"border-border bg-header top-0 flex w-full flex-[0_0_auto] items-center border-b transition-[margin-top] duration-200"]),style:de(l.value)},[e(t).logo?(i(),x("div",{key:0,style:de(n.value)},[B(s.$slots,"logo")],4)):E("",!0),B(s.$slots,"toggle-button"),B(s.$slots,"default")],6))}}),_a=T({__name:"sidebar-collapse-button",props:{collapsed:{type:Boolean},collapsedModifiers:{}},emits:["update:collapsed"],setup(o){const a=k(o,"collapsed");function t(){a.value=!a.value}return(l,n)=>(i(),x("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 left-3 z-10 cursor-pointer rounded-sm p-1",onClick:Ce(t,["stop"])},[a.value?(i(),y(e(zl),{key:0,class:"size-4"})):(i(),y(e(As),{key:1,class:"size-4"}))]))}}),Sa=T({__name:"sidebar-fixed-button",props:{expandOnHover:{type:Boolean},expandOnHoverModifiers:{}},emits:["update:expandOnHover"],setup(o){const a=k(o,"expandOnHover");function t(){a.value=!a.value}return(l,n)=>(i(),x("div",{class:"flex-center hover:text-foreground text-foreground/60 hover:bg-accent-hover bg-accent absolute bottom-2 right-3 z-10 cursor-pointer rounded-sm p-[5px] transition-all duration-300",onClick:t},[a.value?(i(),y(e(Vt),{key:1,class:"size-3.5"})):(i(),y(e(eo),{key:0,class:"size-3.5"}))]))}}),Dd=T({__name:"layout-sidebar",props:xe({collapseHeight:{default:42},collapseWidth:{default:48},domVisible:{type:Boolean,default:!0},extraWidth:{},fixedExtra:{type:Boolean,default:!1},headerHeight:{},isSidebarMixed:{type:Boolean,default:!1},marginTop:{default:0},mixedWidth:{default:70},paddingTop:{default:0},show:{type:Boolean,default:!0},showCollapseButton:{type:Boolean,default:!0},theme:{},width:{},zIndex:{default:0}},{collapse:{type:Boolean},collapseModifiers:{},extraCollapse:{type:Boolean},extraCollapseModifiers:{},expandOnHovering:{type:Boolean},expandOnHoveringModifiers:{},expandOnHover:{type:Boolean},expandOnHoverModifiers:{},extraVisible:{type:Boolean},extraVisibleModifiers:{}}),emits:xe(["leave"],["update:collapse","update:extraCollapse","update:expandOnHovering","update:expandOnHover","update:extraVisible"]),setup(o,{emit:a}){const t=o,l=a,n=k(o,"collapse"),s=k(o,"extraCollapse"),r=k(o,"expandOnHovering"),u=k(o,"expandOnHover"),d=k(o,"extraVisible"),h=os(document.body),m=Ae(),p=wt(),g=w(()=>R(!0)),v=w(()=>{const{isSidebarMixed:z,marginTop:F,paddingTop:j,zIndex:re}=t;return J(ke(J({"--scroll-shadow":"var(--sidebar)"},R(!1)),{height:`calc(100% - ${F}px)`,marginTop:`${F}px`,paddingTop:`${j}px`,zIndex:re}),z&&d.value?{transition:"none"}:{})}),V=w(()=>{const{extraWidth:z,show:F,width:j,zIndex:re}=t;return{left:`${j}px`,width:d.value&&F?`${z}px`:0,zIndex:re}}),_=w(()=>{const{headerHeight:z}=t;return{height:`${z-1}px`}}),U=w(()=>{const{collapseWidth:z,fixedExtra:F,isSidebarMixed:j,mixedWidth:re}=t;return j&&F?{width:`${n.value?z:re}px`}:{}}),I=w(()=>{const{collapseHeight:z,headerHeight:F}=t;return J({height:`calc(100% - ${F+z}px)`,paddingTop:"8px"},U.value)}),N=w(()=>{const{headerHeight:z,isSidebarMixed:F}=t;return J(ke(J({},F?{display:"flex",justifyContent:"center"}:{}),{height:`${z}px`}),U.value)}),Z=w(()=>{const{collapseHeight:z,headerHeight:F}=t;return{height:`calc(100% - ${F+z}px)`}}),H=w(()=>({height:`${t.collapseHeight}px`}));Na(()=>{d.value=t.fixedExtra?!0:d.value});function R(z){const{extraWidth:F,fixedExtra:j,isSidebarMixed:re,show:ve,width:be}=t;let W=`${be+(re&&j&&d.value?F:0)}px`;const{collapseWidth:ee}=t;return z&&r.value&&!u.value&&(W=`${ee}px`),ke(J({},W==="0px"?{overflow:"hidden"}:{}),{flex:`0 0 ${W}`,marginLeft:ve?0:`-${W}`,maxWidth:W,minWidth:W,width:W})}function X(z){(z==null?void 0:z.offsetX)<10||u.value||(r.value||(n.value=!1),t.isSidebarMixed&&(h.value=!0),r.value=!0)}function Q(){l("leave"),t.isSidebarMixed&&(h.value=!1),!u.value&&(r.value=!1,n.value=!0,d.value=!1)}return(z,F)=>(i(),x(Y,null,[z.domVisible?(i(),x("div",{key:0,class:O([z.theme,"h-full transition-all duration-150"]),style:de(g.value)},null,6)):E("",!0),C("aside",{class:O([[z.theme,{"bg-sidebar-deep":z.isSidebarMixed,"bg-sidebar border-border border-r":!z.isSidebarMixed}],"fixed left-0 top-0 h-full transition-all duration-150"]),style:de(v.value),onMouseenter:X,onMouseleave:Q},[!n.value&&!z.isSidebarMixed?(i(),y(e(Sa),{key:0,"expand-on-hover":u.value,"onUpdate:expandOnHover":F[0]||(F[0]=j=>u.value=j)},null,8,["expand-on-hover"])):E("",!0),e(m).logo?(i(),x("div",{key:1,style:de(N.value)},[B(z.$slots,"logo")],4)):E("",!0),f(e(ct),{style:de(I.value),shadow:"","shadow-border":""},{default:c(()=>[B(z.$slots,"default")]),_:3},8,["style"]),C("div",{style:de(H.value)},null,4),z.showCollapseButton&&!z.isSidebarMixed?(i(),y(e(_a),{key:2,collapsed:n.value,"onUpdate:collapsed":F[1]||(F[1]=j=>n.value=j)},null,8,["collapsed"])):E("",!0),z.isSidebarMixed?(i(),x("div",{key:3,ref_key:"asideRef",ref:p,class:O([{"border-l":d.value},"border-border bg-sidebar fixed top-0 h-full overflow-hidden border-r transition-all duration-200"]),style:de(V.value)},[z.isSidebarMixed&&u.value?(i(),y(e(_a),{key:0,collapsed:s.value,"onUpdate:collapsed":F[2]||(F[2]=j=>s.value=j)},null,8,["collapsed"])):E("",!0),s.value?E("",!0):(i(),y(e(Sa),{key:1,"expand-on-hover":u.value,"onUpdate:expandOnHover":F[3]||(F[3]=j=>u.value=j)},null,8,["expand-on-hover"])),s.value?E("",!0):(i(),x("div",{key:2,style:de(_.value),class:"pl-2"},[B(z.$slots,"extra-title")],4)),f(e(ct),{style:de(Z.value),class:"border-border py-2",shadow:"","shadow-border":""},{default:c(()=>[B(z.$slots,"extra")]),_:3},8,["style"])],6)):E("",!0)],38)],64))}}),Rd=T({__name:"layout-tabbar",props:{height:{}},setup(o){const a=o,t=w(()=>{const{height:l}=a;return{height:`${l}px`}});return(l,n)=>(i(),x("section",{style:de(t.value),class:"border-border bg-background flex w-full border-b transition-all"},[B(l.$slots,"default")],4))}});function Wd(o){const a=w(()=>o.isMobile?"sidebar-nav":o.layout),t=w(()=>a.value==="full-content"),l=w(()=>a.value==="sidebar-mixed-nav"),n=w(()=>a.value==="header-nav"),s=w(()=>a.value==="mixed-nav");return{currentLayout:a,isFullContent:t,isHeaderNav:n,isMixedNav:s,isSidebarMixedNav:l}}const Nd={class:"relative flex min-h-full w-full"},Fd=T({name:"VbenLayout",__name:"vben-layout",props:xe({contentCompact:{default:"wide"},contentCompactWidth:{default:1200},contentPadding:{default:0},contentPaddingBottom:{default:0},contentPaddingLeft:{default:0},contentPaddingRight:{default:0},contentPaddingTop:{default:0},footerEnable:{type:Boolean,default:!1},footerFixed:{type:Boolean,default:!0},footerHeight:{default:32},headerHeight:{default:50},headerHidden:{type:Boolean,default:!1},headerMode:{default:"fixed"},headerTheme:{},headerToggleSidebarButton:{type:Boolean,default:!0},headerVisible:{type:Boolean,default:!0},isMobile:{type:Boolean,default:!1},layout:{default:"sidebar-nav"},sidebarCollapse:{type:Boolean},sidebarCollapseShowTitle:{type:Boolean,default:!1},sidebarEnable:{type:Boolean},sidebarExtraCollapsedWidth:{default:60},sidebarHidden:{type:Boolean,default:!1},sidebarMixedWidth:{default:80},sidebarTheme:{default:"dark"},sidebarWidth:{default:180},sideCollapseWidth:{default:60},tabbarEnable:{type:Boolean,default:!0},tabbarHeight:{default:40},zIndex:{default:200}},{sidebarCollapse:{type:Boolean},sidebarCollapseModifiers:{},sidebarExtraVisible:{type:Boolean},sidebarExtraVisibleModifiers:{},sidebarExtraCollapse:{type:Boolean},sidebarExtraCollapseModifiers:{},sidebarExpandOnHover:{type:Boolean},sidebarExpandOnHoverModifiers:{},sidebarEnable:{type:Boolean,default:!0},sidebarEnableModifiers:{}}),emits:xe(["sideMouseLeave","toggleSidebar"],["update:sidebarCollapse","update:sidebarExtraVisible","update:sidebarExtraCollapse","update:sidebarExpandOnHover","update:sidebarEnable"]),setup(o,{emit:a}){const t=o,l=a,n=k(o,"sidebarCollapse"),s=k(o,"sidebarExtraVisible"),r=k(o,"sidebarExtraCollapse"),u=k(o,"sidebarExpandOnHover"),d=k(o,"sidebarEnable"),h=q(!1),m=q(!1),p=q(),{arrivedState:g,directions:v,isScrolling:V,y:_}=ls(document),{setLayoutHeaderHeight:U}=ss(),{setLayoutFooterHeight:I}=ns(),{y:N}=rs({target:p,type:"client"}),{currentLayout:Z,isFullContent:H,isHeaderNav:R,isMixedNav:X,isSidebarMixedNav:Q}=Wd(t),z=w(()=>t.headerMode==="auto"),F=w(()=>{let A=0;return t.headerVisible&&!t.headerHidden&&(A+=t.headerHeight),t.tabbarEnable&&(A+=t.tabbarHeight),A}),j=w(()=>{const{sidebarCollapseShowTitle:A,sidebarMixedWidth:oe,sideCollapseWidth:fe}=t;return A||Q.value?oe:fe}),re=w(()=>!R.value&&d.value),ve=w(()=>{const{headerHeight:A,isMobile:oe}=t;return X.value&&!oe?A:0}),be=w(()=>{const{isMobile:A,sidebarHidden:oe,sidebarMixedWidth:fe,sidebarWidth:Ue}=t;let ze=0;return oe||!re.value||oe&&!Q.value&&!X.value||(Q.value&&!A?ze=fe:n.value?ze=A?0:j.value:ze=Ue),ze}),W=w(()=>{const{sidebarExtraCollapsedWidth:A,sidebarWidth:oe}=t;return r.value?A:oe}),ee=w(()=>Z.value==="mixed-nav"||Z.value==="sidebar-mixed-nav"||Z.value==="sidebar-nav"),ge=w(()=>{const{headerMode:A}=t;return X.value||A==="fixed"||A==="auto-scroll"||A==="auto"}),he=w(()=>ee.value&&d.value),Me=w(()=>!n.value&&t.isMobile),P=w(()=>{let A="100%",oe="unset";if(ge.value&&Z.value!=="header-nav"&&Z.value!=="mixed-nav"&&he.value&&!t.isMobile)if(Q.value&&u.value&&s.value){const Ue=n.value?j.value:t.sidebarMixedWidth,ze=r.value?t.sidebarExtraCollapsedWidth:t.sidebarWidth;oe=`${Ue+ze}px`,A=`calc(100% - ${oe})`}else oe=h.value&&!u.value?`${j.value}px`:`${be.value}px`,A=`calc(100% - ${oe})`;return{sidebarAndExtraWidth:oe,width:A}}),G=w(()=>{let A="",oe=0;if(!X.value||t.sidebarHidden)A="100%";else if(d.value){const fe=u.value?t.sidebarWidth:j.value;oe=n.value?j.value:fe,A=`calc(100% - ${n.value?be.value:fe}px)`}else A="100%";return{marginLeft:`${oe}px`,width:A}}),te=w(()=>{const A=ge.value,{footerEnable:oe,footerFixed:fe,footerHeight:Ue}=t;return{marginTop:A&&!H.value&&!m.value&&(!z.value||_.value<F.value)?`${F.value}px`:0,paddingBottom:`${oe&&fe?Ue:0}px`}}),ae=w(()=>{const{zIndex:A}=t,oe=X.value?1:0;return A+oe}),se=w(()=>{const A=ge.value;return{height:H.value?"0":`${F.value}px`,left:X.value?0:P.value.sidebarAndExtraWidth,position:A?"fixed":"static",top:m.value||H.value?`-${F.value}px`:0,width:P.value.width,"z-index":ae.value}}),ye=w(()=>{const{isMobile:A,zIndex:oe}=t;let fe=A||ee.value?1:-1;return X.value&&(fe+=1),oe+fe}),Ye=w(()=>t.footerFixed?P.value.width:"100%"),Ne=w(()=>({zIndex:t.zIndex})),Xe=w(()=>t.isMobile||t.headerToggleSidebarButton&&ee.value&&!Q.value&&!X.value&&!t.isMobile),Fe=w(()=>!ee.value||X.value||t.isMobile);we(()=>t.isMobile,A=>{A&&(n.value=!0)},{immediate:!0}),we([()=>F.value,()=>H.value],([A])=>{U(H.value?0:A)},{immediate:!0}),we(()=>t.footerHeight,A=>{I(A)},{immediate:!0});{const A=()=>{N.value>F.value?m.value=!0:m.value=!1};we([()=>t.headerMode,()=>N.value],()=>{if(!z.value||X.value||H.value){t.headerMode!=="auto-scroll"&&(m.value=!1);return}m.value=!0,A()},{immediate:!0})}{const A=Ht((oe,fe,Ue)=>{if(_.value<F.value){m.value=!1;return}if(Ue){m.value=!1;return}oe?m.value=!1:fe&&(m.value=!0)},300);we(()=>_.value,()=>{t.headerMode!=="auto-scroll"||X.value||H.value||V.value&&A(v.top,v.bottom,g.top)})}function Je(){n.value=!0}function He(){t.isMobile?n.value=!1:l("toggleSidebar")}return(A,oe)=>(i(),x("div",Nd,[re.value?(i(),y(e(Dd),{key:0,collapse:n.value,"onUpdate:collapse":oe[0]||(oe[0]=fe=>n.value=fe),"expand-on-hover":u.value,"onUpdate:expandOnHover":oe[1]||(oe[1]=fe=>u.value=fe),"expand-on-hovering":h.value,"onUpdate:expandOnHovering":oe[2]||(oe[2]=fe=>h.value=fe),"extra-collapse":r.value,"onUpdate:extraCollapse":oe[3]||(oe[3]=fe=>r.value=fe),"extra-visible":s.value,"onUpdate:extraVisible":oe[4]||(oe[4]=fe=>s.value=fe),"collapse-width":j.value,"dom-visible":!A.isMobile,"extra-width":W.value,"fixed-extra":u.value,"header-height":e(X)?0:A.headerHeight,"is-sidebar-mixed":e(Q),"margin-top":ve.value,"mixed-width":A.sidebarMixedWidth,show:he.value,theme:A.sidebarTheme,width:be.value,"z-index":ye.value,onLeave:oe[5]||(oe[5]=()=>l("sideMouseLeave"))},kt({extra:c(()=>[B(A.$slots,"side-extra")]),"extra-title":c(()=>[B(A.$slots,"side-extra-title")]),default:c(()=>[e(Q)?B(A.$slots,"mixed-menu",{key:0}):B(A.$slots,"menu",{key:1})]),_:2},[ee.value&&!e(X)?{name:"logo",fn:c(()=>[B(A.$slots,"logo")]),key:"0"}:void 0]),1032,["collapse","expand-on-hover","expand-on-hovering","extra-collapse","extra-visible","collapse-width","dom-visible","extra-width","fixed-extra","header-height","is-sidebar-mixed","margin-top","mixed-width","show","theme","width","z-index"])):E("",!0),C("div",{ref_key:"contentRef",ref:p,class:"flex flex-1 flex-col overflow-hidden transition-all duration-300 ease-in"},[C("div",{class:O([[{"shadow-[0_16px_24px_hsl(var(--background))]":e(_)>20},e(is)],"overflow-hidden transition-all duration-200"]),style:de(se.value)},[A.headerVisible?(i(),y(e(Od),{key:0,"full-width":!ee.value,height:A.headerHeight,"is-mobile":A.isMobile,show:!e(H)&&!A.headerHidden,"sidebar-width":A.sidebarWidth,theme:A.headerTheme,width:P.value.width,"z-index":ae.value},kt({"toggle-button":c(()=>[Xe.value?(i(),y(e(je),{key:0,class:"my-0 ml-2 mr-1 rounded-md",onClick:He},{default:c(()=>[f(e(vs),{class:"size-4"})]),_:1})):E("",!0)]),default:c(()=>[B(A.$slots,"header")]),_:2},[Fe.value?{name:"logo",fn:c(()=>[B(A.$slots,"logo")]),key:"0"}:void 0]),1032,["full-width","height","is-mobile","show","sidebar-width","theme","width","z-index"])):E("",!0),A.tabbarEnable?(i(),y(e(Rd),{key:1,height:A.tabbarHeight,style:de(G.value)},{default:c(()=>[B(A.$slots,"tabbar")]),_:3},8,["height","style"])):E("",!0)],6),f(e(Ad),{"content-compact":A.contentCompact,"content-compact-width":A.contentCompactWidth,padding:A.contentPadding,"padding-bottom":A.contentPaddingBottom,"padding-left":A.contentPaddingLeft,"padding-right":A.contentPaddingRight,"padding-top":A.contentPaddingTop,style:de(te.value),class:"transition-[margin-top] duration-200"},{overlay:c(()=>[B(A.$slots,"content-overlay")]),default:c(()=>[B(A.$slots,"content")]),_:3},8,["content-compact","content-compact-width","padding","padding-bottom","padding-left","padding-right","padding-top","style"]),A.footerEnable?(i(),y(e(Hd),{key:0,fixed:A.footerFixed,height:A.footerHeight,show:!e(H),width:Ye.value,"z-index":A.zIndex},{default:c(()=>[B(A.$slots,"footer")]),_:3},8,["fixed","height","show","width","z-index"])):E("",!0)],512),B(A.$slots,"extra"),Me.value?(i(),x("div",{key:1,style:de(Ne.value),class:"bg-overlay fixed left-0 top-0 h-full w-full transition-[background-color] duration-200",onClick:Je},null,4)):E("",!0)]))}}),Kd={key:0,class:"relative size-full"},jd=["src","onLoad"],Gd=T({name:"IFrameRouterView",__name:"iframe-router-view",setup(o){const a=q([]),t=st(),l=We(),n=w(()=>D.tabbar.enable),s=w(()=>n.value?t.getTabs.filter(g=>{var v;return!!((v=g.meta)!=null&&v.iframeSrc)}):l.meta.iframeSrc?[l]:[]),r=w(()=>new Set(s.value.map(g=>g.name))),u=w(()=>s.value.length>0);function d(g){return g.name===l.name}function h(g){const{meta:v,name:V}=g;return!V||!t.renderRouteView?!1:n.value?!(v!=null&&v.keepAlive)&&r.value.has(V)&&V!==l.name?!1:t.getTabs.some(_=>_.name===V):d(g)}function m(g){a.value[g]=!1}function p(g){const v=a.value[g];return v===void 0?!0:v}return(g,v)=>u.value?(i(!0),x(Y,{key:0},ce(s.value,(V,_)=>(i(),x(Y,{key:V.fullPath},[h(V)?$e((i(),x("div",Kd,[f(e(lo),{spinning:p(_)},null,8,["spinning"]),C("iframe",{src:V.meta.iframeSrc,class:"size-full",onLoad:U=>m(_)},null,40,jd)],512)),[[Be,d(V)]]):E("",!0)],64))),128)):E("",!0)}}),qd={class:"relative h-full"},Yd=T({name:"LayoutContent",__name:"content",setup(o){const a=st(),{keepAlive:t}=Qe(),{getCachedTabs:l,getExcludeCachedTabs:n,renderRouteView:s}=Va(a);function r(d){const{tabbar:h,transition:m}=D,p=m.name;if(!(!p||!m.enable))return!h.enable||!t,p}function u(d,h){var g;if(!d){console.error("Component view not found，please check the route configuration");return}const m=h.name;if(!m)return d;const p=(g=d==null?void 0:d.type)==null?void 0:g.name;return p||p===m||(d.type||(d.type={}),d.type.name=m),d}return(d,h)=>(i(),x("div",qd,[f(e(Gd)),f(e(Il),null,{default:c(({Component:m,route:p})=>[f(Ze,{name:r(p),appear:"",mode:"out-in"},{default:c(()=>[e(t)?(i(),y(ds,{key:0,exclude:e(n),include:e(l)},[e(s)?$e((i(),y(Le(u(m,p)),{key:p.fullPath})),[[Be,!p.meta.iframeSrc]]):E("",!0)],1032,["exclude","include"])):e(s)?(i(),y(Le(m),{key:p.fullPath})):E("",!0)]),_:2},1032,["name"])]),_:1})]))}});function Xd(){const o=q(!1),a=q(0),t=et(),l=500,n=w(()=>D.transition.loading),s=()=>{if(!n.value)return;const r=performance.now()-a.value;r<l?setTimeout(()=>{o.value=!1},l-r):o.value=!1};return t.beforeEach(r=>(r.meta.loaded||!n.value||r.meta.iframeSrc||(a.value=performance.now(),o.value=!0),!0)),t.afterEach(r=>(r.meta.loaded||!n.value||r.meta.iframeSrc||s(),!0)),{spinning:o}}const Jd=T({name:"LayoutContentSpinner",__name:"content-spinner",setup(o){const{spinning:a}=Xd();return(t,l)=>(i(),y(e(lo),{spinning:e(a)},null,8,["spinning"]))}}),Zd={class:"flex-center text-muted-foreground relative h-full w-full text-xs"},Qd=T({name:"LayoutFooter",__name:"footer",setup(o){return(a,t)=>(i(),x("div",Zd,[B(a.$slots,"default")]))}}),eu={class:"flex-center hidden lg:block"},tu={class:"flex h-full min-w-0 flex-1 items-center"},au={class:"flex h-full min-w-0 flex-shrink-0 items-center"},De=50,ou=T({name:"LayoutHeader",__name:"header",props:{theme:{default:"light"}},emits:["clearPreferencesAndLogout"],setup(o,{emit:a}){const t=a,l=Ct(),{globalSearchShortcutKey:n,preferencesButtonPosition:s}=Qe(),r=Ae(),{refresh:u}=Ya(),d=w(()=>{const p=[{index:De+100,name:"user-dropdown"}];return D.widget.globalSearch&&p.push({index:De,name:"global-search"}),s.value.header&&p.push({index:De+10,name:"preferences"}),D.widget.themeToggle&&p.push({index:De+20,name:"theme-toggle"}),D.widget.languageToggle&&p.push({index:De+30,name:"language-toggle"}),D.widget.fullscreen&&p.push({index:De+40,name:"fullscreen"}),D.widget.notification&&p.push({index:De+50,name:"notification"}),Object.keys(r).forEach(g=>{const v=g.split("-");g.startsWith("header-right")&&p.push({index:Number(v[2]),name:g})}),p.sort((g,v)=>g.index-v.index)}),h=w(()=>{const p=[];return D.widget.refresh&&p.push({index:0,name:"refresh"}),Object.keys(r).forEach(g=>{const v=g.split("-");g.startsWith("header-left")&&p.push({index:Number(v[2]),name:g})}),p.sort((g,v)=>g.index-v.index)});function m(){t("clearPreferencesAndLogout")}return(p,g)=>(i(),x(Y,null,[(i(!0),x(Y,null,ce(h.value.filter(v=>v.index<De),v=>B(p.$slots,v.name,{key:v.name},()=>[v.name==="refresh"?(i(),y(e(je),{key:0,class:"my-0 mr-1 rounded-md",onClick:e(u)},{default:c(()=>[f(e(Nt),{class:"size-4"})]),_:1},8,["onClick"])):E("",!0)])),128)),C("div",eu,[B(p.$slots,"breadcrumb")]),(i(!0),x(Y,null,ce(h.value.filter(v=>v.index>De),v=>B(p.$slots,v.name,{key:v.name})),128)),C("div",tu,[B(p.$slots,"menu")]),C("div",au,[(i(!0),x(Y,null,ce(d.value,v=>B(p.$slots,v.name,{key:v.name},()=>[v.name==="global-search"?(i(),y(e(zr),{key:0,"enable-shortcut-key":e(n),menus:e(l).accessMenus,class:"mr-1 sm:mr-4"},null,8,["enable-shortcut-key","menus"])):v.name==="preferences"?(i(),y(e(Pd),{key:1,class:"mr-1",onClearPreferencesAndLogout:m})):v.name==="theme-toggle"?(i(),y(e(ks),{key:2,class:"mr-1 mt-[2px]"})):v.name==="language-toggle"?(i(),y(e(_s),{key:3,class:"mr-1"})):v.name==="fullscreen"?(i(),y(e(Xn),{key:4,class:"mr-1"})):E("",!0)])),128))])],64))}}),lu=["onClick","onMouseenter"],su=T({name:"NormalMenu",__name:"normal-menu",props:{activePath:{default:""},collapse:{type:Boolean,default:!1},menus:{default:()=>[]},rounded:{type:Boolean},theme:{default:"dark"}},emits:["enter","select"],setup(o,{emit:a}){const t=o,l=a,{b:n,e:s,is:r}=Ge("normal-menu");function u(d){return t.activePath===d.path&&d.activeIcon||d.icon}return(d,h)=>(i(),x("ul",{class:O([[d.theme,e(n)(),e(r)("collapse",d.collapse),e(r)(d.theme,!0),e(r)("rounded",d.rounded)],"relative"])},[(i(!0),x(Y,null,ce(d.menus,m=>(i(),x("li",{key:m.path,class:O([e(s)("item"),e(r)("active",d.activePath===m.path)]),onClick:()=>l("select",m),onMouseenter:()=>l("enter",m)},[f(e(Ie),{class:O(e(s)("icon")),icon:u(m),fallback:""},null,8,["class","icon"]),C("span",{class:O([e(s)("name"),"truncate"])},S(m.name),3)],42,lu))),128))],2))}}),nu=Ee(su,[["__scopeId","data-v-3ebda870"]]);function uo(o,a){var l,n;let t=o.parent;for(;t&&!a.includes((n=(l=t==null?void 0:t.type)==null?void 0:l.name)!=null?n:"");)t=t.parent;return t}const yt=o=>{const a=Array.isArray(o)?o:[o],t=[];return a.forEach(l=>{var n;Array.isArray(l)?t.push(...yt(l)):ha(l)&&Array.isArray(l.children)?t.push(...yt(l.children)):(t.push(l),ha(l)&&((n=l.component)!=null&&n.subTree)&&t.push(...yt(l.component.subTree)))}),t};function co(){const o=Bt();if(!o)throw new Error("instance is required");const a=w(()=>{var s;let l=o.parent;const n=[o.props.path];for(;(l==null?void 0:l.type.name)!=="Menu";)l!=null&&l.props.path&&n.unshift(l.props.path),l=(s=l==null?void 0:l.parent)!=null?s:null;return n});return{parentMenu:w(()=>uo(o,["Menu","SubMenu"])),parentPaths:a}}function po(o){return w(()=>{var t;return{"--menu-level":o?(t=o==null?void 0:o.level)!=null?t:1:0}})}const fo=Symbol("menuContext");function ru(o){Mt(fo,o)}function mo(o){const a=Bt();Mt(`subMenu:${a==null?void 0:a.uid}`,o)}function Gt(){if(!Bt())throw new Error("instance is required");return Tt(fo)}function ho(){const o=Bt();if(!o)throw new Error("instance is required");const a=uo(o,["Menu","SubMenu"]);return Tt(`subMenu:${a==null?void 0:a.uid}`)}const iu=T({name:"CollapseTransition",__name:"collapse-transition",setup(o){const a=l=>{l.style.maxHeight="",l.style.overflow=l.dataset.oldOverflow,l.style.paddingTop=l.dataset.oldPaddingTop,l.style.paddingBottom=l.dataset.oldPaddingBottom},t={afterEnter(l){l.style.maxHeight="",l.style.overflow=l.dataset.oldOverflow},afterLeave(l){a(l)},beforeEnter(l){l.dataset||(l.dataset={}),l.dataset.oldPaddingTop=l.style.paddingTop,l.dataset.oldMarginTop=l.style.marginTop,l.dataset.oldPaddingBottom=l.style.paddingBottom,l.dataset.oldMarginBottom=l.style.marginBottom,l.style.height&&(l.dataset.elExistsHeight=l.style.height),l.style.maxHeight=0,l.style.paddingTop=0,l.style.marginTop=0,l.style.paddingBottom=0,l.style.marginBottom=0},beforeLeave(l){l.dataset||(l.dataset={}),l.dataset.oldPaddingTop=l.style.paddingTop,l.dataset.oldMarginTop=l.style.marginTop,l.dataset.oldPaddingBottom=l.style.paddingBottom,l.dataset.oldMarginBottom=l.style.marginBottom,l.dataset.oldOverflow=l.style.overflow,l.style.maxHeight=`${l.scrollHeight}px`,l.style.overflow="hidden"},enter(l){requestAnimationFrame(()=>{l.dataset.oldOverflow=l.style.overflow,l.dataset.elExistsHeight?l.style.maxHeight=l.dataset.elExistsHeight:l.scrollHeight===0?l.style.maxHeight=0:l.style.maxHeight=`${l.scrollHeight}px`,l.style.paddingTop=l.dataset.oldPaddingTop,l.style.paddingBottom=l.dataset.oldPaddingBottom,l.style.marginTop=l.dataset.oldMarginTop,l.style.marginBottom=l.dataset.oldMarginBottom,l.style.overflow="hidden"})},enterCancelled(l){a(l)},leave(l){l.scrollHeight!==0&&(l.style.maxHeight=0,l.style.paddingTop=0,l.style.paddingBottom=0,l.style.marginTop=0,l.style.marginBottom=0)},leaveCancelled(l){a(l)}};return(l,n)=>(i(),y(Ze,ie({name:"collapse-transition"},Wa(t)),{default:c(()=>[B(l.$slots,"default")]),_:3},16))}}),Ca=T({name:"SubMenuContent",__name:"sub-menu-content",props:{isMenuMore:{type:Boolean,default:!1},isTopLevelMenuSubmenu:{type:Boolean},level:{default:0},activeIcon:{},disabled:{type:Boolean},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(o){const a=o,t=Gt(),{b:l,e:n,is:s}=Ge("sub-menu-content"),r=Ge("menu"),u=w(()=>t==null?void 0:t.openedMenus.includes(a.path)),d=w(()=>t.props.collapse),h=w(()=>a.level===1),m=w(()=>t.props.collapseShowTitle&&h.value&&d.value),p=w(()=>t==null?void 0:t.props.mode),g=w(()=>p.value==="horizontal"||!(h.value&&d.value)),v=w(()=>p.value==="vertical"&&h.value&&d.value&&!m.value),V=w(()=>p.value==="horizontal"&&!h.value||p.value==="vertical"&&d.value?jt:At),_=w(()=>u.value?{transform:"rotate(180deg)"}:{});return(U,I)=>(i(),x("div",{class:O([e(l)(),e(s)("collapse-show-title",m.value),e(s)("more",U.isMenuMore)])},[B(U.$slots,"default"),U.isMenuMore?E("",!0):(i(),y(e(Ie),{key:0,class:O(e(r).e("icon")),icon:U.icon,fallback:""},null,8,["class","icon"])),v.value?E("",!0):(i(),x("div",{key:1,class:O([e(n)("title")])},[B(U.$slots,"title")],2)),U.isMenuMore?E("",!0):$e((i(),y(Le(V.value),{key:2,class:O([[e(n)("icon-arrow")],"size-4"]),style:de(_.value)},null,8,["class","style"])),[[Be,g.value]])],2))}}),bo=T({name:"SubMenu",__name:"sub-menu",props:{isSubMenuMore:{type:Boolean,default:!1},activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},setup(o){var be;const a=o,{parentMenu:t,parentPaths:l}=co(),{b:n,is:s}=Ge("sub-menu"),r=Ge("menu"),u=Gt(),d=ho(),h=po(d),m=q(!1),p=q({}),g=q({}),v=q(null);mo({addSubMenu:X,handleMouseleave:j,level:((be=d==null?void 0:d.level)!=null?be:0)+1,mouseInChild:m,removeSubMenu:Q});const V=w(()=>u==null?void 0:u.openedMenus.includes(a.path)),_=w(()=>{var W;return((W=t.value)==null?void 0:W.type.name)==="Menu"}),U=w(()=>{var W;return(W=u==null?void 0:u.props.mode)!=null?W:"vertical"}),I=w(()=>u==null?void 0:u.props.rounded),N=w(()=>{var W;return(W=d==null?void 0:d.level)!=null?W:0}),Z=w(()=>N.value===1),H=w(()=>{const W=U.value==="horizontal",ee=W&&Z.value?"bottom":"right";return{collisionPadding:{top:20},side:ee,sideOffset:W?5:10}}),R=w(()=>{let W=!1;return Object.values(p.value).forEach(ee=>{ee.active&&(W=!0)}),Object.values(g.value).forEach(ee=>{ee.active&&(W=!0)}),W});function X(W){g.value[W.path]=W}function Q(W){Reflect.deleteProperty(g.value,W.path)}function z(){const W=u==null?void 0:u.props.mode;a.disabled||u!=null&&u.props.collapse&&W==="vertical"||W==="horizontal"||u==null||u.handleSubMenuClick({active:R.value,parentPaths:l.value,path:a.path})}function F(W,ee=300){var ge,he;if(W.type!=="focus"){if(!(u!=null&&u.props.collapse)&&(u==null?void 0:u.props.mode)==="vertical"||a.disabled){d&&(d.mouseInChild.value=!0);return}d&&(d.mouseInChild.value=!0),v.value&&window.clearTimeout(v.value),v.value=setTimeout(()=>{u==null||u.openMenu(a.path,l.value)},ee),(he=(ge=t.value)==null?void 0:ge.vnode.el)==null||he.dispatchEvent(new MouseEvent("mouseenter"))}}function j(W=!1){var ee;if(!(u!=null&&u.props.collapse)&&(u==null?void 0:u.props.mode)==="vertical"&&d){d.mouseInChild.value=!1;return}v.value&&window.clearTimeout(v.value),d&&(d.mouseInChild.value=!1),v.value=setTimeout(()=>{!m.value&&(u==null||u.closeMenu(a.path,l.value))},300),W&&((ee=d==null?void 0:d.handleMouseleave)==null||ee.call(d,!0))}const re=w(()=>R.value&&a.activeIcon||a.icon),ve=lt({active:R,parentPaths:l,path:a.path});return qe(()=>{var W,ee;(W=d==null?void 0:d.addSubMenu)==null||W.call(d,ve),(ee=u==null?void 0:u.addSubMenu)==null||ee.call(u,ve)}),Fa(()=>{var W,ee;(W=d==null?void 0:d.removeSubMenu)==null||W.call(d,ve),(ee=u==null?void 0:u.removeSubMenu)==null||ee.call(u,ve)}),(W,ee)=>(i(),x("li",{class:O([e(n)(),e(s)("opened",V.value),e(s)("active",R.value),e(s)("disabled",W.disabled)]),onFocus:F,onMouseenter:F,onMouseleave:ee[3]||(ee[3]=()=>j())},[e(u).isMenuPopup?(i(),y(e(Zn),{key:0,"content-class":[e(u).theme,e(r).e("popup-container"),e(s)(e(u).theme,!0),V.value?"":"hidden"],"content-props":H.value,open:!0,"open-delay":0},{trigger:c(()=>[f(Ca,{class:O(e(s)("active",R.value)),icon:re.value,"is-menu-more":W.isSubMenuMore,"is-top-level-menu-submenu":_.value,level:N.value,path:W.path,onClick:Ce(z,["stop"])},{title:c(()=>[B(W.$slots,"title")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"])]),default:c(()=>[C("div",{class:O([e(r).is(U.value,!0),e(r).e("popup")]),onFocus:ee[0]||(ee[0]=ge=>F(ge,100)),onMouseenter:ee[1]||(ee[1]=ge=>F(ge,100)),onMouseleave:ee[2]||(ee[2]=()=>j(!0))},[C("ul",{class:O([e(r).b(),e(s)("rounded",I.value)]),style:de(e(h))},[B(W.$slots,"default")],6)],34)]),_:3},8,["content-class","content-props"])):(i(),x(Y,{key:1},[f(Ca,{class:O(e(s)("active",R.value)),icon:re.value,"is-menu-more":W.isSubMenuMore,"is-top-level-menu-submenu":_.value,level:N.value,path:W.path,onClick:Ce(z,["stop"])},{title:c(()=>[B(W.$slots,"title")]),default:c(()=>[B(W.$slots,"content")]),_:3},8,["class","icon","is-menu-more","is-top-level-menu-submenu","level","path"]),f(iu,null,{default:c(()=>[$e(C("ul",{class:O([e(r).b(),e(s)("rounded",I.value)]),style:de(e(h))},[B(W.$slots,"default")],6),[[Be,V.value]])]),_:3})],64))],34))}}),du=T({name:"Menu",__name:"menu",props:{accordion:{type:Boolean,default:!0},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{default:"vertical"},rounded:{type:Boolean,default:!0},theme:{default:"dark"}},emits:["close","open","select"],setup(o,{emit:a}){const t=o,l=a,{b:n,is:s}=Ge("menu"),r=po(),u=Ae(),d=q(),h=q(-1),m=q(t.defaultOpeneds&&!t.collapse?[...t.defaultOpeneds]:[]),p=q(t.defaultActive),g=q({}),v=q({}),V=q(!1),_=w(()=>t.mode==="horizontal"||t.mode==="vertical"&&t.collapse),U=w(()=>{var se,ye;const P=(ye=(se=u.default)==null?void 0:se.call(u))!=null?ye:[],G=yt(P),te=h.value===-1?G:G.slice(0,h.value),ae=h.value===-1?[]:G.slice(h.value);return{showSlotMore:ae.length>0,slotDefault:te,slotMore:ae}});we(()=>t.collapse,P=>{P&&(m.value=[])}),we(g.value,z),we(()=>t.defaultActive,(P="")=>{g.value[P]||(p.value=""),F(P)});let I;Na(()=>{t.mode==="horizontal"?I=cs(d,X).stop:I==null||I()}),ru(lt({activePath:p,addMenuItem:ee,addSubMenu:ge,closeMenu:be,handleMenuItemClick:j,handleSubMenuClick:re,isMenuPopup:_,openedMenus:m,openMenu:W,props:t,removeMenuItem:Me,removeSubMenu:he,subMenus:v,theme:us(t,"theme"),items:g})),mo({addSubMenu:ge,level:1,mouseInChild:V,removeSubMenu:he});function N(P){const G=getComputedStyle(P),te=Number.parseInt(G.marginLeft,10),ae=Number.parseInt(G.marginRight,10);return P.offsetWidth+te+ae||0}function Z(){var Xe,Fe,Je;if(!d.value)return-1;const P=[...(Fe=(Xe=d.value)==null?void 0:Xe.childNodes)!=null?Fe:[]].filter(He=>He.nodeName!=="#comment"&&(He.nodeName!=="#text"||He.nodeValue)),G=46,te=getComputedStyle(d==null?void 0:d.value),ae=Number.parseInt(te.paddingLeft,10),se=Number.parseInt(te.paddingRight,10),ye=((Je=d.value)==null?void 0:Je.clientWidth)-ae-se;let Ye=0,Ne=0;return P.forEach((He,A)=>{Ye+=N(He),Ye<=ye-G&&(Ne=A+1)}),Ne===P.length?-1:Ne}function H(P,G=33.34){let te;return()=>{te&&clearTimeout(te),te=setTimeout(()=>{P()},G)}}let R=!0;function X(){if(h.value===Z())return;const P=()=>{h.value=-1,Re(()=>{h.value=Z()})};P(),R?P():H(P)(),R=!1}function Q(){const P=p.value&&g.value[p.value];return!P||t.mode==="horizontal"||t.collapse?[]:P.parentPaths}function z(){Q().forEach(G=>{const te=v.value[G];te&&W(G,te.parentPaths)})}function F(P){const G=g.value,te=G[P]||p.value&&G[p.value]||G[t.defaultActive||""];p.value=te?te.path:P}function j(P){const{collapse:G,mode:te}=t;(te==="horizontal"||G)&&(m.value=[]);const{parentPaths:ae,path:se}=P;!se||!ae||(Ot(se)||(p.value=se),l("select",se,ae))}function re({parentPaths:P,path:G}){m.value.includes(G)?be(G,P):W(G,P)}function ve(P){const G=m.value.indexOf(P);G!==-1&&m.value.splice(G,1)}function be(P,G){var te,ae;t.accordion&&(m.value=(ae=(te=v.value[P])==null?void 0:te.parentPaths)!=null?ae:[]),ve(P),l("close",P,G)}function W(P,G){if(!m.value.includes(P)){if(t.accordion){const te=Q();te.includes(P)&&(G=te),m.value=m.value.filter(ae=>G.includes(ae))}m.value.push(P),l("open",P,G)}}function ee(P){g.value[P.path]=P}function ge(P){v.value[P.path]=P}function he(P){Reflect.deleteProperty(v.value,P.path)}function Me(P){Reflect.deleteProperty(g.value,P.path)}return(P,G)=>(i(),x("ul",{ref_key:"menu",ref:d,class:O([P.theme,e(n)(),e(s)(P.mode,!0),e(s)(P.theme,!0),e(s)("rounded",P.rounded),e(s)("collapse",P.collapse)]),style:de(e(r)),role:"menu"},[P.mode==="horizontal"&&U.value.showSlotMore?(i(),x(Y,{key:0},[(i(!0),x(Y,null,ce(U.value.slotDefault,te=>(i(),y(Le(te),{key:te.key}))),128)),f(bo,{"is-sub-menu-more":"",path:"sub-menu-more"},{title:c(()=>[f(e(Ds),{class:"size-4"})]),default:c(()=>[(i(!0),x(Y,null,ce(U.value.slotMore,te=>(i(),y(Le(te),{key:te.key}))),128))]),_:1})],64)):B(P.$slots,"default",{key:1})],6))}}),uu={class:"relative mr-1 flex size-1.5"},cu=T({__name:"menu-badge-dot",props:{dotClass:{default:""},dotStyle:{default:()=>({})}},setup(o){return(a,t)=>(i(),x("span",uu,[C("span",{class:O([a.dotClass,"absolute inline-flex h-full w-full animate-ping rounded-full opacity-75"]),style:de(a.dotStyle)},null,6),C("span",{class:O([a.dotClass,"relative inline-flex size-1.5 rounded-full"]),style:de(a.dotStyle)},null,6)]))}}),vo=T({__name:"menu-badge",props:{hasChildren:{type:Boolean},badge:{},badgeType:{},badgeVariants:{}},setup(o){const a=o,t={default:"bg-green-500",destructive:"bg-destructive",primary:"bg-primary",success:"bg-green-500",warning:"bg-yellow-500"},l=w(()=>a.badgeType==="dot"),n=w(()=>{const{badgeVariants:r}=a;return r?t[r]||r:t.default}),s=w(()=>n.value&&ps(n.value)?{backgroundColor:n.value}:{});return(r,u)=>l.value||r.badge?(i(),x("span",{key:0,class:O([r.$attrs.class,"absolute"])},[l.value?(i(),y(cu,{key:0,"dot-class":n.value,"dot-style":s.value},null,8,["dot-class","dot-style"])):(i(),x("div",{key:1,class:O([n.value,"text-primary-foreground flex-center rounded-xl px-1.5 py-0.5 text-[10px]"]),style:de(s.value)},S(r.badge),7))],2)):E("",!0)}}),pu=T({name:"MenuItem",__name:"menu-item",props:{activeIcon:{},disabled:{type:Boolean,default:!1},icon:{},path:{},badge:{},badgeType:{},badgeVariants:{}},emits:["click"],setup(o,{emit:a}){const t=o,l=a,n=Ae(),{b:s,e:r,is:u}=Ge("menu-item"),d=Ge("menu"),h=Gt(),m=ho(),{parentMenu:p,parentPaths:g}=co(),v=w(()=>t.path===(h==null?void 0:h.activePath)),V=w(()=>v.value&&t.activeIcon||t.icon),_=w(()=>{var H;return((H=p.value)==null?void 0:H.type.name)==="Menu"}),U=w(()=>{var H;return((H=h.props)==null?void 0:H.collapseShowTitle)&&_.value&&h.props.collapse}),I=w(()=>{var H;return h.props.mode==="vertical"&&_.value&&((H=h.props)==null?void 0:H.collapse)&&n.title}),N=lt({active:v,parentPaths:g.value,path:t.path||""});function Z(){var H;t.disabled||((H=h==null?void 0:h.handleMenuItemClick)==null||H.call(h,{parentPaths:g.value,path:t.path}),l("click",N))}return qe(()=>{var H,R;(H=m==null?void 0:m.addSubMenu)==null||H.call(m,N),(R=h==null?void 0:h.addMenuItem)==null||R.call(h,N)}),Fa(()=>{var H,R;(H=m==null?void 0:m.removeSubMenu)==null||H.call(m,N),(R=h==null?void 0:h.removeMenuItem)==null||R.call(h,N)}),(H,R)=>(i(),x("li",{class:O([e(h).theme,e(s)(),e(u)("active",v.value),e(u)("disabled",H.disabled),e(u)("collapse-show-title",U.value)]),role:"menuitem",onClick:Ce(Z,["stop"])},[I.value?(i(),y(e(ot),{key:0,"content-class":[e(h).theme],side:"right"},{trigger:c(()=>[C("div",{class:O([e(d).be("tooltip","trigger")])},[f(e(Ie),{class:O(e(d).e("icon")),icon:V.value,fallback:""},null,8,["class","icon"]),B(H.$slots,"default"),U.value?(i(),x("span",{key:0,class:O(e(d).e("name"))},[B(H.$slots,"title")],2)):E("",!0)],2)]),default:c(()=>[B(H.$slots,"title")]),_:3},8,["content-class"])):E("",!0),$e(C("div",{class:O([e(r)("content")])},[e(h).props.mode!=="horizontal"?(i(),y(e(vo),ie({key:0,class:"right-2"},t),null,16)):E("",!0),f(e(Ie),{class:O(e(d).e("icon")),icon:V.value},null,8,["class","icon"]),B(H.$slots,"default"),B(H.$slots,"title")],2),[[Be,!I.value]])],2))}}),go=T({name:"SubMenuUi",__name:"sub-menu",props:{menu:{}},setup(o){const a=o,t=w(()=>{const{menu:l}=a;return Reflect.has(l,"children")&&!!l.children&&l.children.length>0});return(l,n)=>t.value?(i(),y(e(bo),{key:`${l.menu.path}_sub`,"active-icon":l.menu.activeIcon,icon:l.menu.icon,path:l.menu.path},{content:c(()=>[f(e(vo),{badge:l.menu.badge,"badge-type":l.menu.badgeType,"badge-variants":l.menu.badgeVariants,class:"right-6"},null,8,["badge","badge-type","badge-variants"])]),title:c(()=>[C("span",null,S(l.menu.name),1)]),default:c(()=>[(i(!0),x(Y,null,ce(l.menu.children||[],s=>(i(),y(go,{key:s.path,menu:s},null,8,["menu"]))),128))]),_:1},8,["active-icon","icon","path"])):(i(),y(e(pu),{key:l.menu.path,"active-icon":l.menu.activeIcon,badge:l.menu.badge,"badge-type":l.menu.badgeType,"badge-variants":l.menu.badgeVariants,icon:l.menu.icon,path:l.menu.path},{title:c(()=>[C("span",null,S(l.menu.name),1)]),_:1},8,["active-icon","badge","badge-type","badge-variants","icon","path"]))}}),yo=T({name:"MenuView",__name:"menu",props:{menus:{},accordion:{type:Boolean},collapse:{type:Boolean,default:!1},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},theme:{}},setup(o){const t=tt(o);return(l,n)=>(i(),y(e(du),_e(Ve(e(t))),{default:c(()=>[(i(!0),x(Y,null,ce(l.menus,s=>(i(),y(go,{key:s.path,menu:s},null,8,["menu"]))),128))]),_:1},16))}});function qt(){const o=et(),a=o.getRoutes(),t=new Map;return a.forEach(n=>{t.set(n.path,n)}),{navigation:n=>K(null,null,function*(){var d;const s=t.get(n),{openInNewWindow:r=!1,query:u={}}=(d=s==null?void 0:s.meta)!=null?d:{};Ot(n)?fs(n,{target:"_blank"}):r?za(n):yield o.push({path:n,query:u})})}}const fu=T({__name:"extra-menu",props:{collapse:{type:Boolean},menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},theme:{}},setup(o){const a=We(),{navigation:t}=qt();function l(n){return K(this,null,function*(){yield t(n)})}return(n,s)=>{var r;return i(),y(e(yo),{accordion:n.accordion,collapse:n.collapse,"default-active":((r=e(a).meta)==null?void 0:r.activePath)||e(a).path,menus:n.menus,rounded:n.rounded,theme:n.theme,mode:"vertical",onSelect:l},null,8,["accordion","collapse","default-active","menus","rounded","theme"])}}}),Ta=T({__name:"menu",props:{menus:{default:()=>[]},accordion:{type:Boolean,default:!0},collapse:{type:Boolean},collapseShowTitle:{type:Boolean},defaultActive:{},defaultOpeneds:{},mode:{},rounded:{type:Boolean},theme:{}},emits:["select"],setup(o,{emit:a}){const t=o,l=a;function n(s){l("select",s,t.mode)}return(s,r)=>(i(),y(e(yo),{accordion:s.accordion,collapse:s.collapse,"collapse-show-title":s.collapseShowTitle,"default-active":s.defaultActive,menus:s.menus,mode:s.mode,rounded:s.rounded,theme:s.theme,onSelect:n},null,8,["accordion","collapse","collapse-show-title","default-active","menus","mode","rounded","theme"]))}}),mu=T({__name:"mixed-menu",props:{activePath:{},collapse:{type:Boolean},menus:{},rounded:{type:Boolean},theme:{}},emits:["defaultSelect","enter","select"],setup(o,{emit:a}){const t=o,l=a,n=We();return Ka(()=>{const s=Ft(t.menus||[],n.path);if(s){const r=(t.menus||[]).find(u=>{var d;return u.path===((d=s.parents)==null?void 0:d[0])});l("defaultSelect",s,r)}}),(s,r)=>(i(),y(e(nu),{"active-path":s.activePath,collapse:s.collapse,menus:s.menus,rounded:s.rounded,theme:s.theme,onEnter:r[0]||(r[0]=u=>l("enter",u)),onSelect:r[1]||(r[1]=u=>l("select",u))},null,8,["active-path","collapse","menus","rounded","theme"]))}});function hu(){const o=Ct(),{navigation:a}=qt(),t=w(()=>o.accessMenus),l=We(),n=q([]),s=q(!1),r=q(""),u=p=>K(null,null,function*(){var v,V,_;n.value=(v=p==null?void 0:p.children)!=null?v:[],r.value=(_=(V=p.parents)==null?void 0:V[0])!=null?_:p.path;const g=n.value.length>0;s.value=g,g||(yield a(p.path))}),d=(p,g)=>{var v,V,_;n.value=(v=g==null?void 0:g.children)!=null?v:[],r.value=(_=(V=p.parents)==null?void 0:V[0])!=null?_:p.path,D.sidebar.expandOnHover&&(s.value=n.value.length>0)},h=()=>{var V,_;if(D.sidebar.expandOnHover)return;s.value=!1;const{findMenu:p,rootMenu:g,rootMenuPath:v}=gt(t.value,l.path);r.value=(V=v!=null?v:p==null?void 0:p.path)!=null?V:"",n.value=(_=g==null?void 0:g.children)!=null?_:[]},m=p=>{var g,v,V;if(!D.sidebar.expandOnHover){const{findMenu:_}=gt(t.value,p.path);n.value=(g=_==null?void 0:_.children)!=null?g:[],r.value=(V=(v=p.parents)==null?void 0:v[0])!=null?V:p.path,s.value=n.value.length>0}};return we(()=>l.path,p=>{var U,I,N;const g=((U=l.meta)==null?void 0:U.activePath)||p,{findMenu:v,rootMenu:V,rootMenuPath:_}=gt(t.value,g);r.value=(I=_!=null?_:v==null?void 0:v.path)!=null?I:"",n.value=(N=V==null?void 0:V.children)!=null?N:[]},{immediate:!0}),{extraActiveMenu:r,extraMenus:n,handleDefaultSelect:d,handleMenuMouseEnter:m,handleMixedMenuSelect:u,handleSideMouseLeave:h,sidebarExtraVisible:s}}function bu(){const{navigation:o}=qt(),a=Ct(),t=We(),l=q([]),n=q(""),{isMixedNav:s}=Qe(),r=w(()=>D.navigation.split&&s.value),u=w(()=>{const _=D.sidebar.enable;return r.value?_&&l.value.length>0:_}),d=w(()=>a.accessMenus),h=w(()=>r.value?d.value.map(_=>ke(J({},_),{children:[]})):d.value),m=w(()=>r.value?l.value:d.value),p=w(()=>{var _,U;return(U=(_=t==null?void 0:t.meta)==null?void 0:_.activePath)!=null?U:t.path}),g=w(()=>r.value?n.value:t.path),v=(_,U)=>{var N,Z;if(!r.value||U==="vertical"){o(_);return}const I=d.value.find(H=>H.path===_);n.value=(N=I==null?void 0:I.path)!=null?N:"",l.value=(Z=I==null?void 0:I.children)!=null?Z:[],l.value.length===0&&o(_)};function V(_=t.path){var I,N;let{rootMenu:U}=gt(d.value,_);U||(U=d.value.find(Z=>Z.path===_)),n.value=(I=U==null?void 0:U.path)!=null?I:"",l.value=(N=U==null?void 0:U.children)!=null?N:[]}return we(()=>t.path,_=>{var I,N;const U=(N=(I=t==null?void 0:t.meta)==null?void 0:I.activePath)!=null?N:_;V(U)},{immediate:!0}),Ka(()=>{var _;V(((_=t.meta)==null?void 0:_.activePath)||t.path)}),{handleMenuSelect:v,headerActive:g,headerMenus:h,sidebarActive:p,sidebarMenus:m,sidebarVisible:u}}const vu={class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold"},gu=T({__name:"tool-more",props:{menus:{}},setup(o){return(a,t)=>(i(),y(e(Yn),{menus:a.menus,modal:!1},{default:c(()=>[C("div",vu,[f(e(At),{class:"size-4"})])]),_:1},8,["menus"]))}}),yu=T({__name:"tool-screen",props:{screen:{type:Boolean},screenModifiers:{}},emits:["update:screen"],setup(o){const a=k(o,"screen");function t(){a.value=!a.value}return(l,n)=>(i(),x("div",{class:"flex-center hover:bg-muted hover:text-foreground text-muted-foreground border-border h-full cursor-pointer border-l px-2 text-lg font-semibold",onClick:t},[a.value?(i(),y(e(Qa),{key:0,class:"size-4"})):(i(),y(e(Ja),{key:1,class:"size-4"}))]))}}),wu=["data-index","onClick"],xu={class:"relative flex size-full items-center"},ku={class:"absolute right-1.5 top-1/2 z-[3] translate-y-[-50%] overflow-hidden"},_u={class:"text-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mx-3 mr-4 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pr-3 transition-all duration-300"},Su={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},Cu=T({name:"VbenTabs",inheritAttrs:!1,__name:"tabs",props:xe({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{},maxWidth:{},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]}},{active:{},activeModifiers:{}}),emits:xe(["close","unpin"],["update:active"]),setup(o,{emit:a}){const t=o,l=a,n=k(o,"active"),s=w(()=>({brisk:{content:"h-full after:content-['']  after:absolute after:bottom-0 after:left-0 after:w-full after:h-[1.5px] after:bg-primary after:scale-x-0 after:transition-[transform] after:ease-out after:duration-300 hover:after:scale-x-100 after:origin-left [&.is-active]:after:scale-x-100 [&:not(:first-child)]:border-l last:border-r last:border-r border-border"},card:{content:"h-[calc(100%-6px)] rounded-md ml-2 border border-border  transition-all"},plain:{content:"h-full [&:not(:first-child)]:border-l last:border-r border-border"}})[t.styleType||"plain"]||{content:""}),r=w(()=>t.tabs.map(u=>{const{fullPath:d,meta:h,name:m,path:p}=u||{},{affixTab:g,icon:v,newTabTitle:V,tabClosable:_,title:U}=h||{};return{affixTab:!!g,closable:Reflect.has(h,"tabClosable")?!!_:!0,fullPath:d,icon:v,key:d||p,meta:h,name:m,path:p,title:V||U||m}}));return(u,d)=>(i(),x("div",{class:O([u.contentClass,"relative !flex h-full w-max items-center overflow-hidden pr-6"])},[f(St,{name:"slide-left"},{default:c(()=>[(i(!0),x(Y,null,ce(r.value,(h,m)=>(i(),x("div",{key:h.key,class:O([[{"is-active dark:bg-accent bg-primary/15":h.key===n.value,draggable:!h.affixTab,"affix-tab":h.affixTab},s.value.content],"tab-item [&:not(.is-active)]:hover:bg-accent translate-all group relative flex cursor-pointer select-none"]),"data-index":m,"data-tab-item":"true",onClick:p=>n.value=h.key},[f(e(oo),{"handler-data":h,menus:u.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[C("div",xu,[C("div",ku,[$e(f(e(mt),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground dark:group-[.is-active]:text-accent-foreground group-[.is-active]:text-primary size-3 cursor-pointer rounded-full transition-all",onClick:Ce(()=>l("close",h.key),["stop"])},null,8,["onClick"]),[[Be,!h.affixTab&&r.value.length>1&&h.closable]]),$e(f(e(Vt),{class:"hover:bg-accent hover:stroke-accent-foreground group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:Ce(()=>l("unpin",h),["stop"])},null,8,["onClick"]),[[Be,h.affixTab&&r.value.length>1&&h.closable]])]),C("div",_u,[u.showIcon?(i(),y(e(Ie),{key:0,icon:h.icon,class:"mr-2 flex size-4 items-center overflow-hidden",fallback:""},null,8,["icon"])):E("",!0),C("span",Su,S(h.title),1)])])]),_:2},1032,["handler-data","menus"])],10,wu))),128))]),_:1})],2))}}),Tu=["data-active-tab","data-index","onClick"],Mu={class:"relative size-full px-1"},$u={key:0,class:"tabs-chrome__divider bg-border absolute left-[var(--gap)] top-1/2 z-0 h-4 w-[1px] translate-y-[-50%] transition-all"},Bu={class:"tabs-chrome__extra absolute right-[var(--gap)] top-1/2 z-[3] size-4 translate-y-[-50%]"},Vu={class:"tabs-chrome__item-main group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground text-accent-foreground z-[2] mx-[calc(var(--gap)*2)] my-0 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pl-2 pr-4 duration-150"},Pu={class:"flex-1 overflow-hidden whitespace-nowrap text-sm"},Lu=T({name:"VbenTabsChrome",inheritAttrs:!1,__name:"tabs",props:xe({active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{type:Function,default:()=>[]},draggable:{type:Boolean},gap:{default:7},maxWidth:{},minWidth:{},showIcon:{type:Boolean},styleType:{},tabs:{default:()=>[]}},{active:{},activeModifiers:{}}),emits:xe(["close","unpin"],["update:active"]),setup(o,{emit:a}){const t=o,l=a,n=k(o,"active"),s=q(),r=q(),u=w(()=>{const{gap:h}=t;return{"--gap":`${h}px`}}),d=w(()=>t.tabs.map(h=>{const{fullPath:m,meta:p,name:g,path:v}=h||{},{affixTab:V,icon:_,newTabTitle:U,tabClosable:I,title:N}=p||{};return{affixTab:!!V,closable:Reflect.has(p,"tabClosable")?!!I:!0,fullPath:m,icon:_,key:m||v,meta:p,name:g,path:v,title:U||N||g}}));return(h,m)=>(i(),x("div",{ref_key:"contentRef",ref:s,class:O([h.contentClass,"tabs-chrome !flex h-full w-max overflow-y-hidden pr-6"]),style:de(u.value)},[f(St,{name:"slide-left"},{default:c(()=>[(i(!0),x(Y,null,ce(d.value,(p,g)=>(i(),x("div",{key:p.key,ref_for:!0,ref_key:"tabRef",ref:r,class:O([[{"is-active":p.key===n.value,draggable:!p.affixTab,"affix-tab":p.affixTab}],"tabs-chrome__item draggable translate-all group relative -mr-3 flex h-full select-none items-center"]),"data-active-tab":n.value,"data-index":g,"data-tab-item":"true",onClick:v=>n.value=p.key},[f(e(oo),{"handler-data":p,menus:h.contextMenus,modal:!1,"item-class":"pr-6"},{default:c(()=>[C("div",Mu,[g!==0&&p.key!==n.value?(i(),x("div",$u)):E("",!0),m[0]||(m[0]=C("div",{class:"tabs-chrome__background absolute z-[-1] size-full px-[calc(var(--gap)-1px)] py-0 transition-opacity duration-150"},[C("div",{class:"tabs-chrome__background-content group-[.is-active]:bg-primary/15 dark:group-[.is-active]:bg-accent h-full rounded-tl-[var(--gap)] rounded-tr-[var(--gap)] duration-150"}),C("svg",{class:"tabs-chrome__background-before group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 left-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[C("path",{d:"M 0 7 A 7 7 0 0 0 7 0 L 7 7 Z"})]),C("svg",{class:"tabs-chrome__background-after group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 right-[-1px] fill-transparent transition-all duration-150",height:"7",width:"7"},[C("path",{d:"M 0 0 A 7 7 0 0 0 7 7 L 0 7 Z"})])],-1)),C("div",Bu,[$e(f(e(mt),{class:"hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[2px] size-3 cursor-pointer rounded-full transition-all",onClick:Ce(()=>l("close",p.key),["stop"])},null,8,["onClick"]),[[Be,!p.affixTab&&d.value.length>1&&p.closable]]),$e(f(e(Vt),{class:"hover:text-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all",onClick:Ce(()=>l("unpin",p),["stop"])},null,8,["onClick"]),[[Be,p.affixTab&&d.value.length>1&&p.closable]])]),C("div",Vu,[h.showIcon?(i(),y(e(Ie),{key:0,icon:p.icon,class:"mr-1 flex size-4 items-center overflow-hidden"},null,8,["icon"])):E("",!0),C("span",Pu,S(p.title),1)])])]),_:2},1032,["handler-data","menus"])],10,Tu))),128))]),_:1})],6))}}),Eu=Ee(Lu,[["__scopeId","data-v-14123fd7"]]);function It(o){const a="group";return o.classList.contains(a)?o:o.closest(`.${a}`)}function zu(o,a){const t=q(null);function l(){return K(this,null,function*(){var d;yield Re();const s=(d=document.querySelectorAll(`.${o.contentClass}`))==null?void 0:d[0];if(!s){console.warn("Element not found for sortable initialization");return}const r=()=>K(null,null,function*(){var h;s.style.cursor="default",(h=s.querySelector(".draggable"))==null||h.classList.remove("dragging")}),{initializeSortable:u}=ms(s,{filter:(h,m)=>{const p=It(m);return!(p==null?void 0:p.classList.contains("draggable"))||!o.draggable},onEnd(h){const{newIndex:m,oldIndex:p}=h,{srcElement:g}=h.originalEvent;if(!g){r();return}const v=It(g);if(!v){r();return}if(!v.classList.contains("draggable")){r();return}p!==void 0&&m!==void 0&&!Number.isNaN(p)&&!Number.isNaN(m)&&p!==m&&a("sortTabs",p,m),r()},onMove(h){const m=It(h.related);if(m!=null&&m.classList.contains("draggable")&&o.draggable){const p=h.dragged.classList.contains("affix-tab"),g=h.related.classList.contains("affix-tab");return p===g}else return!1},onStart:()=>{var h;s.style.cursor="grabbing",(h=s.querySelector(".draggable"))==null||h.classList.add("dragging")}});t.value=yield u()})}function n(){return K(this,null,function*(){const{isMobile:s}=Ua();s.value||(yield Re(),l())})}qe(n),we(()=>o.styleType,()=>{var s;(s=t.value)==null||s.destroy(),n()}),$t(()=>{var s;(s=t.value)==null||s.destroy()})}function Iu(o){let a=null,t=null,l=0;const n=q(null),s=q(null),r=q(!1),u=q(!0),d=q(!1);function h(){var N;const _=(N=n.value)==null?void 0:N.$el;if(!_||!s.value)return{};const U=_.clientWidth,I=s.value.clientWidth;return{scrollbarWidth:U,scrollViewWidth:I}}function m(_,U=150){var Z;const{scrollbarWidth:I,scrollViewWidth:N}=h();!I||!N||I>N||(Z=s.value)==null||Z.scrollBy({behavior:"smooth",left:_==="left"?-(I-U):+(I-U)})}function p(){return K(this,null,function*(){var I,N;yield Re();const _=(I=n.value)==null?void 0:I.$el;if(!_)return;const U=_==null?void 0:_.querySelector("div[data-radix-scroll-area-viewport]");s.value=U,v(),yield Re(),g(),a==null||a.disconnect(),a=new ResizeObserver(ba(Z=>{v(),g()},100)),a.observe(U),l=((N=o.tabs)==null?void 0:N.length)||0,t==null||t.disconnect(),t=new MutationObserver(()=>{const Z=U.querySelectorAll('div[data-tab-item="true"]').length;Z>l&&g(),Z!==l&&(v(),l=Z)}),t.observe(U,{attributes:!1,childList:!0,subtree:!0})})}function g(){return K(this,null,function*(){if(!s.value)return;yield Re();const _=s.value,{scrollbarWidth:U}=h(),{scrollWidth:I}=_;U>=I||requestAnimationFrame(()=>{const N=_==null?void 0:_.querySelector(".is-active");N==null||N.scrollIntoView({behavior:"smooth",inline:"start"})})})}function v(){return K(this,null,function*(){if(!s.value)return;const{scrollbarWidth:_}=h();r.value=s.value.scrollWidth>_})}const V=ba(({left:_,right:U})=>{u.value=_,d.value=U},100);return we(()=>o.active,()=>K(null,null,function*(){g()}),{flush:"post"}),we(()=>o.styleType,()=>{p()}),qe(p),$t(()=>{a==null||a.disconnect(),t==null||t.disconnect(),a=null,t=null}),{handleScrollAt:V,initScrollbar:p,scrollbarRef:n,scrollDirection:m,scrollIsAtLeft:u,scrollIsAtRight:d,showScrollButton:r}}const Uu={class:"flex h-full flex-1 overflow-hidden"},Au=T({name:"TabsView",__name:"tabs-view",props:{active:{},contentClass:{default:"vben-tabs-content"},contextMenus:{},draggable:{type:Boolean,default:!0},gap:{},maxWidth:{},minWidth:{},showIcon:{type:Boolean},styleType:{default:"chrome"},tabs:{}},emits:["close","sortTabs","unpin"],setup(o,{emit:a}){const t=o,l=a,n=Te(t,l),{handleScrollAt:s,scrollbarRef:r,scrollDirection:u,scrollIsAtLeft:d,scrollIsAtRight:h,showScrollButton:m}=Iu(t);return zu(t,l),(p,g)=>(i(),x("div",Uu,[$e(C("span",{class:O([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(d),"pointer-events-none opacity-30":e(d)},"border-r px-2"]),onClick:g[0]||(g[0]=v=>e(u)("left"))},[f(e(Us),{class:"size-4 h-full"})],2),[[Be,e(m)]]),C("div",{class:O([{"pt-[3px]":p.styleType==="chrome"},"size-full flex-1 overflow-hidden"])},[f(e(ct),{ref_key:"scrollbarRef",ref:r,"shadow-bottom":!1,"shadow-top":!1,class:"h-full",horizontal:"","scroll-bar-class":"z-10 hidden ",shadow:"","shadow-left":"","shadow-right":"",onScrollAt:e(s)},{default:c(()=>[p.styleType==="chrome"?(i(),y(e(Eu),_e(ie({key:0},J(J(J({},e(n)),p.$attrs),p.$props))),null,16)):(i(),y(e(Cu),_e(ie({key:1},J(J(J({},e(n)),p.$attrs),p.$props))),null,16))]),_:1},8,["onScrollAt"])],2),$e(C("span",{class:O([{"hover:bg-muted text-muted-foreground cursor-pointer":!e(h),"pointer-events-none opacity-30":e(h)},"hover:bg-muted text-muted-foreground cursor-pointer border-l px-2"]),onClick:g[1]||(g[1]=v=>e(u)("right"))},[f(e(jt),{class:"size-4 h-full"})],2),[[Be,e(m)]])]))}});function Hu(){const o=et(),a=We(),t=Ct(),l=st(),{contentIsMaximize:n,toggleMaximize:s}=qa(),{closeAllTabs:r,closeCurrentTab:u,closeLeftTabs:d,closeOtherTabs:h,closeRightTabs:m,closeTabByKey:p,getTabDisableState:g,openTabInNewWindow:v,refreshTab:V,toggleTabPin:_}=Xa(),U=w(()=>a.fullPath),{locale:I}=Oa(),N=q();we([()=>l.getTabs,()=>l.updateTime,()=>I.value],([z])=>{N.value=z.map(F=>X(F))});const Z=()=>{const z=hs(o.getRoutes(),F=>{var j;return!!((j=F.meta)!=null&&j.affixTab)});l.setAffixTabs(z)},H=z=>{o.push(z)},R=z=>K(null,null,function*(){yield p(z)});function X(z){var F;return ke(J({},z),{meta:ke(J({},z==null?void 0:z.meta),{title:b((F=z==null?void 0:z.meta)==null?void 0:F.title)})})}return we(()=>t.accessMenus,()=>{Z()},{immediate:!0}),we(()=>a.path,()=>{var F,j;const z=(j=(F=a.matched)==null?void 0:F[a.matched.length-1])==null?void 0:j.meta;l.addTab(ke(J({},a),{meta:z||a.meta}))},{immediate:!0}),{createContextMenus:z=>{var he,Me;const{disabledCloseAll:F,disabledCloseCurrent:j,disabledCloseLeft:re,disabledCloseOther:ve,disabledCloseRight:be,disabledRefresh:W}=g(z),ee=(Me=(he=z==null?void 0:z.meta)==null?void 0:he.affixTab)!=null?Me:!1;return[{disabled:j,handler:()=>K(null,null,function*(){yield u(z)}),icon:mt,key:"close",text:b("preferences.tabbar.contextMenu.close")},{handler:()=>K(null,null,function*(){yield _(z)}),icon:ee?eo:Vt,key:"affix",text:ee?b("preferences.tabbar.contextMenu.unpin"):b("preferences.tabbar.contextMenu.pin")},{handler:()=>K(null,null,function*(){n.value||(yield o.push(z.fullPath)),s()}),icon:n.value?Qa:Ja,key:n.value?"restore-maximize":"maximize",text:n.value?b("preferences.tabbar.contextMenu.restoreMaximize"):b("preferences.tabbar.contextMenu.maximize")},{disabled:W,handler:V,icon:Nt,key:"reload",text:b("preferences.tabbar.contextMenu.reload")},{handler:()=>K(null,null,function*(){yield v(z)}),icon:Rs,key:"open-in-new-window",separator:!0,text:b("preferences.tabbar.contextMenu.openInNewWindow")},{disabled:re,handler:()=>K(null,null,function*(){yield d(z)}),icon:Vs,key:"close-left",text:b("preferences.tabbar.contextMenu.closeLeft")},{disabled:be,handler:()=>K(null,null,function*(){yield m(z)}),icon:Ls,key:"close-right",separator:!0,text:b("preferences.tabbar.contextMenu.closeRight")},{disabled:ve,handler:()=>K(null,null,function*(){yield h(z)}),icon:Ws,key:"close-other",text:b("preferences.tabbar.contextMenu.closeOther")},{disabled:F,handler:r,icon:Ps,key:"close-all",text:b("preferences.tabbar.contextMenu.closeAll")}]},currentActive:U,currentTabs:N,handleClick:H,handleClose:R}}const Ou={class:"flex-center h-full"},Du=T({name:"LayoutTabbar",__name:"tabbar",props:{showIcon:{type:Boolean},theme:{}},setup(o){const a=We(),t=st(),{contentIsMaximize:l,toggleMaximize:n}=qa(),{unpinTab:s}=Xa(),{createContextMenus:r,currentActive:u,currentTabs:d,handleClick:h,handleClose:m}=Hu(),p=w(()=>{const g=t.getTabByPath(u.value);return r(g).map(V=>ke(J({},V),{label:V.text,value:V.key}))});return D.tabbar.persist||t.closeOtherTabs(a),(g,v)=>(i(),x(Y,null,[f(e(Au),{active:e(u),class:O(g.theme),"context-menus":e(r),draggable:e(D).tabbar.draggable,"show-icon":g.showIcon,"style-type":e(D).tabbar.styleType,tabs:e(d),onClose:e(m),onSortTabs:e(t).sortTabs,onUnpin:e(s),"onUpdate:active":e(h)},null,8,["active","class","context-menus","draggable","show-icon","style-type","tabs","onClose","onSortTabs","onUnpin","onUpdate:active"]),C("div",Ou,[e(D).tabbar.showMore?(i(),y(e(gu),{key:0,menus:p.value},null,8,["menus"])):E("",!0),e(D).tabbar.showMaximize?(i(),y(e(yu),{key:1,screen:e(l),onChange:e(n),"onUpdate:screen":e(n)},null,8,["screen","onChange","onUpdate:screen"])):E("",!0)])],64))}}),Zu=T({name:"BasicLayout",__name:"layout",emits:["clearPreferencesAndLogout"],setup(o,{emit:a}){const t=a,{isDark:l,isHeaderNav:n,isMixedNav:s,isMobile:r,isSideMixedNav:u,layout:d,preferencesButtonPosition:h,sidebarCollapsed:m,theme:p}=Qe(),g=Kt(),{refresh:v}=Ya(),V=w(()=>l.value||D.theme.semiDarkSidebar?"dark":"light"),_=w(()=>l.value||D.theme.semiDarkHeader?"dark":"light"),U=w(()=>{const{collapsedShowTitle:ae}=D.sidebar,se=[];return ae&&m.value&&!s.value&&se.push("mx-auto"),u.value&&se.push("flex-center"),se.join(" ")}),I=w(()=>D.navigation.styleType==="rounded"),N=w(()=>r.value&&m.value?!0:n.value||s.value?!1:m.value||u.value),Z=w(()=>!r.value&&(n.value||s.value)),{extraActiveMenu:H,extraMenus:R,handleDefaultSelect:X,handleMenuMouseEnter:Q,handleMixedMenuSelect:z,handleSideMouseLeave:F,sidebarExtraVisible:j}=hu(),{handleMenuSelect:re,headerActive:ve,headerMenus:be,sidebarActive:W,sidebarMenus:ee,sidebarVisible:ge}=bu();function he(ae){return Aa(ae,se=>ke(J({},bs(se)),{name:b(se.name)}))}function Me(){Ke({sidebar:{hidden:!D.sidebar.hidden}})}function P(){t("clearPreferencesAndLogout")}we(()=>D.app.layout,ae=>K(null,null,function*(){ae==="sidebar-mixed-nav"&&D.sidebar.hidden&&Ke({sidebar:{hidden:!1}})})),we(()=>D.app.locale,v);const G=Ae(),te=w(()=>Object.keys(G).filter(ae=>ae.startsWith("header-")));return(ae,se)=>(i(),y(e(Fd),{"sidebar-extra-visible":e(j),"onUpdate:sidebarExtraVisible":se[0]||(se[0]=ye=>Da(j)?j.value=ye:null),"content-compact":e(D).app.contentCompact,"footer-enable":e(D).footer.enable,"footer-fixed":e(D).footer.fixed,"header-hidden":e(D).header.hidden,"header-mode":e(D).header.mode,"header-theme":_.value,"header-toggle-sidebar-button":e(D).widget.sidebarToggle,"header-visible":e(D).header.enable,"is-mobile":e(D).app.isMobile,layout:e(d),"sidebar-collapse":e(D).sidebar.collapsed,"sidebar-collapse-show-title":e(D).sidebar.collapsedShowTitle,"sidebar-enable":e(ge),"sidebar-expand-on-hover":e(D).sidebar.expandOnHover,"sidebar-extra-collapse":e(D).sidebar.extraCollapse,"sidebar-hidden":e(D).sidebar.hidden,"sidebar-theme":V.value,"sidebar-width":e(D).sidebar.width,"tabbar-enable":e(D).tabbar.enable,"tabbar-height":e(D).tabbar.height,onSideMouseLeave:e(F),onToggleSidebar:Me,"onUpdate:sidebarCollapse":se[1]||(se[1]=ye=>e(Ke)({sidebar:{collapsed:ye}})),"onUpdate:sidebarEnable":se[2]||(se[2]=ye=>e(Ke)({sidebar:{enable:ye}})),"onUpdate:sidebarExpandOnHover":se[3]||(se[3]=ye=>e(Ke)({sidebar:{expandOnHover:ye}})),"onUpdate:sidebarExtraCollapse":se[4]||(se[4]=ye=>e(Ke)({sidebar:{extraCollapse:ye}}))},kt({logo:c(()=>[e(D).logo.enable?(i(),y(e(xa),{key:0,class:O(U.value),collapsed:N.value,src:e(D).logo.source,text:e(D).app.name,theme:Z.value?_.value:e(p)},null,8,["class","collapsed","src","text","theme"])):E("",!0)]),header:c(()=>[f(e(ou),{theme:e(p),onClearPreferencesAndLogout:P},kt({"user-dropdown":c(()=>[B(ae.$slots,"user-dropdown")]),notification:c(()=>[B(ae.$slots,"notification")]),_:2},[!Z.value&&e(D).breadcrumb.enable?{name:"breadcrumb",fn:c(()=>[f(e(cr),{"hide-when-only-one":e(D).breadcrumb.hideOnlyOne,"show-home":e(D).breadcrumb.showHome,"show-icon":e(D).breadcrumb.showIcon,type:e(D).breadcrumb.styleType},null,8,["hide-when-only-one","show-home","show-icon","type"])]),key:"0"}:void 0,Z.value?{name:"menu",fn:c(()=>[f(e(Ta),{"default-active":e(ve),menus:he(e(be)),rounded:I.value,theme:_.value,class:"w-full",mode:"horizontal",onSelect:e(re)},null,8,["default-active","menus","rounded","theme","onSelect"])]),key:"1"}:void 0,ce(te.value,ye=>({name:ye,fn:c(()=>[B(ae.$slots,ye)])}))]),1032,["theme"])]),menu:c(()=>[f(e(Ta),{accordion:e(D).navigation.accordion,collapse:e(D).sidebar.collapsed,"collapse-show-title":e(D).sidebar.collapsedShowTitle,"default-active":e(W),menus:he(e(ee)),rounded:I.value,theme:V.value,mode:"vertical",onSelect:e(re)},null,8,["accordion","collapse","collapse-show-title","default-active","menus","rounded","theme","onSelect"])]),"mixed-menu":c(()=>[f(e(mu),{"active-path":e(H),menus:he(e(be)),rounded:I.value,theme:V.value,onDefaultSelect:e(X),onEnter:e(Q),onSelect:e(z)},null,8,["active-path","menus","rounded","theme","onDefaultSelect","onEnter","onSelect"])]),"side-extra":c(()=>[f(e(fu),{accordion:e(D).navigation.accordion,collapse:e(D).sidebar.extraCollapse,menus:he(e(R)),rounded:I.value,theme:V.value},null,8,["accordion","collapse","menus","rounded","theme"])]),"side-extra-title":c(()=>[e(D).logo.enable?(i(),y(e(xa),{key:0,text:e(D).app.name,theme:e(p)},null,8,["text","theme"])):E("",!0)]),tabbar:c(()=>[e(D).tabbar.enable?(i(),y(e(Du),{key:0,"show-icon":e(D).tabbar.showIcon,theme:e(p)},null,8,["show-icon","theme"])):E("",!0)]),content:c(()=>[f(e(Yd))]),extra:c(()=>[B(ae.$slots,"extra"),e(D).app.enableCheckUpdates?(i(),y(e(pr),{key:0,"check-updates-interval":e(D).app.checkUpdatesInterval},null,8,["check-updates-interval"])):E("",!0),e(D).widget.lockScreen?(i(),y(Ze,{key:1,name:"slide-up"},{default:c(()=>[e(g).isLockScreen?B(ae.$slots,"lock-screen",{key:0}):E("",!0)]),_:3})):E("",!0),e(h).fixed?(i(),y(e(io),{key:2,class:"z-100 fixed bottom-20 right-0",onClearPreferencesAndLogout:P})):E("",!0),f(e(Ln))]),_:2},[e(D).transition.loading?{name:"content-overlay",fn:c(()=>[f(e(Jd))]),key:"0"}:void 0,e(D).footer.enable?{name:"footer",fn:c(()=>[f(e(Qd),null,{default:c(()=>[e(D).copyright.enable?(i(),y(e(Ss),_e(ie({key:0},e(D).copyright)),null,16)):E("",!0)]),_:1})]),key:"1"}:void 0]),1032,["sidebar-extra-visible","content-compact","footer-enable","footer-fixed","header-hidden","header-mode","header-theme","header-toggle-sidebar-button","header-visible","is-mobile","layout","sidebar-collapse","sidebar-collapse-show-title","sidebar-enable","sidebar-expand-on-hover","sidebar-extra-collapse","sidebar-hidden","sidebar-theme","sidebar-width","tabbar-enable","tabbar-height","onSideMouseLeave"]))}});export{Xu as N,Zu as _,Gd as a,cr as b,pr as c,zr as d,Yu as e,Zr as f,io as g,Pd as h,Ju as i};
