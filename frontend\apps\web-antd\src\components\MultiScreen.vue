<script>
import {
  Switch as ASwitch,
  TabPane as ATabPane,
  Tabs as ATabs,
  Tree as ATree,
} from 'ant-design-vue';

import { layerService } from '../utils/cesium/layers/index';
import { getKeysFromTreeNodes } from '../utils/utils';
import ztu from '../ztu';

export default {
  components: {
    ATree,
    ATabs,
    ATabPane,
    ASwitch,
  },

  data() {
    return {
      activeKey: 0,
      checkedKeys: [],
      screenslist: [],
      layers: [],
      GISLayers: [],
      checkedkeysArray: [],
    };
  },
  mounted() {
    // 图层列表（.layers图层列表；.data图层树）
    // 主屏幕图层服务
    this.GISLayers.push(ztu.global.GISLayers);

    // 图层列表
    const datas = this.GISLayers[0].getData();
    // 图层树列表
    this.layers = this.GISLayers[0].getTreeData();
    // 分屏数
    const num = ztu.global.SplitMap.splitViewers.length;

    this.checkedkeysArray = [[]];
    this.GISLayers[0].on('showChanged', (keys) => {
      this.checkedkeysArray[0] = keys;
      this.change(this.activeKey);
    });
    this.checkedKeys = this.checkedkeysArray[0] = this.GISLayers[0].layers
      .filter((a) => a.show)
      .map((a) => a.key);

    const screenslist = [{ title: '主屏幕', key: 0 }];
    console.log(num);
    for (let i = 1; i < num; i++) {
      // 屏幕N的图层服务
      const subMap = ztu.global.SplitMap.splitViewers[i];
      const layerserver = new layerService(subMap.viewer);
      this.GISLayers.push(layerserver);
      // 加载屏幕N图层
      layerserver.loadData(datas);
      screenslist.push({
        title: `屏幕${i + 1}`,
        key: i,
        tongbu: subMap.synchronization,
      });
      this.checkedkeysArray.push(this.checkedKeys);
      layerserver.show(this.checkedKeys);
    }
    console.log(screenslist);
    this.screenslist = screenslist;
  },
  methods: {
    change(key) {
      this.activeKey = key;
      this.layers = this.GISLayers[Number.parseInt(key)].getTreeData();
      this.checkedKeys = this.checkedkeysArray[Number.parseInt(key)];
    },
    tongbu(checked) {
      const item = this.screenslist[this.activeKey];
      item.tongbu = checked;
      console.log(this.screenslist);
      ztu.global.SplitMap.setSynchronization(item.key, checked);
    },
    layerCheck(checkedKeys, { checked, checkedNodes, node, event }) {
      const nowkey = Number.parseInt(this.activeKey);
      console.log('........node', checked, node);
      if (checked && node.data.type) {
        this.GISLayers[nowkey].showAndZoomToLayer(node.key);
      } else if (checked) {
        const keys = getKeysFromTreeNodes(node);
        this.GISLayers[nowkey].show(keys);
      } else if (node.data.type) {
        this.GISLayers[nowkey].hide(node.key);
      } else {
        getKeysFromTreeNodes(node).forEach((key) => {
          this.GISLayers[nowkey].hide(key);
        });
      }
      this.checkedkeysArray[nowkey] = checkedKeys;
    },
  },
};
</script>
<template>
  <div class="multiscreen-view">
    <ATabs v-model="activeKey" style="width: 280px" @change="change">
      <ATabPane v-for="item in screenslist" :key="item.key" :tab="item.title">
        <div class="setting">
          <ATree
            v-model:checked-keys="checkedKeys"
            :tree-data="layers"
            checkable
            default-expand-all
            @check="layerCheck"
          />
        </div>
        <div>
          <ASwitch
            v-if="item.key > 0"
            v-model:checked="item.tongbu"
            checked-children="同步"
            un-checked-children="不同步"
            @change="tongbu"
          />
        </div>
      </ATabPane>
    </ATabs>
  </div>
</template>

<style lang="less">
.multiscreen-view {
  overflow-y: auto;
  background: none;
  padding: 0px 10px 10px;
  height: 100%;
  font-size: 14px;
  font-weight: 400;
  line-height: 19px;
  .ant-checkbox-wrapper {
    padding: 10px 24px;
  }
  .ant-checkbox-input {
    background: transparent;
  }
  .ant-tabs {
  }
  .ant-tabs-top > .ant-tabs-nav::before,
  .ant-tabs-bottom > .ant-tabs-nav::before,
  .ant-tabs-top > div > .ant-tabs-nav::before,
  .ant-tabs-bottom > div > .ant-tabs-nav::before {
    border-bottom: 1px solid ;
  }

  .ant-tree {
    background: rgba(5, 17, 41, 0);
    flex-grow: 1;
    max-height: 450px;
    overflow-y: scroll;
  }



  .ant-tree-checkbox-inner {
    border: 1px solid
  }
}
.setting {
  display: flex;
  align-items: top;
  justify-content: space-between;
}
</style>
