import * as Cesium from 'cesium';
import ShapeConfig from './config/ShapeConfig.js';
import Base from './core/Base';

let core = {
  /**
   * 获取地图中心点坐标（WGS84）
   * @param {Object} viewer
   * @return {Object}  {lng,lat,alt} 地理坐标
   */
  getMapCenter(viewer) {
    if (!viewer) return false;
    let cartesian = viewer.camera.pickEllipsoid(
      new Cesium.Cartesian2(
        viewer.canvas.clientWidth / 2,
        viewer.canvas.clientHeight / 2,
      ),
    );
    if (!cartesian) return false;
    let wgs84 = this.transformCartesianToWGS84(cartesian);
    return wgs84;
  },
  /**
   * 通过地图中心位置获取相机位置
   * @param {Object} {lng,lat,alt} 地理坐标
   * @param {Object} heading 方向角
   * @param {Object} pitch	俯视角
   * @return {Object} {lng,lat,alt} 地理坐标
   */
  getCameraPositionByMapCenter(position, heading, pitch) {
    var a = 6378137;
    var b = 6356752.3142;
    var f = 1 / 298.257223563;

    var lon1 = position.lng * 1;
    var lat1 = position.lat * 1;
    var s = position.alt / Math.tan((pitch * Math.PI) / 180);
    var alpha1 = heading * (Math.PI / 180);
    var sinAlpha1 = Math.sin(alpha1);
    var cosAlpha1 = Math.cos(alpha1);
    var tanU1 = (1 - f) * Math.tan(lat1 * (Math.PI / 180));
    var cosU1 = 1 / Math.sqrt(1 + tanU1 * tanU1),
      sinU1 = tanU1 * cosU1;
    var sigma1 = Math.atan2(tanU1, cosAlpha1);
    var sinAlpha = cosU1 * sinAlpha1;
    var cosSqAlpha = 1 - sinAlpha * sinAlpha;
    var uSq = (cosSqAlpha * (a * a - b * b)) / (b * b);
    var A = 1 + (uSq / 16384) * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));
    var B = (uSq / 1024) * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));
    var sigma = s / (b * A),
      sigmaP = 2 * Math.PI;
    while (Math.abs(sigma - sigmaP) > 1e-12) {
      var cos2SigmaM = Math.cos(2 * sigma1 + sigma);
      var sinSigma = Math.sin(sigma);
      var cosSigma = Math.cos(sigma);
      var deltaSigma =
        B *
        sinSigma *
        (cos2SigmaM +
          (B / 4) *
            (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM) -
              (B / 6) *
                cos2SigmaM *
                (-3 + 4 * sinSigma * sinSigma) *
                (-3 + 4 * cos2SigmaM * cos2SigmaM)));
      sigmaP = sigma;
      sigma = s / (b * A) + deltaSigma;
    }

    var tmp = sinU1 * sinSigma - cosU1 * cosSigma * cosAlpha1;
    var lat2 = Math.atan2(
      sinU1 * cosSigma + cosU1 * sinSigma * cosAlpha1,
      (1 - f) * Math.sqrt(sinAlpha * sinAlpha + tmp * tmp),
    );
    var lambda = Math.atan2(
      sinSigma * sinAlpha1,
      cosU1 * cosSigma - sinU1 * sinSigma * cosAlpha1,
    );
    var C = (f / 16) * cosSqAlpha * (4 + f * (4 - 3 * cosSqAlpha));
    var L =
      lambda -
      (1 - C) *
        f *
        sinAlpha *
        (sigma +
          C *
            sinSigma *
            (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));

    var revAz = Math.atan2(sinAlpha, -tmp); // final bearing
    console.log('rev', revAz);
    var lngLatObj = {
      lng: lon1 + L * (180 / Math.PI),
      lat: lat2 * (180 / Math.PI),
      alt: s,
    };
    return lngLatObj;
  },
  getDefaultViewRectangle() {
    return Cesium.Camera.DEFAULT_VIEW_RECTANGLE;
  },
  getDefaultViewCenter() {
    let rect = this.getDefaultViewRectangle();
    let cartographic = Cesium.Rectangle.center(rect);
    return core.transformCartographicToWGS84(cartographic);
  },
  computeExtent(positions, range) {
    let rect = Cesium.Rectangle.fromCartesianArray(positions);
    if (range) {
      let x = (Math.max(rect.width, 0.001) / 4) * range;
      let y = (Math.max(rect.height, 0.001) / 4) * range;
      return Cesium.Rectangle.fromRadians(
        rect.west - x,
        rect.south - y,
        rect.east + x,
        rect.north + y,
      );
    }
    return rect;
  },
  /***
   * 坐标转换 84转笛卡尔
   *
   * @param {Object} {lng,lat,alt} 地理坐标
   *
   * @return {Object} Cartesian3 三维位置坐标
   */
  transformWGS84ToCartesian(position, alt) {
    return position
      ? Cesium.Cartesian3.fromDegrees(
          position.lng || position.lon,
          position.lat,
          (position.alt = alt || position.alt),
          Cesium.Ellipsoid.WGS84,
        )
      : Cesium.Cartesian3.ZERO;
  },
  /***
   * 坐标数组转换 84转笛卡尔
   *
   * @param {Array} WSG84Arr {lng,lat,alt} 地理坐标数组
   * @param {Number} alt 拔高
   * @return {Array} Cartesian3 三维位置坐标数组
   */
  transformWGS84ArrayToCartesianArray(WSG84Arr, alt) {
    if (WSG84Arr) {
      var $this = this;
      return WSG84Arr
        ? WSG84Arr.map(function (item) {
            return $this.transformWGS84ToCartesian(item, alt);
          })
        : [];
    }
  },
  /***
   * 坐标转换 笛卡尔转84
   *
   * @param {Object} Cartesian3 三维位置坐标
   *
   * @return {Object} {lng,lat,alt} 地理坐标
   */
  transformCartesianToWGS84(cartesian) {
    if (cartesian) {
      var ellipsoid = Cesium.Ellipsoid.WGS84;
      var cartographic = ellipsoid.cartesianToCartographic(cartesian);
      return {
        lng: Cesium.Math.toDegrees(cartographic.longitude),
        lat: Cesium.Math.toDegrees(cartographic.latitude),
        alt: cartographic.height,
      };
    }
  },
  /***
   * 坐标数组转换 笛卡尔转86
   *
   * @param {Array} cartesianArr 三维位置坐标数组
   *
   * @return {Array} {lng,lat,alt} 地理坐标数组
   */
  transformCartesianArrayToWGS84Array(cartesianArr) {
    var $this = this;
    return cartesianArr
      ? cartesianArr.map(function (item) {
          return $this.transformCartesianToWGS84(item);
        })
      : [];
  },
  /**
   * 84坐标转弧度坐标
   * @param {Object} position wgs84
   * @return {Object} Cartographic 弧度坐标
   *
   */
  transformWGS84ToCartographic(position) {
    return position
      ? Cesium.Cartographic.fromDegrees(
          position.lng || position.lon,
          position.lat,
          position.alt,
        )
      : Cesium.Cartographic.ZERO;
  },
  /**
   * 弧度坐标转84坐标
   * @param {Object} position wgs84
   * @return {Object} Cartographic 弧度坐标
   *
   */
  transformCartographicToWGS84(cartographic) {
    return cartographic
      ? {
          lng: Cesium.Math.toDegrees(cartographic.longitude),
          lat: Cesium.Math.toDegrees(cartographic.latitude),
          alt: cartographic.height,
        }
      : {
          lng: 0,
          lat: 0,
          alt: 0,
        };
  },
  /*
	经纬度转换为世界坐标
	第一种方式：直接转换:
	Cesium.Cartesian3.fromDegrees(longitude, latitude, height, ellipsoid, result)

	第二种方式：先转换成弧度再转换
	var ellipsoid = viewer.scene.globe.ellipsoid;
	var cartographic = Cesium.Cartographic.fromDegrees(lng,lat,alt);
	var cartesian3 = ellipsoid.cartographicToCartesian(cartographic);
	世界坐标转换为经纬度
	var ellipsoid = viewer.scene.globe.ellipsoid;
	var cartesian3 = new Cesium.cartesian3(x,y,z);
	var cartographic = ellipsoid.cartesianToCartographic(cartesian3);
	var lat = Cesium.Math.toDegrees(cartograhphic.latitude);
	var lng = Cesium.Math.toDegrees(cartograhpinc.longitude);
	var alt = cartographic.height;
	弧度和经纬度
	经纬度转弧度：
	Cesium.CesiumMath.toRadians(degrees)
	弧度转经纬度：
	Cesium.CesiumMath.toDegrees(radians)
	屏幕坐标和世界坐标相互转换
	屏幕转世界坐标：
	var pick1= new Cesium.Cartesian2(0,0);
	var cartesian = viewer.scene.globe.pick(viewer.camera.getPickRay(pick1),viewer.scene);
	注意这里屏幕坐标一定要在球上，否则生成出的cartesian对象是undefined

	世界坐标转屏幕坐标
	Cesium.SceneTransforms.wgs84ToWindowCoordinates(scene, Cartesian3);
	结果是Cartesian2对象，取出X,Y即为屏幕坐标。

	Cesium.Cartesian2.fromCartesian3(cartesian, result)→ Cartesian2
	I：经纬度坐标（WGS84）→ Cartesian3
	Cesium.Cartesian3.fromDegrees(longitude, latitude, height, ellipsoid, result) → Cartesian3

	II：弧度坐标→ Cartesian3
	Cesium.Cartesian3.fromRadians(longitude, latitude, height, ellipsoid, result) → Cartesian3
	Cartographic
	I：Cartesian3→ Cartographic
	Cesium.Cartographic.fromCartesian(cartesian, ellipsoid, result) → Cartographic

	II：经纬度坐标（WGS84）→ Cartographic
	Cesium.Cartographic.fromDegrees(longitude, latitude, height, result) → Cartographic

	另外，经纬度坐标和弧度坐标也可以通过Cesium.Math来转换
	Cesium.CesiumMath.toDegrees(radians) → Number

	另外，经纬度坐标和弧度坐标也可以通过Cesium.Math来转换
	Cesium.CesiumMath.toDegrees(radians) → Number
	Cesium.CesiumMath.toRadians(degrees) → Number
	 */
  ClampToHeight(viewer, cartesian) {
    let px = Cesium.SceneTransforms.wgs84ToWindowCoordinates(
      viewer.scene,
      cartesian,
    );
    return core.getCatesian3FromPX(viewer, px);
  },
  /**
   * 拾取位置点
   *
   * @param {Object} px 屏幕坐标
   *
   * @return {Object} Cartesian3 三维坐标
   */
  getCatesian3FromPX(viewer, px) {
    if (viewer && px) {
      var picks = viewer.scene.drillPick(px);
      var cartesian = null;
      var isOn3dtiles = false,
        isOnTerrain = false,
        isOnUnderground = false;
      // drillPick
      for (let i in picks) {
        let pick = picks[i];

        if (
          (pick && pick.primitive instanceof Cesium.Cesium3DTileFeature) ||
          (pick && pick.primitive instanceof Cesium.Cesium3DTileset) ||
          (pick && pick.primitive instanceof Cesium.Model)
        ) {
          //模型上拾取
          isOn3dtiles = true;
          break;
        }
      }
      // 3dtilset
      if (isOn3dtiles) {
        viewer.scene.pick(px); // pick
        cartesian = viewer.scene.pickPosition(px);
        if (cartesian) {
          let cartographic = Cesium.Cartographic.fromCartesian(cartesian);
          if (cartographic.height < 0) cartographic.height = 0;
          let lon = Cesium.Math.toDegrees(cartographic.longitude),
            lat = Cesium.Math.toDegrees(cartographic.latitude),
            height = cartographic.height;
          cartesian = this.transformWGS84ToCartesian({
            lng: lon,
            lat: lat,
            alt: height,
          });
        }
      }
      isOnUnderground = viewer.camera.positionCartographic.clone().height < 0;
      // 地形
      let boolTerrain =
        viewer.terrainProvider instanceof Cesium.EllipsoidTerrainProvider;
      // Terrain
      if (!isOn3dtiles && !isOnUnderground && !boolTerrain) {
        var ray = viewer.scene.camera.getPickRay(px);
        if (!ray) return null;
        cartesian = viewer.scene.globe.pick(ray, viewer.scene);
        isOnTerrain = true;
        //console.log('cartesian',cartesian)
      }
      // 地球
      if (isOnUnderground || (!isOn3dtiles && !isOnTerrain && boolTerrain)) {
        cartesian = viewer.scene.camera.pickEllipsoid(
          px,
          viewer.scene.globe.ellipsoid,
        );
      }
      if (cartesian) {
        let position = this.transformCartesianToWGS84(cartesian);
        if (position.alt < 0) {
          cartesian = this.transformWGS84ToCartesian(position, 0.1);
        }
        return cartesian;
      }
      return false;
    }
  },
  /**
   * 获取84坐标的距离
   * @param {Array} {lng,lat,alt} 地理坐标数组
   */
  getPositionDistance(positions) {
    let distance = 0;
    for (let i = 0; i < positions.length - 1; i++) {
      let point1cartographic = this.transformWGS84ToCartographic(positions[i]);
      let point2cartographic = this.transformWGS84ToCartographic(
        positions[i + 1],
      );
      let geodesic = new Cesium.EllipsoidGeodesic();
      geodesic.setEndPoints(point1cartographic, point2cartographic);
      let s = geodesic.surfaceDistance; //Cesium.Cartesian3.distance(start_position, end_position);
      s = Math.sqrt(
        Math.pow(s, 2) +
          Math.pow(point2cartographic.height - point1cartographic.height, 2),
      );
      distance = distance + s;
    }
    return distance.toFixed(3);
  },
  /**
   * 计算一组84坐标组成多边形的面积
   * @param {Array} {lng,lat,alt} 地理坐标数组
   */
  getPositionsArea(positions) {
    let result = 0;
    if (positions) {
      let h = 0;
      let ellipsoid = Cesium.Ellipsoid.WGS84;
      positions.push(positions[0]);
      for (let i = 1; i < positions.length; i++) {
        let oel = ellipsoid.cartographicToCartesian(
          this.transformWGS84ToCartographic(positions[i - 1]),
        );
        let el = ellipsoid.cartographicToCartesian(
          this.transformWGS84ToCartographic(positions[i]),
        );
        h += oel.x * el.y - el.x * oel.y;
      }
      result = Math.abs(h).toFixed(2);
    }
    return result;
  },
  getPositionsAngle(positions) {
    let angle = 0;
    if (!positions) return angle;
    if (positions.length == 3) {
      let a = this.getPositionDistance([positions[0], positions[1]]);
      let b = this.getPositionDistance([positions[1], positions[2]]);
      let c = this.getPositionDistance([positions[2], positions[0]]);
      angle = Math.acos(
        (Math.pow(a, 2) + Math.pow(b, 2) - Math.pow(c, 2)) / (2 * a * b),
      );
    }
    return angle;
  },
  /**
   * 获取多段线中心点
   * @param {Array} positions Cartesian3 三维位置坐标数组
   * @return {Object} Cartesian3 三维位置坐标数组
   */
  getPolylineCenter(positions) {
    if (positions && positions.length > 0) {
      let startIdx = Math.floor((positions.length - 1) / 2);
      let endIdx = Math.ceil((positions.length - 1) / 2);
      return new Cesium.Cartesian3(
        (positions[startIdx].x + positions[endIdx].x) / 2,
        (positions[startIdx].y + positions[endIdx].y) / 2,
        (positions[startIdx].z + positions[endIdx].z) / 2,
      );
    }
    return false;
  },
  /**
   * 获取多边形中心点
   * @param {Array} positions Cartesian3 三维位置坐标数组
   * @return {Object} Cartesian3 三维位置坐标数组
   */
  getPolygonCenter(positions) {
    if (!positions) return false;
    else if (positions.length == 1) return positions[0];
    else if (positions.length == 2) return this.getPolylineCenter(positions);
    else return Cesium.BoundingSphere.fromPoints(positions).center;
  },
  /**
   * 获取长度（带单位）
   * @param {Number} length 长度
   */
  getLengthText(length) {
    /* // 计算距离
		let length=0;
		for(let i=0;i<positions.length-1;i++){
			length = length + Cesium.Cartesian3.distance(positions[i], positions[i+1])
		} */
    if (length > 1000) {
      return (length / 1000).toFixed(2) + ' 公里';
    } else {
      return (length / 1.0).toFixed(2) + ' 米';
    }
  },
  getAreaText(area) {
    if (area < 1000000) {
      return Math.abs(area / 1.0).toFixed(2) + ' 平方米';
    } else {
      return Math.abs((area / 1000000.0).toFixed(2)) + ' 平方公里';
    }
  },
  getMapLevel(height) {
    if (height > 48000000) {
      return 0;
    } else if (height > 24000000) {
      return 1;
    } else if (height > 12000000) {
      return 2;
    } else if (height > 6000000) {
      return 3;
    } else if (height > 3000000) {
      return 4;
    } else if (height > 1500000) {
      return 5;
    } else if (height > 750000) {
      return 6;
    } else if (height > 375000) {
      return 7;
    } else if (height > 187500) {
      return 8;
    } else if (height > 93750) {
      return 9;
    } else if (height > 46875) {
      return 10;
    } else if (height > 23437.5) {
      return 11;
    } else if (height > 11718.75) {
      return 12;
    } else if (height > 5859.38) {
      return 13;
    } else if (height > 2929.69) {
      return 14;
    } else if (height > 1464.84) {
      return 15;
    } else if (height > 732.42) {
      return 16;
    } else if (height > 366.21) {
      return 17;
    } else {
      return 18;
    }
  },
  entity2Json(entity) {
    console.log(entity);
    let json = {};
    if (!entity) return json;
    let getValue = (prop) => {
      if (!prop) return prop;
      return prop._value || prop.getValue();
    };
    // if(entity.id)
    // {
    // 	json.id = entity.id
    // }
    if (entity.position) json.position = getValue(entity.position);
    if (entity.point) {
      json.point = (({ color, pixelSize, heightReference }) => {
        return {
          color: getValue(color).toCssColorString(),
          pixelSize: getValue(pixelSize),
          heightReference: heightReference ? getValue(heightReference) : 0,
        };
      })(entity.point);
    }
    if (entity.polyline) {
      json.polyline = (({ clampToGround, material, positions, width }) => {
        return {
          clampToGround: getValue(clampToGround),
          material: getValue(material.color).toCssColorString(),
          positions: getValue(positions),
          width: getValue(width),
        };
      })(entity.polyline);
    }
    if (entity.polygon) {
      json.polygon = (({ fill, hierarchy, material }) => {
        return {
          fill: getValue(fill),
          hierarchy: getValue(hierarchy),
          material: getValue(material.color).toCssColorString(),
        };
      })(entity.polygon);
    }
    if (entity.ellipse) {
      json.ellipse = (({ fill, semiMajorAxis, semiMinorAxis, material }) => {
        return {
          fill: getValue(fill),
          semiMajorAxis: getValue(semiMajorAxis),
          semiMinorAxis: getValue(semiMinorAxis),
          material: getValue(material.color).toCssColorString(),
        };
      })(entity.ellipse);
    }
    if (entity.label) {
      json.label = (({ fillColor, font, text }) => {
        return {
          fillColor: getValue(fillColor).toCssColorString(),
          font: getValue(font),
          text: getValue(text),
        };
      })(entity.label);
    }
    if (entity.rectangle) {
      json.rectangle = (({ coordinates, fill, material }) => {
        return {
          coordinates: getValue(coordinates),
          fill: getValue(fill),
          material: getValue(material.color).toCssColorString(),
        };
      })(entity.rectangle);
    }
    if (entity.billboard) {
      json.billboard = (({ image, scale }) => {
        return {
          image: getValue(image),
          scale: getValue(scale),
        };
      })(entity.billboard);
    }
    if (entity.model) {
      json.model = (({ uri, scale }) => {
        return {
          uri: getValue(uri),
          scale: getValue(scale),
        };
      })(entity.model);
    }
    if (entity.name) {
      json.name = entity.name;
    }
    if (entity.properties) {
      //console.log(entity)
      json.properties = {};
      entity.properties.propertyNames.forEach((name) => {
        json.properties[name] = getValue(entity.properties[name]);
      });
      //json.properties = getValue(entity.properties);
    }
    console.log(json);
    console.log(json, JSON.stringify(json));
    return json;
  },
  json2Entity(json, entity, config) {
    config = config || ShapeConfig;
    if (!entity) {
      entity = new Cesium.Entity();
    }

    let getCartesian = (arr) => {
      console.log(arr);
      if (arr instanceof Array) {
        console.log(arr);
        return arr.map((a) => {
          console.log(a);
          return new Cesium.Cartesian3(a.x, a.y, a.z);
        });
      } else return new Cesium.Cartesian3(arr.x, arr.y, arr.z);
    };
    let getColor = (color) => {
      return Cesium.Color.fromCssColorString(color);
    };

    if (json.position) {
      entity.position = getCartesian(json.position);
    }
    if (json.point) {
      if (!entity.point)
        entity.point = new Cesium.PointGraphics(config.pointStyle);
      entity.point.color = getColor(json.point.color);
      entity.point.pixelSize = json.point.pixelSize;
      entity.point.heightReference = json.point.heightReference;
    }
    if (json.polyline) {
      if (!entity.polyline)
        entity.polyline = new Cesium.PolylineGraphics(config.polylineStyle);
      json.polyline.clampToGround != undefined &&
        (entity.polyline.clampToGround = json.polyline.clampToGround);
      json.polyline.material != undefined &&
        (entity.polyline.material = getColor(json.polyline.material));
      json.polyline.positions != undefined &&
        (entity.polyline.positions = getCartesian(json.polyline.positions));
      json.polyline.width != undefined &&
        (entity.polyline.width = json.polyline.width);
    }

    if (json.polygon) {
      if (!entity.polygon)
        entity.polygon = new Cesium.PolygonGraphics(config.polygonStyle);
      entity.polygon.fill = json.polygon.fill;
      entity.polygon.hierarchy = new Cesium.PolygonHierarchy(
        getCartesian(json.polygon.hierarchy.positions),
        getCartesian(json.polygon.hierarchy.holes),
      );
      entity.polygon.material = getColor(json.polygon.material);
    }
    if (json.ellipse) {
      if (!entity.ellipse)
        entity.ellipse = new Cesium.EllipseGraphics(config.circleStyle);
      entity.ellipse.fill = json.ellipse.fill;
      entity.ellipse.semiMajorAxis = json.ellipse.semiMajorAxis;
      entity.ellipse.semiMinorAxis = json.ellipse.semiMinorAxis;
      entity.ellipse.material = getColor(json.ellipse.material);
    }
    if (json.label) {
      if (!entity.label)
        entity.label = new Cesium.LabelGraphics(config.textStyle);
      entity.label.fillColor = getColor(json.label.fillColor);
      entity.label.font = json.label.font;
      entity.label.text = json.label.text;
    }
    if (json.rectangle) {
      if (!entity.rectangle)
        entity.rectangle = new Cesium.RectangleGraphics(config.rectangleStyle);
      (entity.rectangle.coordinates = (({ west, south, east, north }) => {
        return Cesium.Rectangle.fromRadians(west, south, east, north);
      })(json.rectangle.coordinates)),
        (entity.rectangle.fill = json.rectangle.fill);
      entity.rectangle.material = getColor(json.rectangle.material);
    }
    if (json.billboard) {
      if (!entity.billboard)
        entity.billboard = new Cesium.BillboardGraphics(config.markerStyle);
      entity.billboard.image = json.billboard.image;
      entity.billboard.scale = json.billboard.scale;
    }
    if (json.model) {
      if (!entity.model)
        entity.model = new Cesium.ModelGraphics(config.modelStyle);
      entity.model.uri = json.model.uri;
      entity.model.scale = json.model.scale;
    }
    if (json.name) {
      entity.name = json.name;
    }
    if (json.properties) {
      entity.properties = json.properties;
    }
    return entity;
  },
  /**
   * 对齐
   * @param {Array} positions
   * @param {Number | String} x e.g 'left''center''right'
   * @param {Number | String} y e.g 'front''center''back'
   * @param {Number | String} z e.g 'top''center''bottom'
   */
  align(positions, x, y, z) {
    let xyz = [
      Number.MAX_VALUE,
      Number.MAX_VALUE,
      Number.MAX_VALUE,
      Number.MIN_VALUE,
      Number.MIN_VALUE,
      Number.MIN_VALUE,
    ];
    positions.forEach((position) => {
      xyz[0] = Math.min(xyz[0], position.x);
      xyz[1] = Math.min(xyz[1], position.y);
      xyz[2] = Math.min(xyz[2], position.z);
      xyz[3] = Math.max(xyz[3], position.x);
      xyz[4] = Math.max(xyz[4], position.y);
      xyz[5] = Math.max(xyz[5], position.z);
    });
    if (typeof x === 'string') {
      x = x.toLocaleLowerCase();
      if (x == 'left') {
        x = xyz[0];
      } else if (x == 'center') {
        x = (xyz[0] + xyz[3]) / 2;
      } else if (x == 'right') {
        x = xyz[3];
      }
    }
    if (typeof x !== 'number') {
      x = null;
    }
    if (typeof y === 'string') {
      y = y.toLocaleLowerCase();
      if (y == 'front') {
        y = xyz[1];
      } else if (y == 'center') {
        y = (xyz[1] + xyz[4]) / 2;
      } else if (y == 'back') {
        y = xyz[4];
      }
    }
    if (typeof y !== 'number') {
      y = null;
    }
    if (typeof z === 'string') {
      z = z.toLocaleLowerCase();
      if (z == 'top') {
        z = xyz[2];
      } else if (z == 'center') {
        z = (xyz[2] + xyz[5]) / 2;
      } else if (z == 'bottom') {
        z = xyz[5];
      }
    }
    if (typeof z !== 'number') {
      z = null;
    }
    return positions.map(
      (position) =>
        new Cesium.Cartesian3(
          x == null ? position.x : x,
          y == null ? position.y : y,
          z == null ? position.z : z,
        ),
    );
  },
  boxSelect(viewer, options) {
    return new Promise(function (resolve, reject) {
      /* //cesium默认右键为放大缩小，此处给zoomEventTypes设置新值
			viewer.scene.screenSpaceCameraController.zoomEventTypes=[Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK]
			//earthsdk默认右键为改变视角，此处禁止。
			viewer.scene.screenSpaceCameraController.lookEventTypes=[] */
      //右键按下标识
      var flag = false;
      //起点终点x,y
      var startX = null;
      var startY = null;
      var endX = null;
      var endY = null;
      //创建框选元素
      var selDiv = document.createElement('div');
      var tipDiv = document.createElement('div');
      var handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas);
      viewer._element.style.cursor = 'crosshair';
      viewer.enableCursorStyle = true;
      options = {
        offsetLeft: 0,
        offsetTop: 0,
        ...options,
      };
      //右键按下事件，设置起点，div设置样式和位置，添加到页面
      handler.setInputAction(function (event) {
        flag = true;
        startX = event.position.x;
        startY = event.position.y;
        selDiv.style.cssText =
          'position:absolute;width:0px;height:0px;font-size:0px;margin:0px;padding:0px;border:1px dashed #0099FF;background:rgba(0,153,255,0.3);z-index:1000;pointer-events: none;';
        selDiv.id = 'selectDiv';
        selDiv.style.left = startX + options.offsetLeft + 'px';
        selDiv.style.top = startY + options.offsetTop + 'px';
        document.body.appendChild(selDiv);
      }, Cesium.ScreenSpaceEventType.RIGHT_DOWN);
      //鼠标抬起事件，获取div坐上和右下的x,y 转为经纬度坐标
      handler.setInputAction(function (event) {
        flag = false;
        var l = Math.min(endX, startX);
        var t = Math.min(endY, startY);
        var w = Math.abs(endX - startX);
        var h = Math.abs(endY - startY);
        var earthPosition = viewer.camera.pickEllipsoid(
          { x: l, y: t },
          viewer.scene.globe.ellipsoid,
        );
        var result = {
          rectangle: {
            x: l,
            y: t,
            width: w,
            height: h,
          },
          positions: [
            core.getCatesian3FromPX(viewer, { x: l, y: t }),
            core.getCatesian3FromPX(viewer, { x: l + w, y: t }),
            core.getCatesian3FromPX(viewer, { x: l + w, y: t + h }),
            core.getCatesian3FromPX(viewer, { x: l, y: t + h }),
          ],
        };
        //console.log(result);
        resolve(result);
        document
          .getElementById('selectDiv')
          .parentNode.removeChild(document.getElementById('selectDiv'));
        end();
      }, Cesium.ScreenSpaceEventType.RIGHT_UP);
      //鼠标移动事件，处理位置css
      handler.setInputAction(function (event) {
        if (flag) {
          endX = event.endPosition.x;
          endY = event.endPosition.y;
          selDiv.style.left =
            Math.min(endX, startX) + options.offsetLeft + 'px';
          selDiv.style.top = Math.min(endY, startY) + options.offsetTop + 'px';
          selDiv.style.width = Math.abs(endX - startX) + 'px';
          selDiv.style.height = Math.abs(endY - startY) + 'px';
        }
        tipDiv.style.display = 'block';
        tipDiv.style.left =
          event.endPosition.x + options.offsetLeft + 10 + 'px';
        tipDiv.style.top = event.endPosition.y + options.offsetTop + 10 + 'px';
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      tipDiv.style.cssText =
        'position:absolute;font-size:12px;margin:0px;left:0;top:0;padding:5px;background:rgba(0,0,0,0.5);z-index:1001;color:#fff;pointer-events: none;display:none;';
      tipDiv.id = 'tipDiv';
      tipDiv.innerText = '右键框选范围,左击取消';
      document.body.appendChild(tipDiv);
      handler.setInputAction(function (event) {
        end();
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      function end() {
        document
          .getElementById('tipDiv')
          .parentNode.removeChild(document.getElementById('tipDiv'));
        viewer._element.style.cursor = 'default';
        viewer.enableCursorStyle = false;
        if (handler && !handler.isDestroyed()) {
          handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
          handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_UP);
          handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
          handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_DOWN);
          handler.destroy();
        }
      }
    });
  },
  //position_A绕position_B逆时针旋转angle度（角度）得到新点
  rotatedPointByAngle(position_A, position_B, angle) {
    //以B点为原点建立局部坐标系（东方向为x轴,北方向为y轴,垂直于地面为z轴），得到一个局部坐标到世界坐标转换的变换矩阵
    var localToWorld_Matrix =
      Cesium.Transforms.eastNorthUpToFixedFrame(position_B);
    //求世界坐标到局部坐标的变换矩阵
    var worldToLocal_Matrix = Cesium.Matrix4.inverse(
      localToWorld_Matrix,
      new Cesium.Matrix4(),
    );
    //B点在局部坐标的位置，其实就是局部坐标原点
    var localPosition_B = Cesium.Matrix4.multiplyByPoint(
      worldToLocal_Matrix,
      position_B,
      new Cesium.Cartesian3(),
    );
    //A点在以B点为原点的局部的坐标位置
    var localPosition_A = Cesium.Matrix4.multiplyByPoint(
      worldToLocal_Matrix,
      position_A,
      new Cesium.Cartesian3(),
    );
    //根据数学公式A点逆时针旋转angle度后在局部坐标系中的x,y,z位置
    var new_x =
      localPosition_A.x * Math.cos(Cesium.Math.toRadians(angle)) +
      localPosition_A.y * Math.sin(Cesium.Math.toRadians(angle));
    var new_y =
      localPosition_A.y * Math.cos(Cesium.Math.toRadians(angle)) -
      localPosition_A.x * Math.sin(Cesium.Math.toRadians(angle));
    var new_z = localPosition_A.z;
    //最后应用局部坐标到世界坐标的转换矩阵求得旋转后的A点世界坐标
    return Cesium.Matrix4.multiplyByPoint(
      localToWorld_Matrix,
      new Cesium.Cartesian3(new_x, new_y, new_z),
      new Cesium.Cartesian3(),
    );
  },
  Base,
};
export default core;
