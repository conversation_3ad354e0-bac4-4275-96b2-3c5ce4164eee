import * as Cesium from 'cesium';
import core from '../core.js';

class KeepView {
  constructor(viewer) {
    this._viewer = viewer;
    this._2dPC = { time: Date.parse(new Date()) };
    this._3dPC = { time: Date.parse(new Date()) };
    this._t = undefined;
    //this._viewer.scene.morphTime=0;
  }
  update() {
    let height = this._viewer.camera.positionCartographic.clone().height;
    let mapcenter =
      core.getMapCenter(this._viewer) || core.getDefaultViewCenter();
    let _pc = {
      height: height,
      longitude: Cesium.Math.toRadians(mapcenter.lng),
      latitude: Cesium.Math.toRadians(mapcenter.lat),
      heading: this._viewer.camera.heading,
      pitch: this._viewer.camera.pitch,
      roll: this._viewer.camera.roll,
    };
    console.log(_pc);
    let _curSceneMode = this._viewer.scene.mode;
    if (_curSceneMode == Cesium.SceneMode.SCENE2D) {
      _pc.height = _pc.height * 0.78;
      this._2dPC = {
        pc: _pc,
        time: Date.parse(new Date()),
      };
    } else if (_curSceneMode == Cesium.SceneMode.SCENE3D) {
      console.log(_pc.pitch);
      _pc.height = _pc.height / 0.78 / Math.abs(Math.sin(_pc.pitch));
      this._3dPC = {
        pc: _pc,
        time: Date.parse(new Date()),
      };
    }
  }
  keep() {
    let _correctPC = this._3dPC;
    let _waitMode = undefined;
    if (this._2dPC.time > this._3dPC.time) {
      _waitMode = Cesium.SceneMode.SCENE3D;
      _correctPC = this._2dPC.pc;
      this._3dPC = this._2dPC;
    } else if (this._2dPC.time < this._3dPC.time) {
      _waitMode = Cesium.SceneMode.SCENE2D;
      _correctPC = this._3dPC.pc;
      this._2dPC = this._3dPC;
    } else {
      return;
    }
    this._waitMode = _waitMode;
    (function (obj, correctPC) {
      if (obj._t != undefined) clearInterval(obj._t);
      obj._t = setInterval(function () {
        if (_waitMode === obj._viewer.scene.mode) {
          var c3 = new Cesium.Cartesian3.fromRadians(
            correctPC.longitude,
            correctPC.latitude,
            correctPC.height,
          );
          obj._viewer.camera.setView({
            destination: c3,
            orientation: {
              heading: correctPC.heading,
              pitch: Cesium.Math.toRadians(-90),
              roll: 0,
            },
          });
          clearInterval(obj._t);
        }
      }, 1);
    })(this, _correctPC);
  }
}

export default KeepView;
