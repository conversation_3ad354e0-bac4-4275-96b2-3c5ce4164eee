<template>
  <a-tooltip v-if="isOverflow" :title="text">
    <span class="truncate-text">{{ text }}</span>
  </a-tooltip>
  <span v-else class="truncate-text">{{ text }}</span>
</template>

<script>
import { ref, onMounted } from 'vue';

export default {
  name: 'EllipsisText',
  props: {
    text: {
      type: String,
      required: true,
    },
    maxWidth: {
      type: String,
      default: '200px', // 设置默认的最大宽度
    },
  },
  setup(props) {
    const isOverflow = ref(false);
    const textElement = ref(null);

    const checkOverflow = () => {
      if (textElement.value) {
        isOverflow.value =
          textElement.value.scrollWidth > textElement.value.clientWidth;
      }
    };

    onMounted(() => {
      checkOverflow();
    });

    return {
      isOverflow,
      textElement,
    };
  },
};
</script>

<style scoped lang="less">
@import '#/styles/dark-antd.less';

.truncate-text {
  display: inline-block;
  max-width: var(--max-width, 200px); /* 可通过属性调整宽度 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}
</style>
