<script lang="ts" setup>
import {onMounted, ref, watch} from "vue";
import {onStartSpaceQuery} from  '#/utils/cesium/mapTools'
import { GISMap } from "#/utils/cesium/index.js";
import {
  shapeViewer
} from '#/utils/cesium/layers/ShapeViewer'
import * as Cesium from "cesium";
import { list } from "#/views/dataManage/scene/scene.api.js";
import { useVbenVxeGrid } from "#/adapter/vxe-table.js";
import { useVbenModal, VbenButton } from '@vben/common-ui';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { columns } from '#/views/dataView/query.data';
import { exportData, query } from "#/views/dataView/view.api";
import { getUploadStatusLabel } from "#/views/dataManage/entity/entity.data";
import eb from "#/ztu/eventbus";
import { MenuFold, RoundLine,PolygonLine,RectangleOutline } from "@vben/icons";
import {showError, showWarn} from "#/utils/toast";
import {getCoordinatesParams} from '#/utils/cesium/space/query.js'
const props = defineProps(['visible','checkOptLayers']);
const emits = defineEmits(['startQuery','onCLoseSpaceQuery']);
const showContent =ref(true);

watch(() => props.visible, (newValue, oldValue) => {
  console.log(`count changed from ${oldValue} to ${newValue}`);
  if(!newValue) {
    // clear();
  }
});

onMounted(()=>{
  eb.on('clearData',clear);
})

const dataSource = [
  { id: 1, type: 'OSGB', datasetName: '某区一期', dataSize: 100 },
  { id: 2, type: 'DEM', datasetName: '某区一期', dataSize: 20 },
  { id: 3, type: '几何点', datasetName: '某区一期', dataSize: 10 },
];

const selectedShape = ref('');

const selectShape = (shape) => {
  clear();
  clearTableData();
  selectedShape.value = shape;
  gridApi.reload();
  shapeViewer.add(shape);

};

const clear = ()=> {
  eb.emit('captureEnd',{target:'tool'});
  eb.emit('clearAll',{target:'tool'});
  showContent.value = true;
  selectedShape.value = '';
}

const drawEnd = ()=>{
  eb.emit('captureEnd',{target:'tool'});
}

const getLayerParams = (checkOptLayers) => {
  if(!checkOptLayers) {
    return [];
  }
  let result = [];
  checkOptLayers.forEach((item) => {
    result.push({
      layerId: item.layerId,
      dataType: item.dataType,
    });
  });
  return result;
}



const gridOptions: VxeGridProps = {
  columns,
  pagerConfig: { enabled: false,},
  height: '300px',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        if(selectedShape.value == '') {
          return {
            total: 0,
            items: [],
          };
        }
        let checkOptLayers = props.checkOptLayers;
        let layerParams = getLayerParams(checkOptLayers);
        let paramDatas = getCoordinatesParams(shapeViewer);
        if(paramDatas.coordinates.length == 0) {
          return {
            total: 0,
            items: [],
          };
        }
        let params = {
          page : {
            current: page.currentPage,
            size: page.pageSize,
            searchCount:true
          },
          spatialRegionType:selectedShape.value,
          layers: layerParams,
          coordinates: paramDatas.coordinates
        };
        if(paramDatas.radius) {
          params.radius = paramDatas.radius;
        }
        let data = await query(params);
        if(!data) {
          data = [];
        }
        return {
          total: data && data.length,
          items: data,
        };
      },
    },
  },
  rowConfig: {
    isHover: true,
  },
};

const [Grid,gridApi] = useVbenVxeGrid({
  gridOptions,
  gridClass:'custom-table-class'
});


// 清空表格数据
const clearTableData = () => {
  gridApi.reload();
};

const clearQuery = () => {
  clear();
  clearTableData();
}

const performQuery =  async () =>  {
  // Logic for querying spatial data based on selected shape
  console.log('Query performed for shape:', selectedShape.value);
  // emits('startQuery', selectedShape.value);
  if(selectedShape.value.length == 0) {
    showWarn('请先选择几何图形！');
    reutrn;
  }

  gridApi.reload();
  // selectedShape.value = '';

};
// 关闭模态框
const handleCancel = () => {
  emits('onCancel', {});
};

const close = () =>{
  emits('onCLoseSpaceQuery',{});
}

function handleExport(row) {
  exportData(row.searchKey);
}
const changeFold = () => {
  showContent.value = !showContent.value;
}
</script>

<template>
  <view v-if="props.visible">
    <a class="foldMenu text-lin" @click="close" :style="{ color: 'hsl(var(--foreground))' }">隐藏</a>
    <a-card
      v-if="showContent"
      class="spaceQueryModal"
      width="900px"
      @cancel="handleCancel"
      :bodyStyle="{ padding: '0px', background: 'hsl(var(--card))' }"
    >
      <div class="spatial-query-container">
        <!-- Query Area -->
        <div class="query-area">
          <div style="margin-bottom: 10px">
            <h3 :style="{ color: 'hsl(var(--foreground))' }">查询范围</h3>
            <div class="text-light" :style="{ color: 'hsl(var(--muted-foreground))' }">
              (选择下列几何图形工具在界面上绘制需要查询的空间范围，仅支持倾斜摄影、矢量、栅格数据)
            </div>
          </div>
          <a-space size="large">
            <a-button
              :type="selectedShape === 'circle' ? 'primary' : 'default'"
              @click="selectShape('circle')"
              class="selectButton flex flex-col-center"
            >
              <RoundLine class="size-8" :style="{ color: selectedShape === 'circle' ? 'hsl(var(--primary-foreground))' : 'hsl(var(--foreground))' }"/>
              <div style="margin-top: 2px" :style="{ color: selectedShape === 'circle' ? 'hsl(var(--primary-foreground))' : 'hsl(var(--foreground))' }">圆形</div>
            </a-button>
            <a-button
              :type="selectedShape === 'polygon' ? 'primary' : 'default'"
              @click="selectShape('polygon')"
              class="selectButton flex flex-col-center"
            >
              <PolygonLine class="size-8" :style="{ color: selectedShape === 'polygon' ? 'hsl(var(--primary-foreground))' : 'hsl(var(--foreground))' }"/>
              <div style="margin-top: 2px" :style="{ color: selectedShape === 'polygon' ? 'hsl(var(--primary-foreground))' : 'hsl(var(--foreground))' }">多边形</div>
            </a-button>
            <a-button
              :type="selectedShape === 'rectangle' ? 'primary' : 'default'"
              @click="selectShape('rectangle')"
              class="selectButton flex flex-col-center"
            >
              <RectangleOutline class="size-8" :style="{ color: selectedShape === 'rectangle' ? 'hsl(var(--primary-foreground))' : 'hsl(var(--foreground))' }"/>
              <div style="margin-top: 2px" :style="{ color: selectedShape === 'rectangle' ? 'hsl(var(--primary-foreground))' : 'hsl(var(--foreground))' }">矩形</div>
            </a-button>
          </a-space>

          <!-- Query Button on a New Line -->
          <div style="margin-top: 16px;margin-bottom: 10px; text-align: right">
            <a-button style="margin-right: 10px" type="primary" @click="clearQuery">清除</a-button>
            <a-button type="primary" @click="performQuery">查询</a-button>
          </div>
        </div>

        <!-- Query Result -->
        <div class="query-result">
          <h3 :style="{ color: 'hsl(var(--foreground))' }">查询结果</h3>
          <Grid>
            <template #action="{ row }">
              <a-button
                class="actionButton"
                type="link"
                @click="handleExport(row)"
                :style="{ color: 'hsl(var(--primary))' }"
              >
                导出
              </a-button>
            </template>
          </Grid>
        </div>
      </div>
    </a-card>
  </view>

</template>
<style scoped lang="less">
@import '#/styles/dark-antd.less';
.foldMenu {
  position: absolute;
  top: 83px;
  right: 18px;
  z-index: 102;
  cursor: pointer;
  transition: color 0.2s ease;

  &:hover {
    color: hsl(var(--primary)) !important;
  }
}

.spaceQueryModal {
  position: absolute;
  top: 80px;
  right: 5px;
  z-index: 101;
  width: 600px;
  border-radius: 4px;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  box-shadow: 0 2px 8px hsl(var(--overlay));

}


.selectButton {
  border: 1px solid hsl(var(--border));
  background: hsl(var(--card));
  width: 80px;
  height: 80px;
  margin-left: 10px;
  margin-top: 10px;
  transition: all 0.2s ease;

  &:hover {
    background: hsl(var(--accent));
    border-color: hsl(var(--primary));
  }

  &.ant-btn-primary {
    background: hsl(var(--primary));
    border-color: hsl(var(--primary));
  }
}

.spatial-query-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  background: hsl(var(--card));
}

.query-area,
.query-result {
  width: 95%;
  padding: 16px;
  margin-top: 16px;
  border: 1px solid hsl(var(--border));
  border-radius: 4px;
  background: hsl(var(--card));
}



:deep(.ant-space) {
  .ant-space-item {
    .ant-btn {
      &:hover {
        .size-8 {
          color: hsl(var(--primary));
        }
      }
    }
  }
}
</style>
