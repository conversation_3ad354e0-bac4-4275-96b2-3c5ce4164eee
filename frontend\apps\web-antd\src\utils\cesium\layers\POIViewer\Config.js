import * as Cesium from 'cesium'
export default {
	markerStyle:{
		image: '/images/img2.png',
		width:24,
		height:24,
		verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
		horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
		//pixelOffset: new Cesium.Cartesian2(0, -20),
		eyeOffset:new Cesium.Cartesian3(0.0, 0.05, 0.0),
		heightReference:Cesium.HeightReference.CLAMP_TO_GROUND,
		//style: Cesium.LabelStyle.FILL_AND_OUTLINE,
		//outlineWidth: 12,
		//verticalOrigin: Cesium.VerticalOrigin.Button,
		//disableDepthTestDistance: 1,
		distanceDisplayCondition :new Cesium.DistanceDisplayCondition(0, 300000.0)
	},
	textStyle:{
		text :"名称",
		disableDepthTestDistance: Number.POSITIVE_INFINITY,
		verticalOrigin: Cesium.VerticalOrigin.BASELINE,
		horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
		pixelOffset: new Cesium.Cartesian2(0, -34),
		eyeOffset:new Cesium.Cartesian3(0.0, 0.01, 0.0),
		heightReference:Cesium.HeightReference.CLAMP_TO_GROUND,
		//showBackground: true, //指定标签后面背景的可见性
		//backgroundColor: new Cesium.Color(0, 0, 0, 0.3), // 背景颜色
		//backgroundPadding: new Cesium.Cartesian2(5, 5), //指定以像素为单位的水平和垂直背景填充padding
		distanceDisplayCondition :new Cesium.DistanceDisplayCondition(0, 100000.0),
		fillColor:Cesium.Color.BLACK,
		outlineColor: Cesium.Color.WHITE,
		outlineWidth: 2,
		style: Cesium.LabelStyle.FILL_AND_OUTLINE,
	}
}

