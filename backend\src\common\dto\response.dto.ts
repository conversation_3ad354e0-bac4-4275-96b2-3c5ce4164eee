import { ApiProperty } from '@nestjs/swagger';

export class ResponseDto<T = any> {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '状态码' })
  code: string;

  @ApiProperty({ description: '数据', required: false })
  data?: T;

  @ApiProperty({ description: '当前时间' })
  currentTime: string;

  constructor(success: boolean, code: string, data?: T) {
    this.success = success;
    this.code = code;
    this.data = data;
    this.currentTime = new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).replace(/\//g, '-');
  }

  static success<T>(data?: T, message = '操作成功'): ResponseDto<T> {
    return new ResponseDto(true, '00000', data);
  }

  static error(message = '操作失败', code = '50000'): ResponseDto {
    return new ResponseDto(false, code);
  }
}
