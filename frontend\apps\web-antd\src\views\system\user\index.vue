<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { Page } from '@vben/common-ui';
import { onMounted, ref } from 'vue';

import { useVbenModal, VbenButton, VbenSwitch, type VbenFormProps } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import UserModal from './components/UserModal.vue';
import OrganizationSidebar from '../organization/components/OrganizationSidebar.vue';
import {
  getUserList,
  deleteUser,
  updateUserStatus,
  batchDeleteUsers,
  batchUpdateStatus,
  resetUserPassword,
} from './user.api';
import {
  columns,
  searchFormSchema,
  getStatusLabel,
} from './user.data';
import { showConform } from '#/utils/alert.js';
import { message } from 'ant-design-vue';

// 组织筛选状态
const selectedOrganizationId = ref<number | null>(null);
const selectedOrganizationName = ref('全部组织');

const [CreateModal, createModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: UserModal,
  fullscreenButton: false,
  destroyOnClose: true,
  onClosed: () => {
    gridApi.query();
  }
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: searchFormSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  submitButtonOptions: {
    content: '查询',
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  },
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeGridProps = {
  columns,
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async (_: any, formValues: any) => {
        const queryParams = {
          ...formValues,
        };

        // 如果选择了组织，添加组织筛选条件
        if (selectedOrganizationId.value) {
          queryParams.departmentId = selectedOrganizationId.value;
        }

        const data = await getUserList(queryParams);

        return {
          total: data?.total || 0,
          items: data?.list || [],
        };
      },
    },
  },
  rowConfig: {
    isHover: true,
  },
  checkboxConfig: {
    reserve: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 新增事件
 */
const handleAdd = () => {
  createModalApi.setState({ departmentId: selectedOrganizationId.value });
  createModalApi.open();
};

/**
 * 编辑事件
 */
const handleEdit = (row: any) => {
  createModalApi.setState({ row });
  createModalApi.open();
};

/**
 * 删除事件
 */
async function handleDelete(record: any) {
  showConform('提示', '确定要删除该用户吗？删除后不可恢复！', async () => {
    try {
      await deleteUser(record.id);
      message.success('删除成功');
      onUpdate();
    } catch (error: any) {
      // message.error(error.message || '删除失败');
    }
  });
};

/**
 * 批量删除事件
 */
const handleBatchDelete = () => {
  const selectedRows = gridApi.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.warning('请选择要删除的用户');
    return;
  }

  showConform('提示', `确定要删除选中的 ${selectedRows.length} 个用户吗？删除后不可恢复！`, async () => {
    try {
      const ids = selectedRows.map(row => row.id);
      await batchDeleteUsers(ids);
      message.success('批量删除成功');
      onUpdate();
    } catch (error: any) {
      // message.error(error.message || '批量删除失败');
    }
  });
};

/**
 * 批量启用/禁用事件
 */
const handleBatchStatus = (status: number) => {
  const selectedRows = gridApi.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.warning('请选择要操作的用户');
    return;
  }

  const statusText = status === 1 ? '启用' : '禁用';
  showConform('提示', `确定要${statusText}选中的 ${selectedRows.length} 个用户吗？`, async () => {
    try {
      const ids = selectedRows.map(row => row.id);
      await batchUpdateStatus(ids, status);
      message.success(`批量${statusText}成功`);
      onUpdate();
    } catch (error: any) {
      // message.error(error.message || `批量${statusText}失败`);
    }
  });
};

/**
 * 重置密码事件
 */
const handleResetPassword = (record: any) => {
  showConform('提示', '确定要重置该用户的密码吗？重置后密码为：123456', async () => {
    try {
      await resetUserPassword(record.id, '123456');
      message.success('密码重置成功');
    } catch (error: any) {
      // message.error(error.message || '密码重置失败');
    }
  });
};

/**
 * 处理状态切换
 */
async function handleStatusChange(row: Record<string, any>, checked: boolean) {
  try {
    // 添加 loading 状态
    row.statusLoading = true;

    await updateUserStatus(row.id, checked ? 1 : 0);

    message.success(`${checked ? '启用' : '禁用'}成功`);

    // 更新本地数据
    row.status = checked ? 1 : 0;
  } catch (error: any) {
    // 如果失败，回退状态
    message.error(error.message || '操作失败');
  } finally {
    // 移除 loading 状态
    row.statusLoading = false;
  }
}

/**
 * 组织选择事件
 */
const handleOrganizationSelect = (organizationId: number | null, organizationName: string) => {
  selectedOrganizationId.value = organizationId;
  selectedOrganizationName.value = organizationName;

  // 重新查询数据
  gridApi.query();
};

/**
 * 更新事件
 */
const onUpdate = () => {
  gridApi.query();
};

// Fetch data on component mounted
onMounted(() => {
  // 初始化数据
});
</script>

<template>
  <Page auto-content-height>
    <div class="user-management">
      <!-- 左侧组织筛选侧边栏 -->
      <div class="sidebar-container">
        <OrganizationSidebar
          :selected-organization-id="selectedOrganizationId"
          @select="handleOrganizationSelect"
        />
      </div>

      <!-- 右侧用户管理内容 -->
      <div class="content-container">
        <!--引用表格-->
        <Grid>
          <!--插槽:table标题-->
          <template #toolbar-tools>
            <div class="flex items-center space-x-3">
              <VbenButton
                pre-icon="ant-design:plus-outlined"
                type="primary"
                @click="handleAdd()"
              >
                新增用户
              </VbenButton>
              <VbenButton
                pre-icon="ant-design:delete-outlined"
                type="default"
                @click="handleBatchDelete"
              >
                批量删除
              </VbenButton>
            </div>
          </template>

          <!-- 状态插槽 -->
          <template #status="{ row }">
            <VbenSwitch
              :checked="row.status === 1"
              :disabled="row.statusLoading"
              @update:checked="(checked) => handleStatusChange(row, checked)"
            >
              <template #label>
                {{ row.status === 1 ? '启用' : '禁用' }}
              </template>
            </VbenSwitch>
          </template>

          <!--操作栏-->
          <template #action="{ row }">
            <div class="actionBar">
              <a-button
                class="actionButton"
                type="link"
                @click="handleEdit(row)"
              >
                编辑
              </a-button>
              <a-button
                class="actionButton"
                type="link"
                @click="handleResetPassword(row)"
              >
                重置密码
              </a-button>
              <a-button
                class="actionButton"
                type="link"
                @click="handleDelete(row)"
              >
                删除
              </a-button>
            </div>
          </template>
        </Grid>

        <CreateModal @success="onUpdate" />
      </div>
    </div>
  </Page>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';

.user-management {
  display: flex;
  height: 100%;
  gap: 0;

  .sidebar-container {
    width: 280px;
    flex-shrink: 0;
    height: 100%;
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  }

  .content-container {
    flex: 1;
    margin-left: 16px;
    display: flex;
    flex-direction: column;
    min-width: 0; // 防止flex子项溢出
  }
}

.actionButton {
  padding: 6px;
  color: hsl(var(--primary));
}

.actionButton:hover {
  color: hsl(var(--primary));
}

.actionBar {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

// 响应式设计
@media (max-width: 1200px) {
  .user-management {
    .sidebar-container {
      width: 240px;
    }
  }
}

@media (max-width: 992px) {
  .user-management {
    flex-direction: column;

    .sidebar-container {
      width: 100%;
      height: 300px;
      margin-bottom: 16px;
    }

    .content-container {
      margin-left: 0;
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .user-management {
    .sidebar-container {
      background: #141414;
      box-shadow: 0 1px 2px 0 rgba(255, 255, 255, 0.03), 0 1px 6px -1px rgba(255, 255, 255, 0.02), 0 2px 4px 0 rgba(255, 255, 255, 0.02);
    }
  }
}
</style>
