import{ao as s,k as i,aP as a,l as t,ay as e,j as l}from"./chunks/framework.C8U7mBUf.js";const n=JSON.parse('{"title":"项目更新","description":"","frontmatter":{},"headers":[],"relativePath":"guide/other/project-update.md","filePath":"guide/other/project-update.md"}');const p=s({name:"guide/other/project-update.md"},[["render",function(s,n,p,h,k,d){const o=e("NolebaseGitContributors"),r=e("NolebaseGitChangelog");return l(),i("div",null,[n[0]||(n[0]=a('<h1 id="项目更新" tabindex="-1">项目更新 <a class="header-anchor" href="#项目更新" aria-label="Permalink to &quot;项目更新&quot;">​</a></h1><h2 id="为什么无法像-npm-插件一样更新" tabindex="-1">为什么无法像 npm 插件一样更新 <a class="header-anchor" href="#为什么无法像-npm-插件一样更新" aria-label="Permalink to &quot;为什么无法像 npm 插件一样更新&quot;">​</a></h2><p>因为项目是一个完整的项目模版，不是一个插件或者安装包，无法像插件一样更新，你使用代码后，会根据业务需求，进行二次开发，需要自行手动合并升级。</p><h2 id="我需要怎么做" tabindex="-1">我需要怎么做 <a class="header-anchor" href="#我需要怎么做" aria-label="Permalink to &quot;我需要怎么做&quot;">​</a></h2><p>项目采用了 <code>Monorepo</code> 的方式进行管理，并将一些比较核心的代码进行了抽离，比如 <code>packages/@core</code>、<code>packages/effects</code>，只要业务代码没有修改这部分代码，那么你可以直接拉取最新代码，然后合并到你的分支上，只需要简单的处理部分冲突即可。其余文件夹只会进行一些小的调整，不会对业务代码产生影响。</p><div class="tip custom-block"><p class="custom-block-title">推荐</p><p>建议关注仓库动态，积极去合并，不要长时间积累，否则将会导致合并冲突过多，增加合并难度。</p></div><h2 id="使用-git-更新代码" tabindex="-1">使用 Git 更新代码 <a class="header-anchor" href="#使用-git-更新代码" aria-label="Permalink to &quot;使用 Git 更新代码&quot;">​</a></h2><ol><li>克隆代码</li></ol><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> clone</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> https://github.com/vbenjs/vue-vben-admin.git</span></span></code></pre></div><ol start="2"><li>添加自己的公司 git 源地址</li></ol><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># up 为源名称,可以随意设置</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># gitUrl为开源最新代码</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> remote</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> add</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> up</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> gitUrl</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span></code></pre></div><ol start="3"><li>提交代码到自己公司 git</li></ol><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 提交代码到自己公司</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># main为分支名 需要自行根据情况修改</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> push</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> up</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> main</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 同步公司的代码</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># main为分支名 需要自行根据情况修改</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> pull</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> up</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> main</span></span></code></pre></div><ol start="4"><li>如何同步开源最新代码</li></ol><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> pull</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> origin</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> main</span></span></code></pre></div><div class="tip custom-block"><p class="custom-block-title">提示</p><p>同步代码的时候会出现冲突。只需要把冲突解决即可</p></div>',16)),t(o),t(r)])}]]);export{n as __pageData,p as default};
