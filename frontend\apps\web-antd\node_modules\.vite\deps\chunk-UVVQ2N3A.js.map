{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/table/src/column.js"], "sourcesContent": ["import { defineComponent, h, onUnmounted, inject, ref, provide, onMounted, createCommentVNode } from 'vue';\nimport { watchColumn, assembleColumn, destroyColumn } from './util';\nimport Cell from './cell';\nexport const columnProps = {\n    // 列唯一主键\n    colId: [String, Number],\n    // 渲染类型 index,radio,checkbox,expand,html\n    type: String,\n    // 列字段名\n    field: String,\n    // 列标题\n    title: String,\n    // 列宽度\n    width: [Number, String],\n    // 列最小宽度，把剩余宽度按比例分配\n    minWidth: [Number, String],\n    // 列最大宽度\n    maxWidth: [Number, String],\n    // 是否允许拖动列宽调整大小\n    resizable: {\n        type: Boolean,\n        default: null\n    },\n    // 将列固定在左侧或者右侧\n    fixed: String,\n    // 列对其方式\n    align: String,\n    // 表头对齐方式\n    headerAlign: String,\n    // 表尾列的对齐方式\n    footerAlign: String,\n    // 当内容过长时显示为省略号\n    showOverflow: {\n        type: [Boolean, String],\n        default: null\n    },\n    // 当表头内容过长时显示为省略号\n    showHeaderOverflow: {\n        type: [Boolean, String],\n        default: null\n    },\n    // 当表尾内容过长时显示为省略号\n    showFooterOverflow: {\n        type: [Boolean, String],\n        default: null\n    },\n    // 给单元格附加 className\n    className: [String, Function],\n    // 给表头单元格附加 className\n    headerClassName: [String, Function],\n    // 给表尾单元格附加 className\n    footerClassName: [String, Function],\n    // 格式化显示内容\n    formatter: [Function, Array, String],\n    // 格式化表尾显示内容\n    footerFormatter: [Function, Array, String],\n    // 是否显示间距\n    padding: {\n        type: Boolean,\n        default: null\n    },\n    // 垂直对齐方式\n    verticalAlign: {\n        type: String,\n        default: null\n    },\n    // 是否允许排序\n    sortable: Boolean,\n    // 自定义排序的属性\n    sortBy: [String, Function],\n    // 排序的字段类型，比如字符串转数值等\n    sortType: String,\n    // 配置筛选条件数组\n    filters: {\n        type: Array,\n        default: null\n    },\n    // 筛选是否允许多选\n    filterMultiple: {\n        type: Boolean,\n        default: true\n    },\n    // 自定义筛选方法\n    filterMethod: Function,\n    // 筛选重置方法\n    filterResetMethod: Function,\n    // 筛选复原方法\n    filterRecoverMethod: Function,\n    // 筛选模板配置项\n    filterRender: Object,\n    // 设置为分组节点\n    rowGroupNode: Boolean,\n    // 设置为树节点\n    treeNode: Boolean,\n    // 设置为拖拽排序\n    dragSort: Boolean,\n    // 设置为行高拖拽\n    rowResize: Boolean,\n    // 是否可视\n    visible: {\n        type: Boolean,\n        default: null\n    },\n    // 表头单元格数据导出方法\n    headerExportMethod: Function,\n    // 单元格数据导出方法\n    exportMethod: Function,\n    // 表尾单元格数据导出方法\n    footerExportMethod: Function,\n    // 已废弃，被 titlePrefix 替换\n    titleHelp: Object,\n    // 标题前缀图标配置项\n    titlePrefix: Object,\n    // 标题后缀图标配置项\n    titleSuffix: Object,\n    // 单元格值类型\n    cellType: String,\n    // 单元格渲染配置项\n    cellRender: Object,\n    // 单元格编辑渲染配置项\n    editRender: Object,\n    // 内容渲染配置项\n    contentRender: Object,\n    // 额外的参数\n    params: Object\n};\nexport default defineComponent({\n    name: 'VxeColumn',\n    props: columnProps,\n    setup(props, { slots }) {\n        const refElem = ref();\n        const $xeTable = inject('$xeTable', null);\n        const $xeColgroup = inject('$xeColgroup', null);\n        if (!$xeTable) {\n            return () => createCommentVNode();\n        }\n        const columnConfig = Cell.createColumn($xeTable, props);\n        columnConfig.slots = slots;\n        const renderVN = () => {\n            return h('div', {\n                ref: refElem\n            });\n        };\n        const $xeColumn = {\n            columnConfig,\n            renderVN\n        };\n        watchColumn($xeTable, props, columnConfig);\n        onMounted(() => {\n            const elem = refElem.value;\n            if (elem) {\n                assembleColumn($xeTable, elem, columnConfig, $xeColgroup);\n            }\n        });\n        onUnmounted(() => {\n            destroyColumn($xeTable, columnConfig);\n        });\n        provide('$xeColumn', $xeColumn);\n        provide('$xeGrid', null);\n        return renderVN;\n    }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAGO,IAAM,cAAc;AAAA;AAAA,EAEvB,OAAO,CAAC,QAAQ,MAAM;AAAA;AAAA,EAEtB,MAAM;AAAA;AAAA,EAEN,OAAO;AAAA;AAAA,EAEP,OAAO;AAAA;AAAA,EAEP,OAAO,CAAC,QAAQ,MAAM;AAAA;AAAA,EAEtB,UAAU,CAAC,QAAQ,MAAM;AAAA;AAAA,EAEzB,UAAU,CAAC,QAAQ,MAAM;AAAA;AAAA,EAEzB,WAAW;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AAAA;AAAA,EAEA,OAAO;AAAA;AAAA,EAEP,OAAO;AAAA;AAAA,EAEP,aAAa;AAAA;AAAA,EAEb,aAAa;AAAA;AAAA,EAEb,cAAc;AAAA,IACV,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS;AAAA,EACb;AAAA;AAAA,EAEA,oBAAoB;AAAA,IAChB,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS;AAAA,EACb;AAAA;AAAA,EAEA,oBAAoB;AAAA,IAChB,MAAM,CAAC,SAAS,MAAM;AAAA,IACtB,SAAS;AAAA,EACb;AAAA;AAAA,EAEA,WAAW,CAAC,QAAQ,QAAQ;AAAA;AAAA,EAE5B,iBAAiB,CAAC,QAAQ,QAAQ;AAAA;AAAA,EAElC,iBAAiB,CAAC,QAAQ,QAAQ;AAAA;AAAA,EAElC,WAAW,CAAC,UAAU,OAAO,MAAM;AAAA;AAAA,EAEnC,iBAAiB,CAAC,UAAU,OAAO,MAAM;AAAA;AAAA,EAEzC,SAAS;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AAAA;AAAA,EAEA,eAAe;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AAAA;AAAA,EAEA,UAAU;AAAA;AAAA,EAEV,QAAQ,CAAC,QAAQ,QAAQ;AAAA;AAAA,EAEzB,UAAU;AAAA;AAAA,EAEV,SAAS;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AAAA;AAAA,EAEA,gBAAgB;AAAA,IACZ,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AAAA;AAAA,EAEA,cAAc;AAAA;AAAA,EAEd,mBAAmB;AAAA;AAAA,EAEnB,qBAAqB;AAAA;AAAA,EAErB,cAAc;AAAA;AAAA,EAEd,cAAc;AAAA;AAAA,EAEd,UAAU;AAAA;AAAA,EAEV,UAAU;AAAA;AAAA,EAEV,WAAW;AAAA;AAAA,EAEX,SAAS;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AAAA;AAAA,EAEA,oBAAoB;AAAA;AAAA,EAEpB,cAAc;AAAA;AAAA,EAEd,oBAAoB;AAAA;AAAA,EAEpB,WAAW;AAAA;AAAA,EAEX,aAAa;AAAA;AAAA,EAEb,aAAa;AAAA;AAAA,EAEb,UAAU;AAAA;AAAA,EAEV,YAAY;AAAA;AAAA,EAEZ,YAAY;AAAA;AAAA,EAEZ,eAAe;AAAA;AAAA,EAEf,QAAQ;AACZ;AACA,IAAO,iBAAQ,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM,OAAO,EAAE,MAAM,GAAG;AACpB,UAAM,UAAU,IAAI;AACpB,UAAM,WAAW,OAAO,YAAY,IAAI;AACxC,UAAM,cAAc,OAAO,eAAe,IAAI;AAC9C,QAAI,CAAC,UAAU;AACX,aAAO,MAAM,mBAAmB;AAAA,IACpC;AACA,UAAM,eAAe,aAAK,aAAa,UAAU,KAAK;AACtD,iBAAa,QAAQ;AACrB,UAAM,WAAW,MAAM;AACnB,aAAO,EAAE,OAAO;AAAA,QACZ,KAAK;AAAA,MACT,CAAC;AAAA,IACL;AACA,UAAM,YAAY;AAAA,MACd;AAAA,MACA;AAAA,IACJ;AACA,gBAAY,UAAU,OAAO,YAAY;AACzC,cAAU,MAAM;AACZ,YAAM,OAAO,QAAQ;AACrB,UAAI,MAAM;AACN,uBAAe,UAAU,MAAM,cAAc,WAAW;AAAA,MAC5D;AAAA,IACJ,CAAC;AACD,gBAAY,MAAM;AACd,oBAAc,UAAU,YAAY;AAAA,IACxC,CAAC;AACD,YAAQ,aAAa,SAAS;AAC9B,YAAQ,WAAW,IAAI;AACvB,WAAO;AAAA,EACX;AACJ,CAAC;", "names": []}