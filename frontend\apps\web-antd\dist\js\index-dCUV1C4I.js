import p from"./Dashboard-SMthZzgV.js";import{o as m,x as t,j as n,b as i,q as o,s as r}from"../jse/index-index-DyHD_jbN.js";import"./bootstrap-5OPUVRWy.js";import"./vxe-table-CZ9gPHn5.js";import"./loading-DzjUKA94.js";import"./form-DdFfsSWf.js";import"./SpaceQuery-BLePLjaE.js";import"./index-D4Q7xmlJ.js";import"./Base-xeJpkIWP.js";import"./index-BYvs451p.js";import"./util-BadkgFi3.js";import"./scene.data-BMXeOdST.js";import"./entity.data-u4HDUExc.js";import"./toast-CQjPPeQ1.js";import"./MapViewer-CBAv8z2T.js";const w={__name:"index",setup(s){return m(()=>{}),(_,c)=>{const e=t("a-layout-content"),a=t("a-layout");return i(),n(a,{style:{height:"88.5vh"}},{default:o(()=>[r(e,null,{default:o(()=>[r(p)]),_:1})]),_:1})}}};export{w as default};
