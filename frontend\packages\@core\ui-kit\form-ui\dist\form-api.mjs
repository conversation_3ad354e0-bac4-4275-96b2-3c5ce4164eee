import { toRaw } from "vue";
import { Store } from "@vben-core/shared/store";
import {
  bindMethods,
  isFunction,
  mergeWithArrayOverride,
  StateHandler
} from "@vben-core/shared/utils";
import { objectPick } from "@vueuse/core";
function getDefaultState() {
  return {
    actionWrapperClass: "",
    collapsed: false,
    collapsedRows: 1,
    collapseTriggerResize: false,
    commonConfig: {},
    handleReset: void 0,
    handleSubmit: void 0,
    handleValuesChange: void 0,
    layout: "horizontal",
    resetButtonOptions: {},
    schema: [],
    showCollapseButton: false,
    showDefaultActions: true,
    submitButtonOptions: {},
    submitOnEnter: false,
    wrapperClass: "grid-cols-1"
  };
}
export class FormApi {
  // 最后一次点击提交时的表单值
  latestSubmissionValues = null;
  prevState = null;
  // private api: Pick<VbenFormProps, 'handleReset' | 'handleSubmit'>;
  form = {};
  isMounted = false;
  state = null;
  stateHandler;
  store;
  constructor(options = {}) {
    const { ...storeState } = options;
    const defaultState = getDefaultState();
    this.store = new Store(
      {
        ...defaultState,
        ...storeState
      },
      {
        onUpdate: () => {
          this.prevState = this.state;
          this.state = this.store.state;
          this.updateState();
        }
      }
    );
    this.state = this.store.state;
    this.stateHandler = new StateHandler();
    bindMethods(this);
  }
  async getForm() {
    if (!this.isMounted) {
      await this.stateHandler.waitForCondition();
    }
    if (!this.form?.meta) {
      throw new Error("<VbenForm /> is not mounted");
    }
    return this.form;
  }
  updateState() {
    const currentSchema = this.state?.schema ?? [];
    const prevSchema = this.prevState?.schema ?? [];
    if (currentSchema.length < prevSchema.length) {
      const currentFields = new Set(
        currentSchema.map((item) => item.fieldName)
      );
      const deletedSchema = prevSchema.filter(
        (item) => !currentFields.has(item.fieldName)
      );
      for (const schema of deletedSchema) {
        this.form?.setFieldValue(schema.fieldName, void 0);
      }
    }
  }
  // 如果需要多次更新状态，可以使用 batch 方法
  batchStore(cb) {
    this.store.batch(cb);
  }
  getLatestSubmissionValues() {
    return this.latestSubmissionValues || {};
  }
  getState() {
    return this.state;
  }
  async getValues() {
    const form = await this.getForm();
    return form.values;
  }
  merge(formApi) {
    const chain = [this, formApi];
    const proxy = new Proxy(formApi, {
      get(target, prop) {
        if (prop === "merge") {
          return (nextFormApi) => {
            chain.push(nextFormApi);
            return proxy;
          };
        }
        if (prop === "submitAllForm") {
          return async (needMerge = true) => {
            try {
              const results = await Promise.all(
                chain.map(async (api) => {
                  const form = await api.getForm();
                  const validateResult = await api.validate();
                  if (!validateResult.valid) {
                    return;
                  }
                  const rawValues = toRaw(form.values || {});
                  return rawValues;
                })
              );
              if (needMerge) {
                const mergedResults = Object.assign({}, ...results);
                return mergedResults;
              }
              return results;
            } catch (error) {
              console.error("Validation error:", error);
            }
          };
        }
        return target[prop];
      }
    });
    return proxy;
  }
  mount(formActions) {
    if (!this.isMounted) {
      Object.assign(this.form, formActions);
      this.stateHandler.setConditionTrue();
      this.setLatestSubmissionValues({ ...toRaw(this.form.values) });
      this.isMounted = true;
    }
  }
  /**
   * 根据字段名移除表单项
   * @param fields
   */
  async removeSchemaByFields(fields) {
    const fieldSet = new Set(fields);
    const schema = this.state?.schema ?? [];
    const filterSchema = schema.filter((item) => fieldSet.has(item.fieldName));
    this.setState({
      schema: filterSchema
    });
  }
  /**
   * 重置表单
   */
  async resetForm(state, opts) {
    const form = await this.getForm();
    return form.resetForm(state, opts);
  }
  async resetValidate() {
    const form = await this.getForm();
    const fields = Object.keys(form.errors.value);
    fields.forEach((field) => {
      form.setFieldError(field, void 0);
    });
  }
  async setFieldValue(field, value, shouldValidate) {
    const form = await this.getForm();
    form.setFieldValue(field, value, shouldValidate);
  }
  setLatestSubmissionValues(values) {
    this.latestSubmissionValues = { ...toRaw(values) };
  }
  setState(stateOrFn) {
    if (isFunction(stateOrFn)) {
      this.store.setState((prev) => {
        return mergeWithArrayOverride(stateOrFn(prev), prev);
      });
    } else {
      this.store.setState((prev) => mergeWithArrayOverride(stateOrFn, prev));
    }
  }
  /**
   * 设置表单值
   * @param fields record
   * @param filterFields 过滤不在schema中定义的字段 默认为true
   * @param shouldValidate
   */
  async setValues(fields, filterFields = true, shouldValidate = false) {
    const form = await this.getForm();
    if (!filterFields) {
      form.setValues(fields, shouldValidate);
      return;
    }
    const fieldNames = this.state?.schema?.map((item) => item.fieldName) ?? [];
    const filteredFields = objectPick(fields, fieldNames);
    form.setValues(filteredFields, shouldValidate);
  }
  async submitForm(e) {
    e?.preventDefault();
    e?.stopPropagation();
    const form = await this.getForm();
    await form.submitForm();
    const rawValues = toRaw(form.values || {});
    await this.state?.handleSubmit?.(rawValues);
    return rawValues;
  }
  unmount() {
    this.form?.resetForm?.();
    this.latestSubmissionValues = null;
    this.isMounted = false;
    this.stateHandler.reset();
  }
  updateSchema(schema) {
    const updated = [...schema];
    const hasField = updated.every(
      (item) => Reflect.has(item, "fieldName") && item.fieldName
    );
    if (!hasField) {
      console.error(
        "All items in the schema array must have a valid `fieldName` property to be updated"
      );
      return;
    }
    const currentSchema = [...this.state?.schema ?? []];
    const updatedMap = {};
    updated.forEach((item) => {
      if (item.fieldName) {
        updatedMap[item.fieldName] = item;
      }
    });
    currentSchema.forEach((schema2, index) => {
      const updatedData = updatedMap[schema2.fieldName];
      if (updatedData) {
        currentSchema[index] = mergeWithArrayOverride(
          updatedData,
          schema2
        );
      }
    });
    this.setState({ schema: currentSchema });
  }
  async validate(opts) {
    const form = await this.getForm();
    const validateResult = await form.validate(opts);
    if (Object.keys(validateResult?.errors ?? {}).length > 0) {
      console.error("validate error", validateResult?.errors);
    }
    return validateResult;
  }
  async validateAndSubmitForm() {
    const form = await this.getForm();
    const { valid } = await form.validate();
    if (!valid) {
      return;
    }
    return await this.submitForm();
  }
}
