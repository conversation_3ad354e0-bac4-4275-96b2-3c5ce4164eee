/**
 * 格式化字符串
 * @param {String} str
 * @param  {...any|Array} values
 * @returns {String}
 * @example String.format('Hello. My name is {0} {1}.', firstName, lastName);
 */
String.format = function (str, ...values) {
  str = str || '';
  values = values || [];
  if (values.length == 1 && values[0] instanceof Array) {
    values = values[0];
  }

  for (var i = 0; i < values.length; i++) {
    var re = new RegExp('\\{' + i + '\\}', 'gm');
    str = str.replace(re, values[i]);
  }
  return str;
};
String.prototype.format = function (...values) {
  return String.format(this, values);
};
/**
 *删除左右两端的空格
 */
String.prototype.trim = function () {
  return this.replace(/(^\s*)|(\s*$)/g, '');
};
/**
 *删除左边的空格
 */
String.prototype.ltrim = function () {
  return this.replace(/(^\s*)/g, '');
};
/**
 *删除右边的空格
 */
String.prototype.rtrim = function () {
  return this.replace(/(\s*$)/g, '');
};
/**
 * 把字符串转换成日期
 * @returns 转换成的日期
 */
String.prototype.toDate = function () {
  return new Date(this);
};

/**
 * 首字母大写
 * @example let str = "follow for more"; str.capitalize() //Follow for more
 */
String.prototype.capitalize = () =>
  this.charAt(0).toUpperCase() + this.slice(1);
