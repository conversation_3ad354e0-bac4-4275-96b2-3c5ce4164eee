<script lang="ts">
import { defineComponent, reactive, ref, watchEffect } from 'vue';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

import { cloneDeep } from 'lodash-es';

export default defineComponent({
  name: 'Bar',
  components: { EchartsUI },
  props: {
    chartData: {
      type: Object,
      default: () => {},
    },
    option: {
      type: Object,
      default: () => ({}),
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: 'calc(100vh - 78px)',
    },
    // update-begin--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
    seriesColor: {
      type: String,
      default: '#1890ff',
    },
    // update-end--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
  },
  setup(props) {
    const chartRef = ref<EchartsUIType>();
    const { renderEcharts } = useEcharts(chartRef);
    const option = reactive({
      tooltip: {
        formatter: '{b} ({c})',
      },
      legend: {
        // 底部分类标签
        show: true,
        bottom: 0,
      },
      grid: {
        bottom: '20%',
        // 其他可选属性：left, right, bottom, width, height, containLabel
      },
      series: [
        {
          type: 'pie',
          radius: '70%',
          center: ['50%', '40%'],
          data: [],
          labelLine: { show: true },
          label: {
            show: true,
            formatter: '{b} \n ({d}%)',
            color: '#B1B9D3',
          },
        },
      ],
    });

    watchEffect(() => {
      props.chartData && initCharts();
    });

    function initCharts() {
      if (props.option) {
        Object.assign(option, cloneDeep(props.option));
      }
      option.series[0].data = props.chartData;
      renderEcharts(option);
      // resize();
      // getInstance()?.off('click', onClick);
      // getInstance()?.on('click', onClick);
    }
    return { chartRef };
  },
});
</script>
<template>
  <EchartsUI ref="chartRef" />
</template>
