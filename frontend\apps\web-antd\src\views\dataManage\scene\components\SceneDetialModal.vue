<script setup>
import { onMounted, reactive, ref, watch } from "vue";

import { message } from 'ant-design-vue';

import {
  getLayerDatas,
  getLayers,
  getMetadata, getPreview3dDatas, getPreviewRasterDatas
} from "#/views/dataManage/scene/scene.api";
import { getUploadStatusLabel } from "#/views/dataManage/scene/scene.data";
import LayerPreview from "#/views/dataManage/components/LayerPreview.vue";
import { useAppConfig } from "@vben/hooks";
import { appendTokenToUrl } from "#/views/common/util.ts";
import { useVbenModal, VbenButton, Tabs, TabsList, TabsTrigger, TabsContent } from '@vben/common-ui';
import ImageWithFallback from "#/views/dataManage/components/ImageWithFallback.vue";

const emit = defineEmits(['handleCancel']);
let row = null;
const defaultSelectedKeys = ref([]);

const [Modal, modalApi] = useVbenModal({
  closeOnClickModal:false,
  onCancel() {
    modalApi.close();
  },
  onConfirm() {},
  onOpenChange(isOpen) {
    if (isOpen) {
      clearData();
      const state = modalApi.useStore();
      row = state.value.row;
      loadDatas();
      return;
    }
    clearData();
  },
});

const isThree = (dataType) => {
  return  dataType === 'osgb' || dataType === 'tiles'  || dataType === 'obj'|| dataType === 'point-cloud';
}

let layerTree = ref([
  {
    title: '包含图层',
    key: '',
    children: [
    ],
  },
]);

const loadLayerData = async (dataType,layerId,layerUrl) => {
  //图层数据
  let res = await getLayerDatas(dataType,layerId);
  if(res && res.metadata) {
    Object.assign(spaceData, res.metadata);
  }
  //预览数据
  if(isThree(dataType)) {
    const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);
    let alayerUrl = layerUrl ? layerUrl :  `${apiURL}/layer-data/preview/3d/${layerId}/tileset.json`;
    previewData.serviceUrl = alayerUrl;
    return;
  }

  if(dataType === 'dom' || dataType === 'dem') {
    const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);
    let layerUrl = `${apiURL}/layer-data/preview/raster/${layerId}`;
    previewData.imageUrl = layerUrl;
  }

}

const onCheck = (checkedKeys, info) => {
  console.log('checkedKeys:', checkedKeys, 'info:', info);
  defaultSelectedKeys.value = checkedKeys;
  if(checkedKeys[0] && checkedKeys[0].length > 0) {
    previewData.layerUrl =  info.node.dataRef.layerUrl;
    loadLayerData(row.dataType,checkedKeys[0],info.node.dataRef.layerUrl);
  }
};

// 控制模态框的显示与隐藏
const activeTab = ref('1');

// 初始化元数据
let metadata = reactive({
  name: '',
  category: '',
  dataType: '',
  timeCreated: '',
  regionCode: '',
  dataSize: '',
  provider: '',
  uploadStatus: '',
  resolution: '',
  range: '',
  area: '',
  coordinateSystem: '',
  timeUpdated:'',
});

let spaceData = reactive({
  id: "",
  timeCreated: "",
  threeDimensionsInfoId: "",
  name: "",
  minX: 0,
  minY: 0,
  minZ:0,
  maxX: 0,
  maxY: 0,
  maxZ: 0,
  width: 0,
  height: 0,
  depth: 0,
  geometricError: 0,
  area: 0,
  epsg: 0,
  spatialRef: "0",
  level: 0
})

// 初始化预览数据
let previewData = reactive({
  serviceUrl: '',
  imageUrl: '',
  layerUrl:'',
});

const clearData = () =>{
  spaceData = reactive({
    id: "",
    timeCreated: "",
    threeDimensionsInfoId: "",
    name: "",
    minX: 0,
    minY: 0,
    minZ:0,
    maxX: 0,
    maxY: 0,
    maxZ: 0,
    width: 0,
    height: 0,
    depth: 0,
    geometricError: 0,
    area: 0,
    epsg: 0,
    spatialRef: "0",
    level: 0
  })

  previewData = reactive({
    serviceUrl: '',
    imageUrl: '',
    layerUrl:'',
  });

  metadata = reactive({
    name: '',
    category: '',
    dataType: '',
    timeCreated: '',
    regionCode: '',
    dataSize: '',
    provider: '',
    uploadStatus: '',
    resolution: '',
    range: '',
    area: '',
    coordinateSystem: '',
    timeUpdated:'',
  });

  layerTree = ref([
    {
      title: '包含图层',
      key: '',
      children: [
      ],
    },
  ]);
}


const copyToClipboard = () => {
  navigator.clipboard
    .writeText(previewData.serviceUrl)
    .then(() => {
      message.success('服务地址复制成功');
    })
    .catch((error) => {
      message.error('复制失败');
    });
};

// 初始化日志数据
const logData = reactive({
  message: '',
});

const loadDatas = () => {
  clearData();
  getMetadata(row.dataType,row.id).then((data) => {
    Object.assign(metadata, data);
  });
  getLayers(row.dataType,row.id).then((data) => {
    layerTree.value[0].children = [];
    if(data != null && data.length > 0) {
      data.forEach((item) => {
        layerTree.value[0].children.push({
          title: item.layerName,
          key: item.layerId,
          layerUrl:item.layerUrl
        });
      });

      let firstLayerId = data[0].layerId;
      defaultSelectedKeys.value = [firstLayerId];
      previewData.layerUrl =  data[0].layerUrl;
      loadLayerData(row.dataType,firstLayerId);
    }
  });
}

// 模拟API请求
onMounted(() => {

});

// 关闭模态框
const handleCancel = () => {
  emit('onCancel', {});
};

const getTimeYear = (dateStr) => {
  const formattedDateStr = dateStr.replace(" ", "T"); // 将空格替换为 T 以符合 ISO 8601 格式
  const date = new Date(formattedDateStr);
// 获取年、月、日
  return date.getFullYear();
}
</script>

<template>
  <Modal
    title="数据集详情"
    class="w-[800px]"
    @cancel="handleCancel"
    destroy-on-close
    :maskClosable="false"
  >
    <view>
    <view class="metadata-section">
      <view class="bg-gray-200 dark:bg-gray-800 itemTitle">基本信息</view>
      <view class="metadata-section">
      <view class="smetadata-row">
        <span>数据集名称：{{ metadata.name }}</span>
        <span>数据情况：{{metadata.dataSituation === 1?'正式数据' : row.dataSituation == null ? '' : '过程数据'}}</span>
        <span>数据类型：{{ metadata.dataType }}</span>
      </view>
      <view class="smetadata-row">
        <span>行政区划：{{ metadata.regionProvinceName }}{{ metadata.regionCityName }}{{ metadata.regionCountyName }}</span>
        <span>数据年份：{{ getTimeYear(metadata.timeCreated) }}</span>
        <span>文件格式：{{ metadata.fileFormat }}</span>
      </view>
      <view class="smetadata-row">
        <span>面积(平方米）：{{ metadata.area && metadata.area.toFixed(2) }}</span>
        <span>数据量：{{ metadata.dataSize }}</span>
        <span>数据来源：{{ metadata.provider }}</span>
      </view>
      <view class="smetadata-row">
        <span>创建人：{{ metadata.userNameCreated }}</span>
        <span>创建时间：{{ metadata.timeCreated }}</span>
        <span>状态：{{ getUploadStatusLabel(metadata) }}</span>
      </view>
        <view class="smetadata-row">
          <span>更新人：{{ metadata.userNameUpdated }}</span>
          <span>更新时间：{{ metadata.timeUpdated }}</span>
        </view>
      </view>
    </view>
    <view class="bg-gray-200 dark:bg-gray-800 itemTitle">图层信息</view>
    <view class="metadata-row">
      <view class="layerTree">
        <a-tree
          :defaultExpandAll="true"
          :treeData="layerTree"
          :checkable="false"
          :checkStrictly="true"
          @select="onCheck"
          :selectedKeys="defaultSelectedKeys"
        />
      </view>


      <Tabs v-model="activeTab" class="tabClass">
        <TabsList>
          <TabsTrigger value="1">元数据</TabsTrigger>
          <TabsTrigger value="2">预览</TabsTrigger>
        </TabsList>
        <!-- 元数据 -->
        <TabsContent value="1" class="pannelArea">
          <view v-if="metadata.uploadStatus == 1" class="metadata-section">
            <view class="spacedata-row">
              <view>图层名称： {{ spaceData.name }}</view>
            </view>
            <view class="spacedata-row">
              <view>坐标系： {{ spaceData.spatialRef }}</view>
              <view>EPSG： {{ spaceData.epsg }}</view>
            </view>
            <view v-if="isThree(metadata.dataType)" class="spacedata-row">
              <div>原点坐标： {{ spaceData.originX && spaceData.originX.toFixed(2) }}，{{ spaceData.originY && spaceData.originY.toFixed(2)}}，{{ spaceData.originZ && spaceData.originZ.toFixed(2)}}</div>
            </view>
            <view v-if="isThree(metadata.dataType)" class="spacedata-row">
              <view>面积(平方米）： {{ spaceData.area && spaceData.area.toFixed(2)}}</view>
<!--              <view>误差： {{ spaceData.geometricError }}</view>-->
            </view>
<!--            <view v-if="isThree(metadata.dataType)" class="spacedata-row">-->
<!--              <div>长宽高(米）： {{ spaceData.width }}，{{ spaceData.height }}，{{ spaceData.depth }}</div>-->
<!--            </view>-->
            <view  v-if="!isThree(metadata.dataType)" class="spacedata-row">
              <view>分辨率：{{ spaceData.pixelWidth }}{{ spaceData.pixelWidth && 'x' }}{{ spaceData.pixelHeight }}</view>
              <view>单位： {{ spaceData.unit }}</view>
            </view>
            <view v-if="!isThree(metadata.dataType)" class="spacedata-row">
              <view>面积(平方米)： {{ spaceData.area && spaceData.area.toFixed(2)}}</view>
              <view>高程范围：{{ spaceData.minValue }}{{ spaceData.maxValue && '-' }}{{ spaceData.maxValue }}</view>
            </view>
            <view class="spacedata-row">
              <div>范围：  {{ spaceData.minX && spaceData.minX.toFixed(2) }},{{ spaceData.minY && spaceData.minY.toFixed(2) }},{{ spaceData.minZ && spaceData.minZ.toFixed(2) }} -
                {{ spaceData.maxX && spaceData.maxX.toFixed(2) }},{{ spaceData.maxY && spaceData.maxY.toFixed(2) }},{{ spaceData.maxZ &&  spaceData.maxZ.toFixed(2) }}</div>
            </view>
          </view>
        </TabsContent>
        <!-- 预览 -->
        <TabsContent value="2" class="pannelArea">
          <div class="truncate-text">服务地址: {{ previewData.layerUrl }}</div>
          <a-flex v-if="previewData.imageUrl" align="center" justify="start" style="width: 100%;">
            <ImageWithFallback
              :src="appendTokenToUrl(previewData.imageUrl)"
              :width="450"
              :height="280"
            />
          </a-flex>
          <view v-if="previewData.serviceUrl" style="width:100%;height: 400px">
            <LayerPreview :layerUrl="previewData.serviceUrl"></LayerPreview>
          </view>
        </TabsContent>
      </Tabs>
    </view>

    </view>
    <template #footer>
      <div class="border-t border-gray-300" style="margin-bottom: 10px"></div>
      <VbenButton type="primary" @click="handleCancel">确定</VbenButton>
    </template>
  </Modal>
</template>

<style lang="less"  scoped>
@import '#/styles/dark-antd.less';

// Tree 组件样式
 :deep(.ant-tree .ant-tree-treenode) {
    margin-left: 0 !important;
    padding-left: 0 !important;
  }

  :deep(.ant-tree-indent) {
    width: 0px;
  }

.treeList {
  height: 100px;
  max-height: 100px;
  overflow-y: auto;
}

.pannelArea {
  height: 330px;
  background: transparent;
}

.itemTitle {
  display: flex;
  width: 100%;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
  margin-top: 5px;
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 5px;
}

.sitemTitle {
  display: flex;
  width: 100%;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
  padding-top: 5px;
  padding-bottom: 5px;
}

/* 元数据区域的样式 */
.metadata-section {
  margin-bottom: 10px;
  border-radius: 8px; /* 添加圆角效果 */
}

.metadata-row {
  display: flex;
  justify-content: flex-start;
}

.metadata-row span  {
  margin-right: 5px;
  width: 280px;
  margin-top:5px;
}

.smetadata-row {
  display: flex;
  font-size: 14px;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.smetadata-row span  {
  width: 250px;
  margin-top:5px;
  margin-left: 5px;
}

.spacedata-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.spacedata-row view:first-child {
  width: 330px;
}

.layerTree {
  width: 250px;
  overflow-y: auto;
  max-height: 400px; /* 设置最大高度 */
  margin-right: 10px;
  margin-top: 10px;
}

.tabClass {
  width: 97%;
  margin-top: 0px;
  background: transparent;
}

.metadata-row span:first-child {
  width: 250px;
}

/* 模态框内容的整体布局 */
a-modal {
  padding: 20px; /* 模态框内部内容增加填充空间 */
}

/* 预览图片区域样式 */
a-image {
  margin-top: 10px; /* 与上方标题拉开距离 */
  border: 1px solid #d9d9d9; /* 为预览图片添加边框 */
  border-radius: 4px; /* 添加圆角 */
}

/* 日志信息部分的样式 */
a-tab-pane {
  padding: 20px; /* 各个Tab Pane内部增加填充空间 */
}

.truncate-text {
  max-width: 550px; /* 设置最大宽度 */
  word-wrap: break-word; /* 自动换行 */
  overflow-wrap: break-word; /* 兼容旧浏览器 */
  margin-bottom: 10px;
}

</style>
