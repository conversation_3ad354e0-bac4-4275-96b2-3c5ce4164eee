var I=Object.defineProperty;var F=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var S=(o,a,e)=>a in o?I(o,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[a]=e,T=(o,a)=>{for(var e in a||(a={}))N.call(a,e)&&S(o,e,a[e]);if(F)for(var e of F(a))O.call(a,e)&&S(o,e,a[e]);return o};var w=(o,a,e)=>new Promise((v,g)=>{var c=i=>{try{l(e.next(i))}catch(u){g(u)}},t=i=>{try{l(e.throw(i))}catch(u){g(u)}},l=i=>i.done?v(i.value):Promise.resolve(i.value).then(c,t);l((e=e.apply(o,a)).next())});import{v as D,a as j}from"./bootstrap-5OPUVRWy.js";import{u as q}from"./form-DdFfsSWf.js";import{f as z}from"./scene.data-BMXeOdST.js";import{e as H,s as L,u as R}from"./scene.api-DEW02Ykq.js";import{s as $,a as E}from"./toast-CQjPPeQ1.js";import{u as G,a as J}from"./fileUpload-DI0dJ9zY.js";import{d as K,r as m,c as Q,k as h,j as W,b as X,q as Y,f as n,H as Z,s as ee,v as ae,I as te}from"../jse/index-index-DyHD_jbN.js";import{u as se}from"./use-modal-uChFuhJy.js";import"./loading-DzjUKA94.js";const oe={style:{"padding-left":"20px","padding-right":"20px","padding-top":"20px"}},le={style:{width:"900px"}},ie=K({__name:"SceneUpdateModal",emits:["register","success"],setup(o,{emit:a}){const e=a;let v=null;const[g,c]=se({closeOnClickModal:!1,onCancel(){c.close()},onClosed(){k()},onConfirm(){},onOpenChange(s){if(s){p.resetForm();const r=c.getData();console.log(r),l.value=t.value&&t.value.update,i.value=t.value&&t.value.upload,u=t.value&&t.value.category,x=t.value&&t.value.row&&t.value.row.dataType;let d={};l.value&&(d=T({},t.value.row)),v=d.id,d.update=l.value,d.upload=i.value,p.setValues(d)}}}),t=c.useStore();m({id:"",datasetName:""});let l=m(!1),i=m(!1),u,x;const[P,p]=q({commonConfig:{componentProps:{class:"w-full"},labelClass:"w-2/8"},handleSubmit:M,handleReset:B,layout:"horizontal",schema:z,wrapperClass:"grid-cols-1"});let y=null;m([]);const V=m("0"),_=m(!1),U=Q(()=>h(l)?"编辑":"新增"),k=()=>{p.setState({commonConfig:{disabled:!1},submitButtonOptions:{disabled:!1},resetButtonOptions:{disabled:!1}})};function B(s){return w(this,null,function*(){let r=s.id;yield p.resetForm(),yield p.setFieldValue("id",r),yield p.setFieldValue("dataType",x)})}function M(s){return w(this,null,function*(){try{s.category=u,_.value=!0,$("操作处理中...");let r=l.value?yield H(s,v):yield L(s);if(!l.value){if(y.files==null||y.files.length==0){f&&f();return}let d=A=>w(null,null,function*(){yield R({id:r,fileId:A,dataType:s.dataType}),f&&f()}),b=s.dataType,C=s.fileFormat;V.value==="0"?yield G(y,d,b,C):yield J(y,d,b,C);return}f&&f()}finally{}})}const f=()=>{c.close(),E("操作成功！"),e("success")};return(s,r)=>(X(),W(h(g),te(s.$attrs,{footer:!1,title:U.value,class:"w-[800px]","destroy-on-close":"",maskClosable:!1}),{default:Y(()=>[n("div",oe,[ee(h(P))]),Z(n("view",le,r[0]||(r[0]=[n("div",{id:"folderProgressArea",class:"folderProgressArea"},[ae(" 文件夹总进度："),n("div",{id:"folderProgress",class:"folderProgress"},"0%")],-1),n("div",{style:{display:"flex","flex-direction":"column","justify-content":"flex-start","align-items":"flex-start"}},[n("div",{style:{"margin-bottom":"5px"}},"当前文件进度："),n("div",{id:"fileProgress",class:"fileProgress bg-primary text-white"})],-1)]),512),[[D,_.value]])]),_:1},16,["title"]))}}),ye=j(ie,[["__scopeId","data-v-784f3349"]]);export{ye as default};
