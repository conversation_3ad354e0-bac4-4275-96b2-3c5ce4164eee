# 系统管理路由配置指南

## 当前配置

目前系统管理模块只包含已完成的组织管理功能，其他模块可以根据需要逐步添加。

## 路由结构

```typescript
{
  component: BasicLayout,
  meta: {
    icon: 'ion:settings-outline',      // 菜单图标
    order: 1000,                       // 菜单排序
    title: '系统管理',                  // 菜单标题
    hideChildrenInMenu: false,         // 是否隐藏子菜单
  },
  name: 'System',
  path: '/system',
  children: [
    // 子路由配置
  ],
}
```

## 扩展其他管理模块

当需要添加其他系统管理模块时，可以参考以下配置：

### 1. 用户管理
```typescript
{
  name: 'User',
  path: '/system/user',
  component: () => import('#/views/system/user/index.vue'),
  meta: {
    icon: 'ion:people-outline',
    title: '用户管理',
    order: 2,
    keepAlive: true,
    hideInBreadcrumb: false,
  },
},
```

### 2. 角色管理
```typescript
{
  name: 'Role',
  path: '/system/role',
  component: () => import('#/views/system/role/index.vue'),
  meta: {
    icon: 'ion:shield-outline',
    title: '角色管理',
    order: 3,
    keepAlive: true,
    hideInBreadcrumb: false,
  },
},
```

### 3. 岗位管理
```typescript
{
  name: 'Position',
  path: '/system/position',
  component: () => import('#/views/system/position/index.vue'),
  meta: {
    icon: 'ion:briefcase-outline',
    title: '岗位管理',
    order: 4,
    keepAlive: true,
    hideInBreadcrumb: false,
  },
},
```

### 4. 用户组管理
```typescript
{
  name: 'UserGroup',
  path: '/system/user-group',
  component: () => import('#/views/system/user-group/index.vue'),
  meta: {
    icon: 'ion:people-circle-outline',
    title: '用户组管理',
    order: 5,
    keepAlive: true,
    hideInBreadcrumb: false,
  },
},
```

### 5. 应用管理
```typescript
{
  name: 'Application',
  path: '/system/application',
  component: () => import('#/views/system/application/index.vue'),
  meta: {
    icon: 'ion:apps-outline',
    title: '应用管理',
    order: 6,
    keepAlive: true,
    hideInBreadcrumb: false,
  },
},
```

### 6. 功能管理
```typescript
{
  name: 'Function',
  path: '/system/function',
  component: () => import('#/views/system/function/index.vue'),
  meta: {
    icon: 'ion:grid-outline',
    title: '功能管理',
    order: 7,
    keepAlive: true,
    hideInBreadcrumb: false,
  },
},
```

### 7. 字典管理
```typescript
{
  name: 'Dict',
  path: '/system/dict',
  component: () => import('#/views/system/dict/index.vue'),
  meta: {
    icon: 'ion:library-outline',
    title: '字典管理',
    order: 8,
    keepAlive: true,
    hideInBreadcrumb: false,
  },
},
```

### 8. 系统配置
```typescript
{
  name: 'SysConfig',
  path: '/system/config',
  component: () => import('#/views/system/config/index.vue'),
  meta: {
    icon: 'ion:cog-outline',
    title: '系统配置',
    order: 9,
    keepAlive: true,
    hideInBreadcrumb: false,
  },
},
```

## Meta 配置说明

- `icon`: 菜单图标，使用 Ionicons 图标库
- `title`: 菜单显示名称
- `order`: 菜单排序，数字越小越靠前
- `keepAlive`: 是否缓存页面状态
- `hideInBreadcrumb`: 是否在面包屑中隐藏
- `hideChildrenInMenu`: 是否隐藏子菜单
- `hideInMenu`: 是否在菜单中隐藏

## 图标推荐

系统管理相关的常用图标：

- 组织管理: `ion:business-outline`
- 用户管理: `ion:people-outline`
- 角色管理: `ion:shield-outline`
- 岗位管理: `ion:briefcase-outline`
- 用户组管理: `ion:people-circle-outline`
- 应用管理: `ion:apps-outline`
- 功能管理: `ion:grid-outline`
- 字典管理: `ion:library-outline`
- 系统配置: `ion:cog-outline`
- 权限管理: `ion:key-outline`
- 日志管理: `ion:document-text-outline`
- 监控管理: `ion:analytics-outline`

## 添加新模块的步骤

1. 创建对应的页面组件文件
2. 在 `system.ts` 中添加路由配置
3. 确保后端API接口已实现
4. 测试页面功能是否正常

## 注意事项

1. 路由名称（name）必须唯一
2. 路径（path）建议使用 kebab-case 命名
3. 组件路径要确保文件存在
4. order 值建议预留间隔，方便后续插入新菜单
5. 保持图标风格统一，建议都使用 outline 风格
