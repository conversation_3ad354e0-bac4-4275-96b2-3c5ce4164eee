import{_ as s}from"./chunks/test.CKPCCeqA.js";import{ao as i,k as t,aP as e,l as a,ay as n,j as h}from"./chunks/framework.C8U7mBUf.js";const l=JSON.parse('{"title":"Unit Testing","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/project/test.md","filePath":"en/guide/project/test.md"}');const p=i({name:"en/guide/project/test.md"},[["render",function(i,l,p,r,o,k){const d=n("NolebaseGitContributors"),c=n("NolebaseGitChangelog");return h(),t("div",null,[l[0]||(l[0]=e('<h1 id="unit-testing" tabindex="-1">Unit Testing <a class="header-anchor" href="#unit-testing" aria-label="Permalink to &quot;Unit Testing&quot;">​</a></h1><p>The project incorporates <a href="https://vitest.dev/" target="_blank" rel="noreferrer">Vitest</a> as the unit testing tool. Vitest is a test runner based on Vite, offering a simple API for writing test cases.</p><h2 id="writing-test-cases" tabindex="-1">Writing Test Cases <a class="header-anchor" href="#writing-test-cases" aria-label="Permalink to &quot;Writing Test Cases&quot;">​</a></h2><p>Within the project, we follow the convention of naming test files with a <code>.test.ts</code> suffix or placing them inside a <code>__tests__</code> directory. For example, if you create a <code>utils.ts</code> file, then you would create a corresponding <code>utils.spec.ts</code> file in the same directory,</p><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">// utils.test.ts</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { expect, test } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;vitest&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { sum } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;./sum&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">test</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&#39;adds 1 + 2 to equal 3&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, () </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=&gt;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  expect</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">sum</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">1</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">, </span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)).</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">toBe</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">3</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">);</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">});</span></span></code></pre></div><h2 id="running-tests" tabindex="-1">Running Tests <a class="header-anchor" href="#running-tests" aria-label="Permalink to &quot;Running Tests&quot;">​</a></h2><p>To run the tests, execute the following command at the root of the monorepo:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> test:unit</span></span></code></pre></div><h2 id="existing-unit-tests" tabindex="-1">Existing Unit Tests <a class="header-anchor" href="#existing-unit-tests" aria-label="Permalink to &quot;Existing Unit Tests&quot;">​</a></h2><p>There are already some unit test cases in the project. You can search for files ending with .test.ts to view them. When you make changes to related code, you can run the unit tests to ensure the correctness of your code. It is recommended to maintain the coverage of unit tests during the development process and to run unit tests as part of the CI/CD process to ensure tests pass before deploying the project.</p><p>Existing unit test status:</p><p><img src="'+s+'" alt=""></p>',12)),a(d),a(c)])}]]);export{l as __pageData,p as default};
