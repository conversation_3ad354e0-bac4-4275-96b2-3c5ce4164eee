export const layerSources = [
  {
    fid: 0,
    id: 1,
    title: '矢量数据',
    key: 'sl',
    type: '',
  },
  {
    fid: 0,
    id: 2,
    title: '影像数据',
    key: 'yx',
    type: '',
  },
  {
    fid: 0,
    id: 3,
    title: '倾斜模型数据',
    key: 'qxsj',
    type: '',
  },
  {
    fid: 1,
    id: 4,
    title: '闽侯县',
    key: 'mh',
    type: '',
  },
  {
    fid: 1,
    id: 5,
    title: '安溪',
    key: 'anxi',
    type: '',
  },
  {
    fid: 2,
    id: 6,
    title: '平潭影像',
    key: 'pintan',
    type: '',
  },
  {
    fid: 3,
    id: 7,
    title: '科山村',
    key: 'keshan',
    type: '',
  },
  {
    fid: 3,
    id: 8,
    title: '闽侯荆溪闽江沿岸',
    key: 'minhoujingxi',
    type: '',
  },
  {
    fid: 3,
    id: 9,
    title: '福银高速',
    key: 'fuyin',
    type: '',
  },
  {
    fid: 3,
    id: 10,
    title: '旗山社区',
    key: 'qishanshequ',
    type: '',
  },
  {
    fid: 3,
    id: 11,
    title: '平潭校区',
    key: 'pingtanxiaoqu',
    type: '',
  },
  {
    fid: 3,
    id: 12,
    title: '葫芦岛城区',
    key: 'huludao',
    type: '',
  },
  // {
  //     fid:0,
  //     id:200,
  //     title:'全国行政区划',
  //     key:'china',
  //     type:'',
  // },
  {
    fid: 0,
    id: 100,
    title: '底图',
    key: 'basic',
    type: '',
  },

  {
    fid: 4,
    id: 13,
    title: '隧道',
    key: 'SD',
    data: {
      url: 'http://fzzt.fzjhdn.com:18086/geoserver/gis_base_platform/wms',
      layers: 'gis_base_platform:min_suidao',
    },
    type: 'wms',
  },
  {
    fid: 4,
    id: 14,
    title: '桥梁',
    key: 'QL',
    data: {
      url: 'http://fzzt.fzjhdn.com:18086/geoserver/gis_base_platform/wms',
      layers: 'gis_base_platform:min_qiaoliang',
    },
    type: 'wms',
  },
  {
    fid: 4,
    id: 15,
    title: '高速公路',
    key: 'GSGL',
    data: {
      url: 'http://fzzt.fzjhdn.com:18086/geoserver/gis_base_platform/wms',
      layers: 'gis_base_platform:min_gaosugonglu',
    },
    type: 'wms',
  },
  {
    fid: 4,
    id: 16,
    title: '国道',
    key: 'GD',
    data: {
      url: 'http://fzzt.fzjhdn.com:18086/geoserver/gis_base_platform/wms',
      layers: 'gis_base_platform:min_guodao',
    },
    type: 'wms',
  },
  {
    fid: 4,
    id: 17,
    title: '村道',
    key: 'CD',
    data: {
      url: 'http://fzzt.fzjhdn.com:18086/geoserver/gis_base_platform/wms',
      layers: 'gis_base_platform:minhou_cundao',
    },
    type: 'wms',
  },
  {
    fid: 5,
    id: 21,
    title: '林斑',
    key: 'LB',
    data: {
      url: 'http://fzzt.fzjhdn.com:18086/geoserver/anxi_xiaoban/wms',
      layers: 'anxi_xiaoban:anxilinban',
    },

    type: 'wms',
  },
  {
    fid: 5,
    id: 22,
    title: '乡镇界线',
    key: 'XZJ',
    data: {
      url: 'http://fzzt.fzjhdn.com:18086/geoserver/anxi_xiaoban/wms',
      layers: 'anxi_xiaoban:anxi_xiangzheng',
    },
    type: 'wms',
  },
  {
    fid: 5,
    id: 23,
    title: '县界',
    key: 'XJ',
    data: {
      url: 'http://fzzt.fzjhdn.com:18086/geoserver/anxi_xiaoban/wms',
      layers: 'anxi_xiaoban:anxi_xianjie',
    },
    type: 'wms',
  },

  {
    fid: 1,
    id: 24,
    title: '平潭兴趣点',
    key: 'PTXQD',
    data: {
      url: 'https://fzzt.fzjhdn.com:6443/arcgis/rest/services/PTXingQuDian/MapServer',
      layers: '0',
    },
    type: 'arcgis:wms',
  },
  {
    fid: 6,
    id: 31,
    title: '高铁站2017',
    key: 'GT2017',
    data: {
      url: 'https://fzzt.fzjhdn.com:6443/arcgis/rest/services/PingTanGT201719/MapServer',
      layers: '0',
    },
    type: 'arcgis:wms',
  },
  {
    fid: 6,
    id: 32,
    title: '高铁站2019',
    key: 'GT2019',
    data: {
      url: 'https://fzzt.fzjhdn.com:6443/arcgis/rest/services/PingTanGT201919/MapServer',
      layers: '0',
    },
    type: 'arcgis:wms',
  },
  {
    fid: 6,
    id: 33,
    title: '高铁站2020',
    key: 'GT2020',
    data: {
      url: 'https://fzzt.fzjhdn.com:6443/arcgis/rest/services/PingTanGT202019/MapServer',
      layers: '0',
    },
    type: 'arcgis:wms',
  },
  {
    fid: 6,
    id: 34,
    title: '高铁站2021',
    key: 'GT2021',
    data: {
      url: 'https://fzzt.fzjhdn.com:6443/arcgis/rest/services/PingTanGT202119/MapServer',
      layers: '0',
    },
    type: 'arcgis:wms',
  },

  {
    fid: 7,
    id: 41,
    title: '科山村模型',
    key: 'KSC',
    data: {
      url: 'http://fzzt.fzjhdn.com:19003/keshan3DTile/tileset.json',
    },
    type: '3DTiles',
  },
  {
    fid: 8,
    id: 51,
    title: '闽侯荆溪闽江沿岸模型',
    key: 'MHJXMJ',
    data: {
      // url: "http://fzzt.fzjhdn.com:19005/model/U7MeOClu/tileset.json",
      url: 'http://fzzt.fzjhdn.com:19005/model/tYwkjli07/tileset.json',
    },
    type: '3DTiles',
  },
  {
    fid: 9,
    id: 61,
    title: '福银高速模型',
    key: 'FYGS',
    data: {
      url: 'http://fzzt.fzjhdn.com:19005/model/GYsiSGQd/tileset.json',
    },
    type: '3DTiles',
  },
  {
    fid: 10,
    id: 71,
    title: '旗山社区模型',
    key: 'QSSQ',
    data: {
      url: 'http://fzzt.fzjhdn.com:19005/model/zrZ6KKuk/tileset.json',
    },
    type: '3DTiles',
  },
  {
    fid: 10,
    id: 25,
    title: '旗山社区楼房',
    key: 'QSSQLF',
    data: {
      url: 'https://fzzt.fzjhdn.com:6443/arcgis/rest/services/QiShanSheQu/MapServer',
      layers: '0',
    },
    type: 'arcgis:wms',
  },
  {
    fid: 11,
    id: 81,
    title: '信息学院模型',
    key: 'XXXY',
    data: {
      url: 'http://fzzt.fzjhdn.com:19005/model/fklDgTbK/tileset.json',
    },
    type: '3DTiles',
  },
  {
    fid: 12,
    id: 91,
    title: '葫芦岛城区模型',
    key: 'HLDCQ',
    data: {
      url: 'http://fzzt.fzjhdn.com:19005/model/OiiEgtLO/tileset.json',
    },
    type: '3DTiles',
  },

  // 全国矢量地图
  // {
  //     fid:200,
  //     id:201,
  //     title: "县级点",
  //     key: "XQJPoint",
  //     data:{
  //         url: "https://fzzt.fzjhdn.com:6443/arcgis/rest/services/China/MapServer",
  //         layers:'0',
  //         usePreCachedTilesIfAvailable:false

  //     },
  //     type: "arcgis:wms",
  // },
  // {
  //     fid:200,
  //     id:202,
  //     title: "乡镇级点",
  //     key: "XPoint",
  //     data:{
  //         url: "https://fzzt.fzjhdn.com:6443/arcgis/rest/services/China/MapServer",
  //         layers:'1',
  //         usePreCachedTilesIfAvailable:false

  //     },
  //     type: "arcgis:wms",
  // },
  // {
  //     fid:200,
  //     id:203,
  //     title: "省级点",
  //     key: "SPoint",
  //     data:{
  //         url: "https://fzzt.fzjhdn.com:6443/arcgis/rest/services/China/MapServer",
  //         layers:'2',
  //         usePreCachedTilesIfAvailable:false

  //     },
  //     type: "arcgis:wms",
  // },
  // {
  //     fid:200,
  //     id:204,
  //     title: "地级市点",
  //     key: "DJPoint",
  //     data:{
  //         url: "https://fzzt.fzjhdn.com:6443/arcgis/rest/services/China/MapServer",
  //         layers:'3',
  //         usePreCachedTilesIfAvailable:false

  //     },
  //     type: "arcgis:wms",
  // },
  // {
  //     fid:200,
  //     id:205,
  //     title: "村级点",
  //     key: "CJPoint",
  //     data:{
  //         url: "https://fzzt.fzjhdn.com:6443/arcgis/rest/services/China/MapServer",
  //         layers:'4',
  //         usePreCachedTilesIfAvailable:false

  //     },
  //     type: "arcgis:wms",
  // },
  // {
  //     fid:200,
  //     id:206,
  //     title: "县级行政区划",
  //     key: "XJPolygon",
  //     data:{
  //         url: "https://fzzt.fzjhdn.com:6443/arcgis/rest/services/China/MapServer",
  //         layers:'5',
  //         usePreCachedTilesIfAvailable:false

  //     },
  //     type: "arcgis:wms",
  // },
  // {
  //     fid:200,
  //     id:207,
  //     title: "乡镇级行政区划",
  //     key: "XZJPolygon",
  //     data:{
  //         url: "https://fzzt.fzjhdn.com:6443/arcgis/rest/services/China/MapServer",
  //         layers:'6',
  //         usePreCachedTilesIfAvailable:false

  //     },
  //     type: "arcgis:wms",
  // },
  // {
  //     fid:200,
  //     id:208,
  //     title: "省级行政区划",
  //     key: "SJPolygon",
  //     data:{
  //         url: "https://fzzt.fzjhdn.com:6443/arcgis/rest/services/China/MapServer",
  //         layers:'7',
  //         usePreCachedTilesIfAvailable:false

  //     },
  //     type: "arcgis:wms",
  // },
  // {
  //     fid:200,
  //     id:209,
  //     title: "地级市行政区划",
  //     key: "DJSPolygon",
  //     data:{
  //         url: "https://fzzt.fzjhdn.com:6443/arcgis/rest/services/China/MapServer",
  //         layers:'8',
  //         usePreCachedTilesIfAvailable:false

  //     },
  //     type: "arcgis:wms",
  // },
  // {
  //     fid:200,
  //     id:210,
  //     title: "中国",
  //     key: "China",
  //     data:{
  //         url: "https://fzzt.fzjhdn.com:6443/arcgis/rest/services/China/MapServer",
  //         layers:'9',
  //         usePreCachedTilesIfAvailable:false

  //     },
  //     type: "arcgis:wms",
  // },

  // 底图配置
  {
    fid: 100,
    id: 1002,
    title: '矢量注记',
    key: 'tianditu2',
    data: {
      url: 'http://t{s}.tianditu.gov.cn/cva_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cva&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=76620c5b63143f80f875dffb556b57ef',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      layer: 'tdtImgLayer',
      style: 'default',
      format: 'image/jpeg',
      tileMatrixSetID: 'GoogleMapsCompatible', // 使用谷歌的瓦片切片方式
      show: true,
      maximumLevel: 20,
    },
    type: 'wmts',
  },
  {
    fid: 100,
    id: 1001,
    title: '天地图矢量',
    key: 'tianditu1',
    data: {
      url: 'http://t{s}.tianditu.gov.cn/vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=76620c5b63143f80f875dffb556b57ef',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      layer: 'tdtImgLayer',
      style: 'default',
      format: 'image/jpeg',
      tileMatrixSetID: 'GoogleMapsCompatible', // 使用谷歌的瓦片切片方式
      show: true,
      maximumLevel: 20,
    },
    type: 'wmts',
  },

  {
    fid: 100,
    id: 1003,
    title: '天地图影像',
    key: 'tianditu3',
    data: {
      url: 'http://t0.tianditu.com/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=9189606639aa352dc18b332505a47597',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      layer: 'tdtImgLayer',
      style: 'default',
      format: 'image/jpeg',
      tileMatrixSetID: 'GoogleMapsCompatible', // 使用谷歌的瓦片切片方式
      show: true,
      maximumLevel: 19,
    },
    type: 'wmts',
  },
  {
    fid: 100,
    id: 1005,
    title: '天地图地形',
    key: 'tianditu5',
    data: {
      url: 'http://t{s}.tianditu.gov.cn/ter_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=ter&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=76620c5b63143f80f875dffb556b57ef',
      subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
      layer: 'tdtImgLayer',
      style: 'default',
      format: 'image/jpeg',
      tileMatrixSetID: 'GoogleMapsCompatible', // 使用谷歌的瓦片切片方式
      show: true,
    },
    type: 'wmts',
  },
  {
    fid: 100,
    id: 1006,
    title: 'Bing影像',
    key: 'Bingimage',
    data: {},
    type: 'bing',
  },
  {
    fid: 100,
    id: 1007,
    title: 'Cesium地形',
    key: 'Cesiumterrain',
    data: {},
    type: 'terrain',
  },
];
