import { Modal } from "ant-design-vue";
import { usePreferences } from '@vben/preferences';
import { h } from 'vue';

export function showConform(title, content, ok) {
  const { isDark } = usePreferences();

  Modal.confirm({
    title: () => h('div', { class: 'modal-title' }, title),
    content: () => h('div', { class: 'modal-content' }, content),
    zIndex: 2001,
    class: isDark.value ? 'dark-mode-modal' : '',
    okButtonProps: {
      class: 'primary-button',
    },
    cancelButtonProps: {
      class: 'default-button',
    },
    onOk() {
      return new Promise((resolve, reject) => {
        ok && ok();
        resolve && resolve();
      }).catch(() => console.log('Oops errors!'));
    },
    onCancel() {},
  });
}

// 添加全局样式
const style = document.createElement('style');
style.textContent = `
  .modal-title {
    color: hsl(var(--foreground));
    font-weight: 500;
  }

  .modal-content {
    color: hsl(var(--muted-foreground));
  }

  .dark-mode-modal {
    .ant-modal-content {
      background-color: hsl(var(--background));
    }
    .ant-modal-header {
      background-color: hsl(var(--background));
      border-bottom-color: hsl(var(--border));
    }
    .ant-modal-title {
      color: hsl(var(--foreground));
    }
    .ant-modal-close {
      color: hsl(var(--foreground));
    }
    .ant-modal-body {
      color: hsl(var(--foreground));
    }
    .ant-modal-footer {
      border-top-color: hsl(var(--border));
    }
  }

      .primary-button {
    background: hsl(var(--primary));
    border-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

    &:hover {
      background: hsl(var(--primary) / 0.85) !important;
      border-color: hsl(var(--primary) / 0.85) !important;
      color: hsl(var(--primary-foreground)) !important;
      box-shadow: 0 4px 12px hsl(var(--primary) / 0.4);
    }

    &:active {
      background: hsl(var(--primary) / 0.9) !important;
      border-color: hsl(var(--primary) / 0.9) !important;
      box-shadow: none;
    }

    &:focus {
      box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
    }
  }

  .default-button {
    background: hsl(var(--background));
    border-color: hsl(var(--border));
    color: hsl(var(--foreground));
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

    &:hover {
      color: hsl(var(--primary)) !important;
      border-color: hsl(var(--primary) / 0.85) !important;
      background: hsl(var(--primary) / 0.1) !important;
    }

    &:active {
      background: hsl(var(--primary) / 0.15) !important;
      border-color: hsl(var(--primary)) !important;
      color: hsl(var(--primary)) !important;
    }

    &:focus {
      box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
    }
  }
`;
document.head.appendChild(style);
