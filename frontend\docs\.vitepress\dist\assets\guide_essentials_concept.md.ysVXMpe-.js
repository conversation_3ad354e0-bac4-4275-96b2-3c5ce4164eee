import{ao as s,k as a,aP as i,l as e,ay as n,j as t}from"./chunks/framework.C8U7mBUf.js";const p=JSON.parse('{"title":"基础概念","description":"","frontmatter":{},"headers":[],"relativePath":"guide/essentials/concept.md","filePath":"guide/essentials/concept.md"}');const l=s({name:"guide/essentials/concept.md"},[["render",function(s,p,l,h,o,d){const k=n("NolebaseGitContributors"),c=n("NolebaseGitChangelog");return t(),a("div",null,[p[0]||(p[0]=i('<h1 id="基础概念" tabindex="-1">基础概念 <a class="header-anchor" href="#基础概念" aria-label="Permalink to &quot;基础概念&quot;">​</a></h1><p>新版本中，整体工程进行了重构，现在我们将会介绍一些基础概念，以便于你更好的理解整个文档。请务必仔细阅读这一部分。</p><h2 id="大仓" tabindex="-1">大仓 <a class="header-anchor" href="#大仓" aria-label="Permalink to &quot;大仓&quot;">​</a></h2><p>大仓指的是整个项目的仓库，包含了所有的代码、包、应用、规范、文档、配置等，也就是一整个 <code>Monorepo</code> 目录的所有内容。</p><h2 id="应用" tabindex="-1">应用 <a class="header-anchor" href="#应用" aria-label="Permalink to &quot;应用&quot;">​</a></h2><p>应用指的是一个完整的项目，一个项目可以包含多个应用，这些项目可以复用大仓内的代码、包、规范等。应用都被放置在 <code>apps</code> 目录下。每个应用都是独立的，可以单独运行、构建、测试、部署，可以引入不同的组件库等等。</p><div class="tip custom-block"><p class="custom-block-title">TIP</p><p>应用不限于前端应用，也可以是后端应用、移动端应用等，例如 <code>apps/backend-mock</code>就是一个后端服务。</p></div><h2 id="包" tabindex="-1">包 <a class="header-anchor" href="#包" aria-label="Permalink to &quot;包&quot;">​</a></h2><p>包指的是一个独立的模块，可以是一个组件、一个工具、一个库等。包可以被多个应用引用，也可以被其他包引用。包都被放置在 <code>packages</code> 目录下。</p><p>对于这些包，你可以把它看作是一个独立的 <code>npm</code> 包，使用方式与 <code>npm</code> 包一样。</p><h3 id="包引入" tabindex="-1">包引入 <a class="header-anchor" href="#包引入" aria-label="Permalink to &quot;包引入&quot;">​</a></h3><p>在 <code>package.json</code> 中引入包：</p><div class="language-json vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;dependencies&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>\n<span class="line highlighted"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;@vben/utils&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;workspace:*&quot;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><h3 id="包使用" tabindex="-1">包使用 <a class="header-anchor" href="#包使用" aria-label="Permalink to &quot;包使用&quot;">​</a></h3><p>在代码中引入包：</p><div class="language-ts vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">ts</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">import</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> { isString } </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">from</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;@vben/utils&#39;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span></code></pre></div><h2 id="别名" tabindex="-1">别名 <a class="header-anchor" href="#别名" aria-label="Permalink to &quot;别名&quot;">​</a></h2><p>在项目中，你可以看到一些 <code>#</code> 开头的路径，例如： <code>#/api</code>、<code>#/views</code>, 这些路径都是别名，用于快速定位到某个目录。它不是通过 <code>vite</code> 的 <code>alias</code> 实现的，而是通过 <code>Node.js</code> 本身的 <a href="https://nodejs.org/api/packages.html#subpath-imports" target="_blank" rel="noreferrer">subpath imports</a> 原理。只需要在 <code>package.json</code> 中配置 <code>imports</code> 字段即可。</p><div class="language-json vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;imports&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>\n<span class="line highlighted"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;#/*&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;./src/*&quot;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p>为了 IDE 能够识别这些别名，我们还需要在<code>tsconfig.json</code>内配置：</p><div class="language-json vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;compilerOptions&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;baseUrl&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;.&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;paths&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>\n<span class="line highlighted"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      &quot;#/*&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: [</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;src/*&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">]</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p>这样，你就可以在代码中使用别名了。</p>',22)),e(k),e(c)])}]]);export{p as __pageData,l as default};
