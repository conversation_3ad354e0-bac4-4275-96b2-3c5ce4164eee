import { requestClient } from '#/api/request.ts';



/**
 * 查询所有底图
 * @param {*} params
 */
export async function getAppBaseMap(params) {
  let res = [
    {
      mapName: '天地图标记',
      mapUrl:
        'http://t0.tianditu.com/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&tk=9189606639aa352dc18b332505a47597',
      isDefault: 0,
      mapType: 'wmts',
    },
    {
      mapName: '天地图地形',
      mapUrl:
        'http://t0.tianditu.gov.cn/ter_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=ter&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=9189606639aa352dc18b332505a47597',
      isDefault: 0,
      mapType: 'wmts',
    },
    {
      mapName: '天地图矢量',
      mapUrl:
        'http://t0.tianditu.gov.cn/vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=9189606639aa352dc18b332505a47597',
      isDefault: 0,
      mapType: 'wmts',
    },
    {
      mapName: '天地图影像',
      mapUrl:
        'http://t0.tianditu.com/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=9189606639aa352dc18b332505a47597',
      isDefault: 0,
      mapType: 'wmts',
    },
    {
      mapName: '全球地形',
      mapUrl: 'http://www.cesium.com',
      isDefault: 0,
      mapType: 'terrain',
    },
    {
      mapName: '全球影像',
      mapUrl: 'http://www.virtualearth.net',
      isDefault: 1,
      mapType: 'wmts',
    },
  ];
  if (!res || res.length === 0) return null;
  const fkey = `${Math.random()}`;
  const result = {
    showKey: '',
    layers: [
      {
        fid: 0,
        id: fkey,
        title: '底图',
        key: 'baseMap',
      },
    ],
  };
  res.forEach((item) => {
    const key = `baseMap_${item.mapName}`; // Math.random()+'';
    if (item.isDefault) result.showKey = key;
    if (
      `${item.mapUrl}`.toLocaleLowerCase() == 'http://www.virtualearth.net'
    ) {
      result.layers.push({
        fid: fkey,
        id: key,
        title: item.mapName,
        key,
        data: {},
        type: 'bing',
      });
    } else if (
      `${item.mapUrl}`.toLocaleLowerCase() == 'http://www.cesium.com'
    ) {
      result.layers.push({
        fid: fkey,
        id: key,
        title: item.mapName,
        key,
        data: {
          provider: '',
        },
        type: 'terrain',
      });
    } else {
      result.layers.push({
        fid: fkey,
        id: key,
        title: item.mapName,
        key,
        data: {
          url: item.mapUrl,
        },
        type: item.mapType,
      });
    }
  });

  return result;
}
