import{_ as s}from"./chunks/report.Dsxn5c6D.js";import{ao as i,k as a,aP as e,l as n,ay as t,j as l}from"./chunks/framework.C8U7mBUf.js";const h=JSON.parse('{"title":"Build and Deployment","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/essentials/build.md","filePath":"en/guide/essentials/build.md"}');const p=i({name:"en/guide/essentials/build.md"},[["render",function(i,h,p,k,r,o){const d=t("NolebaseGitContributors"),c=t("NolebaseGitChangelog");return l(),a("div",null,[h[0]||(h[0]=e('<h1 id="build-and-deployment" tabindex="-1">Build and Deployment <a class="header-anchor" href="#build-and-deployment" aria-label="Permalink to &quot;Build and Deployment&quot;">​</a></h1><div class="tip custom-block"><p class="custom-block-title">Preface</p><p>Since this is a demonstration project, the package size after building is relatively large. If there are plugins in the project that are not used, you can delete the corresponding files or routes. If they are not referenced, they will not be packaged.</p></div><h2 id="building" tabindex="-1">Building <a class="header-anchor" href="#building" aria-label="Permalink to &quot;Building&quot;">​</a></h2><p>After the project development is completed, execute the following command to build:</p><p><strong>Note:</strong> Please execute the following command in the project root directory.</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> build</span></span></code></pre></div><p>After the build is successful, a <code>dist</code> folder for the corresponding application will be generated in the root directory, which contains the built and packaged files, for example: <code>apps/web-antd/dist/</code></p><h2 id="preview" tabindex="-1">Preview <a class="header-anchor" href="#preview" aria-label="Permalink to &quot;Preview&quot;">​</a></h2><p>Before publishing, you can preview it locally in several ways, here are two:</p><ul><li>Using the project&#39;s custom command for preview (recommended)</li></ul><p><strong>Note：</strong> Please execute the following command in the project root directory.</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> preview</span></span></code></pre></div><p>After waiting for the build to succeed, visit <code>http://localhost:4173</code> to view the effect.</p><ul><li>Local server preview</li></ul><p>You can globally install a <code>serve</code> service on your computer, such as <code>live-server</code>,</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">npm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> i</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> -g</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> live-server</span></span></code></pre></div><p>Then execute the <code>live-server</code> command in the <code>dist</code> directory to view the effect locally.</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">cd</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> apps/web-antd/dist</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Local preview, default port 8080</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">live-server</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Specify port</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">live-server</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> --port</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 9000</span></span></code></pre></div><h2 id="compression" tabindex="-1">Compression <a class="header-anchor" href="#compression" aria-label="Permalink to &quot;Compression&quot;">​</a></h2><h3 id="enable-gzip-compression" tabindex="-1">Enable <code>gzip</code> Compression <a class="header-anchor" href="#enable-gzip-compression" aria-label="Permalink to &quot;Enable `gzip` Compression&quot;">​</a></h3><p>To enable during the build process, change the <code>.env.production</code> configuration:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_COMPRESS</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">gzip</span></span></code></pre></div><h3 id="enable-brotli-compression" tabindex="-1">Enable <code>brotli</code> Compression <a class="header-anchor" href="#enable-brotli-compression" aria-label="Permalink to &quot;Enable `brotli` Compression&quot;">​</a></h3><p>To enable during the build process, change the <code>.env.production</code> configuration:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_COMPRESS</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">brotli</span></span></code></pre></div><h3 id="enable-both-gzip-and-brotli-compression" tabindex="-1">Enable Both <code>gzip</code> and <code>brotli</code> Compression <a class="header-anchor" href="#enable-both-gzip-and-brotli-compression" aria-label="Permalink to &quot;Enable Both `gzip` and `brotli` Compression&quot;">​</a></h3><p>To enable during the build process, change the <code>.env.production</code> configuration:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_COMPRESS</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">gzip,brotli</span></span></code></pre></div><div class="tip custom-block"><p class="custom-block-title">Note</p><p>Both <code>gzip</code> and <code>brotli</code> require specific modules to be installed for use.</p></div><details class="details custom-block"><summary>gzip 与 brotli 在 nginx 内的配置</summary><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">http</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # Enable gzip</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> on</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # Enable gzip_static</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # After enabling gzip_static, there might be errors, requiring the installation of specific modules. The installation method can be researched independently.</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # Only with this enabled, the .gz files packaged by vue files will be effective; otherwise, there is no need to enable gzip for packaging.</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_static</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> on</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_proxied</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> any</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_min_length</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> 1k</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_buffers</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 4</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> 16k</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # If nginx uses multiple layers of proxy, this must be set to enable gzip.</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_http_version</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 1.0</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_comp_level</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 2</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_types</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/plain</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/x-javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/css</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/xml</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/x-httpd-php</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> image/jpeg</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> image/gif</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> image/png</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_vary</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> off</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  gzip_disable</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;MSIE [1-6]\\.&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # Enable brotli compression</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # Requires the installation of the corresponding nginx module, which can be researched independently.</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # Can coexist with gzip without conflict.</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  brotli</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> on</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  brotli_comp_level</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 6</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  brotli_buffers</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 16</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> 8k</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  brotli_min_length</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 20</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  brotli_types</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/plain</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/css</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/json</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/x-javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/xml</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/xml</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/xml+rss</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> text/javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> application/javascript</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> image/svg+xml</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div></details><h2 id="build-analysis" tabindex="-1">Build Analysis <a class="header-anchor" href="#build-analysis" aria-label="Permalink to &quot;Build Analysis&quot;">​</a></h2><p>If your build files are large, you can optimize your code by analyzing the code size with the built-in <a href="https://github.com/doesdev/rollup-plugin-analyzer" target="_blank" rel="noreferrer">rollup-plugin-analyzer</a> plugin. Just execute the following command in the <code>root directory</code>:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> run</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> build:analyze</span></span></code></pre></div><p>After running, you can see the specific distribution of sizes on the automatically opened page to analyze which dependencies are problematic.</p><p><img src="'+s+'" alt="Build analysis report"></p><h2 id="deployment" tabindex="-1">Deployment <a class="header-anchor" href="#deployment" aria-label="Permalink to &quot;Deployment&quot;">​</a></h2><p>A simple deployment only requires publishing the final static files, the static files in the dist folder, to your CDN or static server. It&#39;s important to note that the index.html is usually the entry page for your backend service. After determining the static js and css, you may need to change the page&#39;s import path.</p><p>For example, to upload to an nginx server, you can upload the files under the dist folder to the server&#39;s <code>/srv/www/project/index.html</code> directory, and then access the configured domain name.</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># nginx configuration</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">location</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # Do not cache html to prevent cache from continuing to be effective after program updates</span></span>\n<span class="line"><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">  if</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> ($request_filename </span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">~*</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> .</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">*</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\.</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">(</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">?</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">:htm</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">|</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">html</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">)</span><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">$</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">) {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    add_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Cache-Control</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;private, no-store, no-cache, must-revalidate, proxy-revalidate&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    access_log</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> on</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # This is the storage path for the files inside the vue packaged dist folder</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  root</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">   /srv/www/project/</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  index</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">  index.html</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> index.htm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><p>If you find the resource path is incorrect during deployment, you just need to modify the <code>.env.production</code> file.</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Configure the change according to your own path</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Note that it needs to start and end with /</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_BASE</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_BASE</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/xxx/</span></span></code></pre></div><h3 id="integration-of-frontend-routing-and-server" tabindex="-1">Integration of Frontend Routing and Server <a class="header-anchor" href="#integration-of-frontend-routing-and-server" aria-label="Permalink to &quot;Integration of Frontend Routing and Server&quot;">​</a></h3><p>The project uses vue-router for frontend routing, so you can choose between two modes: history and hash.</p><ul><li><code>hash</code> mode will append <code>#</code> to the URL by default.</li><li><code>history</code> mode will not, but <code>history</code> mode requires server-side support.</li></ul><p>You can modify the mode in <code>.env.production</code>:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_ROUTER_HISTORY</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">hash</span></span></code></pre></div><h3 id="server-configuration-for-history-mode-routing" tabindex="-1">Server Configuration for History Mode Routing <a class="header-anchor" href="#server-configuration-for-history-mode-routing" aria-label="Permalink to &quot;Server Configuration for History Mode Routing&quot;">​</a></h3><p>Enabling <code>history</code> mode requires server configuration. For more details on server configuration, see <a href="https://router.vuejs.org/guide/essentials/history-mode.html#html5-mode" target="_blank" rel="noreferrer">history-mode</a></p><p>Here is an example of <code>nginx</code> configuration:</p><h4 id="deployment-at-the-root-directory" tabindex="-1">Deployment at the Root Directory <a class="header-anchor" href="#deployment-at-the-root-directory" aria-label="Permalink to &quot;Deployment at the Root Directory&quot;">​</a></h4><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">server</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  listen</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> 80</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  location</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    # For use with History mode</span></span>\n<span class="line highlighted"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    try_files</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> $uri $uri</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /index.html</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><h4 id="deployment-to-a-non-root-directory" tabindex="-1">Deployment to a Non-root Directory <a class="header-anchor" href="#deployment-to-a-non-root-directory" aria-label="Permalink to &quot;Deployment to a Non-root Directory&quot;">​</a></h4><ul><li>First, you need to change the <code>.env.production</code> configuration during packaging:</li></ul><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">VITE_BASE</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> =</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /sub/</span></span></code></pre></div><ul><li>Then configure in the nginx configuration file</li></ul><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">server</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    listen</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">       80</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    server_name</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">  localhost</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    location</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /sub/</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">      # This is the path where the vue packaged dist files are stored</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">      alias</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">   /srv/www/project/</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">      index</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> index.html</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> index.htm</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line highlighted"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">      try_files</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> $uri $uri</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /sub/index.html</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">    }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><h2 id="cross-domain-handling" tabindex="-1">Cross-Domain Handling <a class="header-anchor" href="#cross-domain-handling" aria-label="Permalink to &quot;Cross-Domain Handling&quot;">​</a></h2><p>Using nginx to handle cross-domain issues after project deployment</p><ol><li>Configure the frontend project API address in the <code>.env.production</code> file in the project directory:</li></ol><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_GLOB_API_URL</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/api</span></span></code></pre></div><ol start="2"><li>Configure nginx to forward requests to the backend</li></ol><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">server</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  listen</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">       8080</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  server_name</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">  localhost</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">  # API proxy for solving cross-domain issues</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">  location</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /api</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> {</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    proxy_set_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Host</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> $host;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    proxy_set_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> X-Real-IP</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> $remote_addr;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    proxy_set_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> X-Forwarded-For</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> $proxy_add_x_forwarded_for;</span></span>\n<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    # Backend API address</span></span>\n<span class="line highlighted"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    proxy_pass</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> http://***********:8080/api</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line highlighted"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    rewrite</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;^/api/(.*)$&quot;</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> /</span><span style="--shiki-light:#E36209;--shiki-dark:#FFAB70;">$1</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> break</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    proxy_redirect</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> default</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    add_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Access-Control-Allow-Origin</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> *</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    add_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Access-Control-Allow-Headers</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> X-Requested-With</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">    add_header</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> Access-Control-Allow-Methods</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> GET,POST,OPTIONS</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div>',62)),n(d),n(c)])}]]);export{h as __pageData,p as default};
