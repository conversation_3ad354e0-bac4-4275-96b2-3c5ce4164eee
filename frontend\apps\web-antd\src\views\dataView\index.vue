<script lang="ts"  setup>
import { onMounted } from 'vue';

import Dashboard from './components/Dashboard.vue';

onMounted(() => {});
</script>

<template>
  <a-layout style="height: 88.5vh">
    <a-layout-content>
      <Dashboard />
    </a-layout-content>
  </a-layout>
</template>

<style lang="less">
@import '#/styles/dark-antd.less';

.header {
  text-align: center;
}

.title {
  padding: 10px;
  margin: 0;
}
</style>
