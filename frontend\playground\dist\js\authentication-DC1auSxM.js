const e="Welcome Back",o="Plug-and-play Admin system",t="Efficient, versatile frontend template",n="Login Successful",s="Welcome Back",i="Enter your account details to manage your projects",c="Quick Select Account",r="Username",a="Password",l="Please enter username",d="Password is incorrect",g="Please enter password",u="Please complete the verification first",m="Remember Me",p="Create an Account",T="Create Account",y="Already have an account?",b="Don't have an account?",P="Sign Up",w="Make managing your applications simple and fun",f="Confirm Password",S="The passwords do not match",h="I agree to",A="Privacy-policy",L="Terms",v="Please agree to the Privacy Policy and Terms",E="Login instead",R="Use 8 or more characters with a mix of letters, numbers & symbols",k="Forget Password?",q="Enter your email and we'll send you instructions to reset your password",C="Please enter email",U="The email format you entered is incorrect",M="Send Reset Link",x="Email",D="Scan the QR code with your phone to login",B="Click 'Confirm' after scanning to complete login",Q="QR Code Login",j="Enter your phone number to start managing your project",H="Security code",I="Security code is required",V="Mobile",W="Mobile Login",F="Please enter mobile number",G="The phone number format is incorrect",O="Get Security code",Y="Resend in {0}s",z="Or continue with",J="Please Log In Again",K="Your login session has expired. Please log in again to continue.",N={center:"Align Center",alignLeft:"Align Left",alignRight:"Align Right"},X={welcomeBack:e,pageTitle:o,pageDesc:t,loginSuccess:n,loginSuccessDesc:s,loginSubtitle:i,selectAccount:c,username:r,password:a,usernameTip:l,passwordErrorTip:d,passwordTip:g,verifyRequiredTip:u,rememberMe:m,createAnAccount:p,createAccount:T,alreadyHaveAccount:y,accountTip:b,signUp:P,signUpSubtitle:w,confirmPassword:f,confirmPasswordTip:S,agree:h,privacyPolicy:A,terms:L,agreeTip:v,goToLogin:E,passwordStrength:R,forgetPassword:k,forgetPasswordSubtitle:q,emailTip:C,emailValidErrorTip:U,sendResetLink:M,email:x,qrcodeSubtitle:D,qrcodePrompt:B,qrcodeLogin:Q,codeSubtitle:j,code:H,codeTip:I,mobile:V,mobileLogin:W,mobileTip:F,mobileErrortip:G,sendCode:O,sendText:Y,thirdPartyLogin:z,loginAgainTitle:J,loginAgainSubTitle:K,layout:N};export{b as accountTip,h as agree,v as agreeTip,y as alreadyHaveAccount,H as code,j as codeSubtitle,I as codeTip,f as confirmPassword,S as confirmPasswordTip,T as createAccount,p as createAnAccount,X as default,x as email,C as emailTip,U as emailValidErrorTip,k as forgetPassword,q as forgetPasswordSubtitle,E as goToLogin,N as layout,K as loginAgainSubTitle,J as loginAgainTitle,i as loginSubtitle,n as loginSuccess,s as loginSuccessDesc,V as mobile,G as mobileErrortip,W as mobileLogin,F as mobileTip,t as pageDesc,o as pageTitle,a as password,d as passwordErrorTip,R as passwordStrength,g as passwordTip,A as privacyPolicy,Q as qrcodeLogin,B as qrcodePrompt,D as qrcodeSubtitle,m as rememberMe,c as selectAccount,O as sendCode,M as sendResetLink,Y as sendText,P as signUp,w as signUpSubtitle,L as terms,z as thirdPartyLogin,r as username,l as usernameTip,u as verifyRequiredTip,e as welcomeBack};
