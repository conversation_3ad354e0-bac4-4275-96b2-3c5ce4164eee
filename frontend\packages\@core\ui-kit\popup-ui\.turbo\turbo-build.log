
> @vben-core/popup-ui@5.2.1 build E:\work\git\gisbase-datamanage-fed\packages\@core\ui-kit\popup-ui
> pnpm unbuild

[36mi[39m [36mBuilding popup-ui[39m
[36mi[39m Cleaning dist directory: [36m./dist[39m
[32m√[39m [32mBuild succeeded for popup-ui[39m
  [1mdist[22m (total size: [36m13.3 kB[39m)
[90m  └─ dist/modal/modal.vue[1m (7.61 kB)[22m[39m
[90m  └─ dist/drawer/drawer.vue[1m (5.69 kB)[22m[39m
  [1mdist[22m (total size: [36m28.4 kB[39m)
[90m  └─ dist/drawer/drawer.mjs[39m
[90m  └─ dist/modal/modal.mjs[39m
[90m  └─ dist/drawer/__tests__/drawer-api.test.d.ts[1m (11 B)[22m[39m
[90m  └─ dist/index.mjs[1m (71 B)[22m[39m
[90m  └─ dist/drawer/drawer-api.mjs[1m (2.32 kB)[22m[39m
[90m  └─ dist/index.d.ts[1m (51 B)[22m[39m
[90m  └─ dist/drawer/drawer-api.d.ts[1m (720 B)[22m[39m
[90m  └─ dist/drawer/drawer.d.ts[1m (2.62 kB)[22m[39m
[90m  └─ dist/drawer/index.mjs[1m (104 B)[22m[39m
[90m  └─ dist/drawer/use-drawer.d.ts[1m (337 B)[22m[39m
[90m  └─ dist/modal/index.mjs[1m (100 B)[22m[39m
[90m  └─ dist/drawer/use-drawer.mjs[1m (2.31 kB)[22m[39m
[90m  └─ dist/modal/modal-api.d.ts[1m (887 B)[22m[39m
[90m  └─ dist/modal/modal-api.mjs[1m (2.82 kB)[22m[39m
[90m  └─ dist/modal/modal.d.ts[1m (3.1 kB)[22m[39m
[90m  └─ dist/modal/use-modal-draggable.mjs[1m (2.67 kB)[22m[39m
[90m  └─ dist/modal/use-modal-draggable.d.ts[1m (479 B)[22m[39m
[90m  └─ dist/modal/__tests__/modal-api.test.mjs[1m (3.61 kB)[22m[39m
[90m  └─ dist/drawer/__tests__/drawer-api.test.mjs[1m (3.19 kB)[22m[39m
[90m  └─ dist/drawer/index.d.ts[1m (131 B)[22m[39m
[90m  └─ dist/modal/use-modal.d.ts[1m (325 B)[22m[39m
[90m  └─ dist/modal/index.d.ts[1m (126 B)[22m[39m
[90m  └─ dist/modal/use-modal.mjs[1m (2.43 kB)[22m[39m
[90m  └─ dist/modal/__tests__/modal-api.test.d.ts[1m (11 B)[22m[39m
Σ Total dist size (byte size): [36m41.7 kB[39m

