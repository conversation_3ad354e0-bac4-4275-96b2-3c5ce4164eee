// uploadManager.js
import { requestClient } from '#/api/request';
import { useAppConfig } from "@vben/hooks";


const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);
class FolderUploadManager {
  constructor(file, projectResultId='',appendixCategory='1',parentAppendixId='0',chunkSize = 5 * 1024 * 1024) {
    this.file = file;
    this.chunkSize = chunkSize;
    this.projectResultId = projectResultId;
    this.appendixCategory = appendixCategory;
    this.parentAppendixId = parentAppendixId;
    let fileId = new Date().getTime();
    this.identifier = fileId;
    this.aborted = false;
  }

  abort() {
    this.aborted = true;
  }


  createChunks(file) {
    const chunks = [];
    let start = 0;

    while (start < file.size) {
      chunks.push(file.slice(start, start + this.chunkSize));
      start += this.chunkSize;
    }

    return chunks;
  }



  async start() {
    try {
      // 开始上传文件
      await this.uploadFile(this.file);

    } catch (error) {
      console.error('Upload failed:', error);
      throw error;
    }
  }

  async uploadChunk(fileId,file, chunk, chunkNumber, totalChunks) {
    const formData = new FormData();
    formData.append('file', chunk);
    formData.append('fileId', fileId);
    formData.append('relativePath', file.name);
    formData.append('chunkNumber', chunkNumber);
    formData.append('totalChunks', totalChunks);
    formData.append('chunkSize', chunk.size);
    formData.append('totalSize', file.size);
    formData.append('fileName', file.name);

    const response = await fetch(`${apiURL}/project-result-appendix/upload-chunk/${this.projectResultId}/${this.appendixCategory}`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Chunk upload failed: ${response.statusText}`);
    }
  }

  async uploadFile(file) {
    const chunks = this.createChunks(file);
    const uploads = [];

    let fileId = this.identifier;
    for (let i = 0; i < chunks.length; i++) {
      if (this.aborted) break;

      uploads.push(this.uploadChunk(fileId,file, chunks[i], i + 1, chunks.length));
    }

    await Promise.all(uploads);
  }

}

// progressMonitor.js
class UploadProgressMonitor {
  constructor(identifier, onConnect,onProgress) {
    this.identifier = identifier;
    this.onProgress = onProgress;
    this.onConnect = onConnect;
  }

  async checkProgress() {
    try {

      // Use Server-Sent Events (SSE) to monitor progress
      this.eventSource = new EventSource(`${apiURL}/project-result-appendix/upload-progress/${this.identifier}`);

      this.eventSource.addEventListener('connect', (event) => {
        console.log('收到连接确认消息:', event.data);
        console.log('事件ID:', event.lastEventId);
        // 可以在这里执行一些初始化操作 this.initializeAfterConnection(); });
        this.onConnect && this.onConnect();
      });

      this.eventSource.addEventListener('progress', (event) => {
        console.log(event.data)
        const data = JSON.parse(event.data); // 处理进度数据
        this.onProgress && this.onProgress(data);
      });
      // 监听完成事件
      this.eventSource.addEventListener('complete', (event) => {
        console.log('Complete:', event.data);
        this.onProgress && this.onProgress( {"fileProgress":100,"chunkProgress":100});
        this.stop();
      });

    } catch (error) {
      console.error('Failed to fetch progress:', error);
    }
  }

  async initUpload(folderStructure) {
    const data = await requestClient.post(`/project-result-appendix/init-upload/${this.projectResultId}/${this.appendixCategory}/${this.parentAppendixId}`, folderStructure);
    return data;
  }

  start() {
    this.checkProgress()
  }

  stop() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }
}

// 使用示例
export async function uploadFile(fileInput,onSuccess,projectResultId,appendixCategory) {
  const file = fileInput.files[0];
  const uploader = new FolderUploadManager(file,projectResultId,appendixCategory);

  try {

    // 初始化上传
    uploader.identifier = await uploader.initUpload('/');

    const monitor = new UploadProgressMonitor(
      uploader.identifier,
      ()=>{
        //进度链接创建后，开始上传
        uploader.start();
      },
      (progress) => {
        console.log('-------progress'+JSON.stringify(progress));
        // 更新UI显示总进度
        updateFileProgress(progress.fileProgress);

        if(progress.fileProgress === 100) {
          onSuccess && onSuccess(uploader.identifier);
        }
        // 更新每个文件的进度
        // for (const [path, fileProgress] of Object.entries(
        //   progress.fileProgresses,
        // )) {
        //   updateFileProgress(path, fileProgress.progress);
        // }
      },
    );

    monitor.start();

  } catch (error) {
    console.error('Upload failed:', error);
  }
}

// UI更新示例
function updateTotalProgress(progress) {
  const progressBar = document.querySelector('#folderProgress');
  if(progressBar) {
    progressBar.style.width = `${progress}%`;
    progressBar.textContent = `${progress}%`;
  }
}

function updateFileProgress(progress) {
  const folderProgressArea = document.querySelector('#folderProgressArea');
  const fileProgressBar = document.querySelector('#fileProgress');
  if(folderProgressArea) {
    folderProgressArea.style.visibility  = 'hidden';
  }
  if (fileProgressBar) {
    fileProgressBar.style.width = `${progress}%`;
    fileProgressBar.textContent = `${progress}%`;
  }
}
