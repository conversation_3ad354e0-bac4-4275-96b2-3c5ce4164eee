<template>
  <div class="fixed bottom-4 right-4 z-[9999]">
    <!-- 最小化状态 -->
    <div v-if="isMinimized && hasActiveTasks"
      @click="isMinimized = false"
      class="upload-minimized"
    >
      <loading-outlined spin class="text-primary text-lg" />
      <span class="text-sm">正在上传 ({{ activeTasks.length }}个任务)</span>
    </div>

    <!-- 展开状态 -->
    <div v-else-if="hasActiveTasks" class="upload-expanded">
      <div class="upload-header">
        <div class="font-medium">上传进度</div>
        <div class="flex items-center space-x-2">
          <minus-outlined class="cursor-pointer hover:text-primary" @click="isMinimized = true" />
          <!-- <close-outlined class="cursor-pointer hover:text-primary" @click="handleClose" /> -->
        </div>
      </div>

      <div class="upload-content">
        <div v-for="task in activeTasks" :key="task.id" class="upload-task-item">
          <!-- 文件夹名称和总进度 -->
          <div class="task-header">
            <div class="task-name" :title="task.fileName">
              {{ task.fileName }}
            </div>
            <div v-if="task.fileCount > 1" class="task-progress">总进度：{{ task.folderProgress ? task.folderProgress : '0' }}%</div>
          </div>

          <!-- 当前文件进度 -->
          <div class="task-progress-container">
            <div class="task-progress-header">
              <div class="task-progress-text">
                当前文件进度：
              </div>
            </div>
            <a-progress
              :percent="task.fileProgress"
              size="small"
              :status="'active'"
              :stroke-color="{ from: 'hsl(var(--primary))', to: 'hsl(var(--success))' }"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { LoadingOutlined, MinusOutlined, CloseOutlined } from '@ant-design/icons-vue';
import { useUploadStore } from '#/stores/modules/upload';

const isMinimized = ref(false);
const uploadStore = useUploadStore();

// 只显示正在上传和出错的任务
const activeTasks = computed(() =>
  uploadStore.tasks.filter(task =>
    task.status === 'uploading' || task.status === 'error'
  )
);

const hasActiveTasks = computed(() => {
  return activeTasks.value.length > 0;
});

const handleClose = () => {
  uploadStore.clearCompletedTasks();
  if (!hasActiveTasks.value) {
    isMinimized.value = false;
  }
};

// 监听任务变化，当有新任务时自动展开
watch(() => uploadStore.tasks.length, (newLength, oldLength = 0) => {
  if (newLength > oldLength) {
    isMinimized.value = false;
  }
}, { immediate: true });

// 自动清理已完成的任务
watch(() => uploadStore.tasks, (tasks) => {
  const completedTasks = tasks.filter(task => task.status === 'completed');
  if (completedTasks.length > 0) {
    setTimeout(() => {
      uploadStore.clearCompletedTasks();
    }, 3000); // 3秒后自动清理已完成的任务
  }
}, { deep: true });
</script>

<style lang="less" scoped>
.upload-minimized {
  @apply bg-white rounded-lg shadow-lg p-3 cursor-pointer hover:shadow-xl transition-shadow flex items-center space-x-2;
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
}

.upload-expanded {
  @apply bg-white rounded-lg shadow-lg w-96;
  background: hsl(var(--background));
  border: 1px solid hsl(var(--border));
}

.upload-header {
  @apply p-3 border-b flex items-center justify-between;
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
}

.upload-content {
  @apply max-h-96 overflow-y-auto;
  background: hsl(var(--background));

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }
}

.upload-task-item {
  @apply p-3 border-b last:border-b-0;
  border-color: hsl(var(--border));
  color: hsl(var(--foreground));
}

.task-header {
  @apply flex items-center justify-between mb-2;
}

.task-name {
  @apply text-sm font-medium truncate flex-1;
  color: hsl(var(--foreground));
}

.task-progress {
  @apply text-xs ml-2;
  color: hsl(var(--muted-foreground));
}

.task-progress-container {
  @apply mt-2 p-2 rounded;
  background: hsl(var(--muted));
}

.task-progress-header {
  @apply flex items-center justify-between mb-1;
}

.task-progress-text {
  @apply text-xs truncate flex-1;
  color: hsl(var(--muted-foreground));
}

.text-primary {
  color: hsl(var(--primary));
}

.hover\:text-primary:hover {
  color: hsl(var(--primary));
}

.upload-progress-enter-active,
.upload-progress-leave-active {
  transition: all 0.3s ease;
}

.upload-progress-enter-from,
.upload-progress-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

// 覆盖 ant-design-vue 进度条样式
:deep(.ant-progress) {
  .ant-progress-text {
    color: hsl(var(--foreground));
  }

  .ant-progress-outer {
    .ant-progress-inner {
      background-color: hsl(var(--muted));
    }
  }
}
</style>
