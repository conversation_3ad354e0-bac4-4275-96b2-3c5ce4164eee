import { defineStore } from 'pinia';
import { ref } from 'vue';

interface UploadTask {
  id: string;
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  fileCount: number;
  folderProgress?: number;
  fileProgress?: number;
}

export const useUploadStore = defineStore('upload', () => {
  const tasks = ref<UploadTask[]>([]);

  // 添加上传任务
  const addTask = (fileName: string, options: { fileCount: number } = { fileCount: 1 }) => {
    const task: UploadTask = {
      id: Date.now().toString(),
      fileName,
      progress: 0,
      status: 'uploading',
      fileCount: options.fileCount
    };
    tasks.value.push(task);
    return task.id;
  };

  // 更新任务进度
  const updateProgress = (taskId: string, progress: object) => {
    const task = tasks.value.find(t => t.id === taskId);
    if (task) {
      if(progress.folderProgress){
        task.folderProgress = Math.min(Math.round(progress.folderProgress), 100);
      }
      if(progress.fileProgress){
        task.fileProgress = Math.min(Math.round(progress.fileProgress), 100);
      }
    }
  };

  // 设置任务状态
  const setTaskStatus = (taskId: string, status: UploadTask['status']) => {
    const task = tasks.value.find(t => t.id === taskId);
    if (task) {
      task.status = status;
    }
  };

  // 清除已完成的任务
  const clearCompletedTasks = () => {
    tasks.value = tasks.value.filter(task => task.status === 'uploading');
  };

  // 移除指定任务
  const removeTask = (taskId: string) => {
    tasks.value = tasks.value.filter(task => task.id !== taskId);
  };

  // 重置 store 状态
  const $reset = () => {
    tasks.value = [];
  };

  return {
    tasks,
    addTask,
    updateProgress,
    setTaskStatus,
    clearCompletedTasks,
    removeTask,
    $reset
  };
});
