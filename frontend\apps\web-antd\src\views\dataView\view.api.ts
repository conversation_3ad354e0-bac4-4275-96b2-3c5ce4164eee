import { requestClient } from '#/api/request';
import { useAppConfig } from "@vben/hooks";
import { appendTokenToUrl } from "#/views/common/util";

enum Api {
  layers = '/sys/tableWhiteList/delete',
  query = '/layer-data/query',
  queryCoverageLayers = '/layer-data/coverage-layer/page'
}


/**
 * 获取图层列表
 * type:图层类型：1-场景图层；2-实体图层
 * category
 * collectionId	数据集ID，为空时候，返回数据集
 */
export async function getLayers(type,category,collectionId) {
  if(!collectionId) {
    collectionId = '';
  }
  return requestClient.get(`/layer-data/layers/${type}/${category}?collectionId=${collectionId}`,{collectionId:collectionId});
}

/**
 * 发布图层
 * type:图层类型：1-场景图层；2-实体图层
 * layerId
 */
export async function gereleaseLayer(type,layerId) {
  return requestClient.get(`/layer-data/release/${type}/${layerId}`,{});
}


/**
 * 查询列表接口
 * @param params
 */
export const query = (params) => {
  return requestClient.post(Api.query, {
    ...params,
  });
};


/**
 * 查询列表接口
 * @param params
 */
export const queryLayers = (params) => {
  return requestClient.post(Api.queryCoverageLayers, {
    ...params,
  });
};

/**
 * 导出接口
 * @param params
 */
export const exportData = (searchKey) => {
  const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);
  let url = appendTokenToUrl(`${apiURL}/layer-data/export/${searchKey}`);
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', 'merged-content.png');
  document.body.appendChild(link);
  link.click();
};



