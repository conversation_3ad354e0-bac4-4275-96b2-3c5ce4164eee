var Be=Object.defineProperty,De=Object.defineProperties;var Oe=Object.getOwnPropertyDescriptors;var R=Object.getOwnPropertySymbols;var q=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable;var j=(o,e,s)=>e in o?Be(o,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):o[e]=s,f=(o,e)=>{for(var s in e||(e={}))q.call(e,s)&&j(o,s,e[s]);if(R)for(var s of R(e))G.call(e,s)&&j(o,s,e[s]);return o},L=(o,e)=>De(o,Oe(e));var $=(o,e)=>{var s={};for(var a in o)q.call(o,a)&&e.indexOf(a)<0&&(s[a]=o[a]);if(o!=null&&R)for(var a of R(o))e.indexOf(a)<0&&G.call(o,a)&&(s[a]=o[a]);return s};var k=(o,e,s)=>j(o,typeof e!="symbol"?e+"":e,s);var X=(o,e,s)=>new Promise((a,l)=>{var r=i=>{try{y(s.next(i))}catch(_){l(_)}},h=i=>{try{y(s.throw(i))}catch(_){l(_)}},y=i=>i.done?a(i.value):Promise.resolve(i.value).then(r,h);y((s=s.apply(o,e)).next())});import{p as ve,x as oe,bA as xe,bB as Se,bC as $e,bD as ke,bE as Ae,bF as Pe,bG as Ee,bH as Ve,bI as Te,bJ as Fe,bK as Ie,bL as Re,bM as Le,bN as Me,aR as je,d as Z,bO as ze,bP as Ne}from"./bootstrap-DShsrVit.js";import{a4 as C,af as d,ag as u,ah as p,ae as m,ai as ae,aj as ne,a3 as t,U as re,am as N,J as K,n as E,al as w,ac as W,aX as B,aZ as v,aB as Ke,az as le,O as We,V as Je,ap as z,an as A,ao as P,aq as Y,b5 as Ue,i as He,W as qe,h as Q,q as Ge}from"../jse/index-index-BMh_AyeW.js";import{X as Xe}from"./x-B-ntYT_e.js";import{V as Ze}from"./loading-Cqdke3S1.js";const Ye=ve("fixed z-[1000] bg-background shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500 border-border",{defaultVariants:{side:"right"},variants:{side:{bottom:"inset-x-0 bottom-0 border-t border-border data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left ",right:"inset-y-0 right-0 h-full w-3/4 border-l  data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right",top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top"}}}),Qe=C({__name:"Sheet",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(o,{emit:e}){const l=oe(o,e);return(r,h)=>(d(),u(t(xe),ae(ne(t(l))),{default:p(()=>[m(r.$slots,"default")]),_:3},16))}}),et=C({__name:"SheetClose",props:{asChild:{type:Boolean},as:{}},setup(o){const e=o;return(s,a)=>(d(),u(t(Se),ae(ne(e)),{default:p(()=>[m(s.$slots,"default")]),_:3},16))}}),tt=["data-dismissable-drawer"],st=C({__name:"SheetOverlay",setup(o){$e();const e=re("DISMISSABLE_DRAWER_ID");return(s,a)=>(d(),N("div",{"data-dismissable-drawer":t(e),class:"bg-overlay fixed inset-0 z-[1000]"},null,8,tt))}}),ot=C({inheritAttrs:!1,__name:"SheetContent",props:{class:{},modal:{type:Boolean},open:{type:Boolean},side:{},forceMount:{type:Boolean},trapFocus:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(o,{emit:e}){const s=o,a=e,l=K(()=>{const b=s,{class:h,modal:y,open:i,side:_}=b;return $(b,["class","modal","open","side"])}),r=oe(l,a);return(h,y)=>(d(),u(t(Pe),null,{default:p(()=>[E(ke,{name:"fade"},{default:p(()=>[h.open&&h.modal?(d(),u(st,{key:0})):w("",!0)]),_:1}),E(t(Ae),W({class:t(B)(t(Ye)({side:h.side}),"z-[1000]",s.class)},f(f({},t(r)),h.$attrs)),{default:p(()=>[m(h.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),ee=C({__name:"SheetDescription",props:{class:{},asChild:{type:Boolean},as:{}},setup(o){const e=o,s=K(()=>{const r=e,{class:a}=r;return $(r,["class"])});return(a,l)=>(d(),u(t(Ee),W({class:t(B)("text-muted-foreground text-sm",e.class)},s.value),{default:p(()=>[m(a.$slots,"default")]),_:3},16,["class"]))}}),at=C({__name:"SheetFooter",props:{class:{}},setup(o){const e=o;return(s,a)=>(d(),N("div",{class:v(t(B)("flex flex-col-reverse sm:flex-row sm:justify-end sm:gap-x-2",e.class))},[m(s.$slots,"default")],2))}}),nt=C({__name:"SheetHeader",props:{class:{}},setup(o){const e=o;return(s,a)=>(d(),N("div",{class:v(t(B)("flex flex-col text-center sm:text-left",e.class))},[m(s.$slots,"default")],2))}}),te=C({__name:"SheetTitle",props:{class:{},asChild:{type:Boolean},as:{}},setup(o){const e=o,s=K(()=>{const r=e,{class:a}=r;return $(r,["class"])});return(a,l)=>(d(),u(t(Ve),W({class:t(B)("text-foreground font-medium",e.class)},s.value),{default:p(()=>[m(a.$slots,"default")]),_:3},16,["class"]))}}),rt={class:"flex-center"},lt=C({__name:"drawer",props:{drawerApi:{default:void 0},cancelText:{},class:{},closable:{type:Boolean},closeOnClickModal:{type:Boolean},closeOnPressEscape:{type:Boolean},confirmLoading:{type:Boolean},confirmText:{},contentClass:{},description:{},footer:{type:Boolean},footerClass:{},header:{type:Boolean},headerClass:{},loading:{type:Boolean},modal:{type:Boolean},openAutoFocus:{type:Boolean},showCancelButton:{type:Boolean},showConfirmButton:{type:Boolean},title:{},titleTooltip:{}},setup(o){var F,H;const e=o,s=Te.getComponents(),a=Ke();le("DISMISSABLE_DRAWER_ID",a);const l=We(),{$t:r}=Fe(),{isMobile:h}=Ie(),y=(H=(F=e.drawerApi)==null?void 0:F.useStore)==null?void 0:H.call(F),{cancelText:i,class:_,closable:c,closeOnClickModal:b,closeOnPressEscape:D,confirmLoading:O,confirmText:ie,contentClass:ce,description:V,footer:de,footerClass:ue,header:pe,headerClass:fe,loading:M,modal:me,openAutoFocus:he,showCancelButton:_e,showConfirmButton:ye,title:T,titleTooltip:J}=Re(e,y);Je(()=>M.value,n=>{n&&l.value&&l.value.scrollTo({top:0})});function be(n){b.value||n.preventDefault()}function we(n){D.value||n.preventDefault()}function Ce(n){const g=n.target,I=g==null?void 0:g.dataset.dismissableDrawer;(!b.value||I!==a)&&n.preventDefault()}function ge(n){he.value||n==null||n.preventDefault()}function U(n){n.preventDefault(),n.stopPropagation()}return(n,g)=>{var I;return d(),u(t(Qe),{modal:!1,open:(I=t(y))==null?void 0:I.isOpen,"onUpdate:open":g[2]||(g[2]=()=>{var x;return(x=n.drawerApi)==null?void 0:x.close()})},{default:p(()=>{var x;return[E(t(ot),{class:v(t(B)("flex w-[520px] flex-col",t(_),{"!w-full":t(h)})),modal:t(me),open:(x=t(y))==null?void 0:x.isOpen,onCloseAutoFocus:U,onEscapeKeyDown:we,onFocusOutside:U,onInteractOutside:be,onOpenAutoFocus:ge,onPointerDownOutside:Ce},{default:p(()=>[t(pe)?(d(),u(t(nt),{key:0,class:v(t(B)("!flex flex-row items-center justify-between border-b px-6 py-5",t(fe),{"px-4 py-3":t(c)}))},{default:p(()=>[z("div",null,[t(T)?(d(),u(t(te),{key:0,class:"text-left"},{default:p(()=>[m(n.$slots,"title",{},()=>[A(P(t(T))+" ",1),t(J)?(d(),u(t(Le),{key:0,"trigger-class":"pb-1"},{default:p(()=>[A(P(t(J)),1)]),_:1})):w("",!0)])]),_:3})):w("",!0),t(V)?(d(),u(t(ee),{key:1,class:"mt-1 text-xs"},{default:p(()=>[m(n.$slots,"description",{},()=>[A(P(t(V)),1)])]),_:3})):w("",!0)]),!t(T)||!t(V)?(d(),u(t(Me),{key:0},{default:p(()=>[t(T)?w("",!0):(d(),u(t(te),{key:0})),t(V)?w("",!0):(d(),u(t(ee),{key:1}))]),_:1})):w("",!0),z("div",rt,[m(n.$slots,"extra"),t(c)?(d(),u(t(et),{key:0,"as-child":"",class:"data-[state=open]:bg-secondary ml-[2px] cursor-pointer rounded-full opacity-80 transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none"},{default:p(()=>[E(t(je),null,{default:p(()=>[E(t(Xe),{class:"size-4"})]),_:1})]),_:1})):w("",!0)])]),_:3},8,["class"])):w("",!0),z("div",{ref_key:"wrapperRef",ref:l,class:v(t(B)("relative flex-1 overflow-y-auto p-3",t(ce),{"overflow-hidden":t(M)}))},[t(M)?(d(),u(t(Ze),{key:0,class:"size-full",spinning:""})):w("",!0),m(n.$slots,"default")],2),t(de)?(d(),u(t(at),{key:1,class:v(t(B)("w-full flex-row items-center justify-end border-t p-2 px-3",t(ue)))},{default:p(()=>[m(n.$slots,"prepend-footer"),m(n.$slots,"footer",{},()=>[t(_e)?(d(),u(Y(t(s).DefaultButton||t(Z)),{key:0,variant:"ghost",onClick:g[0]||(g[0]=()=>{var S;return(S=n.drawerApi)==null?void 0:S.onCancel()})},{default:p(()=>[m(n.$slots,"cancelText",{},()=>[A(P(t(i)||t(r)("cancel")),1)])]),_:3})):w("",!0),t(ye)?(d(),u(Y(t(s).PrimaryButton||t(Z)),{key:1,loading:t(O),onClick:g[1]||(g[1]=()=>{var S;return(S=n.drawerApi)==null?void 0:S.onConfirm()})},{default:p(()=>[m(n.$slots,"confirmText",{},()=>[A(P(t(ie)||t(r)("confirm")),1)])]),_:3},8,["loading"])):w("",!0)]),m(n.$slots,"append-footer")]),_:3},8,["class"])):w("",!0)]),_:3},8,["class","modal","open"])]}),_:3},8,["open"])}}});class it{constructor(e={}){k(this,"api");k(this,"state");k(this,"sharedData",{payload:{}});k(this,"store");const _=e,{connectedComponent:s,onBeforeClose:a,onCancel:l,onConfirm:r,onOpenChange:h}=_,y=$(_,["connectedComponent","onBeforeClose","onCancel","onConfirm","onOpenChange"]),i={class:"",closable:!0,closeOnClickModal:!0,closeOnPressEscape:!0,confirmLoading:!1,contentClass:"",footer:!0,header:!0,isOpen:!1,loading:!1,modal:!0,openAutoFocus:!1,showCancelButton:!0,showConfirmButton:!0,title:""};this.store=new ze(f(f({},i),y),{onUpdate:()=>{var b,D,O;const c=this.store.state;(c==null?void 0:c.isOpen)===((b=this.state)==null?void 0:b.isOpen)?this.state=c:(this.state=c,(O=(D=this.api).onOpenChange)==null||O.call(D,!!(c!=null&&c.isOpen)))}}),this.state=this.store.state,this.api={onBeforeClose:a,onCancel:l,onConfirm:r,onOpenChange:h},Ue(this)}batchStore(e){this.store.batch(e)}close(){var s,a,l;((l=(a=(s=this.api).onBeforeClose)==null?void 0:a.call(s))!=null?l:!0)&&this.store.setState(r=>L(f({},r),{isOpen:!1}))}getData(){var e,s;return(s=(e=this.sharedData)==null?void 0:e.payload)!=null?s:{}}onCancel(){var e,s;this.api.onCancel?(s=(e=this.api).onCancel)==null||s.call(e):this.close()}onConfirm(){var e,s;(s=(e=this.api).onConfirm)==null||s.call(e)}open(){this.store.setState(e=>L(f({},e),{isOpen:!0}))}setData(e){this.sharedData.payload=e}setState(e){He(e)?this.store.setState(e):this.store.setState(s=>f(f({},s),e))}}const se=Symbol("VBEN_DRAWER_INJECT");function ht(o={}){var y;const{connectedComponent:e}=o;if(e){const i=qe({});return[C((c,{attrs:b,slots:D})=>(le(se,{extendApi(O){Object.setPrototypeOf(i,O)},options:o}),ct(i,f(f(f({},c),b),D)),()=>Q(e,f(f({},c),b),D)),{inheritAttrs:!1,name:"VbenParentDrawer"}),i]}const s=re(se,{}),a=f(f({},s.options),o);a.onOpenChange=i=>{var _,c,b;(_=o.onOpenChange)==null||_.call(o,i),(b=(c=s.options)==null?void 0:c.onOpenChange)==null||b.call(c,i)};const l=new it(a),r=l;r.useStore=i=>Ne(l.store,i);const h=C((i,{attrs:_,slots:c})=>()=>Q(lt,L(f(f({},i),_),{drawerApi:r}),c),{inheritAttrs:!1,name:"VbenDrawer"});return(y=s.extendApi)==null||y.call(s,r),[h,r]}function ct(o,e){return X(this,null,function*(){var l;if(!e||Object.keys(e).length===0)return;yield Ge();const s=(l=o==null?void 0:o.store)==null?void 0:l.state;if(!s)return;const a=new Set(Object.keys(s));for(const r of Object.keys(e))a.has(r)&&!["class"].includes(r)&&console.warn(`[Vben Drawer]: When 'connectedComponent' exists, do not set props or slots '${r}', which will increase complexity. If you need to modify the props of Drawer, please use useVbenDrawer or api.`)})}export{ht as u};
