import * as Cesium from 'cesium';
export default {
  pointStyle: {
    color: Cesium.Color.RED,
    pixelSize: 10,
    outlineColor: Cesium.Color.BLACK,
    outlineWidth: 0,
    show: true,
    clampToGround: true,

    //distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0.1, 25000.0)
  },
  markerStyle: {
    image: '',
    scale: 1,
    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
    //pixelOffset: new Cesium.Cartesian2(0, -20),
    eyeOffset: new Cesium.Cartesian3(0.0, 0.05, 0.0),
    clampToGround: true,
    //style: Cesium.LabelStyle.FILL_AND_OUTLINE,
    //outlineWidth: 12,
    //verticalOrigin: Cesium.VerticalOrigin.Button,
    distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
      10.0,
      100000.0,
    ),
  },
  rectangleStyle: {
    clampToGround: true,
    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
    outlineWidth: 20,
    verticalOrigin: Cesium.VerticalOrigin.TOP,
    horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
    //	material: Cesium.Color.GREEN.withAlpha(0.5)
  },
  circleStyle: {
    clampToGround: true,
    //	material: Cesium.Color.DARKGOLDENROD.withAlpha(0.5)
    heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
    outlineWidth: 20,
    verticalOrigin: Cesium.VerticalOrigin.TOP,
    horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
    pixelOffset: new Cesium.Cartesian2(0, -50),
  },
  polygonStyle: {
    clampToGround: true,
    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
    outlineWidth: 20,
    verticalOrigin: Cesium.VerticalOrigin.TOP,
    horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
    pixelOffset: new Cesium.Cartesian2(0, -50),
  },
  polylineStyle: {
    clampToGround: true,
    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
    outlineWidth: 20,
    verticalOrigin: Cesium.VerticalOrigin.Button,
    horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
    pixelOffset: new Cesium.Cartesian2(0, -50),
  },
  textStyle: {
    text: '名称',
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
    pixelOffset: new Cesium.Cartesian2(0, -50),
    eyeOffset: new Cesium.Cartesian3(0.0, 0.01, 0.0),
    distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
      10.0,
      100000.0,
    ),
  },
  modelStyle: {
    uri: '/assets/models/Cesium_Man.glb', // 模型路径，自己换成自己的模型
    scale: 1000,
  },
};
