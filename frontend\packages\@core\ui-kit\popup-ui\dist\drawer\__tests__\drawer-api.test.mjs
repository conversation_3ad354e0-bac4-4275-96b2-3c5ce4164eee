import { beforeEach, describe, expect, it, vi } from "vitest";
import { DrawerApi } from "../drawer-api.mjs";
vi.mock("@vben-core/shared/store", () => {
  return {
    isFunction: (fn) => typeof fn === "function",
    Store: class {
      _state;
      options;
      constructor(initialState, options) {
        this._state = initialState;
        this.options = options;
      }
      batch(cb) {
        cb();
      }
      setState(fn) {
        this._state = fn(this._state);
        this.options.onUpdate();
      }
      get state() {
        return this._state;
      }
    }
  };
});
describe("drawerApi", () => {
  let drawerApi;
  let drawerState;
  beforeEach(() => {
    drawerApi = new DrawerApi();
    drawerState = drawerApi.store.state;
  });
  it("should initialize with default state", () => {
    expect(drawerState.isOpen).toBe(false);
    expect(drawerState.cancelText).toBe(void 0);
    expect(drawerState.confirmText).toBe(void 0);
  });
  it("should open the drawer", () => {
    drawerApi.open();
    expect(drawerApi.store.state.isOpen).toBe(true);
  });
  it("should close the drawer if onBeforeClose allows it", () => {
    drawerApi.open();
    drawerApi.close();
    expect(drawerApi.store.state.isOpen).toBe(false);
  });
  it("should not close the drawer if onBeforeClose returns false", () => {
    const onBeforeClose = vi.fn(() => false);
    const drawerApiWithHook = new DrawerApi({ onBeforeClose });
    drawerApiWithHook.open();
    drawerApiWithHook.close();
    expect(drawerApiWithHook.store.state.isOpen).toBe(true);
    expect(onBeforeClose).toHaveBeenCalled();
  });
  it("should trigger onCancel and keep drawer open if onCancel is provided", () => {
    const onCancel = vi.fn();
    const drawerApiWithHook = new DrawerApi({ onCancel });
    drawerApiWithHook.open();
    drawerApiWithHook.onCancel();
    expect(onCancel).toHaveBeenCalled();
    expect(drawerApiWithHook.store.state.isOpen).toBe(true);
  });
  it("should update shared data correctly", () => {
    const testData = { key: "value" };
    drawerApi.setData(testData);
    expect(drawerApi.getData()).toEqual(testData);
  });
  it("should set state correctly using an object", () => {
    drawerApi.setState({ title: "New Title" });
    expect(drawerApi.store.state.title).toBe("New Title");
  });
  it("should set state correctly using a function", () => {
    drawerApi.setState((prev) => ({ ...prev, confirmText: "Yes" }));
    expect(drawerApi.store.state.confirmText).toBe("Yes");
  });
  it("should call onOpenChange when state changes", () => {
    const onOpenChange = vi.fn();
    const drawerApiWithHook = new DrawerApi({ onOpenChange });
    drawerApiWithHook.open();
    expect(onOpenChange).toHaveBeenCalledWith(true);
  });
  it("should batch state updates", () => {
    const batchSpy = vi.spyOn(drawerApi.store, "batch");
    drawerApi.batchStore(() => {
      drawerApi.setState({ title: "Batch Title" });
      drawerApi.setState({ confirmText: "Batch Confirm" });
    });
    expect(batchSpy).toHaveBeenCalled();
    expect(drawerApi.store.state.title).toBe("Batch Title");
    expect(drawerApi.store.state.confirmText).toBe("Batch Confirm");
  });
});
