import { Entity, Column, Index, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '@/common/entities/base.entity';
import { Role } from '@/modules/roles/entities/role.entity';

@Entity('organizations')
@Index(['parentId'])
@Index(['code'])
@Index(['status'])
@Index(['sortOrder'])
export class Organization extends BaseEntity {
  @ApiProperty({ description: '机构名称' })
  @Column({
    type: 'varchar',
    length: 100,
    comment: '机构名称',
  })
  name: string;

  @ApiProperty({ description: '机构编码' })
  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: '机构编码',
  })
  code: string;

  @ApiProperty({ description: '上级机构ID，0为根节点' })
  @Column({
    name: 'parent_id',
    type: 'bigint',
    default: 0,
    comment: '上级机构ID，0为根节点',
  })
  parentId: number;

  @ApiProperty({ description: '类别：department部门，office科室' })
  @Column({
    type: 'enum',
    enum: ['department', 'office'],
    default: 'department',
    comment: '类别：department部门，office科室',
  })
  type: string;

  @ApiProperty({ description: '排序' })
  @Column({
    name: 'sort_order',
    type: 'int',
    default: 0,
    comment: '排序',
  })
  sortOrder: number;

  @ApiProperty({ description: '默认角色ID' })
  @Column({
    name: 'default_role_id',
    type: 'bigint',
    nullable: true,
    comment: '默认角色ID',
  })
  defaultRoleId?: number;

  @ApiProperty({ description: '状态：1启用，0停用' })
  @Column({
    type: 'tinyint',
    default: 1,
    comment: '状态：1启用，0停用',
  })
  status: number;

  @ApiProperty({ description: '描述' })
  @Column({
    type: 'text',
    nullable: true,
    comment: '描述',
  })
  description?: string;

  // 关联关系
  @ManyToOne(() => Organization, (org) => org.children)
  @JoinColumn({ name: 'parent_id' })
  parent?: Organization;

  @OneToMany(() => Organization, (org) => org.parent)
  children?: Organization[];

  @ManyToOne(() => Role)
  @JoinColumn({ name: 'default_role_id' })
  defaultRole?: Role;
}
