import { requestClient } from '#/api/request';

enum Api {
  deleteOne = '/sys/tableWhiteList/delete',
  edit = '/sys/tableWhiteList/edit',
  list = '/data/scene',
  logs = '/data/logs',
  metadata = '/data/metadata',
  preview = '/data/preview',
  save = '/sys/tableWhiteList/add',
}

/**
 * 列表接口
 * @param params
 */
export const list = (params) => {
  return requestClient.get(Api.list, {
    params,
    headers: {
      ignoreCancelToken: true,
    },
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return requestClient.post(url, { params });
};

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return requestClient.delete(Api.deleteOne, { params }).then(() => {
    handleSuccess();
  });
};

export const getMetadata = (params) => {
  return requestClient.get(Api.metadata, {
    params,
    headers: {
      ignoreCancelToken: true,
    },
  });
};

export const getPreview = (params) => {
  return requestClient.get(Api.preview, {
    params,
    headers: {
      ignoreCancelToken: true,
    },
  });
};

export const getLogs = (params) => {
  return requestClient.get(Api.logs, {
    params,
    headers: {
      ignoreCancelToken: true,
    },
  });
};
