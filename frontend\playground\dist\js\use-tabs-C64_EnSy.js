var V=Object.defineProperty,q=Object.defineProperties;var M=Object.getOwnPropertyDescriptors;var p=Object.getOwnPropertySymbols;var R=Object.prototype.hasOwnProperty,_=Object.prototype.propertyIsEnumerable;var P=(t,a,e)=>a in t?V(t,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[a]=e,r=(t,a)=>{for(var e in a||(a={}))R.call(a,e)&&P(t,e,a[e]);if(p)for(var e of p(a))_.call(a,e)&&P(t,e,a[e]);return t},x=(t,a)=>q(t,M(a));var A=(t,a)=>{var e={};for(var s in t)R.call(t,s)&&a.indexOf(s)<0&&(e[s]=t[s]);if(t!=null&&p)for(var s of p(t))a.indexOf(s)<0&&_.call(t,s)&&(e[s]=t[s]);return e};var c=(t,a,e)=>new Promise((s,i)=>{var n=T=>{try{b(e.next(T))}catch(d){i(d)}},h=T=>{try{b(e.throw(T))}catch(d){i(d)}},b=T=>T.done?s(T.value):Promise.resolve(T.value).then(n,h);b((e=e.apply(t,a)).next())});import{b8 as F,u as J,b9 as j}from"./bootstrap-DShsrVit.js";import{k as z,bJ as G,bm as H,bn as Q}from"../jse/index-index-BMh_AyeW.js";const X=F("core-tabbar",{actions:{_bulkCloseByPaths(t){return c(this,null,function*(){this.tabs=this.tabs.filter(a=>!t.includes(o(a))),this.updateCacheTabs()})},_close(t){const{fullPath:a}=t;if(l(t))return;const e=this.tabs.findIndex(s=>s.fullPath===a);e!==-1&&this.tabs.splice(e,1)},_goToDefaultTab(t){return c(this,null,function*(){if(this.getTabs.length<=0)return;const a=this.getTabs[0];a&&(yield this._goToTab(a,t))})},_goToTab(t,a){return c(this,null,function*(){const{params:e,path:s,query:i}=t,n={params:e||{},path:s,query:i||{}};yield a.replace(n)})},addTab(t){var s,i;const a=Y(t);if(!Z(a))return;const e=this.tabs.findIndex(n=>o(n)===o(t));if(e===-1){const n=(i=(s=t==null?void 0:t.meta)==null?void 0:s.maxNumOfOpenTab)!=null?i:-1;if(n>0&&this.tabs.filter(h=>h.name===t.name).length>=n){const h=this.tabs.findIndex(b=>b.name===t.name);h!==-1&&this.tabs.splice(h,1)}this.tabs.push(a)}else{const n=z(this.tabs)[e],h=x(r(r({},n),a),{meta:r(r({},n==null?void 0:n.meta),a.meta)});if(n){const b=n.meta;Reflect.has(b,"affixTab")&&(h.meta.affixTab=b.affixTab),Reflect.has(b,"newTabTitle")&&(h.meta.newTabTitle=b.newTabTitle)}this.tabs.splice(e,1,h)}this.updateCacheTabs()},closeAllTabs(t){return c(this,null,function*(){const a=this.tabs.filter(e=>l(e));this.tabs=a.length>0?a:[...this.tabs].splice(0,1),yield this._goToDefaultTab(t),this.updateCacheTabs()})},closeLeftTabs(t){return c(this,null,function*(){const a=this.tabs.findIndex(i=>o(i)===o(t));if(a<1)return;const e=this.tabs.slice(0,a),s=[];for(const i of e)l(i)||s.push(o(i));yield this._bulkCloseByPaths(s)})},closeOtherTabs(t){return c(this,null,function*(){const a=this.tabs.map(s=>o(s)),e=[];for(const s of a)if(s!==t.fullPath){const i=this.tabs.find(n=>o(n)===s);if(!i)continue;l(i)||e.push(o(i))}yield this._bulkCloseByPaths(e)})},closeRightTabs(t){return c(this,null,function*(){const a=this.tabs.findIndex(e=>o(e)===o(t));if(a!==-1&&a<this.tabs.length-1){const e=this.tabs.slice(a+1),s=[];for(const i of e)l(i)||s.push(o(i));yield this._bulkCloseByPaths(s)}})},closeTab(t,a){return c(this,null,function*(){const{currentRoute:e}=a;if(o(e.value)!==o(t)){this._close(t),this.updateCacheTabs();return}const s=this.getTabs.findIndex(h=>o(h)===o(e.value)),i=this.getTabs[s-1],n=this.getTabs[s+1];n?(this._close(t),yield this._goToTab(n,a)):i?(this._close(t),yield this._goToTab(i,a)):console.error("Failed to close the tab; only one tab remains open.")})},closeTabByKey(t,a){return c(this,null,function*(){const e=decodeURIComponent(t),s=this.tabs.findIndex(n=>o(n)===e);if(s===-1)return;const i=this.tabs[s];i&&(yield this.closeTab(i,a))})},getTabByPath(t){return this.getTabs.find(a=>o(a)===t)},openTabInNewWindow(t){return c(this,null,function*(){G(t.fullPath||t.path)})},pinTab(t){return c(this,null,function*(){var i;const a=this.tabs.findIndex(n=>o(n)===o(t));if(a!==-1){const n=this.tabs[a];t.meta.affixTab=!0,t.meta.title=(i=n==null?void 0:n.meta)==null?void 0:i.title,this.tabs.splice(a,1,t)}const s=this.tabs.filter(n=>l(n)).findIndex(n=>o(n)===o(t));yield this.sortTabs(a,s)})},refresh(t){return c(this,null,function*(){const{currentRoute:a}=t,{name:e}=a.value;this.excludeCachedTabs.add(e),this.renderRouteView=!1,H(),yield new Promise(s=>setTimeout(s,200)),this.excludeCachedTabs.delete(e),this.renderRouteView=!0,Q()})},resetTabTitle(t){return c(this,null,function*(){var e;if((e=t==null?void 0:t.meta)!=null&&e.newTabTitle)return;const a=this.tabs.find(s=>o(s)===o(t));a&&(a.meta.newTabTitle=void 0,yield this.updateCacheTabs())})},setAffixTabs(t){for(const a of t)a.meta.affixTab=!0,this.addTab($(a))},setTabTitle(t,a){return c(this,null,function*(){const e=this.tabs.find(s=>o(s)===o(t));e&&(e.meta.newTabTitle=a,yield this.updateCacheTabs())})},setUpdateTime(){this.updateTime=Date.now()},sortTabs(t,a){return c(this,null,function*(){const e=this.tabs[t];e&&(this.tabs.splice(t,1),this.tabs.splice(a,0,e),this.dragEndIndex=this.dragEndIndex+1)})},toggleTabPin(t){return c(this,null,function*(){var e,s;yield((s=(e=t==null?void 0:t.meta)==null?void 0:e.affixTab)!=null?s:!1)?this.unpinTab(t):this.pinTab(t)})},unpinTab(t){return c(this,null,function*(){var i;const a=this.tabs.findIndex(n=>o(n)===o(t));if(a!==-1){const n=this.tabs[a];t.meta.affixTab=!1,t.meta.title=(i=n==null?void 0:n.meta)==null?void 0:i.title,this.tabs.splice(a,1,t)}const s=this.tabs.filter(n=>l(n)).length;yield this.sortTabs(a,s)})},updateCacheTabs(){return c(this,null,function*(){var a;const t=new Set;for(const e of this.tabs){if(!((a=e.meta)==null?void 0:a.keepAlive))continue;(e.matched||[]).forEach((n,h)=>{h>0&&t.add(n.name)});const i=e.name;t.add(i)}this.cachedTabs=t})}},getters:{affixTabs(){return this.tabs.filter(a=>l(a)).sort((a,e)=>{var n,h,b,T;const s=(h=(n=a.meta)==null?void 0:n.affixTabOrder)!=null?h:0,i=(T=(b=e.meta)==null?void 0:b.affixTabOrder)!=null?T:0;return s-i})},getCachedTabs(){return[...this.cachedTabs]},getExcludeCachedTabs(){return[...this.excludeCachedTabs]},getTabs(){const t=this.tabs.filter(a=>!l(a));return[...this.affixTabs,...t].filter(Boolean)}},persist:[{pick:["tabs"],storage:sessionStorage}],state:()=>({cachedTabs:new Set,dragEndIndex:0,excludeCachedTabs:new Set,renderRouteView:!0,tabs:[],updateTime:Date.now()})});function Y(t){if(!t)return t;const i=t,{matched:a,meta:e}=i,s=A(i,["matched","meta"]);return x(r({},s),{matched:a?a.map(n=>({meta:n.meta,name:n.name,path:n.path})):void 0,meta:x(r({},e),{newTabTitle:e.newTabTitle})})}function l(t){var a,e;return(e=(a=t==null?void 0:t.meta)==null?void 0:a.affixTab)!=null?e:!1}function Z(t){var e;const a=(e=t==null?void 0:t.matched)!=null?e:[];return!t.meta.hideInTab&&a.every(s=>!s.meta.hideInTab)}function o(t){return decodeURIComponent(t.fullPath||t.path)}function $(t){return{meta:t.meta,name:t.name,path:t.path}}function st(){const t=J(),a=j(),e=X();function s(f){return c(this,null,function*(){yield e.closeLeftTabs(f||a)})}function i(){return c(this,null,function*(){yield e.closeAllTabs(t)})}function n(f){return c(this,null,function*(){yield e.closeRightTabs(f||a)})}function h(f){return c(this,null,function*(){yield e.closeOtherTabs(f||a)})}function b(f){return c(this,null,function*(){yield e.closeTab(f||a,t)})}function T(f){return c(this,null,function*(){yield e.pinTab(f||a)})}function d(f){return c(this,null,function*(){yield e.unpinTab(f||a)})}function B(f){return c(this,null,function*(){yield e.toggleTabPin(f||a)})}function O(){return c(this,null,function*(){yield e.refresh(t)})}function k(f){return c(this,null,function*(){yield e.openTabInNewWindow(f||a)})}function S(f){return c(this,null,function*(){yield e.closeTabByKey(f,t)})}function v(f){return c(this,null,function*(){e.setUpdateTime(),yield e.setTabTitle(a,f)})}function N(){return c(this,null,function*(){e.setUpdateTime(),yield e.resetTabTitle(a)})}function D(f=a){var I;const u=e.getTabs,C=e.affixTabs,w=u.findIndex(W=>W.path===f.path),g=u.length<=1,{meta:y}=f,E=(I=y==null?void 0:y.affixTab)!=null?I:!1,m=a.path===f.path,U=w===0||w-C.length<=0||!m,K=!m||w===u.length-1,L=g||!m||u.length-C.length<=1;return{disabledCloseAll:g,disabledCloseCurrent:!!E||g,disabledCloseLeft:U,disabledCloseOther:L,disabledCloseRight:K,disabledRefresh:!m}}return{closeAllTabs:i,closeCurrentTab:b,closeLeftTabs:s,closeOtherTabs:h,closeRightTabs:n,closeTabByKey:S,getTabDisableState:D,openTabInNewWindow:k,pinTab:T,refreshTab:O,resetTabTitle:N,setTabTitle:v,toggleTabPin:B,unpinTab:d}}export{X as a,st as u};
