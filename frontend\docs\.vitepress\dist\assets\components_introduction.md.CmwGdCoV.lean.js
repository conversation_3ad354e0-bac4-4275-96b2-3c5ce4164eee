import{ao as a,k as o,aP as t,l as e,ay as n,j as i}from"./chunks/framework.C8U7mBUf.js";const s=JSON.parse('{"title":"介绍","description":"","frontmatter":{},"headers":[],"relativePath":"components/introduction.md","filePath":"components/introduction.md"}');const r=a({name:"components/introduction.md"},[["render",function(a,s,r,c,l,d){const h=n("NolebaseGitContributors"),m=n("NolebaseGitChangelog");return i(),o("div",null,[s[0]||(s[0]=t('<h1 id="介绍" tabindex="-1">介绍 <a class="header-anchor" href="#介绍" aria-label="Permalink to &quot;介绍&quot;">​</a></h1><div class="info custom-block"><p class="custom-block-title">README</p><p>该文档介绍的是框架组件的使用方法、属性、事件等。如果你觉得现有组件的封装不够理想，或者不完全符合你的需求，大可以直接使用原生组件，亦或亲手封装一个适合的组件。框架提供的组件并非束缚，使用与否，完全取决于你的需求与自由。</p></div><h2 id="通用组件" tabindex="-1">通用组件 <a class="header-anchor" href="#通用组件" aria-label="Permalink to &quot;通用组件&quot;">​</a></h2><p>通用组件是一些常用的组件，比如弹窗、抽屉、表单等。大部分基于 <code>Tailwind CSS</code> 实现，可适用于不同 UI 组件库的应用。</p>',4)),e(h),e(m)])}]]);export{s as __pageData,r as default};
