{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@ctrl+tinycolor@4.1.0/node_modules/@ctrl/tinycolor/dist/module/util.js", "../../../../../node_modules/.pnpm/@ctrl+tinycolor@4.1.0/node_modules/@ctrl/tinycolor/dist/module/conversion.js", "../../../../../node_modules/.pnpm/@ctrl+tinycolor@4.1.0/node_modules/@ctrl/tinycolor/dist/module/css-color-names.js", "../../../../../node_modules/.pnpm/@ctrl+tinycolor@4.1.0/node_modules/@ctrl/tinycolor/dist/module/format-input.js", "../../../../../node_modules/.pnpm/@ctrl+tinycolor@4.1.0/node_modules/@ctrl/tinycolor/dist/module/index.js", "../../../../../node_modules/.pnpm/@ctrl+tinycolor@4.1.0/node_modules/@ctrl/tinycolor/dist/module/readability.js", "../../../../../node_modules/.pnpm/@ctrl+tinycolor@4.1.0/node_modules/@ctrl/tinycolor/dist/module/to-ms-filter.js", "../../../../../node_modules/.pnpm/@ctrl+tinycolor@4.1.0/node_modules/@ctrl/tinycolor/dist/module/from-ratio.js", "../../../../../node_modules/.pnpm/@ctrl+tinycolor@4.1.0/node_modules/@ctrl/tinycolor/dist/module/random.js"], "sourcesContent": ["/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */\nexport function bound01(n, max) {\n    if (isOnePointZero(n)) {\n        n = '100%';\n    }\n    const isPercent = isPercentage(n);\n    n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n    // Automatically convert percentage into number\n    if (isPercent) {\n        n = parseInt(String(n * max), 10) / 100;\n    }\n    // Handle floating point rounding errors\n    if (Math.abs(n - max) < 0.000001) {\n        return 1;\n    }\n    // Convert into [0, 1] range if it isn't already\n    if (max === 360) {\n        // If n is a hue given in degrees,\n        // wrap around out-of-range values into [0, 360] range\n        // then convert into [0, 1].\n        n = (n < 0 ? (n % max) + max : n % max) / parseFloat(String(max));\n    }\n    else {\n        // If n not a hue given in degrees\n        // Convert into [0, 1] range if it isn't already.\n        n = (n % max) / parseFloat(String(max));\n    }\n    return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */\nexport function clamp01(val) {\n    return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */\nexport function isOnePointZero(n) {\n    return typeof n === 'string' && n.indexOf('.') !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */\nexport function isPercentage(n) {\n    return typeof n === 'string' && n.indexOf('%') !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */\nexport function boundAlpha(a) {\n    a = parseFloat(a);\n    if (isNaN(a) || a < 0 || a > 1) {\n        a = 1;\n    }\n    return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */\nexport function convertToPercentage(n) {\n    if (Number(n) <= 1) {\n        return `${Number(n) * 100}%`;\n    }\n    return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */\nexport function pad2(c) {\n    return c.length === 1 ? '0' + c : String(c);\n}\n", "import { bound01, pad2 } from './util.js';\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n/**\n * Handle bounds / percentage checking to conform to CSS color spec\n * <http://www.w3.org/TR/css3-color/>\n * *Assumes:* r, g, b in [0, 255] or [0, 1]\n * *Returns:* { r, g, b } in [0, 255]\n */\nexport function rgbToRgb(r, g, b) {\n    return {\n        r: bound01(r, 255) * 255,\n        g: bound01(g, 255) * 255,\n        b: bound01(b, 255) * 255,\n    };\n}\n/**\n * Converts an RGB color value to HSL.\n * *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n * *Returns:* { h, s, l } in [0,1]\n */\nexport function rgbToHsl(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    const max = Math.max(r, g, b);\n    const min = Math.min(r, g, b);\n    let h = 0;\n    let s = 0;\n    const l = (max + min) / 2;\n    if (max === min) {\n        s = 0;\n        h = 0; // achromatic\n    }\n    else {\n        const d = max - min;\n        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h, s, l };\n}\nfunction hue2rgb(p, q, t) {\n    if (t < 0) {\n        t += 1;\n    }\n    if (t > 1) {\n        t -= 1;\n    }\n    if (t < 1 / 6) {\n        return p + (q - p) * (6 * t);\n    }\n    if (t < 1 / 2) {\n        return q;\n    }\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n    return p;\n}\n/**\n * Converts an HSL color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hslToRgb(h, s, l) {\n    let r;\n    let g;\n    let b;\n    h = bound01(h, 360);\n    s = bound01(s, 100);\n    l = bound01(l, 100);\n    if (s === 0) {\n        // achromatic\n        g = l;\n        b = l;\n        r = l;\n    }\n    else {\n        const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n        const p = 2 * l - q;\n        r = hue2rgb(p, q, h + 1 / 3);\n        g = hue2rgb(p, q, h);\n        b = hue2rgb(p, q, h - 1 / 3);\n    }\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color value to HSV\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n * *Returns:* { h, s, v } in [0,1]\n */\nexport function rgbToHsv(r, g, b) {\n    r = bound01(r, 255);\n    g = bound01(g, 255);\n    b = bound01(b, 255);\n    const max = Math.max(r, g, b);\n    const min = Math.min(r, g, b);\n    let h = 0;\n    const v = max;\n    const d = max - min;\n    const s = max === 0 ? 0 : d / max;\n    if (max === min) {\n        h = 0; // achromatic\n    }\n    else {\n        switch (max) {\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return { h, s, v };\n}\n/**\n * Converts an HSV color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */\nexport function hsvToRgb(h, s, v) {\n    h = bound01(h, 360) * 6;\n    s = bound01(s, 100);\n    v = bound01(v, 100);\n    const i = Math.floor(h);\n    const f = h - i;\n    const p = v * (1 - s);\n    const q = v * (1 - f * s);\n    const t = v * (1 - (1 - f) * s);\n    const mod = i % 6;\n    const r = [v, q, p, p, t, v][mod];\n    const g = [t, v, v, q, p, p][mod];\n    const b = [p, p, t, v, v, q][mod];\n    return { r: r * 255, g: g * 255, b: b * 255 };\n}\n/**\n * Converts an RGB color to hex\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255]\n * *Returns:* a 3 or 6 character hex\n */\nexport function rgbToHex(r, g, b, allow3Char) {\n    const hex = [\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n    ];\n    // Return a 3 character hex if possible\n    if (allow3Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color plus alpha transparency to hex\n *\n * *Assumes:* r, g, b are contained in the set [0, 255] and a in [0, 1]\n * *Returns:* a 4 or 8 character rgba hex\n */\n// eslint-disable-next-line max-params\nexport function rgbaToHex(r, g, b, a, allow4Char) {\n    const hex = [\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n        pad2(convertDecimalToHex(a)),\n    ];\n    // Return a 4 character hex if possible\n    if (allow4Char &&\n        hex[0].startsWith(hex[0].charAt(1)) &&\n        hex[1].startsWith(hex[1].charAt(1)) &&\n        hex[2].startsWith(hex[2].charAt(1)) &&\n        hex[3].startsWith(hex[3].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n    }\n    return hex.join('');\n}\n/**\n * Converts an RGBA color to an ARGB Hex8 string\n * Rarely used, but required for \"toFilter()\"\n *\n * *Assumes:* r, g, b are contained in the set [0, 255] and a in [0, 1]\n * *Returns:* a 8 character argb hex\n */\nexport function rgbaToArgbHex(r, g, b, a) {\n    const hex = [\n        pad2(convertDecimalToHex(a)),\n        pad2(Math.round(r).toString(16)),\n        pad2(Math.round(g).toString(16)),\n        pad2(Math.round(b).toString(16)),\n    ];\n    return hex.join('');\n}\n/**\n * Converts CMYK to RBG\n * Assumes c, m, y, k are in the set [0, 100]\n */\nexport function cmykToRgb(c, m, y, k) {\n    const cConv = c / 100;\n    const mConv = m / 100;\n    const yConv = y / 100;\n    const kConv = k / 100;\n    const r = 255 * (1 - cConv) * (1 - kConv);\n    const g = 255 * (1 - mConv) * (1 - kConv);\n    const b = 255 * (1 - yConv) * (1 - kConv);\n    return { r, g, b };\n}\nexport function rgbToCmyk(r, g, b) {\n    let c = 1 - r / 255;\n    let m = 1 - g / 255;\n    let y = 1 - b / 255;\n    let k = Math.min(c, m, y);\n    if (k === 1) {\n        c = 0;\n        m = 0;\n        y = 0;\n    }\n    else {\n        c = ((c - k) / (1 - k)) * 100;\n        m = ((m - k) / (1 - k)) * 100;\n        y = ((y - k) / (1 - k)) * 100;\n    }\n    k *= 100;\n    return {\n        c: Math.round(c),\n        m: Math.round(m),\n        y: Math.round(y),\n        k: Math.round(k),\n    };\n}\n/** Converts a decimal to a hex value */\nexport function convertDecimalToHex(d) {\n    return Math.round(parseFloat(d) * 255).toString(16);\n}\n/** Converts a hex value to a decimal */\nexport function convertHexToDecimal(h) {\n    return parseIntFromHex(h) / 255;\n}\n/** Parse a base-16 hex value into a base-10 integer */\nexport function parseIntFromHex(val) {\n    return parseInt(val, 16);\n}\nexport function numberInputToObject(color) {\n    return {\n        r: color >> 16,\n        g: (color & 0xff00) >> 8,\n        b: color & 0xff,\n    };\n}\n", "// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */\nexport const names = {\n    aliceblue: '#f0f8ff',\n    antiquewhite: '#faebd7',\n    aqua: '#00ffff',\n    aquamarine: '#7fffd4',\n    azure: '#f0ffff',\n    beige: '#f5f5dc',\n    bisque: '#ffe4c4',\n    black: '#000000',\n    blanchedalmond: '#ffebcd',\n    blue: '#0000ff',\n    blueviolet: '#8a2be2',\n    brown: '#a52a2a',\n    burlywood: '#deb887',\n    cadetblue: '#5f9ea0',\n    chartreuse: '#7fff00',\n    chocolate: '#d2691e',\n    coral: '#ff7f50',\n    cornflowerblue: '#6495ed',\n    cornsilk: '#fff8dc',\n    crimson: '#dc143c',\n    cyan: '#00ffff',\n    darkblue: '#00008b',\n    darkcyan: '#008b8b',\n    darkgoldenrod: '#b8860b',\n    darkgray: '#a9a9a9',\n    darkgreen: '#006400',\n    darkgrey: '#a9a9a9',\n    darkkhaki: '#bdb76b',\n    darkmagenta: '#8b008b',\n    darkolivegreen: '#556b2f',\n    darkorange: '#ff8c00',\n    darkorchid: '#9932cc',\n    darkred: '#8b0000',\n    darksalmon: '#e9967a',\n    darkseagreen: '#8fbc8f',\n    darkslateblue: '#483d8b',\n    darkslategray: '#2f4f4f',\n    darkslategrey: '#2f4f4f',\n    darkturquoise: '#00ced1',\n    darkviolet: '#9400d3',\n    deeppink: '#ff1493',\n    deepskyblue: '#00bfff',\n    dimgray: '#696969',\n    dimgrey: '#696969',\n    dodgerblue: '#1e90ff',\n    firebrick: '#b22222',\n    floralwhite: '#fffaf0',\n    forestgreen: '#228b22',\n    fuchsia: '#ff00ff',\n    gainsboro: '#dcdcdc',\n    ghostwhite: '#f8f8ff',\n    goldenrod: '#daa520',\n    gold: '#ffd700',\n    gray: '#808080',\n    green: '#008000',\n    greenyellow: '#adff2f',\n    grey: '#808080',\n    honeydew: '#f0fff0',\n    hotpink: '#ff69b4',\n    indianred: '#cd5c5c',\n    indigo: '#4b0082',\n    ivory: '#fffff0',\n    khaki: '#f0e68c',\n    lavenderblush: '#fff0f5',\n    lavender: '#e6e6fa',\n    lawngreen: '#7cfc00',\n    lemonchiffon: '#fffacd',\n    lightblue: '#add8e6',\n    lightcoral: '#f08080',\n    lightcyan: '#e0ffff',\n    lightgoldenrodyellow: '#fafad2',\n    lightgray: '#d3d3d3',\n    lightgreen: '#90ee90',\n    lightgrey: '#d3d3d3',\n    lightpink: '#ffb6c1',\n    lightsalmon: '#ffa07a',\n    lightseagreen: '#20b2aa',\n    lightskyblue: '#87cefa',\n    lightslategray: '#778899',\n    lightslategrey: '#778899',\n    lightsteelblue: '#b0c4de',\n    lightyellow: '#ffffe0',\n    lime: '#00ff00',\n    limegreen: '#32cd32',\n    linen: '#faf0e6',\n    magenta: '#ff00ff',\n    maroon: '#800000',\n    mediumaquamarine: '#66cdaa',\n    mediumblue: '#0000cd',\n    mediumorchid: '#ba55d3',\n    mediumpurple: '#9370db',\n    mediumseagreen: '#3cb371',\n    mediumslateblue: '#7b68ee',\n    mediumspringgreen: '#00fa9a',\n    mediumturquoise: '#48d1cc',\n    mediumvioletred: '#c71585',\n    midnightblue: '#191970',\n    mintcream: '#f5fffa',\n    mistyrose: '#ffe4e1',\n    moccasin: '#ffe4b5',\n    navajowhite: '#ffdead',\n    navy: '#000080',\n    oldlace: '#fdf5e6',\n    olive: '#808000',\n    olivedrab: '#6b8e23',\n    orange: '#ffa500',\n    orangered: '#ff4500',\n    orchid: '#da70d6',\n    palegoldenrod: '#eee8aa',\n    palegreen: '#98fb98',\n    paleturquoise: '#afeeee',\n    palevioletred: '#db7093',\n    papayawhip: '#ffefd5',\n    peachpuff: '#ffdab9',\n    peru: '#cd853f',\n    pink: '#ffc0cb',\n    plum: '#dda0dd',\n    powderblue: '#b0e0e6',\n    purple: '#800080',\n    rebeccapurple: '#663399',\n    red: '#ff0000',\n    rosybrown: '#bc8f8f',\n    royalblue: '#4169e1',\n    saddlebrown: '#8b4513',\n    salmon: '#fa8072',\n    sandybrown: '#f4a460',\n    seagreen: '#2e8b57',\n    seashell: '#fff5ee',\n    sienna: '#a0522d',\n    silver: '#c0c0c0',\n    skyblue: '#87ceeb',\n    slateblue: '#6a5acd',\n    slategray: '#708090',\n    slategrey: '#708090',\n    snow: '#fffafa',\n    springgreen: '#00ff7f',\n    steelblue: '#4682b4',\n    tan: '#d2b48c',\n    teal: '#008080',\n    thistle: '#d8bfd8',\n    tomato: '#ff6347',\n    turquoise: '#40e0d0',\n    violet: '#ee82ee',\n    wheat: '#f5deb3',\n    white: '#ffffff',\n    whitesmoke: '#f5f5f5',\n    yellow: '#ffff00',\n    yellowgreen: '#9acd32',\n};\n", "import { cmykToRgb, convertHexToDecimal, hslToRgb, hsvToRgb, parseIntFromHex, rgbToRgb, } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { boundAlpha, convertToPercentage } from './util.js';\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * \"cmyk(0, 20, 0, 0)\" or \"cmyk 0 20 0 0\"\n * ```\n */\nexport function inputToRGB(color) {\n    let rgb = { r: 0, g: 0, b: 0 };\n    let a = 1;\n    let s = null;\n    let v = null;\n    let l = null;\n    let ok = false;\n    let format = false;\n    if (typeof color === 'string') {\n        color = stringInputToObject(color);\n    }\n    if (typeof color === 'object') {\n        if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n            rgb = rgbToRgb(color.r, color.g, color.b);\n            ok = true;\n            format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n            s = convertToPercentage(color.s);\n            v = convertToPercentage(color.v);\n            rgb = hsvToRgb(color.h, s, v);\n            ok = true;\n            format = 'hsv';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n            s = convertToPercentage(color.s);\n            l = convertToPercentage(color.l);\n            rgb = hslToRgb(color.h, s, l);\n            ok = true;\n            format = 'hsl';\n        }\n        else if (isValidCSSUnit(color.c) &&\n            isValidCSSUnit(color.m) &&\n            isValidCSSUnit(color.y) &&\n            isValidCSSUnit(color.k)) {\n            rgb = cmykToRgb(color.c, color.m, color.y, color.k);\n            ok = true;\n            format = 'cmyk';\n        }\n        if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n            a = color.a;\n        }\n    }\n    a = boundAlpha(a);\n    return {\n        ok,\n        format: color.format || format,\n        r: Math.min(255, Math.max(rgb.r, 0)),\n        g: Math.min(255, Math.max(rgb.g, 0)),\n        b: Math.min(255, Math.max(rgb.b, 0)),\n        a,\n    };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nconst CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nconst CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nconst CSS_UNIT = '(?:' + CSS_NUMBER + ')|(?:' + CSS_INTEGER + ')';\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\n// eslint-disable-next-line prettier/prettier\nconst PERMISSIVE_MATCH3 = '[\\\\s|\\\\(]+(' + CSS_UNIT + ')[,|\\\\s]+(' + CSS_UNIT + ')[,|\\\\s]+(' + CSS_UNIT + ')\\\\s*\\\\)?';\nconst PERMISSIVE_MATCH4 = \n// eslint-disable-next-line prettier/prettier\n'[\\\\s|\\\\(]+(' + CSS_UNIT + ')[,|\\\\s]+(' + CSS_UNIT + ')[,|\\\\s]+(' + CSS_UNIT + ')[,|\\\\s]+(' + CSS_UNIT + ')\\\\s*\\\\)?';\nconst matchers = {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n    rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n    hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n    hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n    hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n    hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n    cmyk: new RegExp('cmyk' + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}` or `{c, m, y, k}` or `{c, m, y, k, a}`\n */\nexport function stringInputToObject(color) {\n    color = color.trim().toLowerCase();\n    if (color.length === 0) {\n        return false;\n    }\n    let named = false;\n    if (names[color]) {\n        color = names[color];\n        named = true;\n    }\n    else if (color === 'transparent') {\n        return { r: 0, g: 0, b: 0, a: 0, format: 'name' };\n    }\n    // Try to match string input using regular expressions.\n    // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n    // Just return an object and let the conversion functions handle that.\n    // This way the result will be the same whether the tinycolor is initialized with string or object.\n    let match = matchers.rgb.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3] };\n    }\n    match = matchers.rgba.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3], a: match[4] };\n    }\n    match = matchers.hsl.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3] };\n    }\n    match = matchers.hsla.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3], a: match[4] };\n    }\n    match = matchers.hsv.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3] };\n    }\n    match = matchers.hsva.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3], a: match[4] };\n    }\n    match = matchers.cmyk.exec(color);\n    if (match) {\n        return {\n            c: match[1],\n            m: match[2],\n            y: match[3],\n            k: match[4],\n        };\n    }\n    match = matchers.hex8.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            a: convertHexToDecimal(match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex6.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    match = matchers.hex4.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            a: convertHexToDecimal(match[4] + match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex3.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nexport function isValidCSSUnit(color) {\n    if (typeof color === 'number') {\n        return !Number.isNaN(color);\n    }\n    return matchers.CSS_UNIT.test(color);\n}\n", "import { numberInputToObject, rgbaToHex, rgbToCmyk, rgbToHex, rgbToHsl, rgbToHsv, } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { inputToRGB } from './format-input.js';\nimport { bound01, boundAlpha, clamp01 } from './util.js';\nexport class TinyColor {\n    constructor(color = '', opts = {}) {\n        // If input is already a tinycolor, return itself\n        if (color instanceof TinyColor) {\n            // eslint-disable-next-line no-constructor-return\n            return color;\n        }\n        if (typeof color === 'number') {\n            color = numberInputToObject(color);\n        }\n        this.originalInput = color;\n        const rgb = inputToRGB(color);\n        this.originalInput = color;\n        this.r = rgb.r;\n        this.g = rgb.g;\n        this.b = rgb.b;\n        this.a = rgb.a;\n        this.roundA = Math.round(100 * this.a) / 100;\n        this.format = opts.format ?? rgb.format;\n        this.gradientType = opts.gradientType;\n        // Don't let the range of [0,255] come back in [0,1].\n        // Potentially lose a little bit of precision here, but will fix issues where\n        // .5 gets interpreted as half of the total, instead of half of 1\n        // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n        if (this.r < 1) {\n            this.r = Math.round(this.r);\n        }\n        if (this.g < 1) {\n            this.g = Math.round(this.g);\n        }\n        if (this.b < 1) {\n            this.b = Math.round(this.b);\n        }\n        this.isValid = rgb.ok;\n    }\n    isDark() {\n        return this.getBrightness() < 128;\n    }\n    isLight() {\n        return !this.isDark();\n    }\n    /**\n     * Returns the perceived brightness of the color, from 0-255.\n     */\n    getBrightness() {\n        // http://www.w3.org/TR/AERT#color-contrast\n        const rgb = this.toRgb();\n        return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n    }\n    /**\n     * Returns the perceived luminance of a color, from 0-1.\n     */\n    getLuminance() {\n        // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n        const rgb = this.toRgb();\n        let R;\n        let G;\n        let B;\n        const RsRGB = rgb.r / 255;\n        const GsRGB = rgb.g / 255;\n        const BsRGB = rgb.b / 255;\n        if (RsRGB <= 0.03928) {\n            R = RsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (GsRGB <= 0.03928) {\n            G = GsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (BsRGB <= 0.03928) {\n            B = BsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n        }\n        return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n    }\n    /**\n     * Returns the alpha value of a color, from 0-1.\n     */\n    getAlpha() {\n        return this.a;\n    }\n    /**\n     * Sets the alpha value on the current color.\n     *\n     * @param alpha - The new alpha value. The accepted range is 0-1.\n     */\n    setAlpha(alpha) {\n        this.a = boundAlpha(alpha);\n        this.roundA = Math.round(100 * this.a) / 100;\n        return this;\n    }\n    /**\n     * Returns whether the color is monochrome.\n     */\n    isMonochrome() {\n        const { s } = this.toHsl();\n        return s === 0;\n    }\n    /**\n     * Returns the object as a HSVA object.\n     */\n    toHsv() {\n        const hsv = rgbToHsv(this.r, this.g, this.b);\n        return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this.a };\n    }\n    /**\n     * Returns the hsva values interpolated into a string with the following format:\n     * \"hsva(xxx, xxx, xxx, xx)\".\n     */\n    toHsvString() {\n        const hsv = rgbToHsv(this.r, this.g, this.b);\n        const h = Math.round(hsv.h * 360);\n        const s = Math.round(hsv.s * 100);\n        const v = Math.round(hsv.v * 100);\n        return this.a === 1 ? `hsv(${h}, ${s}%, ${v}%)` : `hsva(${h}, ${s}%, ${v}%, ${this.roundA})`;\n    }\n    /**\n     * Returns the object as a HSLA object.\n     */\n    toHsl() {\n        const hsl = rgbToHsl(this.r, this.g, this.b);\n        return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this.a };\n    }\n    /**\n     * Returns the hsla values interpolated into a string with the following format:\n     * \"hsla(xxx, xxx, xxx, xx)\".\n     */\n    toHslString() {\n        const hsl = rgbToHsl(this.r, this.g, this.b);\n        const h = Math.round(hsl.h * 360);\n        const s = Math.round(hsl.s * 100);\n        const l = Math.round(hsl.l * 100);\n        return this.a === 1 ? `hsl(${h}, ${s}%, ${l}%)` : `hsla(${h}, ${s}%, ${l}%, ${this.roundA})`;\n    }\n    /**\n     * Returns the hex value of the color.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    toHex(allow3Char = false) {\n        return rgbToHex(this.r, this.g, this.b, allow3Char);\n    }\n    /**\n     * Returns the hex value of the color -with a # prefixed.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    toHexString(allow3Char = false) {\n        return '#' + this.toHex(allow3Char);\n    }\n    /**\n     * Returns the hex 8 value of the color.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    toHex8(allow4Char = false) {\n        return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);\n    }\n    /**\n     * Returns the hex 8 value of the color -with a # prefixed.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    toHex8String(allow4Char = false) {\n        return '#' + this.toHex8(allow4Char);\n    }\n    /**\n     * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n     * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n     */\n    toHexShortString(allowShortChar = false) {\n        return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n    }\n    /**\n     * Returns the object as a RGBA object.\n     */\n    toRgb() {\n        return {\n            r: Math.round(this.r),\n            g: Math.round(this.g),\n            b: Math.round(this.b),\n            a: this.a,\n        };\n    }\n    /**\n     * Returns the RGBA values interpolated into a string with the following format:\n     * \"RGBA(xxx, xxx, xxx, xx)\".\n     */\n    toRgbString() {\n        const r = Math.round(this.r);\n        const g = Math.round(this.g);\n        const b = Math.round(this.b);\n        return this.a === 1 ? `rgb(${r}, ${g}, ${b})` : `rgba(${r}, ${g}, ${b}, ${this.roundA})`;\n    }\n    /**\n     * Returns the object as a RGBA object.\n     */\n    toPercentageRgb() {\n        const fmt = (x) => `${Math.round(bound01(x, 255) * 100)}%`;\n        return {\n            r: fmt(this.r),\n            g: fmt(this.g),\n            b: fmt(this.b),\n            a: this.a,\n        };\n    }\n    /**\n     * Returns the RGBA relative values interpolated into a string\n     */\n    toPercentageRgbString() {\n        const rnd = (x) => Math.round(bound01(x, 255) * 100);\n        return this.a === 1\n            ? `rgb(${rnd(this.r)}%, ${rnd(this.g)}%, ${rnd(this.b)}%)`\n            : `rgba(${rnd(this.r)}%, ${rnd(this.g)}%, ${rnd(this.b)}%, ${this.roundA})`;\n    }\n    toCmyk() {\n        return {\n            ...rgbToCmyk(this.r, this.g, this.b),\n        };\n    }\n    toCmykString() {\n        const { c, m, y, k } = rgbToCmyk(this.r, this.g, this.b);\n        return `cmyk(${c}, ${m}, ${y}, ${k})`;\n    }\n    /**\n     * The 'real' name of the color -if there is one.\n     */\n    toName() {\n        if (this.a === 0) {\n            return 'transparent';\n        }\n        if (this.a < 1) {\n            return false;\n        }\n        const hex = '#' + rgbToHex(this.r, this.g, this.b, false);\n        for (const [key, value] of Object.entries(names)) {\n            if (hex === value) {\n                return key;\n            }\n        }\n        return false;\n    }\n    toString(format) {\n        const formatSet = Boolean(format);\n        format = format ?? this.format;\n        let formattedString = false;\n        const hasAlpha = this.a < 1 && this.a >= 0;\n        const needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n        if (needsAlphaFormat) {\n            // Special case for \"transparent\", all other non-alpha formats\n            // will return rgba when there is transparency.\n            if (format === 'name' && this.a === 0) {\n                return this.toName();\n            }\n            return this.toRgbString();\n        }\n        if (format === 'rgb') {\n            formattedString = this.toRgbString();\n        }\n        if (format === 'prgb') {\n            formattedString = this.toPercentageRgbString();\n        }\n        if (format === 'hex' || format === 'hex6') {\n            formattedString = this.toHexString();\n        }\n        if (format === 'hex3') {\n            formattedString = this.toHexString(true);\n        }\n        if (format === 'hex4') {\n            formattedString = this.toHex8String(true);\n        }\n        if (format === 'hex8') {\n            formattedString = this.toHex8String();\n        }\n        if (format === 'name') {\n            formattedString = this.toName();\n        }\n        if (format === 'hsl') {\n            formattedString = this.toHslString();\n        }\n        if (format === 'hsv') {\n            formattedString = this.toHsvString();\n        }\n        if (format === 'cmyk') {\n            formattedString = this.toCmykString();\n        }\n        return formattedString || this.toHexString();\n    }\n    toNumber() {\n        return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n    }\n    clone() {\n        return new TinyColor(this.toString());\n    }\n    /**\n     * Lighten the color a given amount. Providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    lighten(amount = 10) {\n        const hsl = this.toHsl();\n        hsl.l += amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    }\n    /**\n     * Brighten the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    brighten(amount = 10) {\n        const rgb = this.toRgb();\n        rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n        rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n        rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n        return new TinyColor(rgb);\n    }\n    /**\n     * Darken the color a given amount, from 0 to 100.\n     * Providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    darken(amount = 10) {\n        const hsl = this.toHsl();\n        hsl.l -= amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    }\n    /**\n     * Mix the color with pure white, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    tint(amount = 10) {\n        return this.mix('white', amount);\n    }\n    /**\n     * Mix the color with pure black, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    shade(amount = 10) {\n        return this.mix('black', amount);\n    }\n    /**\n     * Desaturate the color a given amount, from 0 to 100.\n     * Providing 100 will is the same as calling greyscale\n     * @param amount - valid between 1-100\n     */\n    desaturate(amount = 10) {\n        const hsl = this.toHsl();\n        hsl.s -= amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    }\n    /**\n     * Saturate the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    saturate(amount = 10) {\n        const hsl = this.toHsl();\n        hsl.s += amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    }\n    /**\n     * Completely desaturates a color into greyscale.\n     * Same as calling `desaturate(100)`\n     */\n    greyscale() {\n        return this.desaturate(100);\n    }\n    /**\n     * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n     * Values outside of this range will be wrapped into this range.\n     */\n    spin(amount) {\n        const hsl = this.toHsl();\n        const hue = (hsl.h + amount) % 360;\n        hsl.h = hue < 0 ? 360 + hue : hue;\n        return new TinyColor(hsl);\n    }\n    /**\n     * Mix the current color a given amount with another color, from 0 to 100.\n     * 0 means no mixing (return current color).\n     */\n    mix(color, amount = 50) {\n        const rgb1 = this.toRgb();\n        const rgb2 = new TinyColor(color).toRgb();\n        const p = amount / 100;\n        const rgba = {\n            r: (rgb2.r - rgb1.r) * p + rgb1.r,\n            g: (rgb2.g - rgb1.g) * p + rgb1.g,\n            b: (rgb2.b - rgb1.b) * p + rgb1.b,\n            a: (rgb2.a - rgb1.a) * p + rgb1.a,\n        };\n        return new TinyColor(rgba);\n    }\n    analogous(results = 6, slices = 30) {\n        const hsl = this.toHsl();\n        const part = 360 / slices;\n        const ret = [this];\n        for (hsl.h = (hsl.h - ((part * results) >> 1) + 720) % 360; --results;) {\n            hsl.h = (hsl.h + part) % 360;\n            ret.push(new TinyColor(hsl));\n        }\n        return ret;\n    }\n    /**\n     * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n     */\n    complement() {\n        const hsl = this.toHsl();\n        hsl.h = (hsl.h + 180) % 360;\n        return new TinyColor(hsl);\n    }\n    monochromatic(results = 6) {\n        const hsv = this.toHsv();\n        const { h } = hsv;\n        const { s } = hsv;\n        let { v } = hsv;\n        const res = [];\n        const modification = 1 / results;\n        while (results--) {\n            res.push(new TinyColor({ h, s, v }));\n            v = (v + modification) % 1;\n        }\n        return res;\n    }\n    splitcomplement() {\n        const hsl = this.toHsl();\n        const { h } = hsl;\n        return [\n            this,\n            new TinyColor({ h: (h + 72) % 360, s: hsl.s, l: hsl.l }),\n            new TinyColor({ h: (h + 216) % 360, s: hsl.s, l: hsl.l }),\n        ];\n    }\n    /**\n     * Compute how the color would appear on a background\n     */\n    onBackground(background) {\n        const fg = this.toRgb();\n        const bg = new TinyColor(background).toRgb();\n        const alpha = fg.a + bg.a * (1 - fg.a);\n        return new TinyColor({\n            r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n            g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n            b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n            a: alpha,\n        });\n    }\n    /**\n     * Alias for `polyad(3)`\n     */\n    triad() {\n        return this.polyad(3);\n    }\n    /**\n     * Alias for `polyad(4)`\n     */\n    tetrad() {\n        return this.polyad(4);\n    }\n    /**\n     * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n     * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n     */\n    polyad(n) {\n        const hsl = this.toHsl();\n        const { h } = hsl;\n        const result = [this];\n        const increment = 360 / n;\n        for (let i = 1; i < n; i++) {\n            result.push(new TinyColor({ h: (h + i * increment) % 360, s: hsl.s, l: hsl.l }));\n        }\n        return result;\n    }\n    /**\n     * compare color vs current color\n     */\n    equals(color) {\n        const comparedColor = new TinyColor(color);\n        /**\n         * RGB and CMYK do not have the same color gamut, so a CMYK conversion will never be 100%.\n         * This means we need to compare CMYK to CMYK to ensure accuracy of the equals function.\n         */\n        if (this.format === 'cmyk' || comparedColor.format === 'cmyk') {\n            return this.toCmykString() === comparedColor.toCmykString();\n        }\n        return this.toRgbString() === comparedColor.toRgbString();\n    }\n}\n", "import { TinyColor } from './index.js';\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n/**\n * AKA `contrast`\n *\n * Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\n */\nexport function readability(color1, color2) {\n    const c1 = new TinyColor(color1);\n    const c2 = new TinyColor(color2);\n    return ((Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) /\n        (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05));\n}\n/**\n * Ensure that foreground and background color combinations meet WCAG2 guidelines.\n * The third argument is an object.\n *      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n *      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n * If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n *\n * Example\n * ```ts\n * new TinyColor().isReadable('#000', '#111') => false\n * new TinyColor().isReadable('#000', '#111', { level: 'AA', size: 'large' }) => false\n * ```\n */\nexport function isReadable(color1, color2, wcag2 = { level: 'AA', size: 'small' }) {\n    const readabilityLevel = readability(color1, color2);\n    switch ((wcag2.level ?? 'AA') + (wcag2.size ?? 'small')) {\n        case 'AAsmall':\n        case 'AAAlarge':\n            return readabilityLevel >= 4.5;\n        case 'AAlarge':\n            return readabilityLevel >= 3;\n        case 'AAAsmall':\n            return readabilityLevel >= 7;\n        default:\n            return false;\n    }\n}\n/**\n * Given a base color and a list of possible foreground or background\n * colors for that base, returns the most readable color.\n * Optionally returns Black or White if the most readable color is unreadable.\n *\n * @param baseColor - the base color.\n * @param colorList - array of colors to pick the most readable one from.\n * @param args - and object with extra arguments\n *\n * Example\n * ```ts\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'], { includeFallbackColors: false }).toHexString(); // \"#112255\"\n * new TinyColor().mostReadable('#123', ['#124\", \"#125'],{ includeFallbackColors: true }).toHexString();  // \"#ffffff\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'large' }).toHexString(); // \"#faf3f3\"\n * new TinyColor().mostReadable('#a8015a', [\"#faf3f3\"], { includeFallbackColors:true, level: 'AAA', size: 'small' }).toHexString(); // \"#ffffff\"\n * ```\n */\nexport function mostReadable(baseColor, colorList, args = { includeFallbackColors: false, level: 'AA', size: 'small' }) {\n    let bestColor = null;\n    let bestScore = 0;\n    const { includeFallbackColors, level, size } = args;\n    for (const color of colorList) {\n        const score = readability(baseColor, color);\n        if (score > bestScore) {\n            bestScore = score;\n            bestColor = new TinyColor(color);\n        }\n    }\n    if (isReadable(baseColor, bestColor, { level, size }) || !includeFallbackColors) {\n        return bestColor;\n    }\n    args.includeFallbackColors = false;\n    return mostReadable(baseColor, ['#fff', '#000'], args);\n}\n", "import { rgbaToArgbHex } from './conversion.js';\nimport { TinyColor } from './index.js';\n/**\n * Returns the color represented as a Microsoft filter for use in old versions of IE.\n */\nexport function toMsFilter(firstColor, secondColor) {\n    const color = new TinyColor(firstColor);\n    const hex8String = '#' + rgbaToArgbHex(color.r, color.g, color.b, color.a);\n    let secondHex8String = hex8String;\n    const gradientType = color.gradientType ? 'GradientType = 1, ' : '';\n    if (secondColor) {\n        const s = new TinyColor(secondColor);\n        secondHex8String = '#' + rgbaToArgbHex(s.r, s.g, s.b, s.a);\n    }\n    return `progid:DXImageTransform.Microsoft.gradient(${gradientType}startColorstr=${hex8String},endColorstr=${secondHex8String})`;\n}\n", "import { TinyColor } from './index.js';\nimport { convertToPercentage } from './util.js';\n/**\n * If input is an object, force 1 into \"1.0\" to handle ratios properly\n * String input requires \"1.0\" as input, so 1 will be treated as 1\n */\nexport function fromRatio(ratio, opts) {\n    const newColor = {\n        r: convertToPercentage(ratio.r),\n        g: convertToPercentage(ratio.g),\n        b: convertToPercentage(ratio.b),\n    };\n    if (ratio.a !== undefined) {\n        newColor.a = Number(ratio.a);\n    }\n    return new TinyColor(newColor, opts);\n}\n/** old random function */\nexport function legacyRandom() {\n    return new TinyColor({\n        r: Math.random(),\n        g: Math.random(),\n        b: Math.random(),\n    });\n}\n", "// randomColor by <PERSON> under the CC0 license\n// https://github.com/davidmerfield/randomColor/\nimport { TinyColor } from './index.js';\nexport function random(options = {}) {\n    // Check if we need to generate multiple colors\n    if (options.count !== undefined &&\n        options.count !== null) {\n        const totalColors = options.count;\n        const colors = [];\n        options.count = undefined;\n        while (totalColors > colors.length) {\n            // Since we're generating multiple colors,\n            // incremement the seed. Otherwise we'd just\n            // generate the same color each time...\n            options.count = null;\n            if (options.seed) {\n                options.seed += 1;\n            }\n            colors.push(random(options));\n        }\n        options.count = totalColors;\n        return colors;\n    }\n    // First we pick a hue (H)\n    const h = pickHue(options.hue, options.seed);\n    // Then use H to determine saturation (S)\n    const s = pickSaturation(h, options);\n    // Then use S and H to determine brightness (B).\n    const v = pickBrightness(h, s, options);\n    const res = { h, s, v };\n    if (options.alpha !== undefined) {\n        res.a = options.alpha;\n    }\n    // Then we return the HSB color in the desired format\n    return new TinyColor(res);\n}\nfunction pickHue(hue, seed) {\n    const hueRange = getHueRange(hue);\n    let res = randomWithin(hueRange, seed);\n    // Instead of storing red as two seperate ranges,\n    // we group them, using negative numbers\n    if (res < 0) {\n        res = 360 + res;\n    }\n    return res;\n}\nfunction pickSaturation(hue, options) {\n    if (options.hue === 'monochrome') {\n        return 0;\n    }\n    if (options.luminosity === 'random') {\n        return randomWithin([0, 100], options.seed);\n    }\n    const { saturationRange } = getColorInfo(hue);\n    let sMin = saturationRange[0];\n    let sMax = saturationRange[1];\n    switch (options.luminosity) {\n        case 'bright':\n            sMin = 55;\n            break;\n        case 'dark':\n            sMin = sMax - 10;\n            break;\n        case 'light':\n            sMax = 55;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([sMin, sMax], options.seed);\n}\nfunction pickBrightness(H, S, options) {\n    let bMin = getMinimumBrightness(H, S);\n    let bMax = 100;\n    switch (options.luminosity) {\n        case 'dark':\n            bMax = bMin + 20;\n            break;\n        case 'light':\n            bMin = (bMax + bMin) / 2;\n            break;\n        case 'random':\n            bMin = 0;\n            bMax = 100;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([bMin, bMax], options.seed);\n}\nfunction getMinimumBrightness(H, S) {\n    const { lowerBounds } = getColorInfo(H);\n    for (let i = 0; i < lowerBounds.length - 1; i++) {\n        const s1 = lowerBounds[i][0];\n        const v1 = lowerBounds[i][1];\n        const s2 = lowerBounds[i + 1][0];\n        const v2 = lowerBounds[i + 1][1];\n        if (S >= s1 && S <= s2) {\n            const m = (v2 - v1) / (s2 - s1);\n            const b = v1 - m * s1;\n            return m * S + b;\n        }\n    }\n    return 0;\n}\nfunction getHueRange(colorInput) {\n    const num = parseInt(colorInput, 10);\n    if (!Number.isNaN(num) && num < 360 && num > 0) {\n        return [num, num];\n    }\n    if (typeof colorInput === 'string') {\n        const namedColor = bounds.find(n => n.name === colorInput);\n        if (namedColor) {\n            const color = defineColor(namedColor);\n            if (color.hueRange) {\n                return color.hueRange;\n            }\n        }\n        const parsed = new TinyColor(colorInput);\n        if (parsed.isValid) {\n            const hue = parsed.toHsv().h;\n            return [hue, hue];\n        }\n    }\n    return [0, 360];\n}\nfunction getColorInfo(hue) {\n    // Maps red colors to make picking hue easier\n    if (hue >= 334 && hue <= 360) {\n        hue -= 360;\n    }\n    for (const bound of bounds) {\n        const color = defineColor(bound);\n        if (color.hueRange && hue >= color.hueRange[0] && hue <= color.hueRange[1]) {\n            return color;\n        }\n    }\n    throw Error('Color not found');\n}\nfunction randomWithin(range, seed) {\n    if (seed === undefined) {\n        return Math.floor(range[0] + Math.random() * (range[1] + 1 - range[0]));\n    }\n    // Seeded random algorithm from http://indiegamr.com/generate-repeatable-random-numbers-in-js/\n    const max = range[1] || 1;\n    const min = range[0] || 0;\n    seed = (seed * 9301 + 49297) % 233280;\n    const rnd = seed / 233280.0;\n    return Math.floor(min + rnd * (max - min));\n}\nfunction defineColor(bound) {\n    const sMin = bound.lowerBounds[0][0];\n    const sMax = bound.lowerBounds[bound.lowerBounds.length - 1][0];\n    const bMin = bound.lowerBounds[bound.lowerBounds.length - 1][1];\n    const bMax = bound.lowerBounds[0][1];\n    return {\n        name: bound.name,\n        hueRange: bound.hueRange,\n        lowerBounds: bound.lowerBounds,\n        saturationRange: [sMin, sMax],\n        brightnessRange: [bMin, bMax],\n    };\n}\n/**\n * @hidden\n */\nexport const bounds = [\n    {\n        name: 'monochrome',\n        hueRange: null,\n        lowerBounds: [\n            [0, 0],\n            [100, 0],\n        ],\n    },\n    {\n        name: 'red',\n        hueRange: [-26, 18],\n        lowerBounds: [\n            [20, 100],\n            [30, 92],\n            [40, 89],\n            [50, 85],\n            [60, 78],\n            [70, 70],\n            [80, 60],\n            [90, 55],\n            [100, 50],\n        ],\n    },\n    {\n        name: 'orange',\n        hueRange: [19, 46],\n        lowerBounds: [\n            [20, 100],\n            [30, 93],\n            [40, 88],\n            [50, 86],\n            [60, 85],\n            [70, 70],\n            [100, 70],\n        ],\n    },\n    {\n        name: 'yellow',\n        hueRange: [47, 62],\n        lowerBounds: [\n            [25, 100],\n            [40, 94],\n            [50, 89],\n            [60, 86],\n            [70, 84],\n            [80, 82],\n            [90, 80],\n            [100, 75],\n        ],\n    },\n    {\n        name: 'green',\n        hueRange: [63, 178],\n        lowerBounds: [\n            [30, 100],\n            [40, 90],\n            [50, 85],\n            [60, 81],\n            [70, 74],\n            [80, 64],\n            [90, 50],\n            [100, 40],\n        ],\n    },\n    {\n        name: 'blue',\n        hueRange: [179, 257],\n        lowerBounds: [\n            [20, 100],\n            [30, 86],\n            [40, 80],\n            [50, 74],\n            [60, 60],\n            [70, 52],\n            [80, 44],\n            [90, 39],\n            [100, 35],\n        ],\n    },\n    {\n        name: 'purple',\n        hueRange: [258, 282],\n        lowerBounds: [\n            [20, 100],\n            [30, 87],\n            [40, 79],\n            [50, 70],\n            [60, 65],\n            [70, 59],\n            [80, 52],\n            [90, 45],\n            [100, 42],\n        ],\n    },\n    {\n        name: 'pink',\n        hueRange: [283, 334],\n        lowerBounds: [\n            [20, 100],\n            [30, 90],\n            [40, 86],\n            [60, 84],\n            [80, 80],\n            [90, 75],\n            [100, 73],\n        ],\n    },\n];\n"], "mappings": ";AAIO,SAAS,QAAQ,GAAG,KAAK;AAC5B,MAAI,eAAe,CAAC,GAAG;AACnB,QAAI;AAAA,EACR;AACA,QAAM,YAAY,aAAa,CAAC;AAChC,MAAI,QAAQ,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC;AAE9D,MAAI,WAAW;AACX,QAAI,SAAS,OAAO,IAAI,GAAG,GAAG,EAAE,IAAI;AAAA,EACxC;AAEA,MAAI,KAAK,IAAI,IAAI,GAAG,IAAI,MAAU;AAC9B,WAAO;AAAA,EACX;AAEA,MAAI,QAAQ,KAAK;AAIb,SAAK,IAAI,IAAK,IAAI,MAAO,MAAM,IAAI,OAAO,WAAW,OAAO,GAAG,CAAC;AAAA,EACpE,OACK;AAGD,QAAK,IAAI,MAAO,WAAW,OAAO,GAAG,CAAC;AAAA,EAC1C;AACA,SAAO;AACX;AAKO,SAAS,QAAQ,KAAK;AACzB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC;AACvC;AAMO,SAAS,eAAe,GAAG;AAC9B,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM,MAAM,WAAW,CAAC,MAAM;AAC/E;AAKO,SAAS,aAAa,GAAG;AAC5B,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,MAAM;AACvD;AAKO,SAAS,WAAW,GAAG;AAC1B,MAAI,WAAW,CAAC;AAChB,MAAI,MAAM,CAAC,KAAK,IAAI,KAAK,IAAI,GAAG;AAC5B,QAAI;AAAA,EACR;AACA,SAAO;AACX;AAKO,SAAS,oBAAoB,GAAG;AACnC,MAAI,OAAO,CAAC,KAAK,GAAG;AAChB,WAAO,GAAG,OAAO,CAAC,IAAI,GAAG;AAAA,EAC7B;AACA,SAAO;AACX;AAKO,SAAS,KAAK,GAAG;AACpB,SAAO,EAAE,WAAW,IAAI,MAAM,IAAI,OAAO,CAAC;AAC9C;;;ACxEO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,SAAO;AAAA,IACH,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,EACzB;AACJ;AAMO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,QAAM,KAAK,MAAM,OAAO;AACxB,MAAI,QAAQ,KAAK;AACb,QAAI;AACJ,QAAI;AAAA,EACR,OACK;AACD,UAAM,IAAI,MAAM;AAChB,QAAI,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM;AAC/C,YAAQ,KAAK;AAAA,MACT,KAAK;AACD,aAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,MACJ,KAAK;AACD,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ,KAAK;AACD,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ;AACI;AAAA,IACR;AACA,SAAK;AAAA,EACT;AACA,SAAO,EAAE,GAAG,GAAG,EAAE;AACrB;AACA,SAAS,QAAQ,GAAG,GAAG,GAAG;AACtB,MAAI,IAAI,GAAG;AACP,SAAK;AAAA,EACT;AACA,MAAI,IAAI,GAAG;AACP,SAAK;AAAA,EACT;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO,KAAK,IAAI,MAAM,IAAI;AAAA,EAC9B;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO;AAAA,EACX;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AAAA,EACvC;AACA,SAAO;AACX;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,GAAG;AAET,QAAI;AACJ,QAAI;AACJ,QAAI;AAAA,EACR,OACK;AACD,UAAM,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;AAC9C,UAAM,IAAI,IAAI,IAAI;AAClB,QAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AAC3B,QAAI,QAAQ,GAAG,GAAG,CAAC;AACnB,QAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AAAA,EAC/B;AACA,SAAO,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,IAAI;AAChD;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,MAAI,IAAI;AACR,QAAM,IAAI;AACV,QAAM,IAAI,MAAM;AAChB,QAAM,IAAI,QAAQ,IAAI,IAAI,IAAI;AAC9B,MAAI,QAAQ,KAAK;AACb,QAAI;AAAA,EACR,OACK;AACD,YAAQ,KAAK;AAAA,MACT,KAAK;AACD,aAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,MACJ,KAAK;AACD,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ,KAAK;AACD,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ;AACI;AAAA,IACR;AACA,SAAK;AAAA,EACT;AACA,SAAO,EAAE,GAAG,GAAG,EAAE;AACrB;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG;AAC9B,MAAI,QAAQ,GAAG,GAAG,IAAI;AACtB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,QAAM,IAAI,KAAK,MAAM,CAAC;AACtB,QAAM,IAAI,IAAI;AACd,QAAM,IAAI,KAAK,IAAI;AACnB,QAAM,IAAI,KAAK,IAAI,IAAI;AACvB,QAAM,IAAI,KAAK,KAAK,IAAI,KAAK;AAC7B,QAAM,MAAM,IAAI;AAChB,QAAM,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAChC,QAAM,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAChC,QAAM,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAChC,SAAO,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,IAAI;AAChD;AAOO,SAAS,SAAS,GAAG,GAAG,GAAG,YAAY;AAC1C,QAAM,MAAM;AAAA,IACR,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,EACnC;AAEA,MAAI,cACA,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;AACrC,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EAChE;AACA,SAAO,IAAI,KAAK,EAAE;AACtB;AAQO,SAAS,UAAU,GAAG,GAAG,GAAG,GAAG,YAAY;AAC9C,QAAM,MAAM;AAAA,IACR,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,oBAAoB,CAAC,CAAC;AAAA,EAC/B;AAEA,MAAI,cACA,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAClC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG;AACrC,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EACnF;AACA,SAAO,IAAI,KAAK,EAAE;AACtB;AAQO,SAAS,cAAc,GAAG,GAAG,GAAG,GAAG;AACtC,QAAM,MAAM;AAAA,IACR,KAAK,oBAAoB,CAAC,CAAC;AAAA,IAC3B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,IAC/B,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC;AAAA,EACnC;AACA,SAAO,IAAI,KAAK,EAAE;AACtB;AAKO,SAAS,UAAU,GAAG,GAAG,GAAG,GAAG;AAClC,QAAM,QAAQ,IAAI;AAClB,QAAM,QAAQ,IAAI;AAClB,QAAM,QAAQ,IAAI;AAClB,QAAM,QAAQ,IAAI;AAClB,QAAM,IAAI,OAAO,IAAI,UAAU,IAAI;AACnC,QAAM,IAAI,OAAO,IAAI,UAAU,IAAI;AACnC,QAAM,IAAI,OAAO,IAAI,UAAU,IAAI;AACnC,SAAO,EAAE,GAAG,GAAG,EAAE;AACrB;AACO,SAAS,UAAU,GAAG,GAAG,GAAG;AAC/B,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI,IAAI,IAAI,IAAI;AAChB,MAAI,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC;AACxB,MAAI,MAAM,GAAG;AACT,QAAI;AACJ,QAAI;AACJ,QAAI;AAAA,EACR,OACK;AACD,SAAM,IAAI,MAAM,IAAI,KAAM;AAC1B,SAAM,IAAI,MAAM,IAAI,KAAM;AAC1B,SAAM,IAAI,MAAM,IAAI,KAAM;AAAA,EAC9B;AACA,OAAK;AACL,SAAO;AAAA,IACH,GAAG,KAAK,MAAM,CAAC;AAAA,IACf,GAAG,KAAK,MAAM,CAAC;AAAA,IACf,GAAG,KAAK,MAAM,CAAC;AAAA,IACf,GAAG,KAAK,MAAM,CAAC;AAAA,EACnB;AACJ;AAEO,SAAS,oBAAoB,GAAG;AACnC,SAAO,KAAK,MAAM,WAAW,CAAC,IAAI,GAAG,EAAE,SAAS,EAAE;AACtD;AAEO,SAAS,oBAAoB,GAAG;AACnC,SAAO,gBAAgB,CAAC,IAAI;AAChC;AAEO,SAAS,gBAAgB,KAAK;AACjC,SAAO,SAAS,KAAK,EAAE;AAC3B;AACO,SAAS,oBAAoB,OAAO;AACvC,SAAO;AAAA,IACH,GAAG,SAAS;AAAA,IACZ,IAAI,QAAQ,UAAW;AAAA,IACvB,GAAG,QAAQ;AAAA,EACf;AACJ;;;AC9QO,IAAM,QAAQ;AAAA,EACjB,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,KAAK;AAAA,EACL,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,aAAa;AACjB;;;ACnIO,SAAS,WAAW,OAAO;AAC9B,MAAI,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AAC7B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,SAAS;AACb,MAAI,OAAO,UAAU,UAAU;AAC3B,YAAQ,oBAAoB,KAAK;AAAA,EACrC;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AAC/E,YAAM,SAAS,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACxC,WAAK;AACL,eAAS,OAAO,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,MAAM,SAAS;AAAA,IAC3D,WACS,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACpF,UAAI,oBAAoB,MAAM,CAAC;AAC/B,UAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAG,GAAG,CAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACb,WACS,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACpF,UAAI,oBAAoB,MAAM,CAAC;AAC/B,UAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAG,GAAG,CAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACb,WACS,eAAe,MAAM,CAAC,KAC3B,eAAe,MAAM,CAAC,KACtB,eAAe,MAAM,CAAC,KACtB,eAAe,MAAM,CAAC,GAAG;AACzB,YAAM,UAAU,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAClD,WAAK;AACL,eAAS;AAAA,IACb;AACA,QAAI,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,GAAG;AAClD,UAAI,MAAM;AAAA,IACd;AAAA,EACJ;AACA,MAAI,WAAW,CAAC;AAChB,SAAO;AAAA,IACH;AAAA,IACA,QAAQ,MAAM,UAAU;AAAA,IACxB,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC;AAAA,EACJ;AACJ;AAEA,IAAM,cAAc;AAEpB,IAAM,aAAa;AAEnB,IAAM,WAAW,QAAQ,aAAa,UAAU,cAAc;AAK9D,IAAM,oBAAoB,gBAAgB,WAAW,eAAe,WAAW,eAAe,WAAW;AACzG,IAAM;AAAA;AAAA,EAEN,gBAAgB,WAAW,eAAe,WAAW,eAAe,WAAW,eAAe,WAAW;AAAA;AACzG,IAAM,WAAW;AAAA,EACb,UAAU,IAAI,OAAO,QAAQ;AAAA,EAC7B,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,EACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,EACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,EACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,EAC3C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AACV;AAKO,SAAS,oBAAoB,OAAO;AACvC,UAAQ,MAAM,KAAK,EAAE,YAAY;AACjC,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AACZ,MAAI,MAAM,KAAK,GAAG;AACd,YAAQ,MAAM,KAAK;AACnB,YAAQ;AAAA,EACZ,WACS,UAAU,eAAe;AAC9B,WAAO,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,QAAQ,OAAO;AAAA,EACpD;AAKA,MAAI,QAAQ,SAAS,IAAI,KAAK,KAAK;AACnC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EACnD;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EAChE;AACA,UAAQ,SAAS,IAAI,KAAK,KAAK;AAC/B,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EACnD;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EAChE;AACA,UAAQ,SAAS,IAAI,KAAK,KAAK;AAC/B,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EACnD;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO,EAAE,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,EAChE;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACd;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,oBAAoB,MAAM,CAAC,CAAC;AAAA,MAC/B,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,oBAAoB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MAC1C,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,UAAQ,SAAS,KAAK,KAAK,KAAK;AAChC,MAAI,OAAO;AACP,WAAO;AAAA,MACH,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,GAAG,gBAAgB,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,MACtC,QAAQ,QAAQ,SAAS;AAAA,IAC7B;AAAA,EACJ;AACA,SAAO;AACX;AAKO,SAAS,eAAe,OAAO;AAClC,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,CAAC,OAAO,MAAM,KAAK;AAAA,EAC9B;AACA,SAAO,SAAS,SAAS,KAAK,KAAK;AACvC;;;AC1MO,IAAM,YAAN,MAAM,WAAU;AAAA,EACnB,YAAY,QAAQ,IAAI,OAAO,CAAC,GAAG;AAE/B,QAAI,iBAAiB,YAAW;AAE5B,aAAO;AAAA,IACX;AACA,QAAI,OAAO,UAAU,UAAU;AAC3B,cAAQ,oBAAoB,KAAK;AAAA,IACrC;AACA,SAAK,gBAAgB;AACrB,UAAM,MAAM,WAAW,KAAK;AAC5B,SAAK,gBAAgB;AACrB,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,SAAS,KAAK,MAAM,MAAM,KAAK,CAAC,IAAI;AACzC,SAAK,SAAS,KAAK,UAAU,IAAI;AACjC,SAAK,eAAe,KAAK;AAKzB,QAAI,KAAK,IAAI,GAAG;AACZ,WAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,IAC9B;AACA,QAAI,KAAK,IAAI,GAAG;AACZ,WAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,IAC9B;AACA,QAAI,KAAK,IAAI,GAAG;AACZ,WAAK,IAAI,KAAK,MAAM,KAAK,CAAC;AAAA,IAC9B;AACA,SAAK,UAAU,IAAI;AAAA,EACvB;AAAA,EACA,SAAS;AACL,WAAO,KAAK,cAAc,IAAI;AAAA,EAClC;AAAA,EACA,UAAU;AACN,WAAO,CAAC,KAAK,OAAO;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AAEZ,UAAM,MAAM,KAAK,MAAM;AACvB,YAAQ,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,OAAO;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAEX,UAAM,MAAM,KAAK,MAAM;AACvB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,QAAQ,IAAI,IAAI;AACtB,UAAM,QAAQ,IAAI,IAAI;AACtB,UAAM,QAAQ,IAAI,IAAI;AACtB,QAAI,SAAS,SAAS;AAClB,UAAI,QAAQ;AAAA,IAChB,OACK;AAED,UAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,IAC7C;AACA,QAAI,SAAS,SAAS;AAClB,UAAI,QAAQ;AAAA,IAChB,OACK;AAED,UAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,IAC7C;AACA,QAAI,SAAS,SAAS;AAClB,UAAI,QAAQ;AAAA,IAChB,OACK;AAED,UAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AAAA,IAC7C;AACA,WAAO,SAAS,IAAI,SAAS,IAAI,SAAS;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AACZ,SAAK,IAAI,WAAW,KAAK;AACzB,SAAK,SAAS,KAAK,MAAM,MAAM,KAAK,CAAC,IAAI;AACzC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACX,UAAM,EAAE,EAAE,IAAI,KAAK,MAAM;AACzB,WAAO,MAAM;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,UAAM,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAC3C,WAAO,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,EAAE;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACV,UAAM,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAC3C,UAAM,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAChC,UAAM,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAChC,UAAM,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAChC,WAAO,KAAK,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM;AAAA,EAC7F;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,UAAM,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAC3C,WAAO,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,EAAE;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACV,UAAM,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAC3C,UAAM,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAChC,UAAM,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAChC,UAAM,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAChC,WAAO,KAAK,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM;AAAA,EAC7F;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,aAAa,OAAO;AACtB,WAAO,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,UAAU;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,aAAa,OAAO;AAC5B,WAAO,MAAM,KAAK,MAAM,UAAU;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,aAAa,OAAO;AACvB,WAAO,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,UAAU;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,aAAa,OAAO;AAC7B,WAAO,MAAM,KAAK,OAAO,UAAU;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,iBAAiB,OAAO;AACrC,WAAO,KAAK,MAAM,IAAI,KAAK,YAAY,cAAc,IAAI,KAAK,aAAa,cAAc;AAAA,EAC7F;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,WAAO;AAAA,MACH,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,MACpB,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,MACpB,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,MACpB,GAAG,KAAK;AAAA,IACZ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACV,UAAM,IAAI,KAAK,MAAM,KAAK,CAAC;AAC3B,UAAM,IAAI,KAAK,MAAM,KAAK,CAAC;AAC3B,UAAM,IAAI,KAAK,MAAM,KAAK,CAAC;AAC3B,WAAO,KAAK,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AACd,UAAM,MAAM,CAAC,MAAM,GAAG,KAAK,MAAM,QAAQ,GAAG,GAAG,IAAI,GAAG,CAAC;AACvD,WAAO;AAAA,MACH,GAAG,IAAI,KAAK,CAAC;AAAA,MACb,GAAG,IAAI,KAAK,CAAC;AAAA,MACb,GAAG,IAAI,KAAK,CAAC;AAAA,MACb,GAAG,KAAK;AAAA,IACZ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB;AACpB,UAAM,MAAM,CAAC,MAAM,KAAK,MAAM,QAAQ,GAAG,GAAG,IAAI,GAAG;AACnD,WAAO,KAAK,MAAM,IACZ,OAAO,IAAI,KAAK,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,OACpD,QAAQ,IAAI,KAAK,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM;AAAA,EAChF;AAAA,EACA,SAAS;AACL,WAAO;AAAA,MACH,GAAG,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,IACvC;AAAA,EACJ;AAAA,EACA,eAAe;AACX,UAAM,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACvD,WAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,QAAI,KAAK,MAAM,GAAG;AACd,aAAO;AAAA,IACX;AACA,QAAI,KAAK,IAAI,GAAG;AACZ,aAAO;AAAA,IACX;AACA,UAAM,MAAM,MAAM,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AACxD,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC9C,UAAI,QAAQ,OAAO;AACf,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS,QAAQ;AACb,UAAM,YAAY,QAAQ,MAAM;AAChC,aAAS,UAAU,KAAK;AACxB,QAAI,kBAAkB;AACtB,UAAM,WAAW,KAAK,IAAI,KAAK,KAAK,KAAK;AACzC,UAAM,mBAAmB,CAAC,aAAa,aAAa,OAAO,WAAW,KAAK,KAAK,WAAW;AAC3F,QAAI,kBAAkB;AAGlB,UAAI,WAAW,UAAU,KAAK,MAAM,GAAG;AACnC,eAAO,KAAK,OAAO;AAAA,MACvB;AACA,aAAO,KAAK,YAAY;AAAA,IAC5B;AACA,QAAI,WAAW,OAAO;AAClB,wBAAkB,KAAK,YAAY;AAAA,IACvC;AACA,QAAI,WAAW,QAAQ;AACnB,wBAAkB,KAAK,sBAAsB;AAAA,IACjD;AACA,QAAI,WAAW,SAAS,WAAW,QAAQ;AACvC,wBAAkB,KAAK,YAAY;AAAA,IACvC;AACA,QAAI,WAAW,QAAQ;AACnB,wBAAkB,KAAK,YAAY,IAAI;AAAA,IAC3C;AACA,QAAI,WAAW,QAAQ;AACnB,wBAAkB,KAAK,aAAa,IAAI;AAAA,IAC5C;AACA,QAAI,WAAW,QAAQ;AACnB,wBAAkB,KAAK,aAAa;AAAA,IACxC;AACA,QAAI,WAAW,QAAQ;AACnB,wBAAkB,KAAK,OAAO;AAAA,IAClC;AACA,QAAI,WAAW,OAAO;AAClB,wBAAkB,KAAK,YAAY;AAAA,IACvC;AACA,QAAI,WAAW,OAAO;AAClB,wBAAkB,KAAK,YAAY;AAAA,IACvC;AACA,QAAI,WAAW,QAAQ;AACnB,wBAAkB,KAAK,aAAa;AAAA,IACxC;AACA,WAAO,mBAAmB,KAAK,YAAY;AAAA,EAC/C;AAAA,EACA,WAAW;AACP,YAAQ,KAAK,MAAM,KAAK,CAAC,KAAK,OAAO,KAAK,MAAM,KAAK,CAAC,KAAK,KAAK,KAAK,MAAM,KAAK,CAAC;AAAA,EACrF;AAAA,EACA,QAAQ;AACJ,WAAO,IAAI,WAAU,KAAK,SAAS,CAAC;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,SAAS,IAAI;AACjB,UAAM,MAAM,KAAK,MAAM;AACvB,QAAI,KAAK,SAAS;AAClB,QAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,WAAO,IAAI,WAAU,GAAG;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,SAAS,IAAI;AAClB,UAAM,MAAM,KAAK,MAAM;AACvB,QAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,QAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,QAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,WAAO,IAAI,WAAU,GAAG;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,SAAS,IAAI;AAChB,UAAM,MAAM,KAAK,MAAM;AACvB,QAAI,KAAK,SAAS;AAClB,QAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,WAAO,IAAI,WAAU,GAAG;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,SAAS,IAAI;AACd,WAAO,KAAK,IAAI,SAAS,MAAM;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,SAAS,IAAI;AACf,WAAO,KAAK,IAAI,SAAS,MAAM;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,SAAS,IAAI;AACpB,UAAM,MAAM,KAAK,MAAM;AACvB,QAAI,KAAK,SAAS;AAClB,QAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,WAAO,IAAI,WAAU,GAAG;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,SAAS,IAAI;AAClB,UAAM,MAAM,KAAK,MAAM;AACvB,QAAI,KAAK,SAAS;AAClB,QAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,WAAO,IAAI,WAAU,GAAG;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACR,WAAO,KAAK,WAAW,GAAG;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,QAAQ;AACT,UAAM,MAAM,KAAK,MAAM;AACvB,UAAM,OAAO,IAAI,IAAI,UAAU;AAC/B,QAAI,IAAI,MAAM,IAAI,MAAM,MAAM;AAC9B,WAAO,IAAI,WAAU,GAAG;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO,SAAS,IAAI;AACpB,UAAM,OAAO,KAAK,MAAM;AACxB,UAAM,OAAO,IAAI,WAAU,KAAK,EAAE,MAAM;AACxC,UAAM,IAAI,SAAS;AACnB,UAAM,OAAO;AAAA,MACT,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,MAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,MAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,MAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IACpC;AACA,WAAO,IAAI,WAAU,IAAI;AAAA,EAC7B;AAAA,EACA,UAAU,UAAU,GAAG,SAAS,IAAI;AAChC,UAAM,MAAM,KAAK,MAAM;AACvB,UAAM,OAAO,MAAM;AACnB,UAAM,MAAM,CAAC,IAAI;AACjB,SAAK,IAAI,KAAK,IAAI,KAAM,OAAO,WAAY,KAAK,OAAO,KAAK,EAAE,WAAU;AACpE,UAAI,KAAK,IAAI,IAAI,QAAQ;AACzB,UAAI,KAAK,IAAI,WAAU,GAAG,CAAC;AAAA,IAC/B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACT,UAAM,MAAM,KAAK,MAAM;AACvB,QAAI,KAAK,IAAI,IAAI,OAAO;AACxB,WAAO,IAAI,WAAU,GAAG;AAAA,EAC5B;AAAA,EACA,cAAc,UAAU,GAAG;AACvB,UAAM,MAAM,KAAK,MAAM;AACvB,UAAM,EAAE,EAAE,IAAI;AACd,UAAM,EAAE,EAAE,IAAI;AACd,QAAI,EAAE,EAAE,IAAI;AACZ,UAAM,MAAM,CAAC;AACb,UAAM,eAAe,IAAI;AACzB,WAAO,WAAW;AACd,UAAI,KAAK,IAAI,WAAU,EAAE,GAAG,GAAG,EAAE,CAAC,CAAC;AACnC,WAAK,IAAI,gBAAgB;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB;AACd,UAAM,MAAM,KAAK,MAAM;AACvB,UAAM,EAAE,EAAE,IAAI;AACd,WAAO;AAAA,MACH;AAAA,MACA,IAAI,WAAU,EAAE,IAAI,IAAI,MAAM,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;AAAA,MACvD,IAAI,WAAU,EAAE,IAAI,IAAI,OAAO,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;AAAA,IAC5D;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,YAAY;AACrB,UAAM,KAAK,KAAK,MAAM;AACtB,UAAM,KAAK,IAAI,WAAU,UAAU,EAAE,MAAM;AAC3C,UAAM,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG;AACpC,WAAO,IAAI,WAAU;AAAA,MACjB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,MAC9C,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,MAC9C,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM;AAAA,MAC9C,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,WAAO,KAAK,OAAO,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,WAAO,KAAK,OAAO,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,GAAG;AACN,UAAM,MAAM,KAAK,MAAM;AACvB,UAAM,EAAE,EAAE,IAAI;AACd,UAAM,SAAS,CAAC,IAAI;AACpB,UAAM,YAAY,MAAM;AACxB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,aAAO,KAAK,IAAI,WAAU,EAAE,IAAI,IAAI,IAAI,aAAa,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;AAAA,IACnF;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO;AACV,UAAM,gBAAgB,IAAI,WAAU,KAAK;AAKzC,QAAI,KAAK,WAAW,UAAU,cAAc,WAAW,QAAQ;AAC3D,aAAO,KAAK,aAAa,MAAM,cAAc,aAAa;AAAA,IAC9D;AACA,WAAO,KAAK,YAAY,MAAM,cAAc,YAAY;AAAA,EAC5D;AACJ;;;AC1eO,SAAS,YAAY,QAAQ,QAAQ;AACxC,QAAM,KAAK,IAAI,UAAU,MAAM;AAC/B,QAAM,KAAK,IAAI,UAAU,MAAM;AAC/B,UAAS,KAAK,IAAI,GAAG,aAAa,GAAG,GAAG,aAAa,CAAC,IAAI,SACrD,KAAK,IAAI,GAAG,aAAa,GAAG,GAAG,aAAa,CAAC,IAAI;AAC1D;AAcO,SAAS,WAAW,QAAQ,QAAQ,QAAQ,EAAE,OAAO,MAAM,MAAM,QAAQ,GAAG;AAC/E,QAAM,mBAAmB,YAAY,QAAQ,MAAM;AACnD,WAAS,MAAM,SAAS,SAAS,MAAM,QAAQ,UAAU;AAAA,IACrD,KAAK;AAAA,IACL,KAAK;AACD,aAAO,oBAAoB;AAAA,IAC/B,KAAK;AACD,aAAO,oBAAoB;AAAA,IAC/B,KAAK;AACD,aAAO,oBAAoB;AAAA,IAC/B;AACI,aAAO;AAAA,EACf;AACJ;AAkBO,SAAS,aAAa,WAAW,WAAW,OAAO,EAAE,uBAAuB,OAAO,OAAO,MAAM,MAAM,QAAQ,GAAG;AACpH,MAAI,YAAY;AAChB,MAAI,YAAY;AAChB,QAAM,EAAE,uBAAuB,OAAO,KAAK,IAAI;AAC/C,aAAW,SAAS,WAAW;AAC3B,UAAM,QAAQ,YAAY,WAAW,KAAK;AAC1C,QAAI,QAAQ,WAAW;AACnB,kBAAY;AACZ,kBAAY,IAAI,UAAU,KAAK;AAAA,IACnC;AAAA,EACJ;AACA,MAAI,WAAW,WAAW,WAAW,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,uBAAuB;AAC7E,WAAO;AAAA,EACX;AACA,OAAK,wBAAwB;AAC7B,SAAO,aAAa,WAAW,CAAC,QAAQ,MAAM,GAAG,IAAI;AACzD;;;ACtEO,SAAS,WAAW,YAAY,aAAa;AAChD,QAAM,QAAQ,IAAI,UAAU,UAAU;AACtC,QAAM,aAAa,MAAM,cAAc,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACzE,MAAI,mBAAmB;AACvB,QAAM,eAAe,MAAM,eAAe,uBAAuB;AACjE,MAAI,aAAa;AACb,UAAM,IAAI,IAAI,UAAU,WAAW;AACnC,uBAAmB,MAAM,cAAc,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAAA,EAC7D;AACA,SAAO,8CAA8C,YAAY,iBAAiB,UAAU,gBAAgB,gBAAgB;AAChI;;;ACTO,SAAS,UAAU,OAAO,MAAM;AACnC,QAAM,WAAW;AAAA,IACb,GAAG,oBAAoB,MAAM,CAAC;AAAA,IAC9B,GAAG,oBAAoB,MAAM,CAAC;AAAA,IAC9B,GAAG,oBAAoB,MAAM,CAAC;AAAA,EAClC;AACA,MAAI,MAAM,MAAM,QAAW;AACvB,aAAS,IAAI,OAAO,MAAM,CAAC;AAAA,EAC/B;AACA,SAAO,IAAI,UAAU,UAAU,IAAI;AACvC;AAEO,SAAS,eAAe;AAC3B,SAAO,IAAI,UAAU;AAAA,IACjB,GAAG,KAAK,OAAO;AAAA,IACf,GAAG,KAAK,OAAO;AAAA,IACf,GAAG,KAAK,OAAO;AAAA,EACnB,CAAC;AACL;;;ACrBO,SAAS,OAAO,UAAU,CAAC,GAAG;AAEjC,MAAI,QAAQ,UAAU,UAClB,QAAQ,UAAU,MAAM;AACxB,UAAM,cAAc,QAAQ;AAC5B,UAAM,SAAS,CAAC;AAChB,YAAQ,QAAQ;AAChB,WAAO,cAAc,OAAO,QAAQ;AAIhC,cAAQ,QAAQ;AAChB,UAAI,QAAQ,MAAM;AACd,gBAAQ,QAAQ;AAAA,MACpB;AACA,aAAO,KAAK,OAAO,OAAO,CAAC;AAAA,IAC/B;AACA,YAAQ,QAAQ;AAChB,WAAO;AAAA,EACX;AAEA,QAAM,IAAI,QAAQ,QAAQ,KAAK,QAAQ,IAAI;AAE3C,QAAM,IAAI,eAAe,GAAG,OAAO;AAEnC,QAAM,IAAI,eAAe,GAAG,GAAG,OAAO;AACtC,QAAM,MAAM,EAAE,GAAG,GAAG,EAAE;AACtB,MAAI,QAAQ,UAAU,QAAW;AAC7B,QAAI,IAAI,QAAQ;AAAA,EACpB;AAEA,SAAO,IAAI,UAAU,GAAG;AAC5B;AACA,SAAS,QAAQ,KAAK,MAAM;AACxB,QAAM,WAAW,YAAY,GAAG;AAChC,MAAI,MAAM,aAAa,UAAU,IAAI;AAGrC,MAAI,MAAM,GAAG;AACT,UAAM,MAAM;AAAA,EAChB;AACA,SAAO;AACX;AACA,SAAS,eAAe,KAAK,SAAS;AAClC,MAAI,QAAQ,QAAQ,cAAc;AAC9B,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,eAAe,UAAU;AACjC,WAAO,aAAa,CAAC,GAAG,GAAG,GAAG,QAAQ,IAAI;AAAA,EAC9C;AACA,QAAM,EAAE,gBAAgB,IAAI,aAAa,GAAG;AAC5C,MAAI,OAAO,gBAAgB,CAAC;AAC5B,MAAI,OAAO,gBAAgB,CAAC;AAC5B,UAAQ,QAAQ,YAAY;AAAA,IACxB,KAAK;AACD,aAAO;AACP;AAAA,IACJ,KAAK;AACD,aAAO,OAAO;AACd;AAAA,IACJ,KAAK;AACD,aAAO;AACP;AAAA,IACJ;AACI;AAAA,EACR;AACA,SAAO,aAAa,CAAC,MAAM,IAAI,GAAG,QAAQ,IAAI;AAClD;AACA,SAAS,eAAe,GAAG,GAAG,SAAS;AACnC,MAAI,OAAO,qBAAqB,GAAG,CAAC;AACpC,MAAI,OAAO;AACX,UAAQ,QAAQ,YAAY;AAAA,IACxB,KAAK;AACD,aAAO,OAAO;AACd;AAAA,IACJ,KAAK;AACD,cAAQ,OAAO,QAAQ;AACvB;AAAA,IACJ,KAAK;AACD,aAAO;AACP,aAAO;AACP;AAAA,IACJ;AACI;AAAA,EACR;AACA,SAAO,aAAa,CAAC,MAAM,IAAI,GAAG,QAAQ,IAAI;AAClD;AACA,SAAS,qBAAqB,GAAG,GAAG;AAChC,QAAM,EAAE,YAAY,IAAI,aAAa,CAAC;AACtC,WAAS,IAAI,GAAG,IAAI,YAAY,SAAS,GAAG,KAAK;AAC7C,UAAM,KAAK,YAAY,CAAC,EAAE,CAAC;AAC3B,UAAM,KAAK,YAAY,CAAC,EAAE,CAAC;AAC3B,UAAM,KAAK,YAAY,IAAI,CAAC,EAAE,CAAC;AAC/B,UAAM,KAAK,YAAY,IAAI,CAAC,EAAE,CAAC;AAC/B,QAAI,KAAK,MAAM,KAAK,IAAI;AACpB,YAAM,KAAK,KAAK,OAAO,KAAK;AAC5B,YAAM,IAAI,KAAK,IAAI;AACnB,aAAO,IAAI,IAAI;AAAA,IACnB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,YAAY,YAAY;AAC7B,QAAM,MAAM,SAAS,YAAY,EAAE;AACnC,MAAI,CAAC,OAAO,MAAM,GAAG,KAAK,MAAM,OAAO,MAAM,GAAG;AAC5C,WAAO,CAAC,KAAK,GAAG;AAAA,EACpB;AACA,MAAI,OAAO,eAAe,UAAU;AAChC,UAAM,aAAa,OAAO,KAAK,OAAK,EAAE,SAAS,UAAU;AACzD,QAAI,YAAY;AACZ,YAAM,QAAQ,YAAY,UAAU;AACpC,UAAI,MAAM,UAAU;AAChB,eAAO,MAAM;AAAA,MACjB;AAAA,IACJ;AACA,UAAM,SAAS,IAAI,UAAU,UAAU;AACvC,QAAI,OAAO,SAAS;AAChB,YAAM,MAAM,OAAO,MAAM,EAAE;AAC3B,aAAO,CAAC,KAAK,GAAG;AAAA,IACpB;AAAA,EACJ;AACA,SAAO,CAAC,GAAG,GAAG;AAClB;AACA,SAAS,aAAa,KAAK;AAEvB,MAAI,OAAO,OAAO,OAAO,KAAK;AAC1B,WAAO;AAAA,EACX;AACA,aAAW,SAAS,QAAQ;AACxB,UAAM,QAAQ,YAAY,KAAK;AAC/B,QAAI,MAAM,YAAY,OAAO,MAAM,SAAS,CAAC,KAAK,OAAO,MAAM,SAAS,CAAC,GAAG;AACxE,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,MAAM,iBAAiB;AACjC;AACA,SAAS,aAAa,OAAO,MAAM;AAC/B,MAAI,SAAS,QAAW;AACpB,WAAO,KAAK,MAAM,MAAM,CAAC,IAAI,KAAK,OAAO,KAAK,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,EAAE;AAAA,EAC1E;AAEA,QAAM,MAAM,MAAM,CAAC,KAAK;AACxB,QAAM,MAAM,MAAM,CAAC,KAAK;AACxB,UAAQ,OAAO,OAAO,SAAS;AAC/B,QAAM,MAAM,OAAO;AACnB,SAAO,KAAK,MAAM,MAAM,OAAO,MAAM,IAAI;AAC7C;AACA,SAAS,YAAY,OAAO;AACxB,QAAM,OAAO,MAAM,YAAY,CAAC,EAAE,CAAC;AACnC,QAAM,OAAO,MAAM,YAAY,MAAM,YAAY,SAAS,CAAC,EAAE,CAAC;AAC9D,QAAM,OAAO,MAAM,YAAY,MAAM,YAAY,SAAS,CAAC,EAAE,CAAC;AAC9D,QAAM,OAAO,MAAM,YAAY,CAAC,EAAE,CAAC;AACnC,SAAO;AAAA,IACH,MAAM,MAAM;AAAA,IACZ,UAAU,MAAM;AAAA,IAChB,aAAa,MAAM;AAAA,IACnB,iBAAiB,CAAC,MAAM,IAAI;AAAA,IAC5B,iBAAiB,CAAC,MAAM,IAAI;AAAA,EAChC;AACJ;AAIO,IAAM,SAAS;AAAA,EAClB;AAAA,IACI,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,MACT,CAAC,GAAG,CAAC;AAAA,MACL,CAAC,KAAK,CAAC;AAAA,IACX;AAAA,EACJ;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,UAAU,CAAC,KAAK,EAAE;AAAA,IAClB,aAAa;AAAA,MACT,CAAC,IAAI,GAAG;AAAA,MACR,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,KAAK,EAAE;AAAA,IACZ;AAAA,EACJ;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,UAAU,CAAC,IAAI,EAAE;AAAA,IACjB,aAAa;AAAA,MACT,CAAC,IAAI,GAAG;AAAA,MACR,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,KAAK,EAAE;AAAA,IACZ;AAAA,EACJ;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,UAAU,CAAC,IAAI,EAAE;AAAA,IACjB,aAAa;AAAA,MACT,CAAC,IAAI,GAAG;AAAA,MACR,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,KAAK,EAAE;AAAA,IACZ;AAAA,EACJ;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,UAAU,CAAC,IAAI,GAAG;AAAA,IAClB,aAAa;AAAA,MACT,CAAC,IAAI,GAAG;AAAA,MACR,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,KAAK,EAAE;AAAA,IACZ;AAAA,EACJ;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,UAAU,CAAC,KAAK,GAAG;AAAA,IACnB,aAAa;AAAA,MACT,CAAC,IAAI,GAAG;AAAA,MACR,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,KAAK,EAAE;AAAA,IACZ;AAAA,EACJ;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,UAAU,CAAC,KAAK,GAAG;AAAA,IACnB,aAAa;AAAA,MACT,CAAC,IAAI,GAAG;AAAA,MACR,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,KAAK,EAAE;AAAA,IACZ;AAAA,EACJ;AAAA,EACA;AAAA,IACI,MAAM;AAAA,IACN,UAAU,CAAC,KAAK,GAAG;AAAA,IACnB,aAAa;AAAA,MACT,CAAC,IAAI,GAAG;AAAA,MACR,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,IAAI,EAAE;AAAA,MACP,CAAC,KAAK,EAAE;AAAA,IACZ;AAAA,EACJ;AACJ;", "names": []}