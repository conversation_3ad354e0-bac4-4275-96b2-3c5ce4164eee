var v=(p,r,t)=>new Promise((u,e)=>{var m=o=>{try{d(t.next(o))}catch(l){e(l)}},h=o=>{try{d(t.throw(o))}catch(l){e(l)}},d=o=>o.done?u(o.value):Promise.resolve(o.value).then(m,h);d((t=t.apply(p,r)).next())});import{g as C,ce as R,o as A,t as P,cf as S,e as T,$ as b,a_ as V,cg as E,a as N,V as k}from"./bootstrap-DShsrVit.js";import{u as I}from"./use-watermark-C6i-HLM1.js";import{_ as O,N as U,a as W,b as q}from"./layout.vue_vue_type_script_setup_true_lang-D-GDfR6Z.js";import{u as G}from"./use-modal-B0smF4x0.js";import{_ as H}from"./avatar.vue_vue_type_script_setup_true_lang-CyCVfWwM.js";import{a4 as x,a$ as j,b0 as z,V as B,af as M,am as D,n as i,ah as c,a3 as a,ae as F,O as J,J as _,ag as Q,a0 as y,bC as w}from"../jse/index-index-BMh_AyeW.js";import"./bell-D0icksiV.js";import"./popover.vue_vue_type_script_setup_true_lang-CFXFI0jw.js";import"./theme-toggle.vue_vue_type_script_setup_true_lang-DzmrFWHD.js";import"./sun-ChDfqwZ7.js";import"./icon.vue_vue_type_script_setup_true_lang-BK5optdP.js";import"./use-tabs-C64_EnSy.js";import"./x-B-ntYT_e.js";import"./use-drawer-Qcdpj8Bl.js";import"./loading-Cqdke3S1.js";import"./TabsList.vue_vue_type_script_setup_true_lang-DfMjVjnv.js";import"./rotate-cw-B0JNpqtv.js";const X=C("BookOpenTextIcon",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M16 12h2",key:"7q9ll5"}],["path",{d:"M16 8h2",key:"msurwy"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}],["path",{d:"M6 12h2",key:"32wvfc"}],["path",{d:"M6 8h2",key:"30oboj"}]]),K=x({name:"LoginExpiredModal",__name:"login-expired-modal",props:j({avatar:{default:""},codeLoginPath:{},forgetPasswordPath:{},loading:{type:Boolean},qrCodeLoginPath:{},registerPath:{},showCodeLogin:{type:Boolean},showForgetPassword:{type:Boolean},showQrcodeLogin:{type:Boolean},showRegister:{type:Boolean},showRememberMe:{type:Boolean},showThirdPartyLogin:{type:Boolean},subTitle:{},title:{},submitButtonText:{}},{open:{type:Boolean},openModifiers:{}}),emits:["update:open"],setup(p){const r=z(p,"open"),[t,u]=G();return B(()=>r.value,e=>{u.setState({isOpen:e})}),(e,m)=>(M(),D("div",null,[i(a(t),{closable:!1,"close-on-click-modal":!1,"close-on-press-escape":!1,footer:!1,"fullscreen-button":!1,header:!1,class:"border-none px-10 py-6 text-center shadow-xl sm:w-[600px] sm:rounded-2xl md:h-[unset]"},{default:c(()=>[i(a(H),{src:e.avatar,class:"mx-auto mb-6 size-20"},null,8,["src"]),i(a(R),{"show-forget-password":!1,"show-register":!1,"show-remember-me":!1,"sub-title":e.$t("authentication.loginAgainSubTitle"),title:e.$t("authentication.loginAgainTitle")},{default:c(()=>[F(e.$slots,"default")]),_:3},8,["sub-title","title"])]),_:3})]))}}),ge=x({__name:"basic",setup(p){const r=J([{avatar:"https://avatar.vercel.sh/vercel.svg?text=VB",date:"3小时前",isRead:!0,message:"描述信息描述信息描述信息",title:"收到了 14 份新周报"},{avatar:"https://avatar.vercel.sh/1",date:"刚刚",isRead:!1,message:"描述信息描述信息描述信息",title:"朱偏右 回复了你"},{avatar:"https://avatar.vercel.sh/1",date:"2024-01-01",isRead:!1,message:"描述信息描述信息描述信息",title:"曲丽丽 评论了你"},{avatar:"https://avatar.vercel.sh/satori",date:"1天前",isRead:!1,message:"描述信息描述信息描述信息",title:"代办提醒"}]),t=A(),u=P(),e=S(),{destroyWatermark:m,updateWatermark:h}=I(),d=_(()=>r.value.some(s=>!s.isRead)),o=_(()=>[{handler:()=>{w(N,{target:"_blank"})},icon:X,text:b("ui.widgets.document")},{handler:()=>{w(k,{target:"_blank"})},icon:V,text:"GitHub"},{handler:()=>{w(`${k}/issues`,{target:"_blank"})},icon:E,text:b("ui.widgets.qa")}]),l=_(()=>{var s,n;return(n=(s=t.userInfo)==null?void 0:s.avatar)!=null?n:y.app.defaultAvatar});function g(){return v(this,null,function*(){yield u.logout(!1)})}function $(){r.value=[]}function L(){r.value.forEach(s=>s.isRead=!0)}return B(()=>y.app.watermark,s=>v(this,null,function*(){var n;s?yield h({content:`${(n=t.userInfo)==null?void 0:n.username}`}):m()}),{immediate:!0}),(s,n)=>(M(),Q(a(q),{onClearPreferencesAndLogout:g},{"user-dropdown":c(()=>{var f;return[i(a(O),{avatar:l.value,menus:o.value,text:(f=a(t).userInfo)==null?void 0:f.realName,description:"<EMAIL>","tag-text":"Pro",onLogout:g},null,8,["avatar","menus","text"])]}),notification:c(()=>[i(a(U),{dot:d.value,notifications:r.value,onClear:$,onMakeAll:L},null,8,["dot","notifications"])]),extra:c(()=>[i(a(K),{open:a(e).loginExpired,"onUpdate:open":n[0]||(n[0]=f=>a(e).loginExpired=f),avatar:l.value},{default:c(()=>[i(T)]),_:1},8,["open","avatar"])]),"lock-screen":c(()=>[i(a(W),{avatar:l.value,onToLogin:g},null,8,["avatar"])]),_:1}))}});export{ge as default};
