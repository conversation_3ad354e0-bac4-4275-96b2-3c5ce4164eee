var u=(s,o,t)=>new Promise((e,p)=>{var c=a=>{try{l(t.next(a))}catch(d){p(d)}},k=a=>{try{l(t.throw(a))}catch(d){p(d)}},l=a=>a.done?e(a.value):Promise.resolve(a.value).then(c,k);l((t=t.apply(s,o)).next())});import{b7 as y,B as x}from"./bootstrap-DShsrVit.js";import{C as g}from"./index-B_b7xM74.js";import{_}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as B,af as M,ag as b,ah as n,a3 as r,ap as f,an as m,n as i,a0 as v,a1 as C}from"../jse/index-index-BMh_AyeW.js";function E(s){return u(this,null,function*(){return y.get("/status",{params:{status:s}})})}const A=B({__name:"index",setup(s){function o(t){return u(this,null,function*(){const e=v.app.loginExpiredMode;C({app:{loginExpiredMode:t}}),yield E("401"),C({app:{loginExpiredMode:e}})})}return(t,e)=>(M(),b(r(_),{title:"登录过期演示"},{description:n(()=>e[2]||(e[2]=[f("div",{class:"text-foreground/80 mt-2"},[m(" 接口请求遇到401状态码时，需要重新登录。有两种方式： "),f("p",null,"1.转到登录页，登录成功后跳转回原页面"),f("p",null," 2.弹出重新登录弹窗，登录后关闭弹窗，不进行任何页面跳转（刷新后还是会跳转登录页面） ")],-1)])),default:n(()=>[i(r(g),{class:"mb-5",title:"跳转登录页面方式"},{default:n(()=>[i(r(x),{type:"primary",onClick:e[0]||(e[0]=p=>o("page"))},{default:n(()=>e[3]||(e[3]=[m(" 点击触发 ")])),_:1})]),_:1}),i(r(g),{class:"mb-5",title:"登录弹窗方式"},{default:n(()=>[i(r(x),{type:"primary",onClick:e[1]||(e[1]=p=>o("modal"))},{default:n(()=>e[4]||(e[4]=[m(" 点击触发 ")])),_:1})]),_:1})]),_:1}))}});export{A as default};
