import * as Cesium from 'cesium';
import { executeTask } from './GPExecute';
import GPConfig from '../../../config/GPConfig';
import {
  arcgisToGeoJSON,
  geojsonToArcGIS,
} from '@esri/arcgis-to-geojson-utils';

export default class OverlayAnalysis {
  constructor(params) {
    this.viewer = params.that._viewer;
    this.gp = params.gp || GPConfig[params.overlayType];
  }
  async execute(params, positions) {
    this.remove();
    let viewer = this.viewer;
    let filter = null;
    if (positions) {
      positions = positions.map((p) => [p.lng, p.lat]);
      filter = JSON.stringify({
        objectIdFieldName: 'objectid',
        globalIdFieldName: '',
        geometryType: 'esriGeometryPolygon',
        spatialReference: {
          wkid: 4326,
          latestWkid: 4326,
        },
        fields: [],
        features: [
          {
            geometry: {
              rings: [positions],
            },
          },
        ],
      });
    }

    let material = params.material;
    let layerKey = params.layerKey;
    delete params.material;
    delete params.layerKey;
    if (params.filter) {
      filter = JSON.stringify({
        objectIdFieldName: 'objectid',
        globalIdFieldName: '',
        geometryType: 'esriGeometryPolygon',
        spatialReference: {
          wkid: 4326,
          latestWkid: 4326,
        },
        fields: [],
        features: params.filter,
      });
      delete params.filter;
    }
    params = {
      ...params,
      filterFeature: filter,
      'env:outSR': 4326,
      'env:processSR': 4326,
      returnTrueCurves: false,
      returnFeatureCollection: false,
      f: 'json',
    };
    const res = await executeTask(this.gp, params);
    console.log(res);
    const result = res.results[0].value;
    //delete result.crs
    //delete result.exceededTransferLimit
    let json = arcgisToGeoJSON(result);
    console.log(json);
    this.dataSource = await Cesium.GeoJsonDataSource.load(json, {
      clampToGround: true,
    });
    this.dataSource.name = layerKey;
    if (material) {
      //console.log(this.dataSource.entities.values)
      this.dataSource.entities.values.forEach((entity) => {
        /* entity.polygon && (entity.polygon.material = new Cesium.ImageMaterialProperty({
					image:material,
					repeat:new Cesium.Cartesian2(2,2)
				})); */
        /* entity.polygon && (entity.polygon.material = new Cesium.StripeMaterialProperty({
					evenColor : Cesium.Color.WHITE,//棋盘第一个颜色
					oddColor : Cesium.Color.RED,//棋盘第二个颜色
					repeat : 32,//重复次数
					offset:20,//偏移量
					orientation:Cesium.StripeOrientation.VERTICAL //水平或垂直默认水平
				})); */
      });
    }
    viewer.dataSources.add(this.dataSource);
    //viewer.flyTo(this.dataSource)
  }
  remove() {
    let viewer = this.viewer;
    if (this.dataSource) {
      viewer.dataSources.remove(this.dataSource);
    }
  }
}
