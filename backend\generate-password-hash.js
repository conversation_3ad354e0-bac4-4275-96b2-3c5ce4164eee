const bcrypt = require('bcryptjs');

async function generatePasswordHash() {
  const passwords = [
    { name: 'admin123', value: 'admin123' },
    { name: '8888a8888#@', value: '8888a8888#@' }
  ];

  const saltRounds = 10;

  try {
    for (const pwd of passwords) {
      const hash = await bcrypt.hash(pwd.value, saltRounds);
      console.log(`\n=== ${pwd.name} ===`);
      console.log('Password:', pwd.value);
      console.log('Hash:', hash);

      // 验证哈希
      const isValid = await bcrypt.compare(pwd.value, hash);
      console.log('Verification:', isValid);
    }

    // 生成SQL插入语句
    console.log('\n=== SQL Insert Statements ===');

    // admin用户
    const adminHash = await bcrypt.hash('admin123', saltRounds);
    console.log(`-- admin用户（密码：admin123）`);
    console.log(`INSERT INTO users (employee_id, username, password, real_name, status) VALUES`);
    console.log(`('admin', 'admin', '${adminHash}', '系统管理员', 1);`);

    // 测试用户
    const testHash = await bcrypt.hash('8888a8888#@', saltRounds);
    console.log(`\n-- 测试用户（密码：8888a8888#@）`);
    console.log(`INSERT INTO users (employee_id, username, password, real_name, phone, status) VALUES`);
    console.log(`('test001', 'testuser', '${testHash}', '何', '15259630375', 1);`);

  } catch (error) {
    console.error('Error:', error);
  }
}

generatePasswordHash();
