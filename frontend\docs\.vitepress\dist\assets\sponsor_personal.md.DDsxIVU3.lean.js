import{ao as s,k as a,z as l,I as n,l as e,ay as o,j as r}from"./chunks/framework.C8U7mBUf.js";const t=JSON.parse('{"title":"赞助","description":"","frontmatter":{},"headers":[],"relativePath":"sponsor/personal.md","filePath":"sponsor/personal.md"}');const i=s({name:"sponsor/personal.md"},[["render",function(s,t,i,p,u,c){const d=o("NolebaseGitContributors"),m=o("NolebaseGitChangelog");return r(),a("div",null,[t[0]||(t[0]=l("h1",{id:"赞助",tabindex:"-1"},[n("赞助 "),l("a",{class:"header-anchor",href:"#赞助","aria-label":'Permalink to "赞助"'},"​")],-1)),t[1]||(t[1]=l("p",null,"如果你觉得这个项目对你有帮助，你可以帮作者买一杯咖啡表示支持!",-1)),t[2]||(t[2]=l("p",null,[l("img",{src:"https://unpkg.com/@vbenjs/static-source@0.1.7/source/sponsor.png",alt:""})],-1)),t[3]||(t[3]=l("p",null,"您的赞助将帮助我们：",-1)),t[4]||(t[4]=l("ul",null,[l("li",null,"维持项目的基础设施，如服务器、域名、社群费用。"),l("li",null,"支持开发者的贡献和加快新功能的开发。")],-1)),t[5]||(t[5]=l("p",null,"感谢所有现有的和未来的赞助者，您的支持对我们来说至关重要，让我们一起推动项目继续前行。",-1)),e(d),e(m)])}]]);export{t as __pageData,i as default};
