import{a4 as f,af as u,ag as b,ah as e,a3 as o,n as a,an as h,aU as l}from"../jse/index-index-BMh_AyeW.js";import{u as m}from"./form-DnT3S1ma.js";import{_ as N}from"./doc-button.vue_vue_type_script_setup_true_lang-BzJdr9vE.js";import{by as P,B as C}from"./bootstrap-DShsrVit.js";import{C as i}from"./index-B_b7xM74.js";import{_ as k}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";const y=f({__name:"basic",setup(w){const[c,t]=m({commonConfig:{componentProps:{class:"w-full"}},fieldMappingTime:[["rangePicker",["startTime","endTime"],"YYYY-MM-DD"]],handleSubmit:s,layout:"horizontal",schema:[{component:"Input",componentProps:{placeholder:"请输入用户名"},fieldName:"username",label:"字符串"},{component:"InputPassword",componentProps:{placeholder:"请输入密码"},fieldName:"password",label:"密码"},{component:"InputNumber",componentProps:{placeholder:"请输入"},fieldName:"number",label:"数字(带后缀)",suffix:()=>"¥"},{component:"Select",componentProps:{allowClear:!0,filterOption:!0,options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}],placeholder:"请选择",showSearch:!0},fieldName:"options",label:"下拉选"},{component:"RadioGroup",componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]},fieldName:"radioGroup",label:"单选组"},{component:"Radio",fieldName:"radio",label:"",renderComponentContent:()=>({default:()=>["Radio"]})},{component:"CheckboxGroup",componentProps:{name:"cname",options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]},fieldName:"checkboxGroup",label:"多选组"},{component:"Checkbox",fieldName:"checkbox",label:"",renderComponentContent:()=>({default:()=>["我已阅读并同意"]})},{component:"Mentions",componentProps:{options:[{label:"afc163",value:"afc163"},{label:"zombieJ",value:"zombieJ"}],placeholder:"请输入"},fieldName:"mentions",label:"提及"},{component:"Rate",fieldName:"rate",label:"评分"},{component:"Switch",componentProps:{class:"w-auto"},fieldName:"switch",label:"开关"},{component:"DatePicker",fieldName:"datePicker",label:"日期选择框"},{component:"RangePicker",fieldName:"rangePicker",label:"范围选择器"},{component:"TimePicker",fieldName:"timePicker",label:"时间选择框"},{component:"TreeSelect",componentProps:{allowClear:!0,placeholder:"请选择",showSearch:!0,treeData:[{label:"root 1",value:"root 1",children:[{label:"parent 1",value:"parent 1",children:[{label:"parent 1-0",value:"parent 1-0",children:[{label:"my leaf",value:"leaf1"},{label:"your leaf",value:"leaf2"}]},{label:"parent 1-1",value:"parent 1-1"}]},{label:"parent 2",value:"parent 2"}]}],treeNodeFilterProp:"label"},fieldName:"treeSelect",label:"树选择"}],wrapperClass:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"}),[p]=m({commonConfig:{componentProps:{class:"w-full"}},layout:"horizontal",schema:[{component:"Select",fieldName:"field1",label:"字符串"},{component:"TreeSelect",fieldName:"field2",label:"字符串"},{component:"Mentions",fieldName:"field3",label:"字符串"},{component:"Input",fieldName:"field4",label:"字符串"},{component:"InputNumber",fieldName:"field5",formItemClass:"col-start-3",label:"前面空了一列"},{component:"Textarea",fieldName:"field6",formItemClass:"col-span-3 items-baseline",label:"占满三列"},{component:"Input",fieldName:"field7",formItemClass:"col-span-2 col-start-2",label:"占满2列"},{component:"Input",fieldName:"field8",formItemClass:"col-start-2",label:"左右留空"},{component:"InputPassword",fieldName:"field9",formItemClass:"col-start-1",label:"字符串"}],wrapperClass:"grid-cols-3"});function s(n){P.success({content:`form values: ${JSON.stringify(n)}`})}function d(){t.setValues({checkboxGroup:["1"],datePicker:l("2022-01-01"),mentions:"@afc163",number:3,options:"1",password:"2",radioGroup:"1",rangePicker:[l("2022-01-01"),l("2022-01-02")],rate:3,switch:!0,timePicker:l("2022-01-01 12:00:00"),treeSelect:"leaf1",username:"1"}),t.setFieldValue("checkbox",!0)}return(n,r)=>(u(),b(o(k),{"content-class":"flex flex-col gap-4",description:"表单组件基础示例，请注意，该页面用到的参数代码会添加一些简单注释，方便理解，请仔细查看。",title:"表单组件"},{extra:e(()=>[a(N,{path:"/components/common-ui/vben-form"})]),default:e(()=>[a(o(i),{title:"基础示例"},{extra:e(()=>[a(o(C),{type:"primary",onClick:d},{default:e(()=>r[0]||(r[0]=[h("设置表单值")])),_:1})]),default:e(()=>[a(o(c))]),_:1}),a(o(i),{title:"使用tailwind自定义布局"},{default:e(()=>[a(o(p))]),_:1})]),_:1}))}});export{y as default};
