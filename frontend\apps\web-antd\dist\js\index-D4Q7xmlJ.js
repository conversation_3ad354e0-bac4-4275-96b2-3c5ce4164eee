var Ht=Object.defineProperty,qt=Object.defineProperties;var zt=Object.getOwnPropertyDescriptors;var He=Object.getOwnPropertySymbols;var $t=Object.prototype.hasOwnProperty,Gt=Object.prototype.propertyIsEnumerable;var W=(e,t)=>(t=Symbol[e])?t:Symbol.for("Symbol."+e),Wt=e=>{throw TypeError(e)};var qe=(e,t,r)=>t in e?Ht(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,D=(e,t)=>{for(var r in t||(t={}))$t.call(t,r)&&qe(e,r,t[r]);if(He)for(var r of He(t))Gt.call(t,r)&&qe(e,r,t[r]);return e},ye=(e,t)=>qt(e,zt(t));var v=(e,t,r)=>new Promise((n,i)=>{var s=u=>{try{o(r.next(u))}catch(c){i(c)}},a=u=>{try{o(r.throw(u))}catch(c){i(c)}},o=u=>u.done?n(u.value):Promise.resolve(u.value).then(s,a);o((r=r.apply(e,t)).next())}),H=function(e,t){this[0]=e,this[1]=t},ge=(e,t,r)=>{var n=(a,o,u,c)=>{try{var f=r[a](o),d=(o=f.value)instanceof H,w=f.done;Promise.resolve(d?o[0]:o).then(y=>d?n(a==="return"?a:"next",o[1]?{done:y.done,value:y.value}:y,u,c):u({value:y,done:w})).catch(y=>n("throw",y,u,c))}catch(y){c(y)}},i=a=>s[a]=o=>new Promise((u,c)=>n(a,o,u,c)),s={};return r=r.apply(e,t),s[W("asyncIterator")]=()=>s,i("next"),i("throw"),i("return"),s},we=e=>{var t=e[W("asyncIterator")],r=!1,n,i={};return t==null?(t=e[W("iterator")](),n=s=>i[s]=a=>t[s](a)):(t=t.call(e),n=s=>i[s]=a=>{if(r){if(r=!1,s==="throw")throw a;return a}return r=!0,{done:!1,value:new H(new Promise(o=>{var u=t[s](a);u instanceof Object||Wt("Object expected"),o(u)}),1)}}),i[W("iterator")]=()=>i,n("next"),"throw"in t?n("throw"):i.throw=s=>{throw s},"return"in t&&n("return"),i},ze=(e,t,r)=>(t=e[W("asyncIterator")])?t.call(e):(e=e[W("iterator")](),t={},r=(n,i)=>(i=e[n])&&(t[n]=s=>new Promise((a,o,u)=>(s=i.call(e,s),u=s.done,Promise.resolve(s.value).then(c=>a({value:c,done:u}),o)))),r("next"),r("return"),t);import{r as Jt,a as Vt,v as Ce,a7 as q,y as Xt,q as Kt}from"./bootstrap-5OPUVRWy.js";import{p as $e,B as Qt,S as jt}from"./Base-xeJpkIWP.js";import{a as K,b as Q,f as j,H as be,e as Ge,s as B,g as Se,t as Zt,q as Yt,h as er}from"../jse/index-index-DyHD_jbN.js";function ot(e,t){return function(){return e.apply(t,arguments)}}const{toString:tr}=Object.prototype,{getPrototypeOf:Ne}=Object,le=(e=>t=>{const r=tr.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),P=e=>(e=e.toLowerCase(),t=>le(t)===e),ce=e=>t=>typeof t===e,{isArray:V}=Array,ee=ce("undefined");function rr(e){return e!==null&&!ee(e)&&e.constructor!==null&&!ee(e.constructor)&&L(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const lt=P("ArrayBuffer");function nr(e){let t;return typeof ArrayBuffer!="undefined"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&lt(e.buffer),t}const ir=ce("string"),L=ce("function"),ct=ce("number"),ue=e=>e!==null&&typeof e=="object",sr=e=>e===!0||e===!1,ne=e=>{if(le(e)!=="object")return!1;const t=Ne(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},ar=P("Date"),or=P("File"),lr=P("Blob"),cr=P("FileList"),ur=e=>ue(e)&&L(e.pipe),fr=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||L(e.append)&&((t=le(e))==="formdata"||t==="object"&&L(e.toString)&&e.toString()==="[object FormData]"))},dr=P("URLSearchParams"),[pr,hr,mr,yr]=["ReadableStream","Request","Response","Headers"].map(P),gr=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function te(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e=="undefined")return;let n,i;if(typeof e!="object"&&(e=[e]),V(e))for(n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else{const s=r?Object.getOwnPropertyNames(e):Object.keys(e),a=s.length;let o;for(n=0;n<a;n++)o=s[n],t.call(null,e[o],o,e)}}function ut(e,t){t=t.toLowerCase();const r=Object.keys(e);let n=r.length,i;for(;n-- >0;)if(i=r[n],t===i.toLowerCase())return i;return null}const z=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:global,ft=e=>!ee(e)&&e!==z;function Ae(){const{caseless:e}=ft(this)&&this||{},t={},r=(n,i)=>{const s=e&&ut(t,i)||i;ne(t[s])&&ne(n)?t[s]=Ae(t[s],n):ne(n)?t[s]=Ae({},n):V(n)?t[s]=n.slice():t[s]=n};for(let n=0,i=arguments.length;n<i;n++)arguments[n]&&te(arguments[n],r);return t}const wr=(e,t,r,{allOwnKeys:n}={})=>(te(t,(i,s)=>{r&&L(i)?e[s]=ot(i,r):e[s]=i},{allOwnKeys:n}),e),Cr=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),br=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},Sr=(e,t,r,n)=>{let i,s,a;const o={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),s=i.length;s-- >0;)a=i[s],(!n||n(a,e,t))&&!o[a]&&(t[a]=e[a],o[a]=!0);e=r!==!1&&Ne(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},Er=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},xr=e=>{if(!e)return null;if(V(e))return e;let t=e.length;if(!ct(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},Tr=(e=>t=>e&&t instanceof e)(typeof Uint8Array!="undefined"&&Ne(Uint8Array)),vr=(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let i;for(;(i=n.next())&&!i.done;){const s=i.value;t.call(e,s[0],s[1])}},Rr=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},Ar=P("HTMLFormElement"),Or=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,i){return n.toUpperCase()+i}),We=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),_r=P("RegExp"),dt=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};te(r,(i,s)=>{let a;(a=t(i,s,e))!==!1&&(n[s]=a||i)}),Object.defineProperties(e,n)},Mr=e=>{dt(e,(t,r)=>{if(L(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(L(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Lr=(e,t)=>{const r={},n=i=>{i.forEach(s=>{r[s]=!0})};return V(e)?n(e):n(String(e).split(t)),r},kr=()=>{},Pr=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t,Ee="abcdefghijklmnopqrstuvwxyz",Je="0123456789",pt={DIGIT:Je,ALPHA:Ee,ALPHA_DIGIT:Ee+Ee.toUpperCase()+Je},Dr=(e=16,t=pt.ALPHA_DIGIT)=>{let r="";const{length:n}=t;for(;e--;)r+=t[Math.random()*n|0];return r};function Nr(e){return!!(e&&L(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const Ir=e=>{const t=new Array(10),r=(n,i)=>{if(ue(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[i]=n;const s=V(n)?[]:{};return te(n,(a,o)=>{const u=r(a,i+1);!ee(u)&&(s[o]=u)}),t[i]=void 0,s}}return n};return r(e,0)},Fr=P("AsyncFunction"),Br=e=>e&&(ue(e)||L(e))&&L(e.then)&&L(e.catch),ht=((e,t)=>e?setImmediate:t?((r,n)=>(z.addEventListener("message",({source:i,data:s})=>{i===z&&s===r&&n.length&&n.shift()()},!1),i=>{n.push(i),z.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",L(z.postMessage)),Ur=typeof queueMicrotask!="undefined"?queueMicrotask.bind(z):typeof process!="undefined"&&process.nextTick||ht,l={isArray:V,isArrayBuffer:lt,isBuffer:rr,isFormData:fr,isArrayBufferView:nr,isString:ir,isNumber:ct,isBoolean:sr,isObject:ue,isPlainObject:ne,isReadableStream:pr,isRequest:hr,isResponse:mr,isHeaders:yr,isUndefined:ee,isDate:ar,isFile:or,isBlob:lr,isRegExp:_r,isFunction:L,isStream:ur,isURLSearchParams:dr,isTypedArray:Tr,isFileList:cr,forEach:te,merge:Ae,extend:wr,trim:gr,stripBOM:Cr,inherits:br,toFlatObject:Sr,kindOf:le,kindOfTest:P,endsWith:Er,toArray:xr,forEachEntry:vr,matchAll:Rr,isHTMLForm:Ar,hasOwnProperty:We,hasOwnProp:We,reduceDescriptors:dt,freezeMethods:Mr,toObjectSet:Lr,toCamelCase:Or,noop:kr,toFiniteNumber:Pr,findKey:ut,global:z,isContextDefined:ft,ALPHABET:pt,generateString:Dr,isSpecCompliantForm:Nr,toJSONObject:Ir,isAsyncFn:Fr,isThenable:Br,setImmediate:ht,asap:Ur};function g(e,t,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}l.inherits(g,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:l.toJSONObject(this.config),code:this.code,status:this.status}}});const mt=g.prototype,yt={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{yt[e]={value:e}});Object.defineProperties(g,yt);Object.defineProperty(mt,"isAxiosError",{value:!0});g.from=(e,t,r,n,i,s)=>{const a=Object.create(mt);return l.toFlatObject(e,a,function(u){return u!==Error.prototype},o=>o!=="isAxiosError"),g.call(a,e.message,t,r,n,i),a.cause=e,a.name=e.name,s&&Object.assign(a,s),a};const Hr=null;function Oe(e){return l.isPlainObject(e)||l.isArray(e)}function gt(e){return l.endsWith(e,"[]")?e.slice(0,-2):e}function Ve(e,t,r){return e?e.concat(t).map(function(i,s){return i=gt(i),!r&&s?"["+i+"]":i}).join(r?".":""):t}function qr(e){return l.isArray(e)&&!e.some(Oe)}const zr=l.toFlatObject(l,{},null,function(t){return/^is[A-Z]/.test(t)});function fe(e,t,r){if(!l.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=l.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,h){return!l.isUndefined(h[m])});const n=r.metaTokens,i=r.visitor||f,s=r.dots,a=r.indexes,u=(r.Blob||typeof Blob!="undefined"&&Blob)&&l.isSpecCompliantForm(t);if(!l.isFunction(i))throw new TypeError("visitor must be a function");function c(p){if(p===null)return"";if(l.isDate(p))return p.toISOString();if(!u&&l.isBlob(p))throw new g("Blob is not supported. Use a Buffer instead.");return l.isArrayBuffer(p)||l.isTypedArray(p)?u&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function f(p,m,h){let b=p;if(p&&!h&&typeof p=="object"){if(l.endsWith(m,"{}"))m=n?m:m.slice(0,-2),p=JSON.stringify(p);else if(l.isArray(p)&&qr(p)||(l.isFileList(p)||l.endsWith(m,"[]"))&&(b=l.toArray(p)))return m=gt(m),b.forEach(function(x,O){!(l.isUndefined(x)||x===null)&&t.append(a===!0?Ve([m],O,s):a===null?m:m+"[]",c(x))}),!1}return Oe(p)?!0:(t.append(Ve(h,m,s),c(p)),!1)}const d=[],w=Object.assign(zr,{defaultVisitor:f,convertValue:c,isVisitable:Oe});function y(p,m){if(!l.isUndefined(p)){if(d.indexOf(p)!==-1)throw Error("Circular reference detected in "+m.join("."));d.push(p),l.forEach(p,function(b,C){(!(l.isUndefined(b)||b===null)&&i.call(t,b,l.isString(C)?C.trim():C,m,w))===!0&&y(b,m?m.concat(C):[C])}),d.pop()}}if(!l.isObject(e))throw new TypeError("data must be an object");return y(e),t}function Xe(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function Ie(e,t){this._pairs=[],e&&fe(e,this,t)}const wt=Ie.prototype;wt.append=function(t,r){this._pairs.push([t,r])};wt.toString=function(t){const r=t?function(n){return t.call(this,n,Xe)}:Xe;return this._pairs.map(function(i){return r(i[0])+"="+r(i[1])},"").join("&")};function $r(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ct(e,t,r){if(!t)return e;const n=r&&r.encode||$r,i=r&&r.serialize;let s;if(i?s=i(t,r):s=l.isURLSearchParams(t)?t.toString():new Ie(t,r).toString(n),s){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Ke{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){l.forEach(this.handlers,function(n){n!==null&&t(n)})}}const bt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Gr=typeof URLSearchParams!="undefined"?URLSearchParams:Ie,Wr=typeof FormData!="undefined"?FormData:null,Jr=typeof Blob!="undefined"?Blob:null,Vr={isBrowser:!0,classes:{URLSearchParams:Gr,FormData:Wr,Blob:Jr},protocols:["http","https","file","blob","url","data"]},Fe=typeof window!="undefined"&&typeof document!="undefined",_e=typeof navigator=="object"&&navigator||void 0,Xr=Fe&&(!_e||["ReactNative","NativeScript","NS"].indexOf(_e.product)<0),Kr=typeof WorkerGlobalScope!="undefined"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Qr=Fe&&window.location.href||"http://localhost",jr=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Fe,hasStandardBrowserEnv:Xr,hasStandardBrowserWebWorkerEnv:Kr,navigator:_e,origin:Qr},Symbol.toStringTag,{value:"Module"})),R=D(D({},jr),Vr);function Zr(e,t){return fe(e,new R.classes.URLSearchParams,Object.assign({visitor:function(r,n,i,s){return R.isNode&&l.isBuffer(r)?(this.append(n,r.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function Yr(e){return l.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function en(e){const t={},r=Object.keys(e);let n;const i=r.length;let s;for(n=0;n<i;n++)s=r[n],t[s]=e[s];return t}function St(e){function t(r,n,i,s){let a=r[s++];if(a==="__proto__")return!0;const o=Number.isFinite(+a),u=s>=r.length;return a=!a&&l.isArray(i)?i.length:a,u?(l.hasOwnProp(i,a)?i[a]=[i[a],n]:i[a]=n,!o):((!i[a]||!l.isObject(i[a]))&&(i[a]=[]),t(r,n,i[a],s)&&l.isArray(i[a])&&(i[a]=en(i[a])),!o)}if(l.isFormData(e)&&l.isFunction(e.entries)){const r={};return l.forEachEntry(e,(n,i)=>{t(Yr(n),i,r,0)}),r}return null}function tn(e,t,r){if(l.isString(e))try{return(t||JSON.parse)(e),l.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const re={transitional:bt,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",i=n.indexOf("application/json")>-1,s=l.isObject(t);if(s&&l.isHTMLForm(t)&&(t=new FormData(t)),l.isFormData(t))return i?JSON.stringify(St(t)):t;if(l.isArrayBuffer(t)||l.isBuffer(t)||l.isStream(t)||l.isFile(t)||l.isBlob(t)||l.isReadableStream(t))return t;if(l.isArrayBufferView(t))return t.buffer;if(l.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Zr(t,this.formSerializer).toString();if((o=l.isFileList(t))||n.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return fe(o?{"files[]":t}:t,u&&new u,this.formSerializer)}}return s||i?(r.setContentType("application/json",!1),tn(t)):t}],transformResponse:[function(t){const r=this.transitional||re.transitional,n=r&&r.forcedJSONParsing,i=this.responseType==="json";if(l.isResponse(t)||l.isReadableStream(t))return t;if(t&&l.isString(t)&&(n&&!this.responseType||i)){const a=!(r&&r.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(o){if(a)throw o.name==="SyntaxError"?g.from(o,g.ERR_BAD_RESPONSE,this,null,this.response):o}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:R.classes.FormData,Blob:R.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};l.forEach(["delete","get","head","post","put","patch"],e=>{re.headers[e]={}});const rn=l.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),nn=e=>{const t={};let r,n,i;return e&&e.split(`
`).forEach(function(a){i=a.indexOf(":"),r=a.substring(0,i).trim().toLowerCase(),n=a.substring(i+1).trim(),!(!r||t[r]&&rn[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},Qe=Symbol("internals");function Z(e){return e&&String(e).trim().toLowerCase()}function ie(e){return e===!1||e==null?e:l.isArray(e)?e.map(ie):String(e)}function sn(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const an=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function xe(e,t,r,n,i){if(l.isFunction(n))return n.call(this,t,r);if(i&&(t=r),!!l.isString(t)){if(l.isString(n))return t.indexOf(n)!==-1;if(l.isRegExp(n))return n.test(t)}}function on(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function ln(e,t){const r=l.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(i,s,a){return this[n].call(this,t,i,s,a)},configurable:!0})})}let A=class{constructor(t){t&&this.set(t)}set(t,r,n){const i=this;function s(o,u,c){const f=Z(u);if(!f)throw new Error("header name must be a non-empty string");const d=l.findKey(i,f);(!d||i[d]===void 0||c===!0||c===void 0&&i[d]!==!1)&&(i[d||u]=ie(o))}const a=(o,u)=>l.forEach(o,(c,f)=>s(c,f,u));if(l.isPlainObject(t)||t instanceof this.constructor)a(t,r);else if(l.isString(t)&&(t=t.trim())&&!an(t))a(nn(t),r);else if(l.isHeaders(t))for(const[o,u]of t.entries())s(u,o,n);else t!=null&&s(r,t,n);return this}get(t,r){if(t=Z(t),t){const n=l.findKey(this,t);if(n){const i=this[n];if(!r)return i;if(r===!0)return sn(i);if(l.isFunction(r))return r.call(this,i,n);if(l.isRegExp(r))return r.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=Z(t),t){const n=l.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||xe(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let i=!1;function s(a){if(a=Z(a),a){const o=l.findKey(n,a);o&&(!r||xe(n,n[o],o,r))&&(delete n[o],i=!0)}}return l.isArray(t)?t.forEach(s):s(t),i}clear(t){const r=Object.keys(this);let n=r.length,i=!1;for(;n--;){const s=r[n];(!t||xe(this,this[s],s,t,!0))&&(delete this[s],i=!0)}return i}normalize(t){const r=this,n={};return l.forEach(this,(i,s)=>{const a=l.findKey(n,s);if(a){r[a]=ie(i),delete r[s];return}const o=t?on(s):String(s).trim();o!==s&&delete r[s],r[o]=ie(i),n[o]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return l.forEach(this,(n,i)=>{n!=null&&n!==!1&&(r[i]=t&&l.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(i=>n.set(i)),n}static accessor(t){const n=(this[Qe]=this[Qe]={accessors:{}}).accessors,i=this.prototype;function s(a){const o=Z(a);n[o]||(ln(i,a),n[o]=!0)}return l.isArray(t)?t.forEach(s):s(t),this}};A.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);l.reduceDescriptors(A.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});l.freezeMethods(A);function Te(e,t){const r=this||re,n=t||r,i=A.from(n.headers);let s=n.data;return l.forEach(e,function(o){s=o.call(r,s,i.normalize(),t?t.status:void 0)}),i.normalize(),s}function Et(e){return!!(e&&e.__CANCEL__)}function X(e,t,r){g.call(this,e==null?"canceled":e,g.ERR_CANCELED,t,r),this.name="CanceledError"}l.inherits(X,g,{__CANCEL__:!0});function xt(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new g("Request failed with status code "+r.status,[g.ERR_BAD_REQUEST,g.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function cn(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function un(e,t){e=e||10;const r=new Array(e),n=new Array(e);let i=0,s=0,a;return t=t!==void 0?t:1e3,function(u){const c=Date.now(),f=n[s];a||(a=c),r[i]=u,n[i]=c;let d=s,w=0;for(;d!==i;)w+=r[d++],d=d%e;if(i=(i+1)%e,i===s&&(s=(s+1)%e),c-a<t)return;const y=f&&c-f;return y?Math.round(w*1e3/y):void 0}}function fn(e,t){let r=0,n=1e3/t,i,s;const a=(c,f=Date.now())=>{r=f,i=null,s&&(clearTimeout(s),s=null),e.apply(null,c)};return[(...c)=>{const f=Date.now(),d=f-r;d>=n?a(c,f):(i=c,s||(s=setTimeout(()=>{s=null,a(i)},n-d)))},()=>i&&a(i)]}const se=(e,t,r=3)=>{let n=0;const i=un(50,250);return fn(s=>{const a=s.loaded,o=s.lengthComputable?s.total:void 0,u=a-n,c=i(u),f=a<=o;n=a;const d={loaded:a,total:o,progress:o?a/o:void 0,bytes:u,rate:c||void 0,estimated:c&&o&&f?(o-a)/c:void 0,event:s,lengthComputable:o!=null,[t?"download":"upload"]:!0};e(d)},r)},je=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},Ze=e=>(...t)=>l.asap(()=>e(...t)),dn=R.hasStandardBrowserEnv?function(){const t=R.navigator&&/(msie|trident)/i.test(R.navigator.userAgent),r=document.createElement("a");let n;function i(s){let a=s;return t&&(r.setAttribute("href",a),a=r.href),r.setAttribute("href",a),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:r.pathname.charAt(0)==="/"?r.pathname:"/"+r.pathname}}return n=i(window.location.href),function(a){const o=l.isString(a)?i(a):a;return o.protocol===n.protocol&&o.host===n.host}}():function(){return function(){return!0}}(),pn=R.hasStandardBrowserEnv?{write(e,t,r,n,i,s){const a=[e+"="+encodeURIComponent(t)];l.isNumber(r)&&a.push("expires="+new Date(r).toGMTString()),l.isString(n)&&a.push("path="+n),l.isString(i)&&a.push("domain="+i),s===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function hn(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function mn(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Tt(e,t){return e&&!hn(t)?mn(e,t):t}const Ye=e=>e instanceof A?D({},e):e;function G(e,t){t=t||{};const r={};function n(c,f,d){return l.isPlainObject(c)&&l.isPlainObject(f)?l.merge.call({caseless:d},c,f):l.isPlainObject(f)?l.merge({},f):l.isArray(f)?f.slice():f}function i(c,f,d){if(l.isUndefined(f)){if(!l.isUndefined(c))return n(void 0,c,d)}else return n(c,f,d)}function s(c,f){if(!l.isUndefined(f))return n(void 0,f)}function a(c,f){if(l.isUndefined(f)){if(!l.isUndefined(c))return n(void 0,c)}else return n(void 0,f)}function o(c,f,d){if(d in t)return n(c,f);if(d in e)return n(void 0,c)}const u={url:s,method:s,data:s,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:o,headers:(c,f)=>i(Ye(c),Ye(f),!0)};return l.forEach(Object.keys(Object.assign({},e,t)),function(f){const d=u[f]||i,w=d(e[f],t[f],f);l.isUndefined(w)&&d!==o||(r[f]=w)}),r}const vt=e=>{const t=G({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:i,xsrfCookieName:s,headers:a,auth:o}=t;t.headers=a=A.from(a),t.url=Ct(Tt(t.baseURL,t.url),e.params,e.paramsSerializer),o&&a.set("Authorization","Basic "+btoa((o.username||"")+":"+(o.password?unescape(encodeURIComponent(o.password)):"")));let u;if(l.isFormData(r)){if(R.hasStandardBrowserEnv||R.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((u=a.getContentType())!==!1){const[c,...f]=u?u.split(";").map(d=>d.trim()).filter(Boolean):[];a.setContentType([c||"multipart/form-data",...f].join("; "))}}if(R.hasStandardBrowserEnv&&(n&&l.isFunction(n)&&(n=n(t)),n||n!==!1&&dn(t.url))){const c=i&&s&&pn.read(s);c&&a.set(i,c)}return t},yn=typeof XMLHttpRequest!="undefined",gn=yn&&function(e){return new Promise(function(r,n){const i=vt(e);let s=i.data;const a=A.from(i.headers).normalize();let{responseType:o,onUploadProgress:u,onDownloadProgress:c}=i,f,d,w,y,p;function m(){y&&y(),p&&p(),i.cancelToken&&i.cancelToken.unsubscribe(f),i.signal&&i.signal.removeEventListener("abort",f)}let h=new XMLHttpRequest;h.open(i.method.toUpperCase(),i.url,!0),h.timeout=i.timeout;function b(){if(!h)return;const x=A.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),E={data:!o||o==="text"||o==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:x,config:e,request:h};xt(function(T){r(T),m()},function(T){n(T),m()},E),h=null}"onloadend"in h?h.onloadend=b:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(b)},h.onabort=function(){h&&(n(new g("Request aborted",g.ECONNABORTED,e,h)),h=null)},h.onerror=function(){n(new g("Network Error",g.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let O=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const E=i.transitional||bt;i.timeoutErrorMessage&&(O=i.timeoutErrorMessage),n(new g(O,E.clarifyTimeoutError?g.ETIMEDOUT:g.ECONNABORTED,e,h)),h=null},s===void 0&&a.setContentType(null),"setRequestHeader"in h&&l.forEach(a.toJSON(),function(O,E){h.setRequestHeader(E,O)}),l.isUndefined(i.withCredentials)||(h.withCredentials=!!i.withCredentials),o&&o!=="json"&&(h.responseType=i.responseType),c&&([w,p]=se(c,!0),h.addEventListener("progress",w)),u&&h.upload&&([d,y]=se(u),h.upload.addEventListener("progress",d),h.upload.addEventListener("loadend",y)),(i.cancelToken||i.signal)&&(f=x=>{h&&(n(!x||x.type?new X(null,e,h):x),h.abort(),h=null)},i.cancelToken&&i.cancelToken.subscribe(f),i.signal&&(i.signal.aborted?f():i.signal.addEventListener("abort",f)));const C=cn(i.url);if(C&&R.protocols.indexOf(C)===-1){n(new g("Unsupported protocol "+C+":",g.ERR_BAD_REQUEST,e));return}h.send(s||null)})},wn=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,i;const s=function(c){if(!i){i=!0,o();const f=c instanceof Error?c:this.reason;n.abort(f instanceof g?f:new X(f instanceof Error?f.message:f))}};let a=t&&setTimeout(()=>{a=null,s(new g(`timeout ${t} of ms exceeded`,g.ETIMEDOUT))},t);const o=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(s):c.removeEventListener("abort",s)}),e=null)};e.forEach(c=>c.addEventListener("abort",s));const{signal:u}=n;return u.unsubscribe=()=>l.asap(o),u}},Cn=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let n=0,i;for(;n<r;)i=n+t,yield e.slice(n,i),n=i},bn=function(e,t){return ge(this,null,function*(){try{for(var r=ze(Sn(e)),n,i,s;n=!(i=yield new H(r.next())).done;n=!1){const a=i.value;yield*we(Cn(a,t))}}catch(i){s=[i]}finally{try{n&&(i=r.return)&&(yield new H(i.call(r)))}finally{if(s)throw s[0]}}})},Sn=function(e){return ge(this,null,function*(){if(e[Symbol.asyncIterator]){yield*we(e);return}const t=e.getReader();try{for(;;){const{done:r,value:n}=yield new H(t.read());if(r)break;yield n}}finally{yield new H(t.cancel())}})},et=(e,t,r,n)=>{const i=bn(e,t);let s=0,a,o=c=>{a||(a=!0,n&&n(c))};return new ReadableStream({pull(c){return v(this,null,function*(){try{const{done:f,value:d}=yield i.next();if(f){o(),c.close();return}let w=d.byteLength;if(r){let y=s+=w;r(y)}c.enqueue(new Uint8Array(d))}catch(f){throw o(f),f}})},cancel(c){return o(c),i.return()}},{highWaterMark:2})},de=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Rt=de&&typeof ReadableStream=="function",En=de&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):e=>v(null,null,function*(){return new Uint8Array(yield new Response(e).arrayBuffer())})),At=(e,...t)=>{try{return!!e(...t)}catch(r){return!1}},xn=Rt&&At(()=>{let e=!1;const t=new Request(R.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),tt=64*1024,Me=Rt&&At(()=>l.isReadableStream(new Response("").body)),ae={stream:Me&&(e=>e.body)};de&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!ae[t]&&(ae[t]=l.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new g(`Response type '${t}' is not supported`,g.ERR_NOT_SUPPORT,n)})})})(new Response);const Tn=e=>v(null,null,function*(){if(e==null)return 0;if(l.isBlob(e))return e.size;if(l.isSpecCompliantForm(e))return(yield new Request(R.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(l.isArrayBufferView(e)||l.isArrayBuffer(e))return e.byteLength;if(l.isURLSearchParams(e)&&(e=e+""),l.isString(e))return(yield En(e)).byteLength}),vn=(e,t)=>v(null,null,function*(){const r=l.toFiniteNumber(e.getContentLength());return r==null?Tn(t):r}),Rn=de&&(e=>v(null,null,function*(){let{url:t,method:r,data:n,signal:i,cancelToken:s,timeout:a,onDownloadProgress:o,onUploadProgress:u,responseType:c,headers:f,withCredentials:d="same-origin",fetchOptions:w}=vt(e);c=c?(c+"").toLowerCase():"text";let y=wn([i,s&&s.toAbortSignal()],a),p;const m=y&&y.unsubscribe&&(()=>{y.unsubscribe()});let h;try{if(u&&xn&&r!=="get"&&r!=="head"&&(h=yield vn(f,n))!==0){let E=new Request(t,{method:"POST",body:n,duplex:"half"}),k;if(l.isFormData(n)&&(k=E.headers.get("content-type"))&&f.setContentType(k),E.body){const[T,_]=je(h,se(Ze(u)));n=et(E.body,tt,T,_)}}l.isString(d)||(d=d?"include":"omit");const b="credentials"in Request.prototype;p=new Request(t,ye(D({},w),{signal:y,method:r.toUpperCase(),headers:f.normalize().toJSON(),body:n,duplex:"half",credentials:b?d:void 0}));let C=yield fetch(p);const x=Me&&(c==="stream"||c==="response");if(Me&&(o||x&&m)){const E={};["status","statusText","headers"].forEach(I=>{E[I]=C[I]});const k=l.toFiniteNumber(C.headers.get("content-length")),[T,_]=o&&je(k,se(Ze(o),!0))||[];C=new Response(et(C.body,tt,T,()=>{_&&_(),m&&m()}),E)}c=c||"text";let O=yield ae[l.findKey(ae,c)||"text"](C,e);return!x&&m&&m(),yield new Promise((E,k)=>{xt(E,k,{data:O,headers:A.from(C.headers),status:C.status,statusText:C.statusText,config:e,request:p})})}catch(b){throw m&&m(),b&&b.name==="TypeError"&&/fetch/i.test(b.message)?Object.assign(new g("Network Error",g.ERR_NETWORK,e,p),{cause:b.cause||b}):g.from(b,b&&b.code,e,p)}})),Le={http:Hr,xhr:gn,fetch:Rn};l.forEach(Le,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(r){}Object.defineProperty(e,"adapterName",{value:t})}});const rt=e=>`- ${e}`,An=e=>l.isFunction(e)||e===null||e===!1,Ot={getAdapter:e=>{e=l.isArray(e)?e:[e];const{length:t}=e;let r,n;const i={};for(let s=0;s<t;s++){r=e[s];let a;if(n=r,!An(r)&&(n=Le[(a=String(r)).toLowerCase()],n===void 0))throw new g(`Unknown adapter '${a}'`);if(n)break;i[a||"#"+s]=n}if(!n){const s=Object.entries(i).map(([o,u])=>`adapter ${o} `+(u===!1?"is not supported by the environment":"is not available in the build"));let a=t?s.length>1?`since :
`+s.map(rt).join(`
`):" "+rt(s[0]):"as no adapter specified";throw new g("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return n},adapters:Le};function ve(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new X(null,e)}function nt(e){return ve(e),e.headers=A.from(e.headers),e.data=Te.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Ot.getAdapter(e.adapter||re.adapter)(e).then(function(n){return ve(e),n.data=Te.call(e,e.transformResponse,n),n.headers=A.from(n.headers),n},function(n){return Et(n)||(ve(e),n&&n.response&&(n.response.data=Te.call(e,e.transformResponse,n.response),n.response.headers=A.from(n.response.headers))),Promise.reject(n)})}const _t="1.7.7",Be={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Be[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const it={};Be.transitional=function(t,r,n){function i(s,a){return"[Axios v"+_t+"] Transitional option '"+s+"'"+a+(n?". "+n:"")}return(s,a,o)=>{if(t===!1)throw new g(i(a," has been removed"+(r?" in "+r:"")),g.ERR_DEPRECATED);return r&&!it[a]&&(it[a]=!0,console.warn(i(a," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(s,a,o):!0}};function On(e,t,r){if(typeof e!="object")throw new g("options must be an object",g.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let i=n.length;for(;i-- >0;){const s=n[i],a=t[s];if(a){const o=e[s],u=o===void 0||a(o,s,e);if(u!==!0)throw new g("option "+s+" must be "+u,g.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new g("Unknown option "+s,g.ERR_BAD_OPTION)}}const ke={assertOptions:On,validators:Be},F=ke.validators;let $=class{constructor(t){this.defaults=t,this.interceptors={request:new Ke,response:new Ke}}request(t,r){return v(this,null,function*(){try{return yield this._request(t,r)}catch(n){if(n instanceof Error){let i;Error.captureStackTrace?Error.captureStackTrace(i={}):i=new Error;const s=i.stack?i.stack.replace(/^.+\n/,""):"";try{n.stack?s&&!String(n.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+s):n.stack=s}catch(a){}}throw n}})}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=G(this.defaults,r);const{transitional:n,paramsSerializer:i,headers:s}=r;n!==void 0&&ke.assertOptions(n,{silentJSONParsing:F.transitional(F.boolean),forcedJSONParsing:F.transitional(F.boolean),clarifyTimeoutError:F.transitional(F.boolean)},!1),i!=null&&(l.isFunction(i)?r.paramsSerializer={serialize:i}:ke.assertOptions(i,{encode:F.function,serialize:F.function},!0)),r.method=(r.method||this.defaults.method||"get").toLowerCase();let a=s&&l.merge(s.common,s[r.method]);s&&l.forEach(["delete","get","head","post","put","patch","common"],p=>{delete s[p]}),r.headers=A.concat(a,s);const o=[];let u=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(r)===!1||(u=u&&m.synchronous,o.unshift(m.fulfilled,m.rejected))});const c=[];this.interceptors.response.forEach(function(m){c.push(m.fulfilled,m.rejected)});let f,d=0,w;if(!u){const p=[nt.bind(this),void 0];for(p.unshift.apply(p,o),p.push.apply(p,c),w=p.length,f=Promise.resolve(r);d<w;)f=f.then(p[d++],p[d++]);return f}w=o.length;let y=r;for(d=0;d<w;){const p=o[d++],m=o[d++];try{y=p(y)}catch(h){m.call(this,h);break}}try{f=nt.call(this,y)}catch(p){return Promise.reject(p)}for(d=0,w=c.length;d<w;)f=f.then(c[d++],c[d++]);return f}getUri(t){t=G(this.defaults,t);const r=Tt(t.baseURL,t.url);return Ct(r,t.params,t.paramsSerializer)}};l.forEach(["delete","get","head","options"],function(t){$.prototype[t]=function(r,n){return this.request(G(n||{},{method:t,url:r,data:(n||{}).data}))}});l.forEach(["post","put","patch"],function(t){function r(n){return function(s,a,o){return this.request(G(o||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:s,data:a}))}}$.prototype[t]=r(),$.prototype[t+"Form"]=r(!0)});let _n=class Mt{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(s){r=s});const n=this;this.promise.then(i=>{if(!n._listeners)return;let s=n._listeners.length;for(;s-- >0;)n._listeners[s](i);n._listeners=null}),this.promise.then=i=>{let s;const a=new Promise(o=>{n.subscribe(o),s=o}).then(i);return a.cancel=function(){n.unsubscribe(s)},a},t(function(s,a,o){n.reason||(n.reason=new X(s,a,o),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new Mt(function(i){t=i}),cancel:t}}};function Mn(e){return function(r){return e.apply(null,r)}}function Ln(e){return l.isObject(e)&&e.isAxiosError===!0}const Pe={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Pe).forEach(([e,t])=>{Pe[t]=e});function Lt(e){const t=new $(e),r=ot($.prototype.request,t);return l.extend(r,$.prototype,t,{allOwnKeys:!0}),l.extend(r,t,null,{allOwnKeys:!0}),r.create=function(i){return Lt(G(e,i))},r}const S=Lt(re);S.Axios=$;S.CanceledError=X;S.CancelToken=_n;S.isCancel=Et;S.VERSION=_t;S.toFormData=fe;S.AxiosError=g;S.Cancel=S.CanceledError;S.all=function(t){return Promise.all(t)};S.spread=Mn;S.isAxiosError=Ln;S.mergeConfig=G;S.AxiosHeaders=A;S.formToJSON=e=>St(l.isHTMLForm(e)?new FormData(e):e);S.getAdapter=Ot.getAdapter;S.HttpStatusCode=Pe;S.default=S;const{Axios:ai,AxiosError:oi,CanceledError:li,isCancel:ci,CancelToken:ui,VERSION:fi,all:di,Cancel:pi,isAxiosError:hi,spread:mi,toFormData:yi,AxiosHeaders:gi,HttpStatusCode:wi,formToJSON:Ci,getAdapter:bi,mergeConfig:Si}=S;function kn(e){return v(this,null,function*(){const t=yield Jt.get("/webApp/appBaseMap/getAppBaseMap",{});if(!t||t.length===0)return null;const r=`${Math.random()}`,n={showKey:"",layers:[{fid:0,id:r,title:"底图",key:"baseMap"}]};return t.forEach(i=>{const s=`baseMap_${i.mapName}`;i.isDefault&&(n.showKey=s),`${i.mapUrl}`.toLocaleLowerCase()=="http://www.virtualearth.net"?n.layers.push({fid:r,id:s,title:i.mapName,key:s,data:{},type:"bing"}):`${i.mapUrl}`.toLocaleLowerCase()=="http://www.cesium.com"?n.layers.push({fid:r,id:s,title:i.mapName,key:s,data:{provider:""},type:"terrain"}):n.layers.push({fid:r,id:s,title:i.mapName,key:s,data:{url:i.mapUrl},type:i.mapType})}),n})}function Ei(e,t,r){t/=100,r/=100;const n=(1-Math.abs(2*r-1))*t,i=n*(1-Math.abs(e/60%2-1)),s=r-n/2;let a=0,o=0,u=0;return e>=0&&e<60?(a=n,o=i,u=0):e>=60&&e<120?(a=i,o=n,u=0):e>=120&&e<180?(a=0,o=n,u=i):e>=180&&e<240?(a=0,o=i,u=n):e>=240&&e<300?(a=i,o=0,u=n):(a=n,o=0,u=i),a=Math.round((a+s)*255),o=Math.round((o+s)*255),u=Math.round((u+s)*255),"#"+((1<<24)+(a<<16)+(o<<8)+u).toString(16).slice(1).toUpperCase()}function xi(e){const t=e.split(" "),r=parseInt(t[0]),n=parseInt(t[1]),i=parseInt(t[2]);return{h1:r,s1:n,l1:i}}function Pn(e,t,r=""){if(e===null)return;let n={".md":"text/markdown",".json":"application/json",".txt":"text/plain;charset=utf-8"};if(e instanceof Blob||(e=new Blob([e],{type:n[r||".txt"]||n[".txt"]})),window.navigator.msSaveOrOpenBlob)navigator.msSaveBlob(e,t);else{const i=document.createElement("a"),s=document.querySelector("body");i.href=window.URL.createObjectURL(e),i.download=t,i.style.display="none",s.appendChild(i),i.click(),s.removeChild(i),window.URL.revokeObjectURL(i.href)}}function st(...e){if(!e.length)return null;if(e.length==1)return e[0];let t={};for(let n=0;n<e.length;n++){let i=e[n];r(t,i)}function r(n,i){for(let s in i)n[s]=n[s]&&n[s].toString()==="[object Object]"?r(n[s],i[s]):n[s]=i[s];return n}return t}function Ti(e,t){t=D({keyName:"key",idName:"id",fatherName:"fid",titleName:"title",childName:"children",build:null},t),t.build=t.build||(i=>({title:i[t.titleName],key:i[t.keyName],id:i[t.idName],isLeaf:!0,data:i}));let r=[];e instanceof Array&&(r=e.filter(i=>!i[t.fatherName]).map(i=>t.build(i)));let n=i=>{i.forEach(s=>{let a=e.filter(o=>o[t.fatherName]==s.id).map(o=>t.build(o));a.length>0&&(s[t.childName]=a,n(a))})};return n(r),r}function vi(e,t=!0){let r=[];e instanceof Array||(e=[e]);let n=i=>{i.forEach(s=>{r.push(s.key),s.children&&n(s.children)})};return n(e),r}function Ri(e,t){var r;if(e===t)return!0;if(typeof e=="function"||typeof e=="string"||e instanceof String)return e.toString()===t.toString();if(e instanceof Number||typeof e=="number")return t instanceof Number||typeof t=="number"?e.valueOf()===t.valueOf():!1;if(typeof e!=typeof t||t===null||typeof t=="undefined")return!1;function n(i){var s={};return typeof i!="object"?i:(Object.keys(i).sort().forEach(function(a){s[a]=n(i[a])}),s)}if(typeof e=="object"){if(Array.isArray(e))return JSON.stringify(e)===JSON.stringify(t);for(r in e){if(typeof e[r]!=typeof t[r]||e[r]===null!=(t[r]===null))return!1;switch(typeof e[r]){case"undefined":if(typeof t[r]!="undefined")return!1;break;case"object":if(e[r]!==null&&t[r]!==null&&(e[r].constructor.toString()!==t[r].constructor.toString()||!equalsObject(e[r],t[r])))return!1;break;case"function":if(e[r].toString()!==t[r].toString())return!1;break;default:if(e[r]!==t[r])return!1}}}return JSON.stringify(n(e))===JSON.stringify(n(t))}const Ai={saveAs:Pn};function pe(e){return typeof e=="undefined"||!e||/^\s*(null|0|undefined|false|no)\s*$/i.test(e)||e instanceof Array&&!e.length||typeof e=="object"&&(!Object.keys(e).length||"length"in e&&!e.length)}function kt(e){return pe(e)?!1:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(e)}function Dn(e){if(kt(e)){const t=e.slice(0,3),r=e.slice(7,11);return t+"****"+r}else return""}function Nn(e,t=0,r=0){if(!pe(e))if(t==0){var n="***************",r=e.slice(e.length-r,e.length);return n+" "+r}else{var i=e.slice(0,t),r=e.slice(e.length-r,e.length),s=" ******** ";return i+" "+s+" "+r}return""}function In(e){if(pe(e)||typeof e!="string")return!1;var t=/^\d+(\.\d+)?$/,r=t.exec(e);return!r}const Fn={empty:pe,isPhoneNumber:kt,hiddenPhone:Dn,hiddenCarNo:Nn,isMoney:In};let at=0;function Bn(e=3e3){let t=+new Date;return t-at<e?!1:(at=t,!0)}let J=[],Un={validate:Fn,localStorage:{setItem:(e,t)=>{typeof t=="object"&&(t=JSON.stringify(t)),window.localStorage.setItem(e,t)},getItem:e=>{const t=window.localStorage.getItem(e);try{return t?JSON.parse(t):""}catch(r){return t}},removeItem:e=>{window.localStorage.removeItem(e)}},one:(e,t)=>{let r=new Date().getTime();if(console.log(J,J.findIndex(i=>i==t)),J.findIndex(i=>i==t)>-1)return;J.push(t),e();let n=J.findIndex(i=>i==t);n>-1&&J.splice(n,1),console.log(new Date().timeDiff(r))},clickThrottle:Bn},Pt={utils:Un,global:{}};const Hn={props:{title:{type:String,default:""},mask:{type:Boolean,default:!1},maskClosable:{type:Boolean,default:!1},hasHeader:{type:Boolean,default:!0},hasFooter:{type:Boolean,default:!1},moverect:{type:Object,default:{left:0,top:0,bottom:0,right:0}},close:{type:Function}},data(){return{isMinimize:!1}},updated(){console.log(this.$refs.box),this.left!=null&&(this.$refs.box.style.left=this.left,this.$refs.box.style.top=this.top,this.$refs.box.style.right="",this.$refs.box.style.transform="")},mounted(){this.$nextTick(()=>{this.cleanTransform(this.$refs.box)})},methods:{cleanTransform(e){if(e.style.transform){let t=window.getComputedStyle(e).transform||"";t=t.replaceAll(/ |matrix\(|\)/gi,"").split(/\(|,/),e.style.transform="",e.style.left=`${e.offsetLeft+Number(t[4])}px`,e.style.top=`${e.offsetTop+Number(t[5])}px`}},composedPath(e){if(e.path)return e.path;let t=e.target;for(e.path=[];t.parentNode!==null;)e.path.push(t),t=t.parentNode;return e.path.push(document,window),e.path},headerMouseDown(e){const t=this,r=e||window.event;let n=this.composedPath(e);n=n[1];let i=r.clientX,s=r.clientY;n.style.left=`${n.offsetLeft}px`,n.style.top=`${n.offsetTop}px`,this.cleanTransform(n),n.style.right=null,n.style.bottom=null;const a=window.innerWidth,o=window.innerHeight,u=f=>{const d=f||window.event,w=d.clientX,y=d.clientY;let p=n.offsetLeft+w-i,m=n.offsetTop+y-s;p=Math.max(p,this.moverect.left),p=Math.min(p,a-this.moverect.right-n.offsetWidth),m=Math.max(m,this.moverect.top),m=Math.min(m,o-this.moverect.bottom-n.offsetHeight),t.left=n.style.left=`${p}px`,t.top=n.style.top=`${m}px`,i=w,s=y};document.addEventListener("mousemove",u);const c=f=>{document.removeEventListener("mousemove",u),document.removeEventListener("mouseup",c)};document.addEventListener("mouseup",c)}}},qn={class:"bg-white dark:bg-black"},zn=["title"],$n={class:"content"},Gn={key:1,class:"footer"};function Wn(e,t,r,n,i,s){return Q(),K("view",{ref:"box",class:er([{minimize:i.isMinimize},"box-container"]),style:{position:"fixed",display:"flex","flex-direction":"column",width:"320px",height:"auto","z-index":"999"}},[j("view",qn,[be(j("view",{onClick:t[0]||(t[0]=a=>r.maskClosable&&r.close())},null,512),[[Ce,r.mask]]),r.hasHeader?(Q(),K("view",{key:0,ref:"boxDiv",title:r.title,class:"headerBox",style:{cursor:"move"},onMousedown:t[7]||(t[7]=q((...a)=>s.headerMouseDown&&s.headerMouseDown(...a),["stop"]))},[Se(e.$slots,"header",{},()=>[be(j("p",{class:"headerText"},Zt(r.title),513),[[Ce,r.title]]),i.isMinimize?(Q(),K("i",{key:1,class:"headerIcon",title:"还原",onClick:t[3]||(t[3]=q(a=>i.isMinimize=!i.isMinimize,["stop"])),onMousedown:t[4]||(t[4]=q(()=>{},["stop"]))},"-",32)):(Q(),K("i",{key:0,class:"headerIcon",style:{transform:"rotate(0deg)"},title:"最小化",onClick:t[1]||(t[1]=q(a=>i.isMinimize=!i.isMinimize,["stop"])),onMousedown:t[2]||(t[2]=q(()=>{},["stop"]))},"-",32)),j("i",{class:"headerIcon",title:"关闭",onClick:t[5]||(t[5]=q((...a)=>r.close&&r.close(...a),["stop"])),onMousedown:t[6]||(t[6]=q(()=>{},["stop"]))},"+",32)],!0)],40,zn)):Ge("",!0),B(Xt,{mode:"out-in",name:"zoom"},{default:Yt(()=>[be(j("div",$n,[Se(e.$slots,"default",{},void 0,!0)],512),[[Ce,!i.isMinimize]])]),_:3}),r.hasFooter?(Q(),K("div",Gn,[Se(e.$slots,"footer",{},void 0,!0)])):Ge("",!0)])],2)}const Jn=Vt(Hn,[["render",Wn],["__scopeId","data-v-53c1811c"]]),M=[],De={open(e,t,r,n){const i={moverect:{left:0,top:64,right:50,bottom:0},style:{left:"unset",right:"100px",top:"144px"},close:()=>{if(e.beforeClose&&e.beforeClose())return!0;const o=M.find(u=>u.el==r);o&&(o.inst=null,this.close(r))}};typeof t=="string"?t=[B("div",{style:{padding:"10px"}},t)]:Array.isArray(t)||(t=[B(t)]),typeof n=="string"&&(n=document.getElementById(n)),n instanceof HTMLElement||(n=document.body);let s="";r instanceof HTMLElement?s=r.id:typeof r=="string"&&(s=r,r=document.getElementById(r)),r&&r.firstChild&&(e=st({style:{left:r.firstChild.style.left,top:r.firstChild.style.top,transform:r.firstChild.style.transform}},e));const a=B(Jn,st(i,e),t);if(r){const o=M.findIndex(u=>u.el==r);o!==-1&&(r.remove(),M[o].inst=null,M.splice(o,1))}return r=document.createElement("div"),s&&(r.id=s),Kt(a,r),n.append(r),M.push({el:r,inst:a}),r},alert(e,t){const r=window.innerHeight,i={mask:!0,maskClosable:!0,hasHeader:!1,style:{left:`${window.innerWidth/2-150}px`,top:`${r/2-80}px`,width:"300px"}};return this.open(i,e,t)},info(e,t,r){const n=window.innerHeight,i=window.innerWidth,s=D({mask:!0,style:{left:`${i/2-150}px`,top:`${n/2-80}px`,width:"300px"}},typeof e=="string"?{title:e}:e);return this.open(s,t,r)},confirm(e,t,r,n){const i=window.innerHeight,s=window.innerWidth,a=D({mask:!0,style:{left:`${s/2-150}px`,top:`${i/2-80}px`,width:"300px"}},typeof e=="string"?{title:e}:e),o=this.open(a,[B("div",{style:{padding:"10px"}},t),B("div",{style:{"text-align":"center","border-top":"1px solid",padding:"5px"}},[B("button",{style:{padding:"2px 20px",margin:"5px"},onclick:()=>{n&&typeof n=="function"&&n(),De.close(o)}},"取消"),B("button",{style:{padding:"2px 20px",margin:"5px"},onclick:()=>{r&&typeof r=="function"&&r(),De.close(o)}},"确定")])])},close(e){const t=M.findIndex(r=>r.el==e);t!==-1&&(console.log(M[t].inst),M[t].inst?M[t].inst.props.close(e):(e.remove(),M[t].inst=null,M.splice(t,1)))},closeAll(){Pt.utils.one(()=>{M.map(t=>t).forEach(t=>{this.close(t.el)})},"950DC476-6710-929A-7013-79A4A39FC497")},show(e){if(e){const t=M.find(r=>r.el===e);if(!t)return;t.inst&&t.inst.component&&t.inst.component.data.isMinimize&&(t.inst.component.data.isMinimize=!1)}e&&(e.style.display="unset")},hide(e){e&&(e.style.display="none")}};function Vn(){const e=this;e.fromFile=function(n,i){const s=window.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP");s.open("GET",n,!1),s.send(null);const a=r(t(s.responseXML));return i===void 0?JSON.parse(a):a},e.fromStr=function(n,i){if(window.DOMParser)var s=new DOMParser().parseFromString(n,"text/xml");else{var s=new ActiveXObject("Microsoft.XMLDOM");s.async="false"}const a=r(t(s));return i===void 0?JSON.parse(a):a};var t=function(n){let i={};if(n.nodeType==1){if(n.attributes.length>0){i["@attributes"]={};for(let s=0;s<n.attributes.length;s++){const a=n.attributes.item(s);i["@attributes"][a.nodeName]=a.value}}}else n.nodeType==3&&(i=n.nodeValue);if(n.hasChildNodes())for(let s=0;s<n.childNodes.length;s++){const a=n.childNodes.item(s),o=a.nodeName;if(i[o]===void 0)i[o]=t(a);else{if(i[o].push===void 0){const u=i[o];i[o]=[],i[o].push(u)}i[o].push(t(a))}}return i},r=function(n){const i=JSON.stringify(n,void 0,2).replaceAll(/(\\t|\\r|\\n)/g,"").replaceAll(/"",\s+"",*/g,"").replaceAll(/(\n\s*\n)/g,"").replaceAll(/\s{2,}"",?/g,"").replaceAll(/"\s+",?/g,"");return i.includes('"parsererror": {')?"Invalid XML format":i}}let Re=[{fid:0,id:99,title:"底图",key:"imagery"},{fid:99,id:323391,title:"信息学院1223",key:"xzqh1221",data:{url:"http://fzzt.fzjhdn.com:6080/arcgis/rest/services/MyMapService/MapServer",layers:0},type:"arcgis:geojson"},{fid:99,id:20,title:"天地图标记",key:"tianditu4",data:{url:"http://t0.tianditu.com/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&tk=9189606639aa352dc18b332505a47597"},type:"wmts"},{fid:99,id:18,title:"天地图地形",key:"tianditu3",data:{url:"http://t0.tianditu.gov.cn/ter_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=ter&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=9189606639aa352dc18b332505a47597"},type:"wmts"},{fid:99,id:17,title:"天地图矢量",key:"tianditu2",data:{url:"http://t0.tianditu.gov.cn/vec_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=9189606639aa352dc18b332505a47597"},type:"wmts"},{fid:99,id:16,title:"天地图影像",key:"tianditu1",data:{url:"http://t0.tianditu.com/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=9189606639aa352dc18b332505a47597"},type:"wmts"},{fid:99,id:39,title:"福州地形",key:"fzdx",data:{url:"http://fzzt.fzjhdn.com:19005/terrain/srgznFSa"},type:"terrain"},{fid:99,id:101,title:"全球地形",key:"terrain",data:{provider:""},type:"terrain"},{fid:99,id:100,title:"Bing影像",key:"bingmap",data:{},type:"bing"}];const Xn={隧道:[{name:"SDMC",cnName:"隧道名称",allowQuery:!0,allowDisplay:!0},{name:"SQS_Q",cnName:"城市",allowQuery:!0,allowDisplay:!0},{name:"XZDJ",cnName:"公路等级",allowQuery:!0,allowDisplay:!0},{name:"SZLDBH",cnName:"隧道编码",allowQuery:!0,allowDisplay:!0}]},Kn={esriFieldTypeDouble:"number",esriFieldTypeString:"string",esriFieldTypeOID:"number",esriFieldTypeInteger:"number",int:"number",number:"number",string:"string"};class Ue{constructor(t){t=t||{},this.fullTitle=t.fullTitle||"",this.title=t.title||"",this.key=t.key||"",this.type=t.type||"",this._data=t.data||null,this.isStaticLayer=t.isStaticLayer||!!this._data,this.refLayer=null,this.show=!1,this._fieldList=null,this._capabilities=null,this.isBaseLayer=t.isBaseLayer||!1}clone(){const t=new Ue({fullTitle:this.fullTitle,title:this.title,key:this.key,type:this.type,data:JSON.parse(JSON.stringify(this.data)),isStaticLayer:this.isStaticLayer});return t._fieldList=this._fieldList,t._capabilities=this._capabilities,t}getCapabilities(t=!0){return v(this,null,function*(){if(this.isStaticLayer)return null;const r=N.router.currentRoute.value.meta.func;if(t&&r){const i=N.funs[r];if(i)yield(void 0)({funcId:i.id,funcPath:i.fullTitle,layerId:this.key,layerPath:this.fullTitle});else return yield N.message("该功能未授权"),N.router.replace({name:"login"})}if(this._capabilities)return this._capabilities;const n=yield this.getData();if(n){if(this.type.toLocaleLowerCase().startsWith("arcgis:mapserver")){const i=yield S.get(`${n.url.trim("/")}/${n.layers}?f=pjson&token=${n.token||""}`);if(i&&i.data.error)return yield this.getData(!0),yield getCapabilities(!1);i&&(this._capabilities={useStandardizedQueries:i.data.useStandardizedQueries||!1,supportsPagination:i.data.supportsAdvancedQueries&&i.data.advancedQueryCapabilities.supportsPagination||!1,maxRecordCount:i.data.maxRecordCount||0,supportsStatistics:i.data.supportsStatistics||!1,supportsAdvancedQueries:i.data.supportsAdvancedQueries||!1,fields:(i.data.fields||[]).map(s=>({name:s.name,cnName:s.alias,allowQuery:s.type=="esriFieldTypeString",allowDisplay:s.type!="esriFieldTypeGeometry"})),extent:ye(D({},i.data.extent||i.data.fullExtent),{wkid:`EPSG:${(i.data.extent||i.data.fullExtent).spatialReference.wkid}`,wkt:(i.data.extent||i.data.fullExtent).spatialReference.wkt}),isRasterLayer:i.data.type=="Raster Layer",isFeatureLayer:i.data.type=="Feature Layer"})}else if(this.type.toLocaleLowerCase().startsWith("geoserver:wms")){let i=yield S.get(`${n.url.trim("/")}?service=wfs&version=2.0.0&request=DescribeFeatureType&typeNames=${n.layers}&outputFormat=application%2Fjson`),s=[];if(i&&i.data&&Array.isArray(i.data.featureTypes)&&i.data.featureTypes.length>0&&Array.isArray(i.data.featureTypes[0].properties)&&(s=i.data.featureTypes[0].properties.map(a=>({name:a.name,cnName:a.name,allowQuery:a.localType=="string",allowDisplay:a.name!="the_geom"}))),i=yield S.get(`${n.url.trim("/")}?request=getCapabilities`),i&&i.data){const a=new Vn().fromStr(i.data);console.log(a);const o=a.WMS_Capabilities.Capability.Layer.Layer,u=Array.isArray(o)?o.find(c=>c.Name["#text"]==this.data.layers):o;if(u){const c=u["@attributes"].queryable=="1";this._capabilities={useStandardizedQueries:c,supportsPagination:c,maxRecordCount:0,supportsStatistics:c,supportsAdvancedQueries:c,fields:s,extent:u.BoundingBox.map(f=>{const d=f["@attributes"];return{xmin:Number.parseFloat(d.minx),ymin:Number.parseFloat(d.miny),xmax:Number.parseFloat(d.maxx),ymax:Number.parseFloat(d.maxy),wkid:d.CRS}}).shift()}}console.log(u,this._capabilities)}}if(this._capabilities&&this._capabilities.extent){const i=this._capabilities.extent;console.log(i);const s=$e(this._capabilities.extent.wkt||this._capabilities.extent.wkid,"EPSG:4326",[i.xmin,i.ymin]),a=$e(this._capabilities.extent.wkt||this._capabilities.extent.wkid,"EPSG:4326",[i.xmax,i.ymax]);console.log(s,a),this.data.rectangle=Cesium.Rectangle.fromDegrees(s[0],s[1],a[0],a[1])}else this._capabilities={};return this._capabilities}})}getData(t=!1){return v(this,null,function*(){return this.isStaticLayer?this._data:this._data&&!t?this._data:this._data})}getFieldList(){return v(this,null,function*(){if(this.isBaseLayer)return[];if(this._fieldList)return this._fieldList;const t=yield(void 0)({layerId:this.key});return t&&t.length>0?(this._fieldList=t.map(r=>({name:r.attrName,cnName:r.alias,allowQuery:r.searchable,allowDisplay:r.visible,type:Kn[r.type]})),this._fieldList):(this._fieldList=Xn[this.title],this._fieldList||(yield this.getCapabilities(),this._capabilities&&(this._fieldList=this._capabilities.fields)),this._fieldList=this._fieldList||[],this._fieldList)})}get capabilities(){return this._capabilities}get data(){return this._data}get fieldList(){return this._fieldList||[]}}class Qn{constructor(t){this.router=t,this.userInfo={},this.appInfo=null,this._layersOrigin=[],this._funsOrigin=[],this.layers={},this.funs={},this.isReady=!1,this.extent=null}chkFuns(t){return!!this.funs[t]}getLayer(t){return this.layers[t]}goto(t){if(!t||!this.chkFuns(t))return;const r=this.router.getRoutes().find(n=>n.meta&&n.meta.func===t);r&&this.router.replace({name:r.name})}initLogin(t){return v(this,null,function*(){const r=yield kn();r&&(this.defaultShowLayerKey=r.showKey,Re=r.layers,this.layersOrigin=[]),this.isReady=!0})}addLayerDatas(t){return v(this,null,function*(){let r=Pt.global.GISLayers;t&&t.map(n=>{let i=null;this.layersOrigin.forEach(s=>{s.id===n.id&&(i=s)}),i||this.layersOrigin.push({fid:n.parentId=="-1"?0:n.parentId,id:n.id,title:n.name,key:n.id,type:n.type||"",data:{url:n.url,style:""}}),r.loadLayer([{type:n.type,key:n.id,title:n.name,show:!1,data:{url:n.url,style:""}}])})})}logout(){localStorage.removeItem("user/token"),localStorage.removeItem("user/account"),localStorage.removeItem("layer/checked"),localStorage.removeItem("layer/shapes"),this.router.replace({name:"login"})}message(t,r){return new Promise((n,i)=>{const s=r?t:"提示",a=r||t,o=window.innerHeight,c={mask:!0,style:{left:`${window.innerWidth/2-150}px`,top:`${o/2-80}px`,width:"300px"},title:s,beforeClose:()=>{n()}};De.open(c,a)})}get account(){return localStorage.getItem("user/account")}set account(t){t!=this.account&&(localStorage.removeItem("layer/checked"),localStorage.removeItem("layer/shapes")),localStorage.setItem("user/account",t||"")}get chkLayers(){console.log("checklayers");const t=JSON.parse(localStorage.getItem("layer/checked")||"[]");return console.log(t),this.defaultShowLayerKey&&t.filter(r=>r.startsWith("baseMap_")).length===0&&t.push(this.defaultShowLayerKey),console.log(t),t}set chkLayers(t){t&&typeof t=="string"&&(t=[t]),Array.isArray(t)||(t=[]),localStorage.setItem("layer/checked",JSON.stringify(t)),console.log(t)}get funsOrigin(){return this._funsOrigin}set funsOrigin(t){t=t||[],this._funsOrigin=t,this.funs={};const r=(n=0)=>{const i=t.find(s=>s.id==n);t.filter(s=>s.fid==n).forEach(s=>{i&&(s.fullTitle=`${i.fullTitle||i.title}.${s.title}`),r(s.id)})};r(),t.forEach(n=>{this.funs[n.fullTitle]=n})}get layersOrigin(){return this._layersOrigin}set layersOrigin(t){t=t||[],this.layers={};const r=(n=0)=>{const i=t.find(s=>s.id==n);t.filter(s=>s.fid==n).forEach(s=>{i&&(s.fullTitle=`${i.fullTitle||i.title}.${s.title}`),r(s.id)})};r(),Re.forEach(n=>n.isStaticLayer=!0),this._layersOrigin=[...t,...Re],this._layersOrigin.filter(n=>n.type).forEach(n=>{this.layers[n.key]=new Ue(n)})}get logined(){return!!localStorage.getItem("user/token")}get shapes(){return JSON.parse(localStorage.getItem("layer/shapes")||"[]")}set shapes(t){Array.isArray(t)||(t=[]),localStorage.setItem("layer/shapes",JSON.stringify(t))}get token(){return localStorage.getItem("user/token")}set token(t){localStorage.setItem("user/token",t||"")}}let N=null;function Oi(e){return N=N||new Qn(e),N}const Y={};class jn{constructor(t){this.viewer=t.viewer,this.init()}init(){var t=this.viewer;t.scene;var r=t.canvas;r.setAttribute("tabindex","0"),r.onclick=function(){r.focus()};var n={looking:!1,moveForward:!1,moveBackward:!1,moveUp:!1,moveDown:!1,moveLeft:!1,moveRight:!1};new Cesium.ScreenSpaceEventHandler(r);function i(s){switch(s){case 81:return"moveForward";case 69:return"moveBackward";case 87:return"moveUp";case 83:return"moveDown";case 68:return"moveRight";case 65:return"moveLeft";default:return}}r.addEventListener("keydown",function(s){var a=i(s.keyCode);typeof a!="undefined"&&(n[a]=!0)},!1),r.addEventListener("keyup",function(s){var a=i(s.keyCode);typeof a!="undefined"&&(n[a]=!1)},!1),t.clock.onTick.addEventListener(function(s){var a=t.camera,o=t.camera.positionCartographic.clone().height,u=o/100;n.moveForward&&a.moveForward(u),n.moveBackward&&a.moveBackward(u),n.moveUp&&a.moveUp(u),n.moveDown&&a.moveDown(u),n.moveLeft&&a.moveLeft(u),n.moveRight&&a.moveRight(u)})}}let U={getMapCenter(e){if(!e)return!1;let t=e.camera.pickEllipsoid(new Cesium.Cartesian2(e.canvas.clientWidth/2,e.canvas.clientHeight/2));return t?this.transformCartesianToWGS84(t):!1},getCameraPositionByMapCenter(e,t,r){for(var n=6378137,i=63567523142e-4,s=1/298.257223563,a=e.lng*1,o=e.lat*1,u=e.alt/Math.tan(r*Math.PI/180),c=t*(Math.PI/180),f=Math.sin(c),d=Math.cos(c),w=(1-s)*Math.tan(o*(Math.PI/180)),y=1/Math.sqrt(1+w*w),p=w*y,m=Math.atan2(w,d),h=y*f,b=1-h*h,C=b*(n*n-i*i)/(i*i),x=1+C/16384*(4096+C*(-768+C*(320-175*C))),O=C/1024*(256+C*(-128+C*(74-47*C))),E=u/(i*x),k=2*Math.PI;Math.abs(E-k)>1e-12;){var T=Math.cos(2*m+E),_=Math.sin(E),I=Math.cos(E),Dt=O*_*(T+O/4*(I*(-1+2*T*T)-O/6*T*(-3+4*_*_)*(-3+4*T*T)));k=E,E=u/(i*x)+Dt}var he=p*_-y*I*d,Nt=Math.atan2(p*I+y*_*d,(1-s)*Math.sqrt(h*h+he*he)),It=Math.atan2(_*f,y*I-p*_*d),me=s/16*b*(4+s*(4-3*b)),Ft=It-(1-me)*s*h*(E+me*_*(T+me*I*(-1+2*T*T))),Bt=Math.atan2(h,-he);console.log("rev",Bt);var Ut={lng:a+Ft*(180/Math.PI),lat:Nt*(180/Math.PI),alt:u};return Ut},getDefaultViewRectangle(){return Cesium.Camera.DEFAULT_VIEW_RECTANGLE},getDefaultViewCenter(){let e=this.getDefaultViewRectangle(),t=Cesium.Rectangle.center(e);return U.transformCartographicToWGS84(t)},computeExtent(e,t){let r=Cesium.Rectangle.fromCartesianArray(e);if(t){let n=Math.max(r.width,.001)/4*t,i=Math.max(r.height,.001)/4*t;return Cesium.Rectangle.fromRadians(r.west-n,r.south-i,r.east+n,r.north+i)}return r},transformWGS84ToCartesian(e,t){return e?Cesium.Cartesian3.fromDegrees(e.lng||e.lon,e.lat,e.alt=t||e.alt,Cesium.Ellipsoid.WGS84):Cesium.Cartesian3.ZERO},transformWGS84ArrayToCartesianArray(e,t){if(e){var r=this;return e?e.map(function(n){return r.transformWGS84ToCartesian(n,t)}):[]}},transformCartesianToWGS84(e){if(e){var t=Cesium.Ellipsoid.WGS84,r=t.cartesianToCartographic(e);return{lng:Cesium.Math.toDegrees(r.longitude),lat:Cesium.Math.toDegrees(r.latitude),alt:r.height}}},transformCartesianArrayToWGS84Array(e){var t=this;return e?e.map(function(r){return t.transformCartesianToWGS84(r)}):[]},transformWGS84ToCartographic(e){return e?Cesium.Cartographic.fromDegrees(e.lng||e.lon,e.lat,e.alt):Cesium.Cartographic.ZERO},transformCartographicToWGS84(e){return e?{lng:Cesium.Math.toDegrees(e.longitude),lat:Cesium.Math.toDegrees(e.latitude),alt:e.height}:{lng:0,lat:0,alt:0}},ClampToHeight(e,t){let r=Cesium.SceneTransforms.wgs84ToWindowCoordinates(e.scene,t);return U.getCatesian3FromPX(e,r)},getCatesian3FromPX(e,t){if(e&&t){var r=e.scene.drillPick(t),n=null,i=!1,s=!1,a=!1;for(let c in r){let f=r[c];if(f&&f.primitive instanceof Cesium.Cesium3DTileFeature||f&&f.primitive instanceof Cesium.Cesium3DTileset||f&&f.primitive instanceof Cesium.Model){i=!0;break}}if(i&&(e.scene.pick(t),n=e.scene.pickPosition(t),n)){let c=Cesium.Cartographic.fromCartesian(n);c.height<0&&(c.height=0);let f=Cesium.Math.toDegrees(c.longitude),d=Cesium.Math.toDegrees(c.latitude),w=c.height;n=this.transformWGS84ToCartesian({lng:f,lat:d,alt:w})}a=e.camera.positionCartographic.clone().height<0;let u=e.terrainProvider instanceof Cesium.EllipsoidTerrainProvider;if(!i&&!a&&!u){var o=e.scene.camera.getPickRay(t);if(!o)return null;n=e.scene.globe.pick(o,e.scene),s=!0}if((a||!i&&!s&&u)&&(n=e.scene.camera.pickEllipsoid(t,e.scene.globe.ellipsoid)),n){let c=this.transformCartesianToWGS84(n);return c.alt<0&&(n=this.transformWGS84ToCartesian(c,.1)),n}return!1}},getPositionDistance(e){let t=0;for(let r=0;r<e.length-1;r++){let n=this.transformWGS84ToCartographic(e[r]),i=this.transformWGS84ToCartographic(e[r+1]),s=new Cesium.EllipsoidGeodesic;s.setEndPoints(n,i);let a=s.surfaceDistance;a=Math.sqrt(Math.pow(a,2)+Math.pow(i.height-n.height,2)),t=t+a}return t.toFixed(3)},getPositionsArea(e){let t=0;if(e){let r=0,n=Cesium.Ellipsoid.WGS84;e.push(e[0]);for(let i=1;i<e.length;i++){let s=n.cartographicToCartesian(this.transformWGS84ToCartographic(e[i-1])),a=n.cartographicToCartesian(this.transformWGS84ToCartographic(e[i]));r+=s.x*a.y-a.x*s.y}t=Math.abs(r).toFixed(2)}return t},getPositionsAngle(e){let t=0;if(!e)return t;if(e.length==3){let r=this.getPositionDistance([e[0],e[1]]),n=this.getPositionDistance([e[1],e[2]]),i=this.getPositionDistance([e[2],e[0]]);t=Math.acos((Math.pow(r,2)+Math.pow(n,2)-Math.pow(i,2))/(2*r*n))}return t},getPolylineCenter(e){if(e&&e.length>0){let t=Math.floor((e.length-1)/2),r=Math.ceil((e.length-1)/2);return new Cesium.Cartesian3((e[t].x+e[r].x)/2,(e[t].y+e[r].y)/2,(e[t].z+e[r].z)/2)}return!1},getPolygonCenter(e){return e?e.length==1?e[0]:e.length==2?this.getPolylineCenter(e):Cesium.BoundingSphere.fromPoints(e).center:!1},getLengthText(e){return e>1e3?(e/1e3).toFixed(2)+" 公里":(e/1).toFixed(2)+" 米"},getAreaText(e){return e<1e6?Math.abs(e/1).toFixed(2)+" 平方米":Math.abs((e/1e6).toFixed(2))+" 平方公里"},getMapLevel(e){return e>48e6?0:e>24e6?1:e>12e6?2:e>6e6?3:e>3e6?4:e>15e5?5:e>75e4?6:e>375e3?7:e>187500?8:e>93750?9:e>46875?10:e>23437.5?11:e>11718.75?12:e>5859.38?13:e>2929.69?14:e>1464.84?15:e>732.42?16:e>366.21?17:18},entity2Json(e){console.log(e);let t={};if(!e)return t;let r=n=>n&&(n._value||n.getValue());return e.position&&(t.position=r(e.position)),e.point&&(t.point=(({color:n,pixelSize:i,heightReference:s})=>({color:r(n).toCssColorString(),pixelSize:r(i),heightReference:s?r(s):0}))(e.point)),e.polyline&&(t.polyline=(({clampToGround:n,material:i,positions:s,width:a})=>({clampToGround:r(n),material:r(i.color).toCssColorString(),positions:r(s),width:r(a)}))(e.polyline)),e.polygon&&(t.polygon=(({fill:n,hierarchy:i,material:s})=>({fill:r(n),hierarchy:r(i),material:r(s.color).toCssColorString()}))(e.polygon)),e.ellipse&&(t.ellipse=(({fill:n,semiMajorAxis:i,semiMinorAxis:s,material:a})=>({fill:r(n),semiMajorAxis:r(i),semiMinorAxis:r(s),material:r(a.color).toCssColorString()}))(e.ellipse)),e.label&&(t.label=(({fillColor:n,font:i,text:s})=>({fillColor:r(n).toCssColorString(),font:r(i),text:r(s)}))(e.label)),e.rectangle&&(t.rectangle=(({coordinates:n,fill:i,material:s})=>({coordinates:r(n),fill:r(i),material:r(s.color).toCssColorString()}))(e.rectangle)),e.billboard&&(t.billboard=(({image:n,scale:i})=>({image:r(n),scale:r(i)}))(e.billboard)),e.model&&(t.model=(({uri:n,scale:i})=>({uri:r(n),scale:r(i)}))(e.model)),e.name&&(t.name=e.name),e.properties&&(t.properties={},e.properties.propertyNames.forEach(n=>{t.properties[n]=r(e.properties[n])})),console.log(t),console.log(t,JSON.stringify(t)),t},json2Entity(e,t,r){r=r||jt,t||(t=new Cesium.Entity);let n=s=>(console.log(s),s instanceof Array?(console.log(s),s.map(a=>(console.log(a),new Cesium.Cartesian3(a.x,a.y,a.z)))):new Cesium.Cartesian3(s.x,s.y,s.z)),i=s=>Cesium.Color.fromCssColorString(s);return e.position&&(t.position=n(e.position)),e.point&&(t.point||(t.point=new Cesium.PointGraphics(r.pointStyle)),t.point.color=i(e.point.color),t.point.pixelSize=e.point.pixelSize,t.point.heightReference=e.point.heightReference),e.polyline&&(t.polyline||(t.polyline=new Cesium.PolylineGraphics(r.polylineStyle)),e.polyline.clampToGround!=null&&(t.polyline.clampToGround=e.polyline.clampToGround),e.polyline.material!=null&&(t.polyline.material=i(e.polyline.material)),e.polyline.positions!=null&&(t.polyline.positions=n(e.polyline.positions)),e.polyline.width!=null&&(t.polyline.width=e.polyline.width)),e.polygon&&(t.polygon||(t.polygon=new Cesium.PolygonGraphics(r.polygonStyle)),t.polygon.fill=e.polygon.fill,t.polygon.hierarchy=new Cesium.PolygonHierarchy(n(e.polygon.hierarchy.positions),n(e.polygon.hierarchy.holes)),t.polygon.material=i(e.polygon.material)),e.ellipse&&(t.ellipse||(t.ellipse=new Cesium.EllipseGraphics(r.circleStyle)),t.ellipse.fill=e.ellipse.fill,t.ellipse.semiMajorAxis=e.ellipse.semiMajorAxis,t.ellipse.semiMinorAxis=e.ellipse.semiMinorAxis,t.ellipse.material=i(e.ellipse.material)),e.label&&(t.label||(t.label=new Cesium.LabelGraphics(r.textStyle)),t.label.fillColor=i(e.label.fillColor),t.label.font=e.label.font,t.label.text=e.label.text),e.rectangle&&(t.rectangle||(t.rectangle=new Cesium.RectangleGraphics(r.rectangleStyle)),t.rectangle.coordinates=(({west:s,south:a,east:o,north:u})=>Cesium.Rectangle.fromRadians(s,a,o,u))(e.rectangle.coordinates),t.rectangle.fill=e.rectangle.fill,t.rectangle.material=i(e.rectangle.material)),e.billboard&&(t.billboard||(t.billboard=new Cesium.BillboardGraphics(r.markerStyle)),t.billboard.image=e.billboard.image,t.billboard.scale=e.billboard.scale),e.model&&(t.model||(t.model=new Cesium.ModelGraphics(r.modelStyle)),t.model.uri=e.model.uri,t.model.scale=e.model.scale),e.name&&(t.name=e.name),e.properties&&(t.properties=e.properties),t},align(e,t,r,n){let i=[Number.MAX_VALUE,Number.MAX_VALUE,Number.MAX_VALUE,Number.MIN_VALUE,Number.MIN_VALUE,Number.MIN_VALUE];return e.forEach(s=>{i[0]=Math.min(i[0],s.x),i[1]=Math.min(i[1],s.y),i[2]=Math.min(i[2],s.z),i[3]=Math.max(i[3],s.x),i[4]=Math.max(i[4],s.y),i[5]=Math.max(i[5],s.z)}),typeof t=="string"&&(t=t.toLocaleLowerCase(),t=="left"?t=i[0]:t=="center"?t=(i[0]+i[3])/2:t=="right"&&(t=i[3])),typeof t!="number"&&(t=null),typeof r=="string"&&(r=r.toLocaleLowerCase(),r=="front"?r=i[1]:r=="center"?r=(i[1]+i[4])/2:r=="back"&&(r=i[4])),typeof r!="number"&&(r=null),typeof n=="string"&&(n=n.toLocaleLowerCase(),n=="top"?n=i[2]:n=="center"?n=(i[2]+i[5])/2:n=="bottom"&&(n=i[5])),typeof n!="number"&&(n=null),e.map(s=>new Cesium.Cartesian3(t==null?s.x:t,r==null?s.y:r,n==null?s.z:n))},boxSelect(e,t){return new Promise(function(r,n){var i=!1,s=null,a=null,o=null,u=null,c=document.createElement("div"),f=document.createElement("div"),d=new Cesium.ScreenSpaceEventHandler(e.canvas);e._element.style.cursor="crosshair",e.enableCursorStyle=!0,t=D({offsetLeft:0,offsetTop:0},t),d.setInputAction(function(y){i=!0,s=y.position.x,a=y.position.y,c.style.cssText="position:absolute;width:0px;height:0px;font-size:0px;margin:0px;padding:0px;border:1px dashed #0099FF;background:rgba(0,153,255,0.3);z-index:1000;pointer-events: none;",c.id="selectDiv",c.style.left=s+t.offsetLeft+"px",c.style.top=a+t.offsetTop+"px",document.body.appendChild(c)},Cesium.ScreenSpaceEventType.RIGHT_DOWN),d.setInputAction(function(y){i=!1;var p=Math.min(o,s),m=Math.min(u,a),h=Math.abs(o-s),b=Math.abs(u-a);e.camera.pickEllipsoid({x:p,y:m},e.scene.globe.ellipsoid);var C={rectangle:{x:p,y:m,width:h,height:b},positions:[U.getCatesian3FromPX(e,{x:p,y:m}),U.getCatesian3FromPX(e,{x:p+h,y:m}),U.getCatesian3FromPX(e,{x:p+h,y:m+b}),U.getCatesian3FromPX(e,{x:p,y:m+b})]};r(C),document.getElementById("selectDiv").parentNode.removeChild(document.getElementById("selectDiv")),w()},Cesium.ScreenSpaceEventType.RIGHT_UP),d.setInputAction(function(y){i&&(o=y.endPosition.x,u=y.endPosition.y,c.style.left=Math.min(o,s)+t.offsetLeft+"px",c.style.top=Math.min(u,a)+t.offsetTop+"px",c.style.width=Math.abs(o-s)+"px",c.style.height=Math.abs(u-a)+"px"),f.style.display="block",f.style.left=y.endPosition.x+t.offsetLeft+10+"px",f.style.top=y.endPosition.y+t.offsetTop+10+"px"},Cesium.ScreenSpaceEventType.MOUSE_MOVE),f.style.cssText="position:absolute;font-size:12px;margin:0px;left:0;top:0;padding:5px;background:rgba(0,0,0,0.5);z-index:1001;color:#fff;pointer-events: none;display:none;",f.id="tipDiv",f.innerText="右键框选范围,左击取消",document.body.appendChild(f),d.setInputAction(function(y){w()},Cesium.ScreenSpaceEventType.LEFT_CLICK);function w(){document.getElementById("tipDiv").parentNode.removeChild(document.getElementById("tipDiv")),e._element.style.cursor="default",e.enableCursorStyle=!1,d&&!d.isDestroyed()&&(d.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK),d.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_UP),d.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE),d.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_DOWN),d.destroy())}})},rotatedPointByAngle(e,t,r){var n=Cesium.Transforms.eastNorthUpToFixedFrame(t),i=Cesium.Matrix4.inverse(n,new Cesium.Matrix4);Cesium.Matrix4.multiplyByPoint(i,t,new Cesium.Cartesian3);var s=Cesium.Matrix4.multiplyByPoint(i,e,new Cesium.Cartesian3),a=s.x*Math.cos(Cesium.Math.toRadians(r))+s.y*Math.sin(Cesium.Math.toRadians(r)),o=s.y*Math.cos(Cesium.Math.toRadians(r))-s.x*Math.sin(Cesium.Math.toRadians(r)),u=s.z;return Cesium.Matrix4.multiplyByPoint(n,new Cesium.Cartesian3(a,o,u),new Cesium.Cartesian3)},Base:Qt};class Zn{constructor(t){this._viewer=t,this._2dPC={time:Date.parse(new Date)},this._3dPC={time:Date.parse(new Date)},this._t=void 0}update(){let t=this._viewer.camera.positionCartographic.clone().height,r=U.getMapCenter(this._viewer)||U.getDefaultViewCenter(),n={height:t,longitude:Cesium.Math.toRadians(r.lng),latitude:Cesium.Math.toRadians(r.lat),heading:this._viewer.camera.heading,pitch:this._viewer.camera.pitch,roll:this._viewer.camera.roll};console.log(n);let i=this._viewer.scene.mode;i==Cesium.SceneMode.SCENE2D?(n.height=n.height*.78,this._2dPC={pc:n,time:Date.parse(new Date)}):i==Cesium.SceneMode.SCENE3D&&(console.log(n.pitch),n.height=n.height/.78/Math.abs(Math.sin(n.pitch)),this._3dPC={pc:n,time:Date.parse(new Date)})}keep(){let t=this._3dPC,r;if(this._2dPC.time>this._3dPC.time)r=Cesium.SceneMode.SCENE3D,t=this._2dPC.pc,this._3dPC=this._2dPC;else if(this._2dPC.time<this._3dPC.time)r=Cesium.SceneMode.SCENE2D,t=this._3dPC.pc,this._2dPC=this._3dPC;else return;this._waitMode=r,function(n,i){n._t!=null&&clearInterval(n._t),n._t=setInterval(function(){if(r===n._viewer.scene.mode){var s=new Cesium.Cartesian3.fromRadians(i.longitude,i.latitude,i.height);n._viewer.camera.setView({destination:s,orientation:{heading:i.heading,pitch:Cesium.Math.toRadians(-90),roll:0}}),clearInterval(n._t)}},1)}(this,t)}}class Yn{constructor(t,r){this.initOptions={animation:!1,baseLayerPicker:!1,fullscreenButton:!1,geocoder:!1,homeButton:!1,timeline:!1,infoBox:!1,sceneModePicker:!1,selectionIndicator:!1,sceneMode:Cesium.SceneMode.SCENE3D,navigationHelpButton:!1,scene3DOnly:!1,navigationInstructionsInitiallyVisible:!1,showRenderLoopErrors:!1,orderIndependentTranslucency:!1,mapMode2D:Cesium.MapMode2D.ROTATE,contextOptions:{webgl:{alpha:!0,depth:!0,stencil:!0,antialias:!0,premultipliedAlpha:!0,preserveDrawingBuffer:!0,failIfMajorPerformanceCaveat:!0}},imageryProvider:!1},this.init(t,r)}init(t,r){return v(this,null,function*(){Cesium.Ion.defaultAccessToken="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.9c-7Dhe21dqbq4HjLyV8wceZSShZVcstL9yDPmUpILw";const n=N&&N.extent||(Y.extent?Cesium.Rectangle.fromDegrees(Y.extent[0],Y.extent[1],Y.extent[2],Y.extent[3]):Cesium.Rectangle.fromDegrees(-180,-90,180,90));Cesium.Camera.DEFAULT_VIEW_FACTOR=0,Cesium.Camera.DEFAULT_VIEW_RECTANGLE=n;try{document.getElementById(t).innerHTML="",this.viewer=new Cesium.Viewer(t,this.initOptions)}catch(i){this.initOptions.contextOptions={},document.getElementById(t).innerHTML="",this.viewer=new Cesium.Viewer(t,this.initOptions)}this.keepViewer=new Zn(this.viewer),this.hotkey=new jn({viewer:this.viewer}),this.viewer._cesiumWidget._creditContainer.style.display="none",r&&r(this.viewer),this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK),this.viewer.scene.screenSpaceCameraController.zoomEventTypes=[Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK],this.viewer.scene.screenSpaceCameraController.lookEventTypes=[],this.viewer.scene.globe.depthTestAgainstTerrain=!1,this.viewer.scene.postProcessStages.fxaa.enabled=!1,this.viewer.scene.skyBox.show=!1,this.viewer.scene.backgroundColor=new Cesium.Color(0,0,0,0),console.log("开始画点线面"),this.viewer})}}let oe=null;function _i(e){const t=setInterval(()=>{oe&&(e&&e(oe),clearInterval(t))},1e3)}function Mi(e,t){return oe=new Yn(e,t),oe}export{De as B,Yn as C,oe as G,Ei as H,Ue as L,Oi as a,U as b,N as c,st as d,S as e,Ri as f,_i as g,vi as h,Mi as i,Ti as l,xi as s,Ai as u,Pt as z};
