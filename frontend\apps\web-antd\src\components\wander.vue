<script setup>
import { ref } from 'vue';

const selectValue = ref('one');
const adding = ref(false);
const roadName = ref('');
const resultList = ref([
  {
    name: '飞行路线1',
  },
  {
    name: '飞行路线2',
  },
  {
    name: '飞行路线3',
  },
  {
    name: '飞行路线4',
  },
  {
    name: '飞行路线5',
  },
  {
    name: '飞行路线6',
  },
]);
const pointList = ref([
  {
    name: '点1',
  },
  {
    name: '点2',
  },
  {
    name: '点3',
  },
  {
    name: '点4',
  },
  {
    name: '点5',
  },
  {
    name: '点6',
  },
]);
</script>

<template>
  <div class="wander">
    <div class="layerHeader">
      <div style="display: flex; align-items: center; justify-content: center">
        <div style="display: flex; align-items: center">
          <CaretLeftOutlined
            style="
              position: relative;
              margin-right: -7px;
              font-size: 36px;
              color: #1b3157;
            "
          />
          <span
            style="
              display: inline-block;
              height: 28px;
              padding: 5px 13px;
              font-size: 16px;
              font-weight: bold;
              line-height: 18px;
              color: #aad5f2;
              background: #1b3157;
            "
          >
            地图漫游
          </span>
          <CaretRightOutlined
            style="
              position: relative;
              margin-left: -7px;
              font-size: 40px;
              color: #1b3157;
            "
          />
        </div>
      </div>
    </div>
    <div class="layerContent">
      <div v-if="!adding">
        <div style="display: flex; width: 100%; margin: 10px 5px">
          <a-button size="small" type="primary" @click="adding = true">
            新增路线
          </a-button>
          <a-button danger size="small" type="primary">清空路线</a-button>
        </div>
        <div class="resultList" style="margin: 10px 5px; background: #031126">
          <a-list
            :data-source="resultList"
            class="listStyle"
            item-layout="horizontal"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <template #actions>
                  <div>
                    <a>飞行</a>
                  </div>
                  <div>
                    <a>编辑</a>
                  </div>
                  <div>
                    <a style="color: red">删除</a>
                  </div>
                </template>
                <a-list-item-meta style="cursor: pointer">
                  <template #title>
                    <span> {{ item.name }} </span>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
      <div v-else>
        <a-button
          size="small"
          style="color: white"
          type="text"
          @click="adding = false"
        >
          《 返回
        </a-button>
        <br /><br />
        <a-input
          v-model:value="roadName"
          addon-before="路线名称："
          placeholder="请输入"
        />
        <a-input-group class="selectGroup" compact>
          <div
            style="
              width: 25%;
              height: 32px;
              color: whitesmoke;
              text-align: center;
              background: rgb(32 73 117 / 50%);
            "
          >
            视角
          </div>
          <a-select ref="select" v-model:value="selectValue" style="width: 75%">
            <a-select-option value="one">第一人称视角</a-select-option>
            <a-select-option value="two">相机跟随</a-select-option>
            <a-select-option value="three">上帝视角</a-select-option>
          </a-select>
        </a-input-group>

        <a-tabs
          v-model:active-key="activeKey"
          :tab-bar-style="{ color: 'darkgray' }"
        >
          <a-tab-pane key="1" tab="自定义绘制">
            <div
              class="resultList"
              style="margin: 10px 5px; background: #031126"
            >
              <a-list
                :data-source="pointList"
                class="listStyle"
                item-layout="horizontal"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <template #actions>
                      <div>
                        <a>定位</a>
                      </div>
                      <div style="color: red">
                        <a>删除</a>
                      </div>
                    </template>
                    <a-list-item-meta style="cursor: pointer">
                      <template #title>
                        <span> {{ item.name }} </span>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="导入">
            <a-input
              v-model:value="roadName"
              addon-before="选择文件："
              type="file"
            />
          </a-tab-pane>
        </a-tabs>
        <br /><br />
        <a-button size="small" type="primary">飞行播放</a-button>
        <a-button danger size="small" type="primary">确定添加</a-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
:deep(.ant-input) {
  background-color: rgba(32, 73, 117, 0.5);
  color: #aacdf9;
  height: 40px;
  border: none;
}

:deep(.ant-input-group-addon) {
  background-color: rgba(32, 73, 117, 0.5);
  color: #aacdf9;
  height: 40px;
  border: none;
}

:deep(.selectGroup) {
  .ant-input-group {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .ant-select {
      width: 30%;
      margin-bottom: 0;

      .ant-select-selector {
        height: 40px;
        //background: linear-gradient(270deg, #1376B2 0%, #043B78 100%);
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #ffffff;
        border: none;
        opacity: 1;
      }

      .ant-select-arrow {
        color: #ffffff;
      }
    }

    label {
      color: #ffffff;
    }

    .ant-calendar-range-picker-separator {
      color: #ffffff;
    }
  }

  .ant-select-selector {
    background: rgba(32, 73, 117, 0.5);
    border-radius: 4px;
    display: flex;
    align-items: center;
    color: #aacdf9;
    border: none;
    padding: 0 10px;
  }
  .ant-select-multiple {
    .ant-select-selector {
      height: auto;
      min-height: 40px;
      padding: 5px;

      .ant-select-selection-item {
        border: 1px solid #0e629e;
        background: transparent;
      }
    }
  }

  .ant-select-arrow,
  .ant-cascader-picker-arrow {
    color: #aacdf9;
  }
}

.wander {
  width: 300px;

  .layerHeader {
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #051129;
    text-align: center;

    .icon-font {
      position: absolute;
      left: 10px;
      top: 8px;
    }

    div {
      padding: 8px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      line-height: 32px;
      height: 50px;
      color: #ffffff;
      letter-spacing: 3px;
      opacity: 1;
    }
  }

  .layerContent {
    background: rgba(5, 17, 41, 0.8);
    padding: 10px 10px 10px 10px;
    font-size: 14px;
    font-weight: 400;
    line-height: 19px;
    width: 100%;
    color: white;

    .resultList {
      overflow-y: scroll;
      height: 500px;
      background: #031126;

      .listStyle {
        :deep(.ant-list-item) {
          background: #204975;
          padding: 12px;
          border-bottom: 1px solid #051129;

          .ant-list-item-meta-title {
            color: #aacdf9;
          }

          .ant-list-item-meta-description {
            color: #44b966;
          }

          .ant-list-item-action {
            margin-left: 0px !important;
          }
        }
      }
    }
  }
}
</style>
