const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: 'system_manage'
};

async function debugUserRoles() {
  let connection;
  
  try {
    console.log('🔍 调试用户角色关系...');
    
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 连接数据库成功');
    
    // 检查用户数据
    console.log('\n--- 用户数据 ---');
    const [users] = await connection.execute('SELECT id, username, real_name, phone FROM users');
    users.forEach(user => {
      console.log(`ID: ${user.id}, 用户名: ${user.username}, 姓名: ${user.real_name}, 手机: ${user.phone || '无'}`);
    });
    
    // 检查角色数据
    console.log('\n--- 角色数据 ---');
    const [roles] = await connection.execute('SELECT id, name, code FROM roles');
    roles.forEach(role => {
      console.log(`ID: ${role.id}, 名称: ${role.name}, 编码: ${role.code}`);
    });
    
    // 检查用户角色关联
    console.log('\n--- 用户角色关联 ---');
    const [userRoles] = await connection.execute(`
      SELECT ur.user_id, ur.role_id, u.username, r.name as role_name 
      FROM user_roles ur 
      LEFT JOIN users u ON ur.user_id = u.id 
      LEFT JOIN roles r ON ur.role_id = r.id
    `);
    
    if (userRoles.length === 0) {
      console.log('❌ 没有用户角色关联数据');
    } else {
      userRoles.forEach(ur => {
        console.log(`用户: ${ur.username} (ID: ${ur.user_id}) -> 角色: ${ur.role_name} (ID: ${ur.role_id})`);
      });
    }
    
    // 测试用户查询（模拟TypeORM的查询）
    console.log('\n--- 模拟用户查询 ---');
    const [adminQuery] = await connection.execute(`
      SELECT u.*, r.id as role_id, r.name as role_name, r.code as role_code
      FROM users u
      LEFT JOIN user_roles ur ON u.id = ur.user_id
      LEFT JOIN roles r ON ur.role_id = r.id
      WHERE u.username = 'admin'
    `);
    
    if (adminQuery.length > 0) {
      console.log('admin用户查询结果:');
      console.log(JSON.stringify(adminQuery[0], null, 2));
    } else {
      console.log('❌ 未找到admin用户');
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

debugUserRoles().catch(console.error);
