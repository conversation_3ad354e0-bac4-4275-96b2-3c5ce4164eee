var i=(m,a,t)=>new Promise((o,n)=>{var s=e=>{try{r(t.next(e))}catch(c){n(c)}},u=e=>{try{r(t.throw(e))}catch(c){n(c)}},r=e=>e.done?o(e.value):Promise.resolve(e.value).then(s,u);r((t=t.apply(m,a)).next())});import{u as _}from"./useQuery-CNjS4j1-.js";import{B as d}from"./bootstrap-DShsrVit.js";import{a4 as g,O as h,af as p,am as f,n as w,ah as v,an as B,a3 as l,ao as y,al as k,ap as q,F as x}from"../jse/index-index-BMh_AyeW.js";const C={key:0,class:"my-3"},K=g({__name:"query-retries",setup(m){const a=h(-1);function t(){return i(this,null,function*(){return a.value+=1,new Promise((r,e)=>{setTimeout(()=>{e(new Error("something went wrong!"))},1e3)})})}const{error:o,isFetching:n,refetch:s}=_({enabled:!1,queryFn:t,queryKey:["queryKey"],retry:3}),u=()=>i(this,null,function*(){a.value=-1,yield s()});return(r,e)=>(p(),f(x,null,[w(l(d),{loading:l(n),onClick:u},{default:v(()=>e[0]||(e[0]=[B(" 发起错误重试 ")])),_:1},8,["loading"]),a.value>0?(p(),f("p",C,"重试次数"+y(a.value),1)):k("",!0),q("p",null,y(l(o)),1)],64))}});export{K as _};
