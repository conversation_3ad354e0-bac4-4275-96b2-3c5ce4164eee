function refreshTokenRetryCallback(resource, error) {
  if (error.statusCode === 403) {
	return getNewAccessToken()
	  .then(function(token) {
		resource.queryParameters.access_token = token;
		return true;
	  })
	  .catch(function() {
		return false;
	  });
  }

  return false;
}

export default (options)=>{
	options = {
		url:'',
		headers:{},
		
		...options
	}
	let resource = new Cesium.Resource({
		url: options.url,
		//proxy: new Cesium.DefaultProxy('/proxy/'),
		headers: options.headers,
		queryParameters: options.params,
		retryCallback: (resource, error)=>{
			if(refreshTokenRetryCallback(resource, error)){
				
			}
			if(options.retryCallback){
				return options.retryCallback(resource, error)
			}
			return false;//return true;重试
		},
		retryAttempts: 1
	});
	return 	resource
}