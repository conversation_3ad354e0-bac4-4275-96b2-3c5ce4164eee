import{ao as i,k as s,aP as e,l as a,ay as n,j as t}from"./chunks/framework.C8U7mBUf.js";const l=JSON.parse('{"title":"Slimmed-Down Version","description":"","frontmatter":{},"headers":[],"relativePath":"en/guide/introduction/thin.md","filePath":"en/guide/introduction/thin.md"}');const o=i({name:"en/guide/introduction/thin.md"},[["render",function(i,l,o,p,d,h){const r=n("NolebaseGitContributors"),k=n("NolebaseGitChangelog");return t(),s("div",null,[l[0]||(l[0]=e('<h1 id="slimmed-down-version" tabindex="-1">Slimmed-Down Version <a class="header-anchor" href="#slimmed-down-version" aria-label="Permalink to &quot;Slimmed-Down Version&quot;">​</a></h1><p>Starting from version <code>5.0</code>, we no longer provide slimmed-down repositories or branches. Our goal is to offer a more consistent development experience while reducing maintenance costs. Here’s how we introduce our project, slim down, and remove unnecessary features.</p><h2 id="application-slimming" tabindex="-1">Application Slimming <a class="header-anchor" href="#application-slimming" aria-label="Permalink to &quot;Application Slimming&quot;">​</a></h2><p>First, identify the version of the <code>UI</code> component library you need, and then delete the corresponding applications. For example, if you choose to use <code>Ant Design Vue</code>, you can delete the other applications. Simply remove the following two folders:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">apps/web-ele</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">apps/web-native</span></span></code></pre></div><div class="tip custom-block"><p class="custom-block-title">TIP</p><p>If your project doesn’t include the <code>UI</code> component library you need, you can delete all other applications and create your own new application as needed.</p></div><h2 id="demo-code-slimming" tabindex="-1">Demo Code Slimming <a class="header-anchor" href="#demo-code-slimming" aria-label="Permalink to &quot;Demo Code Slimming&quot;">​</a></h2><p>If you don’t need demo code, you can simply delete the <code>playground</code> folder</p><h2 id="documentation-slimming" tabindex="-1">Documentation Slimming <a class="header-anchor" href="#documentation-slimming" aria-label="Permalink to &quot;Documentation Slimming&quot;">​</a></h2><p>If you don’t need documentation, you can delete the <code>docs</code> folder.</p><h2 id="remove-mock-service" tabindex="-1">Remove Mock Service <a class="header-anchor" href="#remove-mock-service" aria-label="Permalink to &quot;Remove Mock Service&quot;">​</a></h2><p>If you don’t need the <code>Mock</code> service, you can delete the <code>apps/backend-mock</code> folder. Also, remove the <code>VITE_NITRO_MOCK</code> variable from the <code>.env.development</code> file in your application.</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Whether to enable Nitro Mock service, true to enable, false to disable</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_NITRO_MOCK</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">false</span></span></code></pre></div><h2 id="installing-dependencies" tabindex="-1">Installing Dependencies <a class="header-anchor" href="#installing-dependencies" aria-label="Permalink to &quot;Installing Dependencies&quot;">​</a></h2><p>Now that you’ve completed the slimming operations, you can install the dependencies and start your project:</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># Run in the root directory</span></span>\n<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> install</span></span></code></pre></div><h2 id="adjusting-commands" tabindex="-1">Adjusting Commands <a class="header-anchor" href="#adjusting-commands" aria-label="Permalink to &quot;Adjusting Commands&quot;">​</a></h2><p>After slimming down, you may need to adjust commands according to your project. In the <code>package.json</code> file in the root directory, you can adjust the <code>scripts</code> field and remove any commands you don’t need.</p><div class="language-json vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;scripts&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:antd&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/web-antd&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:docs&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/docs&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:ele&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/web-ele&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:naive&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/web-naive&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:play&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/playground&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:antd&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/web-antd run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:docs&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/docs run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:ele&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/web-ele run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:play&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/playground run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>\n<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:naive&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/web-naive run dev&quot;</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>\n<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div>',19)),a(r),a(k)])}]]);export{l as __pageData,o as default};
