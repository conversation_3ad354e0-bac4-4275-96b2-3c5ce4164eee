import{a4 as i,O as d,bD as f,af as u,ag as m,ah as a,a3 as t,n as o,ap as s,an as n,ao as c}from"../jse/index-index-BMh_AyeW.js";import{aH as _,B as x}from"./bootstrap-DShsrVit.js";import{C}from"./index-B_b7xM74.js";import{_ as y}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";const B={class:"mb-3"},b={class:"flex"},H=i({__name:"index",setup(g){const r=d("Hello"),{copy:l,text:p}=f({legacy:!0,source:r});return(k,e)=>(u(),m(t(y),{title:"剪切板示例"},{default:a(()=>[o(t(C),{title:"基本使用"},{default:a(()=>[s("p",B,[e[1]||(e[1]=n(" Current copied: ")),s("code",null,c(t(p)||"none"),1)]),s("div",b,[o(t(_),{class:"mr-3 flex w-[200px]"}),o(t(x),{type:"primary",onClick:e[0]||(e[0]=v=>t(l)(r.value))},{default:a(()=>e[2]||(e[2]=[n(" Copy ")])),_:1})])]),_:1})]),_:1}))}});export{H as default};
