<script lang="ts">
import { defineComponent, reactive, ref, watchEffect } from 'vue';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

import { cloneDeep } from 'lodash-es';

export default defineComponent({
  name: 'SCharts',
  components: { EchartsUI },
  props: {
    option: {
      type: Object,
      default: () => ({}),
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: 'calc(100vh - 78px)',
    },
  },
  setup(props) {
    const chartRef = ref<EchartsUIType>();
    const { renderEcharts } = useEcharts(chartRef);
    const option = reactive({
    });

    watchEffect(() => {
      initCharts();
    });

    function initCharts() {
      if (props.option) {
        Object.assign(option, cloneDeep(props.option));
      }

      renderEcharts(option);
    }
    return { chartRef };
  },
});
</script>
<template>
  <EchartsUI ref="chartRef" />
</template>
