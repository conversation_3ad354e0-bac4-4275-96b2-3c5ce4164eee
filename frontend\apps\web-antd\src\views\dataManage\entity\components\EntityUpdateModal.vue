<script lang="ts" setup>
import { computed, ref, unref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';

import { getFormSchemaByCategory } from '../entity.data';
import type { FileInfo } from "cspell/dist/esm/util/fileHelper";
import { save, edit, updateFileState } from "#/views/dataManage/entity/entity.api";
import { message } from "ant-design-vue";
import { showLoading, showSuccess } from "#/utils/toast.js";
import { uploadFolder } from "#/utils/folderUpload.js";
import  {uploadFile} from '#/utils/fileUpload.js'
import {RegionUtils} from "#/utils/regionUtils";
let category;
let dataType;
// 声明Emits
const emit = defineEmits(['register', 'success']);

let id = null;
let reginCodeValue = null;

const clearData = () => {
  formApi.setState({
    commonConfig: { disabled: false },
    submitButtonOptions: { disabled: false },
    resetButtonOptions: { disabled: false }
  });
}

const [Modal, modalApi] = useVbenModal({
  closeOnClickModal:false,
  closeOnClickModal:false,
  onCancel() {
    modalApi.close();
  },
  onClosed() {
    clearData();
  },
  onConfirm() {},
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      formApi.resetForm();
      const data = modalApi.getData<Record<string, any>>();
      const state = modalApi.useStore();
      console.log(data);
      isUpdate.value = state.value && state.value.update;
      isUpload.value = state.value && state.value.upload;
      category = state.value && state.value.category;
      dataType = state.value && state.value.row && state.value.row.dataType;

      let rowData = {...state.value.row}
      let regionCode = [parseInt(rowData.regionProvinceCode),parseInt(rowData.regionCityCode), parseInt(rowData.regionCountyCode)];
      let regionName = `${rowData.regionProvinceName || ''} ${rowData.regionCityName || ''} ${rowData.regionCityName || ''}`.trim();
      rowData.regionCode = regionName;
      reginCodeValue = regionCode;


      delete rowData.regionProvinceCode;
      delete rowData.regionCityCode;
      delete rowData.regionCountyCode;
      delete rowData.regionProvinceName;
      delete rowData.regionCityName;
      delete rowData.regionCountyName;

      let values = {}
      if (isUpdate.value) {
        values = {...rowData};
      }
      id = values.id;
      values.update = isUpdate.value;
      values.upload = isUpload.value;
      formApi.setValues(values);
    }
  },
});
const formModel = ref({
  id: '', // 确保初始化
  datasetName: '',  // 其他字段
});
let isUpdate = ref(false);
let isUpload = ref(false);
const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'text-right pr-4',
    labelWidth: 100,
  },
  // 提交函数
  handleReset,
  handleSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: getFormSchemaByCategory(category),
  wrapperClass: 'grid-cols-1',
  submitButtonOptions:{
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  }
});
let fileInput : any= null;
const fileList = ref([]);
const uploadType = ref('0');
const startUpload = ref(false);

const headers = {
    authorization: 'authorization-text',
  };

// 设置标题
const title = computed(() => (unref(isUpdate) ? '编辑' : '新增'));

async function handleReset(values) {
  let id = values.id;
  await formApi.resetForm();
  await formApi.setFieldValue("id",id);
  await formApi.setFieldValue("dataType",dataType);
}

// 表单提交事件
async function handleSubmit(values) {
  try {
    values.category = category;
    // startUpload.value = true;
    let regionCode = values.regionCode;
    if(regionCode != null && typeof regionCode === "string"){
      regionCode = reginCodeValue;
    }

    let regionProvinceCode = regionCode[0];
    let regionCityCode = regionCode[1];
    let regionCountyCode = regionCode[2];

    // 从表单中获取省市区名称
    let region = RegionUtils.getRegionNameByCode(''+regionCountyCode);
    let regionProvinceName = region.province;
    let regionCityName = region.city;
    let regionCountyName = region.area;

    // 将省市区名称添加到values中
    values.regionProvinceCode = regionProvinceCode;
    values.regionProvinceName = regionProvinceName;
    values.regionCityCode = regionCityCode;
    values.regionCityName = regionCityName;
    values.regionCountyCode = regionCountyCode;
    values.regionCountyName = regionCountyName;

    showLoading('操作处理中...');
    values.dataType = 'vector';
    let result =  isUpdate.value ? await edit(values,id) :  await save(values) ;
    delete values.regionCode;
    if(!isUpdate.value){
      if(fileInput.files == null || fileInput.files.length == 0) {
        onSuccess && onSuccess();
        return;
      }

      let onUpload = async (uploadId)=> {
        await updateFileState({
          id: result,
          fileId:uploadId,
          dataType:values.dataType
        })
        onSuccess && onSuccess();
      }
      let dataType = values.dataType;
      let fileFormat = values.fileFormat;
      uploadType.value === '0' ?
          await uploadFolder(fileInput,onUpload,dataType,fileFormat)
        : await uploadFile(fileInput,onUpload,dataType,fileFormat);
      return;
    }
    onSuccess && onSuccess();
  } finally {
  }
}

const onSuccess = () =>{
  // 关闭弹窗
  modalApi.close();
  showSuccess('操作成功！');
  emit('success');
}

const handleChange = (info: FileInfo) => {
  if (info.file.status !== 'uploading') {
    console.log(info.file, info.fileList);
  }
  if (info.file.status === 'done') {
  } else if (info.file.status === 'error') {
  }
};

const handleRemove = (file) => {
  const index = fileList.value.indexOf(file);
  const newFileList = fileList.value.slice();
  newFileList.splice(index, 1);
  fileList.value = newFileList;
};

const beforeUpload = (file) => {
  fileList.value = [...fileList.value, file];
  return false;
};

const handleUploadTypeChange = (data) =>{
  uploadType.value = data.target.value;
}

const handleFolderSelect = (data) =>{
  console.log(data);
  fileInput = data.target;
  // uploadFolder(fileInput);
  const fileName = document.getElementById('fileName');
  fileName.textContent = fileInput.files.length > 0 ? '已选'+fileInput.files.length+'个文件' : '没有选择文件';

  formApi.setFieldValue("fileUpload",fileInput.files);
}


const triggerFileInput = () => {
  document.getElementById('fileInput').click();
}

</script>
<template>
  <Modal
    v-bind="$attrs"
    :footer="false"
    :title="title"
    destroy-on-close
    width="900px"
    :maskClosable="false"
  >
    <Form>
<!--      <template #uploadType="slotProps">-->
<!--        <a-radio-group v-show="!isUpdate" v-model:value="uploadType" @change="handleUploadTypeChange":disabled="true">-->
<!--          <a-radio value="0">目录</a-radio>-->
<!--          <a-radio value="1">文件</a-radio>-->
<!--        </a-radio-group>-->
<!--      </template>-->
      <template #fileUpload="slotProps">
        <input
          class="hidden"
          v-show="!isUpdate"  id="fileInput" type="file"  :directory="uploadType ==='0'"  :webkitdirectory="uploadType ==='0'" @change="handleFolderSelect"  />

        <!-- 自定义按钮 -->
        <label for="fileInput" v-if="startUpload" onclick="triggerFileInput"  class="custom-label bg-gray-200 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400">
          选择文件
        </label>

        <label for="fileInput" v-else   onclick="triggerFileInput"  class="custom-label  bg-primary-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-primary-400">
          选择文件
        </label>

        <!-- 显示选择的文件名称 -->
        <span id="fileName" class="ml-4">没有选择文件</span>
      </template>
    </Form>
    <view v-show="startUpload"  style="width:900px">
      <div id="folderProgressArea" class="folderProgressArea">
        文件夹总进度：<div id="folderProgress" class="folderProgress">0%</div>
      </div>
      <div style="display: flex;flex-direction:column;justify-content: flex-start;align-items: flex-start">
        <div style="margin-bottom: 5px">当前文件进度：</div>
        <div id="fileProgress" class="fileProgress bg-primary text-white "></div>
      </div>
    </view>

  </Modal>
</template>
<style lang="less" scoped>
@import '#/styles/dark-antd.less';
.fileProgress {
  color:white;
  background-color: #00a2ff;
  //padding: 5px;
  width:0px;
}
.folderProgressArea {
  width:100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-bottom: 5px;
  visibility:hidden;
}

</style>
