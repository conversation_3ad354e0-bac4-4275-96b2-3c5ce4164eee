import axios from 'axios';
import * as Cesium from 'cesium';

import { GISMap as map } from '../index';
import {
  arcgisToGeoJSON,
  geojsonToArcGIS,
} from '@esri/arcgis-to-geojson-utils';
import { h } from 'vue';
import core from '../core';
import request from './arcgisRequest';
//https://developers.arcgis.com/rest/services-reference/enterprise/query-feature-service-layer-.htm
export default class ArcGisQuery {
  constructor(layerInfo) {
    this.layerInfo = layerInfo;
    this.url = layerInfo.data.url || '';
    this.layers = layerInfo.data.layers || '0';
    this._where = '1=1';
    //this.text = '';
    //this.objectIds='';
    /**
     * time=1199145600000[, 1230768000000]
     */
    //this.time='';
    /**
     * 118.2546,26.23548//点
     * {xmin:118,ymin:24,xmax:119,ymax:25}//四至
     *
     */
    this.geometry = '';
    /**
     * esriGeometryPoint | esriGeometryMultipoint | esriGeometryPolyline | esriGeometryPolygon | esriGeometryEnvelope
     */
    this.geometryType = 'esriGeometryEnvelope';
    //this.inSR='4326';
    /**
		 * 空间关系
		 * esriSpatialRelIntersects | esriSpatialRelContains | esriSpatialRelCrosses | esriSpatialRelEnvelopeIntersects |
		 * esriSpatialRelIndexIntersects | esriSpatialRelOverlaps | esriSpatialRelTouches | esriSpatialRelWithin
		 * （1）intersects(相交） 空间关系返回图层视图中与查询几何体相交的要素
			（2）contains（包含）空间关系返回图层视图中完全由查询几何体包含的要素
			（3）crosses（穿过）当查询几何体的内部与图层视图中要素的内部或边界接触时，空间关系将返回图层视图中的要素
			（4）envelope-intersects（图层视图中与查询几何相交）空间关系返回图层视图中与筛选器几何体的包络（或范围）相交的要素
			（5）overlaps（重叠） 空间关系返回图层视图中与查询几何体重叠的要素。只能比较相同几何体的要素
			（6）touches（边界相交） 空间关系返回图层视图中接触查询几何体的要素。几何体的边界相交，但内部不同
			（7）within（完全包含） 空间关系返回图层视图中完全包含查询几何体的要素。
			（8）disjoint intersects 空间关系返回图层视图中不相交的要素
		 */
    this.spatialRel = 'esriSpatialRelIntersects';
    this.relationParam = ''; //distance//units
    this.outFields = '*';
    this.returnGeometry = false;
    //this.returnTrueCurves=false;
    //this.maxAllowableOffset='';
    //this.geometryPrecision='';
    //this.outSR='4326';
    //this.returnIdsOnly=false;
    this.returnCountOnly = false;
    /**
     * 排序
     * orderByFields=STATE_NAME ASC, RACE DESC, GENDER
     */
    //this.orderByFields='';
    /**
     * 统计分组字段
     * groupByFieldsForStatistics='Name1,Name2'
     */
    this.groupByFieldsForStatistics = '';
    /**
		 * 统计输出字段
		 * [
			  {
				"statisticType": "sum",//"<count | sum | min | max | avg | stddev | var>"
				"onStatisticField": "GENDER",
				"outStatisticFieldName": "PopulationByGender"
			  },
			  {
				"statisticType": "avg",
				"onStatisticField": "INCOME",
				"outStatisticFieldName": "AverageIncome"
			  }
			]
		 */
    this.outStatistics = '';
    //this.returnZ=false;
    //this.returnM=false;
    //this.gdbVersion='';
    this.returnDistinctValues = false;
    this.resultOffset = '';
    this.resultRecordCount = '';
    this.queryByDistance = '';
    //this.returnExtentsOnly=false;
    //this.datumTransformation='';
    //this.parameterValues='';
    //this.rangeValues='';
    //this.f='json';
  }
  get where() {
    return this._where ? this._where : '1=1';
  }
  set where(value) {
    this._where = value;
  }
  getData(data) {
    let defaultData = {
      where: '1=1',
      text: '',
      objectIds: '',
      time: '',
      geometry: '',
      geometryType: 'esriGeometryEnvelope',
      inSR: '4326',
      spatialRel: 'esriSpatialRelIntersects',
      relationParam: '',
      outFields: '*',
      returnGeometry: false,
      returnTrueCurves: false,
      maxAllowableOffset: '',
      geometryPrecision: '',
      outSR: '4326',
      returnIdsOnly: false,
      returnCountOnly: false,
      orderByFields: '',
      groupByFieldsForStatistics: '',
      outStatistics: '',
      returnZ: false,
      returnM: false,
      gdbVersion: '',
      returnDistinctValues: false,
      resultOffset: '',
      resultRecordCount: '',
      queryByDistance: '',
      returnExtentsOnly: false,
      datumTransformation: '',
      parameterValues: '',
      rangeValues: '',
      f: 'json',
    };
    let token = this.layerInfo.data.token;
    return {
      ...defaultData,
      ...data,
      ...(() => {
        return token
          ? {
              token: token,
            }
          : {};
      })(),
    };
  }
  query(callback) {
    this.showHighLight();
    this.showList().then((res) => {
      if (callback) callback(res);
    });
  }
  async queryById() {
    let viewer = map.viewer;
    let name = '___HIGHLIGHT';
    let data = await request({
      url: `${this.url.trim('/')}/${encodeURIComponent(this.layers)}/query`,
      method: 'get',
      params: this.getData({
        where: this.where,
        returnGeometry: true,
      }),
    });
    if (data.features && data.features.length) {
      let json = arcgisToGeoJSON(data.features[0]);
      let dataSource = await Cesium.GeoJsonDataSource.load(json, {
        clampToGround: true,
      });
      /* dataSource.entities.values.forEach(entity=>{
				console.log(entity)
				if(entity.polyline){
					entity.polyline.width = 10;
					entity.polyline.material =  Cesium.Color.fromCssColorString("#00fcff");
				}
				else if(entity.polygon){
					entity.polygon.fill = undefined;
					entity.polygon.outlineWidth = 10;
					entity.polygon.outlineColor = Cesium.Color.fromCssColorString("#00fcff");
				}
			}) */
      let dss = viewer.dataSources.getByName(name);
      if (dss.length) {
        viewer.dataSources.remove(dss[0], true);
      }
      viewer.dataSources.add(dataSource);
      viewer.flyTo(dataSource, {
        offset: new Cesium.HeadingPitchRange(
          viewer.camera.heading,
          viewer.camera.pitch,
          0,
        ),
      });
      dataSource.name = name;
      return dataSource;
    }
    return;
  }
  async statistics(options) {
    this.groupByFieldsForStatistics = options.groupfield || '';
    let outStatistics = (options.statList || [])
      .filter((a) => a.field && a.exp)
      .map((a) => {
        return {
          onStatisticField: a.field,
          outStatisticFieldName: `${a.field}_${a.exp}`,
          statisticType: a.exp,
        };
      });

    this.outStatistics = '';
    if (outStatistics.length) {
      //this.outFields
      this.outStatistics = JSON.stringify(outStatistics);
    }
    let data = null;
    if (options.betweens && options.betweens.length > 0) {
      let result = [];
      for (let index = 0; index < options.betweens.length; index++) {
        let item = options.betweens[index];
        let where = `(${this.where})`;
        /* if(index==options.betweens.length-1){
					where += ` and ${options.groupfield} > ${item.value}`;
				}
				else{
					where += ` and ${options.groupfield} <= ${item.value}`;
					if(index>0){
						where += ` and ${options.groupfield} > ${options.betweens[index-1].value}`;
					}
				} */
        where += ` and ${options.groupfield} <= ${item.value2}`;
        where += ` and ${options.groupfield} > ${item.value1}`;
        data = await request({
          url: `${this.url.trim('/')}/${encodeURIComponent(this.layers)}/query`,
          method: 'get',
          params: this.getData({
            where: where,
            geometry: this.geometry,
            geometryType: this.geometryType,
            spatialRel: this.spatialRel,
            //orderByFields:this.orderByFields,
            groupByFieldsForStatistics: '', //this.groupByFieldsForStatistics,
            outStatistics: this.outStatistics,
            queryByDistance: this.queryByDistance,
          }),
        });
        (data && data.features
          ? data.features.map((a) => a.attributes)
          : []
        ).forEach((item2) => {
          item2 &&
            (item2[options.groupfield] =
              item.label || `${item.value1}-${item.value2}`);
          result.push(item2 || {});
        });
      }
      return result;
    } else {
      data = await request({
        url: `${this.url.trim('/')}/${encodeURIComponent(this.layers)}/query`,
        method: 'get',
        params: this.getData({
          where: this.where,
          geometry: this.geometry,
          geometryType: this.geometryType,
          spatialRel: this.spatialRel,
          //orderByFields:this.orderByFields,
          groupByFieldsForStatistics: this.groupByFieldsForStatistics,
          outStatistics: this.outStatistics,
          queryByDistance: this.queryByDistance,
        }),
      });
      return data && data.features
        ? data.features.map((a) => a.attributes)
        : [];
    }
  }

  async showList() {
    let res1 = await request({
      url: `${this.url.trim('/')}/${encodeURIComponent(this.layers)}/query`,
      method: 'get',
      params: this.getData({
        where: this.where,
        outFields: this.outFields,
        geometry: this.geometry,
        geometryType: this.geometryType,
        spatialRel: this.spatialRel,
        resultOffset: this.resultOffset,
        resultRecordCount: this.resultRecordCount,
        queryByDistance: this.queryByDistance,
        returnDistinctValues: this.returnDistinctValues,
      }),
    });
    let res2 = await request({
      url: `${this.url.trim('/')}/${encodeURIComponent(this.layers)}/query`,
      method: 'get',
      params: this.getData({
        where: this.where,
        outFields: this.outFields,
        geometry: this.geometry,
        geometryType: this.geometryType,
        spatialRel: this.spatialRel,
        returnCountOnly: true,
        queryByDistance: this.queryByDistance,
        returnDistinctValues: this.returnDistinctValues,
      }),
    });
    return {
      list: res1.features ? res1.features.map((a) => a.attributes) : [],
      count: res2.count || 0,
    };
  }
  async showHighLight() {
    /* let where = {};
		where[this.layers] = this.where;
		//console.log(`${this.url}?layerDefs=${encodeURIComponent(JSON.stringify(where))}`)
		this.layerInfo.data.url = `${this.url}?layerDefs=${encodeURIComponent(JSON.stringify(where))}`;
		//”0:sde.st_within (shape, sde.st_geometry ('polygon ((12818466 4196179,13308886 4196179,13308886 4499482,12818466 4499482,12818466 4196179))', 3857)) = 't'”
		//https://wenku.baidu.com/view/7892601f463610661ed9ad51f01dc281e53a56b0.html
		//console.log('ssss', this.layerInfo.data, this.layerInfo.data.url)
		this.layerInfo.show = false;
		console.log('showHighLight', this.layerInfo.data.url)
		return await mapLayers.loadHighLightLayer(this.layerInfo); */
  }
  setGeometriy(entity) {
    //esriGeometryPoint | esriGeometryMultipoint | esriGeometryPolyline | esriGeometryPolygon | esriGeometryEnvelope
    let geometry = [];
    let geometryType = '';
    //console.log(entity)
    let trans = (position) => {
      var ellipsoid = Cesium.Ellipsoid.WGS84;
      var cartographic = ellipsoid.cartesianToCartographic(
        new Cesium.Cartesian3(position.x, position.y, position.z),
      );
      //console.log(position,cartographic)
      return {
        x: Cesium.Math.toDegrees(cartographic.longitude),
        y: Cesium.Math.toDegrees(cartographic.latitude),
      };
    };

    let loop = (entity) => {
      entity = core.entity2Json(entity);
      //点
      if (
        entity.point &&
        (!geometryType || geometryType == 'esriGeometryPoint')
      ) {
        entity.position = trans(entity.position);
        geometryType = 'esriGeometryPoint';
        geometry.push([entity.position.x, entity.position.y]);
      }
      //线
      else if (
        entity.polyline &&
        (!geometryType || geometryType == 'esriGeometryPolyline')
      ) {
        geometryType = 'esriGeometryPolyline';
        geometry.push(
          entity.polyline.positions.map((a) => {
            a = trans(a);
            return [a.x, a.y];
          }),
        );
      }
      //面
      else if (
        entity.polygon &&
        (!geometryType || geometryType == 'esriGeometryPolygon')
      ) {
        geometryType = 'esriGeometryPolygon';
        geometry.push(
          entity.polygon.hierarchy.positions.map((a) => {
            a = trans(a);
            return [a.x, a.y];
          }),
        );
      }
      //矩形
      else if (
        entity.rectangle &&
        (!geometryType || geometryType == 'esriGeometryRectangle')
      ) {
        geometryType = 'esriGeometryRectangle';
        geometry.push({
          xmin: (entity.rectangle.coordinates.west / Math.PI) * 180,
          ymin: (entity.rectangle.coordinates.south / Math.PI) * 180,
          xmax: (entity.rectangle.coordinates.east / Math.PI) * 180,
          ymax: (entity.rectangle.coordinates.north / Math.PI) * 180,
        });
      }
      //圆
      else if (
        entity.ellipse &&
        (!geometryType || geometryType == 'esriGeometryEllipse')
      ) {
        geometryType = 'esriGeometryEllipse';
        //entity.position = trans(entity.position)
        let b = entity.position;
        let a = new Cesium.Cartesian3(
          b.x + entity.ellipse.semiMinorAxis,
          b.y,
          b.z,
        );
        let positions = [];
        [...Array(360)].forEach((_, i) => {
          positions.push(core.rotatedPointByAngle(a, b, i));
        });
        geometry.push(
          positions.map((a) => {
            a = trans(a);
            return [a.x, a.y];
          }),
        );
      }
    };
    if (entity instanceof Array) {
      entity.forEach((a) => {
        loop(a);
      });
    } else if (entity) {
      loop(entity);
    } else {
      this.geometryType = 'esriGeometryEnvelope';
      this.geometry = '';
      return;
    }
    let spatialReference = {
      spatialReference: {
        wkid: 4326,
      },
    };
    if (geometryType == 'esriGeometryRectangle') {
      geometryType = 'esriGeometryEnvelope';
      geometry = geometry.shift();
    } else if (geometryType == 'esriGeometryPoint') {
      if (geometry.length == 1) {
        let p = geometry.shift();
        geometry = {
          x: p[0],
          y: p[1],
          ...spatialReference,
        };
      } else {
        geometryType = 'esriGeometryMultipoint';
        geometry = {
          points: geometry,
          ...spatialReference,
        };
      }
    } else if (geometryType == 'esriGeometryPolyline') {
      geometry = {
        paths: geometry,
        ...spatialReference,
      };
    } else if (geometryType == 'esriGeometryPolygon') {
      geometry = {
        rings: geometry,
        ...spatialReference,
      };
    } else if (geometryType == 'esriGeometryEllipse') {
      geometryType = 'esriGeometryPolygon';
      geometry = {
        rings: geometry,
        ...spatialReference,
      };
    }
    if (geometryType) {
      this.geometryType = geometryType;
      this.geometry = JSON.stringify(geometry);
    }
  }
  setSpatialRel(spatialRel) {
    let spatialRels = {
      intersects: 'esriSpatialRelIntersects',
      contains: 'esriSpatialRelContains',
      crosses: 'esriSpatialRelCrosses',
      'envelope-intersects': 'esriSpatialRelEnvelopeIntersects',
      overlaps: 'esriSpatialRelOverlaps',
      touches: 'esriSpatialRelTouches',
      within: 'esriSpatialRelWithin',
      'disjoint-intersects': 'esriSpatialRelIndexIntersects',
    };
    this.spatialRel = spatialRels[spatialRel] || 'esriSpatialRelIntersects';
  }
}
