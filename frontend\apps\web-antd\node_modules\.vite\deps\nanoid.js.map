{"version": 3, "sources": ["../../../../../node_modules/.pnpm/nanoid@5.1.5/node_modules/nanoid/url-alphabet/index.js", "../../../../../node_modules/.pnpm/nanoid@5.1.5/node_modules/nanoid/index.browser.js"], "sourcesContent": ["export const urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n", "/* @ts-self-types=\"./index.d.ts\" */\nimport { url<PERSON>lphabet as scopedUrlAlphabet } from './url-alphabet/index.js'\nexport { urlAlphabet } from './url-alphabet/index.js'\nexport let random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nexport let customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << Math.log2(alphabet.length - 1)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length >= size) return id\n      }\n    }\n  }\n}\nexport let customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size | 0, random)\nexport let nanoid = (size = 21) => {\n  let id = ''\n  let bytes = crypto.getRandomValues(new Uint8Array((size |= 0)))\n  while (size--) {\n    id += scopedUrlAlphabet[bytes[size] & 63]\n  }\n  return id\n}\n"], "mappings": ";;;AAAO,IAAM,cACX;;;ACEK,IAAI,SAAS,WAAS,OAAO,gBAAgB,IAAI,WAAW,KAAK,CAAC;AAClE,IAAI,eAAe,CAAC,UAAU,aAAa,cAAc;AAC9D,MAAI,QAAQ,KAAK,KAAK,KAAK,SAAS,SAAS,CAAC,KAAK;AACnD,MAAI,OAAO,CAAC,EAAG,MAAM,OAAO,cAAe,SAAS;AACpD,SAAO,CAAC,OAAO,gBAAgB;AAC7B,QAAI,KAAK;AACT,WAAO,MAAM;AACX,UAAI,QAAQ,UAAU,IAAI;AAC1B,UAAI,IAAI,OAAO;AACf,aAAO,KAAK;AACV,cAAM,SAAS,MAAM,CAAC,IAAI,IAAI,KAAK;AACnC,YAAI,GAAG,UAAU,KAAM,QAAO;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACF;AACO,IAAI,iBAAiB,CAAC,UAAU,OAAO,OAC5C,aAAa,UAAU,OAAO,GAAG,MAAM;AAClC,IAAI,SAAS,CAAC,OAAO,OAAO;AACjC,MAAI,KAAK;AACT,MAAI,QAAQ,OAAO,gBAAgB,IAAI,WAAY,QAAQ,CAAE,CAAC;AAC9D,SAAO,QAAQ;AACb,UAAM,YAAkB,MAAM,IAAI,IAAI,EAAE;AAAA,EAC1C;AACA,SAAO;AACT;", "names": []}