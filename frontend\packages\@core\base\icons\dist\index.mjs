import { defineComponent, h } from 'vue';
import { Icon } from '@iconify/vue';
export { Icon as IconifyIcon, addCollection, addIcon, listIcons } from '@iconify/vue';
export { ArrowDown, ArrowLeft, ArrowLeftToLine, ArrowRightLeft, ArrowRightToLine, ArrowUp, ArrowUpToLine, Bell, BookOpenText, Check, ChevronDown, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, CircleHelp, Copy, CornerDownLeft, Ellipsis, Expand, ExternalLink, Eye, EyeOff, FoldHorizontal, Fullscreen, Github, Grip, Menu as IconDefault, Info, InspectionPanel, Languages, LoaderCircle, LockKeyhole, LogOut, MailCheck, Maximize, ArrowRightFromLine as MdiMenuClose, ArrowLeftFromLine as MdiMenuOpen, Menu, Minimize, Minimize2, MoonStar, <PERSON><PERSON>, PanelLeft, PanelRight, Pin, <PERSON>nOff, RotateCw, Search, SearchX, <PERSON><PERSON>s, Shrink, Sun, SunMoon, SwatchBook, UserRoundPen, X } from 'lucide-vue-next';

function createIconifyIcon(icon) {
  return defineComponent({
    name: `Icon-${icon}`,
    setup(props, { attrs }) {
      return () => h(Icon, { icon, ...props, ...attrs });
    }
  });
}

export { createIconifyIcon };
