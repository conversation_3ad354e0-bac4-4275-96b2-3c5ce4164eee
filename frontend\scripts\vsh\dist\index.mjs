import { createJiti } from "../../../node_modules/.pnpm/jiti@2.4.2/node_modules/jiti/lib/jiti.mjs";

const jiti = createJiti(import.meta.url, {
  "interopDefault": true,
  "alias": {
    "@vben/vsh": "E:/work/git/system-manage-fed/frontend/scripts/vsh"
  },
  "transformOptions": {
    "babel": {
      "plugins": []
    }
  }
})

/** @type {import("E:/work/git/system-manage-fed/frontend/scripts/vsh/src/index.js")} */
const _module = await jiti.import("E:/work/git/system-manage-fed/frontend/scripts/vsh/src/index.ts");

export default _module?.default ?? _module;