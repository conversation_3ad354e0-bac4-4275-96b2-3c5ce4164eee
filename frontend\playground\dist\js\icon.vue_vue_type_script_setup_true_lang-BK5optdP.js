import{g as p,I as y}from"./bootstrap-DShsrVit.js";import{a4 as k,J as t,af as n,ag as s,ai as i,ac as o,aq as f,am as b,a3 as c,al as g,a as r,bA as d,b as I,i as B}from"../jse/index-index-BMh_AyeW.js";const C=p("MenuIcon",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),$=["src"],j=k({__name:"icon",props:{fallback:{type:Boolean},icon:{type:[Function,String]}},setup(l){const a=l,m=t(()=>r(a.icon)&&d(a.icon)),u=t(()=>{const{icon:e}=a;return!r(e)&&(I(e)||B(e))});return(e,v)=>u.value?(n(),s(f(e.icon),i(o({key:0},e.$attrs)),null,16)):m.value?(n(),b("img",o({key:1,src:e.icon},e.$attrs),null,16,$)):e.icon?(n(),s(c(y),o({key:2},e.$attrs,{icon:e.icon}),null,16,["icon"])):e.fallback?(n(),s(c(C),i(o({key:3},e.$attrs)),null,16)):g("",!0)}});export{C as M,j as _};
