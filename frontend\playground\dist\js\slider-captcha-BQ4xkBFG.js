import{bz as o,B as f,by as g}from"./bootstrap-DShsrVit.js";import{C as u}from"./index-B_b7xM74.js";import{_ as z}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as N,O as m,af as d,ag as y,ah as l,a3 as s,n as t,ap as c,an as r,am as B,F as S}from"../jse/index-index-BMh_AyeW.js";import{B as $}from"./bell-D0icksiV.js";import{S as j}from"./sun-ChDfqwZ7.js";const V={class:"flex items-center justify-center p-4 px-[30%]"},F={class:"flex items-center justify-center p-4 px-[30%]"},w={class:"flex items-center justify-center p-4 px-[30%]"},E={class:"flex items-center justify-center p-4 px-[30%]"},I={class:"flex items-center justify-center p-4 px-[30%]"},O={class:"flex items-center justify-center p-4 px-[30%]"},K=N({__name:"slider-captcha",setup(T){function a(p){const{time:e}=p;g.success(`校验成功,耗时${e}秒`)}function i(p){p&&p.resume()}const k=m(),x=m(),v=m(),_=m(),C=m(),b=m();return(p,e)=>(d(),y(s(z),{description:"用于前端简单的拖动校验场景",title:"滑块校验"},{default:l(()=>[t(s(u),{class:"mb-5",title:"基础示例"},{default:l(()=>[c("div",V,[t(s(o),{ref_key:"el1",ref:k,onSuccess:a},null,512),t(s(f),{class:"ml-2",type:"primary",onClick:e[0]||(e[0]=n=>i(k.value))},{default:l(()=>e[6]||(e[6]=[r(" 还原 ")])),_:1})])]),_:1}),t(s(u),{class:"mb-5",title:"自定义圆角"},{default:l(()=>[c("div",F,[t(s(o),{ref_key:"el2",ref:x,class:"rounded-full",onSuccess:a},null,512),t(s(f),{class:"ml-2",type:"primary",onClick:e[1]||(e[1]=n=>i(x.value))},{default:l(()=>e[7]||(e[7]=[r(" 还原 ")])),_:1})])]),_:1}),t(s(u),{class:"mb-5",title:"自定义背景色"},{default:l(()=>[c("div",w,[t(s(o),{ref_key:"el3",ref:v,"bar-style":{backgroundColor:"#018ffb"},"success-text":"校验成功",text:"拖动以进行校验",onSuccess:a},null,512),t(s(f),{class:"ml-2",type:"primary",onClick:e[2]||(e[2]=n=>i(v.value))},{default:l(()=>e[8]||(e[8]=[r(" 还原 ")])),_:1})])]),_:1}),t(s(u),{class:"mb-5",title:"自定义拖拽图标"},{default:l(()=>[c("div",E,[t(s(o),{ref_key:"el4",ref:_,onSuccess:a},{actionIcon:l(({isPassing:n})=>[n?(d(),y(s($),{key:0})):(d(),y(s(j),{key:1}))]),_:1},512),t(s(f),{class:"ml-2",type:"primary",onClick:e[3]||(e[3]=n=>i(_.value))},{default:l(()=>e[9]||(e[9]=[r(" 还原 ")])),_:1})])]),_:1}),t(s(u),{class:"mb-5",title:"自定义文本"},{default:l(()=>[c("div",I,[t(s(o),{ref_key:"el5",ref:C,"success-text":"成功",text:"拖动",onSuccess:a},null,512),t(s(f),{class:"ml-2",type:"primary",onClick:e[4]||(e[4]=n=>i(C.value))},{default:l(()=>e[10]||(e[10]=[r(" 还原 ")])),_:1})])]),_:1}),t(s(u),{class:"mb-5",title:"自定义内容(slot)"},{default:l(()=>[c("div",O,[t(s(o),{ref_key:"el6",ref:b,onSuccess:a},{text:l(({isPassing:n})=>[n?(d(),B(S,{key:0},[t(s($),{class:"mr-2 size-4"}),e[11]||(e[11]=r(" 成功 "))],64)):(d(),B(S,{key:1},[e[12]||(e[12]=r(" 拖动 ")),t(s(j),{class:"ml-2 size-4"})],64))]),_:1},512),t(s(f),{class:"ml-2",type:"primary",onClick:e[5]||(e[5]=n=>i(b.value))},{default:l(()=>e[13]||(e[13]=[r(" 还原 ")])),_:1})])]),_:1})]),_:1}))}});export{K as default};
