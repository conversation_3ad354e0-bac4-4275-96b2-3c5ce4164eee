{"name": "@vben/web-antd", "version": "5.4.6", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-antd"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "pinia": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "@ant-design/icons-vue": "^7.0.1", "axios": "1.7.7", "cesium": "1.95.0", "less": "^4.2.0", "mitt": "^3.0.1", "mockjs": "^1.1.0", "proj4": "^2.14.0", "qs": "^6.13.0", "shpjs": "^4.0.4", "vite-plugin-cesium": "^1.2.23", "vite-plugin-static-copy": "^2.2.0", "vue3-colorpicker": "^2.3.0", "lodash-es": "^4.17.21", "p-limit": "^6.1.0", "nanoid": "^5.0.8", "@iconify/vue": "catalog:"}}