import {
  input_default
} from "./chunk-P2FEXGNH.js";
import {
  dynamicApp
} from "./chunk-VRANVM3Q.js";
import {
  VxeUI
} from "./chunk-DULHHPCE.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/input/index.js
var VxeInput = Object.assign(input_default, {
  install(app) {
    app.component(input_default.name, input_default);
  }
});
dynamicApp.use(VxeInput);
VxeUI.component(input_default);
var Input = VxeInput;
var input_default2 = VxeInput;

export {
  VxeInput,
  Input,
  input_default2 as input_default
};
//# sourceMappingURL=chunk-AK4GXAQH.js.map
