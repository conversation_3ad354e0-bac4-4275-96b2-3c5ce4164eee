@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\publint@0.3.12\node_modules\publint\src\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\publint@0.3.12\node_modules\publint\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\publint@0.3.12\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\publint@0.3.12\node_modules\publint\src\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\publint@0.3.12\node_modules\publint\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\publint@0.3.12\node_modules;E:\work\git\system-manage-fed\frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\publint\src\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\publint\src\cli.js" %*
)
