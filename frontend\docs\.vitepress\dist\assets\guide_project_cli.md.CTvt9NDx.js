import{ao as a,k as e,aP as t,l as s,ay as i,j as h}from"./chunks/framework.C8U7mBUf.js";const d=JSON.parse('{"title":"CLI","description":"","frontmatter":{"outline":"deep"},"headers":[],"relativePath":"guide/project/cli.md","filePath":"guide/project/cli.md"}');const o=a({name:"guide/project/cli.md"},[["render",function(a,d,o,l,r,n){const c=i("NolebaseGitContributors"),p=i("NolebaseGitChangelog");return h(),e("div",null,[d[0]||(d[0]=t('<h1 id="cli" tabindex="-1">CLI <a class="header-anchor" href="#cli" aria-label="Permalink to &quot;CLI&quot;">​</a></h1><p>项目中，提供了一些命令行工具，用于一些常用的操作，代码位于 <code>scrips</code> 内。</p><h2 id="vsh" tabindex="-1">vsh <a class="header-anchor" href="#vsh" aria-label="Permalink to &quot;vsh&quot;">​</a></h2><p>用于一些项目操作，如清理项目、检查项目等。</p><h3 id="用法" tabindex="-1">用法 <a class="header-anchor" href="#用法" aria-label="Permalink to &quot;用法&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> [command] [options]</span></span></code></pre></div><h3 id="vsh-check-circular" tabindex="-1">vsh check-circular <a class="header-anchor" href="#vsh-check-circular" aria-label="Permalink to &quot;vsh check-circular&quot;">​</a></h3><p>检查整个项目循环引用，如果有循环引用，会在控制台输出循环引用的模块。</p><h4 id="用法-1" tabindex="-1">用法 <a class="header-anchor" href="#用法-1" aria-label="Permalink to &quot;用法&quot;">​</a></h4><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> check-circular</span></span></code></pre></div><h4 id="选项" tabindex="-1">选项 <a class="header-anchor" href="#选项" aria-label="Permalink to &quot;选项&quot;">​</a></h4><table tabindex="0"><thead><tr><th>选项</th><th>说明</th></tr></thead><tbody><tr><td><code>--staged</code></td><td>只检查git暂存区内的文件,默认<code>false</code></td></tr></tbody></table><h3 id="vsh-check-dep" tabindex="-1">vsh check-dep <a class="header-anchor" href="#vsh-check-dep" aria-label="Permalink to &quot;vsh check-dep&quot;">​</a></h3><p>检查整个项目依赖情况，并在控制台输出<code>未使用的依赖</code>、<code>未安装的依赖</code>信息</p><h4 id="用法-2" tabindex="-1">用法 <a class="header-anchor" href="#用法-2" aria-label="Permalink to &quot;用法&quot;">​</a></h4><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> check-dep</span></span></code></pre></div><h4 id="选项-1" tabindex="-1">选项 <a class="header-anchor" href="#选项-1" aria-label="Permalink to &quot;选项&quot;">​</a></h4><table tabindex="0"><thead><tr><th>选项</th><th>说明</th></tr></thead><tbody><tr><td><code>-r,--recursive</code></td><td>递归删除整个项目,默认<code>true</code></td></tr><tr><td><code>--del-lock</code></td><td>是否删除<code>pnpm-lock.yaml</code>文件,默认<code>true</code></td></tr></tbody></table><h3 id="vsh-lint" tabindex="-1">vsh lint <a class="header-anchor" href="#vsh-lint" aria-label="Permalink to &quot;vsh lint&quot;">​</a></h3><p>对项目进行lint检查，检查项目中的代码是否符合规范。</p><h4 id="用法-3" tabindex="-1">用法 <a class="header-anchor" href="#用法-3" aria-label="Permalink to &quot;用法&quot;">​</a></h4><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> lint</span></span></code></pre></div><h4 id="选项-2" tabindex="-1">选项 <a class="header-anchor" href="#选项-2" aria-label="Permalink to &quot;选项&quot;">​</a></h4><table tabindex="0"><thead><tr><th>选项</th><th>说明</th></tr></thead><tbody><tr><td><code>--format</code></td><td>检查并尝试修复错误,默认<code>false</code></td></tr></tbody></table><h3 id="vsh-publint" tabindex="-1">vsh publint <a class="header-anchor" href="#vsh-publint" aria-label="Permalink to &quot;vsh publint&quot;">​</a></h3><p>对 <code>Monorepo</code> 项目进行包规范检查，检查项目中的包是否符合规范。</p><h4 id="用法-4" tabindex="-1">用法 <a class="header-anchor" href="#用法-4" aria-label="Permalink to &quot;用法&quot;">​</a></h4><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> publint</span></span></code></pre></div><h4 id="选项-3" tabindex="-1">选项 <a class="header-anchor" href="#选项-3" aria-label="Permalink to &quot;选项&quot;">​</a></h4><table tabindex="0"><thead><tr><th>选项</th><th>说明</th></tr></thead><tbody><tr><td><code>--check</code></td><td>仅执行检查,默认<code>false</code></td></tr></tbody></table><h3 id="vsh-code-workspace" tabindex="-1">vsh code-workspace <a class="header-anchor" href="#vsh-code-workspace" aria-label="Permalink to &quot;vsh code-workspace&quot;">​</a></h3><p>生成 <code>vben-admin.code-workspace</code> 文件，目前不需要手动执行，会在代码提交时自动执行。</p><h4 id="用法-5" tabindex="-1">用法 <a class="header-anchor" href="#用法-5" aria-label="Permalink to &quot;用法&quot;">​</a></h4><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> code-workspace</span></span></code></pre></div><h4 id="选项-4" tabindex="-1">选项 <a class="header-anchor" href="#选项-4" aria-label="Permalink to &quot;选项&quot;">​</a></h4><table tabindex="0"><thead><tr><th>选项</th><th>说明</th></tr></thead><tbody><tr><td><code>--auto-commit</code></td><td><code>git commit</code>时候，自动提交,默认<code>false</code></td></tr><tr><td><code>--spaces</code></td><td>缩进格式,默认 <code>2</code>个缩进</td></tr></tbody></table><h2 id="turbo-run" tabindex="-1">turbo-run <a class="header-anchor" href="#turbo-run" aria-label="Permalink to &quot;turbo-run&quot;">​</a></h2><p>用于快速执行大仓中脚本，并提供选项式交互选择。</p><h3 id="用法-6" tabindex="-1">用法 <a class="header-anchor" href="#用法-6" aria-label="Permalink to &quot;用法&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> turbo-run</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;"> [command]</span></span></code></pre></div><h3 id="turbo-run-dev" tabindex="-1">turbo-run dev <a class="header-anchor" href="#turbo-run-dev" aria-label="Permalink to &quot;turbo-run dev&quot;">​</a></h3><p>快速执行<code>dev</code>命令，并提供选项式交互选择。</p>',42)),s(c),s(p)])}]]);export{d as __pageData,o as default};
