<script lang="ts"  setup>
import { computed, onMounted, ref } from "vue";
import { useRouter } from 'vue-router';
import { useVbenVxeGrid } from "#/adapter/vxe-table";
import {columns, layerQueryColumns} from '#/views/dataView/query.data';
import {query, queryLayers} from "#/views/dataView/view.api";

import Box from '#/components/JS/box';
import { capp } from '#/utils/app.js';
import { getKeysFromTreeNodes } from '#/utils/utils';

import compass from '../../../components/Compass.vue';
import distanceLegend from '../../../components/DistanceLegend.vue';
import location from '../../../components/Location.vue';
import MapTools from '../../../components/mapTools.vue';
import ztu from '../../../ztu';
import MapViewer from '../components/MapViewer.vue';
import { gereleaseLayer, getLayers } from "#/views/dataView/view.api.ts";
import {showLoading, showWarn} from "#/utils/toast.js";
import { Page } from "@vben/common-ui";
import { MenuFold, SplitScreen, AreaSelect, ClearIcon } from "@vben/icons";
import { onStartSpaceQuery } from '#/utils/cesium/mapTools';
import { shapeViewer } from '#/utils/cesium/layers/ShapeViewer';
import * as Cesium from "cesium";
import eb from "#/ztu/eventbus.js";
import {getCoordinatesParams} from "#/utils/cesium/space/query.js";
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@vben/common-ui';
const theme = ref({
  token: {
    colorPrimary: '#1890ff', // 默认主题颜色
  },
});

const mapInit = ref(false);
let GISLayers = null;
const treeData = ref([
  {
    title: '影像数据',
    key: 'scene',
    checkable: false,
    layerType:3,
    index:0,
    children: [
      {
        title: 'DOM',
        key: 'dom',
        checkable: false,
        index:0,
        layerType:3,
        children: [
        ],
      },
      {
        title: 'DEM',
        key: 'dem',
        checkable: false,
        index:1,
        layerType:3,
        children: [
        ],
      },
      {
        title: 'DSM',
        key: 'dsm',
        checkable: false,
        index:2,
        layerType:3,
        children: [
        ],
      },
    ],
  },
  {
    title: '三维数据',
    checkable: false,
    key: 'entity',
    layerType:4,
    index:1,
    children: [
      {
        title: 'LOD',
        key: 'lod',
        checkable: false,
        index:0,
        layerType:4,
        children: [
        ],
      },
      {
        title: 'MESH',
        key: 'mesh',
        checkable: false,
        index:1,
        layerType:4,
        children: [
        ],
      },
      {
        title: '点云',
        key: 'point-cloud',
        checkable: false,
        index:1,
        layerType:4,
        children: [
        ],
      },
    ],
  },
  {
    title: '专题数据',
    checkable: false,
    key: 'special',
    layerType:5,
    index:1,
    children: [
    ],
  },
  {
    title: '底图',
    checkable: false,
    key: 'baseMap',
    children: [],
  },
]);
const checkedKeys = ref([]);
const checkOptLayers = ref([]); //选中的逻辑图层
const expandedKeys = ref([]);
const searchValue = ref('');
const maxHeight = ref('');
const router = useRouter();
const showLayerList = ref(true);
const visibleLayers = ref([]);
const activeTab = ref('layer');
const drawMode = ref(false);
let lastLayerQueryParams = null;

const gridOptions = {
  columns: layerQueryColumns,
  checkboxConfig: {
    highlight: true,
    labelField: 'layerName',
    checkRowKeys: checkedKeys.value
  },
  layerQueryColumns,
  showHeader: false, // 这里设置隐藏表头
  pagerConfig: { enabled: true, layouts: ['PrevPage', 'JumpNumber', 'NextPage', 'FullJump'] },
  height: '600px',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        // if (!drawMode.value) {
        //   return {
        //     total: 0,
        //     items: [],
        //   };
        // }
        let paramDatas = lastLayerQueryParams;
        if(drawMode.value) {
          paramDatas = getCoordinatesParams(shapeViewer);
          lastLayerQueryParams = paramDatas;
        }

        if (!paramDatas.coordinates || paramDatas.coordinates.length === 0) {
          return {
            total: 0,
            items: [],
          };
        }
        const params =
          { page: {
              current: page.currentPage,
              size: page.pageSize,
              searchCount: true
            },
            condition:{
              coordinates: paramDatas.coordinates
            }
        };
        const data = await queryLayers(params);
        let total = data ?.total;
        let records = data.records;
        return {
          total: total,
          items: records,
        };
      },
    },
  },
  rowConfig: {
    isHover: true,
    keyField: 'layerId' // 指定主键字段，必须和数据的 key 对应
  },

};


const gridEvents = {
  // 监听单个复选框选中事件
  async checkboxChange( { row, checked }) {
    console.log('选中状态改变:', row, '是否选中:', checked);
    row.key = row.layerId;
    if (checked) {
      checkOptLayers.value.push(row);
      if(checkedKeys.value) {
        if(checkedKeys.value.checked) {
          checkedKeys.value.checked.push(row.key);
        }else {
          checkedKeys.value.push(row.key);
        }
      }

      await getLayerAndShow(row);
      return;
    }
    if(checkedKeys.value) {
      let checked = checkedKeys.value.checked ? checkedKeys.value.checked : checkedKeys.value;
      let index = checked.indexOf(row.key);
      if (index > -1) {
        checked.splice(index, 1); // 从index位置开始删除1个元素
      }
    }

    removeLayer(checkOptLayers.value,row);
    GISLayers.unLoadLayer(row.key);
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  gridEvents,
  gridClass: 'custom-table-class'
});

const viewLayer = (row) => {
  // 实现查看图层的逻辑
  if (row.layerId) {
    GISLayers.showAndZoomToLayer(row.layerId, row);
  }
};

// 空间查询相关方法
const startDraw = () => {
  clearDraw();
  drawMode.value = true;
  shapeViewer.add('polygon');
};

const clearDraw = () => {
  eb.emit('captureEnd',{target:'tool'});
  eb.emit('clearAll',{target:'tool'});
  drawMode.value = false;
};

const queryByPoint = () => {
  if(!drawMode.value) {
    showWarn('请先绘制查询区域！');
    return;
  }

  // 实现点查询逻辑
  gridApi.reload();

  ressetQueryLayerCheck(true);
  // clearDraw();
};

const ressetQueryLayerCheck = () =>{
  if(!checkedKeys.value && !checkedKeys.value.checked) {
    return;
  }
  setTimeout(()=>{
    if(!gridApi || !gridApi.grid) {
      return;
    }
    gridApi.grid.setAllCheckboxRow && gridApi.grid.setAllCheckboxRow(false);
    let checked = checkedKeys.value.checked ? checkedKeys.value.checked : checkedKeys.value;
    gridApi.grid.setCheckboxRowKey && gridApi.grid.setCheckboxRowKey(checked, true);
  },100);
}

const onLayerCheck = (Keys) => {
  checkedKeys.value = Keys;
  visibleLayers.value = Keys;
};

const changeFold = () => {
  showLayerList.value = !showLayerList.value;
}

const viewMapLoaded = async (value) => {
  mapInit.value = true;
  GISLayers = ztu.global.GISLayers;
  let baseLayerDatas = GISLayers.getTreeData();
  let baseLayer = treeData.value[treeData.value.length -1];
  baseLayer.children = baseLayer.children.concat(baseLayerDatas[0].children);
  checkedKeys.value = capp.chkLayers;
};

const search = () => {
  if (!searchValue.value) return;
  const keys = [];
  const data = treeData.value;
  const loop = (node) => {
    let result = false;
    (node.children || []).forEach((child, index) => {
      if (child.title && child.title.includes(searchValue.value)) {
        result = true;
        keys.push(child.key);
        if (!keys.includes(node.key)) keys.push(node.key);
      }
      if (loop(child) && !result) {
        result = true;
        if (!keys.includes(node.key)) keys.push(node.key);
      }
    });
    return result;
  };
  data.forEach((node) => loop(node));
  expandedKeys.value = keys;
};


// 动态加载子节点数据f
const loadData = async (treeNode) => {
  const {index, key,title,parent,layerType } = treeNode;
  // 判断是否是最后一级节点
  if (treeNode.children && treeNode.children.length === 0) {
    let type = layerType;
    let collectionId = treeNode.collectionId;
    let category = key === 'special' ? 'all' : key;
    const children = await fetchLayerData(type,category,collectionId); // 模拟异步加载数据
    let datas = children.map((child) => {
      // 判断是否是最后一级节点
      const isLeaf = collectionId != null;
      return {
        ...child,
        checkable: child.isLeaf, // 只有最后一级节点设置为可选
      };
    });
    if(datas && datas.length > 0) {
      treeNode.children = treeNode.children.concat(datas);
      treeNode.dataRef.children = datas;
    }
  }
};

// 模拟异步请求数据
const fetchLayerData = async (type,title,collectionId) => {
  let listDatas = await getLayers(type,title,collectionId);
  let result = [];
  listDatas && listDatas.forEach((item) => {
    if(collectionId != null && collectionId.length > 0) {
      //图层
      result.push({
        ...item,
        title: item.layerName,
        key: item.layerId,
        layerType:type,
        isLeaf: true,
      });
    }else {
      result.push({
        ...item,
        title: item.collectionName,
        key: item.collectionId,
        layerType:type,
        isLeaf: false,
        children:[]
      });
    }

  })
  return result;
};

const addAndShowLaner = async (data) =>{
  await capp.addLayerDatas([{
    id:data.layerId,
    name:data.title,
    type:data.mapServerType,
    url:data.layerUrl,
  }]);
  GISLayers.showAndZoomToLayer(data.key,data);
}

const getLayerAndShow = async (data) => {
    //未加载图层
    let releaseStatus = data.releaseStatus;
    if(!releaseStatus) {
      //发布
      const hide = showLoading('图层发布中...');
      let optData = await gereleaseLayer(data.dataType,data.layerId);
      if(optData && optData.layerId) {
        data.releaseStatus = 1;
        data.layerUrl = optData.layerUrl;
        data.layerId = optData.layerId;
        data.mapServerType = optData.mapServerType;
        data.url = optData.layerUrl;
        data.mapServerBox = optData.mapServerBox;
        hide();
        await addAndShowLaner(data);
      }
      return;
    }

    await addAndShowLaner(data);
}

const removeLayer = (list,item) => {
   for(var i = 0,n = 0;i < list.length; i++) {
       if(list[i].layerId != item.layerId) {
           list[n++] = list[i];
       }
   }
   if(list.length > 0) {
     list.length -= 1;
   }
};

const layerCheckedChange = async (
  checkedKeys,
  { checked, checkedNodes, node, event },
)  => {
  capp.chkLayers = checkedKeys;
  if (checked) {
    if(node.data && node.data.type) {
      //底图
      GISLayers.showAndZoomToLayer(node.key,node)
      return;
    }

    checkOptLayers.value.push(node.dataRef);
    await getLayerAndShow(node.dataRef);
    ressetQueryLayerCheck();
    return;
  }
  removeLayer(checkOptLayers.value,node.dataRef);
  GISLayers.unLoadLayer(node.key);
  ressetQueryLayerCheck();
};

const onDrop = (info) => {
  const dropKey = info.node.key;
  const dragKey = info.dragNode.key;
  const dropPos = info.node.pos.split('-');
  const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

  const loop = (data, key, callback) => {
    data.forEach((item, index) => {
      if (item.key === key) {
        return callback(item, index, data);
      }
      if (item.children) {
        return loop(item.children, key, callback);
      }
    });
  };
  const data = JSON.parse(JSON.stringify(treeData.value)); // Find dragObject
  let dragObj;
  let dragObjBrother;
  let dropObjBrother;
  loop(data, dragKey, (item, index, arr) => {
    dragObjBrother = arr;
    arr.splice(index, 1);
    dragObj = item;
  });

  if (!info.dropToGap) {
    // Drop on the content
    loop(data, dropKey, (item) => {
      item.children = item.children || []; // / where to insert 示例添加到头部，可以是随意位置
      item.children.unshift(dragObj);
      dropObjBrother = item.children;
    });
  } else if (
    (info.node.children || []).length > 0 && // Has children
    info.node.expanded && // Is expanded
    dropPosition === 1 // On the bottom gap
  ) {
    loop(data, dropKey, (item) => {
      item.children = item.children || []; // where to insert 示例添加到头部，可以是随意位置
      item.children.unshift(dragObj);
      dropObjBrother = item.children;
    });
  } else {
    let ar = [];
    let i = 0;
    loop(data, dropKey, (_item, index, arr) => {
      ar = arr;
      i = index;
    });
    dropObjBrother = ar;
    if (dropPosition === -1) {
      ar.splice(i, 0, dragObj);
    } else {
      ar.splice(i + 1, 0, dragObj);
    }
  }
  if (dragObjBrother == dropObjBrother) {
    const keys = getKeysFromTreeNodes(data);
    GISLayers.sortByKeys(keys);
    treeData.value = data;
  } else {
    // Box.info('图层调序', '图层不可跨级调序')
  }
};

const reLoading = () => {
  Box.confirm(
    '刷新图层数据',
    '确定要刷新图层数据(注:将清空所有图层数据)?',
    () => {
      capp.chkLayers = [];
      checkedKeys.value = capp.chkLayers;
      GISLayers.reload();
      GISLayers.show(capp.chkLayers);
    },
  );
};

onMounted(async () => {
  // 计算图层树框高度
  maxHeight.value = `${document.documentElement.clientHeight - 34}px`;
  window.addEventListener(
    'resize',
    () => {
      setTimeout(() => {
        maxHeight.value = `${document.documentElement.clientHeight - 34}px`;
      }, 300);
    },
    false,
  );
});

let layerPropertyBox = null;
/**
 *@description 图层树
 */
// 配置图层透明度
function showProps(key) {
  const props = GISLayers.getLayerProperty(key);
  if (!props) return;
  layerPropertyBox = Box.open(
    {
      title: '图层配置',
      style: {
        left: 'unset',
        right: '100px',
        top: '150px',
        width: '300px',
      },
      beforeClose: () => {
        layerPropertyBox = null;
      },
      // style:{
      //   width:'300px'
      // }
    },
    h(layerPropsSetting, {
      ...props,
      save: (options) => {
        GISLayers.setLayerProperty(key, options);
      },
    }),
    layerPropertyBox,
  );
}
const handleNodeClick = (event, node) => {
  // 切换展开/折叠状态
  const index = expandedKeys.value.indexOf(node.key);
  if (index > -1) {
    // 如果节点已经展开，则折叠
    expandedKeys.value.splice(index, 1);
  } else {
    // 如果节点没有展开，则展开
    expandedKeys.value.push(node.key);
  }
}

</script>

<template>
  <MenuFold class="size-8 foldMenu" @click="changeFold"/>
  <a-card v-if="showLayerList" class="layerTree" :bodyStyle="{ padding: '0px' }" >
    <Tabs v-model="activeTab" class="tabClass" >
      <TabsList>
        <TabsTrigger value="layer">图层列表</TabsTrigger>
        <TabsTrigger value="query">图层查询</TabsTrigger>
      </TabsList>
      <TabsContent value="layer">
        <div class="layerContent">
          <a-tree
            v-model:checked-keys="checkedKeys"
            v-model:expanded-keys="expandedKeys"
            :selectable="false"
            :tree-data="treeData"
            :checkable="true"
            checkStrictly
            block-node
            checkable
            virtual
            default-expand-all
            :load-data="loadData"
            @check="layerCheckedChange"
            @drop="onDrop"
            @click="handleNodeClick"
          >
            <template #title="{ title, key, data }">
              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  user-select: none;
                "
              >
                <span
                  :style="{
                    color:
                      searchValue && title.indexOf(searchValue) > -1
                        ? 'red'
                        : 'unset',
                  }"
                  >{{ title }}</span
                >
                <span style="cursor: pointer" @click.stop="showProps(key)">
                  <span
                    v-if="
                      data.data && data.data.type && data.data.type != 'terrain'
                    "
                    class="iconfont icon-toumingdu1"
                    style="font-size: 18px; color: #fff"
                  ></span>
                </span>
              </div>
            </template>
          </a-tree>
          <div style="height: 100px;"></div>
        </div>
      </TabsContent>
      <TabsContent value="query">
        <div class="query-content">
          <div class="query-tools">
            <a-button  @click="startDraw">
              绘制范围
            </a-button>
            <a-button  @click="clearDraw">
              清除选区
            </a-button>
            <a-button @click="queryByPoint">
              查询
            </a-button>
          </div>
          <div class="query-result">
            <Grid>
            </Grid>
          </div>
        </div>
      </TabsContent>
    </Tabs>
  </a-card>
  <MapViewer :visible-layers="visibleLayers" @view-map-loaded="viewMapLoaded" />
  <div
    v-if="mapInit"
    style="
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 1;
      width: 100%;
      height: 30px;
      background: rgb(40 44 52 / 70%);
    "
  >
    <div
      style="
        display: flex;
        align-items: center;
        align-items: stretch;
        height: 30px;
        margin-left: 50px;
        border-top: 1px solid rgb(40 44 52 / 80%);
      "
    >
      <!-- 左下角比例尺 -->
      <distanceLegend style="bottom: 0; padding-top: 2px; margin-left: 20px" />
      <!-- 坐标拾取器 -->
      <location style="bottom: 0; left: 200px" />
      <!-- <div style="color:#fff;font-size: 14px;line-height: 30px;position: absolute;right:20px;">QQ:123456789</div> -->
    </div>
  </div>

  <MapTools :checkOptLayers="checkOptLayers" />
  <!-- 指北针 -->
  <compass style="top: 110px; right: 20px; bottom: unset; left: unset" />
</template>

<style lang="less" scoped>
@import '#/styles/dark-antd.less';
.foldMenu {
  position: absolute;
  top: 12px;
  left: 13px;
  z-index: 101;
  border-width: 0px;
  border-radius: 5px;
}

.layerTree {
  width: 350px;
  margin:10px;
  z-index: 1;
  position: absolute;
  overflow: hidden;
  height: calc(100vh - 20px);


  .query-content {
    padding: 16px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .query-tools {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
      justify-content: center;
      align-items: center;
      width: 100%;

      :deep(.ant-btn) {
        min-width: 80px;
        background: hsl(var(--secondary));
        border-color: hsl(var(--border));
        color: hsl(var(--secondary-foreground));

        &:hover {
          background: hsl(var(--accent));
          border-color: hsl(var(--accent-hover));
          color: hsl(var(--accent-foreground));
        }
      }
    }

    .query-result {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      :deep(.vxe-grid) {
        flex: 1;
        height: 100%;
      }
    }
  }

  .layerIcon {
    position: absolute;
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-font {
    }
  }

  .reload {
    position: absolute;
    height: 30px;
    width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    right: 20px;
    margin-top: 10px;
  }

  .layerHeader {
    width: 100px;
    height: 50px;
    line-height: 50px;
    text-align: center;

    .icon-font {
      position: absolute;
      left: 10px;
      top: 8px;
    }

    div {
      padding: 8px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      line-height: 32px;
      height: 50px;
      letter-spacing: 3px;
      opacity: 1;
    }
  }

  .inputSty {
    padding: 10px 10px;

    .ant-input-affix-wrapper {
      border: none;
      border-radius: 4px;

      .ant-input {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
      }

    }
  }

  :deep(.layerContent) {
    max-height: 833px !important;
    overflow-y: auto;
    padding: 10px 30px 10px 30px;
    height: 100%;
    font-size: 14px;
    font-weight: 400;
    line-height: 19px;


  }

  .tool {
    right: 0px;
  }

  .setting {
    display: flex;
    align-items: top;
    justify-content: space-between;
  }


}

.tabClass {
  width: 97%;
  margin-top: 0px;
  background: transparent;
}

/* 标签列表样式 */
:deep([role="tablist"]) {
  background: transparent;
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
  border-bottom: 1px solid hsl(var(--border));
}

/* 标签按钮基础样式 */
:deep([role="tab"]) {
  position: relative;
  margin: 0 32px;
  padding: 8px 0;
}



/* 图层树样式 */
.layerTree {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
}

.layerContent {
  color: hsl(var(--foreground));
}

/* 查询工具按钮样式 */
.query-tools {
  :deep(.ant-btn) {
    background: hsl(var(--secondary));
    border-color: hsl(var(--border));
    color: hsl(var(--secondary-foreground));

    &:hover {
      background: hsl(var(--accent));
      border-color: hsl(var(--accent-hover));
      color: hsl(var(--accent-foreground));
    }
  }
}


</style>
