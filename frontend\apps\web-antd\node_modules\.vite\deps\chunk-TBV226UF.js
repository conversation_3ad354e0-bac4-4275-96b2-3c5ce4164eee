import {
  column_default
} from "./chunk-UVVQ2N3A.js";
import {
  VxeUI
} from "./chunk-DULHHPCE.js";

// ../../node_modules/.pnpm/vxe-table@4.13.16_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-table/es/column/index.js
var VxeColumn = Object.assign({}, column_default, {
  install(app) {
    app.component(column_default.name, column_default);
    app.component("VxeTableColumn", column_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(column_default.name, column_default);
  VxeUI.dynamicApp.component("VxeTableColumn", column_default);
}
VxeUI.component(column_default);
var Column = VxeColumn;
var column_default2 = VxeColumn;

export {
  VxeColumn,
  Column,
  column_default2 as column_default
};
//# sourceMappingURL=chunk-TBV226UF.js.map
