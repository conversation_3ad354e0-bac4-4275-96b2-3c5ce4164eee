import{x as U,l as H,u as L}from"./bootstrap-5OPUVRWy.js";import{_ as S}from"./analytics-visits-source.vue_vue_type_script_setup_true_lang-CDxpLUXm.js";import{_ as B,a as T}from"./icon.vue_vue_type_script_setup_true_lang-CgLK7NiC.js";import{d as p,a as c,b as r,h,k as t,J as C,g as _,j as v,q as o,s as l,v as m,t as i,e as z,l as M,F as j,D as N,f as a,r as A,z as q,N as D}from"../jse/index-index-DyHD_jbN.js";import"./use-echarts-CfT8MSbz.js";const b=p({__name:"Card",props:{class:{}},setup(d){const e=d;return(n,s)=>(r(),c("div",{class:h(t(C)("bg-card text-card-foreground border-border rounded-xl border",e.class))},[_(n.$slots,"default")],2))}}),$=p({__name:"CardContent",props:{class:{}},setup(d){const e=d;return(n,s)=>(r(),c("div",{class:h(t(C)("p-6 pt-0",e.class))},[_(n.$slots,"default")],2))}}),k=p({__name:"CardHeader",props:{class:{}},setup(d){const e=d;return(n,s)=>(r(),c("div",{class:h(t(C)("flex flex-col gap-y-1.5 p-5",e.class))},[_(n.$slots,"default")],2))}}),y=p({__name:"CardTitle",props:{class:{}},setup(d){const e=d;return(n,s)=>(r(),c("h3",{class:h(t(C)("font-semibold leading-none tracking-tight",e.class))},[_(n.$slots,"default")],2))}}),E=p({name:"AnalysisChartCard",__name:"analysis-chart-card",props:{title:{}},setup(d){return(e,n)=>(r(),v(t(b),null,{default:o(()=>[l(t(k),null,{default:o(()=>[l(t(y),{class:"text-xl"},{default:o(()=>[m(i(e.title),1)]),_:1})]),_:1}),l(t($),null,{default:o(()=>[_(e.$slots,"default")]),_:3})]),_:3}))}}),G={class:"card-box p-4 py-6 lg:flex"},J={key:0,class:"flex flex-col justify-center md:ml-6 md:mt-0"},R={key:0,class:"text-md font-semibold md:text-xl"},F={key:1,class:"text-foreground/80 mt-1"},P=p({name:"WorkbenchHeader",__name:"workbench-header",props:{avatar:{default:""}},setup(d){return(e,n)=>(r(),c("div",G,[l(t(B),{src:e.avatar,class:"size-20"},null,8,["src"]),e.$slots.title||e.$slots.description?(r(),c("div",J,[e.$slots.title?(r(),c("h1",R,[_(e.$slots,"title")])):z("",!0),e.$slots.description?(r(),c("span",F,[_(e.$slots,"description")])):z("",!0)])):z("",!0),n[0]||(n[0]=M('<div class="mt-4 flex flex-1 justify-end md:mt-0"><div class="flex flex-col justify-center text-right"><span class="text-foreground/80"> 待办 </span><span class="text-2xl">2/10</span></div><div class="mx-12 flex flex-col justify-center text-right md:mx-16"><span class="text-foreground/80"> 项目 </span><span class="text-2xl">8</span></div><div class="mr-4 flex flex-col justify-center text-right md:mr-10"><span class="text-foreground/80"> 团队 </span><span class="text-2xl">300</span></div></div>',1))]))}}),Q={class:"flex items-center"},K={class:"ml-4 text-lg font-medium"},O={class:"text-foreground/80 mt-4 flex h-10"},X={class:"text-foreground/80 flex justify-between"},Y=p({name:"WorkbenchProject",__name:"workbench-project",props:{items:{default:()=>[]},title:{}},emits:["click"],setup(d){return(e,n)=>(r(),v(t(b),null,{default:o(()=>[l(t(k),{class:"py-4"},{default:o(()=>[l(t(y),{class:"text-lg"},{default:o(()=>[m(i(e.title),1)]),_:1})]),_:1}),l(t($),{class:"flex flex-wrap p-0"},{default:o(()=>[(r(!0),c(j,null,N(e.items,(s,u)=>(r(),c("div",{key:s.title,class:h([{"border-r-0":u%3===2,"border-b-0":u<3,"pb-4":u>2},"border-border group w-full cursor-pointer border-b border-r border-t p-4 transition-all hover:shadow-xl md:w-1/2 lg:w-1/3"])},[a("div",Q,[l(t(T),{color:s.color,icon:s.icon,class:"size-8 transition-all duration-300 group-hover:scale-110",onClick:V=>e.$emit("click",s)},null,8,["color","icon","onClick"]),a("span",K,i(s.title),1)]),a("div",O,i(s.content),1),a("div",X,[a("span",null,i(s.group),1),a("span",null,i(s.date),1)])],2))),128))]),_:1})]),_:1}))}}),Z=["onClick"],tt={class:"text-md mt-2 truncate"},et=p({name:"WorkbenchQuickNav",__name:"workbench-quick-nav",props:{items:{default:()=>[]},title:{}},emits:["click"],setup(d){return(e,n)=>(r(),v(t(b),null,{default:o(()=>[l(t(k),{class:"py-4"},{default:o(()=>[l(t(y),{class:"text-lg"},{default:o(()=>[m(i(e.title),1)]),_:1})]),_:1}),l(t($),{class:"flex flex-wrap p-0"},{default:o(()=>[(r(!0),c(j,null,N(e.items,(s,u)=>(r(),c("div",{key:s.title,class:h([{"border-r-0":u%3===2,"pb-4":u>2,"border-b-0":u<3},"flex-col-center border-border group w-1/3 cursor-pointer border-b border-r border-t py-8 hover:shadow-xl"]),onClick:V=>e.$emit("click",s)},[l(t(T),{color:s.color,icon:s.icon,class:"size-7 transition-all duration-300 group-hover:scale-125"},null,8,["color","icon"]),a("span",tt,i(s.title),1)],10,Z))),128))]),_:1})]),_:1}))}}),st={class:"divide-border w-full divide-y",role:"list"},lt={class:"flex min-w-0 items-center gap-x-4"},at={class:"min-w-0 flex-auto"},ot={class:"text-foreground text-sm font-semibold leading-6"},rt=["innerHTML"],nt={class:"hidden h-full shrink-0 sm:flex sm:flex-col sm:items-end"},ct={class:"text-foreground/80 mt-6 text-xs leading-6"},it=p({name:"WorkbenchTodo",__name:"workbench-todo",props:{items:{default:()=>[]},title:{}},setup(d){return(e,n)=>(r(),v(t(b),null,{default:o(()=>[l(t(k),{class:"py-4"},{default:o(()=>[l(t(y),{class:"text-lg"},{default:o(()=>[m(i(e.title),1)]),_:1})]),_:1}),l(t($),{class:"flex flex-wrap p-5 pt-0"},{default:o(()=>[a("ul",st,[(r(!0),c(j,null,N(e.items,s=>(r(),c("li",{key:s.title,class:h([{"select-none line-through opacity-60":s.completed},"flex cursor-pointer justify-between gap-x-6 py-5"])},[a("div",lt,[l(t(U),{checked:s.completed,"onUpdate:checked":u=>s.completed=u,name:"completed"},null,8,["checked","onUpdate:checked"]),a("div",at,[a("p",ot,i(s.title),1),a("p",{class:"text-foreground/80 *:text-primary mt-1 truncate text-xs leading-5",innerHTML:s.content},null,8,rt)])]),a("div",nt,[a("span",ct,i(s.date),1)])],2))),128))])]),_:1})]),_:1}))}}),dt={class:"divide-border w-full divide-y",role:"list"},ut={class:"flex min-w-0 items-center gap-x-4"},pt={class:"min-w-0 flex-auto"},ft={class:"text-foreground text-sm font-semibold leading-6"},_t=["innerHTML"],mt={class:"hidden h-full shrink-0 sm:flex sm:flex-col sm:items-end"},ht={class:"text-foreground/80 mt-6 text-xs leading-6"},gt=p({name:"WorkbenchTrends",__name:"workbench-trends",props:{items:{default:()=>[]},title:{}},setup(d){return(e,n)=>(r(),v(t(b),null,{default:o(()=>[l(t(k),{class:"py-4"},{default:o(()=>[l(t(y),{class:"text-lg"},{default:o(()=>[m(i(e.title),1)]),_:1})]),_:1}),l(t($),{class:"flex flex-wrap p-5 pt-0"},{default:o(()=>[a("ul",dt,[(r(!0),c(j,null,N(e.items,s=>(r(),c("li",{key:s.title,class:"flex justify-between gap-x-6 py-5"},[a("div",ut,[l(t(T),{icon:s.avatar,alt:"",class:"size-10 flex-none rounded-full"},null,8,["icon"]),a("div",pt,[a("p",ft,i(s.title),1),a("p",{class:"text-foreground/80 *:text-primary mt-1 truncate text-xs leading-5",innerHTML:s.content},null,8,_t)])]),a("div",mt,[a("span",ht,i(s.date),1)])]))),128))])]),_:1})]),_:1}))}}),xt={class:"p-5"},vt={class:"mt-5 flex flex-col lg:flex-row"},bt={class:"mr-4 w-full lg:w-3/5"},$t={class:"w-full lg:w-2/5"},Nt=p({__name:"index",setup(d){const e=H(),n=[{color:"",content:"不要等待机会，而要创造机会。",date:"2021-04-01",group:"开源组",icon:"carbon:logo-github",title:"Github",url:"https://github.com"},{color:"#3fb27f",content:"现在的你决定将来的你。",date:"2021-04-01",group:"算法组",icon:"ion:logo-vue",title:"Vue",url:"https://vuejs.org"},{color:"#e18525",content:"没有什么才能比努力更重要。",date:"2021-04-01",group:"上班摸鱼",icon:"ion:logo-html5",title:"Html5",url:"https://developer.mozilla.org/zh-CN/docs/Web/HTML"},{color:"#bf0c2c",content:"热情和欲望可以突破一切难关。",date:"2021-04-01",group:"UI",icon:"ion:logo-angular",title:"Angular",url:"https://angular.io"},{color:"#00d8ff",content:"健康的身体是实现目标的基石。",date:"2021-04-01",group:"技术牛",icon:"bx:bxl-react",title:"React",url:"https://reactjs.org"},{color:"#EBD94E",content:"路是走出来的，而不是空想出来的。",date:"2021-04-01",group:"架构组",icon:"ion:logo-javascript",title:"Js",url:"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript"}],s=[{color:"#1fdaca",icon:"ion:home-outline",title:"首页",url:"/"},{color:"#bf0c2c",icon:"ion:grid-outline",title:"仪表盘",url:"/dashboard"},{color:"#e18525",icon:"ion:layers-outline",title:"组件",url:"/demos/features/icons"},{color:"#3fb27f",icon:"ion:settings-outline",title:"系统管理",url:"/demos/features/login-expired"},{color:"#4daf1bc9",icon:"ion:key-outline",title:"权限管理",url:"/demos/access/page-control"},{color:"#00d8ff",icon:"ion:bar-chart-outline",title:"图表",url:"/analytics"}],u=A([{completed:!1,content:"审查最近提交到Git仓库的前端代码，确保代码质量和规范。",date:"2024-07-30 11:00:00",title:"审查前端代码提交"},{completed:!0,content:"检查并优化系统性能，降低CPU使用率。",date:"2024-07-30 11:00:00",title:"系统性能优化"},{completed:!1,content:"进行系统安全检查，确保没有安全漏洞或未授权的访问。 ",date:"2024-07-30 11:00:00",title:"安全检查"},{completed:!1,content:"更新项目中的所有npm依赖包，确保使用最新版本。",date:"2024-07-30 11:00:00",title:"更新项目依赖"},{completed:!1,content:"修复用户报告的页面UI显示问题，确保在不同浏览器中显示一致。 ",date:"2024-07-30 11:00:00",title:"修复UI显示问题"}]),V=[{avatar:"svg:avatar-1",content:"在 <a>开源组</a> 创建了项目 <a>Vue</a>",date:"刚刚",title:"威廉"},{avatar:"svg:avatar-2",content:"关注了 <a>威廉</a> ",date:"1个小时前",title:"艾文"},{avatar:"svg:avatar-3",content:"发布了 <a>个人动态</a> ",date:"1天前",title:"克里斯"},{avatar:"svg:avatar-4",content:"发表文章 <a>如何编写一个Vite插件</a> ",date:"2天前",title:"Vben"},{avatar:"svg:avatar-1",content:"回复了 <a>杰克</a> 的问题 <a>如何进行项目优化？</a>",date:"3天前",title:"皮特"},{avatar:"svg:avatar-2",content:"关闭了问题 <a>如何运行项目</a> ",date:"1周前",title:"杰克"},{avatar:"svg:avatar-3",content:"发布了 <a>个人动态</a> ",date:"1周前",title:"威廉"},{avatar:"svg:avatar-4",content:"推送了代码到 <a>Github</a>",date:"2021-04-01 20:00",title:"威廉"},{avatar:"svg:avatar-4",content:"发表文章 <a>如何编写使用 Admin Vben</a> ",date:"2021-03-01 20:00",title:"Vben"}],I=L();function W(f){var g,x;if((g=f.url)!=null&&g.startsWith("http")){D(f.url);return}(x=f.url)!=null&&x.startsWith("/")?I.push(f.url).catch(w=>{console.error("Navigation failed:",w)}):console.warn(`Unknown URL for navigation item: ${f.title} -> ${f.url}`)}return(f,g)=>{var x;return r(),c("div",xt,[l(t(P),{avatar:((x=t(e).userInfo)==null?void 0:x.avatar)||t(q).app.defaultAvatar},{title:o(()=>{var w;return[m(" 早安, "+i((w=t(e).userInfo)==null?void 0:w.realName)+", 开始您一天的工作吧！ ",1)]}),description:o(()=>g[0]||(g[0]=[m(" 今日晴，20℃ - 32℃！ ")])),_:1},8,["avatar"]),a("div",vt,[a("div",bt,[l(t(Y),{items:n,title:"项目",onClick:W}),l(t(gt),{items:V,class:"mt-5",title:"最新动态"})]),a("div",$t,[l(t(et),{items:s,class:"mt-5 lg:mt-0",title:"快捷导航",onClick:W}),l(t(it),{items:u.value,class:"mt-5",title:"待办事项"},null,8,["items"]),l(t(E),{class:"mt-5",title:"访问来源"},{default:o(()=>[l(S)]),_:1})])])])}}});export{Nt as default};
