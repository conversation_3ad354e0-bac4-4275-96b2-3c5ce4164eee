import { Entity, Column, Index, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '@/common/entities/base.entity';
import { Function } from '@/modules/functions/entities/function.entity';

@Entity('applications')
@Index(['appKey'])
@Index(['status'])
export class Application extends BaseEntity {
  @ApiProperty({ description: '应用名称' })
  @Column({
    type: 'varchar',
    length: 100,
    comment: '应用名称',
  })
  name: string;

  @ApiProperty({ description: '应用Key' })
  @Column({
    name: 'app_key',
    type: 'varchar',
    length: 100,
    unique: true,
    comment: '应用Key',
  })
  appKey: string;

  @ApiProperty({ description: '联系人' })
  @Column({
    name: 'contact_person',
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '联系人',
  })
  contactPerson?: string;

  @ApiProperty({ description: '联系电话' })
  @Column({
    name: 'contact_phone',
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: '联系电话',
  })
  contactPhone?: string;

  @ApiProperty({ description: '状态：1启用，0禁用' })
  @Column({
    type: 'tinyint',
    default: 1,
    comment: '状态：1启用，0禁用',
  })
  status: number;

  @ApiProperty({ description: '应用描述' })
  @Column({
    type: 'text',
    nullable: true,
    comment: '应用描述',
  })
  description?: string;

  // 关联关系
  @OneToMany(() => Function, (func) => func.application)
  functions?: Function[];
}
