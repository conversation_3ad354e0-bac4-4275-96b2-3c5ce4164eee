var m=(l,r,t)=>new Promise((i,a)=>{var c=e=>{try{o(t.next(e))}catch(n){a(n)}},d=e=>{try{o(t.throw(e))}catch(n){a(n)}},o=e=>e.done?i(e.value):Promise.resolve(e.value).then(c,d);o((t=t.apply(l,r)).next())});import{u as s}from"./vxe-table-a0ubJ4nQ.js";import"./bootstrap-DShsrVit.js";import{g as f}from"./table-eRKxsFfH.js";import{_ as u}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as g,af as _,ag as h,ah as x,a3 as p,n as C}from"../jse/index-index-BMh_AyeW.js";import"./empty-icon-Crs2MoG4.js";import"./loading-Cqdke3S1.js";import"./form-DnT3S1ma.js";const q=g({__name:"edit-cell",setup(l){const r={columns:[{title:"序号",type:"seq",width:50},{editRender:{name:"input"},field:"category",title:"Category"},{editRender:{name:"input"},field:"color",title:"Color"},{editRender:{name:"input"},field:"productName",title:"Product Name"},{field:"price",title:"Price"},{field:"releaseDate",formatter:"formatDateTime",title:"Date"}],editConfig:{mode:"cell",trigger:"click"},height:"auto",pagerConfig:{},proxyConfig:{ajax:{query:a=>m(this,[a],function*({page:i}){return yield f({page:i.currentPage,pageSize:i.pageSize})})}},showOverflow:!0},[t]=s({gridOptions:r});return(i,a)=>(_(),h(p(u),{"auto-content-height":""},{default:x(()=>[C(p(t))]),_:1}))}});export{q as default};
