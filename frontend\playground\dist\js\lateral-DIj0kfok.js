import{u as s,B as n}from"./bootstrap-DShsrVit.js";import{_ as i}from"./fallback.vue_vue_type_script_setup_true_lang-BkEwlnJ0.js";import{a4 as m,af as u,ag as c,ah as a,a3 as e,n as p,an as f}from"../jse/index-index-BMh_AyeW.js";import"./rotate-cw-B0JNpqtv.js";const C=m({__name:"lateral",setup(l){const o=s();function r(){o.push({name:"BreadcrumbLateralDetailDemo"})}return(_,t)=>(u(),c(e(i),{description:"点击查看详情，并观察面包屑导航变化",status:"coming-soon",title:"面包屑导航-平级模式"},{action:a(()=>[p(e(n),{type:"primary",onClick:r},{default:a(()=>t[0]||(t[0]=[f("点击查看详情")])),_:1})]),_:1}))}});export{C as default};
