import type { VxeColumnProps } from '#/adapter/vxe-table';
import type { VbenFormSchema } from '@vben/common-ui';

export const columns: VxeColumnProps[] = [
  {
    title: '项目名称',
    field: 'projectName',
    width: 400,
  },
  {
    title: '项目编号',
    field: 'projectCode',
    width: 120,
  },
  {
    title: '委托单位',
    field: 'commissionUnit',
    width: 300,
  },
  {
    title: '项目类型',
    field: 'projectType',
    width: 120,
  },
  {
    title: '所属部门',
    field: 'belongDept',
    width: 180,
  },
  {
    title: '导入时间',
    field: 'timeCreated',
    width: 180,
    slots: { default: 'timeCreated' },
  },
  {
    title: '导入人',
    field: 'userNameCreated',
    width: 120,
    slots: { default: 'userNameCreated' },
  },
];

export const searchFormSchema: VbenFormSchema[] = [
  {
    fieldName: 'projectName',
    label: '项目名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    fieldName: 'projectCode',
    label: '项目编号',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    fieldName: 'projectType',
    label: '项目类型',
    component: 'Input',
    colProps: { span: 8 },
  },
];
