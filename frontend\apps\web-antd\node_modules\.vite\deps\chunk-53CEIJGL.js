import {
  tooltip_default
} from "./chunk-RX4SHEIY.js";
import {
  dynamicApp
} from "./chunk-VRANVM3Q.js";
import {
  VxeUI
} from "./chunk-DULHHPCE.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.35_vue@3.5.13_typescript@5.8.3_/node_modules/vxe-pc-ui/es/tooltip/index.js
var VxeTooltip = Object.assign({}, tooltip_default, {
  install(app) {
    app.component(tooltip_default.name, tooltip_default);
  }
});
dynamicApp.use(VxeTooltip);
VxeUI.component(tooltip_default);
var Tooltip = VxeTooltip;
var tooltip_default2 = VxeTooltip;

export {
  VxeTooltip,
  Tooltip,
  tooltip_default2 as tooltip_default
};
//# sourceMappingURL=chunk-53CEIJGL.js.map
