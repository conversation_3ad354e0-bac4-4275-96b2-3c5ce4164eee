import { Entity, Column, Index, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '@/common/entities/base.entity';

@Entity('dict_data')
@Index(['dictType'])
@Index(['parentId'])
@Index(['status'])
@Index(['sortOrder'])
export class DictData extends BaseEntity {
  @ApiProperty({ description: '字典类型' })
  @Column({
    name: 'dict_type',
    type: 'varchar',
    length: 100,
    comment: '字典类型',
  })
  dictType: string;

  @ApiProperty({ description: '字典标签' })
  @Column({
    name: 'dict_label',
    type: 'varchar',
    length: 100,
    comment: '字典标签',
  })
  dictLabel: string;

  @ApiProperty({ description: '字典键值' })
  @Column({
    name: 'dict_value',
    type: 'varchar',
    length: 100,
    comment: '字典键值',
  })
  dictValue: string;

  @ApiProperty({ description: '父级ID，支持层级结构' })
  @Column({
    name: 'parent_id',
    type: 'bigint',
    default: 0,
    comment: '父级ID，支持层级结构',
  })
  parentId: number;

  @ApiProperty({ description: '排序' })
  @Column({
    name: 'sort_order',
    type: 'int',
    default: 0,
    comment: '排序',
  })
  sortOrder: number;

  @ApiProperty({ description: '状态：1启用，0停用' })
  @Column({
    type: 'tinyint',
    default: 1,
    comment: '状态：1启用，0停用',
  })
  status: number;

  @ApiProperty({ description: '是否默认：1是，0否' })
  @Column({
    name: 'is_default',
    type: 'tinyint',
    default: 0,
    comment: '是否默认：1是，0否',
  })
  isDefault: number;

  @ApiProperty({ description: '备注' })
  @Column({
    type: 'text',
    nullable: true,
    comment: '备注',
  })
  remark?: string;

  // 关联关系
  @ManyToOne(() => DictData, (dictData) => dictData.children)
  @JoinColumn({ name: 'parent_id' })
  parent?: DictData;

  @OneToMany(() => DictData, (dictData) => dictData.parent)
  children?: DictData[];
}
