1.替换apps下的web-antd

2.packages\effects\common-ui\src\components\index.ts
添加
  Tabs,
  <PERSON>bs<PERSON>ontent,
  TabsTrigger,
  TabsList,

3.packages\icons\src\iconify\index.ts
添加
export const SplitScreen = createIconifyIcon('carbon:split-screen');
export const AreaSelect = createIconifyIcon('vaadin:area-select');
export const CompassNeedle = createIconifyIcon('mdi:arrow-compass');
export const HomeIcon = createIconifyIcon('mdi-light:home');
export const FullScreen = createIconifyIcon('solar:full-screen-square-outline');
export const GlassPlus = createIconifyIcon('heroicons:magnifying-glass-plus');
export const GlassMinus = createIconifyIcon('heroicons:magnifying-glass-minus');
export const LookDown = createIconifyIcon('iconoir:emoji-look-down');
export const GridIcon = createIconifyIcon('mdi-light:grid');
export const MenuFold = createIconifyIcon('tdesign:menu-fold');
export const RoundLine = createIconifyIcon('mingcute:round-line');
export const PolygonLine = createIconifyIcon('hugeicons:polygon');
export const RectangleOutline = createIconifyIcon('ic:outline-rectangle');
export const ClearIcon = createIconifyIcon('tdesign:clear');

4.packages\locales\src\langs\zh-CN\authentication.json
pageTitle 修改成系统名称，例如：硕威数据管理系统，删除pageDesc内容

5.packages/effects/request/src/request-client/request-client.ts
默认超时时间 修改，例如 180_000

6.packages\@core\preferences\src\config.ts
logo 下的 source 修改 '/images/favicon.png',
enableCheckUpdates 修改成false
globalSearch 修改成 false
theme 中 mode 修改成 light
widget 中 globalSearch，languageToggle，notification 修改成false

7.packages/effects/common-ui/src/ui/authentication/login.vue
  showCodeLogin: false,
  showForgetPassword: false,
  showQrcodeLogin: false,
  showRegister: false,
  showRememberMe: true,
  showThirdPartyLogin: false,

8.packages/effects/layouts/src/authentication/authentication.vue
注释copyright
<!--      <template v-if="copyright" #copyright>-->
<!--        <slot name="copyright">-->
<!--          <Copyright-->
<!--            v-if="preferences.copyright.enable"-->
<!--            v-bind="preferences.copyright"-->
<!--          />-->
<!--        </slot>-->
<!--      </template>-->

9.packages/effects/layouts/src/widgets/check-updates/check-updates.vue
注释onMounted 下的 start和stop方法

10.packages/effects/layouts/src/widgets/user-dropdown/user-dropdown.vue
注释
<!--      <DropdownMenuItem-->
<!--        v-for="menu in menus"-->
<!--        :key="menu.text"-->
<!--        class="mx-1 flex cursor-pointer items-center rounded-sm py-1 leading-8"-->
<!--        @click="menu.handler"-->
<!--      >-->
<!--        <VbenIcon :icon="menu.icon" class="mr-2 size-4" />-->
<!--        {{ menu.text }}-->
<!--      </DropdownMenuItem>-->
<!--      <DropdownMenuSeparator />-->

11.packages/stores/src/modules/user.ts
state下面添加
 persist: {
    enabled: true,
    strategies: [
      {
        key: 'user-store',
        storage: localStorage, // 可选 localStorage 或 sessionStorage
      },
    ],
  },


12.packages\@core\base\design\src\design-tokens\default.css

--popup-z-index修改成1000

13.internal\vite-config\src\plugins\index.ts
import cesium from 'vite-plugin-cesium';
loadConditionPlugins方法添加
 {
      condition: true,
      plugins: async () => {
        return [cesium()];
      },
    },

14.apps\web-antd\package.json
添加依赖  "vite-plugin-cesium": "^1.2.23",
