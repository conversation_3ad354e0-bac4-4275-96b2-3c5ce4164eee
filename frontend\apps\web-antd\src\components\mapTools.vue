<script setup>
import {h, onUnmounted, ref} from 'vue';
import { useRouter } from 'vue-router';

import * as Cesium from 'cesium';

import { getGisMap } from '#/utils/cesium';

import iconFangda from '../assets/images/fangda.png';
import iconFushi from '../assets/images/fushi.png';
import iconGrid from '../assets/images/grid.png';
import iconFenping from '../assets/images/icon_fenping.png';
import iconQuanping from '../assets/images/quanping.png';
import iconSpaceQuery from '../assets/images/spaceQuery.png';
import iconSuoxiao from '../assets/images/suoxiao.png';
import iconTuichuquanping from '../assets/images/tuichuquanping.png';
import iconZhuye from '../assets/images/zhuye.png';
import Box from '../components/JS/box';
import { poiViewer } from '../utils/cesium/layers/POIViewer';
import { shapeViewer } from '../utils/cesium/layers/ShapeViewer';
import mapTools from '../utils/cesium/mapTools/index';
import SpaceQuery from '../views/dataView/components/SpaceQuery.vue';
import ztu from '../ztu';
import CurtainView from './CurtainView.vue';
import MultiScreen from './MultiScreen.vue';
import Wander from './wander.vue';
import eb from "#/ztu/eventbus";
import { Page } from "@vben/common-ui";
import { SplitScreen ,AreaSelect,HomeIcon,FullScreen,GlassPlus,GlassMinus,LookDown,GridIcon,ClearIcon} from '@vben/icons';
import {HSLToHex, stringToHSL} from "#/utils/utils.js";
const showFullScreen = ref(false);
const locationvisible = ref(false);
const showFly = ref(false);
const cityvalue = ref([]);
const scenemode = ref(3);
const router = useRouter();
const multiScreenEl = ref(null);
const measureEl = ref(null);
const shapeEl = ref(null);

const spaceQueryVisible = ref(false);

const props = defineProps(['checkOptLayers']);

// 获取 CSS 变量值
const getCssVariable = (variableName) => {
  return getComputedStyle(document.documentElement).getPropertyValue(variableName);
};

// 获取主色调
const primaryColor1 = getCssVariable('--primary');
// 解析并转换为 HSL
const { h1, s1, l1 } = stringToHSL(primaryColor1);
// 转换为 Hex 颜色值
const primaryColor = HSLToHex(h1, s1, l1);

console.log(primaryColor);

const toFullMap = () => {
  mapTools.fullMap();
};
const fullScreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen();
  } else {
    document.documentElement.requestFullscreen();
  }
  showFullScreen.value = !showFullScreen.value;
};
const zoomIn = () => {
  mapTools.zoomIn();
};
const zoomOut = () => {
  mapTools.zoomOut();
};

const measures = (e) => {
  console.log('measures', e.key);
  const key = e.key;
  if (key == '1')
    mapTools.calLength(null, {
      clampToGround: true,
    });
  // 空间距离
  else if (key == '2')
    mapTools.calLDistance(); // 直线距离
  else if (key == '3')
    mapTools.calHDistance(); // 水平距离
  else if (key == '4')
    mapTools.calVDistance(); // 垂直距离
  else if (key == '5')
    mapTools.calAngle(); // 角度测量
  else if (key == '6')
    mapTools.calArea(null, {
      clampToGround: true,
    });
  // 面积测量
  else if (key == '7') mapTools.calHeight(); // 高度测量
};

const removeAll = () => {
  mapTools.removeAll();
};
// 切换二三维
const changeScene = () => {
  scenemode.value = mapTools.morph();
  console.log('...........scenemode', scenemode);
};
const changeFly = () => {
  // showFly.value = !showFly.value
  mapTools.fly();
};

// 截图
const jietu = () => {
  mapTools.cameraPhoto();
};
// 俯视
const fushi = () => {
  mapTools.fushi();
};
// 切换底图（天地图）
const changemap = (e) => {
  mapTools.changeMap(e.key);
};

const regionBox = null;
// 行政区定位（天地图）
const location = () => {
  /* router.replace({
			name: 'region'
		}); */
  // if(regionBox){
  // 	return Box.show(regionBox);
  // }
  // regionBox = Box.open({
  // 	title:'定位',
  // 	beforeClose:()=>{
  // 		Box.hide(regionBox);
  // 		return true;
  // 	}
  // },h(Region))
};

const spaceQueryBox = null;
// 空间查询
const spaceQuery = () => {
  spaceQueryVisible.value = !spaceQueryVisible.value;
};

const onCancelspaceQueryModal = () => {
  spaceQueryVisible.value = false;
};

// 绘图
const drawer = (e) => {
  // mapTools.drawShape(e.key)
  /* Box.closeAll()
		mapTools.drawShape(e.key, null, null, (entity) => {
		  //触发事件总线
		  VueEvent.emit("entity",{data:entity,type:e.key})
		  Box.closeAll()
		  Box.open({
		    moverect: {left: 50, top: 64, right: 0, bottom: 0},
		    style: {right: '100px', top: '140px', left: "unset"},
		    title: '属性',
		  }, [createVNode(ShapeSetting, {entity: entity, type: e.key})])
		}) */
  shapeViewer.add(e.key);
};

// 兴趣点编辑
const newpoi = () => {
  /* Box.closeAll()
  mapTools.drawPoint('marker', null, null, (entity) => {
    Box.closeAll()
	entity.billboard.width = 24;
	entity.billboard.height = 24;
    Box.open({
      moverect: {left: 50, top: 64, right: 0, bottom: 0},
      style: {right: '100px', top: '140px', left: "unset"},
      title: '属性',
    }, [createVNode(POISetting, {entity: entity, type:'marker'})])
  }) */
  poiViewer.add();
};

const addAdditionalLayerOption = async (
  gisMap,
  name,
  imageryProviderPromise,
  alpha,
  show,
) => {
  try {
    const imageryProvider = await Promise.resolve(imageryProviderPromise);
    const layer = new Cesium.ImageryLayer(imageryProvider);
    layer.alpha = Cesium.defaultValue(alpha, 0.5);
    layer.show = Cesium.defaultValue(show, true);
    layer.name = name;
    gisMap.viewer.imageryLayers.add(layer);
    Cesium.knockout.track(layer, ['alpha', 'show', 'name']);
    return layer;
  } catch (error) {
    console.error(`There was an error while creating ${name}. ${error}`);
  }
};

const clearDatas = () => {
  eb.emit('clearData',{});
}

let gridLayer = null;
let coordinatesLayer = null;
const addOrRemoveGrid = () => {
  getGisMap(async (gisMap) => {
    if (gridLayer && coordinatesLayer) {
      gisMap.viewer.imageryLayers.remove(gridLayer);
      gisMap.viewer.imageryLayers.remove(coordinatesLayer);
      gridLayer = null;
      coordinatesLayer = null;
      return;
    }

    gridLayer = await addAdditionalLayerOption(
      gisMap,
      'Grid',
      new Cesium.GridImageryProvider(),
      1,
      true,
    );
    coordinatesLayer = await addAdditionalLayerOption(
      gisMap,
      'Tile Coordinates',
      new Cesium.TileCoordinatesImageryProvider(),
      1,
      true,
    );
  });
};

onUnmounted(() => {
  Box.closeAll();
});

// 分屏幕操作
const changesceen = (e) => {
  switch (e.key) {
    case 'curtain': {
      Box.open(
        {
          title: '卷帘对比',
          beforeClose: () => {
            ztu.global.SplitMap.restore();
          },
        },
        h(CurtainView),
      );
      break;
    }
    case 'doublescreen': {
      ztu.global.SplitMap.split('viewDiv', {
        layout: [[0, 1]],
        synchronization: true,
      });
      Box.open(
        {
          title: '双屏对比',
          beforeClose: () => {
            ztu.global.SplitMap.restore();
          },
        },
        h(MultiScreen),
      );
      break;
    }
    case 'fourscreen': {
      ztu.global.SplitMap.split('viewDiv', {
        layout: [
          [0, 0, 1, 1],
          [0, 0, 1, 1],
          [2, 2, 3, 3],
          [2, 2, 3, 3],
        ],
        synchronization: true,
      });
      Box.open(
        {
          title: '四屏对比',
          beforeClose: () => {
            ztu.global.SplitMap.restore();
          },
        },
        h(MultiScreen),
      );
      break;
    }
    case 'threescreen': {
      ztu.global.SplitMap.split('viewDiv', {
        layout: [
          [0, 1, 2],
          [0, 1, 2],
          [0, 1, 2],
        ],
        synchronization: true,
      });
      Box.open(
        {
          title: '三屏对比',
          beforeClose: () => {
            ztu.global.SplitMap.restore();
          },
        },
        h(MultiScreen),
      );
      break;
    }
    /* 			case 'cancle':
				ztu.global.SplitMap.restore(); */
  }
};

// 坐标拾取
const getLocation = () => {
  mapTools.getLocation();
};

// 空间查询
const onStartSpaceQuery = (selectedShape) => {
  mapTools.onStartSpaceQuery(selectedShape);
};

const onCLoseSpaceQuery = () => {
  spaceQueryVisible.value = false;
}

</script>

<template>
  <!-- 常用工具栏 -->
  <view class="tools">
    <a-card :bodyStyle="{ padding: '10px' }">
    <!--		<a class="item" style="width:70px" v-if="$chkFuns('功能树.工具栏.行政区定位')">-->
    <!--			<div style="width:fit-content;" @click="location">-->
    <!--				<span class="icon iconfont icon-dingwei"></span>-->
    <!--				<div class="text">行政区定位</div>-->
    <!--			</div>-->
    <!--		</a>-->
      <a-flex justify="space-between">
        <view v-if="$chkFuns('功能树.工具栏.底图') && false" class="item">
          <span class="icon iconfont icon-ditu"></span>
          <a-dropdown>
            <template #overlay>
              <a-menu class="menu" @click="changemap">
                <a-menu-item key="1">
                  <!-- <UserOutlined /> -->
                  天地图矢量
                </a-menu-item>
                <a-menu-item key="2"> 天地图影像 </a-menu-item>
                <a-menu-item key="3"> 天地图地形 </a-menu-item>
                <a-menu-item key="4"> Bing影像 </a-menu-item>
                <a-menu-item key="5"> Cesium地形 </a-menu-item>
              </a-menu>
            </template>
            <div class="text">
              底图
              <DownOutlined />
            </div>
          </a-dropdown>
        </view>
        <view
          v-if="$chkFuns('功能树.工具栏.分屏')"
          ref="multiScreenEl"
          class="item "
        >
          <a-dropdown :get-popup-container="() => multiScreenEl">
            <template #overlay>
              <a-menu class="" @click="changesceen">
                <a-menu-item key="curtain"> 卷帘对比 </a-menu-item>
                <a-menu-item key="doublescreen"> 双屏对比 </a-menu-item>
                <a-menu-item key="threescreen"> 三屏对比 </a-menu-item>
                <a-menu-item key="fourscreen"> 四屏对比 </a-menu-item>
              </a-menu>
            </template>
            <div>
              <SplitScreen class="size-8" />
              <div class="text">
                分屏
                <DownOutlined />
              </div>
            </div>
          </a-dropdown>
        </view>

    <view class="item " @click="spaceQuery">
      <AreaSelect class="size-8" :style="{'color': spaceQueryVisible ? primaryColor : 'hsl(var(--foreground))'}"/>
      <div class="text">数据分发</div>
    </view>
      </a-flex>
<!--    <a ref="measureEl" class="item tool-menu">-->
<!--      <a-dropdown :get-popup-container="() => measureEl">-->
<!--        <template #overlay>-->
<!--          <a-menu class="" @click="measures">-->
<!--            <a-sub-menu title="距离">-->
<!--              <a-menu-item key="1" style="width: 80px">空间距离</a-menu-item>-->
<!--              <a-menu-item key="2" style="width: 80px">直线距离</a-menu-item>-->
<!--              <a-menu-item key="3" style="width: 80px">水平距离</a-menu-item>-->
<!--              <a-menu-item key="4" style="width: 80px">垂直距离</a-menu-item>-->
<!--            </a-sub-menu>-->
<!--            <a-menu-item key="5"> 角度 </a-menu-item>-->
<!--            <a-menu-item key="6"> 面积 </a-menu-item>-->
<!--            <a-menu-item v-if="scenemode === 3" key="7"> 高度 </a-menu-item>-->
<!--          </a-menu>-->
<!--        </template>-->
<!--        <div>-->
<!--          <span class="icon iconfont icon-celiangleixing"></span>-->
<!--          <div class="text">-->
<!--            测量-->
<!--            <DownOutlined />-->
<!--          </div>-->
<!--        </div>-->
<!--      </a-dropdown>-->
<!--    </a>-->
    <!--		<a class="item tool-menu" ref="shapeEl" v-if="$chkFuns('功能树.工具栏.绘图')">-->
    <!--			-->
    <!--			<a-dropdown :getPopupContainer="()=>shapeEl">-->
    <!--				<template #overlay>-->
    <!--					<a-menu @click="drawer">-->
    <!--						&lt;!&ndash; <a-menu-item key="marker">-->
    <!--              标注点-->
    <!--            </a-menu-item> &ndash;&gt;-->
    <!--						<a-menu-item key="polyline">-->
    <!--							线-->
    <!--						</a-menu-item>-->
    <!--						<a-menu-item key="polygon">-->
    <!--							多边形-->
    <!--						</a-menu-item>-->
    <!--						<a-menu-item key="rectangle">-->
    <!--							矩形-->
    <!--						</a-menu-item>-->
    <!--						<a-menu-item key="circle">-->
    <!--							圆-->
    <!--						</a-menu-item>-->
    <!--						&lt;!&ndash; <a-menu-item key="text">-->
    <!--              文本-->
    <!--            </a-menu-item> &ndash;&gt;-->
    <!--						&lt;!&ndash; <a-menu-item  key="marker">-->
    <!--              标记-->
    <!--            </a-menu-item> &ndash;&gt;-->
    <!--					</a-menu>-->
    <!--				</template>-->
    <!--				<div>-->
    <!--					<icon-font type="icon-mianji" class="icon"></icon-font>-->
    <!--					<div class="text">绘图-->
    <!--						<DownOutlined />-->
    <!--					</div>-->
    <!--				</div>-->
    <!--				-->
    <!--			</a-dropdown>-->
    <!--		</a>-->
    <!--		<a class="item" @click="newpoi" v-if="$chkFuns('功能树.工具栏.兴趣点')">-->
    <!--			<icon-font type="icon-pointer" class="icon"></icon-font>-->
    <!--			<div class="text">兴趣点</div>-->
    <!--		</a>-->
    <!--		<a class="item" @click="removeAll" v-if="$chkFuns('功能树.工具栏.清除')">-->
    <!--			<icon-font type="icon-qingchu" class="icon"></icon-font>-->
    <!--			<div class="text">清除</div>-->
    <!--		</a>-->
    <!--		<a class="item" @click="getLocation" v-if="$chkFuns('功能树.工具栏.坐标拾取')">-->
    <!--			<icon-font type="icon-dianji" class="icon"></icon-font>-->
    <!--			<div class="text">坐标拾取</div>-->
    <!--		</a>-->
    <!--		<a class="item" @click="jietu" v-if="$chkFuns('功能树.工具栏.截图')">-->
    <!--			<span class="icon iconfont icon-jietu"></span>-->
    <!--			<div class="text">截图</div>-->
    <!--		</a>-->
  </a-card>
  </view>
  <!-- 地图工具栏 -->
  <div class="maptools bg-white dark:bg-black">
    <a-tooltip
      v-if="$chkFuns('功能树.侧边栏.全图/起始位置')"
      placement="left"
      title="全图/起始位置"
    >
      <a class="item" @click="toFullMap">
        <HomeIcon class="size-6"/>
      </a>
    </a-tooltip>
    <a-tooltip
      v-if="$chkFuns('功能树.侧边栏.全屏/退出全屏')"
      placement="left"
      title="全屏/退出全屏"
    >
      <a class="item" @click="fullScreen()">
        <FullScreen class="size-6"/>
      </a>
    </a-tooltip>
    <a-tooltip
      v-if="$chkFuns('功能树.侧边栏.放大')"
      placement="left"
      title="放大"
    >
      <a class="item" @click="zoomIn">
        <GlassPlus class="size-6"/>
      </a>
    </a-tooltip>
    <a-tooltip
      v-if="$chkFuns('功能树.侧边栏.缩小')"
      placement="left"
      title="缩小"
    >
      <a class="item" @click="zoomOut">
        <GlassMinus class="size-6"/>
      </a>
    </a-tooltip>
    <a-tooltip
      v-if="$chkFuns('功能树.侧边栏.俯视')"
      placement="left"
      title="俯视"
    >
      <a class="item" @click="fushi()">
        <LookDown class="size-6"/>
      </a>
    </a-tooltip>
    <a-tooltip
      v-if="$chkFuns('功能树.侧边栏.2D/3D')"
      placement="left"
      title="2D/3D"
    >
      <a class="item" @click="changeScene">
        <span v-if="scenemode == 2" class="iconfont" style="font-size: 14px"
          >2D</span
        >
        <span v-if="scenemode == 3" class="iconfont" style="font-size: 14px"
          >3D</span
        >
      </a>
    </a-tooltip>
    <a-tooltip
      v-if="$chkFuns('功能树.侧边栏.网格')"
      placement="left"
      title="网格"
    >
      <a class="item" @click="addOrRemoveGrid()">
        <GridIcon class="size-6"/>
      </a>
    </a-tooltip>
    <a-tooltip
      v-if="$chkFuns('功能树.侧边栏.网格')"
      placement="left"
      title="清除"
    >
      <a class="item" @click="clearDatas()">
        <ClearIcon class="size-6"/>
      </a>
    </a-tooltip>
    <!--		<a-tooltip title="漫游" placement="left" v-if="$chkFuns('功能树.侧边栏.漫游')">-->
    <!--			<a class="item" @click="changeFly">-->
    <!--        <a-image :src="iconFeixingmanyou" :preview="false" mode="contain" width="20px" height="20px"></a-image>-->
    <!--			</a>-->
    <!--		</a-tooltip>-->
  </div>
  <Wander v-if="showFly" class="wander" />
  <SpaceQuery :checkOptLayers="props.checkOptLayers" :visible="spaceQueryVisible"
              @onCLoseSpaceQuery="onCLoseSpaceQuery"
              @start-query="onStartSpaceQuery" />
</template>

<style scoped lang="less">
.tools {
  position: absolute;
  right: 20px;
  top: 10px;
  z-index: 100;
  display: flex;
  flex-direction: row;

  :deep(.ant-card) {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    box-shadow: 0 2px 8px hsl(var(--overlay));
  }

  .item {
    text-align: center;
    margin-right: 0px;
    padding-left: 10px;
    padding-right: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: hsl(var(--foreground));

    &:last-child:after {
      border-right: 0;
    }

    .icon {
      font-size: 23px;
      color: hsl(var(--foreground));
    }

    .text {
      font-size: 12px;
      cursor: pointer;
      color: hsl(var(--foreground));

      &:hover {
        color: hsl(var(--primary));
      }
    }

    .iconfont {
      line-height: 21.5px;
      color: hsl(var(--foreground));
    }
  }

  :deep(.item.tool-menu) {
    .ant-dropdown-menu {
      background: hsl(var(--card));
      border: 1px solid hsl(var(--border));
      box-shadow: 0 2px 8px hsl(var(--overlay));

      .ant-dropdown-menu-item {
        color: hsl(var(--foreground));

        &:hover {
          background: hsl(var(--accent));
          color: hsl(var(--accent-foreground));
        }
      }
    }
  }
}

.maptools {
  position: absolute;
  right: 0;
  top: 50%;
  z-index: 100;
  border: 1px solid hsl(var(--border));
  border-top: 0;
  border-radius: 35px 0 0 35px;
  width: 35px;
  display: flex;
  flex-direction: column;
  transform: translate3d(0, -50%, 0);
  background: hsl(var(--card));
  box-shadow: -2px 0 8px hsl(var(--overlay));

  .item {
    height: 35px;
    text-align: center;
    border-bottom: 1px solid hsl(var(--border));
    margin: 0;
    padding: 5px;
    color: hsl(var(--foreground));
    transition: all 0.2s ease;

    &:hover {
      background: hsl(var(--accent));
      color: hsl(var(--accent-foreground));
    }

    .iconfont {
      font-size: 21px;
    }

    &:first-child {
      border-radius: 35px 0 0 0;
      height: 50px;
      padding-top: 20px;
    }

    &:last-child {
      border-radius: 0 0 0 35px;
      height: 50px;
      border-bottom: none;
    }
  }
}

.wander {
  position: absolute;
  right: 50px;
  top: 250px;
  z-index: 999;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  box-shadow: 0 2px 8px hsl(var(--overlay));
}

:deep(.ant-select-show-arrow) {
  top: 10px;
  color: hsl(var(--foreground));
}

:deep(.ant-cascader) {
  width: unset;
  color: hsl(var(--foreground));
}

:deep(.ant-tooltip) {
  .ant-tooltip-inner {
    background: hsl(var(--card));
    color: hsl(var(--foreground));
    box-shadow: 0 2px 8px hsl(var(--overlay));
  }

  .ant-tooltip-arrow-content {
    background: hsl(var(--card));
  }
}

:deep(.ant-dropdown) {
  .ant-dropdown-menu {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    box-shadow: 0 2px 8px hsl(var(--overlay));

    .ant-dropdown-menu-item {
      color: hsl(var(--foreground));

      &:hover {
        background: hsl(var(--accent));
        color: hsl(var(--accent-foreground));
      }
    }
  }
}
</style>
