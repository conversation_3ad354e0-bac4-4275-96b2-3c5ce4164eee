import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  UseGuards,
  Put,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { PositionsService } from './positions.service';
import { CreatePositionDto } from './dto/create-position.dto';
import { UpdatePositionDto } from './dto/update-position.dto';
import { QueryPositionDto } from './dto/query-position.dto';
import { Position } from './entities/position.entity';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { ResponseDto } from '@/common/dto/response.dto';
import { User } from '@/modules/users/entities/user.entity';

@ApiTags('岗位管理')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('positions')
export class PositionsController {
  constructor(private readonly positionsService: PositionsService) {}

  @Post()
  @ApiOperation({ summary: '创建岗位' })
  @ApiResponse({ status: 201, description: '创建成功', type: Position })
  async create(
    @Body() createPositionDto: CreatePositionDto,
    @CurrentUser() currentUser: User,
  ): Promise<ResponseDto<Position>> {
    const position = await this.positionsService.create(createPositionDto, currentUser.id);
    return ResponseDto.success(position, '创建成功');
  }

    @Get()
  @ApiOperation({ summary: '获取岗位列表' })
  @ApiResponse({ status: 200, description: '获取成功', type: [Position] })
  async findAll(@Query() queryDto: QueryPositionDto): Promise<ResponseDto<Position[]>> {
    const positions = await this.positionsService.findAll(queryDto, ['department', 'parentPosition']);
    return ResponseDto.success(positions, '获取成功');
  }

  @Get('tree')
  @ApiOperation({ summary: '获取岗位树形结构' })
  @ApiResponse({ status: 200, description: '获取成功', type: [Position] })
  async findTree(@Query() queryDto: QueryPositionDto): Promise<ResponseDto<Position[]>> {
    const tree = await this.positionsService.findTree(queryDto);
    return ResponseDto.success(tree, '获取成功');
  }

  @Get(':id')
  @ApiOperation({ summary: '获取岗位详情' })
  @ApiResponse({ status: 200, description: '获取成功', type: Position })
  async findOne(@Param('id') id: string): Promise<ResponseDto<Position>> {
    const position = await this.positionsService.findOne(+id);
    return ResponseDto.success(position, '获取成功');
  }

  @Put(':id')
  @ApiOperation({ summary: '更新岗位' })
  @ApiResponse({ status: 200, description: '更新成功', type: Position })
  async update(
    @Param('id') id: string,
    @Body() updatePositionDto: UpdatePositionDto,
    @CurrentUser() currentUser: User,
  ): Promise<ResponseDto<Position>> {
    const position = await this.positionsService.update(+id, updatePositionDto, currentUser.id);
    return ResponseDto.success(position, '更新成功');
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除岗位' })
  @ApiResponse({ status: 200, description: '删除成功' })
  async remove(@Param('id') id: string): Promise<ResponseDto<null>> {
    await this.positionsService.remove(+id);
    return ResponseDto.success(null, '删除成功');
  }

  @Delete()
  @ApiOperation({ summary: '批量删除岗位' })
  @ApiResponse({ status: 200, description: '删除成功' })
  async batchRemove(@Body() body: { ids: number[] }): Promise<ResponseDto<null>> {
    await this.positionsService.batchRemove(body.ids);
    return ResponseDto.success(null, '删除成功');
  }
}
