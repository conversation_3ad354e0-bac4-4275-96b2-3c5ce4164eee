# API路径变更说明

## 变更概述

已将所有API请求地址中的版本号 `/v1` 去掉，现在使用 `/api` 作为统一的全局前缀。

## 变更前后对比

### 变更前
```
/api/v1/system/login
/api/v1/users
/api/v1/organizations
/api/v1/roles
...
```

### 变更后
```
/api/system/login
/api/users
/api/organizations
/api/roles
...
```

## 修改的文件

### 后端修改

1. **`backend/src/main.ts`**
   - 全局前缀从 `api/v1` 改为 `api`
   ```typescript
   // 变更前
   app.setGlobalPrefix(process.env.APP_PREFIX || 'api/v1');
   
   // 变更后
   app.setGlobalPrefix(process.env.APP_PREFIX || 'api');
   ```

2. **`backend/.env.example`**
   - 环境变量示例更新
   ```bash
   # 变更前
   APP_PREFIX=api/v1
   
   # 变更后
   APP_PREFIX=api
   ```

3. **`backend/src/modules/auth/auth.controller.ts`**
   - 登录控制器路径已由用户手动修改为 `@Controller('system')`

### 前端修改

1. **`frontend/apps/web-antd/vite.config.mts`**
   - 代理目标地址更新为本地开发地址
   ```typescript
   proxy: {
     '/api': {
       changeOrigin: true,
       rewrite: (path) => path.replace(/^\/api/, ''),
       target: 'http://localhost:3000/api', // 更新为本地地址
       ws: true,
     },
   }
   ```

## 当前API路径列表

### 认证相关
- `POST /api/system/login` - 用户登录
- `POST /api/system/logout` - 用户退出

### 业务模块
- `GET|POST|PUT|DELETE /api/users` - 用户管理
- `GET|POST|PUT|DELETE /api/organizations` - 组织管理
- `GET /api/organizations/tree` - 组织树形结构
- `GET|POST|PUT|DELETE /api/roles` - 角色管理
- `GET|POST|PUT|DELETE /api/positions` - 岗位管理
- `GET|POST|PUT|DELETE /api/user-groups` - 用户组管理
- `GET|POST|PUT|DELETE /api/applications` - 应用管理
- `GET|POST|PUT|DELETE /api/functions` - 功能管理
- `GET|POST|PUT|DELETE /api/dict` - 字典管理
- `GET|POST|PUT|DELETE /api/sys-config` - 系统配置

### 系统接口
- `GET /api` - 应用信息
- `GET /api/health` - 健康检查

## 环境配置

### 前端环境变量
所有环境配置文件中的 `VITE_GLOB_API_URL` 保持为 `/api`：

- `.env.development`: `VITE_GLOB_API_URL=/api`
- `.env.production`: `VITE_GLOB_API_URL=/api`
- `.env.analyze`: `VITE_GLOB_API_URL=/api`

### 后端环境变量
在 `.env` 文件中设置：
```bash
APP_PREFIX=api
```

## 开发和部署注意事项

1. **本地开发**
   - 后端服务运行在 `http://localhost:3000`
   - 前端代理配置已更新为指向本地后端

2. **生产部署**
   - 确保反向代理配置更新，去掉 `/v1` 路径
   - 更新前端构建配置中的API地址

3. **API文档**
   - Swagger文档地址: `http://localhost:3000/api-docs`
   - 所有接口路径已自动更新

## 兼容性说明

- ✅ 新的API路径已生效
- ✅ 前端代理配置已更新
- ✅ 环境配置已同步
- ✅ 文档已更新

## 测试验证

可以使用以下命令测试登录接口：

```bash
curl -X POST http://localhost:3000/api/system/login \
  -H "Content-Type: application/json" \
  -d '{
    "account": "admin",
    "password": "admin123",
    "captcha": false
  }'
```

预期响应格式：
```json
{
  "success": true,
  "code": "00000",
  "data": {
    "roleType": 1,
    "name": "管理员",
    "token": "eyJ...",
    "account": "admin"
  },
  "currentTime": "2025-07-28 15:06:31"
}
```
