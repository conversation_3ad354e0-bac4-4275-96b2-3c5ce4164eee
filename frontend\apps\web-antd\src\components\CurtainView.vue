<script>
import {
  TabPane as ATabPane,
  Tabs as ATabs,
  Tree as ATree,
} from 'ant-design-vue';
import * as Cesium from 'cesium';

import ztu from '../ztu';

export default {
  components: {
    ATree,
    ATabs,
    ATabPane,
  },
  data() {
    return {
      activeKey: 'left',
      checkedKeys: [],
      checkedKeys2: [],
    };
  },
  mounted() {
    // 主图层树
    this.GISLayers = ztu.global.GISLayers;
    this.SplitMap = ztu.global.SplitMap;
    // 可用于卷帘的图层列表
    this.layers = this.GISLayers.getSplitLayers();
    this.checkchange();
    // 初始化卷帘对比
    this.SplitMap.curtain('viewDiv');
    this.GISLayers.on('showChanged', () => {
      this.layers = this.GISLayers.getSplitLayers();
      this.checkchange();
    });
  },
  methods: {
    change(key) {
      this.activeKey = key;
    },
    layerCheck(checkedKeys, { checked, checkedNodes, node, event }) {
      const key = node.key;
      const layer = this.GISLayers.getLayer(key);
      const refLayer = layer.refLayer;
      // 左帘勾选，若右帘也是显示状态，则为NONE两边都显示
      if (checked && this.activeKey == 'left') {
        // -1
        refLayer.splitDirection = layer.show
          ? Cesium.SplitDirection.NONE
          : Cesium.SplitDirection.LEFT;
        layer.show || this.GISLayers.showAndZoomToLayer(node.key);
      }
      // 右帘勾选，若左帘也是显示状态，则为NONE两边都显示
      else if (checked && this.activeKey == 'right') {
        // 1
        refLayer.splitDirection = layer.show
          ? Cesium.SplitDirection.NONE
          : Cesium.SplitDirection.RIGHT;
        layer.show || this.GISLayers.showAndZoomToLayer(node.key);
      }
      // 左帘取消勾选，若仅左帘是显示状态，则直接将图层隐藏
      else if (this.activeKey == 'left') {
        if (refLayer.splitDirection == Cesium.SplitDirection.NONE) {
          refLayer.splitDirection = Cesium.SplitDirection.RIGHT;
        } else this.GISLayers.hide(key);
      } else if (this.activeKey == 'right') {
        if (refLayer.splitDirection == Cesium.SplitDirection.NONE) {
          refLayer.splitDirection = Cesium.SplitDirection.LEFT;
        } else this.GISLayers.hide(key);
      }

      this.checkchange();
    },
    // 树节点勾选
    checkchange() {
      this.checkedKeys = this.GISLayers.showLeftKeys;
      this.checkedKeys2 = this.GISLayers.showRightKeys;
      console.log(this.checkedKeys, this.checkedKeys2);
      // this.checkedKeys = this.layers.filter(layer => layer.refLayer.splitDirection != Cesium.SplitDirection.RIGHT && layer.show)
      // .map(a => a.key)
      // this.checkedKeys2 = this.layers.filter(layer => layer.refLayer.splitDirection != Cesium.SplitDirection.LEFT && layer.show)
      // .map(a => a.key)
    },
  },
};
</script>

<template>
  <div class="curtain-view">
    <ATabs v-model="activeKey" @change="change">
      <ATabPane key="left" tab="左帘">
        <div
          style="max-height: 300px; margin-top: -15px; overflow: hidden auto"
        >
          <ATree
            v-model:checked-keys="checkedKeys"
            :tree-data="layers"
            checkable
            @check="layerCheck"
          />
        </div>
      </ATabPane>
      <ATabPane key="right" force-render tab="右帘">
        <div
          style="max-height: 300px; margin-top: -15px; overflow: hidden auto"
        >
          <ATree
            v-model:checked-keys="checkedKeys2"
            :tree-data="layers"
            checkable
            @check="layerCheck"
          />
        </div>
      </ATabPane>
    </ATabs>
  </div>
</template>

<style lang="less">
.curtain-view {
  overflow-y: auto;
  background: none;
  width: 100%;
  height: 100%;
  font-size: 14px;
  font-weight: 400;
  line-height: 19px;
  .ant-checkbox-wrapper {
    padding: 10px 24px;
  }
  .ant-checkbox-input {
    background: transparent;
  }
  .ant-tabs {
  }
  .ant-tabs-top > .ant-tabs-nav::before,
  .ant-tabs-bottom > .ant-tabs-nav::before,
  .ant-tabs-top > div > .ant-tabs-nav::before,
  .ant-tabs-bottom > div > .ant-tabs-nav::before {
    border-bottom: 1px solid ;
  }
  .ant-tabs-nav-wrap {
    padding: 0 20px;
  }


  .ant-tree-checkbox-inner {
    border: 1px solid;
  }
}
</style>
