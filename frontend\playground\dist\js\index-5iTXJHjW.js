import{_ as x}from"./doc-button.vue_vue_type_script_setup_true_lang-BzJdr9vE.js";import{_ as g}from"./auto-height-demo.vue_vue_type_script_setup_true_lang-gyxDbEZK.js";import{_ as S}from"./base-demo.vue_vue_type_script_setup_true_lang-BWM8GvnJ.js";import{_ as V}from"./dynamic-demo.vue_vue_type_script_setup_true_lang-CmGpI3z0.js";import{_ as H}from"./form-drawer-demo.vue_vue_type_script_setup_true_lang-B3Xt5Qco.js";import{_ as N}from"./shared-data-demo.vue_vue_type_script_setup_true_lang-BgKX7Slp.js";import{B as r}from"./bootstrap-DShsrVit.js";import{C as s}from"./index-B_b7xM74.js";import{_ as v}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";import{a4 as F,af as T,ag as U,ah as o,a3 as e,n as a,ap as m,an as n}from"../jse/index-index-BMh_AyeW.js";import{u as i}from"./use-drawer-Qcdpj8Bl.js";import"./form-DnT3S1ma.js";import"./x-B-ntYT_e.js";import"./loading-Cqdke3S1.js";const Y=F({__name:"index",setup(j){const[d,u]=i({connectedComponent:S}),[c,D]=i({connectedComponent:g}),[_,p]=i({connectedComponent:V}),[w,l]=i({connectedComponent:N}),[y,f]=i({connectedComponent:H});function C(){u.open()}function b(){D.open()}function k(){p.open()}function A(){p.setState({title:"外部动态标题"}),p.open()}function B(){l.setData({content:"外部传递的数据 content",payload:"外部传递的数据 payload"}),l.open()}function $(){f.setData({values:{field1:"abc",field2:"123"}}),f.open()}return(q,t)=>(T(),U(e(v),{description:"抽屉组件通常用于在当前页面上显示一个覆盖层，用以展示重要信息或提供用户交互界面。",title:"抽屉组件示例"},{extra:o(()=>[a(x,{path:"/components/common-ui/vben-drawer"})]),default:o(()=>[a(e(d)),a(e(c)),a(e(_)),a(e(w)),a(e(y)),a(e(s),{class:"mb-4",title:"基本使用"},{default:o(()=>[t[1]||(t[1]=m("p",{class:"mb-3"},"一个基础的抽屉示例",-1)),a(e(r),{type:"primary",onClick:C},{default:o(()=>t[0]||(t[0]=[n("打开抽屉")])),_:1})]),_:1}),a(e(s),{class:"mb-4",title:"内容高度自适应滚动"},{default:o(()=>[t[3]||(t[3]=m("p",{class:"mb-3"},"可根据内容自动计算滚动高度",-1)),a(e(r),{type:"primary",onClick:b},{default:o(()=>t[2]||(t[2]=[n("打开抽屉")])),_:1})]),_:1}),a(e(s),{class:"mb-4",title:"动态配置示例"},{default:o(()=>[t[6]||(t[6]=m("p",{class:"mb-3"},"通过 setState 动态调整抽屉数据",-1)),a(e(r),{type:"primary",onClick:k},{default:o(()=>t[4]||(t[4]=[n("打开抽屉")])),_:1}),a(e(r),{class:"ml-2",type:"primary",onClick:A},{default:o(()=>t[5]||(t[5]=[n(" 从外部修改标题并打开 ")])),_:1})]),_:1}),a(e(s),{class:"mb-4",title:"内外数据共享示例"},{default:o(()=>[t[8]||(t[8]=m("p",{class:"mb-3"},"通过共享 sharedData 来进行数据交互",-1)),a(e(r),{type:"primary",onClick:B},{default:o(()=>t[7]||(t[7]=[n(" 打开抽屉并传递数据 ")])),_:1})]),_:1}),a(e(s),{class:"mb-4",title:"表单抽屉示例"},{default:o(()=>[t[10]||(t[10]=m("p",{class:"mb-3"},"打开抽屉并设置表单schema以及数据",-1)),a(e(r),{type:"primary",onClick:$},{default:o(()=>t[9]||(t[9]=[n(" 打开抽屉并设置表单schema以及数据 ")])),_:1})]),_:1})]),_:1}))}});export{Y as default};
