import { requestClient } from '#/api/request';

enum Api {
  deleteOne = '/sys/tableWhiteList/delete',
  edit = '/geo-scene/update',
  list = '/geo-scene/page',
  logs = '/data/logs',
  metadata = '/data/metadata',
  preview = '/data/preview',
  save = '/geo-scene/add',
}

/**
 * 保存
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const save = (params) => {
  let nParams = {...params};
  delete nParams.regionCode;
  return requestClient.post(Api.save, nParams);
};

/**
 * 修改
 * @param params
 * @param isUpdate 是否是更新数据
 */
export const edit = (params,id) => {
  let nParams = {...params};
  delete nParams.regionCode;
  return requestClient.put(`/geo-scene/update/${id}`, nParams);
};

/**
 * 更新文件状态
 * @param params
 */
export const updateFileState = (params) => {
  return requestClient.put(`/geo-scene/update-file`, { ...params });
};

/**
 * 列表接口
 * @param params
 */
export const list = (params) => {
  return requestClient.post(Api.list, {
    ...params,
  });
};

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (dataType,id, handleSuccess) => {
  return requestClient.delete(`/geo-scene/${dataType}/${id}`, {  }).then(() => {
    handleSuccess();
  });
};

export const getMetadata = (dataType,id) => {
  return requestClient.get(`/geo-scene/detail/${dataType}/${id}`, {
  });
};

//获取图层列表
export const getLayers = (dataType,id) => {
  return requestClient.get(`/geo-scene/layers/${dataType}/${id}`, {
  });
};

//获取图层元数据
export const getLayerDatas = (dataType,id) => {
  return requestClient.get(`/geo-scene/layer/metadata/${dataType}/${id}`, {
  });
};

//获取三维tile数据：用于dataType为osgb或tiles数据的预览
export const getPreview3dDatas = (layerId) => {
  return requestClient.get(`/layer-data/preview/3d/${layerId}/tileset.json`, {
  });
};

//获取栅格预览图片：用于dataType为dom或dem数据的预览
export const getPreviewRasterDatas = (layerId) => {
  return requestClient.get(`/layer-data/preview/raster/${layerId}`, {
  });
};



