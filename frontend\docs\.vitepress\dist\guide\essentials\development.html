<!DOCTYPE html>
<html lang="zh-Hans" dir="ltr">
  <head>
    <meta charset="utf-8">
    
    <title>本地开发 | Vben Admin</title>
    <meta name="description" content="Vben Admin & 企业级管理系统框架">
    <meta name="generator" content="VitePress v1.5.0">
    <link rel="preload stylesheet" href="/assets/style.PjDwLoZW.css" as="style">
    <link rel="preload stylesheet" href="/vp-icons.css" as="style">
    
    <script type="module" src="/assets/app.BR3tRqYw.js"></script>
    <link rel="preload" href="/assets/inter-roman-latin.Di8DUHzh.woff2" as="font" type="font/woff2" crossorigin="">
    <link rel="modulepreload" href="/assets/chunks/theme.TDvSnEYR.js">
    <link rel="modulepreload" href="/assets/chunks/framework.C8U7mBUf.js">
    <link rel="modulepreload" href="/assets/chunks/devtools.CyZ89dsO.js">
    <link rel="modulepreload" href="/assets/guide_essentials_development.md.CF3SXatH.lean.js">
    <meta content="Vbenjs Team" name="author">
    <meta content="vben, vitejs, vite, shacdn-ui, vue" name="keywords">
    <link href="/favicon.ico" rel="icon" type="image/svg+xml">
    <meta content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" name="viewport">
    <meta content="vben admin docs" name="keywords">
    <link href="/favicon.ico" rel="icon">
    <script id="check-dark-mode">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"dark",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
    <script id="check-mac-os">document.documentElement.classList.toggle("mac",/Mac|iPhone|iPod|iPad/i.test(navigator.platform));</script>
    <link rel="manifest" href="/manifest.webmanifest">
    <script id="vite-plugin-pwa:register-sw" src="/registerSW.js"></script>
  </head>
  <body>
    <div id="app"><!--[--><!--[--><!--[--><div class="Layout" data-v-8ee6be8d><!--[--><!--]--><!--[--><span tabindex="-1" data-v-6df81371></span><a href="#VPContent" class="VPSkipLink visually-hidden" data-v-6df81371> Skip to content </a><!--]--><!----><header class="VPNav" data-v-8ee6be8d data-v-7bd04492><div class="VPNavBar" data-v-7bd04492 data-v-8424412b><div class="wrapper" data-v-8424412b><div class="container" data-v-8424412b><div class="title" data-v-8424412b><div class="VPNavBarTitle has-sidebar" data-v-8424412b data-v-597c1cdc><a class="title" href="/" data-v-597c1cdc><!--[--><!--]--><!--[--><img class="VPImage logo" src="https://unpkg.com/@vbenjs/static-source@0.1.7/source/logo-v1.webp" alt data-v-0d18bd34><!--]--><span data-v-597c1cdc>Vben Admin</span><!--[--><!--]--></a></div></div><div class="content" data-v-8424412b><div class="content-body" data-v-8424412b><!--[--><!--]--><div class="VPNavBarSearch search" data-v-8424412b><!--[--><!----><div id="local-search"><button type="button" class="DocSearch DocSearch-Button" aria-label="搜索文档"><span class="DocSearch-Button-Container"><span class="vp-icon DocSearch-Search-Icon"></span><span class="DocSearch-Button-Placeholder">搜索文档</span></span><span class="DocSearch-Button-Keys"><kbd class="DocSearch-Button-Key"></kbd><kbd class="DocSearch-Button-Key">K</kbd></span></button></div><!--]--></div><nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu menu" data-v-8424412b data-v-1fe73b82><span id="main-nav-aria-label" class="visually-hidden" data-v-1fe73b82> Main Navigation </span><!--[--><!--[--><div class="VPFlyout VPNavBarMenuGroup active" data-v-1fe73b82 data-v-27308979><button type="button" class="button" aria-haspopup="true" aria-expanded="false" data-v-27308979><span class="text" data-v-27308979><!----><span data-v-27308979>文档</span><span class="vpi-chevron-down text-icon" data-v-27308979></span></span></button><div class="menu" data-v-27308979><div class="VPMenu" data-v-27308979 data-v-b1bc7863><div class="items" data-v-b1bc7863><!--[--><!--[--><div class="VPMenuLink" data-v-b1bc7863 data-v-c01e5ae6><a class="VPLink link active" href="/guide/introduction/vben.html" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>指南</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-b1bc7863 data-v-c01e5ae6><a class="VPLink link" href="/components/introduction.html" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>组件</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuGroup" data-v-b1bc7863 data-v-85893c1c><p class="title" data-v-85893c1c>历史版本</p><!--[--><!--[--><div class="VPMenuLink" data-v-85893c1c data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://doc.vvbin.cn" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>2.x版本文档</span><!--]--></a></div><!--]--><!--]--></div><!--]--><!--]--></div><!--[--><!--]--></div></div></div><!--]--><!--[--><div class="VPFlyout VPNavBarMenuGroup" data-v-1fe73b82 data-v-27308979><button type="button" class="button" aria-haspopup="true" aria-expanded="false" data-v-27308979><span class="text" data-v-27308979><!----><span data-v-27308979>演示</span><span class="vpi-chevron-down text-icon" data-v-27308979></span></span></button><div class="menu" data-v-27308979><div class="VPMenu" data-v-27308979 data-v-b1bc7863><div class="items" data-v-b1bc7863><!--[--><!--[--><div class="VPMenuGroup" data-v-b1bc7863 data-v-85893c1c><p class="title" data-v-85893c1c>Vben Admin</p><!--[--><!--[--><div class="VPMenuLink" data-v-85893c1c data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://www.vben.pro" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>演示版本</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-85893c1c data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://ant.vben.pro" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Ant Design Vue 版本</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-85893c1c data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://naive.vben.pro" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Naive 版本</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-85893c1c data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://ele.vben.pro" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Element Plus版本</span><!--]--></a></div><!--]--><!--]--></div><!--]--><!--[--><div class="VPMenuGroup" data-v-b1bc7863 data-v-85893c1c><p class="title" data-v-85893c1c>其他</p><!--[--><!--[--><div class="VPMenuLink" data-v-85893c1c data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://vben.vvbin.cn" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>Vben Admin 2.x</span><!--]--></a></div><!--]--><!--]--></div><!--]--><!--]--></div><!--[--><!--]--></div></div></div><!--]--><!--[--><div class="VPFlyout VPNavBarMenuGroup" data-v-1fe73b82 data-v-27308979><button type="button" class="button" aria-haspopup="true" aria-expanded="false" data-v-27308979><span class="text" data-v-27308979><!----><span data-v-27308979>5.4.6</span><span class="vpi-chevron-down text-icon" data-v-27308979></span></span></button><div class="menu" data-v-27308979><div class="VPMenu" data-v-27308979 data-v-b1bc7863><div class="items" data-v-b1bc7863><!--[--><!--[--><div class="VPMenuLink" data-v-b1bc7863 data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://github.com/vbenjs/vue-vben-admin/releases" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>更新日志</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-b1bc7863 data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://github.com/orgs/vbenjs/projects/5" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>路线图</span><!--]--></a></div><!--]--><!--[--><div class="VPMenuLink" data-v-b1bc7863 data-v-c01e5ae6><a class="VPLink link vp-external-link-icon" href="https://github.com/vbenjs/vue-vben-admin/blob/main/.github/contributing.md" target="_blank" rel="noreferrer" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>贡献</span><!--]--></a></div><!--]--><!--]--></div><!--[--><!--]--></div></div></div><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/commercial/technical-support.html" tabindex="0" data-v-1fe73b82 data-v-d3981cb9><!--[--><span data-v-d3981cb9>🦄 技术支持</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/sponsor/personal.html" tabindex="0" data-v-1fe73b82 data-v-d3981cb9><!--[--><span data-v-d3981cb9>✨ 赞助</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/commercial/community.html" tabindex="0" data-v-1fe73b82 data-v-d3981cb9><!--[--><span data-v-d3981cb9>👨‍👦‍👦 社区</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/friend-links/" tabindex="0" data-v-1fe73b82 data-v-d3981cb9><!--[--><span data-v-d3981cb9>🤝 友情链接</span><!--]--></a><!--]--><!--]--></nav><div class="VPFlyout VPNavBarTranslations translations" data-v-8424412b data-v-d8f13475 data-v-27308979><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="多语言" data-v-27308979><span class="text" data-v-27308979><span class="vpi-languages option-icon" data-v-27308979></span><!----><span class="vpi-chevron-down text-icon" data-v-27308979></span></span></button><div class="menu" data-v-27308979><div class="VPMenu" data-v-27308979 data-v-b1bc7863><!----><!--[--><!--[--><div class="items" data-v-d8f13475><p class="title" data-v-d8f13475>简体中文</p><!--[--><div class="VPMenuLink" data-v-d8f13475 data-v-c01e5ae6><a class="VPLink link" href="/en/guide/essentials/development.html" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>English</span><!--]--></a></div><!--]--></div><!--]--><!--]--></div></div></div><div class="VPNavBarAppearance appearance" data-v-8424412b data-v-3be21465><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title aria-checked="true" data-v-3be21465 data-v-169bc600 data-v-013e1365><span class="check" data-v-013e1365><span class="icon" data-v-013e1365><!--[--><span class="vpi-sun sun" data-v-169bc600></span><span class="vpi-moon moon" data-v-169bc600></span><!--]--></span></span></button></div><div class="VPSocialLinks VPNavBarSocialLinks social-links" data-v-8424412b data-v-f68fde0a data-v-4af35d43><!--[--><a class="VPSocialLink no-icon" href="https://github.com/vbenjs/vue-vben-admin" aria-label="github" target="_blank" rel="noopener" data-v-4af35d43 data-v-33e06d91><span class="vpi-social-github"></span></a><!--]--></div><div class="VPFlyout VPNavBarExtra extra" data-v-8424412b data-v-931559f6 data-v-27308979><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="extra navigation" data-v-27308979><span class="vpi-more-horizontal icon" data-v-27308979></span></button><div class="menu" data-v-27308979><div class="VPMenu" data-v-27308979 data-v-b1bc7863><!----><!--[--><!--[--><div class="group translations" data-v-931559f6><p class="trans-title" data-v-931559f6>简体中文</p><!--[--><div class="VPMenuLink" data-v-931559f6 data-v-c01e5ae6><a class="VPLink link" href="/en/guide/essentials/development.html" data-v-c01e5ae6><!--[--><span data-v-c01e5ae6>English</span><!--]--></a></div><!--]--></div><div class="group" data-v-931559f6><div class="item appearance" data-v-931559f6><p class="label" data-v-931559f6>主题</p><div class="appearance-action" data-v-931559f6><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title aria-checked="true" data-v-931559f6 data-v-169bc600 data-v-013e1365><span class="check" data-v-013e1365><span class="icon" data-v-013e1365><!--[--><span class="vpi-sun sun" data-v-169bc600></span><span class="vpi-moon moon" data-v-169bc600></span><!--]--></span></span></button></div></div></div><div class="group" data-v-931559f6><div class="item social-links" data-v-931559f6><div class="VPSocialLinks social-links-list" data-v-931559f6 data-v-4af35d43><!--[--><a class="VPSocialLink no-icon" href="https://github.com/vbenjs/vue-vben-admin" aria-label="github" target="_blank" rel="noopener" data-v-4af35d43 data-v-33e06d91><span class="vpi-social-github"></span></a><!--]--></div></div></div><!--]--><!--]--></div></div></div><!--[--><!--]--><button type="button" class="VPNavBarHamburger hamburger" aria-label="mobile navigation" aria-expanded="false" aria-controls="VPNavScreen" data-v-8424412b data-v-f79c6a8f><span class="container" data-v-f79c6a8f><span class="top" data-v-f79c6a8f></span><span class="middle" data-v-f79c6a8f></span><span class="bottom" data-v-f79c6a8f></span></span></button></div></div></div></div><div class="divider" data-v-8424412b><div class="divider-line" data-v-8424412b></div></div></div><!----></header><div class="VPLocalNav has-sidebar empty" data-v-8ee6be8d data-v-23aa38bb><div class="container" data-v-23aa38bb><button class="menu" aria-expanded="false" aria-controls="VPSidebarNav" data-v-23aa38bb><span class="vpi-align-left menu-icon" data-v-23aa38bb></span><span class="menu-text" data-v-23aa38bb>菜单</span></button><div class="VPLocalNavOutlineDropdown" style="--vp-vh:0px;" data-v-23aa38bb data-v-34175b7f><button data-v-34175b7f>回到顶部</button><!----></div></div></div><aside class="VPSidebar" data-v-8ee6be8d data-v-831f1005><div class="curtain" data-v-831f1005></div><nav class="nav" id="VPSidebarNav" aria-labelledby="sidebar-aria-label" tabindex="-1" data-v-831f1005><span class="visually-hidden" id="sidebar-aria-label" data-v-831f1005> Sidebar Navigation </span><!--[--><!--]--><!--[--><div class="no-transition group" data-v-7983d959><section class="VPSidebarItem level-0 collapsible" data-v-7983d959 data-v-6f438999><div class="item" role="button" tabindex="0" data-v-6f438999><div class="indicator" data-v-6f438999></div><h2 class="text" data-v-6f438999>简介</h2><div class="caret" role="button" aria-label="toggle section" tabindex="0" data-v-6f438999><span class="vpi-chevron-right caret-icon" data-v-6f438999></span></div></div><div class="items" data-v-6f438999><!--[--><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/introduction/vben.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>关于 Vben Admin</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/introduction/why.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>为什么选择我们?</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/introduction/quick-start.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>快速开始</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/introduction/thin.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>精简版本</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/components/introduction.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>组件文档</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><div class="no-transition group" data-v-7983d959><section class="VPSidebarItem level-0 has-active" data-v-7983d959 data-v-6f438999><div class="item" role="button" tabindex="0" data-v-6f438999><div class="indicator" data-v-6f438999></div><h2 class="text" data-v-6f438999>基础</h2><!----></div><div class="items" data-v-6f438999><!--[--><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/essentials/concept.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>基础概念</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/essentials/development.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>本地开发</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/essentials/route.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>路由和菜单</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/essentials/settings.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>配置</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/essentials/icons.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>图标</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/essentials/styles.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>样式</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/essentials/external-module.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>外部模块</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/essentials/build.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>构建与部署</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/essentials/server.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>服务端交互与数据Mock</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><div class="no-transition group" data-v-7983d959><section class="VPSidebarItem level-0" data-v-7983d959 data-v-6f438999><div class="item" role="button" tabindex="0" data-v-6f438999><div class="indicator" data-v-6f438999></div><h2 class="text" data-v-6f438999>深入</h2><!----></div><div class="items" data-v-6f438999><!--[--><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/in-depth/login.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>登录</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/in-depth/theme.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>主题</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/in-depth/access.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>权限</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/in-depth/locale.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>国际化</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/in-depth/features.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>常用功能</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/in-depth/check-updates.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>检查更新</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/in-depth/loading.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>全局loading</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/in-depth/ui-framework.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>组件库切换</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><div class="no-transition group" data-v-7983d959><section class="VPSidebarItem level-0" data-v-7983d959 data-v-6f438999><div class="item" role="button" tabindex="0" data-v-6f438999><div class="indicator" data-v-6f438999></div><h2 class="text" data-v-6f438999>工程</h2><!----></div><div class="items" data-v-6f438999><!--[--><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/project/standard.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>规范</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/project/cli.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>CLI</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/project/dir.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>目录说明</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/project/test.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>单元测试</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/project/tailwindcss.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Tailwind CSS</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/project/changeset.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Changeset</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/project/vite.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>Vite Config</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><div class="no-transition group" data-v-7983d959><section class="VPSidebarItem level-0" data-v-7983d959 data-v-6f438999><div class="item" role="button" tabindex="0" data-v-6f438999><div class="indicator" data-v-6f438999></div><h2 class="text" data-v-6f438999>其他</h2><!----></div><div class="items" data-v-6f438999><!--[--><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/other/project-update.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>项目更新</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/other/remove-code.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>移除代码</p><!--]--></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-6f438999 data-v-6f438999><div class="item" data-v-6f438999><div class="indicator" data-v-6f438999></div><a class="VPLink link link" href="/guide/other/faq.html" data-v-6f438999><!--[--><p class="text" data-v-6f438999>常见问题</p><!--]--></a><!----></div><!----></div><!--]--></div></section></div><!--]--><!--[--><!--]--></nav></aside><div class="VPContent has-sidebar" id="VPContent" data-v-8ee6be8d data-v-5ebfaf97><div class="VPDoc has-sidebar has-aside" data-v-5ebfaf97 data-v-243c1dc3><!--[--><!--]--><div class="container" data-v-243c1dc3><div class="aside" data-v-243c1dc3><div class="aside-curtain" data-v-243c1dc3></div><div class="aside-container" data-v-243c1dc3><div class="aside-content" data-v-243c1dc3><div class="VPDocAside" data-v-243c1dc3 data-v-8347cfb5><!--[--><!--]--><!--[--><!--]--><nav aria-labelledby="doc-outline-aria-label" class="VPDocAsideOutline" data-v-8347cfb5 data-v-2f034185><div class="content" data-v-2f034185><div class="outline-marker" data-v-2f034185></div><div aria-level="2" class="outline-title" id="doc-outline-aria-label" role="heading" data-v-2f034185>页面导航</div><ul class="VPDocOutlineItem root" data-v-2f034185 data-v-743dbc50><!--[--><!--]--></ul></div></nav><!--[--><!--]--><div class="spacer" data-v-8347cfb5></div><!--[--><!--]--><!----><!--[--><!--]--><!--[--><!--]--></div></div></div></div><div class="content" data-v-243c1dc3><div class="content-container" data-v-243c1dc3><!--[--><!--]--><main class="main" data-v-243c1dc3><div style="position:relative;" class="vp-doc _guide_essentials_development" data-v-243c1dc3><div><h1 id="development" tabindex="-1">本地开发 <a class="header-anchor" href="#development" aria-label="Permalink to &quot;本地开发 {#development}&quot;">​</a></h1><div class="tip custom-block"><p class="custom-block-title">代码获取</p><p>如果你还没有获取代码，可以先从 <a href="./../introduction/quick-start.html">快速开始</a> 处开始阅读文档。</p></div><h2 id="前置准备" tabindex="-1">前置准备 <a class="header-anchor" href="#前置准备" aria-label="Permalink to &quot;前置准备&quot;">​</a></h2><p>为了更好的开发体验，我们提供了一些工具配置、项目说明，以便于您更好的开发。</p><h3 id="需要掌握的基础知识" tabindex="-1">需要掌握的基础知识 <a class="header-anchor" href="#需要掌握的基础知识" aria-label="Permalink to &quot;需要掌握的基础知识&quot;">​</a></h3><p>本项目需要一定前端基础知识，请确保掌握 Vue 的基础知识，以便能处理一些常见的问题。建议在开发前先学一下以下内容，提前了解和学习这些知识，会对项目理解非常有帮助:</p><ul><li><a href="https://vuejs.org/" target="_blank" rel="noreferrer">Vue3</a></li><li><a href="https://tailwindcss.com/" target="_blank" rel="noreferrer">Tailwind CSS</a></li><li><a href="https://www.typescriptlang.org/" target="_blank" rel="noreferrer">TypeScript</a></li><li><a href="https://router.vuejs.org/" target="_blank" rel="noreferrer">Vue Router</a></li><li><a href="https://vitejs.dev/" target="_blank" rel="noreferrer">Vitejs</a></li><li><a href="https://pnpm.io/" target="_blank" rel="noreferrer">Pnpm</a></li><li><a href="https://turbo.build/" target="_blank" rel="noreferrer">Turbo</a></li></ul><h3 id="工具配置" tabindex="-1">工具配置 <a class="header-anchor" href="#工具配置" aria-label="Permalink to &quot;工具配置&quot;">​</a></h3><p>如果您使用的 IDE 是<a href="https://code.visualstudio.com/" target="_blank" rel="noreferrer">vscode</a>(推荐)的话，可以安装以下工具来提高开发效率及代码格式化:</p><ul><li><a href="https://marketplace.visualstudio.com/items?itemName=Vue.volar" target="_blank" rel="noreferrer">Vue - Official</a> - Vue 官方插件（必备）。</li><li><a href="https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss" target="_blank" rel="noreferrer">Tailwind CSS</a> - Tailwindcss 提示插件。</li><li><a href="https://marketplace.visualstudio.com/items?itemName=bradlc.vunguyentuan.vscode-css-variables" target="_blank" rel="noreferrer">CSS Variable Autocomplete</a> - Css 变量提示插件。</li><li><a href="https://marketplace.visualstudio.com/items?itemName=antfu.iconify" target="_blank" rel="noreferrer">Iconify IntelliSense</a> - Iconify 图标插件</li><li><a href="https://marketplace.visualstudio.com/items?itemName=Lokalise.i18n-ally" target="_blank" rel="noreferrer">i18n Ally</a> - i18n 插件</li><li><a href="https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint" target="_blank" rel="noreferrer">ESLint</a> - 脚本代码检查</li><li><a href="https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode" target="_blank" rel="noreferrer">Prettier</a> - 代码格式化</li><li><a href="https://marketplace.visualstudio.com/items?itemName=stylelint.vscode-stylelint" target="_blank" rel="noreferrer">Stylelint</a> - css 格式化</li><li><a href="https://marketplace.visualstudio.com/items?itemName=streetsidesoftware.code-spell-checker" target="_blank" rel="noreferrer">Code Spell Checker</a> - 单词语法检查</li><li><a href="https://marketplace.visualstudio.com/items?itemName=mikestead.dotenv" target="_blank" rel="noreferrer">DotENV</a> - .env 文件 高亮</li></ul><h2 id="npm-scripts" tabindex="-1">Npm Scripts <a class="header-anchor" href="#npm-scripts" aria-label="Permalink to &quot;Npm Scripts&quot;">​</a></h2><p>npm 脚本是项目常见的配置，用于执行一些常见的任务，比如启动项目、打包项目等。以下的脚本都可以在项目根目录的 <code>package.json</code> 文件中找到。</p><p>执行方式为：<code>pnpm run [script]</code> 或 <code>npm run [script]</code>。</p><div class="language-json vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">{</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  &quot;scripts&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: {</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 构建项目</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;cross-env NODE_OPTIONS=--max-old-space-size=8192 turbo build&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 构建项目并分析</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:analyze&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;turbo build:analyze&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 构建本地 docker 镜像</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:docker&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;./build-local-docker-image.sh&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 单独构建 web-antd 应用</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:antd&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/web-antd&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 单独构建文档</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:docs&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/docs&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 单独构建 web-ele 应用</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:ele&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/web-ele&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 单独构建 web-naive 应用</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:naive&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/naive&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 单独构建 playground 应用</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;build:play&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run build --filter=@vben/playground&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // changeset 版本管理</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;changeset&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm exec changeset&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 检查项目各种问题</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;check&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm run check:circular &amp;&amp; pnpm run check:dep &amp;&amp; pnpm run check:type &amp;&amp; pnpm check:cspell&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 检查循环引用</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;check:circular&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;vsh check-circular&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 检查拼写</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;check:cspell&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;cspell lint **/*.ts **/README.md .changeset/*.md --no-progress&quot;</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 检查依赖</span></span>
<span class="line"><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">    &quot;check:dep&quot;</span><span style="--shiki-light:#B31D28;--shiki-light-font-style:italic;--shiki-dark:#FDAEB7;--shiki-dark-font-style:italic;">:</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;vsh check-dep&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 检查类型</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;check:type&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;turbo run typecheck&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 清理项目（删除node_modules、dist、.turbo）等目录</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;clean&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;node ./scripts/clean.mjs&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 提交代码</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;commit&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;czg&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 启动项目（默认会运行整个仓库所有包的dev脚本）</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;turbo-run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 启动web-antd应用</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:antd&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/web-antd run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 启动文档</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:docs&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/docs run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 启动web-ele应用</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:ele&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/web-ele run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 启动web-naive应用</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:naive&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/web-naive run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 启动演示应用</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;dev:play&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -F @vben/playground run dev&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 格式化代码</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;format&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;vsh lint --format&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // lint 代码</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;lint&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;vsh lint&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 依赖安装完成之后，执行所有包的stub脚本</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;postinstall&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm -r run stub --if-present&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 只允许使用pnpm</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;preinstall&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;npx only-allow pnpm&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // husky的安装</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;prepare&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;is-ci || husky&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 预览应用</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;preview&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;turbo-run preview&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 包规范检查</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;publint&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;vsh publint&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 删除所有的node_modules、yarn.lock、package.lock.json，重新安装依赖</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;reinstall&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm clean --del-lock &amp;&amp; pnpm install&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 运行 vitest 单元测试</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;test:unit&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;vitest run --dom&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // 更新项目依赖</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;update:deps&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot; pnpm update --latest --recursive&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">,</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;">    // changeset生成提交集</span></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">    &quot;version&quot;</span><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">: </span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;pnpm exec changeset version &amp;&amp; pnpm install --no-frozen-lockfile&quot;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">  }</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">}</span></span></code></pre></div><h2 id="本地运行项目" tabindex="-1">本地运行项目 <a class="header-anchor" href="#本地运行项目" aria-label="Permalink to &quot;本地运行项目&quot;">​</a></h2><p>如需本地运行文档，并进行调整，可以执行以下命令，执行该命令，你可以选择需要的应用进行开发：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> dev</span></span></code></pre></div><p>如果你想直接运行某个应用，可以执行以下命令：</p><p>运行 <code>web-antd</code> 应用：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> dev:antd</span></span></code></pre></div><p>运行 <code>web-naive</code> 应用：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> dev:naive</span></span></code></pre></div><p>运行 <code>web-ele</code> 应用：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> dev:ele</span></span></code></pre></div><p>运行 <code>docs</code> 应用：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> dev:docs</span></span></code></pre></div><h2 id="公共静态资源" tabindex="-1">公共静态资源 <a class="header-anchor" href="#公共静态资源" aria-label="Permalink to &quot;公共静态资源&quot;">​</a></h2><p>项目中需要使用到的公共静态资源，如：图片、静态HTML等，需要在开发中通过 <code>src=&quot;/xxx.png&quot;</code> 直接引入的。</p><p>需要将资源放在对应项目的 <code>public/static</code> 目录下。引入的路径为：<code>src=&quot;/static/xxx.png&quot;</code>。</p><h2 id="devtools" tabindex="-1">DevTools <a class="header-anchor" href="#devtools" aria-label="Permalink to &quot;DevTools&quot;">​</a></h2><p>项目内置了 <a href="https://github.com/vuejs/devtools-next" target="_blank" rel="noreferrer">Vue DevTools</a> 插件，可以在开发过程中使用。默认关闭，可在<code>.env.development</code> 内开启，并重新运行项目即可：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">VITE_DEVTOOLS</span><span style="--shiki-light:#D73A49;--shiki-dark:#F97583;">=</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">true</span></span></code></pre></div><p>开启后，项目运行会在页面底部显示一个 Vue DevTools 的图标，点击即可打开 DevTools。</p><p><img src="/guide/devtools.png" alt="Vue DevTools"></p><h2 id="本地运行文档" tabindex="-1">本地运行文档 <a class="header-anchor" href="#本地运行文档" aria-label="Permalink to &quot;本地运行文档&quot;">​</a></h2><p>如需本地运行文档，并进行调整，可以执行以下命令：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> dev:docs</span></span></code></pre></div><h2 id="问题解决" tabindex="-1">问题解决 <a class="header-anchor" href="#问题解决" aria-label="Permalink to &quot;问题解决&quot;">​</a></h2><p>如果你在使用过程中遇到依赖相关的问题，可以尝试以下重新安装依赖：</p><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 请在项目根目录下执行</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 该命令会删除整个仓库所有的 node_modules、yarn.lock、package.lock.json后</span></span>
<span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 再进行依赖重新安装（安装速度会明显变慢）。</span></span>
<span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> reinstall</span></span></code></pre></div><!--[--><h2 id="贡献者">贡献者 <a class="header-anchor" href="#贡献者" aria-label="Permalink to &#39;贡献者&#39;"></a></h2><div class="vp-nolebase-git-changelog vp-nolebase-git-changelog-contributors vp-nolebase-git-changelog-contributors-container vp-nolebase-git-changelog-contributors-list" flex flex-wrap gap-4 pt-2><!--[--><!--[--><div class="flex items-center gap-2"><img src="https://gravatar.com/avatar/6562db53c1fe917b9bc26347d1e1ab8e6c701cc11f88dda1880ea151d54cde52?d=retro" alt="The avatar of contributor named as linbing" class="h-8 w-8 rounded-full"> linbing</div><!--]--><!--]--></div><!--]--><!--[--><h2 id="页面历史" data-v-73eeb447>页面历史 <a class="header-anchor" href="#页面历史" aria-label="Permalink to &#39;页面历史&#39;" data-v-73eeb447></a></h2><div class="bg-$vp-custom-block-details-bg vp-nolebase-git-changelog vp-nolebase-git-changelog-history vp-nolebase-git-changelog-history-list vp-nolebase-git-changelog-history-container" rounded-lg p-4 data-v-73eeb447><label cursor-pointer data-v-73eeb447><div class="vp-nolebase-git-changelog-title flex select-none items-center justify-between" transition="color ease-in-out" text="&lt;sm:xs" duration-200 data-v-73eeb447><span class="vp-nolebase-git-changelog-last-edited-title inline-flex items-center gap-3" data-v-73eeb447><div class="i-octicon:history-16" data-v-73eeb447></div><span data-v-73eeb447>最后编辑于 6 个月前</span></span><div class="i-octicon:sort-desc-16" ml-auto mr-4 cursor-pointer data-v-73eeb447></div><span class="vp-nolebase-git-changelog-view-full-history-title inline-flex cursor-pointer items-center gap-3" data-v-73eeb447><span class="&lt;sm:hidden" data-v-73eeb447>查看完整历史</span><svg class="rotate-0 i-octicon:chevron-down-16" transition="transform ease-in-out" duration-200 data-v-73eeb447></svg></span></div></label><div style="display:none;" class="grid grid-cols-[30px_auto] mt-3 gap-1.5 children:my-auto -ml-1.5" text="&lt;sm:xs" data-v-73eeb447><!--[--><!--[--><!--[--><div class="i-octicon:git-commit-16 m-auto rotate-90 transform op-30" data-v-09c8433a></div><div flex gap-1 align-baseline data-v-09c8433a><a href="https://github.com/vbenjs/vue-vben-admin/commit/b1be5e06d283e34342b7792083ddb2b847e78c3a" target="_blank" class="no-icon" data-v-09c8433a><code class="text-xs text-$vp-c-brand-1 hover:text-$vp-c-brand-1" transition="color ease-in-out" duration-200 data-v-09c8433a>b1be5e0</code></a><span data-v-09c8433a>-</span><span data-v-09c8433a><span class="text-sm &lt;sm:text-xs" data-v-09c8433a>vben admin 框架替换部分代码</span><!----><!----></span></div><!--]--><!--]--><!--]--></div></div><!--]--></div></div></main><footer class="VPDocFooter" data-v-243c1dc3 data-v-9f803837><!--[--><!--]--><div class="edit-info" data-v-9f803837><div class="edit-link" data-v-9f803837><a class="VPLink link vp-external-link-icon no-icon edit-link-button" href="https://github.com/vbenjs/vue-vben-admin/edit/main/docs/src/guide/essentials/development.md" target="_blank" rel="noreferrer" data-v-9f803837><!--[--><span class="vpi-square-pen edit-link-icon" data-v-9f803837></span> 在 GitHub 上编辑此页面<!--]--></a></div><!----></div><nav class="prev-next" aria-labelledby="doc-footer-aria-label" data-v-9f803837><span class="visually-hidden" id="doc-footer-aria-label" data-v-9f803837>Pager</span><div class="pager" data-v-9f803837><a class="VPLink link pager-link prev" href="/guide/essentials/concept.html" data-v-9f803837><!--[--><span class="desc" data-v-9f803837>上一页</span><span class="title" data-v-9f803837>基础概念</span><!--]--></a></div><div class="pager" data-v-9f803837><a class="VPLink link pager-link next" href="/guide/essentials/route.html" data-v-9f803837><!--[--><span class="desc" data-v-9f803837>下一页</span><span class="title" data-v-9f803837>路由和菜单</span><!--]--></a></div></nav></footer><!--[--><!--]--></div></div></div><!--[--><!--]--></div></div><footer class="VPFooter has-sidebar" data-v-8ee6be8d data-v-3b5caf9d><div class="container" data-v-3b5caf9d><p class="message" data-v-3b5caf9d>基于 MIT 许可发布.</p><p class="copyright" data-v-3b5caf9d>Copyright © 2020-2025 Vben</p></div></footer><!--[--><!--]--></div><!--]--><!--]--><!--]--></div>
    <script>window.__VP_HASH_MAP__=JSON.parse("{\"commercial_community.md\":\"BQ4EpBPV\",\"commercial_customized.md\":\"DSawm_Q-\",\"commercial_technical-support.md\":\"BFYCk-Gb\",\"components_common-ui_vben-count-to-animator.md\":\"BhKqyXNB\",\"components_common-ui_vben-drawer.md\":\"B33mnOV3\",\"components_common-ui_vben-form.md\":\"CLzJ4fky\",\"components_common-ui_vben-modal.md\":\"Bx6murLj\",\"components_common-ui_vben-vxe-table.md\":\"CivaGbeF\",\"components_introduction.md\":\"CmwGdCoV\",\"en_guide_essentials_build.md\":\"MIih9kir\",\"en_guide_essentials_concept.md\":\"DNmIhCiA\",\"en_guide_essentials_development.md\":\"ibGZim_0\",\"en_guide_essentials_external-module.md\":\"q3dxfwiu\",\"en_guide_essentials_icons.md\":\"BjPbd9JU\",\"en_guide_essentials_route.md\":\"Dp38MXpi\",\"en_guide_essentials_server.md\":\"CSQr1B2I\",\"en_guide_essentials_settings.md\":\"DcAyDDU9\",\"en_guide_essentials_styles.md\":\"DxhGSj_6\",\"en_guide_in-depth_access.md\":\"Chfb1-SK\",\"en_guide_in-depth_check-updates.md\":\"-R9oMLqS\",\"en_guide_in-depth_features.md\":\"Bejfz70K\",\"en_guide_in-depth_layout.md\":\"D-15-xeX\",\"en_guide_in-depth_loading.md\":\"CmZJfmkY\",\"en_guide_in-depth_locale.md\":\"0Aeuao-6\",\"en_guide_in-depth_login.md\":\"BArKpw5r\",\"en_guide_in-depth_theme.md\":\"B9KJ8e5L\",\"en_guide_in-depth_ui-framework.md\":\"7omv8PEF\",\"en_guide_introduction_changelog.md\":\"8wY91cKi\",\"en_guide_introduction_quick-start.md\":\"BytLPvPF\",\"en_guide_introduction_roadmap.md\":\"40ztGFQt\",\"en_guide_introduction_thin.md\":\"CXqskMY5\",\"en_guide_introduction_vben.md\":\"BWJ5xMjU\",\"en_guide_introduction_why.md\":\"z9Xu63fv\",\"en_guide_other_faq.md\":\"BgmNWqTH\",\"en_guide_other_project-update.md\":\"CDFRjDYN\",\"en_guide_other_remove-code.md\":\"CwXtJBRo\",\"en_guide_project_changeset.md\":\"BD2tPmDu\",\"en_guide_project_cli.md\":\"C-wc3LTU\",\"en_guide_project_dir.md\":\"1HUpj9N1\",\"en_guide_project_standard.md\":\"CQc_4pMI\",\"en_guide_project_tailwindcss.md\":\"qo2umvYt\",\"en_guide_project_test.md\":\"IcLXrOtF\",\"en_guide_project_vite.md\":\"B8fkU20S\",\"en_index.md\":\"BK0EQWpa\",\"friend-links_index.md\":\"D77oQbQ-\",\"guide_essentials_build.md\":\"CQkLo245\",\"guide_essentials_concept.md\":\"ysVXMpe-\",\"guide_essentials_development.md\":\"CF3SXatH\",\"guide_essentials_external-module.md\":\"C_kmTK3-\",\"guide_essentials_icons.md\":\"D0KFfSIs\",\"guide_essentials_route.md\":\"uOqQTT6J\",\"guide_essentials_server.md\":\"DEvXY5t3\",\"guide_essentials_settings.md\":\"BLrqQw1B\",\"guide_essentials_styles.md\":\"DNNZSM1G\",\"guide_in-depth_access.md\":\"fqh6BPIB\",\"guide_in-depth_check-updates.md\":\"BOEFzrpM\",\"guide_in-depth_features.md\":\"HNLQtK2P\",\"guide_in-depth_layout.md\":\"DECXVOlM\",\"guide_in-depth_loading.md\":\"tN6N3kYp\",\"guide_in-depth_locale.md\":\"8wyk1Yqv\",\"guide_in-depth_login.md\":\"B0uIcPWn\",\"guide_in-depth_theme.md\":\"DWjDTe1g\",\"guide_in-depth_ui-framework.md\":\"Bd452hrK\",\"guide_introduction_changelog.md\":\"DvtHokp3\",\"guide_introduction_quick-start.md\":\"uxGzUiCf\",\"guide_introduction_roadmap.md\":\"BSJVqFJh\",\"guide_introduction_thin.md\":\"BkO-ybF-\",\"guide_introduction_vben.md\":\"DXXb_m7A\",\"guide_introduction_why.md\":\"7F7cA3dM\",\"guide_other_faq.md\":\"D6nD4Ts7\",\"guide_other_project-update.md\":\"NXdU7BDQ\",\"guide_other_remove-code.md\":\"BhBdmNJH\",\"guide_project_changeset.md\":\"DIv7y1Ob\",\"guide_project_cli.md\":\"CTvt9NDx\",\"guide_project_dir.md\":\"B727TzC-\",\"guide_project_standard.md\":\"egezrkKp\",\"guide_project_tailwindcss.md\":\"DGd2QTPf\",\"guide_project_test.md\":\"BdJV3p8J\",\"guide_project_vite.md\":\"t313fnW6\",\"index.md\":\"B-Mue5Vq\",\"sponsor_personal.md\":\"DDsxIVU3\"}");window.__VP_SITE_DATA__=JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"Vben Admin\",\"description\":\"A VitePress site\",\"base\":\"/\",\"head\":[],\"router\":{\"prefetchLinks\":true},\"appearance\":\"dark\",\"themeConfig\":{\"i18nRouting\":true,\"logo\":\"https://unpkg.com/@vbenjs/static-source@0.1.7/source/logo-v1.webp\",\"search\":{\"options\":{\"locales\":{\"root\":{\"placeholder\":\"搜索文档\",\"translations\":{\"button\":{\"buttonAriaLabel\":\"搜索文档\",\"buttonText\":\"搜索文档\"},\"modal\":{\"errorScreen\":{\"helpText\":\"你可能需要检查你的网络连接\",\"titleText\":\"无法获取结果\"},\"footer\":{\"closeText\":\"关闭\",\"navigateText\":\"切换\",\"searchByText\":\"搜索提供者\",\"selectText\":\"选择\"},\"noResultsScreen\":{\"noResultsText\":\"无法找到相关结果\",\"reportMissingResultsLinkText\":\"点击反馈\",\"reportMissingResultsText\":\"你认为该查询应该有结果？\",\"suggestedQueryText\":\"你可以尝试查询\"},\"searchBox\":{\"cancelButtonAriaLabel\":\"取消\",\"cancelButtonText\":\"取消\",\"resetButtonAriaLabel\":\"清除查询条件\",\"resetButtonTitle\":\"清除查询条件\"},\"startScreen\":{\"favoriteSearchesTitle\":\"收藏\",\"noRecentSearchesText\":\"没有搜索历史\",\"recentSearchesTitle\":\"搜索历史\",\"removeFavoriteSearchButtonTitle\":\"从收藏中移除\",\"removeRecentSearchButtonTitle\":\"从搜索历史中移除\",\"saveRecentSearchButtonTitle\":\"保存至搜索历史\"}}}}}},\"provider\":\"local\"},\"siteTitle\":\"Vben Admin\",\"socialLinks\":[{\"icon\":\"github\",\"link\":\"https://github.com/vbenjs/vue-vben-admin\"}]},\"locales\":{\"en\":{\"label\":\"English\",\"lang\":\"en-US\",\"link\":\"/en/\",\"description\":\"Vben Admin & Enterprise level management system framework\",\"themeConfig\":{\"darkModeSwitchLabel\":\"Theme\",\"darkModeSwitchTitle\":\"Switch to Dark Mode\",\"docFooter\":{\"next\":\"Next Page\",\"prev\":\"Previous Page\"},\"editLink\":{\"pattern\":\"https://github.com/vbenjs/vue-vben-admin/edit/main/docs/src/:path\",\"text\":\"Edit this page on GitHub\"},\"footer\":{\"copyright\":\"Copyright © 2020-2025 Vben\",\"message\":\"Released under the MIT License.\"},\"langMenuLabel\":\"Language\",\"lastUpdated\":{\"formatOptions\":{\"dateStyle\":\"short\",\"timeStyle\":\"medium\"},\"text\":\"Last updated on\"},\"lightModeSwitchTitle\":\"Switch to Light Mode\",\"nav\":[{\"activeMatch\":\"^/en/(guide|components)/\",\"text\":\"Doc\",\"items\":[{\"activeMatch\":\"^/en/guide/\",\"link\":\"/en/guide/introduction/vben\",\"text\":\"Guide\"},{\"text\":\"Historical Versions\",\"items\":[{\"link\":\"https://doc.vvbin.cn\",\"text\":\"2.x Version Documentation\"}]}]},{\"text\":\"Demo\",\"items\":[{\"text\":\"Vben Admin\",\"items\":[{\"link\":\"https://www.vben.pro\",\"text\":\"Demo Version\"},{\"link\":\"https://ant.vben.pro\",\"text\":\"Ant Design Vue Version\"},{\"link\":\"https://naive.vben.pro\",\"text\":\"Naive Version\"},{\"link\":\"https://ele.vben.pro\",\"text\":\"Element Plus Version\"}]},{\"text\":\"Others\",\"items\":[{\"link\":\"https://vben.vvbin.cn\",\"text\":\"Vben Admin 2.x\"}]}]},{\"text\":\"5.4.6\",\"items\":[{\"link\":\"https://github.com/vbenjs/vue-vben-admin/releases\",\"text\":\"Changelog\"},{\"link\":\"https://github.com/orgs/vbenjs/projects/5\",\"text\":\"Roadmap\"},{\"link\":\"https://github.com/vbenjs/vue-vben-admin/blob/main/.github/contributing.md\",\"text\":\"Contribution\"}]},{\"link\":\"/commercial/technical-support\",\"text\":\"🦄 Tech Support\"},{\"link\":\"/sponsor/personal\",\"text\":\"✨ Sponsor\"},{\"link\":\"/commercial/community\",\"text\":\"👨‍👦‍👦 Community\"},{\"link\":\"/friend-links/\",\"text\":\"🤝 Friend Links\"}],\"outline\":{\"label\":\"Navigate\"},\"returnToTopLabel\":\"Back to top\",\"sidebar\":{\"/en/commercial/\":{\"base\":\"/en/commercial/\",\"items\":[{\"link\":\"community\",\"text\":\"Community\"},{\"link\":\"technical-support\",\"text\":\"Technical-support\"},{\"link\":\"customized\",\"text\":\"Customized\"}]},\"/en/guide/\":{\"base\":\"/en/guide/\",\"items\":[{\"collapsed\":false,\"text\":\"Introduction\",\"items\":[{\"link\":\"introduction/vben\",\"text\":\"About Vben Admin\"},{\"link\":\"introduction/why\",\"text\":\"Why Choose Us?\"},{\"link\":\"introduction/quick-start\",\"text\":\"Quick Start\"},{\"link\":\"introduction/thin\",\"text\":\"Lite Version\"}]},{\"text\":\"Basics\",\"items\":[{\"link\":\"essentials/concept\",\"text\":\"Basic Concepts\"},{\"link\":\"essentials/development\",\"text\":\"Local Development\"},{\"link\":\"essentials/route\",\"text\":\"Routing and Menu\"},{\"link\":\"essentials/settings\",\"text\":\"Configuration\"},{\"link\":\"essentials/icons\",\"text\":\"Icons\"},{\"link\":\"essentials/styles\",\"text\":\"Styles\"},{\"link\":\"essentials/external-module\",\"text\":\"External Modules\"},{\"link\":\"essentials/build\",\"text\":\"Build and Deployment\"},{\"link\":\"essentials/server\",\"text\":\"Server Interaction and Data Mock\"}]},{\"text\":\"Advanced\",\"items\":[{\"link\":\"in-depth/login\",\"text\":\"Login\"},{\"link\":\"in-depth/theme\",\"text\":\"Theme\"},{\"link\":\"in-depth/access\",\"text\":\"Access Control\"},{\"link\":\"in-depth/locale\",\"text\":\"Internationalization\"},{\"link\":\"in-depth/features\",\"text\":\"Common Features\"},{\"link\":\"in-depth/check-updates\",\"text\":\"Check Updates\"},{\"link\":\"in-depth/loading\",\"text\":\"Global Loading\"},{\"link\":\"in-depth/ui-framework\",\"text\":\"UI Framework Switching\"}]},{\"text\":\"Engineering\",\"items\":[{\"link\":\"project/standard\",\"text\":\"Standards\"},{\"link\":\"project/cli\",\"text\":\"CLI\"},{\"link\":\"project/dir\",\"text\":\"Directory Explanation\"},{\"link\":\"project/test\",\"text\":\"Unit Testing\"},{\"link\":\"project/tailwindcss\",\"text\":\"Tailwind CSS\"},{\"link\":\"project/changeset\",\"text\":\"Changeset\"},{\"link\":\"project/vite\",\"text\":\"Vite Config\"}]},{\"text\":\"Others\",\"items\":[{\"link\":\"other/project-update\",\"text\":\"Project Update\"},{\"link\":\"other/remove-code\",\"text\":\"Remove Code\"},{\"link\":\"other/faq\",\"text\":\"FAQ\"}]}]}}}},\"root\":{\"label\":\"简体中文\",\"lang\":\"zh-Hans\",\"description\":\"Vben Admin & 企业级管理系统框架\",\"themeConfig\":{\"darkModeSwitchLabel\":\"主题\",\"darkModeSwitchTitle\":\"切换到深色模式\",\"docFooter\":{\"next\":\"下一页\",\"prev\":\"上一页\"},\"editLink\":{\"pattern\":\"https://github.com/vbenjs/vue-vben-admin/edit/main/docs/src/:path\",\"text\":\"在 GitHub 上编辑此页面\"},\"footer\":{\"copyright\":\"Copyright © 2020-2025 Vben\",\"message\":\"基于 MIT 许可发布.\"},\"langMenuLabel\":\"多语言\",\"lastUpdated\":{\"formatOptions\":{\"dateStyle\":\"short\",\"timeStyle\":\"medium\"},\"text\":\"最后更新于\"},\"lightModeSwitchTitle\":\"切换到浅色模式\",\"nav\":[{\"activeMatch\":\"^/(guide|components)/\",\"text\":\"文档\",\"items\":[{\"activeMatch\":\"^/guide/\",\"link\":\"/guide/introduction/vben\",\"text\":\"指南\"},{\"activeMatch\":\"^/components/\",\"link\":\"/components/introduction\",\"text\":\"组件\"},{\"text\":\"历史版本\",\"items\":[{\"link\":\"https://doc.vvbin.cn\",\"text\":\"2.x版本文档\"}]}]},{\"text\":\"演示\",\"items\":[{\"text\":\"Vben Admin\",\"items\":[{\"link\":\"https://www.vben.pro\",\"text\":\"演示版本\"},{\"link\":\"https://ant.vben.pro\",\"text\":\"Ant Design Vue 版本\"},{\"link\":\"https://naive.vben.pro\",\"text\":\"Naive 版本\"},{\"link\":\"https://ele.vben.pro\",\"text\":\"Element Plus版本\"}]},{\"text\":\"其他\",\"items\":[{\"link\":\"https://vben.vvbin.cn\",\"text\":\"Vben Admin 2.x\"}]}]},{\"text\":\"5.4.6\",\"items\":[{\"link\":\"https://github.com/vbenjs/vue-vben-admin/releases\",\"text\":\"更新日志\"},{\"link\":\"https://github.com/orgs/vbenjs/projects/5\",\"text\":\"路线图\"},{\"link\":\"https://github.com/vbenjs/vue-vben-admin/blob/main/.github/contributing.md\",\"text\":\"贡献\"}]},{\"link\":\"/commercial/technical-support\",\"text\":\"🦄 技术支持\"},{\"link\":\"/sponsor/personal\",\"text\":\"✨ 赞助\"},{\"link\":\"/commercial/community\",\"text\":\"👨‍👦‍👦 社区\"},{\"link\":\"/friend-links/\",\"text\":\"🤝 友情链接\"}],\"outline\":{\"label\":\"页面导航\"},\"returnToTopLabel\":\"回到顶部\",\"sidebar\":{\"/commercial/\":{\"base\":\"/commercial/\",\"items\":[{\"link\":\"community\",\"text\":\"社区\"},{\"link\":\"technical-support\",\"text\":\"技术支持\"},{\"link\":\"customized\",\"text\":\"定制开发\"}]},\"/components/\":{\"base\":\"/components/\",\"items\":[{\"text\":\"组件\",\"items\":[{\"link\":\"introduction\",\"text\":\"介绍\"}]},{\"collapsed\":false,\"text\":\"通用组件\",\"items\":[{\"link\":\"common-ui/vben-modal\",\"text\":\"Modal 模态框\"},{\"link\":\"common-ui/vben-drawer\",\"text\":\"Drawer 抽屉\"},{\"link\":\"common-ui/vben-form\",\"text\":\"Form 表单\"},{\"link\":\"common-ui/vben-vxe-table\",\"text\":\"Vxe Table 表格\"},{\"link\":\"common-ui/vben-count-to-animator\",\"text\":\"CountToAnimator 数字动画\"}]}]},\"/guide/\":{\"base\":\"/guide/\",\"items\":[{\"collapsed\":false,\"text\":\"简介\",\"items\":[{\"link\":\"introduction/vben\",\"text\":\"关于 Vben Admin\"},{\"link\":\"introduction/why\",\"text\":\"为什么选择我们?\"},{\"link\":\"introduction/quick-start\",\"text\":\"快速开始\"},{\"link\":\"introduction/thin\",\"text\":\"精简版本\"},{\"base\":\"/\",\"link\":\"components/introduction\",\"text\":\"组件文档\"}]},{\"text\":\"基础\",\"items\":[{\"link\":\"essentials/concept\",\"text\":\"基础概念\"},{\"link\":\"essentials/development\",\"text\":\"本地开发\"},{\"link\":\"essentials/route\",\"text\":\"路由和菜单\"},{\"link\":\"essentials/settings\",\"text\":\"配置\"},{\"link\":\"essentials/icons\",\"text\":\"图标\"},{\"link\":\"essentials/styles\",\"text\":\"样式\"},{\"link\":\"essentials/external-module\",\"text\":\"外部模块\"},{\"link\":\"essentials/build\",\"text\":\"构建与部署\"},{\"link\":\"essentials/server\",\"text\":\"服务端交互与数据Mock\"}]},{\"text\":\"深入\",\"items\":[{\"link\":\"in-depth/login\",\"text\":\"登录\"},{\"link\":\"in-depth/theme\",\"text\":\"主题\"},{\"link\":\"in-depth/access\",\"text\":\"权限\"},{\"link\":\"in-depth/locale\",\"text\":\"国际化\"},{\"link\":\"in-depth/features\",\"text\":\"常用功能\"},{\"link\":\"in-depth/check-updates\",\"text\":\"检查更新\"},{\"link\":\"in-depth/loading\",\"text\":\"全局loading\"},{\"link\":\"in-depth/ui-framework\",\"text\":\"组件库切换\"}]},{\"text\":\"工程\",\"items\":[{\"link\":\"project/standard\",\"text\":\"规范\"},{\"link\":\"project/cli\",\"text\":\"CLI\"},{\"link\":\"project/dir\",\"text\":\"目录说明\"},{\"link\":\"project/test\",\"text\":\"单元测试\"},{\"link\":\"project/tailwindcss\",\"text\":\"Tailwind CSS\"},{\"link\":\"project/changeset\",\"text\":\"Changeset\"},{\"link\":\"project/vite\",\"text\":\"Vite Config\"}]},{\"text\":\"其他\",\"items\":[{\"link\":\"other/project-update\",\"text\":\"项目更新\"},{\"link\":\"other/remove-code\",\"text\":\"移除代码\"},{\"link\":\"other/faq\",\"text\":\"常见问题\"}]}]}},\"sidebarMenuLabel\":\"菜单\"}}},\"scrollOffset\":134,\"cleanUrls\":false}");</script>
    
  </body>
</html>