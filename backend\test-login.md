# 登录接口测试

## 接口信息

- **URL**: `/api/system/login`
- **Method**: `POST`
- **Content-Type**: `application/json`

> **注意**: API路径已去掉版本号 `/v1`，现在使用 `/api` 作为全局前缀

## 请求参数

```json
{
  "account": "***********",
  "password": "8888a8888#@"
}
```

### 参数说明

- `account`: 账号（支持用户名或手机号）
- `password`: 密码（明文传输，后端使用bcrypt验证）
- `captcha`: 验证码验证（可选，已禁用，传任何值都会被忽略）

## 测试账号

### 管理员账号
- **用户名**: admin
- **密码**: admin123
- **角色**: 超级管理员
- **状态**: 启用

### 测试账号
- **用户名**: testuser
- **手机号**: ***********
- **密码**: 8888a8888#@
- **角色**: 系统管理员
- **状态**: 启用

## 响应格式

### 成功响应

```json
{
  "success": true,
  "code": "00000",
  "data": {
    "roleType": 1,
    "name": "何",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************.kIbXlxPUb3P-QONwBcnRPhL6d9nzeF3aS3tbpcAnxks",
    "account": "***********"
  },
  "currentTime": "2025-07-28 15:06:31"
}
```

### 失败响应

```json
{
  "success": false,
  "code": "50000",
  "currentTime": "2025-07-28 15:06:31"
}
```

## JWT Token 内容

Token 解码后包含以下信息：

```json
{
  "accountId": "1584770581741338626",
  "roleId": "1584770581695201282",
  "appId": "1584770581682618369",
  "name": "何",
  "roleType": "1",
  "exp": **********,
  "userId": "1584770581770698753",
  "account": "***********"
}
```

## 测试用例

### 1. 管理员登录（用户名）

```bash
curl -X POST http://localhost:3000/api/system/login \
  -H "Content-Type: application/json" \
  -d '{
    "account": "admin",
    "password": "admin123"
  }'
```

### 2. 手机号登录

```bash
curl -X POST http://localhost:3000/api/system/login \
  -H "Content-Type: application/json" \
  -d '{
    "account": "***********",
    "password": "8888a8888#@"
  }'
```

### 3. 用户名登录

```bash
curl -X POST http://localhost:3000/api/system/login \
  -H "Content-Type: application/json" \
  -d '{
    "account": "testuser",
    "password": "8888a8888#@"
  }'
```

### 4. 带captcha参数的登录（兼容性测试）

```bash
curl -X POST http://localhost:3000/api/system/login \
  -H "Content-Type: application/json" \
  -d '{
    "account": "admin",
    "password": "admin123",
    "captcha": true
  }'
```

### 3. 错误的账号或密码

```bash
curl -X POST http://localhost:3000/api/system/login \
  -H "Content-Type: application/json" \
  -d '{
    "account": "wronguser",
    "password": "wrongpass",
    "captcha": true
  }'
```

## 其他API接口路径

去掉版本号后，其他API接口路径如下：

- 用户管理: `/api/users`
- 组织管理: `/api/organizations`
- 角色管理: `/api/roles`
- 岗位管理: `/api/positions`
- 用户组管理: `/api/user-groups`
- 应用管理: `/api/applications`
- 功能管理: `/api/functions`
- 字典管理: `/api/dict`
- 系统配置: `/api/sys-config`

## 注意事项

1. 接口路径已从 `/auth/login` 改为 `/api/system/login`
2. 请求参数从 `username` 改为 `account`，支持用户名或手机号登录
3. 响应格式完全按照新的规范返回
4. JWT Token 包含更多用户信息
5. Token 有效期设置为7天
6. 时间格式为中文本地化格式

## 数据库准备

确保数据库中有对应的测试用户：

```sql
-- 插入测试用户（如果不存在）
INSERT INTO users (username, password, real_name, phone, status) VALUES
('admin', '$2b$10$7JB720yubVSUvpBWEXZvOeVPiAYZLQQWkTcxiEmu.TI.FASjHdAoS', '何', '***********', 1);
```

### 生成密码哈希

运行以下命令生成密码 `8888a8888#@` 的哈希值：

```bash
node generate-password-hash.js
```

## 修改内容总结

### 1. 控制器修改
- 路径从 `/auth/login` 改为 `/api/system/login`
- 控制器路径从 `@Controller('auth')` 改为 `@Controller('api/system')`

### 2. DTO修改
- 登录参数从 `username` 改为 `account`
- 添加 `captcha` 可选参数
- 响应格式完全重构，包含 `roleType`, `name`, `token`, `account`

### 3. 响应格式修改
- 从标准REST格式改为自定义格式
- 添加 `success` 布尔字段
- `code` 改为字符串格式（成功："00000"，失败："50000"）
- 时间格式改为中文本地化格式

### 4. 认证逻辑增强
- 支持用户名或手机号登录
- JWT payload 包含更多用户信息
- Token 有效期设置为7天

### 5. 服务层修改
- 添加 `findByPhone` 方法支持手机号查找用户
- 更新JWT策略以支持新的payload格式

## 兼容性说明

- 新接口完全兼容您提供的参数和响应格式
- 支持向后兼容，同时支持用户名和手机号登录
- JWT Token 包含完整的用户信息，便于前端使用
