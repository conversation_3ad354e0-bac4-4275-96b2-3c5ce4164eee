import { Entity, Column, Index, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '@/common/entities/base.entity';
import { DictData } from './dict-data.entity';

@Entity('dict_types')
@Index(['type'])
@Index(['status'])
export class DictType extends BaseEntity {
  @ApiProperty({ description: '字典名称' })
  @Column({
    type: 'varchar',
    length: 100,
    comment: '字典名称',
  })
  name: string;

  @ApiProperty({ description: '字典类型' })
  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: '字典类型',
  })
  type: string;

  @ApiProperty({ description: '状态：1启用，0停用' })
  @Column({
    type: 'tinyint',
    default: 1,
    comment: '状态：1启用，0停用',
  })
  status: number;

  @ApiProperty({ description: '备注' })
  @Column({
    type: 'text',
    nullable: true,
    comment: '备注',
  })
  remark?: string;

  // 关联关系
  @OneToMany(() => DictData, (dictData) => dictData.dictType)
  dictData?: DictData[];
}
