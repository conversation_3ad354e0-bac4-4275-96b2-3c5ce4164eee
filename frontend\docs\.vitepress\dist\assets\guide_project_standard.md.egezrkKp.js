import{ao as i,k as a,aP as e,l as t,ay as s,j as l}from"./chunks/framework.C8U7mBUf.js";const n=JSON.parse('{"title":"规范","description":"","frontmatter":{},"headers":[],"relativePath":"guide/project/standard.md","filePath":"guide/project/standard.md"}');const h=i({name:"guide/project/standard.md"},[["render",function(i,n,h,r,o,p){const d=s("NolebaseGitContributors"),c=s("NolebaseGitChangelog");return l(),a("div",null,[n[0]||(n[0]=e('<h1 id="规范" tabindex="-1">规范 <a class="header-anchor" href="#规范" aria-label="Permalink to &quot;规范&quot;">​</a></h1><div class="tip custom-block"><p class="custom-block-title">贡献代码</p><ul><li><p>如果你想向项目贡献代码，请确保你的代码符合项目的代码规范。</p></li><li><p>如果你使用的是 <code>vscode</code>，需要安装以下插件：</p><ul><li><a href="https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint" target="_blank" rel="noreferrer">ESLint</a> - 脚本代码检查</li><li><a href="https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode" target="_blank" rel="noreferrer">Prettier</a> - 代码格式化</li><li><a href="https://marketplace.visualstudio.com/items?itemName=streetsidesoftware.code-spell-checker" target="_blank" rel="noreferrer">Code Spell Checker</a> - 单词语法检查</li><li><a href="https://marketplace.visualstudio.com/items?itemName=stylelint.vscode-stylelint" target="_blank" rel="noreferrer">Stylelint</a> - css 格式化</li></ul></li></ul></div><h2 id="作用" tabindex="-1">作用 <a class="header-anchor" href="#作用" aria-label="Permalink to &quot;作用&quot;">​</a></h2><p>具备基本工程素养的同学都会注重编码规范，而代码风格检查（Code Linting，简称 Lint）是保障代码规范一致性的重要手段。</p><p>遵循相应的代码规范有以下好处：</p><ul><li>较少 bug 错误率</li><li>高效的开发效率</li><li>更高的可读性</li></ul><h2 id="工具" tabindex="-1">工具 <a class="header-anchor" href="#工具" aria-label="Permalink to &quot;工具&quot;">​</a></h2><p>项目的配置文件位于 <code>internal/lint-configs</code> 下，你可以在这里修改各种lint的配置。</p><p>项目内集成了以下几种代码校验工具：</p><ul><li><a href="https://eslint.org/" target="_blank" rel="noreferrer">ESLint</a> 用于 JavaScript 代码检查</li><li><a href="https://stylelint.io/" target="_blank" rel="noreferrer">Stylelint</a> 用于 CSS 样式检查</li><li><a href="https://prettier.io/" target="_blank" rel="noreferrer">Prettier</a> 用于代码格式化</li><li><a href="https://commitlint.js.org/" target="_blank" rel="noreferrer">Commitlint</a> 用于检查 git 提交信息的规范</li><li><a href="https://publint.dev/" target="_blank" rel="noreferrer">Publint</a> 用于检查 npm 包的规范</li><li><a href="https://github.com/lint-staged/lint-staged" target="_blank" rel="noreferrer">Lint Staged</a> 用于在 git 提交前运行代码校验</li><li><a href="https://cspell.org/" target="_blank" rel="noreferrer">Cspell</a> 用于检查拼写错误</li></ul><h2 id="eslint" tabindex="-1">ESLint <a class="header-anchor" href="#eslint" aria-label="Permalink to &quot;ESLint&quot;">​</a></h2><p>ESLint 是一个代码规范和错误检查工具，用于识别和报告 TypeScript 代码中的语法错误。</p><h3 id="命令" tabindex="-1">命令 <a class="header-anchor" href="#命令" aria-label="Permalink to &quot;命令&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> eslint</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> .</span></span></code></pre></div><h3 id="配置" tabindex="-1">配置 <a class="header-anchor" href="#配置" aria-label="Permalink to &quot;配置&quot;">​</a></h3><p>eslint 配置文件为 <code>eslint.config.mjs</code>，其核心配置放在<code>internal/lint-configs/eslint-config</code>目录下，可以根据项目需求进行修改。</p><h2 id="stylelint" tabindex="-1">Stylelint <a class="header-anchor" href="#stylelint" aria-label="Permalink to &quot;Stylelint&quot;">​</a></h2><p>Stylelint 用于校验项目内部 css 的风格,加上编辑器的自动修复，可以很好的统一项目内部 css 风格</p><h3 id="命令-1" tabindex="-1">命令 <a class="header-anchor" href="#命令-1" aria-label="Permalink to &quot;命令&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> stylelint</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;**/*.{vue,css,less.scss}&quot;</span></span></code></pre></div><h3 id="配置-1" tabindex="-1">配置 <a class="header-anchor" href="#配置-1" aria-label="Permalink to &quot;配置&quot;">​</a></h3><p>Stylelint 配置文件为 <code>stylelint.config.mjs</code>，其核心配置放在<code>internal/lint-configs/stylelint-config</code>目录下，可以根据项目需求进行修改。</p><h2 id="prettier" tabindex="-1">Prettier <a class="header-anchor" href="#prettier" aria-label="Permalink to &quot;Prettier&quot;">​</a></h2><p>Prettier 可以用于统一项目代码风格，统一的缩进，单双引号，尾逗号等等风格</p><h3 id="命令-2" tabindex="-1">命令 <a class="header-anchor" href="#命令-2" aria-label="Permalink to &quot;命令&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> prettier</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> .</span></span></code></pre></div><h3 id="配置-2" tabindex="-1">配置 <a class="header-anchor" href="#配置-2" aria-label="Permalink to &quot;配置&quot;">​</a></h3><p>Prettier 配置文件为 <code>.prettier.mjs</code>，其核心配置放在<code>internal/lint-configs/prettier-config</code>目录下，可以根据项目需求进行修改。</p><h2 id="commitlint" tabindex="-1">CommitLint <a class="header-anchor" href="#commitlint" aria-label="Permalink to &quot;CommitLint&quot;">​</a></h2><p>在一个团队中，每个人的 git 的 commit 信息都不一样，五花八门，没有一个机制很难保证规范化，如何才能规范化呢？可能你想到的是 git 的 hook 机制，去写 shell 脚本去实现。这当然可以，其实 JavaScript 有一个很好的工具可以实现这个模板，它就是 commitlint（用于校验 git 提交信息规范）。</p><h3 id="配置-3" tabindex="-1">配置 <a class="header-anchor" href="#配置-3" aria-label="Permalink to &quot;配置&quot;">​</a></h3><p>CommitLint 配置文件为 <code>.commitlintrc.mjs</code>，其核心配置放在<code>internal/lint-configs/commitlint-config</code>目录下，可以根据项目需求进行修改。</p><h3 id="git-提交规范" tabindex="-1">Git 提交规范 <a class="header-anchor" href="#git-提交规范" aria-label="Permalink to &quot;Git 提交规范&quot;">​</a></h3><p>参考 <a href="https://github.com/conventional-changelog/conventional-changelog/tree/master/packages/conventional-changelog-angular" target="_blank" rel="noreferrer">Angular</a></p><ul><li><code>feat</code> 增加新功能</li><li><code>fix</code> 修复问题/BUG</li><li><code>style</code> 代码风格相关无影响运行结果的</li><li><code>perf</code> 优化/性能提升</li><li><code>refactor</code> 重构</li><li><code>revert</code> 撤销修改</li><li><code>test</code> 测试相关</li><li><code>docs</code> 文档/注释</li><li><code>chore</code> 依赖更新/脚手架配置修改等</li><li><code>workflow</code> 工作流改进</li><li><code>ci</code> 持续集成</li><li><code>types</code> 类型修改</li></ul><h3 id="关闭git提交规范检查" tabindex="-1">关闭Git提交规范检查 <a class="header-anchor" href="#关闭git提交规范检查" aria-label="Permalink to &quot;关闭Git提交规范检查&quot;">​</a></h3><p>如果你想关闭 Git 提交规范检查，有两种方式：</p><div class="vp-code-group vp-adaptive-theme"><div class="tabs"><input type="radio" name="group-CdzeA" id="tab-GXDwATK" checked><label data-title="临时关闭" for="tab-GXDwATK">临时关闭</label><input type="radio" name="group-CdzeA" id="tab-OmLuWLS"><label data-title="永久关闭" for="tab-OmLuWLS">永久关闭</label></div><div class="blocks"><div class="language-bash vp-adaptive-theme active"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">git</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> commit</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> -m</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &#39;feat: add home page&#39;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> --no-verify</span></span></code></pre></div><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark has-diff vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6A737D;--shiki-dark:#6A737D;"># 在 .husky/commit-msg 内注释以下代码即可</span></span>\n<span class="line diff remove"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> exec</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> commitlint</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> --edit</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> &quot;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">$1</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">&quot;</span></span></code></pre></div></div></div><h2 id="publint" tabindex="-1">Publint <a class="header-anchor" href="#publint" aria-label="Permalink to &quot;Publint&quot;">​</a></h2><p>Publint 是一个用于检查 npm 包的规范的工具，可以检查包的版本号是否符合规范，是否符合标准的 ESM 规范包等等。</p><h3 id="命令-3" tabindex="-1">命令 <a class="header-anchor" href="#命令-3" aria-label="Permalink to &quot;命令&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> vsh</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> publint</span></span></code></pre></div><h2 id="cspell" tabindex="-1">Cspell <a class="header-anchor" href="#cspell" aria-label="Permalink to &quot;Cspell&quot;">​</a></h2><p>Cspell 是一个用于检查拼写错误的工具，可以检查代码中的拼写错误，避免因为拼写错误导致的 bug。</p><h3 id="命令-4" tabindex="-1">命令 <a class="header-anchor" href="#命令-4" aria-label="Permalink to &quot;命令&quot;">​</a></h3><div class="language-bash vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#6F42C1;--shiki-dark:#B392F0;">pnpm</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> cspell</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;"> lint</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> \\&quot;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">**</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">*</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">.ts</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\&quot;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">  \\&quot;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">**</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">/README.md</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\&quot;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> \\&quot;</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">.changeset/</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">*</span><span style="--shiki-light:#032F62;--shiki-dark:#9ECBFF;">.md</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;">\\&quot;</span><span style="--shiki-light:#005CC5;--shiki-dark:#79B8FF;"> --no-progress</span></span></code></pre></div><h3 id="配置-4" tabindex="-1">配置 <a class="header-anchor" href="#配置-4" aria-label="Permalink to &quot;配置&quot;">​</a></h3><p>cspell 配置文件为 <code>cspell.json</code>，可以根据项目需求进行修改。</p><h2 id="git-hook" tabindex="-1">Git Hook <a class="header-anchor" href="#git-hook" aria-label="Permalink to &quot;Git Hook&quot;">​</a></h2><p>git hook 一般结合各种 lint，在 git 提交代码的时候进行代码风格校验，如果校验没通过，则不会进行提交。需要开发者自行修改后再次进行提交</p><h3 id="husky" tabindex="-1">husky <a class="header-anchor" href="#husky" aria-label="Permalink to &quot;husky&quot;">​</a></h3><p>有一个问题就是校验会校验全部代码，但是我们只想校验我们自己提交的代码，这个时候就可以使用 husky。</p><p>最有效的解决方案就是将 Lint 校验放到本地，常见做法是使用 husky 或者 pre-commit 在本地提交之前先做一次 Lint 校验。</p><p>项目在 <code>.husky</code> 内部定义了相应的 hooks</p><h4 id="如何关闭-husky" tabindex="-1">如何关闭 Husky <a class="header-anchor" href="#如何关闭-husky" aria-label="Permalink to &quot;如何关闭 Husky&quot;">​</a></h4><p>如果你想关闭 Husky，直接删除 <code>.husky</code> 目录即可。</p><h3 id="lint-staged" tabindex="-1">lint-staged <a class="header-anchor" href="#lint-staged" aria-label="Permalink to &quot;lint-staged&quot;">​</a></h3><p>用于自动修复提交文件风格问题,其配置文件为 <code>.lintstagedrc.mjs</code>，可以根据项目需求进行修改。</p>',58)),t(d),t(c)])}]]);export{n as __pageData,h as default};
