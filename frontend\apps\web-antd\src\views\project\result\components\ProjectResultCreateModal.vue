<script setup>
import { computed, ref, unref } from 'vue';
import { useVbenModal, VbenButton } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import {saveResult, list ,getResultDetail} from '#/views/project/project.api'; // 引入 list 和 editResult 函数
import { message } from 'ant-design-vue';
import { showLoading, showSuccess } from '#/utils/toast.js';
import { RegionUtils } from '#/utils/regionUtils';
import {formSchema} from "#/views/project/result/result.data.js";

// 声明Emits
const emit = defineEmits(['register', 'success']);

let id = null;
let reginCodeValue = null;
let isUpdate = ref(false); // 新增一个响应式变量来判断是否是更新模式

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  footer:null,
  onCancel() {
    modalApi.close();
  },
  onClosed() {
    clearData();
  },
  onOpenChange: async (isOpen) => {
    if (isOpen) {
      await getProjectList(); // 确保在打开modal时获取项目列表
      const data = modalApi.getData();

      isUpdate.value = data && data.update; // 根据传入数据判断是否是更新模式
      if (isUpdate.value) {
        let rowData = {...data.row}
        rowData = await getResultDetail(rowData.id);
        let regionCode = [parseInt(rowData.regionProvinceCode),parseInt(rowData.regionCityCode), parseInt(rowData.regionCountyCode)];
        let regionName = `${rowData.regionProvinceName || ''} ${rowData.regionCityName || ''} ${rowData.regionCityName || ''}`.trim();
        rowData.regionCode = regionName;
        reginCodeValue = regionCode;


        delete rowData.regionProvinceCode;
        delete rowData.regionCityCode;
        delete rowData.regionCountyCode;
        delete rowData.regionProvinceName;
        delete rowData.regionCityName;
        delete rowData.regionCountyName;

        let values = {}
        if (isUpdate.value) {
          values = {...rowData};
        }
        id = data.row.id; // 获取编辑数据的ID
        await formApi.setValues(rowData); // 填充表单数据
      } else {
        await formApi.resetForm(); // 新增模式下重置表单
      }
    }
  },
});

const state = modalApi.useStore();
const formModel = ref({
  id: '',
  projectName: '',
});

const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-32',
  },
  handleSubmit,
  handleReset,
  layout: 'horizontal',
  schema: formSchema,
  wrapperClass: 'grid-cols-3 gap-4',
  showDefaultActions: false,
  submitButtonOptions:{
    style: {
      backgroundColor: 'hsl(var(--primary))',
    },
  }
});

// 设置标题
const title = computed(() => (unref(isUpdate) ? '编辑项目成果' : '新增项目成果'));

// 清除数据函数
function clearData() {
  id = null;
  isUpdate.value = false;
  formApi.resetForm();
}

// 获取项目列表并更新表单schema
async function getProjectList() {
  try {
    const res = await list({
      page: {
        current: 1,
        size: 999999,
        searchCount: true,
      },
      condition: {},
    });
    if (res.records) {
      const projectOptions = res.records.map(item => ({
        label: item.projectName,
        value: item.projectName,
        data: item // 将完整的项目数据存储在选项中
      }));
      // 找到 projectName 字段并更新 options
      const projectNameSchema = formSchema.find(item => item.fieldName === 'projectName');
      if (projectNameSchema) {
        projectNameSchema.componentProps.options = projectOptions;
      }
    }
  } catch (error) {
    console.error('获取项目列表失败:', error);
    message.error('获取项目列表失败');
  }
}

getProjectList();

async function handleReset() {
  await formApi.resetForm();
}


// 表单提交事件
async function handleSubmit(values) {
  try {
    showLoading('操作处理中...');
    let regionCode = values.regionCode;
    if(regionCode != null && typeof regionCode === "string"){
      regionCode = reginCodeValue;
    }

    let regionProvinceCode = regionCode[0];
    let regionCityCode = regionCode[1];
    let regionCountyCode = regionCode[2];

    // 从表单中获取省市区名称
    let region = RegionUtils.getRegionNameByCode(''+regionCountyCode);
    let regionProvinceName = region.province;
    let regionCityName = region.city;
    let regionCountyName = region.area;


    // 将省市区名称添加到values中
    values.regionProvinceCode = regionProvinceCode;
    values.regionProvinceName = regionProvinceName;
    values.regionCityCode = regionCityCode;
    values.regionCityName = regionCityName;
    values.regionCountyCode = regionCountyCode;
    values.regionCountyName = regionCountyName;

    if (unref(isUpdate)) {
      values.id = id;
      await saveResult(values); // 调用编辑接口
    } else {
      await saveResult(values); // 调用新增接口
    }
    delete values.regionCode;
    showSuccess('操作成功！');
    modalApi.close();
    emit('success');
  } catch (e) {
    console.log(e);
    // message.error('操作失败');
  }
}
</script>

<template>
  <Modal v-bind="$attrs" :footer="false" :title="title" class="w-[1400px]" :destroy-on-close="true" :maskClosable="false">
    <div class="p-6">
      <Form />
      <div class="flex justify-end mt-4 space-x-2">
        <a-button @click="handleReset">重置</a-button>
        <VbenButton type="primary" @click="formApi.validateAndSubmitForm()">提交</VbenButton>
      </div>
    </div>
  </Modal>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';
.ant-input-disabled {
  color:  hsl(var(--text-color-secondary)) !important;
}
</style>
