import{by as r}from"./bootstrap-DShsrVit.js";import{a4 as n,O as s,af as c,ag as i,ah as f,a3 as p,ap as l,ao as m}from"../jse/index-index-BMh_AyeW.js";import{u as _}from"./use-drawer-Qcdpj8Bl.js";const d={class:"flex-col-center"},x=n({__name:"shared-data-demo",setup(u){const a=s(),[t,e]=_({onCancel(){e.close()},onConfirm(){r.info("onConfirm")},onOpenChange(o){o&&(a.value=e.getData())}});return(o,h)=>(c(),i(p(t),{title:"数据共享示例"},{default:f(()=>[l("div",d,"外部传递数据： "+m(a.value),1)]),_:1}))}});export{x as _};
