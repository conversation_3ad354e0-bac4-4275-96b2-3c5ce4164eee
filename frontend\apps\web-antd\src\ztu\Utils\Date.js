Date.prototype.timeDiff = function (date) {
  let diff;
  diff = date - this;
  return diff;
};

Date.prototype.dayDiff = function (date) {
  return Math.ceil(Math.abs(this.getTime() - date.getTime()) / 86400000);
};

Date.prototype.format = function (format) {
  date = this;
  format = format || 'Y-m-d';
  let _date = new Date(date);
  let [year, month, day, weekDay, hours, minutes, seconds] = [
    _date.getFullYear() + '',
    _date.getMonth() + 1 + '',
    _date.getDate() + '',
    _date.getDay() + '',
    _date.getHours() + '',
    _date.getMinutes() + '',
    _date.getSeconds() + '',
  ];
  let [monthEn, weekEn] = [
    _date.toUTCString().substr(8, 3),
    _date.toUTCString().substr(0, 3),
  ];
  let weekDay_ISO8601 = weekDay === '0' ? '7' : weekDay;
  return format
    .replace(/Y/g, year) //1970
    .replace(/y/g, year.slice(-2)) //70
    .replace(/m/g, ('0' + month).slice(-2)) //09
    .replace(/n/g, month) //9
    .replace(/M/g, monthEn) //Sep
    .replace(/F/g, months[monthEn].en) //September
    .replace(/\_F/g, months[monthEn].cn) //九
    .replace(/j/g, day) //9
    .replace(/d/g, ('0' + day).slice(-2)) //09
    .replace(/D/g, weekEn) //Sun
    .replace(/l/g, weeks[weekEn].en) //Sunday
    .replace(/_l/g, weeks[weekEn].cn) //日
    .replace(/w/g, weekDay) //0
    .replace(/N/g, weekDay_ISO8601) //7
    .replace(/H/g, ('0' + hours).slice(-2)) //06
    .replace(/G/g, hours) //6
    .replace(/i/g, ('0' + minutes).slice(-2)) //06
    .replace(/s/g, ('0' + seconds).slice(-2)); //06
};
