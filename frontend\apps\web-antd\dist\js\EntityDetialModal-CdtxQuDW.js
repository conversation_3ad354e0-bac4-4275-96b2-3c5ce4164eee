var Y=(S,C,v)=>new Promise((d,_)=>{var c=r=>{try{g(v.next(r))}catch(f){_(f)}},U=r=>{try{g(v.throw(r))}catch(f){_(f)}},g=r=>r.done?d(r.value):Promise.resolve(r.value).then(c,U);g((v=v.apply(S,C)).next())});import{g as B,a as O,b as z}from"./entity.api-CPgpBrqe.js";import{g as $}from"./entity.data-u4HDUExc.js";import q from"./EntityLayerData-B2GLrKMm.js";import{a as G,g as P}from"./bootstrap-5OPUVRWy.js";import"./Base-xeJpkIWP.js";/* empty css                                                                     */import{a as H}from"./util-BadkgFi3.js";import{r as x,o as E,y,x as w,j as L,b,q as m,f as a,t,k as e,s as p,a as N,e as D,v as J}from"../jse/index-index-DyHD_jbN.js";import{u as K}from"./use-modal-uChFuhJy.js";import"./vxe-table-CZ9gPHn5.js";import"./loading-DzjUKA94.js";import"./form-DdFfsSWf.js";const Q={class:"metadata-section"},W={class:"smetadata-row"},ee={class:"smetadata-row"},ae={class:"smetadata-row"},te={class:"smetadata-row"},le={class:"smetadata-row"},se={class:"metadata-row"},ne={class:"layerTree"},oe={key:0,class:"metadata-section"},re={class:"spacedata-row"},ie={class:"spacedata-row"},de={class:"spacedata-row"},ce={class:"spacedata-row"},me={key:0,class:"spacedata-row"},pe={class:"spacedata-row"},ue={class:"truncate-text"},ye={__name:"EntityDetialModal",emits:["handleCancel"],setup(S,{emit:C}){const v=C;let d=null;const _=x([]),c=x(""),[U,g]=K({closeOnClickModal:!1,onCancel(){g.close()},onConfirm(){},onOpenChange(n){if(n){k(),d=g.useStore().value.row,Z();return}k()}});let r=x([{title:"包含图层",key:"",children:[]}]);const f=(n,s)=>Y(null,null,function*(){let i=yield z(n,s);if(i&&i.metadata&&Object.assign(l,i.metadata),n==="vector"){const{apiURL:h}=P();let T=`${h}/layer-data/preview/vector/${s}`;u.imageUrl=T;return}}),X=(n,s)=>{console.log("checkedKeys:",n,"info:",s),_.value=n,c.value=n[0],n[0]&&n[0].length>0&&(u.layerUrl=s.node.dataRef.layerUrl,f(d.dataType,n[0]))};E(()=>{c.value=""});const F=x("1");let o=y({name:"",category:"",dataType:"",timeCreated:"",regionCode:"",dataSize:"",provider:"",uploadStatus:"",resolution:"",range:"",area:"",coordinateSystem:"",timeUpdated:""}),l=y({id:"",timeCreated:"",threeDimensionsInfoId:"",name:"",minX:0,minY:0,minZ:0,maxX:0,maxY:0,maxZ:0,width:0,height:0,depth:0,geometricError:0,area:0,epsg:0,spatialRef:"0",level:0}),u=y({serviceUrl:"",imageUrl:"",layerUrl:""});const k=()=>{c.value="",l=y({id:"",timeCreated:"",threeDimensionsInfoId:"",name:"",minX:0,minY:0,minZ:0,maxX:0,maxY:0,maxZ:0,width:0,height:0,depth:0,geometricError:0,area:0,epsg:0,spatialRef:"0",level:0}),u=y({serviceUrl:"",imageUrl:"",layerUrl:""}),o=y({name:"",category:"",dataType:"",timeCreated:"",regionCode:"",dataSize:"",provider:"",uploadStatus:"",resolution:"",range:"",area:"",coordinateSystem:"",timeUpdated:""}),r=x([{title:"包含图层",key:"",children:[]}])};y({message:""});const Z=()=>{k(),B(d.dataType,d.id).then(n=>{Object.assign(o,n)}),O(d.dataType,d.id).then(n=>{if(r.value[0].children=[],n!=null&&n.length>0){n.forEach(i=>{r.value[0].children.push({title:i.layerName,key:i.layerId,layerUrl:i.layerUrl})});let s=n[0].layerId;c.value=s,_.value=[s],u.layerUrl=n[0].layerUrl,f(d.dataType,s);return}c.value=""})};E(()=>{});const I=()=>{v("onCancel",{})},M=n=>{const s=n.replace(" ","T");return new Date(s).getFullYear()};return(n,s)=>{const i=w("a-tree"),h=w("a-tab-pane"),T=w("a-image"),A=w("a-flex"),R=w("a-tabs"),V=w("a-button");return b(),L(e(U),{title:"数据集详情",class:"w-[800px]",onCancel:I,"destroy-on-close":"",maskClosable:!1},{footer:m(()=>[s[2]||(s[2]=a("div",{class:"border-t border-gray-300",style:{"margin-bottom":"10px"}},null,-1)),p(V,{type:"primary",onClick:I},{default:m(()=>s[1]||(s[1]=[J("确定")])),_:1})]),default:m(()=>[s[3]||(s[3]=a("view",{class:"bg-gray-200 dark:bg-gray-800 itemTitle",style:{"margin-top":"5px"}},"基本信息",-1)),a("view",Q,[a("view",W,[a("span",null,"数据集名称："+t(e(o).name),1),a("span",null,"类型："+t(e(o).category),1),a("span",null,"数据类型："+t(e(o).dataType),1)]),a("view",ee,[a("span",null,"行政区划："+t(e(o).regionName),1),a("span",null,"建设年份："+t(M(e(o).timeCreated)),1),a("span",null,"文件格式："+t(e(o).fileFormat),1)]),a("view",ae,[a("span",null,"面积(平方米)："+t(e(o).area&&e(o).area.toFixed(2)),1),a("span",null,"长度(米）："+t(e(o).length&&e(o).length.toFixed(2)),1),a("span",null,"数据量："+t(e(o).dataSize),1)]),a("view",te,[a("span",null,"创建人："+t(e(o).userNameCreated),1),a("span",null,"创建时间："+t(e(o).timeCreated),1),a("span",null,"数据提供者："+t(e(o).provider),1)]),a("view",le,[a("span",null,"更新人："+t(e(o).userNameUpdated),1),a("span",null,"更新时间："+t(e(o).timeUpdated),1),a("span",null,"状态："+t(e($)(e(o))),1)])]),s[4]||(s[4]=a("view",{class:"bg-gray-200 dark:bg-gray-800 itemTitle"},"图层信息",-1)),a("view",se,[a("view",ne,[p(i,{defaultExpandAll:!0,treeData:e(r),checkable:!1,onSelect:X,indent:0,selectedKeys:_.value},null,8,["treeData","selectedKeys"])]),p(R,{class:"tabClass","active-key":F.value,"onUpdate:activeKey":s[0]||(s[0]=j=>F.value=j)},{default:m(()=>[p(h,{key:"1",tab:"元数据",class:"pannelArea"},{default:m(()=>[e(o).uploadStatus==1?(b(),N("view",oe,[a("view",re,[a("view",null,"图层名称： "+t(e(l).name),1)]),a("view",ie,[a("view",null,"坐标系： "+t(e(l).spatialRef),1),a("view",null,"EPSG： "+t(e(l).epsg),1)]),a("view",de,[a("view",null,"几何类型： "+t(e(l).geomType),1),a("view",null,"要数个数： "+t(e(l).featureCount),1)]),a("view",ce,[a("view",null,"面积： "+t(e(l).area&&e(l).area.toFixed(2)),1),a("view",null,"长度： "+t(e(l).length&&e(l).length.toFixed(2)),1)]),e(o).dataType==="vector"?(b(),N("view",me,[a("view",null,"单位： "+t(e(l).unit),1)])):D("",!0),a("view",pe,[a("div",null,"范围： "+t(e(l).minX&&e(l).minX.toFixed(2))+","+t(e(l).minY&&e(l).minY.toFixed(2))+","+t(e(l).minZ&&e(l).minZ.toFixed(2))+" - "+t(e(l).maxX&&e(l).maxX.toFixed(2))+","+t(e(l).maxY&&e(l).maxY.toFixed(2))+","+t(e(l).maxZ&&e(l).maxZ.toFixed(2)),1)])])):D("",!0)]),_:1}),p(h,{key:"2",tab:"属性表",class:"pannelArea"},{default:m(()=>[p(q,{layerId:c.value},null,8,["layerId"])]),_:1}),p(h,{key:"3",tab:"预览",class:"pannelArea"},{default:m(()=>[a("div",ue,"服务地址: "+t(e(u).layerUrl),1),p(A,{align:"left",justify:"left",style:{width:"100%","margin-left":"10px"}},{default:m(()=>[e(u).imageUrl?(b(),L(T,{key:0,width:450,height:280,src:e(H)(e(u).imageUrl)},null,8,["src"])):D("",!0)]),_:1})]),_:1})]),_:1},8,["active-key"])])]),_:1})}}},Se=G(ye,[["__scopeId","data-v-176b1a0d"]]);export{Se as default};
