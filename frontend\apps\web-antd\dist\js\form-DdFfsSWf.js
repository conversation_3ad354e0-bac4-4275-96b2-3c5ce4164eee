import{$ as o}from"../jse/index-index-DyHD_jbN.js";import{b as i,s}from"./bootstrap-5OPUVRWy.js";s({config:{baseModelPropName:"value",modelPropNameMap:{Checkbox:"checked",Radio:"checked",Switch:"checked",Upload:"fileList"}},defineRules:{required:(e,u,r)=>e==null||e.length===0?o("ui.formRules.required",[r.label]):!0,selectRequired:(e,u,r)=>e==null?o("ui.formRules.selectRequired",[r.label]):!0}});const a=i;export{a as u};
