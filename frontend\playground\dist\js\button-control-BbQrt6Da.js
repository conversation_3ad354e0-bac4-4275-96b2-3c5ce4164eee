var $=(B,v,m)=>new Promise((f,_)=>{var d=n=>{try{y(m.next(n))}catch(i){_(i)}},S=n=>{try{y(m.throw(n))}catch(i){_(i)}},y=n=>n.done?f(n.value):Promise.resolve(n.value).then(d,S);y((m=m.apply(B,v)).next())});import{r as w,t as R,o as N,u as V,B as l,v as D}from"./bootstrap-DShsrVit.js";import{C as k}from"./index-B_b7xM74.js";import{a4 as x,J as T,ae as U,al as b,af as u,ag as a,ah as t,a3 as e,bh as j,n as r,ap as g,ao as J,an as o,ak as A}from"../jse/index-index-BMh_AyeW.js";import{_ as L}from"./page.vue_vue_type_script_setup_true_lang-B5GXo9YC.js";const C=x({name:"AccessControl",__name:"access-control",props:{codes:{default:()=>[]},type:{default:"role"}},setup(B){const v=B,{hasAccessByCodes:m,hasAccessByRoles:f}=w(),_=T(()=>{const{codes:d,type:S}=v;return S==="role"?f(d):m(d)});return(d,S)=>d.codes?_.value?U(d.$slots,"default",{key:1}):b("",!0):U(d.$slots,"default",{key:0})}}),M={class:"text-primary mx-4 text-lg"},H=x({__name:"button-control",setup(B){const v={admin:{password:"123456",username:"admin"},super:{password:"123456",username:"vben"},user:{password:"123456",username:"jack"}},{accessMode:m,hasAccessByCodes:f}=w(),_=R(),d=N(),S=V();function y(i){return d.userRoles.includes(i)?"primary":"default"}function n(i){return $(this,null,function*(){if(d.userRoles.includes(i))return;const s=v[i];D(),s&&(yield _.authLogin(s,()=>$(this,null,function*(){S.go(0)})))})}return(i,s)=>{const p=j("access");return u(),a(e(L),{title:`${e(m)==="frontend"?"前端":"后端"}按钮访问权限演示`,description:"切换不同的账号，观察按钮变化。"},{default:t(()=>[r(e(k),{class:"mb-5"},{title:t(()=>{var c;return[s[3]||(s[3]=g("span",{class:"font-semibold"},"当前角色:",-1)),g("span",M,J((c=e(d).userRoles)==null?void 0:c[0]),1)]}),default:t(()=>[r(e(l),{type:y("super"),onClick:s[0]||(s[0]=c=>n("super"))},{default:t(()=>s[4]||(s[4]=[o(" 切换为 Super 账号 ")])),_:1},8,["type"]),r(e(l),{type:y("admin"),class:"mx-4",onClick:s[1]||(s[1]=c=>n("admin"))},{default:t(()=>s[5]||(s[5]=[o(" 切换为 Admin 账号 ")])),_:1},8,["type"]),r(e(l),{type:y("user"),onClick:s[2]||(s[2]=c=>n("user"))},{default:t(()=>s[6]||(s[6]=[o(" 切换为 User 账号 ")])),_:1},8,["type"])]),_:1}),r(e(k),{class:"mb-5",title:"组件形式控制 - 权限码"},{default:t(()=>[r(e(C),{codes:["AC_100100"],type:"code"},{default:t(()=>[r(e(l),{class:"mr-4"},{default:t(()=>s[7]||(s[7]=[o(' Super 账号可见 ["AC_1000001"] ')])),_:1})]),_:1}),r(e(C),{codes:["AC_100030"],type:"code"},{default:t(()=>[r(e(l),{class:"mr-4"},{default:t(()=>s[8]||(s[8]=[o(' Admin 账号可见 ["AC_100010"] ')])),_:1})]),_:1}),r(e(C),{codes:["AC_1000001"],type:"code"},{default:t(()=>[r(e(l),{class:"mr-4"},{default:t(()=>s[9]||(s[9]=[o(' User 账号可见 ["AC_1000001"] ')])),_:1})]),_:1}),r(e(C),{codes:["AC_100100","AC_100010"],type:"code"},{default:t(()=>[r(e(l),{class:"mr-4"},{default:t(()=>s[10]||(s[10]=[o(' Super & Admin 账号可见 ["AC_100100","AC_1000001"] ')])),_:1})]),_:1})]),_:1}),e(m)==="frontend"?(u(),a(e(k),{key:0,class:"mb-5",title:"组件形式控制 - 角色"},{default:t(()=>[r(e(C),{codes:["super"],type:"role"},{default:t(()=>[r(e(l),{class:"mr-4"},{default:t(()=>s[11]||(s[11]=[o(" Super 角色可见 ")])),_:1})]),_:1}),r(e(C),{codes:["admin"],type:"role"},{default:t(()=>[r(e(l),{class:"mr-4"},{default:t(()=>s[12]||(s[12]=[o(" Admin 角色可见 ")])),_:1})]),_:1}),r(e(C),{codes:["user"],type:"role"},{default:t(()=>[r(e(l),{class:"mr-4"},{default:t(()=>s[13]||(s[13]=[o(" User 角色可见 ")])),_:1})]),_:1}),r(e(C),{codes:["super","admin"],type:"role"},{default:t(()=>[r(e(l),{class:"mr-4"},{default:t(()=>s[14]||(s[14]=[o(" Super & Admin 角色可见 ")])),_:1})]),_:1})]),_:1})):b("",!0),r(e(k),{class:"mb-5",title:"函数形式控制"},{default:t(()=>[e(f)(["AC_100100"])?(u(),a(e(l),{key:0,class:"mr-4"},{default:t(()=>s[15]||(s[15]=[o(' Super 账号可见 ["AC_1000001"] ')])),_:1})):b("",!0),e(f)(["AC_100030"])?(u(),a(e(l),{key:1,class:"mr-4"},{default:t(()=>s[16]||(s[16]=[o(' Admin 账号可见 ["AC_100010"] ')])),_:1})):b("",!0),e(f)(["AC_1000001"])?(u(),a(e(l),{key:2,class:"mr-4"},{default:t(()=>s[17]||(s[17]=[o(' User 账号可见 ["AC_1000001"] ')])),_:1})):b("",!0),e(f)(["AC_100100","AC_1000001"])?(u(),a(e(l),{key:3,class:"mr-4"},{default:t(()=>s[18]||(s[18]=[o(' Super & Admin 账号可见 ["AC_100100","AC_1000001"] ')])),_:1})):b("",!0)]),_:1}),r(e(k),{class:"mb-5",title:"指令方式 - 权限码"},{default:t(()=>[A((u(),a(e(l),{class:"mr-4"},{default:t(()=>s[19]||(s[19]=[o(' Super 账号可见 ["AC_1000001"] ')])),_:1})),[[p,["AC_100100"],"code"]]),A((u(),a(e(l),{class:"mr-4"},{default:t(()=>s[20]||(s[20]=[o(' Admin 账号可见 ["AC_100010"] ')])),_:1})),[[p,["AC_100030"],"code"]]),A((u(),a(e(l),{class:"mr-4"},{default:t(()=>s[21]||(s[21]=[o(' User 账号可见 ["AC_1000001"] ')])),_:1})),[[p,["AC_1000001"],"code"]]),A((u(),a(e(l),{class:"mr-4"},{default:t(()=>s[22]||(s[22]=[o(' Super & Admin 账号可见 ["AC_100100","AC_1000001"] ')])),_:1})),[[p,["AC_100100","AC_1000001"],"code"]])]),_:1}),r(e(k),{class:"mb-5",title:"指令方式 - 角色"},{default:t(()=>[A((u(),a(e(l),{class:"mr-4"},{default:t(()=>s[23]||(s[23]=[o(" Super 角色可见 ")])),_:1})),[[p,["super"],"role"]]),A((u(),a(e(l),{class:"mr-4"},{default:t(()=>s[24]||(s[24]=[o(" Admin 角色可见 ")])),_:1})),[[p,["admin"],"role"]]),A((u(),a(e(l),{class:"mr-4"},{default:t(()=>s[25]||(s[25]=[o(" User 角色可见 ")])),_:1})),[[p,["user"],"role"]]),A((u(),a(e(l),{class:"mr-4"},{default:t(()=>s[26]||(s[26]=[o(" Super & Admin 角色可见 ")])),_:1})),[[p,["super","admin"],"role"]])]),_:1})]),_:1},8,["title"])}}});export{H as default};
