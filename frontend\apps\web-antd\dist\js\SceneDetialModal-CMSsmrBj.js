var N=(X,T,v)=>new Promise((d,g)=>{var D=i=>{try{c(v.next(i))}catch(w){g(w)}},C=i=>{try{c(v.throw(i))}catch(w){g(w)}},c=i=>i.done?d(i.value):Promise.resolve(i.value).then(D,C);c((v=v.apply(X,T)).next())});import{g as B,a as O,b as $}from"./scene.api-DEW02Ykq.js";import{g as z}from"./scene.data-BMXeOdST.js";import P from"./LayerPreview-B-iME7wH.js";import{a as W}from"./util-BadkgFi3.js";import{a as q,g as V}from"./bootstrap-5OPUVRWy.js";import{r as S,y,o as G,x as h,j as Y,b as p,q as _,f as a,t,k as e,s as x,a as U,e as u,v as H}from"../jse/index-index-DyHD_jbN.js";import{u as K}from"./use-modal-uChFuhJy.js";import"./index-D4Q7xmlJ.js";import"./Base-xeJpkIWP.js";/* empty css                                                                     */import"./loading-DzjUKA94.js";const J={class:"metadata-section"},Q={class:"metadata-section"},ee={class:"smetadata-row"},ae={class:"smetadata-row"},te={class:"smetadata-row"},le={class:"smetadata-row"},se={class:"smetadata-row"},re={class:"metadata-row"},oe={class:"layerTree"},ie={key:0,class:"metadata-section"},ne={class:"spacedata-row"},de={class:"spacedata-row"},ce={key:0,class:"spacedata-row"},me={key:1,class:"spacedata-row"},pe={key:2,class:"spacedata-row"},ue={key:3,class:"spacedata-row"},ye={class:"spacedata-row"},_e={class:"truncate-text"},ve={key:1,style:{width:"100%",height:"400px"}},ge={__name:"SceneDetialModal",emits:["handleCancel"],setup(X,{emit:T}){const v=T;let d=null;const g=S([]),[D,C]=K({closeOnClickModal:!1,onCancel(){C.close()},onConfirm(){},onOpenChange(s){if(s){F(),d=C.useStore().value.row,R();return}F()}}),c=s=>s==="osgb"||s==="tiles"||s==="obj";let i=S([{title:"包含图层",key:"",children:[]}]);const w=(s,r,m)=>N(null,null,function*(){let f=yield $(s,r);if(f&&f.metadata&&Object.assign(l,f.metadata),c(s)){const{apiURL:b}=V();let k=m||`${b}/layer-data/preview/3d/${r}/tileset.json`;n.serviceUrl=k;return}if(s==="dom"||s==="dem"){const{apiURL:b}=V();let k=`${b}/layer-data/preview/raster/${r}`;n.imageUrl=k}}),M=(s,r)=>{console.log("checkedKeys:",s,"info:",r),g.value=s,s[0]&&s[0].length>0&&(n.layerUrl=r.node.dataRef.layerUrl,w(d.dataType,s[0],r.node.dataRef.layerUrl))},Z=S("1");let o=y({name:"",category:"",dataType:"",timeCreated:"",regionCode:"",dataSize:"",provider:"",uploadStatus:"",resolution:"",range:"",area:"",coordinateSystem:"",timeUpdated:""}),l=y({id:"",timeCreated:"",threeDimensionsInfoId:"",name:"",minX:0,minY:0,minZ:0,maxX:0,maxY:0,maxZ:0,width:0,height:0,depth:0,geometricError:0,area:0,epsg:0,spatialRef:"0",level:0}),n=y({serviceUrl:"",imageUrl:"",layerUrl:""});const F=()=>{l=y({id:"",timeCreated:"",threeDimensionsInfoId:"",name:"",minX:0,minY:0,minZ:0,maxX:0,maxY:0,maxZ:0,width:0,height:0,depth:0,geometricError:0,area:0,epsg:0,spatialRef:"0",level:0}),n=y({serviceUrl:"",imageUrl:"",layerUrl:""}),o=y({name:"",category:"",dataType:"",timeCreated:"",regionCode:"",dataSize:"",provider:"",uploadStatus:"",resolution:"",range:"",area:"",coordinateSystem:"",timeUpdated:""}),i=S([{title:"包含图层",key:"",children:[]}])};y({message:""});const R=()=>{F(),B(d.dataType,d.id).then(s=>{Object.assign(o,s)}),O(d.dataType,d.id).then(s=>{if(i.value[0].children=[],s!=null&&s.length>0){s.forEach(m=>{i.value[0].children.push({title:m.layerName,key:m.layerId,layerUrl:m.layerUrl})});let r=s[0].layerId;g.value=[r],n.layerUrl=s[0].layerUrl,w(d.dataType,r)}})};G(()=>{});const L=()=>{v("onCancel",{})},j=s=>{const r=s.replace(" ","T");return new Date(r).getFullYear()};return(s,r)=>{const m=h("a-tree"),f=h("a-tab-pane"),b=h("a-image"),k=h("a-flex"),E=h("a-tabs"),I=h("a-button");return p(),Y(e(D),{title:"数据集详情",class:"w-[800px]",onCancel:L,"destroy-on-close":"",maskClosable:!1},{footer:_(()=>[r[4]||(r[4]=a("div",{class:"border-t border-gray-300",style:{"margin-bottom":"10px"}},null,-1)),x(I,{type:"primary",onClick:L},{default:_(()=>r[3]||(r[3]=[H("确定")])),_:1})]),default:_(()=>[a("view",null,[a("view",J,[r[1]||(r[1]=a("view",{class:"bg-gray-200 dark:bg-gray-800 itemTitle"},"基本信息",-1)),a("view",Q,[a("view",ee,[a("span",null,"数据集名称："+t(e(o).name),1),a("span",null,"类型："+t(e(o).category),1),a("span",null,"数据类型："+t(e(o).dataType),1)]),a("view",ae,[a("span",null,"行政区划："+t(e(o).regionName),1),a("span",null,"建设年份："+t(j(e(o).timeCreated)),1),a("span",null,"文件格式："+t(e(o).fileFormat),1)]),a("view",te,[a("span",null,"面积(平方米）："+t(e(o).area&&e(o).area.toFixed(2)),1),a("span",null,"数据量："+t(e(o).dataSize),1),a("span",null,"数据提供者："+t(e(o).provider),1)]),a("view",le,[a("span",null,"创建人："+t(e(o).userNameCreated),1),a("span",null,"创建时间："+t(e(o).timeCreated),1),a("span",null,"状态："+t(e(z)(e(o))),1)]),a("view",se,[a("span",null,"更新人："+t(e(o).userNameUpdated),1),a("span",null,"更新时间："+t(e(o).timeUpdated),1)])])]),r[2]||(r[2]=a("view",{class:"bg-gray-200 dark:bg-gray-800 itemTitle"},"图层信息",-1)),a("view",re,[a("view",oe,[x(m,{defaultExpandAll:!0,treeData:e(i),checkable:!1,checkStrictly:!0,onSelect:M,selectedKeys:g.value},null,8,["treeData","selectedKeys"])]),x(E,{class:"tabClass","active-key":Z.value,"onUpdate:activeKey":r[0]||(r[0]=A=>Z.value=A)},{default:_(()=>[x(f,{key:"1",tab:"元数据",class:"pannelArea"},{default:_(()=>[e(o).uploadStatus==1?(p(),U("view",ie,[a("view",ne,[a("view",null,"图层名称： "+t(e(l).name),1)]),a("view",de,[a("view",null,"坐标系： "+t(e(l).spatialRef),1),a("view",null,"EPSG： "+t(e(l).epsg),1)]),c(e(o).dataType)?(p(),U("view",ce,[a("div",null,"原点坐标： "+t(e(l).originX&&e(l).originX.toFixed(2))+"，"+t(e(l).originY&&e(l).originY.toFixed(2))+"，"+t(e(l).originZ&&e(l).originZ.toFixed(2)),1)])):u("",!0),c(e(o).dataType)?(p(),U("view",me,[a("view",null,"面积(平方米）： "+t(e(l).area&&e(l).area.toFixed(2)),1)])):u("",!0),c(e(o).dataType)?u("",!0):(p(),U("view",pe,[a("view",null,"分辨率："+t(e(l).pixelWidth)+t(e(l).pixelWidth&&"x")+t(e(l).pixelHeight),1),a("view",null,"单位： "+t(e(l).unit),1)])),c(e(o).dataType)?u("",!0):(p(),U("view",ue,[a("view",null,"面积(平方米)： "+t(e(l).area&&e(l).area.toFixed(2)),1),a("view",null,"高程范围："+t(e(l).minValue)+t(e(l).maxValue&&"-")+t(e(l).maxValue),1)])),a("view",ye,[a("div",null,"范围： "+t(e(l).minX&&e(l).minX.toFixed(2))+","+t(e(l).minY&&e(l).minY.toFixed(2))+","+t(e(l).minZ&&e(l).minZ.toFixed(2))+" - "+t(e(l).maxX&&e(l).maxX.toFixed(2))+","+t(e(l).maxY&&e(l).maxY.toFixed(2))+","+t(e(l).maxZ&&e(l).maxZ.toFixed(2)),1)])])):u("",!0)]),_:1}),x(f,{key:"2",tab:"预览",class:"pannelArea"},{default:_(()=>[a("div",_e,"服务地址: "+t(e(n).layerUrl),1),e(n).imageUrl?(p(),Y(k,{key:0,align:"center",justify:"start",style:{width:"100%"}},{default:_(()=>[e(n).imageUrl?(p(),Y(b,{key:0,width:450,height:280,src:e(W)(e(n).imageUrl)},null,8,["src"])):u("",!0)]),_:1})):u("",!0),e(n).serviceUrl?(p(),U("view",ve,[x(P,{layerUrl:e(n).serviceUrl},null,8,["layerUrl"])])):u("",!0)]),_:1})]),_:1},8,["active-key"])])])]),_:1})}}},Ye=q(ge,[["__scopeId","data-v-bfdd89b1"]]);export{Ye as default};
