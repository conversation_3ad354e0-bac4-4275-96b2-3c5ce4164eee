<script lang="ts" setup>
import { computed, ref, unref, watch } from 'vue';
import { useVbenModal, <PERSON><PERSON>, <PERSON>bsList, <PERSON>bsTrigger, TabsContent, VbenButton } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { formSchema, buildTreeSelectData, buildSelectOptions, roleOptions, technicalLevelOptions, managementLevelOptions, userGroupOptions } from '../user.data';
import {
  createUser,
  updateUser,
} from '../user.api';
import { getOrganizationTree } from '../../organization/organization.api';
import { getPositionList } from '../../position/position.api';
import { message } from 'ant-design-vue';
import { showLoading, showSuccess } from '#/utils/toast.js';

// 声明Props
const props = defineProps<{
  departmentId?: number;
  row?: any;
}>();

// 声明Emits
const emit = defineEmits(['register', 'success']);



const isUpdate = ref(false);
const userId = ref<number | null>(null);
const organizationTreeData = ref([]);
const positionOptions = ref<Array<{ value: number; label: string }>>([]);
const userPositions = ref<Array<{ id: number; name: string; code: string; department: { name: string } }>>([]);
const userDepartments = ref<Array<{ departmentId?: number; positionId?: number }>>([]);
const selectedPositionId = ref<number | undefined>();
const activeTab = ref('basic'); // 当前激活的标签页
const basicFormValid = ref(false); // 基础信息表单是否有效
const departmentFormValid = ref(false); // 部门信息表单是否有效
const savedFormData = ref<any>({}); // 保存的表单数据，防止切换tab时丢失

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onClosed() {
    formApi.resetForm();
    isUpdate.value = false;
    userId.value = null;
    userPositions.value = [];
    userDepartments.value = [];
    selectedPositionId.value = undefined;
  },
  onConfirm() {},
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      // 加载表单选项数据
      loadFormOptions();
      // 处理传入的参数
      handleModalOpen();
    }
  },
});



// 加载表单选项数据
const loadFormOptions = async () => {
  try {
    // 加载组织树和岗位列表
    await loadOrganizationTree();
    await loadPositionList();

    // 更新表单schema中的选项数据
    formApi.updateSchema([
      {
        fieldName: 'roleIds',
        componentProps: {
          options: roleOptions,
        },
      },
      {
        fieldName: 'technicalLevel',
        componentProps: {
          options: technicalLevelOptions,
        },
      },
      {
        fieldName: 'managementLevel',
        componentProps: {
          options: managementLevelOptions,
        },
      },
      {
        fieldName: 'userGroupIds',
        componentProps: {
          options: userGroupOptions,
        },
      },
    ]);
  } catch (error) {
    console.error('加载表单选项失败:', error);
  }
};

// 加载组织树数据
const loadOrganizationTree = async () => {
  try {
    const data = await getOrganizationTree();
    organizationTreeData.value = buildTreeSelectData(data);
  } catch (error) {
    console.error('加载组织树失败:', error);
  }
};

// 加载岗位列表
const loadPositionList = async () => {
  try {
    const data = await getPositionList();
    positionOptions.value = buildSelectOptions(data);
  } catch (error) {
    console.error('加载岗位列表失败:', error);
  }
};

// 处理模态框打开时的逻辑
const handleModalOpen = () => {
  // 如果是编辑模式
  if (props.row) {
    isUpdate.value = true;
    userId.value = props.row.id;
    activeTab.value = 'basic';

    // 设置表单值
    formApi.setValues(props.row);

    // 初始化用户部门信息
    if (props.row.userDepartments && props.row.userDepartments.length > 0) {
      userDepartments.value = props.row.userDepartments.map((ud: any) => ({
        departmentId: ud.departmentId,
        positionId: ud.positionId,
      }));
    } else {
      // 如果没有多部门信息，使用主部门信息
      userDepartments.value = [{
        departmentId: props.row.departmentId,
        positionId: props.row.positionId,
      }];
    }
  }
  // 如果是新增模式
  else {
    isUpdate.value = false;
    userId.value = null;
    activeTab.value = 'basic';

    // 如果传入了部门ID，设置默认部门
    if (props.departmentId) {
      userDepartments.value = [{
        departmentId: props.departmentId,
        positionId: undefined,
      }];
    } else {
      // 确保至少有一个空的部门选项
      userDepartments.value = [{
        departmentId: undefined,
        positionId: undefined,
      }];
    }
  }
};

// 添加部门
const handleAddDepartment = () => {
  userDepartments.value.push({
    departmentId: undefined,
    positionId: undefined,
  });
};

// 删除部门
const handleRemoveDepartment = (index: number) => {
  userDepartments.value.splice(index, 1);
};

// 加载用户岗位信息
const loadUserPositions = async (userId: number) => {
  try {
    // 这里应该调用获取用户岗位的API，暂时使用空数组
    userPositions.value = [];
  } catch (error) {
    console.error('加载用户岗位失败:', error);
  }
};



const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'text-right pr-4',
    labelWidth: 100,
  },
  handleReset,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: formSchema,
  wrapperClass: 'grid-cols-2',
  showDefaultActions:false,
});

// 监听tab切换，自动保存表单数据
watch(activeTab, async (newTab, oldTab) => {
  if (oldTab && oldTab !== newTab) {
    try {
      // 保存当前tab的表单数据
      const currentValues = await formApi.getValues();
      savedFormData.value = { ...savedFormData.value, ...currentValues };
    } catch (error) {
      console.warn('保存表单数据失败:', error);
    }
  }

  if (newTab && savedFormData.value && Object.keys(savedFormData.value).length > 0) {
    try {
      // 恢复表单数据
      formApi.setValues(savedFormData.value);
    } catch (error) {
      console.warn('恢复表单数据失败:', error);
    }
  }
});

// 计算弹窗标题
const title = computed(() => {
  return isUpdate.value ? '编辑用户' : '新增用户';
});

// 获取部门名称
const getDepartmentName = (departmentId: number | undefined) => {
  if (!departmentId) return '';

  const findDepartment = (options: any[], id: number): any => {
    for (const option of options) {
      if (option.value === id) {
        return option;
      }
      if (option.children) {
        const found = findDepartment(option.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  const dept = findDepartment(organizationTreeData.value, departmentId);
  return dept?.title || '';
};

// 表单重置事件
async function handleReset() {
  modalApi.close();
}

// 下一步 - 从基础信息到部门信息
async function handleNext() {
  try {
    // 验证基础信息表单
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }

    // 验证通过，切换到部门信息tab
    activeTab.value = 'departments';
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 保存基础信息（更新模式）
async function handleSaveBasic() {
  try {
    // 验证基础信息表单
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }

    const values = await formApi.getValues();
    showLoading('保存基础信息中...');

    if (isUpdate.value && userId.value) {
      await updateUser(userId.value, values);
      showSuccess('基础信息保存成功！');
    }
  } catch (error) {
    console.error('保存基础信息失败:', error);
  }
}

// 保存部门信息（更新模式）
async function handleSaveDepartments() {
  try {
    // 验证部门信息：至少要有一个部门
    if (!userDepartments.value || userDepartments.value.length === 0) {
      message.warning('请至少添加一个部门信息');
      return;
    }

    // 验证每个部门都有选择部门ID
    const hasInvalidDepartment = userDepartments.value.some(dept => !dept.departmentId);
    if (hasInvalidDepartment) {
      message.warning('请为所有部门选择具体的部门');
      return;
    }

    showLoading('保存部门信息中...');

    if (isUpdate.value && userId.value) {
      const submitData = {
        departments: userDepartments.value,
      };
      await updateUser(userId.value, submitData);
      showSuccess('部门信息保存成功！');
    }
  } catch (error) {
    console.error('保存部门信息失败:', error);
  }
}

// 完成创建用户（新增模式）
async function handleCreateUser() {
  try {
    // 验证基础信息表单
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }

    // 验证部门信息：至少要有一个部门
    if (!userDepartments.value || userDepartments.value.length === 0) {
      message.warning('请至少添加一个部门信息');
      return;
    }

    // 验证每个部门都有选择部门ID
    const hasInvalidDepartment = userDepartments.value.some(dept => !dept.departmentId);
    if (hasInvalidDepartment) {
      message.warning('请为所有部门选择具体的部门');
      return;
    }

    const values = await formApi.getValues();
    showLoading('创建用户中...');

    // 添加用户部门信息
    const submitData = {
      ...values,
      departments: userDepartments.value,
    };

    await createUser(submitData);
    showSuccess('创建成功！');
    modalApi.close();
    emit('success');
  } catch (error) {
    console.error('创建用户失败:', error);
  }
}




</script>

<template>
  <Modal
    v-bind="$attrs"
    :footer="false"
    :title="title"
    class="w-[850px]"
    :destroy-on-close="true"
    :maskClosable="false"
  >
    <div style="padding: 0px">
      <Tabs v-model="activeTab">
        <TabsList>
          <TabsTrigger value="basic">基础信息</TabsTrigger>
          <TabsTrigger value="departments">部门信息</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" force-mount>
          <div class="px-5 py-4">
            <div class="hide-form-buttons">
              <Form />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="departments" force-mount>
          <div class="px-5 py-4">
            <!-- 添加部门 -->
            <div class="mb-4 flex items-center gap-2">
              <VbenButton variant="default" size="sm" @click="handleAddDepartment">
                新增
              </VbenButton>
            </div>

            <!-- 部门信息表格 -->
            <div class="border rounded">
              <table class="w-full">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-4 py-2 text-left border-b">任职部门</th>
                    <th class="px-4 py-2 text-left border-b">岗位</th>
                    <th class="px-4 py-2 text-left border-b">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(dept, index) in userDepartments" :key="index" class="border-b">
                    <td class="px-4 py-2">
                      <a-tree-select
                        v-model="dept.departmentId"
                        placeholder="请选择部门"
                        :tree-data="organizationTreeData"
                        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                        tree-default-expand-all
                        show-search
                        :tree-node-filter-prop="'title'"
                        class="w-full"
                      />
                    </td>
                    <td class="px-4 py-2">
                      <a-select
                        v-model="dept.positionId"
                        placeholder="请选择岗位"
                        :options="positionOptions"
                        show-search
                        option-filter-prop="label"
                        class="w-full"
                      />
                    </td>
                    <td class="px-4 py-2">
                      <VbenButton
                        variant="destructive"
                        size="sm"
                        @click="handleRemoveDepartment(index)"
                      >
                        删除
                      </VbenButton>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <!-- 按钮区域 - 移到tab外边 -->
      <div class="flex justify-end gap-3 mt-6 pt-4 border-t px-5">
        <VbenButton variant="outline" @click="handleReset">
          取消
        </VbenButton>

        <!-- 基础信息tab的按钮 -->
        <template v-if="activeTab === 'basic'">
          <!-- 新增模式：显示下一步 -->
          <VbenButton v-if="!isUpdate" variant="default" @click="handleNext">
            下一步
          </VbenButton>
          <!-- 更新模式：显示保存基础信息 -->
          <VbenButton v-else variant="default" @click="handleSaveBasic">
            保存基础信息
          </VbenButton>
        </template>

        <!-- 部门信息tab的按钮 -->
        <template v-if="activeTab === 'departments'">
          <VbenButton variant="secondary" @click="activeTab = 'basic'">
            上一步
          </VbenButton>
          <!-- 新增模式：显示完成创建 -->
          <VbenButton v-if="!isUpdate" variant="default" @click="handleCreateUser">
            完成创建
          </VbenButton>
          <!-- 更新模式：显示保存部门信息 -->
          <VbenButton v-else variant="default" @click="handleSaveDepartments">
            保存部门信息
          </VbenButton>
        </template>
      </div>
    </div>
  </Modal>
</template>

<style scoped lang="less">
@import '#/styles/dark-antd.less';

.tab-content {
  min-height: 400px;
}

// 隐藏表单默认按钮
.hide-form-buttons {
  :deep(.ant-form-item:last-child) {
    display: none;
  }

  :deep(.form-footer) {
    display: none;
  }

  :deep(.ant-form-item-control-input) {
    .ant-form-item-control-input-content {
      .ant-btn {
        display: none;
      }
    }
  }
}
</style>
