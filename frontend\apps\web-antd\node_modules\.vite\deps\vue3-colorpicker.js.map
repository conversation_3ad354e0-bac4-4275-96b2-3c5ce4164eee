{"version": 3, "sources": ["../../../../../node_modules/.pnpm/gradient-parser@1.1.1/node_modules/gradient-parser/build/node.js", "../../../../../node_modules/.pnpm/tinycolor2@1.6.0/node_modules/tinycolor2/esm/tinycolor.js", "../../../../../node_modules/.pnpm/vue3-colorpicker@2.3.0_@aesoper+normal-utils@0.1.5_@popperjs+core@2.11.8_@vueuse+core@13.1.0__at25dv4s6dxcxn6wzvdp5wznem/node_modules/vue3-colorpicker/index.es.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/enums.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/math.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/userAgent.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/within.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getVariation.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/flip.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/hide.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/offset.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/debounce.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/createPopper.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper-lite.js", "../../../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper.js", "../../../../../node_modules/.pnpm/@aesoper+normal-utils@0.1.5/node_modules/@aesoper/normal-utils/NormalUtils.es.js"], "sourcesContent": ["// Copyright (c) 2014 <PERSON>. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nvar GradientParser = (GradientParser || {});\n\nGradientParser.stringify = (function() {\n\n  var visitor = {\n\n    'visit_linear-gradient': function(node) {\n      return visitor.visit_gradient(node);\n    },\n\n    'visit_repeating-linear-gradient': function(node) {\n      return visitor.visit_gradient(node);\n    },\n\n    'visit_radial-gradient': function(node) {\n      return visitor.visit_gradient(node);\n    },\n\n    'visit_repeating-radial-gradient': function(node) {\n      return visitor.visit_gradient(node);\n    },\n\n    'visit_gradient': function(node) {\n      var orientation = visitor.visit(node.orientation);\n      if (orientation) {\n        orientation += ', ';\n      }\n\n      return node.type + '(' + orientation + visitor.visit(node.colorStops) + ')';\n    },\n\n    'visit_shape': function(node) {\n      var result = node.value,\n          at = visitor.visit(node.at),\n          style = visitor.visit(node.style);\n\n      if (style) {\n        result += ' ' + style;\n      }\n\n      if (at) {\n        result += ' at ' + at;\n      }\n\n      return result;\n    },\n\n    'visit_default-radial': function(node) {\n      var result = '',\n          at = visitor.visit(node.at);\n\n      if (at) {\n        result += at;\n      }\n      return result;\n    },\n\n    'visit_extent-keyword': function(node) {\n      var result = node.value,\n          at = visitor.visit(node.at);\n\n      if (at) {\n        result += ' at ' + at;\n      }\n\n      return result;\n    },\n\n    'visit_position-keyword': function(node) {\n      return node.value;\n    },\n\n    'visit_position': function(node) {\n      return visitor.visit(node.value.x) + ' ' + visitor.visit(node.value.y);\n    },\n\n    'visit_%': function(node) {\n      return node.value + '%';\n    },\n\n    'visit_em': function(node) {\n      return node.value + 'em';\n    },\n\n    'visit_px': function(node) {\n      return node.value + 'px';\n    },\n\n    'visit_calc': function(node) {\n      return 'calc(' + node.value + ')';\n    },\n\n    'visit_literal': function(node) {\n      return visitor.visit_color(node.value, node);\n    },\n\n    'visit_hex': function(node) {\n      return visitor.visit_color('#' + node.value, node);\n    },\n\n    'visit_rgb': function(node) {\n      return visitor.visit_color('rgb(' + node.value.join(', ') + ')', node);\n    },\n\n    'visit_rgba': function(node) {\n      return visitor.visit_color('rgba(' + node.value.join(', ') + ')', node);\n    },\n\n    'visit_hsl': function(node) {\n      return visitor.visit_color('hsl(' + node.value[0] + ', ' + node.value[1] + '%, ' + node.value[2] + '%)', node);\n    },\n\n    'visit_hsla': function(node) {\n      return visitor.visit_color('hsla(' + node.value[0] + ', ' + node.value[1] + '%, ' + node.value[2] + '%, ' + node.value[3] + ')', node);\n    },\n\n    'visit_var': function(node) {\n      return visitor.visit_color('var(' + node.value + ')', node);\n    },\n\n    'visit_color': function(resultColor, node) {\n      var result = resultColor,\n          length = visitor.visit(node.length);\n\n      if (length) {\n        result += ' ' + length;\n      }\n      return result;\n    },\n\n    'visit_angular': function(node) {\n      return node.value + 'deg';\n    },\n\n    'visit_directional': function(node) {\n      return 'to ' + node.value;\n    },\n\n    'visit_array': function(elements) {\n      var result = '',\n          size = elements.length;\n\n      elements.forEach(function(element, i) {\n        result += visitor.visit(element);\n        if (i < size - 1) {\n          result += ', ';\n        }\n      });\n\n      return result;\n    },\n\n    'visit_object': function(obj) {\n      if (obj.width && obj.height) {\n        return visitor.visit(obj.width) + ' ' + visitor.visit(obj.height);\n      }\n      return '';\n    },\n\n    'visit': function(element) {\n      if (!element) {\n        return '';\n      }\n      var result = '';\n\n      if (element instanceof Array) {\n        return visitor.visit_array(element);\n      } else if (typeof element === 'object' && !element.type) {\n        return visitor.visit_object(element);\n      } else if (element.type) {\n        var nodeVisitor = visitor['visit_' + element.type];\n        if (nodeVisitor) {\n          return nodeVisitor(element);\n        } else {\n          throw Error('Missing visitor visit_' + element.type);\n        }\n      } else {\n        throw Error('Invalid node.');\n      }\n    }\n\n  };\n\n  return function(root) {\n    return visitor.visit(root);\n  };\n})();\n\n// Copyright (c) 2014 Rafael Caricio. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\nvar GradientParser = (GradientParser || {});\n\nGradientParser.parse = (function() {\n\n  var tokens = {\n    linearGradient: /^(\\-(webkit|o|ms|moz)\\-)?(linear\\-gradient)/i,\n    repeatingLinearGradient: /^(\\-(webkit|o|ms|moz)\\-)?(repeating\\-linear\\-gradient)/i,\n    radialGradient: /^(\\-(webkit|o|ms|moz)\\-)?(radial\\-gradient)/i,\n    repeatingRadialGradient: /^(\\-(webkit|o|ms|moz)\\-)?(repeating\\-radial\\-gradient)/i,\n    sideOrCorner: /^to (left (top|bottom)|right (top|bottom)|top (left|right)|bottom (left|right)|left|right|top|bottom)/i,\n    extentKeywords: /^(closest\\-side|closest\\-corner|farthest\\-side|farthest\\-corner|contain|cover)/,\n    positionKeywords: /^(left|center|right|top|bottom)/i,\n    pixelValue: /^(-?(([0-9]*\\.[0-9]+)|([0-9]+\\.?)))px/,\n    percentageValue: /^(-?(([0-9]*\\.[0-9]+)|([0-9]+\\.?)))\\%/,\n    emValue: /^(-?(([0-9]*\\.[0-9]+)|([0-9]+\\.?)))em/,\n    angleValue: /^(-?(([0-9]*\\.[0-9]+)|([0-9]+\\.?)))deg/,\n    radianValue: /^(-?(([0-9]*\\.[0-9]+)|([0-9]+\\.?)))rad/,\n    startCall: /^\\(/,\n    endCall: /^\\)/,\n    comma: /^,/,\n    hexColor: /^\\#([0-9a-fA-F]+)/,\n    literalColor: /^([a-zA-Z]+)/,\n    rgbColor: /^rgb/i,\n    rgbaColor: /^rgba/i,\n    varColor: /^var/i,\n    calcValue: /^calc/i,\n    variableName: /^(--[a-zA-Z0-9-,\\s\\#]+)/,\n    number: /^(([0-9]*\\.[0-9]+)|([0-9]+\\.?))/,\n    hslColor: /^hsl/i,\n    hslaColor: /^hsla/i,\n  };\n\n  var input = '';\n\n  function error(msg) {\n    var err = new Error(input + ': ' + msg);\n    err.source = input;\n    throw err;\n  }\n\n  function getAST() {\n    var ast = matchListDefinitions();\n\n    if (input.length > 0) {\n      error('Invalid input not EOF');\n    }\n\n    return ast;\n  }\n\n  function matchListDefinitions() {\n    return matchListing(matchDefinition);\n  }\n\n  function matchDefinition() {\n    return matchGradient(\n            'linear-gradient',\n            tokens.linearGradient,\n            matchLinearOrientation) ||\n\n          matchGradient(\n            'repeating-linear-gradient',\n            tokens.repeatingLinearGradient,\n            matchLinearOrientation) ||\n\n          matchGradient(\n            'radial-gradient',\n            tokens.radialGradient,\n            matchListRadialOrientations) ||\n\n          matchGradient(\n            'repeating-radial-gradient',\n            tokens.repeatingRadialGradient,\n            matchListRadialOrientations);\n  }\n\n  function matchGradient(gradientType, pattern, orientationMatcher) {\n    return matchCall(pattern, function(captures) {\n\n      var orientation = orientationMatcher();\n      if (orientation) {\n        if (!scan(tokens.comma)) {\n          error('Missing comma before color stops');\n        }\n      }\n\n      return {\n        type: gradientType,\n        orientation: orientation,\n        colorStops: matchListing(matchColorStop)\n      };\n    });\n  }\n\n  function matchCall(pattern, callback) {\n    var captures = scan(pattern);\n\n    if (captures) {\n      if (!scan(tokens.startCall)) {\n        error('Missing (');\n      }\n\n      var result = callback(captures);\n\n      if (!scan(tokens.endCall)) {\n        error('Missing )');\n      }\n\n      return result;\n    }\n  }\n\n  function matchLinearOrientation() {\n    // Check for standard CSS3 \"to\" direction\n    var sideOrCorner = matchSideOrCorner();\n    if (sideOrCorner) {\n      return sideOrCorner;\n    }\n    \n    // Check for legacy single keyword direction (e.g., \"right\", \"top\")\n    var legacyDirection = match('position-keyword', tokens.positionKeywords, 1);\n    if (legacyDirection) {\n      // For legacy syntax, we convert to the directional type\n      return {\n        type: 'directional',\n        value: legacyDirection.value\n      };\n    }\n    \n    // If neither, check for angle\n    return matchAngle();\n  }\n\n  function matchSideOrCorner() {\n    return match('directional', tokens.sideOrCorner, 1);\n  }\n\n  function matchAngle() {\n    return match('angular', tokens.angleValue, 1) ||\n      match('angular', tokens.radianValue, 1);\n  }\n\n  function matchListRadialOrientations() {\n    var radialOrientations,\n        radialOrientation = matchRadialOrientation(),\n        lookaheadCache;\n\n    if (radialOrientation) {\n      radialOrientations = [];\n      radialOrientations.push(radialOrientation);\n\n      lookaheadCache = input;\n      if (scan(tokens.comma)) {\n        radialOrientation = matchRadialOrientation();\n        if (radialOrientation) {\n          radialOrientations.push(radialOrientation);\n        } else {\n          input = lookaheadCache;\n        }\n      }\n    }\n\n    return radialOrientations;\n  }\n\n  function matchRadialOrientation() {\n    var radialType = matchCircle() ||\n      matchEllipse();\n\n    if (radialType) {\n      radialType.at = matchAtPosition();\n    } else {\n      var extent = matchExtentKeyword();\n      if (extent) {\n        radialType = extent;\n        var positionAt = matchAtPosition();\n        if (positionAt) {\n          radialType.at = positionAt;\n        }\n      } else {\n        // Check for \"at\" position first, which is a common browser output format\n        var atPosition = matchAtPosition();\n        if (atPosition) {\n          radialType = {\n            type: 'default-radial',\n            at: atPosition\n          };\n        } else {\n          var defaultPosition = matchPositioning();\n          if (defaultPosition) {\n            radialType = {\n              type: 'default-radial',\n              at: defaultPosition\n            };\n          }\n        }\n      }\n    }\n\n    return radialType;\n  }\n\n  function matchCircle() {\n    var circle = match('shape', /^(circle)/i, 0);\n\n    if (circle) {\n      circle.style = matchLength() || matchExtentKeyword();\n    }\n\n    return circle;\n  }\n\n  function matchEllipse() {\n    var ellipse = match('shape', /^(ellipse)/i, 0);\n\n    if (ellipse) {\n      ellipse.style = matchPositioning() || matchDistance() || matchExtentKeyword();\n    }\n\n    return ellipse;\n  }\n\n  function matchExtentKeyword() {\n    return match('extent-keyword', tokens.extentKeywords, 1);\n  }\n\n  function matchAtPosition() {\n    if (match('position', /^at/, 0)) {\n      var positioning = matchPositioning();\n\n      if (!positioning) {\n        error('Missing positioning value');\n      }\n\n      return positioning;\n    }\n  }\n\n  function matchPositioning() {\n    var location = matchCoordinates();\n\n    if (location.x || location.y) {\n      return {\n        type: 'position',\n        value: location\n      };\n    }\n  }\n\n  function matchCoordinates() {\n    return {\n      x: matchDistance(),\n      y: matchDistance()\n    };\n  }\n\n  function matchListing(matcher) {\n    var captures = matcher(),\n      result = [];\n\n    if (captures) {\n      result.push(captures);\n      while (scan(tokens.comma)) {\n        captures = matcher();\n        if (captures) {\n          result.push(captures);\n        } else {\n          error('One extra comma');\n        }\n      }\n    }\n\n    return result;\n  }\n\n  function matchColorStop() {\n    var color = matchColor();\n\n    if (!color) {\n      error('Expected color definition');\n    }\n\n    color.length = matchDistance();\n    return color;\n  }\n\n  function matchColor() {\n    return matchHexColor() ||\n      matchHSLAColor() ||\n      matchHSLColor() ||\n      matchRGBAColor() ||\n      matchRGBColor() ||\n      matchVarColor() ||\n      matchLiteralColor();\n  }\n\n  function matchLiteralColor() {\n    return match('literal', tokens.literalColor, 0);\n  }\n\n  function matchHexColor() {\n    return match('hex', tokens.hexColor, 1);\n  }\n\n  function matchRGBColor() {\n    return matchCall(tokens.rgbColor, function() {\n      return  {\n        type: 'rgb',\n        value: matchListing(matchNumber)\n      };\n    });\n  }\n\n  function matchRGBAColor() {\n    return matchCall(tokens.rgbaColor, function() {\n      return  {\n        type: 'rgba',\n        value: matchListing(matchNumber)\n      };\n    });\n  }\n\n  function matchVarColor() {\n    return matchCall(tokens.varColor, function () {\n      return {\n        type: 'var',\n        value: matchVariableName()\n      };\n    });\n  }\n\n  function matchHSLColor() {\n    return matchCall(tokens.hslColor, function() {\n      // Check for percentage before trying to parse the hue\n      var lookahead = scan(tokens.percentageValue);\n      if (lookahead) {\n        error('HSL hue value must be a number in degrees (0-360) or normalized (-360 to 360), not a percentage');\n      }\n      \n      var hue = matchNumber();\n      scan(tokens.comma);\n      var captures = scan(tokens.percentageValue);\n      var sat = captures ? captures[1] : null;\n      scan(tokens.comma);\n      captures = scan(tokens.percentageValue);\n      var light = captures ? captures[1] : null;\n      if (!sat || !light) {\n        error('Expected percentage value for saturation and lightness in HSL');\n      }\n      return {\n        type: 'hsl',\n        value: [hue, sat, light]\n      };\n    });\n  }\n\n  function matchHSLAColor() {\n    return matchCall(tokens.hslaColor, function() {\n      var hue = matchNumber();\n      scan(tokens.comma);\n      var captures = scan(tokens.percentageValue);\n      var sat = captures ? captures[1] : null;\n      scan(tokens.comma);\n      captures = scan(tokens.percentageValue);\n      var light = captures ? captures[1] : null;\n      scan(tokens.comma);\n      var alpha = matchNumber();\n      if (!sat || !light) {\n        error('Expected percentage value for saturation and lightness in HSLA');\n      }\n      return {\n        type: 'hsla',\n        value: [hue, sat, light, alpha]\n      };\n    });\n  }\n\n  function matchPercentage() {\n    var captures = scan(tokens.percentageValue);\n    return captures ? captures[1] : null;\n  }\n\n  function matchVariableName() {\n    return scan(tokens.variableName)[1];\n  }\n\n  function matchNumber() {\n    return scan(tokens.number)[1];\n  }\n\n  function matchDistance() {\n    return match('%', tokens.percentageValue, 1) ||\n      matchPositionKeyword() ||\n      matchCalc() ||\n      matchLength();\n  }\n\n  function matchPositionKeyword() {\n    return match('position-keyword', tokens.positionKeywords, 1);\n  }\n\n  function matchCalc() {\n    return matchCall(tokens.calcValue, function() {\n      var openParenCount = 1; // Start with the opening parenthesis from calc(\n      var i = 0;\n      \n      // Parse through the content looking for balanced parentheses\n      while (openParenCount > 0 && i < input.length) {\n        var char = input.charAt(i);\n        if (char === '(') {\n          openParenCount++;\n        } else if (char === ')') {\n          openParenCount--;\n        }\n        i++;\n      }\n      \n      // If we exited because we ran out of input but still have open parentheses, error\n      if (openParenCount > 0) {\n        error('Missing closing parenthesis in calc() expression');\n      }\n      \n      // Get the content inside the calc() without the last closing paren\n      var calcContent = input.substring(0, i - 1);\n      \n      // Consume the calc expression content\n      consume(i - 1); // -1 because we don't want to consume the closing parenthesis\n      \n      return {\n        type: 'calc',\n        value: calcContent\n      };\n    });\n  }\n\n  function matchLength() {\n    return match('px', tokens.pixelValue, 1) ||\n      match('em', tokens.emValue, 1);\n  }\n\n  function match(type, pattern, captureIndex) {\n    var captures = scan(pattern);\n    if (captures) {\n      return {\n        type: type,\n        value: captures[captureIndex]\n      };\n    }\n  }\n\n  function scan(regexp) {\n    var captures,\n        blankCaptures;\n\n    blankCaptures = /^[\\n\\r\\t\\s]+/.exec(input);\n    if (blankCaptures) {\n        consume(blankCaptures[0].length);\n    }\n\n    captures = regexp.exec(input);\n    if (captures) {\n        consume(captures[0].length);\n    }\n\n    return captures;\n  }\n\n  function consume(size) {\n    input = input.substr(size);\n  }\n\n  return function(code) {\n    input = code.toString().trim();\n    // Remove trailing semicolon if present\n    if (input.endsWith(';')) {\n      input = input.slice(0, -1);\n    }\n    return getAST();\n  };\n})();\n\nexports.parse = GradientParser.parse;\nexports.stringify = GradientParser.stringify;\n", "// This file is autogenerated. It's used to publish ESM to npm.\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\n\n// https://github.com/bgrins/TinyColor\n// <PERSON>, MIT License\n\nvar trimLeft = /^\\s+/;\nvar trimRight = /\\s+$/;\nfunction tinycolor(color, opts) {\n  color = color ? color : \"\";\n  opts = opts || {};\n\n  // If input is already a tinycolor, return itself\n  if (color instanceof tinycolor) {\n    return color;\n  }\n  // If we are called as a function, call using new instead\n  if (!(this instanceof tinycolor)) {\n    return new tinycolor(color, opts);\n  }\n  var rgb = inputToRGB(color);\n  this._originalInput = color, this._r = rgb.r, this._g = rgb.g, this._b = rgb.b, this._a = rgb.a, this._roundA = Math.round(100 * this._a) / 100, this._format = opts.format || rgb.format;\n  this._gradientType = opts.gradientType;\n\n  // Don't let the range of [0,255] come back in [0,1].\n  // Potentially lose a little bit of precision here, but will fix issues where\n  // .5 gets interpreted as half of the total, instead of half of 1\n  // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n  if (this._r < 1) this._r = Math.round(this._r);\n  if (this._g < 1) this._g = Math.round(this._g);\n  if (this._b < 1) this._b = Math.round(this._b);\n  this._ok = rgb.ok;\n}\ntinycolor.prototype = {\n  isDark: function isDark() {\n    return this.getBrightness() < 128;\n  },\n  isLight: function isLight() {\n    return !this.isDark();\n  },\n  isValid: function isValid() {\n    return this._ok;\n  },\n  getOriginalInput: function getOriginalInput() {\n    return this._originalInput;\n  },\n  getFormat: function getFormat() {\n    return this._format;\n  },\n  getAlpha: function getAlpha() {\n    return this._a;\n  },\n  getBrightness: function getBrightness() {\n    //http://www.w3.org/TR/AERT#color-contrast\n    var rgb = this.toRgb();\n    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n  },\n  getLuminance: function getLuminance() {\n    //http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n    var rgb = this.toRgb();\n    var RsRGB, GsRGB, BsRGB, R, G, B;\n    RsRGB = rgb.r / 255;\n    GsRGB = rgb.g / 255;\n    BsRGB = rgb.b / 255;\n    if (RsRGB <= 0.03928) R = RsRGB / 12.92;else R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n    if (GsRGB <= 0.03928) G = GsRGB / 12.92;else G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n    if (BsRGB <= 0.03928) B = BsRGB / 12.92;else B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  },\n  setAlpha: function setAlpha(value) {\n    this._a = boundAlpha(value);\n    this._roundA = Math.round(100 * this._a) / 100;\n    return this;\n  },\n  toHsv: function toHsv() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v,\n      a: this._a\n    };\n  },\n  toHsvString: function toHsvString() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    var h = Math.round(hsv.h * 360),\n      s = Math.round(hsv.s * 100),\n      v = Math.round(hsv.v * 100);\n    return this._a == 1 ? \"hsv(\" + h + \", \" + s + \"%, \" + v + \"%)\" : \"hsva(\" + h + \", \" + s + \"%, \" + v + \"%, \" + this._roundA + \")\";\n  },\n  toHsl: function toHsl() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    return {\n      h: hsl.h * 360,\n      s: hsl.s,\n      l: hsl.l,\n      a: this._a\n    };\n  },\n  toHslString: function toHslString() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    var h = Math.round(hsl.h * 360),\n      s = Math.round(hsl.s * 100),\n      l = Math.round(hsl.l * 100);\n    return this._a == 1 ? \"hsl(\" + h + \", \" + s + \"%, \" + l + \"%)\" : \"hsla(\" + h + \", \" + s + \"%, \" + l + \"%, \" + this._roundA + \")\";\n  },\n  toHex: function toHex(allow3Char) {\n    return rgbToHex(this._r, this._g, this._b, allow3Char);\n  },\n  toHexString: function toHexString(allow3Char) {\n    return \"#\" + this.toHex(allow3Char);\n  },\n  toHex8: function toHex8(allow4Char) {\n    return rgbaToHex(this._r, this._g, this._b, this._a, allow4Char);\n  },\n  toHex8String: function toHex8String(allow4Char) {\n    return \"#\" + this.toHex8(allow4Char);\n  },\n  toRgb: function toRgb() {\n    return {\n      r: Math.round(this._r),\n      g: Math.round(this._g),\n      b: Math.round(this._b),\n      a: this._a\n    };\n  },\n  toRgbString: function toRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \")\" : \"rgba(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \", \" + this._roundA + \")\";\n  },\n  toPercentageRgb: function toPercentageRgb() {\n    return {\n      r: Math.round(bound01(this._r, 255) * 100) + \"%\",\n      g: Math.round(bound01(this._g, 255) * 100) + \"%\",\n      b: Math.round(bound01(this._b, 255) * 100) + \"%\",\n      a: this._a\n    };\n  },\n  toPercentageRgbString: function toPercentageRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%)\" : \"rgba(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%, \" + this._roundA + \")\";\n  },\n  toName: function toName() {\n    if (this._a === 0) {\n      return \"transparent\";\n    }\n    if (this._a < 1) {\n      return false;\n    }\n    return hexNames[rgbToHex(this._r, this._g, this._b, true)] || false;\n  },\n  toFilter: function toFilter(secondColor) {\n    var hex8String = \"#\" + rgbaToArgbHex(this._r, this._g, this._b, this._a);\n    var secondHex8String = hex8String;\n    var gradientType = this._gradientType ? \"GradientType = 1, \" : \"\";\n    if (secondColor) {\n      var s = tinycolor(secondColor);\n      secondHex8String = \"#\" + rgbaToArgbHex(s._r, s._g, s._b, s._a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\" + gradientType + \"startColorstr=\" + hex8String + \",endColorstr=\" + secondHex8String + \")\";\n  },\n  toString: function toString(format) {\n    var formatSet = !!format;\n    format = format || this._format;\n    var formattedString = false;\n    var hasAlpha = this._a < 1 && this._a >= 0;\n    var needsAlphaFormat = !formatSet && hasAlpha && (format === \"hex\" || format === \"hex6\" || format === \"hex3\" || format === \"hex4\" || format === \"hex8\" || format === \"name\");\n    if (needsAlphaFormat) {\n      // Special case for \"transparent\", all other non-alpha formats\n      // will return rgba when there is transparency.\n      if (format === \"name\" && this._a === 0) {\n        return this.toName();\n      }\n      return this.toRgbString();\n    }\n    if (format === \"rgb\") {\n      formattedString = this.toRgbString();\n    }\n    if (format === \"prgb\") {\n      formattedString = this.toPercentageRgbString();\n    }\n    if (format === \"hex\" || format === \"hex6\") {\n      formattedString = this.toHexString();\n    }\n    if (format === \"hex3\") {\n      formattedString = this.toHexString(true);\n    }\n    if (format === \"hex4\") {\n      formattedString = this.toHex8String(true);\n    }\n    if (format === \"hex8\") {\n      formattedString = this.toHex8String();\n    }\n    if (format === \"name\") {\n      formattedString = this.toName();\n    }\n    if (format === \"hsl\") {\n      formattedString = this.toHslString();\n    }\n    if (format === \"hsv\") {\n      formattedString = this.toHsvString();\n    }\n    return formattedString || this.toHexString();\n  },\n  clone: function clone() {\n    return tinycolor(this.toString());\n  },\n  _applyModification: function _applyModification(fn, args) {\n    var color = fn.apply(null, [this].concat([].slice.call(args)));\n    this._r = color._r;\n    this._g = color._g;\n    this._b = color._b;\n    this.setAlpha(color._a);\n    return this;\n  },\n  lighten: function lighten() {\n    return this._applyModification(_lighten, arguments);\n  },\n  brighten: function brighten() {\n    return this._applyModification(_brighten, arguments);\n  },\n  darken: function darken() {\n    return this._applyModification(_darken, arguments);\n  },\n  desaturate: function desaturate() {\n    return this._applyModification(_desaturate, arguments);\n  },\n  saturate: function saturate() {\n    return this._applyModification(_saturate, arguments);\n  },\n  greyscale: function greyscale() {\n    return this._applyModification(_greyscale, arguments);\n  },\n  spin: function spin() {\n    return this._applyModification(_spin, arguments);\n  },\n  _applyCombination: function _applyCombination(fn, args) {\n    return fn.apply(null, [this].concat([].slice.call(args)));\n  },\n  analogous: function analogous() {\n    return this._applyCombination(_analogous, arguments);\n  },\n  complement: function complement() {\n    return this._applyCombination(_complement, arguments);\n  },\n  monochromatic: function monochromatic() {\n    return this._applyCombination(_monochromatic, arguments);\n  },\n  splitcomplement: function splitcomplement() {\n    return this._applyCombination(_splitcomplement, arguments);\n  },\n  // Disabled until https://github.com/bgrins/TinyColor/issues/254\n  // polyad: function (number) {\n  //   return this._applyCombination(polyad, [number]);\n  // },\n  triad: function triad() {\n    return this._applyCombination(polyad, [3]);\n  },\n  tetrad: function tetrad() {\n    return this._applyCombination(polyad, [4]);\n  }\n};\n\n// If input is an object, force 1 into \"1.0\" to handle ratios properly\n// String input requires \"1.0\" as input, so 1 will be treated as 1\ntinycolor.fromRatio = function (color, opts) {\n  if (_typeof(color) == \"object\") {\n    var newColor = {};\n    for (var i in color) {\n      if (color.hasOwnProperty(i)) {\n        if (i === \"a\") {\n          newColor[i] = color[i];\n        } else {\n          newColor[i] = convertToPercentage(color[i]);\n        }\n      }\n    }\n    color = newColor;\n  }\n  return tinycolor(color, opts);\n};\n\n// Given a string or object, convert that input to RGB\n// Possible string inputs:\n//\n//     \"red\"\n//     \"#f00\" or \"f00\"\n//     \"#ff0000\" or \"ff0000\"\n//     \"#ff000000\" or \"ff000000\"\n//     \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n//     \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n//     \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n//     \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n//     \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n//     \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n//     \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n//\nfunction inputToRGB(color) {\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  var a = 1;\n  var s = null;\n  var v = null;\n  var l = null;\n  var ok = false;\n  var format = false;\n  if (typeof color == \"string\") {\n    color = stringInputToObject(color);\n  }\n  if (_typeof(color) == \"object\") {\n    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n      rgb = rgbToRgb(color.r, color.g, color.b);\n      ok = true;\n      format = String(color.r).substr(-1) === \"%\" ? \"prgb\" : \"rgb\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n      s = convertToPercentage(color.s);\n      v = convertToPercentage(color.v);\n      rgb = hsvToRgb(color.h, s, v);\n      ok = true;\n      format = \"hsv\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n      s = convertToPercentage(color.s);\n      l = convertToPercentage(color.l);\n      rgb = hslToRgb(color.h, s, l);\n      ok = true;\n      format = \"hsl\";\n    }\n    if (color.hasOwnProperty(\"a\")) {\n      a = color.a;\n    }\n  }\n  a = boundAlpha(a);\n  return {\n    ok: ok,\n    format: color.format || format,\n    r: Math.min(255, Math.max(rgb.r, 0)),\n    g: Math.min(255, Math.max(rgb.g, 0)),\n    b: Math.min(255, Math.max(rgb.b, 0)),\n    a: a\n  };\n}\n\n// Conversion Functions\n// --------------------\n\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n\n// `rgbToRgb`\n// Handle bounds / percentage checking to conform to CSS color spec\n// <http://www.w3.org/TR/css3-color/>\n// *Assumes:* r, g, b in [0, 255] or [0, 1]\n// *Returns:* { r, g, b } in [0, 255]\nfunction rgbToRgb(r, g, b) {\n  return {\n    r: bound01(r, 255) * 255,\n    g: bound01(g, 255) * 255,\n    b: bound01(b, 255) * 255\n  };\n}\n\n// `rgbToHsl`\n// Converts an RGB color value to HSL.\n// *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n// *Returns:* { h, s, l } in [0,1]\nfunction rgbToHsl(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    l = (max + min) / 2;\n  if (max == min) {\n    h = s = 0; // achromatic\n  } else {\n    var d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    l: l\n  };\n}\n\n// `hslToRgb`\n// Converts an HSL color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hslToRgb(h, s, l) {\n  var r, g, b;\n  h = bound01(h, 360);\n  s = bound01(s, 100);\n  l = bound01(l, 100);\n  function hue2rgb(p, q, t) {\n    if (t < 0) t += 1;\n    if (t > 1) t -= 1;\n    if (t < 1 / 6) return p + (q - p) * 6 * t;\n    if (t < 1 / 2) return q;\n    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n  }\n  if (s === 0) {\n    r = g = b = l; // achromatic\n  } else {\n    var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n    var p = 2 * l - q;\n    r = hue2rgb(p, q, h + 1 / 3);\n    g = hue2rgb(p, q, h);\n    b = hue2rgb(p, q, h - 1 / 3);\n  }\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHsv`\n// Converts an RGB color value to HSV\n// *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n// *Returns:* { h, s, v } in [0,1]\nfunction rgbToHsv(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    v = max;\n  var d = max - min;\n  s = max === 0 ? 0 : d / max;\n  if (max == min) {\n    h = 0; // achromatic\n  } else {\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    v: v\n  };\n}\n\n// `hsvToRgb`\n// Converts an HSV color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hsvToRgb(h, s, v) {\n  h = bound01(h, 360) * 6;\n  s = bound01(s, 100);\n  v = bound01(v, 100);\n  var i = Math.floor(h),\n    f = h - i,\n    p = v * (1 - s),\n    q = v * (1 - f * s),\n    t = v * (1 - (1 - f) * s),\n    mod = i % 6,\n    r = [v, q, p, p, t, v][mod],\n    g = [t, v, v, q, p, p][mod],\n    b = [p, p, t, v, v, q][mod];\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHex`\n// Converts an RGB color to hex\n// Assumes r, g, and b are contained in the set [0, 255]\n// Returns a 3 or 6 character hex\nfunction rgbToHex(r, g, b, allow3Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n\n  // Return a 3 character hex if possible\n  if (allow3Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToHex`\n// Converts an RGBA color plus alpha transparency to hex\n// Assumes r, g, b are contained in the set [0, 255] and\n// a in [0, 1]. Returns a 4 or 8 character rgba hex\nfunction rgbaToHex(r, g, b, a, allow4Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16)), pad2(convertDecimalToHex(a))];\n\n  // Return a 4 character hex if possible\n  if (allow4Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1) && hex[3].charAt(0) == hex[3].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToArgbHex`\n// Converts an RGBA color to an ARGB Hex8 string\n// Rarely used, but required for \"toFilter()\"\nfunction rgbaToArgbHex(r, g, b, a) {\n  var hex = [pad2(convertDecimalToHex(a)), pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  return hex.join(\"\");\n}\n\n// `equals`\n// Can be called with any tinycolor input\ntinycolor.equals = function (color1, color2) {\n  if (!color1 || !color2) return false;\n  return tinycolor(color1).toRgbString() == tinycolor(color2).toRgbString();\n};\ntinycolor.random = function () {\n  return tinycolor.fromRatio({\n    r: Math.random(),\n    g: Math.random(),\n    b: Math.random()\n  });\n};\n\n// Modification Functions\n// ----------------------\n// Thanks to less.js for some of the basics here\n// <https://github.com/cloudhead/less.js/blob/master/lib/less/functions.js>\n\nfunction _desaturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s -= amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _saturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s += amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _greyscale(color) {\n  return tinycolor(color).desaturate(100);\n}\nfunction _lighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l += amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\nfunction _brighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var rgb = tinycolor(color).toRgb();\n  rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n  rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n  rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n  return tinycolor(rgb);\n}\nfunction _darken(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l -= amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\n\n// Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n// Values outside of this range will be wrapped into this range.\nfunction _spin(color, amount) {\n  var hsl = tinycolor(color).toHsl();\n  var hue = (hsl.h + amount) % 360;\n  hsl.h = hue < 0 ? 360 + hue : hue;\n  return tinycolor(hsl);\n}\n\n// Combination Functions\n// ---------------------\n// Thanks to jQuery xColor for some of the ideas behind these\n// <https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js>\n\nfunction _complement(color) {\n  var hsl = tinycolor(color).toHsl();\n  hsl.h = (hsl.h + 180) % 360;\n  return tinycolor(hsl);\n}\nfunction polyad(color, number) {\n  if (isNaN(number) || number <= 0) {\n    throw new Error(\"Argument to polyad must be a positive number\");\n  }\n  var hsl = tinycolor(color).toHsl();\n  var result = [tinycolor(color)];\n  var step = 360 / number;\n  for (var i = 1; i < number; i++) {\n    result.push(tinycolor({\n      h: (hsl.h + i * step) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }));\n  }\n  return result;\n}\nfunction _splitcomplement(color) {\n  var hsl = tinycolor(color).toHsl();\n  var h = hsl.h;\n  return [tinycolor(color), tinycolor({\n    h: (h + 72) % 360,\n    s: hsl.s,\n    l: hsl.l\n  }), tinycolor({\n    h: (h + 216) % 360,\n    s: hsl.s,\n    l: hsl.l\n  })];\n}\nfunction _analogous(color, results, slices) {\n  results = results || 6;\n  slices = slices || 30;\n  var hsl = tinycolor(color).toHsl();\n  var part = 360 / slices;\n  var ret = [tinycolor(color)];\n  for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;) {\n    hsl.h = (hsl.h + part) % 360;\n    ret.push(tinycolor(hsl));\n  }\n  return ret;\n}\nfunction _monochromatic(color, results) {\n  results = results || 6;\n  var hsv = tinycolor(color).toHsv();\n  var h = hsv.h,\n    s = hsv.s,\n    v = hsv.v;\n  var ret = [];\n  var modification = 1 / results;\n  while (results--) {\n    ret.push(tinycolor({\n      h: h,\n      s: s,\n      v: v\n    }));\n    v = (v + modification) % 1;\n  }\n  return ret;\n}\n\n// Utility Functions\n// ---------------------\n\ntinycolor.mix = function (color1, color2, amount) {\n  amount = amount === 0 ? 0 : amount || 50;\n  var rgb1 = tinycolor(color1).toRgb();\n  var rgb2 = tinycolor(color2).toRgb();\n  var p = amount / 100;\n  var rgba = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b,\n    a: (rgb2.a - rgb1.a) * p + rgb1.a\n  };\n  return tinycolor(rgba);\n};\n\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n\n// `contrast`\n// Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\ntinycolor.readability = function (color1, color2) {\n  var c1 = tinycolor(color1);\n  var c2 = tinycolor(color2);\n  return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);\n};\n\n// `isReadable`\n// Ensure that foreground and background color combinations meet WCAG2 guidelines.\n// The third argument is an optional Object.\n//      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n//      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n// If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n\n// *Example*\n//    tinycolor.isReadable(\"#000\", \"#111\") => false\n//    tinycolor.isReadable(\"#000\", \"#111\",{level:\"AA\",size:\"large\"}) => false\ntinycolor.isReadable = function (color1, color2, wcag2) {\n  var readability = tinycolor.readability(color1, color2);\n  var wcag2Parms, out;\n  out = false;\n  wcag2Parms = validateWCAG2Parms(wcag2);\n  switch (wcag2Parms.level + wcag2Parms.size) {\n    case \"AAsmall\":\n    case \"AAAlarge\":\n      out = readability >= 4.5;\n      break;\n    case \"AAlarge\":\n      out = readability >= 3;\n      break;\n    case \"AAAsmall\":\n      out = readability >= 7;\n      break;\n  }\n  return out;\n};\n\n// `mostReadable`\n// Given a base color and a list of possible foreground or background\n// colors for that base, returns the most readable color.\n// Optionally returns Black or White if the most readable color is unreadable.\n// *Example*\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:false}).toHexString(); // \"#112255\"\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:true}).toHexString();  // \"#ffffff\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"large\"}).toHexString(); // \"#faf3f3\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"small\"}).toHexString(); // \"#ffffff\"\ntinycolor.mostReadable = function (baseColor, colorList, args) {\n  var bestColor = null;\n  var bestScore = 0;\n  var readability;\n  var includeFallbackColors, level, size;\n  args = args || {};\n  includeFallbackColors = args.includeFallbackColors;\n  level = args.level;\n  size = args.size;\n  for (var i = 0; i < colorList.length; i++) {\n    readability = tinycolor.readability(baseColor, colorList[i]);\n    if (readability > bestScore) {\n      bestScore = readability;\n      bestColor = tinycolor(colorList[i]);\n    }\n  }\n  if (tinycolor.isReadable(baseColor, bestColor, {\n    level: level,\n    size: size\n  }) || !includeFallbackColors) {\n    return bestColor;\n  } else {\n    args.includeFallbackColors = false;\n    return tinycolor.mostReadable(baseColor, [\"#fff\", \"#000\"], args);\n  }\n};\n\n// Big List of Colors\n// ------------------\n// <https://www.w3.org/TR/css-color-4/#named-colors>\nvar names = tinycolor.names = {\n  aliceblue: \"f0f8ff\",\n  antiquewhite: \"faebd7\",\n  aqua: \"0ff\",\n  aquamarine: \"7fffd4\",\n  azure: \"f0ffff\",\n  beige: \"f5f5dc\",\n  bisque: \"ffe4c4\",\n  black: \"000\",\n  blanchedalmond: \"ffebcd\",\n  blue: \"00f\",\n  blueviolet: \"8a2be2\",\n  brown: \"a52a2a\",\n  burlywood: \"deb887\",\n  burntsienna: \"ea7e5d\",\n  cadetblue: \"5f9ea0\",\n  chartreuse: \"7fff00\",\n  chocolate: \"d2691e\",\n  coral: \"ff7f50\",\n  cornflowerblue: \"6495ed\",\n  cornsilk: \"fff8dc\",\n  crimson: \"dc143c\",\n  cyan: \"0ff\",\n  darkblue: \"00008b\",\n  darkcyan: \"008b8b\",\n  darkgoldenrod: \"b8860b\",\n  darkgray: \"a9a9a9\",\n  darkgreen: \"006400\",\n  darkgrey: \"a9a9a9\",\n  darkkhaki: \"bdb76b\",\n  darkmagenta: \"8b008b\",\n  darkolivegreen: \"556b2f\",\n  darkorange: \"ff8c00\",\n  darkorchid: \"9932cc\",\n  darkred: \"8b0000\",\n  darksalmon: \"e9967a\",\n  darkseagreen: \"8fbc8f\",\n  darkslateblue: \"483d8b\",\n  darkslategray: \"2f4f4f\",\n  darkslategrey: \"2f4f4f\",\n  darkturquoise: \"00ced1\",\n  darkviolet: \"9400d3\",\n  deeppink: \"ff1493\",\n  deepskyblue: \"00bfff\",\n  dimgray: \"696969\",\n  dimgrey: \"696969\",\n  dodgerblue: \"1e90ff\",\n  firebrick: \"b22222\",\n  floralwhite: \"fffaf0\",\n  forestgreen: \"228b22\",\n  fuchsia: \"f0f\",\n  gainsboro: \"dcdcdc\",\n  ghostwhite: \"f8f8ff\",\n  gold: \"ffd700\",\n  goldenrod: \"daa520\",\n  gray: \"808080\",\n  green: \"008000\",\n  greenyellow: \"adff2f\",\n  grey: \"808080\",\n  honeydew: \"f0fff0\",\n  hotpink: \"ff69b4\",\n  indianred: \"cd5c5c\",\n  indigo: \"4b0082\",\n  ivory: \"fffff0\",\n  khaki: \"f0e68c\",\n  lavender: \"e6e6fa\",\n  lavenderblush: \"fff0f5\",\n  lawngreen: \"7cfc00\",\n  lemonchiffon: \"fffacd\",\n  lightblue: \"add8e6\",\n  lightcoral: \"f08080\",\n  lightcyan: \"e0ffff\",\n  lightgoldenrodyellow: \"fafad2\",\n  lightgray: \"d3d3d3\",\n  lightgreen: \"90ee90\",\n  lightgrey: \"d3d3d3\",\n  lightpink: \"ffb6c1\",\n  lightsalmon: \"ffa07a\",\n  lightseagreen: \"20b2aa\",\n  lightskyblue: \"87cefa\",\n  lightslategray: \"789\",\n  lightslategrey: \"789\",\n  lightsteelblue: \"b0c4de\",\n  lightyellow: \"ffffe0\",\n  lime: \"0f0\",\n  limegreen: \"32cd32\",\n  linen: \"faf0e6\",\n  magenta: \"f0f\",\n  maroon: \"800000\",\n  mediumaquamarine: \"66cdaa\",\n  mediumblue: \"0000cd\",\n  mediumorchid: \"ba55d3\",\n  mediumpurple: \"9370db\",\n  mediumseagreen: \"3cb371\",\n  mediumslateblue: \"7b68ee\",\n  mediumspringgreen: \"00fa9a\",\n  mediumturquoise: \"48d1cc\",\n  mediumvioletred: \"c71585\",\n  midnightblue: \"191970\",\n  mintcream: \"f5fffa\",\n  mistyrose: \"ffe4e1\",\n  moccasin: \"ffe4b5\",\n  navajowhite: \"ffdead\",\n  navy: \"000080\",\n  oldlace: \"fdf5e6\",\n  olive: \"808000\",\n  olivedrab: \"6b8e23\",\n  orange: \"ffa500\",\n  orangered: \"ff4500\",\n  orchid: \"da70d6\",\n  palegoldenrod: \"eee8aa\",\n  palegreen: \"98fb98\",\n  paleturquoise: \"afeeee\",\n  palevioletred: \"db7093\",\n  papayawhip: \"ffefd5\",\n  peachpuff: \"ffdab9\",\n  peru: \"cd853f\",\n  pink: \"ffc0cb\",\n  plum: \"dda0dd\",\n  powderblue: \"b0e0e6\",\n  purple: \"800080\",\n  rebeccapurple: \"663399\",\n  red: \"f00\",\n  rosybrown: \"bc8f8f\",\n  royalblue: \"4169e1\",\n  saddlebrown: \"8b4513\",\n  salmon: \"fa8072\",\n  sandybrown: \"f4a460\",\n  seagreen: \"2e8b57\",\n  seashell: \"fff5ee\",\n  sienna: \"a0522d\",\n  silver: \"c0c0c0\",\n  skyblue: \"87ceeb\",\n  slateblue: \"6a5acd\",\n  slategray: \"708090\",\n  slategrey: \"708090\",\n  snow: \"fffafa\",\n  springgreen: \"00ff7f\",\n  steelblue: \"4682b4\",\n  tan: \"d2b48c\",\n  teal: \"008080\",\n  thistle: \"d8bfd8\",\n  tomato: \"ff6347\",\n  turquoise: \"40e0d0\",\n  violet: \"ee82ee\",\n  wheat: \"f5deb3\",\n  white: \"fff\",\n  whitesmoke: \"f5f5f5\",\n  yellow: \"ff0\",\n  yellowgreen: \"9acd32\"\n};\n\n// Make it easy to access colors via `hexNames[hex]`\nvar hexNames = tinycolor.hexNames = flip(names);\n\n// Utilities\n// ---------\n\n// `{ 'name1': 'val1' }` becomes `{ 'val1': 'name1' }`\nfunction flip(o) {\n  var flipped = {};\n  for (var i in o) {\n    if (o.hasOwnProperty(i)) {\n      flipped[o[i]] = i;\n    }\n  }\n  return flipped;\n}\n\n// Return a valid alpha value [0,1] with all invalid values being set to 1\nfunction boundAlpha(a) {\n  a = parseFloat(a);\n  if (isNaN(a) || a < 0 || a > 1) {\n    a = 1;\n  }\n  return a;\n}\n\n// Take input from [0, n] and return it as [0, 1]\nfunction bound01(n, max) {\n  if (isOnePointZero(n)) n = \"100%\";\n  var processPercent = isPercentage(n);\n  n = Math.min(max, Math.max(0, parseFloat(n)));\n\n  // Automatically convert percentage into number\n  if (processPercent) {\n    n = parseInt(n * max, 10) / 100;\n  }\n\n  // Handle floating point rounding errors\n  if (Math.abs(n - max) < 0.000001) {\n    return 1;\n  }\n\n  // Convert into [0, 1] range if it isn't already\n  return n % max / parseFloat(max);\n}\n\n// Force a number between 0 and 1\nfunction clamp01(val) {\n  return Math.min(1, Math.max(0, val));\n}\n\n// Parse a base-16 hex value into a base-10 integer\nfunction parseIntFromHex(val) {\n  return parseInt(val, 16);\n}\n\n// Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n// <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\nfunction isOnePointZero(n) {\n  return typeof n == \"string\" && n.indexOf(\".\") != -1 && parseFloat(n) === 1;\n}\n\n// Check to see if string passed in is a percentage\nfunction isPercentage(n) {\n  return typeof n === \"string\" && n.indexOf(\"%\") != -1;\n}\n\n// Force a hex value to have 2 characters\nfunction pad2(c) {\n  return c.length == 1 ? \"0\" + c : \"\" + c;\n}\n\n// Replace a decimal with it's percentage value\nfunction convertToPercentage(n) {\n  if (n <= 1) {\n    n = n * 100 + \"%\";\n  }\n  return n;\n}\n\n// Converts a decimal to a hex value\nfunction convertDecimalToHex(d) {\n  return Math.round(parseFloat(d) * 255).toString(16);\n}\n// Converts a hex value to a decimal\nfunction convertHexToDecimal(h) {\n  return parseIntFromHex(h) / 255;\n}\nvar matchers = function () {\n  // <http://www.w3.org/TR/css3-values/#integers>\n  var CSS_INTEGER = \"[-\\\\+]?\\\\d+%?\";\n\n  // <http://www.w3.org/TR/css3-values/#number-value>\n  var CSS_NUMBER = \"[-\\\\+]?\\\\d*\\\\.\\\\d+%?\";\n\n  // Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\n  var CSS_UNIT = \"(?:\" + CSS_NUMBER + \")|(?:\" + CSS_INTEGER + \")\";\n\n  // Actual matching.\n  // Parentheses and commas are optional, but not required.\n  // Whitespace can take the place of commas or opening paren\n  var PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  var PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  return {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp(\"rgb\" + PERMISSIVE_MATCH3),\n    rgba: new RegExp(\"rgba\" + PERMISSIVE_MATCH4),\n    hsl: new RegExp(\"hsl\" + PERMISSIVE_MATCH3),\n    hsla: new RegExp(\"hsla\" + PERMISSIVE_MATCH4),\n    hsv: new RegExp(\"hsv\" + PERMISSIVE_MATCH3),\n    hsva: new RegExp(\"hsva\" + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n  };\n}();\n\n// `isValidCSSUnit`\n// Take in a single string / number and check to see if it looks like a CSS unit\n// (see `matchers` above for definition).\nfunction isValidCSSUnit(color) {\n  return !!matchers.CSS_UNIT.exec(color);\n}\n\n// `stringInputToObject`\n// Permissive string parsing.  Take in a number of formats, and output an object\n// based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\nfunction stringInputToObject(color) {\n  color = color.replace(trimLeft, \"\").replace(trimRight, \"\").toLowerCase();\n  var named = false;\n  if (names[color]) {\n    color = names[color];\n    named = true;\n  } else if (color == \"transparent\") {\n    return {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 0,\n      format: \"name\"\n    };\n  }\n\n  // Try to match string input using regular expressions.\n  // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n  // Just return an object and let the conversion functions handle that.\n  // This way the result will be the same whether the tinycolor is initialized with string or object.\n  var match;\n  if (match = matchers.rgb.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3]\n    };\n  }\n  if (match = matchers.rgba.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsl.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3]\n    };\n  }\n  if (match = matchers.hsla.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsv.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3]\n    };\n  }\n  if (match = matchers.hsva.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hex8.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      a: convertHexToDecimal(match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex6.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  if (match = matchers.hex4.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      a: convertHexToDecimal(match[4] + \"\" + match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex3.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  return false;\n}\nfunction validateWCAG2Parms(parms) {\n  // return valid WCAG2 parms for isReadable.\n  // If input parms are invalid, return {\"level\":\"AA\", \"size\":\"small\"}\n  var level, size;\n  parms = parms || {\n    level: \"AA\",\n    size: \"small\"\n  };\n  level = (parms.level || \"AA\").toUpperCase();\n  size = (parms.size || \"small\").toLowerCase();\n  if (level !== \"AA\" && level !== \"AAA\") {\n    level = \"AA\";\n  }\n  if (size !== \"small\" && size !== \"large\") {\n    size = \"small\";\n  }\n  return {\n    level: level,\n    size: size\n  };\n}\n\nexport { tinycolor as default };\n", "var qe = Object.defineProperty;\nvar Ye = (e, t, o) => t in e ? qe(e, t, { enumerable: !0, configurable: !0, writable: !0, value: o }) : e[t] = o;\nvar W = (e, t, o) => (Ye(e, typeof t != \"symbol\" ? t + \"\" : t, o), o);\nimport { defineComponent as G, ref as M, reactive as Y, watch as x, computed as K, openBlock as C, createElementBlock as $, normalizeClass as O, createElementVNode as u, normalizeStyle as D, pushScopeId as ee, popScopeId as te, Fragment as Q, renderList as ne, getCurrentInstance as Ue, nextTick as je, createCommentVNode as B, withDirectives as le, vModelText as Ze, createTextVNode as Ee, toDisplayString as se, resolveComponent as V, createBlock as I, createVNode as Z, onMounted as Je, inject as Ie, vShow as ge, renderSlot as he, provide as Qe, useSlots as xe, withCtx as Be, resolveDynamicComponent as He, mergeProps as Re, Teleport as et } from \"vue\";\nimport { tryOnMounted as oe, whenever as T, useClipboard as tt, useDebounceFn as j, useLocalStorage as pe, onClickOutside as ot } from \"@vueuse/core\";\nimport R from \"tinycolor2\";\nimport { stringify as nt, parse as at } from \"gradient-parser\";\nimport { createPopper as rt } from \"@popperjs/core\";\nimport v from \"vue-types\";\nimport { DOMUtils as ae } from \"@aesoper/normal-utils\";\nimport { merge as ie } from \"lodash-es\";\nconst P = (e) => Math.round(e * 100) / 100;\nclass A {\n  constructor(t) {\n    W(this, \"instance\");\n    W(this, \"alphaValue\", 0);\n    // RGB\n    W(this, \"redValue\", 0);\n    W(this, \"greenValue\", 0);\n    W(this, \"blueValue\", 0);\n    // HSV\n    W(this, \"hueValue\", 0);\n    W(this, \"saturationValue\", 0);\n    W(this, \"brightnessValue\", 0);\n    // HSL\n    W(this, \"hslSaturationValue\", 0);\n    W(this, \"lightnessValue\", 0);\n    W(this, \"initAlpha\", () => {\n      const t = this.instance.getAlpha();\n      this.alphaValue = Math.min(1, t) * 100;\n    });\n    W(this, \"initLightness\", () => {\n      const { s: t, l: o } = this.instance.toHsl();\n      this.hslSaturationValue = P(t), this.lightnessValue = P(o);\n    });\n    W(this, \"initRgb\", () => {\n      const { r: t, g: o, b: n } = this.instance.toRgb();\n      this.redValue = P(t), this.greenValue = P(o), this.blueValue = P(n);\n    });\n    W(this, \"initHsb\", () => {\n      const { h: t, s: o, v: n } = this.instance.toHsv();\n      this.hueValue = Math.min(360, Math.ceil(t)), this.saturationValue = P(o), this.brightnessValue = P(n);\n    });\n    W(this, \"toHexString\", () => this.instance.toHexString());\n    W(this, \"toRgbString\", () => this.instance.toRgbString());\n    this.instance = R(t), this.initRgb(), this.initHsb(), this.initLightness(), this.initAlpha();\n  }\n  toString(t) {\n    return this.instance.toString(t);\n  }\n  get hex() {\n    return this.instance.toHex();\n  }\n  set hex(t) {\n    this.instance = R(t), this.initHsb(), this.initRgb(), this.initAlpha(), this.initLightness();\n  }\n  // 色调\n  set hue(t) {\n    this.saturation === 0 && this.brightness === 0 && (this.saturationValue = 1, this.brightnessValue = 1), this.instance = R({\n      h: P(t),\n      s: this.saturation,\n      v: this.brightness,\n      a: this.alphaValue / 100\n    }), this.initRgb(), this.initLightness(), this.hueValue = P(t);\n  }\n  get hue() {\n    return this.hueValue;\n  }\n  // 饱和度\n  set saturation(t) {\n    this.instance = R({\n      h: this.hue,\n      s: P(t),\n      v: this.brightness,\n      a: this.alphaValue / 100\n    }), this.initRgb(), this.initLightness(), this.saturationValue = P(t);\n  }\n  get saturation() {\n    return this.saturationValue;\n  }\n  // 明度\n  set brightness(t) {\n    this.instance = R({\n      h: this.hue,\n      s: this.saturation,\n      v: P(t),\n      a: this.alphaValue / 100\n    }), this.initRgb(), this.initLightness(), this.brightnessValue = P(t);\n  }\n  get brightness() {\n    return this.brightnessValue;\n  }\n  // 亮度\n  set lightness(t) {\n    this.instance = R({\n      h: this.hue,\n      s: this.hslSaturationValue,\n      l: P(t),\n      a: this.alphaValue / 100\n    }), this.initRgb(), this.initHsb(), this.lightnessValue = P(t);\n  }\n  get lightness() {\n    return this.lightnessValue;\n  }\n  // red\n  set red(t) {\n    const o = this.instance.toRgb();\n    this.instance = R({\n      ...o,\n      r: P(t),\n      a: this.alphaValue / 100\n    }), this.initHsb(), this.initLightness(), this.redValue = P(t);\n  }\n  get red() {\n    return this.redValue;\n  }\n  // green\n  set green(t) {\n    const o = this.instance.toRgb();\n    this.instance = R({\n      ...o,\n      g: P(t),\n      a: this.alphaValue / 100\n    }), this.initHsb(), this.initLightness(), this.greenValue = P(t);\n  }\n  get green() {\n    return this.greenValue;\n  }\n  // blue\n  set blue(t) {\n    const o = this.instance.toRgb();\n    this.instance = R({\n      ...o,\n      b: P(t),\n      a: this.alphaValue / 100\n    }), this.initHsb(), this.initLightness(), this.blueValue = P(t);\n  }\n  get blue() {\n    return this.blueValue;\n  }\n  // alpha\n  set alpha(t) {\n    this.instance.setAlpha(t / 100), this.alphaValue = t;\n  }\n  get alpha() {\n    return this.alphaValue;\n  }\n  get RGB() {\n    return [this.red, this.green, this.blue, parseFloat((this.alpha / 100).toFixed(2))];\n  }\n  get HSB() {\n    return [this.hue, this.saturation, this.brightness, parseFloat((this.alpha / 100).toFixed(2))];\n  }\n  get HSL() {\n    return [\n      this.hue,\n      this.hslSaturationValue,\n      this.lightness,\n      parseFloat((this.alpha / 100).toFixed(2))\n    ];\n  }\n}\nfunction Ae(e, t, o, n) {\n  return `rgba(${[e, t, o, n / 100].join(\",\")})`;\n}\nconst ue = (e, t, o) => t < o ? e < t ? t : e > o ? o : e : e < o ? o : e > t ? t : e, fe = \"color-history\", Ce = 8;\nconst q = (e, t) => {\n  const o = e.__vccOpts || e;\n  for (const [n, i] of t)\n    o[n] = i;\n  return o;\n}, lt = G({\n  name: \"Alpha\",\n  props: {\n    color: v.instanceOf(A),\n    size: v.oneOf([\"small\", \"default\"]).def(\"default\")\n  },\n  emits: [\"change\"],\n  setup(e, { emit: t }) {\n    const o = M(null), n = M(null);\n    let i = e.color || new A();\n    const l = Y({\n      red: i.red,\n      green: i.green,\n      blue: i.blue,\n      alpha: i.alpha\n    });\n    x(\n      () => e.color,\n      (g) => {\n        g && (i = g, ie(l, {\n          red: g.red,\n          green: g.green,\n          blue: g.blue,\n          alpha: g.alpha\n        }));\n      },\n      { deep: !0 }\n    );\n    const a = K(() => {\n      const g = Ae(l.red, l.green, l.blue, 0), d = Ae(l.red, l.green, l.blue, 100);\n      return {\n        background: `linear-gradient(to right, ${g} , ${d})`\n      };\n    }), r = () => {\n      if (o.value && n.value) {\n        const g = l.alpha / 100, d = o.value.getBoundingClientRect(), m = n.value.offsetWidth;\n        return Math.round(g * (d.width - m) + m / 2);\n      }\n      return 0;\n    }, c = K(() => ({\n      left: r() + \"px\",\n      top: 0\n    })), k = (g) => {\n      g.target !== o.value && p(g);\n    }, p = (g) => {\n      if (g.stopPropagation(), o.value && n.value) {\n        const d = o.value.getBoundingClientRect(), m = n.value.offsetWidth;\n        let b = g.clientX - d.left;\n        b = Math.max(m / 2, b), b = Math.min(b, d.width - m / 2);\n        const h = Math.round((b - m / 2) / (d.width - m) * 100);\n        i.alpha = h, l.alpha = h, t(\"change\", h);\n      }\n    };\n    return oe(() => {\n      const g = {\n        drag: (d) => {\n          p(d);\n        },\n        end: (d) => {\n          p(d);\n        }\n      };\n      o.value && n.value && ae.triggerDragEvent(o.value, g);\n    }), { barElement: o, cursorElement: n, getCursorStyle: c, getBackgroundStyle: a, onClickSider: k };\n  }\n}), st = (e) => (ee(\"data-v-18925ba6\"), e = e(), te(), e), it = /* @__PURE__ */ st(() => /* @__PURE__ */ u(\"div\", { class: \"vc-alpha-slider__bar-handle\" }, null, -1)), ct = [\n  it\n];\nfunction ut(e, t, o, n, i, l) {\n  return C(), $(\"div\", {\n    class: O([\"vc-alpha-slider\", \"transparent\", { \"small-slider\": e.size === \"small\" }])\n  }, [\n    u(\"div\", {\n      ref: \"barElement\",\n      class: \"vc-alpha-slider__bar\",\n      style: D(e.getBackgroundStyle),\n      onClick: t[0] || (t[0] = (...a) => e.onClickSider && e.onClickSider(...a))\n    }, [\n      u(\"div\", {\n        class: O([\"vc-alpha-slider__bar-pointer\", { \"small-bar\": e.size === \"small\" }]),\n        ref: \"cursorElement\",\n        style: D(e.getCursorStyle)\n      }, ct, 6)\n    ], 4)\n  ], 2);\n}\nconst ve = /* @__PURE__ */ q(lt, [[\"render\", ut], [\"__scopeId\", \"data-v-18925ba6\"]]);\nconst dt = [\n  // 第一行\n  [\n    \"#fcc02e\",\n    \"#f67c01\",\n    \"#e64a19\",\n    \"#d81b43\",\n    \"#8e24aa\",\n    \"#512da7\",\n    \"#1f87e8\",\n    \"#008781\",\n    \"#05a045\"\n  ],\n  // 第二行\n  [\n    \"#fed835\",\n    \"#fb8c00\",\n    \"#f5511e\",\n    \"#eb1d4e\",\n    \"#9c28b1\",\n    \"#5d35b0\",\n    \"#2097f3\",\n    \"#029688\",\n    \"#4cb050\"\n  ],\n  // 第三行\n  [\n    \"#ffeb3c\",\n    \"#ffa727\",\n    \"#fe5722\",\n    \"#eb4165\",\n    \"#aa47bc\",\n    \"#673bb7\",\n    \"#42a5f6\",\n    \"#26a59a\",\n    \"#83c683\"\n  ],\n  // 第四行\n  [\n    \"#fff176\",\n    \"#ffb74e\",\n    \"#ff8a66\",\n    \"#f1627e\",\n    \"#b968c7\",\n    \"#7986cc\",\n    \"#64b5f6\",\n    \"#80cbc4\",\n    \"#a5d6a7\"\n  ],\n  // 第五行\n  [\n    \"#fff59c\",\n    \"#ffcc80\",\n    \"#ffab91\",\n    \"#fb879e\",\n    \"#cf93d9\",\n    \"#9ea8db\",\n    \"#90caf8\",\n    \"#b2dfdc\",\n    \"#c8e6ca\"\n  ],\n  // 最后一行\n  [\n    \"transparent\",\n    \"#ffffff\",\n    \"#dedede\",\n    \"#a9a9a9\",\n    \"#4b4b4b\",\n    \"#353535\",\n    \"#212121\",\n    \"#000000\",\n    \"advance\"\n  ]\n], gt = G({\n  name: \"Palette\",\n  emits: [\"change\"],\n  setup(e, { emit: t }) {\n    return { palettes: dt, computedBgStyle: (i) => i === \"transparent\" ? i : i === \"advance\" ? {} : { background: R(i).toRgbString() }, onColorChange: (i) => {\n      t(\"change\", i);\n    } };\n  }\n}), ht = { class: \"vc-compact\" }, pt = [\"onClick\"];\nfunction ft(e, t, o, n, i, l) {\n  return C(), $(\"div\", ht, [\n    (C(!0), $(Q, null, ne(e.palettes, (a, r) => (C(), $(\"div\", {\n      key: r,\n      class: \"vc-compact__row\"\n    }, [\n      (C(!0), $(Q, null, ne(a, (c, k) => (C(), $(\"div\", {\n        key: k,\n        class: \"vc-compact__color-cube--wrap\",\n        onClick: (p) => e.onColorChange(c)\n      }, [\n        u(\"div\", {\n          class: O([\n            \"vc-compact__color_cube\",\n            {\n              advance: c === \"advance\",\n              transparent: c === \"transparent\"\n            }\n          ]),\n          style: D(e.computedBgStyle(c))\n        }, null, 6)\n      ], 8, pt))), 128))\n    ]))), 128))\n  ]);\n}\nconst Ke = /* @__PURE__ */ q(gt, [[\"render\", ft], [\"__scopeId\", \"data-v-b969fd48\"]]);\nconst Ct = G({\n  name: \"Board\",\n  props: {\n    color: v.instanceOf(A),\n    round: v.bool.def(!1),\n    hide: v.bool.def(!0)\n  },\n  emits: [\"change\"],\n  setup(e, { emit: t }) {\n    var y, f, w;\n    const o = Ue(), n = {\n      h: ((y = e.color) == null ? void 0 : y.hue) || 0,\n      s: 1,\n      v: 1\n    }, i = new A(n).toHexString(), l = Y({\n      hueColor: i,\n      saturation: ((f = e.color) == null ? void 0 : f.saturation) || 0,\n      brightness: ((w = e.color) == null ? void 0 : w.brightness) || 0\n    }), a = M(0), r = M(0), c = M(), k = K(() => ({\n      top: a.value + \"px\",\n      left: r.value + \"px\"\n    })), p = () => {\n      if (o) {\n        const S = o.vnode.el;\n        r.value = l.saturation * (S == null ? void 0 : S.clientWidth), a.value = (1 - l.brightness) * (S == null ? void 0 : S.clientHeight);\n      }\n    };\n    let g = !1;\n    const d = (S) => {\n      g = !0, h(S);\n    }, m = (S) => {\n      g && h(S);\n    }, b = () => {\n      g = !1;\n    }, h = (S) => {\n      if (o) {\n        const F = o.vnode.el, E = F == null ? void 0 : F.getBoundingClientRect();\n        let L = S.clientX - E.left, U = S.clientY - E.top;\n        L = ue(L, 0, E.width), U = ue(U, 0, E.height);\n        const J = L / E.width, X = ue(-(U / E.height) + 1, 0, 1);\n        r.value = L, a.value = U, l.saturation = J, l.brightness = X, t(\"change\", J, X);\n      }\n    };\n    return oe(() => {\n      o && o.vnode.el && c.value && je(() => {\n        p();\n      });\n    }), T(\n      () => e.color,\n      (S) => {\n        ie(l, {\n          hueColor: new A({ h: S.hue, s: 1, v: 1 }).toHexString(),\n          saturation: S.saturation,\n          brightness: S.brightness\n        }), p();\n      },\n      { deep: !0 }\n    ), { state: l, cursorElement: c, getCursorStyle: k, onClickBoard: d, onDrag: m, onDragEnd: b };\n  }\n}), be = (e) => (ee(\"data-v-7f0cdcdf\"), e = e(), te(), e), vt = /* @__PURE__ */ be(() => /* @__PURE__ */ u(\"div\", { class: \"vc-saturation__white\" }, null, -1)), bt = /* @__PURE__ */ be(() => /* @__PURE__ */ u(\"div\", { class: \"vc-saturation__black\" }, null, -1)), yt = /* @__PURE__ */ be(() => /* @__PURE__ */ u(\"div\", null, null, -1)), _t = [\n  yt\n];\nfunction mt(e, t, o, n, i, l) {\n  return C(), $(\"div\", {\n    ref: \"boardElement\",\n    class: O([\"vc-saturation\", { \"vc-saturation__chrome\": e.round, \"vc-saturation__hidden\": e.hide }]),\n    style: D({ backgroundColor: e.state.hueColor }),\n    onMousedown: t[0] || (t[0] = (...a) => e.onClickBoard && e.onClickBoard(...a)),\n    onMousemove: t[1] || (t[1] = (...a) => e.onDrag && e.onDrag(...a)),\n    onMouseup: t[2] || (t[2] = (...a) => e.onDragEnd && e.onDragEnd(...a))\n  }, [\n    vt,\n    bt,\n    u(\"div\", {\n      class: \"vc-saturation__cursor\",\n      ref: \"cursorElement\",\n      style: D(e.getCursorStyle)\n    }, _t, 4)\n  ], 38);\n}\nconst ye = /* @__PURE__ */ q(Ct, [[\"render\", mt], [\"__scopeId\", \"data-v-7f0cdcdf\"]]);\nconst St = G({\n  name: \"Hue\",\n  props: {\n    color: v.instanceOf(A),\n    size: v.oneOf([\"small\", \"default\"]).def(\"default\")\n  },\n  emits: [\"change\"],\n  setup(e, { emit: t }) {\n    const o = M(null), n = M(null);\n    let i = e.color || new A();\n    const l = Y({\n      hue: i.hue || 0\n    });\n    x(\n      () => e.color,\n      (p) => {\n        p && (i = p, ie(l, { hue: i.hue }));\n      },\n      { deep: !0 }\n    );\n    const a = () => {\n      if (o.value && n.value) {\n        const p = o.value.getBoundingClientRect(), g = n.value.offsetWidth;\n        return l.hue === 360 ? p.width - g / 2 : l.hue % 360 * (p.width - g) / 360 + g / 2;\n      }\n      return 0;\n    }, r = K(() => ({\n      left: a() + \"px\",\n      top: 0\n    })), c = (p) => {\n      p.target !== o.value && k(p);\n    }, k = (p) => {\n      if (p.stopPropagation(), o.value && n.value) {\n        const g = o.value.getBoundingClientRect(), d = n.value.offsetWidth;\n        let m = p.clientX - g.left;\n        m = Math.min(m, g.width - d / 2), m = Math.max(d / 2, m);\n        const b = Math.round((m - d / 2) / (g.width - d) * 360);\n        i.hue = b, l.hue = b, t(\"change\", b);\n      }\n    };\n    return oe(() => {\n      const p = {\n        drag: (g) => {\n          k(g);\n        },\n        end: (g) => {\n          k(g);\n        }\n      };\n      o.value && n.value && ae.triggerDragEvent(o.value, p);\n    }), { barElement: o, cursorElement: n, getCursorStyle: r, onClickSider: c };\n  }\n}), kt = (e) => (ee(\"data-v-e1a08576\"), e = e(), te(), e), $t = /* @__PURE__ */ kt(() => /* @__PURE__ */ u(\"div\", { class: \"vc-hue-slider__bar-handle\" }, null, -1)), wt = [\n  $t\n];\nfunction Bt(e, t, o, n, i, l) {\n  return C(), $(\"div\", {\n    class: O([\"vc-hue-slider\", { \"small-slider\": e.size === \"small\" }])\n  }, [\n    u(\"div\", {\n      ref: \"barElement\",\n      class: \"vc-hue-slider__bar\",\n      onClick: t[0] || (t[0] = (...a) => e.onClickSider && e.onClickSider(...a))\n    }, [\n      u(\"div\", {\n        class: O([\"vc-hue-slider__bar-pointer\", { \"small-bar\": e.size === \"small\" }]),\n        ref: \"cursorElement\",\n        style: D(e.getCursorStyle)\n      }, wt, 6)\n    ], 512)\n  ], 2);\n}\nconst _e = /* @__PURE__ */ q(St, [[\"render\", Bt], [\"__scopeId\", \"data-v-e1a08576\"]]);\nconst Ht = G({\n  name: \"Lightness\",\n  props: {\n    color: v.instanceOf(A),\n    size: v.oneOf([\"small\", \"default\"]).def(\"default\")\n  },\n  emits: [\"change\"],\n  setup(e, { emit: t }) {\n    const o = M(null), n = M(null);\n    let i = e.color || new A();\n    const [l, a, r] = i.HSL, c = Y({\n      hue: l,\n      saturation: a,\n      lightness: r\n    });\n    x(\n      () => e.color,\n      (b) => {\n        if (b) {\n          i = b;\n          const [h, y, f] = i.HSL;\n          ie(c, {\n            hue: h,\n            saturation: y,\n            lightness: f\n          });\n        }\n      },\n      { deep: !0 }\n    );\n    const k = K(() => {\n      const b = R({\n        h: c.hue,\n        s: c.saturation,\n        l: 0.8\n      }).toPercentageRgbString(), h = R({\n        h: c.hue,\n        s: c.saturation,\n        l: 0.6\n      }).toPercentageRgbString(), y = R({\n        h: c.hue,\n        s: c.saturation,\n        l: 0.4\n      }).toPercentageRgbString(), f = R({\n        h: c.hue,\n        s: c.saturation,\n        l: 0.2\n      }).toPercentageRgbString();\n      return {\n        background: [\n          `linear-gradient(to right, rgb(255, 255, 255), ${b}, ${h}, ${y}, ${f}, rgb(0, 0, 0))`,\n          `-webkit-linear-gradient(left, rgb(255, 255, 255), ${b}, ${h}, ${y}, ${f}, rgb(0, 0, 0))`,\n          `-moz-linear-gradient(left, rgb(255, 255, 255), ${b}, ${h}, ${y}, ${f}, rgb(0, 0, 0))`,\n          `-ms-linear-gradient(left, rgb(255, 255, 255), ${b}, ${h}, ${y}, ${f}, rgb(0, 0, 0))`\n        ]\n      };\n    }), p = () => {\n      if (o.value && n.value) {\n        const b = c.lightness, h = o.value.getBoundingClientRect(), y = n.value.offsetWidth;\n        return (1 - b) * (h.width - y) + y / 2;\n      }\n      return 0;\n    }, g = K(() => ({\n      left: p() + \"px\",\n      top: 0\n    })), d = (b) => {\n      b.target !== o.value && m(b);\n    }, m = (b) => {\n      if (b.stopPropagation(), o.value && n.value) {\n        const h = o.value.getBoundingClientRect(), y = n.value.offsetWidth;\n        let f = b.clientX - h.left;\n        f = Math.max(y / 2, f), f = Math.min(f, h.width - y / 2);\n        const w = 1 - (f - y / 2) / (h.width - y);\n        i.lightness = w, t(\"change\", w);\n      }\n    };\n    return oe(() => {\n      const b = {\n        drag: (h) => {\n          m(h);\n        },\n        end: (h) => {\n          m(h);\n        }\n      };\n      o.value && n.value && ae.triggerDragEvent(o.value, b);\n    }), { barElement: o, cursorElement: n, getCursorStyle: g, getBackgroundStyle: k, onClickSider: d };\n  }\n}), Rt = (e) => (ee(\"data-v-94a50a9e\"), e = e(), te(), e), At = /* @__PURE__ */ Rt(() => /* @__PURE__ */ u(\"div\", { class: \"vc-lightness-slider__bar-handle\" }, null, -1)), Pt = [\n  At\n];\nfunction Vt(e, t, o, n, i, l) {\n  return C(), $(\"div\", {\n    class: O([\"vc-lightness-slider\", { \"small-slider\": e.size === \"small\" }])\n  }, [\n    u(\"div\", {\n      ref: \"barElement\",\n      class: \"vc-lightness-slider__bar\",\n      style: D(e.getBackgroundStyle),\n      onClick: t[0] || (t[0] = (...a) => e.onClickSider && e.onClickSider(...a))\n    }, [\n      u(\"div\", {\n        class: O([\"vc-lightness-slider__bar-pointer\", { \"small-bar\": e.size === \"small\" }]),\n        ref: \"cursorElement\",\n        style: D(e.getCursorStyle)\n      }, Pt, 6)\n    ], 4)\n  ], 2);\n}\nconst Le = /* @__PURE__ */ q(Ht, [[\"render\", Vt], [\"__scopeId\", \"data-v-94a50a9e\"]]);\nconst Mt = G({\n  name: \"History\",\n  props: {\n    colors: v.arrayOf(String).def(() => []),\n    round: v.bool.def(!1)\n  },\n  emits: [\"change\"],\n  setup(e, { emit: t }) {\n    return { onColorSelect: (n) => {\n      t(\"change\", n);\n    } };\n  }\n}), Et = {\n  key: 0,\n  class: \"vc-colorPicker__record\"\n}, It = { class: \"color-list\" }, Kt = [\"onClick\"];\nfunction Lt(e, t, o, n, i, l) {\n  return e.colors && e.colors.length > 0 ? (C(), $(\"div\", Et, [\n    u(\"div\", It, [\n      (C(!0), $(Q, null, ne(e.colors, (a, r) => (C(), $(\"div\", {\n        key: r,\n        class: O([\"color-item\", \"transparent\", { \"color-item__round\": e.round }]),\n        onClick: (c) => e.onColorSelect(a)\n      }, [\n        u(\"div\", {\n          class: \"color-item__display\",\n          style: D({ backgroundColor: a })\n        }, null, 4)\n      ], 10, Kt))), 128))\n    ])\n  ])) : B(\"\", !0);\n}\nconst me = /* @__PURE__ */ q(Mt, [[\"render\", Lt], [\"__scopeId\", \"data-v-0f657238\"]]);\nconst Nt = G({\n  name: \"Display\",\n  props: {\n    color: v.instanceOf(A),\n    disableAlpha: v.bool.def(!1)\n  },\n  emits: [\"update:color\", \"change\"],\n  setup(e, { emit: t }) {\n    var m, b, h, y;\n    const { copy: o, copied: n, isSupported: i } = tt(), l = M(\"hex\"), a = Y({\n      color: e.color,\n      hex: (m = e.color) == null ? void 0 : m.hex,\n      alpha: Math.round(((b = e.color) == null ? void 0 : b.alpha) || 100),\n      rgba: (h = e.color) == null ? void 0 : h.RGB,\n      previewBgColor: (y = e.color) == null ? void 0 : y.toRgbString()\n    }), r = K(() => ({\n      background: a.previewBgColor\n    })), c = () => {\n      l.value = l.value === \"rgba\" ? \"hex\" : \"rgba\";\n    }, k = j((f) => {\n      if (!f.target.value)\n        return;\n      let w = parseInt(f.target.value.replace(\"%\", \"\"));\n      w > 100 && (f.target.value = \"100\", w = 100), w < 0 && (f.target.value = \"0\", w = 0), isNaN(w) && (f.target.value = \"100\", w = 100), !isNaN(w) && a.color && (a.color.alpha = w), t(\"change\", a.color);\n    }, 300), p = j((f, w) => {\n      if (a.color) {\n        if (l.value === \"hex\") {\n          const S = f.target.value.replace(\"#\", \"\");\n          R(S).isValid() ? [3, 4].includes(S.length) && (a.color.hex = S) : a.color.hex = \"000000\", t(\"change\", a.color);\n        } else if (l.value === \"rgba\" && w === 3 && f.target.value.toString() === \"0.\" && a.rgba) {\n          a.rgba[w] = f.target.value;\n          const [S, F, E, L] = a.rgba;\n          a.color.hex = R({ r: S, g: F, b: E }).toHex(), a.color.alpha = Math.round(L * 100), t(\"change\", a.color);\n        }\n      }\n    }, 100), g = j((f, w) => {\n      if (f.target.value) {\n        if (l.value === \"hex\") {\n          const S = f.target.value.replace(\"#\", \"\");\n          R(S).isValid() && a.color && [6, 8].includes(S.length) && (a.color.hex = S);\n        } else if (w !== void 0 && a.rgba && a.color) {\n          if (f.target.value < 0 && (f.target.value = 0), w === 3 && ((f.target.value > 1 || isNaN(f.target.value)) && (f.target.value = 1), f.target.value.toString() === \"0.\"))\n            return;\n          w < 3 && f.target.value > 255 && (f.target.value = 255), a.rgba[w] = f.target.value;\n          const [S, F, E, L] = a.rgba;\n          a.color.hex = R({ r: S, g: F, b: E }).toHex(), a.color.alpha = Math.round(L * 100);\n        }\n        t(\"change\", a.color);\n      }\n    }, 300), d = () => {\n      if (i && a.color) {\n        const f = l.value === \"hex\" ? a.color.toString(a.color.alpha === 100 ? \"hex6\" : \"hex8\") : a.color.toRgbString();\n        o(f || \"\");\n      }\n    };\n    return T(\n      () => e.color,\n      (f) => {\n        f && (a.color = f, a.alpha = Math.round(a.color.alpha), a.hex = a.color.hex, a.rgba = a.color.RGB);\n      },\n      { deep: !0 }\n    ), T(\n      () => a.color,\n      () => {\n        a.color && (a.previewBgColor = a.color.toRgbString());\n      },\n      { deep: !0 }\n    ), {\n      state: a,\n      getBgColorStyle: r,\n      inputType: l,\n      copied: n,\n      onInputTypeChange: c,\n      onAlphaBlur: k,\n      onInputChange: g,\n      onBlurChange: p,\n      onCopyColorStr: d\n    };\n  }\n}), Wt = { class: \"vc-display\" }, Dt = { class: \"vc-current-color vc-transparent\" }, Tt = {\n  key: 0,\n  class: \"copy-text\"\n}, Ot = {\n  key: 0,\n  style: { display: \"flex\", flex: \"1\", gap: \"4px\", height: \"100%\" }\n}, zt = { class: \"vc-color-input\" }, Gt = {\n  key: 0,\n  class: \"vc-alpha-input\"\n}, Ft = [\"value\"], Xt = {\n  key: 1,\n  style: { display: \"flex\", flex: \"1\", gap: \"4px\", height: \"100%\" }\n}, qt = [\"value\", \"onInput\", \"onBlur\"];\nfunction Yt(e, t, o, n, i, l) {\n  return C(), $(\"div\", Wt, [\n    u(\"div\", Dt, [\n      u(\"div\", {\n        class: \"color-cube\",\n        style: D(e.getBgColorStyle),\n        onClick: t[0] || (t[0] = (...a) => e.onCopyColorStr && e.onCopyColorStr(...a))\n      }, [\n        e.copied ? (C(), $(\"span\", Tt, \"Copied!\")) : B(\"\", !0)\n      ], 4)\n    ]),\n    e.inputType === \"hex\" ? (C(), $(\"div\", Ot, [\n      u(\"div\", zt, [\n        le(u(\"input\", {\n          \"onUpdate:modelValue\": t[1] || (t[1] = (a) => e.state.hex = a),\n          maxlength: \"8\",\n          onInput: t[2] || (t[2] = (...a) => e.onInputChange && e.onInputChange(...a)),\n          onBlur: t[3] || (t[3] = (...a) => e.onBlurChange && e.onBlurChange(...a))\n        }, null, 544), [\n          [Ze, e.state.hex]\n        ])\n      ]),\n      e.disableAlpha ? B(\"\", !0) : (C(), $(\"div\", Gt, [\n        u(\"input\", {\n          class: \"vc-alpha-input__inner\",\n          value: e.state.alpha,\n          onInput: t[4] || (t[4] = (...a) => e.onAlphaBlur && e.onAlphaBlur(...a))\n        }, null, 40, Ft),\n        Ee(\"% \")\n      ]))\n    ])) : e.state.rgba ? (C(), $(\"div\", Xt, [\n      (C(!0), $(Q, null, ne(e.state.rgba, (a, r) => (C(), $(\"div\", {\n        class: \"vc-color-input\",\n        key: r\n      }, [\n        u(\"input\", {\n          value: a,\n          onInput: (c) => e.onInputChange(c, r),\n          onBlur: (c) => e.onBlurChange(c, r)\n        }, null, 40, qt)\n      ]))), 128))\n    ])) : B(\"\", !0),\n    u(\"div\", {\n      class: \"vc-input-toggle\",\n      onClick: t[5] || (t[5] = (...a) => e.onInputTypeChange && e.onInputTypeChange(...a))\n    }, se(e.inputType), 1)\n  ]);\n}\nconst Se = /* @__PURE__ */ q(Nt, [[\"render\", Yt], [\"__scopeId\", \"data-v-7334ac20\"]]);\nconst Ut = G({\n  name: \"FkColorPicker\",\n  components: { Display: Se, Alpha: ve, Palette: Ke, Board: ye, Hue: _e, Lightness: Le, History: me },\n  props: {\n    color: v.instanceOf(A),\n    disableHistory: v.bool.def(!1),\n    roundHistory: v.bool.def(!1),\n    disableAlpha: v.bool.def(!1)\n  },\n  emits: [\"update:color\", \"change\", \"advanceChange\"],\n  setup(e, { emit: t }) {\n    const o = e.color || new A(), n = Y({\n      color: o,\n      hex: o.toHexString(),\n      rgb: o.toRgbString()\n    }), i = M(!1), l = K(() => ({ background: n.rgb })), a = () => {\n      i.value = !1, t(\"advanceChange\", !1);\n    }, r = pe(fe, [], {}), c = j(() => {\n      if (e.disableHistory)\n        return;\n      const h = n.color.toRgbString();\n      if (r.value = r.value.filter((y) => !R.equals(y, h)), !r.value.includes(h)) {\n        for (; r.value.length > Ce; )\n          r.value.pop();\n        r.value.unshift(h);\n      }\n    }, 500), k = (h) => {\n      h === \"advance\" ? (i.value = !0, t(\"advanceChange\", !0)) : (n.color.hex = h, t(\"advanceChange\", !1));\n    }, p = (h) => {\n      n.color.alpha = h;\n    }, g = (h) => {\n      n.color.hue = h;\n    }, d = (h, y) => {\n      n.color.saturation = h, n.color.brightness = y;\n    }, m = (h) => {\n      n.color.lightness = h;\n    }, b = (h) => {\n      const f = h.target.value.replace(\"#\", \"\");\n      R(f).isValid() && (n.color.hex = f);\n    };\n    return T(\n      () => e.color,\n      (h) => {\n        h && (n.color = h);\n      },\n      { deep: !0 }\n    ), T(\n      () => n.color,\n      () => {\n        n.hex = n.color.hex, n.rgb = n.color.toRgbString(), c(), t(\"update:color\", n.color), t(\"change\", n.color);\n      },\n      { deep: !0 }\n    ), {\n      state: n,\n      advancePanelShow: i,\n      onBack: a,\n      onCompactChange: k,\n      onAlphaChange: p,\n      onHueChange: g,\n      onBoardChange: d,\n      onLightChange: m,\n      onInputChange: b,\n      previewStyle: l,\n      historyColors: r\n    };\n  }\n}), jt = (e) => (ee(\"data-v-48e3c224\"), e = e(), te(), e), Zt = { class: \"vc-fk-colorPicker\" }, Jt = { class: \"vc-fk-colorPicker__inner\" }, Qt = { class: \"vc-fk-colorPicker__header\" }, xt = /* @__PURE__ */ jt(() => /* @__PURE__ */ u(\"div\", { class: \"back\" }, null, -1)), eo = [\n  xt\n];\nfunction to(e, t, o, n, i, l) {\n  const a = V(\"Palette\"), r = V(\"Board\"), c = V(\"Hue\"), k = V(\"Lightness\"), p = V(\"Alpha\"), g = V(\"Display\"), d = V(\"History\");\n  return C(), $(\"div\", Zt, [\n    u(\"div\", Jt, [\n      u(\"div\", Qt, [\n        e.advancePanelShow ? (C(), $(\"span\", {\n          key: 0,\n          style: { cursor: \"pointer\" },\n          onClick: t[0] || (t[0] = (...m) => e.onBack && e.onBack(...m))\n        }, eo)) : B(\"\", !0)\n      ]),\n      e.advancePanelShow ? B(\"\", !0) : (C(), I(a, {\n        key: 0,\n        onChange: e.onCompactChange\n      }, null, 8, [\"onChange\"])),\n      e.advancePanelShow ? (C(), I(r, {\n        key: 1,\n        color: e.state.color,\n        onChange: e.onBoardChange\n      }, null, 8, [\"color\", \"onChange\"])) : B(\"\", !0),\n      e.advancePanelShow ? (C(), I(c, {\n        key: 2,\n        color: e.state.color,\n        onChange: e.onHueChange\n      }, null, 8, [\"color\", \"onChange\"])) : B(\"\", !0),\n      e.advancePanelShow ? B(\"\", !0) : (C(), I(k, {\n        key: 3,\n        color: e.state.color,\n        onChange: e.onLightChange\n      }, null, 8, [\"color\", \"onChange\"])),\n      e.disableAlpha ? B(\"\", !0) : (C(), I(p, {\n        key: 4,\n        color: e.state.color,\n        onChange: e.onAlphaChange\n      }, null, 8, [\"color\", \"onChange\"])),\n      Z(g, {\n        color: e.state.color,\n        \"disable-alpha\": e.disableAlpha\n      }, null, 8, [\"color\", \"disable-alpha\"]),\n      e.disableHistory ? B(\"\", !0) : (C(), I(d, {\n        key: 5,\n        round: e.roundHistory,\n        colors: e.historyColors,\n        onChange: e.onCompactChange\n      }, null, 8, [\"round\", \"colors\", \"onChange\"]))\n    ])\n  ]);\n}\nconst Pe = /* @__PURE__ */ q(Ut, [[\"render\", to], [\"__scopeId\", \"data-v-48e3c224\"]]);\nconst oo = G({\n  name: \"ChromeColorPicker\",\n  components: { Display: Se, Alpha: ve, Board: ye, Hue: _e, History: me },\n  props: {\n    color: v.instanceOf(A),\n    disableHistory: v.bool.def(!1),\n    roundHistory: v.bool.def(!1),\n    disableAlpha: v.bool.def(!1)\n  },\n  emits: [\"update:color\", \"change\"],\n  setup(e, { emit: t }) {\n    const o = e.color || new A(), n = Y({\n      color: o,\n      hex: o.toHexString(),\n      rgb: o.toRgbString()\n    }), i = K(() => ({ background: n.rgb })), l = pe(fe, [], {}), a = j(() => {\n      if (e.disableHistory)\n        return;\n      const d = n.color.toRgbString();\n      if (l.value = l.value.filter((m) => !R.equals(m, d)), !l.value.includes(d)) {\n        for (; l.value.length > Ce; )\n          l.value.pop();\n        l.value.unshift(d);\n      }\n    }, 500), r = (d) => {\n      n.color.alpha = d;\n    }, c = (d) => {\n      n.color.hue = d;\n    }, k = (d) => {\n      d.hex !== void 0 && (n.color.hex = d.hex), d.alpha !== void 0 && (n.color.alpha = d.alpha);\n    }, p = (d, m) => {\n      n.color.saturation = d, n.color.brightness = m;\n    }, g = (d) => {\n      d !== \"advance\" && (n.color.hex = d);\n    };\n    return T(\n      () => e.color,\n      (d) => {\n        d && (n.color = d);\n      },\n      { deep: !0 }\n    ), T(\n      () => n.color,\n      () => {\n        n.hex = n.color.hex, n.rgb = n.color.toRgbString(), a(), t(\"update:color\", n.color), t(\"change\", n.color);\n      },\n      { deep: !0 }\n    ), {\n      state: n,\n      previewStyle: i,\n      historyColors: l,\n      onAlphaChange: r,\n      onHueChange: c,\n      onBoardChange: p,\n      onInputChange: k,\n      onCompactChange: g\n    };\n  }\n}), no = { class: \"vc-chrome-colorPicker\" }, ao = { class: \"vc-chrome-colorPicker-body\" }, ro = { class: \"chrome-controls\" }, lo = { class: \"chrome-sliders\" };\nfunction so(e, t, o, n, i, l) {\n  const a = V(\"Board\"), r = V(\"Hue\"), c = V(\"Alpha\"), k = V(\"Display\"), p = V(\"History\");\n  return C(), $(\"div\", no, [\n    Z(a, {\n      round: !0,\n      hide: !1,\n      color: e.state.color,\n      onChange: e.onBoardChange\n    }, null, 8, [\"color\", \"onChange\"]),\n    u(\"div\", ao, [\n      u(\"div\", ro, [\n        u(\"div\", lo, [\n          Z(r, {\n            size: \"small\",\n            color: e.state.color,\n            onChange: e.onHueChange\n          }, null, 8, [\"color\", \"onChange\"]),\n          e.disableAlpha ? B(\"\", !0) : (C(), I(c, {\n            key: 0,\n            size: \"small\",\n            color: e.state.color,\n            onChange: e.onAlphaChange\n          }, null, 8, [\"color\", \"onChange\"]))\n        ])\n      ]),\n      Z(k, {\n        color: e.state.color,\n        \"disable-alpha\": e.disableAlpha\n      }, null, 8, [\"color\", \"disable-alpha\"]),\n      e.disableHistory ? B(\"\", !0) : (C(), I(p, {\n        key: 0,\n        round: e.roundHistory,\n        colors: e.historyColors,\n        onChange: e.onCompactChange\n      }, null, 8, [\"round\", \"colors\", \"onChange\"]))\n    ])\n  ]);\n}\nconst Ve = /* @__PURE__ */ q(oo, [[\"render\", so], [\"__scopeId\", \"data-v-2611d66c\"]]), ke = \"Vue3ColorPickerProvider\", io = (e, t) => {\n  const o = e.getBoundingClientRect(), n = o.left + o.width / 2, i = o.top + o.height / 2, l = Math.abs(n - t.clientX), a = Math.abs(i - t.clientY), r = Math.sqrt(Math.pow(l, 2) + Math.pow(a, 2)), c = a / r, k = Math.acos(c);\n  let p = Math.floor(180 / (Math.PI / k));\n  return t.clientX > n && t.clientY > i && (p = 180 - p), t.clientX == n && t.clientY > i && (p = 180), t.clientX > n && t.clientY == i && (p = 90), t.clientX < n && t.clientY > i && (p = 180 + p), t.clientX < n && t.clientY == i && (p = 270), t.clientX < n && t.clientY < i && (p = 360 - p), p;\n};\nlet de = !1;\nconst co = (e, t) => {\n  const o = function(i) {\n    var l;\n    (l = t.drag) == null || l.call(t, i);\n  }, n = function(i) {\n    var l;\n    document.removeEventListener(\"mousemove\", o, !1), document.removeEventListener(\"mouseup\", n, !1), document.onselectstart = null, document.ondragstart = null, de = !1, (l = t.end) == null || l.call(t, i);\n  };\n  e && e.addEventListener(\"mousedown\", (i) => {\n    var l;\n    de || (document.onselectstart = () => !1, document.ondragstart = () => !1, document.addEventListener(\"mousemove\", o, !1), document.addEventListener(\"mouseup\", n, !1), de = !0, (l = t.start) == null || l.call(t, i));\n  });\n};\nconst uo = {\n  angle: {\n    type: Number,\n    default: 0\n  },\n  size: {\n    type: Number,\n    default: 16,\n    validator: (e) => e >= 16\n  },\n  borderWidth: {\n    type: Number,\n    default: 1,\n    validator: (e) => e >= 1\n  },\n  borderColor: {\n    type: String,\n    default: \"#666\"\n  }\n}, go = G({\n  name: \"Angle\",\n  props: uo,\n  emits: [\"update:angle\", \"change\"],\n  setup(e, {\n    emit: t\n  }) {\n    const o = M(null), n = M(0);\n    x(() => e.angle, (r) => {\n      n.value = r;\n    });\n    const i = () => {\n      let r = Number(n.value);\n      isNaN(r) || (r = r > 360 || r < 0 ? e.angle : r, n.value = r === 360 ? 0 : r, t(\"update:angle\", n.value), t(\"change\", n.value));\n    }, l = K(() => ({\n      width: e.size + \"px\",\n      height: e.size + \"px\",\n      borderWidth: e.borderWidth + \"px\",\n      borderColor: e.borderColor,\n      transform: `rotate(${n.value}deg)`\n    })), a = (r) => {\n      o.value && (n.value = io(o.value, r) % 360, i());\n    };\n    return Je(() => {\n      const r = {\n        drag: (c) => {\n          a(c);\n        },\n        end: (c) => {\n          a(c);\n        }\n      };\n      o.value && co(o.value, r);\n    }), () => Z(\"div\", {\n      class: \"bee-angle\"\n    }, [Z(\"div\", {\n      class: \"bee-angle__round\",\n      ref: o,\n      style: l.value\n    }, null)]);\n  }\n});\nconst ho = G({\n  name: \"GradientColorPicker\",\n  components: { Angle: go, Display: Se, Alpha: ve, Palette: Ke, Board: ye, Hue: _e, Lightness: Le, History: me },\n  props: {\n    startColor: v.instanceOf(A).isRequired,\n    endColor: v.instanceOf(A).isRequired,\n    startColorStop: v.number.def(0),\n    endColorStop: v.number.def(100),\n    angle: v.number.def(0),\n    type: v.oneOf([\"linear\", \"radial\"]).def(\"linear\"),\n    disableHistory: v.bool.def(!1),\n    roundHistory: v.bool.def(!1),\n    disableAlpha: v.bool.def(!1),\n    pickerType: v.oneOf([\"fk\", \"chrome\"]).def(\"fk\")\n  },\n  emits: [\n    \"update:startColor\",\n    \"update:endColor\",\n    \"update:angle\",\n    \"update:startColorStop\",\n    \"update:endColorStop\",\n    \"startColorChange\",\n    \"endColorChange\",\n    \"advanceChange\",\n    \"angleChange\",\n    \"startColorStopChange\",\n    \"endColorStopChange\",\n    \"typeChange\"\n  ],\n  setup(e, { emit: t }) {\n    const o = Y({\n      startActive: !0,\n      startColor: e.startColor,\n      endColor: e.endColor,\n      startColorStop: e.startColorStop,\n      endColorStop: e.endColorStop,\n      angle: e.angle,\n      type: e.type,\n      // rgba\n      startColorRgba: e.startColor.toRgbString(),\n      endColorRgba: e.endColor.toRgbString()\n    }), n = Ie(ke), i = M(e.pickerType === \"chrome\"), l = M(), a = M(), r = M();\n    x(\n      () => [e.startColor, e.endColor, e.angle],\n      (s) => {\n        o.startColor = s[0], o.endColor = s[1], o.angle = s[2];\n      }\n    ), x(\n      () => e.type,\n      (s) => {\n        o.type = s;\n      }\n    );\n    const c = K({\n      get: () => o.startActive ? o.startColor : o.endColor,\n      set: (s) => {\n        if (o.startActive) {\n          o.startColor = s;\n          return;\n        }\n        o.endColor = s;\n      }\n    }), k = K(() => {\n      if (r.value && l.value) {\n        const s = o.startColorStop / 100, _ = r.value.getBoundingClientRect(), H = l.value.offsetWidth;\n        return Math.round(s * (_.width - H) + H / 2);\n      }\n      return 0;\n    }), p = K(() => {\n      if (r.value && a.value) {\n        const s = o.endColorStop / 100, _ = r.value.getBoundingClientRect(), H = a.value.offsetWidth;\n        return Math.round(s * (_.width - H) + H / 2);\n      }\n      return 0;\n    }), g = K(() => {\n      let s = `background: linear-gradient(${o.angle}deg, ${o.startColorRgba} ${o.startColorStop}%, ${o.endColorRgba} ${o.endColorStop}%)`;\n      return o.type === \"radial\" && (s = `background: radial-gradient(circle, ${o.startColorRgba} ${o.startColorStop}%, ${o.endColorRgba} ${o.endColorStop}%)`), s;\n    }), d = (s) => {\n      var _;\n      if (o.startActive = !0, r.value && l.value) {\n        const H = (_ = r.value) == null ? void 0 : _.getBoundingClientRect();\n        let N = s.clientX - H.left;\n        N = Math.max(l.value.offsetWidth / 2, N), N = Math.min(N, H.width - l.value.offsetWidth / 2), o.startColorStop = Math.round(\n          (N - l.value.offsetWidth / 2) / (H.width - l.value.offsetWidth) * 100\n        ), t(\"update:startColorStop\", o.startColorStop), t(\"startColorStopChange\", o.startColorStop);\n      }\n    }, m = (s) => {\n      var _;\n      if (o.startActive = !1, r.value && a.value) {\n        const H = (_ = r.value) == null ? void 0 : _.getBoundingClientRect();\n        let N = s.clientX - H.left;\n        N = Math.max(a.value.offsetWidth / 2, N), N = Math.min(N, H.width - a.value.offsetWidth / 2), o.endColorStop = Math.round(\n          (N - a.value.offsetWidth / 2) / (H.width - a.value.offsetWidth) * 100\n        ), t(\"update:endColorStop\", o.endColorStop), t(\"endColorStopChange\", o.endColorStop);\n      }\n    }, b = (s) => {\n      const _ = s.target, H = parseInt(_.value.replace(\"°\", \"\"));\n      isNaN(H) || (o.angle = H % 360), t(\"update:angle\", o.angle), t(\"angleChange\", o.angle);\n    }, h = (s) => {\n      o.angle = s, t(\"update:angle\", o.angle), t(\"angleChange\", o.angle);\n    }, y = (s) => {\n      s === \"advance\" ? (i.value = !0, t(\"advanceChange\", !0)) : (c.value.hex = s, t(\"advanceChange\", !1)), L();\n    }, f = (s) => {\n      c.value.alpha = s, L();\n    }, w = (s) => {\n      c.value.hue = s, L();\n    }, S = (s, _) => {\n      c.value.saturation = s, c.value.brightness = _, L();\n    }, F = (s) => {\n      c.value.lightness = s, L();\n    }, E = () => {\n      L();\n    }, L = () => {\n      o.startActive ? (t(\"update:startColor\", o.startColor), t(\"startColorChange\", o.startColor)) : (t(\"update:endColor\", o.endColor), t(\"endColorChange\", o.endColor));\n    }, U = () => {\n      i.value = !1, t(\"advanceChange\", !1);\n    }, J = () => {\n      o.type = o.type === \"linear\" ? \"radial\" : \"linear\", t(\"typeChange\", o.type);\n    }, X = pe(fe, [], {}), ce = j(() => {\n      if (e.disableHistory)\n        return;\n      const s = c.value.toRgbString();\n      if (X.value = X.value.filter((_) => !R.equals(_, s)), !X.value.includes(s)) {\n        for (; X.value.length > Ce; )\n          X.value.pop();\n        X.value.unshift(s);\n      }\n    }, 500);\n    return oe(() => {\n      a.value && l.value && (ae.triggerDragEvent(a.value, {\n        drag: (s) => {\n          m(s);\n        },\n        end: (s) => {\n          m(s);\n        }\n      }), ae.triggerDragEvent(l.value, {\n        drag: (s) => {\n          d(s);\n        },\n        end: (s) => {\n          d(s);\n        }\n      }));\n    }), T(\n      () => o.startColor,\n      (s) => {\n        o.startColorRgba = s.toRgbString();\n      },\n      { deep: !0 }\n    ), T(\n      () => o.endColor,\n      (s) => {\n        o.endColorRgba = s.toRgbString();\n      },\n      { deep: !0 }\n    ), T(\n      () => c.value,\n      () => {\n        ce();\n      },\n      { deep: !0 }\n    ), {\n      startGradientRef: l,\n      stopGradientRef: a,\n      colorRangeRef: r,\n      state: o,\n      currentColor: c,\n      getStartColorLeft: k,\n      getEndColorLeft: p,\n      gradientBg: g,\n      advancePanelShow: i,\n      onDegreeBlur: b,\n      onCompactChange: y,\n      onAlphaChange: f,\n      onHueChange: w,\n      onBoardChange: S,\n      onLightChange: F,\n      historyColors: X,\n      onBack: U,\n      onDegreeChange: h,\n      onDisplayChange: E,\n      onTypeChange: J,\n      lang: n == null ? void 0 : n.lang\n    };\n  }\n}), Ne = (e) => (ee(\"data-v-c4d6d6ea\"), e = e(), te(), e), po = { class: \"vc-gradient-picker\" }, fo = { class: \"vc-gradient-picker__header\" }, Co = { class: \"vc-gradient__types\" }, vo = { class: \"vc-gradient-wrap__types\" }, bo = { class: \"vc-picker-degree-input vc-degree-input\" }, yo = { class: \"vc-degree-input__control\" }, _o = [\"value\"], mo = { class: \"vc-degree-input__panel\" }, So = { class: \"vc-degree-input__disk\" }, ko = { class: \"vc-gradient-picker__body\" }, $o = {\n  class: \"vc-color-range\",\n  ref: \"colorRangeRef\"\n}, wo = { class: \"vc-color-range__container\" }, Bo = { class: \"vc-gradient__stop__container\" }, Ho = [\"title\"], Ro = /* @__PURE__ */ Ne(() => /* @__PURE__ */ u(\"span\", { class: \"vc-gradient__stop--inner\" }, null, -1)), Ao = [\n  Ro\n], Po = [\"title\"], Vo = /* @__PURE__ */ Ne(() => /* @__PURE__ */ u(\"span\", { class: \"vc-gradient__stop--inner\" }, null, -1)), Mo = [\n  Vo\n];\nfunction Eo(e, t, o, n, i, l) {\n  var b, h;\n  const a = V(\"Angle\"), r = V(\"Board\"), c = V(\"Hue\"), k = V(\"Palette\"), p = V(\"Lightness\"), g = V(\"Alpha\"), d = V(\"Display\"), m = V(\"History\");\n  return C(), $(\"div\", po, [\n    u(\"div\", fo, [\n      u(\"div\", null, [\n        le(u(\"div\", {\n          class: \"back\",\n          style: { cursor: \"pointer\" },\n          onClick: t[0] || (t[0] = (...y) => e.onBack && e.onBack(...y))\n        }, null, 512), [\n          [ge, e.pickerType === \"fk\" && e.advancePanelShow]\n        ])\n      ]),\n      u(\"div\", Co, [\n        u(\"div\", vo, [\n          (C(), $(Q, null, ne([\"linear\", \"radial\"], (y) => u(\"div\", {\n            class: O([\"vc-gradient__type\", { active: e.state.type === y }]),\n            key: y,\n            onClick: t[1] || (t[1] = (...f) => e.onTypeChange && e.onTypeChange(...f))\n          }, se(e.lang ? e.lang[y] : y), 3)), 64))\n        ]),\n        le(u(\"div\", bo, [\n          u(\"div\", yo, [\n            u(\"input\", {\n              value: e.state.angle,\n              onBlur: t[2] || (t[2] = (...y) => e.onDegreeBlur && e.onDegreeBlur(...y))\n            }, null, 40, _o),\n            Ee(\"deg \")\n          ]),\n          u(\"div\", mo, [\n            u(\"div\", So, [\n              Z(a, {\n                angle: e.state.angle,\n                \"onUpdate:angle\": t[3] || (t[3] = (y) => e.state.angle = y),\n                size: 40,\n                onChange: e.onDegreeChange\n              }, null, 8, [\"angle\", \"onChange\"])\n            ])\n          ])\n        ], 512), [\n          [ge, e.state.type === \"linear\"]\n        ])\n      ])\n    ]),\n    u(\"div\", ko, [\n      u(\"div\", $o, [\n        u(\"div\", wo, [\n          u(\"div\", {\n            class: \"vc-background\",\n            style: D(e.gradientBg)\n          }, null, 4),\n          u(\"div\", Bo, [\n            u(\"div\", {\n              class: O([\"vc-gradient__stop\", {\n                \"vc-gradient__stop--current\": e.state.startActive\n              }]),\n              ref: \"startGradientRef\",\n              title: (b = e.lang) == null ? void 0 : b.start,\n              style: D({ left: e.getStartColorLeft + \"px\", backgroundColor: e.state.startColorRgba })\n            }, Ao, 14, Ho),\n            u(\"div\", {\n              class: O([\"vc-gradient__stop\", {\n                \"vc-gradient__stop--current\": !e.state.startActive\n              }]),\n              ref: \"stopGradientRef\",\n              title: (h = e.lang) == null ? void 0 : h.end,\n              style: D({ left: e.getEndColorLeft + \"px\", backgroundColor: e.state.endColorRgba })\n            }, Mo, 14, Po)\n          ])\n        ])\n      ], 512)\n    ]),\n    e.advancePanelShow ? (C(), I(r, {\n      key: 0,\n      color: e.currentColor,\n      onChange: e.onBoardChange\n    }, null, 8, [\"color\", \"onChange\"])) : B(\"\", !0),\n    e.advancePanelShow ? (C(), I(c, {\n      key: 1,\n      color: e.currentColor,\n      onChange: e.onHueChange\n    }, null, 8, [\"color\", \"onChange\"])) : B(\"\", !0),\n    e.advancePanelShow ? B(\"\", !0) : (C(), I(k, {\n      key: 2,\n      onChange: e.onCompactChange\n    }, null, 8, [\"onChange\"])),\n    e.advancePanelShow ? B(\"\", !0) : (C(), I(p, {\n      key: 3,\n      color: e.currentColor,\n      onChange: e.onLightChange\n    }, null, 8, [\"color\", \"onChange\"])),\n    e.disableAlpha ? B(\"\", !0) : (C(), I(g, {\n      key: 4,\n      color: e.currentColor,\n      onChange: e.onAlphaChange\n    }, null, 8, [\"color\", \"onChange\"])),\n    Z(d, {\n      color: e.currentColor,\n      \"disable-alpha\": e.disableAlpha,\n      onChange: e.onDisplayChange\n    }, null, 8, [\"color\", \"disable-alpha\", \"onChange\"]),\n    e.disableHistory ? B(\"\", !0) : (C(), I(m, {\n      key: 5,\n      round: e.roundHistory,\n      colors: e.historyColors,\n      onChange: e.onCompactChange\n    }, null, 8, [\"round\", \"colors\", \"onChange\"]))\n  ]);\n}\nconst Me = /* @__PURE__ */ q(ho, [[\"render\", Eo], [\"__scopeId\", \"data-v-c4d6d6ea\"]]);\nconst Io = G({\n  name: \"WrapContainer\",\n  props: {\n    theme: v.oneOf([\"white\", \"black\"]).def(\"white\"),\n    showTab: v.bool.def(!1),\n    activeKey: v.oneOf([\"pure\", \"gradient\"]).def(\"pure\")\n  },\n  emits: [\"update:activeKey\", \"change\"],\n  setup(e, { emit: t }) {\n    const o = Y({\n      activeKey: e.activeKey\n    }), n = Ie(ke), i = (l) => {\n      o.activeKey = l, t(\"update:activeKey\", l), t(\"change\", l);\n    };\n    return T(\n      () => e.activeKey,\n      (l) => {\n        o.activeKey = l;\n      }\n    ), { state: o, onActiveKeyChange: i, lang: n == null ? void 0 : n.lang };\n  }\n}), Ko = { class: \"vc-colorpicker--container\" }, Lo = {\n  key: 0,\n  class: \"vc-colorpicker--tabs\"\n}, No = { class: \"vc-colorpicker--tabs__inner\" }, Wo = { class: \"vc-btn__content\" }, Do = { class: \"vc-btn__content\" };\nfunction To(e, t, o, n, i, l) {\n  var a, r;\n  return C(), $(\"div\", {\n    class: O([\"vc-colorpicker\", e.theme])\n  }, [\n    u(\"div\", Ko, [\n      e.showTab ? (C(), $(\"div\", Lo, [\n        u(\"div\", No, [\n          u(\"div\", {\n            class: O([\n              \"vc-colorpicker--tabs__btn\",\n              {\n                \"vc-btn-active\": e.state.activeKey === \"pure\"\n              }\n            ]),\n            onClick: t[0] || (t[0] = (c) => e.onActiveKeyChange(\"pure\"))\n          }, [\n            u(\"button\", null, [\n              u(\"div\", Wo, se((a = e.lang) == null ? void 0 : a.pure), 1)\n            ])\n          ], 2),\n          u(\"div\", {\n            class: O([\n              \"vc-colorpicker--tabs__btn\",\n              {\n                \"vc-btn-active\": e.state.activeKey === \"gradient\"\n              }\n            ]),\n            onClick: t[1] || (t[1] = (c) => e.onActiveKeyChange(\"gradient\"))\n          }, [\n            u(\"button\", null, [\n              u(\"div\", Do, se((r = e.lang) == null ? void 0 : r.gradient), 1)\n            ])\n          ], 2),\n          u(\"div\", {\n            class: \"vc-colorpicker--tabs__bg\",\n            style: D({\n              width: \"50%\",\n              left: `calc(${e.state.activeKey === \"gradient\" ? 50 : 0}%)`\n            })\n          }, null, 4)\n        ])\n      ])) : B(\"\", !0),\n      he(e.$slots, \"default\", {}, void 0, !0)\n    ])\n  ], 2);\n}\nconst Oo = /* @__PURE__ */ q(Io, [[\"render\", To], [\"__scopeId\", \"data-v-0492277d\"]]), zo = {\n  start: \"Start\",\n  end: \"End\",\n  pure: \"Pure\",\n  gradient: \"Gradient\",\n  linear: \"linear\",\n  radial: \"radial\"\n}, Go = {\n  start: \"开始\",\n  end: \"结束\",\n  pure: \"纯色\",\n  gradient: \"渐变\",\n  linear: \"线性\",\n  radial: \"径向\"\n}, Fo = {\n  En: zo,\n  \"ZH-cn\": Go\n};\nconst Xo = {\n  isWidget: v.bool.def(!1),\n  pickerType: v.oneOf([\"fk\", \"chrome\"]).def(\"fk\"),\n  shape: v.oneOf([\"circle\", \"square\"]).def(\"square\"),\n  pureColor: {\n    type: [String, Object],\n    default: \"#000000\"\n  },\n  gradientColor: v.string.def(\n    \"linear-gradient(90deg, rgba(255, 255, 255, 1) 0%, rgba(0, 0, 0, 1) 100%)\"\n  ),\n  format: {\n    type: String,\n    default: \"rgb\"\n  },\n  disableAlpha: v.bool.def(!1),\n  disableHistory: v.bool.def(!1),\n  roundHistory: v.bool.def(!1),\n  useType: v.oneOf([\"pure\", \"gradient\", \"both\"]).def(\"pure\"),\n  activeKey: v.oneOf([\"pure\", \"gradient\"]).def(\"pure\"),\n  lang: {\n    type: String,\n    default: \"ZH-cn\"\n  },\n  zIndex: v.number.def(9999),\n  pickerContainer: {\n    type: [String, HTMLElement],\n    default: \"body\"\n  },\n  debounce: v.number.def(100),\n  theme: v.oneOf([\"white\", \"black\"]).def(\"white\"),\n  blurClose: v.bool.def(!1),\n  defaultPopup: v.bool.def(!1)\n}, qo = G({\n  name: \"ColorPicker\",\n  components: { FkColorPicker: Pe, ChromeColorPicker: Ve, GradientColorPicker: Me, WrapContainer: Oo },\n  inheritAttrs: !1,\n  props: Xo,\n  emits: [\n    \"update:pureColor\",\n    \"pureColorChange\",\n    \"update:gradientColor\",\n    \"gradientColorChange\",\n    \"update:activeKey\",\n    \"activeKeyChange\"\n  ],\n  setup(e, { emit: t }) {\n    Qe(ke, {\n      lang: K(() => Fo[e.lang || \"ZH-cn\"])\n    });\n    const o = !!xe().extra, n = Y({\n      pureColor: e.pureColor || \"\",\n      activeKey: e.useType === \"gradient\" ? \"gradient\" : e.activeKey,\n      //  \"pure\" | \"gradient\"\n      isAdvanceMode: !1\n    }), i = new A(\"#000\"), l = new A(\"#000\"), a = new A(n.pureColor), r = Y({\n      startColor: i,\n      endColor: l,\n      startColorStop: 0,\n      endColorStop: 100,\n      angle: 0,\n      type: \"linear\",\n      gradientColor: e.gradientColor\n    }), c = M(a), k = M(e.defaultPopup), p = M(null), g = M(null);\n    let d = null;\n    const m = K(() => ({\n      background: n.activeKey !== \"gradient\" ? R(n.pureColor).toRgbString() : r.gradientColor\n    })), b = K(() => n.activeKey === \"gradient\" ? Me.name : e.pickerType === \"fk\" ? Pe.name : Ve.name), h = (s) => {\n      n.isAdvanceMode = s;\n    }, y = K(() => {\n      const s = {\n        disableAlpha: e.disableAlpha,\n        disableHistory: e.disableHistory,\n        roundHistory: e.roundHistory,\n        pickerType: e.pickerType\n      };\n      return n.activeKey === \"gradient\" ? {\n        ...s,\n        startColor: r.startColor,\n        endColor: r.endColor,\n        angle: r.angle,\n        type: r.type,\n        startColorStop: r.startColorStop,\n        endColorStop: r.endColorStop,\n        onStartColorChange: (_) => {\n          r.startColor = _, E();\n        },\n        onEndColorChange: (_) => {\n          r.endColor = _, E();\n        },\n        onStartColorStopChange: (_) => {\n          r.startColorStop = _, E();\n        },\n        onEndColorStopChange: (_) => {\n          r.endColorStop = _, E();\n        },\n        onAngleChange: (_) => {\n          r.angle = _, E();\n        },\n        onTypeChange: (_) => {\n          r.type = _, E();\n        },\n        onAdvanceChange: h\n      } : {\n        ...s,\n        disableAlpha: e.disableAlpha,\n        disableHistory: e.disableHistory,\n        roundHistory: e.roundHistory,\n        color: c.value,\n        onChange: J,\n        onAdvanceChange: h\n      };\n    }), f = () => {\n      k.value = !0, d ? d.update() : U();\n    }, w = () => {\n      k.value = !1;\n    }, S = j(() => {\n      !e.isWidget && e.blurClose && w();\n    }, 100);\n    ot(g, () => {\n      w();\n    });\n    const F = () => {\n      var s, _, H, N;\n      try {\n        const [z] = at(r.gradientColor);\n        if (z && z.type.includes(\"gradient\") && z.colorStops.length >= 2) {\n          const $e = z.colorStops[0], we = z.colorStops[1];\n          r.startColorStop = Number((s = $e.length) == null ? void 0 : s.value) || 0, r.endColorStop = Number((_ = we.length) == null ? void 0 : _.value) || 0, z.type === \"linear-gradient\" && ((H = z.orientation) == null ? void 0 : H.type) === \"angular\" && (r.angle = Number((N = z.orientation) == null ? void 0 : N.value) || 0), r.type = z.type.split(\"-\")[0];\n          const [We, De, Te, Oe] = $e.value, [ze, Ge, Fe, Xe] = we.value;\n          r.startColor = new A({\n            r: Number(We),\n            g: Number(De),\n            b: Number(Te),\n            a: Number(Oe)\n          }), r.endColor = new A({\n            r: Number(ze),\n            g: Number(Ge),\n            b: Number(Fe),\n            a: Number(Xe)\n          });\n        }\n      } catch (z) {\n        console.log(`[Parse Color]: ${z}`);\n      }\n    }, E = j(() => {\n      const s = L();\n      try {\n        r.gradientColor = nt(s), t(\"update:gradientColor\", r.gradientColor), t(\"gradientColorChange\", r.gradientColor);\n      } catch (_) {\n        console.log(_);\n      }\n    }, e.debounce), L = () => {\n      const s = [], _ = r.startColor.RGB.map((z) => z.toString()), H = r.endColor.RGB.map((z) => z.toString()), N = [\n        {\n          type: \"rgba\",\n          value: [_[0], _[1], _[2], _[3]],\n          length: { value: r.startColorStop + \"\", type: \"%\" }\n        },\n        {\n          type: \"rgba\",\n          value: [H[0], H[1], H[2], H[3]],\n          length: { value: r.endColorStop + \"\", type: \"%\" }\n        }\n      ];\n      return r.type === \"linear\" ? s.push({\n        type: \"linear-gradient\",\n        orientation: { type: \"angular\", value: r.angle + \"\" },\n        colorStops: N\n      }) : r.type === \"radial\" && s.push({\n        type: \"radial-gradient\",\n        orientation: [{ type: \"shape\", value: \"circle\" }],\n        colorStops: N\n      }), s;\n    }, U = () => {\n      p.value && g.value && (d = rt(p.value, g.value, {\n        placement: \"auto\",\n        modifiers: [\n          {\n            name: \"offset\",\n            options: {\n              offset: [0, 8]\n            }\n          },\n          {\n            name: \"flip\",\n            options: {\n              allowedAutoPlacements: [\"top\", \"bottom\", \"left\", \"right\"],\n              rootBoundary: \"viewport\"\n            }\n          }\n        ]\n      }));\n    }, J = (s) => {\n      c.value = s, n.pureColor = s.toString(e.format), X();\n    }, X = j(() => {\n      t(\"update:pureColor\", n.pureColor), t(\"pureColorChange\", n.pureColor);\n    }, e.debounce), ce = (s) => {\n      n.activeKey = s, t(\"update:activeKey\", s), t(\"activeKeyChange\", s);\n    };\n    return oe(() => {\n      F(), d || U();\n    }), T(\n      () => e.gradientColor,\n      (s) => {\n        s != r.gradientColor && (r.gradientColor = s);\n      }\n    ), T(\n      () => r.gradientColor,\n      () => {\n        F();\n      }\n    ), T(\n      () => e.activeKey,\n      (s) => {\n        n.activeKey = s;\n      }\n    ), T(\n      () => e.useType,\n      (s) => {\n        n.activeKey !== \"gradient\" && s === \"gradient\" ? n.activeKey = \"gradient\" : n.activeKey = \"pure\";\n      }\n    ), T(\n      () => e.pureColor,\n      (s) => {\n        R.equals(s, n.pureColor) || (n.pureColor = s, c.value = new A(s));\n      },\n      { deep: !0 }\n    ), {\n      colorCubeRef: p,\n      pickerRef: g,\n      showPicker: k,\n      colorInstance: c,\n      getBgColorStyle: m,\n      getComponentName: b,\n      getBindArgs: y,\n      state: n,\n      hasExtra: o,\n      onColorChange: J,\n      onShowPicker: f,\n      onActiveKeyChange: ce,\n      onAutoClose: S\n    };\n  }\n}), Yo = {\n  key: 0,\n  class: \"vc-color-extra\"\n}, Uo = {\n  key: 0,\n  class: \"vc-color-extra\"\n};\nfunction jo(e, t, o, n, i, l) {\n  const a = V(\"WrapContainer\");\n  return C(), $(Q, null, [\n    e.isWidget ? (C(), I(a, {\n      key: 0,\n      \"active-key\": e.state.activeKey,\n      \"onUpdate:activeKey\": t[0] || (t[0] = (r) => e.state.activeKey = r),\n      \"show-tab\": e.useType === \"both\",\n      style: D({ zIndex: e.zIndex }),\n      theme: e.theme,\n      onChange: e.onActiveKeyChange\n    }, {\n      default: Be(() => [\n        (C(), I(He(e.getComponentName), Re({ key: e.getComponentName }, e.getBindArgs), null, 16)),\n        e.hasExtra ? (C(), $(\"div\", Yo, [\n          he(e.$slots, \"extra\", {}, void 0, !0)\n        ])) : B(\"\", !0)\n      ]),\n      _: 3\n    }, 8, [\"active-key\", \"show-tab\", \"style\", \"theme\", \"onChange\"])) : B(\"\", !0),\n    e.isWidget ? B(\"\", !0) : (C(), $(Q, { key: 1 }, [\n      u(\"div\", {\n        class: O([\"vc-color-wrap transparent\", { round: e.shape === \"circle\" }]),\n        ref: \"colorCubeRef\"\n      }, [\n        u(\"div\", {\n          class: \"current-color\",\n          style: D(e.getBgColorStyle),\n          onClick: t[1] || (t[1] = (...r) => e.onShowPicker && e.onShowPicker(...r))\n        }, null, 4)\n      ], 2),\n      (C(), I(et, { to: e.pickerContainer }, [\n        le(u(\"div\", {\n          ref: \"pickerRef\",\n          style: D({ zIndex: e.zIndex }),\n          onMouseleave: t[3] || (t[3] = (...r) => e.onAutoClose && e.onAutoClose(...r))\n        }, [\n          e.showPicker ? (C(), I(a, {\n            key: 0,\n            \"show-tab\": e.useType === \"both\" && !e.state.isAdvanceMode,\n            theme: e.theme,\n            \"active-key\": e.state.activeKey,\n            \"onUpdate:activeKey\": t[2] || (t[2] = (r) => e.state.activeKey = r),\n            onChange: e.onActiveKeyChange\n          }, {\n            default: Be(() => [\n              (C(), I(He(e.getComponentName), Re({ key: e.getComponentName }, e.getBindArgs), null, 16)),\n              e.hasExtra ? (C(), $(\"div\", Uo, [\n                he(e.$slots, \"extra\", {}, void 0, !0)\n              ])) : B(\"\", !0)\n            ]),\n            _: 3\n          }, 8, [\"show-tab\", \"theme\", \"active-key\", \"onChange\"])) : B(\"\", !0)\n        ], 36), [\n          [ge, e.showPicker]\n        ])\n      ], 8, [\"to\"]))\n    ], 64))\n  ], 64);\n}\nconst re = /* @__PURE__ */ q(qo, [[\"render\", jo], [\"__scopeId\", \"data-v-354ca836\"]]), rn = {\n  install: (e) => {\n    e.component(re.name, re), e.component(\"Vue3\" + re.name, re);\n  }\n};\nexport {\n  re as ColorPicker,\n  rn as default\n};\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}", "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref, win) {\n  var x = _ref.x,\n      y = _ref.y;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }, getWindow(popper)) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "var t=\"undefined\"!=typeof globalThis?globalThis:\"undefined\"!=typeof window?window:\"undefined\"!=typeof global?global:\"undefined\"!=typeof self?self:{};function e(t){var e={exports:{}};return t(e,e.exports),e.exports}var n=function(t){return t&&t.Math==Math&&t},r=n(\"object\"==typeof globalThis&&globalThis)||n(\"object\"==typeof window&&window)||n(\"object\"==typeof self&&self)||n(\"object\"==typeof t&&t)||function(){return this}()||Function(\"return this\")(),o=function(t){try{return!!t()}catch(t){return!0}},i=!o((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),u={}.propertyIsEnumerable,a=Object.getOwnPropertyDescriptor,c={f:a&&!u.call({1:2},1)?function(t){var e=a(this,t);return!!e&&e.enumerable}:u},l=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},f={}.toString,s=function(t){return f.call(t).slice(8,-1)},d=\"\".split,v=o((function(){return!Object(\"z\").propertyIsEnumerable(0)}))?function(t){return\"String\"==s(t)?d.call(t,\"\"):Object(t)}:Object,p=function(t){if(null==t)throw TypeError(\"Can't call method on \"+t);return t},g=function(t){return v(p(t))},h=function(t){return\"object\"==typeof t?null!==t:\"function\"==typeof t},y=function(t,e){if(!h(t))return t;var n,r;if(e&&\"function\"==typeof(n=t.toString)&&!h(r=n.call(t)))return r;if(\"function\"==typeof(n=t.valueOf)&&!h(r=n.call(t)))return r;if(!e&&\"function\"==typeof(n=t.toString)&&!h(r=n.call(t)))return r;throw TypeError(\"Can't convert object to primitive value\")},m={}.hasOwnProperty,S=function(t,e){return m.call(t,e)},x=r.document,b=h(x)&&h(x.createElement),E=function(t){return b?x.createElement(t):{}},w=!i&&!o((function(){return 7!=Object.defineProperty(E(\"div\"),\"a\",{get:function(){return 7}}).a})),O=Object.getOwnPropertyDescriptor,T={f:i?O:function(t,e){if(t=g(t),e=y(e,!0),w)try{return O(t,e)}catch(t){}if(S(t,e))return l(!c.f.call(t,e),t[e])}},A=function(t){if(!h(t))throw TypeError(String(t)+\" is not an object\");return t},k=Object.defineProperty,R={f:i?k:function(t,e,n){if(A(t),e=y(e,!0),A(n),w)try{return k(t,e,n)}catch(t){}if(\"get\"in n||\"set\"in n)throw TypeError(\"Accessors not supported\");return\"value\"in n&&(t[e]=n.value),t}},I=i?function(t,e,n){return R.f(t,e,l(1,n))}:function(t,e,n){return t[e]=n,t},j=function(t,e){try{I(r,t,e)}catch(n){r[t]=e}return e},C=r[\"__core-js_shared__\"]||j(\"__core-js_shared__\",{}),L=Function.toString;\"function\"!=typeof C.inspectSource&&(C.inspectSource=function(t){return L.call(t)});var P,M,_,D=C.inspectSource,U=r.WeakMap,N=\"function\"==typeof U&&/native code/.test(D(U)),F=e((function(t){(t.exports=function(t,e){return C[t]||(C[t]=void 0!==e?e:{})})(\"versions\",[]).push({version:\"3.8.3\",mode:\"global\",copyright:\"© 2021 Denis Pushkarev (zloirock.ru)\"})})),W=0,z=Math.random(),$=function(t){return\"Symbol(\"+String(void 0===t?\"\":t)+\")_\"+(++W+z).toString(36)},B=F(\"keys\"),Y=function(t){return B[t]||(B[t]=$(t))},G={},H=r.WeakMap;if(N){var X=C.state||(C.state=new H),V=X.get,K=X.has,q=X.set;P=function(t,e){return e.facade=t,q.call(X,t,e),e},M=function(t){return V.call(X,t)||{}},_=function(t){return K.call(X,t)}}else{var Q=Y(\"state\");G[Q]=!0,P=function(t,e){return e.facade=t,I(t,Q,e),e},M=function(t){return S(t,Q)?t[Q]:{}},_=function(t){return S(t,Q)}}var J={set:P,get:M,has:_,enforce:function(t){return _(t)?M(t):P(t,{})},getterFor:function(t){return function(e){var n;if(!h(e)||(n=M(e)).type!==t)throw TypeError(\"Incompatible receiver, \"+t+\" required\");return n}}},Z=e((function(t){var e=J.get,n=J.enforce,o=String(String).split(\"String\");(t.exports=function(t,e,i,u){var a,c=!!u&&!!u.unsafe,l=!!u&&!!u.enumerable,f=!!u&&!!u.noTargetGet;\"function\"==typeof i&&(\"string\"!=typeof e||S(i,\"name\")||I(i,\"name\",e),(a=n(i)).source||(a.source=o.join(\"string\"==typeof e?e:\"\"))),t!==r?(c?!f&&t[e]&&(l=!0):delete t[e],l?t[e]=i:I(t,e,i)):l?t[e]=i:j(e,i)})(Function.prototype,\"toString\",(function(){return\"function\"==typeof this&&e(this).source||D(this)}))})),tt=r,et=function(t){return\"function\"==typeof t?t:void 0},nt=function(t,e){return arguments.length<2?et(tt[t])||et(r[t]):tt[t]&&tt[t][e]||r[t]&&r[t][e]},rt=Math.ceil,ot=Math.floor,it=function(t){return isNaN(t=+t)?0:(t>0?ot:rt)(t)},ut=Math.min,at=function(t){return t>0?ut(it(t),9007199254740991):0},ct=Math.max,lt=Math.min,ft=function(t,e){var n=it(t);return n<0?ct(n+e,0):lt(n,e)},st=function(t){return function(e,n,r){var o,i=g(e),u=at(i.length),a=ft(r,u);if(t&&n!=n){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===n)return t||a||0;return!t&&-1}},dt={includes:st(!0),indexOf:st(!1)},vt=dt.indexOf,pt=function(t,e){var n,r=g(t),o=0,i=[];for(n in r)!S(G,n)&&S(r,n)&&i.push(n);for(;e.length>o;)S(r,n=e[o++])&&(~vt(i,n)||i.push(n));return i},gt=[\"constructor\",\"hasOwnProperty\",\"isPrototypeOf\",\"propertyIsEnumerable\",\"toLocaleString\",\"toString\",\"valueOf\"],ht=gt.concat(\"length\",\"prototype\"),yt={f:Object.getOwnPropertyNames||function(t){return pt(t,ht)}},mt={f:Object.getOwnPropertySymbols},St=nt(\"Reflect\",\"ownKeys\")||function(t){var e=yt.f(A(t)),n=mt.f;return n?e.concat(n(t)):e},xt=function(t,e){for(var n=St(e),r=R.f,o=T.f,i=0;i<n.length;i++){var u=n[i];S(t,u)||r(t,u,o(e,u))}},bt=/#|\\.prototype\\./,Et=function(t,e){var n=Ot[wt(t)];return n==At||n!=Tt&&(\"function\"==typeof e?o(e):!!e)},wt=Et.normalize=function(t){return String(t).replace(bt,\".\").toLowerCase()},Ot=Et.data={},Tt=Et.NATIVE=\"N\",At=Et.POLYFILL=\"P\",kt=Et,Rt=T.f,It=function(t,e){var n,o,i,u,a,c=t.target,l=t.global,f=t.stat;if(n=l?r:f?r[c]||j(c,{}):(r[c]||{}).prototype)for(o in e){if(u=e[o],i=t.noTargetGet?(a=Rt(n,o))&&a.value:n[o],!kt(l?o:c+(f?\".\":\"#\")+o,t.forced)&&void 0!==i){if(typeof u==typeof i)continue;xt(u,i)}(t.sham||i&&i.sham)&&I(u,\"sham\",!0),Z(n,o,u,t)}},jt=function(t,e){var n=[][t];return!!n&&o((function(){n.call(null,e||function(){throw 1},1)}))},Ct=Object.defineProperty,Lt={},Pt=function(t){throw t},Mt=function(t,e){if(S(Lt,t))return Lt[t];e||(e={});var n=[][t],r=!!S(e,\"ACCESSORS\")&&e.ACCESSORS,u=S(e,0)?e[0]:Pt,a=S(e,1)?e[1]:void 0;return Lt[t]=!!n&&!o((function(){if(r&&!i)return!0;var t={length:-1};r?Ct(t,1,{enumerable:!0,get:Pt}):t[1]=1,n.call(t,u,a)}))},_t=dt.indexOf,Dt=[].indexOf,Ut=!!Dt&&1/[1].indexOf(1,-0)<0,Nt=jt(\"indexOf\"),Ft=Mt(\"indexOf\",{ACCESSORS:!0,1:0});function Wt(t,e){if(!(t instanceof e))throw new TypeError(\"Cannot call a class as a function\")}function zt(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,\"value\"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function $t(t,e,n){return e&&zt(t.prototype,e),n&&zt(t,n),t}It({target:\"Array\",proto:!0,forced:Ut||!Nt||!Ft},{indexOf:function(t){return Ut?Dt.apply(this,arguments)||0:_t(this,t,arguments.length>1?arguments[1]:void 0)}});var Bt=function(){function t(){Wt(this,t)}return $t(t,null,[{key:\"isInBrowser\",value:function(){return\"undefined\"!=typeof window}},{key:\"isServer\",value:function(){return\"undefined\"==typeof window}},{key:\"getUA\",value:function(){return t.isInBrowser()?window.navigator.userAgent.toLowerCase():\"\"}},{key:\"isMobile\",value:function(){return/Mobile|mini|Fennec|Android|iP(ad|od|hone)/.test(navigator.appVersion)}},{key:\"isOpera\",value:function(){return-1!==navigator.userAgent.indexOf(\"Opera\")}},{key:\"isIE\",value:function(){var e=t.getUA();return\"\"!==e&&e.indexOf(\"msie\")>0}},{key:\"isIE9\",value:function(){var e=t.getUA();return\"\"!==e&&e.indexOf(\"msie 9.0\")>0}},{key:\"isEdge\",value:function(){var e=t.getUA();return\"\"!==e&&e.indexOf(\"edge/\")>0}},{key:\"isChrome\",value:function(){var e=t.getUA();return\"\"!==e&&/chrome\\/\\d+/.test(e)&&!t.isEdge()}},{key:\"isPhantomJS\",value:function(){var e=t.getUA();return\"\"!==e&&/phantomjs/.test(e)}},{key:\"isFirefox\",value:function(){var e=t.getUA();return\"\"!==e&&/firefox/.test(e)}}]),t}(),Yt=[].join,Gt=v!=Object,Ht=jt(\"join\",\",\");It({target:\"Array\",proto:!0,forced:Gt||!Ht},{join:function(t){return Yt.call(g(this),void 0===t?\",\":t)}});var Xt,Vt,Kt=function(t){return Object(p(t))},qt=Array.isArray||function(t){return\"Array\"==s(t)},Qt=!!Object.getOwnPropertySymbols&&!o((function(){return!String(Symbol())})),Jt=Qt&&!Symbol.sham&&\"symbol\"==typeof Symbol.iterator,Zt=F(\"wks\"),te=r.Symbol,ee=Jt?te:te&&te.withoutSetter||$,ne=function(t){return S(Zt,t)||(Qt&&S(te,t)?Zt[t]=te[t]:Zt[t]=ee(\"Symbol.\"+t)),Zt[t]},re=ne(\"species\"),oe=function(t,e){var n;return qt(t)&&(\"function\"!=typeof(n=t.constructor)||n!==Array&&!qt(n.prototype)?h(n)&&null===(n=n[re])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===e?0:e)},ie=function(t,e,n){var r=y(e);r in t?R.f(t,r,l(0,n)):t[r]=n},ue=nt(\"navigator\",\"userAgent\")||\"\",ae=r.process,ce=ae&&ae.versions,le=ce&&ce.v8;le?Vt=(Xt=le.split(\".\"))[0]+Xt[1]:ue&&(!(Xt=ue.match(/Edge\\/(\\d+)/))||Xt[1]>=74)&&(Xt=ue.match(/Chrome\\/(\\d+)/))&&(Vt=Xt[1]);var fe=Vt&&+Vt,se=ne(\"species\"),de=function(t){return fe>=51||!o((function(){var e=[];return(e.constructor={})[se]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},ve=de(\"splice\"),pe=Mt(\"splice\",{ACCESSORS:!0,0:0,1:2}),ge=Math.max,he=Math.min;It({target:\"Array\",proto:!0,forced:!ve||!pe},{splice:function(t,e){var n,r,o,i,u,a,c=Kt(this),l=at(c.length),f=ft(t,l),s=arguments.length;if(0===s?n=r=0:1===s?(n=0,r=l-f):(n=s-2,r=he(ge(it(e),0),l-f)),l+n-r>9007199254740991)throw TypeError(\"Maximum allowed length exceeded\");for(o=oe(c,r),i=0;i<r;i++)(u=f+i)in c&&ie(o,i,c[u]);if(o.length=r,n<r){for(i=f;i<l-r;i++)a=i+n,(u=i+r)in c?c[a]=c[u]:delete c[a];for(i=l;i>l-r+n;i--)delete c[i-1]}else if(n>r)for(i=l-r;i>f;i--)a=i+n-1,(u=i+r-1)in c?c[a]=c[u]:delete c[a];for(i=0;i<n;i++)c[i+f]=arguments[i+2];return c.length=l-r+n,o}});var ye={};ye[ne(\"toStringTag\")]=\"z\";var me=\"[object z]\"===String(ye),Se=ne(\"toStringTag\"),xe=\"Arguments\"==s(function(){return arguments}()),be=me?s:function(t){var e,n,r;return void 0===t?\"Undefined\":null===t?\"Null\":\"string\"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),Se))?n:xe?s(e):\"Object\"==(r=s(e))&&\"function\"==typeof e.callee?\"Arguments\":r},Ee=me?{}.toString:function(){return\"[object \"+be(this)+\"]\"};me||Z(Object.prototype,\"toString\",Ee,{unsafe:!0});var we=function(){var t=A(this),e=\"\";return t.global&&(e+=\"g\"),t.ignoreCase&&(e+=\"i\"),t.multiline&&(e+=\"m\"),t.dotAll&&(e+=\"s\"),t.unicode&&(e+=\"u\"),t.sticky&&(e+=\"y\"),e};function Oe(t,e){return RegExp(t,e)}var Te,Ae,ke={UNSUPPORTED_Y:o((function(){var t=Oe(\"a\",\"y\");return t.lastIndex=2,null!=t.exec(\"abcd\")})),BROKEN_CARET:o((function(){var t=Oe(\"^r\",\"gy\");return t.lastIndex=2,null!=t.exec(\"str\")}))},Re=RegExp.prototype.exec,Ie=String.prototype.replace,je=Re,Ce=(Te=/a/,Ae=/b*/g,Re.call(Te,\"a\"),Re.call(Ae,\"a\"),0!==Te.lastIndex||0!==Ae.lastIndex),Le=ke.UNSUPPORTED_Y||ke.BROKEN_CARET,Pe=void 0!==/()??/.exec(\"\")[1];(Ce||Pe||Le)&&(je=function(t){var e,n,r,o,i=this,u=Le&&i.sticky,a=we.call(i),c=i.source,l=0,f=t;return u&&(-1===(a=a.replace(\"y\",\"\")).indexOf(\"g\")&&(a+=\"g\"),f=String(t).slice(i.lastIndex),i.lastIndex>0&&(!i.multiline||i.multiline&&\"\\n\"!==t[i.lastIndex-1])&&(c=\"(?: \"+c+\")\",f=\" \"+f,l++),n=new RegExp(\"^(?:\"+c+\")\",a)),Pe&&(n=new RegExp(\"^\"+c+\"$(?!\\\\s)\",a)),Ce&&(e=i.lastIndex),r=Re.call(u?n:i,f),u?r?(r.input=r.input.slice(l),r[0]=r[0].slice(l),r.index=i.lastIndex,i.lastIndex+=r[0].length):i.lastIndex=0:Ce&&r&&(i.lastIndex=i.global?r.index+r[0].length:e),Pe&&r&&r.length>1&&Ie.call(r[0],n,(function(){for(o=1;o<arguments.length-2;o++)void 0===arguments[o]&&(r[o]=void 0)})),r});var Me=je;It({target:\"RegExp\",proto:!0,forced:/./.exec!==Me},{exec:Me});var _e=RegExp.prototype,De=_e.toString,Ue=o((function(){return\"/a/b\"!=De.call({source:\"a\",flags:\"b\"})})),Ne=\"toString\"!=De.name;(Ue||Ne)&&Z(RegExp.prototype,\"toString\",(function(){var t=A(this),e=String(t.source),n=t.flags;return\"/\"+e+\"/\"+String(void 0===n&&t instanceof RegExp&&!(\"flags\"in _e)?we.call(t):n)}),{unsafe:!0});var Fe=ne(\"species\"),We=!o((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:\"7\"},t},\"7\"!==\"\".replace(t,\"$<a>\")})),ze=\"$0\"===\"a\".replace(/./,\"$0\"),$e=ne(\"replace\"),Be=!!/./[$e]&&\"\"===/./[$e](\"a\",\"$0\"),Ye=!o((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n=\"ab\".split(t);return 2!==n.length||\"a\"!==n[0]||\"b\"!==n[1]})),Ge=function(t,e,n,r){var i=ne(t),u=!o((function(){var e={};return e[i]=function(){return 7},7!=\"\"[t](e)})),a=u&&!o((function(){var e=!1,n=/a/;return\"split\"===t&&((n={}).constructor={},n.constructor[Fe]=function(){return n},n.flags=\"\",n[i]=/./[i]),n.exec=function(){return e=!0,null},n[i](\"\"),!e}));if(!u||!a||\"replace\"===t&&(!We||!ze||Be)||\"split\"===t&&!Ye){var c=/./[i],l=n(i,\"\"[t],(function(t,e,n,r,o){return e.exec===Me?u&&!o?{done:!0,value:c.call(e,n,r)}:{done:!0,value:t.call(n,e,r)}:{done:!1}}),{REPLACE_KEEPS_$0:ze,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:Be}),f=l[0],s=l[1];Z(String.prototype,t,f),Z(RegExp.prototype,i,2==e?function(t,e){return s.call(t,this,e)}:function(t){return s.call(t,this)})}r&&I(RegExp.prototype[i],\"sham\",!0)},He=ne(\"match\"),Xe=function(t){var e;return h(t)&&(void 0!==(e=t[He])?!!e:\"RegExp\"==s(t))},Ve=function(t){if(\"function\"!=typeof t)throw TypeError(String(t)+\" is not a function\");return t},Ke=ne(\"species\"),qe=function(t){return function(e,n){var r,o,i=String(p(e)),u=it(n),a=i.length;return u<0||u>=a?t?\"\":void 0:(r=i.charCodeAt(u))<55296||r>56319||u+1===a||(o=i.charCodeAt(u+1))<56320||o>57343?t?i.charAt(u):r:t?i.slice(u,u+2):o-56320+(r-55296<<10)+65536}},Qe={codeAt:qe(!1),charAt:qe(!0)},Je=Qe.charAt,Ze=function(t,e,n){return e+(n?Je(t,e).length:1)},tn=function(t,e){var n=t.exec;if(\"function\"==typeof n){var r=n.call(t,e);if(\"object\"!=typeof r)throw TypeError(\"RegExp exec method returned something other than an Object or null\");return r}if(\"RegExp\"!==s(t))throw TypeError(\"RegExp#exec called on incompatible receiver\");return Me.call(t,e)},en=[].push,nn=Math.min,rn=!o((function(){return!RegExp(4294967295,\"y\")}));Ge(\"split\",2,(function(t,e,n){var r;return r=\"c\"==\"abbc\".split(/(b)*/)[1]||4!=\"test\".split(/(?:)/,-1).length||2!=\"ab\".split(/(?:ab)*/).length||4!=\".\".split(/(.?)(.?)/).length||\".\".split(/()()/).length>1||\"\".split(/.?/).length?function(t,n){var r=String(p(this)),o=void 0===n?4294967295:n>>>0;if(0===o)return[];if(void 0===t)return[r];if(!Xe(t))return e.call(r,t,o);for(var i,u,a,c=[],l=(t.ignoreCase?\"i\":\"\")+(t.multiline?\"m\":\"\")+(t.unicode?\"u\":\"\")+(t.sticky?\"y\":\"\"),f=0,s=new RegExp(t.source,l+\"g\");(i=Me.call(s,r))&&!((u=s.lastIndex)>f&&(c.push(r.slice(f,i.index)),i.length>1&&i.index<r.length&&en.apply(c,i.slice(1)),a=i[0].length,f=u,c.length>=o));)s.lastIndex===i.index&&s.lastIndex++;return f===r.length?!a&&s.test(\"\")||c.push(\"\"):c.push(r.slice(f)),c.length>o?c.slice(0,o):c}:\"0\".split(void 0,0).length?function(t,n){return void 0===t&&0===n?[]:e.call(this,t,n)}:e,[function(e,n){var o=p(this),i=null==e?void 0:e[t];return void 0!==i?i.call(e,o,n):r.call(String(o),e,n)},function(t,o){var i=n(r,t,this,o,r!==e);if(i.done)return i.value;var u=A(t),a=String(this),c=function(t,e){var n,r=A(t).constructor;return void 0===r||null==(n=A(r)[Ke])?e:Ve(n)}(u,RegExp),l=u.unicode,f=(u.ignoreCase?\"i\":\"\")+(u.multiline?\"m\":\"\")+(u.unicode?\"u\":\"\")+(rn?\"y\":\"g\"),s=new c(rn?u:\"^(?:\"+u.source+\")\",f),d=void 0===o?4294967295:o>>>0;if(0===d)return[];if(0===a.length)return null===tn(s,a)?[a]:[];for(var v=0,p=0,g=[];p<a.length;){s.lastIndex=rn?p:0;var h,y=tn(s,rn?a:a.slice(p));if(null===y||(h=nn(at(s.lastIndex+(rn?0:p)),a.length))===v)p=Ze(a,p,l);else{if(g.push(a.slice(v,p)),g.length===d)return g;for(var m=1;m<=y.length-1;m++)if(g.push(y[m]),g.length===d)return g;p=v=h}}return g.push(a.slice(v)),g}]}),!rn);var on=\"\\t\\n\\v\\f\\r                　\\u2028\\u2029\\ufeff\",un=\"[\"+on+\"]\",an=RegExp(\"^\"+un+un+\"*\"),cn=RegExp(un+un+\"*$\"),ln=function(t){return function(e){var n=String(p(e));return 1&t&&(n=n.replace(an,\"\")),2&t&&(n=n.replace(cn,\"\")),n}},fn={start:ln(1),end:ln(2),trim:ln(3)},sn=fn.trim;It({target:\"String\",proto:!0,forced:function(t){return o((function(){return!!on[t]()||\"​᠎\"!=\"​᠎\"[t]()||on[t].name!==t}))}(\"trim\")},{trim:function(){return sn(this)}});var dn=de(\"slice\"),vn=Mt(\"slice\",{ACCESSORS:!0,0:0,1:2}),pn=ne(\"species\"),gn=[].slice,hn=Math.max;It({target:\"Array\",proto:!0,forced:!dn||!vn},{slice:function(t,e){var n,r,o,i=g(this),u=at(i.length),a=ft(t,u),c=ft(void 0===e?u:e,u);if(qt(i)&&(\"function\"!=typeof(n=i.constructor)||n!==Array&&!qt(n.prototype)?h(n)&&null===(n=n[pn])&&(n=void 0):n=void 0,n===Array||void 0===n))return gn.call(i,a,c);for(r=new(void 0===n?Array:n)(hn(c-a,0)),o=0;a<c;a++,o++)a in i&&ie(r,o,i[a]);return r.length=o,r}});var yn=Object.keys||function(t){return pt(t,gt)},mn=o((function(){yn(1)}));It({target:\"Object\",stat:!0,forced:mn},{keys:function(t){return yn(Kt(t))}});var Sn,xn=function(t){if(Xe(t))throw TypeError(\"The method doesn't accept regular expressions\");return t},bn=ne(\"match\"),En=T.f,wn=\"\".startsWith,On=Math.min,Tn=function(t){var e=/./;try{\"/./\"[t](e)}catch(n){try{return e[bn]=!1,\"/./\"[t](e)}catch(t){}}return!1}(\"startsWith\"),An=!(Tn||(Sn=En(String.prototype,\"startsWith\"),!Sn||Sn.writable));function kn(t){return(kn=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&\"function\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\"symbol\":typeof t})(t)}It({target:\"String\",proto:!0,forced:!An&&!Tn},{startsWith:function(t){var e=String(p(this));xn(t);var n=at(On(arguments.length>1?arguments[1]:void 0,e.length)),r=String(t);return wn?wn.call(e,r,n):e.slice(n,n+r.length)===r}});var Rn=function(t){return t instanceof Date},In=function(t){return\"function\"==typeof t},jn=function(t){return\"string\"==typeof t},Cn=function(t){return\"symbol\"===kn(t)},Ln=function(t){return\"boolean\"==typeof t},Pn=function(t){return\"number\"==typeof t},Mn=function(t){return null!==t&&\"object\"===kn(t)},_n=Object.prototype.toString,Dn=function(t){return _n.call(t)},Un=Object.prototype.hasOwnProperty,Nn=function(t,e){return Un.call(t,e)},Fn=Array.isArray,Wn=function(t){return\"[object Map]\"===Dn(t)},zn=function(t){return\"[object Set]\"===Dn(t)},$n=function(t){return Dn(t).slice(8,-1)},Bn=function(t){return $n(t).startsWith(\"HTML\")},Yn=function(t){return Mn(t)&&In(t.then)&&In(t.catch)},Gn=function(t){return null===t},Hn=function(t){return void 0===t},Xn=function(t){return!t&&0!==t&&\"\"!==t||Fn(t)&&!t.length||Mn(t)&&!Object.keys(t).length},Vn=function(){function t(){Wt(this,t)}return $t(t,null,[{key:\"isWindow\",value:function(t){return t===window}},{key:\"addEventListener\",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];t&&e&&n&&t.addEventListener(e,n,r)}},{key:\"removeEventListener\",value:function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];t&&e&&n&&t.removeEventListener(e,n,r)}},{key:\"triggerDragEvent\",value:function(e,n){var r=!1,o=function(t){var e;null===(e=n.drag)||void 0===e||e.call(n,t)},i=function e(i){var u;t.removeEventListener(document,\"mousemove\",o),t.removeEventListener(document,\"mouseup\",e),document.onselectstart=null,document.ondragstart=null,r=!1,null===(u=n.end)||void 0===u||u.call(n,i)};t.addEventListener(e,\"mousedown\",(function(e){var u;r||(document.onselectstart=function(){return!1},document.ondragstart=function(){return!1},t.addEventListener(document,\"mousemove\",o),t.addEventListener(document,\"mouseup\",i),r=!0,null===(u=n.start)||void 0===u||u.call(n,e))}))}},{key:\"getBoundingClientRect\",value:function(t){return t&&Mn(t)&&1===t.nodeType?t.getBoundingClientRect():null}},{key:\"hasClass\",value:function(t,e){return!!(t&&Mn(t)&&jn(e)&&1===t.nodeType)&&t.classList.contains(e.trim())}},{key:\"addClass\",value:function(e,n){if(e&&Mn(e)&&jn(n)&&1===e.nodeType&&(n=n.trim(),!t.hasClass(e,n))){var r=e.className;e.className=r?r+\" \"+n:n}}},{key:\"removeClass\",value:function(t,e){if(t&&Mn(t)&&jn(e)&&1===t.nodeType&&\"string\"==typeof t.className){e=e.trim();for(var n=t.className.trim().split(\" \"),r=n.length-1;r>=0;r--)n[r]=n[r].trim(),n[r]&&n[r]!==e||n.splice(r,1);t.className=n.join(\" \")}}},{key:\"toggleClass\",value:function(t,e,n){t&&Mn(t)&&jn(e)&&1===t.nodeType&&t.classList.toggle(e,n)}},{key:\"replaceClass\",value:function(e,n,r){e&&Mn(e)&&jn(n)&&jn(r)&&1===e.nodeType&&(n=n.trim(),r=r.trim(),t.removeClass(e,n),t.addClass(e,r))}},{key:\"getScrollTop\",value:function(t){var e=\"scrollTop\"in t?t.scrollTop:t.pageYOffset;return Math.max(e,0)}},{key:\"setScrollTop\",value:function(t,e){\"scrollTop\"in t?t.scrollTop=e:t.scrollTo(t.scrollX,e)}},{key:\"getRootScrollTop\",value:function(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0}},{key:\"setRootScrollTop\",value:function(e){t.setScrollTop(window,e),t.setScrollTop(document.body,e)}},{key:\"getElementTop\",value:function(e,n){if(t.isWindow(e))return 0;var r=n?t.getScrollTop(n):t.getRootScrollTop();return e.getBoundingClientRect().top+r}},{key:\"getVisibleHeight\",value:function(e){return t.isWindow(e)?e.innerHeight:e.getBoundingClientRect().height}},{key:\"isHidden\",value:function(t){if(!t)return!1;var e=window.getComputedStyle(t),n=\"none\"===e.display,r=null===t.offsetParent&&\"fixed\"!==e.position;return n||r}},{key:\"triggerEvent\",value:function(t,e){if(\"createEvent\"in document){var n=document.createEvent(\"HTMLEvents\");n.initEvent(e,!1,!0),t.dispatchEvent(n)}}},{key:\"calcAngle\",value:function(t,e){var n=t.getBoundingClientRect(),r=n.left+n.width/2,o=n.top+n.height/2,i=Math.abs(r-e.clientX),u=Math.abs(o-e.clientY),a=u/Math.sqrt(Math.pow(i,2)+Math.pow(u,2)),c=Math.acos(a),l=Math.floor(180/(Math.PI/c));return e.clientX>r&&e.clientY>o&&(l=180-l),e.clientX==r&&e.clientY>o&&(l=180),e.clientX>r&&e.clientY==o&&(l=90),e.clientX<r&&e.clientY>o&&(l=180+l),e.clientX<r&&e.clientY==o&&(l=270),e.clientX<r&&e.clientY<o&&(l=360-l),l}},{key:\"querySelector\",value:function(t,e){return e?e.querySelector(t):document.querySelector(t)}},{key:\"createElement\",value:function(t){for(var e=document.createElement(t),n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];for(var i=0;i<r.length;i++)r[i]&&e.classList.add(r[i]);return e}},{key:\"appendChild\",value:function(t){for(var e=0;e<(arguments.length<=1?0:arguments.length-1);e++)t.appendChild(e+1<1||arguments.length<=e+1?void 0:arguments[e+1])}},{key:\"getWindow\",value:function(t){if(\"[object Window]\"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}},{key:\"isElement\",value:function(t){return t instanceof this.getWindow(t).Element||t instanceof Element}},{key:\"isHTMLElement\",value:function(t){return t instanceof this.getWindow(t).HTMLElement||t instanceof HTMLElement}},{key:\"isShadowRoot\",value:function(t){return\"undefined\"!=typeof ShadowRoot&&(t instanceof this.getWindow(t).ShadowRoot||t instanceof ShadowRoot)}},{key:\"getWindowScroll\",value:function(t){var e=this.getWindow(t);return{scrollLeft:e.pageXOffset||0,scrollTop:e.pageYOffset||0}}}]),t}(),Kn=Math.floor,qn=\"\".replace,Qn=/\\$([$&'`]|\\d\\d?|<[^>]*>)/g,Jn=/\\$([$&'`]|\\d\\d?)/g,Zn=function(t,e,n,r,o,i){var u=n+t.length,a=r.length,c=Jn;return void 0!==o&&(o=Kt(o),c=Qn),qn.call(i,c,(function(i,c){var l;switch(c.charAt(0)){case\"$\":return\"$\";case\"&\":return t;case\"`\":return e.slice(0,n);case\"'\":return e.slice(u);case\"<\":l=o[c.slice(1,-1)];break;default:var f=+c;if(0===f)return i;if(f>a){var s=Kn(f/10);return 0===s?i:s<=a?void 0===r[s-1]?c.charAt(1):r[s-1]+c.charAt(1):i}l=r[f-1]}return void 0===l?\"\":l}))},tr=Math.max,er=Math.min;Ge(\"replace\",2,(function(t,e,n,r){var o=r.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,i=r.REPLACE_KEEPS_$0,u=o?\"$\":\"$0\";return[function(n,r){var o=p(this),i=null==n?void 0:n[t];return void 0!==i?i.call(n,o,r):e.call(String(o),n,r)},function(t,r){if(!o&&i||\"string\"==typeof r&&-1===r.indexOf(u)){var a=n(e,t,this,r);if(a.done)return a.value}var c=A(t),l=String(this),f=\"function\"==typeof r;f||(r=String(r));var s=c.global;if(s){var d=c.unicode;c.lastIndex=0}for(var v=[];;){var p=tn(c,l);if(null===p)break;if(v.push(p),!s)break;\"\"===String(p[0])&&(c.lastIndex=Ze(l,at(c.lastIndex),d))}for(var g,h=\"\",y=0,m=0;m<v.length;m++){p=v[m];for(var S=String(p[0]),x=tr(er(it(p.index),l.length),0),b=[],E=1;E<p.length;E++)b.push(void 0===(g=p[E])?g:String(g));var w=p.groups;if(f){var O=[S].concat(b,x,l);void 0!==w&&O.push(w);var T=String(r.apply(void 0,O))}else T=Zn(S,l,x,b,w,r);x>=y&&(h+=l.slice(y,x)+T,y=x+S.length)}return h+l.slice(y)}]}));var nr=function(){function t(){Wt(this,t)}return $t(t,null,[{key:\"camelize\",value:function(t){return t.replace(/-(\\w)/g,(function(t,e){return e?e.toUpperCase():\"\"}))}},{key:\"capitalize\",value:function(t){return t.charAt(0).toUpperCase()+t.slice(1)}}]),t}(),rr=function(){function t(){Wt(this,t)}return $t(t,null,[{key:\"_clone\",value:function(){}}]),t}(),or=ne(\"isConcatSpreadable\"),ir=fe>=51||!o((function(){var t=[];return t[or]=!1,t.concat()[0]!==t})),ur=de(\"concat\"),ar=function(t){if(!h(t))return!1;var e=t[or];return void 0!==e?!!e:qt(t)};It({target:\"Array\",proto:!0,forced:!ir||!ur},{concat:function(t){var e,n,r,o,i,u=Kt(this),a=oe(u,0),c=0;for(e=-1,r=arguments.length;e<r;e++)if(ar(i=-1===e?u:arguments[e])){if(c+(o=at(i.length))>9007199254740991)throw TypeError(\"Maximum allowed index exceeded\");for(n=0;n<o;n++,c++)n in i&&ie(a,c,i[n])}else{if(c>=9007199254740991)throw TypeError(\"Maximum allowed index exceeded\");ie(a,c++,i)}return a.length=c,a}});var cr,lr=function(t,e,n){if(Ve(t),void 0===e)return t;switch(n){case 0:return function(){return t.call(e)};case 1:return function(n){return t.call(e,n)};case 2:return function(n,r){return t.call(e,n,r)};case 3:return function(n,r,o){return t.call(e,n,r,o)}}return function(){return t.apply(e,arguments)}},fr=[].push,sr=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,a=5==t||i;return function(c,l,f,s){for(var d,p,g=Kt(c),h=v(g),y=lr(l,f,3),m=at(h.length),S=0,x=s||oe,b=e?x(c,m):n||u?x(c,0):void 0;m>S;S++)if((a||S in h)&&(p=y(d=h[S],S,g),t))if(e)b[S]=p;else if(p)switch(t){case 3:return!0;case 5:return d;case 6:return S;case 2:fr.call(b,d)}else switch(t){case 4:return!1;case 7:fr.call(b,d)}return i?-1:r||o?o:b}},dr={forEach:sr(0),map:sr(1),filter:sr(2),some:sr(3),every:sr(4),find:sr(5),findIndex:sr(6),filterOut:sr(7)},vr=i?Object.defineProperties:function(t,e){A(t);for(var n,r=yn(e),o=r.length,i=0;o>i;)R.f(t,n=r[i++],e[n]);return t},pr=nt(\"document\",\"documentElement\"),gr=Y(\"IE_PROTO\"),hr=function(){},yr=function(t){return\"<script>\"+t+\"<\\/script>\"},mr=function(){try{cr=document.domain&&new ActiveXObject(\"htmlfile\")}catch(t){}var t,e;mr=cr?function(t){t.write(yr(\"\")),t.close();var e=t.parentWindow.Object;return t=null,e}(cr):((e=E(\"iframe\")).style.display=\"none\",pr.appendChild(e),e.src=String(\"javascript:\"),(t=e.contentWindow.document).open(),t.write(yr(\"document.F=Object\")),t.close(),t.F);for(var n=gt.length;n--;)delete mr.prototype[gt[n]];return mr()};G[gr]=!0;var Sr=Object.create||function(t,e){var n;return null!==t?(hr.prototype=A(t),n=new hr,hr.prototype=null,n[gr]=t):n=mr(),void 0===e?n:vr(n,e)},xr=ne(\"unscopables\"),br=Array.prototype;null==br[xr]&&R.f(br,xr,{configurable:!0,value:Sr(null)});var Er=function(t){br[xr][t]=!0},wr=dr.find,Or=!0,Tr=Mt(\"find\");\"find\"in[]&&Array(1).find((function(){Or=!1})),It({target:\"Array\",proto:!0,forced:Or||!Tr},{find:function(t){return wr(this,t,arguments.length>1?arguments[1]:void 0)}}),Er(\"find\");var Ar=dr.findIndex,kr=!0,Rr=Mt(\"findIndex\");\"findIndex\"in[]&&Array(1).findIndex((function(){kr=!1})),It({target:\"Array\",proto:!0,forced:kr||!Rr},{findIndex:function(t){return Ar(this,t,arguments.length>1?arguments[1]:void 0)}}),Er(\"findIndex\");var Ir=function(t,e,n,r,o,i,u,a){for(var c,l=o,f=0,s=!!u&&lr(u,a,3);f<r;){if(f in n){if(c=s?s(n[f],f,e):n[f],i>0&&qt(c))l=Ir(t,e,c,at(c.length),l,i-1)-1;else{if(l>=9007199254740991)throw TypeError(\"Exceed the acceptable array length\");t[l]=c}l++}f++}return l},jr=Ir;It({target:\"Array\",proto:!0},{flat:function(){var t=arguments.length?arguments[0]:void 0,e=Kt(this),n=at(e.length),r=oe(e,0);return r.length=jr(r,e,e,n,0,void 0===t?1:it(t)),r}});var Cr=function(t){var e=t.return;if(void 0!==e)return A(e.call(t)).value},Lr=function(t,e,n,r){try{return r?e(A(n)[0],n[1]):e(n)}catch(e){throw Cr(t),e}},Pr={},Mr=ne(\"iterator\"),_r=Array.prototype,Dr=function(t){return void 0!==t&&(Pr.Array===t||_r[Mr]===t)},Ur=ne(\"iterator\"),Nr=function(t){if(null!=t)return t[Ur]||t[\"@@iterator\"]||Pr[be(t)]},Fr=ne(\"iterator\"),Wr=!1;try{var zr=0,$r={next:function(){return{done:!!zr++}},return:function(){Wr=!0}};$r[Fr]=function(){return this},Array.from($r,(function(){throw 2}))}catch(t){}var Br=function(t,e){if(!e&&!Wr)return!1;var n=!1;try{var r={};r[Fr]=function(){return{next:function(){return{done:n=!0}}}},t(r)}catch(t){}return n},Yr=!Br((function(t){Array.from(t)}));It({target:\"Array\",stat:!0,forced:Yr},{from:function(t){var e,n,r,o,i,u,a=Kt(t),c=\"function\"==typeof this?this:Array,l=arguments.length,f=l>1?arguments[1]:void 0,s=void 0!==f,d=Nr(a),v=0;if(s&&(f=lr(f,l>2?arguments[2]:void 0,2)),null==d||c==Array&&Dr(d))for(n=new c(e=at(a.length));e>v;v++)u=s?f(a[v],v):a[v],ie(n,v,u);else for(i=(o=d.call(a)).next,n=new c;!(r=i.call(o)).done;v++)u=s?Lr(o,f,[r.value,v],!0):r.value,ie(n,v,u);return n.length=v,n}});var Gr=function(t){return function(e,n,r,o){Ve(n);var i=Kt(e),u=v(i),a=at(i.length),c=t?a-1:0,l=t?-1:1;if(r<2)for(;;){if(c in u){o=u[c],c+=l;break}if(c+=l,t?c<0:a<=c)throw TypeError(\"Reduce of empty array with no initial value\")}for(;t?c>=0:a>c;c+=l)c in u&&(o=n(o,u[c],c,i));return o}},Hr={left:Gr(!1),right:Gr(!0)},Xr=\"process\"==s(r.process),Vr=Hr.left,Kr=jt(\"reduce\"),qr=Mt(\"reduce\",{1:0});It({target:\"Array\",proto:!0,forced:!Kr||!qr||!Xr&&fe>79&&fe<83},{reduce:function(t){return Vr(this,t,arguments.length,arguments.length>1?arguments[1]:void 0)}}),Er(\"flat\");var Qr,Jr,Zr,to=!o((function(){return Object.isExtensible(Object.preventExtensions({}))})),eo=e((function(t){var e=R.f,n=$(\"meta\"),r=0,o=Object.isExtensible||function(){return!0},i=function(t){e(t,n,{value:{objectID:\"O\"+ ++r,weakData:{}}})},u=t.exports={REQUIRED:!1,fastKey:function(t,e){if(!h(t))return\"symbol\"==typeof t?t:(\"string\"==typeof t?\"S\":\"P\")+t;if(!S(t,n)){if(!o(t))return\"F\";if(!e)return\"E\";i(t)}return t[n].objectID},getWeakData:function(t,e){if(!S(t,n)){if(!o(t))return!0;if(!e)return!1;i(t)}return t[n].weakData},onFreeze:function(t){return to&&u.REQUIRED&&o(t)&&!S(t,n)&&i(t),t}};G[n]=!0})),no=function(t,e){this.stopped=t,this.result=e},ro=function(t,e,n){var r,o,i,u,a,c,l,f=n&&n.that,s=!(!n||!n.AS_ENTRIES),d=!(!n||!n.IS_ITERATOR),v=!(!n||!n.INTERRUPTED),p=lr(e,f,1+s+v),g=function(t){return r&&Cr(r),new no(!0,t)},h=function(t){return s?(A(t),v?p(t[0],t[1],g):p(t[0],t[1])):v?p(t,g):p(t)};if(d)r=t;else{if(\"function\"!=typeof(o=Nr(t)))throw TypeError(\"Target is not iterable\");if(Dr(o)){for(i=0,u=at(t.length);u>i;i++)if((a=h(t[i]))&&a instanceof no)return a;return new no(!1)}r=o.call(t)}for(c=r.next;!(l=c.call(r)).done;){try{a=h(l.value)}catch(t){throw Cr(r),t}if(\"object\"==typeof a&&a&&a instanceof no)return a}return new no(!1)},oo=function(t,e,n){if(!(t instanceof e))throw TypeError(\"Incorrect \"+(n?n+\" \":\"\")+\"invocation\");return t},io=R.f,uo=ne(\"toStringTag\"),ao=function(t,e,n){t&&!S(t=n?t:t.prototype,uo)&&io(t,uo,{configurable:!0,value:e})},co=Object.setPrototypeOf||(\"__proto__\"in{}?function(){var t,e=!1,n={};try{(t=Object.getOwnPropertyDescriptor(Object.prototype,\"__proto__\").set).call(n,[]),e=n instanceof Array}catch(t){}return function(n,r){return A(n),function(t){if(!h(t)&&null!==t)throw TypeError(\"Can't set \"+String(t)+\" as a prototype\")}(r),e?t.call(n,r):n.__proto__=r,n}}():void 0),lo=function(t,e,n){for(var r in e)Z(t,r,e[r],n);return t},fo=!o((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),so=Y(\"IE_PROTO\"),vo=Object.prototype,po=fo?Object.getPrototypeOf:function(t){return t=Kt(t),S(t,so)?t[so]:\"function\"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?vo:null},go=ne(\"iterator\"),ho=!1;[].keys&&(\"next\"in(Zr=[].keys())?(Jr=po(po(Zr)))!==Object.prototype&&(Qr=Jr):ho=!0),(null==Qr||o((function(){var t={};return Qr[go].call(t)!==t})))&&(Qr={}),S(Qr,go)||I(Qr,go,(function(){return this}));var yo={IteratorPrototype:Qr,BUGGY_SAFARI_ITERATORS:ho},mo=yo.IteratorPrototype,So=function(){return this},xo=yo.IteratorPrototype,bo=yo.BUGGY_SAFARI_ITERATORS,Eo=ne(\"iterator\"),wo=function(){return this},Oo=function(t,e,n,r,o,i,u){!function(t,e,n){var r=e+\" Iterator\";t.prototype=Sr(mo,{next:l(1,n)}),ao(t,r,!1),Pr[r]=So}(n,e,r);var a,c,f,s=function(t){if(t===o&&h)return h;if(!bo&&t in p)return p[t];switch(t){case\"keys\":case\"values\":case\"entries\":return function(){return new n(this,t)}}return function(){return new n(this)}},d=e+\" Iterator\",v=!1,p=t.prototype,g=p[Eo]||p[\"@@iterator\"]||o&&p[o],h=!bo&&g||s(o),y=\"Array\"==e&&p.entries||g;if(y&&(a=po(y.call(new t)),xo!==Object.prototype&&a.next&&(po(a)!==xo&&(co?co(a,xo):\"function\"!=typeof a[Eo]&&I(a,Eo,wo)),ao(a,d,!0))),\"values\"==o&&g&&\"values\"!==g.name&&(v=!0,h=function(){return g.call(this)}),p[Eo]!==h&&I(p,Eo,h),Pr[e]=h,o)if(c={values:s(\"values\"),keys:i?h:s(\"keys\"),entries:s(\"entries\")},u)for(f in c)(bo||v||!(f in p))&&Z(p,f,c[f]);else It({target:e,proto:!0,forced:bo||v},c);return c},To=ne(\"species\"),Ao=R.f,ko=eo.fastKey,Ro=J.set,Io=J.getterFor;!function(t,e,n){var i=-1!==t.indexOf(\"Map\"),u=-1!==t.indexOf(\"Weak\"),a=i?\"set\":\"add\",c=r[t],l=c&&c.prototype,f=c,s={},d=function(t){var e=l[t];Z(l,t,\"add\"==t?function(t){return e.call(this,0===t?0:t),this}:\"delete\"==t?function(t){return!(u&&!h(t))&&e.call(this,0===t?0:t)}:\"get\"==t?function(t){return u&&!h(t)?void 0:e.call(this,0===t?0:t)}:\"has\"==t?function(t){return!(u&&!h(t))&&e.call(this,0===t?0:t)}:function(t,n){return e.call(this,0===t?0:t,n),this})};if(kt(t,\"function\"!=typeof c||!(u||l.forEach&&!o((function(){(new c).entries().next()})))))f=n.getConstructor(e,t,i,a),eo.REQUIRED=!0;else if(kt(t,!0)){var v=new f,p=v[a](u?{}:-0,1)!=v,g=o((function(){v.has(1)})),y=Br((function(t){new c(t)})),m=!u&&o((function(){for(var t=new c,e=5;e--;)t[a](e,e);return!t.has(-0)}));y||((f=e((function(e,n){oo(e,f,t);var r=function(t,e,n){var r,o;return co&&\"function\"==typeof(r=e.constructor)&&r!==n&&h(o=r.prototype)&&o!==n.prototype&&co(t,o),t}(new c,e,f);return null!=n&&ro(n,r[a],{that:r,AS_ENTRIES:i}),r}))).prototype=l,l.constructor=f),(g||m)&&(d(\"delete\"),d(\"has\"),i&&d(\"get\")),(m||p)&&d(a),u&&l.clear&&delete l.clear}s[t]=f,It({global:!0,forced:f!=c},s),ao(f,t),u||n.setStrong(f,t,i)}(\"Set\",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),{getConstructor:function(t,e,n,r){var o=t((function(t,u){oo(t,o,e),Ro(t,{type:e,index:Sr(null),first:void 0,last:void 0,size:0}),i||(t.size=0),null!=u&&ro(u,t[r],{that:t,AS_ENTRIES:n})})),u=Io(e),a=function(t,e,n){var r,o,a=u(t),l=c(t,e);return l?l.value=n:(a.last=l={index:o=ko(e,!0),key:e,value:n,previous:r=a.last,next:void 0,removed:!1},a.first||(a.first=l),r&&(r.next=l),i?a.size++:t.size++,\"F\"!==o&&(a.index[o]=l)),t},c=function(t,e){var n,r=u(t),o=ko(e);if(\"F\"!==o)return r.index[o];for(n=r.first;n;n=n.next)if(n.key==e)return n};return lo(o.prototype,{clear:function(){for(var t=u(this),e=t.index,n=t.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete e[n.index],n=n.next;t.first=t.last=void 0,i?t.size=0:this.size=0},delete:function(t){var e=this,n=u(e),r=c(e,t);if(r){var o=r.next,a=r.previous;delete n.index[r.index],r.removed=!0,a&&(a.next=o),o&&(o.previous=a),n.first==r&&(n.first=o),n.last==r&&(n.last=a),i?n.size--:e.size--}return!!r},forEach:function(t){for(var e,n=u(this),r=lr(t,arguments.length>1?arguments[1]:void 0,3);e=e?e.next:n.first;)for(r(e.value,e.key,this);e&&e.removed;)e=e.previous},has:function(t){return!!c(this,t)}}),lo(o.prototype,n?{get:function(t){var e=c(this,t);return e&&e.value},set:function(t,e){return a(this,0===t?0:t,e)}}:{add:function(t){return a(this,t=0===t?0:t,t)}}),i&&Ao(o.prototype,\"size\",{get:function(){return u(this).size}}),o},setStrong:function(t,e,n){var r=e+\" Iterator\",o=Io(e),u=Io(r);Oo(t,e,(function(t,e){Ro(this,{type:r,target:t,state:o(t),kind:e,last:void 0})}),(function(){for(var t=u(this),e=t.kind,n=t.last;n&&n.removed;)n=n.previous;return t.target&&(t.last=n=n?n.next:t.state.first)?\"keys\"==e?{value:n.key,done:!1}:\"values\"==e?{value:n.value,done:!1}:{value:[n.key,n.value],done:!1}:(t.target=void 0,{value:void 0,done:!0})}),n?\"entries\":\"values\",!n,!0),function(t){var e=nt(t),n=R.f;i&&e&&!e[To]&&n(e,To,{configurable:!0,get:function(){return this}})}(e)}});var jo=Qe.charAt,Co=J.set,Lo=J.getterFor(\"String Iterator\");Oo(String,\"String\",(function(t){Co(this,{type:\"String Iterator\",string:String(t),index:0})}),(function(){var t,e=Lo(this),n=e.string,r=e.index;return r>=n.length?{value:void 0,done:!0}:(t=jo(n,r),e.index+=t.length,{value:t,done:!1})}));var Po={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Mo=J.set,_o=J.getterFor(\"Array Iterator\"),Do=Oo(Array,\"Array\",(function(t,e){Mo(this,{type:\"Array Iterator\",target:g(t),index:0,kind:e})}),(function(){var t=_o(this),e=t.target,n=t.kind,r=t.index++;return!e||r>=e.length?(t.target=void 0,{value:void 0,done:!0}):\"keys\"==n?{value:r,done:!1}:\"values\"==n?{value:e[r],done:!1}:{value:[r,e[r]],done:!1}}),\"values\");Pr.Arguments=Pr.Array,Er(\"keys\"),Er(\"values\"),Er(\"entries\");var Uo=ne(\"iterator\"),No=ne(\"toStringTag\"),Fo=Do.values;for(var Wo in Po){var zo=r[Wo],$o=zo&&zo.prototype;if($o){if($o[Uo]!==Fo)try{I($o,Uo,Fo)}catch(t){$o[Uo]=Fo}if($o[No]||I($o,No,Wo),Po[Wo])for(var Bo in Do)if($o[Bo]!==Do[Bo])try{I($o,Bo,Do[Bo])}catch(t){$o[Bo]=Do[Bo]}}}var Yo=function(){function t(){Wt(this,t)}return $t(t,null,[{key:\"deduplicate\",value:function(t){return Array.from(new Set(t))}},{key:\"flat\",value:function(e){return e.reduce((function(e,n){var r=Array.isArray(n)?t.flat(n):n;return e.concat(r)}),[])}},{key:\"find\",value:function(t,e){return t.find(e)}},{key:\"findIndex\",value:function(t,e){return t.findIndex(e)}}]),t}(),Go=function(){function t(){Wt(this,t)}return $t(t,null,[{key:\"today\",value:function(){return new Date}}]),t}(),Ho=function(){},Xo=function(t){return t},Vo=function(){function t(){Wt(this,t)}return $t(t,null,[{key:\"range\",value:function(t,e,n){return Math.min(Math.max(t,e),n)}},{key:\"clamp\",value:function(t,e,n){return e<n?t<e?e:t>n?n:t:t<n?n:t>e?e:t}}]),t}();export{Yo as ArrayUtils,Bt as BrowserUtils,Vn as DOMUtils,Go as DateUtils,Vo as NumberUtils,rr as ObjectUtils,nr as StringUtils,Nn as hasOwn,Xo as identity,Fn as isArray,Ln as isBool,Rn as isDate,Hn as isDef,Xn as isEmpty,In as isFunction,Bn as isHTMLElement,Wn as isMap,Gn as isNull,Pn as isNumber,Mn as isObject,Yn as isPromise,zn as isSet,jn as isString,Cn as isSymbol,Ho as noop,_n as objectToString,$n as toRawType,Dn as toTypeString};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAIA,QAAI,iBAAkB,kBAAkB,CAAC;AAEzC,mBAAe,YAAa,2BAAW;AAErC,UAAI,UAAU;AAAA,QAEZ,yBAAyB,SAAS,MAAM;AACtC,iBAAO,QAAQ,eAAe,IAAI;AAAA,QACpC;AAAA,QAEA,mCAAmC,SAAS,MAAM;AAChD,iBAAO,QAAQ,eAAe,IAAI;AAAA,QACpC;AAAA,QAEA,yBAAyB,SAAS,MAAM;AACtC,iBAAO,QAAQ,eAAe,IAAI;AAAA,QACpC;AAAA,QAEA,mCAAmC,SAAS,MAAM;AAChD,iBAAO,QAAQ,eAAe,IAAI;AAAA,QACpC;AAAA,QAEA,kBAAkB,SAAS,MAAM;AAC/B,cAAI,cAAc,QAAQ,MAAM,KAAK,WAAW;AAChD,cAAI,aAAa;AACf,2BAAe;AAAA,UACjB;AAEA,iBAAO,KAAK,OAAO,MAAM,cAAc,QAAQ,MAAM,KAAK,UAAU,IAAI;AAAA,QAC1E;AAAA,QAEA,eAAe,SAAS,MAAM;AAC5B,cAAI,SAAS,KAAK,OACdA,MAAK,QAAQ,MAAM,KAAK,EAAE,GAC1B,QAAQ,QAAQ,MAAM,KAAK,KAAK;AAEpC,cAAI,OAAO;AACT,sBAAU,MAAM;AAAA,UAClB;AAEA,cAAIA,KAAI;AACN,sBAAU,SAASA;AAAA,UACrB;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,wBAAwB,SAAS,MAAM;AACrC,cAAI,SAAS,IACTA,MAAK,QAAQ,MAAM,KAAK,EAAE;AAE9B,cAAIA,KAAI;AACN,sBAAUA;AAAA,UACZ;AACA,iBAAO;AAAA,QACT;AAAA,QAEA,wBAAwB,SAAS,MAAM;AACrC,cAAI,SAAS,KAAK,OACdA,MAAK,QAAQ,MAAM,KAAK,EAAE;AAE9B,cAAIA,KAAI;AACN,sBAAU,SAASA;AAAA,UACrB;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,0BAA0B,SAAS,MAAM;AACvC,iBAAO,KAAK;AAAA,QACd;AAAA,QAEA,kBAAkB,SAAS,MAAM;AAC/B,iBAAO,QAAQ,MAAM,KAAK,MAAM,CAAC,IAAI,MAAM,QAAQ,MAAM,KAAK,MAAM,CAAC;AAAA,QACvE;AAAA,QAEA,WAAW,SAAS,MAAM;AACxB,iBAAO,KAAK,QAAQ;AAAA,QACtB;AAAA,QAEA,YAAY,SAAS,MAAM;AACzB,iBAAO,KAAK,QAAQ;AAAA,QACtB;AAAA,QAEA,YAAY,SAAS,MAAM;AACzB,iBAAO,KAAK,QAAQ;AAAA,QACtB;AAAA,QAEA,cAAc,SAAS,MAAM;AAC3B,iBAAO,UAAU,KAAK,QAAQ;AAAA,QAChC;AAAA,QAEA,iBAAiB,SAAS,MAAM;AAC9B,iBAAO,QAAQ,YAAY,KAAK,OAAO,IAAI;AAAA,QAC7C;AAAA,QAEA,aAAa,SAAS,MAAM;AAC1B,iBAAO,QAAQ,YAAY,MAAM,KAAK,OAAO,IAAI;AAAA,QACnD;AAAA,QAEA,aAAa,SAAS,MAAM;AAC1B,iBAAO,QAAQ,YAAY,SAAS,KAAK,MAAM,KAAK,IAAI,IAAI,KAAK,IAAI;AAAA,QACvE;AAAA,QAEA,cAAc,SAAS,MAAM;AAC3B,iBAAO,QAAQ,YAAY,UAAU,KAAK,MAAM,KAAK,IAAI,IAAI,KAAK,IAAI;AAAA,QACxE;AAAA,QAEA,aAAa,SAAS,MAAM;AAC1B,iBAAO,QAAQ,YAAY,SAAS,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,MAAM,CAAC,IAAI,QAAQ,KAAK,MAAM,CAAC,IAAI,MAAM,IAAI;AAAA,QAC/G;AAAA,QAEA,cAAc,SAAS,MAAM;AAC3B,iBAAO,QAAQ,YAAY,UAAU,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,MAAM,CAAC,IAAI,QAAQ,KAAK,MAAM,CAAC,IAAI,QAAQ,KAAK,MAAM,CAAC,IAAI,KAAK,IAAI;AAAA,QACvI;AAAA,QAEA,aAAa,SAAS,MAAM;AAC1B,iBAAO,QAAQ,YAAY,SAAS,KAAK,QAAQ,KAAK,IAAI;AAAA,QAC5D;AAAA,QAEA,eAAe,SAAS,aAAa,MAAM;AACzC,cAAI,SAAS,aACT,SAAS,QAAQ,MAAM,KAAK,MAAM;AAEtC,cAAI,QAAQ;AACV,sBAAU,MAAM;AAAA,UAClB;AACA,iBAAO;AAAA,QACT;AAAA,QAEA,iBAAiB,SAAS,MAAM;AAC9B,iBAAO,KAAK,QAAQ;AAAA,QACtB;AAAA,QAEA,qBAAqB,SAAS,MAAM;AAClC,iBAAO,QAAQ,KAAK;AAAA,QACtB;AAAA,QAEA,eAAe,SAAS,UAAU;AAChC,cAAI,SAAS,IACT,OAAO,SAAS;AAEpB,mBAAS,QAAQ,SAAS,SAASC,IAAG;AACpC,sBAAU,QAAQ,MAAM,OAAO;AAC/B,gBAAIA,KAAI,OAAO,GAAG;AAChB,wBAAU;AAAA,YACZ;AAAA,UACF,CAAC;AAED,iBAAO;AAAA,QACT;AAAA,QAEA,gBAAgB,SAAS,KAAK;AAC5B,cAAI,IAAI,SAAS,IAAI,QAAQ;AAC3B,mBAAO,QAAQ,MAAM,IAAI,KAAK,IAAI,MAAM,QAAQ,MAAM,IAAI,MAAM;AAAA,UAClE;AACA,iBAAO;AAAA,QACT;AAAA,QAEA,SAAS,SAAS,SAAS;AACzB,cAAI,CAAC,SAAS;AACZ,mBAAO;AAAA,UACT;AACA,cAAI,SAAS;AAEb,cAAI,mBAAmB,OAAO;AAC5B,mBAAO,QAAQ,YAAY,OAAO;AAAA,UACpC,WAAW,OAAO,YAAY,YAAY,CAAC,QAAQ,MAAM;AACvD,mBAAO,QAAQ,aAAa,OAAO;AAAA,UACrC,WAAW,QAAQ,MAAM;AACvB,gBAAI,cAAc,QAAQ,WAAW,QAAQ,IAAI;AACjD,gBAAI,aAAa;AACf,qBAAO,YAAY,OAAO;AAAA,YAC5B,OAAO;AACL,oBAAM,MAAM,2BAA2B,QAAQ,IAAI;AAAA,YACrD;AAAA,UACF,OAAO;AACL,kBAAM,MAAM,eAAe;AAAA,UAC7B;AAAA,QACF;AAAA,MAEF;AAEA,aAAO,SAAS,MAAM;AACpB,eAAO,QAAQ,MAAM,IAAI;AAAA,MAC3B;AAAA,IACF,EAAG;AAMH,QAAI,iBAAkB,kBAAkB,CAAC;AAEzC,mBAAe,QAAS,2BAAW;AAEjC,UAAI,SAAS;AAAA,QACX,gBAAgB;AAAA,QAChB,yBAAyB;AAAA,QACzB,gBAAgB;AAAA,QAChB,yBAAyB;AAAA,QACzB,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,QACP,UAAU;AAAA,QACV,cAAc;AAAA,QACd,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,QACX,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAEA,UAAI,QAAQ;AAEZ,eAAS,MAAM,KAAK;AAClB,YAAI,MAAM,IAAI,MAAM,QAAQ,OAAO,GAAG;AACtC,YAAI,SAAS;AACb,cAAM;AAAA,MACR;AAEA,eAAS,SAAS;AAChB,YAAI,MAAM,qBAAqB;AAE/B,YAAI,MAAM,SAAS,GAAG;AACpB,gBAAM,uBAAuB;AAAA,QAC/B;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,uBAAuB;AAC9B,eAAO,aAAa,eAAe;AAAA,MACrC;AAEA,eAAS,kBAAkB;AACzB,eAAO;AAAA,UACC;AAAA,UACA,OAAO;AAAA,UACP;AAAA,QAAsB,KAExB;AAAA,UACE;AAAA,UACA,OAAO;AAAA,UACP;AAAA,QAAsB,KAExB;AAAA,UACE;AAAA,UACA,OAAO;AAAA,UACP;AAAA,QAA2B,KAE7B;AAAA,UACE;AAAA,UACA,OAAO;AAAA,UACP;AAAA,QAA2B;AAAA,MACrC;AAEA,eAAS,cAAc,cAAc,SAAS,oBAAoB;AAChE,eAAO,UAAU,SAAS,SAAS,UAAU;AAE3C,cAAI,cAAc,mBAAmB;AACrC,cAAI,aAAa;AACf,gBAAI,CAAC,KAAK,OAAO,KAAK,GAAG;AACvB,oBAAM,kCAAkC;AAAA,YAC1C;AAAA,UACF;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,YACA,YAAY,aAAa,cAAc;AAAA,UACzC;AAAA,QACF,CAAC;AAAA,MACH;AAEA,eAAS,UAAU,SAAS,UAAU;AACpC,YAAI,WAAW,KAAK,OAAO;AAE3B,YAAI,UAAU;AACZ,cAAI,CAAC,KAAK,OAAO,SAAS,GAAG;AAC3B,kBAAM,WAAW;AAAA,UACnB;AAEA,cAAI,SAAS,SAAS,QAAQ;AAE9B,cAAI,CAAC,KAAK,OAAO,OAAO,GAAG;AACzB,kBAAM,WAAW;AAAA,UACnB;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,yBAAyB;AAEhC,YAAI,eAAe,kBAAkB;AACrC,YAAI,cAAc;AAChB,iBAAO;AAAA,QACT;AAGA,YAAI,kBAAkB,MAAM,oBAAoB,OAAO,kBAAkB,CAAC;AAC1E,YAAI,iBAAiB;AAEnB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO,gBAAgB;AAAA,UACzB;AAAA,QACF;AAGA,eAAO,WAAW;AAAA,MACpB;AAEA,eAAS,oBAAoB;AAC3B,eAAO,MAAM,eAAe,OAAO,cAAc,CAAC;AAAA,MACpD;AAEA,eAAS,aAAa;AACpB,eAAO,MAAM,WAAW,OAAO,YAAY,CAAC,KAC1C,MAAM,WAAW,OAAO,aAAa,CAAC;AAAA,MAC1C;AAEA,eAAS,8BAA8B;AACrC,YAAI,oBACA,oBAAoB,uBAAuB,GAC3C;AAEJ,YAAI,mBAAmB;AACrB,+BAAqB,CAAC;AACtB,6BAAmB,KAAK,iBAAiB;AAEzC,2BAAiB;AACjB,cAAI,KAAK,OAAO,KAAK,GAAG;AACtB,gCAAoB,uBAAuB;AAC3C,gBAAI,mBAAmB;AACrB,iCAAmB,KAAK,iBAAiB;AAAA,YAC3C,OAAO;AACL,sBAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,yBAAyB;AAChC,YAAI,aAAa,YAAY,KAC3B,aAAa;AAEf,YAAI,YAAY;AACd,qBAAW,KAAK,gBAAgB;AAAA,QAClC,OAAO;AACL,cAAI,SAAS,mBAAmB;AAChC,cAAI,QAAQ;AACV,yBAAa;AACb,gBAAI,aAAa,gBAAgB;AACjC,gBAAI,YAAY;AACd,yBAAW,KAAK;AAAA,YAClB;AAAA,UACF,OAAO;AAEL,gBAAI,aAAa,gBAAgB;AACjC,gBAAI,YAAY;AACd,2BAAa;AAAA,gBACX,MAAM;AAAA,gBACN,IAAI;AAAA,cACN;AAAA,YACF,OAAO;AACL,kBAAI,kBAAkB,iBAAiB;AACvC,kBAAI,iBAAiB;AACnB,6BAAa;AAAA,kBACX,MAAM;AAAA,kBACN,IAAI;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,cAAc;AACrB,YAAI,SAAS,MAAM,SAAS,cAAc,CAAC;AAE3C,YAAI,QAAQ;AACV,iBAAO,QAAQ,YAAY,KAAK,mBAAmB;AAAA,QACrD;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,eAAe;AACtB,YAAI,UAAU,MAAM,SAAS,eAAe,CAAC;AAE7C,YAAI,SAAS;AACX,kBAAQ,QAAQ,iBAAiB,KAAK,cAAc,KAAK,mBAAmB;AAAA,QAC9E;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,qBAAqB;AAC5B,eAAO,MAAM,kBAAkB,OAAO,gBAAgB,CAAC;AAAA,MACzD;AAEA,eAAS,kBAAkB;AACzB,YAAI,MAAM,YAAY,OAAO,CAAC,GAAG;AAC/B,cAAI,cAAc,iBAAiB;AAEnC,cAAI,CAAC,aAAa;AAChB,kBAAM,2BAA2B;AAAA,UACnC;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,mBAAmB;AAC1B,YAAI,WAAW,iBAAiB;AAEhC,YAAI,SAAS,KAAK,SAAS,GAAG;AAC5B,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,eAAS,mBAAmB;AAC1B,eAAO;AAAA,UACL,GAAG,cAAc;AAAA,UACjB,GAAG,cAAc;AAAA,QACnB;AAAA,MACF;AAEA,eAAS,aAAa,SAAS;AAC7B,YAAI,WAAW,QAAQ,GACrB,SAAS,CAAC;AAEZ,YAAI,UAAU;AACZ,iBAAO,KAAK,QAAQ;AACpB,iBAAO,KAAK,OAAO,KAAK,GAAG;AACzB,uBAAW,QAAQ;AACnB,gBAAI,UAAU;AACZ,qBAAO,KAAK,QAAQ;AAAA,YACtB,OAAO;AACL,oBAAM,iBAAiB;AAAA,YACzB;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,iBAAiB;AACxB,YAAI,QAAQ,WAAW;AAEvB,YAAI,CAAC,OAAO;AACV,gBAAM,2BAA2B;AAAA,QACnC;AAEA,cAAM,SAAS,cAAc;AAC7B,eAAO;AAAA,MACT;AAEA,eAAS,aAAa;AACpB,eAAO,cAAc,KACnB,eAAe,KACf,cAAc,KACd,eAAe,KACf,cAAc,KACd,cAAc,KACd,kBAAkB;AAAA,MACtB;AAEA,eAAS,oBAAoB;AAC3B,eAAO,MAAM,WAAW,OAAO,cAAc,CAAC;AAAA,MAChD;AAEA,eAAS,gBAAgB;AACvB,eAAO,MAAM,OAAO,OAAO,UAAU,CAAC;AAAA,MACxC;AAEA,eAAS,gBAAgB;AACvB,eAAO,UAAU,OAAO,UAAU,WAAW;AAC3C,iBAAQ;AAAA,YACN,MAAM;AAAA,YACN,OAAO,aAAa,WAAW;AAAA,UACjC;AAAA,QACF,CAAC;AAAA,MACH;AAEA,eAAS,iBAAiB;AACxB,eAAO,UAAU,OAAO,WAAW,WAAW;AAC5C,iBAAQ;AAAA,YACN,MAAM;AAAA,YACN,OAAO,aAAa,WAAW;AAAA,UACjC;AAAA,QACF,CAAC;AAAA,MACH;AAEA,eAAS,gBAAgB;AACvB,eAAO,UAAU,OAAO,UAAU,WAAY;AAC5C,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO,kBAAkB;AAAA,UAC3B;AAAA,QACF,CAAC;AAAA,MACH;AAEA,eAAS,gBAAgB;AACvB,eAAO,UAAU,OAAO,UAAU,WAAW;AAE3C,cAAI,YAAY,KAAK,OAAO,eAAe;AAC3C,cAAI,WAAW;AACb,kBAAM,iGAAiG;AAAA,UACzG;AAEA,cAAI,MAAM,YAAY;AACtB,eAAK,OAAO,KAAK;AACjB,cAAI,WAAW,KAAK,OAAO,eAAe;AAC1C,cAAI,MAAM,WAAW,SAAS,CAAC,IAAI;AACnC,eAAK,OAAO,KAAK;AACjB,qBAAW,KAAK,OAAO,eAAe;AACtC,cAAI,QAAQ,WAAW,SAAS,CAAC,IAAI;AACrC,cAAI,CAAC,OAAO,CAAC,OAAO;AAClB,kBAAM,+DAA+D;AAAA,UACvE;AACA,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO,CAAC,KAAK,KAAK,KAAK;AAAA,UACzB;AAAA,QACF,CAAC;AAAA,MACH;AAEA,eAAS,iBAAiB;AACxB,eAAO,UAAU,OAAO,WAAW,WAAW;AAC5C,cAAI,MAAM,YAAY;AACtB,eAAK,OAAO,KAAK;AACjB,cAAI,WAAW,KAAK,OAAO,eAAe;AAC1C,cAAI,MAAM,WAAW,SAAS,CAAC,IAAI;AACnC,eAAK,OAAO,KAAK;AACjB,qBAAW,KAAK,OAAO,eAAe;AACtC,cAAI,QAAQ,WAAW,SAAS,CAAC,IAAI;AACrC,eAAK,OAAO,KAAK;AACjB,cAAI,QAAQ,YAAY;AACxB,cAAI,CAAC,OAAO,CAAC,OAAO;AAClB,kBAAM,gEAAgE;AAAA,UACxE;AACA,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO,CAAC,KAAK,KAAK,OAAO,KAAK;AAAA,UAChC;AAAA,QACF,CAAC;AAAA,MACH;AAEA,eAAS,kBAAkB;AACzB,YAAI,WAAW,KAAK,OAAO,eAAe;AAC1C,eAAO,WAAW,SAAS,CAAC,IAAI;AAAA,MAClC;AAEA,eAAS,oBAAoB;AAC3B,eAAO,KAAK,OAAO,YAAY,EAAE,CAAC;AAAA,MACpC;AAEA,eAAS,cAAc;AACrB,eAAO,KAAK,OAAO,MAAM,EAAE,CAAC;AAAA,MAC9B;AAEA,eAAS,gBAAgB;AACvB,eAAO,MAAM,KAAK,OAAO,iBAAiB,CAAC,KACzC,qBAAqB,KACrB,UAAU,KACV,YAAY;AAAA,MAChB;AAEA,eAAS,uBAAuB;AAC9B,eAAO,MAAM,oBAAoB,OAAO,kBAAkB,CAAC;AAAA,MAC7D;AAEA,eAAS,YAAY;AACnB,eAAO,UAAU,OAAO,WAAW,WAAW;AAC5C,cAAI,iBAAiB;AACrB,cAAIA,KAAI;AAGR,iBAAO,iBAAiB,KAAKA,KAAI,MAAM,QAAQ;AAC7C,gBAAI,OAAO,MAAM,OAAOA,EAAC;AACzB,gBAAI,SAAS,KAAK;AAChB;AAAA,YACF,WAAW,SAAS,KAAK;AACvB;AAAA,YACF;AACA,YAAAA;AAAA,UACF;AAGA,cAAI,iBAAiB,GAAG;AACtB,kBAAM,kDAAkD;AAAA,UAC1D;AAGA,cAAI,cAAc,MAAM,UAAU,GAAGA,KAAI,CAAC;AAG1C,kBAAQA,KAAI,CAAC;AAEb,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH;AAEA,eAAS,cAAc;AACrB,eAAO,MAAM,MAAM,OAAO,YAAY,CAAC,KACrC,MAAM,MAAM,OAAO,SAAS,CAAC;AAAA,MACjC;AAEA,eAAS,MAAM,MAAM,SAAS,cAAc;AAC1C,YAAI,WAAW,KAAK,OAAO;AAC3B,YAAI,UAAU;AACZ,iBAAO;AAAA,YACL;AAAA,YACA,OAAO,SAAS,YAAY;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAEA,eAAS,KAAK,QAAQ;AACpB,YAAI,UACA;AAEJ,wBAAgB,eAAe,KAAK,KAAK;AACzC,YAAI,eAAe;AACf,kBAAQ,cAAc,CAAC,EAAE,MAAM;AAAA,QACnC;AAEA,mBAAW,OAAO,KAAK,KAAK;AAC5B,YAAI,UAAU;AACV,kBAAQ,SAAS,CAAC,EAAE,MAAM;AAAA,QAC9B;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,QAAQ,MAAM;AACrB,gBAAQ,MAAM,OAAO,IAAI;AAAA,MAC3B;AAEA,aAAO,SAAS,MAAM;AACpB,gBAAQ,KAAK,SAAS,EAAE,KAAK;AAE7B,YAAI,MAAM,SAAS,GAAG,GAAG;AACvB,kBAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,QAC3B;AACA,eAAO,OAAO;AAAA,MAChB;AAAA,IACF,EAAG;AAEH,YAAQ,QAAQ,eAAe;AAC/B,YAAQ,YAAY,eAAe;AAAA;AAAA;;;ACrqBnC,SAAS,QAAQ,KAAK;AACpB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,MAAK;AAClG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,MAAK;AACjB,WAAOA,QAAO,cAAc,OAAO,UAAUA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,EAC1H,GAAG,QAAQ,GAAG;AAChB;AAKA,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,SAAS,UAAU,OAAO,MAAM;AAC9B,UAAQ,QAAQ,QAAQ;AACxB,SAAO,QAAQ,CAAC;AAGhB,MAAI,iBAAiB,WAAW;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,EAAE,gBAAgB,YAAY;AAChC,WAAO,IAAI,UAAU,OAAO,IAAI;AAAA,EAClC;AACA,MAAI,MAAM,WAAW,KAAK;AAC1B,OAAK,iBAAiB,OAAO,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,UAAU,KAAK,MAAM,MAAM,KAAK,EAAE,IAAI,KAAK,KAAK,UAAU,KAAK,UAAU,IAAI;AACnL,OAAK,gBAAgB,KAAK;AAM1B,MAAI,KAAK,KAAK,EAAG,MAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC7C,MAAI,KAAK,KAAK,EAAG,MAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC7C,MAAI,KAAK,KAAK,EAAG,MAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC7C,OAAK,MAAM,IAAI;AACjB;AACA,UAAU,YAAY;AAAA,EACpB,QAAQ,SAAS,SAAS;AACxB,WAAO,KAAK,cAAc,IAAI;AAAA,EAChC;AAAA,EACA,SAAS,SAAS,UAAU;AAC1B,WAAO,CAAC,KAAK,OAAO;AAAA,EACtB;AAAA,EACA,SAAS,SAAS,UAAU;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB,SAAS,mBAAmB;AAC5C,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW,SAAS,YAAY;AAC9B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe,SAAS,gBAAgB;AAEtC,QAAI,MAAM,KAAK,MAAM;AACrB,YAAQ,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,OAAO;AAAA,EACrD;AAAA,EACA,cAAc,SAAS,eAAe;AAEpC,QAAI,MAAM,KAAK,MAAM;AACrB,QAAI,OAAO,OAAO,OAAOC,IAAGC,IAAGC;AAC/B,YAAQ,IAAI,IAAI;AAChB,YAAQ,IAAI,IAAI;AAChB,YAAQ,IAAI,IAAI;AAChB,QAAI,SAAS,QAAS,CAAAF,KAAI,QAAQ;AAAA,QAAW,CAAAA,KAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AACtF,QAAI,SAAS,QAAS,CAAAC,KAAI,QAAQ;AAAA,QAAW,CAAAA,KAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AACtF,QAAI,SAAS,QAAS,CAAAC,KAAI,QAAQ;AAAA,QAAW,CAAAA,KAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AACtF,WAAO,SAASF,KAAI,SAASC,KAAI,SAASC;AAAA,EAC5C;AAAA,EACA,UAAU,SAAS,SAAS,OAAO;AACjC,SAAK,KAAK,WAAW,KAAK;AAC1B,SAAK,UAAU,KAAK,MAAM,MAAM,KAAK,EAAE,IAAI;AAC3C,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,QAAI,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAC5C,WAAO;AAAA,MACL,GAAG,IAAI,IAAI;AAAA,MACX,GAAG,IAAI;AAAA,MACP,GAAG,IAAI;AAAA,MACP,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA,EACA,aAAa,SAAS,cAAc;AAClC,QAAI,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAC5C,QAAIC,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG,GAC5BC,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG,GAC1BC,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC5B,WAAO,KAAK,MAAM,IAAI,SAASF,KAAI,OAAOC,KAAI,QAAQC,KAAI,OAAO,UAAUF,KAAI,OAAOC,KAAI,QAAQC,KAAI,QAAQ,KAAK,UAAU;AAAA,EAC/H;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,QAAI,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAC5C,WAAO;AAAA,MACL,GAAG,IAAI,IAAI;AAAA,MACX,GAAG,IAAI;AAAA,MACP,GAAG,IAAI;AAAA,MACP,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA,EACA,aAAa,SAAS,cAAc;AAClC,QAAI,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAC5C,QAAIF,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG,GAC5BC,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG,GAC1BE,KAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC5B,WAAO,KAAK,MAAM,IAAI,SAASH,KAAI,OAAOC,KAAI,QAAQE,KAAI,OAAO,UAAUH,KAAI,OAAOC,KAAI,QAAQE,KAAI,QAAQ,KAAK,UAAU;AAAA,EAC/H;AAAA,EACA,OAAO,SAAS,MAAM,YAAY;AAChC,WAAO,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,UAAU;AAAA,EACvD;AAAA,EACA,aAAa,SAAS,YAAY,YAAY;AAC5C,WAAO,MAAM,KAAK,MAAM,UAAU;AAAA,EACpC;AAAA,EACA,QAAQ,SAAS,OAAO,YAAY;AAClC,WAAO,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,UAAU;AAAA,EACjE;AAAA,EACA,cAAc,SAAS,aAAa,YAAY;AAC9C,WAAO,MAAM,KAAK,OAAO,UAAU;AAAA,EACrC;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,WAAO;AAAA,MACL,GAAG,KAAK,MAAM,KAAK,EAAE;AAAA,MACrB,GAAG,KAAK,MAAM,KAAK,EAAE;AAAA,MACrB,GAAG,KAAK,MAAM,KAAK,EAAE;AAAA,MACrB,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA,EACA,aAAa,SAAS,cAAc;AAClC,WAAO,KAAK,MAAM,IAAI,SAAS,KAAK,MAAM,KAAK,EAAE,IAAI,OAAO,KAAK,MAAM,KAAK,EAAE,IAAI,OAAO,KAAK,MAAM,KAAK,EAAE,IAAI,MAAM,UAAU,KAAK,MAAM,KAAK,EAAE,IAAI,OAAO,KAAK,MAAM,KAAK,EAAE,IAAI,OAAO,KAAK,MAAM,KAAK,EAAE,IAAI,OAAO,KAAK,UAAU;AAAA,EACvO;AAAA,EACA,iBAAiB,SAAS,kBAAkB;AAC1C,WAAO;AAAA,MACL,GAAG,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;AAAA,MAC7C,GAAG,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;AAAA,MAC7C,GAAG,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;AAAA,MAC7C,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA,EACA,uBAAuB,SAAS,wBAAwB;AACtD,WAAO,KAAK,MAAM,IAAI,SAAS,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,OAAO,UAAU,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK,UAAU;AAAA,EACrW;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,QAAI,KAAK,OAAO,GAAG;AACjB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,KAAK,GAAG;AACf,aAAO;AAAA,IACT;AACA,WAAO,SAAS,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK;AAAA,EAChE;AAAA,EACA,UAAU,SAAS,SAAS,aAAa;AACvC,QAAI,aAAa,MAAM,cAAc,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AACvE,QAAI,mBAAmB;AACvB,QAAI,eAAe,KAAK,gBAAgB,uBAAuB;AAC/D,QAAI,aAAa;AACf,UAAIF,KAAI,UAAU,WAAW;AAC7B,yBAAmB,MAAM,cAAcA,GAAE,IAAIA,GAAE,IAAIA,GAAE,IAAIA,GAAE,EAAE;AAAA,IAC/D;AACA,WAAO,gDAAgD,eAAe,mBAAmB,aAAa,kBAAkB,mBAAmB;AAAA,EAC7I;AAAA,EACA,UAAU,SAAS,SAAS,QAAQ;AAClC,QAAI,YAAY,CAAC,CAAC;AAClB,aAAS,UAAU,KAAK;AACxB,QAAI,kBAAkB;AACtB,QAAI,WAAW,KAAK,KAAK,KAAK,KAAK,MAAM;AACzC,QAAI,mBAAmB,CAAC,aAAa,aAAa,WAAW,SAAS,WAAW,UAAU,WAAW,UAAU,WAAW,UAAU,WAAW,UAAU,WAAW;AACrK,QAAI,kBAAkB;AAGpB,UAAI,WAAW,UAAU,KAAK,OAAO,GAAG;AACtC,eAAO,KAAK,OAAO;AAAA,MACrB;AACA,aAAO,KAAK,YAAY;AAAA,IAC1B;AACA,QAAI,WAAW,OAAO;AACpB,wBAAkB,KAAK,YAAY;AAAA,IACrC;AACA,QAAI,WAAW,QAAQ;AACrB,wBAAkB,KAAK,sBAAsB;AAAA,IAC/C;AACA,QAAI,WAAW,SAAS,WAAW,QAAQ;AACzC,wBAAkB,KAAK,YAAY;AAAA,IACrC;AACA,QAAI,WAAW,QAAQ;AACrB,wBAAkB,KAAK,YAAY,IAAI;AAAA,IACzC;AACA,QAAI,WAAW,QAAQ;AACrB,wBAAkB,KAAK,aAAa,IAAI;AAAA,IAC1C;AACA,QAAI,WAAW,QAAQ;AACrB,wBAAkB,KAAK,aAAa;AAAA,IACtC;AACA,QAAI,WAAW,QAAQ;AACrB,wBAAkB,KAAK,OAAO;AAAA,IAChC;AACA,QAAI,WAAW,OAAO;AACpB,wBAAkB,KAAK,YAAY;AAAA,IACrC;AACA,QAAI,WAAW,OAAO;AACpB,wBAAkB,KAAK,YAAY;AAAA,IACrC;AACA,WAAO,mBAAmB,KAAK,YAAY;AAAA,EAC7C;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,WAAO,UAAU,KAAK,SAAS,CAAC;AAAA,EAClC;AAAA,EACA,oBAAoB,SAAS,mBAAmBG,KAAI,MAAM;AACxD,QAAI,QAAQA,IAAG,MAAM,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,MAAM,KAAK,IAAI,CAAC,CAAC;AAC7D,SAAK,KAAK,MAAM;AAChB,SAAK,KAAK,MAAM;AAChB,SAAK,KAAK,MAAM;AAChB,SAAK,SAAS,MAAM,EAAE;AACtB,WAAO;AAAA,EACT;AAAA,EACA,SAAS,SAAS,UAAU;AAC1B,WAAO,KAAK,mBAAmB,UAAU,SAAS;AAAA,EACpD;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,WAAO,KAAK,mBAAmB,WAAW,SAAS;AAAA,EACrD;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,WAAO,KAAK,mBAAmB,SAAS,SAAS;AAAA,EACnD;AAAA,EACA,YAAY,SAAS,aAAa;AAChC,WAAO,KAAK,mBAAmB,aAAa,SAAS;AAAA,EACvD;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,WAAO,KAAK,mBAAmB,WAAW,SAAS;AAAA,EACrD;AAAA,EACA,WAAW,SAAS,YAAY;AAC9B,WAAO,KAAK,mBAAmB,YAAY,SAAS;AAAA,EACtD;AAAA,EACA,MAAM,SAAS,OAAO;AACpB,WAAO,KAAK,mBAAmB,OAAO,SAAS;AAAA,EACjD;AAAA,EACA,mBAAmB,SAAS,kBAAkBA,KAAI,MAAM;AACtD,WAAOA,IAAG,MAAM,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,MAAM,KAAK,IAAI,CAAC,CAAC;AAAA,EAC1D;AAAA,EACA,WAAW,SAAS,YAAY;AAC9B,WAAO,KAAK,kBAAkB,YAAY,SAAS;AAAA,EACrD;AAAA,EACA,YAAY,SAAS,aAAa;AAChC,WAAO,KAAK,kBAAkB,aAAa,SAAS;AAAA,EACtD;AAAA,EACA,eAAe,SAAS,gBAAgB;AACtC,WAAO,KAAK,kBAAkB,gBAAgB,SAAS;AAAA,EACzD;AAAA,EACA,iBAAiB,SAAS,kBAAkB;AAC1C,WAAO,KAAK,kBAAkB,kBAAkB,SAAS;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,SAAS,QAAQ;AACtB,WAAO,KAAK,kBAAkB,QAAQ,CAAC,CAAC,CAAC;AAAA,EAC3C;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,WAAO,KAAK,kBAAkB,QAAQ,CAAC,CAAC,CAAC;AAAA,EAC3C;AACF;AAIA,UAAU,YAAY,SAAU,OAAO,MAAM;AAC3C,MAAI,QAAQ,KAAK,KAAK,UAAU;AAC9B,QAAI,WAAW,CAAC;AAChB,aAASC,MAAK,OAAO;AACnB,UAAI,MAAM,eAAeA,EAAC,GAAG;AAC3B,YAAIA,OAAM,KAAK;AACb,mBAASA,EAAC,IAAI,MAAMA,EAAC;AAAA,QACvB,OAAO;AACL,mBAASA,EAAC,IAAI,oBAAoB,MAAMA,EAAC,CAAC;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AACA,YAAQ;AAAA,EACV;AACA,SAAO,UAAU,OAAO,IAAI;AAC9B;AAiBA,SAAS,WAAW,OAAO;AACzB,MAAI,MAAM;AAAA,IACR,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,MAAIC,KAAI;AACR,MAAIL,KAAI;AACR,MAAIC,KAAI;AACR,MAAIC,KAAI;AACR,MAAI,KAAK;AACT,MAAI,SAAS;AACb,MAAI,OAAO,SAAS,UAAU;AAC5B,YAAQ,oBAAoB,KAAK;AAAA,EACnC;AACA,MAAI,QAAQ,KAAK,KAAK,UAAU;AAC9B,QAAI,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACjF,YAAM,SAAS,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACxC,WAAK;AACL,eAAS,OAAO,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,MAAM,SAAS;AAAA,IACzD,WAAW,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACxF,MAAAF,KAAI,oBAAoB,MAAM,CAAC;AAC/B,MAAAC,KAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAGD,IAAGC,EAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACX,WAAW,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACxF,MAAAD,KAAI,oBAAoB,MAAM,CAAC;AAC/B,MAAAE,KAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAGF,IAAGE,EAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACX;AACA,QAAI,MAAM,eAAe,GAAG,GAAG;AAC7B,MAAAG,KAAI,MAAM;AAAA,IACZ;AAAA,EACF;AACA,EAAAA,KAAI,WAAWA,EAAC;AAChB,SAAO;AAAA,IACL;AAAA,IACA,QAAQ,MAAM,UAAU;AAAA,IACxB,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAGA;AAAA,EACL;AACF;AAaA,SAAS,SAASC,IAAGC,IAAGC,IAAG;AACzB,SAAO;AAAA,IACL,GAAG,QAAQF,IAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQC,IAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQC,IAAG,GAAG,IAAI;AAAA,EACvB;AACF;AAMA,SAAS,SAASF,IAAGC,IAAGC,IAAG;AACzB,EAAAF,KAAI,QAAQA,IAAG,GAAG;AAClB,EAAAC,KAAI,QAAQA,IAAG,GAAG;AAClB,EAAAC,KAAI,QAAQA,IAAG,GAAG;AAClB,MAAIC,OAAM,KAAK,IAAIH,IAAGC,IAAGC,EAAC,GACxBE,OAAM,KAAK,IAAIJ,IAAGC,IAAGC,EAAC;AACxB,MAAIT,IACFC,IACAE,MAAKO,OAAMC,QAAO;AACpB,MAAID,QAAOC,MAAK;AACd,IAAAX,KAAIC,KAAI;AAAA,EACV,OAAO;AACL,QAAIW,KAAIF,OAAMC;AACd,IAAAV,KAAIE,KAAI,MAAMS,MAAK,IAAIF,OAAMC,QAAOC,MAAKF,OAAMC;AAC/C,YAAQD,MAAK;AAAA,MACX,KAAKH;AACH,QAAAP,MAAKQ,KAAIC,MAAKG,MAAKJ,KAAIC,KAAI,IAAI;AAC/B;AAAA,MACF,KAAKD;AACH,QAAAR,MAAKS,KAAIF,MAAKK,KAAI;AAClB;AAAA,MACF,KAAKH;AACH,QAAAT,MAAKO,KAAIC,MAAKI,KAAI;AAClB;AAAA,IACJ;AACA,IAAAZ,MAAK;AAAA,EACP;AACA,SAAO;AAAA,IACL,GAAGA;AAAA,IACH,GAAGC;AAAA,IACH,GAAGE;AAAA,EACL;AACF;AAMA,SAAS,SAASH,IAAGC,IAAGE,IAAG;AACzB,MAAII,IAAGC,IAAGC;AACV,EAAAT,KAAI,QAAQA,IAAG,GAAG;AAClB,EAAAC,KAAI,QAAQA,IAAG,GAAG;AAClB,EAAAE,KAAI,QAAQA,IAAG,GAAG;AAClB,WAAS,QAAQU,IAAGC,IAAGC,IAAG;AACxB,QAAIA,KAAI,EAAG,CAAAA,MAAK;AAChB,QAAIA,KAAI,EAAG,CAAAA,MAAK;AAChB,QAAIA,KAAI,IAAI,EAAG,QAAOF,MAAKC,KAAID,MAAK,IAAIE;AACxC,QAAIA,KAAI,IAAI,EAAG,QAAOD;AACtB,QAAIC,KAAI,IAAI,EAAG,QAAOF,MAAKC,KAAID,OAAM,IAAI,IAAIE,MAAK;AAClD,WAAOF;AAAA,EACT;AACA,MAAIZ,OAAM,GAAG;AACX,IAAAM,KAAIC,KAAIC,KAAIN;AAAA,EACd,OAAO;AACL,QAAIW,KAAIX,KAAI,MAAMA,MAAK,IAAIF,MAAKE,KAAIF,KAAIE,KAAIF;AAC5C,QAAIY,KAAI,IAAIV,KAAIW;AAChB,IAAAP,KAAI,QAAQM,IAAGC,IAAGd,KAAI,IAAI,CAAC;AAC3B,IAAAQ,KAAI,QAAQK,IAAGC,IAAGd,EAAC;AACnB,IAAAS,KAAI,QAAQI,IAAGC,IAAGd,KAAI,IAAI,CAAC;AAAA,EAC7B;AACA,SAAO;AAAA,IACL,GAAGO,KAAI;AAAA,IACP,GAAGC,KAAI;AAAA,IACP,GAAGC,KAAI;AAAA,EACT;AACF;AAMA,SAAS,SAASF,IAAGC,IAAGC,IAAG;AACzB,EAAAF,KAAI,QAAQA,IAAG,GAAG;AAClB,EAAAC,KAAI,QAAQA,IAAG,GAAG;AAClB,EAAAC,KAAI,QAAQA,IAAG,GAAG;AAClB,MAAIC,OAAM,KAAK,IAAIH,IAAGC,IAAGC,EAAC,GACxBE,OAAM,KAAK,IAAIJ,IAAGC,IAAGC,EAAC;AACxB,MAAIT,IACFC,IACAC,KAAIQ;AACN,MAAIE,KAAIF,OAAMC;AACd,EAAAV,KAAIS,SAAQ,IAAI,IAAIE,KAAIF;AACxB,MAAIA,QAAOC,MAAK;AACd,IAAAX,KAAI;AAAA,EACN,OAAO;AACL,YAAQU,MAAK;AAAA,MACX,KAAKH;AACH,QAAAP,MAAKQ,KAAIC,MAAKG,MAAKJ,KAAIC,KAAI,IAAI;AAC/B;AAAA,MACF,KAAKD;AACH,QAAAR,MAAKS,KAAIF,MAAKK,KAAI;AAClB;AAAA,MACF,KAAKH;AACH,QAAAT,MAAKO,KAAIC,MAAKI,KAAI;AAClB;AAAA,IACJ;AACA,IAAAZ,MAAK;AAAA,EACP;AACA,SAAO;AAAA,IACL,GAAGA;AAAA,IACH,GAAGC;AAAA,IACH,GAAGC;AAAA,EACL;AACF;AAMA,SAAS,SAASF,IAAGC,IAAGC,IAAG;AACzB,EAAAF,KAAI,QAAQA,IAAG,GAAG,IAAI;AACtB,EAAAC,KAAI,QAAQA,IAAG,GAAG;AAClB,EAAAC,KAAI,QAAQA,IAAG,GAAG;AAClB,MAAIG,KAAI,KAAK,MAAML,EAAC,GAClBgB,KAAIhB,KAAIK,IACRQ,KAAIX,MAAK,IAAID,KACba,KAAIZ,MAAK,IAAIc,KAAIf,KACjBc,KAAIb,MAAK,KAAK,IAAIc,MAAKf,KACvB,MAAMI,KAAI,GACVE,KAAI,CAACL,IAAGY,IAAGD,IAAGA,IAAGE,IAAGb,EAAC,EAAE,GAAG,GAC1BM,KAAI,CAACO,IAAGb,IAAGA,IAAGY,IAAGD,IAAGA,EAAC,EAAE,GAAG,GAC1BJ,KAAI,CAACI,IAAGA,IAAGE,IAAGb,IAAGA,IAAGY,EAAC,EAAE,GAAG;AAC5B,SAAO;AAAA,IACL,GAAGP,KAAI;AAAA,IACP,GAAGC,KAAI;AAAA,IACP,GAAGC,KAAI;AAAA,EACT;AACF;AAMA,SAAS,SAASF,IAAGC,IAAGC,IAAG,YAAY;AACrC,MAAI,MAAM,CAAC,KAAK,KAAK,MAAMF,EAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAMC,EAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAMC,EAAC,EAAE,SAAS,EAAE,CAAC,CAAC;AAG/G,MAAI,cAAc,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,GAAG;AACtI,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EAC9D;AACA,SAAO,IAAI,KAAK,EAAE;AACpB;AAMA,SAAS,UAAUF,IAAGC,IAAGC,IAAGH,IAAG,YAAY;AACzC,MAAI,MAAM,CAAC,KAAK,KAAK,MAAMC,EAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAMC,EAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAMC,EAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,oBAAoBH,EAAC,CAAC,CAAC;AAG7I,MAAI,cAAc,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,GAAG;AAC9K,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EACjF;AACA,SAAO,IAAI,KAAK,EAAE;AACpB;AAKA,SAAS,cAAcC,IAAGC,IAAGC,IAAGH,IAAG;AACjC,MAAI,MAAM,CAAC,KAAK,oBAAoBA,EAAC,CAAC,GAAG,KAAK,KAAK,MAAMC,EAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAMC,EAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAMC,EAAC,EAAE,SAAS,EAAE,CAAC,CAAC;AAC7I,SAAO,IAAI,KAAK,EAAE;AACpB;AAIA,UAAU,SAAS,SAAU,QAAQ,QAAQ;AAC3C,MAAI,CAAC,UAAU,CAAC,OAAQ,QAAO;AAC/B,SAAO,UAAU,MAAM,EAAE,YAAY,KAAK,UAAU,MAAM,EAAE,YAAY;AAC1E;AACA,UAAU,SAAS,WAAY;AAC7B,SAAO,UAAU,UAAU;AAAA,IACzB,GAAG,KAAK,OAAO;AAAA,IACf,GAAG,KAAK,OAAO;AAAA,IACf,GAAG,KAAK,OAAO;AAAA,EACjB,CAAC;AACH;AAOA,SAAS,YAAY,OAAO,QAAQ;AAClC,WAAS,WAAW,IAAI,IAAI,UAAU;AACtC,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,KAAK,SAAS;AAClB,MAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,SAAO,UAAU,GAAG;AACtB;AACA,SAAS,UAAU,OAAO,QAAQ;AAChC,WAAS,WAAW,IAAI,IAAI,UAAU;AACtC,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,KAAK,SAAS;AAClB,MAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,SAAO,UAAU,GAAG;AACtB;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,UAAU,KAAK,EAAE,WAAW,GAAG;AACxC;AACA,SAAS,SAAS,OAAO,QAAQ;AAC/B,WAAS,WAAW,IAAI,IAAI,UAAU;AACtC,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,KAAK,SAAS;AAClB,MAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,SAAO,UAAU,GAAG;AACtB;AACA,SAAS,UAAU,OAAO,QAAQ;AAChC,WAAS,WAAW,IAAI,IAAI,UAAU;AACtC,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,MAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,MAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,SAAO,UAAU,GAAG;AACtB;AACA,SAAS,QAAQ,OAAO,QAAQ;AAC9B,WAAS,WAAW,IAAI,IAAI,UAAU;AACtC,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,KAAK,SAAS;AAClB,MAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,SAAO,UAAU,GAAG;AACtB;AAIA,SAAS,MAAM,OAAO,QAAQ;AAC5B,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,OAAO,IAAI,IAAI,UAAU;AAC7B,MAAI,IAAI,MAAM,IAAI,MAAM,MAAM;AAC9B,SAAO,UAAU,GAAG;AACtB;AAOA,SAAS,YAAY,OAAO;AAC1B,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,KAAK,IAAI,IAAI,OAAO;AACxB,SAAO,UAAU,GAAG;AACtB;AACA,SAAS,OAAO,OAAO,QAAQ;AAC7B,MAAI,MAAM,MAAM,KAAK,UAAU,GAAG;AAChC,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAChE;AACA,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,SAAS,CAAC,UAAU,KAAK,CAAC;AAC9B,MAAI,OAAO,MAAM;AACjB,WAASJ,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAC/B,WAAO,KAAK,UAAU;AAAA,MACpB,IAAI,IAAI,IAAIA,KAAI,QAAQ;AAAA,MACxB,GAAG,IAAI;AAAA,MACP,GAAG,IAAI;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAIL,KAAI,IAAI;AACZ,SAAO,CAAC,UAAU,KAAK,GAAG,UAAU;AAAA,IAClC,IAAIA,KAAI,MAAM;AAAA,IACd,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT,CAAC,GAAG,UAAU;AAAA,IACZ,IAAIA,KAAI,OAAO;AAAA,IACf,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT,CAAC,CAAC;AACJ;AACA,SAAS,WAAW,OAAO,SAAS,QAAQ;AAC1C,YAAU,WAAW;AACrB,WAAS,UAAU;AACnB,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,OAAO,MAAM;AACjB,MAAI,MAAM,CAAC,UAAU,KAAK,CAAC;AAC3B,OAAK,IAAI,KAAK,IAAI,KAAK,OAAO,WAAW,KAAK,OAAO,KAAK,EAAE,WAAU;AACpE,QAAI,KAAK,IAAI,IAAI,QAAQ;AACzB,QAAI,KAAK,UAAU,GAAG,CAAC;AAAA,EACzB;AACA,SAAO;AACT;AACA,SAAS,eAAe,OAAO,SAAS;AACtC,YAAU,WAAW;AACrB,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAIA,KAAI,IAAI,GACVC,KAAI,IAAI,GACRC,KAAI,IAAI;AACV,MAAI,MAAM,CAAC;AACX,MAAI,eAAe,IAAI;AACvB,SAAO,WAAW;AAChB,QAAI,KAAK,UAAU;AAAA,MACjB,GAAGF;AAAA,MACH,GAAGC;AAAA,MACH,GAAGC;AAAA,IACL,CAAC,CAAC;AACF,IAAAA,MAAKA,KAAI,gBAAgB;AAAA,EAC3B;AACA,SAAO;AACT;AAKA,UAAU,MAAM,SAAU,QAAQ,QAAQ,QAAQ;AAChD,WAAS,WAAW,IAAI,IAAI,UAAU;AACtC,MAAI,OAAO,UAAU,MAAM,EAAE,MAAM;AACnC,MAAI,OAAO,UAAU,MAAM,EAAE,MAAM;AACnC,MAAIW,KAAI,SAAS;AACjB,MAAI,OAAO;AAAA,IACT,IAAI,KAAK,IAAI,KAAK,KAAKA,KAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAKA,KAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAKA,KAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAKA,KAAI,KAAK;AAAA,EAClC;AACA,SAAO,UAAU,IAAI;AACvB;AAQA,UAAU,cAAc,SAAU,QAAQ,QAAQ;AAChD,MAAI,KAAK,UAAU,MAAM;AACzB,MAAI,KAAK,UAAU,MAAM;AACzB,UAAQ,KAAK,IAAI,GAAG,aAAa,GAAG,GAAG,aAAa,CAAC,IAAI,SAAS,KAAK,IAAI,GAAG,aAAa,GAAG,GAAG,aAAa,CAAC,IAAI;AACrH;AAYA,UAAU,aAAa,SAAU,QAAQ,QAAQ,OAAO;AACtD,MAAI,cAAc,UAAU,YAAY,QAAQ,MAAM;AACtD,MAAI,YAAY;AAChB,QAAM;AACN,eAAa,mBAAmB,KAAK;AACrC,UAAQ,WAAW,QAAQ,WAAW,MAAM;AAAA,IAC1C,KAAK;AAAA,IACL,KAAK;AACH,YAAM,eAAe;AACrB;AAAA,IACF,KAAK;AACH,YAAM,eAAe;AACrB;AAAA,IACF,KAAK;AACH,YAAM,eAAe;AACrB;AAAA,EACJ;AACA,SAAO;AACT;AAWA,UAAU,eAAe,SAAU,WAAW,WAAW,MAAM;AAC7D,MAAI,YAAY;AAChB,MAAI,YAAY;AAChB,MAAI;AACJ,MAAI,uBAAuB,OAAO;AAClC,SAAO,QAAQ,CAAC;AAChB,0BAAwB,KAAK;AAC7B,UAAQ,KAAK;AACb,SAAO,KAAK;AACZ,WAASR,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,kBAAc,UAAU,YAAY,WAAW,UAAUA,EAAC,CAAC;AAC3D,QAAI,cAAc,WAAW;AAC3B,kBAAY;AACZ,kBAAY,UAAU,UAAUA,EAAC,CAAC;AAAA,IACpC;AAAA,EACF;AACA,MAAI,UAAU,WAAW,WAAW,WAAW;AAAA,IAC7C;AAAA,IACA;AAAA,EACF,CAAC,KAAK,CAAC,uBAAuB;AAC5B,WAAO;AAAA,EACT,OAAO;AACL,SAAK,wBAAwB;AAC7B,WAAO,UAAU,aAAa,WAAW,CAAC,QAAQ,MAAM,GAAG,IAAI;AAAA,EACjE;AACF;AAKA,IAAI,QAAQ,UAAU,QAAQ;AAAA,EAC5B,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,eAAe;AAAA,EACf,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,KAAK;AAAA,EACL,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,aAAa;AACf;AAGA,IAAI,WAAW,UAAU,WAAW,KAAK,KAAK;AAM9C,SAAS,KAAKY,IAAG;AACf,MAAI,UAAU,CAAC;AACf,WAASZ,MAAKY,IAAG;AACf,QAAIA,GAAE,eAAeZ,EAAC,GAAG;AACvB,cAAQY,GAAEZ,EAAC,CAAC,IAAIA;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,WAAWC,IAAG;AACrB,EAAAA,KAAI,WAAWA,EAAC;AAChB,MAAI,MAAMA,EAAC,KAAKA,KAAI,KAAKA,KAAI,GAAG;AAC9B,IAAAA,KAAI;AAAA,EACN;AACA,SAAOA;AACT;AAGA,SAAS,QAAQY,IAAGR,MAAK;AACvB,MAAI,eAAeQ,EAAC,EAAG,CAAAA,KAAI;AAC3B,MAAI,iBAAiB,aAAaA,EAAC;AACnC,EAAAA,KAAI,KAAK,IAAIR,MAAK,KAAK,IAAI,GAAG,WAAWQ,EAAC,CAAC,CAAC;AAG5C,MAAI,gBAAgB;AAClB,IAAAA,KAAI,SAASA,KAAIR,MAAK,EAAE,IAAI;AAAA,EAC9B;AAGA,MAAI,KAAK,IAAIQ,KAAIR,IAAG,IAAI,MAAU;AAChC,WAAO;AAAA,EACT;AAGA,SAAOQ,KAAIR,OAAM,WAAWA,IAAG;AACjC;AAGA,SAAS,QAAQ,KAAK;AACpB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC;AACrC;AAGA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,SAAS,KAAK,EAAE;AACzB;AAIA,SAAS,eAAeQ,IAAG;AACzB,SAAO,OAAOA,MAAK,YAAYA,GAAE,QAAQ,GAAG,KAAK,MAAM,WAAWA,EAAC,MAAM;AAC3E;AAGA,SAAS,aAAaA,IAAG;AACvB,SAAO,OAAOA,OAAM,YAAYA,GAAE,QAAQ,GAAG,KAAK;AACpD;AAGA,SAAS,KAAKC,IAAG;AACf,SAAOA,GAAE,UAAU,IAAI,MAAMA,KAAI,KAAKA;AACxC;AAGA,SAAS,oBAAoBD,IAAG;AAC9B,MAAIA,MAAK,GAAG;AACV,IAAAA,KAAIA,KAAI,MAAM;AAAA,EAChB;AACA,SAAOA;AACT;AAGA,SAAS,oBAAoBN,IAAG;AAC9B,SAAO,KAAK,MAAM,WAAWA,EAAC,IAAI,GAAG,EAAE,SAAS,EAAE;AACpD;AAEA,SAAS,oBAAoBZ,IAAG;AAC9B,SAAO,gBAAgBA,EAAC,IAAI;AAC9B;AACA,IAAI,WAAW,WAAY;AAEzB,MAAI,cAAc;AAGlB,MAAI,aAAa;AAGjB,MAAI,WAAW,QAAQ,aAAa,UAAU,cAAc;AAK5D,MAAI,oBAAoB,gBAAgB,WAAW,eAAe,WAAW,eAAe,WAAW;AACvG,MAAI,oBAAoB,gBAAgB,WAAW,eAAe,WAAW,eAAe,WAAW,eAAe,WAAW;AACjI,SAAO;AAAA,IACL,UAAU,IAAI,OAAO,QAAQ;AAAA,IAC7B,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,IACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,IAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,IACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,IAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,IACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,IAC3C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AACF,EAAE;AAKF,SAAS,eAAe,OAAO;AAC7B,SAAO,CAAC,CAAC,SAAS,SAAS,KAAK,KAAK;AACvC;AAKA,SAAS,oBAAoB,OAAO;AAClC,UAAQ,MAAM,QAAQ,UAAU,EAAE,EAAE,QAAQ,WAAW,EAAE,EAAE,YAAY;AACvE,MAAI,QAAQ;AACZ,MAAI,MAAM,KAAK,GAAG;AAChB,YAAQ,MAAM,KAAK;AACnB,YAAQ;AAAA,EACV,WAAW,SAAS,eAAe;AACjC,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,QAAQ;AAAA,IACV;AAAA,EACF;AAMA,MAAI;AACJ,MAAI,QAAQ,SAAS,IAAI,KAAK,KAAK,GAAG;AACpC,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,IAAI,KAAK,KAAK,GAAG;AACpC,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,IAAI,KAAK,KAAK,GAAG;AACpC,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,oBAAoB,MAAM,CAAC,CAAC;AAAA,MAC/B,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC3C,GAAG,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC3C,GAAG,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC3C,GAAG,oBAAoB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC/C,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC3C,GAAG,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC3C,GAAG,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC3C,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,OAAO;AAGjC,MAAI,OAAO;AACX,UAAQ,SAAS;AAAA,IACf,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AACA,WAAS,MAAM,SAAS,MAAM,YAAY;AAC1C,UAAQ,MAAM,QAAQ,SAAS,YAAY;AAC3C,MAAI,UAAU,QAAQ,UAAU,OAAO;AACrC,YAAQ;AAAA,EACV;AACA,MAAI,SAAS,WAAW,SAAS,SAAS;AACxC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;;;ACnpCA,6BAA6C;;;ACNtC,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,iBAAiB,CAAC,KAAK,QAAQ,OAAO,IAAI;AAC9C,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,sBAAmC,eAAe,OAAO,SAAU,KAAK,WAAW;AAC5F,SAAO,IAAI,OAAO,CAAC,YAAY,MAAM,OAAO,YAAY,MAAM,GAAG,CAAC;AACpE,GAAG,CAAC,CAAC;AACE,IAAI,aAA0B,CAAC,EAAE,OAAO,gBAAgB,CAAC,IAAI,CAAC,EAAE,OAAO,SAAU,KAAK,WAAW;AACtG,SAAO,IAAI,OAAO,CAAC,WAAW,YAAY,MAAM,OAAO,YAAY,MAAM,GAAG,CAAC;AAC/E,GAAG,CAAC,CAAC;AAEE,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY;AAEhB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,YAAY;AAEhB,IAAI,cAAc;AAClB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,iBAAiB,CAAC,YAAY,MAAM,WAAW,YAAY,MAAM,WAAW,aAAa,OAAO,UAAU;;;AC9BtG,SAAR,YAA6B,SAAS;AAC3C,SAAO,WAAW,QAAQ,YAAY,IAAI,YAAY,IAAI;AAC5D;;;ACFe,SAAR,UAA2B,MAAM;AACtC,MAAI,QAAQ,MAAM;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,SAAS,MAAM,mBAAmB;AACzC,QAAI,gBAAgB,KAAK;AACzB,WAAO,gBAAgB,cAAc,eAAe,SAAS;AAAA,EAC/D;AAEA,SAAO;AACT;;;ACTA,SAAS,UAAU,MAAM;AACvB,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,cAAc,MAAM;AAC3B,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;AAEA,SAAS,aAAa,MAAM;AAE1B,MAAI,OAAO,eAAe,aAAa;AACrC,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,UAAU,IAAI,EAAE;AACjC,SAAO,gBAAgB,cAAc,gBAAgB;AACvD;;;AChBA,SAAS,YAAY,MAAM;AACzB,MAAI,QAAQ,KAAK;AACjB,SAAO,KAAK,MAAM,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAClD,QAAI,QAAQ,MAAM,OAAO,IAAI,KAAK,CAAC;AACnC,QAAI,aAAa,MAAM,WAAW,IAAI,KAAK,CAAC;AAC5C,QAAI,UAAU,MAAM,SAAS,IAAI;AAEjC,QAAI,CAAC,cAAc,OAAO,KAAK,CAAC,YAAY,OAAO,GAAG;AACpD;AAAA,IACF;AAKA,WAAO,OAAO,QAAQ,OAAO,KAAK;AAClC,WAAO,KAAK,UAAU,EAAE,QAAQ,SAAUoB,OAAM;AAC9C,UAAI,QAAQ,WAAWA,KAAI;AAE3B,UAAI,UAAU,OAAO;AACnB,gBAAQ,gBAAgBA,KAAI;AAAA,MAC9B,OAAO;AACL,gBAAQ,aAAaA,OAAM,UAAU,OAAO,KAAK,KAAK;AAAA,MACxD;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,QAAQ,MAAM;AAClB,MAAI,gBAAgB;AAAA,IAClB,QAAQ;AAAA,MACN,UAAU,MAAM,QAAQ;AAAA,MACxB,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,IACA,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,WAAW,CAAC;AAAA,EACd;AACA,SAAO,OAAO,MAAM,SAAS,OAAO,OAAO,cAAc,MAAM;AAC/D,QAAM,SAAS;AAEf,MAAI,MAAM,SAAS,OAAO;AACxB,WAAO,OAAO,MAAM,SAAS,MAAM,OAAO,cAAc,KAAK;AAAA,EAC/D;AAEA,SAAO,WAAY;AACjB,WAAO,KAAK,MAAM,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAClD,UAAI,UAAU,MAAM,SAAS,IAAI;AACjC,UAAI,aAAa,MAAM,WAAW,IAAI,KAAK,CAAC;AAC5C,UAAI,kBAAkB,OAAO,KAAK,MAAM,OAAO,eAAe,IAAI,IAAI,MAAM,OAAO,IAAI,IAAI,cAAc,IAAI,CAAC;AAE9G,UAAI,QAAQ,gBAAgB,OAAO,SAAUC,QAAO,UAAU;AAC5D,QAAAA,OAAM,QAAQ,IAAI;AAClB,eAAOA;AAAA,MACT,GAAG,CAAC,CAAC;AAEL,UAAI,CAAC,cAAc,OAAO,KAAK,CAAC,YAAY,OAAO,GAAG;AACpD;AAAA,MACF;AAEA,aAAO,OAAO,QAAQ,OAAO,KAAK;AAClC,aAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,WAAW;AACnD,gBAAQ,gBAAgB,SAAS;AAAA,MACnC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAGA,IAAO,sBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ;AAAA,EACA,UAAU,CAAC,eAAe;AAC5B;;;AClFe,SAAR,iBAAkC,WAAW;AAClD,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;;;ACHO,IAAI,MAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAI,QAAQ,KAAK;;;ACFT,SAAR,cAA+B;AACpC,MAAI,SAAS,UAAU;AAEvB,MAAI,UAAU,QAAQ,OAAO,UAAU,MAAM,QAAQ,OAAO,MAAM,GAAG;AACnE,WAAO,OAAO,OAAO,IAAI,SAAU,MAAM;AACvC,aAAO,KAAK,QAAQ,MAAM,KAAK;AAAA,IACjC,CAAC,EAAE,KAAK,GAAG;AAAA,EACb;AAEA,SAAO,UAAU;AACnB;;;ACTe,SAAR,mBAAoC;AACzC,SAAO,CAAC,iCAAiC,KAAK,YAAY,CAAC;AAC7D;;;ACCe,SAAR,sBAAuC,SAAS,cAAc,iBAAiB;AACpF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AAEA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AAEA,MAAI,aAAa,QAAQ,sBAAsB;AAC/C,MAAI,SAAS;AACb,MAAI,SAAS;AAEb,MAAI,gBAAgB,cAAc,OAAO,GAAG;AAC1C,aAAS,QAAQ,cAAc,IAAI,MAAM,WAAW,KAAK,IAAI,QAAQ,eAAe,IAAI;AACxF,aAAS,QAAQ,eAAe,IAAI,MAAM,WAAW,MAAM,IAAI,QAAQ,gBAAgB,IAAI;AAAA,EAC7F;AAEA,MAAI,OAAO,UAAU,OAAO,IAAI,UAAU,OAAO,IAAI,QACjD,iBAAiB,KAAK;AAE1B,MAAI,mBAAmB,CAAC,iBAAiB,KAAK;AAC9C,MAAIC,MAAK,WAAW,QAAQ,oBAAoB,iBAAiB,eAAe,aAAa,MAAM;AACnG,MAAIC,MAAK,WAAW,OAAO,oBAAoB,iBAAiB,eAAe,YAAY,MAAM;AACjG,MAAI,QAAQ,WAAW,QAAQ;AAC/B,MAAI,SAAS,WAAW,SAAS;AACjC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAKA;AAAA,IACL,OAAOD,KAAI;AAAA,IACX,QAAQC,KAAI;AAAA,IACZ,MAAMD;AAAA,IACN,GAAGA;AAAA,IACH,GAAGC;AAAA,EACL;AACF;;;ACrCe,SAAR,cAA+B,SAAS;AAC7C,MAAI,aAAa,sBAAsB,OAAO;AAG9C,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,QAAQ;AAErB,MAAI,KAAK,IAAI,WAAW,QAAQ,KAAK,KAAK,GAAG;AAC3C,YAAQ,WAAW;AAAA,EACrB;AAEA,MAAI,KAAK,IAAI,WAAW,SAAS,MAAM,KAAK,GAAG;AAC7C,aAAS,WAAW;AAAA,EACtB;AAEA,SAAO;AAAA,IACL,GAAG,QAAQ;AAAA,IACX,GAAG,QAAQ;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;;;ACvBe,SAAR,SAA0B,QAAQ,OAAO;AAC9C,MAAI,WAAW,MAAM,eAAe,MAAM,YAAY;AAEtD,MAAI,OAAO,SAAS,KAAK,GAAG;AAC1B,WAAO;AAAA,EACT,WACS,YAAY,aAAa,QAAQ,GAAG;AACzC,QAAI,OAAO;AAEX,OAAG;AACD,UAAI,QAAQ,OAAO,WAAW,IAAI,GAAG;AACnC,eAAO;AAAA,MACT;AAGA,aAAO,KAAK,cAAc,KAAK;AAAA,IACjC,SAAS;AAAA,EACX;AAGF,SAAO;AACT;;;ACrBe,SAAR,iBAAkC,SAAS;AAChD,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;;;ACFe,SAAR,eAAgC,SAAS;AAC9C,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,QAAQ,YAAY,OAAO,CAAC,KAAK;AAChE;;;ACFe,SAAR,mBAAoC,SAAS;AAElD,WAAS,UAAU,OAAO,IAAI,QAAQ;AAAA;AAAA,IACtC,QAAQ;AAAA,QAAa,OAAO,UAAU;AACxC;;;ACFe,SAAR,cAA+B,SAAS;AAC7C,MAAI,YAAY,OAAO,MAAM,QAAQ;AACnC,WAAO;AAAA,EACT;AAEA;AAAA;AAAA;AAAA;AAAA,IAGE,QAAQ;AAAA,IACR,QAAQ;AAAA,KACR,aAAa,OAAO,IAAI,QAAQ,OAAO;AAAA;AAAA,IAEvC,mBAAmB,OAAO;AAAA;AAG9B;;;ACVA,SAAS,oBAAoB,SAAS;AACpC,MAAI,CAAC,cAAc,OAAO;AAAA,EAC1B,iBAAiB,OAAO,EAAE,aAAa,SAAS;AAC9C,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ;AACjB;AAIA,SAAS,mBAAmB,SAAS;AACnC,MAAI,YAAY,WAAW,KAAK,YAAY,CAAC;AAC7C,MAAI,OAAO,WAAW,KAAK,YAAY,CAAC;AAExC,MAAI,QAAQ,cAAc,OAAO,GAAG;AAElC,QAAI,aAAa,iBAAiB,OAAO;AAEzC,QAAI,WAAW,aAAa,SAAS;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,cAAc,cAAc,OAAO;AAEvC,MAAI,aAAa,WAAW,GAAG;AAC7B,kBAAc,YAAY;AAAA,EAC5B;AAEA,SAAO,cAAc,WAAW,KAAK,CAAC,QAAQ,MAAM,EAAE,QAAQ,YAAY,WAAW,CAAC,IAAI,GAAG;AAC3F,QAAI,MAAM,iBAAiB,WAAW;AAItC,QAAI,IAAI,cAAc,UAAU,IAAI,gBAAgB,UAAU,IAAI,YAAY,WAAW,CAAC,aAAa,aAAa,EAAE,QAAQ,IAAI,UAAU,MAAM,MAAM,aAAa,IAAI,eAAe,YAAY,aAAa,IAAI,UAAU,IAAI,WAAW,QAAQ;AACpP,aAAO;AAAA,IACT,OAAO;AACL,oBAAc,YAAY;AAAA,IAC5B;AAAA,EACF;AAEA,SAAO;AACT;AAIe,SAAR,gBAAiC,SAAS;AAC/C,MAAIC,UAAS,UAAU,OAAO;AAC9B,MAAI,eAAe,oBAAoB,OAAO;AAE9C,SAAO,gBAAgB,eAAe,YAAY,KAAK,iBAAiB,YAAY,EAAE,aAAa,UAAU;AAC3G,mBAAe,oBAAoB,YAAY;AAAA,EACjD;AAEA,MAAI,iBAAiB,YAAY,YAAY,MAAM,UAAU,YAAY,YAAY,MAAM,UAAU,iBAAiB,YAAY,EAAE,aAAa,WAAW;AAC1J,WAAOA;AAAA,EACT;AAEA,SAAO,gBAAgB,mBAAmB,OAAO,KAAKA;AACxD;;;ACpEe,SAAR,yBAA0C,WAAW;AAC1D,SAAO,CAAC,OAAO,QAAQ,EAAE,QAAQ,SAAS,KAAK,IAAI,MAAM;AAC3D;;;ACDO,SAAS,OAAOC,MAAK,OAAOC,MAAK;AACtC,SAAO,IAAQD,MAAK,IAAQ,OAAOC,IAAG,CAAC;AACzC;AACO,SAAS,eAAeD,MAAK,OAAOC,MAAK;AAC9C,MAAIC,KAAI,OAAOF,MAAK,OAAOC,IAAG;AAC9B,SAAOC,KAAID,OAAMA,OAAMC;AACzB;;;ACPe,SAAR,qBAAsC;AAC3C,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;;;ACNe,SAAR,mBAAoC,eAAe;AACxD,SAAO,OAAO,OAAO,CAAC,GAAG,mBAAmB,GAAG,aAAa;AAC9D;;;ACHe,SAAR,gBAAiC,OAAO,MAAM;AACnD,SAAO,KAAK,OAAO,SAAU,SAAS,KAAK;AACzC,YAAQ,GAAG,IAAI;AACf,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;;;ACKA,IAAI,kBAAkB,SAASC,iBAAgB,SAAS,OAAO;AAC7D,YAAU,OAAO,YAAY,aAAa,QAAQ,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO;AAAA,IAC/E,WAAW,MAAM;AAAA,EACnB,CAAC,CAAC,IAAI;AACN,SAAO,mBAAmB,OAAO,YAAY,WAAW,UAAU,gBAAgB,SAAS,cAAc,CAAC;AAC5G;AAEA,SAAS,MAAM,MAAM;AACnB,MAAI;AAEJ,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK,MACZ,UAAU,KAAK;AACnB,MAAI,eAAe,MAAM,SAAS;AAClC,MAAIC,iBAAgB,MAAM,cAAc;AACxC,MAAI,gBAAgB,iBAAiB,MAAM,SAAS;AACpD,MAAI,OAAO,yBAAyB,aAAa;AACjD,MAAI,aAAa,CAAC,MAAM,KAAK,EAAE,QAAQ,aAAa,KAAK;AACzD,MAAI,MAAM,aAAa,WAAW;AAElC,MAAI,CAAC,gBAAgB,CAACA,gBAAe;AACnC;AAAA,EACF;AAEA,MAAI,gBAAgB,gBAAgB,QAAQ,SAAS,KAAK;AAC1D,MAAI,YAAY,cAAc,YAAY;AAC1C,MAAI,UAAU,SAAS,MAAM,MAAM;AACnC,MAAI,UAAU,SAAS,MAAM,SAAS;AACtC,MAAI,UAAU,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,MAAM,UAAU,IAAI,IAAIA,eAAc,IAAI,IAAI,MAAM,MAAM,OAAO,GAAG;AACrH,MAAI,YAAYA,eAAc,IAAI,IAAI,MAAM,MAAM,UAAU,IAAI;AAChE,MAAI,oBAAoB,gBAAgB,YAAY;AACpD,MAAI,aAAa,oBAAoB,SAAS,MAAM,kBAAkB,gBAAgB,IAAI,kBAAkB,eAAe,IAAI;AAC/H,MAAI,oBAAoB,UAAU,IAAI,YAAY;AAGlD,MAAIC,OAAM,cAAc,OAAO;AAC/B,MAAIC,OAAM,aAAa,UAAU,GAAG,IAAI,cAAc,OAAO;AAC7D,MAAI,SAAS,aAAa,IAAI,UAAU,GAAG,IAAI,IAAI;AACnD,MAAIC,UAAS,OAAOF,MAAK,QAAQC,IAAG;AAEpC,MAAI,WAAW;AACf,QAAM,cAAc,IAAI,KAAK,wBAAwB,CAAC,GAAG,sBAAsB,QAAQ,IAAIC,SAAQ,sBAAsB,eAAeA,UAAS,QAAQ;AAC3J;AAEA,SAASC,QAAO,OAAO;AACrB,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM;AACpB,MAAI,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,wBAAwB;AAEzE,MAAI,gBAAgB,MAAM;AACxB;AAAA,EACF;AAGA,MAAI,OAAO,iBAAiB,UAAU;AACpC,mBAAe,MAAM,SAAS,OAAO,cAAc,YAAY;AAE/D,QAAI,CAAC,cAAc;AACjB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,SAAS,MAAM,SAAS,QAAQ,YAAY,GAAG;AAClD;AAAA,EACF;AAEA,QAAM,SAAS,QAAQ;AACzB;AAGA,IAAO,gBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,QAAQA;AAAA,EACR,UAAU,CAAC,eAAe;AAAA,EAC1B,kBAAkB,CAAC,iBAAiB;AACtC;;;ACzFe,SAAR,aAA8B,WAAW;AAC9C,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;;;ACOA,IAAI,aAAa;AAAA,EACf,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACR;AAIA,SAAS,kBAAkB,MAAM,KAAK;AACpC,MAAIC,KAAI,KAAK,GACTC,KAAI,KAAK;AACb,MAAI,MAAM,IAAI,oBAAoB;AAClC,SAAO;AAAA,IACL,GAAG,MAAMD,KAAI,GAAG,IAAI,OAAO;AAAA,IAC3B,GAAG,MAAMC,KAAI,GAAG,IAAI,OAAO;AAAA,EAC7B;AACF;AAEO,SAAS,YAAY,OAAO;AACjC,MAAI;AAEJ,MAAIC,UAAS,MAAM,QACf,aAAa,MAAM,YACnB,YAAY,MAAM,WAClB,YAAY,MAAM,WAClB,UAAU,MAAM,SAChB,WAAW,MAAM,UACjB,kBAAkB,MAAM,iBACxB,WAAW,MAAM,UACjB,eAAe,MAAM,cACrB,UAAU,MAAM;AACpB,MAAI,aAAa,QAAQ,GACrBF,KAAI,eAAe,SAAS,IAAI,YAChC,aAAa,QAAQ,GACrBC,KAAI,eAAe,SAAS,IAAI;AAEpC,MAAI,QAAQ,OAAO,iBAAiB,aAAa,aAAa;AAAA,IAC5D,GAAGD;AAAA,IACH,GAAGC;AAAA,EACL,CAAC,IAAI;AAAA,IACH,GAAGD;AAAA,IACH,GAAGC;AAAA,EACL;AAEA,EAAAD,KAAI,MAAM;AACV,EAAAC,KAAI,MAAM;AACV,MAAI,OAAO,QAAQ,eAAe,GAAG;AACrC,MAAI,OAAO,QAAQ,eAAe,GAAG;AACrC,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,MAAM;AAEV,MAAI,UAAU;AACZ,QAAI,eAAe,gBAAgBC,OAAM;AACzC,QAAI,aAAa;AACjB,QAAI,YAAY;AAEhB,QAAI,iBAAiB,UAAUA,OAAM,GAAG;AACtC,qBAAe,mBAAmBA,OAAM;AAExC,UAAI,iBAAiB,YAAY,EAAE,aAAa,YAAY,aAAa,YAAY;AACnF,qBAAa;AACb,oBAAY;AAAA,MACd;AAAA,IACF;AAGA,mBAAe;AAEf,QAAI,cAAc,QAAQ,cAAc,QAAQ,cAAc,UAAU,cAAc,KAAK;AACzF,cAAQ;AACR,UAAI,UAAU,WAAW,iBAAiB,OAAO,IAAI,iBAAiB,IAAI,eAAe;AAAA;AAAA,QACzF,aAAa,UAAU;AAAA;AACvB,MAAAD,MAAK,UAAU,WAAW;AAC1B,MAAAA,MAAK,kBAAkB,IAAI;AAAA,IAC7B;AAEA,QAAI,cAAc,SAAS,cAAc,OAAO,cAAc,WAAW,cAAc,KAAK;AAC1F,cAAQ;AACR,UAAI,UAAU,WAAW,iBAAiB,OAAO,IAAI,iBAAiB,IAAI,eAAe;AAAA;AAAA,QACzF,aAAa,SAAS;AAAA;AACtB,MAAAD,MAAK,UAAU,WAAW;AAC1B,MAAAA,MAAK,kBAAkB,IAAI;AAAA,IAC7B;AAAA,EACF;AAEA,MAAI,eAAe,OAAO,OAAO;AAAA,IAC/B;AAAA,EACF,GAAG,YAAY,UAAU;AAEzB,MAAI,QAAQ,iBAAiB,OAAO,kBAAkB;AAAA,IACpD,GAAGA;AAAA,IACH,GAAGC;AAAA,EACL,GAAG,UAAUC,OAAM,CAAC,IAAI;AAAA,IACtB,GAAGF;AAAA,IACH,GAAGC;AAAA,EACL;AAEA,EAAAD,KAAI,MAAM;AACV,EAAAC,KAAI,MAAM;AAEV,MAAI,iBAAiB;AACnB,QAAI;AAEJ,WAAO,OAAO,OAAO,CAAC,GAAG,eAAe,iBAAiB,CAAC,GAAG,eAAe,KAAK,IAAI,OAAO,MAAM,IAAI,eAAe,KAAK,IAAI,OAAO,MAAM,IAAI,eAAe,aAAa,IAAI,oBAAoB,MAAM,IAAI,eAAeD,KAAI,SAASC,KAAI,QAAQ,iBAAiBD,KAAI,SAASC,KAAI,UAAU,eAAe;AAAA,EAClT;AAEA,SAAO,OAAO,OAAO,CAAC,GAAG,eAAe,kBAAkB,CAAC,GAAG,gBAAgB,KAAK,IAAI,OAAOA,KAAI,OAAO,IAAI,gBAAgB,KAAK,IAAI,OAAOD,KAAI,OAAO,IAAI,gBAAgB,YAAY,IAAI,gBAAgB;AAC9M;AAEA,SAAS,cAAc,OAAO;AAC5B,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM;AACpB,MAAI,wBAAwB,QAAQ,iBAChC,kBAAkB,0BAA0B,SAAS,OAAO,uBAC5D,oBAAoB,QAAQ,UAC5B,WAAW,sBAAsB,SAAS,OAAO,mBACjD,wBAAwB,QAAQ,cAChC,eAAe,0BAA0B,SAAS,OAAO;AAC7D,MAAI,eAAe;AAAA,IACjB,WAAW,iBAAiB,MAAM,SAAS;AAAA,IAC3C,WAAW,aAAa,MAAM,SAAS;AAAA,IACvC,QAAQ,MAAM,SAAS;AAAA,IACvB,YAAY,MAAM,MAAM;AAAA,IACxB;AAAA,IACA,SAAS,MAAM,QAAQ,aAAa;AAAA,EACtC;AAEA,MAAI,MAAM,cAAc,iBAAiB,MAAM;AAC7C,UAAM,OAAO,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO,QAAQ,YAAY,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,MACvG,SAAS,MAAM,cAAc;AAAA,MAC7B,UAAU,MAAM,QAAQ;AAAA,MACxB;AAAA,MACA;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL;AAEA,MAAI,MAAM,cAAc,SAAS,MAAM;AACrC,UAAM,OAAO,QAAQ,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO,OAAO,YAAY,OAAO,OAAO,CAAC,GAAG,cAAc;AAAA,MACrG,SAAS,MAAM,cAAc;AAAA,MAC7B,UAAU;AAAA,MACV,UAAU;AAAA,MACV;AAAA,IACF,CAAC,CAAC,CAAC;AAAA,EACL;AAEA,QAAM,WAAW,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,WAAW,QAAQ;AAAA,IACnE,yBAAyB,MAAM;AAAA,EACjC,CAAC;AACH;AAGA,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM,CAAC;AACT;;;ACtKA,IAAI,UAAU;AAAA,EACZ,SAAS;AACX;AAEA,SAASG,QAAO,MAAM;AACpB,MAAI,QAAQ,KAAK,OACb,WAAW,KAAK,UAChB,UAAU,KAAK;AACnB,MAAI,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO,iBAC7C,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO;AACjD,MAAIC,UAAS,UAAU,MAAM,SAAS,MAAM;AAC5C,MAAI,gBAAgB,CAAC,EAAE,OAAO,MAAM,cAAc,WAAW,MAAM,cAAc,MAAM;AAEvF,MAAI,QAAQ;AACV,kBAAc,QAAQ,SAAU,cAAc;AAC5C,mBAAa,iBAAiB,UAAU,SAAS,QAAQ,OAAO;AAAA,IAClE,CAAC;AAAA,EACH;AAEA,MAAI,QAAQ;AACV,IAAAA,QAAO,iBAAiB,UAAU,SAAS,QAAQ,OAAO;AAAA,EAC5D;AAEA,SAAO,WAAY;AACjB,QAAI,QAAQ;AACV,oBAAc,QAAQ,SAAU,cAAc;AAC5C,qBAAa,oBAAoB,UAAU,SAAS,QAAQ,OAAO;AAAA,MACrE,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ;AACV,MAAAA,QAAO,oBAAoB,UAAU,SAAS,QAAQ,OAAO;AAAA,IAC/D;AAAA,EACF;AACF;AAGA,IAAO,yBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI,SAAS,KAAK;AAAA,EAAC;AAAA,EACnB,QAAQD;AAAA,EACR,MAAM,CAAC;AACT;;;AChDA,IAAI,OAAO;AAAA,EACT,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACe,SAAR,qBAAsC,WAAW;AACtD,SAAO,UAAU,QAAQ,0BAA0B,SAAU,SAAS;AACpE,WAAO,KAAK,OAAO;AAAA,EACrB,CAAC;AACH;;;ACVA,IAAIE,QAAO;AAAA,EACT,OAAO;AAAA,EACP,KAAK;AACP;AACe,SAAR,8BAA+C,WAAW;AAC/D,SAAO,UAAU,QAAQ,cAAc,SAAU,SAAS;AACxD,WAAOA,MAAK,OAAO;AAAA,EACrB,CAAC;AACH;;;ACPe,SAAR,gBAAiC,MAAM;AAC5C,MAAI,MAAM,UAAU,IAAI;AACxB,MAAI,aAAa,IAAI;AACrB,MAAI,YAAY,IAAI;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;;;ACNe,SAAR,oBAAqC,SAAS;AAQnD,SAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO,gBAAgB,OAAO,EAAE;AAC5F;;;ACRe,SAAR,gBAAiC,SAAS,UAAU;AACzD,MAAI,MAAM,UAAU,OAAO;AAC3B,MAAI,OAAO,mBAAmB,OAAO;AACrC,MAAI,iBAAiB,IAAI;AACzB,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAIC,KAAI;AACR,MAAIC,KAAI;AAER,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,QAAI,iBAAiB,iBAAiB;AAEtC,QAAI,kBAAkB,CAAC,kBAAkB,aAAa,SAAS;AAC7D,MAAAD,KAAI,eAAe;AACnB,MAAAC,KAAI,eAAe;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAGD,KAAI,oBAAoB,OAAO;AAAA,IAClC,GAAGC;AAAA,EACL;AACF;;;ACvBe,SAAR,gBAAiC,SAAS;AAC/C,MAAI;AAEJ,MAAI,OAAO,mBAAmB,OAAO;AACrC,MAAI,YAAY,gBAAgB,OAAO;AACvC,MAAI,QAAQ,wBAAwB,QAAQ,kBAAkB,OAAO,SAAS,sBAAsB;AACpG,MAAI,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,OAAO,KAAK,cAAc,GAAG,OAAO,KAAK,cAAc,CAAC;AAC5G,MAAI,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,OAAO,KAAK,eAAe,GAAG,OAAO,KAAK,eAAe,CAAC;AACjH,MAAIC,KAAI,CAAC,UAAU,aAAa,oBAAoB,OAAO;AAC3D,MAAIC,KAAI,CAAC,UAAU;AAEnB,MAAI,iBAAiB,QAAQ,IAAI,EAAE,cAAc,OAAO;AACtD,IAAAD,MAAK,IAAI,KAAK,aAAa,OAAO,KAAK,cAAc,CAAC,IAAI;AAAA,EAC5D;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAGA;AAAA,IACH,GAAGC;AAAA,EACL;AACF;;;AC3Be,SAAR,eAAgC,SAAS;AAE9C,MAAI,oBAAoB,iBAAiB,OAAO,GAC5C,WAAW,kBAAkB,UAC7B,YAAY,kBAAkB,WAC9B,YAAY,kBAAkB;AAElC,SAAO,6BAA6B,KAAK,WAAW,YAAY,SAAS;AAC3E;;;ACLe,SAAR,gBAAiC,MAAM;AAC5C,MAAI,CAAC,QAAQ,QAAQ,WAAW,EAAE,QAAQ,YAAY,IAAI,CAAC,KAAK,GAAG;AAEjE,WAAO,KAAK,cAAc;AAAA,EAC5B;AAEA,MAAI,cAAc,IAAI,KAAK,eAAe,IAAI,GAAG;AAC/C,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,cAAc,IAAI,CAAC;AAC5C;;;ACJe,SAAR,kBAAmC,SAAS,MAAM;AACvD,MAAI;AAEJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,eAAe,gBAAgB,OAAO;AAC1C,MAAI,SAAS,mBAAmB,wBAAwB,QAAQ,kBAAkB,OAAO,SAAS,sBAAsB;AACxH,MAAI,MAAM,UAAU,YAAY;AAChC,MAAI,SAAS,SAAS,CAAC,GAAG,EAAE,OAAO,IAAI,kBAAkB,CAAC,GAAG,eAAe,YAAY,IAAI,eAAe,CAAC,CAAC,IAAI;AACjH,MAAI,cAAc,KAAK,OAAO,MAAM;AACpC,SAAO,SAAS;AAAA;AAAA,IAChB,YAAY,OAAO,kBAAkB,cAAc,MAAM,CAAC,CAAC;AAAA;AAC7D;;;ACzBe,SAAR,iBAAkC,MAAM;AAC7C,SAAO,OAAO,OAAO,CAAC,GAAG,MAAM;AAAA,IAC7B,MAAM,KAAK;AAAA,IACX,KAAK,KAAK;AAAA,IACV,OAAO,KAAK,IAAI,KAAK;AAAA,IACrB,QAAQ,KAAK,IAAI,KAAK;AAAA,EACxB,CAAC;AACH;;;ACQA,SAAS,2BAA2B,SAAS,UAAU;AACrD,MAAI,OAAO,sBAAsB,SAAS,OAAO,aAAa,OAAO;AACrE,OAAK,MAAM,KAAK,MAAM,QAAQ;AAC9B,OAAK,OAAO,KAAK,OAAO,QAAQ;AAChC,OAAK,SAAS,KAAK,MAAM,QAAQ;AACjC,OAAK,QAAQ,KAAK,OAAO,QAAQ;AACjC,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB,OAAK,IAAI,KAAK;AACd,OAAK,IAAI,KAAK;AACd,SAAO;AACT;AAEA,SAAS,2BAA2B,SAAS,gBAAgB,UAAU;AACrE,SAAO,mBAAmB,WAAW,iBAAiB,gBAAgB,SAAS,QAAQ,CAAC,IAAI,UAAU,cAAc,IAAI,2BAA2B,gBAAgB,QAAQ,IAAI,iBAAiB,gBAAgB,mBAAmB,OAAO,CAAC,CAAC;AAC9O;AAKA,SAAS,mBAAmB,SAAS;AACnC,MAAIC,mBAAkB,kBAAkB,cAAc,OAAO,CAAC;AAC9D,MAAI,oBAAoB,CAAC,YAAY,OAAO,EAAE,QAAQ,iBAAiB,OAAO,EAAE,QAAQ,KAAK;AAC7F,MAAI,iBAAiB,qBAAqB,cAAc,OAAO,IAAI,gBAAgB,OAAO,IAAI;AAE9F,MAAI,CAAC,UAAU,cAAc,GAAG;AAC9B,WAAO,CAAC;AAAA,EACV;AAGA,SAAOA,iBAAgB,OAAO,SAAU,gBAAgB;AACtD,WAAO,UAAU,cAAc,KAAK,SAAS,gBAAgB,cAAc,KAAK,YAAY,cAAc,MAAM;AAAA,EAClH,CAAC;AACH;AAIe,SAAR,gBAAiC,SAAS,UAAU,cAAc,UAAU;AACjF,MAAI,sBAAsB,aAAa,oBAAoB,mBAAmB,OAAO,IAAI,CAAC,EAAE,OAAO,QAAQ;AAC3G,MAAIA,mBAAkB,CAAC,EAAE,OAAO,qBAAqB,CAAC,YAAY,CAAC;AACnE,MAAI,sBAAsBA,iBAAgB,CAAC;AAC3C,MAAI,eAAeA,iBAAgB,OAAO,SAAU,SAAS,gBAAgB;AAC3E,QAAI,OAAO,2BAA2B,SAAS,gBAAgB,QAAQ;AACvE,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,2BAA2B,SAAS,qBAAqB,QAAQ,CAAC;AACrE,eAAa,QAAQ,aAAa,QAAQ,aAAa;AACvD,eAAa,SAAS,aAAa,SAAS,aAAa;AACzD,eAAa,IAAI,aAAa;AAC9B,eAAa,IAAI,aAAa;AAC9B,SAAO;AACT;;;ACjEe,SAAR,eAAgC,MAAM;AAC3C,MAAIC,aAAY,KAAK,WACjB,UAAU,KAAK,SACf,YAAY,KAAK;AACrB,MAAI,gBAAgB,YAAY,iBAAiB,SAAS,IAAI;AAC9D,MAAI,YAAY,YAAY,aAAa,SAAS,IAAI;AACtD,MAAI,UAAUA,WAAU,IAAIA,WAAU,QAAQ,IAAI,QAAQ,QAAQ;AAClE,MAAI,UAAUA,WAAU,IAAIA,WAAU,SAAS,IAAI,QAAQ,SAAS;AACpE,MAAI;AAEJ,UAAQ,eAAe;AAAA,IACrB,KAAK;AACH,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAGA,WAAU,IAAI,QAAQ;AAAA,MAC3B;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAGA,WAAU,IAAIA,WAAU;AAAA,MAC7B;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAGA,WAAU,IAAIA,WAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IAEF,KAAK;AACH,gBAAU;AAAA,QACR,GAAGA,WAAU,IAAI,QAAQ;AAAA,QACzB,GAAG;AAAA,MACL;AACA;AAAA,IAEF;AACE,gBAAU;AAAA,QACR,GAAGA,WAAU;AAAA,QACb,GAAGA,WAAU;AAAA,MACf;AAAA,EACJ;AAEA,MAAI,WAAW,gBAAgB,yBAAyB,aAAa,IAAI;AAEzE,MAAI,YAAY,MAAM;AACpB,QAAI,MAAM,aAAa,MAAM,WAAW;AAExC,YAAQ,WAAW;AAAA,MACjB,KAAK;AACH,gBAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAKA,WAAU,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI;AAC7E;AAAA,MAEF,KAAK;AACH,gBAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAKA,WAAU,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI;AAC7E;AAAA,MAEF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AC3De,SAAR,eAAgC,OAAO,SAAS;AACrD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,SACX,qBAAqB,SAAS,WAC9B,YAAY,uBAAuB,SAAS,MAAM,YAAY,oBAC9D,oBAAoB,SAAS,UAC7B,WAAW,sBAAsB,SAAS,MAAM,WAAW,mBAC3D,oBAAoB,SAAS,UAC7B,WAAW,sBAAsB,SAAS,kBAAkB,mBAC5D,wBAAwB,SAAS,cACjC,eAAe,0BAA0B,SAAS,WAAW,uBAC7D,wBAAwB,SAAS,gBACjC,iBAAiB,0BAA0B,SAAS,SAAS,uBAC7D,uBAAuB,SAAS,aAChC,cAAc,yBAAyB,SAAS,QAAQ,sBACxD,mBAAmB,SAAS,SAC5B,UAAU,qBAAqB,SAAS,IAAI;AAChD,MAAI,gBAAgB,mBAAmB,OAAO,YAAY,WAAW,UAAU,gBAAgB,SAAS,cAAc,CAAC;AACvH,MAAI,aAAa,mBAAmB,SAAS,YAAY;AACzD,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,UAAU,MAAM,SAAS,cAAc,aAAa,cAAc;AACtE,MAAI,qBAAqB,gBAAgB,UAAU,OAAO,IAAI,UAAU,QAAQ,kBAAkB,mBAAmB,MAAM,SAAS,MAAM,GAAG,UAAU,cAAc,QAAQ;AAC7K,MAAI,sBAAsB,sBAAsB,MAAM,SAAS,SAAS;AACxE,MAAIC,iBAAgB,eAAe;AAAA,IACjC,WAAW;AAAA,IACX,SAAS;AAAA,IACT,UAAU;AAAA,IACV;AAAA,EACF,CAAC;AACD,MAAI,mBAAmB,iBAAiB,OAAO,OAAO,CAAC,GAAG,YAAYA,cAAa,CAAC;AACpF,MAAI,oBAAoB,mBAAmB,SAAS,mBAAmB;AAGvE,MAAI,kBAAkB;AAAA,IACpB,KAAK,mBAAmB,MAAM,kBAAkB,MAAM,cAAc;AAAA,IACpE,QAAQ,kBAAkB,SAAS,mBAAmB,SAAS,cAAc;AAAA,IAC7E,MAAM,mBAAmB,OAAO,kBAAkB,OAAO,cAAc;AAAA,IACvE,OAAO,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc;AAAA,EAC5E;AACA,MAAI,aAAa,MAAM,cAAc;AAErC,MAAI,mBAAmB,UAAU,YAAY;AAC3C,QAAIC,UAAS,WAAW,SAAS;AACjC,WAAO,KAAK,eAAe,EAAE,QAAQ,SAAU,KAAK;AAClD,UAAI,WAAW,CAAC,OAAO,MAAM,EAAE,QAAQ,GAAG,KAAK,IAAI,IAAI;AACvD,UAAI,OAAO,CAAC,KAAK,MAAM,EAAE,QAAQ,GAAG,KAAK,IAAI,MAAM;AACnD,sBAAgB,GAAG,KAAKA,QAAO,IAAI,IAAI;AAAA,IACzC,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;AC5De,SAAR,qBAAsC,OAAO,SAAS;AAC3D,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,WAAW,SACX,YAAY,SAAS,WACrB,WAAW,SAAS,UACpB,eAAe,SAAS,cACxB,UAAU,SAAS,SACnB,iBAAiB,SAAS,gBAC1B,wBAAwB,SAAS,uBACjC,wBAAwB,0BAA0B,SAAS,aAAgB;AAC/E,MAAI,YAAY,aAAa,SAAS;AACtC,MAAIC,cAAa,YAAY,iBAAiB,sBAAsB,oBAAoB,OAAO,SAAUC,YAAW;AAClH,WAAO,aAAaA,UAAS,MAAM;AAAA,EACrC,CAAC,IAAI;AACL,MAAI,oBAAoBD,YAAW,OAAO,SAAUC,YAAW;AAC7D,WAAO,sBAAsB,QAAQA,UAAS,KAAK;AAAA,EACrD,CAAC;AAED,MAAI,kBAAkB,WAAW,GAAG;AAClC,wBAAoBD;AAAA,EACtB;AAGA,MAAI,YAAY,kBAAkB,OAAO,SAAU,KAAKC,YAAW;AACjE,QAAIA,UAAS,IAAI,eAAe,OAAO;AAAA,MACrC,WAAWA;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,EAAE,iBAAiBA,UAAS,CAAC;AAC9B,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,SAAO,OAAO,KAAK,SAAS,EAAE,KAAK,SAAUC,IAAGC,IAAG;AACjD,WAAO,UAAUD,EAAC,IAAI,UAAUC,EAAC;AAAA,EACnC,CAAC;AACH;;;AClCA,SAAS,8BAA8B,WAAW;AAChD,MAAI,iBAAiB,SAAS,MAAM,MAAM;AACxC,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,oBAAoB,qBAAqB,SAAS;AACtD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AAEA,SAASC,MAAK,MAAM;AAClB,MAAI,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,OAAO,KAAK;AAEhB,MAAI,MAAM,cAAc,IAAI,EAAE,OAAO;AACnC;AAAA,EACF;AAEA,MAAI,oBAAoB,QAAQ,UAC5B,gBAAgB,sBAAsB,SAAS,OAAO,mBACtD,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,OAAO,kBACpD,8BAA8B,QAAQ,oBACtC,UAAU,QAAQ,SAClB,WAAW,QAAQ,UACnB,eAAe,QAAQ,cACvB,cAAc,QAAQ,aACtB,wBAAwB,QAAQ,gBAChC,iBAAiB,0BAA0B,SAAS,OAAO,uBAC3D,wBAAwB,QAAQ;AACpC,MAAI,qBAAqB,MAAM,QAAQ;AACvC,MAAI,gBAAgB,iBAAiB,kBAAkB;AACvD,MAAI,kBAAkB,kBAAkB;AACxC,MAAI,qBAAqB,gCAAgC,mBAAmB,CAAC,iBAAiB,CAAC,qBAAqB,kBAAkB,CAAC,IAAI,8BAA8B,kBAAkB;AAC3L,MAAIC,cAAa,CAAC,kBAAkB,EAAE,OAAO,kBAAkB,EAAE,OAAO,SAAU,KAAKC,YAAW;AAChG,WAAO,IAAI,OAAO,iBAAiBA,UAAS,MAAM,OAAO,qBAAqB,OAAO;AAAA,MACnF,WAAWA;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,IAAIA,UAAS;AAAA,EAChB,GAAG,CAAC,CAAC;AACL,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,YAAY,oBAAI,IAAI;AACxB,MAAI,qBAAqB;AACzB,MAAI,wBAAwBD,YAAW,CAAC;AAExC,WAASE,KAAI,GAAGA,KAAIF,YAAW,QAAQE,MAAK;AAC1C,QAAI,YAAYF,YAAWE,EAAC;AAE5B,QAAI,iBAAiB,iBAAiB,SAAS;AAE/C,QAAI,mBAAmB,aAAa,SAAS,MAAM;AACnD,QAAI,aAAa,CAAC,KAAK,MAAM,EAAE,QAAQ,cAAc,KAAK;AAC1D,QAAI,MAAM,aAAa,UAAU;AACjC,QAAI,WAAW,eAAe,OAAO;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,oBAAoB,aAAa,mBAAmB,QAAQ,OAAO,mBAAmB,SAAS;AAEnG,QAAI,cAAc,GAAG,IAAI,WAAW,GAAG,GAAG;AACxC,0BAAoB,qBAAqB,iBAAiB;AAAA,IAC5D;AAEA,QAAI,mBAAmB,qBAAqB,iBAAiB;AAC7D,QAAI,SAAS,CAAC;AAEd,QAAI,eAAe;AACjB,aAAO,KAAK,SAAS,cAAc,KAAK,CAAC;AAAA,IAC3C;AAEA,QAAI,cAAc;AAChB,aAAO,KAAK,SAAS,iBAAiB,KAAK,GAAG,SAAS,gBAAgB,KAAK,CAAC;AAAA,IAC/E;AAEA,QAAI,OAAO,MAAM,SAAU,OAAO;AAChC,aAAO;AAAA,IACT,CAAC,GAAG;AACF,8BAAwB;AACxB,2BAAqB;AACrB;AAAA,IACF;AAEA,cAAU,IAAI,WAAW,MAAM;AAAA,EACjC;AAEA,MAAI,oBAAoB;AAEtB,QAAI,iBAAiB,iBAAiB,IAAI;AAE1C,QAAI,QAAQ,SAASC,OAAMC,KAAI;AAC7B,UAAI,mBAAmBJ,YAAW,KAAK,SAAUC,YAAW;AAC1D,YAAII,UAAS,UAAU,IAAIJ,UAAS;AAEpC,YAAII,SAAQ;AACV,iBAAOA,QAAO,MAAM,GAAGD,GAAE,EAAE,MAAM,SAAU,OAAO;AAChD,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,kBAAkB;AACpB,gCAAwB;AACxB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,KAAK,gBAAgB,KAAK,GAAG,MAAM;AAC1C,UAAI,OAAO,MAAM,EAAE;AAEnB,UAAI,SAAS,QAAS;AAAA,IACxB;AAAA,EACF;AAEA,MAAI,MAAM,cAAc,uBAAuB;AAC7C,UAAM,cAAc,IAAI,EAAE,QAAQ;AAClC,UAAM,YAAY;AAClB,UAAM,QAAQ;AAAA,EAChB;AACF;AAGA,IAAO,eAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAIL;AAAA,EACJ,kBAAkB,CAAC,QAAQ;AAAA,EAC3B,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AACF;;;AC/IA,SAAS,eAAe,UAAU,MAAM,kBAAkB;AACxD,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,MACjB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AAEA,SAAO;AAAA,IACL,KAAK,SAAS,MAAM,KAAK,SAAS,iBAAiB;AAAA,IACnD,OAAO,SAAS,QAAQ,KAAK,QAAQ,iBAAiB;AAAA,IACtD,QAAQ,SAAS,SAAS,KAAK,SAAS,iBAAiB;AAAA,IACzD,MAAM,SAAS,OAAO,KAAK,QAAQ,iBAAiB;AAAA,EACtD;AACF;AAEA,SAAS,sBAAsB,UAAU;AACvC,SAAO,CAAC,KAAK,OAAO,QAAQ,IAAI,EAAE,KAAK,SAAU,MAAM;AACrD,WAAO,SAAS,IAAI,KAAK;AAAA,EAC3B,CAAC;AACH;AAEA,SAAS,KAAK,MAAM;AAClB,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK;AAChB,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,mBAAmB,MAAM,cAAc;AAC3C,MAAI,oBAAoB,eAAe,OAAO;AAAA,IAC5C,gBAAgB;AAAA,EAClB,CAAC;AACD,MAAI,oBAAoB,eAAe,OAAO;AAAA,IAC5C,aAAa;AAAA,EACf,CAAC;AACD,MAAI,2BAA2B,eAAe,mBAAmB,aAAa;AAC9E,MAAI,sBAAsB,eAAe,mBAAmB,YAAY,gBAAgB;AACxF,MAAI,oBAAoB,sBAAsB,wBAAwB;AACtE,MAAI,mBAAmB,sBAAsB,mBAAmB;AAChE,QAAM,cAAc,IAAI,IAAI;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,WAAW,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,WAAW,QAAQ;AAAA,IACnE,gCAAgC;AAAA,IAChC,uBAAuB;AAAA,EACzB,CAAC;AACH;AAGA,IAAO,eAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,kBAAkB,CAAC,iBAAiB;AAAA,EACpC,IAAI;AACN;;;ACzDO,SAAS,wBAAwB,WAAW,OAAOO,SAAQ;AAChE,MAAI,gBAAgB,iBAAiB,SAAS;AAC9C,MAAI,iBAAiB,CAAC,MAAM,GAAG,EAAE,QAAQ,aAAa,KAAK,IAAI,KAAK;AAEpE,MAAI,OAAO,OAAOA,YAAW,aAAaA,QAAO,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,IACxE;AAAA,EACF,CAAC,CAAC,IAAIA,SACF,WAAW,KAAK,CAAC,GACjB,WAAW,KAAK,CAAC;AAErB,aAAW,YAAY;AACvB,cAAY,YAAY,KAAK;AAC7B,SAAO,CAAC,MAAM,KAAK,EAAE,QAAQ,aAAa,KAAK,IAAI;AAAA,IACjD,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,QAAQ,MAAM,OACd,UAAU,MAAM,SAChB,OAAO,MAAM;AACjB,MAAI,kBAAkB,QAAQ,QAC1BA,UAAS,oBAAoB,SAAS,CAAC,GAAG,CAAC,IAAI;AACnD,MAAI,OAAO,WAAW,OAAO,SAAU,KAAK,WAAW;AACrD,QAAI,SAAS,IAAI,wBAAwB,WAAW,MAAM,OAAOA,OAAM;AACvE,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACL,MAAI,wBAAwB,KAAK,MAAM,SAAS,GAC5CC,KAAI,sBAAsB,GAC1BC,KAAI,sBAAsB;AAE9B,MAAI,MAAM,cAAc,iBAAiB,MAAM;AAC7C,UAAM,cAAc,cAAc,KAAKD;AACvC,UAAM,cAAc,cAAc,KAAKC;AAAA,EACzC;AAEA,QAAM,cAAc,IAAI,IAAI;AAC9B;AAGA,IAAO,iBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,UAAU,CAAC,eAAe;AAAA,EAC1B,IAAI;AACN;;;ACnDA,SAAS,cAAc,MAAM;AAC3B,MAAI,QAAQ,KAAK,OACb,OAAO,KAAK;AAKhB,QAAM,cAAc,IAAI,IAAI,eAAe;AAAA,IACzC,WAAW,MAAM,MAAM;AAAA,IACvB,SAAS,MAAM,MAAM;AAAA,IACrB,UAAU;AAAA,IACV,WAAW,MAAM;AAAA,EACnB,CAAC;AACH;AAGA,IAAO,wBAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM,CAAC;AACT;;;ACxBe,SAAR,WAA4B,MAAM;AACvC,SAAO,SAAS,MAAM,MAAM;AAC9B;;;ACUA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,OAAO,KAAK;AAChB,MAAI,oBAAoB,QAAQ,UAC5B,gBAAgB,sBAAsB,SAAS,OAAO,mBACtD,mBAAmB,QAAQ,SAC3B,eAAe,qBAAqB,SAAS,QAAQ,kBACrD,WAAW,QAAQ,UACnB,eAAe,QAAQ,cACvB,cAAc,QAAQ,aACtB,UAAU,QAAQ,SAClB,kBAAkB,QAAQ,QAC1B,SAAS,oBAAoB,SAAS,OAAO,iBAC7C,wBAAwB,QAAQ,cAChC,eAAe,0BAA0B,SAAS,IAAI;AAC1D,MAAI,WAAW,eAAe,OAAO;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,gBAAgB,iBAAiB,MAAM,SAAS;AACpD,MAAI,YAAY,aAAa,MAAM,SAAS;AAC5C,MAAI,kBAAkB,CAAC;AACvB,MAAI,WAAW,yBAAyB,aAAa;AACrD,MAAI,UAAU,WAAW,QAAQ;AACjC,MAAIC,iBAAgB,MAAM,cAAc;AACxC,MAAI,gBAAgB,MAAM,MAAM;AAChC,MAAI,aAAa,MAAM,MAAM;AAC7B,MAAI,oBAAoB,OAAO,iBAAiB,aAAa,aAAa,OAAO,OAAO,CAAC,GAAG,MAAM,OAAO;AAAA,IACvG,WAAW,MAAM;AAAA,EACnB,CAAC,CAAC,IAAI;AACN,MAAI,8BAA8B,OAAO,sBAAsB,WAAW;AAAA,IACxE,UAAU;AAAA,IACV,SAAS;AAAA,EACX,IAAI,OAAO,OAAO;AAAA,IAChB,UAAU;AAAA,IACV,SAAS;AAAA,EACX,GAAG,iBAAiB;AACpB,MAAI,sBAAsB,MAAM,cAAc,SAAS,MAAM,cAAc,OAAO,MAAM,SAAS,IAAI;AACrG,MAAI,OAAO;AAAA,IACT,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,MAAI,CAACA,gBAAe;AAClB;AAAA,EACF;AAEA,MAAI,eAAe;AACjB,QAAI;AAEJ,QAAI,WAAW,aAAa,MAAM,MAAM;AACxC,QAAI,UAAU,aAAa,MAAM,SAAS;AAC1C,QAAI,MAAM,aAAa,MAAM,WAAW;AACxC,QAAIC,UAASD,eAAc,QAAQ;AACnC,QAAIE,OAAMD,UAAS,SAAS,QAAQ;AACpC,QAAIE,OAAMF,UAAS,SAAS,OAAO;AACnC,QAAI,WAAW,SAAS,CAAC,WAAW,GAAG,IAAI,IAAI;AAC/C,QAAI,SAAS,cAAc,QAAQ,cAAc,GAAG,IAAI,WAAW,GAAG;AACtE,QAAI,SAAS,cAAc,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,GAAG;AAGxE,QAAI,eAAe,MAAM,SAAS;AAClC,QAAI,YAAY,UAAU,eAAe,cAAc,YAAY,IAAI;AAAA,MACrE,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,QAAI,qBAAqB,MAAM,cAAc,kBAAkB,IAAI,MAAM,cAAc,kBAAkB,EAAE,UAAU,mBAAmB;AACxI,QAAI,kBAAkB,mBAAmB,QAAQ;AACjD,QAAI,kBAAkB,mBAAmB,OAAO;AAMhD,QAAI,WAAW,OAAO,GAAG,cAAc,GAAG,GAAG,UAAU,GAAG,CAAC;AAC3D,QAAI,YAAY,kBAAkB,cAAc,GAAG,IAAI,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,WAAW,SAAS,WAAW,kBAAkB,4BAA4B;AAC5M,QAAI,YAAY,kBAAkB,CAAC,cAAc,GAAG,IAAI,IAAI,WAAW,WAAW,kBAAkB,4BAA4B,WAAW,SAAS,WAAW,kBAAkB,4BAA4B;AAC7M,QAAI,oBAAoB,MAAM,SAAS,SAAS,gBAAgB,MAAM,SAAS,KAAK;AACpF,QAAI,eAAe,oBAAoB,aAAa,MAAM,kBAAkB,aAAa,IAAI,kBAAkB,cAAc,IAAI;AACjI,QAAI,uBAAuB,wBAAwB,uBAAuB,OAAO,SAAS,oBAAoB,QAAQ,MAAM,OAAO,wBAAwB;AAC3J,QAAI,YAAYA,UAAS,YAAY,sBAAsB;AAC3D,QAAI,YAAYA,UAAS,YAAY;AACrC,QAAI,kBAAkB,OAAO,SAAS,IAAQC,MAAK,SAAS,IAAIA,MAAKD,SAAQ,SAAS,IAAQE,MAAK,SAAS,IAAIA,IAAG;AACnH,IAAAH,eAAc,QAAQ,IAAI;AAC1B,SAAK,QAAQ,IAAI,kBAAkBC;AAAA,EACrC;AAEA,MAAI,cAAc;AAChB,QAAI;AAEJ,QAAI,YAAY,aAAa,MAAM,MAAM;AAEzC,QAAI,WAAW,aAAa,MAAM,SAAS;AAE3C,QAAI,UAAUD,eAAc,OAAO;AAEnC,QAAI,OAAO,YAAY,MAAM,WAAW;AAExC,QAAI,OAAO,UAAU,SAAS,SAAS;AAEvC,QAAI,OAAO,UAAU,SAAS,QAAQ;AAEtC,QAAI,eAAe,CAAC,KAAK,IAAI,EAAE,QAAQ,aAAa,MAAM;AAE1D,QAAI,wBAAwB,yBAAyB,uBAAuB,OAAO,SAAS,oBAAoB,OAAO,MAAM,OAAO,yBAAyB;AAE7J,QAAI,aAAa,eAAe,OAAO,UAAU,cAAc,IAAI,IAAI,WAAW,IAAI,IAAI,uBAAuB,4BAA4B;AAE7I,QAAI,aAAa,eAAe,UAAU,cAAc,IAAI,IAAI,WAAW,IAAI,IAAI,uBAAuB,4BAA4B,UAAU;AAEhJ,QAAI,mBAAmB,UAAU,eAAe,eAAe,YAAY,SAAS,UAAU,IAAI,OAAO,SAAS,aAAa,MAAM,SAAS,SAAS,aAAa,IAAI;AAExK,IAAAA,eAAc,OAAO,IAAI;AACzB,SAAK,OAAO,IAAI,mBAAmB;AAAA,EACrC;AAEA,QAAM,cAAc,IAAI,IAAI;AAC9B;AAGA,IAAO,0BAAQ;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,kBAAkB,CAAC,QAAQ;AAC7B;;;AC7Ie,SAAR,qBAAsC,SAAS;AACpD,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;;;ACDe,SAAR,cAA+B,MAAM;AAC1C,MAAI,SAAS,UAAU,IAAI,KAAK,CAAC,cAAc,IAAI,GAAG;AACpD,WAAO,gBAAgB,IAAI;AAAA,EAC7B,OAAO;AACL,WAAO,qBAAqB,IAAI;AAAA,EAClC;AACF;;;ACDA,SAAS,gBAAgB,SAAS;AAChC,MAAI,OAAO,QAAQ,sBAAsB;AACzC,MAAI,SAAS,MAAM,KAAK,KAAK,IAAI,QAAQ,eAAe;AACxD,MAAI,SAAS,MAAM,KAAK,MAAM,IAAI,QAAQ,gBAAgB;AAC1D,SAAO,WAAW,KAAK,WAAW;AACpC;AAIe,SAAR,iBAAkC,yBAAyB,cAAc,SAAS;AACvF,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AAEA,MAAI,0BAA0B,cAAc,YAAY;AACxD,MAAI,uBAAuB,cAAc,YAAY,KAAK,gBAAgB,YAAY;AACtF,MAAI,kBAAkB,mBAAmB,YAAY;AACrD,MAAI,OAAO,sBAAsB,yBAAyB,sBAAsB,OAAO;AACvF,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,UAAU;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM;AAAA,IAClC,eAAe,eAAe,GAAG;AAC/B,eAAS,cAAc,YAAY;AAAA,IACrC;AAEA,QAAI,cAAc,YAAY,GAAG;AAC/B,gBAAU,sBAAsB,cAAc,IAAI;AAClD,cAAQ,KAAK,aAAa;AAC1B,cAAQ,KAAK,aAAa;AAAA,IAC5B,WAAW,iBAAiB;AAC1B,cAAQ,IAAI,oBAAoB,eAAe;AAAA,IACjD;AAAA,EACF;AAEA,SAAO;AAAA,IACL,GAAG,KAAK,OAAO,OAAO,aAAa,QAAQ;AAAA,IAC3C,GAAG,KAAK,MAAM,OAAO,YAAY,QAAQ;AAAA,IACzC,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;;;ACvDA,SAAS,MAAM,WAAW;AACxB,MAAI,MAAM,oBAAI,IAAI;AAClB,MAAI,UAAU,oBAAI,IAAI;AACtB,MAAI,SAAS,CAAC;AACd,YAAU,QAAQ,SAAU,UAAU;AACpC,QAAI,IAAI,SAAS,MAAM,QAAQ;AAAA,EACjC,CAAC;AAED,WAAS,KAAK,UAAU;AACtB,YAAQ,IAAI,SAAS,IAAI;AACzB,QAAI,WAAW,CAAC,EAAE,OAAO,SAAS,YAAY,CAAC,GAAG,SAAS,oBAAoB,CAAC,CAAC;AACjF,aAAS,QAAQ,SAAU,KAAK;AAC9B,UAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACrB,YAAI,cAAc,IAAI,IAAI,GAAG;AAE7B,YAAI,aAAa;AACf,eAAK,WAAW;AAAA,QAClB;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO,KAAK,QAAQ;AAAA,EACtB;AAEA,YAAU,QAAQ,SAAU,UAAU;AACpC,QAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,GAAG;AAE/B,WAAK,QAAQ;AAAA,IACf;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEe,SAAR,eAAgC,WAAW;AAEhD,MAAI,mBAAmB,MAAM,SAAS;AAEtC,SAAO,eAAe,OAAO,SAAU,KAAK,OAAO;AACjD,WAAO,IAAI,OAAO,iBAAiB,OAAO,SAAU,UAAU;AAC5D,aAAO,SAAS,UAAU;AAAA,IAC5B,CAAC,CAAC;AAAA,EACJ,GAAG,CAAC,CAAC;AACP;;;AC3Ce,SAAR,SAA0BI,KAAI;AACnC,MAAI;AACJ,SAAO,WAAY;AACjB,QAAI,CAAC,SAAS;AACZ,gBAAU,IAAI,QAAQ,SAAU,SAAS;AACvC,gBAAQ,QAAQ,EAAE,KAAK,WAAY;AACjC,oBAAU;AACV,kBAAQA,IAAG,CAAC;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AACF;;;ACde,SAAR,YAA6B,WAAW;AAC7C,MAAI,SAAS,UAAU,OAAO,SAAUC,SAAQ,SAAS;AACvD,QAAI,WAAWA,QAAO,QAAQ,IAAI;AAClC,IAAAA,QAAO,QAAQ,IAAI,IAAI,WAAW,OAAO,OAAO,CAAC,GAAG,UAAU,SAAS;AAAA,MACrE,SAAS,OAAO,OAAO,CAAC,GAAG,SAAS,SAAS,QAAQ,OAAO;AAAA,MAC5D,MAAM,OAAO,OAAO,CAAC,GAAG,SAAS,MAAM,QAAQ,IAAI;AAAA,IACrD,CAAC,IAAI;AACL,WAAOA;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,SAAO,OAAO,KAAK,MAAM,EAAE,IAAI,SAAU,KAAK;AAC5C,WAAO,OAAO,GAAG;AAAA,EACnB,CAAC;AACH;;;ACJA,IAAI,kBAAkB;AAAA,EACpB,WAAW;AAAA,EACX,WAAW,CAAC;AAAA,EACZ,UAAU;AACZ;AAEA,SAAS,mBAAmB;AAC1B,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,SAAK,IAAI,IAAI,UAAU,IAAI;AAAA,EAC7B;AAEA,SAAO,CAAC,KAAK,KAAK,SAAU,SAAS;AACnC,WAAO,EAAE,WAAW,OAAO,QAAQ,0BAA0B;AAAA,EAC/D,CAAC;AACH;AAEO,SAAS,gBAAgB,kBAAkB;AAChD,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB,CAAC;AAAA,EACtB;AAEA,MAAI,oBAAoB,kBACpB,wBAAwB,kBAAkB,kBAC1CC,oBAAmB,0BAA0B,SAAS,CAAC,IAAI,uBAC3D,yBAAyB,kBAAkB,gBAC3C,iBAAiB,2BAA2B,SAAS,kBAAkB;AAC3E,SAAO,SAASC,cAAaC,YAAWC,SAAQ,SAAS;AACvD,QAAI,YAAY,QAAQ;AACtB,gBAAU;AAAA,IACZ;AAEA,QAAI,QAAQ;AAAA,MACV,WAAW;AAAA,MACX,kBAAkB,CAAC;AAAA,MACnB,SAAS,OAAO,OAAO,CAAC,GAAG,iBAAiB,cAAc;AAAA,MAC1D,eAAe,CAAC;AAAA,MAChB,UAAU;AAAA,QACR,WAAWD;AAAA,QACX,QAAQC;AAAA,MACV;AAAA,MACA,YAAY,CAAC;AAAA,MACb,QAAQ,CAAC;AAAA,IACX;AACA,QAAI,mBAAmB,CAAC;AACxB,QAAI,cAAc;AAClB,QAAI,WAAW;AAAA,MACb;AAAA,MACA,YAAY,SAAS,WAAW,kBAAkB;AAChD,YAAIC,WAAU,OAAO,qBAAqB,aAAa,iBAAiB,MAAM,OAAO,IAAI;AACzF,+BAAuB;AACvB,cAAM,UAAU,OAAO,OAAO,CAAC,GAAG,gBAAgB,MAAM,SAASA,QAAO;AACxE,cAAM,gBAAgB;AAAA,UACpB,WAAW,UAAUF,UAAS,IAAI,kBAAkBA,UAAS,IAAIA,WAAU,iBAAiB,kBAAkBA,WAAU,cAAc,IAAI,CAAC;AAAA,UAC3I,QAAQ,kBAAkBC,OAAM;AAAA,QAClC;AAGA,YAAI,mBAAmB,eAAe,YAAY,CAAC,EAAE,OAAOH,mBAAkB,MAAM,QAAQ,SAAS,CAAC,CAAC;AAEvG,cAAM,mBAAmB,iBAAiB,OAAO,SAAUK,IAAG;AAC5D,iBAAOA,GAAE;AAAA,QACX,CAAC;AACD,2BAAmB;AACnB,eAAO,SAAS,OAAO;AAAA,MACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,aAAa,SAAS,cAAc;AAClC,YAAI,aAAa;AACf;AAAA,QACF;AAEA,YAAI,kBAAkB,MAAM,UACxBH,aAAY,gBAAgB,WAC5BC,UAAS,gBAAgB;AAG7B,YAAI,CAAC,iBAAiBD,YAAWC,OAAM,GAAG;AACxC;AAAA,QACF;AAGA,cAAM,QAAQ;AAAA,UACZ,WAAW,iBAAiBD,YAAW,gBAAgBC,OAAM,GAAG,MAAM,QAAQ,aAAa,OAAO;AAAA,UAClG,QAAQ,cAAcA,OAAM;AAAA,QAC9B;AAMA,cAAM,QAAQ;AACd,cAAM,YAAY,MAAM,QAAQ;AAKhC,cAAM,iBAAiB,QAAQ,SAAU,UAAU;AACjD,iBAAO,MAAM,cAAc,SAAS,IAAI,IAAI,OAAO,OAAO,CAAC,GAAG,SAAS,IAAI;AAAA,QAC7E,CAAC;AAED,iBAAS,QAAQ,GAAG,QAAQ,MAAM,iBAAiB,QAAQ,SAAS;AAClE,cAAI,MAAM,UAAU,MAAM;AACxB,kBAAM,QAAQ;AACd,oBAAQ;AACR;AAAA,UACF;AAEA,cAAI,wBAAwB,MAAM,iBAAiB,KAAK,GACpDG,MAAK,sBAAsB,IAC3B,yBAAyB,sBAAsB,SAC/C,WAAW,2BAA2B,SAAS,CAAC,IAAI,wBACpD,OAAO,sBAAsB;AAEjC,cAAI,OAAOA,QAAO,YAAY;AAC5B,oBAAQA,IAAG;AAAA,cACT;AAAA,cACA,SAAS;AAAA,cACT;AAAA,cACA;AAAA,YACF,CAAC,KAAK;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA,MAGA,QAAQ,SAAS,WAAY;AAC3B,eAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,mBAAS,YAAY;AACrB,kBAAQ,KAAK;AAAA,QACf,CAAC;AAAA,MACH,CAAC;AAAA,MACD,SAAS,SAAS,UAAU;AAC1B,+BAAuB;AACvB,sBAAc;AAAA,MAChB;AAAA,IACF;AAEA,QAAI,CAAC,iBAAiBJ,YAAWC,OAAM,GAAG;AACxC,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,OAAO,EAAE,KAAK,SAAUI,QAAO;AACjD,UAAI,CAAC,eAAe,QAAQ,eAAe;AACzC,gBAAQ,cAAcA,MAAK;AAAA,MAC7B;AAAA,IACF,CAAC;AAMD,aAAS,qBAAqB;AAC5B,YAAM,iBAAiB,QAAQ,SAAU,MAAM;AAC7C,YAAI,OAAO,KAAK,MACZ,eAAe,KAAK,SACpBH,WAAU,iBAAiB,SAAS,CAAC,IAAI,cACzCI,UAAS,KAAK;AAElB,YAAI,OAAOA,YAAW,YAAY;AAChC,cAAI,YAAYA,QAAO;AAAA,YACrB;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAASJ;AAAA,UACX,CAAC;AAED,cAAI,SAAS,SAASK,UAAS;AAAA,UAAC;AAEhC,2BAAiB,KAAK,aAAa,MAAM;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,yBAAyB;AAChC,uBAAiB,QAAQ,SAAUH,KAAI;AACrC,eAAOA,IAAG;AAAA,MACZ,CAAC;AACD,yBAAmB,CAAC;AAAA,IACtB;AAEA,WAAO;AAAA,EACT;AACF;AACO,IAAI,eAA4B,gBAAgB;;;AC/LvD,IAAI,mBAAmB,CAAC,wBAAgB,uBAAe,uBAAe,mBAAW;AACjF,IAAII,gBAA4B,gBAAgB;AAAA,EAC9C;AACF,CAAC;;;ACED,IAAIC,oBAAmB,CAAC,wBAAgB,uBAAe,uBAAe,qBAAa,gBAAQ,cAAM,yBAAiB,eAAO,YAAI;AAC7H,IAAIC,gBAA4B,gBAAgB;AAAA,EAC9C,kBAAkBD;AACpB,CAAC;;;ACbD,IAAI,IAAE,eAAa,OAAO,aAAW,aAAW,eAAa,OAAO,SAAO,SAAO,eAAa,OAAO,SAAO,SAAO,eAAa,OAAO,OAAK,OAAK,CAAC;AAAE,SAAS,EAAEE,IAAE;AAAC,MAAIC,KAAE,EAAC,SAAQ,CAAC,EAAC;AAAE,SAAOD,GAAEC,IAAEA,GAAE,OAAO,GAAEA,GAAE;AAAO;AAAC,IAAI,IAAE,SAASD,IAAE;AAAC,SAAOA,MAAGA,GAAE,QAAM,QAAMA;AAAC;AAA3C,IAA6C,IAAE,EAAE,YAAU,OAAO,cAAY,UAAU,KAAG,EAAE,YAAU,OAAO,UAAQ,MAAM,KAAG,EAAE,YAAU,OAAO,QAAM,IAAI,KAAG,EAAE,YAAU,OAAO,KAAG,CAAC,KAAG,2BAAU;AAAC,SAAO;AAAI,EAAE,KAAG,SAAS,aAAa,EAAE;AAA5O,IAA8O,IAAE,SAASA,IAAE;AAAC,MAAG;AAAC,WAAM,CAAC,CAACA,GAAE;AAAA,EAAC,SAAOA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAC;AAA9R,IAAgS,IAAE,CAAC,EAAG,WAAU;AAAC,SAAO,KAAG,OAAO,eAAe,CAAC,GAAE,GAAE,EAAC,KAAI,WAAU;AAAC,WAAO;AAAA,EAAC,EAAC,CAAC,EAAE,CAAC;AAAC,CAAE;AAAtX,IAAwX,IAAE,CAAC,EAAE;AAA7X,IAAkZ,IAAE,OAAO;AAA3Z,IAAob,IAAE,EAAC,GAAE,KAAG,CAAC,EAAE,KAAK,EAAC,GAAE,EAAC,GAAE,CAAC,IAAE,SAASA,IAAE;AAAC,MAAIC,KAAE,EAAE,MAAKD,EAAC;AAAE,SAAM,CAAC,CAACC,MAAGA,GAAE;AAAU,IAAE,EAAC;AAAngB,IAAqgB,IAAE,SAASD,IAAEC,IAAE;AAAC,SAAM,EAAC,YAAW,EAAE,IAAED,KAAG,cAAa,EAAE,IAAEA,KAAG,UAAS,EAAE,IAAEA,KAAG,OAAMC,GAAC;AAAC;AAA1lB,IAA4lB,IAAE,CAAC,EAAE;AAAjmB,IAA0mB,IAAE,SAASD,IAAE;AAAC,SAAO,EAAE,KAAKA,EAAC,EAAE,MAAM,GAAE,EAAE;AAAC;AAAppB,IAAspB,IAAE,GAAG;AAA3pB,IAAiqB,IAAE,EAAG,WAAU;AAAC,SAAM,CAAC,OAAO,GAAG,EAAE,qBAAqB,CAAC;AAAC,CAAE,IAAE,SAASA,IAAE;AAAC,SAAM,YAAU,EAAEA,EAAC,IAAE,EAAE,KAAKA,IAAE,EAAE,IAAE,OAAOA,EAAC;AAAC,IAAE;AAAxxB,IAA+xB,IAAE,SAASA,IAAE;AAAC,MAAG,QAAMA,GAAE,OAAM,UAAU,0BAAwBA,EAAC;AAAE,SAAOA;AAAC;AAA32B,IAA62B,IAAE,SAASA,IAAE;AAAC,SAAO,EAAE,EAAEA,EAAC,CAAC;AAAC;AAAz4B,IAA24B,IAAE,SAASA,IAAE;AAAC,SAAM,YAAU,OAAOA,KAAE,SAAOA,KAAE,cAAY,OAAOA;AAAC;AAA/8B,IAAi9B,IAAE,SAASA,IAAEC,IAAE;AAAC,MAAG,CAAC,EAAED,EAAC,EAAE,QAAOA;AAAE,MAAIE,IAAEC;AAAE,MAAGF,MAAG,cAAY,QAAOC,KAAEF,GAAE,aAAW,CAAC,EAAEG,KAAED,GAAE,KAAKF,EAAC,CAAC,EAAE,QAAOG;AAAE,MAAG,cAAY,QAAOD,KAAEF,GAAE,YAAU,CAAC,EAAEG,KAAED,GAAE,KAAKF,EAAC,CAAC,EAAE,QAAOG;AAAE,MAAG,CAACF,MAAG,cAAY,QAAOC,KAAEF,GAAE,aAAW,CAAC,EAAEG,KAAED,GAAE,KAAKF,EAAC,CAAC,EAAE,QAAOG;AAAE,QAAM,UAAU,yCAAyC;AAAC;AAArvC,IAAuvC,IAAE,CAAC,EAAE;AAA5vC,IAA2wC,IAAE,SAASH,IAAEC,IAAE;AAAC,SAAO,EAAE,KAAKD,IAAEC,EAAC;AAAC;AAA7yC,IAA+yC,IAAE,EAAE;AAAnzC,IAA4zC,IAAE,EAAE,CAAC,KAAG,EAAE,EAAE,aAAa;AAAr1C,IAAu1C,IAAE,SAASD,IAAE;AAAC,SAAO,IAAE,EAAE,cAAcA,EAAC,IAAE,CAAC;AAAC;AAAn4C,IAAq4C,IAAE,CAAC,KAAG,CAAC,EAAG,WAAU;AAAC,SAAO,KAAG,OAAO,eAAe,EAAE,KAAK,GAAE,KAAI,EAAC,KAAI,WAAU;AAAC,WAAO;AAAA,EAAC,EAAC,CAAC,EAAE;AAAC,CAAE;AAAt+C,IAAw+C,IAAE,OAAO;AAAj/C,IAA0gD,IAAE,EAAC,GAAE,IAAE,IAAE,SAASA,IAAEC,IAAE;AAAC,MAAGD,KAAE,EAAEA,EAAC,GAAEC,KAAE,EAAEA,IAAE,IAAE,GAAE,EAAE,KAAG;AAAC,WAAO,EAAED,IAAEC,EAAC;AAAA,EAAC,SAAOD,IAAE;AAAA,EAAC;AAAC,MAAG,EAAEA,IAAEC,EAAC,EAAE,QAAO,EAAE,CAAC,EAAE,EAAE,KAAKD,IAAEC,EAAC,GAAED,GAAEC,EAAC,CAAC;AAAC,EAAC;AAA3nD,IAA6nD,IAAE,SAASD,IAAE;AAAC,MAAG,CAAC,EAAEA,EAAC,EAAE,OAAM,UAAU,OAAOA,EAAC,IAAE,mBAAmB;AAAE,SAAOA;AAAC;AAA3sD,IAA6sD,IAAE,OAAO;AAAttD,IAAquD,IAAE,EAAC,GAAE,IAAE,IAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,EAAEF,EAAC,GAAEC,KAAE,EAAEA,IAAE,IAAE,GAAE,EAAEC,EAAC,GAAE,EAAE,KAAG;AAAC,WAAO,EAAEF,IAAEC,IAAEC,EAAC;AAAA,EAAC,SAAOF,IAAE;AAAA,EAAC;AAAC,MAAG,SAAQE,MAAG,SAAQA,GAAE,OAAM,UAAU,yBAAyB;AAAE,SAAM,WAAUA,OAAIF,GAAEC,EAAC,IAAEC,GAAE,QAAOF;AAAC,EAAC;AAA55D,IAA85D,IAAE,IAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,EAAE,EAAEF,IAAEC,IAAE,EAAE,GAAEC,EAAC,CAAC;AAAC,IAAE,SAASF,IAAEC,IAAEC,IAAE;AAAC,SAAOF,GAAEC,EAAC,IAAEC,IAAEF;AAAC;AAAz+D,IAA2+D,IAAE,SAASA,IAAEC,IAAE;AAAC,MAAG;AAAC,MAAE,GAAED,IAAEC,EAAC;AAAA,EAAC,SAAOC,IAAE;AAAC,MAAEF,EAAC,IAAEC;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAhiE,IAAkiE,IAAE,EAAE,oBAAoB,KAAG,EAAE,sBAAqB,CAAC,CAAC;AAAtlE,IAAwlE,IAAE,SAAS;AAAS,cAAY,OAAO,EAAE,kBAAgB,EAAE,gBAAc,SAASD,IAAE;AAAC,SAAO,EAAE,KAAKA,EAAC;AAAC;AAAG,IAAI;AAAJ,IAAM;AAAN,IAAQ;AAAR,IAAU,IAAE,EAAE;AAAd,IAA4B,IAAE,EAAE;AAAhC,IAAwC,IAAE,cAAY,OAAO,KAAG,cAAc,KAAK,EAAE,CAAC,CAAC;AAAvF,IAAyF,IAAE,EAAG,SAASA,IAAE;AAAC,GAACA,GAAE,UAAQ,SAASA,IAAEC,IAAE;AAAC,WAAO,EAAED,EAAC,MAAI,EAAEA,EAAC,IAAE,WAASC,KAAEA,KAAE,CAAC;AAAA,EAAE,GAAG,YAAW,CAAC,CAAC,EAAE,KAAK,EAAC,SAAQ,SAAQ,MAAK,UAAS,WAAU,uCAAsC,CAAC;AAAC,CAAE;AAAhR,IAAkR,IAAE;AAApR,IAAsR,IAAE,KAAK,OAAO;AAApS,IAAsS,IAAE,SAASD,IAAE;AAAC,SAAM,YAAU,OAAO,WAASA,KAAE,KAAGA,EAAC,IAAE,QAAM,EAAE,IAAE,GAAG,SAAS,EAAE;AAAC;AAArX,IAAuX,IAAE,EAAE,MAAM;AAAjY,IAAmY,IAAE,SAASA,IAAE;AAAC,SAAO,EAAEA,EAAC,MAAI,EAAEA,EAAC,IAAE,EAAEA,EAAC;AAAE;AAAza,IAA2a,IAAE,CAAC;AAA9a,IAAgb,IAAE,EAAE;AAAQ,IAAG,GAAE;AAAK,MAAE,EAAE,UAAQ,EAAE,QAAM,IAAI,MAAG,IAAE,EAAE,KAAI,IAAE,EAAE,KAAII,KAAE,EAAE;AAAI,MAAE,SAASJ,IAAEC,IAAE;AAAC,WAAOA,GAAE,SAAOD,IAAEI,GAAE,KAAK,GAAEJ,IAAEC,EAAC,GAAEA;AAAA,EAAC,GAAE,IAAE,SAASD,IAAE;AAAC,WAAO,EAAE,KAAK,GAAEA,EAAC,KAAG,CAAC;AAAA,EAAC,GAAE,IAAE,SAASA,IAAE;AAAC,WAAO,EAAE,KAAK,GAAEA,EAAC;AAAA,EAAC;AAAC,OAAK;AAAK,MAAE,EAAE,OAAO;AAAE,IAAE,CAAC,IAAE,MAAG,IAAE,SAASA,IAAEC,IAAE;AAAC,WAAOA,GAAE,SAAOD,IAAE,EAAEA,IAAE,GAAEC,EAAC,GAAEA;AAAA,EAAC,GAAE,IAAE,SAASD,IAAE;AAAC,WAAO,EAAEA,IAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,CAAC;AAAA,EAAC,GAAE,IAAE,SAASA,IAAE;AAAC,WAAO,EAAEA,IAAE,CAAC;AAAA,EAAC;AAAC;AAA3T;AAA2B;AAAQ;AAAQ,IAAAI;AAA4I;AAAqI,IAAI,IAAE,EAAC,KAAI,GAAE,KAAI,GAAE,KAAI,GAAE,SAAQ,SAASJ,IAAE;AAAC,SAAO,EAAEA,EAAC,IAAE,EAAEA,EAAC,IAAE,EAAEA,IAAE,CAAC,CAAC;AAAC,GAAE,WAAU,SAASA,IAAE;AAAC,SAAO,SAASC,IAAE;AAAC,QAAIC;AAAE,QAAG,CAAC,EAAED,EAAC,MAAIC,KAAE,EAAED,EAAC,GAAG,SAAOD,GAAE,OAAM,UAAU,4BAA0BA,KAAE,WAAW;AAAE,WAAOE;AAAA,EAAC;AAAC,EAAC;AAArN,IAAuN,IAAE,EAAG,SAASF,IAAE;AAAC,MAAIC,KAAE,EAAE,KAAIC,KAAE,EAAE,SAAQG,KAAE,OAAO,MAAM,EAAE,MAAM,QAAQ;AAAE,GAACL,GAAE,UAAQ,SAASA,IAAEC,IAAEK,IAAEC,IAAE;AAAC,QAAIC,IAAEC,KAAE,CAAC,CAACF,MAAG,CAAC,CAACA,GAAE,QAAOG,KAAE,CAAC,CAACH,MAAG,CAAC,CAACA,GAAE,YAAWI,KAAE,CAAC,CAACJ,MAAG,CAAC,CAACA,GAAE;AAAY,kBAAY,OAAOD,OAAI,YAAU,OAAOL,MAAG,EAAEK,IAAE,MAAM,KAAG,EAAEA,IAAE,QAAOL,EAAC,IAAGO,KAAEN,GAAEI,EAAC,GAAG,WAASE,GAAE,SAAOH,GAAE,KAAK,YAAU,OAAOJ,KAAEA,KAAE,EAAE,KAAID,OAAI,KAAGS,KAAE,CAACE,MAAGX,GAAEC,EAAC,MAAIS,KAAE,QAAI,OAAOV,GAAEC,EAAC,GAAES,KAAEV,GAAEC,EAAC,IAAEK,KAAE,EAAEN,IAAEC,IAAEK,EAAC,KAAGI,KAAEV,GAAEC,EAAC,IAAEK,KAAE,EAAEL,IAAEK,EAAC;AAAA,EAAC,GAAG,SAAS,WAAU,YAAY,WAAU;AAAC,WAAM,cAAY,OAAO,QAAML,GAAE,IAAI,EAAE,UAAQ,EAAE,IAAI;AAAA,EAAC,CAAE;AAAC,CAAE;AAAtrB,IAAwrB,KAAG;AAA3rB,IAA6rB,KAAG,SAASD,IAAE;AAAC,SAAM,cAAY,OAAOA,KAAEA,KAAE;AAAM;AAA/uB,IAAivB,KAAG,SAASA,IAAEC,IAAE;AAAC,SAAO,UAAU,SAAO,IAAE,GAAG,GAAGD,EAAC,CAAC,KAAG,GAAG,EAAEA,EAAC,CAAC,IAAE,GAAGA,EAAC,KAAG,GAAGA,EAAC,EAAEC,EAAC,KAAG,EAAED,EAAC,KAAG,EAAEA,EAAC,EAAEC,EAAC;AAAC;AAA90B,IAAg1B,KAAG,KAAK;AAAx1B,IAA61B,KAAG,KAAK;AAAr2B,IAA22B,KAAG,SAASD,IAAE;AAAC,SAAO,MAAMA,KAAE,CAACA,EAAC,IAAE,KAAGA,KAAE,IAAE,KAAG,IAAIA,EAAC;AAAC;AAA75B,IAA+5B,KAAG,KAAK;AAAv6B,IAA26B,KAAG,SAASA,IAAE;AAAC,SAAOA,KAAE,IAAE,GAAG,GAAGA,EAAC,GAAE,gBAAgB,IAAE;AAAC;AAAj+B,IAAm+B,KAAG,KAAK;AAA3+B,IAA++B,KAAG,KAAK;AAAv/B,IAA2/B,KAAG,SAASA,IAAEC,IAAE;AAAC,MAAIC,KAAE,GAAGF,EAAC;AAAE,SAAOE,KAAE,IAAE,GAAGA,KAAED,IAAE,CAAC,IAAE,GAAGC,IAAED,EAAC;AAAC;AAApjC,IAAsjC,KAAG,SAASD,IAAE;AAAC,SAAO,SAASC,IAAEC,IAAEC,IAAE;AAAC,QAAIE,IAAEC,KAAE,EAAEL,EAAC,GAAEM,KAAE,GAAGD,GAAE,MAAM,GAAEE,KAAE,GAAGL,IAAEI,EAAC;AAAE,QAAGP,MAAGE,MAAGA,IAAE;AAAC,aAAKK,KAAEC,KAAG,MAAIH,KAAEC,GAAEE,IAAG,MAAIH,GAAE,QAAM;AAAA,IAAE,MAAM,QAAKE,KAAEC,IAAEA,KAAI,MAAIR,MAAGQ,MAAKF,OAAIA,GAAEE,EAAC,MAAIN,GAAE,QAAOF,MAAGQ,MAAG;AAAE,WAAM,CAACR,MAAG;AAAA,EAAE;AAAC;AAAzvC,IAA2vC,KAAG,EAAC,UAAS,GAAG,IAAE,GAAE,SAAQ,GAAG,KAAE,EAAC;AAA7xC,IAA+xC,KAAG,GAAG;AAAryC,IAA6yC,KAAG,SAASA,IAAEC,IAAE;AAAC,MAAIC,IAAEC,KAAE,EAAEH,EAAC,GAAEK,KAAE,GAAEC,KAAE,CAAC;AAAE,OAAIJ,MAAKC,GAAE,EAAC,EAAE,GAAED,EAAC,KAAG,EAAEC,IAAED,EAAC,KAAGI,GAAE,KAAKJ,EAAC;AAAE,SAAKD,GAAE,SAAOI,KAAG,GAAEF,IAAED,KAAED,GAAEI,IAAG,CAAC,MAAI,CAAC,GAAGC,IAAEJ,EAAC,KAAGI,GAAE,KAAKJ,EAAC;AAAG,SAAOI;AAAC;AAAx7C,IAA07C,KAAG,CAAC,eAAc,kBAAiB,iBAAgB,wBAAuB,kBAAiB,YAAW,SAAS;AAAziD,IAA2iD,KAAG,GAAG,OAAO,UAAS,WAAW;AAA5kD,IAA8kD,KAAG,EAAC,GAAE,OAAO,uBAAqB,SAASN,IAAE;AAAC,SAAO,GAAGA,IAAE,EAAE;AAAC,EAAC;AAA5oD,IAA8oD,KAAG,EAAC,GAAE,OAAO,sBAAqB;AAAhrD,IAAkrD,KAAG,GAAG,WAAU,SAAS,KAAG,SAASA,IAAE;AAAC,MAAIC,KAAE,GAAG,EAAE,EAAED,EAAC,CAAC,GAAEE,KAAE,GAAG;AAAE,SAAOA,KAAED,GAAE,OAAOC,GAAEF,EAAC,CAAC,IAAEC;AAAC;AAA3wD,IAA6wD,KAAG,SAASD,IAAEC,IAAE;AAAC,WAAQC,KAAE,GAAGD,EAAC,GAAEE,KAAE,EAAE,GAAEE,KAAE,EAAE,GAAEC,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,MAAI;AAAC,QAAIC,KAAEL,GAAEI,EAAC;AAAE,MAAEN,IAAEO,EAAC,KAAGJ,GAAEH,IAAEO,IAAEF,GAAEJ,IAAEM,EAAC,CAAC;AAAA,EAAC;AAAC;AAA/2D,IAAi3D,KAAG;AAAp3D,IAAs4D,KAAG,SAASP,IAAEC,IAAE;AAAC,MAAIC,KAAE,GAAG,GAAGF,EAAC,CAAC;AAAE,SAAOE,MAAG,MAAIA,MAAG,OAAK,cAAY,OAAOD,KAAE,EAAEA,EAAC,IAAE,CAAC,CAACA;AAAE;AAA39D,IAA69D,KAAG,GAAG,YAAU,SAASD,IAAE;AAAC,SAAO,OAAOA,EAAC,EAAE,QAAQ,IAAG,GAAG,EAAE,YAAY;AAAC;AAAviE,IAAyiE,KAAG,GAAG,OAAK,CAAC;AAArjE,IAAujE,KAAG,GAAG,SAAO;AAApkE,IAAwkE,KAAG,GAAG,WAAS;AAAvlE,IAA2lE,KAAG;AAA9lE,IAAimE,KAAG,EAAE;AAAtmE,IAAwmE,KAAG,SAASA,IAAEC,IAAE;AAAC,MAAIC,IAAEG,IAAEC,IAAEC,IAAEC,IAAEC,KAAET,GAAE,QAAOU,KAAEV,GAAE,QAAOW,KAAEX,GAAE;AAAK,MAAGE,KAAEQ,KAAE,IAAEC,KAAE,EAAEF,EAAC,KAAG,EAAEA,IAAE,CAAC,CAAC,KAAG,EAAEA,EAAC,KAAG,CAAC,GAAG,UAAU,MAAIJ,MAAKJ,IAAE;AAAC,QAAGM,KAAEN,GAAEI,EAAC,GAAEC,KAAEN,GAAE,eAAaQ,KAAE,GAAGN,IAAEG,EAAC,MAAIG,GAAE,QAAMN,GAAEG,EAAC,GAAE,CAAC,GAAGK,KAAEL,KAAEI,MAAGE,KAAE,MAAI,OAAKN,IAAEL,GAAE,MAAM,KAAG,WAASM,IAAE;AAAC,UAAG,OAAOC,MAAG,OAAOD,GAAE;AAAS,SAAGC,IAAED,EAAC;AAAA,IAAC;AAAC,KAACN,GAAE,QAAMM,MAAGA,GAAE,SAAO,EAAEC,IAAE,QAAO,IAAE,GAAE,EAAEL,IAAEG,IAAEE,IAAEP,EAAC;AAAA,EAAC;AAAC;AAAz5E,IAA25E,KAAG,SAASA,IAAEC,IAAE;AAAC,MAAIC,KAAE,CAAC,EAAEF,EAAC;AAAE,SAAM,CAAC,CAACE,MAAG,EAAG,WAAU;AAAC,IAAAA,GAAE,KAAK,MAAKD,MAAG,WAAU;AAAC,YAAM;AAAA,IAAC,GAAE,CAAC;AAAA,EAAC,CAAE;AAAC;AAAz/E,IAA2/E,KAAG,OAAO;AAArgF,IAAohF,KAAG,CAAC;AAAxhF,IAA0hF,KAAG,SAASD,IAAE;AAAC,QAAMA;AAAC;AAAhjF,IAAkjF,KAAG,SAASA,IAAEC,IAAE;AAAC,MAAG,EAAE,IAAGD,EAAC,EAAE,QAAO,GAAGA,EAAC;AAAE,EAAAC,OAAIA,KAAE,CAAC;AAAG,MAAIC,KAAE,CAAC,EAAEF,EAAC,GAAEG,KAAE,CAAC,CAAC,EAAEF,IAAE,WAAW,KAAGA,GAAE,WAAUM,KAAE,EAAEN,IAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,IAAGO,KAAE,EAAEP,IAAE,CAAC,IAAEA,GAAE,CAAC,IAAE;AAAO,SAAO,GAAGD,EAAC,IAAE,CAAC,CAACE,MAAG,CAAC,EAAG,WAAU;AAAC,QAAGC,MAAG,CAAC,EAAE,QAAM;AAAG,QAAIH,KAAE,EAAC,QAAO,GAAE;AAAE,IAAAG,KAAE,GAAGH,IAAE,GAAE,EAAC,YAAW,MAAG,KAAI,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,GAAEE,GAAE,KAAKF,IAAEO,IAAEC,EAAC;AAAA,EAAC,CAAE;AAAC;AAAtzF,IAAwzF,KAAG,GAAG;AAA9zF,IAAs0F,KAAG,CAAC,EAAE;AAA50F,IAAo1F,KAAG,CAAC,CAAC,MAAI,IAAE,CAAC,CAAC,EAAE,QAAQ,GAAE,EAAE,IAAE;AAAj3F,IAAm3F,KAAG,GAAG,SAAS;AAAl4F,IAAo4F,KAAG,GAAG,WAAU,EAAC,WAAU,MAAG,GAAE,EAAC,CAAC;AAAE,SAAS,GAAGR,IAAEC,IAAE;AAAC,MAAG,EAAED,cAAaC,IAAG,OAAM,IAAI,UAAU,mCAAmC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,QAAIC,KAAEF,GAAEC,EAAC;AAAE,IAAAC,GAAE,aAAWA,GAAE,cAAY,OAAGA,GAAE,eAAa,MAAG,WAAUA,OAAIA,GAAE,WAAS,OAAI,OAAO,eAAeH,IAAEG,GAAE,KAAIA,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAE;AAAC,SAAOD,MAAG,GAAGD,GAAE,WAAUC,EAAC,GAAEC,MAAG,GAAGF,IAAEE,EAAC,GAAEF;AAAC;AAAC,GAAG,EAAC,QAAO,SAAQ,OAAM,MAAG,QAAO,MAAI,CAAC,MAAI,CAAC,GAAE,GAAE,EAAC,SAAQ,SAASA,IAAE;AAAC,SAAO,KAAG,GAAG,MAAM,MAAK,SAAS,KAAG,IAAE,GAAG,MAAKA,IAAE,UAAU,SAAO,IAAE,UAAU,CAAC,IAAE,MAAM;AAAC,EAAC,CAAC;AAAE,IAAI,KAAG,WAAU;AAAC,WAASA,KAAG;AAAC,OAAG,MAAKA,EAAC;AAAA,EAAC;AAAC,SAAO,GAAGA,IAAE,MAAK,CAAC,EAAC,KAAI,eAAc,OAAM,WAAU;AAAC,WAAM,eAAa,OAAO;AAAA,EAAM,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,WAAU;AAAC,WAAM,eAAa,OAAO;AAAA,EAAM,EAAC,GAAE,EAAC,KAAI,SAAQ,OAAM,WAAU;AAAC,WAAOA,GAAE,YAAY,IAAE,OAAO,UAAU,UAAU,YAAY,IAAE;AAAA,EAAE,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,WAAU;AAAC,WAAM,4CAA4C,KAAK,UAAU,UAAU;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,WAAU,OAAM,WAAU;AAAC,WAAM,OAAK,UAAU,UAAU,QAAQ,OAAO;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,WAAU;AAAC,QAAIC,KAAED,GAAE,MAAM;AAAE,WAAM,OAAKC,MAAGA,GAAE,QAAQ,MAAM,IAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,SAAQ,OAAM,WAAU;AAAC,QAAIA,KAAED,GAAE,MAAM;AAAE,WAAM,OAAKC,MAAGA,GAAE,QAAQ,UAAU,IAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,WAAU;AAAC,QAAIA,KAAED,GAAE,MAAM;AAAE,WAAM,OAAKC,MAAGA,GAAE,QAAQ,OAAO,IAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,WAAU;AAAC,QAAIA,KAAED,GAAE,MAAM;AAAE,WAAM,OAAKC,MAAG,cAAc,KAAKA,EAAC,KAAG,CAACD,GAAE,OAAO;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,WAAU;AAAC,QAAIC,KAAED,GAAE,MAAM;AAAE,WAAM,OAAKC,MAAG,YAAY,KAAKA,EAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,WAAU;AAAC,QAAIA,KAAED,GAAE,MAAM;AAAE,WAAM,OAAKC,MAAG,UAAU,KAAKA,EAAC;AAAA,EAAC,EAAC,CAAC,CAAC,GAAED;AAAC,EAAE;AAA5/B,IAA8/B,KAAG,CAAC,EAAE;AAApgC,IAAygC,KAAG,KAAG;AAA/gC,IAAshC,KAAG,GAAG,QAAO,GAAG;AAAE,GAAG,EAAC,QAAO,SAAQ,OAAM,MAAG,QAAO,MAAI,CAAC,GAAE,GAAE,EAAC,MAAK,SAASA,IAAE;AAAC,SAAO,GAAG,KAAK,EAAE,IAAI,GAAE,WAASA,KAAE,MAAIA,EAAC;AAAC,EAAC,CAAC;AAAE,IAAI;AAAJ,IAAO;AAAP,IAAU,KAAG,SAASA,IAAE;AAAC,SAAO,OAAO,EAAEA,EAAC,CAAC;AAAC;AAA5C,IAA8C,KAAG,MAAM,WAAS,SAASA,IAAE;AAAC,SAAM,WAAS,EAAEA,EAAC;AAAC;AAA/F,IAAiG,KAAG,CAAC,CAAC,OAAO,yBAAuB,CAAC,EAAG,WAAU;AAAC,SAAM,CAAC,OAAO,OAAO,CAAC;AAAC,CAAE;AAA5K,IAA8K,KAAG,MAAI,CAAC,OAAO,QAAM,YAAU,OAAO,OAAO;AAA3N,IAAoO,KAAG,EAAE,KAAK;AAA9O,IAAgP,KAAG,EAAE;AAArP,IAA4P,KAAG,KAAG,KAAG,MAAI,GAAG,iBAAe;AAA3R,IAA6R,KAAG,SAASA,IAAE;AAAC,SAAO,EAAE,IAAGA,EAAC,MAAI,MAAI,EAAE,IAAGA,EAAC,IAAE,GAAGA,EAAC,IAAE,GAAGA,EAAC,IAAE,GAAGA,EAAC,IAAE,GAAG,YAAUA,EAAC,IAAG,GAAGA,EAAC;AAAC;AAAjX,IAAmX,KAAG,GAAG,SAAS;AAAlY,IAAoY,KAAG,SAASA,IAAEC,IAAE;AAAC,MAAIC;AAAE,SAAO,GAAGF,EAAC,MAAI,cAAY,QAAOE,KAAEF,GAAE,gBAAcE,OAAI,SAAO,CAAC,GAAGA,GAAE,SAAS,IAAE,EAAEA,EAAC,KAAG,UAAQA,KAAEA,GAAE,EAAE,OAAKA,KAAE,UAAQA,KAAE,SAAQ,KAAI,WAASA,KAAE,QAAMA,IAAG,MAAID,KAAE,IAAEA,EAAC;AAAC;AAA1jB,IAA4jB,KAAG,SAASD,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,EAAEF,EAAC;AAAE,EAAAE,MAAKH,KAAE,EAAE,EAAEA,IAAEG,IAAE,EAAE,GAAED,EAAC,CAAC,IAAEF,GAAEG,EAAC,IAAED;AAAC;AAAvnB,IAAynB,KAAG,GAAG,aAAY,WAAW,KAAG;AAAzpB,IAA4pB,KAAG,EAAE;AAAjqB,IAAyqB,KAAG,MAAI,GAAG;AAAnrB,IAA4rB,KAAG,MAAI,GAAG;AAAG,KAAG,MAAI,KAAG,GAAG,MAAM,GAAG,GAAG,CAAC,IAAE,GAAG,CAAC,IAAE,OAAK,EAAE,KAAG,GAAG,MAAM,aAAa,MAAI,GAAG,CAAC,KAAG,QAAM,KAAG,GAAG,MAAM,eAAe,OAAK,KAAG,GAAG,CAAC;AAAG,IAAI,KAAG,MAAI,CAAC;AAAZ,IAAe,KAAG,GAAG,SAAS;AAA9B,IAAgC,KAAG,SAASF,IAAE;AAAC,SAAO,MAAI,MAAI,CAAC,EAAG,WAAU;AAAC,QAAIC,KAAE,CAAC;AAAE,YAAOA,GAAE,cAAY,CAAC,GAAG,EAAE,IAAE,WAAU;AAAC,aAAM,EAAC,KAAI,EAAC;AAAA,IAAC,GAAE,MAAIA,GAAED,EAAC,EAAE,OAAO,EAAE;AAAA,EAAG,CAAE;AAAC;AAArK,IAAuK,KAAG,GAAG,QAAQ;AAArL,IAAuL,KAAG,GAAG,UAAS,EAAC,WAAU,MAAG,GAAE,GAAE,GAAE,EAAC,CAAC;AAA5N,IAA8N,KAAG,KAAK;AAAtO,IAA0O,KAAG,KAAK;AAAI,GAAG,EAAC,QAAO,SAAQ,OAAM,MAAG,QAAO,CAAC,MAAI,CAAC,GAAE,GAAE,EAAC,QAAO,SAASA,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEE,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,GAAG,IAAI,GAAEC,KAAE,GAAGD,GAAE,MAAM,GAAEE,KAAE,GAAGX,IAAEU,EAAC,GAAEE,KAAE,UAAU;AAAO,MAAG,MAAIA,KAAEV,KAAEC,KAAE,IAAE,MAAIS,MAAGV,KAAE,GAAEC,KAAEO,KAAEC,OAAIT,KAAEU,KAAE,GAAET,KAAE,GAAG,GAAG,GAAGF,EAAC,GAAE,CAAC,GAAES,KAAEC,EAAC,IAAGD,KAAER,KAAEC,KAAE,iBAAiB,OAAM,UAAU,iCAAiC;AAAE,OAAIE,KAAE,GAAGI,IAAEN,EAAC,GAAEG,KAAE,GAAEA,KAAEH,IAAEG,KAAI,EAACC,KAAEI,KAAEL,OAAKG,MAAG,GAAGJ,IAAEC,IAAEG,GAAEF,EAAC,CAAC;AAAE,MAAGF,GAAE,SAAOF,IAAED,KAAEC,IAAE;AAAC,SAAIG,KAAEK,IAAEL,KAAEI,KAAEP,IAAEG,KAAI,CAAAE,KAAEF,KAAEJ,KAAGK,KAAED,KAAEH,OAAKM,KAAEA,GAAED,EAAC,IAAEC,GAAEF,EAAC,IAAE,OAAOE,GAAED,EAAC;AAAE,SAAIF,KAAEI,IAAEJ,KAAEI,KAAEP,KAAED,IAAEI,KAAI,QAAOG,GAAEH,KAAE,CAAC;AAAA,EAAC,WAASJ,KAAEC,GAAE,MAAIG,KAAEI,KAAEP,IAAEG,KAAEK,IAAEL,KAAI,CAAAE,KAAEF,KAAEJ,KAAE,IAAGK,KAAED,KAAEH,KAAE,MAAKM,KAAEA,GAAED,EAAC,IAAEC,GAAEF,EAAC,IAAE,OAAOE,GAAED,EAAC;AAAE,OAAIF,KAAE,GAAEA,KAAEJ,IAAEI,KAAI,CAAAG,GAAEH,KAAEK,EAAC,IAAE,UAAUL,KAAE,CAAC;AAAE,SAAOG,GAAE,SAAOC,KAAEP,KAAED,IAAEG;AAAC,EAAC,CAAC;AAAE,IAAI,KAAG,CAAC;AAAE,GAAG,GAAG,aAAa,CAAC,IAAE;AAAI,IAAI,KAAG,iBAAe,OAAO,EAAE;AAA/B,IAAiC,KAAG,GAAG,aAAa;AAApD,IAAsD,KAAG,eAAa,EAAE,2BAAU;AAAC,SAAO;AAAS,EAAE,CAAC;AAAtG,IAAwG,KAAG,KAAG,IAAE,SAASL,IAAE;AAAC,MAAIC,IAAEC,IAAEC;AAAE,SAAO,WAASH,KAAE,cAAY,SAAOA,KAAE,SAAO,YAAU,QAAOE,KAAE,SAASF,IAAEC,IAAE;AAAC,QAAG;AAAC,aAAOD,GAAEC,EAAC;AAAA,IAAC,SAAOD,IAAE;AAAA,IAAC;AAAA,EAAC,EAAEC,KAAE,OAAOD,EAAC,GAAE,EAAE,KAAGE,KAAE,KAAG,EAAED,EAAC,IAAE,aAAWE,KAAE,EAAEF,EAAC,MAAI,cAAY,OAAOA,GAAE,SAAO,cAAYE;AAAC;AAAzU,IAA2U,KAAG,KAAG,CAAC,EAAE,WAAS,WAAU;AAAC,SAAM,aAAW,GAAG,IAAI,IAAE;AAAG;AAAE,MAAI,EAAE,OAAO,WAAU,YAAW,IAAG,EAAC,QAAO,KAAE,CAAC;AAAE,IAAI,KAAG,WAAU;AAAC,MAAIH,KAAE,EAAE,IAAI,GAAEC,KAAE;AAAG,SAAOD,GAAE,WAASC,MAAG,MAAKD,GAAE,eAAaC,MAAG,MAAKD,GAAE,cAAYC,MAAG,MAAKD,GAAE,WAASC,MAAG,MAAKD,GAAE,YAAUC,MAAG,MAAKD,GAAE,WAASC,MAAG,MAAKA;AAAC;AAAE,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAO,OAAOD,IAAEC,EAAC;AAAC;AAAC,IAAI;AAAJ,IAAO;AAAP,IAAU,KAAG,EAAC,eAAc,EAAG,WAAU;AAAC,MAAID,KAAE,GAAG,KAAI,GAAG;AAAE,SAAOA,GAAE,YAAU,GAAE,QAAMA,GAAE,KAAK,MAAM;AAAC,CAAE,GAAE,cAAa,EAAG,WAAU;AAAC,MAAIA,KAAE,GAAG,MAAK,IAAI;AAAE,SAAOA,GAAE,YAAU,GAAE,QAAMA,GAAE,KAAK,KAAK;AAAC,CAAE,EAAC;AAAnM,IAAqM,KAAG,OAAO,UAAU;AAAzN,IAA8N,KAAG,OAAO,UAAU;AAAlP,IAA0P,KAAG;AAA7P,IAAgQ,MAAI,KAAG,KAAI,KAAG,OAAM,GAAG,KAAK,IAAG,GAAG,GAAE,GAAG,KAAK,IAAG,GAAG,GAAE,MAAI,GAAG,aAAW,MAAI,GAAG;AAA7U,IAAwV,KAAG,GAAG,iBAAe,GAAG;AAAhX,IAA6X,KAAG,WAAS,OAAO,KAAK,EAAE,EAAE,CAAC;AAAA,CAAG,MAAI,MAAI,QAAM,KAAG,SAASA,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEE,IAAEC,KAAE,MAAKC,KAAE,MAAID,GAAE,QAAOE,KAAE,GAAG,KAAKF,EAAC,GAAEG,KAAEH,GAAE,QAAOI,KAAE,GAAEC,KAAEX;AAAE,SAAOO,OAAI,QAAMC,KAAEA,GAAE,QAAQ,KAAI,EAAE,GAAG,QAAQ,GAAG,MAAIA,MAAG,MAAKG,KAAE,OAAOX,EAAC,EAAE,MAAMM,GAAE,SAAS,GAAEA,GAAE,YAAU,MAAI,CAACA,GAAE,aAAWA,GAAE,aAAW,SAAON,GAAEM,GAAE,YAAU,CAAC,OAAKG,KAAE,SAAOA,KAAE,KAAIE,KAAE,MAAIA,IAAED,OAAKR,KAAE,IAAI,OAAO,SAAOO,KAAE,KAAID,EAAC,IAAG,OAAKN,KAAE,IAAI,OAAO,MAAIO,KAAE,YAAWD,EAAC,IAAG,OAAKP,KAAEK,GAAE,YAAWH,KAAE,GAAG,KAAKI,KAAEL,KAAEI,IAAEK,EAAC,GAAEJ,KAAEJ,MAAGA,GAAE,QAAMA,GAAE,MAAM,MAAMO,EAAC,GAAEP,GAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,MAAMO,EAAC,GAAEP,GAAE,QAAMG,GAAE,WAAUA,GAAE,aAAWH,GAAE,CAAC,EAAE,UAAQG,GAAE,YAAU,IAAE,MAAIH,OAAIG,GAAE,YAAUA,GAAE,SAAOH,GAAE,QAAMA,GAAE,CAAC,EAAE,SAAOF,KAAG,MAAIE,MAAGA,GAAE,SAAO,KAAG,GAAG,KAAKA,GAAE,CAAC,GAAED,IAAG,WAAU;AAAC,SAAIG,KAAE,GAAEA,KAAE,UAAU,SAAO,GAAEA,KAAI,YAAS,UAAUA,EAAC,MAAIF,GAAEE,EAAC,IAAE;AAAA,EAAO,CAAE,GAAEF;AAAC;AAAG,IAAI,KAAG;AAAG,GAAG,EAAC,QAAO,UAAS,OAAM,MAAG,QAAO,IAAI,SAAO,GAAE,GAAE,EAAC,MAAK,GAAE,CAAC;AAAE,IAAI,KAAG,OAAO;AAAd,IAAwB,KAAG,GAAG;AAA9B,IAAuC,KAAG,EAAG,WAAU;AAAC,SAAM,UAAQ,GAAG,KAAK,EAAC,QAAO,KAAI,OAAM,IAAG,CAAC;AAAC,CAAE;AAAvG,IAAyG,KAAG,cAAY,GAAG;AAAA,CAAM,MAAI,OAAK,EAAE,OAAO,WAAU,YAAY,WAAU;AAAC,MAAIH,KAAE,EAAE,IAAI,GAAEC,KAAE,OAAOD,GAAE,MAAM,GAAEE,KAAEF,GAAE;AAAM,SAAM,MAAIC,KAAE,MAAI,OAAO,WAASC,MAAGF,cAAa,UAAQ,EAAE,WAAU,MAAI,GAAG,KAAKA,EAAC,IAAEE,EAAC;AAAC,GAAG,EAAC,QAAO,KAAE,CAAC;AAAE,IAAI,KAAG,GAAG,SAAS;AAAnB,IAAqB,KAAG,CAAC,EAAG,WAAU;AAAC,MAAIF,KAAE;AAAI,SAAOA,GAAE,OAAK,WAAU;AAAC,QAAIA,KAAE,CAAC;AAAE,WAAOA,GAAE,SAAO,EAAC,GAAE,IAAG,GAAEA;AAAA,EAAC,GAAE,QAAM,GAAG,QAAQA,IAAE,MAAM;AAAC,CAAE;AAA1I,IAA4I,KAAG,SAAO,IAAI,QAAQ,KAAI,IAAI;AAA1K,IAA4K,KAAG,GAAG,SAAS;AAA3L,IAA6L,KAAG,CAAC,CAAC,IAAI,EAAE,KAAG,OAAK,IAAI,EAAE,EAAE,KAAI,IAAI;AAAhO,IAAkO,KAAG,CAAC,EAAG,WAAU;AAAC,MAAIA,KAAE,QAAOC,KAAED,GAAE;AAAK,EAAAA,GAAE,OAAK,WAAU;AAAC,WAAOC,GAAE,MAAM,MAAK,SAAS;AAAA,EAAC;AAAE,MAAIC,KAAE,KAAK,MAAMF,EAAC;AAAE,SAAO,MAAIE,GAAE,UAAQ,QAAMA,GAAE,CAAC,KAAG,QAAMA,GAAE,CAAC;AAAC,CAAE;AAA7X,IAA+X,KAAG,SAASF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIG,KAAE,GAAGN,EAAC,GAAEO,KAAE,CAAC,EAAG,WAAU;AAAC,QAAIN,KAAE,CAAC;AAAE,WAAOA,GAAEK,EAAC,IAAE,WAAU;AAAC,aAAO;AAAA,IAAC,GAAE,KAAG,GAAGN,EAAC,EAAEC,EAAC;AAAA,EAAC,CAAE,GAAEO,KAAED,MAAG,CAAC,EAAG,WAAU;AAAC,QAAIN,KAAE,OAAGC,KAAE;AAAI,WAAM,YAAUF,QAAKE,KAAE,CAAC,GAAG,cAAY,CAAC,GAAEA,GAAE,YAAY,EAAE,IAAE,WAAU;AAAC,aAAOA;AAAA,IAAC,GAAEA,GAAE,QAAM,IAAGA,GAAEI,EAAC,IAAE,IAAIA,EAAC,IAAGJ,GAAE,OAAK,WAAU;AAAC,aAAOD,KAAE,MAAG;AAAA,IAAI,GAAEC,GAAEI,EAAC,EAAE,EAAE,GAAE,CAACL;AAAA,EAAC,CAAE;AAAE,MAAG,CAACM,MAAG,CAACC,MAAG,cAAYR,OAAI,CAAC,MAAI,CAAC,MAAI,OAAK,YAAUA,MAAG,CAAC,IAAG;AAAC,QAAIS,KAAE,IAAIH,EAAC,GAAEI,KAAER,GAAEI,IAAE,GAAGN,EAAC,GAAG,SAASA,IAAEC,IAAEC,IAAEC,IAAEE,IAAE;AAAC,aAAOJ,GAAE,SAAO,KAAGM,MAAG,CAACF,KAAE,EAAC,MAAK,MAAG,OAAMI,GAAE,KAAKR,IAAEC,IAAEC,EAAC,EAAC,IAAE,EAAC,MAAK,MAAG,OAAMH,GAAE,KAAKE,IAAED,IAAEE,EAAC,EAAC,IAAE,EAAC,MAAK,MAAE;AAAA,IAAC,GAAG,EAAC,kBAAiB,IAAG,8CAA6C,GAAE,CAAC,GAAEQ,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC;AAAE,MAAE,OAAO,WAAUV,IAAEW,EAAC,GAAE,EAAE,OAAO,WAAUL,IAAE,KAAGL,KAAE,SAASD,IAAEC,IAAE;AAAC,aAAOW,GAAE,KAAKZ,IAAE,MAAKC,EAAC;AAAA,IAAC,IAAE,SAASD,IAAE;AAAC,aAAOY,GAAE,KAAKZ,IAAE,IAAI;AAAA,IAAC,CAAC;AAAA,EAAC;AAAC,EAAAG,MAAG,EAAE,OAAO,UAAUG,EAAC,GAAE,QAAO,IAAE;AAAC;AAAzmC,IAA2mC,KAAG,GAAG,OAAO;AAAxnC,IAA0nC,KAAG,SAASN,IAAE;AAAC,MAAIC;AAAE,SAAO,EAAED,EAAC,MAAI,YAAUC,KAAED,GAAE,EAAE,KAAG,CAAC,CAACC,KAAE,YAAU,EAAED,EAAC;AAAE;AAAnsC,IAAqsC,KAAG,SAASA,IAAE;AAAC,MAAG,cAAY,OAAOA,GAAE,OAAM,UAAU,OAAOA,EAAC,IAAE,oBAAoB;AAAE,SAAOA;AAAC;AAApyC,IAAsyC,KAAG,GAAG,SAAS;AAArzC,IAAuzC,KAAG,SAASA,IAAE;AAAC,SAAO,SAASC,IAAEC,IAAE;AAAC,QAAIC,IAAEE,IAAEC,KAAE,OAAO,EAAEL,EAAC,CAAC,GAAEM,KAAE,GAAGL,EAAC,GAAEM,KAAEF,GAAE;AAAO,WAAOC,KAAE,KAAGA,MAAGC,KAAER,KAAE,KAAG,UAAQG,KAAEG,GAAE,WAAWC,EAAC,KAAG,SAAOJ,KAAE,SAAOI,KAAE,MAAIC,OAAIH,KAAEC,GAAE,WAAWC,KAAE,CAAC,KAAG,SAAOF,KAAE,QAAML,KAAEM,GAAE,OAAOC,EAAC,IAAEJ,KAAEH,KAAEM,GAAE,MAAMC,IAAEA,KAAE,CAAC,IAAEF,KAAE,SAAOF,KAAE,SAAO,MAAI;AAAA,EAAK;AAAC;AAAjjD,IAAmjD,KAAG,EAAC,QAAO,GAAG,KAAE,GAAE,QAAO,GAAG,IAAE,EAAC;AAAllD,IAAolD,KAAG,GAAG;AAA1lD,IAAimD,KAAG,SAASH,IAAEC,IAAEC,IAAE;AAAC,SAAOD,MAAGC,KAAE,GAAGF,IAAEC,EAAC,EAAE,SAAO;AAAE;AAAjpD,IAAmpD,KAAG,SAASD,IAAEC,IAAE;AAAC,MAAIC,KAAEF,GAAE;AAAK,MAAG,cAAY,OAAOE,IAAE;AAAC,QAAIC,KAAED,GAAE,KAAKF,IAAEC,EAAC;AAAE,QAAG,YAAU,OAAOE,GAAE,OAAM,UAAU,oEAAoE;AAAE,WAAOA;AAAA,EAAC;AAAC,MAAG,aAAW,EAAEH,EAAC,EAAE,OAAM,UAAU,6CAA6C;AAAE,SAAO,GAAG,KAAKA,IAAEC,EAAC;AAAC;AAAt7D,IAAw7D,KAAG,CAAC,EAAE;AAA97D,IAAm8D,KAAG,KAAK;AAA38D,IAA+8D,KAAG,CAAC,EAAG,WAAU;AAAC,SAAM,CAAC,OAAO,YAAW,GAAG;AAAC,CAAE;AAAE,GAAG,SAAQ,GAAG,SAASD,IAAEC,IAAEC,IAAE;AAAC,MAAIC;AAAE,SAAOA,KAAE,OAAK,OAAO,MAAM,MAAM,EAAE,CAAC,KAAG,KAAG,OAAO,MAAM,QAAO,EAAE,EAAE,UAAQ,KAAG,KAAK,MAAM,SAAS,EAAE,UAAQ,KAAG,IAAI,MAAM,UAAU,EAAE,UAAQ,IAAI,MAAM,MAAM,EAAE,SAAO,KAAG,GAAG,MAAM,IAAI,EAAE,SAAO,SAASH,IAAEE,IAAE;AAAC,QAAIC,KAAE,OAAO,EAAE,IAAI,CAAC,GAAEE,KAAE,WAASH,KAAE,aAAWA,OAAI;AAAE,QAAG,MAAIG,GAAE,QAAM,CAAC;AAAE,QAAG,WAASL,GAAE,QAAM,CAACG,EAAC;AAAE,QAAG,CAAC,GAAGH,EAAC,EAAE,QAAOC,GAAE,KAAKE,IAAEH,IAAEK,EAAC;AAAE,aAAQC,IAAEC,IAAEC,IAAEC,KAAE,CAAC,GAAEC,MAAGV,GAAE,aAAW,MAAI,OAAKA,GAAE,YAAU,MAAI,OAAKA,GAAE,UAAQ,MAAI,OAAKA,GAAE,SAAO,MAAI,KAAIW,KAAE,GAAEC,KAAE,IAAI,OAAOZ,GAAE,QAAOU,KAAE,GAAG,IAAGJ,KAAE,GAAG,KAAKM,IAAET,EAAC,MAAI,GAAGI,KAAEK,GAAE,aAAWD,OAAIF,GAAE,KAAKN,GAAE,MAAMQ,IAAEL,GAAE,KAAK,CAAC,GAAEA,GAAE,SAAO,KAAGA,GAAE,QAAMH,GAAE,UAAQ,GAAG,MAAMM,IAAEH,GAAE,MAAM,CAAC,CAAC,GAAEE,KAAEF,GAAE,CAAC,EAAE,QAAOK,KAAEJ,IAAEE,GAAE,UAAQJ,OAAK,CAAAO,GAAE,cAAYN,GAAE,SAAOM,GAAE;AAAY,WAAOD,OAAIR,GAAE,SAAO,CAACK,MAAGI,GAAE,KAAK,EAAE,KAAGH,GAAE,KAAK,EAAE,IAAEA,GAAE,KAAKN,GAAE,MAAMQ,EAAC,CAAC,GAAEF,GAAE,SAAOJ,KAAEI,GAAE,MAAM,GAAEJ,EAAC,IAAEI;AAAA,EAAC,IAAE,IAAI,MAAM,QAAO,CAAC,EAAE,SAAO,SAAST,IAAEE,IAAE;AAAC,WAAO,WAASF,MAAG,MAAIE,KAAE,CAAC,IAAED,GAAE,KAAK,MAAKD,IAAEE,EAAC;AAAA,EAAC,IAAED,IAAE,CAAC,SAASA,IAAEC,IAAE;AAAC,QAAIG,KAAE,EAAE,IAAI,GAAEC,KAAE,QAAML,KAAE,SAAOA,GAAED,EAAC;AAAE,WAAO,WAASM,KAAEA,GAAE,KAAKL,IAAEI,IAAEH,EAAC,IAAEC,GAAE,KAAK,OAAOE,EAAC,GAAEJ,IAAEC,EAAC;AAAA,EAAC,GAAE,SAASF,IAAEK,IAAE;AAAC,QAAIC,KAAEJ,GAAEC,IAAEH,IAAE,MAAKK,IAAEF,OAAIF,EAAC;AAAE,QAAGK,GAAE,KAAK,QAAOA,GAAE;AAAM,QAAIC,KAAE,EAAEP,EAAC,GAAEQ,KAAE,OAAO,IAAI,GAAEC,KAAE,SAAST,IAAEC,IAAE;AAAC,UAAIC,IAAEC,KAAE,EAAEH,EAAC,EAAE;AAAY,aAAO,WAASG,MAAG,SAAOD,KAAE,EAAEC,EAAC,EAAE,EAAE,KAAGF,KAAE,GAAGC,EAAC;AAAA,IAAC,EAAEK,IAAE,MAAM,GAAEG,KAAEH,GAAE,SAAQI,MAAGJ,GAAE,aAAW,MAAI,OAAKA,GAAE,YAAU,MAAI,OAAKA,GAAE,UAAQ,MAAI,OAAK,KAAG,MAAI,MAAKK,KAAE,IAAIH,GAAE,KAAGF,KAAE,SAAOA,GAAE,SAAO,KAAII,EAAC,GAAEE,KAAE,WAASR,KAAE,aAAWA,OAAI;AAAE,QAAG,MAAIQ,GAAE,QAAM,CAAC;AAAE,QAAG,MAAIL,GAAE,OAAO,QAAO,SAAO,GAAGI,IAAEJ,EAAC,IAAE,CAACA,EAAC,IAAE,CAAC;AAAE,aAAQM,KAAE,GAAEC,KAAE,GAAEC,KAAE,CAAC,GAAED,KAAEP,GAAE,UAAQ;AAAC,MAAAI,GAAE,YAAU,KAAGG,KAAE;AAAE,UAAIE,IAAEC,KAAE,GAAGN,IAAE,KAAGJ,KAAEA,GAAE,MAAMO,EAAC,CAAC;AAAE,UAAG,SAAOG,OAAID,KAAE,GAAG,GAAGL,GAAE,aAAW,KAAG,IAAEG,GAAE,GAAEP,GAAE,MAAM,OAAKM,GAAE,CAAAC,KAAE,GAAGP,IAAEO,IAAEL,EAAC;AAAA,WAAM;AAAC,YAAGM,GAAE,KAAKR,GAAE,MAAMM,IAAEC,EAAC,CAAC,GAAEC,GAAE,WAASH,GAAE,QAAOG;AAAE,iBAAQG,KAAE,GAAEA,MAAGD,GAAE,SAAO,GAAEC,KAAI,KAAGH,GAAE,KAAKE,GAAEC,EAAC,CAAC,GAAEH,GAAE,WAASH,GAAE,QAAOG;AAAE,QAAAD,KAAED,KAAEG;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOD,GAAE,KAAKR,GAAE,MAAMM,EAAC,CAAC,GAAEE;AAAA,EAAC,CAAC;AAAC,GAAG,CAAC,EAAE;AAAE,IAAI,KAAG;AAAP,IAAuD,KAAG,MAAI,KAAG;AAAjE,IAAqE,KAAG,OAAO,MAAI,KAAG,KAAG,GAAG;AAA5F,IAA8F,KAAG,OAAO,KAAG,KAAG,IAAI;AAAlH,IAAoH,KAAG,SAAShB,IAAE;AAAC,SAAO,SAASC,IAAE;AAAC,QAAIC,KAAE,OAAO,EAAED,EAAC,CAAC;AAAE,WAAO,IAAED,OAAIE,KAAEA,GAAE,QAAQ,IAAG,EAAE,IAAG,IAAEF,OAAIE,KAAEA,GAAE,QAAQ,IAAG,EAAE,IAAGA;AAAA,EAAC;AAAC;AAAtO,IAAwOkB,MAAG,EAAC,OAAM,GAAG,CAAC,GAAE,KAAI,GAAG,CAAC,GAAE,MAAK,GAAG,CAAC,EAAC;AAA5Q,IAA8Q,KAAGA,IAAG;AAAK,GAAG,EAAC,QAAO,UAAS,OAAM,MAAG,QAAO,SAASpB,IAAE;AAAC,SAAO,EAAG,WAAU;AAAC,WAAM,CAAC,CAAC,GAAGA,EAAC,EAAE,KAAG,SAAO,MAAMA,EAAC,EAAE,KAAG,GAAGA,EAAC,EAAE,SAAOA;AAAA,EAAC,CAAE;AAAC,EAAE,MAAM,EAAC,GAAE,EAAC,MAAK,WAAU;AAAC,SAAO,GAAG,IAAI;AAAC,EAAC,CAAC;AAAE,IAAI,KAAG,GAAG,OAAO;AAAjB,IAAmB,KAAG,GAAG,SAAQ,EAAC,WAAU,MAAG,GAAE,GAAE,GAAE,EAAC,CAAC;AAAvD,IAAyD,KAAG,GAAG,SAAS;AAAxE,IAA0E,KAAG,CAAC,EAAE;AAAhF,IAAsF,KAAG,KAAK;AAAI,GAAG,EAAC,QAAO,SAAQ,OAAM,MAAG,QAAO,CAAC,MAAI,CAAC,GAAE,GAAE,EAAC,OAAM,SAASA,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEE,IAAEC,KAAE,EAAE,IAAI,GAAEC,KAAE,GAAGD,GAAE,MAAM,GAAEE,KAAE,GAAGR,IAAEO,EAAC,GAAEE,KAAE,GAAG,WAASR,KAAEM,KAAEN,IAAEM,EAAC;AAAE,MAAG,GAAGD,EAAC,MAAI,cAAY,QAAOJ,KAAEI,GAAE,gBAAcJ,OAAI,SAAO,CAAC,GAAGA,GAAE,SAAS,IAAE,EAAEA,EAAC,KAAG,UAAQA,KAAEA,GAAE,EAAE,OAAKA,KAAE,UAAQA,KAAE,QAAOA,OAAI,SAAO,WAASA,IAAG,QAAO,GAAG,KAAKI,IAAEE,IAAEC,EAAC;AAAE,OAAIN,KAAE,KAAI,WAASD,KAAE,QAAMA,IAAG,GAAGO,KAAED,IAAE,CAAC,CAAC,GAAEH,KAAE,GAAEG,KAAEC,IAAED,MAAIH,KAAI,CAAAG,MAAKF,MAAG,GAAGH,IAAEE,IAAEC,GAAEE,EAAC,CAAC;AAAE,SAAOL,GAAE,SAAOE,IAAEF;AAAC,EAAC,CAAC;AAAE,IAAI,KAAG,OAAO,QAAM,SAASH,IAAE;AAAC,SAAO,GAAGA,IAAE,EAAE;AAAC;AAA/C,IAAiD,KAAG,EAAG,WAAU;AAAC,KAAG,CAAC;AAAC,CAAE;AAAE,GAAG,EAAC,QAAO,UAAS,MAAK,MAAG,QAAO,GAAE,GAAE,EAAC,MAAK,SAASA,IAAE;AAAC,SAAO,GAAG,GAAGA,EAAC,CAAC;AAAC,EAAC,CAAC;AAAE,IAAI;AAAJ,IAAO,KAAG,SAASA,IAAE;AAAC,MAAG,GAAGA,EAAC,EAAE,OAAM,UAAU,+CAA+C;AAAE,SAAOA;AAAC;AAAxG,IAA0G,KAAG,GAAG,OAAO;AAAvH,IAAyH,KAAG,EAAE;AAA9H,IAAgI,KAAG,GAAG;AAAtI,IAAiJ,KAAG,KAAK;AAAzJ,IAA6J,KAAG,SAASA,IAAE;AAAC,MAAIC,KAAE;AAAI,MAAG;AAAC,UAAMD,EAAC,EAAEC,EAAC;AAAA,EAAC,SAAOC,IAAE;AAAC,QAAG;AAAC,aAAOD,GAAE,EAAE,IAAE,OAAG,MAAMD,EAAC,EAAEC,EAAC;AAAA,IAAC,SAAOD,IAAE;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM;AAAE,EAAE,YAAY;AAAhR,IAAkR,KAAG,EAAE,OAAK,KAAG,GAAG,OAAO,WAAU,YAAY,GAAE,CAAC,MAAI,GAAG;AAAW,SAAS,GAAGA,IAAE;AAAC,UAAO,KAAG,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAASA,IAAE;AAAC,WAAO,OAAOA;AAAA,EAAC,IAAE,SAASA,IAAE;AAAC,WAAOA,MAAG,cAAY,OAAO,UAAQA,GAAE,gBAAc,UAAQA,OAAI,OAAO,YAAU,WAAS,OAAOA;AAAA,EAAC,GAAGA,EAAC;AAAC;AAAC,GAAG,EAAC,QAAO,UAAS,OAAM,MAAG,QAAO,CAAC,MAAI,CAAC,GAAE,GAAE,EAAC,YAAW,SAASA,IAAE;AAAC,MAAIC,KAAE,OAAO,EAAE,IAAI,CAAC;AAAE,KAAGD,EAAC;AAAE,MAAIE,KAAE,GAAG,GAAG,UAAU,SAAO,IAAE,UAAU,CAAC,IAAE,QAAOD,GAAE,MAAM,CAAC,GAAEE,KAAE,OAAOH,EAAC;AAAE,SAAO,KAAG,GAAG,KAAKC,IAAEE,IAAED,EAAC,IAAED,GAAE,MAAMC,IAAEA,KAAEC,GAAE,MAAM,MAAIA;AAAC,EAAC,CAAC;AAAE,IAAwF,KAAG,SAASkB,IAAE;AAAC,SAAM,YAAU,OAAOA;AAAC;AAA/H,IAA2P,KAAG,SAASC,IAAE;AAAC,SAAO,SAAOA,MAAG,aAAW,GAAGA,EAAC;AAAC;AAA3S,IAAqb,KAAG,MAAM;AAA9b,IAA00B,KAAG,WAAU;AAAC,WAASC,KAAG;AAAC,OAAG,MAAKA,EAAC;AAAA,EAAC;AAAC,SAAO,GAAGA,IAAE,MAAK,CAAC,EAAC,KAAI,YAAW,OAAM,SAASA,IAAE;AAAC,WAAOA,OAAI;AAAA,EAAM,EAAC,GAAE,EAAC,KAAI,oBAAmB,OAAM,SAASA,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC;AAAE,IAAAH,MAAGC,MAAGC,MAAGF,GAAE,iBAAiBC,IAAEC,IAAEC,EAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,uBAAsB,OAAM,SAASH,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAE,UAAU,SAAO,KAAG,WAAS,UAAU,CAAC,KAAG,UAAU,CAAC;AAAE,IAAAH,MAAGC,MAAGC,MAAGF,GAAE,oBAAoBC,IAAEC,IAAEC,EAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,oBAAmB,OAAM,SAASF,IAAEC,IAAE;AAAC,QAAIC,KAAE,OAAGC,KAAE,SAASJ,IAAE;AAAC,UAAIC;AAAE,gBAAQA,KAAEC,GAAE,SAAO,WAASD,MAAGA,GAAE,KAAKC,IAAEF,EAAC;AAAA,IAAC,GAAEK,KAAE,SAASJ,GAAEI,IAAE;AAAC,UAAIC;AAAE,MAAAN,GAAE,oBAAoB,UAAS,aAAYI,EAAC,GAAEJ,GAAE,oBAAoB,UAAS,WAAUC,EAAC,GAAE,SAAS,gBAAc,MAAK,SAAS,cAAY,MAAKE,KAAE,OAAG,UAAQG,KAAEJ,GAAE,QAAM,WAASI,MAAGA,GAAE,KAAKJ,IAAEG,EAAC;AAAA,IAAC;AAAE,IAAAL,GAAE,iBAAiBC,IAAE,aAAa,SAASA,IAAE;AAAC,UAAIK;AAAE,MAAAH,OAAI,SAAS,gBAAc,WAAU;AAAC,eAAM;AAAA,MAAE,GAAE,SAAS,cAAY,WAAU;AAAC,eAAM;AAAA,MAAE,GAAEH,GAAE,iBAAiB,UAAS,aAAYI,EAAC,GAAEJ,GAAE,iBAAiB,UAAS,WAAUK,EAAC,GAAEF,KAAE,MAAG,UAAQG,KAAEJ,GAAE,UAAQ,WAASI,MAAGA,GAAE,KAAKJ,IAAED,EAAC;AAAA,IAAE,CAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,yBAAwB,OAAM,SAASD,IAAE;AAAC,WAAOA,MAAG,GAAGA,EAAC,KAAG,MAAIA,GAAE,WAASA,GAAE,sBAAsB,IAAE;AAAA,EAAI,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,SAASA,IAAEC,IAAE;AAAC,WAAM,CAAC,EAAED,MAAG,GAAGA,EAAC,KAAG,GAAGC,EAAC,KAAG,MAAID,GAAE,aAAWA,GAAE,UAAU,SAASC,GAAE,KAAK,CAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,SAASA,IAAEC,IAAE;AAAC,QAAGD,MAAG,GAAGA,EAAC,KAAG,GAAGC,EAAC,KAAG,MAAID,GAAE,aAAWC,KAAEA,GAAE,KAAK,GAAE,CAACF,GAAE,SAASC,IAAEC,EAAC,IAAG;AAAC,UAAIC,KAAEF,GAAE;AAAU,MAAAA,GAAE,YAAUE,KAAEA,KAAE,MAAID,KAAEA;AAAA,IAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASF,IAAEC,IAAE;AAAC,QAAGD,MAAG,GAAGA,EAAC,KAAG,GAAGC,EAAC,KAAG,MAAID,GAAE,YAAU,YAAU,OAAOA,GAAE,WAAU;AAAC,MAAAC,KAAEA,GAAE,KAAK;AAAE,eAAQC,KAAEF,GAAE,UAAU,KAAK,EAAE,MAAM,GAAG,GAAEG,KAAED,GAAE,SAAO,GAAEC,MAAG,GAAEA,KAAI,CAAAD,GAAEC,EAAC,IAAED,GAAEC,EAAC,EAAE,KAAK,GAAED,GAAEC,EAAC,KAAGD,GAAEC,EAAC,MAAIF,MAAGC,GAAE,OAAOC,IAAE,CAAC;AAAE,MAAAH,GAAE,YAAUE,GAAE,KAAK,GAAG;AAAA,IAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASF,IAAEC,IAAEC,IAAE;AAAC,IAAAF,MAAG,GAAGA,EAAC,KAAG,GAAGC,EAAC,KAAG,MAAID,GAAE,YAAUA,GAAE,UAAU,OAAOC,IAAEC,EAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASD,IAAEC,IAAEC,IAAE;AAAC,IAAAF,MAAG,GAAGA,EAAC,KAAG,GAAGC,EAAC,KAAG,GAAGC,EAAC,KAAG,MAAIF,GAAE,aAAWC,KAAEA,GAAE,KAAK,GAAEC,KAAEA,GAAE,KAAK,GAAEH,GAAE,YAAYC,IAAEC,EAAC,GAAEF,GAAE,SAASC,IAAEE,EAAC;AAAA,EAAE,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASH,IAAE;AAAC,QAAIC,KAAE,eAAcD,KAAEA,GAAE,YAAUA,GAAE;AAAY,WAAO,KAAK,IAAIC,IAAE,CAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASD,IAAEC,IAAE;AAAC,mBAAcD,KAAEA,GAAE,YAAUC,KAAED,GAAE,SAASA,GAAE,SAAQC,EAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,oBAAmB,OAAM,WAAU;AAAC,WAAO,OAAO,eAAa,SAAS,gBAAgB,aAAW,SAAS,KAAK,aAAW;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,oBAAmB,OAAM,SAASA,IAAE;AAAC,IAAAD,GAAE,aAAa,QAAOC,EAAC,GAAED,GAAE,aAAa,SAAS,MAAKC,EAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAASA,IAAEC,IAAE;AAAC,QAAGF,GAAE,SAASC,EAAC,EAAE,QAAO;AAAE,QAAIE,KAAED,KAAEF,GAAE,aAAaE,EAAC,IAAEF,GAAE,iBAAiB;AAAE,WAAOC,GAAE,sBAAsB,EAAE,MAAIE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,oBAAmB,OAAM,SAASF,IAAE;AAAC,WAAOD,GAAE,SAASC,EAAC,IAAEA,GAAE,cAAYA,GAAE,sBAAsB,EAAE;AAAA,EAAM,EAAC,GAAE,EAAC,KAAI,YAAW,OAAM,SAASD,IAAE;AAAC,QAAG,CAACA,GAAE,QAAM;AAAG,QAAIC,KAAE,OAAO,iBAAiBD,EAAC,GAAEE,KAAE,WAASD,GAAE,SAAQE,KAAE,SAAOH,GAAE,gBAAc,YAAUC,GAAE;AAAS,WAAOC,MAAGC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASH,IAAEC,IAAE;AAAC,QAAG,iBAAgB,UAAS;AAAC,UAAIC,KAAE,SAAS,YAAY,YAAY;AAAE,MAAAA,GAAE,UAAUD,IAAE,OAAG,IAAE,GAAED,GAAE,cAAcE,EAAC;AAAA,IAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,SAASF,IAAEC,IAAE;AAAC,QAAIC,KAAEF,GAAE,sBAAsB,GAAEG,KAAED,GAAE,OAAKA,GAAE,QAAM,GAAEE,KAAEF,GAAE,MAAIA,GAAE,SAAO,GAAEG,KAAE,KAAK,IAAIF,KAAEF,GAAE,OAAO,GAAEK,KAAE,KAAK,IAAIF,KAAEH,GAAE,OAAO,GAAEM,KAAED,KAAE,KAAK,KAAK,KAAK,IAAID,IAAE,CAAC,IAAE,KAAK,IAAIC,IAAE,CAAC,CAAC,GAAEE,KAAE,KAAK,KAAKD,EAAC,GAAEE,KAAE,KAAK,MAAM,OAAK,KAAK,KAAGD,GAAE;AAAE,WAAOP,GAAE,UAAQE,MAAGF,GAAE,UAAQG,OAAIK,KAAE,MAAIA,KAAGR,GAAE,WAASE,MAAGF,GAAE,UAAQG,OAAIK,KAAE,MAAKR,GAAE,UAAQE,MAAGF,GAAE,WAASG,OAAIK,KAAE,KAAIR,GAAE,UAAQE,MAAGF,GAAE,UAAQG,OAAIK,KAAE,MAAIA,KAAGR,GAAE,UAAQE,MAAGF,GAAE,WAASG,OAAIK,KAAE,MAAKR,GAAE,UAAQE,MAAGF,GAAE,UAAQG,OAAIK,KAAE,MAAIA,KAAGA;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAAST,IAAEC,IAAE;AAAC,WAAOA,KAAEA,GAAE,cAAcD,EAAC,IAAE,SAAS,cAAcA,EAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAASA,IAAE;AAAC,aAAQC,KAAE,SAAS,cAAcD,EAAC,GAAEE,KAAE,UAAU,QAAOC,KAAE,IAAI,MAAMD,KAAE,IAAEA,KAAE,IAAE,CAAC,GAAEE,KAAE,GAAEA,KAAEF,IAAEE,KAAI,CAAAD,GAAEC,KAAE,CAAC,IAAE,UAAUA,EAAC;AAAE,aAAQC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,CAAAF,GAAEE,EAAC,KAAGJ,GAAE,UAAU,IAAIE,GAAEE,EAAC,CAAC;AAAE,WAAOJ;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,eAAc,OAAM,SAASD,IAAE;AAAC,aAAQC,KAAE,GAAEA,MAAG,UAAU,UAAQ,IAAE,IAAE,UAAU,SAAO,IAAGA,KAAI,CAAAD,GAAE,YAAYC,KAAE,IAAE,KAAG,UAAU,UAAQA,KAAE,IAAE,SAAO,UAAUA,KAAE,CAAC,CAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,SAASD,IAAE;AAAC,QAAG,sBAAoBA,GAAE,SAAS,GAAE;AAAC,UAAIC,KAAED,GAAE;AAAc,aAAOC,MAAGA,GAAE,eAAa;AAAA,IAAM;AAAC,WAAOD;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,SAASA,IAAE;AAAC,WAAOA,cAAa,KAAK,UAAUA,EAAC,EAAE,WAASA,cAAa;AAAA,EAAO,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAASA,IAAE;AAAC,WAAOA,cAAa,KAAK,UAAUA,EAAC,EAAE,eAAaA,cAAa;AAAA,EAAW,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAASA,IAAE;AAAC,WAAM,eAAa,OAAO,eAAaA,cAAa,KAAK,UAAUA,EAAC,EAAE,cAAYA,cAAa;AAAA,EAAW,EAAC,GAAE,EAAC,KAAI,mBAAkB,OAAM,SAASA,IAAE;AAAC,QAAIC,KAAE,KAAK,UAAUD,EAAC;AAAE,WAAM,EAAC,YAAWC,GAAE,eAAa,GAAE,WAAUA,GAAE,eAAa,EAAC;AAAA,EAAC,EAAC,CAAC,CAAC,GAAED;AAAC,EAAE;AAAttK,IAAwtK,KAAG,KAAK;AAAhuK,IAAsuK,KAAG,GAAG;AAA5uK,IAAovK,KAAG;AAAvvK,IAAmxK,KAAG;AAAtxK,IAA0yK,KAAG,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEJ,KAAEF,GAAE,QAAOO,KAAEJ,GAAE,QAAOK,KAAE;AAAG,SAAO,WAASJ,OAAIA,KAAE,GAAGA,EAAC,GAAEI,KAAE,KAAI,GAAG,KAAKH,IAAEG,IAAG,SAASH,IAAEG,IAAE;AAAC,QAAIC;AAAE,YAAOD,GAAE,OAAO,CAAC,GAAE;AAAA,MAAC,KAAI;AAAI,eAAM;AAAA,MAAI,KAAI;AAAI,eAAOR;AAAA,MAAE,KAAI;AAAI,eAAOC,GAAE,MAAM,GAAEC,EAAC;AAAA,MAAE,KAAI;AAAI,eAAOD,GAAE,MAAMK,EAAC;AAAA,MAAE,KAAI;AAAI,QAAAG,KAAEL,GAAEI,GAAE,MAAM,GAAE,EAAE,CAAC;AAAE;AAAA,MAAM;AAAQ,YAAIE,KAAE,CAACF;AAAE,YAAG,MAAIE,GAAE,QAAOL;AAAE,YAAGK,KAAEH,IAAE;AAAC,cAAII,KAAE,GAAGD,KAAE,EAAE;AAAE,iBAAO,MAAIC,KAAEN,KAAEM,MAAGJ,KAAE,WAASJ,GAAEQ,KAAE,CAAC,IAAEH,GAAE,OAAO,CAAC,IAAEL,GAAEQ,KAAE,CAAC,IAAEH,GAAE,OAAO,CAAC,IAAEH;AAAA,QAAC;AAAC,QAAAI,KAAEN,GAAEO,KAAE,CAAC;AAAA,IAAC;AAAC,WAAO,WAASD,KAAE,KAAGA;AAAA,EAAC,CAAE;AAAC;AAAttL,IAAwtL,KAAG,KAAK;AAAhuL,IAAouL,KAAG,KAAK;AAAI,GAAG,WAAU,GAAG,SAAST,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAED,GAAE,8CAA6CE,KAAEF,GAAE,kBAAiBG,KAAEF,KAAE,MAAI;AAAK,SAAM,CAAC,SAASF,IAAEC,IAAE;AAAC,QAAIC,KAAE,EAAE,IAAI,GAAEC,KAAE,QAAMH,KAAE,SAAOA,GAAEF,EAAC;AAAE,WAAO,WAASK,KAAEA,GAAE,KAAKH,IAAEE,IAAED,EAAC,IAAEF,GAAE,KAAK,OAAOG,EAAC,GAAEF,IAAEC,EAAC;AAAA,EAAC,GAAE,SAASH,IAAEG,IAAE;AAAC,QAAG,CAACC,MAAGC,MAAG,YAAU,OAAOF,MAAG,OAAKA,GAAE,QAAQG,EAAC,GAAE;AAAC,UAAIC,KAAEL,GAAED,IAAED,IAAE,MAAKG,EAAC;AAAE,UAAGI,GAAE,KAAK,QAAOA,GAAE;AAAA,IAAK;AAAC,QAAIC,KAAE,EAAER,EAAC,GAAES,KAAE,OAAO,IAAI,GAAEC,KAAE,cAAY,OAAOP;AAAE,IAAAO,OAAIP,KAAE,OAAOA,EAAC;AAAG,QAAIQ,KAAEH,GAAE;AAAO,QAAGG,IAAE;AAAC,UAAIC,KAAEJ,GAAE;AAAQ,MAAAA,GAAE,YAAU;AAAA,IAAC;AAAC,aAAQK,KAAE,CAAC,OAAI;AAAC,UAAIC,KAAE,GAAGN,IAAEC,EAAC;AAAE,UAAG,SAAOK,GAAE;AAAM,UAAGD,GAAE,KAAKC,EAAC,GAAE,CAACH,GAAE;AAAM,aAAK,OAAOG,GAAE,CAAC,CAAC,MAAIN,GAAE,YAAU,GAAGC,IAAE,GAAGD,GAAE,SAAS,GAAEI,EAAC;AAAA,IAAE;AAAC,aAAQG,IAAEC,KAAE,IAAGC,KAAE,GAAEC,KAAE,GAAEA,KAAEL,GAAE,QAAOK,MAAI;AAAC,MAAAJ,KAAED,GAAEK,EAAC;AAAE,eAAQC,KAAE,OAAOL,GAAE,CAAC,CAAC,GAAEM,KAAE,GAAG,GAAG,GAAGN,GAAE,KAAK,GAAEL,GAAE,MAAM,GAAE,CAAC,GAAEY,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAER,GAAE,QAAOQ,KAAI,CAAAD,GAAE,KAAK,YAAUN,KAAED,GAAEQ,EAAC,KAAGP,KAAE,OAAOA,EAAC,CAAC;AAAE,UAAIQ,KAAET,GAAE;AAAO,UAAGJ,IAAE;AAAC,YAAIc,KAAE,CAACL,EAAC,EAAE,OAAOE,IAAED,IAAEX,EAAC;AAAE,mBAASc,MAAGC,GAAE,KAAKD,EAAC;AAAE,YAAIE,KAAE,OAAOtB,GAAE,MAAM,QAAOqB,EAAC,CAAC;AAAA,MAAC,MAAM,CAAAC,KAAE,GAAGN,IAAEV,IAAEW,IAAEC,IAAEE,IAAEpB,EAAC;AAAE,MAAAiB,MAAGH,OAAID,MAAGP,GAAE,MAAMQ,IAAEG,EAAC,IAAEK,IAAER,KAAEG,KAAED,GAAE;AAAA,IAAO;AAAC,WAAOH,KAAEP,GAAE,MAAMQ,EAAC;AAAA,EAAC,CAAC;AAAC,CAAE;AAAE,IAAI,KAAG,WAAU;AAAC,WAASjB,KAAG;AAAC,OAAG,MAAKA,EAAC;AAAA,EAAC;AAAC,SAAO,GAAGA,IAAE,MAAK,CAAC,EAAC,KAAI,YAAW,OAAM,SAASA,IAAE;AAAC,WAAOA,GAAE,QAAQ,UAAU,SAASA,IAAEC,IAAE;AAAC,aAAOA,KAAEA,GAAE,YAAY,IAAE;AAAA,IAAE,CAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,SAASD,IAAE;AAAC,WAAOA,GAAE,OAAO,CAAC,EAAE,YAAY,IAAEA,GAAE,MAAM,CAAC;AAAA,EAAC,EAAC,CAAC,CAAC,GAAEA;AAAC,EAAE;AAA/P,IAAiQ,KAAG,WAAU;AAAC,WAASA,KAAG;AAAC,OAAG,MAAKA,EAAC;AAAA,EAAC;AAAC,SAAO,GAAGA,IAAE,MAAK,CAAC,EAAC,KAAI,UAAS,OAAM,WAAU;AAAA,EAAC,EAAC,CAAC,CAAC,GAAEA;AAAC,EAAE;AAAhW,IAAkW,KAAG,GAAG,oBAAoB;AAA5X,IAA8X,KAAG,MAAI,MAAI,CAAC,EAAG,WAAU;AAAC,MAAIA,KAAE,CAAC;AAAE,SAAOA,GAAE,EAAE,IAAE,OAAGA,GAAE,OAAO,EAAE,CAAC,MAAIA;AAAC,CAAE;AAApc,IAAsc,KAAG,GAAG,QAAQ;AAApd,IAAsd,KAAG,SAASA,IAAE;AAAC,MAAG,CAAC,EAAEA,EAAC,EAAE,QAAM;AAAG,MAAIC,KAAED,GAAE,EAAE;AAAE,SAAO,WAASC,KAAE,CAAC,CAACA,KAAE,GAAGD,EAAC;AAAC;AAAE,GAAG,EAAC,QAAO,SAAQ,OAAM,MAAG,QAAO,CAAC,MAAI,CAAC,GAAE,GAAE,EAAC,QAAO,SAASA,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,GAAG,IAAI,GAAEC,KAAE,GAAGD,IAAE,CAAC,GAAEE,KAAE;AAAE,OAAIP,KAAE,IAAGE,KAAE,UAAU,QAAOF,KAAEE,IAAEF,KAAI,KAAG,GAAGI,KAAE,OAAKJ,KAAEK,KAAE,UAAUL,EAAC,CAAC,GAAE;AAAC,QAAGO,MAAGJ,KAAE,GAAGC,GAAE,MAAM,KAAG,iBAAiB,OAAM,UAAU,gCAAgC;AAAE,SAAIH,KAAE,GAAEA,KAAEE,IAAEF,MAAIM,KAAI,CAAAN,MAAKG,MAAG,GAAGE,IAAEC,IAAEH,GAAEH,EAAC,CAAC;AAAA,EAAC,OAAK;AAAC,QAAGM,MAAG,iBAAiB,OAAM,UAAU,gCAAgC;AAAE,OAAGD,IAAEC,MAAIH,EAAC;AAAA,EAAC;AAAC,SAAOE,GAAE,SAAOC,IAAED;AAAC,EAAC,CAAC;AAAE,IAAI;AAAJ,IAAO,KAAG,SAASP,IAAEC,IAAEC,IAAE;AAAC,MAAG,GAAGF,EAAC,GAAE,WAASC,GAAE,QAAOD;AAAE,UAAOE,IAAE;AAAA,IAAC,KAAK;AAAE,aAAO,WAAU;AAAC,eAAOF,GAAE,KAAKC,EAAC;AAAA,MAAC;AAAA,IAAE,KAAK;AAAE,aAAO,SAASC,IAAE;AAAC,eAAOF,GAAE,KAAKC,IAAEC,EAAC;AAAA,MAAC;AAAA,IAAE,KAAK;AAAE,aAAO,SAASA,IAAEC,IAAE;AAAC,eAAOH,GAAE,KAAKC,IAAEC,IAAEC,EAAC;AAAA,MAAC;AAAA,IAAE,KAAK;AAAE,aAAO,SAASD,IAAEC,IAAEC,IAAE;AAAC,eAAOJ,GAAE,KAAKC,IAAEC,IAAEC,IAAEC,EAAC;AAAA,MAAC;AAAA,EAAC;AAAC,SAAO,WAAU;AAAC,WAAOJ,GAAE,MAAMC,IAAE,SAAS;AAAA,EAAC;AAAC;AAAhT,IAAkT,KAAG,CAAC,EAAE;AAAxT,IAA6T,KAAG,SAASD,IAAE;AAAC,MAAIC,KAAE,KAAGD,IAAEE,KAAE,KAAGF,IAAEG,KAAE,KAAGH,IAAEI,KAAE,KAAGJ,IAAEK,KAAE,KAAGL,IAAEM,KAAE,KAAGN,IAAEO,KAAE,KAAGP,MAAGK;AAAE,SAAO,SAASG,IAAEC,IAAEC,IAAEC,IAAE;AAAC,aAAQC,IAAEE,IAAEC,KAAE,GAAGP,EAAC,GAAEQ,KAAE,EAAED,EAAC,GAAEE,KAAE,GAAGR,IAAEC,IAAE,CAAC,GAAEQ,KAAE,GAAGF,GAAE,MAAM,GAAEG,KAAE,GAAEC,KAAET,MAAG,IAAGU,KAAEpB,KAAEmB,GAAEZ,IAAEU,EAAC,IAAEhB,MAAGI,KAAEc,GAAEZ,IAAE,CAAC,IAAE,QAAOU,KAAEC,IAAEA,KAAI,MAAIZ,MAAGY,MAAKH,QAAKF,KAAEG,GAAEL,KAAEI,GAAEG,EAAC,GAAEA,IAAEJ,EAAC,GAAEf,IAAG,KAAGC,GAAE,CAAAoB,GAAEF,EAAC,IAAEL;AAAA,aAAUA,GAAE,SAAOd,IAAE;AAAA,MAAC,KAAK;AAAE,eAAM;AAAA,MAAG,KAAK;AAAE,eAAOY;AAAA,MAAE,KAAK;AAAE,eAAOO;AAAA,MAAE,KAAK;AAAE,WAAG,KAAKE,IAAET,EAAC;AAAA,IAAC;AAAA,QAAM,SAAOZ,IAAE;AAAA,MAAC,KAAK;AAAE,eAAM;AAAA,MAAG,KAAK;AAAE,WAAG,KAAKqB,IAAET,EAAC;AAAA,IAAC;AAAC,WAAOP,KAAE,KAAGF,MAAGC,KAAEA,KAAEiB;AAAA,EAAC;AAAC;AAArtB,IAAutB,KAAG,EAAC,SAAQ,GAAG,CAAC,GAAE,KAAI,GAAG,CAAC,GAAE,QAAO,GAAG,CAAC,GAAE,MAAK,GAAG,CAAC,GAAE,OAAM,GAAG,CAAC,GAAE,MAAK,GAAG,CAAC,GAAE,WAAU,GAAG,CAAC,GAAE,WAAU,GAAG,CAAC,EAAC;AAAj0B,IAAm0B,KAAG,IAAE,OAAO,mBAAiB,SAASrB,IAAEC,IAAE;AAAC,IAAED,EAAC;AAAE,WAAQE,IAAEC,KAAE,GAAGF,EAAC,GAAEG,KAAED,GAAE,QAAOE,KAAE,GAAED,KAAEC,KAAG,GAAE,EAAEL,IAAEE,KAAEC,GAAEE,IAAG,GAAEJ,GAAEC,EAAC,CAAC;AAAE,SAAOF;AAAC;AAAt7B,IAAw7B,KAAG,GAAG,YAAW,iBAAiB;AAA19B,IAA49B,KAAG,EAAE,UAAU;AAA3+B,IAA6+B,KAAG,WAAU;AAAC;AAA3/B,IAA6/B,KAAG,SAASA,IAAE;AAAC,SAAM,aAAWA,KAAE;AAAY;AAA3iC,IAA6iC,KAAG,WAAU;AAAC,MAAG;AAAC,SAAG,SAAS,UAAQ,IAAI,cAAc,UAAU;AAAA,EAAC,SAAOA,IAAE;AAAA,EAAC;AAAC,MAAIA,IAAEC;AAAE,OAAG,KAAG,SAASD,IAAE;AAAC,IAAAA,GAAE,MAAM,GAAG,EAAE,CAAC,GAAEA,GAAE,MAAM;AAAE,QAAIC,KAAED,GAAE,aAAa;AAAO,WAAOA,KAAE,MAAKC;AAAA,EAAC,EAAE,EAAE,MAAIA,KAAE,EAAE,QAAQ,GAAG,MAAM,UAAQ,QAAO,GAAG,YAAYA,EAAC,GAAEA,GAAE,MAAI,OAAO,aAAa,IAAGD,KAAEC,GAAE,cAAc,UAAU,KAAK,GAAED,GAAE,MAAM,GAAG,mBAAmB,CAAC,GAAEA,GAAE,MAAM,GAAEA,GAAE;AAAG,WAAQE,KAAE,GAAG,QAAOA,OAAK,QAAO,GAAG,UAAU,GAAGA,EAAC,CAAC;AAAE,SAAO,GAAG;AAAC;AAAE,EAAE,EAAE,IAAE;AAAG,IAAI,KAAG,OAAO,UAAQ,SAASF,IAAEC,IAAE;AAAC,MAAIC;AAAE,SAAO,SAAOF,MAAG,GAAG,YAAU,EAAEA,EAAC,GAAEE,KAAE,IAAI,MAAG,GAAG,YAAU,MAAKA,GAAE,EAAE,IAAEF,MAAGE,KAAE,GAAG,GAAE,WAASD,KAAEC,KAAE,GAAGA,IAAED,EAAC;AAAC;AAA5I,IAA8I,KAAG,GAAG,aAAa;AAAjK,IAAmK,KAAG,MAAM;AAAU,QAAM,GAAG,EAAE,KAAG,EAAE,EAAE,IAAG,IAAG,EAAC,cAAa,MAAG,OAAM,GAAG,IAAI,EAAC,CAAC;AAAE,IAAI,KAAG,SAASD,IAAE;AAAC,KAAG,EAAE,EAAEA,EAAC,IAAE;AAAE;AAA/B,IAAiC,KAAG,GAAG;AAAvC,IAA4C,KAAG;AAA/C,IAAkD,KAAG,GAAG,MAAM;AAAE,UAAQ,CAAC,KAAG,MAAM,CAAC,EAAE,KAAM,WAAU;AAAC,OAAG;AAAE,CAAE,GAAE,GAAG,EAAC,QAAO,SAAQ,OAAM,MAAG,QAAO,MAAI,CAAC,GAAE,GAAE,EAAC,MAAK,SAASA,IAAE;AAAC,SAAO,GAAG,MAAKA,IAAE,UAAU,SAAO,IAAE,UAAU,CAAC,IAAE,MAAM;AAAC,EAAC,CAAC,GAAE,GAAG,MAAM;AAAE,IAAI,KAAG,GAAG;AAAV,IAAoB,KAAG;AAAvB,IAA0B,KAAG,GAAG,WAAW;AAAE,eAAa,CAAC,KAAG,MAAM,CAAC,EAAE,UAAW,WAAU;AAAC,OAAG;AAAE,CAAE,GAAE,GAAG,EAAC,QAAO,SAAQ,OAAM,MAAG,QAAO,MAAI,CAAC,GAAE,GAAE,EAAC,WAAU,SAASA,IAAE;AAAC,SAAO,GAAG,MAAKA,IAAE,UAAU,SAAO,IAAE,UAAU,CAAC,IAAE,MAAM;AAAC,EAAC,CAAC,GAAE,GAAG,WAAW;AAAE,IAAI,KAAG,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAQC,IAAEC,KAAEL,IAAEM,KAAE,GAAEC,KAAE,CAAC,CAACL,MAAG,GAAGA,IAAEC,IAAE,CAAC,GAAEG,KAAEP,MAAG;AAAC,QAAGO,MAAKR,IAAE;AAAC,UAAGM,KAAEG,KAAEA,GAAET,GAAEQ,EAAC,GAAEA,IAAET,EAAC,IAAEC,GAAEQ,EAAC,GAAEL,KAAE,KAAG,GAAGG,EAAC,EAAE,CAAAC,KAAE,GAAGT,IAAEC,IAAEO,IAAE,GAAGA,GAAE,MAAM,GAAEC,IAAEJ,KAAE,CAAC,IAAE;AAAA,WAAM;AAAC,YAAGI,MAAG,iBAAiB,OAAM,UAAU,oCAAoC;AAAE,QAAAT,GAAES,EAAC,IAAED;AAAA,MAAC;AAAC,MAAAC;AAAA,IAAG;AAAC,IAAAC;AAAA,EAAG;AAAC,SAAOD;AAAC;AAAlQ,IAAoQ,KAAG;AAAG,GAAG,EAAC,QAAO,SAAQ,OAAM,KAAE,GAAE,EAAC,MAAK,WAAU;AAAC,MAAIT,KAAE,UAAU,SAAO,UAAU,CAAC,IAAE,QAAOC,KAAE,GAAG,IAAI,GAAEC,KAAE,GAAGD,GAAE,MAAM,GAAEE,KAAE,GAAGF,IAAE,CAAC;AAAE,SAAOE,GAAE,SAAO,GAAGA,IAAEF,IAAEA,IAAEC,IAAE,GAAE,WAASF,KAAE,IAAE,GAAGA,EAAC,CAAC,GAAEG;AAAC,EAAC,CAAC;AAAE,IAAI,KAAG,SAASH,IAAE;AAAC,MAAIC,KAAED,GAAE;AAAO,MAAG,WAASC,GAAE,QAAO,EAAEA,GAAE,KAAKD,EAAC,CAAC,EAAE;AAAK;AAAzE,IAA2E,KAAG,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG;AAAC,WAAOA,KAAEF,GAAE,EAAEC,EAAC,EAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,IAAED,GAAEC,EAAC;AAAA,EAAC,SAAOD,IAAE;AAAC,UAAM,GAAGD,EAAC,GAAEC;AAAA,EAAC;AAAC;AAAzJ,IAA2J,KAAG,CAAC;AAA/J,IAAiK,KAAG,GAAG,UAAU;AAAjL,IAAmL,KAAG,MAAM;AAA5L,IAAsM,KAAG,SAASD,IAAE;AAAC,SAAO,WAASA,OAAI,GAAG,UAAQA,MAAG,GAAG,EAAE,MAAIA;AAAE;AAAlQ,IAAoQ,KAAG,GAAG,UAAU;AAApR,IAAsR,KAAG,SAASA,IAAE;AAAC,MAAG,QAAMA,GAAE,QAAOA,GAAE,EAAE,KAAGA,GAAE,YAAY,KAAG,GAAG,GAAGA,EAAC,CAAC;AAAC;AAAxV,IAA0V,KAAG,GAAG,UAAU;AAA1W,IAA4W,KAAG;AAAG,IAAG;AAAK,OAAG,GAAE,KAAG,EAAC,MAAK,WAAU;AAAC,WAAM,EAAC,MAAK,CAAC,CAAC,KAAI;AAAA,EAAC,GAAE,QAAO,WAAU;AAAC,SAAG;AAAA,EAAE,EAAC;AAAE,KAAG,EAAE,IAAE,WAAU;AAAC,WAAO;AAAA,EAAI,GAAE,MAAM,KAAK,IAAI,WAAU;AAAC,UAAM;AAAA,EAAC,CAAE;AAAC,SAAOA,IAAE;AAAC;AAArJ;AAAK;AAAiJ,IAAI,KAAG,SAASA,IAAEC,IAAE;AAAC,MAAG,CAACA,MAAG,CAAC,GAAG,QAAM;AAAG,MAAIC,KAAE;AAAG,MAAG;AAAC,QAAIC,KAAE,CAAC;AAAE,IAAAA,GAAE,EAAE,IAAE,WAAU;AAAC,aAAM,EAAC,MAAK,WAAU;AAAC,eAAM,EAAC,MAAKD,KAAE,KAAE;AAAA,MAAC,EAAC;AAAA,IAAC,GAAEF,GAAEG,EAAC;AAAA,EAAC,SAAOH,IAAE;AAAA,EAAC;AAAC,SAAOE;AAAC;AAAnJ,IAAqJ,KAAG,CAAC,GAAI,SAASF,IAAE;AAAC,QAAM,KAAKA,EAAC;AAAC,CAAE;AAAE,GAAG,EAAC,QAAO,SAAQ,MAAK,MAAG,QAAO,GAAE,GAAE,EAAC,MAAK,SAASA,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,GAAGP,EAAC,GAAEQ,KAAE,cAAY,OAAO,OAAK,OAAK,OAAMC,KAAE,UAAU,QAAOC,KAAED,KAAE,IAAE,UAAU,CAAC,IAAE,QAAOE,KAAE,WAASD,IAAEE,KAAE,GAAGL,EAAC,GAAEM,KAAE;AAAE,MAAGF,OAAID,KAAE,GAAGA,IAAED,KAAE,IAAE,UAAU,CAAC,IAAE,QAAO,CAAC,IAAG,QAAMG,MAAGJ,MAAG,SAAO,GAAGI,EAAC,EAAE,MAAIV,KAAE,IAAIM,GAAEP,KAAE,GAAGM,GAAE,MAAM,CAAC,GAAEN,KAAEY,IAAEA,KAAI,CAAAP,KAAEK,KAAED,GAAEH,GAAEM,EAAC,GAAEA,EAAC,IAAEN,GAAEM,EAAC,GAAE,GAAGX,IAAEW,IAAEP,EAAC;AAAA,MAAO,MAAID,MAAGD,KAAEQ,GAAE,KAAKL,EAAC,GAAG,MAAKL,KAAE,IAAIM,MAAE,EAAEL,KAAEE,GAAE,KAAKD,EAAC,GAAG,MAAKS,KAAI,CAAAP,KAAEK,KAAE,GAAGP,IAAEM,IAAE,CAACP,GAAE,OAAMU,EAAC,GAAE,IAAE,IAAEV,GAAE,OAAM,GAAGD,IAAEW,IAAEP,EAAC;AAAE,SAAOJ,GAAE,SAAOW,IAAEX;AAAC,EAAC,CAAC;AAAE,IAAI,KAAG,SAASF,IAAE;AAAC,SAAO,SAASC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,OAAGF,EAAC;AAAE,QAAIG,KAAE,GAAGJ,EAAC,GAAEK,KAAE,EAAED,EAAC,GAAEE,KAAE,GAAGF,GAAE,MAAM,GAAEG,KAAER,KAAEO,KAAE,IAAE,GAAEE,KAAET,KAAE,KAAG;AAAE,QAAGG,KAAE,EAAE,YAAO;AAAC,UAAGK,MAAKF,IAAE;AAAC,QAAAF,KAAEE,GAAEE,EAAC,GAAEA,MAAGC;AAAE;AAAA,MAAK;AAAC,UAAGD,MAAGC,IAAET,KAAEQ,KAAE,IAAED,MAAGC,GAAE,OAAM,UAAU,6CAA6C;AAAA,IAAC;AAAC,WAAKR,KAAEQ,MAAG,IAAED,KAAEC,IAAEA,MAAGC,GAAE,CAAAD,MAAKF,OAAIF,KAAEF,GAAEE,IAAEE,GAAEE,EAAC,GAAEA,IAAEH,EAAC;AAAG,WAAOD;AAAA,EAAC;AAAC;AAA7R,IAA+R,KAAG,EAAC,MAAK,GAAG,KAAE,GAAE,OAAM,GAAG,IAAE,EAAC;AAA3T,IAA6T,KAAG,aAAW,EAAE,EAAE,OAAO;AAAtV,IAAwV,KAAG,GAAG;AAA9V,IAAmW,KAAG,GAAG,QAAQ;AAAjX,IAAmX,KAAG,GAAG,UAAS,EAAC,GAAE,EAAC,CAAC;AAAE,GAAG,EAAC,QAAO,SAAQ,OAAM,MAAG,QAAO,CAAC,MAAI,CAAC,MAAI,CAAC,MAAI,KAAG,MAAI,KAAG,GAAE,GAAE,EAAC,QAAO,SAASJ,IAAE;AAAC,SAAO,GAAG,MAAKA,IAAE,UAAU,QAAO,UAAU,SAAO,IAAE,UAAU,CAAC,IAAE,MAAM;AAAC,EAAC,CAAC,GAAE,GAAG,MAAM;AAAE,IAAI;AAAJ,IAAO;AAAP,IAAU;AAAV,IAAa,KAAG,CAAC,EAAG,WAAU;AAAC,SAAO,OAAO,aAAa,OAAO,kBAAkB,CAAC,CAAC,CAAC;AAAC,CAAE;AAAzF,IAA2F,KAAG,EAAG,SAASA,IAAE;AAAC,MAAIC,KAAE,EAAE,GAAEC,KAAE,EAAE,MAAM,GAAEC,KAAE,GAAEC,KAAE,OAAO,gBAAc,WAAU;AAAC,WAAM;AAAA,EAAE,GAAEC,KAAE,SAASL,IAAE;AAAC,IAAAC,GAAED,IAAEE,IAAE,EAAC,OAAM,EAAC,UAAS,MAAK,EAAEC,IAAE,UAAS,CAAC,EAAC,EAAC,CAAC;AAAA,EAAC,GAAEG,KAAEN,GAAE,UAAQ,EAAC,UAAS,OAAG,SAAQ,SAASA,IAAEC,IAAE;AAAC,QAAG,CAAC,EAAED,EAAC,EAAE,QAAM,YAAU,OAAOA,KAAEA,MAAG,YAAU,OAAOA,KAAE,MAAI,OAAKA;AAAE,QAAG,CAAC,EAAEA,IAAEE,EAAC,GAAE;AAAC,UAAG,CAACE,GAAEJ,EAAC,EAAE,QAAM;AAAI,UAAG,CAACC,GAAE,QAAM;AAAI,MAAAI,GAAEL,EAAC;AAAA,IAAC;AAAC,WAAOA,GAAEE,EAAC,EAAE;AAAA,EAAQ,GAAE,aAAY,SAASF,IAAEC,IAAE;AAAC,QAAG,CAAC,EAAED,IAAEE,EAAC,GAAE;AAAC,UAAG,CAACE,GAAEJ,EAAC,EAAE,QAAM;AAAG,UAAG,CAACC,GAAE,QAAM;AAAG,MAAAI,GAAEL,EAAC;AAAA,IAAC;AAAC,WAAOA,GAAEE,EAAC,EAAE;AAAA,EAAQ,GAAE,UAAS,SAASF,IAAE;AAAC,WAAO,MAAIM,GAAE,YAAUF,GAAEJ,EAAC,KAAG,CAAC,EAAEA,IAAEE,EAAC,KAAGG,GAAEL,EAAC,GAAEA;AAAA,EAAC,EAAC;AAAE,IAAEE,EAAC,IAAE;AAAE,CAAE;AAA5lB,IAA8lB,KAAG,SAASF,IAAEC,IAAE;AAAC,OAAK,UAAQD,IAAE,KAAK,SAAOC;AAAC;AAA3oB,IAA6oB,KAAG,SAASD,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,KAAER,MAAGA,GAAE,MAAKS,KAAE,EAAE,CAACT,MAAG,CAACA,GAAE,aAAYU,KAAE,EAAE,CAACV,MAAG,CAACA,GAAE,cAAaW,KAAE,EAAE,CAACX,MAAG,CAACA,GAAE,cAAaY,KAAE,GAAGb,IAAES,IAAE,IAAEC,KAAEE,EAAC,GAAEE,KAAE,SAASf,IAAE;AAAC,WAAOG,MAAG,GAAGA,EAAC,GAAE,IAAI,GAAG,MAAGH,EAAC;AAAA,EAAC,GAAEgB,KAAE,SAAShB,IAAE;AAAC,WAAOW,MAAG,EAAEX,EAAC,GAAEa,KAAEC,GAAEd,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEe,EAAC,IAAED,GAAEd,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,KAAGa,KAAEC,GAAEd,IAAEe,EAAC,IAAED,GAAEd,EAAC;AAAA,EAAC;AAAE,MAAGY,GAAE,CAAAT,KAAEH;AAAA,OAAM;AAAC,QAAG,cAAY,QAAOI,KAAE,GAAGJ,EAAC,GAAG,OAAM,UAAU,wBAAwB;AAAE,QAAG,GAAGI,EAAC,GAAE;AAAC,WAAIC,KAAE,GAAEC,KAAE,GAAGN,GAAE,MAAM,GAAEM,KAAED,IAAEA,KAAI,MAAIE,KAAES,GAAEhB,GAAEK,EAAC,CAAC,MAAIE,cAAa,GAAG,QAAOA;AAAE,aAAO,IAAI,GAAG,KAAE;AAAA,IAAC;AAAC,IAAAJ,KAAEC,GAAE,KAAKJ,EAAC;AAAA,EAAC;AAAC,OAAIQ,KAAEL,GAAE,MAAK,EAAEM,KAAED,GAAE,KAAKL,EAAC,GAAG,QAAM;AAAC,QAAG;AAAC,MAAAI,KAAES,GAAEP,GAAE,KAAK;AAAA,IAAC,SAAOT,IAAE;AAAC,YAAM,GAAGG,EAAC,GAAEH;AAAA,IAAC;AAAC,QAAG,YAAU,OAAOO,MAAGA,MAAGA,cAAa,GAAG,QAAOA;AAAA,EAAC;AAAC,SAAO,IAAI,GAAG,KAAE;AAAC;AAAluC,IAAouC,KAAG,SAASP,IAAEC,IAAEC,IAAE;AAAC,MAAG,EAAEF,cAAaC,IAAG,OAAM,UAAU,gBAAcC,KAAEA,KAAE,MAAI,MAAI,YAAY;AAAE,SAAOF;AAAC;AAA50C,IAA80C,KAAG,EAAE;AAAn1C,IAAq1C,KAAG,GAAG,aAAa;AAAx2C,IAA02C,KAAG,SAASA,IAAEC,IAAEC,IAAE;AAAC,EAAAF,MAAG,CAAC,EAAEA,KAAEE,KAAEF,KAAEA,GAAE,WAAU,EAAE,KAAG,GAAGA,IAAE,IAAG,EAAC,cAAa,MAAG,OAAMC,GAAC,CAAC;AAAC;AAA57C,IAA87C,KAAG,OAAO,mBAAiB,eAAa,CAAC,IAAE,WAAU;AAAC,MAAID,IAAEC,KAAE,OAAGC,KAAE,CAAC;AAAE,MAAG;AAAC,KAACF,KAAE,OAAO,yBAAyB,OAAO,WAAU,WAAW,EAAE,KAAK,KAAKE,IAAE,CAAC,CAAC,GAAED,KAAEC,cAAa;AAAA,EAAK,SAAOF,IAAE;AAAA,EAAC;AAAC,SAAO,SAASE,IAAEC,IAAE;AAAC,WAAO,EAAED,EAAC,GAAE,SAASF,IAAE;AAAC,UAAG,CAAC,EAAEA,EAAC,KAAG,SAAOA,GAAE,OAAM,UAAU,eAAa,OAAOA,EAAC,IAAE,iBAAiB;AAAA,IAAC,EAAEG,EAAC,GAAEF,KAAED,GAAE,KAAKE,IAAEC,EAAC,IAAED,GAAE,YAAUC,IAAED;AAAA,EAAC;AAAC,EAAE,IAAE;AAAxxD,IAAgyD,KAAG,SAASF,IAAEC,IAAEC,IAAE;AAAC,WAAQC,MAAKF,GAAE,GAAED,IAAEG,IAAEF,GAAEE,EAAC,GAAED,EAAC;AAAE,SAAOF;AAAC;AAAx1D,IAA01D,KAAG,CAAC,EAAG,WAAU;AAAC,WAASA,KAAG;AAAA,EAAC;AAAC,SAAOA,GAAE,UAAU,cAAY,MAAK,OAAO,eAAe,IAAIA,IAAC,MAAIA,GAAE;AAAS,CAAE;AAA18D,IAA48D,KAAG,EAAE,UAAU;AAA39D,IAA69D,KAAG,OAAO;AAAv+D,IAAi/D,KAAG,KAAG,OAAO,iBAAe,SAASA,IAAE;AAAC,SAAOA,KAAE,GAAGA,EAAC,GAAE,EAAEA,IAAE,EAAE,IAAEA,GAAE,EAAE,IAAE,cAAY,OAAOA,GAAE,eAAaA,cAAaA,GAAE,cAAYA,GAAE,YAAY,YAAUA,cAAa,SAAO,KAAG;AAAI;AAAtqE,IAAwqE,KAAG,GAAG,UAAU;AAAxrE,IAA0rE,KAAG;AAAG,CAAC,EAAE,SAAO,WAAS,KAAG,CAAC,EAAE,KAAK,MAAI,KAAG,GAAG,GAAG,EAAE,CAAC,OAAK,OAAO,cAAY,KAAG,MAAI,KAAG,QAAK,QAAM,MAAI,EAAG,WAAU;AAAC,MAAIA,KAAE,CAAC;AAAE,SAAO,GAAG,EAAE,EAAE,KAAKA,EAAC,MAAIA;AAAC,CAAE,OAAK,KAAG,CAAC,IAAG,EAAE,IAAG,EAAE,KAAG,EAAE,IAAG,IAAI,WAAU;AAAC,SAAO;AAAI,CAAE;AAAE,IAAI,KAAG,EAAC,mBAAkB,IAAG,wBAAuB,GAAE;AAAtD,IAAwD,KAAG,GAAG;AAA9D,IAAgF,KAAG,WAAU;AAAC,SAAO;AAAI;AAAzG,IAA2G,KAAG,GAAG;AAAjH,IAAmI,KAAG,GAAG;AAAzI,IAAgK,KAAG,GAAG,UAAU;AAAhL,IAAkL,KAAG,WAAU;AAAC,SAAO;AAAI;AAA3M,IAA6M,KAAG,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,GAAC,SAASN,IAAEC,IAAEC,IAAE;AAAC,QAAIC,KAAEF,KAAE;AAAY,IAAAD,GAAE,YAAU,GAAG,IAAG,EAAC,MAAK,EAAE,GAAEE,EAAC,EAAC,CAAC,GAAE,GAAGF,IAAEG,IAAE,KAAE,GAAE,GAAGA,EAAC,IAAE;AAAA,EAAE,EAAED,IAAED,IAAEE,EAAC;AAAE,MAAII,IAAEC,IAAEE,IAAEC,KAAE,SAASX,IAAE;AAAC,QAAGA,OAAII,MAAGY,GAAE,QAAOA;AAAE,QAAG,CAAC,MAAIhB,MAAKc,GAAE,QAAOA,GAAEd,EAAC;AAAE,YAAOA,IAAE;AAAA,MAAC,KAAI;AAAA,MAAO,KAAI;AAAA,MAAS,KAAI;AAAU,eAAO,WAAU;AAAC,iBAAO,IAAIE,GAAE,MAAKF,EAAC;AAAA,QAAC;AAAA,IAAC;AAAC,WAAO,WAAU;AAAC,aAAO,IAAIE,GAAE,IAAI;AAAA,IAAC;AAAA,EAAC,GAAEU,KAAEX,KAAE,aAAYY,KAAE,OAAGC,KAAEd,GAAE,WAAUe,KAAED,GAAE,EAAE,KAAGA,GAAE,YAAY,KAAGV,MAAGU,GAAEV,EAAC,GAAEY,KAAE,CAAC,MAAID,MAAGJ,GAAEP,EAAC,GAAEa,KAAE,WAAShB,MAAGa,GAAE,WAASC;AAAE,MAAGE,OAAIV,KAAE,GAAGU,GAAE,KAAK,IAAIjB,IAAC,CAAC,GAAE,OAAK,OAAO,aAAWO,GAAE,SAAO,GAAGA,EAAC,MAAI,OAAK,KAAG,GAAGA,IAAE,EAAE,IAAE,cAAY,OAAOA,GAAE,EAAE,KAAG,EAAEA,IAAE,IAAG,EAAE,IAAG,GAAGA,IAAEK,IAAE,IAAE,KAAI,YAAUR,MAAGW,MAAG,aAAWA,GAAE,SAAOF,KAAE,MAAGG,KAAE,WAAU;AAAC,WAAOD,GAAE,KAAK,IAAI;AAAA,EAAC,IAAGD,GAAE,EAAE,MAAIE,MAAG,EAAEF,IAAE,IAAGE,EAAC,GAAE,GAAGf,EAAC,IAAEe,IAAEZ,GAAE,KAAGI,KAAE,EAAC,QAAOG,GAAE,QAAQ,GAAE,MAAKN,KAAEW,KAAEL,GAAE,MAAM,GAAE,SAAQA,GAAE,SAAS,EAAC,GAAEL,GAAE,MAAII,MAAKF,GAAE,EAAC,MAAIK,MAAG,EAAEH,MAAKI,QAAK,EAAEA,IAAEJ,IAAEF,GAAEE,EAAC,CAAC;AAAA,MAAO,IAAG,EAAC,QAAOT,IAAE,OAAM,MAAG,QAAO,MAAIY,GAAC,GAAEL,EAAC;AAAE,SAAOA;AAAC;AAArhC,IAAuhC,KAAG,GAAG,SAAS;AAAtiC,IAAwiC,KAAG,EAAE;AAA7iC,IAA+iC,KAAG,GAAG;AAArjC,IAA6jC,KAAG,EAAE;AAAlkC,IAAskC,KAAG,EAAE;AAAU,CAAC,SAASR,IAAEC,IAAEC,IAAE;AAAC,MAAIG,KAAE,OAAKL,GAAE,QAAQ,KAAK,GAAEM,KAAE,OAAKN,GAAE,QAAQ,MAAM,GAAEO,KAAEF,KAAE,QAAM,OAAMG,KAAE,EAAER,EAAC,GAAES,KAAED,MAAGA,GAAE,WAAUE,KAAEF,IAAEG,KAAE,CAAC,GAAEC,KAAE,SAASZ,IAAE;AAAC,QAAIC,KAAEQ,GAAET,EAAC;AAAE,MAAES,IAAET,IAAE,SAAOA,KAAE,SAASA,IAAE;AAAC,aAAOC,GAAE,KAAK,MAAK,MAAID,KAAE,IAAEA,EAAC,GAAE;AAAA,IAAI,IAAE,YAAUA,KAAE,SAASA,IAAE;AAAC,aAAM,EAAEM,MAAG,CAAC,EAAEN,EAAC,MAAIC,GAAE,KAAK,MAAK,MAAID,KAAE,IAAEA,EAAC;AAAA,IAAC,IAAE,SAAOA,KAAE,SAASA,IAAE;AAAC,aAAOM,MAAG,CAAC,EAAEN,EAAC,IAAE,SAAOC,GAAE,KAAK,MAAK,MAAID,KAAE,IAAEA,EAAC;AAAA,IAAC,IAAE,SAAOA,KAAE,SAASA,IAAE;AAAC,aAAM,EAAEM,MAAG,CAAC,EAAEN,EAAC,MAAIC,GAAE,KAAK,MAAK,MAAID,KAAE,IAAEA,EAAC;AAAA,IAAC,IAAE,SAASA,IAAEE,IAAE;AAAC,aAAOD,GAAE,KAAK,MAAK,MAAID,KAAE,IAAEA,IAAEE,EAAC,GAAE;AAAA,IAAI,CAAC;AAAA,EAAC;AAAE,MAAG,GAAGF,IAAE,cAAY,OAAOQ,MAAG,EAAEF,MAAGG,GAAE,WAAS,CAAC,EAAG,WAAU;AAAC,IAAC,IAAID,KAAG,QAAQ,EAAE,KAAK;AAAA,EAAC,CAAE,EAAE,EAAE,CAAAE,KAAER,GAAE,eAAeD,IAAED,IAAEK,IAAEE,EAAC,GAAE,GAAG,WAAS;AAAA,WAAW,GAAGP,IAAE,IAAE,GAAE;AAAC,QAAIa,KAAE,IAAIH,MAAEI,KAAED,GAAEN,EAAC,EAAED,KAAE,CAAC,IAAE,IAAG,CAAC,KAAGO,IAAEE,KAAE,EAAG,WAAU;AAAC,MAAAF,GAAE,IAAI,CAAC;AAAA,IAAC,CAAE,GAAEI,KAAE,GAAI,SAASjB,IAAE;AAAC,UAAIQ,GAAER,EAAC;AAAA,IAAC,CAAE,GAAEkB,KAAE,CAACZ,MAAG,EAAG,WAAU;AAAC,eAAQN,KAAE,IAAIQ,MAAEP,KAAE,GAAEA,OAAK,CAAAD,GAAEO,EAAC,EAAEN,IAAEA,EAAC;AAAE,aAAM,CAACD,GAAE,IAAI,EAAE;AAAA,IAAC,CAAE;AAAE,IAAAiB,QAAKP,KAAET,GAAG,SAASA,IAAEC,IAAE;AAAC,SAAGD,IAAES,IAAEV,EAAC;AAAE,UAAIG,KAAE,SAASH,IAAEC,IAAEC,IAAE;AAAC,YAAIC,IAAEC;AAAE,eAAO,MAAI,cAAY,QAAOD,KAAEF,GAAE,gBAAcE,OAAID,MAAG,EAAEE,KAAED,GAAE,SAAS,KAAGC,OAAIF,GAAE,aAAW,GAAGF,IAAEI,EAAC,GAAEJ;AAAA,MAAC,EAAE,IAAIQ,MAAEP,IAAES,EAAC;AAAE,aAAO,QAAMR,MAAG,GAAGA,IAAEC,GAAEI,EAAC,GAAE,EAAC,MAAKJ,IAAE,YAAWE,GAAC,CAAC,GAAEF;AAAA,IAAC,CAAE,GAAG,YAAUM,IAAEA,GAAE,cAAYC,MAAIK,MAAGG,QAAKN,GAAE,QAAQ,GAAEA,GAAE,KAAK,GAAEP,MAAGO,GAAE,KAAK,KAAIM,MAAGJ,OAAIF,GAAEL,EAAC,GAAED,MAAGG,GAAE,SAAO,OAAOA,GAAE;AAAA,EAAK;AAAC,EAAAE,GAAEX,EAAC,IAAEU,IAAE,GAAG,EAAC,QAAO,MAAG,QAAOA,MAAGF,GAAC,GAAEG,EAAC,GAAE,GAAGD,IAAEV,EAAC,GAAEM,MAAGJ,GAAE,UAAUQ,IAAEV,IAAEK,EAAC;AAAC,EAAE,OAAO,SAASL,IAAE;AAAC,SAAO,WAAU;AAAC,WAAOA,GAAE,MAAK,UAAU,SAAO,UAAU,CAAC,IAAE,MAAM;AAAA,EAAC;AAAC,GAAG,EAAC,gBAAe,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEJ,GAAG,SAASA,IAAEM,IAAE;AAAC,OAAGN,IAAEI,IAAEH,EAAC,GAAE,GAAGD,IAAE,EAAC,MAAKC,IAAE,OAAM,GAAG,IAAI,GAAE,OAAM,QAAO,MAAK,QAAO,MAAK,EAAC,CAAC,GAAE,MAAID,GAAE,OAAK,IAAG,QAAMM,MAAG,GAAGA,IAAEN,GAAEG,EAAC,GAAE,EAAC,MAAKH,IAAE,YAAWE,GAAC,CAAC;AAAA,EAAC,CAAE,GAAEI,KAAE,GAAGL,EAAC,GAAEM,KAAE,SAASP,IAAEC,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEG,KAAED,GAAEN,EAAC,GAAES,KAAED,GAAER,IAAEC,EAAC;AAAE,WAAOQ,KAAEA,GAAE,QAAMP,MAAGK,GAAE,OAAKE,KAAE,EAAC,OAAML,KAAE,GAAGH,IAAE,IAAE,GAAE,KAAIA,IAAE,OAAMC,IAAE,UAASC,KAAEI,GAAE,MAAK,MAAK,QAAO,SAAQ,MAAE,GAAEA,GAAE,UAAQA,GAAE,QAAME,KAAGN,OAAIA,GAAE,OAAKM,KAAG,IAAEF,GAAE,SAAOP,GAAE,QAAO,QAAMI,OAAIG,GAAE,MAAMH,EAAC,IAAEK,MAAIT;AAAA,EAAC,GAAEQ,KAAE,SAASR,IAAEC,IAAE;AAAC,QAAIC,IAAEC,KAAEG,GAAEN,EAAC,GAAEI,KAAE,GAAGH,EAAC;AAAE,QAAG,QAAMG,GAAE,QAAOD,GAAE,MAAMC,EAAC;AAAE,SAAIF,KAAEC,GAAE,OAAMD,IAAEA,KAAEA,GAAE,KAAK,KAAGA,GAAE,OAAKD,GAAE,QAAOC;AAAA,EAAC;AAAE,SAAO,GAAGE,GAAE,WAAU,EAAC,OAAM,WAAU;AAAC,aAAQJ,KAAEM,GAAE,IAAI,GAAEL,KAAED,GAAE,OAAME,KAAEF,GAAE,OAAME,KAAG,CAAAA,GAAE,UAAQ,MAAGA,GAAE,aAAWA,GAAE,WAASA,GAAE,SAAS,OAAK,SAAQ,OAAOD,GAAEC,GAAE,KAAK,GAAEA,KAAEA,GAAE;AAAK,IAAAF,GAAE,QAAMA,GAAE,OAAK,QAAO,IAAEA,GAAE,OAAK,IAAE,KAAK,OAAK;AAAA,EAAC,GAAE,QAAO,SAASA,IAAE;AAAC,QAAIC,KAAE,MAAKC,KAAEI,GAAEL,EAAC,GAAEE,KAAEK,GAAEP,IAAED,EAAC;AAAE,QAAGG,IAAE;AAAC,UAAIC,KAAED,GAAE,MAAKI,KAAEJ,GAAE;AAAS,aAAOD,GAAE,MAAMC,GAAE,KAAK,GAAEA,GAAE,UAAQ,MAAGI,OAAIA,GAAE,OAAKH,KAAGA,OAAIA,GAAE,WAASG,KAAGL,GAAE,SAAOC,OAAID,GAAE,QAAME,KAAGF,GAAE,QAAMC,OAAID,GAAE,OAAKK,KAAG,IAAEL,GAAE,SAAOD,GAAE;AAAA,IAAM;AAAC,WAAM,CAAC,CAACE;AAAA,EAAC,GAAE,SAAQ,SAASH,IAAE;AAAC,aAAQC,IAAEC,KAAEI,GAAE,IAAI,GAAEH,KAAE,GAAGH,IAAE,UAAU,SAAO,IAAE,UAAU,CAAC,IAAE,QAAO,CAAC,GAAEC,KAAEA,KAAEA,GAAE,OAAKC,GAAE,QAAO,MAAIC,GAAEF,GAAE,OAAMA,GAAE,KAAI,IAAI,GAAEA,MAAGA,GAAE,UAAS,CAAAA,KAAEA,GAAE;AAAA,EAAQ,GAAE,KAAI,SAASD,IAAE;AAAC,WAAM,CAAC,CAACQ,GAAE,MAAKR,EAAC;AAAA,EAAC,EAAC,CAAC,GAAE,GAAGI,GAAE,WAAUF,KAAE,EAAC,KAAI,SAASF,IAAE;AAAC,QAAIC,KAAEO,GAAE,MAAKR,EAAC;AAAE,WAAOC,MAAGA,GAAE;AAAA,EAAK,GAAE,KAAI,SAASD,IAAEC,IAAE;AAAC,WAAOM,GAAE,MAAK,MAAIP,KAAE,IAAEA,IAAEC,EAAC;AAAA,EAAC,EAAC,IAAE,EAAC,KAAI,SAASD,IAAE;AAAC,WAAOO,GAAE,MAAKP,KAAE,MAAIA,KAAE,IAAEA,IAAEA,EAAC;AAAA,EAAC,EAAC,CAAC,GAAE,KAAG,GAAGI,GAAE,WAAU,QAAO,EAAC,KAAI,WAAU;AAAC,WAAOE,GAAE,IAAI,EAAE;AAAA,EAAI,EAAC,CAAC,GAAEF;AAAC,GAAE,WAAU,SAASJ,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEF,KAAE,aAAYG,KAAE,GAAGH,EAAC,GAAEK,KAAE,GAAGH,EAAC;AAAE,KAAGH,IAAEC,IAAG,SAASD,IAAEC,IAAE;AAAC,OAAG,MAAK,EAAC,MAAKE,IAAE,QAAOH,IAAE,OAAMI,GAAEJ,EAAC,GAAE,MAAKC,IAAE,MAAK,OAAM,CAAC;AAAA,EAAC,GAAI,WAAU;AAAC,aAAQD,KAAEM,GAAE,IAAI,GAAEL,KAAED,GAAE,MAAKE,KAAEF,GAAE,MAAKE,MAAGA,GAAE,UAAS,CAAAA,KAAEA,GAAE;AAAS,WAAOF,GAAE,WAASA,GAAE,OAAKE,KAAEA,KAAEA,GAAE,OAAKF,GAAE,MAAM,SAAO,UAAQC,KAAE,EAAC,OAAMC,GAAE,KAAI,MAAK,MAAE,IAAE,YAAUD,KAAE,EAAC,OAAMC,GAAE,OAAM,MAAK,MAAE,IAAE,EAAC,OAAM,CAACA,GAAE,KAAIA,GAAE,KAAK,GAAE,MAAK,MAAE,KAAGF,GAAE,SAAO,QAAO,EAAC,OAAM,QAAO,MAAK,KAAE;AAAA,EAAE,GAAGE,KAAE,YAAU,UAAS,CAACA,IAAE,IAAE,GAAE,SAASF,IAAE;AAAC,QAAIC,KAAE,GAAGD,EAAC,GAAEE,KAAE,EAAE;AAAE,SAAGD,MAAG,CAACA,GAAE,EAAE,KAAGC,GAAED,IAAE,IAAG,EAAC,cAAa,MAAG,KAAI,WAAU;AAAC,aAAO;AAAA,IAAI,EAAC,CAAC;AAAA,EAAC,EAAEA,EAAC;AAAC,EAAC,CAAC;AAAE,IAAI,KAAG,GAAG;AAAV,IAAiB,KAAG,EAAE;AAAtB,IAA0B,KAAG,EAAE,UAAU,iBAAiB;AAAE,GAAG,QAAO,UAAU,SAASD,IAAE;AAAC,KAAG,MAAK,EAAC,MAAK,mBAAkB,QAAO,OAAOA,EAAC,GAAE,OAAM,EAAC,CAAC;AAAC,GAAI,WAAU;AAAC,MAAIA,IAAEC,KAAE,GAAG,IAAI,GAAEC,KAAED,GAAE,QAAOE,KAAEF,GAAE;AAAM,SAAOE,MAAGD,GAAE,SAAO,EAAC,OAAM,QAAO,MAAK,KAAE,KAAGF,KAAE,GAAGE,IAAEC,EAAC,GAAEF,GAAE,SAAOD,GAAE,QAAO,EAAC,OAAMA,IAAE,MAAK,MAAE;AAAE,CAAE;AAAE,IAAI,KAAG,EAAC,aAAY,GAAE,qBAAoB,GAAE,cAAa,GAAE,gBAAe,GAAE,aAAY,GAAE,eAAc,GAAE,cAAa,GAAE,sBAAqB,GAAE,UAAS,GAAE,mBAAkB,GAAE,gBAAe,GAAE,iBAAgB,GAAE,mBAAkB,GAAE,WAAU,GAAE,eAAc,GAAE,cAAa,GAAE,UAAS,GAAE,kBAAiB,GAAE,QAAO,GAAE,aAAY,GAAE,eAAc,GAAE,eAAc,GAAE,gBAAe,GAAE,cAAa,GAAE,eAAc,GAAE,kBAAiB,GAAE,kBAAiB,GAAE,gBAAe,GAAE,kBAAiB,GAAE,eAAc,GAAE,WAAU,EAAC;AAA3f,IAA6f,KAAG,EAAE;AAAlgB,IAAsgB,KAAG,EAAE,UAAU,gBAAgB;AAAriB,IAAuiB,KAAG,GAAG,OAAM,SAAS,SAASA,IAAEC,IAAE;AAAC,KAAG,MAAK,EAAC,MAAK,kBAAiB,QAAO,EAAED,EAAC,GAAE,OAAM,GAAE,MAAKC,GAAC,CAAC;AAAC,GAAI,WAAU;AAAC,MAAID,KAAE,GAAG,IAAI,GAAEC,KAAED,GAAE,QAAOE,KAAEF,GAAE,MAAKG,KAAEH,GAAE;AAAQ,SAAM,CAACC,MAAGE,MAAGF,GAAE,UAAQD,GAAE,SAAO,QAAO,EAAC,OAAM,QAAO,MAAK,KAAE,KAAG,UAAQE,KAAE,EAAC,OAAMC,IAAE,MAAK,MAAE,IAAE,YAAUD,KAAE,EAAC,OAAMD,GAAEE,EAAC,GAAE,MAAK,MAAE,IAAE,EAAC,OAAM,CAACA,IAAEF,GAAEE,EAAC,CAAC,GAAE,MAAK,MAAE;AAAC,GAAG,QAAQ;AAAE,GAAG,YAAU,GAAG,OAAM,GAAG,MAAM,GAAE,GAAG,QAAQ,GAAE,GAAG,SAAS;AAAE,IAAI,KAAG,GAAG,UAAU;AAApB,IAAsB,KAAG,GAAG,aAAa;AAAzC,IAA2C,KAAG,GAAG;AAAO,KAAQuB,OAAM,IAAG;AAAK,EAAAC,MAAG,EAAED,GAAE,GAAEE,MAAGD,OAAIA,IAAG;AAAU,MAAGC,KAAG;AAAC,QAAGA,IAAG,EAAE,MAAI,GAAG,KAAG;AAAC,QAAEA,KAAG,IAAG,EAAE;AAAA,IAAC,SAAO5B,IAAE;AAAC,MAAA4B,IAAG,EAAE,IAAE;AAAA,IAAE;AAAC,QAAGA,IAAG,EAAE,KAAG,EAAEA,KAAG,IAAGF,GAAE,GAAE,GAAGA,GAAE;AAAE,WAAQG,OAAM,GAAG,KAAGD,IAAGC,GAAE,MAAI,GAAGA,GAAE,EAAE,KAAG;AAAC,UAAED,KAAGC,KAAG,GAAGA,GAAE,CAAC;AAAA,MAAC,SAAO7B,IAAE;AAAC,QAAA4B,IAAGC,GAAE,IAAE,GAAGA,GAAE;AAAA,MAAC;AAAA;AAAA,EAAC;AAAC;AAApM,IAAAF;AAAS,IAAAC;AAAmH,IAAAC;AAA1I,IAAAH;AAAmN,IAAI,KAAG,WAAU;AAAC,WAAS1B,KAAG;AAAC,OAAG,MAAKA,EAAC;AAAA,EAAC;AAAC,SAAO,GAAGA,IAAE,MAAK,CAAC,EAAC,KAAI,eAAc,OAAM,SAASA,IAAE;AAAC,WAAO,MAAM,KAAK,IAAI,IAAIA,EAAC,CAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,SAASC,IAAE;AAAC,WAAOA,GAAE,OAAQ,SAASA,IAAEC,IAAE;AAAC,UAAIC,KAAE,MAAM,QAAQD,EAAC,IAAEF,GAAE,KAAKE,EAAC,IAAEA;AAAE,aAAOD,GAAE,OAAOE,EAAC;AAAA,IAAC,GAAG,CAAC,CAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,QAAO,OAAM,SAASH,IAAEC,IAAE;AAAC,WAAOD,GAAE,KAAKC,EAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,aAAY,OAAM,SAASD,IAAEC,IAAE;AAAC,WAAOD,GAAE,UAAUC,EAAC;AAAA,EAAC,EAAC,CAAC,CAAC,GAAED;AAAC,EAAE;AAAjX,IAAmX,KAAG,WAAU;AAAC,WAASA,KAAG;AAAC,OAAG,MAAKA,EAAC;AAAA,EAAC;AAAC,SAAO,GAAGA,IAAE,MAAK,CAAC,EAAC,KAAI,SAAQ,OAAM,WAAU;AAAC,WAAO,oBAAI;AAAA,EAAI,EAAC,CAAC,CAAC,GAAEA;AAAC,EAAE;AAAhe,IAA2gB,KAAG,WAAU;AAAC,WAAS8B,KAAG;AAAC,OAAG,MAAKA,EAAC;AAAA,EAAC;AAAC,SAAO,GAAGA,IAAE,MAAK,CAAC,EAAC,KAAI,SAAQ,OAAM,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAI,KAAK,IAAIF,IAAEC,EAAC,GAAEC,EAAC;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,SAAQ,OAAM,SAASF,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAEC,KAAEF,KAAEC,KAAEA,KAAED,KAAEE,KAAEA,KAAEF,KAAEA,KAAEE,KAAEA,KAAEF,KAAEC,KAAEA,KAAED;AAAA,EAAC,EAAC,CAAC,CAAC,GAAEA;AAAC,EAAE;;;AxDA9mrC,IAAIG,MAAK,OAAO;AAChB,IAAIC,MAAK,CAACC,IAAGC,IAAGC,OAAMD,MAAKD,KAAIF,IAAGE,IAAGC,IAAG,EAAE,YAAY,MAAI,cAAc,MAAI,UAAU,MAAI,OAAOC,GAAE,CAAC,IAAIF,GAAEC,EAAC,IAAIC;AAC/G,IAAIC,KAAI,CAACH,IAAGC,IAAGC,QAAOH,IAAGC,IAAG,OAAOC,MAAK,WAAWA,KAAI,KAAKA,IAAGC,EAAC,GAAGA;AASnE,IAAME,KAAI,CAACJ,OAAM,KAAK,MAAMA,KAAI,GAAG,IAAI;AACvC,IAAMK,KAAN,MAAQ;AAAA,EACN,YAAYJ,IAAG;AACb,IAAAE,GAAE,MAAM,UAAU;AAClB,IAAAA,GAAE,MAAM,cAAc,CAAC;AAEvB,IAAAA,GAAE,MAAM,YAAY,CAAC;AACrB,IAAAA,GAAE,MAAM,cAAc,CAAC;AACvB,IAAAA,GAAE,MAAM,aAAa,CAAC;AAEtB,IAAAA,GAAE,MAAM,YAAY,CAAC;AACrB,IAAAA,GAAE,MAAM,mBAAmB,CAAC;AAC5B,IAAAA,GAAE,MAAM,mBAAmB,CAAC;AAE5B,IAAAA,GAAE,MAAM,sBAAsB,CAAC;AAC/B,IAAAA,GAAE,MAAM,kBAAkB,CAAC;AAC3B,IAAAA,GAAE,MAAM,aAAa,MAAM;AACzB,YAAMF,KAAI,KAAK,SAAS,SAAS;AACjC,WAAK,aAAa,KAAK,IAAI,GAAGA,EAAC,IAAI;AAAA,IACrC,CAAC;AACD,IAAAE,GAAE,MAAM,iBAAiB,MAAM;AAC7B,YAAM,EAAE,GAAGF,IAAG,GAAGC,GAAE,IAAI,KAAK,SAAS,MAAM;AAC3C,WAAK,qBAAqBE,GAAEH,EAAC,GAAG,KAAK,iBAAiBG,GAAEF,EAAC;AAAA,IAC3D,CAAC;AACD,IAAAC,GAAE,MAAM,WAAW,MAAM;AACvB,YAAM,EAAE,GAAGF,IAAG,GAAGC,IAAG,GAAGI,GAAE,IAAI,KAAK,SAAS,MAAM;AACjD,WAAK,WAAWF,GAAEH,EAAC,GAAG,KAAK,aAAaG,GAAEF,EAAC,GAAG,KAAK,YAAYE,GAAEE,EAAC;AAAA,IACpE,CAAC;AACD,IAAAH,GAAE,MAAM,WAAW,MAAM;AACvB,YAAM,EAAE,GAAGF,IAAG,GAAGC,IAAG,GAAGI,GAAE,IAAI,KAAK,SAAS,MAAM;AACjD,WAAK,WAAW,KAAK,IAAI,KAAK,KAAK,KAAKL,EAAC,CAAC,GAAG,KAAK,kBAAkBG,GAAEF,EAAC,GAAG,KAAK,kBAAkBE,GAAEE,EAAC;AAAA,IACtG,CAAC;AACD,IAAAH,GAAE,MAAM,eAAe,MAAM,KAAK,SAAS,YAAY,CAAC;AACxD,IAAAA,GAAE,MAAM,eAAe,MAAM,KAAK,SAAS,YAAY,CAAC;AACxD,SAAK,WAAW,UAAEF,EAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG,KAAK,cAAc,GAAG,KAAK,UAAU;AAAA,EAC7F;AAAA,EACA,SAASA,IAAG;AACV,WAAO,KAAK,SAAS,SAASA,EAAC;AAAA,EACjC;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,SAAS,MAAM;AAAA,EAC7B;AAAA,EACA,IAAI,IAAIA,IAAG;AACT,SAAK,WAAW,UAAEA,EAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG,KAAK,UAAU,GAAG,KAAK,cAAc;AAAA,EAC7F;AAAA;AAAA,EAEA,IAAI,IAAIA,IAAG;AACT,SAAK,eAAe,KAAK,KAAK,eAAe,MAAM,KAAK,kBAAkB,GAAG,KAAK,kBAAkB,IAAI,KAAK,WAAW,UAAE;AAAA,MACxH,GAAGG,GAAEH,EAAC;AAAA,MACN,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK,aAAa;AAAA,IACvB,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,cAAc,GAAG,KAAK,WAAWG,GAAEH,EAAC;AAAA,EAC/D;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,WAAWA,IAAG;AAChB,SAAK,WAAW,UAAE;AAAA,MAChB,GAAG,KAAK;AAAA,MACR,GAAGG,GAAEH,EAAC;AAAA,MACN,GAAG,KAAK;AAAA,MACR,GAAG,KAAK,aAAa;AAAA,IACvB,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,cAAc,GAAG,KAAK,kBAAkBG,GAAEH,EAAC;AAAA,EACtE;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,WAAWA,IAAG;AAChB,SAAK,WAAW,UAAE;AAAA,MAChB,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAGG,GAAEH,EAAC;AAAA,MACN,GAAG,KAAK,aAAa;AAAA,IACvB,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,cAAc,GAAG,KAAK,kBAAkBG,GAAEH,EAAC;AAAA,EACtE;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,UAAUA,IAAG;AACf,SAAK,WAAW,UAAE;AAAA,MAChB,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAGG,GAAEH,EAAC;AAAA,MACN,GAAG,KAAK,aAAa;AAAA,IACvB,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAG,KAAK,iBAAiBG,GAAEH,EAAC;AAAA,EAC/D;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,IAAIA,IAAG;AACT,UAAMC,KAAI,KAAK,SAAS,MAAM;AAC9B,SAAK,WAAW,UAAE;AAAA,MAChB,GAAGA;AAAA,MACH,GAAGE,GAAEH,EAAC;AAAA,MACN,GAAG,KAAK,aAAa;AAAA,IACvB,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,cAAc,GAAG,KAAK,WAAWG,GAAEH,EAAC;AAAA,EAC/D;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,MAAMA,IAAG;AACX,UAAMC,KAAI,KAAK,SAAS,MAAM;AAC9B,SAAK,WAAW,UAAE;AAAA,MAChB,GAAGA;AAAA,MACH,GAAGE,GAAEH,EAAC;AAAA,MACN,GAAG,KAAK,aAAa;AAAA,IACvB,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,cAAc,GAAG,KAAK,aAAaG,GAAEH,EAAC;AAAA,EACjE;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,KAAKA,IAAG;AACV,UAAMC,KAAI,KAAK,SAAS,MAAM;AAC9B,SAAK,WAAW,UAAE;AAAA,MAChB,GAAGA;AAAA,MACH,GAAGE,GAAEH,EAAC;AAAA,MACN,GAAG,KAAK,aAAa;AAAA,IACvB,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,cAAc,GAAG,KAAK,YAAYG,GAAEH,EAAC;AAAA,EAChE;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,MAAMA,IAAG;AACX,SAAK,SAAS,SAASA,KAAI,GAAG,GAAG,KAAK,aAAaA;AAAA,EACrD;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM;AACR,WAAO,CAAC,KAAK,KAAK,KAAK,OAAO,KAAK,MAAM,YAAY,KAAK,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC;AAAA,EACpF;AAAA,EACA,IAAI,MAAM;AACR,WAAO,CAAC,KAAK,KAAK,KAAK,YAAY,KAAK,YAAY,YAAY,KAAK,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC;AAAA,EAC/F;AAAA,EACA,IAAI,MAAM;AACR,WAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,YAAY,KAAK,QAAQ,KAAK,QAAQ,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AACF;AACA,SAASM,IAAGP,IAAGC,IAAGC,IAAGI,IAAG;AACtB,SAAO,QAAQ,CAACN,IAAGC,IAAGC,IAAGI,KAAI,GAAG,EAAE,KAAK,GAAG,CAAC;AAC7C;AACA,IAAME,MAAK,CAACR,IAAGC,IAAGC,OAAMD,KAAIC,KAAIF,KAAIC,KAAIA,KAAID,KAAIE,KAAIA,KAAIF,KAAIA,KAAIE,KAAIA,KAAIF,KAAIC,KAAIA,KAAID;AAApF,IAAuFS,MAAK;AAA5F,IAA6GC,MAAK;AAClH,IAAM,IAAI,CAACV,IAAGC,OAAM;AAClB,QAAMC,KAAIF,GAAE,aAAaA;AACzB,aAAW,CAACM,IAAGK,EAAC,KAAKV;AACnB,IAAAC,GAAEI,EAAC,IAAIK;AACT,SAAOT;AACT;AALA,IAKGU,MAAK,gBAAE;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO,oBAAE,WAAWP,EAAC;AAAA,IACrB,MAAM,oBAAE,MAAM,CAAC,SAAS,SAAS,CAAC,EAAE,IAAI,SAAS;AAAA,EACnD;AAAA,EACA,OAAO,CAAC,QAAQ;AAAA,EAChB,MAAML,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,UAAMC,KAAI,IAAE,IAAI,GAAGI,KAAI,IAAE,IAAI;AAC7B,QAAIK,KAAIX,GAAE,SAAS,IAAIK,GAAE;AACzB,UAAMQ,KAAI,SAAE;AAAA,MACV,KAAKF,GAAE;AAAA,MACP,OAAOA,GAAE;AAAA,MACT,MAAMA,GAAE;AAAA,MACR,OAAOA,GAAE;AAAA,IACX,CAAC;AACD;AAAA,MACE,MAAMX,GAAE;AAAA,MACR,CAACc,OAAM;AACL,QAAAA,OAAMH,KAAIG,IAAG,cAAGD,IAAG;AAAA,UACjB,KAAKC,GAAE;AAAA,UACP,OAAOA,GAAE;AAAA,UACT,MAAMA,GAAE;AAAA,UACR,OAAOA,GAAE;AAAA,QACX,CAAC;AAAA,MACH;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb;AACA,UAAMC,KAAI,SAAE,MAAM;AAChB,YAAMD,KAAIP,IAAGM,GAAE,KAAKA,GAAE,OAAOA,GAAE,MAAM,CAAC,GAAGG,KAAIT,IAAGM,GAAE,KAAKA,GAAE,OAAOA,GAAE,MAAM,GAAG;AAC3E,aAAO;AAAA,QACL,YAAY,6BAA6BC,EAAC,MAAME,EAAC;AAAA,MACnD;AAAA,IACF,CAAC,GAAGC,KAAI,MAAM;AACZ,UAAIf,GAAE,SAASI,GAAE,OAAO;AACtB,cAAMQ,KAAID,GAAE,QAAQ,KAAKG,KAAId,GAAE,MAAM,sBAAsB,GAAGgB,KAAIZ,GAAE,MAAM;AAC1E,eAAO,KAAK,MAAMQ,MAAKE,GAAE,QAAQE,MAAKA,KAAI,CAAC;AAAA,MAC7C;AACA,aAAO;AAAA,IACT,GAAGC,KAAI,SAAE,OAAO;AAAA,MACd,MAAMF,GAAE,IAAI;AAAA,MACZ,KAAK;AAAA,IACP,EAAE,GAAGG,KAAI,CAACN,OAAM;AACd,MAAAA,GAAE,WAAWZ,GAAE,SAASmB,GAAEP,EAAC;AAAA,IAC7B,GAAGO,KAAI,CAACP,OAAM;AACZ,UAAIA,GAAE,gBAAgB,GAAGZ,GAAE,SAASI,GAAE,OAAO;AAC3C,cAAMU,KAAId,GAAE,MAAM,sBAAsB,GAAGgB,KAAIZ,GAAE,MAAM;AACvD,YAAIgB,KAAIR,GAAE,UAAUE,GAAE;AACtB,QAAAM,KAAI,KAAK,IAAIJ,KAAI,GAAGI,EAAC,GAAGA,KAAI,KAAK,IAAIA,IAAGN,GAAE,QAAQE,KAAI,CAAC;AACvD,cAAMK,KAAI,KAAK,OAAOD,KAAIJ,KAAI,MAAMF,GAAE,QAAQE,MAAK,GAAG;AACtD,QAAAP,GAAE,QAAQY,IAAGV,GAAE,QAAQU,IAAGtB,GAAE,UAAUsB,EAAC;AAAA,MACzC;AAAA,IACF;AACA,WAAO,aAAG,MAAM;AACd,YAAMT,KAAI;AAAA,QACR,MAAM,CAACE,OAAM;AACX,UAAAK,GAAEL,EAAC;AAAA,QACL;AAAA,QACA,KAAK,CAACA,OAAM;AACV,UAAAK,GAAEL,EAAC;AAAA,QACL;AAAA,MACF;AACA,MAAAd,GAAE,SAASI,GAAE,SAAS,GAAG,iBAAiBJ,GAAE,OAAOY,EAAC;AAAA,IACtD,CAAC,GAAG,EAAE,YAAYZ,IAAG,eAAeI,IAAG,gBAAgBa,IAAG,oBAAoBJ,IAAG,cAAcK,GAAE;AAAA,EACnG;AACF,CAAC;AAtED,IAsEII,MAAK,CAACxB,QAAO,YAAG,iBAAiB,GAAGA,KAAIA,GAAE,GAAG,WAAG,GAAGA;AAtEvD,IAsE2DyB,MAAqBD,IAAG,MAAsB,gBAAE,OAAO,EAAE,OAAO,8BAA8B,GAAG,MAAM,EAAE,CAAC;AAtErK,IAsEwKE,MAAK;AAAA,EAC3KD;AACF;AACA,SAASE,IAAG3B,IAAGC,IAAGC,IAAGI,IAAGK,IAAGE,IAAG;AAC5B,SAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,IACnB,OAAO,eAAE,CAAC,mBAAmB,eAAe,EAAE,gBAAgBb,GAAE,SAAS,QAAQ,CAAC,CAAC;AAAA,EACrF,GAAG;AAAA,IACD,gBAAE,OAAO;AAAA,MACP,KAAK;AAAA,MACL,OAAO;AAAA,MACP,OAAO,eAAEA,GAAE,kBAAkB;AAAA,MAC7B,SAASC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIc,OAAMf,GAAE,gBAAgBA,GAAE,aAAa,GAAGe,EAAC;AAAA,IAC1E,GAAG;AAAA,MACD,gBAAE,OAAO;AAAA,QACP,OAAO,eAAE,CAAC,gCAAgC,EAAE,aAAaf,GAAE,SAAS,QAAQ,CAAC,CAAC;AAAA,QAC9E,KAAK;AAAA,QACL,OAAO,eAAEA,GAAE,cAAc;AAAA,MAC3B,GAAG0B,KAAI,CAAC;AAAA,IACV,GAAG,CAAC;AAAA,EACN,GAAG,CAAC;AACN;AACA,IAAME,MAAqB,EAAEhB,KAAI,CAAC,CAAC,UAAUe,GAAE,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AACnF,IAAME,MAAK;AAAA;AAAA,EAET;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAzEA,IAyEGC,MAAK,gBAAE;AAAA,EACR,MAAM;AAAA,EACN,OAAO,CAAC,QAAQ;AAAA,EAChB,MAAM9B,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,WAAO,EAAE,UAAU4B,KAAI,iBAAiB,CAAClB,OAAMA,OAAM,gBAAgBA,KAAIA,OAAM,YAAY,CAAC,IAAI,EAAE,YAAY,UAAEA,EAAC,EAAE,YAAY,EAAE,GAAG,eAAe,CAACA,OAAM;AACxJ,MAAAV,GAAE,UAAUU,EAAC;AAAA,IACf,EAAE;AAAA,EACJ;AACF,CAAC;AAjFD,IAiFIoB,MAAK,EAAE,OAAO,aAAa;AAjF/B,IAiFkCC,MAAK,CAAC,SAAS;AACjD,SAASC,IAAGjC,IAAGC,IAAGC,IAAGI,IAAGK,IAAGE,IAAG;AAC5B,SAAO,UAAE,GAAG,mBAAE,OAAOkB,KAAI;AAAA,KACtB,UAAE,IAAE,GAAG,mBAAE,UAAG,MAAM,WAAG/B,GAAE,UAAU,CAACe,IAAGE,QAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,MACzD,KAAKA;AAAA,MACL,OAAO;AAAA,IACT,GAAG;AAAA,OACA,UAAE,IAAE,GAAG,mBAAE,UAAG,MAAM,WAAGF,IAAG,CAACI,IAAGC,QAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,QAChD,KAAKA;AAAA,QACL,OAAO;AAAA,QACP,SAAS,CAACC,OAAMrB,GAAE,cAAcmB,EAAC;AAAA,MACnC,GAAG;AAAA,QACD,gBAAE,OAAO;AAAA,UACP,OAAO,eAAE;AAAA,YACP;AAAA,YACA;AAAA,cACE,SAASA,OAAM;AAAA,cACf,aAAaA,OAAM;AAAA,YACrB;AAAA,UACF,CAAC;AAAA,UACD,OAAO,eAAEnB,GAAE,gBAAgBmB,EAAC,CAAC;AAAA,QAC/B,GAAG,MAAM,CAAC;AAAA,MACZ,GAAG,GAAGa,GAAE,EAAE,GAAG,GAAG;AAAA,IAClB,CAAC,EAAE,GAAG,GAAG;AAAA,EACX,CAAC;AACH;AACA,IAAME,MAAqB,EAAEJ,KAAI,CAAC,CAAC,UAAUG,GAAE,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AACnF,IAAME,MAAK,gBAAE;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO,oBAAE,WAAW9B,EAAC;AAAA,IACrB,OAAO,oBAAE,KAAK,IAAI,KAAE;AAAA,IACpB,MAAM,oBAAE,KAAK,IAAI,IAAE;AAAA,EACrB;AAAA,EACA,OAAO,CAAC,QAAQ;AAAA,EAChB,MAAML,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,QAAImC,IAAGC,IAAGC;AACV,UAAMpC,KAAI,mBAAG,GAAGI,KAAI;AAAA,MAClB,KAAK8B,KAAIpC,GAAE,UAAU,OAAO,SAASoC,GAAE,QAAQ;AAAA,MAC/C,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAGzB,KAAI,IAAIN,GAAEC,EAAC,EAAE,YAAY,GAAGO,KAAI,SAAE;AAAA,MACnC,UAAUF;AAAA,MACV,cAAc0B,KAAIrC,GAAE,UAAU,OAAO,SAASqC,GAAE,eAAe;AAAA,MAC/D,cAAcC,KAAItC,GAAE,UAAU,OAAO,SAASsC,GAAE,eAAe;AAAA,IACjE,CAAC,GAAGvB,KAAI,IAAE,CAAC,GAAGE,KAAI,IAAE,CAAC,GAAGE,KAAI,IAAE,GAAGC,KAAI,SAAE,OAAO;AAAA,MAC5C,KAAKL,GAAE,QAAQ;AAAA,MACf,MAAME,GAAE,QAAQ;AAAA,IAClB,EAAE,GAAGI,KAAI,MAAM;AACb,UAAInB,IAAG;AACL,cAAMqC,KAAIrC,GAAE,MAAM;AAClB,QAAAe,GAAE,QAAQJ,GAAE,cAAc0B,MAAK,OAAO,SAASA,GAAE,cAAcxB,GAAE,SAAS,IAAIF,GAAE,eAAe0B,MAAK,OAAO,SAASA,GAAE;AAAA,MACxH;AAAA,IACF;AACA,QAAIzB,KAAI;AACR,UAAME,KAAI,CAACuB,OAAM;AACf,MAAAzB,KAAI,MAAIS,GAAEgB,EAAC;AAAA,IACb,GAAGrB,KAAI,CAACqB,OAAM;AACZ,MAAAzB,MAAKS,GAAEgB,EAAC;AAAA,IACV,GAAGjB,KAAI,MAAM;AACX,MAAAR,KAAI;AAAA,IACN,GAAGS,KAAI,CAACgB,OAAM;AACZ,UAAIrC,IAAG;AACL,cAAMsC,KAAItC,GAAE,MAAM,IAAIuC,KAAID,MAAK,OAAO,SAASA,GAAE,sBAAsB;AACvE,YAAIE,KAAIH,GAAE,UAAUE,GAAE,MAAME,KAAIJ,GAAE,UAAUE,GAAE;AAC9C,QAAAC,KAAIlC,IAAGkC,IAAG,GAAGD,GAAE,KAAK,GAAGE,KAAInC,IAAGmC,IAAG,GAAGF,GAAE,MAAM;AAC5C,cAAMG,KAAIF,KAAID,GAAE,OAAO,IAAIjC,IAAG,EAAEmC,KAAIF,GAAE,UAAU,GAAG,GAAG,CAAC;AACvD,QAAAxB,GAAE,QAAQyB,IAAG3B,GAAE,QAAQ4B,IAAG9B,GAAE,aAAa+B,IAAG/B,GAAE,aAAa,GAAGZ,GAAE,UAAU2C,IAAG,CAAC;AAAA,MAChF;AAAA,IACF;AACA,WAAO,aAAG,MAAM;AACd,MAAA1C,MAAKA,GAAE,MAAM,MAAMiB,GAAE,SAAS,SAAG,MAAM;AACrC,QAAAE,GAAE;AAAA,MACJ,CAAC;AAAA,IACH,CAAC,GAAG;AAAA,MACF,MAAMrB,GAAE;AAAA,MACR,CAACuC,OAAM;AACL,sBAAG1B,IAAG;AAAA,UACJ,UAAU,IAAIR,GAAE,EAAE,GAAGkC,GAAE,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,YAAY;AAAA,UACtD,YAAYA,GAAE;AAAA,UACd,YAAYA,GAAE;AAAA,QAChB,CAAC,GAAGlB,GAAE;AAAA,MACR;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG,EAAE,OAAOR,IAAG,eAAeM,IAAG,gBAAgBC,IAAG,cAAcJ,IAAG,QAAQE,IAAG,WAAWI,GAAE;AAAA,EAC/F;AACF,CAAC;AA3DD,IA2DIuB,MAAK,CAAC7C,QAAO,YAAG,iBAAiB,GAAGA,KAAIA,GAAE,GAAG,WAAG,GAAGA;AA3DvD,IA2D2D8C,MAAqBD,IAAG,MAAsB,gBAAE,OAAO,EAAE,OAAO,uBAAuB,GAAG,MAAM,EAAE,CAAC;AA3D9J,IA2DiKE,MAAqBF,IAAG,MAAsB,gBAAE,OAAO,EAAE,OAAO,uBAAuB,GAAG,MAAM,EAAE,CAAC;AA3DpQ,IA2DuQG,MAAqBH,IAAG,MAAsB,gBAAE,OAAO,MAAM,MAAM,EAAE,CAAC;AA3D7U,IA2DgVI,MAAK;AAAA,EACnVD;AACF;AACA,SAASE,IAAGlD,IAAGC,IAAGC,IAAGI,IAAGK,IAAGE,IAAG;AAC5B,SAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,IACnB,KAAK;AAAA,IACL,OAAO,eAAE,CAAC,iBAAiB,EAAE,yBAAyBb,GAAE,OAAO,yBAAyBA,GAAE,KAAK,CAAC,CAAC;AAAA,IACjG,OAAO,eAAE,EAAE,iBAAiBA,GAAE,MAAM,SAAS,CAAC;AAAA,IAC9C,aAAaC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIc,OAAMf,GAAE,gBAAgBA,GAAE,aAAa,GAAGe,EAAC;AAAA,IAC5E,aAAad,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIc,OAAMf,GAAE,UAAUA,GAAE,OAAO,GAAGe,EAAC;AAAA,IAChE,WAAWd,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIc,OAAMf,GAAE,aAAaA,GAAE,UAAU,GAAGe,EAAC;AAAA,EACtE,GAAG;AAAA,IACD+B;AAAA,IACAC;AAAA,IACA,gBAAE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,MACL,OAAO,eAAE/C,GAAE,cAAc;AAAA,IAC3B,GAAGiD,KAAI,CAAC;AAAA,EACV,GAAG,EAAE;AACP;AACA,IAAME,MAAqB,EAAEhB,KAAI,CAAC,CAAC,UAAUe,GAAE,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AACnF,IAAME,MAAK,gBAAE;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO,oBAAE,WAAW/C,EAAC;AAAA,IACrB,MAAM,oBAAE,MAAM,CAAC,SAAS,SAAS,CAAC,EAAE,IAAI,SAAS;AAAA,EACnD;AAAA,EACA,OAAO,CAAC,QAAQ;AAAA,EAChB,MAAML,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,UAAMC,KAAI,IAAE,IAAI,GAAGI,KAAI,IAAE,IAAI;AAC7B,QAAIK,KAAIX,GAAE,SAAS,IAAIK,GAAE;AACzB,UAAMQ,KAAI,SAAE;AAAA,MACV,KAAKF,GAAE,OAAO;AAAA,IAChB,CAAC;AACD;AAAA,MACE,MAAMX,GAAE;AAAA,MACR,CAACqB,OAAM;AACL,QAAAA,OAAMV,KAAIU,IAAG,cAAGR,IAAG,EAAE,KAAKF,GAAE,IAAI,CAAC;AAAA,MACnC;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb;AACA,UAAMI,KAAI,MAAM;AACd,UAAIb,GAAE,SAASI,GAAE,OAAO;AACtB,cAAMe,KAAInB,GAAE,MAAM,sBAAsB,GAAGY,KAAIR,GAAE,MAAM;AACvD,eAAOO,GAAE,QAAQ,MAAMQ,GAAE,QAAQP,KAAI,IAAID,GAAE,MAAM,OAAOQ,GAAE,QAAQP,MAAK,MAAMA,KAAI;AAAA,MACnF;AACA,aAAO;AAAA,IACT,GAAGG,KAAI,SAAE,OAAO;AAAA,MACd,MAAMF,GAAE,IAAI;AAAA,MACZ,KAAK;AAAA,IACP,EAAE,GAAGI,KAAI,CAACE,OAAM;AACd,MAAAA,GAAE,WAAWnB,GAAE,SAASkB,GAAEC,EAAC;AAAA,IAC7B,GAAGD,KAAI,CAACC,OAAM;AACZ,UAAIA,GAAE,gBAAgB,GAAGnB,GAAE,SAASI,GAAE,OAAO;AAC3C,cAAMQ,KAAIZ,GAAE,MAAM,sBAAsB,GAAGc,KAAIV,GAAE,MAAM;AACvD,YAAIY,KAAIG,GAAE,UAAUP,GAAE;AACtB,QAAAI,KAAI,KAAK,IAAIA,IAAGJ,GAAE,QAAQE,KAAI,CAAC,GAAGE,KAAI,KAAK,IAAIF,KAAI,GAAGE,EAAC;AACvD,cAAMI,KAAI,KAAK,OAAOJ,KAAIF,KAAI,MAAMF,GAAE,QAAQE,MAAK,GAAG;AACtD,QAAAL,GAAE,MAAMW,IAAGT,GAAE,MAAMS,IAAGrB,GAAE,UAAUqB,EAAC;AAAA,MACrC;AAAA,IACF;AACA,WAAO,aAAG,MAAM;AACd,YAAMD,KAAI;AAAA,QACR,MAAM,CAACP,OAAM;AACX,UAAAM,GAAEN,EAAC;AAAA,QACL;AAAA,QACA,KAAK,CAACA,OAAM;AACV,UAAAM,GAAEN,EAAC;AAAA,QACL;AAAA,MACF;AACA,MAAAZ,GAAE,SAASI,GAAE,SAAS,GAAG,iBAAiBJ,GAAE,OAAOmB,EAAC;AAAA,IACtD,CAAC,GAAG,EAAE,YAAYnB,IAAG,eAAeI,IAAG,gBAAgBW,IAAG,cAAcE,GAAE;AAAA,EAC5E;AACF,CAAC;AApDD,IAoDIkC,MAAK,CAACrD,QAAO,YAAG,iBAAiB,GAAGA,KAAIA,GAAE,GAAG,WAAG,GAAGA;AApDvD,IAoD2DsD,MAAqBD,IAAG,MAAsB,gBAAE,OAAO,EAAE,OAAO,4BAA4B,GAAG,MAAM,EAAE,CAAC;AApDnK,IAoDsKE,MAAK;AAAA,EACzKD;AACF;AACA,SAASE,IAAGxD,IAAGC,IAAGC,IAAGI,IAAGK,IAAGE,IAAG;AAC5B,SAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,IACnB,OAAO,eAAE,CAAC,iBAAiB,EAAE,gBAAgBb,GAAE,SAAS,QAAQ,CAAC,CAAC;AAAA,EACpE,GAAG;AAAA,IACD,gBAAE,OAAO;AAAA,MACP,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAASC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIc,OAAMf,GAAE,gBAAgBA,GAAE,aAAa,GAAGe,EAAC;AAAA,IAC1E,GAAG;AAAA,MACD,gBAAE,OAAO;AAAA,QACP,OAAO,eAAE,CAAC,8BAA8B,EAAE,aAAaf,GAAE,SAAS,QAAQ,CAAC,CAAC;AAAA,QAC5E,KAAK;AAAA,QACL,OAAO,eAAEA,GAAE,cAAc;AAAA,MAC3B,GAAGuD,KAAI,CAAC;AAAA,IACV,GAAG,GAAG;AAAA,EACR,GAAG,CAAC;AACN;AACA,IAAME,MAAqB,EAAEL,KAAI,CAAC,CAAC,UAAUI,GAAE,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AACnF,IAAME,MAAK,gBAAE;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO,oBAAE,WAAWrD,EAAC;AAAA,IACrB,MAAM,oBAAE,MAAM,CAAC,SAAS,SAAS,CAAC,EAAE,IAAI,SAAS;AAAA,EACnD;AAAA,EACA,OAAO,CAAC,QAAQ;AAAA,EAChB,MAAML,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,UAAMC,KAAI,IAAE,IAAI,GAAGI,KAAI,IAAE,IAAI;AAC7B,QAAIK,KAAIX,GAAE,SAAS,IAAIK,GAAE;AACzB,UAAM,CAACQ,IAAGE,IAAGE,EAAC,IAAIN,GAAE,KAAKQ,KAAI,SAAE;AAAA,MAC7B,KAAKN;AAAA,MACL,YAAYE;AAAA,MACZ,WAAWE;AAAA,IACb,CAAC;AACD;AAAA,MACE,MAAMjB,GAAE;AAAA,MACR,CAACsB,OAAM;AACL,YAAIA,IAAG;AACL,UAAAX,KAAIW;AACJ,gBAAM,CAACC,IAAGa,IAAGC,EAAC,IAAI1B,GAAE;AACpB,wBAAGQ,IAAG;AAAA,YACJ,KAAKI;AAAA,YACL,YAAYa;AAAA,YACZ,WAAWC;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb;AACA,UAAMjB,KAAI,SAAE,MAAM;AAChB,YAAME,KAAI,UAAE;AAAA,QACV,GAAGH,GAAE;AAAA,QACL,GAAGA,GAAE;AAAA,QACL,GAAG;AAAA,MACL,CAAC,EAAE,sBAAsB,GAAGI,KAAI,UAAE;AAAA,QAChC,GAAGJ,GAAE;AAAA,QACL,GAAGA,GAAE;AAAA,QACL,GAAG;AAAA,MACL,CAAC,EAAE,sBAAsB,GAAGiB,KAAI,UAAE;AAAA,QAChC,GAAGjB,GAAE;AAAA,QACL,GAAGA,GAAE;AAAA,QACL,GAAG;AAAA,MACL,CAAC,EAAE,sBAAsB,GAAGkB,KAAI,UAAE;AAAA,QAChC,GAAGlB,GAAE;AAAA,QACL,GAAGA,GAAE;AAAA,QACL,GAAG;AAAA,MACL,CAAC,EAAE,sBAAsB;AACzB,aAAO;AAAA,QACL,YAAY;AAAA,UACV,iDAAiDG,EAAC,KAAKC,EAAC,KAAKa,EAAC,KAAKC,EAAC;AAAA,UACpE,qDAAqDf,EAAC,KAAKC,EAAC,KAAKa,EAAC,KAAKC,EAAC;AAAA,UACxE,kDAAkDf,EAAC,KAAKC,EAAC,KAAKa,EAAC,KAAKC,EAAC;AAAA,UACrE,iDAAiDf,EAAC,KAAKC,EAAC,KAAKa,EAAC,KAAKC,EAAC;AAAA,QACtE;AAAA,MACF;AAAA,IACF,CAAC,GAAGhB,KAAI,MAAM;AACZ,UAAInB,GAAE,SAASI,GAAE,OAAO;AACtB,cAAMgB,KAAIH,GAAE,WAAWI,KAAIrB,GAAE,MAAM,sBAAsB,GAAGkC,KAAI9B,GAAE,MAAM;AACxE,gBAAQ,IAAIgB,OAAMC,GAAE,QAAQa,MAAKA,KAAI;AAAA,MACvC;AACA,aAAO;AAAA,IACT,GAAGtB,KAAI,SAAE,OAAO;AAAA,MACd,MAAMO,GAAE,IAAI;AAAA,MACZ,KAAK;AAAA,IACP,EAAE,GAAGL,KAAI,CAACM,OAAM;AACd,MAAAA,GAAE,WAAWpB,GAAE,SAASgB,GAAEI,EAAC;AAAA,IAC7B,GAAGJ,KAAI,CAACI,OAAM;AACZ,UAAIA,GAAE,gBAAgB,GAAGpB,GAAE,SAASI,GAAE,OAAO;AAC3C,cAAMiB,KAAIrB,GAAE,MAAM,sBAAsB,GAAGkC,KAAI9B,GAAE,MAAM;AACvD,YAAI+B,KAAIf,GAAE,UAAUC,GAAE;AACtB,QAAAc,KAAI,KAAK,IAAID,KAAI,GAAGC,EAAC,GAAGA,KAAI,KAAK,IAAIA,IAAGd,GAAE,QAAQa,KAAI,CAAC;AACvD,cAAME,KAAI,KAAKD,KAAID,KAAI,MAAMb,GAAE,QAAQa;AACvC,QAAAzB,GAAE,YAAY2B,IAAGrC,GAAE,UAAUqC,EAAC;AAAA,MAChC;AAAA,IACF;AACA,WAAO,aAAG,MAAM;AACd,YAAMhB,KAAI;AAAA,QACR,MAAM,CAACC,OAAM;AACX,UAAAL,GAAEK,EAAC;AAAA,QACL;AAAA,QACA,KAAK,CAACA,OAAM;AACV,UAAAL,GAAEK,EAAC;AAAA,QACL;AAAA,MACF;AACA,MAAArB,GAAE,SAASI,GAAE,SAAS,GAAG,iBAAiBJ,GAAE,OAAOoB,EAAC;AAAA,IACtD,CAAC,GAAG,EAAE,YAAYpB,IAAG,eAAeI,IAAG,gBAAgBQ,IAAG,oBAAoBM,IAAG,cAAcJ,GAAE;AAAA,EACnG;AACF,CAAC;AAxFD,IAwFI2C,MAAK,CAAC3D,QAAO,YAAG,iBAAiB,GAAGA,KAAIA,GAAE,GAAG,WAAG,GAAGA;AAxFvD,IAwF2D4D,MAAqBD,IAAG,MAAsB,gBAAE,OAAO,EAAE,OAAO,kCAAkC,GAAG,MAAM,EAAE,CAAC;AAxFzK,IAwF4KE,MAAK;AAAA,EAC/KD;AACF;AACA,SAASE,IAAG9D,IAAGC,IAAGC,IAAGI,IAAGK,IAAGE,IAAG;AAC5B,SAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,IACnB,OAAO,eAAE,CAAC,uBAAuB,EAAE,gBAAgBb,GAAE,SAAS,QAAQ,CAAC,CAAC;AAAA,EAC1E,GAAG;AAAA,IACD,gBAAE,OAAO;AAAA,MACP,KAAK;AAAA,MACL,OAAO;AAAA,MACP,OAAO,eAAEA,GAAE,kBAAkB;AAAA,MAC7B,SAASC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIc,OAAMf,GAAE,gBAAgBA,GAAE,aAAa,GAAGe,EAAC;AAAA,IAC1E,GAAG;AAAA,MACD,gBAAE,OAAO;AAAA,QACP,OAAO,eAAE,CAAC,oCAAoC,EAAE,aAAaf,GAAE,SAAS,QAAQ,CAAC,CAAC;AAAA,QAClF,KAAK;AAAA,QACL,OAAO,eAAEA,GAAE,cAAc;AAAA,MAC3B,GAAG6D,KAAI,CAAC;AAAA,IACV,GAAG,CAAC;AAAA,EACN,GAAG,CAAC;AACN;AACA,IAAME,MAAqB,EAAEL,KAAI,CAAC,CAAC,UAAUI,GAAE,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AACnF,IAAME,MAAK,gBAAE;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,IACL,QAAQ,oBAAE,QAAQ,MAAM,EAAE,IAAI,MAAM,CAAC,CAAC;AAAA,IACtC,OAAO,oBAAE,KAAK,IAAI,KAAE;AAAA,EACtB;AAAA,EACA,OAAO,CAAC,QAAQ;AAAA,EAChB,MAAMhE,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,WAAO,EAAE,eAAe,CAACK,OAAM;AAC7B,MAAAL,GAAE,UAAUK,EAAC;AAAA,IACf,EAAE;AAAA,EACJ;AACF,CAAC;AAZD,IAYI2D,MAAK;AAAA,EACP,KAAK;AAAA,EACL,OAAO;AACT;AAfA,IAeGC,MAAK,EAAE,OAAO,aAAa;AAf9B,IAeiCC,MAAK,CAAC,SAAS;AAChD,SAASC,IAAGpE,IAAGC,IAAGC,IAAGI,IAAGK,IAAGE,IAAG;AAC5B,SAAOb,GAAE,UAAUA,GAAE,OAAO,SAAS,KAAK,UAAE,GAAG,mBAAE,OAAOiE,KAAI;AAAA,IAC1D,gBAAE,OAAOC,KAAI;AAAA,OACV,UAAE,IAAE,GAAG,mBAAE,UAAG,MAAM,WAAGlE,GAAE,QAAQ,CAACe,IAAGE,QAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,QACvD,KAAKA;AAAA,QACL,OAAO,eAAE,CAAC,cAAc,eAAe,EAAE,qBAAqBjB,GAAE,MAAM,CAAC,CAAC;AAAA,QACxE,SAAS,CAACmB,OAAMnB,GAAE,cAAce,EAAC;AAAA,MACnC,GAAG;AAAA,QACD,gBAAE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO,eAAE,EAAE,iBAAiBA,GAAE,CAAC;AAAA,QACjC,GAAG,MAAM,CAAC;AAAA,MACZ,GAAG,IAAIoD,GAAE,EAAE,GAAG,GAAG;AAAA,IACnB,CAAC;AAAA,EACH,CAAC,KAAK,mBAAE,IAAI,IAAE;AAChB;AACA,IAAME,MAAqB,EAAEL,KAAI,CAAC,CAAC,UAAUI,GAAE,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AACnF,IAAME,MAAK,gBAAE;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO,oBAAE,WAAWjE,EAAC;AAAA,IACrB,cAAc,oBAAE,KAAK,IAAI,KAAE;AAAA,EAC7B;AAAA,EACA,OAAO,CAAC,gBAAgB,QAAQ;AAAA,EAChC,MAAML,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,QAAIiB,IAAGI,IAAGC,IAAGa;AACb,UAAM,EAAE,MAAMlC,IAAG,QAAQI,IAAG,aAAaK,GAAE,IAAI,aAAG,GAAGE,KAAI,IAAE,KAAK,GAAGE,KAAI,SAAE;AAAA,MACvE,OAAOf,GAAE;AAAA,MACT,MAAMkB,KAAIlB,GAAE,UAAU,OAAO,SAASkB,GAAE;AAAA,MACxC,OAAO,KAAK,QAAQI,KAAItB,GAAE,UAAU,OAAO,SAASsB,GAAE,UAAU,GAAG;AAAA,MACnE,OAAOC,KAAIvB,GAAE,UAAU,OAAO,SAASuB,GAAE;AAAA,MACzC,iBAAiBa,KAAIpC,GAAE,UAAU,OAAO,SAASoC,GAAE,YAAY;AAAA,IACjE,CAAC,GAAGnB,KAAI,SAAE,OAAO;AAAA,MACf,YAAYF,GAAE;AAAA,IAChB,EAAE,GAAGI,KAAI,MAAM;AACb,MAAAN,GAAE,QAAQA,GAAE,UAAU,SAAS,QAAQ;AAAA,IACzC,GAAGO,KAAI,cAAE,CAACiB,OAAM;AACd,UAAI,CAACA,GAAE,OAAO;AACZ;AACF,UAAIC,KAAI,SAASD,GAAE,OAAO,MAAM,QAAQ,KAAK,EAAE,CAAC;AAChD,MAAAC,KAAI,QAAQD,GAAE,OAAO,QAAQ,OAAOC,KAAI,MAAMA,KAAI,MAAMD,GAAE,OAAO,QAAQ,KAAKC,KAAI,IAAI,MAAMA,EAAC,MAAMD,GAAE,OAAO,QAAQ,OAAOC,KAAI,MAAM,CAAC,MAAMA,EAAC,KAAKvB,GAAE,UAAUA,GAAE,MAAM,QAAQuB,KAAIrC,GAAE,UAAUc,GAAE,KAAK;AAAA,IACvM,GAAG,GAAG,GAAGM,KAAI,cAAE,CAACgB,IAAGC,OAAM;AACvB,UAAIvB,GAAE,OAAO;AACX,YAAIF,GAAE,UAAU,OAAO;AACrB,gBAAM0B,KAAIF,GAAE,OAAO,MAAM,QAAQ,KAAK,EAAE;AACxC,oBAAEE,EAAC,EAAE,QAAQ,IAAI,CAAC,GAAG,CAAC,EAAE,SAASA,GAAE,MAAM,MAAMxB,GAAE,MAAM,MAAMwB,MAAKxB,GAAE,MAAM,MAAM,UAAUd,GAAE,UAAUc,GAAE,KAAK;AAAA,QAC/G,WAAWF,GAAE,UAAU,UAAUyB,OAAM,KAAKD,GAAE,OAAO,MAAM,SAAS,MAAM,QAAQtB,GAAE,MAAM;AACxF,UAAAA,GAAE,KAAKuB,EAAC,IAAID,GAAE,OAAO;AACrB,gBAAM,CAACE,IAAGC,IAAGC,IAAGC,EAAC,IAAI3B,GAAE;AACvB,UAAAA,GAAE,MAAM,MAAM,UAAE,EAAE,GAAGwB,IAAG,GAAGC,IAAG,GAAGC,GAAE,CAAC,EAAE,MAAM,GAAG1B,GAAE,MAAM,QAAQ,KAAK,MAAM2B,KAAI,GAAG,GAAGzC,GAAE,UAAUc,GAAE,KAAK;AAAA,QACzG;AAAA,MACF;AAAA,IACF,GAAG,GAAG,GAAGD,KAAI,cAAE,CAACuB,IAAGC,OAAM;AACvB,UAAID,GAAE,OAAO,OAAO;AAClB,YAAIxB,GAAE,UAAU,OAAO;AACrB,gBAAM0B,KAAIF,GAAE,OAAO,MAAM,QAAQ,KAAK,EAAE;AACxC,oBAAEE,EAAC,EAAE,QAAQ,KAAKxB,GAAE,SAAS,CAAC,GAAG,CAAC,EAAE,SAASwB,GAAE,MAAM,MAAMxB,GAAE,MAAM,MAAMwB;AAAA,QAC3E,WAAWD,OAAM,UAAUvB,GAAE,QAAQA,GAAE,OAAO;AAC5C,cAAIsB,GAAE,OAAO,QAAQ,MAAMA,GAAE,OAAO,QAAQ,IAAIC,OAAM,OAAOD,GAAE,OAAO,QAAQ,KAAK,MAAMA,GAAE,OAAO,KAAK,OAAOA,GAAE,OAAO,QAAQ,IAAIA,GAAE,OAAO,MAAM,SAAS,MAAM;AAC/J;AACF,UAAAC,KAAI,KAAKD,GAAE,OAAO,QAAQ,QAAQA,GAAE,OAAO,QAAQ,MAAMtB,GAAE,KAAKuB,EAAC,IAAID,GAAE,OAAO;AAC9E,gBAAM,CAACE,IAAGC,IAAGC,IAAGC,EAAC,IAAI3B,GAAE;AACvB,UAAAA,GAAE,MAAM,MAAM,UAAE,EAAE,GAAGwB,IAAG,GAAGC,IAAG,GAAGC,GAAE,CAAC,EAAE,MAAM,GAAG1B,GAAE,MAAM,QAAQ,KAAK,MAAM2B,KAAI,GAAG;AAAA,QACnF;AACA,QAAAzC,GAAE,UAAUc,GAAE,KAAK;AAAA,MACrB;AAAA,IACF,GAAG,GAAG,GAAGC,KAAI,MAAM;AACjB,UAAIL,MAAKI,GAAE,OAAO;AAChB,cAAMsB,KAAIxB,GAAE,UAAU,QAAQE,GAAE,MAAM,SAASA,GAAE,MAAM,UAAU,MAAM,SAAS,MAAM,IAAIA,GAAE,MAAM,YAAY;AAC9G,QAAAb,GAAEmC,MAAK,EAAE;AAAA,MACX;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAMrC,GAAE;AAAA,MACR,CAACqC,OAAM;AACL,QAAAA,OAAMtB,GAAE,QAAQsB,IAAGtB,GAAE,QAAQ,KAAK,MAAMA,GAAE,MAAM,KAAK,GAAGA,GAAE,MAAMA,GAAE,MAAM,KAAKA,GAAE,OAAOA,GAAE,MAAM;AAAA,MAChG;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG;AAAA,MACD,MAAMA,GAAE;AAAA,MACR,MAAM;AACJ,QAAAA,GAAE,UAAUA,GAAE,iBAAiBA,GAAE,MAAM,YAAY;AAAA,MACrD;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG;AAAA,MACD,OAAOA;AAAA,MACP,iBAAiBE;AAAA,MACjB,WAAWJ;AAAA,MACX,QAAQP;AAAA,MACR,mBAAmBa;AAAA,MACnB,aAAaC;AAAA,MACb,eAAeN;AAAA,MACf,cAAcO;AAAA,MACd,gBAAgBL;AAAA,IAClB;AAAA,EACF;AACF,CAAC;AA/ED,IA+EIuD,MAAK,EAAE,OAAO,aAAa;AA/E/B,IA+EkCC,MAAK,EAAE,OAAO,kCAAkC;AA/ElF,IA+EqFC,MAAK;AAAA,EACxF,KAAK;AAAA,EACL,OAAO;AACT;AAlFA,IAkFGC,MAAK;AAAA,EACN,KAAK;AAAA,EACL,OAAO,EAAE,SAAS,QAAQ,MAAM,KAAK,KAAK,OAAO,QAAQ,OAAO;AAClE;AArFA,IAqFGC,MAAK,EAAE,OAAO,iBAAiB;AArFlC,IAqFqCC,MAAK;AAAA,EACxC,KAAK;AAAA,EACL,OAAO;AACT;AAxFA,IAwFGC,MAAK,CAAC,OAAO;AAxFhB,IAwFmBC,MAAK;AAAA,EACtB,KAAK;AAAA,EACL,OAAO,EAAE,SAAS,QAAQ,MAAM,KAAK,KAAK,OAAO,QAAQ,OAAO;AAClE;AA3FA,IA2FGC,MAAK,CAAC,SAAS,WAAW,QAAQ;AACrC,SAASC,IAAGhF,IAAGC,IAAGC,IAAGI,IAAGK,IAAGE,IAAG;AAC5B,SAAO,UAAE,GAAG,mBAAE,OAAO0D,KAAI;AAAA,IACvB,gBAAE,OAAOC,KAAI;AAAA,MACX,gBAAE,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO,eAAExE,GAAE,eAAe;AAAA,QAC1B,SAASC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIc,OAAMf,GAAE,kBAAkBA,GAAE,eAAe,GAAGe,EAAC;AAAA,MAC9E,GAAG;AAAA,QACDf,GAAE,UAAU,UAAE,GAAG,mBAAE,QAAQyE,KAAI,SAAS,KAAK,mBAAE,IAAI,IAAE;AAAA,MACvD,GAAG,CAAC;AAAA,IACN,CAAC;AAAA,IACDzE,GAAE,cAAc,SAAS,UAAE,GAAG,mBAAE,OAAO0E,KAAI;AAAA,MACzC,gBAAE,OAAOC,KAAI;AAAA,QACX,eAAG,gBAAE,SAAS;AAAA,UACZ,uBAAuB1E,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,CAACc,OAAMf,GAAE,MAAM,MAAMe;AAAA,UAC5D,WAAW;AAAA,UACX,SAASd,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIc,OAAMf,GAAE,iBAAiBA,GAAE,cAAc,GAAGe,EAAC;AAAA,UAC1E,QAAQd,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIc,OAAMf,GAAE,gBAAgBA,GAAE,aAAa,GAAGe,EAAC;AAAA,QACzE,GAAG,MAAM,GAAG,GAAG;AAAA,UACb,CAAC,YAAIf,GAAE,MAAM,GAAG;AAAA,QAClB,CAAC;AAAA,MACH,CAAC;AAAA,MACDA,GAAE,eAAe,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,mBAAE,OAAO4E,KAAI;AAAA,QAC9C,gBAAE,SAAS;AAAA,UACT,OAAO;AAAA,UACP,OAAO5E,GAAE,MAAM;AAAA,UACf,SAASC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIc,OAAMf,GAAE,eAAeA,GAAE,YAAY,GAAGe,EAAC;AAAA,QACxE,GAAG,MAAM,IAAI8D,GAAE;AAAA,QACf,gBAAG,IAAI;AAAA,MACT,CAAC;AAAA,IACH,CAAC,KAAK7E,GAAE,MAAM,QAAQ,UAAE,GAAG,mBAAE,OAAO8E,KAAI;AAAA,OACrC,UAAE,IAAE,GAAG,mBAAE,UAAG,MAAM,WAAG9E,GAAE,MAAM,MAAM,CAACe,IAAGE,QAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,QAC3D,OAAO;AAAA,QACP,KAAKA;AAAA,MACP,GAAG;AAAA,QACD,gBAAE,SAAS;AAAA,UACT,OAAOF;AAAA,UACP,SAAS,CAACI,OAAMnB,GAAE,cAAcmB,IAAGF,EAAC;AAAA,UACpC,QAAQ,CAACE,OAAMnB,GAAE,aAAamB,IAAGF,EAAC;AAAA,QACpC,GAAG,MAAM,IAAI8D,GAAE;AAAA,MACjB,CAAC,EAAE,GAAG,GAAG;AAAA,IACX,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,IACd,gBAAE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS9E,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIc,OAAMf,GAAE,qBAAqBA,GAAE,kBAAkB,GAAGe,EAAC;AAAA,IACpF,GAAG,gBAAGf,GAAE,SAAS,GAAG,CAAC;AAAA,EACvB,CAAC;AACH;AACA,IAAMiF,MAAqB,EAAEX,KAAI,CAAC,CAAC,UAAUU,GAAE,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AACnF,IAAME,MAAK,gBAAE;AAAA,EACX,MAAM;AAAA,EACN,YAAY,EAAE,SAASD,KAAI,OAAOrD,KAAI,SAASM,KAAI,OAAOiB,KAAI,KAAKM,KAAI,WAAWM,KAAI,SAASM,IAAG;AAAA,EAClG,OAAO;AAAA,IACL,OAAO,oBAAE,WAAWhE,EAAC;AAAA,IACrB,gBAAgB,oBAAE,KAAK,IAAI,KAAE;AAAA,IAC7B,cAAc,oBAAE,KAAK,IAAI,KAAE;AAAA,IAC3B,cAAc,oBAAE,KAAK,IAAI,KAAE;AAAA,EAC7B;AAAA,EACA,OAAO,CAAC,gBAAgB,UAAU,eAAe;AAAA,EACjD,MAAML,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,UAAMC,KAAIF,GAAE,SAAS,IAAIK,GAAE,GAAGC,KAAI,SAAE;AAAA,MAClC,OAAOJ;AAAA,MACP,KAAKA,GAAE,YAAY;AAAA,MACnB,KAAKA,GAAE,YAAY;AAAA,IACrB,CAAC,GAAGS,KAAI,IAAE,KAAE,GAAGE,KAAI,SAAE,OAAO,EAAE,YAAYP,GAAE,IAAI,EAAE,GAAGS,KAAI,MAAM;AAC7D,MAAAJ,GAAE,QAAQ,OAAIV,GAAE,iBAAiB,KAAE;AAAA,IACrC,GAAGgB,KAAI,gBAAGR,KAAI,CAAC,GAAG,CAAC,CAAC,GAAGU,KAAI,cAAE,MAAM;AACjC,UAAInB,GAAE;AACJ;AACF,YAAMuB,KAAIjB,GAAE,MAAM,YAAY;AAC9B,UAAIW,GAAE,QAAQA,GAAE,MAAM,OAAO,CAACmB,OAAM,CAAC,UAAE,OAAOA,IAAGb,EAAC,CAAC,GAAG,CAACN,GAAE,MAAM,SAASM,EAAC,GAAG;AAC1E,eAAON,GAAE,MAAM,SAASP;AACtB,UAAAO,GAAE,MAAM,IAAI;AACd,QAAAA,GAAE,MAAM,QAAQM,EAAC;AAAA,MACnB;AAAA,IACF,GAAG,GAAG,GAAGH,KAAI,CAACG,OAAM;AAClB,MAAAA,OAAM,aAAaZ,GAAE,QAAQ,MAAIV,GAAE,iBAAiB,IAAE,MAAMK,GAAE,MAAM,MAAMiB,IAAGtB,GAAE,iBAAiB,KAAE;AAAA,IACpG,GAAGoB,KAAI,CAACE,OAAM;AACZ,MAAAjB,GAAE,MAAM,QAAQiB;AAAA,IAClB,GAAGT,KAAI,CAACS,OAAM;AACZ,MAAAjB,GAAE,MAAM,MAAMiB;AAAA,IAChB,GAAGP,KAAI,CAACO,IAAGa,OAAM;AACf,MAAA9B,GAAE,MAAM,aAAaiB,IAAGjB,GAAE,MAAM,aAAa8B;AAAA,IAC/C,GAAGlB,KAAI,CAACK,OAAM;AACZ,MAAAjB,GAAE,MAAM,YAAYiB;AAAA,IACtB,GAAGD,KAAI,CAACC,OAAM;AACZ,YAAMc,KAAId,GAAE,OAAO,MAAM,QAAQ,KAAK,EAAE;AACxC,gBAAEc,EAAC,EAAE,QAAQ,MAAM/B,GAAE,MAAM,MAAM+B;AAAA,IACnC;AACA,WAAO;AAAA,MACL,MAAMrC,GAAE;AAAA,MACR,CAACuB,OAAM;AACL,QAAAA,OAAMjB,GAAE,QAAQiB;AAAA,MAClB;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG;AAAA,MACD,MAAMjB,GAAE;AAAA,MACR,MAAM;AACJ,QAAAA,GAAE,MAAMA,GAAE,MAAM,KAAKA,GAAE,MAAMA,GAAE,MAAM,YAAY,GAAGa,GAAE,GAAGlB,GAAE,gBAAgBK,GAAE,KAAK,GAAGL,GAAE,UAAUK,GAAE,KAAK;AAAA,MAC1G;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG;AAAA,MACD,OAAOA;AAAA,MACP,kBAAkBK;AAAA,MAClB,QAAQI;AAAA,MACR,iBAAiBK;AAAA,MACjB,eAAeC;AAAA,MACf,aAAaP;AAAA,MACb,eAAeE;AAAA,MACf,eAAeE;AAAA,MACf,eAAeI;AAAA,MACf,cAAcT;AAAA,MACd,eAAeI;AAAA,IACjB;AAAA,EACF;AACF,CAAC;AAlED,IAkEIkE,MAAK,CAACnF,QAAO,YAAG,iBAAiB,GAAGA,KAAIA,GAAE,GAAG,WAAG,GAAGA;AAlEvD,IAkE2DoF,MAAK,EAAE,OAAO,oBAAoB;AAlE7F,IAkEgGC,MAAK,EAAE,OAAO,2BAA2B;AAlEzI,IAkE4IC,MAAK,EAAE,OAAO,4BAA4B;AAlEtL,IAkEyLC,MAAqBJ,IAAG,MAAsB,gBAAE,OAAO,EAAE,OAAO,OAAO,GAAG,MAAM,EAAE,CAAC;AAlE5Q,IAkE+QK,MAAK;AAAA,EAClRD;AACF;AACA,SAASE,IAAGzF,IAAGC,IAAGC,IAAGI,IAAGK,IAAGE,IAAG;AAC5B,QAAME,KAAI,iBAAE,SAAS,GAAGE,KAAI,iBAAE,OAAO,GAAGE,KAAI,iBAAE,KAAK,GAAGC,KAAI,iBAAE,WAAW,GAAGC,KAAI,iBAAE,OAAO,GAAGP,KAAI,iBAAE,SAAS,GAAGE,KAAI,iBAAE,SAAS;AAC3H,SAAO,UAAE,GAAG,mBAAE,OAAOoE,KAAI;AAAA,IACvB,gBAAE,OAAOC,KAAI;AAAA,MACX,gBAAE,OAAOC,KAAI;AAAA,QACXtF,GAAE,oBAAoB,UAAE,GAAG,mBAAE,QAAQ;AAAA,UACnC,KAAK;AAAA,UACL,OAAO,EAAE,QAAQ,UAAU;AAAA,UAC3B,SAASC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIiB,OAAMlB,GAAE,UAAUA,GAAE,OAAO,GAAGkB,EAAC;AAAA,QAC9D,GAAGsE,GAAE,KAAK,mBAAE,IAAI,IAAE;AAAA,MACpB,CAAC;AAAA,MACDxF,GAAE,mBAAmB,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,YAAEe,IAAG;AAAA,QAC1C,KAAK;AAAA,QACL,UAAUf,GAAE;AAAA,MACd,GAAG,MAAM,GAAG,CAAC,UAAU,CAAC;AAAA,MACxBA,GAAE,oBAAoB,UAAE,GAAG,YAAEiB,IAAG;AAAA,QAC9B,KAAK;AAAA,QACL,OAAOjB,GAAE,MAAM;AAAA,QACf,UAAUA,GAAE;AAAA,MACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MAC9CA,GAAE,oBAAoB,UAAE,GAAG,YAAEmB,IAAG;AAAA,QAC9B,KAAK;AAAA,QACL,OAAOnB,GAAE,MAAM;AAAA,QACf,UAAUA,GAAE;AAAA,MACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MAC9CA,GAAE,mBAAmB,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,YAAEoB,IAAG;AAAA,QAC1C,KAAK;AAAA,QACL,OAAOpB,GAAE,MAAM;AAAA,QACf,UAAUA,GAAE;AAAA,MACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,CAAC;AAAA,MACjCA,GAAE,eAAe,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,YAAEqB,IAAG;AAAA,QACtC,KAAK;AAAA,QACL,OAAOrB,GAAE,MAAM;AAAA,QACf,UAAUA,GAAE;AAAA,MACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,CAAC;AAAA,MACjC,YAAEc,IAAG;AAAA,QACH,OAAOd,GAAE,MAAM;AAAA,QACf,iBAAiBA,GAAE;AAAA,MACrB,GAAG,MAAM,GAAG,CAAC,SAAS,eAAe,CAAC;AAAA,MACtCA,GAAE,iBAAiB,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,YAAEgB,IAAG;AAAA,QACxC,KAAK;AAAA,QACL,OAAOhB,GAAE;AAAA,QACT,QAAQA,GAAE;AAAA,QACV,UAAUA,GAAE;AAAA,MACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,UAAU,CAAC;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAM0F,MAAqB,EAAER,KAAI,CAAC,CAAC,UAAUO,GAAE,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AACnF,IAAME,MAAK,gBAAE;AAAA,EACX,MAAM;AAAA,EACN,YAAY,EAAE,SAASV,KAAI,OAAOrD,KAAI,OAAOuB,KAAI,KAAKM,KAAI,SAASY,IAAG;AAAA,EACtE,OAAO;AAAA,IACL,OAAO,oBAAE,WAAWhE,EAAC;AAAA,IACrB,gBAAgB,oBAAE,KAAK,IAAI,KAAE;AAAA,IAC7B,cAAc,oBAAE,KAAK,IAAI,KAAE;AAAA,IAC3B,cAAc,oBAAE,KAAK,IAAI,KAAE;AAAA,EAC7B;AAAA,EACA,OAAO,CAAC,gBAAgB,QAAQ;AAAA,EAChC,MAAML,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,UAAMC,KAAIF,GAAE,SAAS,IAAIK,GAAE,GAAGC,KAAI,SAAE;AAAA,MAClC,OAAOJ;AAAA,MACP,KAAKA,GAAE,YAAY;AAAA,MACnB,KAAKA,GAAE,YAAY;AAAA,IACrB,CAAC,GAAGS,KAAI,SAAE,OAAO,EAAE,YAAYL,GAAE,IAAI,EAAE,GAAGO,KAAI,gBAAGJ,KAAI,CAAC,GAAG,CAAC,CAAC,GAAGM,KAAI,cAAE,MAAM;AACxE,UAAIf,GAAE;AACJ;AACF,YAAMgB,KAAIV,GAAE,MAAM,YAAY;AAC9B,UAAIO,GAAE,QAAQA,GAAE,MAAM,OAAO,CAACK,OAAM,CAAC,UAAE,OAAOA,IAAGF,EAAC,CAAC,GAAG,CAACH,GAAE,MAAM,SAASG,EAAC,GAAG;AAC1E,eAAOH,GAAE,MAAM,SAASH;AACtB,UAAAG,GAAE,MAAM,IAAI;AACd,QAAAA,GAAE,MAAM,QAAQG,EAAC;AAAA,MACnB;AAAA,IACF,GAAG,GAAG,GAAGC,KAAI,CAACD,OAAM;AAClB,MAAAV,GAAE,MAAM,QAAQU;AAAA,IAClB,GAAGG,KAAI,CAACH,OAAM;AACZ,MAAAV,GAAE,MAAM,MAAMU;AAAA,IAChB,GAAGI,KAAI,CAACJ,OAAM;AACZ,MAAAA,GAAE,QAAQ,WAAWV,GAAE,MAAM,MAAMU,GAAE,MAAMA,GAAE,UAAU,WAAWV,GAAE,MAAM,QAAQU,GAAE;AAAA,IACtF,GAAGK,KAAI,CAACL,IAAGE,OAAM;AACf,MAAAZ,GAAE,MAAM,aAAaU,IAAGV,GAAE,MAAM,aAAaY;AAAA,IAC/C,GAAGJ,KAAI,CAACE,OAAM;AACZ,MAAAA,OAAM,cAAcV,GAAE,MAAM,MAAMU;AAAA,IACpC;AACA,WAAO;AAAA,MACL,MAAMhB,GAAE;AAAA,MACR,CAACgB,OAAM;AACL,QAAAA,OAAMV,GAAE,QAAQU;AAAA,MAClB;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG;AAAA,MACD,MAAMV,GAAE;AAAA,MACR,MAAM;AACJ,QAAAA,GAAE,MAAMA,GAAE,MAAM,KAAKA,GAAE,MAAMA,GAAE,MAAM,YAAY,GAAGS,GAAE,GAAGd,GAAE,gBAAgBK,GAAE,KAAK,GAAGL,GAAE,UAAUK,GAAE,KAAK;AAAA,MAC1G;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG;AAAA,MACD,OAAOA;AAAA,MACP,cAAcK;AAAA,MACd,eAAeE;AAAA,MACf,eAAeI;AAAA,MACf,aAAaE;AAAA,MACb,eAAeE;AAAA,MACf,eAAeD;AAAA,MACf,iBAAiBN;AAAA,IACnB;AAAA,EACF;AACF,CAAC;AA1DD,IA0DI8E,MAAK,EAAE,OAAO,wBAAwB;AA1D1C,IA0D6CC,MAAK,EAAE,OAAO,6BAA6B;AA1DxF,IA0D2FC,MAAK,EAAE,OAAO,kBAAkB;AA1D3H,IA0D8HC,MAAK,EAAE,OAAO,iBAAiB;AAC7J,SAASC,IAAGhG,IAAGC,IAAGC,IAAGI,IAAGK,IAAGE,IAAG;AAC5B,QAAME,KAAI,iBAAE,OAAO,GAAGE,KAAI,iBAAE,KAAK,GAAGE,KAAI,iBAAE,OAAO,GAAGC,KAAI,iBAAE,SAAS,GAAGC,KAAI,iBAAE,SAAS;AACrF,SAAO,UAAE,GAAG,mBAAE,OAAOuE,KAAI;AAAA,IACvB,YAAE7E,IAAG;AAAA,MACH,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAOf,GAAE,MAAM;AAAA,MACf,UAAUA,GAAE;AAAA,IACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,CAAC;AAAA,IACjC,gBAAE,OAAO6F,KAAI;AAAA,MACX,gBAAE,OAAOC,KAAI;AAAA,QACX,gBAAE,OAAOC,KAAI;AAAA,UACX,YAAE9E,IAAG;AAAA,YACH,MAAM;AAAA,YACN,OAAOjB,GAAE,MAAM;AAAA,YACf,UAAUA,GAAE;AAAA,UACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,CAAC;AAAA,UACjCA,GAAE,eAAe,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,YAAEmB,IAAG;AAAA,YACtC,KAAK;AAAA,YACL,MAAM;AAAA,YACN,OAAOnB,GAAE,MAAM;AAAA,YACf,UAAUA,GAAE;AAAA,UACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,CAAC;AAAA,QACnC,CAAC;AAAA,MACH,CAAC;AAAA,MACD,YAAEoB,IAAG;AAAA,QACH,OAAOpB,GAAE,MAAM;AAAA,QACf,iBAAiBA,GAAE;AAAA,MACrB,GAAG,MAAM,GAAG,CAAC,SAAS,eAAe,CAAC;AAAA,MACtCA,GAAE,iBAAiB,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,YAAEqB,IAAG;AAAA,QACxC,KAAK;AAAA,QACL,OAAOrB,GAAE;AAAA,QACT,QAAQA,GAAE;AAAA,QACV,UAAUA,GAAE;AAAA,MACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,UAAU,CAAC;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC;AACH;AACA,IAAMiG,MAAqB,EAAEN,KAAI,CAAC,CAAC,UAAUK,GAAE,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AAAnF,IAAsFE,MAAK;AAA3F,IAAsHC,MAAK,CAACnG,IAAGC,OAAM;AACnI,QAAMC,KAAIF,GAAE,sBAAsB,GAAGM,KAAIJ,GAAE,OAAOA,GAAE,QAAQ,GAAGS,KAAIT,GAAE,MAAMA,GAAE,SAAS,GAAGW,KAAI,KAAK,IAAIP,KAAIL,GAAE,OAAO,GAAGc,KAAI,KAAK,IAAIJ,KAAIV,GAAE,OAAO,GAAGgB,KAAI,KAAK,KAAK,KAAK,IAAIJ,IAAG,CAAC,IAAI,KAAK,IAAIE,IAAG,CAAC,CAAC,GAAGI,KAAIJ,KAAIE,IAAGG,KAAI,KAAK,KAAKD,EAAC;AAC7N,MAAIE,KAAI,KAAK,MAAM,OAAO,KAAK,KAAKD,GAAE;AACtC,SAAOnB,GAAE,UAAUK,MAAKL,GAAE,UAAUU,OAAMU,KAAI,MAAMA,KAAIpB,GAAE,WAAWK,MAAKL,GAAE,UAAUU,OAAMU,KAAI,MAAMpB,GAAE,UAAUK,MAAKL,GAAE,WAAWU,OAAMU,KAAI,KAAKpB,GAAE,UAAUK,MAAKL,GAAE,UAAUU,OAAMU,KAAI,MAAMA,KAAIpB,GAAE,UAAUK,MAAKL,GAAE,WAAWU,OAAMU,KAAI,MAAMpB,GAAE,UAAUK,MAAKL,GAAE,UAAUU,OAAMU,KAAI,MAAMA,KAAIA;AACrS;AACA,IAAI+E,MAAK;AACT,IAAMC,MAAK,CAACrG,IAAGC,OAAM;AACnB,QAAMC,KAAI,SAASS,IAAG;AACpB,QAAIE;AACJ,KAACA,KAAIZ,GAAE,SAAS,QAAQY,GAAE,KAAKZ,IAAGU,EAAC;AAAA,EACrC,GAAGL,KAAI,SAASK,IAAG;AACjB,QAAIE;AACJ,aAAS,oBAAoB,aAAaX,IAAG,KAAE,GAAG,SAAS,oBAAoB,WAAWI,IAAG,KAAE,GAAG,SAAS,gBAAgB,MAAM,SAAS,cAAc,MAAM8F,MAAK,QAAKvF,KAAIZ,GAAE,QAAQ,QAAQY,GAAE,KAAKZ,IAAGU,EAAC;AAAA,EAC3M;AACA,EAAAX,MAAKA,GAAE,iBAAiB,aAAa,CAACW,OAAM;AAC1C,QAAIE;AACJ,IAAAuF,QAAO,SAAS,gBAAgB,MAAM,OAAI,SAAS,cAAc,MAAM,OAAI,SAAS,iBAAiB,aAAalG,IAAG,KAAE,GAAG,SAAS,iBAAiB,WAAWI,IAAG,KAAE,GAAG8F,MAAK,OAAKvF,KAAIZ,GAAE,UAAU,QAAQY,GAAE,KAAKZ,IAAGU,EAAC;AAAA,EACtN,CAAC;AACH;AACA,IAAM2F,MAAK;AAAA,EACT,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW,CAACtG,OAAMA,MAAK;AAAA,EACzB;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW,CAACA,OAAMA,MAAK;AAAA,EACzB;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AAnBA,IAmBGuG,MAAK,gBAAE;AAAA,EACR,MAAM;AAAA,EACN,OAAOD;AAAA,EACP,OAAO,CAAC,gBAAgB,QAAQ;AAAA,EAChC,MAAMtG,IAAG;AAAA,IACP,MAAMC;AAAA,EACR,GAAG;AACD,UAAMC,KAAI,IAAE,IAAI,GAAGI,KAAI,IAAE,CAAC;AAC1B,UAAE,MAAMN,GAAE,OAAO,CAACiB,OAAM;AACtB,MAAAX,GAAE,QAAQW;AAAA,IACZ,CAAC;AACD,UAAMN,KAAI,MAAM;AACd,UAAIM,KAAI,OAAOX,GAAE,KAAK;AACtB,YAAMW,EAAC,MAAMA,KAAIA,KAAI,OAAOA,KAAI,IAAIjB,GAAE,QAAQiB,IAAGX,GAAE,QAAQW,OAAM,MAAM,IAAIA,IAAGhB,GAAE,gBAAgBK,GAAE,KAAK,GAAGL,GAAE,UAAUK,GAAE,KAAK;AAAA,IAC/H,GAAGO,KAAI,SAAE,OAAO;AAAA,MACd,OAAOb,GAAE,OAAO;AAAA,MAChB,QAAQA,GAAE,OAAO;AAAA,MACjB,aAAaA,GAAE,cAAc;AAAA,MAC7B,aAAaA,GAAE;AAAA,MACf,WAAW,UAAUM,GAAE,KAAK;AAAA,IAC9B,EAAE,GAAGS,KAAI,CAACE,OAAM;AACd,MAAAf,GAAE,UAAUI,GAAE,QAAQ6F,IAAGjG,GAAE,OAAOe,EAAC,IAAI,KAAKN,GAAE;AAAA,IAChD;AACA,WAAO,UAAG,MAAM;AACd,YAAMM,KAAI;AAAA,QACR,MAAM,CAACE,OAAM;AACX,UAAAJ,GAAEI,EAAC;AAAA,QACL;AAAA,QACA,KAAK,CAACA,OAAM;AACV,UAAAJ,GAAEI,EAAC;AAAA,QACL;AAAA,MACF;AACA,MAAAjB,GAAE,SAASmG,IAAGnG,GAAE,OAAOe,EAAC;AAAA,IAC1B,CAAC,GAAG,MAAM,YAAE,OAAO;AAAA,MACjB,OAAO;AAAA,IACT,GAAG,CAAC,YAAE,OAAO;AAAA,MACX,OAAO;AAAA,MACP,KAAKf;AAAA,MACL,OAAOW,GAAE;AAAA,IACX,GAAG,IAAI,CAAC,CAAC;AAAA,EACX;AACF,CAAC;AACD,IAAM2F,MAAK,gBAAE;AAAA,EACX,MAAM;AAAA,EACN,YAAY,EAAE,OAAOD,KAAI,SAAStB,KAAI,OAAOrD,KAAI,SAASM,KAAI,OAAOiB,KAAI,KAAKM,KAAI,WAAWM,KAAI,SAASM,IAAG;AAAA,EAC7G,OAAO;AAAA,IACL,YAAY,oBAAE,WAAWhE,EAAC,EAAE;AAAA,IAC5B,UAAU,oBAAE,WAAWA,EAAC,EAAE;AAAA,IAC1B,gBAAgB,oBAAE,OAAO,IAAI,CAAC;AAAA,IAC9B,cAAc,oBAAE,OAAO,IAAI,GAAG;AAAA,IAC9B,OAAO,oBAAE,OAAO,IAAI,CAAC;AAAA,IACrB,MAAM,oBAAE,MAAM,CAAC,UAAU,QAAQ,CAAC,EAAE,IAAI,QAAQ;AAAA,IAChD,gBAAgB,oBAAE,KAAK,IAAI,KAAE;AAAA,IAC7B,cAAc,oBAAE,KAAK,IAAI,KAAE;AAAA,IAC3B,cAAc,oBAAE,KAAK,IAAI,KAAE;AAAA,IAC3B,YAAY,oBAAE,MAAM,CAAC,MAAM,QAAQ,CAAC,EAAE,IAAI,IAAI;AAAA,EAChD;AAAA,EACA,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAML,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,UAAMC,KAAI,SAAE;AAAA,MACV,aAAa;AAAA,MACb,YAAYF,GAAE;AAAA,MACd,UAAUA,GAAE;AAAA,MACZ,gBAAgBA,GAAE;AAAA,MAClB,cAAcA,GAAE;AAAA,MAChB,OAAOA,GAAE;AAAA,MACT,MAAMA,GAAE;AAAA;AAAA,MAER,gBAAgBA,GAAE,WAAW,YAAY;AAAA,MACzC,cAAcA,GAAE,SAAS,YAAY;AAAA,IACvC,CAAC,GAAGM,KAAI,OAAG4F,GAAE,GAAGvF,KAAI,IAAEX,GAAE,eAAe,QAAQ,GAAGa,KAAI,IAAE,GAAGE,KAAI,IAAE,GAAGE,KAAI,IAAE;AAC1E;AAAA,MACE,MAAM,CAACjB,GAAE,YAAYA,GAAE,UAAUA,GAAE,KAAK;AAAA,MACxC,CAACyG,OAAM;AACL,QAAAvG,GAAE,aAAauG,GAAE,CAAC,GAAGvG,GAAE,WAAWuG,GAAE,CAAC,GAAGvG,GAAE,QAAQuG,GAAE,CAAC;AAAA,MACvD;AAAA,IACF,GAAG;AAAA,MACD,MAAMzG,GAAE;AAAA,MACR,CAACyG,OAAM;AACL,QAAAvG,GAAE,OAAOuG;AAAA,MACX;AAAA,IACF;AACA,UAAMtF,KAAI,SAAE;AAAA,MACV,KAAK,MAAMjB,GAAE,cAAcA,GAAE,aAAaA,GAAE;AAAA,MAC5C,KAAK,CAACuG,OAAM;AACV,YAAIvG,GAAE,aAAa;AACjB,UAAAA,GAAE,aAAauG;AACf;AAAA,QACF;AACA,QAAAvG,GAAE,WAAWuG;AAAA,MACf;AAAA,IACF,CAAC,GAAGrF,KAAI,SAAE,MAAM;AACd,UAAIH,GAAE,SAASJ,GAAE,OAAO;AACtB,cAAM4F,KAAIvG,GAAE,iBAAiB,KAAKwG,KAAIzF,GAAE,MAAM,sBAAsB,GAAG0F,KAAI9F,GAAE,MAAM;AACnF,eAAO,KAAK,MAAM4F,MAAKC,GAAE,QAAQC,MAAKA,KAAI,CAAC;AAAA,MAC7C;AACA,aAAO;AAAA,IACT,CAAC,GAAGtF,KAAI,SAAE,MAAM;AACd,UAAIJ,GAAE,SAASF,GAAE,OAAO;AACtB,cAAM0F,KAAIvG,GAAE,eAAe,KAAKwG,KAAIzF,GAAE,MAAM,sBAAsB,GAAG0F,KAAI5F,GAAE,MAAM;AACjF,eAAO,KAAK,MAAM0F,MAAKC,GAAE,QAAQC,MAAKA,KAAI,CAAC;AAAA,MAC7C;AACA,aAAO;AAAA,IACT,CAAC,GAAG7F,KAAI,SAAE,MAAM;AACd,UAAI2F,KAAI,+BAA+BvG,GAAE,KAAK,QAAQA,GAAE,cAAc,IAAIA,GAAE,cAAc,MAAMA,GAAE,YAAY,IAAIA,GAAE,YAAY;AAChI,aAAOA,GAAE,SAAS,aAAauG,KAAI,uCAAuCvG,GAAE,cAAc,IAAIA,GAAE,cAAc,MAAMA,GAAE,YAAY,IAAIA,GAAE,YAAY,OAAOuG;AAAA,IAC7J,CAAC,GAAGzF,KAAI,CAACyF,OAAM;AACb,UAAIC;AACJ,UAAIxG,GAAE,cAAc,MAAIe,GAAE,SAASJ,GAAE,OAAO;AAC1C,cAAM8F,MAAKD,KAAIzF,GAAE,UAAU,OAAO,SAASyF,GAAE,sBAAsB;AACnE,YAAIE,KAAIH,GAAE,UAAUE,GAAE;AACtB,QAAAC,KAAI,KAAK,IAAI/F,GAAE,MAAM,cAAc,GAAG+F,EAAC,GAAGA,KAAI,KAAK,IAAIA,IAAGD,GAAE,QAAQ9F,GAAE,MAAM,cAAc,CAAC,GAAGX,GAAE,iBAAiB,KAAK;AAAA,WACnH0G,KAAI/F,GAAE,MAAM,cAAc,MAAM8F,GAAE,QAAQ9F,GAAE,MAAM,eAAe;AAAA,QACpE,GAAGZ,GAAE,yBAAyBC,GAAE,cAAc,GAAGD,GAAE,wBAAwBC,GAAE,cAAc;AAAA,MAC7F;AAAA,IACF,GAAGgB,KAAI,CAACuF,OAAM;AACZ,UAAIC;AACJ,UAAIxG,GAAE,cAAc,OAAIe,GAAE,SAASF,GAAE,OAAO;AAC1C,cAAM4F,MAAKD,KAAIzF,GAAE,UAAU,OAAO,SAASyF,GAAE,sBAAsB;AACnE,YAAIE,KAAIH,GAAE,UAAUE,GAAE;AACtB,QAAAC,KAAI,KAAK,IAAI7F,GAAE,MAAM,cAAc,GAAG6F,EAAC,GAAGA,KAAI,KAAK,IAAIA,IAAGD,GAAE,QAAQ5F,GAAE,MAAM,cAAc,CAAC,GAAGb,GAAE,eAAe,KAAK;AAAA,WACjH0G,KAAI7F,GAAE,MAAM,cAAc,MAAM4F,GAAE,QAAQ5F,GAAE,MAAM,eAAe;AAAA,QACpE,GAAGd,GAAE,uBAAuBC,GAAE,YAAY,GAAGD,GAAE,sBAAsBC,GAAE,YAAY;AAAA,MACrF;AAAA,IACF,GAAGoB,KAAI,CAACmF,OAAM;AACZ,YAAMC,KAAID,GAAE,QAAQE,KAAI,SAASD,GAAE,MAAM,QAAQ,KAAK,EAAE,CAAC;AACzD,YAAMC,EAAC,MAAMzG,GAAE,QAAQyG,KAAI,MAAM1G,GAAE,gBAAgBC,GAAE,KAAK,GAAGD,GAAE,eAAeC,GAAE,KAAK;AAAA,IACvF,GAAGqB,KAAI,CAACkF,OAAM;AACZ,MAAAvG,GAAE,QAAQuG,IAAGxG,GAAE,gBAAgBC,GAAE,KAAK,GAAGD,GAAE,eAAeC,GAAE,KAAK;AAAA,IACnE,GAAGkC,KAAI,CAACqE,OAAM;AACZ,MAAAA,OAAM,aAAa9F,GAAE,QAAQ,MAAIV,GAAE,iBAAiB,IAAE,MAAMkB,GAAE,MAAM,MAAMsF,IAAGxG,GAAE,iBAAiB,KAAE,IAAIyC,GAAE;AAAA,IAC1G,GAAGL,KAAI,CAACoE,OAAM;AACZ,MAAAtF,GAAE,MAAM,QAAQsF,IAAG/D,GAAE;AAAA,IACvB,GAAGJ,KAAI,CAACmE,OAAM;AACZ,MAAAtF,GAAE,MAAM,MAAMsF,IAAG/D,GAAE;AAAA,IACrB,GAAGH,KAAI,CAACkE,IAAGC,OAAM;AACf,MAAAvF,GAAE,MAAM,aAAasF,IAAGtF,GAAE,MAAM,aAAauF,IAAGhE,GAAE;AAAA,IACpD,GAAGF,KAAI,CAACiE,OAAM;AACZ,MAAAtF,GAAE,MAAM,YAAYsF,IAAG/D,GAAE;AAAA,IAC3B,GAAGD,KAAI,MAAM;AACX,MAAAC,GAAE;AAAA,IACJ,GAAGA,KAAI,MAAM;AACX,MAAAxC,GAAE,eAAeD,GAAE,qBAAqBC,GAAE,UAAU,GAAGD,GAAE,oBAAoBC,GAAE,UAAU,MAAMD,GAAE,mBAAmBC,GAAE,QAAQ,GAAGD,GAAE,kBAAkBC,GAAE,QAAQ;AAAA,IACjK,GAAGyC,KAAI,MAAM;AACX,MAAAhC,GAAE,QAAQ,OAAIV,GAAE,iBAAiB,KAAE;AAAA,IACrC,GAAG2C,KAAI,MAAM;AACX,MAAA1C,GAAE,OAAOA,GAAE,SAAS,WAAW,WAAW,UAAUD,GAAE,cAAcC,GAAE,IAAI;AAAA,IAC5E,GAAG,IAAI,gBAAGO,KAAI,CAAC,GAAG,CAAC,CAAC,GAAGoG,MAAK,cAAE,MAAM;AAClC,UAAI7G,GAAE;AACJ;AACF,YAAMyG,KAAItF,GAAE,MAAM,YAAY;AAC9B,UAAI,EAAE,QAAQ,EAAE,MAAM,OAAO,CAACuF,OAAM,CAAC,UAAE,OAAOA,IAAGD,EAAC,CAAC,GAAG,CAAC,EAAE,MAAM,SAASA,EAAC,GAAG;AAC1E,eAAO,EAAE,MAAM,SAAS/F;AACtB,YAAE,MAAM,IAAI;AACd,UAAE,MAAM,QAAQ+F,EAAC;AAAA,MACnB;AAAA,IACF,GAAG,GAAG;AACN,WAAO,aAAG,MAAM;AACd,MAAA1F,GAAE,SAASF,GAAE,UAAU,GAAG,iBAAiBE,GAAE,OAAO;AAAA,QAClD,MAAM,CAAC0F,OAAM;AACX,UAAAvF,GAAEuF,EAAC;AAAA,QACL;AAAA,QACA,KAAK,CAACA,OAAM;AACV,UAAAvF,GAAEuF,EAAC;AAAA,QACL;AAAA,MACF,CAAC,GAAG,GAAG,iBAAiB5F,GAAE,OAAO;AAAA,QAC/B,MAAM,CAAC4F,OAAM;AACX,UAAAzF,GAAEyF,EAAC;AAAA,QACL;AAAA,QACA,KAAK,CAACA,OAAM;AACV,UAAAzF,GAAEyF,EAAC;AAAA,QACL;AAAA,MACF,CAAC;AAAA,IACH,CAAC,GAAG;AAAA,MACF,MAAMvG,GAAE;AAAA,MACR,CAACuG,OAAM;AACL,QAAAvG,GAAE,iBAAiBuG,GAAE,YAAY;AAAA,MACnC;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG;AAAA,MACD,MAAMvG,GAAE;AAAA,MACR,CAACuG,OAAM;AACL,QAAAvG,GAAE,eAAeuG,GAAE,YAAY;AAAA,MACjC;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG;AAAA,MACD,MAAMtF,GAAE;AAAA,MACR,MAAM;AACJ,QAAA0F,IAAG;AAAA,MACL;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG;AAAA,MACD,kBAAkBhG;AAAA,MAClB,iBAAiBE;AAAA,MACjB,eAAeE;AAAA,MACf,OAAOf;AAAA,MACP,cAAciB;AAAA,MACd,mBAAmBC;AAAA,MACnB,iBAAiBC;AAAA,MACjB,YAAYP;AAAA,MACZ,kBAAkBH;AAAA,MAClB,cAAcW;AAAA,MACd,iBAAiBc;AAAA,MACjB,eAAeC;AAAA,MACf,aAAaC;AAAA,MACb,eAAeC;AAAA,MACf,eAAeC;AAAA,MACf,eAAe;AAAA,MACf,QAAQG;AAAA,MACR,gBAAgBpB;AAAA,MAChB,iBAAiBkB;AAAA,MACjB,cAAcG;AAAA,MACd,MAAMtC,MAAK,OAAO,SAASA,GAAE;AAAA,IAC/B;AAAA,EACF;AACF,CAAC;AA1LD,IA0LIwG,MAAK,CAAC9G,QAAO,YAAG,iBAAiB,GAAGA,KAAIA,GAAE,GAAG,WAAG,GAAGA;AA1LvD,IA0L2D+G,MAAK,EAAE,OAAO,qBAAqB;AA1L9F,IA0LiGC,MAAK,EAAE,OAAO,6BAA6B;AA1L5I,IA0L+IC,MAAK,EAAE,OAAO,qBAAqB;AA1LlL,IA0LqLC,MAAK,EAAE,OAAO,0BAA0B;AA1L7N,IA0LgOC,MAAK,EAAE,OAAO,yCAAyC;AA1LvR,IA0L0RC,MAAK,EAAE,OAAO,2BAA2B;AA1LnU,IA0LsUC,MAAK,CAAC,OAAO;AA1LnV,IA0LsVC,MAAK,EAAE,OAAO,yBAAyB;AA1L7X,IA0LgYC,MAAK,EAAE,OAAO,wBAAwB;AA1Lta,IA0LyaC,MAAK,EAAE,OAAO,2BAA2B;AA1Lld,IA0Lqd,KAAK;AAAA,EACxd,OAAO;AAAA,EACP,KAAK;AACP;AA7LA,IA6LGC,MAAK,EAAE,OAAO,4BAA4B;AA7L7C,IA6LgD,KAAK,EAAE,OAAO,+BAA+B;AA7L7F,IA6LgG,KAAK,CAAC,OAAO;AA7L7G,IA6LgHC,MAAqBZ,IAAG,MAAsB,gBAAE,QAAQ,EAAE,OAAO,2BAA2B,GAAG,MAAM,EAAE,CAAC;AA7LxN,IA6L2Na,MAAK;AAAA,EAC9ND;AACF;AA/LA,IA+LGE,MAAK,CAAC,OAAO;AA/LhB,IA+LmBC,MAAqBf,IAAG,MAAsB,gBAAE,QAAQ,EAAE,OAAO,2BAA2B,GAAG,MAAM,EAAE,CAAC;AA/L3H,IA+L8HgB,MAAK;AAAA,EACjID;AACF;AACA,SAASE,IAAG/H,IAAGC,IAAGC,IAAGI,IAAGK,IAAGE,IAAG;AAC5B,MAAIS,IAAGC;AACP,QAAMR,KAAI,iBAAE,OAAO,GAAGE,KAAI,iBAAE,OAAO,GAAGE,KAAI,iBAAE,KAAK,GAAGC,KAAI,iBAAE,SAAS,GAAGC,KAAI,iBAAE,WAAW,GAAGP,KAAI,iBAAE,OAAO,GAAGE,KAAI,iBAAE,SAAS,GAAGE,KAAI,iBAAE,SAAS;AAC3I,SAAO,UAAE,GAAG,mBAAE,OAAO6F,KAAI;AAAA,IACvB,gBAAE,OAAOC,KAAI;AAAA,MACX,gBAAE,OAAO,MAAM;AAAA,QACb,eAAG,gBAAE,OAAO;AAAA,UACV,OAAO;AAAA,UACP,OAAO,EAAE,QAAQ,UAAU;AAAA,UAC3B,SAAS/G,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAImC,OAAMpC,GAAE,UAAUA,GAAE,OAAO,GAAGoC,EAAC;AAAA,QAC9D,GAAG,MAAM,GAAG,GAAG;AAAA,UACb,CAAC,OAAIpC,GAAE,eAAe,QAAQA,GAAE,gBAAgB;AAAA,QAClD,CAAC;AAAA,MACH,CAAC;AAAA,MACD,gBAAE,OAAOiH,KAAI;AAAA,QACX,gBAAE,OAAOC,KAAI;AAAA,WACV,UAAE,GAAG,mBAAE,UAAG,MAAM,WAAG,CAAC,UAAU,QAAQ,GAAG,CAAC9E,OAAM,gBAAE,OAAO;AAAA,YACxD,OAAO,eAAE,CAAC,qBAAqB,EAAE,QAAQpC,GAAE,MAAM,SAASoC,GAAE,CAAC,CAAC;AAAA,YAC9D,KAAKA;AAAA,YACL,SAASnC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIoC,OAAMrC,GAAE,gBAAgBA,GAAE,aAAa,GAAGqC,EAAC;AAAA,UAC1E,GAAG,gBAAGrC,GAAE,OAAOA,GAAE,KAAKoC,EAAC,IAAIA,EAAC,GAAG,CAAC,CAAC,GAAG,EAAE;AAAA,QACxC,CAAC;AAAA,QACD,eAAG,gBAAE,OAAO+E,KAAI;AAAA,UACd,gBAAE,OAAOC,KAAI;AAAA,YACX,gBAAE,SAAS;AAAA,cACT,OAAOpH,GAAE,MAAM;AAAA,cACf,QAAQC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAImC,OAAMpC,GAAE,gBAAgBA,GAAE,aAAa,GAAGoC,EAAC;AAAA,YACzE,GAAG,MAAM,IAAIiF,GAAE;AAAA,YACf,gBAAG,MAAM;AAAA,UACX,CAAC;AAAA,UACD,gBAAE,OAAOC,KAAI;AAAA,YACX,gBAAE,OAAOC,KAAI;AAAA,cACX,YAAExG,IAAG;AAAA,gBACH,OAAOf,GAAE,MAAM;AAAA,gBACf,kBAAkBC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,CAACmC,OAAMpC,GAAE,MAAM,QAAQoC;AAAA,gBACzD,MAAM;AAAA,gBACN,UAAUpC,GAAE;AAAA,cACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,CAAC;AAAA,YACnC,CAAC;AAAA,UACH,CAAC;AAAA,QACH,GAAG,GAAG,GAAG;AAAA,UACP,CAAC,OAAIA,GAAE,MAAM,SAAS,QAAQ;AAAA,QAChC,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAE,OAAOwH,KAAI;AAAA,MACX,gBAAE,OAAO,IAAI;AAAA,QACX,gBAAE,OAAOC,KAAI;AAAA,UACX,gBAAE,OAAO;AAAA,YACP,OAAO;AAAA,YACP,OAAO,eAAEzH,GAAE,UAAU;AAAA,UACvB,GAAG,MAAM,CAAC;AAAA,UACV,gBAAE,OAAO,IAAI;AAAA,YACX,gBAAE,OAAO;AAAA,cACP,OAAO,eAAE,CAAC,qBAAqB;AAAA,gBAC7B,8BAA8BA,GAAE,MAAM;AAAA,cACxC,CAAC,CAAC;AAAA,cACF,KAAK;AAAA,cACL,QAAQsB,KAAItB,GAAE,SAAS,OAAO,SAASsB,GAAE;AAAA,cACzC,OAAO,eAAE,EAAE,MAAMtB,GAAE,oBAAoB,MAAM,iBAAiBA,GAAE,MAAM,eAAe,CAAC;AAAA,YACxF,GAAG2H,KAAI,IAAI,EAAE;AAAA,YACb,gBAAE,OAAO;AAAA,cACP,OAAO,eAAE,CAAC,qBAAqB;AAAA,gBAC7B,8BAA8B,CAAC3H,GAAE,MAAM;AAAA,cACzC,CAAC,CAAC;AAAA,cACF,KAAK;AAAA,cACL,QAAQuB,KAAIvB,GAAE,SAAS,OAAO,SAASuB,GAAE;AAAA,cACzC,OAAO,eAAE,EAAE,MAAMvB,GAAE,kBAAkB,MAAM,iBAAiBA,GAAE,MAAM,aAAa,CAAC;AAAA,YACpF,GAAG8H,KAAI,IAAIF,GAAE;AAAA,UACf,CAAC;AAAA,QACH,CAAC;AAAA,MACH,GAAG,GAAG;AAAA,IACR,CAAC;AAAA,IACD5H,GAAE,oBAAoB,UAAE,GAAG,YAAEiB,IAAG;AAAA,MAC9B,KAAK;AAAA,MACL,OAAOjB,GAAE;AAAA,MACT,UAAUA,GAAE;AAAA,IACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,IAC9CA,GAAE,oBAAoB,UAAE,GAAG,YAAEmB,IAAG;AAAA,MAC9B,KAAK;AAAA,MACL,OAAOnB,GAAE;AAAA,MACT,UAAUA,GAAE;AAAA,IACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,IAC9CA,GAAE,mBAAmB,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,YAAEoB,IAAG;AAAA,MAC1C,KAAK;AAAA,MACL,UAAUpB,GAAE;AAAA,IACd,GAAG,MAAM,GAAG,CAAC,UAAU,CAAC;AAAA,IACxBA,GAAE,mBAAmB,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,YAAEqB,IAAG;AAAA,MAC1C,KAAK;AAAA,MACL,OAAOrB,GAAE;AAAA,MACT,UAAUA,GAAE;AAAA,IACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,CAAC;AAAA,IACjCA,GAAE,eAAe,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,YAAEc,IAAG;AAAA,MACtC,KAAK;AAAA,MACL,OAAOd,GAAE;AAAA,MACT,UAAUA,GAAE;AAAA,IACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,CAAC;AAAA,IACjC,YAAEgB,IAAG;AAAA,MACH,OAAOhB,GAAE;AAAA,MACT,iBAAiBA,GAAE;AAAA,MACnB,UAAUA,GAAE;AAAA,IACd,GAAG,MAAM,GAAG,CAAC,SAAS,iBAAiB,UAAU,CAAC;AAAA,IAClDA,GAAE,iBAAiB,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,YAAEkB,IAAG;AAAA,MACxC,KAAK;AAAA,MACL,OAAOlB,GAAE;AAAA,MACT,QAAQA,GAAE;AAAA,MACV,UAAUA,GAAE;AAAA,IACd,GAAG,MAAM,GAAG,CAAC,SAAS,UAAU,UAAU,CAAC;AAAA,EAC7C,CAAC;AACH;AACA,IAAMgI,MAAqB,EAAExB,KAAI,CAAC,CAAC,UAAUuB,GAAE,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AACnF,IAAME,MAAK,gBAAE;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO,oBAAE,MAAM,CAAC,SAAS,OAAO,CAAC,EAAE,IAAI,OAAO;AAAA,IAC9C,SAAS,oBAAE,KAAK,IAAI,KAAE;AAAA,IACtB,WAAW,oBAAE,MAAM,CAAC,QAAQ,UAAU,CAAC,EAAE,IAAI,MAAM;AAAA,EACrD;AAAA,EACA,OAAO,CAAC,oBAAoB,QAAQ;AAAA,EACpC,MAAMjI,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,UAAMC,KAAI,SAAE;AAAA,MACV,WAAWF,GAAE;AAAA,IACf,CAAC,GAAGM,KAAI,OAAG4F,GAAE,GAAGvF,KAAI,CAACE,OAAM;AACzB,MAAAX,GAAE,YAAYW,IAAGZ,GAAE,oBAAoBY,EAAC,GAAGZ,GAAE,UAAUY,EAAC;AAAA,IAC1D;AACA,WAAO;AAAA,MACL,MAAMb,GAAE;AAAA,MACR,CAACa,OAAM;AACL,QAAAX,GAAE,YAAYW;AAAA,MAChB;AAAA,IACF,GAAG,EAAE,OAAOX,IAAG,mBAAmBS,IAAG,MAAML,MAAK,OAAO,SAASA,GAAE,KAAK;AAAA,EACzE;AACF,CAAC;AArBD,IAqBI,KAAK,EAAE,OAAO,4BAA4B;AArB9C,IAqBiD4H,MAAK;AAAA,EACpD,KAAK;AAAA,EACL,OAAO;AACT;AAxBA,IAwBGC,MAAK,EAAE,OAAO,8BAA8B;AAxB/C,IAwBkD,KAAK,EAAE,OAAO,kBAAkB;AAxBlF,IAwBqFC,MAAK,EAAE,OAAO,kBAAkB;AACrH,SAASC,IAAGrI,IAAGC,IAAGC,IAAGI,IAAGK,IAAGE,IAAG;AAC5B,MAAIE,IAAGE;AACP,SAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,IACnB,OAAO,eAAE,CAAC,kBAAkBjB,GAAE,KAAK,CAAC;AAAA,EACtC,GAAG;AAAA,IACD,gBAAE,OAAO,IAAI;AAAA,MACXA,GAAE,WAAW,UAAE,GAAG,mBAAE,OAAOkI,KAAI;AAAA,QAC7B,gBAAE,OAAOC,KAAI;AAAA,UACX,gBAAE,OAAO;AAAA,YACP,OAAO,eAAE;AAAA,cACP;AAAA,cACA;AAAA,gBACE,iBAAiBnI,GAAE,MAAM,cAAc;AAAA,cACzC;AAAA,YACF,CAAC;AAAA,YACD,SAASC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,CAACkB,OAAMnB,GAAE,kBAAkB,MAAM;AAAA,UAC5D,GAAG;AAAA,YACD,gBAAE,UAAU,MAAM;AAAA,cAChB,gBAAE,OAAO,IAAI,iBAAIe,KAAIf,GAAE,SAAS,OAAO,SAASe,GAAE,IAAI,GAAG,CAAC;AAAA,YAC5D,CAAC;AAAA,UACH,GAAG,CAAC;AAAA,UACJ,gBAAE,OAAO;AAAA,YACP,OAAO,eAAE;AAAA,cACP;AAAA,cACA;AAAA,gBACE,iBAAiBf,GAAE,MAAM,cAAc;AAAA,cACzC;AAAA,YACF,CAAC;AAAA,YACD,SAASC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,CAACkB,OAAMnB,GAAE,kBAAkB,UAAU;AAAA,UAChE,GAAG;AAAA,YACD,gBAAE,UAAU,MAAM;AAAA,cAChB,gBAAE,OAAOoI,KAAI,iBAAInH,KAAIjB,GAAE,SAAS,OAAO,SAASiB,GAAE,QAAQ,GAAG,CAAC;AAAA,YAChE,CAAC;AAAA,UACH,GAAG,CAAC;AAAA,UACJ,gBAAE,OAAO;AAAA,YACP,OAAO;AAAA,YACP,OAAO,eAAE;AAAA,cACP,OAAO;AAAA,cACP,MAAM,QAAQjB,GAAE,MAAM,cAAc,aAAa,KAAK,CAAC;AAAA,YACzD,CAAC;AAAA,UACH,GAAG,MAAM,CAAC;AAAA,QACZ,CAAC;AAAA,MACH,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MACd,WAAGA,GAAE,QAAQ,WAAW,CAAC,GAAG,QAAQ,IAAE;AAAA,IACxC,CAAC;AAAA,EACH,GAAG,CAAC;AACN;AACA,IAAMsI,MAAqB,EAAEL,KAAI,CAAC,CAAC,UAAUI,GAAE,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AAAnF,IAAsF,KAAK;AAAA,EACzF,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AACV;AAPA,IAOGE,MAAK;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AACV;AAdA,IAcGC,MAAK;AAAA,EACN,IAAI;AAAA,EACJ,SAASD;AACX;AACA,IAAM,KAAK;AAAA,EACT,UAAU,oBAAE,KAAK,IAAI,KAAE;AAAA,EACvB,YAAY,oBAAE,MAAM,CAAC,MAAM,QAAQ,CAAC,EAAE,IAAI,IAAI;AAAA,EAC9C,OAAO,oBAAE,MAAM,CAAC,UAAU,QAAQ,CAAC,EAAE,IAAI,QAAQ;AAAA,EACjD,WAAW;AAAA,IACT,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,SAAS;AAAA,EACX;AAAA,EACA,eAAe,oBAAE,OAAO;AAAA,IACtB;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,cAAc,oBAAE,KAAK,IAAI,KAAE;AAAA,EAC3B,gBAAgB,oBAAE,KAAK,IAAI,KAAE;AAAA,EAC7B,cAAc,oBAAE,KAAK,IAAI,KAAE;AAAA,EAC3B,SAAS,oBAAE,MAAM,CAAC,QAAQ,YAAY,MAAM,CAAC,EAAE,IAAI,MAAM;AAAA,EACzD,WAAW,oBAAE,MAAM,CAAC,QAAQ,UAAU,CAAC,EAAE,IAAI,MAAM;AAAA,EACnD,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,QAAQ,oBAAE,OAAO,IAAI,IAAI;AAAA,EACzB,iBAAiB;AAAA,IACf,MAAM,CAAC,QAAQ,WAAW;AAAA,IAC1B,SAAS;AAAA,EACX;AAAA,EACA,UAAU,oBAAE,OAAO,IAAI,GAAG;AAAA,EAC1B,OAAO,oBAAE,MAAM,CAAC,SAAS,OAAO,CAAC,EAAE,IAAI,OAAO;AAAA,EAC9C,WAAW,oBAAE,KAAK,IAAI,KAAE;AAAA,EACxB,cAAc,oBAAE,KAAK,IAAI,KAAE;AAC7B;AAjCA,IAiCG,KAAK,gBAAE;AAAA,EACR,MAAM;AAAA,EACN,YAAY,EAAE,eAAe7C,KAAI,mBAAmBO,KAAI,qBAAqB+B,KAAI,eAAeM,IAAG;AAAA,EACnG,cAAc;AAAA,EACd,OAAO;AAAA,EACP,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAMtI,IAAG,EAAE,MAAMC,GAAE,GAAG;AACpB,YAAGiG,KAAI;AAAA,MACL,MAAM,SAAE,MAAMsC,IAAGxI,GAAE,QAAQ,OAAO,CAAC;AAAA,IACrC,CAAC;AACD,UAAME,KAAI,CAAC,CAAC,SAAG,EAAE,OAAOI,KAAI,SAAE;AAAA,MAC5B,WAAWN,GAAE,aAAa;AAAA,MAC1B,WAAWA,GAAE,YAAY,aAAa,aAAaA,GAAE;AAAA;AAAA,MAErD,eAAe;AAAA,IACjB,CAAC,GAAGW,KAAI,IAAIN,GAAE,MAAM,GAAGQ,KAAI,IAAIR,GAAE,MAAM,GAAGU,KAAI,IAAIV,GAAEC,GAAE,SAAS,GAAGW,KAAI,SAAE;AAAA,MACtE,YAAYN;AAAA,MACZ,UAAUE;AAAA,MACV,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,OAAO;AAAA,MACP,MAAM;AAAA,MACN,eAAeb,GAAE;AAAA,IACnB,CAAC,GAAGmB,KAAI,IAAEJ,EAAC,GAAGK,KAAI,IAAEpB,GAAE,YAAY,GAAGqB,KAAI,IAAE,IAAI,GAAGP,KAAI,IAAE,IAAI;AAC5D,QAAIE,KAAI;AACR,UAAME,KAAI,SAAE,OAAO;AAAA,MACjB,YAAYZ,GAAE,cAAc,aAAa,UAAEA,GAAE,SAAS,EAAE,YAAY,IAAIW,GAAE;AAAA,IAC5E,EAAE,GAAGK,KAAI,SAAE,MAAMhB,GAAE,cAAc,aAAa0H,IAAG,OAAOhI,GAAE,eAAe,OAAO0F,IAAG,OAAOO,IAAG,IAAI,GAAG1E,KAAI,CAACkF,OAAM;AAC7G,MAAAnG,GAAE,gBAAgBmG;AAAA,IACpB,GAAGrE,KAAI,SAAE,MAAM;AACb,YAAMqE,KAAI;AAAA,QACR,cAAczG,GAAE;AAAA,QAChB,gBAAgBA,GAAE;AAAA,QAClB,cAAcA,GAAE;AAAA,QAChB,YAAYA,GAAE;AAAA,MAChB;AACA,aAAOM,GAAE,cAAc,aAAa;AAAA,QAClC,GAAGmG;AAAA,QACH,YAAYxF,GAAE;AAAA,QACd,UAAUA,GAAE;AAAA,QACZ,OAAOA,GAAE;AAAA,QACT,MAAMA,GAAE;AAAA,QACR,gBAAgBA,GAAE;AAAA,QAClB,cAAcA,GAAE;AAAA,QAChB,oBAAoB,CAACyF,OAAM;AACzB,UAAAzF,GAAE,aAAayF,IAAGjE,GAAE;AAAA,QACtB;AAAA,QACA,kBAAkB,CAACiE,OAAM;AACvB,UAAAzF,GAAE,WAAWyF,IAAGjE,GAAE;AAAA,QACpB;AAAA,QACA,wBAAwB,CAACiE,OAAM;AAC7B,UAAAzF,GAAE,iBAAiByF,IAAGjE,GAAE;AAAA,QAC1B;AAAA,QACA,sBAAsB,CAACiE,OAAM;AAC3B,UAAAzF,GAAE,eAAeyF,IAAGjE,GAAE;AAAA,QACxB;AAAA,QACA,eAAe,CAACiE,OAAM;AACpB,UAAAzF,GAAE,QAAQyF,IAAGjE,GAAE;AAAA,QACjB;AAAA,QACA,cAAc,CAACiE,OAAM;AACnB,UAAAzF,GAAE,OAAOyF,IAAGjE,GAAE;AAAA,QAChB;AAAA,QACA,iBAAiBlB;AAAA,MACnB,IAAI;AAAA,QACF,GAAGkF;AAAA,QACH,cAAczG,GAAE;AAAA,QAChB,gBAAgBA,GAAE;AAAA,QAClB,cAAcA,GAAE;AAAA,QAChB,OAAOmB,GAAE;AAAA,QACT,UAAUyB;AAAA,QACV,iBAAiBrB;AAAA,MACnB;AAAA,IACF,CAAC,GAAGc,KAAI,MAAM;AACZ,MAAAjB,GAAE,QAAQ,MAAIJ,KAAIA,GAAE,OAAO,IAAI2B,GAAE;AAAA,IACnC,GAAGL,KAAI,MAAM;AACX,MAAAlB,GAAE,QAAQ;AAAA,IACZ,GAAGmB,KAAI,cAAE,MAAM;AACb,OAACvC,GAAE,YAAYA,GAAE,aAAasC,GAAE;AAAA,IAClC,GAAG,GAAG;AACN,mBAAGxB,IAAG,MAAM;AACV,MAAAwB,GAAE;AAAA,IACJ,CAAC;AACD,UAAME,KAAI,MAAM;AACd,UAAIiE,IAAGC,IAAGC,IAAGC;AACb,UAAI;AACF,cAAM,CAAC6B,EAAC,QAAI,uBAAAC,OAAGzH,GAAE,aAAa;AAC9B,YAAIwH,MAAKA,GAAE,KAAK,SAAS,UAAU,KAAKA,GAAE,WAAW,UAAU,GAAG;AAChE,gBAAME,MAAKF,GAAE,WAAW,CAAC,GAAGG,MAAKH,GAAE,WAAW,CAAC;AAC/C,UAAAxH,GAAE,iBAAiB,QAAQwF,KAAIkC,IAAG,WAAW,OAAO,SAASlC,GAAE,KAAK,KAAK,GAAGxF,GAAE,eAAe,QAAQyF,KAAIkC,IAAG,WAAW,OAAO,SAASlC,GAAE,KAAK,KAAK,GAAG+B,GAAE,SAAS,uBAAuB9B,KAAI8B,GAAE,gBAAgB,OAAO,SAAS9B,GAAE,UAAU,cAAc1F,GAAE,QAAQ,QAAQ2F,KAAI6B,GAAE,gBAAgB,OAAO,SAAS7B,GAAE,KAAK,KAAK,IAAI3F,GAAE,OAAOwH,GAAE,KAAK,MAAM,GAAG,EAAE,CAAC;AAC5V,gBAAM,CAACI,KAAIC,KAAIC,KAAIC,GAAE,IAAIL,IAAG,OAAO,CAACM,KAAIC,KAAIC,KAAIC,GAAE,IAAIR,IAAG;AACzD,UAAA3H,GAAE,aAAa,IAAIZ,GAAE;AAAA,YACnB,GAAG,OAAOwI,GAAE;AAAA,YACZ,GAAG,OAAOC,GAAE;AAAA,YACZ,GAAG,OAAOC,GAAE;AAAA,YACZ,GAAG,OAAOC,GAAE;AAAA,UACd,CAAC,GAAG/H,GAAE,WAAW,IAAIZ,GAAE;AAAA,YACrB,GAAG,OAAO4I,GAAE;AAAA,YACZ,GAAG,OAAOC,GAAE;AAAA,YACZ,GAAG,OAAOC,GAAE;AAAA,YACZ,GAAG,OAAOC,GAAE;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF,SAASX,IAAG;AACV,gBAAQ,IAAI,kBAAkBA,EAAC,EAAE;AAAA,MACnC;AAAA,IACF,GAAGhG,KAAI,cAAE,MAAM;AACb,YAAMgE,KAAI/D,GAAE;AACZ,UAAI;AACF,QAAAzB,GAAE,oBAAgB,uBAAAoI,WAAG5C,EAAC,GAAGxG,GAAE,wBAAwBgB,GAAE,aAAa,GAAGhB,GAAE,uBAAuBgB,GAAE,aAAa;AAAA,MAC/G,SAASyF,IAAG;AACV,gBAAQ,IAAIA,EAAC;AAAA,MACf;AAAA,IACF,GAAG1G,GAAE,QAAQ,GAAG0C,KAAI,MAAM;AACxB,YAAM+D,KAAI,CAAC,GAAGC,KAAIzF,GAAE,WAAW,IAAI,IAAI,CAACwH,OAAMA,GAAE,SAAS,CAAC,GAAG9B,KAAI1F,GAAE,SAAS,IAAI,IAAI,CAACwH,OAAMA,GAAE,SAAS,CAAC,GAAG7B,KAAI;AAAA,QAC5G;AAAA,UACE,MAAM;AAAA,UACN,OAAO,CAACF,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AAAA,UAC9B,QAAQ,EAAE,OAAOzF,GAAE,iBAAiB,IAAI,MAAM,IAAI;AAAA,QACpD;AAAA,QACA;AAAA,UACE,MAAM;AAAA,UACN,OAAO,CAAC0F,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AAAA,UAC9B,QAAQ,EAAE,OAAO1F,GAAE,eAAe,IAAI,MAAM,IAAI;AAAA,QAClD;AAAA,MACF;AACA,aAAOA,GAAE,SAAS,WAAWwF,GAAE,KAAK;AAAA,QAClC,MAAM;AAAA,QACN,aAAa,EAAE,MAAM,WAAW,OAAOxF,GAAE,QAAQ,GAAG;AAAA,QACpD,YAAY2F;AAAA,MACd,CAAC,IAAI3F,GAAE,SAAS,YAAYwF,GAAE,KAAK;AAAA,QACjC,MAAM;AAAA,QACN,aAAa,CAAC,EAAE,MAAM,SAAS,OAAO,SAAS,CAAC;AAAA,QAChD,YAAYG;AAAA,MACd,CAAC,GAAGH;AAAA,IACN,GAAG9D,KAAI,MAAM;AACX,MAAAtB,GAAE,SAASP,GAAE,UAAUE,KAAIsI,cAAGjI,GAAE,OAAOP,GAAE,OAAO;AAAA,QAC9C,WAAW;AAAA,QACX,WAAW;AAAA,UACT;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,cACP,QAAQ,CAAC,GAAG,CAAC;AAAA,YACf;AAAA,UACF;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,SAAS;AAAA,cACP,uBAAuB,CAAC,OAAO,UAAU,QAAQ,OAAO;AAAA,cACxD,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,GAAG8B,KAAI,CAAC6D,OAAM;AACZ,MAAAtF,GAAE,QAAQsF,IAAGnG,GAAE,YAAYmG,GAAE,SAASzG,GAAE,MAAM,GAAG,EAAE;AAAA,IACrD,GAAG,IAAI,cAAE,MAAM;AACb,MAAAC,GAAE,oBAAoBK,GAAE,SAAS,GAAGL,GAAE,mBAAmBK,GAAE,SAAS;AAAA,IACtE,GAAGN,GAAE,QAAQ,GAAG6G,MAAK,CAACJ,OAAM;AAC1B,MAAAnG,GAAE,YAAYmG,IAAGxG,GAAE,oBAAoBwG,EAAC,GAAGxG,GAAE,mBAAmBwG,EAAC;AAAA,IACnE;AACA,WAAO,aAAG,MAAM;AACd,MAAAjE,GAAE,GAAGxB,MAAK2B,GAAE;AAAA,IACd,CAAC,GAAG;AAAA,MACF,MAAM3C,GAAE;AAAA,MACR,CAACyG,OAAM;AACL,QAAAA,MAAKxF,GAAE,kBAAkBA,GAAE,gBAAgBwF;AAAA,MAC7C;AAAA,IACF,GAAG;AAAA,MACD,MAAMxF,GAAE;AAAA,MACR,MAAM;AACJ,QAAAuB,GAAE;AAAA,MACJ;AAAA,IACF,GAAG;AAAA,MACD,MAAMxC,GAAE;AAAA,MACR,CAACyG,OAAM;AACL,QAAAnG,GAAE,YAAYmG;AAAA,MAChB;AAAA,IACF,GAAG;AAAA,MACD,MAAMzG,GAAE;AAAA,MACR,CAACyG,OAAM;AACL,QAAAnG,GAAE,cAAc,cAAcmG,OAAM,aAAanG,GAAE,YAAY,aAAaA,GAAE,YAAY;AAAA,MAC5F;AAAA,IACF,GAAG;AAAA,MACD,MAAMN,GAAE;AAAA,MACR,CAACyG,OAAM;AACL,kBAAE,OAAOA,IAAGnG,GAAE,SAAS,MAAMA,GAAE,YAAYmG,IAAGtF,GAAE,QAAQ,IAAId,GAAEoG,EAAC;AAAA,MACjE;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG;AAAA,MACD,cAAcpF;AAAA,MACd,WAAWP;AAAA,MACX,YAAYM;AAAA,MACZ,eAAeD;AAAA,MACf,iBAAiBD;AAAA,MACjB,kBAAkBI;AAAA,MAClB,aAAac;AAAA,MACb,OAAO9B;AAAA,MACP,UAAUJ;AAAA,MACV,eAAe0C;AAAA,MACf,cAAcP;AAAA,MACd,mBAAmBwE;AAAA,MACnB,aAAatE;AAAA,IACf;AAAA,EACF;AACF,CAAC;AApPD,IAoPIgH,MAAK;AAAA,EACP,KAAK;AAAA,EACL,OAAO;AACT;AAvPA,IAuPGC,MAAK;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AACT;AACA,SAASC,IAAGzJ,IAAGC,IAAGC,IAAGI,IAAGK,IAAGE,IAAG;AAC5B,QAAME,KAAI,iBAAE,eAAe;AAC3B,SAAO,UAAE,GAAG,mBAAE,UAAG,MAAM;AAAA,IACrBf,GAAE,YAAY,UAAE,GAAG,YAAEe,IAAG;AAAA,MACtB,KAAK;AAAA,MACL,cAAcf,GAAE,MAAM;AAAA,MACtB,sBAAsBC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,CAACgB,OAAMjB,GAAE,MAAM,YAAYiB;AAAA,MACjE,YAAYjB,GAAE,YAAY;AAAA,MAC1B,OAAO,eAAE,EAAE,QAAQA,GAAE,OAAO,CAAC;AAAA,MAC7B,OAAOA,GAAE;AAAA,MACT,UAAUA,GAAE;AAAA,IACd,GAAG;AAAA,MACD,SAAS,QAAG,MAAM;AAAA,SACf,UAAE,GAAG,YAAE,wBAAGA,GAAE,gBAAgB,GAAG,WAAG,EAAE,KAAKA,GAAE,iBAAiB,GAAGA,GAAE,WAAW,GAAG,MAAM,EAAE;AAAA,QACxFA,GAAE,YAAY,UAAE,GAAG,mBAAE,OAAOuJ,KAAI;AAAA,UAC9B,WAAGvJ,GAAE,QAAQ,SAAS,CAAC,GAAG,QAAQ,IAAE;AAAA,QACtC,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,MAChB,CAAC;AAAA,MACD,GAAG;AAAA,IACL,GAAG,GAAG,CAAC,cAAc,YAAY,SAAS,SAAS,UAAU,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,IAC3EA,GAAE,WAAW,mBAAE,IAAI,IAAE,KAAK,UAAE,GAAG,mBAAE,UAAG,EAAE,KAAK,EAAE,GAAG;AAAA,MAC9C,gBAAE,OAAO;AAAA,QACP,OAAO,eAAE,CAAC,6BAA6B,EAAE,OAAOA,GAAE,UAAU,SAAS,CAAC,CAAC;AAAA,QACvE,KAAK;AAAA,MACP,GAAG;AAAA,QACD,gBAAE,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO,eAAEA,GAAE,eAAe;AAAA,UAC1B,SAASC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIgB,OAAMjB,GAAE,gBAAgBA,GAAE,aAAa,GAAGiB,EAAC;AAAA,QAC1E,GAAG,MAAM,CAAC;AAAA,MACZ,GAAG,CAAC;AAAA,OACH,UAAE,GAAG,YAAE,UAAI,EAAE,IAAIjB,GAAE,gBAAgB,GAAG;AAAA,QACrC,eAAG,gBAAE,OAAO;AAAA,UACV,KAAK;AAAA,UACL,OAAO,eAAE,EAAE,QAAQA,GAAE,OAAO,CAAC;AAAA,UAC7B,cAAcC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,IAAIgB,OAAMjB,GAAE,eAAeA,GAAE,YAAY,GAAGiB,EAAC;AAAA,QAC7E,GAAG;AAAA,UACDjB,GAAE,cAAc,UAAE,GAAG,YAAEe,IAAG;AAAA,YACxB,KAAK;AAAA,YACL,YAAYf,GAAE,YAAY,UAAU,CAACA,GAAE,MAAM;AAAA,YAC7C,OAAOA,GAAE;AAAA,YACT,cAAcA,GAAE,MAAM;AAAA,YACtB,sBAAsBC,GAAE,CAAC,MAAMA,GAAE,CAAC,IAAI,CAACgB,OAAMjB,GAAE,MAAM,YAAYiB;AAAA,YACjE,UAAUjB,GAAE;AAAA,UACd,GAAG;AAAA,YACD,SAAS,QAAG,MAAM;AAAA,eACf,UAAE,GAAG,YAAE,wBAAGA,GAAE,gBAAgB,GAAG,WAAG,EAAE,KAAKA,GAAE,iBAAiB,GAAGA,GAAE,WAAW,GAAG,MAAM,EAAE;AAAA,cACxFA,GAAE,YAAY,UAAE,GAAG,mBAAE,OAAOwJ,KAAI;AAAA,gBAC9B,WAAGxJ,GAAE,QAAQ,SAAS,CAAC,GAAG,QAAQ,IAAE;AAAA,cACtC,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,YAChB,CAAC;AAAA,YACD,GAAG;AAAA,UACL,GAAG,GAAG,CAAC,YAAY,SAAS,cAAc,UAAU,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,QACpE,GAAG,EAAE,GAAG;AAAA,UACN,CAAC,OAAIA,GAAE,UAAU;AAAA,QACnB,CAAC;AAAA,MACH,GAAG,GAAG,CAAC,IAAI,CAAC;AAAA,IACd,GAAG,EAAE;AAAA,EACP,GAAG,EAAE;AACP;AACA,IAAM0J,MAAqB,EAAE,IAAI,CAAC,CAAC,UAAUD,GAAE,GAAG,CAAC,aAAa,iBAAiB,CAAC,CAAC;AAAnF,IAAsFE,MAAK;AAAA,EACzF,SAAS,CAAC3J,OAAM;AACd,IAAAA,GAAE,UAAU0J,IAAG,MAAMA,GAAE,GAAG1J,GAAE,UAAU,SAAS0J,IAAG,MAAMA,GAAE;AAAA,EAC5D;AACF;", "names": ["at", "i", "obj", "R", "G", "B", "h", "s", "v", "l", "fn", "i", "a", "r", "g", "b", "max", "min", "d", "p", "q", "t", "f", "o", "n", "c", "name", "style", "x", "y", "window", "min", "max", "v", "toPaddingObject", "popperOffsets", "min", "max", "offset", "effect", "x", "y", "popper", "effect", "window", "hash", "x", "y", "x", "y", "clippingParents", "reference", "popperOffsets", "offset", "placements", "placement", "a", "b", "flip", "placements", "placement", "i", "_loop", "_i", "checks", "offset", "x", "y", "popperOffsets", "offset", "min", "max", "fn", "merged", "defaultModifiers", "createPopper", "reference", "popper", "options", "m", "fn", "state", "effect", "noopFn", "createPopper", "defaultModifiers", "createPopper", "t", "e", "n", "r", "q", "o", "i", "u", "a", "c", "l", "f", "s", "d", "v", "p", "g", "h", "y", "m", "fn", "t", "t", "t", "e", "n", "r", "o", "i", "u", "a", "c", "l", "f", "s", "d", "v", "p", "g", "h", "y", "m", "S", "x", "b", "E", "w", "O", "T", "Wo", "zo", "$o", "<PERSON>", "t", "e", "n", "qe", "Ye", "e", "t", "o", "W", "P", "A", "n", "Ae", "ue", "fe", "Ce", "i", "lt", "l", "g", "a", "d", "r", "m", "c", "k", "p", "b", "h", "st", "it", "ct", "ut", "ve", "dt", "gt", "ht", "pt", "ft", "<PERSON>", "Ct", "y", "f", "w", "S", "F", "E", "L", "U", "J", "be", "vt", "bt", "yt", "_t", "mt", "ye", "St", "kt", "$t", "wt", "Bt", "_e", "Ht", "Rt", "At", "Pt", "Vt", "Le", "Mt", "Et", "It", "Kt", "Lt", "me", "Nt", "Wt", "Dt", "Tt", "<PERSON>t", "zt", "Gt", "Ft", "Xt", "qt", "Yt", "Se", "Ut", "jt", "Zt", "Jt", "Qt", "xt", "eo", "to", "Pe", "oo", "no", "ao", "ro", "lo", "so", "Ve", "ke", "io", "de", "co", "uo", "go", "ho", "s", "_", "H", "N", "ce", "Ne", "po", "fo", "Co", "vo", "bo", "yo", "_o", "mo", "So", "ko", "wo", "Ro", "Ao", "Po", "Vo", "Mo", "Eo", "Me", "Io", "Lo", "No", "Do", "To", "Oo", "Go", "Fo", "z", "at", "$e", "we", "We", "De", "Te", "Oe", "ze", "Ge", "Fe", "Xe", "nt", "createPopper", "Yo", "Uo", "jo", "re", "rn"]}