const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '123456',
};

async function testConnection() {
  let connection;
  
  try {
    console.log('🔍 测试数据库连接...');
    console.log(`📍 连接信息: ${dbConfig.user}@${dbConfig.host}:${dbConfig.port}`);
    
    // 尝试连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功！');
    
    // 获取MySQL版本信息
    const [rows] = await connection.execute('SELECT VERSION() as version');
    console.log(`📊 MySQL版本: ${rows[0].version}`);
    
    // 检查system_manage数据库是否存在
    const [databases] = await connection.execute('SHOW DATABASES LIKE "system_manage"');
    if (databases.length > 0) {
      console.log('✅ system_manage数据库已存在');
      
      // 切换到system_manage数据库
      await connection.execute('USE system_manage');
      
      // 检查表数量
      const [tables] = await connection.execute('SHOW TABLES');
      console.log(`📋 数据表数量: ${tables.length}`);
      
      if (tables.length > 0) {
        console.log('📝 数据表列表:');
        tables.forEach((table, index) => {
          console.log(`   ${index + 1}. ${Object.values(table)[0]}`);
        });
        
        // 检查用户数据
        try {
          const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
          console.log(`👥 用户数量: ${users[0].count}`);
          
          const [adminUser] = await connection.execute(
            'SELECT username, real_name, status FROM users WHERE username = "admin"'
          );
          
          if (adminUser.length > 0) {
            console.log('👤 管理员账号:');
            console.log(`   用户名: ${adminUser[0].username}`);
            console.log(`   姓名: ${adminUser[0].real_name}`);
            console.log(`   状态: ${adminUser[0].status === 1 ? '启用' : '禁用'}`);
          }
        } catch (error) {
          console.log('⚠️  无法查询用户数据，可能表结构不完整');
        }
      } else {
        console.log('⚠️  数据库存在但没有数据表，需要运行初始化脚本');
      }
    } else {
      console.log('⚠️  system_manage数据库不存在，需要运行初始化脚本');
    }
    
    console.log('🎉 数据库连接测试完成！');
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 用户名或密码错误，请检查数据库配置');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 连接被拒绝，请检查：');
      console.error('   1. MySQL服务是否启动');
      console.error('   2. 主机地址和端口是否正确');
      console.error('   3. 防火墙设置');
    } else if (error.code === 'ENOTFOUND') {
      console.error('💡 主机名无法解析，请检查主机地址');
    } else if (error.code === 'ETIMEDOUT') {
      console.error('💡 连接超时，请检查网络连接');
    }
    
    console.error('');
    console.error('🔧 当前配置:');
    console.error(`   主机: ${dbConfig.host}`);
    console.error(`   端口: ${dbConfig.port}`);
    console.error(`   用户: ${dbConfig.user}`);
    console.error(`   密码: ${dbConfig.password ? '***' : '(未设置)'}`);
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 显示使用说明
function showUsage() {
  console.log('='.repeat(50));
  console.log('         数据库连接测试工具');
  console.log('='.repeat(50));
  console.log('');
  console.log('此工具用于测试数据库连接和检查初始化状态');
  console.log('');
  console.log('配置方式:');
  console.log('1. 设置环境变量: DB_HOST, DB_PORT, DB_USERNAME, DB_PASSWORD');
  console.log('2. 或修改脚本中的默认配置');
  console.log('');
}

// 主函数
async function main() {
  showUsage();
  await testConnection();
}

// 运行测试
main().catch(console.error);
